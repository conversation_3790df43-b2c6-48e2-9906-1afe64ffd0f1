/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-08 14:46:51
 * @modify date 2024-02-08 14:46:51
 * @desc TlCmTxb 교과학습 공통처리API(교사)
 */
package com.aidt.api.tl.cmtxb.tcr;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdReqDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdUdtDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlAtvDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlLluDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlTcDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbTcTmDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbTotLrnTmDto;
import com.aidt.common.CommonDao;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class TlCmTxbTcrService {
    private final String MAPPER_NAMESPACE = "api.tl.cmtxb.tcr.";

    @Autowired
    private CommonDao commonDao;
    
    @Autowired
    private WebFluxUtil webFluxUtil;
    
	@Value("${aidt.endpoint.claBoard:}")
	private String endpoint_claBoard;

    /**
     * 학습 상세 현황
     * 
     * @param srhDto
     * @return TlCmTxbLrnDtlPstDto
     */
    @Transactional(readOnly = true)
    public TlCmTxbLrnDtlPstDto selectTxbLrnDtlPst(TlCmTxbLrnDtlPstSrhDto srhDto) {
        TlCmTxbLrnDtlPstDto dtlPstDto = commonDao.select(MAPPER_NAMESPACE + "selectTxbLrnDtlPst", srhDto); // 학생 정보
        List<TlCmTxbLrnDtlLluDto> dtlLluDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbLrnDtlPstLlu",
                srhDto); // 대단원 목록

        dtlLluDtoList.forEach(list1 -> {
            srhDto.setLluNodId(list1.getLrmpNodId());
            List<TlCmTxbLrnDtlTcDto> dtlTcDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbLrnDtlPstTc",
                    srhDto); // 차시 목록

            dtlTcDtoList.forEach(list2 -> {
                srhDto.setLrmpNodId(list2.getLrmpNodId());
                List<TlCmTxbLrnDtlAtvDto> dtlAtvDtoList = commonDao
                        .selectList(MAPPER_NAMESPACE + "selectTxbLrnDtlPstAtv", srhDto); // 활동 목록

                dtlAtvDtoList.forEach(list3 -> {
                    if (list3.getLrnStCd().equals("CL")) {
                        list2.setClCnt(list2.getClCnt() + 1); // 해당 차시 활동완료 수
                    }
                });
                list2.setAtvList(dtlAtvDtoList);

                if (list2.getAtvList().size() != 0) { // 해당 차시 진행률
                    double tcPgrsRt = ((double) list2.getClCnt() / (double) list2.getAtvList().size()) * 100;
                    list2.setPgrsRt(Math.round(tcPgrsRt * 10) / 10.0);
                }

                list1.setClCnt(list1.getClCnt() + list2.getClCnt()); // 단원 내 활동완료 수
                list1.setAtvCnt(list1.getAtvCnt() + list2.getAtvList().size()); // 단원 내 활동 수
            });

            list1.setTcList(dtlTcDtoList);
            if (list1.getAtvCnt() != 0) { // 해당 단원 진행률
                double lluPgrsRt = ((double) list1.getClCnt() / (double) list1.getAtvCnt()) * 100;
                list1.setPgrsRt(Math.round(lluPgrsRt * 10) / 10.0);
            }
        });

        dtlPstDto.setLrnPstList(dtlLluDtoList);
        return dtlPstDto;
    }

    /**
     * 학습활동별 클래스보드URL 저장처리
     * 
     * @param udtDto
     * @return
     */
    @Transactional
    public int updateCmTxbClsBrdUrl(TlCmTxbClsBrdUdtDto udtDto) {
        return commonDao.update(MAPPER_NAMESPACE + "updateCmTxbClsBrdUrl", udtDto);
    }
    /**
     * 학습활동 클래스보드별 클래스보드URL저장처리
     */
    public int updateCmTxbClabdUrl(TlCmTxbClsBrdUdtDto udtDto) {
        return commonDao.update(MAPPER_NAMESPACE + "updateCmTxbClabdUrl", udtDto);
    }

    /**
     * 학생 별 총 학습시간 조회
     * 
     * @return List<TlCmTxbTotLrnTmDto>
     */
    @Transactional(readOnly = true)
    public List<TlCmTxbTotLrnTmDto> selectCmTxbTotLrnTm(TlCmTxbSrhDto srhDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectCmTxbTotLrnTm", srhDto);
    }

    /**
     * 이번 수업 선택
     * 
     * @param tmDto
     * @return 
     */
	public int updateCmTxbTcTm(TlCmTxbTcTmDto tmDto) {
		int ctn = 0;
		int result = commonDao.select(MAPPER_NAMESPACE + "selectCmTxbTcTm", tmDto);
		if(result == 0) {
			ctn = commonDao.insert(MAPPER_NAMESPACE + "insertCmTxbTcTm", tmDto);
		} else {
			ctn = commonDao.update(MAPPER_NAMESPACE +"updateCmTxbTcTm", tmDto);
		}
		return ctn;
	}
	
    /**
     * 학습완료 수 조회(과제)
     * 
     */
    @Transactional(readOnly = true)
    public List<Map<String,Object>> selectLrnAtvSt(String optTxbId, String lrmpNodId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvSt", Map.of("optTxbId", optTxbId, "lrmpNodId", lrmpNodId));
    }
    
    /**
     * 클래스보드 API Call
     * @param url
     * @param dto
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> callCsbApi(String url, TlCmTxbClsBrdReqDto dto) {
    	Map<String, Object> returnMap = new HashMap<>();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        //httpHeaders.add("Content-Type", "application/json;charset=UTF-8");    
        try {
            String jsonString = new ObjectMapper().writeValueAsString(dto);
            
            String post = webFluxUtil.post(endpoint_claBoard + url, httpHeaders, jsonString, String.class);                                   
            return CoreUtil.Json.jsonString2Map(post);
        } catch (JsonProcessingException e) {
        	returnMap = new HashMap<>();
        }
        
    	return returnMap;
    }

	public String selectStrAtvId(TlCmTxbTcTmDto tmDto) {
		String atvId = commonDao.select(MAPPER_NAMESPACE + "selectStrAtvId", tmDto);
		atvId = (atvId == null || atvId.trim().isEmpty()) ? "" : atvId;
		return atvId;
	}

}
