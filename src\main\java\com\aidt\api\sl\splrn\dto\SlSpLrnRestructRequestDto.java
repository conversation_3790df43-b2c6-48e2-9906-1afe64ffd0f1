package com.aidt.api.sl.splrn.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import io.swagger.v3.oas.annotations.Parameter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-08 13:10:01
 * @modify date 2024-02-08 13:10:01
 * @desc SlSpLrnRestructRequestDto
 */
public class SlSpLrnRestructRequestDto {
    @Parameter(name="운영교과서ID")
    @NotBlank(message = "")
    private String optTxbId;

    @Parameter(name="특별학습ID")
    @NotBlank(message = "")
    private String spLrnId;

    @Parameter(name="사용여부")
    @Pattern(regexp = "^[YN]$", message = "")
    private String useYn;

    @Parameter(name="재구성순서")
    @NotNull(message = "")
    private Integer rcstnOrdn;
}