package com.aidt.api.ea.evcom;


import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.aidt.api.common.utils.HalfWidthUtils;
import com.aidt.api.ea.evcom.dto.*;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto;
import com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto;
import com.aidt.api.tl.lrnwif.stu.TlLrnwIfStuService;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 14:05:오후 2:05
 * @modify date 2024-02-15 14:05:오후 2:05
 * @desc 평가(DIY 평가) - Service
 */
@Slf4j
@Service
public class EaEvComService {
    private final String MAPPER_NAMESPACE = "api.ea.evcom.";
    private final String DOMAIN_PATH = "/api/v1/content/";


    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;


    @Autowired
    private CommonDao commonDao;

    @Autowired
    private WebFluxUtil webFluxUtil;
    
    @Autowired
	private JwtProvider jwtProvider;
    
    @Autowired
	private BcCmService bcCmService;
    
    @Autowired
    private TlLrnwIfStuService tlLrnwIfstuService;

    @Value("${aidt.endpoint.lw_myhm_stu_point:}")
    private String endpoint_lw_myhm_stu_point;
	private HttpHeaders area;

    public String getLocalDomain() {
        String domain = "";
		try {
				//BUCKET_NAME = BUCKET_NAME.replace("dev", "prd");

				if("local".equals(SERVER_ACTIVE)) {
					domain = "https://www-n-ele.aitextbook.co.kr";
				}
		}
        catch (Exception e) {
        	log.warn(e.getMessage());
        }

		return domain;
    }

    /**
     * 공통 코드 조회 요청
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    
    @Cacheable(
	    cacheNames = "longCache",
	    key="'eaComCodeList:' + #eaEvComDto.urnkCmCd",
	    cacheManager = "aidtCacheManager"
    )
    public List<Map<String,String>> selectComCodList(EaEvComDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectComCodList", eaEvComDto);
    }


    /**
     * 단원 목록 조회 요청
     *
     * @param optTxbId
     * @return List<Map<String,String>>
     */
    public List<Map<String,String>> selectLuIdList(String optTxbId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectLuIdList", optTxbId);
    }

      /**
     * 중단원/소단원 학습맵노드 목록 조회 요청
     *
     * @param optTxbId
     * @return List<Map<String,String>>
     */
    public List<Map<String,String>> selectMluSluLrmpNodList(String optTxbId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMluSluLrmpNodList", optTxbId);
    }


   /**
     * 중단원 목록 조회 요청
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    public List<Map<String,String>> selectMluLrmpNodId(EaEvComDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMluLrmpNodId", eaEvComDto);
    }

    /**
     * 차시 목록 조회 요청
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    public List<Map<String,String>> selectTcIdList(EaEvComDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectTcIdList", eaEvComDto);
    }

    /**
     * 평가 보충/심화 결과 등록 요청
     *
     * @param param
     * @return Map<String, String>
     */
    @Transactional
    public int insertEaEvSppNtnQtmAnw(InsertEaEvQtmAnwReqDto param) {

        // 보충/심화 결과 업데이트 가능한지 확인
    	EaEvComQtmReqDto eaEvComDto = new EaEvComQtmReqDto();
    	eaEvComDto.setOptTxbId(param.getOptTxbId());
    	eaEvComDto.setUsrId(param.getUsrId());
    	eaEvComDto.setEvId(param.getEvId());
    	eaEvComDto.setTxmPn(param.getTxmPn());

    	Map<String, Object> evInfo = commonDao.select(MAPPER_NAMESPACE + "selectEvSppNtnCheckInfo", eaEvComDto);
    	if(evInfo == null || evInfo.isEmpty()) {
    		return 0;
    	}

        // 중고등 영어는 오답심화 없음
    	String evDtlDvCd = evInfo.get("evDtlDvCd").toString();
        String sbjCd = evInfo.get("sbjCd").toString(); // 과목코드
        String schlGrdCd = evInfo.get("schlGrdCd").toString(); // 학교급코드

        // 형성/차시 평가가 아니면 패스
        if(!"FO".equals(evDtlDvCd) && !"TO".equals(evDtlDvCd))
        {
        	return 0;
        }
        // 중고등 영어 패스
        if("EN".equals(sbjCd) && !"E".equals(schlGrdCd))
        {
        	return 0;
        }

        String noteOnlyYn = param.getNoteOnlyYn();  // 노트만 저장 여부
        String sppNtnTpCd = evInfo.get("sppNtnTpCd").toString(); // 보충심화유형코드
        param.setSppNtnTpCd(sppNtnTpCd);
        param.setQstCnt(param.getQtmList().size());

        // 보충/심화 답변 결과 업데이트
    	String xplStCd = "";
    	String smtAnwVlNew = "";

    	// 노트만 저장일 경우에는 정답 체크 안함
        if ("Y".equals(noteOnlyYn)) {
    		//평가 보충심화문항답변 업데이트
    		commonDao.insert(MAPPER_NAMESPACE + "updateEaEvSppNtnQtmAnw", param);
        }
        // 학습창 제출 일 경우
        else {
        	param.setNoteOnlyYn("N"); // 제출일경우는 노트만 저장 없음.

	        for (InsertEaEvQtmAnwReqDto dto : param.getQtmList()) {
	        	
	        	if(dto.getQtmId() == null) {
	        		return 0;
	        	}
	        		
	        	if(dto.getSmtAnwVl() != null) {
	        		// 정오답 체크
		            smtAnwVlNew = dto.getSmtAnwVl();
		            smtAnwVlNew = smtAnwVlNew.replaceAll("&gt;", ">");
		            smtAnwVlNew = smtAnwVlNew.replaceAll("&lt;", "<");
		            smtAnwVlNew = smtAnwVlNew.replaceAll("&amp;", "&");
					//답변 전각 -> 반각 처리
					smtAnwVlNew = HalfWidthUtils.normalize2HalfWidth(smtAnwVlNew);
	        	}else {
	        		smtAnwVlNew = "";
	        	}

	            dto.setSmtAnwVl(smtAnwVlNew);

	            // 외주 문항여부는 정답정보 전달해줌
	            if (dto.getIsExternal() != null && "Y".equals(dto.getIsExternal())) {
	                dto.setCansYn(dto.getIsCorrect());//정답 여부
	            }
	            else {
	                Map<String, Object> answer = commonDao.select(MAPPER_NAMESPACE + "selectAnswer", dto);//문항 정답 조회
	                dto.setCansYn(getCansYn(dto.getSmtAnwVl(), answer));//정답 여부 확인
	            }

	            dto.setUsrId(param.getUsrId());
	            dto.setEvId(param.getEvId());
	            // 풀이시간 상태 조회
	            Map<String, Object> xplStMap = commonDao.select(MAPPER_NAMESPACE + "selectEvQtmAnwXplStCd", dto);//문항 정답 조회
	            xplStCd = "00";
	            if(xplStMap != null && !xplStMap.isEmpty()) {
	            	xplStCd = xplStMap.get("xplStCd").toString();
	            }

	            dto.setXplStCd(xplStCd);
	        }

			//평가 보충심화문항답변 업데이트
			commonDao.insert(MAPPER_NAMESPACE + "updateEaEvSppNtnQtmAnw", param);
			//평가 보충심화결과 업데이트
			commonDao.insert(MAPPER_NAMESPACE + "updateEaEvSppNtnRs", param);
        }

        return 1;
    }


    /**
     * 평가 결과 등록 요청
     *
     * @param param
     * @return Map<String, String>
     */
    @Transactional
    public Map<String, String> insertEaEvQtmAnw(InsertEaEvQtmAnwReqDto param) {
        int txmPn = 0;
    	Map<String, String> map = new HashMap<>(); // 결과 리턴 map
        map.put("status", "204");
        
        if(!param.getQtmList().isEmpty()) {
            if(param.getQtmList().get(0).getQtmId().isEmpty() || "".equals(param.getQtmList().get(0).getQtmId())){
            	map.put("status", "400");
            	map.put("message", "문항 정보를 확인할 수 없습니다.");
                return map;
            }
        } else {
        	map.put("status", "400");
        	map.put("message", "문항 정보를 확인할 수 없습니다.");
            return map;
        }
        
        //제출 된 평가가 이미 완료가 된 평가일 경우 재응시 평가이고 재응시 일 경우 몇 회차 재응시 인지 확인
        InsertEaEvQtmAnwReqDto ev = commonDao.select(MAPPER_NAMESPACE + "selectEvCmplYn", param);
        if(ev == null) {
        	map.put("status", "400");
        	map.put("message", "평가 정보를 찾을 수 없습니다.");
        	return map;
        }
        
        txmPn = ev.getTxmPn();

        //원평가의 평가완료여부
        String evCmplYn = ev.getEvCmplYn();

        // 2024-11-06 기존에 제출완료만 이 API를 태웠는데 중간에 임시저장기능이 생겨서 param.getEvCmplYn() 제출완료여부 체크 추가
        if ("Y".equals(evCmplYn)) {//원평가 완료된 경우 > 재응시
        	if ("Y".equals(ev.getEvRcmplYn())) {//재응시평가 완료된 경우
        		txmPn = txmPn + 1;
        	}
        	else if (txmPn == 0) {
            	txmPn = 1;
            }
        }
        param.setTxmPn(txmPn);

    	param.setNoteOnlyYn("N"); // 전체문항 제출일 경우는 노트만 저장 없음.

        String smtAnwVlNew = "";
        List<InsertEaEvQtmAnwReqDto> cansYnList = new ArrayList<>();//채점 결과 list
        for (InsertEaEvQtmAnwReqDto dto : param.getQtmList()) {
            dto.setEvId(param.getEvId());
            dto.setUsrId(param.getUsrId());
            dto.setDbId(param.getDbId());
            
            if(dto.getQtmId() == null) {
            	map.put("status", "400");
            	map.put("message", "Invalid qtmId in Request Parameter");
            	return map;
            }
            
            if(dto.getSmtAnwVl() != null) {
            	smtAnwVlNew = dto.getSmtAnwVl();
                smtAnwVlNew = smtAnwVlNew.replaceAll("&gt;", ">");
                smtAnwVlNew = smtAnwVlNew.replaceAll("&lt;", "<");
                smtAnwVlNew = smtAnwVlNew.replaceAll("&amp;", "&");
				//답변 전각 -> 반각 처리
				smtAnwVlNew = HalfWidthUtils.normalize2HalfWidth(smtAnwVlNew);
                
            }else {
            	smtAnwVlNew = "";
            	
            }
            
            dto.setSmtAnwVl(smtAnwVlNew);

            // 외주 문항여부는 정답정보 전달해줌
            if ("Y".equals(dto.getIsExternal())) {
                dto.setCansYn(dto.getIsCorrect());//정답 여부
            }
            else {
                Map<String, Object> answer = commonDao.select(MAPPER_NAMESPACE + "selectAnswer", dto);//문항 정답 조회
                dto.setCansYn(getCansYn(dto.getSmtAnwVl(), answer));//정답 여부 확인
            }

            cansYnList.add(dto);

            //원평가 완료된 경우 > 재응시처리
            if ("Y".equals(evCmplYn)) {
                dto.setTxmPn(txmPn);
                //commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnwRtxm", dto);
                if (checkRecordEaEvQtmAnwRtxm(dto)) {
	        	    // 레코드가 존재하면 UPDATE 실행
	        	    commonDao.update(MAPPER_NAMESPACE + "updateEaEvQtmAnwRtxm", dto);
	        	} else {
	        	    // 레코드가 존재하지 않으면 INSERT 실행
	        	    commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtmAnwRtxm", dto);
	        	}
            } else {
                //commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnw", dto);
            	// 존재하는지 체크하는 로직
	        	if (checkRecordEaEvQtmAnw(dto)) {
	        	    // 레코드가 존재하면 UPDATE 실행
	        	    commonDao.update(MAPPER_NAMESPACE + "updateEaEvQtmAnw", dto);
	        	} else {
	        	    // 레코드가 존재하지 않으면 INSERT 실행
	        	    commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtmAnw", dto);
	        	}
            }
            
        }

        //원평가 완료된 경우 > 재응시처리
        if ("Y".equals(evCmplYn)) {
             //평가 결과 재응시 update
            commonDao.update(MAPPER_NAMESPACE + "updateEaEvRsRtxm", param);
        } else {// 본 평가인 경우
            //평가 결과 update
            commonDao.update(MAPPER_NAMESPACE + "updateEaEvRs", param);
            if(param.getAtvSaveDto() != null) {
                if("ST".equals(param.getUsrTpCd())) {
                	param.setUsrTpCd("S");
                } else {
                	param.setUsrTpCd("T");
                }
                
                param.getAtvSaveDto().setOptTxbId(param.getOptTxbId());
                param.getAtvSaveDto().setLrnUsrId(param.getUsrId());
                param.getAtvSaveDto().setDbId(param.getTxbId());
                
                if(param.getAtvSaveDto().getLrmpNodId() != null 
                		&& param.getAtvSaveDto().getLrnAtvId() != null 
                		&& "".equals(param.getAtvSaveDto().getLrmpNodId())
                		&& "".equals(param.getAtvSaveDto().getLrnAtvId())) {
                    tlLrnwIfstuService.saveLrnAtvInfo(param.getAtvSaveDto(), param.getUsrTpCd());
                } else {
                	Map<String, String> lrmpMap = commonDao.select(MAPPER_NAMESPACE + "selectLrmp", param);
                	if(lrmpMap != null) {
                    	param.getAtvSaveDto().setLrmpNodId(String.valueOf(lrmpMap.get("LRMP_NOD_ID")));
                    	param.getAtvSaveDto().setLrnAtvId(String.valueOf(lrmpMap.get("LRN_ATV_ID")));
                        tlLrnwIfstuService.saveLrnAtvInfo(param.getAtvSaveDto(), param.getUsrTpCd());
                	}
                }
            }
        }
        

        // 형성/차시 평가 일 경우 보충/심화 문제 생성
        // 생성된 보충/심화 없을 경우에만 생성
        String evDtlDvCd = ev.getEvDtlDvCd();

        //정답 개수
        int cansCnt = (int)cansYnList.stream().filter(e -> "Y".equals(e.getCansYn())).count();

        do {
            // 제출 완료인 평가가 아니면 패스
            if(!"Y".equals(param.getEvCmplYn()))
            {
            	break;
            }
            // 형성/차시 평가가 아니면 패스
            if(!"FO".equals(evDtlDvCd) && !"TO".equals(evDtlDvCd))
            {
            	break;
            }
            // 중고등 영어 패스
            if("EN".equals(ev.getSbjCd()) && !"E".equals(ev.getSchlGrdCd()))
            {
            	break;
            }

            // 2024-11-05 재응시도 보충심화 가능하여 제거
//            // 보충심화 평가 생성 되었다면 패스
//            if(ev.getSppNtnEvCnt() > 0)
//            {
//            	break;
//            }


        	InsertEaEvQtmAnwReqDto smlrDto = new InsertEaEvQtmAnwReqDto();

        	smlrDto.setOptTxbId(param.getOptTxbId());
        	smlrDto.setUsrId(param.getUsrId());
        	smlrDto.setDbId(param.getDbId());
        	smlrDto.setEvId(param.getEvId());
        	smlrDto.setTxmPn(txmPn);

        	// 심화문제(만점)
        	if(cansYnList.size() == cansCnt) {
	        	smlrDto.setNtnEvCrtYn("Y");
        		smlrDto.setIansCnt(0);
        		smlrDto.setSppNtnTpCd("DE"); //DE	심화평가(심화학습)
        	}
        	else {//그외 보충 ==> 오답유사문제
        		smlrDto.setSppNtnTpCd("SI"); //SI	유사평가(보충학습)
        	}


        	List<InsertEaEvQtmAnwReqDto> smlrQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvSmlrQtmIdList", smlrDto);

        	// 유사문항리스트 없음
        	if(smlrQtmList == null || smlrQtmList.isEmpty() || smlrQtmList.size() < 1) {
        		break;
        	}

        	smlrDto.setQtmList(smlrQtmList);
        	smlrDto.setQstCnt(smlrQtmList.size());
        	smlrDto.setEvCmplYn("N");

        	try {
	        	//평가 보충심화결과 등록
	    		commonDao.insert(MAPPER_NAMESPACE + "updateEaEvSppNtnRs", smlrDto);
	    		//평가 보충심화문항답변 등록
	    		commonDao.insert(MAPPER_NAMESPACE + "updateEaEvSppNtnQtmAnw", smlrDto);
        	}
            catch (Exception e) {
            	log.warn(e.getMessage());
            }
        } while (false);


        map.put("status", "200");
        map.put("cansCnt", cansCnt+"");
        map.put("message", "정답 수:" + cansCnt);
        map.put("success", "Y");


        return map;
    }

    /**
     * 평가 결과 문항 개별 등록 요청
     *
     * @param param
     * @return Map<String, String>
     */
    @Transactional
    public Map<String, String> insertEaEvQtmAnwEly(InsertEaEvQtmAnwReqDto param) {

        int txmPn = 0;
        // 노트만 저장 여부
        String noteOnlyYn = param.getNoteOnlyYn();

    	Map<String, String> map = new HashMap<>();
        map.put("status", "204"); // no content

        //제출 된 평가가 이미 완료가 된 평가일 경우 재응시 평가이고 재응시 일 경우 몇 회차 재응시 인지 확인
        InsertEaEvQtmAnwReqDto ev = commonDao.select(MAPPER_NAMESPACE + "selectEvCmplYn", param);
        if(ev == null) {
        	map.put("message", "평가 정보를 찾을 수 없습니다.");
        	return map;
        }
        
        if(param.getQtmId() == null || param.getQtmId().isEmpty() || "".equals(param.getQtmId())) {
        	map.put("status", "400");
        	map.put("message", "Invalid qtmId in Request Parameter");
        	return map;
        }

        txmPn = ev.getTxmPn();

        String evCmplYn = ev.getEvCmplYn();

        // 2024.11.26 응시회차 노트만 저장여부 제거  && !"Y".equals(noteOnlyYn)
        if ("Y".equals(evCmplYn)) {//원평가 완료된 경우 > 재응시
        	if ("Y".equals(ev.getEvRcmplYn())) {//재응시평가 완료된 경우
        		txmPn = txmPn + 1;
        	}
        	else if (txmPn == 0) {
        		txmPn = 1;
            }

        }
    	param.setTxmPn(txmPn);



        // 노트만 저장
        if("Y".equals(noteOnlyYn)) {
	        //평가 결과 재응시 update
	        if (txmPn > 0) {
	            //commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnwRtxm", param);
	        	if (checkRecordEaEvQtmAnwRtxm(param)) {
	        	    // 레코드가 존재하면 UPDATE 실행
	        	    commonDao.update(MAPPER_NAMESPACE + "updateEaEvQtmAnwRtxm", param);
	        	} else {
	        	    // 레코드가 존재하지 않으면 INSERT 실행
	        	    commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtmAnwRtxm", param);
	        	}
	        }
	        //평가 결과 update
	        else {
	            //commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnw", param);
	        	// 존재하는지 체크하는 로직
	        	if (checkRecordEaEvQtmAnw(param)) {
	        	    // 레코드가 존재하면 UPDATE 실행
	        	    commonDao.update(MAPPER_NAMESPACE + "updateEaEvQtmAnw", param);
	        	} else {
	        	    // 레코드가 존재하지 않으면 INSERT 실행
	        	    commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtmAnw", param);
	        	}
	        }
        }
        // 학습창 문항 한건씩 정상 제출
        else {
            // 정답 html 변환된 특수문자 처리
            String smtAnwVlNew = "";

            smtAnwVlNew = param.getSmtAnwVl();
            smtAnwVlNew = smtAnwVlNew.replaceAll("&gt;", ">");
            smtAnwVlNew = smtAnwVlNew.replaceAll("&lt;", "<");
            smtAnwVlNew = smtAnwVlNew.replaceAll("&amp;", "&");
			//답변 전각 -> 반각 처리
			smtAnwVlNew = HalfWidthUtils.normalize2HalfWidth(smtAnwVlNew);
            param.setSmtAnwVl(smtAnwVlNew);

            // 외주 문항여부는 정답정보 전달해줌
	        if("Y".equals(param.getIsExternal())){
	        	param.setCansYn(param.getIsCorrect());//정답 여부
	        }
	        else {
	        	// 문통 문항 정답정보 조회
	            Map<String, Object> answer = commonDao.select(MAPPER_NAMESPACE + "selectAnswer", param);//문항 정답 조회
	            param.setCansYn(getCansYn(param.getSmtAnwVl(), answer));//정답 여부 확인
	        }

	        //평가 결과 재응시 update
	        if ("Y".equals(evCmplYn)) {
	            //commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnwRtxm", param);
	            
	        	
	        	if (checkRecordEaEvQtmAnwRtxm(param)) {
	        	    // 레코드가 존재하면 UPDATE 실행
	        	    commonDao.update(MAPPER_NAMESPACE + "updateEaEvQtmAnwRtxm", param);
	        	} else {
	        	    // 레코드가 존재하지 않으면 INSERT 실행
	        	    commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtmAnwRtxm", param);
	        	}
	        	commonDao.update(MAPPER_NAMESPACE + "updateEaEvRsRtxm", param);
	        }
	        //평가 결과 update
	        else {
	            //commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnw", param);
	        	// 존재하는지 체크하는 로직
	        	if (checkRecordEaEvQtmAnw(param)) {
	        	    // 레코드가 존재하면 UPDATE 실행
	        	    commonDao.update(MAPPER_NAMESPACE + "updateEaEvQtmAnw", param);
	        	} else {
	        	    // 레코드가 존재하지 않으면 INSERT 실행
	        	    commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtmAnw", param);
	        	}
	        	
	            commonDao.update(MAPPER_NAMESPACE + "updateEaEvRs", param);
	        }

        }

        map.put("status", "200");
        map.put("success", "Y");
        map.put("cansYn", param.getCansYn());


        return map;
    }


    public boolean checkRecordEaEvQtmAnw(InsertEaEvQtmAnwReqDto dto) {
        // 예시: EV_ID와 QTM_ID로 조회하여 존재 여부 확인
        Integer count = commonDao.select(MAPPER_NAMESPACE + "checkRecordEaEvQtmAnw", dto);
        return count != null && count > 0;
    }
    
    public boolean checkRecordEaEvQtmAnwRtxm(InsertEaEvQtmAnwReqDto dto) {
        // 예시: EV_ID와 QTM_ID로 조회하여 존재 여부 확인
        Integer count = commonDao.select(MAPPER_NAMESPACE + "checkRecordEaEvQtmAnwRtxm", dto);
        return count != null && count > 0;
    }
    
    /**
     * 제출 답변 정답 여부 확인
     */
    public String getCansYn(String smtAnwVl, Map<String, Object> answerInfo) {
    	// 2024-11-26 서술형 체크로직 추가

    	//제출 답변 없을 경우 오답 처리
        if (smtAnwVl == null || smtAnwVl.isEmpty()) {
            return "N";
        }

        // 문통 정답정보 없을 경우
    	if (answerInfo == null ) {
    		return "N";
    	}

    	int i, j;
    	int partCansCnt = 0; //부분정답 개수
    	int plurCansCnt = 0; //복수 정답 개수


    	try {
        	// 문통 정답JSON DATA 내용 없을 경우
        	if(null == answerInfo.get("qpJsonDataCn") || ((String)answerInfo.get("qpJsonDataCn")).isEmpty())
    		{
    			return "N";
    		}


			String[] smtAnwVlArr = smtAnwVl.split("[|]");//제출 답변
			List<Map<String, Object>> jsonData = jsonToMap((String) answerInfo.get("qpJsonDataCn"));//JSON DATA 파싱

	    	String qpQstTypCd = (String) answerInfo.get("qpQstTypCd"); //문항 유형 코드(10:자유 선지,20+30+40+50: 2~5지 선택,60+61+62:단답 유순형,단답 무슨형,단답 묶음형)

	    	partCansCnt = Integer.parseInt(answerInfo.get("partCansCnt").toString()); //부분 정답 개수
    		plurCansCnt = Integer.parseInt(answerInfo.get("plurCansCnt").toString()); //복수 정답 개수

	    	// 서술형 문항
	    	if("85".equals(qpQstTypCd)) {
	    		boolean keywordMatch = false;
	    		boolean answerMatch = false;
	        	int descriptiveKeywordCount = 0;
	        	String qstXplCn = "";

	    		qstXplCn = smtAnwVlArr[0]; //첫번째 데이터는 풀이과정

	    		// 풀이과정 없으면 오답
	    		if(qstXplCn.isEmpty()) {
	    			return "N";
	    		}

	    		
	    		List<Map<String, String>> answerList = (List<Map<String, String>>) jsonData.get(0).get("answerList"); // 문통 정답
	    		
	    		String qtmId = answerInfo.get("qpQtmId").toString();
	    		List<Map<String, Object>> kwdList = commonDao.selectList(MAPPER_NAMESPACE + "selectKwdList", qtmId);
	    		
	    		descriptiveKeywordCount = kwdList.isEmpty() ? 0 : Integer.parseInt(kwdList.get(0).get("kwdCnt").toString());//문제 풀이 과정 키워드 개수
	    		
	    		//문제 풀이 과정 키워드 개수가 0개 일때 키워드 입력했으면 정답으로 인정
	    		if(descriptiveKeywordCount == 0 && !qstXplCn.isEmpty()) {
	    			return "Y";
	    		}

	    		// 문제풀이과정 텍스트중 키워드와 일치하는 개수 확인
	    		int keywordMatchCount = checkKwd(qstXplCn, kwdList);
	    		
	    		//문제풀이과정 맞은 키워드 수 체크, 풀이과정키워드수가 문통키워드수보다 작으면 정답인정 안됨
	    		if(descriptiveKeywordCount > keywordMatchCount) {
	    			return "N";
	    		}
	    		
	    		keywordMatch = true ;

	    		/* 문제 풀이과정만 존재하는 서술형일 경우 키워드 정답이면 정답으로 인정 
	    		 * 키워드만 전달된 상태, 정답정보 없으므로 키워드 정답이면 정답, 키워드 일치 없으면 오답
	    		 * 
	    		 * 	 데이터 케이스
	    		 *   1) 키워드존재, 정답 있음 -> 디폴트
	    		 *   2) 키워드존재, 정답 없음 -> 풀이만 존재
	    		 *   			 정답 "풀이 참고" 데이터 있음
	    		 *   
	    		 *   3) 키워드없음, 정답 없음 -> 해당 케이스 현재 조건에서 선생님 문제출제 불가능
	    		 *   				
	    		 *   1번 케이스에서도 정답을 입력하지 않고 풀이과정만 제출해도 smtAnwVlArr.length: 1 이된다.
	    		 *   
	    		 *   2번케이스 ) -> smtAnwVl에 | 없이 오는 문항
	    		 * */	    		 
	    		
	    		boolean isNotExistAnswerTag = !smtAnwVl.contains("|"); 
	    		// 2번 케이스 정답처리 
	    		if( isNotExistAnswerTag ) { 
	    			return "Y";
	    		}
 
            	// 정답 있을경우 정답 체크, 유사정답 체크(여러개일 수 있음)
	    		String capitalYn, ignoreSpaceYn, answer = "";
	    		String result = "N";
	    		String smtAnwVlTmp = "N";

	            capitalYn = (String) jsonData.get(0).getOrDefault("capitalYn", "");       		// N:대소문자 구분
	            ignoreSpaceYn = (String) jsonData.get(0).getOrDefault("ignoreSpaceYn", "");   	// Y:스페이스 체크

	            for (Map<String, String> map : answerList) {
	            	answer = map.get("answer");
	                answer = replaceAndLower(capitalYn, ignoreSpaceYn, answer); 	//정답 대,소 문자, 스페이스 구분 처리
	                answer = replaceAnswer(answer);                                      	//특수 문자 변환 처리
	                smtAnwVlTmp = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVlArr[1].trim());      	//답변 대,소 문자, 스페이스 구분 처리

	                // 문통 정답이 없을 경우 정답 입력 있으면 정답으로 인정
	                if(answer.isEmpty() && !smtAnwVlTmp.isEmpty()) {
	                	return "Y";
	                }
	                
	                if(smtAnwVlTmp.isEmpty()) { // 정답 항목 입력없으면 오답 
	                	return "N";
	                }

	                result = checkAnswer(smtAnwVlTmp, answer);                              	//정답 확인
	                if ("Y".equals(result)) {
	                	answerMatch = true;
	                	break;
	                }
	            }

	            if(keywordMatch && answerMatch) {
	            	return "Y";
	            }
	            else return "N";
	    	}
	    	// 서술형이 아니면
	    	else {
	    		return scoringProcess(smtAnwVlArr, qpQstTypCd, partCansCnt, plurCansCnt, jsonData);
	    	}

    	} catch (Exception e) {
    		log.error("### unexpected scoringProcess error: " + answerInfo.toString(), e);
    		return "N";
    	}
    }

   
    public int checkKwd(String qstXplCn, List<Map<String, Object>> kwdList) {
    	int matchCount = 0;
    	
    	for (int i = 0; i < kwdList.size(); i++) {
			String capitalYn = kwdList.get(i).get("kwdCaseYn").toString();      		// Y, N:대소문자구분 유무
            String ignoreSpaceYn = kwdList.get(i).get("kwdSpaceSkipYn").toString(); 	// Y, N:공백제거 유무
            
			String tempQstXplCn = replaceAndLower(capitalYn, ignoreSpaceYn, qstXplCn);  // 학생 풀이입력 문자열
			String kwd = replaceAndLower(capitalYn, ignoreSpaceYn, kwdList.get(i).get("qpKwd").toString()); // 문통 키워드

			if(tempQstXplCn.contains(kwd)) {
				matchCount++;// 키워드 정답 수	
				
				int k, q;
				int startIndex = -1;     
				int lastIndex = -1;
				boolean foundMatch = false; // 매치 여부를 추적하는 변수

				for (q = 0; q < qstXplCn.length(); q++) { // 풀이 과정을 순회합니다.
				    startIndex = -1; // 각 q에 대해 초기화
				    for (k = 0; k < kwd.length(); k++) {
				        char kwdChar = kwd.charAt(k);
				        char qChar = qstXplCn.charAt(q);

				        if ("N".equals(capitalYn)) { // 대소문자 구분여부
				            qChar = Character.toLowerCase(qChar);
				            kwdChar = Character.toLowerCase(kwdChar);
				        }

				        if ("Y".equals(ignoreSpaceYn)) {// 공백무시 옵션여부
				            if (qChar == ' ' || qChar == '\t' || qChar == '\n' || qChar == '\r') {
				            	q++;
				            	k--;
				                continue; // 키워드의 index는 유지, 풀이 문자열은 다음으로 진행
				            }
				        }
				        if (kwdChar == qChar) {
				            if (startIndex == -1) {
				                startIndex = q; // 첫 번째 일치하는 인덱스 저장
				            }
				            if (k == kwd.length() - 1) {
				                lastIndex = q; // 모든 글자를 만족했을 때 마지막 인덱스 저장
				                foundMatch = true; 
				                break; // 내부 루프 종료
				            }
				            q++; // 다음 qChar로 이동
				        } else {
				            startIndex = -1;
				            k = -1; // k를 초기화하여 kwd의 첫 글자로 되돌림
				            break; // k가 초기화되었으므로 내부 루프 종료
				        }
				    }
				    
				    if (foundMatch) {
				    	qstXplCn = qstXplCn.substring(0, startIndex) + qstXplCn.substring(lastIndex+1);
				        break; // kwd를 모두 만족하면 외부 루프도 종료
				    }
				}
			}
		}
    	
    	return matchCount;
    }
    
    @SuppressWarnings("unchecked")
	public String scoringProcess(String[] smtAnwVlArr, String qpQstTypCd, int partCansCnt, int plurCansCnt, List<Map<String, Object>> jsonData) {

    	//2024-11-27 서술형 정답 체크 추가됨.
    	//    	// 서술형은 항상 정답
		//    	if ("85".equals(qpQstTypCd)) {
		//    		return "Y";
		//    	}

    	// 자유선지형의 경우 답의 갯수와 정답 갯수가 다르면 오답
    	if ("10".equals(qpQstTypCd) && smtAnwVlArr.length != jsonData.size()) {
            	return "N";
    	}


        String capitalYn, ignoreSpaceYn, smtAnwVl, smtAnwVlReverse, answer, result = "";
        List<Map<String, String>> answerList;
        if (partCansCnt == 1) { //채점 할 정답 수 1개인 경우
            capitalYn = (String) jsonData.get(0).getOrDefault("capitalYn", "");       // N:대소문자 구분
            ignoreSpaceYn = (String) jsonData.get(0).getOrDefault("ignoreSpaceYn", "");   // Y:스페이스 체크
            answerList = (List<Map<String, String>>) jsonData.get(0).get("answerList");               // 정답 list
            smtAnwVl = smtAnwVlArr[0];                                                              // 제출 답변

            if (partCansCnt < plurCansCnt) {//부분 정답 수가 복수 정답 수 보다 적을 경우 복수 정답이 있음
                result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVl, result, answerList, qpQstTypCd);
            } else {//복수 정답이 아닌 경우
                answer = replaceAndLower(capitalYn, ignoreSpaceYn, answerList.get(0).get("answer"));   //문통 정답 대, 소 문자 구분 처리
                answer = replaceAnswer(answer);                                                        //문통 특수 문자 변환 처리
                smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVl);                        //제출 답변 대, 소 문자 구분 처리
                result = checkAnswer(smtAnwVl, answer);                                                //정답 여부 확인
            }
        } else {//채점 할 정답 수 2개 이상인 경우
            // if (smtAnwVlArr.length < jsonData.size()) {//채점 할 정답 수 보다 제출 된 답변 수 가 적은 경우 오답 처리
            if (smtAnwVlArr.length != jsonData.size()) {//채점 할 정답 수와 제출 된 답변 수가 다르면 오답 처리
                result = "N";
            } else {
				String[] smtAnwVlArrReverse = smtAnwVlArr.clone();
            	// 단답 무순형이면서 정답이 2개 이상인 경우 목록 정렬 후 정답 비교
            	if ("61".equals(qpQstTypCd)) {
            		jsonData.sort((a, b) -> {
            			List<Map<String, String>> list1 = (List<Map<String, String>>)a.get("answerList");
            			List<Map<String, String>> list2 = (List<Map<String, String>>)b.get("answerList");
            			String ans1 = list1.get(0).get("answer");
            			String ans2 = list2.get(0).get("answer");
            			return ans1.compareTo(ans2);
            		});
            		Arrays.sort(smtAnwVlArr);
            		Arrays.sort(smtAnwVlArrReverse, Comparator.reverseOrder());
            	}

            	// 단답 묶음형이면 가능한 답안조합 중 하나와 일치하면 정답 처리
            	if ("62".equals(qpQstTypCd)) {
            		boolean isMatch = false;
            		int combCnt = plurCansCnt / partCansCnt; // 조합수
            		for (int j = 0; j < combCnt; j++) {
            			isMatch = true;
            			for (int i = 0; i < jsonData.size(); i++) {
            				capitalYn = (String) jsonData.get(i).getOrDefault("capitalYn", "");    // N:대소문자 구분
            				ignoreSpaceYn = (String) jsonData.get(i).getOrDefault("ignoreSpaceYn", "");// Y:스페이스 체크
            				answerList = (List<Map<String, String>>) jsonData.get(i).get("answerList");
            				smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVlArr[i]);
        					answer = replaceAnswer(replaceAndLower(capitalYn, ignoreSpaceYn, answerList.get(j).get("answer")));
        					if ("N".equals(checkAnswer(smtAnwVl, answer))) {
        						isMatch = false;
        						break;
        					}
            			}
            			if (isMatch) {
            				break;
            			}
            		}
                    return isMatch ? "Y" : "N";
            	}

            	// 그 외 유형
                for (int i = 0; i < jsonData.size(); i++) {
                    capitalYn = (String) jsonData.get(i).getOrDefault("capitalYn", "");    // N:대소문자 구분
                    ignoreSpaceYn = (String) jsonData.get(i).getOrDefault("ignoreSpaceYn", "");// Y:스페이스 체크
                    answerList = (List<Map<String, String>>) jsonData.get(i).get("answerList");
                    smtAnwVl = smtAnwVlArr[i];
					smtAnwVlReverse = smtAnwVlArrReverse[i];
                    if (answerList.size() > 1) {//복수 정답인 경우
                        result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVl, result, answerList, qpQstTypCd);
                        if ("N".equals(result)) {//제출 된 답변 중 한 개라도 틀리면 오답 처리
							if ("61".equals(qpQstTypCd)) {// 단답무순형인 경우 한번 더 체크
								result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVlReverse, result, answerList, qpQstTypCd);
								if ("N".equals(result)) {
									return result;
								}
							} else {
								return result;
							}
                        }
                    } else {
                        answer = replaceAndLower(capitalYn, ignoreSpaceYn, answerList.get(0).get("answer"));// 문통 정답 대, 소 문자 구분 처리
                        answer = replaceAnswer(answer);                                                     // 문통 특수 문자 변환 처리
                        smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVl);                     // 제출 답변 대, 소 문자 구분 처리
                        result = checkAnswer(smtAnwVl, answer);                                             // 정답 여부
                        if ("N".equals(result)) {//제출 된 답변 중 한 개라도 틀리면 오답 처리
							if ("61".equals(qpQstTypCd)) {// 단답무순형인 경우 한번 더 체크
								result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVlReverse, result, answerList, qpQstTypCd);
								if ("N".equals(result)) {
									return result;
								}
							} else {
								return result;
							}
                        }
                    }
                }
            }
        }
        return result;
    }

    public String replaceAnswer(String answer) {
    	if (answer != null) {
    		answer = answer.trim();
    	}
        if ("①".equals(answer)) {
            answer = "1";
        } else if ("②".equals(answer)) {
            answer = "2";
        } else if ("③".equals(answer)) {
            answer = "3";
        } else if ("④".equals(answer)) {
            answer = "4";
        } else if ("⑤".equals(answer)) {
            answer = "5";
        } else if ("ⓐ".equals(answer)) {
            answer = "a";
        } else if ("ⓑ".equals(answer)) {
            answer = "b";
        } else if ("ⓒ".equals(answer)) {
            answer = "c";
        } else if ("ⓓ".equals(answer)) {
            answer = "d";
        } else if ("ⓔ".equals(answer)) {
            answer = "e";
        } else if ("ⓕ".equals(answer)) {
            answer = "f";
        } else if ("㉮".equals(answer)) {
            answer = "가";
        } else if ("㉯".equals(answer)) {
            answer = "나";
        } else if ("㉰".equals(answer)) {
            answer = "다";
        } else if ("㉱".equals(answer)) {
            answer = "라";
        } else if ("㉲".equals(answer)) {
            answer = "마";
        } else if ("㉠".equals(answer)) {
            answer = "ㄱ";
        } else if ("㉡".equals(answer)) {
            answer = "ㄴ";
        } else if ("㉢".equals(answer)) {
            answer = "ㄷ";
        } else if ("㉣".equals(answer)) {
            answer = "ㄹ";
        } else if ("㉤".equals(answer)) {
            answer = "ㅁ";
        } else if ("㉥".equals(answer)) {
            answer = "ㅂ";
        } else if ("㉦".equals(answer)) {
            answer = "ㅅ";
        } else if ("㉧".equals(answer)) {
            answer = "ㅇ";
        } else if ("(A)".equals(answer)) {
            //answer = "A";
        } else if ("(B)".equals(answer)) {
            //answer = "B";
        } else if ("(C)".equals(answer)) {
            //answer = "C";
        } else if ("(D)".equals(answer)) {
            //answer = "D";
        } else if ("(E)".equals(answer)) {
            //answer = "E";
        }

        return answer;
    }

    private String getAnswerYn(String capitalYn, String ignoreSpaceYn, String smtAnwVl, String result, List<Map<String, String>> answerList, String qpQstTypCd) {
        String answer;
        for (Map<String, String> map : answerList) {
            answer = replaceAndLower(capitalYn, ignoreSpaceYn, map.get("answer")); //정답 대,소 문자, 스페이스 구분 처리
            answer = replaceAnswer(answer);                                      //특수 문자 변환 처리
            smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVl);          //답변 대,소 문자, 스페이스 구분 처리
            result = checkAnswer(smtAnwVl, answer);                               //정답 확인
            if ("Y".equals(result)) {
                return result;
            }
        }
        return result;
    }

    public String checkAnswer(String smtAnwVl, String answer) {
    	if(smtAnwVl==null || "".equals(smtAnwVl)) {
    		return "N";
    	}
		//답안에 전각이 존재하면, 반각 변환 후, 비교
		return smtAnwVl.equals(HalfWidthUtils.normalize2HalfWidth(answer)) ? "Y" : "N";
		// if (sanitizeSingleQuote(smtAnwVl.trim()).equals(sanitizeSingleQuote(answer.trim()))) {
		// 	return "Y";
		// } else {
		// 	return "N";
		// }
    }
    
    // 어퍼스트로피(작은따옴표)가 특수문자나 유니코드로 들어가있는 경우 키보드로 입력 가능한 어퍼스트로피(')로 치환한다.
    private String sanitizeSingleQuote(String raw) {
    	return raw.replace("’", "'").replace("\u0027", "'").replace("\\u0027", "'")
    			.replace("＝", "=").replace("\u003d", "=").replace("\\u003d", "=");
    }

    public String replaceAndLower(String capitalYn, String ignoreSpaceYn, String val) {
        if ("N".equals(capitalYn)) {//대소문자 구분
        	// 알파벳 특문이 소문자로 치환되지 않도록
	        if (!("Ⓐ".equals(val) || "Ⓑ".equals(val) || "Ⓒ".equals(val) || "Ⓓ".equals(val) || "Ⓔ".equals(val) || "Ⓕ".equals(val))) {
	            val = val.toLowerCase();
	        }
        }
        if ("Y".equals(ignoreSpaceYn)) {//스페이스 구분
            val = val.replaceAll(" ", "");
        }

        return val;
    }


    public List<Map<String, Object>> jsonToMap(String json) {
    	List<Map<String, Object>> list = null;
		ObjectMapper objectMapper = new ObjectMapper();
    	try {
    		list =  objectMapper.readValue(json, new TypeReference<List<Map<String, Object>>>() {});
    	} catch (JsonProcessingException e) {
    		log.debug("JsonProcessingException ");
    	}
    	
    	return list;
    }
    
    /**
     * 문항 개별 정답여부 조회
     *
     * @param param
     * @return Map<String, String>
     */
    @Transactional
    public Map<String, String> selectQtmCansYn(InsertEaEvQtmAnwReqDto param) {
        Map<String, String> map = new HashMap<>();
        	
        map.put("status", "400");
        if(param.getIsExternal() == null) {
        	map.put("success", "N");
        	map.put("message", "Invalid isExternal in Request Parameter");
        	return map;
        }
      	
        if(param.getQtmId() == null) {
        	map.put("success", "N");
        	map.put("message", "Invalid qtmId in Request Parameter");
        	return map;
        }
        
        if(param.getEvId() == null) {
        	map.put("success", "N");
        	map.put("message", "Invalid evId in Request Parameter");
        	return map;
        }        

        // 정답 html 변환된 특수문자 처리
        String smtAnwVlNew = "";
        smtAnwVlNew = param.getSmtAnwVl();
        smtAnwVlNew = smtAnwVlNew.replaceAll("&gt;", ">");
        smtAnwVlNew = smtAnwVlNew.replaceAll("&lt;", "<");
        smtAnwVlNew = smtAnwVlNew.replaceAll("&amp;", "&");
		//답변 전각 -> 반각 처리
		smtAnwVlNew = HalfWidthUtils.normalize2HalfWidth(smtAnwVlNew);
        param.setSmtAnwVl(smtAnwVlNew);


        // 외주 문항여부는 정답정보 전달해줌
        if(param.getIsExternal() != null &&  param.getIsExternal().equals("Y")){
            param.setCansYn(param.getIsCorrect());//정답 여부
        }
        else {
            // 문통 문항 정답정보 조회
            Map<String, Object> answer = commonDao.select(MAPPER_NAMESPACE + "selectAnswer", param);//문항 정답 조회
            param.setCansYn(getCansYn(param.getSmtAnwVl(), answer));//정답 여부 확인
        }

        map.put("status", "200");
        map.put("success", "Y");
        map.put("cansYn", param.getCansYn());

        return map;
    }    


    /**
     * 평가 문항정보 리스트 조회
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
	@Caching(evict = {
			@CacheEvict(cacheNames = "shortCache", key = "'al:' + #eaEvComDto.optTxbId + ':selectEnMluList:' + #eaEvComDto.usrId", cacheManager = "aidtCacheManager"),
			@CacheEvict(cacheNames = "shortCache", key = "'al:' + #eaEvComDto.optTxbId + ':selectMluLstStuInq:' + #eaEvComDto.usrId", cacheManager = "aidtCacheManager"),
			@CacheEvict(cacheNames = "shortCache", key = "'al:' + #eaEvComDto.optTxbId + ':selectIansQtmSeUgCntEn:' + #eaEvComDto.usrId", cacheManager = "aidtCacheManager"),
			@CacheEvict(cacheNames = "shortCache", key = "'al:' + #eaEvComDto.optTxbId + ':selectIansQtmSeUgCntMa:' + #eaEvComDto.usrId", cacheManager = "aidtCacheManager")
	})
    public Map<String, Object> selectEaEvQtmList(EaEvComQtmReqDto eaEvComDto) {

    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	eaEvComDto.setBucketUrl(objUrl);

        //평가 정보 조회
        Map<String, Object> map = commonDao.select(MAPPER_NAMESPACE + "selectEaEvList", eaEvComDto);
        if (map != null && !map.isEmpty()) {
            //문항 list 조회
            map.put("qtmList", commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvQtmList", eaEvComDto));
        }

        return map;
    }

    /**
     * 문항정보 단건 조회
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    public Map<String, Object> selectQtmInfo(EaEvComQtmReqDto eaEvComDto) {

    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	eaEvComDto.setBucketUrl(objUrl);


        //문항 단건 조회
        return commonDao.select(MAPPER_NAMESPACE + "selectQtmInfo", eaEvComDto);
    }


    /**
     * 평가 보충/심화 문항정보 리스트 조회
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    public List<Map<String, Object>> selectEaEvSppNtnQtmList(EaEvComQtmReqDto eaEvComDto) {

    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	eaEvComDto.setBucketUrl(objUrl);

        return commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvSppNtnQtmList", eaEvComDto);
    }

    /**
     * 평가 - 채점완료 - 보충/심화 문항답변목록 조회 요청
     *
     * @param eaEvComDto
     * @return List<EaEvComQtmAnwDto>
     */
    public List<EaEvComQtmAnwDto> selectEvSppNtnQtmAnwList(EaEvComQtmReqDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEvSppNtnQtmAnwList", eaEvComDto);
    }



    /**
     * 학생 - 평가 리포트 - 응시회차 리스트 조회 요청
     *
     * @param eaEvComDto
     * @return EaEvComStuDto
     */
    public List<EaEvMainResDto> selectEvTxmPnList(EaEvComQtmReqDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEvTxmPnList", eaEvComDto);
    }

	/**
	 * 공통 최대 응시회차 조회
	 *
	 * @param eaEvComDto
	 * @return
	 */
	public List<EaEvMainResDto> selectEvRtxmPn(EaEvComQtmReqDto eaEvComDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectMaxEvRtxmPn", eaEvComDto);
	}

    /**
     * 학생 - 평가리포트 조회 요청 > 평가 메인 정보
     *
     * @param eaEvComDto
     * @return EaEvComStuDto
     */
    public Map<String, Object> selectEvRptStuEvMainList(EaEvComQtmReqDto eaEvComDto) {
    	return commonDao.select(MAPPER_NAMESPACE + "selectEvRptStuList", eaEvComDto);
    }  
    
    /**
     * 학생 - 평가 리포트 조회 요청 > 수학
     *
     * @param eaEvComDto
     * @return EaEvComStuDto
     */
    public Map<String, Object> selectEvRptStuMaList(EaEvComQtmReqDto eaEvComDto) {
        Float cansRtEv = 0.0f;    	
		String posterPath = "";
		String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	String evDtlDvCd = "";

		Map<String, Object> rptInfo = new HashMap<>();

    	//평가 정보 조회
        Map<String, Object> evInfo = commonDao.select(MAPPER_NAMESPACE + "selectEvRptStuList", eaEvComDto);
        rptInfo.put("evInfo", evInfo);
        
    	if(evInfo != null && !evInfo.isEmpty())
		{
    		//evDtlDvCd = evInfo.get("evDtlDvCd").toString();
    		evDtlDvCd = MapUtils.getString(evInfo, "evDtlDvCd", "");
    		//aEvComDto.setTrmDvCd(evInfo.get("trmDvCd").toString());
    		eaEvComDto.setTrmDvCd(MapUtils.getString(evInfo, "trmDvCd"));
    		//cansRtEv = Float.valueOf(evInfo.get("cansRt").toString());
    		cansRtEv = MapUtils.getFloat(evInfo, "cansRt",0f);
    		
    	}    	
    	
        //문항별정오현황
//    	rptInfo.put("qtmOxList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptQtmAnwList", eaEvComDto));
    	

    	//내용체계 영역별 현황 > 수학(학기초) 해당
    	if(evDtlDvCd.equals("ST")) {
        	//교육표준체계 내용영역별 현황
        	List<Map<String, Object>> eduAreaList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaList", eaEvComDto);
        	rptInfo.put("eduAreaList", eduAreaList);

        	List<Map<String, Object>> tpcCtnList = new ArrayList<>();
        	List<Map<String, Object>> tpcCtnListTmp = new ArrayList<>();
        	
        	tpcCtnListTmp = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaLrnAtvCtnList", evInfo);
        	
        	for (Map<String, Object> area : eduAreaList) {
        		Float cansRtArea = Float.valueOf(area.get("cansRt").toString());
        		//Integer.parseInt(area.get("cansRt").toString());
        		String eduAreaCd = area.get("eduAreaCd").toString();
        		
        		if(cansRtArea < 60) {        		
        			tpcCtnList.addAll((List<Map<String, Object>>) tpcCtnListTmp.stream().filter(e -> e.get("eduAreaCd").equals(eduAreaCd)).collect(Collectors.toList()));
        		}
     	   	}
        	
        	//추천학습리스트 > 교과학습노드 > 콘텐츠 전용 > 학생공통
			for (Map<String, Object> tpcCtn : tpcCtnList) {
				posterPath = "";
				if(tpcCtn.get("cdnPthNm") != null && tpcCtn.get("cdnPthNm").toString().length() > 0) {
					posterPath = objUrl + tpcCtn.get("cdnPthNm").toString()+ "images/poster.png";
				}

				tpcCtn.put("cdnPthNm", posterPath);
			}
			
			rptInfo.put("eduAreaTpcCtnList", tpcCtnList);
        	
        	/*
        	//추천학습리스트 > 토픽 > 지식맵 콘텐츠 전용
        	//List<Map<String, Object>> tpcCtnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaTpcCtnList", evInfo);

			for (Map<String, Object> tpcCtn : tpcCtnList) {
				posterPath = "";
				if(tpcCtn.get("aiLrnAtvCdnPth") != null && tpcCtn.get("aiLrnAtvCdnPth").toString().length() > 0) {
					posterPath = objUrl + tpcCtn.get("aiLrnAtvCdnPth").toString()+ "images/poster.png";
				}

				tpcCtn.put("aiLrnAtvCdnPth", posterPath);
			}
			*/
    	}
    	//주제유형별 분석 현황 > 수학(단원진단) 해당 => 내용체계 영역별과 화면 동일 (토픽으로 조회)
    	else if(evDtlDvCd.equals("UD")) {
        	////주제유형별 분석 현황 => 20240809 단원진단 토픽매핑불가로 내용체계 영역 삭제
        	//List<Map<String, Object>> eduAreaList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaThemeTpcList", eaEvComDto);
        	//rptInfo.put("eduAreaList", eduAreaList);

    		// 내용영역별 정답률이 80 미만인 콘텐츠를 조회해야 되는데
    		// 단원진단은 내용영역이 하나라서 전체 정답률과 동일하다고 봐도 무방함.
        	// 추천학습리스트 > 교과학습노드 > 콘텐츠 전용 > 학생공통
        	if(cansRtEv < 80) {
	        	List<Map<String, Object>> tpcCtnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaLrnAtvCtnUDList", evInfo);
				for (Map<String, Object> tpcCtn : tpcCtnList) {
					posterPath = "";
					if(tpcCtn.get("cdnPthNm") != null && tpcCtn.get("cdnPthNm").toString().length() > 0) {
						posterPath = objUrl + tpcCtn.get("cdnPthNm").toString()+ "images/poster.png";
					}
	
					tpcCtn.put("cdnPthNm", posterPath);
				}
				
				rptInfo.put("eduAreaTpcCtnList", tpcCtnList);
        	}

			/*
			//추천학습리스트 > 토픽 > 지식맵 콘텐츠 전용
			List<Map<String, Object>> tpcCtnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaThemeTpcCtnList", eaEvComDto);
			for (Map<String, Object> tpcCtn : tpcCtnList) {
				posterPath = "";
				if(tpcCtn.get("aiLrnAtvCdnPth") != null && tpcCtn.get("aiLrnAtvCdnPth").toString().length() > 0) {
					posterPath = objUrl + tpcCtn.get("aiLrnAtvCdnPth").toString()+ "images/poster.png";
				}

)
				tpcCtn.put("aiLrnAtvCdnPth", posterPath);
			}
			*/

		}
    	//단원평가 전용 > 단원성취도 분석 > 수학 (단원평가)
    	else if(evDtlDvCd.equals("UG")) {
        	//차시별 분석
    		rptInfo.put("ugAchdChList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuUgAchdChList", eaEvComDto));
        	//성취기준별 분석
    		rptInfo.put("ugAchdBsList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuUgAchdBsList", eaEvComDto));
    	}
    	//학기말 전용 > 나의학습성장 그래프 > 수학/영어 (학기말)  > 내용체계영역의 학기초/학기말 정답률 그래프
    	else if(evDtlDvCd.equals("ET")) {
        	//교육표준체계 내용영역별 현황
        	rptInfo.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEduAreaList", eaEvComDto));
        	
    		// 나의학습성장
        	rptInfo.put("myLrnGrthList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuMyLrnGrtMaList", eaEvComDto));
    	}

        return rptInfo;
    }
    
    /**
     * 학생 - 평가 리포트 조회 요청 > 영어
     *
     * @param eaEvComDto
     * @return EaEvComStuDto
     */
    public Map<String, Object> selectEvRptStuEnList(EaEvComQtmReqDto eaEvComDto) {
		Map<String, Object> rptInfo = new HashMap<>();
		
		
    	//평가 정보 조회
        Map<String, Object> evInfo = commonDao.select(MAPPER_NAMESPACE + "selectEvRptStuList", eaEvComDto);

        //평가정보
        rptInfo.put("evInfo", evInfo);
        
//        //문항별정오현황
//    	rptInfo.put("qtmOxList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptQtmAnwList", eaEvComDto));
   	

        String evDtlDvCd = "";
    	if(evInfo != null && !evInfo.isEmpty())
		{
    		evDtlDvCd = evInfo.get("evDtlDvCd").toString();
    		eaEvComDto.setTrmDvCd(evInfo.get("trmDvCd").toString());
		}

    	//영역별 분석 > 영어 (학기초, 차기평가, 단원평가, 학기말)
    	if(!evDtlDvCd.equals("UD")) {
    		rptInfo.put("enArelyAnList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuEnArelyAnList", eaEvComDto));
    	}

    	//학기말 전용 > 나의학습성장 그래프 > 수학/영어 (학기말)  > 내용체계영역의 학기초/학기말 정답률 그래프
    	if(evDtlDvCd.equals("ET")) {
        	//나의학습성장 그래프 - 수학/영어
        	rptInfo.put("myLrnGrthList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuMyLrnGrthEnList", eaEvComDto));
    	}

    	
        return rptInfo;
    }    

    /**
     * 학생 평가리포트 > 정오답현황 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> selectEvRptStuQtmOxList(EaEvComQtmReqDto eaEvComDto) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptQtmAnwList", eaEvComDto);
    }
   
    /**
     * 학생 - 평가 리포트 단원성취도 조회 요청
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> selectEvRptStuUgAchdList(EaEvComQtmReqDto eaEvComDto) {
        if(eaEvComDto.getTabIndex() == 0)
        {
        	//차시별 분석
        	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuUgAchdChList", eaEvComDto);
        }
        else
        {
        	//성취기준별 분석
        	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuUgAchdBsList", eaEvComDto);
        }
    }
    
    /**
     * 학생 평가리포트 > 단원평가 > 단원성취도분석 > 성쥐기준별 > 학습차시ID조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> selectEvRptStuUgAchdBsChList(EaEvComQtmReqDto eaEvComDto) {
    	//차시ID 리스트
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStuUgAchdBsChList", eaEvComDto);
    }


    /**
     * 평가 - 채점완료 - 문항답변목록 조회 요청
     *
     * @param eaEvComDto
     * @return List<EaEvComQtmAnwDto>
     */
    public List<EaEvComQtmAnwDto> selectEvQtmAnwList(EaEvComQtmReqDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmAnwList", eaEvComDto);
    }


    /**
     * 학습 리포트 - 선생,학생 공통 조회
     *
     * @param param
     * @return Map<String, Object>
     */
    public Map<String, Object> selectEaLrnRptMainInfo(EaLrnRptReqDto param) {
        Map<String, Object> map = new HashMap<>();
        //학습 요약 조회
        map.put("lrnSumm", commonDao.select(MAPPER_NAMESPACE + "selectLrnSumm", param));
        //중,고등 학생일 경우 반평균 학습시간,문제 풀이 수 조회
        String yn = commonDao.select(MAPPER_NAMESPACE+"selectSchlGrdCd",param);
        map.put("grdYn",yn);//초등=N, 중 고등 = Y
        if("Y".equals(yn)){
            map.put("lrnSummAvg",commonDao.select(MAPPER_NAMESPACE+"selectLrnSummAvg",param));
        }
        //단원별 성취(정답률) 현황 조회
        map.put("achStatByUnit", commonDao.selectList(MAPPER_NAMESPACE + "selectAchStatByUnit", param));
        //AI 학습 분석 > 잘 하고 있어요! + 조금 더 노력이 필요해요! 조회
        param.setOrder("DESC");//성취도(정답률) 기준 상위 5개
        map.put("goodList", commonDao.selectList(MAPPER_NAMESPACE + "selectTcGoodBadList", param));
        param.setOrder("ASC");//성취도(정답률) 기준 하위 5개
        map.put("badList", commonDao.selectList(MAPPER_NAMESPACE + "selectTcGoodBadList", param));
        //학습 성장 분석 월별 학습시간,성취도 조회
        map.put("evTmScntList",selectMonthInfo("selectTmScntList","evTmScnt",param));
        map.put("cansRtList",selectMonthInfo("cansRtList","cansRt",param));

        return map;
    }
    //학습 성장 분석 월별 정보 조회
    public List<Integer> selectMonthInfo(String mapper,String colNm,EaLrnRptReqDto param){
        List<Integer> list = new ArrayList<>();
//        String strSmtDtm = param.getSmtDtm();
//        int smtDtm = 12;
//        if(!strSmtDtm.isEmpty())
//        {
//        	smtDtm = Integer.parseInt(strSmtDtm);
//        }

        for (int i=1;i<=12;i++){
            param.setSmtDtm(i);

            Map<String,Object> map = commonDao.select(MAPPER_NAMESPACE+mapper,param);
            if (map !=null){
                list.add(Integer.parseInt(map.get(colNm).toString()));
            }else {
                list.add(0);
            }
        }
        return list;
    }

    /**
     * 학습 리포트 - 단원별 성취 현황 - 차시별 푼 문제 + 맞힌 문제 + 정답률 조회
     *
     * @param param
     * @return Map<String, Object>
     */
    public List<Map<String, Object>> selectTcList(EaLrnRptReqDto param) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectTcList", param);
    }

    /**
     * 오답노트 결과 등록 요청
     *
     * @param param
     * @return Map<String, String>
     */
    @Transactional
    public Map<String, Object> insertLansNteQtmAnw(InsertEaEvQtmAnwReqDto param) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<Map<String,String>> qtmList = new ArrayList<>();
        if (param.getQtmList() != null && !param.getQtmList().isEmpty()){
            for (InsertEaEvQtmAnwReqDto dto : param.getQtmList()) {
                Map<String, String> qtm = new HashMap<>();
                dto.setUsrId(param.getUsrId());
                Map<String, Object> answer = commonDao.select(MAPPER_NAMESPACE + "selectAnswer", dto);//문항 정답 조회
	            if ("Y".equals(dto.getIsExternal())) {
	                dto.setCansYn(dto.getIsCorrect());//정답 여부
	            } else {
					//fixme: 오답 노트 전각 반각 처리 추가 신규 API 생성 여부 아직 미정이므로 임시 처리 ..
					if(StringUtils.isNotBlank(dto.getSmtAnwVl())) {
						String smtAnwVlNew = dto.getSmtAnwVl();
						smtAnwVlNew = smtAnwVlNew.replaceAll("&gt;", ">");
						smtAnwVlNew = smtAnwVlNew.replaceAll("&lt;", "<");
						smtAnwVlNew = smtAnwVlNew.replaceAll("&amp;", "&");
						//답변 전각 -> 반각 처리
						smtAnwVlNew = HalfWidthUtils.normalize2HalfWidth(smtAnwVlNew);
						dto.setSmtAnwVl(smtAnwVlNew);
					}
	            	dto.setCansYn(getCansYn(dto.getSmtAnwVl(), answer));//정답 여부 확인
	            }
                commonDao.update(MAPPER_NAMESPACE + "insertLansNteQtmAnw", dto);//오답노트 정답 여부 update
                qtm.put("qtmId",dto.getQtmId());
                qtm.put("cansYn",dto.getCansYn());
                qtmList.add(qtm);
            }
            map.put("status", "200");
            map.put("message", "성공");
            map.put("success", "Y");
            map.put("qtmList",qtmList);//채점 결과 정보
        }else {
            map.put("status", "400");
            map.put("message", "제출 문항 정보 없음");
            map.put("success", "N");
        }
        return map;
    }


	/**
	 * 교사 - 평가 잠금여부 설정 요청
	 *
	 * @param evReqDto
	 * @return int
	 */
	public int updateEvLockYn(EaEvUpdateLockUseYnDto evReqDto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateEvLockYn", evReqDto);
	}

	/**
	 * 교사 - 평가 사용여부 설정 요청
	 *
	 * @param evReqDto
	 * @return int
	 */
	public int updateEvUseYn(EaEvUpdateLockUseYnDto evReqDto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateEvUseYn", evReqDto);
	}

	

	/**
	 * 교사 - 원클릭학습설정 다른학급 대단원/차시 잠금/사용여부 평가 적용
	 *
	 * @param evReqDto
	 * @return int
	*/
	public List<Map<String, Object>> updateOtherClaOneClickLrnSetm(EaEvUpdateLockUseYnDto evReqDto) {
		List<Map<String, Object>> evInfoList = new ArrayList<Map<String, Object>>(); 
		
		if(evReqDto.getOptTxbIdList() == null || evReqDto.getOptTxbIdList().isEmpty())
		{
			
			throw new IllegalArgumentException("optTxbIdList luList in Request Parameter ");
		}
		
		for (EaEvUpdateLockUseYnDto updateDto : evReqDto.getOptTxbIdList()) {
			Map<String, Object> param = new HashMap<>();
			String otherOptTxbId = updateDto.getOptTxbId(); // 다른 학급운영교과서ID
			
			param.put("bcOptTxbId", evReqDto.getOptTxbId()); // 로그인한 운영교과서 아이디
			param.put("optTxbId", otherOptTxbId);
			param.put("useYn", evReqDto.getUseYn());
			param.put("lcknYn", evReqDto.getLcknYn());

			List<Map<String, Object>> updateNeedEvList = commonDao.selectList(MAPPER_NAMESPACE + "selectUpdateNeedEvList", param);

			for (Map<String, Object> evInfo : updateNeedEvList) {
				// 로그인된 운영 교과서ID로 조회한 재구성 노드 순회,
				String luLrmpNodId = evInfo.get("luLrmpNodId").toString(); // 평가 대단원ID
				String tcLrmpNodId = evInfo.get("tcLrmpNodId").toString(); // 평가 차시ID
				String nodLockYn = evInfo.get("lcknYn").toString(); 	   // 재구성 노드 잠금여부
				String nodUseYn = evInfo.get("useYn").toString();		   // 재구성 노드 사용여부
				EaEvUpdateLockUseYnDto dto = new EaEvUpdateLockUseYnDto();

                if(evReqDto.getLcknYn() != null){
                	dto.setLcknYn(nodLockYn); // 재구성 노드 잠금여부 값
                }
                
                if(evReqDto.getUseYn() != null){
                	dto.setUseYn(nodUseYn); // 재구성 노드 사용여부 값
                	dto.setLcknYn(nodLockYn); // 재구성 노드 잠금여부 값
                }
				
                dto.setOptTxbId(otherOptTxbId);// 다른 학급평가 운영교과서ID
            	dto.setLluNodId(luLrmpNodId);// 다른 학급평가 대단원ID
            	dto.setTluNodId(tcLrmpNodId);// 다른 학급평가 차시ID
            	dto.setOtherClaSaveYn("Y");// 다른 학급저장여부 -> 로그 쌓기용으로 추가
            	
            	if("".equals(tcLrmpNodId) || tcLrmpNodId == null) {
            		dto.setRltLuAllUpdYn("N");
            	}
            	
            	List<EaEvUpdateLockUseYnDto>  luList = new ArrayList<>();
            	luList.add(dto);
            	evReqDto.setLuList(luList);
            	
            	List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            	result = updateEvOneClickLrnSetm(evReqDto);// 평가 수정 후 evId, optTxbId list 리턴
            	
            	evInfoList.addAll(result);
			}
			
		}

		return evInfoList;
	}
	
	
	
	/**
	 * 교사 - 원클릭학습설정 대단원/차시 잠금/사용 여부 수정시 평가 적용
	 *
	 * @param evReqDto
	 * @return int
	*/
    @Transactional
	public List<Map<String, Object>> updateEvOneClickLrnSetm(EaEvUpdateLockUseYnDto evReqDto) {
    	List<Map<String, Object>> evInfoList = new ArrayList<Map<String, Object>>();
    	Map<String, Object> map =  new HashMap<>();
    	
		if(evReqDto.getLuList() == null || evReqDto.getLuList().isEmpty())
		{
			map.put("message", "Invalid luList in Request Parameter");
			evInfoList.add(map);
			return evInfoList;
		}

		String optTxbId = "";
		String lluNodId = "";
		String lcknYn = "";
		String useYn = "";

        for (EaEvUpdateLockUseYnDto updateDto : evReqDto.getLuList()) {
        	updateDto.setUsrId(evReqDto.getUsrId());

        	optTxbId = updateDto.getOptTxbId();
        	lluNodId = updateDto.getLluNodId();
        	lcknYn = updateDto.getLcknYn();
        	useYn = updateDto.getUseYn();
        	
        	if(optTxbId == null || optTxbId.isEmpty()) {
        		map.put("message", "Invalid optTxbId in Request Parameter ");
        		evInfoList.add(map);
        		continue;
        	}
    		if(lluNodId == null || lluNodId.isEmpty()) {
    			map.put("message", "Invalid lluNodId in Request Parameter ");
    			evInfoList.add(map);
        		continue;
        	}
        	if((lcknYn == null || lcknYn.isEmpty()) && (useYn == null || useYn.isEmpty())) {
        		map.put("message", "Invalid lcknYn or useYn in Request Parameter ");
        		evInfoList.add(map);
        		continue;
        	}

        	commonDao.update(MAPPER_NAMESPACE + "updateEvOneClickLrnSetm", updateDto);
        	List<Map<String, Object>> updatedEvIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectUpdatedEvInfo", updateDto);
        		
        	
        	if(updateDto.getOtherClaSaveYn() != null && "Y".equals(updateDto.getOtherClaSaveYn())) {
        		// 원클릭 학습설정에서 다른학급 저장을 통한 수정 로그저장 
        		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
            	
            	CmClaCpLogDto logDto = CmClaCpLogDto.builder()
            						   .optTxbId(userDetails.getOptTxbId()) // 로그인한 운영교과서ID
            						   .cpOptTxbId(optTxbId) // 수정하는 운영교과서ID
            						   .cpDvCd("EV")
            						   .cpPrcsYn("Y")
            						   .backendFlePth("EaEvComService.updateEvOneClickLrnSetm")
            						   .kerisUsrId(userDetails.getKerisUsrId())
            						   .crtrId(userDetails.getUsrId())
            						   .build();
            	
            	bcCmService.insertClaCpLog(logDto);
        	}
        	
        	evInfoList.addAll(updatedEvIdList);
		}


		return evInfoList;
	}

	/**
	 * 교사 - 원클릭학습설정 대단원/차시 잠금/사용 여부 수정시 평가 적용
	 *
	 * @param evReqDto
	 * @return int
	*/
    @Transactional
	public int updateEvOneClickLrnSetmAL(EaEvUpdateLockUseYnDto evReqDto) {
		if(evReqDto.getEvList() == null || evReqDto.getEvList().isEmpty())
		{
			throw new IllegalArgumentException("Invalid evList in Request Parameter ");
		}

		String lcknId = "";
		String useYn = "";

        for (EaEvUpdateLockUseYnDto updateDto : evReqDto.getEvList()) {
        	updateDto.setUsrId(evReqDto.getUsrId());

        	lcknId = updateDto.getLcknYn();
        	useYn = updateDto.getUseYn();

        	if(Objects.isNull(updateDto.getEvId()) || updateDto.getEvId() < 1) {
        		throw new IllegalArgumentException("Invalid evId in Request Parameter ");
        	}

        	if((lcknId == null || lcknId.isEmpty()) && ( useYn == null || useYn.isEmpty())) {
        		throw new IllegalArgumentException("Invalid lcknYn or useYn in Request Parameter ");
        	}

        	commonDao.update(MAPPER_NAMESPACE + "updateEvOneClickLrnSetmAL", updateDto);
		}


		return 1;
	}

    /**
     * 교사 > 학생 분석 상세 > 학생 리스트 조회
     *
     * @param null
     * @return Map<String, Object>
     */
    public List<Map<String, Object>> selectUserList(String claId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectUserList", claId);
    }

	/**
	 * LCMS > LMS 이관 후 LMS 평가 생성
	 *
	 * @param String usrId, String optTxbId, String txbId, String dbId
	 * @return int
	 */
    @Transactional
	public int createLcmsEv(String usrId, String optTxbId, String txbId, String claId, String dbId) {

		Map<String, Object> param = new HashMap<>();
		param.put("usrId", usrId);
		param.put("optTxbId", optTxbId);
		param.put("txbId", txbId);
		param.put("claId", claId);
		param.put("dbId", dbId);

		int res = 0;

		List<Map<String, Object>> evshList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvFromLcmsEv", param);
		if(evshList == null || evshList.isEmpty()) {
			return res;
		}


		param.put("evshList", evshList);
		
		try {
			res = commonDao.insert(MAPPER_NAMESPACE + "insertEvFromLcmsEv", param);
		} catch(DuplicateKeyException dke) {
			log.error("Duplicate error (평가 데이터 insert) >>> " + param.toString());
			log.error(dke.getMessage());
		}

		if(res > 0) {
			try {
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvQtmFromLcmsEv", param);
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (평가 문항 데이터 insert) >>> " + param.toString());
				log.error(dke.getMessage());
			}
			
			try {
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvDffdFromLcmsEv", param);
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (평가 난이도구성 데이터 insert) >>> " + param.toString());
				log.error(dke.getMessage());
			}
			
			try {
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRngeFromLcmsEv", param);
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (평가 시험범위 데이터 insert) >>> " + param.toString());
				log.error(dke.getMessage());
			}
			
			try {
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvRsFromLcmsEv", param);
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (평가 결과 데이터 insert) >>> " + param.toString());
				log.error(dke.getMessage());
			}
			
			try {
				res += commonDao.insert(MAPPER_NAMESPACE + "updateSbcLrnAtvEvidFromLcmsEv", param);
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (평가 id 업데이트) >>> " + param.toString());
				log.error(dke.getMessage());
			}

//			res += commonDao.insert(MAPPER_NAMESPACE + "updateEvQstCntFromLcmsEv", param);
		}
		
		
		// 평가 결과 테이블에 없는 학생정보 등록 
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvRsLoginAddStu", param);
		

		return res;
	}
    
	/**
	 * 학생, 교사 로그인 시 평가결과 추가 등록
	 *
	 * @param String usrId, String optTxbId
	 * @return int
	 */
    @Transactional
	public int insertEvRsLoginAddStu(String usrId, String optTxbId) {

		Map<String, Object> param = new HashMap<>();
		param.put("usrId", usrId);
		param.put("optTxbId", optTxbId);

		int res = 0;
		
		// 평가 결과 테이블에 없는 학생정보 등록 
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvRsLoginAddStu", param);

		return res;
	}
    

	/**
	 * 학습창 평가 문항 노트 조회
	 *
	 * @param eaEvComDto
	 * @return
	 */
	public List<Map<String, Object>> selectEaEvQtmNote(EaEvComQtmReqDto eaEvComDto) {
		String path = eaEvComDto.getAnnxFlePthNm();
		try {
			return BcCmUtil.s3JsonFileReader(BUCKET_NAME, path);
		} catch (IOException e) {
			log.debug("selectEaEvQtmNote:: s3JsonFileReader:: ", e);
			return Collections.emptyList();
		}
	}
	
	/**
     * 평가완료 후 학습자수준 업데이트
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    public int updateUsrLrnrVelTpCd(String usrId) {
        return commonDao.update(MAPPER_NAMESPACE + "updateUsrLrnrVelTpCd", usrId);
    }
    
	/**
     * 평가완료 답변 제출 시 단원별 학습수준 업데이트
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    public int updateTlLuLrnrLv(Map<String, Object> luLv) {

    	List<Map<String, Object>> luLvList = (List<Map<String, Object>>) luLv.get("luLvList");
        for (Map<String, Object> updateDto : luLvList) {
        	
        	updateDto.put("usrId", luLv.get("usrId"));
        	updateDto.put("optTxbId", luLv.get("optTxbId"));
        	updateDto.put("dbId", luLv.get("dbId"));

        	commonDao.update(MAPPER_NAMESPACE + "updateTlLuLrnrLv", updateDto);
		}
        
        return 1;
    }

    
	/**
	 * 평가완료 답변 제출 시 단원별 학습수준 조회
	 *
	 * @param eaEvComDto
	 * @return
	 */
	public List<Map<String, Object>> selectLluLrnrLvList(Map<String, Object> luLv) {        
        return commonDao.selectList(MAPPER_NAMESPACE + "selectLluLrnrLvList", luLv);
	}    
	
    /**
     * 평가완료 후 풀이상태 업데이트
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    public int updateEvQtmAnwXplStCd(String usrId, long evId) {
		Map<String, Object> param = new HashMap<>();
		param.put("usrId", usrId);
		param.put("evId", evId);

//		//응시회차 조회
//		Map<String, Object> ev = commonDao.select(MAPPER_NAMESPACE + "selectEvRtxmPn", param);
//
//        int txmPn = Integer.parseInt(ev.get("txmPn").toString());
//        param.put("txmPn", txmPn);
        
        return commonDao.update(MAPPER_NAMESPACE + "updateEvQtmAnwXplStCd", param);
    }
    
    
   /**
    * 교사 메인 평가 성쥐기준별 조회 
    * @param eaEvComDto
    * @return
    */
    public List<Map<String, Object>> selectEvRptTcrAchdBsList(EaEvComQtmReqDto eaEvComDto) {
        
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptTcrAchdBsList", eaEvComDto);
        
    }
    /**
     * 교사 메인 평가 AI 조회 
     * @param eaEvComDto
     * @return
     */
    public List<Map<String, Object>> selectEvRptTcrAiList(EaEvComQtmReqDto eaEvComDto) {
    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	eaEvComDto.setBucketUrl(objUrl);    	
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptTcrAiList", eaEvComDto);
    	
    }
    /**
     * 교사 메인 평가 현황 조회 
     * @param eaEvComDto
     * @return
     */
    public List<Map<String, Object>> selectEvRptTcrState(EaEvComQtmReqDto eaEvComDto) {
    	
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptTcrState", eaEvComDto);
    	
    }

    public void saveMyhmPoint(String accessToken, InsertEaEvQtmAnwReqDto param) {
        InsertEaEvQtmAnwReqDto ev = commonDao.select(MAPPER_NAMESPACE + "selectEvCmplYn", param);
        String myhmPointCode = this.getMyhmPointCode(ev.getEvDvCd(), ev.getEvDtlDvCd());

        if(myhmPointCode == null) return;

        //마이홈 포인트 적립
        try {
            this.callMyhmApi(accessToken, Map.of(
                    "pntCd", myhmPointCode,
                    "pntChkBsVl", String.valueOf(ev.getEvId())));
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("AI 마이홈 포인트 적립실패");
        }
    }

    private Map<String, Object> callMyhmApi(String accessToken, Map<String,String> paramMap) throws JsonProcessingException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Authorization", "Bearer " + accessToken);
        httpHeaders.add("Content-Type", "application/json");

        String jsonString = new ObjectMapper().writeValueAsString(paramMap);
        String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
        return CoreUtil.Json.jsonString2Map(post);
    }

    private String getMyhmPointCode(String evDvCd, String evDtlDvCd) {
        if(StringUtils.isEmpty(evDvCd) || StringUtils.isEmpty(evDtlDvCd) || !evDvCd.equalsIgnoreCase("SE"))
            return null;

        if(evDtlDvCd.equalsIgnoreCase("ST")) {
            return "EV_CC_01";
        } else if(evDtlDvCd.equalsIgnoreCase("UD")) {
            return "EV_CC_02";
        } else if(evDtlDvCd.equalsIgnoreCase("UG")) {
            return "EV_CC_03";
        } else if(evDtlDvCd.equalsIgnoreCase("ET")) {
            return "EV_CC_04";
        } else if(evDtlDvCd.equalsIgnoreCase("TO") || evDtlDvCd.equalsIgnoreCase("FO")) {
            return "SB_CC_02";
        }

        return null;
    }

    /**
     * 평가의 현재 회차
     * @param eaEvComDto
     * @return 평가의 현재 회차
     */
	public String selectCurrentTxmPn(EaEvComQtmReqDto eaEvComDto) {
		// 오답/심화 문제 생성해야 하는 평가인지 여부
		String sppNtnYn = getSppNtnYn(eaEvComDto);
		if (!"N".equals(sppNtnYn) && !"Y".equals(sppNtnYn)) {
			return sppNtnYn;
		}
		eaEvComDto.setSppNtnYn(sppNtnYn);
		return commonDao.select(MAPPER_NAMESPACE + "selectCurrentTxmPn", eaEvComDto);
	}
	
	/**
	 * 오답/심화 문제 생성해야 하는 평가인지 여부
	 * @param evId
	 * @return 오답/심화 문제를 생성해야하는 평가이면 Y 아니면 N, *교사이면서 재응시인 경우는 최신 회차
	 */
	private String getSppNtnYn(EaEvComQtmReqDto eaEvComDto) {
		int evId = (int)eaEvComDto.getEvId();
		InsertEaEvQtmAnwReqDto param = new InsertEaEvQtmAnwReqDto();
		param.setEvId(evId);
		param.setUsrId(eaEvComDto.getUsrId());
        InsertEaEvQtmAnwReqDto ev = commonDao.select(MAPPER_NAMESPACE + "selectEvCmplYn", param);
        String evDtlDvCd = ev.getEvDtlDvCd();
        // 형성/차시 평가가 아니면 패스
        if(!"FO".equals(evDtlDvCd) && !"TO".equals(evDtlDvCd)) 
        { 
        	return "N";
        }
        // 중고등 영어 패스
        if("EN".equals(ev.getSbjCd()) && !"E".equals(ev.getSchlGrdCd()))
        {
        	return "N";
        }      
		// 학생인데 기본 문제 제출 완료만 하고 오답심화문제 풀지 않은 경우
		if ("ST".equals(eaEvComDto.getUsrTpCd()) && "Y".equals(ev.getEvCmplYn())) {
			if (countSppNtnAnw(eaEvComDto) == 0) {
				return "N";
			}
		}
		// 교사인데 재응시인 경우는 문제를 오답 심화문제 여부와 상관없이 문항 클리어
		if ("TE".equals(eaEvComDto.getUsrTpCd())) {
			if (ev.getTxmPn() != 0) {
				return ev.getTxmPn() + 1 + "";
			}
			return "N";
		}
		return "Y";
	}
	
	/**
	 * 오답/심화 문제 제출한 답안 수
	 * @param eaEvComDto
	 * @return 오답/심화 문제 제출한 답안 수
	 */
	private int countSppNtnAnw(EaEvComQtmReqDto eaEvComDto) {
		return commonDao.select(MAPPER_NAMESPACE + "countSppNtnAnw", eaEvComDto);
	}
	
	/**
	 * 평가완료 후 국가표준쳬계 교육과정에 포함된 문항들의 진척율 조회
	 * @param eaEvComDto
	 * @return 완료한 평가의 국가표준쳬계 교육과정에 포함된 문항들의 진척율 리스트
	 */	
    public List<Map<String, Object>> selectEvCmplCrclCtnElm2CdList(EaEvComQtmReqDto eaEvComDto) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvCmplCrclCtnElm2CdList", eaEvComDto);
    }	

	/**
	 * 평가 목록 학습창 호출 시 응시 정보 조회
	 * @param eaEvComDto
	 * @return 평가 목록 학습창 호출 시 응시 정보 조회
	 */	
    public InsertEaEvQtmAnwReqDto selectEvTxmInfo(InsertEaEvQtmAnwReqDto eaEvComDto) {
    	return commonDao.select(MAPPER_NAMESPACE + "selectEvCmplYn", eaEvComDto);
    }

	/**
	 * 평가 문항에 연결된 노트 정보 업데이트
	 * @param UpdateNoteReqDto
	 * @return 평가 문항에 연결된 노트 정보 업데이트 성공 여부 결과
	 */
	public Map<String, String> updateNoteStatus(UpdateNoteReqDto param) {
		Map<String, String> resultMap = new LinkedHashMap<>();

		// 1. 첨부 관련 테이블 데이터 삭제
		long annxId = param.getAnnxId();

		int deleteAnnx = commonDao.delete(MAPPER_NAMESPACE + "deleteAnnx", annxId);
		resultMap.put("deleteAnnx", deleteAnnx > 0 ? "성공" : "실패");
		int deleteAnnxFle = commonDao.delete(MAPPER_NAMESPACE + "deleteAnnxFle", annxId);
		resultMap.put("deleteAnnxFle", deleteAnnxFle > 0 ? "성공" : "실패");

		// 2. s3 파일 삭제
		AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
		String annxFlePthNm = param.getAnnxFlePthNm();

		try {
			boolean deleteS3File = BcCmUtil.deleteS3File(s3, BUCKET_NAME, annxFlePthNm);
			resultMap.put("s3FileDelete", deleteS3File ? "성공" : "실패");
		} catch (AmazonS3Exception e) {
			log.error("ev note file delete AmazonS3Exception error... >>> {}", e.getMessage());
			resultMap.put("s3FileDelete", "실패");
		} catch (SdkClientException e) {
			log.error("ev note file delete SdkClientException error... >>> {}", e.getMessage());
			resultMap.put("s3FileDelete", "실패");
		}

		s3.shutdown();

		// 3. 평가 문항 답변 첨부파일 데이터 삭제
		String mapperKey = "";

		if (param.getRtxmPn() == 0) {
			// 일반 평가
			if (param.getIsExtra()) {
				// 유사/심화 문항
				mapperKey = "updateEaEvSppNtnQtmAnwAnnxId";
			} else {
				// 일반 문항
				mapperKey = "updateEaEvQtmAnwAnnxId";
			}
		} else {
			// 재응시 평가
			mapperKey = "updateEaEvQtmAnwRtxmAnnxId";
		}

		int updateQtmAnwAnnxId = commonDao.update(MAPPER_NAMESPACE + mapperKey, param);
		resultMap.put(mapperKey, updateQtmAnwAnnxId > 0 ? "성공" : "실패");

		return resultMap;
	}

}
