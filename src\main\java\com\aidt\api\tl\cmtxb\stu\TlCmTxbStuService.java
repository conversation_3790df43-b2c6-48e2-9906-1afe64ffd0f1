/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-08 14:46:51
 * @modify date 2024-02-08 14:46:51
 * @desc TlCmTxb 교과학습 공통처리API(학생)
 */
package com.aidt.api.tl.cmtxb.stu;

import java.util.*;
import java.util.stream.Collectors;

import com.aidt.api.tl.cmtxb.TlCmTxbCacheService;
import com.aidt.api.tl.cmtxb.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.common.CommonDao;

@Service
public class TlCmTxbStuService {
    private final String MAPPER_NAMESPACE = "api.tl.cmtxb.stu.";

    @Autowired
    private CommonDao commonDao;

    @Autowired
    private TlCmTxbCacheService tlCmTxbCacheService;
    
    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
    
    /** 
     * 대단원 정보 조회
     * @param srhDto
     * @return List<TlCmTxbLluDto>
     */
    @Transactional(readOnly = true)
    public List<TlCmTxbLluDto> selectTxbLluList(TlCmTxbSrhDto srhDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectTxbLluList", srhDto);
    }

    /**
     * 차시 정보 조회 
     * @param srhDto
     * @return List<TlCmTxbLluDto>
     */
    @Transactional(readOnly = true)
//    @Cacheable(
//    	    cacheNames = "longCache",
//    	    key="'tl:' + #srhDto.optTxbId + ':txbTcList:' + #srhDto.lrnUsrId",  // method의 파라미터 변수로 사용합니다. (교과:운영교과서ID:차시목록 - 운영교과서별 차시목록을 선생님, 학생 모두 동일한 결과값을 사용)
//    	    condition="'Y'.equals(#srhDto.withLrnLuYn)",
//    	    unless = "#result.size() == 0",                 // 결과값이 없을 경우 캐싱하지 않음
//    	    cacheManager = "aidtCacheManager")
	public List<TlCmTxbLluDto> selectTxbTcList(TlCmTxbSrhDto srhDto) {
    	if (srhDto.getWithLrnStYn() == null || "".equals(srhDto.getWithLrnStYn())) {
    		srhDto.setWithLrnStYn("N");
    	}
    	if (srhDto.getWithLrnLuYn() == null || "".equals(srhDto.getWithLrnLuYn())) {
    		srhDto.setWithLrnLuYn("N");
    	}
    	List<TlCmTxbLluDto> getTxbLluList = new ArrayList<TlCmTxbLluDto>();
    	List<TlCmTxbLluDto> txbLluList = new ArrayList<TlCmTxbLluDto>();
    	if("Y".equals(srhDto.getWithLrnStYn()) && "Y".equals(srhDto.getWithLrnLuYn())) {
    		getTxbLluList = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbTocTcList", srhDto);
    		txbLluList = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbTocLluList", srhDto);
    	} else {
    		getTxbLluList = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbTcList", srhDto);
    		txbLluList = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbLluList", srhDto);
    	}
        List<TlCmTxbTcDto> luEvList = commonDao.selectList(MAPPER_NAMESPACE + "selectLuEvList", srhDto);
        List<TlCmTxbLluDto> txbMluList = getTxbLluList.stream().filter(list -> list.getDpth() == 2).collect(Collectors.toList());
        List<TlCmTxbLluDto> txbSluList = getTxbLluList.stream().filter(list -> list.getDpth() == 3).collect(Collectors.toList());
        
        int lluIdx = 0;

        for (TlCmTxbLluDto list1 : txbLluList) { //대단원
            List<TlCmTxbTcDto> tcList = new ArrayList<>(); // child에 넣을 list
            
            
            if(srhDto.getWithLrnLuYn().equals("Y") && luEvList.size() > 0 && lluIdx == 0) { // 우리반수업 목록 페이지에서 호출 시
            	
            	for(TlCmTxbTcDto evList : luEvList) {
            		if(evList.getEvshTpCd().equals("ST") && this.checkTcList(evList)) {
            			tcList.add(this.setTcList(evList));
            		}
            	}
            	
            };
            
            getTxbLluList.stream().filter(idx3 -> idx3.getDpth() == 4 && idx3.getLluNodId().equals(list1.getLrmpNodId()))
            .forEach(list4 -> { //차시
                TlCmTxbTcDto tlCmTxbTcDto = new TlCmTxbTcDto();
                Optional <TlCmTxbLluDto> sluInfo = txbSluList.stream().filter(list->list.getLrmpNodId().equals(list4.getUrnkLrmpNodId())).findFirst();
                Optional <TlCmTxbLluDto> mluInfo = txbMluList.stream().filter(list->list.getLrmpNodId().equals(sluInfo.get().getUrnkLrmpNodId())).findFirst();
                
                tlCmTxbTcDto.setLrmpNodId2(mluInfo.get().getLrmpNodId()); //중단원 ID
                tlCmTxbTcDto.setLrmpNodNm2(mluInfo.get().getLrmpNodNm()); //중단원명
                tlCmTxbTcDto.setLrmpNodId3(sluInfo.get().getLrmpNodId()); //소단원 ID
                tlCmTxbTcDto.setLrmpNodNm3(sluInfo.get().getLrmpNodNm()); //소단원명
                tlCmTxbTcDto.setLrmpNodId4(list4.getLrmpNodId()); //차시 ID
                tlCmTxbTcDto.setLrmpNodNm4(list4.getLrmpNodNm()); //차시명
                tlCmTxbTcDto.setRcstnOrdn(list4.getRcstnOrdn()); //재구성 순서
                tlCmTxbTcDto.setRcstnNo(list4.getRcstnNo());
                tlCmTxbTcDto.setLcknYn(list4.getLcknYn()); //잠금 여부
                tlCmTxbTcDto.setUseYn(list4.getUseYn()); //사용 여부
                tlCmTxbTcDto.setLuNoUseYn(list4.getLuNoUseYn());//번호 사용 여부
                tlCmTxbTcDto.setLrnCmplYn(list4.getLrnCmplYn());
                tlCmTxbTcDto.setLrnAbleYn(list4.getLrnAbleYn());
                if(list4.getLrnSt() != null) {
                	tlCmTxbTcDto.setLrnSt(list4.getLrnSt());
                }
                if(list4.getLuEpsYn() != null) {
                	tlCmTxbTcDto.setLuEpsYn(list4.getLuEpsYn());
                }
                tlCmTxbTcDto.setLrnAtvId(String.valueOf(list4.getLrnAtvId()));
                tlCmTxbTcDto.setEvId(list4.getEvId());
                tlCmTxbTcDto.setEvDtlDvCd(list4.getEvDtlDvCd());
                tlCmTxbTcDto.setEvUseYn(list4.getEvUseYn());
                tlCmTxbTcDto.setEvLcknYn(list4.getEvLcknYn());
                tlCmTxbTcDto.setEvCmplYn(list4.getEvCmplYn());
                tlCmTxbTcDto.setEvshTpCd(list4.getEvshTpCd());
                tlCmTxbTcDto.setPcThbPth(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, list4.getPcThbPth()));
                tlCmTxbTcDto.setTaThbPth(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, list4.getTaThbPth()));
                tcList.add(tlCmTxbTcDto);
            });
            
//            getTxbLluList.stream().filter(idx1 -> idx1.getDpth() == 2 && idx1.getUrnkLrmpNodId().equals(list1.getLrmpNodId()))
//                                    .forEach(list2 -> { //중단원
//                                        getTxbLluList.stream().filter(idx2 -> idx2.getDpth() == 3 && idx2.getUrnkLrmpNodId().equals(list2.getLrmpNodId()))
//                                                                .forEach(list3 -> { //소단원
//                                                                    getTxbLluList.stream().filter(idx3 -> idx3.getDpth() == 4 && idx3.getUrnkLrmpNodId().equals(list3.getLrmpNodId()))
//                                                                                            .forEach(list4 -> { //차시
//                                                                                                TlCmTxbTcDto tlCmTxbTcDto = new TlCmTxbTcDto();
//                                                                                                tlCmTxbTcDto.setLrmpNodId2(list2.getLrmpNodId()); //중단원 ID
//                                                                                                tlCmTxbTcDto.setLrmpNodNm2(list2.getLrmpNodNm()); //중단원명
//                                                                                                tlCmTxbTcDto.setLrmpNodId3(list3.getLrmpNodId()); //소단원 ID
//                                                                                                tlCmTxbTcDto.setLrmpNodNm3(list3.getLrmpNodNm()); //소단원명
//                                                                                                tlCmTxbTcDto.setLrmpNodId4(list4.getLrmpNodId()); //차시 ID
//                                                                                                tlCmTxbTcDto.setLrmpNodNm4(list4.getLrmpNodNm()); //차시명
//                                                                                                tlCmTxbTcDto.setRcstnOrdn(list4.getRcstnOrdn()); //재구성 순서
//                                                                                                tlCmTxbTcDto.setRcstnNo(list4.getRcstnNo());
//                                                                                                tlCmTxbTcDto.setLcknYn(list4.getLcknYn()); //잠금 여부
//                                                                                                tlCmTxbTcDto.setUseYn(list4.getUseYn()); //사용 여부
//                                                                                                tlCmTxbTcDto.setLuNoUseYn(list4.getLuNoUseYn());//번호 사용 여부
//                                                                                                tlCmTxbTcDto.setLrnCmplYn(list4.getLrnCmplYn());
//                                                                                                tlCmTxbTcDto.setLrnAbleYn(list4.getLrnAbleYn());
//                                                                                                if(list4.getLrnSt() != null) {
//                                                                                                	tlCmTxbTcDto.setLrnSt(list4.getLrnSt());
//                                                                                                }
//                                                                                                if(list4.getLuEpsYn() != null) {
//                                                                                                	tlCmTxbTcDto.setLuEpsYn(list4.getLuEpsYn());
//                                                                                                }
//                                                                                                tlCmTxbTcDto.setLrnAtvId(list4.getLrnAtvId());
//                                                                                                tlCmTxbTcDto.setEvId(list4.getEvId());
//                                                                                                tlCmTxbTcDto.setEvUseYn(list4.getEvUseYn());
//                                                                                                tlCmTxbTcDto.setEvLcknYn(list4.getEvLcknYn());
//                                                                                                tlCmTxbTcDto.setEvCmplYn(list4.getEvCmplYn());
//                                                                                                tlCmTxbTcDto.setEvshTpCd(list4.getEvshTpCd());
//                                                                                                tlCmTxbTcDto.setPcThbPth(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, list4.getPcThbPth()));
//                                                                                                tlCmTxbTcDto.setTaThbPth(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, list4.getTaThbPth()));
//                                                                                                tcList.add(tlCmTxbTcDto);
//                                                                                            });
//                                                                });
//                                    });
            
            if(srhDto.getWithLrnLuYn().equals("Y") && luEvList.size() > 0 && lluIdx+1 == txbLluList.size()) { // 우리반수업 목록 페이지에서 호출 시
            	
            	for(TlCmTxbTcDto evList : luEvList) {
            		if(evList.getEvshTpCd().equals("ET") && this.checkTcList(evList)) {
            			tcList.add(this.setTcList(evList));
            		}
            	}
            };
            
            list1.setChild(tcList);
            lluIdx++;
        };

		return txbLluList;
	}
    
    /**
     * 학기 진단평가 셋팅
     * @param TlCmTxbTcDto
     * @return
     */
    private TlCmTxbTcDto setTcList (TlCmTxbTcDto list) {
    	TlCmTxbTcDto tlCmTxbTcDto = new TlCmTxbTcDto();

		tlCmTxbTcDto.setLrmpNodId4(list.getLrmpNodId4());
		tlCmTxbTcDto.setLrmpNodNm4(list.getLrmpNodNm4());
		tlCmTxbTcDto.setLrnAtvId(String.valueOf(list.getLrnAtvId()));
		tlCmTxbTcDto.setEvId(list.getEvId());
        tlCmTxbTcDto.setEvDtlDvCd(list.getEvDtlDvCd());
		tlCmTxbTcDto.setEvCmplYn(list.getEvCmplYn());
		tlCmTxbTcDto.setEvLcknYn(list.getEvLcknYn());
		tlCmTxbTcDto.setEvshTpCd(list.getEvshTpCd());
		tlCmTxbTcDto.setLcknYn(list.getLcknYn()); //잠금 여부
        tlCmTxbTcDto.setUseYn(list.getUseYn()); //사용 여부
        tlCmTxbTcDto.setLuNoUseYn(list.getLuNoUseYn());//번호 사용 여부
        
        return tlCmTxbTcDto;
    }
    
    /**
     * 임시 dto 체크
     * @param TlCmTxbTcDto
     * @return
     */
    private boolean checkTcList (TlCmTxbTcDto list) {
    	boolean result = true;
    	if(StringUtils.isEmpty(list.getLrmpNodId4())) {result = false;};
    	if(StringUtils.isEmpty(list.getLrmpNodNm4())) {result = false;};
    	if(StringUtils.isEmpty(list.getLrnAtvId())) {result = false;};
    	if(StringUtils.isEmpty(list.getEvId())) {result = false;};
    	if(StringUtils.isEmpty(list.getEvCmplYn())) {result = false;};
    	if(StringUtils.isEmpty(list.getEvshTpCd())) {result = false;};
    	if(StringUtils.isEmpty(list.getLcknYn())) {result = false;};
    	if(StringUtils.isEmpty(list.getUseYn())) {result = false;};
    	if(StringUtils.isEmpty(list.getLuNoUseYn())) {result = false;};
        
        return result;
    }

    /**
     * 학습예정 차시정보취득
     * @param srhDto TlCmTxbLastTcSrhDto
     * @return
     */
    @Transactional(readOnly = true)
    public TlCmTxbLastTcDto selectLastTxbTcList(TlCmTxbLastTcSrhDto srhDto) {
        TlCmTxbLastTcDto rtnDto = new TlCmTxbLastTcDto();
        List<Map<String, Object>> list = tlCmTxbCacheService.selectAllTxbTcList(srhDto.getOptTxbId(), srhDto.getLrnUsrId());
        
        if(list.isEmpty() || list.size() == 0) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}
        
        Map<String, Object> lastLrmpNodItem = commonDao.select(MAPPER_NAMESPACE + "selectLastLrmpNodId", srhDto);
        String lastLrmpNodId = lastLrmpNodItem == null ? "" : lastLrmpNodItem.get("LRMP_NOD_ID").toString();
        boolean searchNext = false;
        boolean isNotFound = true;
        int curIdx = 0;
        int ii = 0;
        String bfLluNodChk = "";
        String lluNodChk = "";
        int nTcNo = 0;
      
        for(Map<String, Object> item : list) {
            if (lastLrmpNodId == null || "".equals(lastLrmpNodId)) {
                break;
            }
            if(item.get("URNK_LRMP_NOD_ID") != null) {
            	lluNodChk = item.get("URNK_LRMP_NOD_ID").toString(); // 대단원ID
            	String tcNodNoUseYn = item.get("TC_NOD_NO_USE_YN").toString(); // 차시노드번호 사용여부
                if ("".equals(bfLluNodChk) || !bfLluNodChk.equals(lluNodChk)) { // 새로운 단원에서는 차시 번호 초기화
                	nTcNo = 0;
                }
                if ("Y".equals(tcNodNoUseYn)) { // 차시번호를 사용하는경우만
                    nTcNo++;
                }

                if (!searchNext) {
                    if (lastLrmpNodId.equals(item.get("LRMP_NOD_ID").toString())) { // 마직막 학습한 노드이면
                        if (item.get("CNT_TOT").toString().equals(item.get("CNT_CL").toString())) { // 총건수와 완료건수가 일치하는 경우는 다음 학습노드를 찾는다.
                            searchNext = true;
                        } else {
                            rtnDto = this.setTocData(item);
                            isNotFound = false;
                            curIdx = ii;
                            break;
                        }
                    }
                } else {
                    if (!item.get("CNT_TOT").toString().equals(item.get("CNT_CL").toString())) { // 완료되지 않은 학습노드를 찾는다.
                        rtnDto = this.setTocData(item);
                        isNotFound = false;
                        curIdx = ii;
                        break;
                    }
                }
                bfLluNodChk = lluNodChk;
                ii++;
            }
        }
        // 최근학습데이터를 못찾은 경우는 처음것을 보여준다.
        if (isNotFound && 0 < list.size()) {
            rtnDto = this.setTocData(list.get(curIdx));
            nTcNo = 1;
        }
        // 차시번호셋팅 - 차시번호를 사용하는 경우만 차시번호셋팅 그외는 빈값설정
        rtnDto.setTcNodNo("Y".equals(rtnDto.getTcNodNoUseYn()) ? String.format("%02d", nTcNo) : "");

        // 익힘책콘텐츠표시여부 확인
        if (rtnDto.getTcNodId() != null) {
            int cntWkb = commonDao.select(MAPPER_NAMESPACE + "selectCountWkbCtn", Map.of("optTxbId", srhDto.getOptTxbId(), "lrmpNodId", rtnDto.getTcNodId()));
            rtnDto.setWkbCtnYn(0 < cntWkb ? "Y" : "N");
        } else {
            rtnDto.setWkbCtnYn("N");
        }

        // 이전차시정보 셋팅
        if (curIdx == 0) {
        	rtnDto.setBfLluNodNoUseYn("N");
            rtnDto.setBfLluNodNo("");
            rtnDto.setBfLluNodId("");
            rtnDto.setBfLluNodNm("");
            rtnDto.setBfTcNodId("");
            rtnDto.setBfTcNodNm("");
            rtnDto.setBfEvId("");
        } else {
            Map<String, Object> bfItem = list.get(curIdx -1);
            String lluNodNoUseYn = bfItem.get("LLU_NOD_NO_USE_YN").toString();
            rtnDto.setBfLluNodNoUseYn(lluNodNoUseYn);
            rtnDto.setBfLluNodNo("Y".equals(lluNodNoUseYn) ? bfItem.get("NOD_NO").toString() : "");
            rtnDto.setBfLluNodId(bfItem.get("URNK_LRMP_NOD_ID").toString());
            rtnDto.setBfLluNodNm(bfItem.get("URNK_LRMP_NOD_NM").toString());
            rtnDto.setBfTcNodId(bfItem.get("LRMP_NOD_ID").toString());
            rtnDto.setBfTcNodNm(bfItem.get("LRMP_NOD_NM").toString());
            // 평가ID취득
            String evId = commonDao.select(MAPPER_NAMESPACE + "selectEvId", Map.of("optTxbId", srhDto.getOptTxbId(), "lrmpNodId", rtnDto.getBfTcNodId()));
            rtnDto.setBfEvId(evId);
        }

        if (curIdx < list.size() - 1) {
            String afTcNodId = list.get(curIdx + 1).get("LRMP_NOD_ID").toString();
            rtnDto.setAfTcNodId(afTcNodId);
        }
        return rtnDto;
    }
    /**
     * 데이터셋팅
     * @param tcMap
     * @return
     */
    private TlCmTxbLastTcDto setTocData(Map<String, Object> tcMap) {
        TlCmTxbLastTcDto dto = new TlCmTxbLastTcDto();
        String lluNodNoUseYn = tcMap.get("LLU_NOD_NO_USE_YN").toString();
        String tcNodNoUseYn = tcMap.get("TC_NOD_NO_USE_YN").toString();
        
        /** 대단원No사용여부*/
        dto.setLluNodNoUseYn(lluNodNoUseYn);
        /** 차시No사용여부*/
        dto.setTcNodNoUseYn(tcNodNoUseYn);

        /** 대단원No*/
        dto.setLluNodNo("Y".equals(lluNodNoUseYn) && tcMap.get("NOD_NO") != null ? tcMap.get("NOD_NO").toString() : "");

        /** 대단원ID*/
        dto.setLluNodId(tcMap.get("URNK_LRMP_NOD_ID") != null ? tcMap.get("URNK_LRMP_NOD_ID").toString() : "");

        /** 대단원명*/
        dto.setLluNodNm(tcMap.get("URNK_LRMP_NOD_NM") != null ? tcMap.get("URNK_LRMP_NOD_NM").toString() : "");

        /** 차시ID*/
        dto.setTcNodId(tcMap.get("LRMP_NOD_ID") != null ? tcMap.get("LRMP_NOD_ID").toString() : "");

        /** 차시명*/
        dto.setTcNodNm(tcMap.get("LRMP_NOD_NM") != null ? tcMap.get("LRMP_NOD_NM").toString() : "");

        /** 잠금여부 */
        dto.setLcknYn(tcMap.get("LCKN_YN") != null ? tcMap.get("LCKN_YN").toString() : "");
        
        /** 평가존재여부*/
        dto.setEvCtnYn((tcMap.get("EV_ID") == null || "".equals(tcMap.get("EV_ID").toString())) ? "N" : "Y");
        
        return dto;
    }

    /**
     * 이전차시 및 다음차시 정보조회 
     * @param srhDto
     * @return
     */
    @Transactional(readOnly = true)
    public TlCmTxbPrevNextTcDto selectTxbTcPrevNext(TlCmTxbPrevNextSrhDto srhDto) {
        List<Map<String, Object>> list = tlCmTxbCacheService.selectAllTxbTcList(srhDto.getOptTxbId(),"");
        TlCmTxbPrevNextTcDto rtnDto = new TlCmTxbPrevNextTcDto();
        boolean isNotFound = true;
        int curIdx = -1;
        int ii = 0;
        String bfLluNodChk = "";
        String lluNodChk = "";
        int nTcNo = 0;
        for(Map<String, Object> item : list) {
        	if(item.get("URNK_LRMP_NOD_ID")!=null) {
        		lluNodChk = item.get("URNK_LRMP_NOD_ID").toString();
            	String tcNodNoUseYn = item.get("TC_NOD_NO_USE_YN").toString(); // 차시노드번호 사용여부
            	if (!bfLluNodChk.equals(item.get("URNK_LRMP_NOD_ID").toString())) {
            		nTcNo = 0;
            	}
            	if ("Y".equals(tcNodNoUseYn)) {
            		nTcNo++;
            	}
                if (srhDto.getLrmpNodId().equals(item.get("LRMP_NOD_ID").toString())) { // 지정 차시 노드이면
                    isNotFound = false;
                    curIdx = ii;
                    break;

                }
                bfLluNodChk = lluNodChk;
                ii++;
        	}
        }
        if (!isNotFound) {
            Map<String, Object> curItem = list.get(curIdx);
            String lluNodNoUseYn = curItem.get("LLU_NOD_NO_USE_YN").toString();
            String tcNodNoUseYn = curItem.get("TC_NOD_NO_USE_YN").toString();
            rtnDto.setLluNodNoUseYn(lluNodNoUseYn);
            rtnDto.setLluNodNo("Y".equals(lluNodNoUseYn) ? curItem.get("NOD_NO").toString() : "");
            rtnDto.setLluNodId(curItem.get("URNK_LRMP_NOD_ID").toString());
            rtnDto.setLluNodNm(curItem.get("URNK_LRMP_NOD_NM").toString());
            rtnDto.setTcNodNoUseYn(tcNodNoUseYn);
            rtnDto.setTcNodNo("Y".equals(tcNodNoUseYn) ? String.format("%02d", nTcNo) : "");
            rtnDto.setTcNodId(curItem.get("LRMP_NOD_ID").toString());
            rtnDto.setTcNodNm(curItem.get("LRMP_NOD_NM").toString());
            rtnDto.setLcknYn(curItem.get("LCKN_YN").toString());
            rtnDto.setEvCtnYn((curItem.get("EV_ID") == null || "".equals(curItem.get("EV_ID").toString()) ? "N" : "Y"));
            rtnDto.setEvId((curItem.get("EV_ID") == null || "".equals(curItem.get("EV_ID").toString()) ? "" : curItem.get("EV_ID").toString()));
            int cntWkb = commonDao.select(MAPPER_NAMESPACE + "selectCountWkbCtn", Map.of("optTxbId", srhDto.getOptTxbId(), "lrmpNodId", rtnDto.getTcNodId()));
            rtnDto.setWkbCtnYn(0 < cntWkb ? "Y" : "N");
        }

        
        // 학습데이터를 못찾은 경우나 현재것인 경우
        if (isNotFound || curIdx == 0) {
            // 이전차시 없고, 다음차시만 존재
            rtnDto.setBfLluNodNo("");
            rtnDto.setBfLluNodId("");
            rtnDto.setBfLluNodNm("");
            rtnDto.setBfTcNodId("");
            rtnDto.setBfTcNodNm("");
            rtnDto.setBfEvId("");
            rtnDto.setBfLcknYn("");
        }
        // 이전차시정보 셋팅
        if (0 < curIdx && curIdx <= list.size() -1) {
            Map<String, Object> bfItem = list.get(curIdx - 1);
            rtnDto.setBfLluNodNo(bfItem.get("NOD_NO").toString());
            rtnDto.setBfLluNodId(bfItem.get("URNK_LRMP_NOD_ID").toString());
            rtnDto.setBfLluNodNm(bfItem.get("URNK_LRMP_NOD_NM").toString());
            rtnDto.setBfTcNodId(bfItem.get("LRMP_NOD_ID").toString());
            rtnDto.setBfTcNodNm(bfItem.get("LRMP_NOD_NM").toString());
            rtnDto.setBfLcknYn(bfItem.get("LCKN_YN").toString());
            // 평가ID취득
            String evId = commonDao.select(MAPPER_NAMESPACE + "selectEvId", Map.of("optTxbId", srhDto.getOptTxbId(), "lrmpNodId", rtnDto.getBfTcNodId()));
            rtnDto.setBfEvId(evId);
        } 
        if (-1 < curIdx && curIdx < list.size() -1) {
            Map<String, Object> afItem = list.get(curIdx + 1);
            rtnDto.setAfLluNodNo(afItem.get("NOD_NO").toString());
            rtnDto.setAfLluNodId(afItem.get("URNK_LRMP_NOD_ID").toString());
            rtnDto.setAfLluNodNm(afItem.get("URNK_LRMP_NOD_NM").toString());
            rtnDto.setAfTcNodId(afItem.get("LRMP_NOD_ID").toString());
            rtnDto.setAfTcNodNm(afItem.get("LRMP_NOD_NM").toString());
            rtnDto.setAfLcknYn(afItem.get("LCKN_YN").toString());
            // 평가ID취득
            String evId = commonDao.select(MAPPER_NAMESPACE + "selectEvId", Map.of("optTxbId", srhDto.getOptTxbId(), "lrmpNodId", rtnDto.getAfTcNodId()));
            rtnDto.setAfEvId(evId);
        }
        return rtnDto;    
    }

    /** 
     * 학습활동클래스보드URL 취득
     * @return String 학습활동클래스보드URL
     */
    public Map<String, String> selectTxbClsBrdUrl(TlCmTxbClsBrdSrhDto srhDto) {
        String clsBrdUrl = "";
        clsBrdUrl = commonDao.select(MAPPER_NAMESPACE + "selectTxbClsBrdUrl", srhDto);
        Map<String, String> rtnMap = new HashMap<String, String>();
        rtnMap.put("clsBrdUrl", clsBrdUrl != null ? clsBrdUrl :"");
        return rtnMap;
    }

    /** 
     * 학습활동클래스보드URL 취득 v3.1대응
     * @return String 학습활동클래스보드URL
     */
    public List<Map<String, String>> selectTxbClabdUrl(TlCmTxbClsBrdSrhDto srhDto) {
        List<Map<String, String>> listMap = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbClabdUrl", srhDto);
        List<Map<String, String>> rtnListMap = new ArrayList<Map<String, String>>();
        listMap.forEach(itemMap -> {
            Map<String, String> rtnMap = new HashMap<String, String>();
            String clabdLrgsNm = itemMap.get("CLABD_LRGS_NM").toString();
            String clabdSmlNm = itemMap.get("CLABD_SML_NM").toString();
            String clabdTyp = itemMap.get("CLABD_TYP").toString();
            String clsBrdUrl = itemMap.get("CLABD_URL").toString();
            rtnMap.put("clsBrdUrl", clsBrdUrl);
            rtnMap.put("clabdLrgsNm", clabdLrgsNm);
            rtnMap.put("clabdSmlNm", clabdSmlNm);
            rtnMap.put("clabdTyp", clabdTyp);
            rtnListMap.add(rtnMap);
        });
        return rtnListMap;
    }

    /**
     * 차시 정보 조회 (학생 홈)
     *
     * @param Map<String, String> req
     *                    - optTxbId : 운영교과서ID
     *                    - sbjCd : 과목코드
     * @return ResponseDto<List < TlCmTxbLluDto>>
     */
    @Transactional(readOnly = true)
    public List<TlCmTxbTcHmDto> selectTxbTcListHm(Map<String, String> req) {
        List<TlCmTxbTcHmDto> result = tlCmTxbCacheService.selectTxbTcListHmQuery(req.get("optTxbId"), req.get("sbjCd"));

        if (result == null || result.isEmpty()) {
            return null;
        }

        result = sliceTxbTcList(result, req);

        if (req.get("thbYn") == null || "".equals(req.get("thbYn"))) {
            return result;
        }
        return mapTxbTcListToThumbnailList(result);
    }

    /**
     * 최근 학습한 차시 기준으로 앞뒤로 10개의 차시를 가져온다.
     * @param list
     * @param req
     * @return
     */
    List<TlCmTxbTcHmDto> sliceTxbTcList(List<TlCmTxbTcHmDto> list, Map<String, String> req) {
        int PAGE_SIZE = 10;
        int targetIndex = 0;
        Map<String, Object> lastLrmpNodItem = commonDao.select(MAPPER_NAMESPACE + "selectLastLrmpNodId", req);
        if (lastLrmpNodItem != null) {
            for (int i = 0; i < list.size(); ++i) {
                if (list.get(i).getLrmpNodId().equals(lastLrmpNodItem.get("LRMP_NOD_ID"))) {
                    targetIndex = i;
                    break;
                }
            }
        }
        list.get(targetIndex).setLastTcYn("Y");
        int startIndex = Math.max(targetIndex - (PAGE_SIZE / 2), 0);
        int endIndex = Math.min(startIndex + PAGE_SIZE, list.size());
        return list.subList(startIndex, endIndex);
    }

    /**
     * 썸네일이 필요한 차시 정보
     * @param list
     * @return
     */
    public List<TlCmTxbTcHmDto> mapTxbTcListToThumbnailList(List<TlCmTxbTcHmDto> list) {
        List<String> lrmpNodIds = list.stream()
                .map(TlCmTxbTcHmDto::getLrmpNodId)
                .collect(Collectors.toSet())
                .stream()
                .collect(Collectors.toList());

        Map<String, Object> thbReq = new HashMap<>();
        thbReq.put("type","pc");
        thbReq.put("lrmpNodIds", lrmpNodIds);

        List<Map<String, Object>> pcThbFiles = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbTcThbFile", thbReq);
        Map<Long, String> pcThbFileMap = new HashMap<>();
        for (Map<String, Object> pcThbFile : pcThbFiles) {
            Long lrmpNodId = (Long) pcThbFile.get("lrmpNodId");
            pcThbFileMap.put(lrmpNodId, (String) pcThbFile.get("flePthNm"));
        }

        thbReq.put("type", "ta");
        List<Map<String, Object>> TaThbFile = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbTcThbFile", thbReq);
        Map<Long, String> TaThbFileMap = new HashMap<>();
        for (Map<String, Object> taThbFile : TaThbFile) {
            Long lrmpNodId = (Long) taThbFile.get("lrmpNodId");
            TaThbFileMap.put(lrmpNodId, (String) taThbFile.get("flePthNm"));
        }

        list.stream().forEach(
                item -> {
                    Long lrmpNodId = Long.parseLong(item.getLrmpNodId());
                    String pcThbFile = pcThbFileMap.get(lrmpNodId);
                    if (pcThbFile != null) {
                        item.setPcThbPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, pcThbFile));
                    }
                    String taThbFile = TaThbFileMap.get(lrmpNodId);
                    if (taThbFile != null) {
                        item.setTbThbPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, taThbFile));
                    }
                }
        );
        return list;
    }

    /**
     * pre
     *
     * @param Map<String, String> req
     *                    - optTxbId : 운영교과서ID
     *                    - sbjCd : 과목코드
     * @return ResponseDto<List < TlCmTxbLluDto>>
     */
    @Transactional(readOnly = true)
    public List<TlCmTxbTcHmDto> selectBeforeOrAfterTcListByLrmpNodId(Map<String, String> req) {
        String lrmpNodId = req.get("lrmpNodId");
        String direction = req.get("direction");
        if (lrmpNodId == null || "".equals(lrmpNodId) || direction == null || "".equals(direction)) {
            return null;
        }
        List<TlCmTxbTcHmDto> result = tlCmTxbCacheService.selectTxbTcListHmQuery(req.get("optTxbId"), req.get("sbjCd"));
        int targetIndex = -1;
        for (int i = 0; i < result.size(); ++i) {
            if (result.get(i).getLrmpNodId().equals(lrmpNodId)) {
                targetIndex = i;
                if (req.get("sbjCd") == null || "MA".equals(req.get("sbjCd"))) {
                    break;
                } else {
                    String stpId = req.get("stpId");
                    String tcStpId = result.get(i).getStpId();
                    if (stpId.equals(tcStpId)) {
                        break;
                    }
                }
            }
        }
        if (targetIndex == -1) {
            return null;
        }

        if ("before".equals(direction)) {
            if (targetIndex == 0) {
                return null;
            }
            result = result.subList(Math.max(0, targetIndex - 5), targetIndex);
        }
        if ("after".equals(direction)) {
            if (targetIndex == result.size() - 1) {
                return null;
            }
            result = result.subList(targetIndex + 1, Math.min(result.size(), targetIndex + 6));
        }

        if (req.get("thbYn") == null || "".equals(req.get("thbYn"))) {
            return result;
        }
        return mapTxbTcListToThumbnailList(result);
    }

}
