package com.aidt.api.sl.splrn.dto;

import java.util.Date;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-13 8:41:26
 * @modify : date 2024-02-13 8:41:26
 * @desc : 특별학습 진행상황DTO
 */


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnPgrsDto {


	@Parameter(name="운영교과서아이디", required = true)
	@NotBlank(message = "{field.required}")
	private String optTxbId;

	@Parameter(name="특별학습아이디", required = true)
	@NotBlank(message = "{field.required}")
	private String splrnId;

	@Parameter(name="특별학습콘텐츠아이디", required = true)
	@NotBlank(message = "{field.required}")
	private String splrnCtnId;

	@Parameter(name="학습사용자아이디", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnusrId;

	@Parameter(name="학습상태코드", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnStCd;

	@Parameter(name="학습시간초수", required = true)
	@NotBlank(message = "{field.required}")
	private int lrnTmScnt;

	@Parameter(name="생성자아이디")
	private String crtrId;

	@Parameter(name="생성일시")
	private Date crtDtm;

	@Parameter(name="수정자아이디")
	private String mdfrId;

	@Parameter(name="수정일시", required = true)
	@NotBlank(message = "{field.required}")
	private Date mdfDtm;

	@Parameter(name="데이터베이스아이디")
	private String dbId;


	
}
