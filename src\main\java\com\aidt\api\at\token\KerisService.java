package com.aidt.api.at.token;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.at.token.dto.AtUsrInfoDto;
import com.aidt.api.at.token.dto.KerisDto;
import com.aidt.api.at.token.dto.KerisLrnDataUpsertDto;
import com.aidt.api.at.token.dto.KerisLrnOutDataDto;
import com.aidt.api.at.token.dto.KerisMemberDto;
import com.aidt.api.at.token.dto.KerisMemberInfoDto;
import com.aidt.api.at.token.dto.OptTxbPridDto;
import com.aidt.api.at.user.UserService;
import com.aidt.api.at.user.dto.UserCreateDto;
import com.aidt.api.at.user.dto.UserCreateRstDto;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class KerisService {

	private final String MAPPER_NAMESPACE = "api.at.keris.";
	private final String MAPPER_NAMESPACE_TCR = "api.bc.cm.tcr.";

	private final String MAPPER_NAMESPACE_EA = "api.ea.evcom.";

	@Autowired
	private CommonDao commonDao;

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	@Autowired
	private UserService userService;

	@Autowired
	private OptTxbPridService optTxbPridService;

	// 2024-06-23 KERIS -> LMS 최초 호출시 인증서버 데이터 검증 및 저장
	public void loginAndUsrCheck(KerisDto kerisDto) {
		String userType = kerisDto.getUser_type();
		String lectureCode = kerisDto.getLecture_code();

		AtUsrInfoDto atUsrInfoDto = new AtUsrInfoDto();
		// atUsrInfoDto.setUsrId(kerisDto.getUser_id());
		atUsrInfoDto.setKerisUsrId(kerisDto.getKerisUsrId());
		atUsrInfoDto.setUserStatus(kerisDto.getUser_status());

		// 2025.02.17 이용약관여부, 일자 추가
		atUsrInfoDto.setKerisTermAgrYn(kerisDto.getUse_terms_agree_yn());
		atUsrInfoDto.setKerisTermAgrDt(kerisDto.getUse_terms_agree_dt());

		atUsrInfoDto.setTxbTermCd(DB_ID);
		atUsrInfoDto.setDbId(DB_ID);

		String txbId = BcCmUtil.getTxbID(DB_ID);

		if (StringUtils.isBlank(txbId)) {
			return;
		} else {
			atUsrInfoDto.setTxbId(txbId);
		}

		if ("T".equals(userType)) {
			atUsrInfoDto.setUsrTpCd("TE");
		} else if ("S".equals(userType)) {
			atUsrInfoDto.setUsrTpCd("ST");
		}

		// 동일한 UUID를 갖고있는 교사가 각각 다른 lectrue_code를 갖고 있을 수 있으므로 login_id를 가공하여 판별
		// atUsrInfoDto.setLoginId(lectureCode + "-" + kerisDto.getUser_id());
		atUsrInfoDto.setOptTxbId(DB_ID + "-" + lectureCode);
		atUsrInfoDto.setClaId(DB_ID + "-" + lectureCode);

		// token 및 usr 등록 처리
		UserCreateRstDto rsDto = userService
				.saveCheckTokenUsr(UserCreateDto.builder().kerisUsrId(atUsrInfoDto.getKerisUsrId())
						.claId(atUsrInfoDto.getClaId()).lectureCode(lectureCode).optTxbId(atUsrInfoDto.getOptTxbId())
						.usrTpCd(atUsrInfoDto.getUsrTpCd()).kerisTermAgrYn(atUsrInfoDto.getKerisTermAgrYn())
						.kerisTermAgrDt(atUsrInfoDto.getKerisTermAgrDt()).build());

		if (rsDto == null) {
			throw new BizException("token, usr 가입처리 실패...");
		}

		if (rsDto.isUsrNew()) {
			kerisDto.setKafkaUsrId(rsDto.getUsrId());
		}

		atUsrInfoDto.setUsrId(rsDto.getUsrId());

		/*
		 * 2025.03.21 운영교과서 교시 추가
		 */
		if (StringUtils.isNotBlank(kerisDto.getClass_period())) {
			optTxbPridService.insertOptTxbPrid(
					OptTxbPridDto.builder().optTxbId(atUsrInfoDto.getOptTxbId()).optTxbPrid(kerisDto.getClass_period())
							.kerisLectCd(lectureCode).crtrId(atUsrInfoDto.getUsrId()).build(),
					kerisDto);
		}

		// 학습자인 경우 모니터링 데이터 추가
		if ("S".equals(userType)) {
			// 모니터링 데이터 등록
			BcStuListDto stuInfo = new BcStuListDto();
			stuInfo.setUsrId(atUsrInfoDto.getUsrId());
			stuInfo.setOptTxbId(atUsrInfoDto.getOptTxbId());
			stuInfo.setTxbId(atUsrInfoDto.getTxbId());

			int stuCurLrnStCnt = commonDao.select(MAPPER_NAMESPACE_TCR + "selectStuCurLrnStCnt", stuInfo);
			if (stuCurLrnStCnt == 0) {
				stuCurLrnStCnt = commonDao.insert(MAPPER_NAMESPACE_TCR + "insertStuCurLrnSt", stuInfo);
			}
		}

		Map<String, Object> param = new HashMap<>();
		param.put("usrId", atUsrInfoDto.getUsrId());
		param.put("optTxbId", atUsrInfoDto.getOptTxbId());

		/*
		 *  2025.03.27 Duplicate 오류로 인한 rollback 방지 및 login 기능 유지위해 try catch 설정
		 *  - 회원 공통으로 사용하는 부분으로 밀리세컨 미세한 차이로 duple 장애 발생
		 */
		try {
			// 평가 결과 테이블에 없는 학생정보 등록
			commonDao.insert(MAPPER_NAMESPACE_EA + "insertEvRsLoginAddStu", param);
		} catch(DuplicateKeyException dke) {
			log.error("Duplicate error (다음 로직 진행하도록 try catch) >>> " + param.toString());
		}
	}

	// 학생 로그인시 전입 UPSERT
	public int upsertAtNtlvEduCrsStnData(KerisLrnDataUpsertDto kerisReqListUpsertDto) {
		// 2025.01.13 DBA 명명 규칙 변경 요청에 따라 기존 STN_SST_EDU_CRS_ID -> CRCL_CTN_ELM2_CD 컬럼만
		// 변경 처리
		return commonDao.insert(MAPPER_NAMESPACE + "upsertAtNtlvEduCrsStnData", kerisReqListUpsertDto);
	}

	@Transactional(readOnly = true)
	// 학생 로그인 후 전출데이터 추출
	public List<KerisLrnOutDataDto> selectAtCmNtlvEduCrsStnSstList(String userId) {
		// 2025.01.13 DBA 명명 규칙 변경 요청에 따라 기존 STN_SST_EDU_CRS_ID -> CRCL_CTN_ELM2_CD 변경처리
		// 및 기존 alias 처리
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAtCmNtlvEduCrsStnSstList", userId);
	}

	/**
	 * 케리스 사용자 등록 교사 전용
	 * 
	 * @param kerisMemberDto
	 * @return
	 */
	public int updateKerisStuInfo(KerisMemberDto kerisMemberDto) {
		int result = 0;
		KerisMemberInfoDto profMemberInfo = commonDao.select(MAPPER_NAMESPACE + "selectProfUserInfo",
				kerisMemberDto.getUser_id());

		if (profMemberInfo == null) {
			throw new RuntimeException("회원정보가 없습니다.");
		}

		String profLoginId = kerisMemberDto.getLecture_code() + "-" + kerisMemberDto.getUser_id();
		// lecturecode 추가
		if (!profLoginId.equals(profMemberInfo.getLoginId())) {
			throw new RuntimeException("SSO 회원정보만 가능합니다.");
		}

		List<KerisMemberInfoDto> stuMemberlist = commonDao.selectList(MAPPER_NAMESPACE + "selectUserList",
				profMemberInfo.getClaId());
		if (!stuMemberlist.isEmpty()) {
			for (String stuUid : kerisMemberDto.getMemberIds()) {
				boolean chkInsert = false;
				for (KerisMemberInfoDto member : stuMemberlist) {
					if (member.getUsrId().equals(stuUid)) {
						chkInsert = true;
						break;
					}
				}
				// 학생 회원 등록
				if (chkInsert) {
					result += insertStuUser(profMemberInfo, stuUid, kerisMemberDto.getLecture_code());
				}
			}
		} else {
			for (String stuUid : kerisMemberDto.getMemberIds()) {
				// 학생 회원 등록
				result += insertStuUser(profMemberInfo, stuUid, kerisMemberDto.getLecture_code());
			}

		}
		return result;
	}

	private int insertStuUser(KerisMemberInfoDto profMemberInfo, String stuUid, String lectureCode) {

		AtUsrInfoDto atUsrInfoDto = new AtUsrInfoDto();
		atUsrInfoDto.setUsrId(stuUid);
		atUsrInfoDto.setTxbTermCd(profMemberInfo.getDbId());
		atUsrInfoDto.setDbId(profMemberInfo.getDbId());
		atUsrInfoDto.setTxbId(profMemberInfo.getTxbId());

		atUsrInfoDto.setLoginId(lectureCode + "-" + stuUid);
		atUsrInfoDto.setOptTxbId(profMemberInfo.getOptTxbId());
		atUsrInfoDto.setClaId(profMemberInfo.getClaId());
		atUsrInfoDto.setUsrTpCd("ST");
		int result = 0;
		int atTempUsrCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckTokenInfo", atUsrInfoDto);
		if (atTempUsrCnt == 0) {
			// CM_TOKEN - 토큰 정보 저장
			commonDao.insert(MAPPER_NAMESPACE + "insertTokenInfo", atUsrInfoDto);
			result = 1;
		}

		// CM_USR - 사용자 정보
		int atUsrCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckUserInfo", atUsrInfoDto);
		if (atUsrCnt == 0) {
			// CM_USR - 사용자 정보 등록
			commonDao.insert(MAPPER_NAMESPACE + "insertUserInfo", atUsrInfoDto);
			result = 1;
		}
		// 모니터링 데이터 등록
		BcStuListDto stuInfo = new BcStuListDto();
		stuInfo.setUsrId(atUsrInfoDto.getUsrId());
		stuInfo.setOptTxbId(atUsrInfoDto.getOptTxbId());
		stuInfo.setTxbId(atUsrInfoDto.getTxbId());

		int stuCurLrnStCnt = commonDao.select(MAPPER_NAMESPACE_TCR + "selectStuCurLrnStCnt", stuInfo);
		if (stuCurLrnStCnt == 0) {
			stuCurLrnStCnt = commonDao.insert(MAPPER_NAMESPACE_TCR + "insertStuCurLrnSt", stuInfo);
		}
		return result;
	}

}
