package com.aidt.api.xx.sample.validate;

import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.xx.sample.validate.dto.SampleValidateDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample Validate Controller
 */
@Tag(name="[xx] Sample.Validation" , description="필드 검증")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/sample/validate")
public class SampleValidateController {
	private final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
	private final Validator validator = factory.getValidator();

	/**
	 * @param validateDto
	 * @return ResponseDto<ValidateDto>
	 */
	@Operation(summary="Method 진입 전에 필드 검증", description = "Method 진입 전에 검증하여 invalid field Exception 발생")
	@PostMapping(value = "/before", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<SampleValidateDto> beforeValidate(@Valid @RequestBody SampleValidateDto validateDto) {
		log.debug(validateDto.toString());
		return Response.ok(validateDto);
	}

	/**
	 * @param validateDto
	 * @return ResponseDto<ValidateDto>
	 */
	@Operation(summary="Method 안에서 필드 검증", description = "Method안에서 필드 검증")
	@PostMapping(value = "/inside", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<SampleValidateDto> insideValidate(@RequestBody SampleValidateDto validateDto) {
		Set<ConstraintViolation<SampleValidateDto>> violations = validator.validate(validateDto);
		for (ConstraintViolation<SampleValidateDto> violation : violations) {
			log.info(violation.getMessage());
		}
		return Response.ok(validateDto);
	}
}