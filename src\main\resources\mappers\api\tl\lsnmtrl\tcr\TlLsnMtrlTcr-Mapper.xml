<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.lsnmtrl.tcr">
    <!-- 수업자료 Insert-->
    <insert id="insertLsnMtrl" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        INSERT INTO LMS_LRM.TL_SBC_LRN_LSN_MTRL /* TL_교과학습수업자료 */
            (OPT_TXB_ID                  /* 운영교과서ID */
            ,LRMP_NOD_ID                 /* 학습맵노드ID */
            ,LSN_MTRL_NO                 /* 수업자료번호 */
            ,LRN_MTRL_ID                 /* 학습자료ID */
            ,LSN_MTRL_TP_CD              /* 자료구분코드(게시물,링크,라이브퀴즈,토의토론,워크시트,보드) */
            ,OPNP_YN                     /* 학생에게 공개여부 */
            ,RCSTN_ORDN                  /* 재구성순서 */
            ,BS_MTRL_YN                  /* 기본자료여부 */
            ,DEL_YN
            ,CRTR_ID                     /* 생성자ID */
            ,CRT_DTM                     /* 생성일시 */
            ,MDFR_ID                     /* 수정자ID */
            ,MDF_DTM                     /* 수정일시 */
            ,DB_ID                       /* 접속DB인스턴스ID */
            )
        SELECT
             #{optTxbId}                                /* 운영교과서ID */
            ,#{lrmpNodId}                               /* 학습맵노드ID */
            ,IFNULL((SELECT MAX(LSN_MTRL_NO)            /* 수업자료번호 */
                FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL
                WHERE OPT_TXB_ID = #{optTxbId}
                AND LRMP_NOD_ID = #{lrmpNodId})+1, 1)
            ,LSN_SPP_MTRL_ID                            /* 학습자료ID */
            ,FLE_MG_TP_CD                               /* 파일관리유형코드(LI=링크, DM=문서파일) */
            ,#{opnpYn}                                  /* 학생에게 공개여부 */
            ,SRT_ORDN                                   /* 재구성순서 */
            'Y'                                         /* 기본자료여부 */
            ,'N'                                        /* 삭제여부 */
            ,#{mdfrId}                                  /* 생성자ID */
            ,NOW()                                      /* 생성일시 */
            ,#{mdfrId}                                  /* 수정자ID */
            ,NOW()                                      /* 수정일시 */
            ,#{dbId}                                   /* 접속DB인스턴스ID */
        FROM LMS_CMS.BC_LSN_SPP_MTRL /* BC_수업보충자료 */
        WHERE LSN_SPP_MTRL_ID = #{lrnMtrlId}

        /** 교과학습 김형준 TlLsnMtrlTcr-Mapper.xml - insertLsnMtrl */
    </insert>

    <!-- 수업자료 Update-->
    <update id="updateLsnMtrl" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        UPDATE LMS_LRM.TL_SBC_LRN_LSN_MTRL    /* TL_교과학습수업자료*/
        SET  MDFR_ID = #{mdfrId}              /* 수정자ID */
            ,MDF_DTM = NOW()                  /* 수정일시 */
            ,OPNP_YN = #{opnpYn}              /* 학생에게 공개여부 */
        WHERE OPT_TXB_ID =#{optTxbId}         /* 운영교과서ID */
        AND LRMP_NOD_ID    = #{lrmpNodId}     /* 학습맵노드ID */
        AND LSN_MTRL_NO    = #{lsnMtrlNo}     /* 수업자료번호 */

        /** 교과학습 김형준 TlLsnMtrlTcr-Mapper.xml - updateLsnMtrl */
    </update>
    <!-- 
        수업자료 기본목록 조회 
        LCMS에서 등록한 기본수업자료를 조회한다.
    -->
    <select id="selectLrnMtrlBsList" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto" resultType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        SELECT
		     IFNULL(C.LSN_MTRL_NO, '')   AS LSN_MTRL_NO 
            ,A.LSN_SPP_MTRL_ID           AS LSN_SPP_MTRL_ID  /* 수업보충자료ID */
            ,'BS'                        AS MTRL_DV_CD       /* 자료구분=LCMS등록자료 */
            ,A.LSN_SPP_MTRL_NM           AS MTRL_NM          /* 수업보충자료명 */
            ,A.FLE_MG_TP_CD                                  /* 파일관리유형코드 */
            ,A.FLE_LINK_URL_ADR                              /* 파일링크URL주소 */
            ,IFNULL(B.FLE_PTH_NM, '')    AS FLE_PTH_NM       /* 파일경로명 */
            ,B.FLE_TP_CD                                     /* 파일유형코드 */
            ,B.ALTN_TXT_CN                                   /* 대체텍스트내용 */
            ,IFNULL(C.OPNP_YN, A.EPS_YN) AS OPNP_YN          /* 공개여부 LMS에서 공개여부가 설정되지 않았으면 LCMS설정을 따른다. */
            ,A.LRMP_NOD_ID                                   /* 학습맵노드ID */
            ,IFNULL(B.UPL_FLE_NM, '')    AS FLE_NM           /* 파일명 */
            ,IFNULL(A.DOC_VI_ID,'')	AS  DOC_VI_ID
        FROM LMS_CMS.BC_LSN_SPP_MTRL A /* BC_수업보충자료 */
            LEFT JOIN LMS_CMS.BC_UPL_FLE B /* BC_업로드파일 */
                 ON A.UPL_FLE_ID = B.UPL_FLE_ID
                 AND B.DEL_YN = 'N'
                 AND A.FLE_MG_TP_CD = 'DM'
            LEFT JOIN LMS_LRM.TL_SBC_LRN_LSN_MTRL C /* TL_교과학습수업자료 */
                 ON C.OPT_TXB_ID = #{optTxbId}
                 AND A.LRMP_NOD_ID = C.LRMP_NOD_ID
                 AND A.LSN_SPP_MTRL_ID = C.LRN_MTRL_ID
                 AND C.BS_MTRL_YN = 'Y'
                 AND A.DEL_YN = 'N'
        WHERE 1=1
        <if test='lrmpNodId == ""'>
        	AND A.LRMP_NOD_ID IN (SELECT LRMP_NOD_ID FROM lms_lrm.tl_sbc_lrn_nod_rcstn WHERE OPT_TXB_ID = #{optTxbId} AND DPTH = 4 AND LU_EPS_YN = 'Y')
        </if>
        <if test='lrmpNodId != ""'>
        	AND A.LRMP_NOD_ID = #{lrmpNodId} 
        </if>
        AND A.EPS_YN = 'Y'
        <if test='allDatYn != "Y"'>
            AND IFNULL(C.OPNP_YN, A.EPS_YN) = 'Y'
        </if>
        AND A.DEL_YN = 'N'
        ORDER BY A.CRT_DTM DESC, A.LSN_SPP_MTRL_ID DESC

        /** 교과학습 강성희 TlLsnMtrlTcr-Mapper.xml - selectLrnMtrlBsList */
    </select>
    <!-- 내 자료 조회-->
    <select id="selectLrnMtrlLmList" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto" resultType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        SELECT 
        	 M.LSN_MTRL_NO                            /* 수업자료번호 */
        	,M.LSN_MTRL_TP_CD                         /* 수업자료유형코드 */
        	,M.ANNX_ID                                /* 첨부ID */
        	,M.LSN_SPP_MTRL_NM   AS MTRL_NM           /* 수업보충자료ID */
        	,IFNULL(M.URL,'')               AS FLE_LINK_URL_ADR  /* 파일링크URL주소 */
        	,IFNULL(M.LRN_BLWR_CN,'') 		AS LRN_BLWR_CN /* 학습게시글내용 */
            ,M.OPNP_YN
        	,F.ANNX_FLE_ORGL_NM  AS FLE_NM            /* 파일명 */
        	,IFNULL(F.ANNX_FLE_PTH_NM, '')   AS FLE_PTH_NM        /* 파일경로명 */
        	,IFNULL(F.DOC_VI_ID, '') AS DOC_VI_ID
        	,M.LRMP_NOD_ID
        FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL M      /* TL_교과학습수업자료 */
            LEFT JOIN LMS_LRM.CM_ANNX A             /* CM_첨부 */
                   ON M.ANNX_ID = A.ANNX_ID
                  AND A.USE_YN = 'Y'
            LEFT JOIN LMS_LRM.CM_ANNX_FLE F         /* CM_첨부파일 */
                   ON A.ANNX_ID = F.ANNX_ID
                  AND F.USE_YN = 'Y'
        WHERE M.OPT_TXB_ID = #{optTxbId}
        <if test='lrmpNodId != ""'>
        	AND M.LRMP_NOD_ID = #{lrmpNodId} 
        </if>
          AND M.DEL_YN = 'N'
          AND M.BS_MTRL_YN = 'N'  -- 원클릭학습설정에서 교사가 올린것만
        ORDER BY M.CRT_DTM DESC
    
        /** 교과학습 김형준 TlLsnMtrlTcr-Mapper.xml - selectLrnMtrlLmList */
    </select>

</mapper>