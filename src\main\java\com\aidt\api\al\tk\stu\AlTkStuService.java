package com.aidt.api.al.tk.stu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.tk.dto.AlTkDto;
import com.aidt.common.CommonDao;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-02 09:05:00
 * @modify 2024-06-02 09:05:00
 * @desc AI회화 학생 Service
 */

@Slf4j
@Service
public class AlTkStuService {
	
	private final String MAPPER_NAMESPACE = "api.al.tk.stu.";

	@Autowired
	private CommonDao commonDao;
	
	@Autowired
    private WebFluxUtil webFluxUtil;
	
	@Value("${aidt.endpoint.archipinTalk:}")
	private String endpoint_archipinTalk;
	
	@Value("${aidt.endpoint.lw_myhm_stu_point:}")
	private String endpoint_lw_myhm_stu_point;

    /**
     * AI 회화 토픽 목록 조회 서비스
     *
     * @param AlTkDto
     * @return List<AlTkDto>
     */
	public List<AlTkDto> selectTkStuList(AlTkDto alTkDto) {
		List<AlTkDto> kmmpList = commonDao.selectList(MAPPER_NAMESPACE + "selectkmmpList", alTkDto);
		for (AlTkDto kmmp : kmmpList) {
			//필요한 액션 수행
			kmmp.setLrnUsrId(alTkDto.getLrnUsrId());
			kmmp.setOptTxbId(alTkDto.getOptTxbId());

			// kmmp에 해당하는 tpcList 가져오기
			List<AlTkDto> tpcList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcList", kmmp);

			// kmmpList 안에 tpcList 추가
			kmmp.setTpcList(tpcList);
		}
		return kmmpList;
	}
	
	/**
     * AI회화 나의 최근 학습 조회
     *
     * @param AlTkDto
     * @return List<AlTkDto>
     */
	public List<AlTkDto> selectTkRcnLrnStuInfo(AlTkDto alTkDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTkRcnLrnStuInfo", alTkDto);
	}
	
	/**
     * AI 회화 상세 조회
     *
     * @param AlTkDto
     * @return List<AlTkDto>
     */
	public List<AlTkDto> selectTkStuDetailInfo(AlTkDto alTkDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTkStuDetailInfo", alTkDto);
	}
	
	/**
     * AI 회화 이전, 다음 단원 조회
     *
     * @param AlTkDto
     * @return List<AlTkDto>
     */
	public List<AlTkDto> selectTkStuPrevNextInfo(AlTkDto alTkDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTkStuPrevNextInfo", alTkDto);
	}
	
	/**
     * AI 회화 학습 상태 insert
     *
     * @param AlTkDto
     * @return List<AlTkDto>
     */
	@Transactional
	public int insertTkStuLrnStCd(AlTkDto alTkDto) {
		
		int cnt = 0;
		int checkCnt = 0;
		
		List<AlTkDto> tpcList = alTkDto.getTpcList();
		alTkDto.setLrnUsrId(alTkDto.getLrnUsrId());
		
		if("E".equals(alTkDto.getSchlGrdCd())) {
			if (tpcList != null) {
				for (AlTkDto tpc : tpcList) {
					alTkDto.setTpcKmmpNodId(tpc.getTpcKmmpNodId());
					alTkDto.setKmmpNodId(tpc.getKmmpNodId());
					// 등록된 최근 학습 조회
					checkCnt = commonDao.select(MAPPER_NAMESPACE + "selectTkStuLrnStCdCheck", alTkDto);
					if(checkCnt == 0 ) {
						cnt += commonDao.insert(MAPPER_NAMESPACE + "insertTkStuLrnStCd", alTkDto);
					}
				}
			}
		} else {
			// 등록된 최근 학습 조회
			checkCnt = commonDao.select(MAPPER_NAMESPACE + "selectTkStuLrnStCdCheck", alTkDto);
			if(checkCnt == 0 ) {
				cnt += commonDao.insert(MAPPER_NAMESPACE + "insertTkStuLrnStCd", alTkDto);
			}
		}
		
		return cnt;
	}
	
	/**
     * AI 회화 학습 상태 update
     *
     * @param AlTkDto
     * @return List<AlTkDto>
     */
	@Transactional
	public int updateTkStuLrnStCd(AlTkDto alTkDto) {
		
		int cnt = 0;
		int checkCnt = 0;
		List<AlTkDto> tpcList = alTkDto.getTpcList();
		alTkDto.setLrnUsrId(alTkDto.getLrnUsrId());
		
		if("E".equals(alTkDto.getSchlGrdCd())) {
			if (tpcList != null) {
				for (AlTkDto tpc : tpcList) {
					alTkDto.setTpcKmmpNodId(tpc.getTpcKmmpNodId());
					// 등록된 최근 학습 조회
					checkCnt = commonDao.select(MAPPER_NAMESPACE + "selectTkStuLrnStCdCheck", alTkDto);
					if(checkCnt > 0 ) {
						cnt += commonDao.update(MAPPER_NAMESPACE + "updateTkStuLrnStCd", alTkDto);
					}
				}
			}
		} else {
			// 등록된 최근 학습 조회
			checkCnt = commonDao.select(MAPPER_NAMESPACE + "selectTkStuLrnStCdCheck", alTkDto);
			if(checkCnt > 0 ) {
				cnt += commonDao.update(MAPPER_NAMESPACE + "updateTkStuLrnStCd", alTkDto);
			}
		}
		
		
		return cnt;
	}
	
	@Transactional(readOnly = true)
	public Map<String, Object> startDalogueApi(AlTkDto alTkDto) {        
		Map<String, Object> returnMap = new HashMap<>();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Content-Type", "application/json");

		try {
			String jsonString = new ObjectMapper().writeValueAsString(alTkDto);

			String post = webFluxUtil.post(this.endpoint_archipinTalk + "/api/Dialogue/start_dialogue", httpHeaders, jsonString, String.class);
			//String post = webFluxUtil.post("https://devlocal.archipindev.com:7182/api/Dialogue/start_dialogue", httpHeaders, jsonString, String.class);
			return CoreUtil.Json.jsonString2Map(post);
		} catch (JsonProcessingException e) {
			log.debug("JsonProcessingException");
        }

		return returnMap;
	}
	
	@Transactional(readOnly = true)
	public Map<String, Object> nextDalogueApi(AlTkDto alTkDto) {        
		Map<String, Object> returnMap = new HashMap<>();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Content-Type", "application/json");

		try {
			String jsonString = new ObjectMapper().writeValueAsString(alTkDto);
			log.debug("jsonString == " + jsonString.toString());

			String post = webFluxUtil.post(this.endpoint_archipinTalk + "/api/Dialogue/next_dialogue", httpHeaders, jsonString, String.class);
			//String post = webFluxUtil.post("https://devlocal.archipindev.com:7182/api/Dialogue/next_dialogue", httpHeaders, jsonString, String.class);
			return CoreUtil.Json.jsonString2Map(post);
		} catch (JsonProcessingException e) {
			log.debug("JsonProcessingException");
		}

		return returnMap;
	}

	public Map<String, Object> callMyhmApi(String accessToken, Map<String, String> paramMap) {
		Map<String, Object> returnMap = new HashMap<>();
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Authorization", "Bearer " + accessToken);
		httpHeaders.add("Content-Type", "application/json");
		
		try {
			String jsonString = new ObjectMapper().writeValueAsString(paramMap);
			String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
			return CoreUtil.Json.jsonString2Map(post);
		} catch (JsonProcessingException e) {
			log.debug("JsonProcessingException");
		}
	        
		return returnMap;
	}

}
