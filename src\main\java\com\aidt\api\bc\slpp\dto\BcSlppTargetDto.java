package com.aidt.api.bc.slpp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcSlppTargetDto {
    @Parameter(name="학생사용자ID")
    private String stuUsrId;

    @Parameter(name="학생사용자이미지")
    private String stuImg;
}
