package com.aidt.api.bc.chatbot.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
public class BcCbDataAllDto{
	 private List<BcCbWdDto> snroList;
	 private List<BcCbWdDto> wdList;
	 private List<BcCbKeyWordDto> tlList;
	 private List<BcCbKeyWordDto> AiList;
}
