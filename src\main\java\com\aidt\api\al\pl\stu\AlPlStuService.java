package com.aidt.api.al.pl.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.dto.AlPlStuDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc AI맞춤학습 단원목차 테스트
 */
@Service
public class AlPlStuService {

    private final String MAPPER_NAMESPACE = "api.al.pl.stu.";

    @Autowired
    private CommonDao commonDao;
    
    /**
     * AI맞춤학습 목차목록 조회 서비스(대단원)
     * 
     * @param alPlStuDto
     * @return List<AlPlStuDto>
     */
    @Transactional(readOnly = true)
    public List<AlPlStuDto> selectAiSjList(AlPlStuDto alPlStuDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAiSjList", alPlStuDto);
    }
    
    /**
     * AI맞춤학습 목차목록 조회 서비스(중단원)
     * 
     * @param alPlStuDto
     * @return List<AlPlStuDto>
     */
    @Transactional(readOnly = true)
    public List<AlPlStuDto> selectAiSjList2(AlPlStuDto alPlStuDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAiSjList2", alPlStuDto);
    }

}
