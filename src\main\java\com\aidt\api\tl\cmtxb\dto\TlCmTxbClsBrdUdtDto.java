package com.aidt.api.tl.cmtxb.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-13 09:34:12
 * @modify date 2024-05-13 09:34:12
 * @desc [TlCmTxbClsBrdUdtDto 클래스보드 url update dto ]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbClsBrdUdtDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;

    /** 학습활동ID */
    @Parameter(name="학습활동ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrnAtvId;

    /** 사용자ID */
    @Parameter(name="사용자ID")
    private String usrId;

    /** 클래스보드URL */
    @Parameter(name="클래스보드URL", required = true)
    @NotBlank(message = "{field.required}")
    private String clsBrdUrl;

    /** 클래스보드대제목ID */
    @Parameter(name="클래스보드대제목ID")
    private String clabdLrgsId;

    /** 클래스보드소제목ID */
    @Parameter(name="클래스보드소제목ID")
    private String clabdSmlId;
}
