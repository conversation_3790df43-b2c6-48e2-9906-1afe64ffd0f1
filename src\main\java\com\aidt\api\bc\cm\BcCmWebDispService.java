package com.aidt.api.bc.cm;

import java.util.List;

import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.cm.dto.BcCmWebDispDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-11-25 10:44:00
 * @modify 2024-11-25 10:44:00
 * @desc 공통 웹전시 Service
 */
@Slf4j
@Service
public class BcCmWebDispService {
	private final String MAPPER_NAMESPACE = "api.bc.webdisp.cm.";
	
	@Autowired
    private CommonDao commonDao;
	
	@Autowired
	private JwtProvider jwtProvider;
	
	public int insertCpReq(BcCmWebDispDto dto) {
		int rtnCnt = 0;
		
		String personaId = dto.getPersonaId();
		String txbId = dto.getTxbId();
		String usrId = dto.getUsrId();
		
		if (StringUtil.isBlank(personaId) || StringUtil.isBlank(txbId) || StringUtil.isBlank(usrId)) {
			return 0;
		}
		
		for (int i=0; i<10; i++) {
			BcCmWebDispDto insDto = new BcCmWebDispDto();
			String stuId = personaId + txbId + i;
			insDto.setCpNo(i+1);
			insDto.setStuId(stuId);
			insDto.setUsrId(usrId);
			insDto.setTxbId(txbId);
			int insCnt = commonDao.insert(MAPPER_NAMESPACE + "insertCpReq", insDto);
			rtnCnt += insCnt; 
		}
		
		return rtnCnt;
	}
	
	public int copyPersonaData(BcCmWebDispDto dto) {
		
		String personaId = dto.getPersonaId();
		String txbId = dto.getTxbId();
		String usrId = dto.getUsrId();
		
		if (StringUtil.isBlank(personaId) || StringUtil.isBlank(txbId) || StringUtil.isBlank(usrId)) {
			return 0;
		}
		
		commonDao.insert(MAPPER_NAMESPACE + "callPersonaDataCp", dto);
		
		return dto.getRtnRowCnt();
	}
	
	
	public int callStuDataCp(BcCmWebDispDto dto) {
		
		List<String> stuList = dto.getStuList();
    	int stuCnt = stuList.size();
    	if (stuCnt < 1) {
    		return 0;
    	}
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		String usrId = userDetails.getUsrId();
		String optTxbId = userDetails.getOptTxbId();
		String txbId = userDetails.getTxbId();
		
		int cpNo = 0;
		int totInsCnt = 0;
		for (String stu : stuList) {
			cpNo++;
			BcCmWebDispDto insDto = new BcCmWebDispDto();
			insDto.setCpNo(cpNo);
			insDto.setStuId(stu);
			insDto.setUsrId(usrId);
			insDto.setTxbId(txbId);
			insDto.setOptTxbId(optTxbId);
			int insCnt = commonDao.insert(MAPPER_NAMESPACE + "insertCpReqOpt", insDto);
			totInsCnt += insCnt;
		}
		
		BcCmWebDispDto callDto = new BcCmWebDispDto();
		callDto.setOptTxbId(optTxbId);
		commonDao.insert(MAPPER_NAMESPACE + "callStuDataCp", callDto);
		if (callDto.getRtnRowCnt() > 0) {
			return callDto.getRtnRowCnt();
		}
		
		return totInsCnt;
	}

	public int callStuDataCpLoadTest(BcCmWebDispDto dto) {
		List<String> stuList = dto.getStuList();
		int stuCnt = stuList.size();
		if (stuCnt < 1) {
			return 0;
		}

		int cpNo = 0;
		int totInsCnt = 0;
		for (String stu : stuList) {
			cpNo++;
			BcCmWebDispDto insDto = new BcCmWebDispDto();
			insDto.setCpNo(cpNo);
			insDto.setStuId(stu);
			insDto.setUsrId(dto.getUsrId());
			insDto.setTxbId(dto.getTxbId());
			insDto.setOptTxbId(dto.getOptTxbId());
			int insCnt = commonDao.insert(MAPPER_NAMESPACE + "insertCpReqOpt", insDto);
			totInsCnt += insCnt;
		}

		BcCmWebDispDto callDto = new BcCmWebDispDto();
		callDto.setOptTxbId(dto.getOptTxbId());
		commonDao.insert(MAPPER_NAMESPACE + "callStuDataCp", callDto);
		if (callDto.getRtnRowCnt() > 0) {
			return callDto.getRtnRowCnt();
		}

		return totInsCnt;
	}

}
