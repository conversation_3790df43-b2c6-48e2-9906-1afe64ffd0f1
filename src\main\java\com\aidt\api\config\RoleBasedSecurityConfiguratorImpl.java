package com.aidt.api.config;

import com.aidt.base.config.rolebasedsecurity.RoleBasedPattern;
import com.aidt.base.config.rolebasedsecurity.RoleBasedSecurityConfigurator;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RoleBasedSecurityConfiguratorImpl implements RoleBasedSecurityConfigurator {

    @Override
    public List<RoleBasedPattern> getRoleBasedPatterns() {
        /**
         * 주의) 구체적인 패턴일 수록 리스트 상단에 위치해야 함
         */
        return List.of(
        		// specific access rules
                RoleBasedPattern.builder()
		        		.antPattern("/**/api/**/tcr/home/<USER>/**")
		                .roles(
		                        AIDTRoles.Teacher,
		                        AIDTRoles.Student
		                )
		        ,
                // common access rules
        		RoleBasedPattern.builder()
                		.antPattern("/**/api/**/tcr/**")
                		.roles(
		                        AIDTRoles.Teacher
		                        , AIDTRoles.Student /* 임시추가 */
		                )
                , RoleBasedPattern.builder()
                        .antPattern("/**/api/**/stu/**")
                        .roles(
                                AIDTRoles.Student,
                                AIDTRoles.Teacher
                        )
                , RoleBasedPattern.builder()
                        .antPattern("/**/api/**/cm/**")
                        .roles(
                                AIDTRoles.Teacher,
                                AIDTRoles.Student
                        )
				, RoleBasedPattern.builder()
						.antPattern("/**/api/**/service-usage/**")
						.roles(
								AIDTRoles.Teacher,
								AIDTRoles.Student
						)
        );
    }
}
