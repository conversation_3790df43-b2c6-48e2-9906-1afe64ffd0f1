//package com.aidt.api.appevent.web;
//
//import com.aidt.api.appevent.web.req.UserLoginUsageRequest;
//import com.aidt.base.support.util.ObjectMapperUtilPack;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.mock.web.MockFilterConfig;
//import org.springframework.security.config.BeanIds;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.MockMvcBuilder;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//import org.springframework.web.context.WebApplicationContext;
//import org.springframework.web.filter.DelegatingFilterProxy;
//
//import javax.servlet.ServletException;
//import java.time.LocalDateTime;
//import java.util.concurrent.CountDownLatch;
//
//import static org.junit.Assert.*;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
//@ActiveProfiles("local-dev")
//@SpringBootTest
//public class ServiceUsageControllerTest {
//    @Autowired
//    private WebApplicationContext webApplicationContext;
//    private MockMvc mockMvc;
//    private ObjectMapper objectMapper = ObjectMapperUtilPack.getObjectMapperUtil().getMapper();
//
//    @BeforeEach
//    public void init() throws ServletException {
////        DelegatingFilterProxy delegatingFilterProxy = new DelegatingFilterProxy();
////        delegatingFilterProxy.init(new MockFilterConfig(webApplicationContext.getServletContext(), BeanIds.SPRING_SECURITY_FILTER_CHAIN));
//        mockMvc = MockMvcBuilders
//                .webAppContextSetup(webApplicationContext)
////                .addFilter(delegatingFilterProxy)
//                .alwaysDo(print())
//                .build();
//    }
//
//
//    @Test
//    public void userloginusageShouldBePublished() throws Exception {
//
//        var req = new UserLoginUsageRequest();
//        req.setUsrId("xxxxxxx");
//        req.setTxbId("ttttttt");
//        req.setTxbNm("교과서");
//        req.setSbjCd("math");
//        req.setAutrCd("ab");
//        req.setClaId("claid");
//        req.setSchlGrdCd("E");
//        req.setSchlCd("schoolcode");
//        req.setSgyCd("04");
//        req.setUsrTpCd("ST");
//        req.setLoginDtm(LocalDateTime.now());
//
//        var countdown = new CountDownLatch(10);
//
//        for(var i=0; i < 10; ++i) {
//
//            mockMvc.perform(post("/api/v1/service-usage/user-login")
//                    .content(objectMapper.writeValueAsString(req))
//                    .contentType(MediaType.APPLICATION_JSON)
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//
//            countdown.countDown();
//        }
//
//        countdown.await();
//
//        Thread.sleep(3 * 1000);
//
//
//    }
//
//
//    @Test
//    public void serviceusageEventsShouldBePublished() throws Exception {
//
//        var targetCount = 100000;
//
//        var countdown = new CountDownLatch(targetCount);
//
//        for(var i=0; i < targetCount; ++i) {
//
//            var req = new UserLoginUsageRequest();
//            req.setUsrId("xxxxxxx");
//            req.setTxbId("ttttttt");
//            req.setTxbNm("교과서");
//            req.setSbjCd("math");
//            req.setAutrCd("ab");
//            req.setClaId("claid");
//            req.setSchlGrdCd("E");
//            req.setSchlCd("schoolcode");
//            req.setSgyCd("04");
//            req.setUsrTpCd("ST");
//            req.setLoginDtm(LocalDateTime.now());
//
//            mockMvc.perform(post("/api/v1/service-usage/user-login")
//                    .content(objectMapper.writeValueAsString(req))
//                    .contentType(MediaType.APPLICATION_JSON)
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//
//
//
//            mockMvc.perform(get("/api/v1/service-usage/class-open")
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//            mockMvc.perform(get("/api/v1/service-usage/opt-textbook-open")
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//            mockMvc.perform(get("/api/v1/service-usage/new-student-join")
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//            mockMvc.perform(get("/api/v1/service-usage/student-leave")
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//            mockMvc.perform(get("/api/v1/service-usage/teacher-leave")
//                    .accept(MediaType.APPLICATION_JSON)
//            ).andExpect(status().isOk());
//
//            countdown.countDown();
//        }
//
//        countdown.await();
//
//        Thread.sleep(3 * 1000);
//
//
//    }
//
//
//
//
//}