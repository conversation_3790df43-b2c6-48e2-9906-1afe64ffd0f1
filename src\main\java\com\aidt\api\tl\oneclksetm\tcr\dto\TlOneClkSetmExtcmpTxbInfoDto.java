package com.aidt.api.tl.oneclksetm.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-11-12 16:23:14
 * @modify date 2024-11-12 16:23:14
 * @desc [TlOneClkSetmExtcmpTxbInfoDto 원클릭학습설정 타사교과 정보 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlOneClkSetmExtcmpTxbInfoDto {
	/** 대단원ID */
    @Parameter(name="대단원ID")
    private String lluNodId;
    
    /** 대단원명 */
    @Parameter(name="대단원명")
    private String lluNodNm;
    
    /** 순서 */
    @Parameter(name="순서")
    private int srtOrdn; 
}
