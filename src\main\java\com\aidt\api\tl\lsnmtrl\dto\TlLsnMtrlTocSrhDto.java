package com.aidt.api.tl.lsnmtrl.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLsnMtrlTocSrhDto 학습자료 단원목차 조회조건
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TlLsnMtrlTocSrhDto {
    /** 사용자구분코드 */
    @Parameter(name="사용자구분코드")
    private String usrDvCd;

    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID")
    private String lrmpNodId;

    /** 대단원ID */
    @Parameter(name="대단원ID")
    private String lluNodId;

    /** 학습자료ID */
    @Parameter(name="학습자료ID")
    private String annlstLrnMtrlId;

    /** 사용자ID */
    @Parameter(name="사용자ID")
    private String usrId;

    /** 모든데이터조회여부 */
    @Parameter(name="모든데이터조회여부")
    private String allDatYn;

}
