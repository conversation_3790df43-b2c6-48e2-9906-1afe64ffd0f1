package com.aidt.api.al.pl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-23 10:00:32
 * @modify date 2024-02-23 10:00:32
 * @desc [학습목록조회 학습도구 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class LrnTlDto {
	@Parameter(name="학습도구코드")
	private String lrnTlCd;
	
	@Parameter(name="학습도구명")
	private String lrnTlNm;
	
	@Parameter(name="아이콘CDN경로명")
	private String icnCdnPthNm;
	
	@Parameter(name="아이콘파일유형코드")
	private String icnFleTpCd;
}
