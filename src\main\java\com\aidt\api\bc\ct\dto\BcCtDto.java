package com.aidt.api.bc.ct.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "URL 전송")
public class BcCtDto {
	@Parameter(name="순번")
	private int seqNo;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="URL")
	private String url;
	
	@Parameter(name="사용자ID")
	private String usrId;	
	
	@Parameter(name="생성자ID")
	private String crtrId;
	
	@Parameter(name="생성일시")
	private String crtDtm;
	
	@Parameter(name="수정자ID")
	private String mdfrId;
	
	@Parameter(name="수정일시")
	private String mdfDtm;
}
