package com.aidt.api.at.token.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:59:47
 * @modify 2024-01-05 17:59:47
 * @desc 첨부파일 dto
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class KerisDto {

	private AccessToken access_token;

    public static class AccessToken {
        private String token;
        private String access_id;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getAccess_id() {
            return access_id;
        }

        public void setAccess_id(String access_id) {
            this.access_id = access_id;
        }
    }

	private String api_domain;
	private String user_type;
	private String user_status;
	private String user_id;
	private String count;
	private List<KerisLrnDataDto> data;

	// 2024-06-23 KERIS API에서 lecture_code, class_code를 준다는 가정
	private String lecture_code;
	private String class_code;
	
	// 2025.03.21 추가
	private String class_period;
	
	// 2025.01.13 규격 2.3버전 api_version 추가
	private String api_version;
	
	// 2025.02.04 cm_token, cm_usr keris_usr_id 컬럼 추가에 따른 추가
	private String kerisUsrId;
	
	private String KafkaUsrId;
	private String kafkaOptTxbId;
	private String kafkaOptTxbPrid;
	
	// 2025.02.17 규격 2.3버전 이용약관 추가 (entrusted_info 제외)
	private String use_terms_agree_yn;
	private String use_terms_agree_dt;

}
