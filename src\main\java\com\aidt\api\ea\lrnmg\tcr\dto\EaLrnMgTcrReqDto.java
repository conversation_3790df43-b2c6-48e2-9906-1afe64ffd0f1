package com.aidt.api.ea.lrnmg.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrReqDto {

	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="학급ID", required = true)
	private String claId;
	
	@Parameter(name="조회구분")
	private String searchType;	
	
	@Parameter(name = "단원 ID")
	private String kmmpNodId;	

	@Parameter(name = "사용자ID")
	private String usrId;	
	
	@Parameter(name = "사용자명")
	private String usrNm;	
	
	@Parameter(name = "학생 번호")
	private String usrNo;	
	
	@Parameter(name = "과목")
	private String sbjCd;
	
	@Parameter(name = "학습 수준")
	private String lrnrVelTpCd;
	
	@Parameter(name = "전체 진행율") //종합현황
	private String completionPercentage;
	
	@Parameter(name = "번호") //종합현황
	private String rowNum;
	
	@Parameter(name = "미완료 여부") //과제 미완료 여부
	private String ncmplYn;
	
	@Parameter(name = "선생님Id")
	private String tcrUsrId;
	
	@Parameter(name="페이지 사이즈")
	private int pageSize;
	
	@Parameter(name="페이지 번호")
	private int pageNo;
	
	@Parameter(name="평가에서 탭번호")
	private int tbscDvCdEv;
	
	@Parameter(name="대단원")
	private int srhLuLrmpNodId;
	
	@Parameter(name="평가응시상태")
	private String evExmState;
	
	@Parameter(name="정렬구분코드")
	private int srtDvCd;
	
	//과제 구분1
	@Parameter(name="구분1")
	private String dropdown1;
	
	//과제 구분2
	@Parameter(name="구분2")
	private String dropdown2;
	
	@Parameter(name="관심학생구분")
	private String rtmAbnBhvTpCd;
	
	
	@Parameter(name="ai조회조건")
	private List<String> aiSearchOptionList;
	
	public void setAiSearchOptionList(List<String> aiSearchOptionList) {
	    this.aiSearchOptionList = aiSearchOptionList;
	}
}
