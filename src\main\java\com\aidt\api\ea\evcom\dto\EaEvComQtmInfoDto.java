package com.aidt.api.ea.evcom.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 13:58:오후 1:58
 * @modify date 2024-02-15 13:58:오후 1:58
 * @desc   평가 공통 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvComQtmInfoDto {
    //문항 테이블
    @Parameter(name="문항 ID")
    private String qtmId;
    @Parameter(name="문항 순번")
    private String qtmOrdn;

	@Parameter(name="삭제여부")
	private String delYn;
	@Parameter(name="삭제일시")
	private String delDtm;

	@Parameter(name="제출 답변 값")
    private String smtAnwVl;

    @Parameter(name="문제유형코드(50: 5지 선택, 60:단답 유순형, 61:단답 무순형, 70: 논술형, 85: 서술형)")
    private String questionFormCode;
    @Parameter(name="문제유형 명")
    private String questionFormName;

    @Parameter(name="난이도코드")
    private String difficultyCode;
    @Parameter(name="난이도명")
    private String difficultyName;

    @Parameter(name="질문")
    private String question;
    @Parameter(name="질문html")
    private String questionHtml;
    @Parameter(name="질문htmlTemplate")
    private String questionHtmlTemplate;

    @Parameter(name="선택1html")
    private String choice1Html;
    @Parameter(name="선택2html")
    private String choice2Html;
    @Parameter(name="선택3html")
    private String choice3Html;
    @Parameter(name="선택4html")
    private String choice4Html;
    @Parameter(name="선택5html")
    private String choice5Html;

    @Parameter(name="선택1htmlTemplate")
    private String choice1HtmlTemplate;
    @Parameter(name="선택2htmlTemplate")
    private String choice2HtmlTemplate;
    @Parameter(name="선택3htmlTemplate")
    private String choice3HtmlTemplate;
    @Parameter(name="선택4htmlTemplate")
    private String choice4HtmlTemplate;
    @Parameter(name="선택5htmlTemplate")
    private String choice5HtmlTemplate;

    @Parameter(name="템플릿 xml 내용")
    private String itemStyleXml;

    @Parameter(name="정답")
    private String answer;
    @Parameter(name="정답html")
    private String answerHtml;
    @Parameter(name="정답htmlTemplate")
    private String answerHtmlTemplate;

    @Parameter(name="해설html")
    private String explainHtml;
    @Parameter(name="해석html")
    private String translateHtml;
    @Parameter(name="힌트html")
    private String hintHtml;
    @Parameter(name="해설htmlTemplate")
    private String explainHtmlTemplate;
    @Parameter(name="해석htmlTemplate")
    private String translateHtmlTemplate;
    @Parameter(name="힌트htmlTemplate")
    private String hintHtmlTemplate;

    @Parameter(name="지문ID")
    private String passageID;
    @Parameter(name="지문html")
    private String passageHtml;
    @Parameter(name="지문HtmlTemplate")
    private String passageHtmlTemplate;
    @Parameter(name="지문Template xml내용")
    private String passageTemplateXml;

    @Parameter(name="문항등록형태")
    private String ItemRegisterType;

    @Parameter(name="질문 url")
    private String questionUrl;
    @Parameter(name="정답 url")
    private String answerUrl;
    @Parameter(name="해설 url")
    private String explainUrl;
    @Parameter(name="해설 url")
    private String htmlZipUrl;

    @Parameter(name="문통대단원ID")
    private String lluId;
    @Parameter(name="문통대단원명")
    private String lluNm;
    @Parameter(name="문통토픽ID")
    private String topicId;
    @Parameter(name="문통토픽명")
    private String topicNm;
    @Parameter(name="문항토픽순번")
    private String topicOrdn;


    @Parameter(name="지식맵토픽ID")
    private String topicIdKmmp;
    @Parameter(name="지식맵토픽명")
    private String topicNmKmmp;
    @Parameter(name="문항키워드명")
    private String kwdNm;

	@Parameter(name="정답률")
	private Double cansRt;

    @Parameter(name="복수정답수")
    private int plurCansCnt;

    @Parameter(name="형판코드")
    private String choiceArrayCode;
    @Parameter(name="형판명")
    private String choiceArrayName;

}
