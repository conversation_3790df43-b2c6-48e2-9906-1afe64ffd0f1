/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-20 10:32:05
 * @modify date 2024-05-20 10:32:05
 * @desc [TlCmTxbTotLrnTmDto 학생 별 총 학습시간 dto]
 */

package com.aidt.api.tl.cmtxb.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbTotLrnTmDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학급 ID */
    @Parameter(name="학급 ID")
    private String claId;

    /** 학생 ID */
    @Parameter(name="학생 ID")
    private String usrId;

    /** 총 학습 시간(초) */
    @Parameter(name="총 학습 시간(초)")
    private int totLrnTm;
}
