package com.aidt.api.bc.focLrn.stu;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.focLrn.dto.BcFlDto;
import com.aidt.api.bc.focLrn.tcr.BcFlTcrService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name="[bc] 선생님 집중 학습 관련 정보", description="선생님 집중 학습 관련 정보")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/stu/focLrn")
public class BcFlStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
	private BcFlTcrService bcFlTcrService;
    
    /** DB-ID */
    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;
    
    @Operation(summary="집중 학습 정보", description="선생님이 공유한 화면 정보를 가져온다.")
    @PostMapping(value = "/selectFocLrnInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BcFlDto selectFocLrnInfo() {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        BcFlDto schDto = new BcFlDto();
        schDto.setOptTxbId(userDetails.getOptTxbId());
        return bcFlTcrService.selectFocLrnInfo(schDto);
    }
}
