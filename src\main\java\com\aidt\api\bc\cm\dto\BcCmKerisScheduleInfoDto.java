package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCmKerisScheduleInfoDto {
    private String day_week;          // 요일 코드
    private String class_period;      // 교시
    private String subject_name;      // 과목명
    private String classroom_name;    // 교실명
    private String lecture_code;      // 강의 코드
}
