package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21
 * @modify date 2024-05-21
 * @desc 평가 관리 - 채점완료 - 문항 답변리스트 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlPlEaEvComQtmAnwDto {

	@Parameter(name="평가ID")
	private String evId;
	
	@Parameter(name="평가명")
	private String evNm;	
	
	@Parameter(name="문항ID")
	private String qtmId;

	@Parameter(name="문항번호")
	private String qtmNo;
	
	@Parameter(name="문항순서")
	private String qtmOrdn;
	
	@Parameter(name="문항난이도")
	private String qtmDffdDvCd;

	@Parameter(name="삭제여부")
	private String delYn;
	
	@Parameter(name="삭제일시")
	private String delDtm;
		
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="제출답변값") 
	private String smtAnwVl;
	
	@Parameter(name="정답여부") 
	private String cansYn;
	
	@Parameter(name="풀이시간초수") 
	private long xplTmScnt;
	
	@Parameter(name="풀이시간초수명") 
	private String xplTmScntNm;	
	
	@Parameter(name="찜여부") 
	private String dibsYn;
	
	@Parameter(name="찜생성일시") 
	private String dibsCrtDtm;
	
	@Parameter(name="생성자ID")
	private String crtrId;
	
	@Parameter(name="생성일시")
	private String crtDtm;
	
	@Parameter(name="수정자ID")
	private String mdfrId;
	
	@Parameter(name="수정일시")
	private String mdfDtm;
	
	@Parameter(name="데이터베이스ID")
	private String dbId;
	
	@Parameter(name="지식맵 토픽명")
	private String qpTcNm;
	
	@Parameter(name="차시 지식맵 노드명")
	private String tcKmmpNodNm;
	
	@Parameter(name="차시별 맞힌 문항수")
	private int sumCansYn;
	
	@Parameter(name="차시별 정답률")
	private int tcCansRt;
	
	@Parameter(name="차시 지식맵 노드ID")
	private String tcLrmpNodId;
	
	@Parameter(name="지식맵 토픽명")
	private String tpcNm;
	
	@Parameter(name="지식맵 토픽명")
	private String tpcKmmpNodId;
	
	@Parameter(name="지식맵 토픽명")
	private String tpcKmmpNodNm;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;	
	
	@Parameter(name="차시별 문항수")
	private int qtmCnt;
	
	@Parameter(name = "평균 풀이시간")
	private String avgXplTmScnt;
	
	@Parameter(name = "평균 정답률")
	private double avgCansRt;
	
	@Parameter(name = "힌트사용여부")
	private String hntCofmYn;
	
	@Parameter(name = "풀이상태코드")
	private String xplStCd;
	
	@Parameter(name = "풀이상태코드명")
	private String xplStNm;
	
	@Parameter(name = "해설영상경로")
	private String flePthNm;
	
	@Parameter(name = "문항플랫폼 문제형태 코드")
	private String qpQstTypCd;
	

}
