package com.aidt.api.bc.claan.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcClaanResDto {
	
	@Parameter(name="학습한 학생 수")
	private int lrnUsrCnt;
	
	@Parameter(name="평균 학습일")
	private int avgLrnDtCnt;
	
	@Parameter(name="최소 학습일")
	private int minLrnDtCnt;
	
	@Parameter(name="최대 학습일")
	private int maxLrnDtCnt;
	
	@Parameter(name="평균 학습시간 시")
	private String avgLrnHour;
	
	@Parameter(name="평균 학습시간 분")
	private String avgLrnMin;
	
	@Parameter(name="최소 학습시간")
	private String minLrnTime;
	
	@Parameter(name="최대 학습시간")
	private String maxLrnTime;
	
	@Parameter(name="평균 문제풀이 수")
	private int avgQstCnt;
	
	@Parameter(name="최소 문제풀이 수")
	private int minQstCnt;
	
	@Parameter(name="최대 문제풀이 수")
	private int maxQstCnt;
	
	@Parameter(name="평균 정답률")
	private Double avgCansRt;
	
	@Parameter(name="최소 정답률")
	private Double minCansRt;
	
	@Parameter(name="최대 정답률")
	private Double maxCansRt;
	
	@Parameter(name="학습맵노드ID")
	private String lrmpNodId;
	
	@Parameter(name="학습맵노드명")
	private String lrmpNodNm;
	
	@Parameter(name="단원순서")
	private String rcstnOrdn;
	
	@Parameter(name="총문제수")
	private int fnlQstCnt;
	
	@Parameter(name="정답문제수")
	private int cansCnt;
	
	@Parameter(name="정답률")
	private Double cansRt;
	
	@Parameter(name="최하 정답률")
	private Double tlrkCansRt;
	
	@Parameter(name="하 정답률")
	private Double lrkCansRt;
	
	@Parameter(name="중 정답률")
	private Double mddlCansRt;
	
	@Parameter(name="상 정답률")
	private Double urnkCansRt;
	
	@Parameter(name="최상 정답률")
	private Double thrkCansRt;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
	@Parameter(name="평가상세구분명")
	private String evDtlDvNm;
	
	@Parameter(name="평가ID")
	private String evId;
	
	@Parameter(name="평가명")
	private String evNm;
	
	@Parameter(name="평가구분코드")
	private String evDvCd;
	
	@Parameter(name="평가구분명")
	private String evDvNm;
	
	@Parameter(name="학생ID")
	private String stuId;
	
	@Parameter(name="학생명")
	private String stuNm;
	
	@Parameter(name="학생ID")
	private List<BcClaanResDto> tcAnStuList;
	
	@Parameter(name="교육과정성취기준코드")
	private String eduCrsAchBsCd;
	
	@Parameter(name="교육과정성취기준내용명")
	private String eduCrsAchBsCnNm;
	
	@Parameter(name="교육과정내용코드")
	private String eduCrsCnCd;
	
	@Parameter(name="교육과정내용명")
	private String eduCrsCnNm;
	
	@Parameter(name="학교 전체 평균")
	private Double schlCansRt;
	
	@Parameter(name="학교 전체 문제수")
	private Double schlFnlQstCnt;
	
	@Parameter(name="학교 전체 정답수")
	private Double schlCansCnt;
	
	@Parameter(name="노출학생수")
	private int showStuCnt;
	
	@Parameter(name="문제수")
	private int qtmCnt;
	
	@Parameter(name="학교 전체 문제수")
	private int schlQtmCnt;
	
	@Parameter(name="학교 전체 학습시간")
	private long schlSumLrnScnt;
	
	@Parameter(name="학습시간")
	private long sumLrnScnt;
	
	@Parameter(name="단원번호")
	private int lrmpNum;
	
	@Parameter(name="단원숫자사용여부")
	private String luNoUseYn;
	
}
