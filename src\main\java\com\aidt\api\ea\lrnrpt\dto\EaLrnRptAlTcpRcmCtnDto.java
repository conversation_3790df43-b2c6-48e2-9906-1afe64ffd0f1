package com.aidt.api.ea.lrnrpt.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EaLrnRptAlTcpRcmCtnDto {
	
	String lluKmmpNodId;
	String mluKmmpNodId;
	String sluKmmpNodId;
	String tcKmmpNodId;
	String tpcKmmpNodId;
	String sluKmmpNodNm;
	String tcKmmpNodNm;		
	String tpcKmmpNodNm;	
	String aiLrnAtvId;		//AI학습활동ID
	String aiLrnAtvNm;		//학습활동명
	
	String ctnTpCd;			//컨텐츠유형코드
	String cdnPthNm;		//파일 path
	String kmmpNodId;		//지식맵 노드 ID
	
	
	

}
