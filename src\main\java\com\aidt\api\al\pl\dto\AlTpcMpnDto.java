package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlTpcMpnDto {
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="중단원 지식맵 노드ID")
	private String mluKmmpNodId;
	private String mluKmmpNodNm;

	@Parameter(name="중단원 학습맵 노드ID")
	private String mluLrmpNodId;
	private String mluLrmpNodNm;
	
	@Parameter(name="차시 지식맵 노드ID")
	private String tcKmmpNodId;
	private String tcKmmpNodNm;
	
	@Parameter(name="토픽 지식맵 노드ID")
	private String tpcKmmpNodId;
	private String tpcKmmpNodNm;
	
	@Parameter(name="연결토픽 지식맵 노드ID")
	private String lnkgTpcKmmpNodId;
	private String lnkgTpcKmmpNodNm;
	
	@Parameter(name="토픽연결구분코드")
	private String tpcLnkgDvCd;
	
	@Parameter(name="토픽숙련도")
	private Double tpcAvn;
	
	@Parameter(name="토픽숙련도(취약:01, 보통:02, 완벽:03)")
	private String tpcAvnCd;
	
	// 학습현황
	@Parameter(name="사용자이름")
	private String usrNm;
	
	@Parameter(name="총 풀이시간")
	private String hours;
	private String minutes;
	private String seconds;
	
	@Parameter(name="최근학습일")
	private String mdfDtm;
	
	@Parameter(name="진단평가아이디")
	private String OVEvId;
	
	@Parameter(name="진단평가완료여부")
	private String OVCmplYn;
	
	@Parameter(name="맞춤1아이디")
	private String C1EvId;
	
	@Parameter(name="맞춤1 완료여부")
	private String C1EvCmplYn;
	
//	@Parameter(name="맞춤3아이디")
//	private String C3EvId;
	
//	@Parameter(name="맞춤3완료여부")
//	private String C3EvCmplYn;
	
	@Parameter(name="사용자수준코드")
	private String lrnrVelTpCd;
	
	@Parameter(name="학생번호")
	private String stuNo;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
	@Parameter(name = "학습여부")
	private String lrnYn;
	
	@Parameter(name="진단평가 토픽숙련도")
	private Double aiDgnEvTpcAvn;
	
	@Parameter(name = "AI진단평가예측평균정답률")
	private Double aiDgnEvPredAvgCansRt;
	
	@Parameter(name = "토픽 원본 순서")
	private int srtOrdn;
}
