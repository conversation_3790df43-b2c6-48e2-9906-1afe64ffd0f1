package com.aidt.api.tl.lsnmtrl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-09 12:17:00
 * @modify date 2024-04-09 12:17:00
 * @desc TlLsnMtrlInfoDto 교사용 학습자료정보Dto
 */

@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLsnMtrlInfoDto {

    /** LCMS기본자료목록 */
    @Parameter(name="기본자료목록")
    private List<TlLsnMtrlDto> bsMtrlList;

    /** LMS학습자료목록 */
    @Parameter(name="기본자료목록")
    private List<TlLsnMtrlDto> lmMtrlList;
}
