package com.aidt.api.at.token;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.util.Base64;

/**
 * AES 암호화/복호화 유틸리티 클래스
 */
public class AesEncryptionUtil {
    private static final String AES = "AES";
    private static final String AES_CBC_PADDING = "AES/CBC/PKCS5Padding";
    private static final String SECRET_KEY = "lVbJ52AyFqlI5BZPt/MLGWp5/Wpt1hGqUC9nqK2zryM=";
    private static final String IV_KEY = "QYr26y5cS7vcmu9U7TiRVw==";

    /**
     * SecretKey 객체 생성
     * @return
     */
    private static SecretKey getSecretKey() {
        byte[] decodedKey = Base64.getDecoder().decode(SECRET_KEY);
        return new SecretKeySpec(decodedKey, AES);
    }

    /**
     * IvParameterSpec 객체 생성
     * @return
     */
    private static IvParameterSpec getIvParameterSpec() {
        byte[] decodedIv = Base64.getDecoder().decode(IV_KEY);
        return new IvParameterSpec(decodedIv);
    }

    /**
     * 암호화
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String encrypt(String plainText) throws Exception {
        Cipher cipher = Cipher.getInstance(AES_CBC_PADDING);
        cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(), getIvParameterSpec());
        byte[] cipherText = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(cipherText);
    }

    /**
     * 복호화
     * @param cipherText
     * @return
     * @throws Exception
     */
    public static String decrypt(String cipherText) throws Exception{
        Cipher cipher = Cipher.getInstance(AES_CBC_PADDING);
        cipher.init(Cipher.DECRYPT_MODE, getSecretKey(), getIvParameterSpec());
        byte[] plainText = cipher.doFinal(Base64.getDecoder().decode(cipherText));
        return new String(plainText);
    }
}
