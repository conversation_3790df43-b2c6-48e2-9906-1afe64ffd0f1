package com.aidt.api.ea.evcom.event;

import com.aidt.api.ea.evcom.dto.EaEvAnswerReqDto;
import com.aidt.api.ea.evcom.dto.EaEvResult;
import com.aidt.common.CommonUserDetail;

import lombok.Getter;

@Getter
public class EaEvAnswerSubmitEvent {

	private final EaEvAnswerReqDto eaEvAnswerReqDto;
	private final EaEvResult eaEvRs;
	private final CommonUserDetail userDetail;
	private final String accessToken;

	public EaEvAnswerSubmitEvent(EaEvAnswerReqDto eaEvAnswerReqDto, EaEvResult eaEvRs, CommonUserDetail userDetail,
		String accessToken) {
		this.eaEvAnswerReqDto = eaEvAnswerReqDto;
		this.eaEvRs = eaEvRs;
		this.userDetail = userDetail;
		this.accessToken = accessToken;
	}
}
