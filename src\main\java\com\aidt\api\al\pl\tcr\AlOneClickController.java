package com.aidt.api.al.pl.tcr;

import com.aidt.api.al.pl.dto.AlOneClickDto;
import com.aidt.api.al.pl.dto.AlOneClkSetmAlPlDto;
import com.aidt.api.ea.evcom.EaEvComService;
import com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * AI맞춤 학습창연계처리 서비스
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/tcr/oneClick")
@Tag(name="[al] AI맞춤 원클릭 학습재구성", description="AI맞춤 원클릭 학습재구성")
public class AlOneClickController {
	
	@Autowired 
	private AlOneClickService alOneClickService;
	
	private final JwtProvider jwtProvider;

    @Autowired
    private EaEvComService eaEvComService;
	
	/**
     * 원클릭학습설정 AI지식맵재구성 저장
     * 
     * @param alOneClickDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 AI지식맵 재구성 저장", description="원클릭학습설정 AI지식맵 재구성 저장")
    @PostMapping(value = "/updateRcstn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateRcstn(@RequestBody List<AlOneClickDto> alOneClickDto) {
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(alOneClickDto.get(0).getOptTxbId() == null) {
			alOneClickDto.get(0).setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(alOneClickDto.get(0).getUsrId() == null) {
        	alOneClickDto.get(0).setUsrId(securityUserDetailDto.getUsrId());
        }
        Map<String, Object> mapData = alOneClickService.updateRcstn(alOneClickDto);

        EaEvUpdateLockUseYnDto evReqDto = new EaEvUpdateLockUseYnDto();
        evReqDto.setUsrId(securityUserDetailDto.getUsrId());
        evReqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());

        List<Map<String, Object>> evListMap = null;
        int retVal = 1;

        // mapData에서 evList가 존재하고, List<Map<String, Object>> 타입인지 확인
        if (mapData.containsKey("evList") && mapData.get("evList") instanceof List<?>) {
            Object evListObj = mapData.get("evList");

            // List<?> 타입인지 명확히 확인
            if (evListObj instanceof List<?>) {
                List<?> list = (List<?>) evListObj;

                // 리스트의 첫 번째 요소가 Map<String, Object> 타입인지 확인 (명확히 확인)
                if (!list.isEmpty() && list.get(0) instanceof Map<?, ?>) {
                    evListMap = (List<Map<String, Object>>) list;

                    // evList가 비어 있지 않은 경우에만 실행
                    if (!evListMap.isEmpty()) {
                        List<EaEvUpdateLockUseYnDto> evList = new ArrayList<>();

                        // evListMap을 순회하면서 DTO 리스트를 채움
                        for (Map<String, Object> map : evListMap) {
                            EaEvUpdateLockUseYnDto dto = new EaEvUpdateLockUseYnDto();
                            // 맵의 데이터를 DTO에 설정
                            Object evIdObject = map.get("evId");
                            if (evIdObject instanceof Integer) {
                                dto.setEvId(((Integer) evIdObject).longValue());
                            } else if (evIdObject instanceof Long) {
                                dto.setEvId((Long) evIdObject);
                            } else {
                                // evId가 예상한 타입이 아닐 경우 처리 (예: 예외 던지기)
                                throw new IllegalArgumentException("evId is not a valid type: " + evIdObject.getClass().getName());
                            }

                            dto.setLcknYn((String) map.get("lcknYn"));
                            dto.setUseYn((String) map.get("useYn"));
                            evList.add(dto);
                        }

                        // evList를 DTO에 설정
                        evReqDto.setEvList(evList);

                        // 서비스 호출
                        retVal = eaEvComService.updateEvOneClickLrnSetmAL(evReqDto);
                    }
                }
            }
        }

        return retVal == 1 ? Response.ok() : Response.fail();

    }

    
    
    @Operation(summary="원클릭학습설정 AI지식맵 재구성 다른 학급 저장", description="원클릭학습설정 AI지식맵 재구성 다른 학급 저장")
    @PostMapping(value = "/updateAlPlRcstn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateAlPlRcstn(@RequestBody AlOneClkSetmAlPlDto alOneClickDto) {
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
        String mdfrId = securityUserDetailDto.getUsrId();
        String orgnOptTxbId = securityUserDetailDto.getOptTxbId();
        Map<String, Object> mapData = alOneClickService.updateAlPlRcstn(alOneClickDto, mdfrId, orgnOptTxbId);

        EaEvUpdateLockUseYnDto evReqDto = new EaEvUpdateLockUseYnDto();
        evReqDto.setUsrId(securityUserDetailDto.getUsrId());
        evReqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());

        List<Map<String, Object>> evListMap = null;
        int retVal = 1;

        // mapData에서 evList가 존재하고, List<Map<String, Object>> 타입인지 확인
        if (mapData.containsKey("evList") && mapData.get("evList") instanceof List<?>) {
            Object evListObj = mapData.get("evList");

            // List<?> 타입인지 명확히 확인
            if (evListObj instanceof List<?>) {
                List<?> list = (List<?>) evListObj;

                // 리스트의 첫 번째 요소가 Map<String, Object> 타입인지 확인 (명확히 확인)
                if (!list.isEmpty() && list.get(0) instanceof Map<?, ?>) {
                    evListMap = (List<Map<String, Object>>) list;

                    // evList가 비어 있지 않은 경우에만 실행
                    if (!evListMap.isEmpty()) {
                        List<EaEvUpdateLockUseYnDto> evList = new ArrayList<>();

                        // evListMap을 순회하면서 DTO 리스트를 채움
                        for (Map<String, Object> map : evListMap) {
                            EaEvUpdateLockUseYnDto dto = new EaEvUpdateLockUseYnDto();
                            // 맵의 데이터를 DTO에 설정
                            Object evIdObject = map.get("evId");
                            if (evIdObject instanceof Integer) {
                                dto.setEvId(((Integer) evIdObject).longValue());
                            } else if (evIdObject instanceof Long) {
                                dto.setEvId((Long) evIdObject);
                            } else {
                                // evId가 예상한 타입이 아닐 경우 처리 (예: 예외 던지기)
                                throw new IllegalArgumentException("evId is not a valid type: " + evIdObject.getClass().getName());
                            }

                            dto.setLcknYn((String) map.get("lcknYn"));
                            dto.setUseYn((String) map.get("useYn"));
                            evList.add(dto);
                        }

                        // evList를 DTO에 설정
                        evReqDto.setEvList(evList);

                        // 서비스 호출
                        retVal = eaEvComService.updateEvOneClickLrnSetmAL(evReqDto);
                    }
                }
            }
        }

        return retVal == 1 ? Response.ok() : Response.fail();

    }
}
