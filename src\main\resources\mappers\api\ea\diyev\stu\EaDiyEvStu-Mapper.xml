<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.diyev.stu">

    <select id="selectDiyEvStuList" parameterType="com.aidt.api.ea.evcom.diy.dto.EaDiyEvReqDto"
            resultType="com.aidt.api.ea.evcom.diy.dto.EaDiyEvResDto">

        SELECT
             EE.EV_ID                                   -- 평가 ID
            ,COUNT(1) OVER() 		AS totalCnt 		 -- 총 조회 갯수
            ,EE.OPT_TXB_ID                              -- 운영 교과서 ID
            ,EER.USR_ID                                 -- 사용자 ID
            ,EE.EV_NM                                   -- 평가명
            ,EE.QST_CNT                                 -- 문제수
            ,EE.LCKN_YN                                 -- 잠금 여부
            ,EE.RTXM_PMSN_YN                            -- 재응시 허용 여부
            ,DATE_FORMAT(EE.CRT_DTM ,'%m.%d') CRT_DTM 	-- 생성 일시
            ,EER.EV_CMPL_YN
            ,EER.TXM_STR_YN
            ,CASE WHEN SMT_DTM IS NULL THEN '-'
            	 ELSE DATE_FORMAT(EER.SMT_DTM,'%Y.%m.%d')
            	 END SMT_DTM -- 제출 일시(평가일)
            ,EER.EV_CMPL_YN                             -- 평가 완료 여부
            ,CASE WHEN EER.EV_CMPL_YN = 'Y' THEN '다시 풀기'
            	 ELSE '문제 풀기'
            	 END AS EV_BTN_NM             -- (응시완료: 다시풀기, 미응시+응시중 : 문제풀기)
            ,CASE WHEN EER.TXM_STR_YN = 'N' AND EER.EV_CMPL_YN = 'N' THEN '미응시'
            	 WHEN EER.TXM_STR_YN = 'Y' AND EER.EV_CMPL_YN = 'N' THEN '응시 중'
            	 WHEN EER.EV_CMPL_YN = 'Y' THEN '응시 완료'
            	 END AS EV_CMPL_NM
            , IFNULL((SELECT MAX(RTXM_PN) FROM LMS_LRM.EA_EV_RS_RTXM WHERE EV_ID = EE.EV_ID AND USR_ID = #{usrId} AND EV_CMPL_YN = 'Y'), 0) txmPn -- 응시회차 (본인의 응시정보)
        FROM LMS_LRM.EA_EV EE
        JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
        WHERE EE.OPT_TXB_ID = #{optTxbId} -- 운영 교과서 ID
        <if test='evId != null and evId neq "" '>
        AND EE.EV_ID = #{evId} -- 평가 ID
        </if>
        AND EER.USR_ID   = #{usrId}        -- 사용자 ID
        AND EE.USE_YN   = 'Y'
        AND EE.DEL_YN   = 'N'
        AND EE.EV_DV_CD = 'DE' -- DIY 평가 코드
        <if test='evCmplYn != null and evCmplYn neq "" '>
        AND EER.EV_CMPL_YN = #{evCmplYn} -- 평가 완료 여부
        </if>
        ORDER BY EE.CRT_DTM DESC
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}

        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 목록 조회 */
    </select>

    <update id="updateDiyEvStuNm" parameterType="com.aidt.api.ea.evcom.diy.dto.UpdateDiyEvNmDto">

        UPDATE LMS_LRM.EA_EV
        SET EV_NM = #{evNm},
            MDFR_ID = #{usrId},
            MDF_DTM = NOW(),
            DB_ID   = #{dbId}
        WHERE EV_ID = #{evId}


        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 시험지 명 변경 */
    </update>

    <update id="deleteDiyEvStu" parameterType="com.aidt.api.ea.evcom.diy.dto.DeleteDiyEvDto">

        UPDATE LMS_LRM.EA_EV
        SET
            DEL_YN  = 'Y',
            MDFR_ID = #{usrId},
            MDF_DTM = NOW(),
            DB_ID   = #{dbId}
        WHERE EV_ID = #{evId}


        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 삭제 */
    </update>


    <insert id="insertDiyEvStu" useGeneratedKeys="true" keyProperty="evId" keyColumn="ev_id">

        INSERT INTO LMS_LRM.EA_EV
        (
              OPT_TXB_ID, USR_ID, EV_DV_CD, EV_NM
            , QST_CNT, FNL_QST_CNT
            , RTXM_PMSN_YN, LCKN_YN, USE_YN, DEL_YN
            , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES
        (
              #{optTxbId}, #{usrId}, 'DE', #{evNm}
            , #{qstCnt}, #{qstCnt}
            , #{rtxmPmsnYn}, 'N', 'Y', 'N'
            , #{usrId}, now(), #{usrId}, now(), #{dbId}
        )

        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 EA_EV 테이블 등록 */
    </insert>

    <insert id="insertDiyEvStuRs">

        INSERT INTO LMS_LRM.EA_EV_RS
        (
              EV_ID, USR_ID, EV_CMPL_YN
            , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES (
              #{evId}, #{usrId}, 'N'
            , #{usrId}, now(), #{usrId}, now(), #{dbId}
        )

        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 등록 */

    </insert>

	<insert id="insertEvDffd" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaDiyEvStu-Mapper.xml - insertEvDffd - EA_평가난이도구성 등록*/

		INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN( EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		VALUES
		<foreach collection="dffdList" index="index" item="item" separator=",">
		(#{evId}, #{item.evDffdDvCd}, #{item.evDffdDsbCnt}, #{usrId}, now(), #{usrId}, now(), #{dbId})
		</foreach>
		;

	</insert>

	<insert id="insertEvTsRnge" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaDiyEvStu-Mapper.xml - insertEvTsRnge - EA_평가시험범위 등록 */

		INSERT INTO LMS_LRM.EA_EV_TS_RNGE(
				EV_ID, TS_RNGE_SEQ_NO
			  , LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
			  , MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
			  , SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="tsRngeList" index="index" item="item" separator=",">
		(
				#{evId}, #{index}+1
			  , #{optTxbId}, NULLIF(#{item.luLrmpNodId}, ''), #{item.luLrmpNodNm}
			  , #{optTxbId}, NULLIF(#{item.mluLrmpNodId}, ''), #{item.mluLrmpNodNm}
			  , #{optTxbId}, NULLIF(#{item.sluLrmpNodId}, ''), #{item.sluLrmpNodNm}
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;
	</insert>

	<insert id="insertEvQtm" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">

		/** EaDiyEvStu-Mapper.xml - insertEvQtm - EA_문항리스트 등록 */

		INSERT INTO LMS_LRM.EA_EV_QTM
		(	   EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM
			 , QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM, DEL_YN
			 , TPC_ID
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="qtmIdList" index="index" item="item" separator=",">
		(
			  #{evId}, #{item.qtmId}, #{index}+1, #{item.difficultyCode}, #{item.difficultyName}
			, #{item.lluId}, #{item.lluNm}, #{item.topicId}, #{item.topicNm}, 'N'
			, #{item.topicIdKmmp}
			, #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;
	</insert>



    <insert id="insertDiyEvStuTsRnge" parameterType="com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto">

        INSERT INTO LMS_LRM.EA_EV_TS_RNGE
        (
				EV_ID, TS_RNGE_SEQ_NO
			  , LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
           <if test='mluLrmpNodId != null and mluLrmpNodId neq ""'>
			  , MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
           </if>
           <if test='sluLrmpNodId != null and sluLrmpNodId neq ""'>
           	  , SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
           </if>
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES
        (
            	#{evId}, #{tsRngeSeqNo}
			  , #{optTxbId}, #{luLrmpNodId}, #{luLrmpNodNm}
           <if test='mluLrmpNodId != null and mluLrmpNodId neq ""'>
			  , #{optTxbId}, #{mluLrmpNodId}, #{mluLrmpNodNm}
           </if>
           <if test='sluLrmpNodId != null and sluLrmpNodId neq ""'>
			  , #{optTxbId}, #{sluLrmpNodId}, #{sluLrmpNodNm}
           </if>
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
        )


        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 시험 범위 등록 */
    </insert>

    <insert id="insertDiyEvStuDffdCstn" parameterType="com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto">

        INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN
        (
            EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID,
            CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES
        (
            #{evId},
            #{evDffdDvCd},-- 최상,상,중,하,최하 코드 값
            #{evDffdDsbCnt},
            #{usrId},
            now(),
            #{usrId},
            now(),
            #{dbId}
        )

        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 난이도 등록 */
    </insert>

    <insert id="insertDiyEvStuQtm" parameterType="com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto">
        INSERT INTO LMS_LRM.EA_EV_QTM
        (EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM, QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM,
        DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
        VALUES
        (
            #{evId},
            #{qtmId},
            #{qtmOrdn},
            #{qtmDffdDvCd},
            #{qpDffdNm},
            #{qpLluId},
            #{qpLluNm},
            #{qpTcId},
            #{qpTcNm},
            #{delYn},
            #{usrId},
            NOW(),
            #{usrId},
            NOW(),
            #{dbId}
        )

        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 문항 등록 */
    </insert>

    <select id="selectEaEvRsRtxm" parameterType="com.aidt.api.ea.evcom.diy.dto.SaveEaEvRsRtxmDto"
            resultType="int">

        SELECT IF(COUNT(RTXM_PN)=0, 0, MAX(RTXM_PN)) FROM LMS_LRM.EA_EV_RS_RTXM
        WHERE EV_ID      = #{evId}
        AND   USR_ID     = #{usrId}
        AND   EV_CMPL_YN = 'N'

        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 진행중 재응시 여부 확인 */
    </select>

    <insert id="saveEaEvRsRtxm" parameterType="com.aidt.api.ea.evcom.diy.dto.SaveEaEvRsRtxmDto"
            useGeneratedKeys="true" keyProperty="rtxmPn" keyColumn="rtxm_pn">

        INSERT INTO LMS_LRM.EA_EV_RS_RTXM
        (EV_ID, USR_ID, RTXM_PN, TXM_STR_YN, EV_CMPL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
        VALUES
        (
            #{evId},
            #{usrId},
            (SELECT IFNULL(MAX(T.RTXM_PN),0)+1 AS RTXM_PN FROM LMS_LRM.EA_EV_RS_RTXM T  WHERE T.EV_ID=#{evId} AND T.USR_ID = #{usrId}),
            'N',
            'N',
            #{usrId},
            now(),
            #{usrId},
            now(),
            #{dbId}
        )


        /* 학생 DIY 평가 - 김상민 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 재응시 등록 */
    </insert>


    <select id="selectQpQtmList" parameterType="com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto"
        resultType="com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto">

       SELECT
              QQ.QP_QTM_ID 		   AS qtmId          		  -- 문항 ID
            , QQ.QP_DFFD_CD        AS qtmDffdDvCd     -- 문항 난이도 구분 코드
            , DFFD.QP_UNIF_CD_NM   AS qpDffdNm        -- 문항 플랫폼 난이도 명
            , QL.QP_LLU_ID         AS qpLluId         -- 문항 플랫폼 대단원 ID
            , QL.LLU_NM            AS qpLluNm         -- 문항 플랫폼 대단원 명
            , QTL.QP_TPC_LU_ID     AS qpTcId          -- 토픽(차시) ID
            , QTL.QP_TPC_LU_NM     AS qpTcNm          -- 토픽(차시) 명
	   FROM LMS_CMS.BC_EVSH_QTM_MPN BM
       JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = BM.QTM_ID
       JOIN LMS_CMS.QP_QTM_AN QA ON QA.QP_QTM_ID = QQ.QP_QTM_ID
       JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID = QQ.QP_QTM_ID
       JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID = QQ.QP_QTM_ID
       JOIN LMS_CMS.QP_LLU QL ON QL.QP_LLU_ID = QA.QP_LLU_ID
       LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QA.QP_TPC_LU_ID
       LEFT JOIN LMS_CMS.QP_WRK_CD DFFD ON DFFD.QP_LCL_CD = '02' AND DFFD.QP_UNIF_CD = QQ.QP_DFFD_CD
	   WHERE BM.EVSH_ID IN (
       <foreach item="item" index ="index" collection="eaEvTsRngeList" separator="union">
		SELECT BE.EVSH_ID
		FROM LMS_CMS.BC_EVSH BE
           WHERE BE.DEL_YN = 'N'
           AND BE.USE_YN = 'Y'
           AND BE.LLU_NOD_ID = #{item.luLrmpNodId}
           <if test='item.mluLrmpNodId != null and item.mluLrmpNodId neq ""'>
				AND BE.MLU_NOD_ID = #{item.mluLrmpNodId}
           </if>
           <if test='item.sluLrmpNodId != null and item.sluLrmpNodId neq ""'>
				AND BE.SLU_NOD_ID = #{item.sluLrmpNodId}
           </if>
        </foreach>
		)
		AND QQ.QP_DFFD_CD = #{evDffdDvCd}
        ORDER BY RAND() LIMIT #{evDffdDsbCnt};



        /* 학생 DIY 평가 - 박원희 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 난이도 지정 문제 출제 문항 조회 */
    </select>


    <select id="selectSaveQtmList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvQtmDto">

       SELECT
              QQ.QP_QTM_ID 		   		AS qtmId           	-- 문항 ID
            , MAX(QQ.QP_DFFD_CD)        AS difficultyCode  	-- 문항 난이도 구분 코드
            , MAX(DFFD.QP_UNIF_CD_NM)   AS difficultyName  	-- 문항 플랫폼 난이도 명
            , MAX(QLL.QP_LLU_ID)        AS lluId         	-- 문항 플랫폼 대단원 ID
            , MAX(QLL.LLU_NM)           AS lluNm         	-- 문항 플랫폼 대단원 명
            , MAX(QTL.QP_TPC_LU_ID)     AS topicId          	-- 토픽(차시) ID
            , MAX(QTL.QP_TPC_LU_NM)     AS topicNm          	-- 토픽(차시) 명
		    , MAX(QQ.tpcID)		   	   	AS topicIdKmmp 		-- 지식맵 토픽ID
		FROM (
	            <foreach item="item" index ="index" collection="qtmSearchList" separator="union all">
					SELECT
						    #{item.tpcId} AS tpcID
						  , QQ.QP_QTM_ID
						  , QQ.QP_DFFD_CD
					FROM LMS_CMS.QP_QTM QQ
					WHERE QQ.QP_QTM_ID = #{item.qtmId}
	            </foreach>
		) QQ
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    		= QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_LLU QLL ON QLL.QP_LLU_ID 		= QQA.QP_LLU_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID = QQA.QP_TPC_LU_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD DFFD ON DFFD.QP_LCL_CD = '02' AND DFFD.QP_UNIF_CD = QQ.QP_DFFD_CD
        GROUP BY QQ.QP_QTM_ID



        /* 학생 DIY 평가 - 박원희 - EaDiyEvStu-Mapper.xml - 학생 DIY 평가 난이도 지정 문제 출제 문항 조회 */
    </select>


</mapper>