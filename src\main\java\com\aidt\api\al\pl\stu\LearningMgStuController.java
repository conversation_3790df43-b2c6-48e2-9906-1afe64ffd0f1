package com.aidt.api.al.pl.stu;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.LearningMgDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 학습관리 - AI추천학습(선생님)
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/stu/learningMgStu")
@Tag(name="[al] 학습현황관리 학생", description="학생 학습리포트 학습현황 AI추천학습")
public class LearningMgStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private LearningMgStuService learningMgStuService;
	
	
	
	@Tag(name="[al] 학습리포트 수학 AI학습현황", description="학습리포트 수학 AI학습현황")
    @PostMapping(value = "/selectLearningMgMaLuList")
    public ResponseDto<List<Map<String, Object>>> selectStuAeEvInfoList(@RequestBody @Valid LearningMgDto dto) {
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getClaId() == null) {
        	dto.setClaId(securityUserDetailDto.getClaId());
        }
    	return Response.ok(learningMgStuService.selectLearningMgMaLuList(dto));
    }
	
	
	
	
}
