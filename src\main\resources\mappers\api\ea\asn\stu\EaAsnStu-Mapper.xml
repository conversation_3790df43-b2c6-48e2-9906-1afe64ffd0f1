<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.asn.stu">
	<!-- 과제 목록 조회 -->
	<select id="selectAsnList" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaStuAsn-Mapper.xml - selectAsnList */
		<include refid="api.ea.common.pagingHeader"/>
		SELECT EA.ASN_ID			-- 과제ID
			  ,EA.OPT_TXB_ID 		-- 운영교과서ID
			  ,EA.TCR_USR_ID 		-- 교사사용자ID
			  ,EA.ASN_NM 			-- 과제명
			  ,EA.ASN_CN 			-- 과제설명
			  ,EA.ASN_TP_CD 		-- 과제유형코드
			  ,EA.LRN_TP_CD 		-- 학습유형코드
			  ,CASE
	   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL THEN EA.ASN_TP_CD
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL THEN EA.LRN_TP_CD
			   END AS ASN_LRN_TP	-- 유형코드
			  ,CASE
	   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL
		  	 	      THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.ASN_TP_CD AND CM.URNK_CM_CD ='ASN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL
		   			  THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.LRN_TP_CD AND CM.URNK_CM_CD ='LRN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
			   END AS ASN_LRN_TP_NM	-- 유형코드이름
			  ,EA.ASN_PTME_DV_CD 	-- 과제기간구분코드
			  ,EA.STR_DTM 			-- 시작일시
			  ,CONCAT(DATE_FORMAT(EA.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.STR_DTM,'%h:%i')) AS STR_DTM_NM
			  ,EA.END_DTM 			-- 종료일시
			  ,CONCAT(DATE_FORMAT(EA.END_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.END_DTM,'%h:%i')) AS END_DTM_NM
			  ,EA.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
			  ,EA.EV_MTHD_TP_CD		-- 평가방식유형코드
			  ,EA.PSC_SCR			-- 만점점수
			  ,EA.DEL_YN 			-- 삭제여부
			  ,EA.USE_YN  			-- 사용여부
			  ,EA.LCKN_YN  			-- 잠금여부
			  ,EA.CRTR_ID 			-- 생성자ID
			  ,EA.CRT_DTM 			-- 생성일시
			  ,EA.MDFR_ID 			-- 수정자ID
			  ,EA.MDF_DTM 			-- 수정일시
			  ,EGAS.STU_USR_ID 		-- 학생사용자ID
			  ,EGAS.SMT_DTM 		-- 제출일시
			  ,EGAS.SMT_CN 			-- 제출내용
			  ,EGAS.ANNX_ID 		-- 첨부ID
			  ,EGAS.SMT_CMPL_YN 	-- 제출완료여부
			  ,EGAS.SCR 			-- 점수
			  ,EGAS.FDBK_CN 		-- 피드백내용
			  ,EAR.SP_LRN_ID		-- 특별학습ID
			  ,EAR.LRN_STP_DV_CD	-- 학습단계구분코드
			  ,EAR.LU_NOD_ID		-- 단원노드ID
			  ,EAR.TC_NOD_ID		-- 차시노드ID
		      ,CASE
					WHEN EA.LRN_TP_CD = 'TL' OR EA.ASN_TP_CD = 'GE' OR EA.ASN_TP_CD = 'GR' THEN TSLNR.LRMP_NOD_NM 
<!-- 					WHEN EA.LRN_TP_CD = 'SL' THEN SSLR.SP_LRN_NM -->
					WHEN EA.LRN_TP_CD = 'AL' THEN AKNR.KMMP_NOD_NM
				 END NOD_NM 			-- 단원명
			  ,TS.LRMP_NOD_NM AS TC_NM  -- 차시명
			  ,TSLNR.RCSTN_NO AS NOD_NO			-- 단원넘버링
			  ,TS.RCSTN_NO AS TC_NO				-- 차시넘버링
			  ,TSLNR.RCSTN_ORDN
		  FROM LMS_LRM.EA_ASN_SMT EGAS -- EA_과제제출
		 INNER JOIN LMS_LRM.EA_ASN EA	-- EA_과제
		    ON EGAS.ASN_ID = EA.ASN_ID
		   AND EGAS.STU_USR_ID = #{stuUsrId}
		   AND EA.OPT_TXB_ID = #{optTxbId}
		   AND EA.DEL_YN = 'N'
		   AND EA.USE_YN = 'Y'
		  LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
		    ON EGAS.ASN_ID = EAR.ASN_ID
		   AND EAR.DEL_YN = 'N'
	      LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR	-- TL_교과학습노드재구성
            ON EA.OPT_TXB_ID = TSLNR.OPT_TXB_ID
	       AND EAR.LU_NOD_ID = TSLNR.LRMP_NOD_ID
	       AND TSLNR.USE_YN ='Y'
	       AND IFNULL(TSLNR.URNK_LRMP_NOD_ID, '') = ''
          LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TS
		    ON EAR.OPT_TXB_ID = TS.OPT_TXB_ID
		   AND EAR.TC_NOD_ID = TS.LRMP_NOD_ID
		  -- TL_특별학습노드재구성
	      <!-- LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN SSLR 
	        ON EA.OPT_TXB_ID = SSLR.OPT_TXB_ID
	       AND EAR.SP_LRN_ID = SSLR.SP_LRN_ID
	       AND SSLR.USE_YN ='Y' -->
	      LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR -- AI_지식맵노드재구성
	        ON EA.OPT_TXB_ID = AKNR.OPT_TXB_ID
	       AND EAR.LU_NOD_ID = AKNR.KMMP_NOD_ID
	       AND AKNR.DEL_YN ='N'
		 WHERE 1=1
		   <choose>
		    <!-- 진행중 탭  -->
    		<when test = 'tebCd != null and tebCd.equals("0")'>
				<![CDATA[
			    AND (EA.STR_DTM  <=  NOW() AND EA.END_DTM > NOW() AND EGAS.SMT_CMPL_YN ='N'  AND EA.LCKN_YN = 'N')
			    OR (EA.ASN_PTME_DV_CD ='OT' AND EGAS.SMT_CMPL_YN ='N' AND EA.LCKN_YN = 'N')
			    ]]>
    		</when>
    		<!-- 종료 탭-->
    		<when test = 'tebCd != null and tebCd.equals("1")'>
    			AND EA.LCKN_YN = 'N' 
			 	AND (
				    (EA.ASN_PTME_DV_CD = 'PT' AND EA.END_DTM <![CDATA[<]]> NOW()) 
				    OR (EA.ASN_PTME_DV_CD = 'PT' AND EA.STR_DTM <![CDATA[<=]]> NOW() AND EGAS.SMT_CMPL_YN = 'Y') 
				    OR (EA.ASN_PTME_DV_CD = 'OT' AND EGAS.SMT_CMPL_YN = 'Y')
			     )
			    <!-- AND EA.END_DTM <![CDATA[<]]> NOW() AND EA.LCKN_YN = 'N'
				OR EA.STR_DTM <![CDATA[<=]]> NOW() AND EGAS.SMT_CMPL_YN ='Y' AND EA.LCKN_YN = 'N' -->
    		</when>
		</choose>
	    <if test = 'searchOption != null and !"".equals(searchOption)'>
	    <!-- 
			HAVING EAR.LU_NOD_ID IN (
		        #{searchOption}
		        <if test="aiSearchOptionList != null and aiSearchOptionList.size() > 0">
		            , 
		            <foreach item="aiId" collection="aiSearchOptionList" open="" separator="," close="">
		                #{aiId}
		            </foreach>
		        </if>
		    )
		    -->
		    <if test = 'asnTpOption != null and !"".equals(asnTpOption) and "LU".equals(asnTpOption)'>
				HAVING EAR.LU_NOD_ID IN (
			        #{searchOption}
			        <if test="aiSearchOptionList != null and aiSearchOptionList.size() > 0">
			            , 
			            <foreach item="aiId" collection="aiSearchOptionList" open="" separator="," close="">
			                #{aiId}
			            </foreach>
			        </if>
			    )
			 </if>
			 <if test = 'asnTpOption != null and !"".equals(asnTpOption) and "ASN".equals(asnTpOption)'>
				 <if test="searchOption == 'GE' or searchOption == 'GR'">
			        HAVING EA.ASN_TP_CD = #{searchOption}
			    </if>
			    
			    <if test="searchOption == 'TL' or searchOption == 'AL'">
			        HAVING EA.LRN_TP_CD = #{searchOption}
			    </if>
			 </if>
		</if>
		<choose>
		    <when test="sortOption == 'crtDtm'">
		     	ORDER BY EA.CRT_DTM DESC
		    </when>
		    <when test="sortOption == 'luNodId'">
				ORDER BY
					CASE
				        WHEN TSLNR.RCSTN_ORDN IS NOT NULL THEN 1
				        WHEN TSLNR.RCSTN_ORDN IS NULL THEN 2
				        ELSE 3
				    END,
					CASE
				        WHEN TSLNR.RCSTN_ORDN IS NOT NULL THEN TSLNR.RCSTN_ORDN
				        WHEN TSLNR.RCSTN_ORDN IS NULL THEN
				            CASE
				                WHEN EA.LRN_TP_CD = 'TL' THEN 1
				                WHEN EA.LRN_TP_CD = 'AL' THEN 2
				                WHEN EA.LRN_TP_CD = 'SL' THEN 3
				                WHEN EA.ASN_TP_CD = 'GE' THEN 4
				                WHEN EA.ASN_TP_CD = 'GR' THEN 5
				                ELSE 6
				            END
				    END
		    </when>
		    <otherwise>
		       ORDER BY
				    CASE
				        WHEN EA.ASN_PTME_DV_CD = 'PT' THEN 1
				        WHEN EA.ASN_PTME_DV_CD = 'OT' AND EAR.LU_NOD_ID IS NOT NULL THEN 2
				        WHEN EA.ASN_PTME_DV_CD = 'OT' AND EAR.LU_NOD_ID IS NULL THEN 3
				        ELSE 4
				    END,
				    CASE
				        WHEN EA.ASN_PTME_DV_CD = 'PT' THEN EA.END_DTM
				        WHEN EA.ASN_PTME_DV_CD = 'OT' AND EAR.LU_NOD_ID IS NOT NULL THEN EAR.LU_NOD_ID
				        WHEN EA.ASN_PTME_DV_CD = 'OT' AND EAR.LU_NOD_ID IS NULL THEN
				            CASE
				                WHEN EA.LRN_TP_CD = 'TL' THEN 1
				                WHEN EA.LRN_TP_CD = 'AL' THEN 2
				                WHEN EA.LRN_TP_CD = 'SL' THEN 3
				                WHEN EA.ASN_TP_CD = 'GE' THEN 4
				                WHEN EA.ASN_TP_CD = 'GR' THEN 5
				                ELSE 6
				            END
				    END
		    </otherwise>
  		</choose>
  		<include refid="api.ea.common.pagingFooter"/>
	</select>

	<!-- 제출여부 조회 -->
	<select id="selectSmtYn" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="java.lang.String">
		SELECT
			SMT_CMPL_YN
		FROM
			LMS_LRM.EA_ASN_SMT
		WHERE
			ASN_ID = #{asnId}
		AND
			STU_USR_ID = #{stuUsrId}
	</select>

	<!-- 일반과제 제출 또는 수정 -->
	<update id="updateStuAsn" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaStuAsn-Mapper.xml - updateStuAsn */
		UPDATE LMS_LRM.EA_ASN_SMT
		SET
			 SMT_CMPL_YN = 'Y'
			<if test = 'smtCmplYn != null and !"".equals(smtCmplYn) and "N".equals(smtCmplYn)'>
	   			,SMT_DTM = NOW()
	      	</if>
			, SMT_CN = #{smtCn}
	   		, ANNX_ID =	#{annxId}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE
			ASN_ID = #{asnId}
		AND
			STU_USR_ID = #{stuUsrId}
	</update>

	<!-- 모둠팀원 과제 제출여부 조회 -->
	<select id="AsnGrpCheck" parameterType="int" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaStuAsn-Mapper.xml - AsnGrpCheck */
		SELECT
			   CASE
				   	WHEN COUNT(DISTINCT E.STU_USR_ID) = SUM(CASE WHEN E.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END)
				   	THEN 1
				   	ELSE 0
			    END AS CNT
		  FROM LMS_LRM.EA_GRP_TMBR GT
		 INNER JOIN LMS_LRM.EA_GRP_ASN_SMT GA
		    ON GT.GRP_ID = GA.GRP_ID
		   AND GT.GRP_TEM_ID = GA.GRP_TEM_ID
		  LEFT JOIN LMS_LRM.EA_ASN_SMT E
		    ON GT.STU_USR_ID = E.STU_USR_ID
		   AND GA.ASN_ID = E.ASN_ID
		 WHERE GT.GRP_ID = #{grpId}
		   AND GT.GRP_TEM_ID = #{grpTemId}
	</select>

	<!-- 모둠 과제 최초 제출일시 조회 -->
	<select id="selectGrpFstSmtDtm" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="java.lang.String">
		SELECT
			IFNULL(FST_SMT_DTM, 0) AS fstSmtDtm
		FROM
			LMS_LRM.EA_GRP_ASN_SMT
		WHERE
			GRP_ID = #{grpId}
		AND
			GRP_TEM_ID = #{grpTemId}
		AND
			ASN_ID = #{asnId}
	</select>

	<!-- 모둠과제제출 제출여부 업데이트-->
	<update id="updateGrpAsnYn" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaStuAsn-Mapper.xml - updateGrpAsnYn */
		UPDATE LMS_LRM.EA_GRP_ASN_SMT
		   SET
		   		SMT_CMPL_YN  = 'Y'
				<if test = 'fstSmtDtm.equals("0")'>
					,FST_SMT_DTM = NOW()
				</if>
				<if test = 'smtCmplYn != null and !"".equals(smtCmplYn) and "N".equals(smtCmplYn)'>
	   				,SMT_DTM = NOW()
	      		</if>
		   		, ANNX_ID =	#{annxIdForSmt}
				, SMT_CN = #{smtCn}
				, MDF_DTM = NOW()
		 WHERE GRP_ID  = #{grpId}
		   AND GRP_TEM_ID  = #{grpTemId}
		   AND ASN_ID  = #{asnId}
	</update>

	
	<!-- 모둠 과제 상세 조회 (모둠 구성원 조회) -->
	<select id="selectGrpDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaStuAsn-Mapper.xml - selectGrpDetail */
		SELECT
			T2.GRP_ID					AS grpId
			, T2.GRP_TEM_ID			AS grpTemId
			, T3.STU_USR_ID			AS stuUsrId
			<!-- , T3.USR_NM				AS usrNm -->
			, T3.GRP_TMGR_YN		AS grpTmgrYn
			, T2.ANNX_ID			AS annxIdForSmt
		FROM
		(
			SELECT
				T1.GRP_ID
				, T1.GRP_TEM_ID
				, T1.ANNX_ID
			FROM
			(
				SELECT
					A.GRP_ID
					, A.GRP_TEM_ID
					, A.ASN_ID
					, A.SMT_DTM
					, A.SMT_CN
					, A.ANNX_ID
					, A.SMT_CMPL_YN
					, A.SCR
					, A.FDBK_CN
					, B.STU_USR_ID
					, B.GRP_TMGR_YN
					, B.DEL_YN
				FROM
					LMS_LRM.EA_GRP_ASN_SMT A
				RIGHT JOIN
					LMS_LRM.EA_GRP_TMBR B
				ON
					A.GRP_ID = B.GRP_ID
				AND
					A.GRP_TEM_ID = B.GRP_TEM_ID
				WHERE
					A.ASN_ID = #{asnId}
				AND
					B.STU_USR_ID = #{stuUsrId}
			) T1
		) T2
		LEFT JOIN
			LMS_LRM.EA_GRP_TMBR T3
		ON
			T2.GRP_ID = T3.GRP_ID
		AND
			T2.GRP_TEM_ID = T3.GRP_TEM_ID
		ORDER BY
			T3.GRP_TMGR_YN DESC
	</select>

	<!-- 모둠 과제 제출 또는 수정 -->
	<update id="updateAsnStuDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaStuAsn-Mapper.xml - updateAsnStuDetail */

			UPDATE LMS_LRM.EA_ASN_SMT
			SET
				SMT_CMPL_YN = 'Y'
				<if test = 'smtCmplYn != null and !"".equals(smtCmplYn) and "N".equals(smtCmplYn)'>
	   				,SMT_DTM = NOW()
	      		</if>
	      		, ANNX_ID =	#{annxIdForSmt}
				, SMT_CN = #{smtCn}
				, MDFR_ID = #{mdfrId}
				, MDF_DTM = NOW()
			WHERE
				ASN_ID = #{asnId}
			AND
				STU_USR_ID IN
				<foreach collection="usrIdList" item="temp" separator="," open="(" close=")">
					#{temp.stuUsrId}
				</foreach>
	</update>
	<!-- AND
			STU_USR_ID = #{stuUsrId} -->

	<!-- 과제 상세 파일조회 -->
	<select id="selectAsnFile" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnFleDto">
		/* EaStuAsn-Mapper.xml - selectAsnFile */
		SELECT
			   CAF.ANNX_FLE_ID		AS annxFileId
			  ,CAF.ANNX_ID			AS annxId
			  ,CAF.SRT_ORDN			AS srtOrdn
			  ,CAF.DOC_VI_ID		AS docViId
			  ,CAF.ANNX_FLE_NM		AS annxFileNm
			  ,CAF.ANNX_FLE_ORGL_NM	AS annxFileOrglNm
			  ,CAF.ANNX_FLE_FEXT_NM	AS annxFileFextNm
			  ,CAF.ANNX_FLE_SZE		AS annxFileSize
			  ,CAF.ANNX_FLE_PTH_NM	AS annxFilePathNm
			  ,CAF.USE_YN			AS useYn
		FROM
			LMS_LRM.CM_ANNX_FLE CAF
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>

	<!-- 모둠 게시판 존재 여부 -->
	<select id="selectBlbdCnt" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="int">
		/* EaStuAsn-Mapper.xml - selectBlbdCnt */
		SELECT
			COUNT(BLBD_ID)
		FROM
			LMS_LRM.EA_GRP_BLBD
		WHERE
			GRP_ID = #{grpId}
		AND
			GRP_TEM_ID = #{grpTemId}
		AND
			ASN_ID = #{asnId}
	</select>

	<!-- 해당 모둠 그룹의 모둠팀, 인원, 팀 수 조회 -->
	<select id="selectGrpInfo" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/** EaStuAsn-Mapper.xml - selectGrpInfo */
		SELECT
			A.STU_USR_ID 		AS stuUsrId
		FROM
			LMS_LRM.EA_GRP_TMBR A
		LEFT JOIN
			LMS_LRM.EA_GRP B
		ON
			A.GRP_ID = B.GRP_ID
		WHERE
			A.GRP_ID = #{grpId}
		AND
			A.GRP_TEM_ID = #{grpTemId}
	</select>
	
	<!-- 모둠 확인 -->
    <select id="selectGrpStuDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
        /* EaStuAsn-Mapper.xml - selectGrpStuDetail */
	    SELECT EA.ASN_ID 
          FROM LMS_LRM.EA_ASN EA
         INNER JOIN LMS_LRM.EA_ASN_SMT EAS
            ON EA.ASN_ID = EAS.ASN_ID
           AND EAS.STU_USR_ID = #{stuUsrId}
         INNER JOIN LMS_LRM.EA_GRP_ASN_SMT EGAS
            ON EA.ASN_ID = EGAS.ASN_ID
           AND GRP_ID = #{grpId}
		   AND GRP_TEM_ID = #{grpTemId}
         WHERE EA.ASN_ID = #{asnId}
           AND EA.OPT_TXB_ID = #{optTxbId}
    </select>
    
    <!-- 과제 상세 조회 -->
	<select id="selectAsnDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaAsnStu-Mapper.xml - selectAsnDetail */
		SELECT EA.ASN_ID			-- 과제ID
			  ,EA.OPT_TXB_ID 		-- 운영교과서ID
			  ,EA.TCR_USR_ID 		-- 교사사용자ID
			  ,EA.ASN_NM 			-- 과제명
			  ,EA.ASN_CN 			-- 과제설명
			  ,EA.ASN_TP_CD 		-- 과제유형코드
			  ,EA.LRN_TP_CD 		-- 학습유형코드
			  ,CASE
	   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL THEN EA.ASN_TP_CD
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL THEN EA.LRN_TP_CD
			   END AS ASN_LRN_TP	-- 유형코드
			  ,CASE
		  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL
		  	 	      THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.ASN_TP_CD AND CM.URNK_CM_CD ='ASN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL
		   			  THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.LRN_TP_CD AND CM.URNK_CM_CD ='LRN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
			   END AS ASN_LRN_TP_NM	-- 유형코드이름
			  ,EA.ASN_PTME_DV_CD 	-- 과제기간구분코드
			  ,EA.STR_DTM 			-- 시작일시
			   ,CONCAT(DATE_FORMAT(EA.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.STR_DTM,'%h:%i')) AS STR_DTM_NM
			  ,EA.END_DTM 			-- 종료일시
			  ,CONCAT(DATE_FORMAT(EA.END_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.END_DTM,'%h:%i')) AS END_DTM_NM
			  ,CASE WHEN EA.ASN_PTME_DV_CD = 'PT' THEN DATEDIFF(EA.END_DTM, CURDATE())
				    ELSE ''
				END AS D_DAY
			  ,EA.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
			  ,EA.EV_MTHD_TP_CD 	-- 평가방식유형코드
		  	  ,EA.PSC_SCR 			-- 만점점수
		  	  ,EA.ANNX_ID AS TCR_ANNX_ID		-- 교사첨부ID
			  ,EA.DEL_YN 			-- 삭제여부
			  ,EA.CRTR_ID 			-- 생성자ID
			  ,DATE_FORMAT(EA.CRT_DTM ,'%m. %d.') AS CRT_DTM
			  -- 과제
			  ,EAS.STU_USR_ID 		-- 학생사용자ID
			  ,EAS.SMT_DTM 			-- 제출일시
			  ,EAS.MDF_DTM
			  ,CASE
		          WHEN EAS.MDF_DTM > EAS.SMT_DTM THEN
		              CONCAT(DATE_FORMAT(EAS.MDF_DTM, '%m. %d. '), 
		                     IF(TIME_FORMAT(EAS.MDF_DTM, '%p') = 'AM', '오전 ', '오후 '), 
		                     DATE_FORMAT(EAS.MDF_DTM, '%h:%i'))
		          ELSE
		              CONCAT(DATE_FORMAT(EAS.SMT_DTM, '%m. %d. '), 
		                     IF(TIME_FORMAT(EAS.SMT_DTM, '%p') = 'AM', '오전 ', '오후 '), 
		                     DATE_FORMAT(EAS.SMT_DTM, '%h:%i'))
		      END AS SMT_DTM_NM
			  ,EAS.SMT_CN 			-- 제출내용
			  ,EAS.ANNX_ID AS STU_ANNX_ID		-- 학생첨부ID
			  ,EAS.SMT_CMPL_YN 		-- 제출완료여부
			  ,EAS.SCR 				-- 점수
			  ,CASE
				  WHEN EA.EV_MTHD_TP_CD ='SC' AND EAS.SCR IS NOT NULL THEN 'Y'
				  ELSE 'N'
			   END  AS SCR_YN
			  ,EAS.FDBK_CN 			-- 피드백내용
			  -- 과제범위
			  ,EAR.SP_LRN_ID		-- 특별학습ID
			  ,EAR.LRN_STP_DV_CD	-- 학습단계구분코드
			  ,EAR.LU_NOD_ID		-- 단원노드ID
			  ,EAR.TC_NOD_ID		-- 차시노드ID
			  ,(SELECT TSL.EV_ID 
		          FROM TL_SBC_LRN_ATV_RCSTN TSL
		         INNER JOIN LMS_CMS.BC_LRN_STP S  
		            ON TSL.LRMP_NOD_ID  = S.LRMP_NOD_ID
	               AND TSL.LRN_STP_ID = S.LRN_STP_ID 
	               AND S.LRN_STP_DV_CD = 'EX'
	             WHERE EAR.TC_NOD_ID = TSL.LRMP_NOD_ID 
	               AND EA.OPT_TXB_ID = TSL.OPT_TXB_ID LIMIT 1) AS EV_ID
	    	  ,CASE
					WHEN EA.LRN_TP_CD = 'TL' OR EA.ASN_TP_CD = 'GE' OR EA.ASN_TP_CD = 'GR' 
						THEN (SELECT TSLNR.LRMP_NOD_NM FROM TL_SBC_LRN_NOD_RCSTN TSLNR WHERE TSLNR.OPT_TXB_ID = EA.OPT_TXB_ID AND TSLNR.LRMP_NOD_ID= EAR.LU_NOD_ID )
					WHEN EA.LRN_TP_CD = 'AL' 
						THEN (SELECT AKNR.KMMP_NOD_NM FROM AI_KMMP_NOD_RCSTN AKNR WHERE AKNR.OPT_TXB_ID=EA.OPT_TXB_ID AND AKNR.KMMP_NOD_ID = EAR.LU_NOD_ID )
				 END NOD_NM 			-- 단원명
			  ,(SELECT LRMP_NOD_NM FROM TL_SBC_LRN_NOD_RCSTN  WHERE OPT_TXB_ID=EA.OPT_TXB_ID AND LRMP_NOD_ID = EAR.TC_NOD_ID ) AS TC_NM  -- 차시명
			  ,(SELECT RCSTN_NO  FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE EAR.OPT_TXB_ID = OPT_TXB_ID AND EAR.LU_NOD_ID = LRMP_NOD_ID) AS NOD_NO -- 단원넘버링
			  ,(SELECT RCSTN_NO FROM TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID=EA.OPT_TXB_ID AND LRMP_NOD_ID = EAR.TC_NOD_ID ) AS TC_NO	-- 차시넘버링
			  ,(SELECT TSL.LRN_ATV_ID 
		          FROM TL_SBC_LRN_ATV_RCSTN TSL
		         INNER JOIN LMS_CMS.BC_LRN_STP S  
		            ON TSL.LRMP_NOD_ID  = S.LRMP_NOD_ID
	               AND TSL.LRN_STP_ID = S.LRN_STP_ID 
	               AND S.LRN_STP_DV_CD = 'EX'
	             WHERE EAR.TC_NOD_ID = TSL.LRMP_NOD_ID 
	               AND EA.OPT_TXB_ID = TSL.OPT_TXB_ID LIMIT 1) AS LRN_ATV_ID
	     FROM LMS_LRM.EA_ASN_SMT EAS -- EA_과제제출
	    INNER JOIN LMS_LRM.EA_ASN EA -- EA_과제
	       ON EAS.ASN_ID = EA.ASN_ID
	      AND EA.OPT_TXB_ID = #{optTxbId}
	      AND EA.DEL_YN = 'N'
	      AND EA.USE_YN = 'Y'
	      AND EA.LCKN_YN = 'N'
	     LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
	       ON EA.ASN_ID = EAR.ASN_ID
	      AND EAR.DEL_YN = 'N'
	    WHERE EAS.STU_USR_ID = #{stuUsrId}
	      AND EAS.ASN_ID = #{asnId}
	</select>
	
</mapper>