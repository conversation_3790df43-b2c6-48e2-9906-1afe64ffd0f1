package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-25 13:45:32
 * @modify date 2024-05-25 13:45:32
 * @desc [AI맞춤학습 단원차시조회 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlPlMluLstInqEnElStuResponseDto {
		@Parameter(name="운영교과서id")
		private String optTxbId;
		
		@Parameter(name="중단원 지식맵 노드Id")
		private String mluKmmpNodId;
		
		@Parameter(name="중단원 지식맵 노드명")
		private String mluKmmpNodNm;
		
		@Parameter(name="차시 지식맵 노드Id")
		private String kmmpNodId;
		
		@Parameter(name="차시 지식맵 노드명")
		private String kmmpNodNm;
		
		@Parameter(name="토픽Id")
		private String tpcId;
		
		@Parameter(name="토픽명")
		private String tpcNM;
		
		@Parameter(name="뎁스")
		private String dpth;
		
		@Parameter(name="차시 사용 여부")
		private String tcuseYn;
		
		@Parameter(name="차시 학습맵 노드 ID")
		private String tcLrmpNodId;
		
		@Parameter(name="중단원 학습맵 노드 ID")
		private String mluLrmpNodId;
		
		@Parameter(name="토픽 숙련도")
		private Double tpcAvn;
		
		@Parameter(name="학습맵 차시(노드)명")
		private String lrmpNodNm;
		
		@Parameter(name="학습자속도유형코드")
		private String lrnrVelTpCd;

		@Parameter(name="평가상세구분코드")
	    private String evDtlDvCd;
		
		@Parameter(name="평가완료여부")
	    private String evCmplYn;
		
		@Parameter(name="AI예측평균정답률")
	    private String aiPredAvgCansRt;
		
		@Parameter(name="AI예측평균점수")
	    private String aiPrefAvgScr;
		
		@Parameter(name="영역별 평가 완료 여부")
		private String finishTcYn;
		
		@Parameter(name="차시에 따른 평가 정보")
		private List<AlPlQtmTpcProfDto> aiPlQtmTpcProfList;
		
		@Parameter(name="차시에 따른 토픽 정보")
		private List<AlPlTpcProfInfoStuResponseDto> aiPlTpcProfInfoList;
		
}
