package com.aidt.api.bc.ct.tcr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.ct.dto.BcCtDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[bc] URL 전송[BcCtTcr]", description="URL 전송하기(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/ct")
public class BcCtTcrController {

    @Autowired
    private BcCtTcrService bcCtTcrService;
    
    /**
     * URL 전송목록 조회 요청
     *
     * @param dto: BcCtDto
     * @return List<BcCtDto>
     */
    @Operation(summary="URL 전송목록 조회", description="URL 전송목록")
    @PostMapping(value="/selectUrlSendList")
	public ResponseDto<List<BcCtDto>> selectUrlSendList(@RequestBody BcCtDto dto) {
    	List<BcCtDto> urlSendList = bcCtTcrService.selectUrlSendList(dto);
		return Response.ok(urlSendList);
	}    
    
    /**
     * URL 전송목록 등록 요청
     *
     * @param dto: BcCtDto
     * @return Integer
     */
    @Operation(summary="URL 전송목록 등록", description="URL 전송목록을 등록한다.")
    @PostMapping(value = "/insertUrlSend")
    public ResponseDto<Integer> insertUrlSend(@RequestBody BcCtDto dto){
    	return Response.ok(bcCtTcrService.insertUrlSend(dto));
    }    
    

    /**
     * URL 전송목록 삭제 요청
     *
     * @param dto :BcCtDto
     * @return Integer
     */
    @Operation(summary="URL 전송목록 삭제", description="URL 전송목록을 삭제한다.")
    @PostMapping(value = "/deleteUrlSend")
    public ResponseDto<Integer> deleteUrlSend(@RequestBody BcCtDto dto) {
        return Response.ok(bcCtTcrService.deleteUrlSend(dto));
    }    
    
}
