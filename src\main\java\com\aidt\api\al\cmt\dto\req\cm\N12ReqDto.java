package com.aidt.api.al.cmt.dto.req.cm;

import javax.validation.Valid;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 15:10:58
 * @modify date 2024-05-24 15:10:58
 * @desc 영역별 분석(영어) req dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class N12ReqDto {

    @Valid
    @Parameter(name="듣기 문항")
    private QtmCntReqDto listening;

    @Valid
    @Parameter(name="말하기 문항")
    private QtmCntReqDto speaking;

    @Valid
    @Parameter(name="읽기 문항")
    private QtmCntReqDto reading;

    @Valid
    @Parameter(name="쓰기 문항")
    private QtmCntReqDto writing;

    @Valid
    @Parameter(name="파닉스 문항")
    private QtmCntReqDto phonics;

    @Valid
    @Parameter(name="단어 문항")
    private QtmCntReqDto word;

    @Valid
    @Parameter(name="문법 문항")
    private QtmCntReqDto grammar;

    @Valid
    @Parameter(name="알파벳 문항")
    private QtmCntReqDto alphabet;

    public void setVocabulary(QtmCntReqDto reqDto) {
        this.word = reqDto;
    }

    public N12Dto toDto(AiCmtLvlCalculator calculator) {
        return N12Dto.builder()
                .lstngLvlCd(calculator.calculate(this.listening))
                .spkngLvlCd(calculator.calculate(this.speaking))
                .rdngLvlCd(calculator.calculate(this.reading))
                .wrtngLvlCd(calculator.calculate(this.writing))
                .phncsLvlCd(calculator.calculate(this.phonics))
                .wrdLvlCd(calculator.calculate(this.word))
                .grmrLvlCd(calculator.calculate(this.grammar))
                .alpbLvlCd(calculator.calculate(this.alphabet))
                .build();
    }

    public boolean isAllCorrect() {
        List<QtmCntReqDto> list = new ArrayList<>();

        if(this.listening != null)  list.add(this.listening);
        if(this.speaking != null)   list.add(this.speaking);
        if(this.reading != null)    list.add(this.reading);
        if(this.writing != null)    list.add(this.writing);
        if(this.phonics != null)    list.add(this.phonics);
        if(this.word != null)       list.add(this.word);
        if(this.grammar != null)    list.add(this.grammar);
        if(this.alphabet != null)   list.add(this.alphabet);

        List<QtmCntReqDto> collect = list.stream().filter(s -> s.getTotalQtmCnt().equals(s.getCorrectQtmCnt())).collect(Collectors.toList());
        return collect.size() == list.size();
    }

}
