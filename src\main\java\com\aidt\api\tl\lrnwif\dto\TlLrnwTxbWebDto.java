package com.aidt.api.tl.lrnwif.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email 
 * @create date 2024-03-13 10:53:20
 * @modify date 2024-03-13 10:53:20
 * @desc TlLrnwTxbWebDto 교과서/익힘책 조회조건 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlLrnwTxbWebDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID")
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;
    
    /** 사용자ID */
    @Parameter(name="학습사용자ID")
    private String usrId;
    
    /** 교과서익힘책구분코드 (TE=교과서, WO=익힘책)*/
    @Parameter(name="교과서익힘책구분코드 (TE=교과서, WO=익힘책)")
    @NotBlank(message = "{field.required}")
    private String txbWebDvCd;
}
