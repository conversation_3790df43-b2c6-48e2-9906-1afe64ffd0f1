package com.aidt.api.ea.asn.stu;

import java.util.List;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

//@Slf4j
@Tag(name="[ea] 과제 - 학생", description="과제 - 학생")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/stu/asn")
public class EaAsnStuController {

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private EaAsnStuService eaAsnStuService;

    /**
     * 과제 목록 조회 - 학생
     * @return ResponseDto<List<EaAsnStuDto>>
     */
    @Operation(summary="과제 목록 조회 (학생)", description="과제 목록 조회 (학생)")
    @PostMapping(value="/selectAsnStuList")
	public ResponseDto<List<EaAsnStuDto>> selectAsnList(@RequestBody EaAsnStuDto eaAsnStuDto) {

		List<EaAsnStuDto> eaAsnStuList = eaAsnStuService.selectAsnStuList(eaAsnStuDto);

		return Response.ok(eaAsnStuList);
	}


    /**
     * 일반 제출 또는 수정
     * @param eaAsnStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="일반 과제 제출, 수정 (학생)", description="일반 과제 제출, 수정 (학생)")
    @PostMapping(value="/updateAsnStu")
    public ResponseDto<Map<String, Object>> updateAsnStu(@RequestBody EaAsnStuDto eaAsnStuDto , HttpServletRequest request) {

    	Map<String, Object> result = eaAsnStuService.updateAsnStu(eaAsnStuDto);
    	int resCnt = 0;
    	if (result.containsKey("check")) {
    		resCnt = (int) result.get("check");
    	}
    	//마이홈 point 지급처리
    	if(resCnt > 0 && "N".equals(eaAsnStuDto.getUpdateCheck())) {
    		Map<String, Object> apiResult = this.regMyhmPnt(eaAsnStuDto, request);
    	}
    	return Response.ok(result);
    }

    /**
     * 모둠 과제 상세 조회
     * @param eaAsnStuDto
     * @return ResponseDto<Map<String, Object>>
     */
//    @Operation(summary="모둠 과제 상세 조회 (학생)", description="모둠 과제 상세 조회 (학생)")
//    @PostMapping(value="/selectAsnGrpStuDetail")
//    public ResponseDto<Map<String, Object>> selectAsnGrpStuDetail(@RequestBody EaAsnStuDto eaAsnStuDto) {
//    	// 모둠 과제 상세 조회
//    	Map<String, Object> result = eaAsnStuService.selectAsnGrpStuDetail(eaAsnStuDto);
//
//    	return Response.ok(result);
//    }

    /**
     * 일반, 모둠 과제 제출 또는 수정
     * @param eaAsnStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 과제 제출, 수정 (학생)", description="모둠 과제 제출, 수정 (학생)")
    @PostMapping(value="/updateAsnStuDetail")
    public ResponseDto<Map<String, Object>> updateAsnStuDetail(@RequestBody EaAsnStuDto eaAsnStuDto , HttpServletRequest request) {
    	// 일반, 모둠 과제 제출 또는 수정
    	Map<String, Object> result = eaAsnStuService.updateAsnStuDetail(eaAsnStuDto);
    	if (result.containsKey("result")) {
			 String resCd = (String) result.get("result");
	    	//마이홈 point 지급처리
	    	if("N".equals(eaAsnStuDto.getUpdateCheck()) && resCd.equals("Success")) {
	    		Map<String, Object> apiResult = this.regMyhmPnt(eaAsnStuDto, request);
	    	}
    	}

    	return Response.ok(result);
    }
    
    
    /**
     * MyHome point 등록처리
     * @param eaAsnStuDto
     * @param request
     * @return
     */
    private Map<String, Object> regMyhmPnt(EaAsnStuDto eaAsnStuDto, HttpServletRequest request) {
        Map<String, Object> apiResult = null;
        // 마이홈 포인트처리
           
        String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
        String asnId = String.valueOf(eaAsnStuDto.getAsnId());
            apiResult = eaAsnStuService.callMyhmApi(accessToken, Map.of(
                "pntCd", "AS_CC_01",
                "pntChkBsVl", asnId));

        return apiResult;
    }

}
