package com.aidt.api.ea.asncom;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.asncom.dto.EaAsnFleDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

//@Slf4j
@Tag(name="[ea] 과제 - 공통", description="과제 - 공통")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/cm/asncom")
public class EaAsnComController {

	@Autowired
	private EaAsnComService eaAsnComService;

    /**
     * 과제 상세 조회 - 학생
     * @param eaAsnStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="과제 상세 조회 (학생)", description="과제 상세 조회 (학생)")
    @PostMapping(value="/selectAsnStuDetail")
    public ResponseDto<Map<String, Object>> selectAsnStuDetail(@RequestBody EaAsnStuDto eaAsnStuDto) {

        Map<String, Object> result = eaAsnComService.selectAsnStuDetail(eaAsnStuDto);

    	return Response.ok(result);
    }


    /**
     * 모둠 과제 상세 조회
     * @param eaAsnStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 과제 상세 조회 (학생)", description="모둠 과제 상세 조회 (학생)")
    @PostMapping(value="/selectAsnGrpStuDetail")
    public ResponseDto<Map<String, Object>> selectAsnGrpStuDetail(@RequestBody EaAsnStuDto eaAsnStuDto) {
    	// 모둠 과제 상세 조회
    	Map<String, Object> result = eaAsnComService.selectAsnGrpStuDetail(eaAsnStuDto);

    	return Response.ok(result);
    }

    /**
     * 과제 전체 제출 알림 전송
     * @param eaAsnStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="과제 전체 제출 알림 전송 ", description="과제 전체 제출 알림 전송")
    @PostMapping(value="/selectAllSmt")
    public ResponseDto<Map<String, Object>> selectAllSmt(@RequestBody EaAsnStuDto eaAsnStuDto) {

    	if (eaAsnStuDto.getAsnId() == null) {
    		return Response.fail("Params does not exist.");
    	}
    	
    	Map<String, Object> eaAllSmt = eaAsnComService.selectAllSmt(eaAsnStuDto);

    	return Response.ok(eaAllSmt);
    }
    
    
	/**
	  * 파일 조회 - 교사
	  * @param eaAsnTcrDto
	  * @return ResponseDto<List<EaAnsFleDto>>
	*/
	@Operation(summary="파일 조회 (교사)", description="파일 조회 (교사)")
	@PostMapping(value="/selectAsnFile")
	public ResponseDto<List<EaAsnFleDto>> selectAsnFile(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
		
		String annxId = String.valueOf(eaAsnTcrDto.getAnnxId());
		if(StringUtils.isEmpty(annxId)) {
			return Response.fail("Params does not exist.");
		}
	 	List<EaAsnFleDto> fileList = eaAsnComService.selectFile(annxId);
	 	
		return Response.ok(fileList);
	}
   

}
