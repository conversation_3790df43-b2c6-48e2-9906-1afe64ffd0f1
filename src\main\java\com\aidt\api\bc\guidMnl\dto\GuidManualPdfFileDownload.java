package com.aidt.api.bc.guidMnl.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuidManualPdfFileDownload {
    @Parameter(name="파일명")
    private String fleNm;

    @Parameter(name="파일원본명")
    private String fleOrglNm;

    @Parameter(name="파일경로")
    private String flePthNm;
}
