package com.aidt.api.ea.diyev.stu;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.evcom.diy.dto.DeleteDiyEvDto;
import com.aidt.api.ea.evcom.diy.dto.EaDiyEvReqDto;
import com.aidt.api.ea.evcom.diy.dto.EaDiyEvResDto;
import com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto;
import com.aidt.api.ea.evcom.diy.dto.SaveEaEvRsRtxmDto;
import com.aidt.api.ea.evcom.diy.dto.UpdateDiyEvNmDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto;

import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] DIY 평가 - 학생", description="DIY 평가 - 학생")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/stu/diyev")
public class EaDiyEvStuController {
	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	EaDiyEvStuService eaDiyEvStuService;


	/**
	 * 학생 DIY 평가 목록 조회 요청
	 *
	 * @param eaDiyEvReqDto
	 * @return ResponseList<eaDiyEvReqDto>
	 */
	@Tag(name="[ea] 학생 DIY 평가 목록 조회", description="학생 DIY 평가 목록 조회")
	@PostMapping(value = "/selectDiyEvStuList")
	public ResponseDto<List<EaDiyEvResDto>> selectDiyEvStuList(@RequestBody EaDiyEvReqDto eaDiyEvReqDto) {
		log.debug("Entrance selectDiyEvStuList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaDiyEvReqDto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		eaDiyEvReqDto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		return Response.ok(eaDiyEvStuService.selectDiyEvStuList(eaDiyEvReqDto));
	}

	/**
	 * 학생 DIY 시험지 명 수정 요청
	 *
	 * @param dto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 학생 DIY 시험지 명 수정", description="단건 수정")
	@PutMapping(value = "/updateDiyEvStuNm",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateDiyEvStuNm(@Valid @RequestBody UpdateDiyEvNmDto dto) {
		log.debug("Entrance updateDiyEvStuNm");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId()); //사용자 ID
		dto.setDbId(userDetails.getTxbId());  //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.updateDiyEvStuNm(dto));
	}

	/**
	 * 학생 DIY 평가 삭제 요청
	 *
	 * @param dto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 학생 DIY 평가 삭제", description="단건 삭제")
	@DeleteMapping(value = "/deleteDiyEvStu",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> deleteDiyEvStu(@Valid @RequestBody DeleteDiyEvDto dto) {
		log.debug("Entrance deleteDiyEvStu");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId());	 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.deleteDiyEvStu(dto));
	}

	/**
	 * 학생 DIY 평가 등록 요청
	 *
	 * @param dto
	 * @return ResponseDto<Map<String,Object>>
	 */
	@Tag(name="[ea] 학생 DIY 평가 등록", description="DIY 시험지 등록")
	@PostMapping(value = "/saveDiyEvStu", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Map<String,Object>> saveDiyEvStu(@Valid @RequestBody SaveDiyEvDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.saveDiyEvStu(dto));
	}
	
	/**
	 * 학생 DIY 평가 등록 요청
	 *
	 * @param dto
	 * @return ResponseDto<Map<String,Object>>
	 */
	@Tag(name="[ea] 학생 DIY 평가 등록", description="DIY 시험지 등록")
	@PostMapping(value = "/insertEvStu", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> insertEvStu(@Valid @RequestBody EaEvSaveReqDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.insertEvStu(dto));
	}
	

	/**
	 * 학생 DIY 평가 재응시 등록 요청
	 *
	 * @param dto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 학생 DIY 평가 재응시 등록", description="재응시 등록")
	@PostMapping(value = "/saveEaEvRsRtxm", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> saveEaEvRsRtxm(@Valid @RequestBody SaveEaEvRsRtxmDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.saveEaEvRsRtxm(dto));
	}

	/**
	 * 학생 DIY 평가  > 단원 별 난이도 문항 수 체크
	 *
	 * @param dto
	 * @return ResponseDto<Map<String,Object>>
	 */
	@Tag(name="[ea] 학생 DIY 평가 단원 별 난이도 문항 수 체크", description="학생 DIY 평가 단원 별 난이도 문항 수 체크")
	@PostMapping(value = "/checkStuQtmCnt", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Map<String,Object>> checkStuQtmCnt(@Valid @RequestBody SaveDiyEvDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		return Response.ok(eaDiyEvStuService.checkStuQtmCnt(dto));
	}

}
