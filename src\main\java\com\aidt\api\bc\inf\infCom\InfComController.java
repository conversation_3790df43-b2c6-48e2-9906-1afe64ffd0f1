package com.aidt.api.bc.inf.infCom;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.inf.infCom.dto.InfComDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-02-11 09:30:00
 * @modify 2024-01-05 09:30:00
 * @desc 알림공통 Controller
 */

@Slf4j
@Tag(name="[bc] 알림 공통", description="알림 공통")
@RestController
@RequestMapping("/api/v1/bc/inf/infCom")
public class InfComController {
	
	@Autowired
	private JwtProvider jwtProvider;

    @Autowired
    private InfComService infComService;
	
    
    @Operation(summary="평가 수정 알림 등록", description="평가 수정 알림 등록")
    @PostMapping(value = "/insertEvInfm")
    public ResponseDto<Integer> insertEvInfm(@RequestBody InfComDto dto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	dto.setUsrId(userDetails.getUsrId());
    	dto.setOptTxbId(userDetails.getOptTxbId());
		dto.setDbId(userDetails.getTxbId());		
    	return Response.ok(infComService.insertEvInfm(dto));
    }
    
    /**
     * 원클릭학습설정 > 콘텐츠 일괄 추천 알림등록
     * 
     * @param param
     * @return
     */
    @Operation(summary="콘텐츠 일괄 추천", description="콘텐츠 일괄 추천")
    @PostMapping(value = "/insertRcmCtnInfm")
    public ResponseDto<Integer> insertRcmCtnInfm(@RequestBody List<InfComDto> param) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	for(InfComDto dto : param) {    		    	
    		String rcmCtnNm = "선생님께서 " + dto.getRcmCtnNm() + " 학습을 추천했어요.";    		
    		String infmMvCn = "{\"filePath\":\"SLSP_LRN_STU_MAIN\",\"layout\":\"default\",\"params\":{\"spLrnId\":" + dto.getSpLrnId() + "}}";
    		dto.setInfmTpCd("LR");
    		dto.setInfmClCd("RC");
    		dto.setInfmDtlClCd("CR");	
    		dto.setInfmNm("추천학습 등록");
    		dto.setInfmCn(rcmCtnNm);
    		dto.setInfmMvCn(infmMvCn);
    		dto.setUsrId(userDetails.getUsrId());
    		dto.setDbId(userDetails.getTxbId());
    	}
    	return Response.ok(infComService.insertRcmCtnInfm(param));
    }
    
    /**
     * 공통 알림 삭제
     * 
     * @param map
     * @return
     */
	@Operation(summary="알림 삭제", description="알림을 삭제한다")
    @DeleteMapping(value = "/deleteInfCom")
	public ResponseDto<Integer> deleteInfCom(@RequestBody Map<String, Object> map) {		
		return Response.ok(infComService.deleteInfCom(map));
	}
}
