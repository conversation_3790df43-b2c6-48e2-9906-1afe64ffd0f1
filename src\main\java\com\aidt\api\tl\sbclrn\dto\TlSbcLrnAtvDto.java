package com.aidt.api.tl.sbclrn.dto;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 16:14:31
 * @modify date 2024-01-04 16:14:31
 * @desc TlStuSbcLrnLrnAtv Dto 학습활동상세Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvDto {
       /** 운영교과서ID */
       @Parameter(name="운영교과서ID")
       private String optTxbId;

       /** 학습맵노드ID */
       @Parameter(name="학습맵노드ID")
       private String lrmpNodId;

       /** 학습활동ID */
       @Parameter(name="학습활동ID")
       private String lrnAtvId;

       /** 학습단계ID */
       @Parameter(name="학습단계ID")
       private long lrnStpId;

       /** 학습단계 구분코드 */
       @Parameter(name="학습단계 구분코드")
       private String lrnStpDvCd;

       /** 콘텐츠코드 */
       @Parameter(name="콘텐츠코드")
       private String ctnCd;

       /** 학습활동명 */
       @Parameter(name="학습활동명")
       private String lrnAtvNm;

       /** 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
       @Parameter(name="콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지)")
       private String ctnTpCd;

       /** 사용여부 */
       @Parameter(name="사용여부")
       private String useYn;

       /** 재구성순서 */
       @Parameter(name="재구성순서")
       private int rcstnOrdn;

       /** 평가ID */
       @Parameter(name="평가ID")
       private long evId;

       /** 생성자ID */
       @Parameter(name="생성자ID")
       private String crtrId;

       /** 생성일시 */
       @Parameter(name="생성일시")
       private Timestamp crtDtm;

       /** 수정자ID */
       @Parameter(name="수정자ID")
       private String mdfrId;

       /** 수정일시 */
       @Parameter(name="수정일시")
       private Timestamp mdfDtm;

       /** 학습단계명 */
       @Parameter(name="학습단계명")
       private String lrnStpNm;
}
