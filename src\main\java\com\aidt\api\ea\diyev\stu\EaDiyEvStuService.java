package com.aidt.api.ea.diyev.stu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.ea.evcom.diy.dto.DeleteDiyEvDto;
import com.aidt.api.ea.evcom.diy.dto.EaDiyEvReqDto;
import com.aidt.api.ea.evcom.diy.dto.EaDiyEvResDto;
import com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto;
import com.aidt.api.ea.evcom.diy.dto.SaveEaEvRsRtxmDto;
import com.aidt.api.ea.evcom.diy.dto.UpdateDiyEvNmDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc DIY 평가 - 학생 Service
 */
//@Slf4j
@Service
public class EaDiyEvStuService {

	private final String MAPPER_NAMESPACE = "api.ea.diyev.stu.";

	private final String MAPPER_NAMESPACE_EV_TCR = "api.ea.ev.tcr.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private JwtProvider jwtProvider;

	/**
	 * 학생 DIY 평가 목록 조회 요청
	 *
	 * @param eaDiyEvReqDto
	 * @return List<EaDiyEvResDto>
	 */
	public List<EaDiyEvResDto> selectDiyEvStuList(EaDiyEvReqDto eaDiyEvReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectDiyEvStuList", eaDiyEvReqDto);
	}

	/**
	 * 학생 DIY 시험지 명 수정 요청
	 *
	 * @param dto
	 * @return int
	 */
	@Transactional
	public int updateDiyEvStuNm(UpdateDiyEvNmDto dto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateDiyEvStuNm", dto);
	}

	/**
     * <p>학생/선생 DIY 평가 삭제 요청</p>
     * <B>DELETE TARGET TABLE</B>
     * <li>EA_EV</li>
     * <li>EA_EV_RS</li>
     * <li>EA_EV_TS_RNGE</li>
	 * <li>EA_EV_DFFD_CSTN</li>
	 * <li>EA_EV_QTM</li>
	 * <li>EA_EV_QTM_ANW</li>
	 * <li>EA_EV_RS_RTXM</li>
	 * <li>EA_EV_QTM_ANW_RTXM</li>
     * @param DeleteDiyEvDto
     * @return int
     */
    @Transactional
    public int deleteDiyEvStu(DeleteDiyEvDto dto) {
    	int res = 0;

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		EaDiyEvReqDto reqDto = EaDiyEvReqDto.builder()
											.optTxbId(userDetails.getOptTxbId())
											.evId(dto.getEvId())
											.usrId(userDetails.getUsrId())
											.pageNo(0)
											.pageSize(1)
											.build();

		EaDiyEvResDto diyDto = commonDao.select(MAPPER_NAMESPACE + "selectDiyEvStuList", reqDto);

		if(diyDto == null) {
			throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "평가 정보를 확인할 수 없습니다.");
		}

		// 평가 문항 답변 재응시 [EA_EV_QTM_ANW_RTXM]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEvQtmAnwRtxm", diyDto);
    	// 평가 결과 재응시 [EA_EV_RS_RTXM]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEvRsRtxm", diyDto);
    	// 평가 문항 답변 [EA_EV_QTM_ANW]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEvQtmAnw", diyDto);
    	// 평가 문항 [EA_EV_QTM]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEvQtm", diyDto);
    	// 평가 난이도 [EA_EV_DFFD_CSTN]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEvDffd", diyDto);
    	// 평가 출제범위 [EA_EV_TS_RNGE]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEvTsRnge", diyDto);
    	// 평가 결과 [EA_EV_RS]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEaEvRs", diyDto);
    	// 평가 [EA_EV]
    	res += commonDao.delete(MAPPER_NAMESPACE_EV_TCR + "deleteEaEv", diyDto);

    	return res;
    }

	/**
	 * 학생 DIY 평가 등록 요청
	 *
	 * @param param
	 * @return Map
	 */
	@Transactional
	public Map<String, Object> saveDiyEvStu(SaveDiyEvDto param) {
		Map<String, Object> map = new HashMap<>();
		List<SaveDiyEvDto> qtmList = new ArrayList<>();
		map.put("code", "S");
		map.put("message", "시험지가 저장 되었어요.");
		// LMS_LRM.EA_EV(평가) INSERT
		commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStu", param);

		// LMS_LRM.EA_EV_RS(평가 결과) INSERT
		commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStuRs", param);

		// LMS_LRM.EA_EV_TS_RNGE(평가 시험 범위) INSERT
		if (param.getEaEvTsRngeList() != null && !param.getEaEvTsRngeList().isEmpty()) {
			int num = 1;
			for (SaveDiyEvDto dto : param.getEaEvTsRngeList()) {
				dto.setEvId(param.getEvId()); // 평가 ID
				dto.setUsrId(param.getUsrId()); // 사용자 ID
				dto.setOptTxbId(param.getOptTxbId()); // 운영 교과서 ID
				dto.setDbId(param.getDbId()); // DB ID
				dto.setTsRngeSeqNo(num);
				commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStuTsRnge", dto);
				num = num + 1;
			}
		}

		// LMS_LRM.EA_EV_DFFD_CSTN(평가 난이도 구성) INSERT
        if (param.getEaEvDffdCstnList() != null && !param.getEaEvDffdCstnList().isEmpty() && "SET".equals(param.getQuesMethod())) {
			for (SaveDiyEvDto dto : param.getEaEvDffdCstnList()) {
				dto.setEvId(param.getEvId()); // 평가 ID
				dto.setUsrId(param.getUsrId()); // 사용자 ID
				dto.setDbId(param.getDbId()); // DB ID
				dto.setEaEvTsRngeList(param.getEaEvTsRngeList());
				// 난이도 등록
				commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStuDffdCstn", dto);
				// 난이 별 문항 조회
				qtmList.addAll(commonDao.selectList(MAPPER_NAMESPACE + "selectQpQtmList", dto));
			}
		}
		// 문항 랜덤 정렬 및 문항 등록
		Collections.shuffle(qtmList);
		if (qtmList != null && !qtmList.isEmpty()) {
			int num = 1;
			for (SaveDiyEvDto qtm : qtmList) {
				qtm.setEvId(param.getEvId());
				qtm.setQtmOrdn(num);
				qtm.setDelYn("N");
				qtm.setUsrId(param.getUsrId());
				qtm.setDbId(param.getDbId());
				commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStuQtm", qtm);
				num = num + 1;
			}
		}

		map.put("evId", param.getEvId());
		return map;
	}

	/**
	 * 학생 DIY 평가 재응시 등록 요청
	 *
	 * @param dto
	 * @return int
	 */
	@Transactional
	public int saveEaEvRsRtxm(SaveEaEvRsRtxmDto dto) {
		int res = commonDao.select(MAPPER_NAMESPACE + "selectEaEvRsRtxm", dto);
		if (res == 0) { // 진행 중인 재응시 없음
			res = commonDao.insert(MAPPER_NAMESPACE + "saveEaEvRsRtxm", dto);
		}
		return res;
	}


	/**
	 * 학생 DIY 평가 단원 + 난이도 별 문항 수 체크
	 *
	 * @param param
	 * @return Map<String, Object>
	 */
	@Transactional
	public Map<String, Object> checkStuQtmCnt(SaveDiyEvDto param) {
		Map<String, Object> map = new HashMap<>();
		List<Map<String, Object>> eaEvDffdCstnList = new ArrayList<>();
		List<SaveDiyEvDto> qtmList = new ArrayList<>();
		int matchCnt = 0;
		int missMatchCnt = 0;
		int emptyCnt = 0;
		int totalCnt = 0;

		// 난이도 별 문항 수 체크 (난이도에 해당하는 문항이 없는 경우도 있음.)
        if (param.getEaEvDffdCstnList() != null && !param.getEaEvDffdCstnList().isEmpty() && "SET".equals(param.getQuesMethod())) {
			totalCnt = param.getEaEvDffdCstnList().size();
			for (SaveDiyEvDto dto : param.getEaEvDffdCstnList()) {
				Map<String, Object> eaEvDffdCstn = new HashMap<>();
				dto.setEaEvTsRngeList(param.getEaEvTsRngeList());
				List<SaveDiyEvDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectQpQtmList", dto);
				if (list.size() == dto.getEvDffdDsbCnt()) {
					eaEvDffdCstn.put("evDffdDvCd", dto.getEvDffdDvCd());
					eaEvDffdCstn.put("evDffdDsbCnt", list.size());
					eaEvDffdCstnList.add(eaEvDffdCstn);
					qtmList.addAll(list);
					matchCnt = matchCnt + 1;
				} else if (list != null && !list.isEmpty() && list.size() < dto.getEvDffdDsbCnt()) {
					eaEvDffdCstn.put("evDffdDvCd", dto.getEvDffdDvCd());
					eaEvDffdCstn.put("evDffdDsbCnt", list.size());
					eaEvDffdCstnList.add(eaEvDffdCstn);
					qtmList.addAll(list);
					missMatchCnt = missMatchCnt + 1;
				} else if (list.isEmpty()) {
					emptyCnt = emptyCnt + 1;
				}
			}
		}
		// 조건에 맞는 문항이 아예 없는 경우
		if (totalCnt == emptyCnt) {
			map.put("code", "F");
			map.put("message", "조건에 맞는 문제가 존재하지 않습니다.");
			return map;
        } else if (eaEvDffdCstnList != null && !eaEvDffdCstnList.isEmpty() && missMatchCnt >= 0 && matchCnt < totalCnt) {
			map.put("code", "N");
			map.put("message", "사용자가 원하는 문항 구성을 할 수 없어 문항 구성이 자동으로 변경되어 출제됩니다.");
			map.put("eaEvDffdCstnList", eaEvDffdCstnList); // 재구성 난이도 별 문제 수
			map.put("qstCnt", qtmList.size());// 재구성 문제 수
			return map;
		} else if (totalCnt == matchCnt) {
			map.put("code", "S");
		}

		return map;
	}


	/**
	 * DIY 평가 - 평가 정보 등록 (교사평가, DIY평가)
	 *
	 * @param evReqDto
	 * @return int
	 */
	@Transactional
	public int insertEvStu(EaEvSaveReqDto evReqDto) {

		commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStu", evReqDto);
		commonDao.insert(MAPPER_NAMESPACE + "insertDiyEvStuRs", evReqDto);
		commonDao.insert(MAPPER_NAMESPACE + "insertEvDffd", evReqDto);
		commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRnge", evReqDto);

		// 문항의 등록정보 조회
		evReqDto.setQtmIdList(commonDao.selectList(MAPPER_NAMESPACE + "selectSaveQtmList", evReqDto));
		commonDao.insert(MAPPER_NAMESPACE + "insertEvQtm", evReqDto);


		return 1;
	}


}
