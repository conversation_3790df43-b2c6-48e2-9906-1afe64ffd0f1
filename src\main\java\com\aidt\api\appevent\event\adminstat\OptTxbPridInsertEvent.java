package com.aidt.api.appevent.event.adminstat;

import java.time.LocalDateTime;

import com.aidt.base.message.application.AbstractAppEvent;
import com.aidt.base.message.messagequeue.AbstractPayload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 운영 교과서 교시 데이터 등록 Event
 * - 변수 추가 및 수정 시 관리자 kafka 개발 담당자 전달 필수
 */
public class OptTxbPridInsertEvent extends AbstractAppEvent {

	public OptTxbPridInsertEvent(OptTxbPridInsertPayload payload) {
		super(payload);
	}
	
	@Builder
	@Getter
	@ToString
	public static class OptTxbPridInsertPayload extends AbstractPayload {
		
		@Schema(description = "운영교과서ID")
		private String optTxbId;
		
		@Schema(description = "운영교과서교시")
		private String optTxbPrid;
		
		@Schema(description = "KERIS강의코드")
		private String kerisLectCd;
		
		@Schema(description = "등록일시")
		private LocalDateTime regDtm;
	}
}
