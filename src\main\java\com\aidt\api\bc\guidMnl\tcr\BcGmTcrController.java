package com.aidt.api.bc.guidMnl.tcr;


import com.aidt.api.bc.guidMnl.dto.*;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;

@Slf4j
@Tag(name="[bc] 가이드매뉴얼[BcGuidMnl]", description="가이드 매뉴얼")
@RestController
@RequestMapping("/api/v1/bc/guid")
public class BcGmTcrController {

    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;

    @Value("${spring.profiles.active}")
    private String SERVER_ACTIVE;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
    private String BUCKET_NAME;

    @Autowired
    private BcGmTcrService bcGmTcrService;

    @Autowired
    private JwtProvider jwtProvider;


    @Operation(summary="매뉴얼 조회", description="교과서 id로 해당 교과서의 가이드매뉴얼 노드 데이터를 가져온다. (없을 시 기본값 생성 후 반환)")
    @GetMapping("")
    public ResponseDto<GuideManual> getGuideManualByTextbookId(@RequestParam Map<String, Object> param) {
        return Response.ok(bcGmTcrService.getGuideManualHierarchyByTextbookId(param));
    }

    @Operation(summary="매뉴얼 조회", description="교과서 id로 해당 교과서의 가이드매뉴얼 노드 데이터를 가져온다. (없을 시 기본값 생성 후 반환)")
    @GetMapping("/get1depths")
    public ResponseDto<GuideManual> getGuideManualOnly1depthByTextbookId(@RequestParam Map<String, Object> param) {
        return Response.ok(bcGmTcrService.getGuideManual1depthByTextbookId(param));
    }

    @Operation(summary="복사할 매뉴얼 조회", description="기존 교과서 매뉴얼을 복사하기 위해 리스트를 가지고 온다")
    @GetMapping("/getMnls")
    public ResponseDto<List<Map<String, Object>>> getGuideManuals(@RequestParam String role) {
        return Response.ok(bcGmTcrService.getGuideManuals(role));
    }

    @Operation(summary="매뉴얼 노드 저장", description="매뉴얼 노드를 저장한다.")
    @PostMapping("/saveNod")
    ResponseDto<Integer> saveGuideManualNode(@RequestBody GuideManualNode content) {
        return Response.ok(bcGmTcrService.saveGuideManualNode(content));
    }

    @Operation(summary = "3뎁스 순서 변경", description = "3뎁스 순서 변경 시 ordn를 업데이트한다.")
    @PostMapping("/reorder")
    ResponseDto<Integer> saveGuideManualNodeReorder(@RequestBody List<GuideManualNode> nodes) {
        return Response.ok(bcGmTcrService.saveGuideManualNodeReorder(nodes));
    }

    @Operation(summary="타교과서 메뉴얼 복사 or 매뉴얼 초기화", description = "타교과서의 매뉴얼 목차, 내용을 복사한다 / 매뉴얼을 초기화한다.(toCopyId = 0)")
    @PostMapping("/copyMnl")
    ResponseDto<Integer> copyGuideManual(@RequestBody Map<String, Object> param) {
        return Response.ok(bcGmTcrService.copyManual(param));
    }

    @Operation(summary="매뉴얼 노드 삭제", description="매뉴얼 노드를 삭제한다.")
    @PostMapping("/delNod")
    ResponseDto<Integer> deleteGuideNodeByNodeId(@RequestBody List<Long> ids) {
        return Response.ok(bcGmTcrService.deleteGuideManualNode(ids));
    }

    ///////// file upload
    @Operation(summary="매뉴얼 이미지 업로드", description="매뉴얼 이미지를 업로드한다.")
    @PostMapping(value = "/fileUpload",
            consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseDto<GuideManualUpload> fileUpload(@RequestBody MultipartFile file, Long nodeId)
            throws IllegalStateException, IOException {
        // validation
        Optional.ofNullable(file).orElseThrow(() -> new FileNotFoundException("File Not Found!"));
        try {
            return Response.ok(bcGmTcrService.saveGuideManualUpload(file, nodeId));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Operation(summary="매뉴얼 가이드 업로드", description="매뉴얼 가이드(PDF, ZIP)를 업로드한다.")
    @PostMapping(value = "/pdfUpload",
            consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseDto<GuidManualPdfFile> pdfUpload(
            @RequestParam("file") MultipartFile file,
            @RequestParam("guideManualId") Long guidMnlId,
            @RequestParam("textbookId") Long txbId
    )
            throws IllegalStateException, IOException {
        // validation
        Optional.ofNullable(file).orElseThrow(() -> new FileNotFoundException("File Not Found!"));
        try {
            return Response.ok(bcGmTcrService.savePdfUpload(file, guidMnlId, txbId));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }


    /**
     * Delete File
     *
     * @param upload
     * @return ResponseDto<Integer>
     * @throws IllegalStateException
     * @throws IOException
     */
    @Operation(summary="매뉴얼 이미지 삭제", description="매뉴얼 이미지를 삭제한다.")
    @PostMapping(value = "/deleteFile")
    public ResponseDto<Integer> deleteFile(@RequestBody GuideManualUpload upload)
            throws IllegalStateException, IOException {
        Optional.ofNullable(upload.getGuidMnlNodFleNm()).orElseThrow(() -> new NoSuchElementException("file name is empty"));
        try {
            return Response.ok(bcGmTcrService.deleteGuideManualUpload(upload));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }

    }

    @Operation(summary="매뉴얼 PDF 파일 조회", description="매뉴얼 PDF 파일을 조회한다.")
    @PostMapping(value = "/getPdfFiles")
    public ResponseDto<List<GuidManualPdfFile>> getPdfFiles(@RequestBody GuidManualPdfFile manualPdfFile) {
        return Response.ok(bcGmTcrService.getGuideManualPdfFiles(manualPdfFile));
    }

    @Operation(summary="매뉴얼 PDF 파일 이름 조회", description="매뉴얼 PDF 파일 이름을 조회한다.")
    @PostMapping(value = "/getGuidFileName")
    public ResponseDto<GuidManualPdfFileDownload> getGuidFileName(@RequestBody GuidManualPdfFile manualPdfFile) {
        return Response.ok(bcGmTcrService.getGuidFileName(manualPdfFile));
    }

    @Operation(summary="매뉴얼 PDF 파일 사용여부 변경", description="매뉴얼 PDF 파일 사용여부를 변경한다.")
    @PostMapping(value = "/updatePdfFileUseYn")
    public ResponseDto<Integer> updatePdfFileUseYn(@RequestBody GuidManualPdfFile manualPdfFile) {
        return Response.ok(bcGmTcrService.updatePdfFileUseYn(manualPdfFile));
    }

    @Operation(summary="매뉴얼 PDF 및 ZIP 파일 다운로드", description="매뉴얼 PDF 및 ZIP 파일을 다운로드한다.")
    @PostMapping(value = "/downloadPdfFile")
    public ResponseEntity<?> downloadPdfFile(@RequestBody GuidManualPdfFile manualPdfFile) {
        return bcGmTcrService.downloadPdfFile(manualPdfFile);
    }


    @Operation(summary="매뉴얼 PDF 및 VIDEO 파일 업로드", description="매뉴얼 PDF 및 VIDEO 파일을 업로드한다.")
    @PostMapping("/saveGuideVideoFileUpload")
    public void saveFile(@RequestParam(value = "file", required = false) MultipartFile file,
                         @RequestParam("guidMnlNodId") Long guidMnlNodId,
                         @RequestParam(value = "guidMnlNodFleId", required = false) Long guidMnlNodFleId,
                         @RequestParam(value = "guidMnlVdSmyCn", required = false) String guidMnlVdSmyCn,
                         @RequestParam("contentType") String contentType) throws JsonProcessingException {
        var guideManualFileUploadReqDto = GuideManualFileUploadReqDto.builder()
                .file(file).guidMnlNodId(guidMnlNodId).guidMnlNodFleId(guidMnlNodFleId).guidMnlVdSmyCn(guidMnlVdSmyCn).contentType(contentType).build();

        bcGmTcrService.saveContentFile(guideManualFileUploadReqDto);
    }
}
