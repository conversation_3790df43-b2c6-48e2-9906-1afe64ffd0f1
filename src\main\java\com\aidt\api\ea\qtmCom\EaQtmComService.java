package com.aidt.api.ea.qtmCom;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.ea.evcom.dto.EaEvComDto;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.api.ea.qtmCom.dto.EaQtmAiHintDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;

import com.aidt.common.CommonDao;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 14:05:오후 2:05
 * @modify date 2024-02-15 14:05:오후 2:05
 * @desc 평가(DIY 평가) - Service
 */

@Service
public class EaQtmComService {
    private final String MAPPER_NAMESPACE = "api.ea.qtmcom.";
    private final String DOMAIN_PATH = "/api/v1/content/";

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;

	
    @Autowired
    private CommonDao commonDao;
	

    public String getLocalDomain() {
        String domain = "";
		try {
				BUCKET_NAME = BUCKET_NAME.replace("dev", "prd");

				if("local".equals(SERVER_ACTIVE)) {
					domain = "https://www-n-ele.aitextbook.co.kr";
				}
		}
		finally {}

		return domain;
    }

    /**
     * 문항플랫폼 AI힌트 조회 요청
     *
     * @param eaEvComDto
     * @return List<Map<String,String>>
     */
    public List<EaQtmAiHintDto> selectQpQtmAiHntList(EaEvQtmIdReqDto eaEvComDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectQpQtmAiHntList", eaEvComDto);
    }

    /**
     * 교사평가/DIY평가 - 평가 추가 - 난이도별 문항ID 리스트 조회 요청
     *
     * @param evReqDto
     * @return List<EaEvDffdsQpQtmResDto>
     */
    public List<EaEvDffdsQpQtmResDto> selectDffdDsbQtmIdList(EaEvDffdsQpQtmReqDto evReqDto) {
    	
    	Map<String, Object> dffdQtmReq = new HashMap<>();
    	
    	List<Map<String, Object>> tpcList = commonDao.selectList(MAPPER_NAMESPACE + "selectLuSearchTpcIdList", evReqDto);
    	
    	if(tpcList == null || tpcList.isEmpty())
    	{
    		return null;
    	}

    	
    	dffdQtmReq.put("isDiy", evReqDto.getIsDiy());
    	dffdQtmReq.put("tpcList", tpcList);
    	dffdQtmReq.put("dffdDsbList", evReqDto.getDffdDsbList());
    	
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectDffdTpcQtmList", dffdQtmReq);
    }


}
