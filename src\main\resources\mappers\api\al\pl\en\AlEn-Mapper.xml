<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.en">

	<select id="selectMluTcList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    MAX(DPTH1.KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
		    MAX(DPTH1.KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
		    DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
		    MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
		    DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
		    MAX(DPTH4.KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
		    DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
		    MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
		    MAX(DPTH1.TC_EPS_YN) AS TC_EPS_YN,
		    MAX(DPTH1.TC_USE_YN) AS TC_USE_YN,
		    MAX(DPTH1.RCSTN_ORDN) AS RCSTN_ORDN,
		    MAX(DPTH1.ORGL_ORDN) AS ORGL_ORDN,
		    MAX(DPTH1.LCKN_YN) AS LCKN_YN,
		    MAX(DPTH1.USE_YN) AS USE_YN,
		    IFNULL(MAX(AUTP.TPC_AVN), 0.5) AS TPC_AVN,
		    MAX(BALI.LU_IMG_PTH) AS LU_IMG_PTH,
		    MAX(OV.TC_AVG_CANS_RT) AS TC_AVG_CANS_RT,
		    MAX(OVCNT.OV_QTM_CNT) AS OV_QTM_CNT
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
		INNER JOIN LMS_CMS.BC_KMMP_NOD BKN
		    ON DPTH1.KMMP_NOD_ID = BKN.KMMP_NOD_ID
		    AND BKN.LU_EPS_YN = 'Y'
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
		    ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
		    AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		    AND DPTH2.DPTH = 2
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
		    ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
		    AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
		    AND DPTH3.DPTH = 3
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
		    ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
		    AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
		    AND DPTH4.DPTH = 4
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
		    ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
		    AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		    AND DPTH5.DPTH = 5
		LEFT OUTER JOIN (
		    SELECT
		        USR_ID,
		        LU_KMMP_NOD_ID,
		        TC_KMMP_NOD_ID,
		        AVG(TPC_AVN) AS TPC_AVN
		    FROM LMS_LRM.AI_USRLY_TPC_PROF
		    WHERE USR_ID = #{usrId}
		    GROUP BY LU_KMMP_NOD_ID, TC_KMMP_NOD_ID, USR_ID
		) AUTP
		    ON AUTP.LU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
		    AND AUTP.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
		LEFT OUTER JOIN (
		    SELECT
		        EAETR.MLU_KMMP_NOD_ID,
		        EAETR.TC_KMMP_NOD_ID,
		        (SUM(EEQA.CANS_YN = 'Y') / COUNT(EEQ.QTM_ID)) * 100 AS TC_AVG_CANS_RT
		    FROM LMS_LRM.EA_EV EE
		    INNER JOIN LMS_LRM.EA_EV_RS EER
		        ON EE.EV_ID = EER.EV_ID
		    INNER JOIN LMS_LRM.EA_EV_QTM EEQ
		        ON EE.EV_ID = EEQ.EV_ID
		    INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
		        ON EE.EV_ID = EEQA.EV_ID
		        AND EEQ.QTM_ID = EEQA.QTM_ID
		    INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
		        ON EE.EV_ID = EAETR.EV_ID
		        AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
		    WHERE EER.USR_ID = #{usrId}
		        AND EE.EV_DV_CD = 'AE'
		        AND EER.EV_CMPL_YN = 'Y'
		        AND EE.EV_DTL_DV_CD = 'OV'
		        AND EE.OPT_TXB_ID = #{optTxbId}
		    GROUP BY EAETR.TC_KMMP_NOD_ID, EAETR.MLU_KMMP_NOD_ID
		) OV
		    ON OV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
		    AND OV.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
		LEFT OUTER JOIN (
		    SELECT
		        BE.MLU_NOD_ID,
		        COUNT(*) AS OV_QTM_CNT
		    FROM LMS_CMS.BC_EVSH BE
		    INNER JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM
		        ON BE.EVSH_ID = BEQM.EVSH_ID
		    WHERE BE.EVSH_TP_CD = 'AI'
		        AND BE.DEL_YN = 'N'
		        AND BE.USE_YN = 'Y'
		    GROUP BY BE.MLU_NOD_ID
		) OVCNT
		    ON OVCNT.MLU_NOD_ID = DPTH2.KMMP_NOD_ID
		LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
		    ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
		    AND BALAC.CTN_TP_CD = 'QU'
		    AND BALAC.DEL_YN = 'N'
		LEFT OUTER JOIN LMS_CMS.BC_AI_LU_IMG BALI
		    ON BALI.LU_NOD_ID = DPTH2.KMMP_NOD_ID
		WHERE DPTH1.OPT_TXB_ID = #{optTxbId}
		    AND DPTH1.DPTH = 1
		    AND DPTH1.USE_YN = 'Y'
		    AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY DPTH5.KMMP_NOD_ID, DPTH2.KMMP_NOD_ID, DPTH4.KMMP_NOD_ID
		ORDER BY 
		    MAX(DPTH1.RCSTN_ORDN), 
		    MAX(DPTH1.ORGL_ORDN), 
		    MAX(DPTH4.ORGL_ORDN)
		/* 영어 중단원 리스트 - 이혜인 - AlEn-Mapper.xml - selectMluTcList */
	</select>

	<select id="selectAeEvInfoList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			EE.EV_ID,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
		    MAX(EAETR.TC_KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
		    MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
		    IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS LUEV_CMPL_YN,
		    MAX(EE.EV_DV_CD) AS EV_DV_CD,
		    MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
		    MAX(EER.EV_CMPL_YN) AS EV_CMPL_YN,
		    MAX(EER.MDF_DTM) AS MDF_DTM,
		    ROW_NUMBER() OVER (
		        ORDER BY MAX(EER.MDF_DTM) DESC
		    ) AS mdfDtmOrder,
		    MAX(EE.LCKN_YN) AS LCKN_YN
		FROM
		    LMS_LRM.EA_EV EE
				 INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					ON EE.EV_ID = EAETR.EV_ID
				 INNER JOIN LMS_LRM.EA_EV_RS EER
					ON EE.EV_ID = EER.EV_ID
				 INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR
					ON EAETR.MLU_KMMP_NOD_ID = AKNR.KMMP_NOD_ID
					AND AKNR.DPTH  = 2
					AND AKNR.OPT_TXB_ID = #{optTxbId}
		WHERE
		    EAETR.OPT_TXB_ID = #{optTxbId}
			AND EER.USR_ID = #{usrId}
			AND EE.EV_DV_CD = 'AE'
			AND AKNR.USE_YN = 'Y'
		GROUP BY
		    EE.EV_ID
		ORDER BY
		    MAX(EAETR.MLU_KMMP_NOD_ID),
		   MAX(EAETR.TC_KMMP_NOD_ID),
		    MAX(EE.EV_DTL_DV_CD)
		/* 영어 중단원 리스트 - 이혜인 - AlEn-Mapper.xml - selectAeEvInfoList */
	</select>

	<select id="selectAlPlEvQtmAnwList" parameterType="com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
		/* 영어 진단평가 답변 조회 - 이현빈 - AlEn-Mapper.xml - selectAlPlEvQtmAnwList */
		SELECT
		r.*
		, ROW_NUMBER() OVER(PARTITION BY r.evId, r.usrId ORDER BY r.qtmOrdn) AS qtmNo
		from (
		select
		MAX(EAETR.TPC_KMMP_NOD_NM) as qpTcNm,
		MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
		e.EV_DTL_DV_CD,
		MAX(aqxs.AVG_CANS_RT) AS AVG_CANS_RT
		, MAX(Q.EV_ID) AS evId
		, MAX((SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = Q.EV_ID)) AS evNm
		, Q.QTM_ID AS qtmId
		, MAX(Q.QTM_ORDN) AS qtmOrdn
		, ROW_NUMBER() OVER(PARTITION BY MAX(Q.EV_ID), MAX(QA.USR_ID) ORDER BY MAX(Q.QTM_ORDN)) AS qtmNo
		, MAX(Q.QTM_DFFD_DV_CD) AS qtmDffdDvCd
		, MAX(Q.DEL_YN) AS delYn
		, MAX(Q.DEL_DTM) AS delDtm
		, MAX(QA.USR_ID) AS usrId
		, MAX(QA.SMT_ANW_VL) AS smtAnwVl
		, MAX(QA.CANS_YN) AS cansYn
		, MAX(QA.XPL_TM_SCNT) AS xplTmScnt
		, DATE_FORMAT(SEC_TO_TIME(MAX(QA.XPL_TM_SCNT)), '%i:%s') AS xplTmScntNm -- 풀이시간 초수
		, DATE_FORMAT(SEC_TO_TIME(avg(a.XPL_TM_SCNT)), '%i:%s') as avgXplTmScnt
		, MAX(QA.HNT_COFM_YN) AS HNT_COFM_YN
		, MAX(QA.XPL_ST_CD) AS XPL_ST_CD
		, MAX((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'XPL_ST_CD' AND CM_CD = QA.XPL_ST_CD)) AS XPL_ST_NM
		, MAX(QA.CRTR_ID) AS crtrId
		, MAX(QA.CRT_DTM) AS crtDtm
		, MAX(QA.MDFR_ID) AS mdfrId
		, MAX(QA.MDF_DTM) AS mdfDtm
		, MAX(QA.DB_ID) AS dbId
		, MAX(aknr.ORGL_ORDN) as orglOrdn
		, MAX(EAETR.AI_TS_RNGE_SEQ_NO) as aiTsRngeSeqNo
		, E.EV_DTL_DV_CD as evDtlDvCd
		, MAX(EAETR.TC_KMMP_NOD_NM) as tcKmmpNodNm
		FROM LMS_LRM.EA_EV_QTM Q
		JOIN
		LMS_LRM.EA_EV_QTM_ANW
		QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID
		LEFT JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = Q.QTM_ID
		LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
		inner join lms_lrm.ea_ev e
		on e.ev_id = Q.ev_id
		inner join lms_lrm.EA_AI_EV_TS_RNGE EAETR
		on eaetr.ev_id = e.ev_id
		and Q.TPC_ID = eaetr.TPC_KMMP_NOD_ID
		left outer join (
		SELECT
		b.QTM_ID,
		SUM(b.CANS_YN = 'Y')/COUNT(b.CANS_YN ) as AVG_CANS_RT
		FROM LMS_LRM.EA_EV a
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW b
		ON a.EV_ID = b.EV_ID
		where a.OPT_TXB_ID = #{optTxbId}
		GROUP BY b.QTM_ID
		)aqxs on aqxs.qtm_id = Q.qtm_id
		left join (
		SELECT EEQA.XPL_TM_SCNT, EEQA.QTM_ID
		FROM LMS_LRM.EA_EV_QTM_ANW EEQA
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ
		ON EEQA.EV_ID = EEQ.EV_ID
		AND EEQA.QTM_ID = EEQ.QTM_ID
		INNER JOIN LMS_LRM.EA_EV EE
		ON EEQ.EV_ID = EE.EV_ID
		WHERE OPT_TXB_ID = #{optTxbId}
		)a on a.qtm_id = QA.qtm_id
		left join lms_lrm.ai_kmmp_nod_rcstn aknr
		on eaetr.TC_KMMP_NOD_ID = aknr.KMMP_NOD_ID
		and aknr.OPT_TXB_ID = '241-enm01l204'
		and aknr.DPTH = 4
		WHERE Q.EV_ID in
		<foreach collection="evIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
		AND Q.DEL_YN = 'N'
		AND QA.USR_ID = #{usrId}
		group by E.EV_DTL_DV_CD, QA.qtm_id
		) r
		<if test="evDtlDvCd == 'OV'">
			ORDER BY r.QTMoRDN, r.ORGLORDN, r.AITSRNGESEQNO,  r.EVDTLDVCD,  r.TCKMMPNODNM
		</if>
		<if test="evDtlDvCd != 'OV'">
			ORDER BY r.ORGLORDN, r.EVDTLDVCD,r.QTMoRDN, r.AITSRNGESEQNO, r.TCKMMPNODNM
		</if>
		/* 영어 진단평가 답변 조회 - 이현빈 - AlEn-Mapper.xml - selectAlPlEvQtmAnwList */
	</select>
	
<!--	<select id="selectAlPlEvQtmAnwList" parameterType="com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">-->
<!--		/* 영어 진단평가 답변 조회 - 김현혜 - AlEn-Mapper.xml - selectAlPlEvQtmAnwList */-->
<!--		select-->
<!--				MAX(EAETR.TPC_KMMP_NOD_NM) as qpTcNm,-->
<!--			MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM,-->
<!--					 e.EV_DTL_DV_CD,-->
<!--					 MAX(aqxs.AVG_CANS_RT) AS AVG_CANS_RT-->
<!--				        , MAX(Q.EV_ID) AS evId-->
<!--				        , MAX((SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = Q.EV_ID)) AS evNm-->
<!--				        , Q.QTM_ID AS qtmId-->
<!--				        , MAX(Q.QTM_ORDN) AS qtmOrdn-->
<!--				        , ROW_NUMBER() OVER(PARTITION BY MAX(Q.EV_ID), MAX(QA.USR_ID) ORDER BY MAX(Q.QTM_ORDN)) AS qtmNo-->
<!--				        , MAX(Q.QTM_DFFD_DV_CD) AS qtmDffdDvCd-->
<!--				        , MAX(Q.DEL_YN) AS delYn-->
<!--				        , MAX(Q.DEL_DTM) AS delDtm-->
<!--				        , MAX(QA.USR_ID) AS usrId-->
<!--				        , MAX(QA.SMT_ANW_VL) AS smtAnwVl-->
<!--				        , MAX(QA.CANS_YN) AS cansYn-->
<!--				        , MAX(QA.XPL_TM_SCNT) AS xplTmScnt -->
<!--				        , DATE_FORMAT(SEC_TO_TIME(MAX(QA.XPL_TM_SCNT)), '%i:%s') AS xplTmScntNm &#45;&#45; 풀이시간 초수-->
<!--				        , DATE_FORMAT(SEC_TO_TIME(avg(a.XPL_TM_SCNT)), '%i:%s') as avgXplTmScnt-->
<!--				        , MAX(QA.HNT_COFM_YN) AS HNT_COFM_YN-->
<!--				        , MAX(QA.XPL_ST_CD) AS XPL_ST_CD-->
<!--				        , MAX((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'XPL_ST_CD' AND CM_CD = QA.XPL_ST_CD)) AS XPL_ST_NM-->
<!--				        , MAX(QA.CRTR_ID) AS crtrId-->
<!--				        , MAX(QA.CRT_DTM) AS crtDtm-->
<!--				        , MAX(QA.MDFR_ID) AS mdfrId-->
<!--				        , MAX(QA.MDF_DTM) AS mdfDtm-->
<!--				        , MAX(QA.DB_ID) AS dbId-->
<!--		        FROM LMS_LRM.EA_EV_QTM Q-->
<!--		        JOIN -->
<!--		        	LMS_LRM.EA_EV_QTM_ANW-->
<!--		        QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID-->
<!--		        LEFT JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = Q.QTM_ID-->
<!--		        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID-->
<!--		        inner join lms_lrm.ea_ev e-->
<!--		        on e.ev_id = Q.ev_id-->
<!--		        inner join lms_lrm.EA_AI_EV_TS_RNGE EAETR -->
<!--		        on eaetr.ev_id = e.ev_id-->
<!--		        and Q.TPC_ID = eaetr.TPC_KMMP_NOD_ID-->
<!--				left outer join (-->
<!--					SELECT-->
<!--						b.QTM_ID,-->
<!--						SUM(b.CANS_YN = 'Y')/COUNT(b.CANS_YN ) as AVG_CANS_RT-->
<!--					FROM LMS_LRM.EA_EV a-->
<!--					INNER JOIN LMS_LRM.EA_EV_QTM_ANW b-->
<!--					ON a.EV_ID = b.EV_ID-->
<!--					where a.OPT_TXB_ID = #{optTxbId}-->
<!--					GROUP BY b.QTM_ID-->
<!--				)aqxs on aqxs.qtm_id = Q.qtm_id-->
<!--		        left join (-->
<!--		        	SELECT EEQA.XPL_TM_SCNT, EEQA.QTM_ID-->
<!--					FROM LMS_LRM.EA_EV_QTM_ANW EEQA-->
<!--					INNER JOIN LMS_LRM.EA_EV_QTM EEQ -->
<!--					ON EEQA.EV_ID = EEQ.EV_ID-->
<!--					AND EEQA.QTM_ID = EEQ.QTM_ID-->
<!--					INNER JOIN LMS_LRM.EA_EV EE -->
<!--					ON EEQ.EV_ID = EE.EV_ID-->
<!--					WHERE OPT_TXB_ID = #{optTxbId}-->
<!--		        )a on a.qtm_id = QA.qtm_id -->
<!--		        left join lms_lrm.ai_kmmp_nod_rcstn aknr -->
<!--		        on eaetr.TC_KMMP_NOD_ID = aknr.KMMP_NOD_ID -->
<!--		        and aknr.OPT_TXB_ID = #{optTxbId}-->
<!--		        and aknr.DPTH = 4-->
<!--		        WHERE Q.EV_ID in-->
<!--		        <foreach collection="evIds" item="id" open="(" close=")" separator=",">-->
<!--				 	#{id}-->
<!--				</foreach>-->
<!--		        AND Q.DEL_YN = 'N'-->
<!--		        AND QA.USR_ID = #{usrId}-->
<!--		        group by E.EV_DTL_DV_CD, QA.qtm_id-->
<!--		        <if test="evDtlDvCd == 'OV'">-->
<!--		        	ORDER BY MAX(Q.QTM_ORDN),MAX(aknr.ORGL_ORDN), MAX(EAETR.AI_TS_RNGE_SEQ_NO), E.EV_DTL_DV_CD, MAX(EAETR.TC_KMMP_NOD_NM)-->
<!--		        </if>-->
<!--		        <if test="evDtlDvCd != 'OV'">-->
<!--		        	ORDER BY MAX(aknr.ORGL_ORDN), E.EV_DTL_DV_CD, MAX(Q.QTM_ORDN), MAX(EAETR.AI_TS_RNGE_SEQ_NO), MAX(EAETR.TC_KMMP_NOD_NM)-->
<!--		        </if>-->
<!--		/* 영어 진단평가 답변 조회 - 김현혜 - AlEn-Mapper.xml - selectAlPlEvQtmAnwList */-->
<!--	</select>-->
	
	<select id="selectAlEvTcRptList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
		/* 영어 진단평가 차시별 정답률 조회 - 김현혜 - AlEn-Mapper.xml - selectAlEvTcRptList */
		SELECT MAX(EE.EV_ID)
		     , EAETR.TC_KMMP_NOD_ID AS TC_KMMP_NOD_ID
		     , MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM
		     , SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS SUM_CANS_YN
		     , IFNULL(SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) / COUNT(EAETR.TC_KMMP_NOD_ID) * 100, 0) AS tcCansRt
		     , COUNT(EAETR.TC_KMMP_NOD_ID) AS qtmCnt
		FROM LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID and eeq.TPC_ID = eaetr.TPC_KMMP_NOD_ID
		INNER JOIN lms_lrm.ai_kmmp_nod_rcstn aknr 
		        on eaetr.TC_KMMP_NOD_ID = aknr.KMMP_NOD_ID 
		        and aknr.OPT_TXB_ID = #{optTxbId}
		        and aknr.DPTH = 4
		WHERE EER.USR_ID = #{usrId}
		AND EE.EV_DV_CD = 'AE'
		AND EE.ev_id in
		<foreach collection="evIds" item="id" open="(" close=")" separator=",">
		 	#{id}
		</foreach>
		AND EER.EV_CMPL_YN = 'Y'
		GROUP BY EAETR.TC_KMMP_NOD_ID 
		ORDER BY MAX(aknr.ORGL_ORDN), MAX(EAETR.AI_TS_RNGE_SEQ_NO), MAX(EAETR.TC_KMMP_NOD_NM), MAX(EE.EV_DTL_DV_CD), MAX(EEQ.QTM_ORDN)
		/* 영어 진단평가 차시별 정답률 조회 - 김현혜 - AlEn-Mapper.xml - selectAlEvTcRptList */
	</select>
	
	<select id="selectIansQtmCnt" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="Integer">
		/* AI맞춤 쌓인 오답문항 개수 - 김현혜 - AlEn-Mapper.xml - selectIansQtmCnt */
		SELECT count(distinct EEQA.QTM_ID) FROM 
			LMS_LRM.EA_EV E
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
			ON E.EV_ID = EAETR.EV_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ 
			ON EEQ.EV_ID = E.EV_ID
			INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
			ON EEQA.QTM_ID = EEQ.QTM_ID 
			AND EEQA.EV_ID = EEQ.EV_ID 
			AND EEQA.USR_ID = #{usrId}
			WHERE 1=1 
			AND E.USR_ID = #{usrId}
			AND E.OPT_TXB_ID = #{optTxbId}
			AND EEQA.CANS_YN = 'N'
		/* AI맞춤 쌓인 오답문항 개수 - 김현혜 - AlEn-Mapper.xml - selectIansQtmCnt */
	</select>
	
	<select id="selectEvSeUg" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/* AI맞춤 단원진단평가여부 - 김현혜 - AlEn-Mapper.xml - selectEvSeUg */
		SELECT  EE.EV_ID, MAX(EE.EV_NM) AS EV_NM, 
		IFNULL(MAX(EER.EV_CMPL_YN), 'N') EV_CMPL_YN
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_TS_RNGE EETR
			ON EE.EV_ID = EETR.EV_ID
			LEFT JOIN LMS_LRM.EA_EV_RS EER
			ON EE.EV_ID = EER.EV_ID
			AND EER.USR_ID = #{usrId}
			INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN LRMP 
			ON LRMP.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID 
			AND LRMP.OPT_TXB_ID = #{optTxbId} 
			AND LRMP.DPTH = 1
			INNER JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN LKMAP ON LKMAP.LRMP_NOD_ID = LRMP.LRMP_NOD_ID
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN KMMP
			ON KMMP.KMMP_NOD_ID = LKMAP.KMMP_NOD_ID
			AND KMMP.DPTH =2 
			AND KMMP.KMMP_NOD_ID = LKMAP.KMMP_NOD_ID
			AND KMMP.OPT_TXB_ID = #{optTxbId}
			AND kmmp.KMMP_NOD_ID = #{mluKmmpNodId}
					where ee.EV_DV_CD = 'SE' 
					and ee.EV_DTL_DV_CD = 'UG'
					and ee.DEL_YN ='N'
					and ee.USE_YN = 'Y'
					and ee.EV_NM not like '%검수용%'
					and ee.OPT_TXB_ID = #{optTxbId} 
					GROUP BY EE.EV_ID
		/* AI맞춤 단원진단평가여부 - 김현혜 - AlEn-Mapper.xml - selectEvSeUg */
	</select>
	
	<select id="selectTcAvnList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT MAX(A.USR_ID) AS USR_ID
		, MAX(A.EV_ID) AS EV_ID
			 , MAX(A.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
			 , A.TC_KMMP_NOD_ID
			 , MAX(A.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM
			 , COUNT(A.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_CNT
		 	 , AVG( CASE 
		 	 		WHEN A.CANS_YN = 'Y' AND A.QTM_DFFD_DV_CD = '01' THEN 0.7
		 	 		WHEN A.CANS_YN = 'Y' AND A.QTM_DFFD_DV_CD = '02' THEN 0.85
		 	 		WHEN A.CANS_YN = 'Y' AND A.QTM_DFFD_DV_CD = '03' THEN 1.0
		 	 		WHEN A.CANS_YN = 'Y' AND A.QTM_DFFD_DV_CD = '04' THEN 1.15
		 	 		WHEN A.CANS_YN = 'Y' AND A.QTM_DFFD_DV_CD = '05' THEN 1.3
					ELSE 0 END) AS TC_AVN
			 , MAX(A.QTM_DFFD_DV_CD) AS QTM_DFFD_DV_CD
		 	 , MAX(A.CANS_YN) AS CANS_YN
			 , COUNT(A.TXM_PN) AS TXM_PN
			 , AVG(A.AI_PRED_AVG_CANS_RT) AS AI_PRED_AVG_CANS_RT
		     , AVG(A.AI_PRED_AVG_SCR) AS AI_PRED_AVG_SCR
		FROM 
		(
		SELECT
			   MAX(EER.USR_ID) AS USR_ID
			 , MAX(EE.EV_ID) AS EV_ID
			 , MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
			 , MAX(EAETR.TC_KMMP_NOD_ID) AS TC_KMMP_NOD_ID
			 , MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM
			 , MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
			 , MAX(EEQ.QTM_ID) AS QTM_ID
			 , MAX(QQ.QP_DFFD_CD) AS QTM_DFFD_DV_CD
			 , MAX(ANW.CANS_YN) AS CANS_YN
			 , IFNULL(MAX(AUQP.TXM_PN), 0) AS TXM_PN
			 , IFNULL(MAX(AUTP.AI_PRED_AVG_CANS_RT), 0) AS AI_PRED_AVG_CANS_RT
		     , IFNULL(MAX(AUTP.AI_PRED_AVG_SCR), 0.5) AS AI_PRED_AVG_SCR
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
		INNER JOIN LMS_CMS.QP_QTM QQ ON EEQ.QTM_ID = QQ.QP_QTM_ID
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW ANW ON EE.EV_ID = ANW.EV_ID AND EEQ.QTM_ID = ANW.QTM_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP ON EAETR.TPC_KMMP_NOD_ID = AUTP.TPC_ID AND EER.USR_ID = AUTP.USR_ID AND AUTP.OPT_TXB_ID = #{optTxbId}
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP ON EEQ.QTM_ID = AUQP.QTM_ID AND EER.USR_ID = AUQP.USR_ID AND AUQP.OPT_TXB_ID = #{optTxbId}
		WHERE EER.USR_ID = #{usrId}
		<if test = 'evDtlDvCd != null and evDtlDvCd.equals("OV")'> <!-- 진단평가 -->
			AND EE.EV_DTL_DV_CD = 'OV'
		</if>
		<if test = 'evDtlDvCd != null and !evDtlDvCd.equals("OV")'> <!-- 맞춤학습 -->
			AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
		</if>
		AND EAETR.OPT_TXB_ID = #{optTxbId}
		AND EAETR.MLU_KMMP_NOD_ID  = #{mluKmmpNodId}
		)A
		GROUP BY A.TC_KMMP_NOD_ID
	</select>
	
	<select id="selectEnLuLrnrVelTpCd" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT MAX(TS.MLU_KMMP_NOD_ID) as MLU_KMMP_NOD_ID
			 , TS.TC_KMMP_NOD_ID
			 , COUNT(AN.CANS_YN) AS UG_MM_CNT
	 		 , SUM(AN.CANS_YN = 'Y') AS UG_MM_Y_CNT
		FROM LMS_LRM.EA_EV EV
		INNER JOIN LMS_LRM.EA_EV_RS ER
		ON EV.EV_ID = ER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS
		ON EV.EV_ID = TS.EV_ID
		INNER JOIN LMS_LRM.EA_EV_QTM QT
		ON EV.EV_ID = QT.EV_ID
		AND TS.TPC_KMMP_NOD_ID = QT.TPC_ID
		INNER JOIN LMS_CMS.QP_QTM QQ 
		ON QT.QTM_ID = QQ.QP_QTM_ID
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW AN
		ON EV.EV_ID = AN.EV_ID
		AND QT.QTM_ID = AN.QTM_ID
		WHERE ER.USR_ID =  #{usrId}
		AND TS.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		AND EV.EV_DTL_DV_CD = 'OV'
		and ER.EV_CMPL_YN = 'Y'
		GROUP BY TS.TC_KMMP_NOD_ID
	</select>
	
	<select id="selectTpcList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT dpth5.KMMP_NOD_ID as TPC_KMMP_NOD_ID, dpth5.KMMP_NOD_NM as TPC_KMMP_NOD_NM
			 , IFNULL(AUTP.TPC_AVN, 0.5) AS TPC_AVN
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR 
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3 
		ON AKNR.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID 
		AND AKNR.OPT_TXB_ID = DPTH3.OPT_TXB_ID
		and DPTH3.DPTH = 3
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4 
		ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID 
		AND AKNR.OPT_TXB_ID = DPTH4.OPT_TXB_ID
		and DPTH4.DPTH = 4
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5 
		ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID 
		AND AKNR.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		and DPTH5.DPTH = 5
		INNER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP 
		ON AUTP.TPC_ID = DPTH5.KMMP_NOD_ID
		and AUTP.USR_ID =  #{usrId}
		WHERE AKNR.OPT_TXB_ID = #{optTxbId}
		AND AKNR.DPTH = 2
		AND AKNR.KMMP_NOD_ID = #{mluKmmpNodId}
		ORDER BY AUTP.TPC_AVN DESC
	</select>
	
	<select id="selectTcCmplYnList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AlLrnwEvInfoDto">
    	SELECT
			MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
    	  , MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM
		  , DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID
    	  , MAX(DPTH4.KMMP_NOD_NM) AS TC_KMMP_NOD_NM
		  , MAX(ALLV.USR_ID) AS USR_ID
		  , IFNULL(MAX(ALLV.LRNR_VEL_TP_CD), 'NM') AS LRNR_VEL_TP_CD
		  , MAX(IFNULL(EV.LUEV_CMPL_YN, 'N')) AS LUEV_CMPL_YN
		  , MAX(EV.EV_ID) AS EV_ID
    	  , MAX(EV.EV_DTL_DV_CD) AS EV_DTL_DV_CD
    	  , MAX(EV.EV_CMPL_YN) AS EV_CMPL_YN
		  , MAX(OV.TC_AVG_CANS_RT) AS TC_AVG_CANS_RT
		  , CASE WHEN MAX(OV.TC_AVG_CANS_RT) <![CDATA[>=]]> 90 THEN '심화'
			 	 WHEN MAX(OV.TC_AVG_CANS_RT) <![CDATA[<]]> 50 THEN '기초'
		 		 ELSE '기본' END TC_AVG_CANS_RT_STR
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALLV
					ON ALLV.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
					AND ALLV.USR_ID = #{usrId}
				LEFT OUTER JOIN (
					SELECT
					    EE.EV_ID
					  , EER.USR_ID
					  , EE.EV_DTL_DV_CD
					  , EER.EV_CMPL_YN
					  , EAETR.LUEV_CMPL_YN
					  , EE.OPT_TXB_ID
					  , EAETR.MLU_KMMP_NOD_ID
					  , EAETR.TC_KMMP_NOD_ID
					FROM
					    LMS_LRM.EA_EV EE
							INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
							    ON EE.EV_ID = EAETR.EV_ID
							INNER JOIN LMS_LRM.EA_EV_RS EER
							    ON EE.EV_ID = EER.EV_ID
					WHERE
					    EE.EV_DV_CD = 'AE'
						AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
						AND EE.OPT_TXB_ID = #{optTxbId}
						AND EER.USR_ID = #{usrId}
						AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
					GROUP BY
					    EE.EV_ID
					  , EER.USR_ID
					  , EE.EV_DTL_DV_CD
					  , EER.EV_CMPL_YN
					  , EAETR.LUEV_CMPL_YN
					  , EE.OPT_TXB_ID
					  , EAETR.MLU_KMMP_NOD_ID
					  , EAETR.TC_KMMP_NOD_ID
					ORDER BY
					    CAST(SUBSTRING(EE.EV_DTL_DV_CD, 2) AS UNSIGNED) DESC
				) EV
				    ON EV.OPT_TXB_ID = DPTH2.OPT_TXB_ID
					AND EV.USR_ID = ALLV.USR_ID
					AND EV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
					AND EV.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				LEFT OUTER JOIN (
					SELECT
					    EAETR.TC_KMMP_NOD_ID
					  , (SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) / COUNT(EEQ.QTM_ID)) * 100 AS TC_AVG_CANS_RT
					FROM
					    LMS_LRM.EA_EV EE
							INNER JOIN LMS_LRM.EA_EV_RS EER
							    ON EE.EV_ID = EER.EV_ID
							INNER JOIN LMS_LRM.EA_EV_QTM EEQ
							    ON EE.EV_ID = EEQ.EV_ID
							INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
							    ON EE.EV_ID = EEQA.EV_ID
							    AND EEQ.QTM_ID = EEQA.QTM_ID
							INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
							    ON EE.EV_ID = EAETR.EV_ID
							    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
					WHERE
					    EER.USR_ID = #{usrId}
						AND EE.EV_DV_CD = 'AE'
						AND EER.EV_CMPL_YN = 'Y'
						AND EE.EV_DTL_DV_CD = 'OV'
						AND EE.OPT_TXB_ID = #{optTxbId}
					GROUP BY
					    EAETR.TC_KMMP_NOD_ID
				) OV
				    ON OV.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
					AND BALAC.CTN_TP_CD = 'QU'
					AND BALAC.DEL_YN = 'N'
		WHERE
		    DPTH2.OPT_TXB_ID = #{optTxbId}
			AND DPTH2.DPTH = 2
			AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
			AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY
		    DPTH4.KMMP_NOD_ID
		ORDER BY
		    MAX(DPTH2.RCSTN_ORDN), MAX(DPTH2.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN)
    </select>
	
</mapper>