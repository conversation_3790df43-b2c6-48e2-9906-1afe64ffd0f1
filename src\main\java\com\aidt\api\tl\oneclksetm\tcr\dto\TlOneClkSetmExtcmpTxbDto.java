package com.aidt.api.tl.oneclksetm.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-11-12 16:23:14
 * @modify date 2024-11-12 16:23:14
 * @desc [TlOneClkSetmExtcmpTxbDto 원클릭학습설정 타사교과 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlOneClkSetmExtcmpTxbDto {
	
	/** 교과서ID */
    @Parameter(name="교과서ID")
    private String txbId;
    
    /** 출판사명 */
    @Parameter(name="출판사명")
    private String pblsNm;
    
    /** 출판사코드 */
    @Parameter(name="출판사코드")
    private String pblsCd;
    
    /** 출판사코드명 */
    @Parameter(name="출판사코드명")
    private String cmCdNm;
    
    /** 매핑교과ID */
    @Parameter(name="매핑교과ID")
    private String mpnTxbId;
    
    /** 원천교과여부 */
    @Parameter(name="원천교과여부")
    private String orgnYn;
    
    /** 교과서 대단원 리스트 */
    @Parameter(name="교과서 대단원 리스트")
    private List<TlOneClkSetmExtcmpTxbInfoDto> extcmbTxbInfoList;
}
