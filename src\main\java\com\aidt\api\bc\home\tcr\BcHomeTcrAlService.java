package com.aidt.api.bc.home.tcr;

import com.aidt.api.al.pl.cm.ma.AlMaService;
import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.dto.LearningMgDto;
import com.aidt.common.CommonDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class BcHomeTcrAlService {
    private final String MAPPER_NAMESPACE = "api.al.pl.tcr.learningMg.";
    @Autowired
    private CommonDao commonDao;

    @Autowired
    AiRcmTsshQtmCommService aiRcmTsshQtmCommService;

    @Autowired
    AlMaService alMaService;

    /**
     * (선생님) 학습관리 - 학습현황관리 - AI 맞춤학습 (영어)
     *
     * */
    @Cacheable(
            cacheNames = "shortCache",
            key="'bc:' + #dto.optTxbId + ':selectStuAeEvInfoList:' + #dto.claId + ':' + #dto.mluKmmpNodId",
            cacheManager = "aidtCacheManager")
    public Map<String, Object> selectStuAeEvInfoList(LearningMgDto dto){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<Map<String, Object>> stuEvInfoMapList = new ArrayList<>();

        //진단평가 응시 학생
        int ovTxmStuCnt = 0;
        //우리반평균_빠른
        int lrnrVelTpFsCnt = 0;
        //우리반평균_보통
        int lrnrVelTpNmCnt = 0;
        //우리반평균_느린
        int lrnrVelTpSlCnt = 0;

        //학생리스트
        List<LearningMgDto> selectClaStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectClaStuList", dto);
        //중단원리스트, 진단평가
        List<LearningMgDto> selectOvInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectOvInfo", dto);
        //차시별 맞춤학습
        List<LearningMgDto> stuAeEvInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuAeEvInfoList", dto);
        //차시별 사용자학습수준
        List<LearningMgDto> selectOptTxbIdToAiLrnrLv = commonDao.selectList(MAPPER_NAMESPACE + "selectOptTxbIdToAiLrnrLv", dto);

        for (LearningMgDto stu : selectClaStuList) {
            Map<String, Object> stuMap = new HashMap<String, Object>();
            stuMap.put("stuNo", stu.getStuNo());
            stuMap.put("usrId", stu.getUsrId());
            stuMap.put("usrNm", stu.getUsrNm());

            if(stu.getLrnrVelTpCd().equals("FS")) { lrnrVelTpFsCnt++; }
            if(stu.getLrnrVelTpCd().equals("NM")) { lrnrVelTpNmCnt++; }
            if(stu.getLrnrVelTpCd().equals("SL")) { lrnrVelTpSlCnt++; }


            for (LearningMgDto mlu : selectOvInfo) {
                //진단평가
                if(stu.getUsrId().equals(mlu.getUsrId())) {
                    stuMap.put("ovEvCmplYn", mlu.getOvEvCmplYn());
                    stuMap.put("ovQtmCnt", mlu.getQtmCnt());
                    stuMap.put("ovCansYCny", mlu.getCansYCny());
                    stuMap.put("ovEvTmScnt", mlu.getEvTmScnt());
                    stuMap.put("ovEvId", mlu.getEvId());
                    stuMap.put("ovLastLearnDay", mlu.getCrtDtm());
                    if(mlu.getOvEvCmplYn().equals("Y")) { ovTxmStuCnt++; }


                    //단원별 학습자수준(진단평가 맞힌 문항 개수 / 총개수 기준 - 2024.11.29)
                    String lrnrVelTpCd = "";
                    double calc = (double) mlu.getCansYCny() / mlu.getQtmCnt();
                    if(calc >= 0.9) {
                        lrnrVelTpCd = "FS";
                    } else if (calc >= 0.5 && calc < 0.9) {
                        lrnrVelTpCd = "NM";
                    } else {
                        lrnrVelTpCd = "SL";
                    }

                    log.debug("===usrId:{}, mlu.getCansYCny:{}, mlu.getQtmCnt:{}, calc:{}", mlu.getUsrId(), mlu.getCansYCny(), mlu.getQtmCnt(), calc);
                    stuMap.put("lrnrVelTpCd", lrnrVelTpCd);
                }

                for (LearningMgDto lrnr : selectOptTxbIdToAiLrnrLv) {
                    if(stu.getUsrId().equals(lrnr.getUsrId()) && mlu.getMluKmmpNodId().equals(lrnr.getMluKmmpNodId())) {
                        Map<String, Object> tcMap = new HashMap<String, Object>();
                        tcMap.put("tcKmmpNodId", lrnr.getTcKmmpNodId());
                        tcMap.put("lrnrVelTpCd", lrnr.getLrnrVelTpCd());
                        stuMap.put(lrnr.getTcKmmpNodNm(), tcMap);

                        for (LearningMgDto tc : stuAeEvInfoList) {
                            if(stu.getUsrId().equals(tc.getUsrId()) && lrnr.getTcKmmpNodNm().equals(tc.getTcKmmpNodNm())) {
                                tcMap = (Map<String, Object>) stuMap.get(tc.getTcKmmpNodNm());
                                tcMap.put("cvEvCmplYn", tc.getCvEvCmplYn());
                                tcMap.put("qtmCnt", tc.getQtmCnt());
                                tcMap.put("cansYCny", tc.getCansYCny());
                                tcMap.put("evTmScnt", tc.getEvTmScnt());
                                tcMap.put("crtDtm", tc.getCrtDtm());
                                stuMap.put(tc.getTcKmmpNodNm(), tcMap);
                            }
                        }
                    }
                }

            }
            stuEvInfoMapList.add(stuMap);
        }
        resultMap.put("stuEvInfoMapList", stuEvInfoMapList);

        int claStuListCnt = selectClaStuList.isEmpty() ? 0 : selectClaStuList.size();
        resultMap.put("ovNTxmStuCnt", claStuListCnt - ovTxmStuCnt);
        resultMap.put("lrnrVelTpFsCnt", lrnrVelTpFsCnt);
        resultMap.put("lrnrVelTpNmCnt", lrnrVelTpNmCnt);
        resultMap.put("lrnrVelTpSlCnt", lrnrVelTpSlCnt);

        return resultMap;
    }

}
