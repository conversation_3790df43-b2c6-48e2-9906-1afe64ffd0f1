package com.aidt.api.ea.grpmgmt.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email 
 * @create date 2024-04-26 10:53:20
 * @modify date 2024-04-26 10:53:20
 * @desc EaGrpMgGrpDto 모둠관리 모둠 팀 정보 Dto
 */

 @Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtStuListDto {
	    /** 사용자명 */
	 @Parameter(name = "사용자명")
	    private String usrNm;

	    /** 학생사용자ID */
	    @Parameter(name = "학생사용자ID")
	    @NotBlank(message = "{field.required}")
	    private String usrId;

	    /**  순번*/
	    @Parameter(name = "순번")
	    private int stuNo;

	    /**  성별코드*/
	    @Parameter(name = "성별")
	    private String usrGndCd;

	    /**  성별 */
	    @Parameter(name = "성별")
	    private String usrGndNm;

	    // FS	LRNR_VEL_TP_CD	빠른학습자
	    // NM	LRNR_VEL_TP_CD	보통학습자
	    // SL	LRNR_VEL_TP_CD	느린학습자
	    /**  학습 수준(느린/보통/빠른)*/
	    @Parameter(name = "학습자속도유형코드")
	    private String lrnrVelTpCd;

	    // FS	LRNR_VEL_TP_CD	빠른학습자
	    // NM	LRNR_VEL_TP_CD	보통학습자
	    // SL	LRNR_VEL_TP_CD	느린학습자
	    /**  학습 수준(느린/보통/빠른)*/
	    @Parameter(name = "학습자속도유형명")
	    private String lrnrVelTpNm;
}