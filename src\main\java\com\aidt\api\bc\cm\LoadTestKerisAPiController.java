package com.aidt.api.bc.cm;

import com.aidt.api.bc.cm.constants.BcCmKerisApiEnum;
import com.aidt.api.bc.cm.dto.*;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Tag(name = "[bc] 공통[BcCmKeris]", description = "공통")
@RestController
@RequestMapping("/api/v1/bc/cm/keris")
public class LoadTestKerisAPiController {

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	private final KerisWebClientUtil KerisWebClientUtil = new KerisWebClientUtil();

	private final JwtProvider jwtProvider;
	private final BcCmService bcCmService;

	private final String SUCCESS_CODE = "00000";

	public LoadTestKerisAPiController(BcCmService bcCmService, JwtProvider jwtProvider) {
		this.bcCmService = bcCmService;
		this.jwtProvider = jwtProvider;
	}

	/**
	 * API 1회 호출
	 * @param cmKerisAPiDto
	 * @return
	 */
	@PostMapping(value = "/callOnceSelectKerisUserInfo")
	public ResponseDto<Map<String, Object>> callOnceSelectKerisUserInfo(@RequestBody BcCmKerisAPiDto cmKerisAPiDto) {

		String userType = cmKerisAPiDto.getUser_type();
		Map<String, Object> responseMap = new HashMap<>();
		try {
			if ("T".equals(userType)) {
				responseMap = this.selectTeacher(cmKerisAPiDto);
				return Response.ok(responseMap);
			} else if ("S".equals(userType)) {
				responseMap = this.selectStudent(cmKerisAPiDto);
				return Response.ok(responseMap);
			}

		} catch (Exception e) {
			e.printStackTrace();
			responseMap.put("controller error", e.getMessage());
		}

		return Response.ok(responseMap);
	}

	private Map<String, Object> selectStudent(BcCmKerisAPiDto cmKerisAPiDto) {

		Map<String, Object> responseMap = new HashMap<>();

		CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();

		try {
			Map<String, Object> paramMap = this.makeParamMap(cmKerisAPiDto);
			String partnerId = cmKerisAPiDto.getPartnerId();
			String apiUrl = cmKerisAPiDto.getApiUrl();

			BcCmKerisBodyDto stuAll = KerisWebClientUtil.executePostJsonRequest(paramMap,
					apiUrl + BcCmKerisApiEnum.STU_ALL.getUrl(), partnerId);
			BcUsrInfoDto bcUsrInfoDto = this.makeLectureTcrUserInfo(cmKerisAPiDto, stuAll.getSchool_id());
			if (bcUsrInfoDto != null) {
				bcCmService.checkLectureTcrUserInfo(bcUsrInfoDto);
			}

			BcLoginDto loginInfo = BcLoginDto.builder().usrId(userDetail.getUsrId()).optTxbId(userDetail.getOptTxbId())
					.claId(userDetail.getClaId()).subDomain(DB_ID).build();

			BcUsrInfoDto studentUser = bcCmService.selectStuUser(loginInfo);
			Map<String, Object> newParam = new HashMap<>(paramMap);
			newParam.put("user_id", studentUser.getChgTcrUsrId());

			responseMap.put("stuAll", stuAll);

			return responseMap;
		} catch (Exception e) {
			e.printStackTrace();
			responseMap.put("error", e.getMessage());
		}

		return responseMap;
	}

	private Map<String, Object> selectTeacher(BcCmKerisAPiDto cmKerisAPiDto) {
		Map<String, Object> responseMap = new HashMap<>();
		try {
			Map<String, Object> paramMap = this.makeParamMap(cmKerisAPiDto);
			String partnerId = cmKerisAPiDto.getPartnerId();
			String apiUrl = cmKerisAPiDto.getApiUrl();

			BcCmKerisBodyDto teacherAll = KerisWebClientUtil.executePostJsonRequest(paramMap,
					apiUrl + BcCmKerisApiEnum.TCR_ALL.getUrl(), partnerId);

			responseMap.put("teacherAll", teacherAll);

			return responseMap;

		} catch (Exception e) {
			e.printStackTrace();
			responseMap.put("error", e.getMessage());
		}

		return responseMap;
	}

	private Map<String, Object> makeParamMap(BcCmKerisAPiDto cmKerisAPiDto) {

		Map<String, String> token = new HashMap<>();
		token.put("token", cmKerisAPiDto.getToken());
		token.put("access_id", cmKerisAPiDto.getAccess_id());

		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("access_token", token);

		if (cmKerisAPiDto.getUser_ids() != null && !cmKerisAPiDto.getUser_ids().isEmpty()) {
			paramMap.put("user_ids", cmKerisAPiDto.getUser_ids());
		} else {
			paramMap.put("user_id", cmKerisAPiDto.getUser_id());
		}

		if (cmKerisAPiDto.getClass_code() != null && !cmKerisAPiDto.getClass_code().isEmpty()) {
			paramMap.put("class_code", cmKerisAPiDto.getClass_code());
		}
		if (cmKerisAPiDto.getLecture_code() != null && !cmKerisAPiDto.getLecture_code().isEmpty()) {
			paramMap.put("lecture_code", cmKerisAPiDto.getLecture_code());
		}

		return paramMap;
	}

	private BcUsrInfoDto makeLectureTcrUserInfo(BcCmKerisAPiDto cmKerisAPiDto, String schoolId) {
		return BcUsrInfoDto.builder().lectureCd(cmKerisAPiDto.getLecture_code()).usrId(cmKerisAPiDto.getUser_id())
				.userType(cmKerisAPiDto.getUser_type()).schlCd(schoolId).classCode(cmKerisAPiDto.getClass_code())
				.build();
	}

	/**
	 * bcCmService.checkLectureTcrUserInfo 호출
	 * @param bcUsrInfoDto
	 * @return
	 */
	@PostMapping(value = "/testCallCheckLectureTcrUserInfo")
	public ResponseEntity<?> testCallCheckLectureTcrUserInfo(@RequestBody BcUsrInfoDto bcUsrInfoDto) {

		bcCmService.checkLectureTcrUserInfo(bcUsrInfoDto);

		return ResponseEntity.status(HttpStatus.OK).body("success");
	}

	/**
	 * 교사 KERIS ID 가져오기 API
	 * @return ResponseEntity<String>
	 */
	@Operation(summary = "학생의 담당교사 ID 가져오기", description = "JWT 데이터")
	@GetMapping("/testGetTcrUserId")
	public ResponseEntity<String> testGetTcrUserId() {
		try {
			CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();

			BcLoginDto loginInfo = BcLoginDto.builder()
					.usrId(userDetail.getUsrId())
					.optTxbId(userDetail.getOptTxbId())
					.claId(userDetail.getClaId())
					.subDomain(DB_ID)
					.build();

			BcUsrInfoDto studentUser = bcCmService.selectStuUser(loginInfo);

			if (studentUser == null || studentUser.getTcrKerisUsrId() == null || studentUser.getTcrKerisUsrId().isEmpty()) {
				return ResponseEntity.status(HttpStatus.NO_CONTENT).body("교사 ID가 존재하지 않습니다.");
			}

			return ResponseEntity.ok(studentUser.getTcrKerisUsrId());

		} catch (Exception e) {
			log.error("getTcrUserId API 호출 중 오류 발생", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("서버 오류가 발생했습니다.");
		}
	}

}
