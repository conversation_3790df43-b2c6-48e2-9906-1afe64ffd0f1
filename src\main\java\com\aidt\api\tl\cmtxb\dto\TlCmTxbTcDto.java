/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-14 10:14:58
 * @modify date 2024-02-14 10:14:58
 * @desc [TlCmTxbT 교과학습 차시 dto]
 */
package com.aidt.api.tl.cmtxb.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbTcDto {
        /** 중단원ID*/
        @Parameter(name="중단원ID")
        private String lrmpNodId2;
        
        /** 중단원명*/
        @Parameter(name="중단원명")
        private String lrmpNodNm2;
        
        /** 소단원ID*/
        @Parameter(name="소단원ID")
        private String lrmpNodId3;
        
        /** 소단원명*/
        @Parameter(name="소단원명")
        private String lrmpNodNm3;
        
        /** 차시ID*/
        @Parameter(name="차시ID")
        private String lrmpNodId4;
        
        /** 차시명*/
        @Parameter(name="차시명")
        private String lrmpNodNm4;
        
        /** 활동ID*/
        @Parameter(name="활동ID")
        private String lrnAtvId;
        
        /** 평가ID*/
        @Parameter(name="평가ID")
        private String evId;

        /** 평가상세구분코드*/
        @Parameter(name="평가상세구분코드")
        private String evDtlDvCd;

        /** 평가사용여부*/
        @Parameter(name="평가사용여부")
        private String evUseYn;   
        
        /** 평가잠금여부*/
        @Parameter(name="평가잠금여부")
        private String evLcknYn;   
        
        /** 평가완료여부*/
        @Parameter(name="평가완료여부")
        private String evCmplYn;     
        
        /** 평가지유형코드*/
        @Parameter(name="평가지유형코드")
        private String evshTpCd;  
        
        /** 썸네일 PC PATH*/
        @Parameter(name="썸네일 PC PATH")
        private String pcThbPth; 
        
        /** 썸네일 TA PATH*/
        @Parameter(name="썸네일 TA PATH")
        private String taThbPth; 

        /** 재구성 순서 */
        @Parameter(name="재구성 순서")
        private int rcstnOrdn;
        
        /** 재구성 번호 */
        @Parameter(name="재구성 번호")
        private int rcstnNo;

        /** 잠금여부 */
        @Parameter(name="잠금여부")
        private String lcknYn;

        /** 사용여부 */
        @Parameter(name="사용여부")
        private String useYn;
        
        /** 학습상태 */
        @Parameter(name="학습상태")
        private String lrnSt;
        
        /** 학습가능여부 */
        @Parameter(name="학습가능여부")
        private String lrnAbleYn;

        /** 단원번호사용여부 */
        @Parameter(name="단원번호사용여부")
        private String luNoUseYn;

        /** 단원노출여부 */
        @Parameter(name="단원노출여부")
        private String luEpsYn;

        /** 차시개념학습완료여부 */
        @Parameter(name="차시개념학습완료여부")
        private String lrnCmplYn;

}
