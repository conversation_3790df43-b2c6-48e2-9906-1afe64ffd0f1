package com.aidt.api.al.cmt.dto.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-31 13:01:01
 * @modify date 2024-07-31 13:01:01
 * @desc
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtUsrDataReqDto {

    private String evId;

    private String usrId;

    private String usrTpCd;

    private String aiCmtNo;

    private String cmtCn;

    private String cmtType;

}
