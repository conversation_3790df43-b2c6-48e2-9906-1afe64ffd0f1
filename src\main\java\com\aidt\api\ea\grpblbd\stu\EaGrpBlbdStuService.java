package com.aidt.api.ea.grpblbd.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.api.ea.asncom.dto.EaAsnFleDto;
import com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto;
import com.aidt.api.ea.grpblbd.stu.dto.EaUserSessionDto;
import com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-23
 * @modify date 2024-01-23
 * @desc 모둠 게시판 - 학생 Service
 */
@Service
public class EaGrpBlbdStuService {

	private final String MAPPER_NAMESPACE = "api.ea.grpblbd.stu.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private JwtProvider jwtProvider;
	
	@Autowired
	private EaAsnComService eaAsnComService;

	// Return Object
	List<EaGrpBlbdStuDto> responseList = new ArrayList<>();



	/**
	 * 모둠 게시판 List 조회
	 * @param eaGrpBlbdStuDto
	 * @return HashMap
	 */
	public Map<String, Object> selectGrpBlbdList(EaGrpBlbdStuDto eaGrpBlbdStuDto) {
		Map<String, Object> resMap = new HashMap<>();
		int blbdId = 0;

		// Set Session
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaGrpBlbdStuDto.setUsrId(userDetails.getUsrId());
		eaGrpBlbdStuDto.setSessionOptTxbId(userDetails.getOptTxbId());
		
		// 모둠 구성원 조회
		List<EaGrpBlbdStuDto> grpDetail = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpDetail" , eaGrpBlbdStuDto);

		// 모둠 게시판 List 조회
		List<EaGrpBlbdStuDto> grpBlbdList = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpBlbdList" , eaGrpBlbdStuDto);

		// 게시판 ID 조회
		blbdId = commonDao.select(MAPPER_NAMESPACE + "selectBlbdId" , eaGrpBlbdStuDto);

		if(grpDetail.size() > 0) {
			resMap.put("msg", "SUCCESS");
			resMap.put("grpDetail", grpDetail);
			resMap.put("grpBlbdList", grpBlbdList);
			resMap.put("blbdId", blbdId);
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 * 모둠 게시판 상세, 댓글 조회
	 * @param eaGrpBlbdStuDto
	 * @return HashMap
	 */
	public Map<String, Object> selectBlbdDetail(EaGrpBlbdStuDto eaGrpBlbdStuDto) {
		Map<String, Object> resMap = new HashMap<>();

		// Set Session
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaGrpBlbdStuDto.setUsrId(userDetails.getUsrId());
		eaGrpBlbdStuDto.setSessionOptTxbId(userDetails.getOptTxbId());
		

		// 모둠 게시판 상세 조회
		List<EaGrpBlbdStuDto> blbdDetail = commonDao.selectList(MAPPER_NAMESPACE + "selectBlbdDetail" , eaGrpBlbdStuDto);

		if(blbdDetail.size() > 0 && blbdDetail.get(0).getAnnxId() > 0) {
			String annxId = String.valueOf(blbdDetail.get(0).getAnnxId());
    		if(!StringUtils.isEmpty(annxId)) {
            	
            	List<EaAsnFleDto> fleList  = eaAsnComService.selectFile(annxId);

            	resMap.put("fileList", fleList);
        	}
		}

		// 모둠 게시판 댓글 조회
		List<EaGrpBlbdStuDto> blbdDetailComment = commonDao.selectList(MAPPER_NAMESPACE + "selectBlbdDetailComment" , eaGrpBlbdStuDto);

		if(blbdDetail.size() > 0) {
			resMap.put("msg", "SUCCESS");
			resMap.put("blbdDetail", blbdDetail);
			resMap.put("blbdDetailComment", blbdDetailComment);
			resMap.put("userInfo", this.setSessionInfo());
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 * 모둠 게시판 글 등록, 수정, 삭제
	 * @param eaGrpBlbdStuDto
	 * @return HashMap
	 */
	public Map<String, Object> saveBlbd(EaGrpBlbdStuDto eaGrpBlbdStuDto) {
		Map<String, Object> resMap = new HashMap<>();
		int check = 0;

		// Set Session
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaGrpBlbdStuDto.setUsrId(userDetails.getUsrId());
		eaGrpBlbdStuDto.setSessionOptTxbId(userDetails.getOptTxbId());

		// 모둠 게시판 글 등록
		if(eaGrpBlbdStuDto.getFlag().equals("insert")) {
			check  = commonDao.insert(MAPPER_NAMESPACE + "insertBlbd" , eaGrpBlbdStuDto);
		}
		// 모둠 게시판 글 수정
		else if(eaGrpBlbdStuDto.getFlag().equals("update")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "updateBlbd" , eaGrpBlbdStuDto);
		}
		// 모둠 게시판 글 삭제
		else if(eaGrpBlbdStuDto.getFlag().equals("delete")) {
			//2025.02.25 삭제 여부에 따라 삭져여부 변경에서  - > 데이터 삭제 처리로 수정
			check = commonDao.update(MAPPER_NAMESPACE + "deleteBlbd" , eaGrpBlbdStuDto);
			// 모둠 게시판 댓글 삭제
			check = commonDao.update(MAPPER_NAMESPACE + "deleteComment" , eaGrpBlbdStuDto);
		}

		if(check > 0) {
			resMap.put("msg", "SUCCESS");
		} else {
			resMap.put("msg", "FAIL");
		}


		return resMap;
	}

	/**
	 * 모둠 게시판 댓글 등록, 수정, 삭제
	 * @param eaGrpBlbdStuDto
	 * @return HashMap
	 */
	public Map<String, Object> saveComment(EaGrpBlbdStuDto eaGrpBlbdStuDto) {
		Map<String, Object> resMap = new HashMap<>();
		int check = 0;

		// Set Session
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaGrpBlbdStuDto.setUsrId(userDetails.getUsrId());
		eaGrpBlbdStuDto.setDbId(userDetails.getTxbId());

		// 댓글 등록
		if(eaGrpBlbdStuDto.getFlag().equals("insert")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "insertComment" , eaGrpBlbdStuDto);
		}
		// 댓글 수정
		else if(eaGrpBlbdStuDto.getFlag().equals("update")) {
			check = commonDao.update(MAPPER_NAMESPACE + "updateComment" , eaGrpBlbdStuDto);
		}
		// 댓글 삭제
		else if(eaGrpBlbdStuDto.getFlag().equals("delete")) {
			check = commonDao.update(MAPPER_NAMESPACE + "deleteComment" , eaGrpBlbdStuDto);
		}

		if(check > 0) {
			resMap.put("msg", "SUCCESS");
		} else {
			resMap.put("msg", "FAIL");
		}
		

		return resMap;
	}

	/**
	 * 모둠 게시판 답글 등록, 수정, 삭제
	 * @param eaGrpBlbdStuDto
	 * @return HashMap
	 */
	public Map<String, Object> saveReply(EaGrpBlbdStuDto eaGrpBlbdStuDto) {
		Map<String, Object> resMap = new HashMap<>();
		int check = 0;

		// Set Session
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaGrpBlbdStuDto.setUsrId(userDetails.getUsrId());
		eaGrpBlbdStuDto.setSessionOptTxbId(userDetails.getOptTxbId());

		// 답글 등록
		if(eaGrpBlbdStuDto.getFlag().equals("insert")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "insertReply" , eaGrpBlbdStuDto);
		}
		// 답글 수정
		else if(eaGrpBlbdStuDto.getFlag().equals("update")) {
			// 수정 쿼리
			check = commonDao.update(MAPPER_NAMESPACE + "updateComment" , eaGrpBlbdStuDto);
		}
		// 답글 삭제
		else if(eaGrpBlbdStuDto.getFlag().equals("delete")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "deleteReply" , eaGrpBlbdStuDto);
		}

		if(check > 0) {
			resMap.put("msg", "SUCCESS");
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}


	public List<EaAsnFleDto> selectFile(EaGrpBlbdStuDto eaGrpBlbdStuDto) {
		List<EaAsnFleDto> fileList = new ArrayList<>();
		
		String annxId = String.valueOf(eaGrpBlbdStuDto.getAnnxId());
		if(!StringUtils.isEmpty(annxId)) {
			fileList  = eaAsnComService.selectFile(annxId);
    	}

		return fileList;
	}

	/**
	 * Set Session User Information
	 * @param
	 * @return  List<EaGrpBlbdStuDto>
	 */
	private List<EaUserSessionDto> setSessionInfo() {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		EaUserSessionDto eaUserSessionDto = new EaUserSessionDto();

		eaUserSessionDto.setSessionClaId(userDetails.getClaId());
		eaUserSessionDto.setSessionOptTxbId(userDetails.getOptTxbId());
		eaUserSessionDto.setSessionRole(userDetails.getRole());
		eaUserSessionDto.setSessionTxbId(userDetails.getTxbId());
		eaUserSessionDto.setSessionUsrId(userDetails.getUsrId());
		eaUserSessionDto.setSessionUsrNm(userDetails.getUsrNm());
		eaUserSessionDto.setSessionUsrTpCd(userDetails.getUsrTpCd());

		List<EaUserSessionDto> userInfo = new ArrayList<>();
		userInfo.add(eaUserSessionDto);

		return userInfo;
	}
}
