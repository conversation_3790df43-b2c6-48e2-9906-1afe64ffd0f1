package com.aidt.api.bc.inf.stu;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.inf.dto.BcInfDto;
import com.aidt.api.bc.inf.tcr.BcInfTcrService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:20
 * @modify 2024-01-05 17:52:20
 * @desc 알림 Controller
 */

@Slf4j
@Tag(name="[bc] 알림[BcInfStu]", description="알림(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/inf")
public class BcInfStuController {

	@Autowired
	private JwtProvider jwtProvider;
	
    @Autowired
    private BcInfStuService bcInfStuService;
    
    @Autowired
    private BcInfTcrService bcInfTcrService;

    /**
     * 알림 목록 조회 요청
     *
     * @param BcInfDto
     * @return ResponseList<BcInfDto>
     */
    @Operation(summary="알림 목록 조회", description="알림 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectInfList")
    public ResponseDto<List<BcInfDto>> selectInfList(BcInfDto bcInfDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcInfDto.getUsrId())) {
    		bcInfDto.setUsrId(userDetails.getUsrId());
    	}
    	
        return Response.ok(bcInfStuService.selectInfList(bcInfDto));
    }
    
    /**
     * 알림 삭제 요청
     *
     * @param BcInfDto
     * @return ResponseList<Integer>
     */
    @Operation(summary="알림 삭제", description="알림을 삭제한다.(학생)")
    @DeleteMapping(value = "/deleteInfList")
    public ResponseDto<Integer> deleteInfList(@RequestBody BcInfDto bcInfDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcInfDto.getUsrId())) {
    		bcInfDto.setUsrId(userDetails.getUsrId());
    	}
    	bcInfDto.setDbId(userDetails.getTxbId());
    	
        return Response.ok(bcInfStuService.deleteInfList(bcInfDto));
    }
    
    
    /**
     * 알림 등록 요청
     *
     * @param BcInfDto
     * @return ResponseList<Integer>
     */
    @Operation(summary="알림 등록", description="알림을 등록한다.(학생)")
    @PostMapping(value = "/insertInfList")
    public ResponseDto<Integer> insertInfList(@RequestBody BcInfDto bcInfDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcInfDto.getUsrId())) {
    		bcInfDto.setUsrId(userDetails.getUsrId());
    	}
    	if(StringUtils.isEmpty(bcInfDto.getTcrUsrId())) {
    		bcInfDto.setTcrUsrId(userDetails.getUsrId());
    	}
    	if(StringUtils.isEmpty(bcInfDto.getOptTxbId())) {
    		bcInfDto.setOptTxbId(userDetails.getOptTxbId());
    	}
    	bcInfDto.setDbId(userDetails.getTxbId());
    	
        return Response.ok(bcInfTcrService.insertInfList(bcInfDto));
    }
}
