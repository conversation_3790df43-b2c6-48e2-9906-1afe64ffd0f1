package com.aidt.api.bc.cm.file.dto;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class BcFileReqDto {

	@NotEmpty(message = "첨부파일은 하나 이상 등록하여야 합니다.")
	@Schema(description = "첨부파일")
	private List<MultipartFile> files;

	@NotNull(message = "버킷명은 필수 입력 값입니다.")
	@Schema(description = "버킷명")
	private String taskName;

	@Schema(description = "파일 그룹 ID")
	private Long annxId;

}
