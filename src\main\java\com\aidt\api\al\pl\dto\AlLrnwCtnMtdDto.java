package com.aidt.api.al.pl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 학습창 연계
 * 학습활동 콘텐츠메타데이터
 * */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlLrnwCtnMtdDto {
	
	@Parameter(name="콘텐츠유형코드")
	private String ctnTpCd;
	
	@Parameter(name="콘텐츠ID")
	private String ctnId;
	
	@Parameter(name="학습인정시간")
	private String lrnCofmTmScnt;
	
	@Parameter(name="난이도")
	private String ctnDffdDvCd;
	
	@Parameter(name="완료체크기준코드")
	private String cmplBsCd;
	
	@Parameter(name="학습유형")
	private String lrnTpCd;
	
	@Parameter(name="액티비티총문항수")
	private String atvQtmCnt;
	
	@Parameter(name="OCR사용여부")
	private String ocrUseYn;
	
	@Parameter(name="수식사용여부")
	private String fmlUseYn;
	
	@Parameter(name="저화질")
	private String vdsLRsln;
	
	@Parameter(name="기본화질")
	private String vdsMRsln;
	
	@Parameter(name="고화질")
	private String vdsHRsln;
	
	@Parameter(name="자막SMI파일명")
	private String sttlSmiFleNm;
	
	@Parameter(name="자막VTT파일명")
	private String sttlVttFleNm;
	
	@Parameter(name="대본파일명")
	private String scrbFleNm;
	
	@Parameter(name="음성파일명")
	private String vceFleNm;
	
	@Parameter(name="썸네일파일명")
	private String thbFleNm;
	
	//HTML추가
	private String metaDataId;
	private String ctnCd;
	

}
