<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.sl.inidat.tcr">
    <!-- 
        담당운영교과서ID취득
     -->
    <select id="selectOptTxbId" parameterType="com.aidt.api.sl.inidat.dto.SlIniDatCondDto" resultType="Map">
        SELECT
               A.OPT_TXB_ID    /* 운영교과서ID */
              ,A.TXB_ID        /* 교과서ID */
              ,SUM(IF(D.OPT_TXB_ID IS NULL, 0, 1)) AS CNT   /* 재구성데이터건수 */
        FROM LMS_LRM.CM_OPT_TXB A /* CM운영교과서 */
             INNER JOIN LMS_CMS.BC_SP_LRN_TXB_MPN C /* BC_특별학습교과서매핑 */
                   ON A.TXB_ID = C.TXB_ID
                   AND C.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN D /* SL_특별학습재구성 */
                  ON A.OPT_TXB_ID = D.OPT_TXB_ID
                  AND C.SP_LRN_ID = D.SP_LRN_ID
        WHERE A.OPT_TXB_ID = #{optTxbId}
        GROUP BY A.OPT_TXB_ID, A.TXB_ID
    
        /** 특별학습 강성희 SlIniDatTcr-Mapper.xml - selectOptTxbId */
    </select>

    <!-- SL_특별학습재구성 데이터생성  -->
    <insert id="insertSlSpLrnRcstn" parameterType="Map">
        INSERT INTO LMS_LRM.SL_SP_LRN_RCSTN  /* SL_특별학습재구성 */
        (OPT_TXB_ID     /* 운영교과서ID */
        ,SP_LRN_ID      /* 특별학습ID */
        ,USE_YN         /* 사용여부 */
        ,SP_LRN_NM      /* 특별학습명 */
        ,SP_LRN_DFFD    /* 특별학습난이도 */
        ,LRN_GOAL_CN    /* 학습목표내용 */
        ,ORGL_ORDN      /* 원본순서 */
        ,RCSTN_ORDN     /* 재구성순서 */
        ,LRNW_USE_YN    /* 학습창사용여부 */
        ,CRTR_ID        /* 생성자ID */
        ,CRT_DTM        /* 생성일시 */
        ,MDFR_ID        /* 수정자ID */
        ,MDF_DTM        /* 수정일시 */
        ,DB_ID          /* 데이터베이스ID */
        )
        SELECT 
               M.OPT_TXB_ID
              ,M.SP_LRN_ID
              ,'Y'                  AS USE_YN
              ,M.SP_LRN_NM
              ,M.SP_LRN_DFFD
              ,M.LRN_GOAL_CN
              ,(@RNUM := @RNUM + 1) AS ORGL_ORDN
              ,(@RNUM)              AS RCSTN_ORDN
              ,M.LRNW_USE_YN
              ,'System'
              ,NOW()                AS CRT_DTM
              ,'System'
              ,NOW()                AS MDF_DTM
              ,#{dbId}              AS DB_ID
        FROM (
              SELECT  
                     A.OPT_TXB_ID    AS OPT_TXB_ID
                    ,C.SP_LRN_ID                /* 특별학습ID */
                    ,C.SP_LRN_NM                /* 특별학습명 */
                    ,C.SP_LRN_DFFD              /* 특별학습난이도 */
                    ,B.LRN_GOAL_CN              /* 학습목표내용 */
                  --  ,C.SRT_ORDN      AS ORGL_ORDN /* 정렬순서 - 재구성순서(초기값) */
                  --  ,C.SRT_ORDN      AS RCSTN_ORDN
                    ,C.LRNW_USE_YN
              FROM LMS_LRM.CM_OPT_TXB A /* CM_운영교과서 */
                  INNER JOIN LMS_CMS.BC_SP_LRN_TXB_MPN B /* BC_특별학습교과서매핑 */
                          ON A.TXB_ID = B.TXB_ID 
                         AND B.USE_YN = 'Y'
                  INNER JOIN LMS_CMS.BC_SP_LRN C /* BC_특별학습 */
                          ON B.SP_LRN_ID = C.SP_LRN_ID 
                         AND C.USE_YN = 'Y' 
                         AND C.DEL_YN = 'N'
              WHERE A.OPT_TXB_ID = #{tgtOptTxbId}
              ORDER BY B.CRT_DTM ASC, B.SP_LRN_ID
            ) M
            JOIN (SELECT @RNUM :=0) R 

        /** 특별학습 강성희 SlIniDatTcr-Mapper.xml - insertSlSpLrnRcstn */
    </insert>

</mapper>