<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.lansnte.stu">

    <select id="selectLansNteTsPaperList" parameterType="com.aidt.api.ea.evcom.lansnte.dto.EaLansNteReqDto"
            resultType="com.aidt.api.ea.evcom.lansnte.dto.EaLansNteResDto">

	SELECT
            T.EV_ID
          , COUNT(1) OVER() AS totalCnt
          , T.EV_NM
	      , T.EV_DV_CD
          , CASE WHEN T.EV_DV_CD = 'SE' THEN '나의 평가'
            	 WHEN T.EV_DV_CD = 'TE' THEN '나의 평가'
                 WHEN T.EV_DV_CD = 'DE' THEN
                 	  CASE WHEN T.EV_DTL_DV_CD = 'IS' THEN '오답 유사' ELSE '내가 만든 평가' END
                 WHEN T.EV_DV_CD = 'AE' THEN 'AI 맞춤 학습'
                 ELSE ''
            	 END AS EV_DV_NM -- 구분(교과 평가, DIY 평가)
	      , T.EV_DTL_DV_CD
          , T.QST_CNT
          , T.CA_QST_CNT
          , IFNULL(
	          	CASE WHEN EV_DTL_DV_CD IN ('ST', 'ET') THEN ''
	                 WHEN T.LU_CNT > 1 THEN
	                 			CONCAT(CASE WHEN IFNULL(NOD_LU.RCSTN_NO, '') = '' THEN NOD_LU.LRMP_NOD_NM
	                 						ELSE CONCAT(NOD_LU.RCSTN_NO, '. ', NOD_LU.LRMP_NOD_NM)
	                 						END
	                 			, ' 외 ', CAST(T.LU_CNT-1 AS CHAR ),'개')
	                 WHEN T.LU_CNT = 1 THEN
	                 		CASE WHEN IFNULL(NOD_LU.RCSTN_NO, '') = '' THEN NOD_LU.LRMP_NOD_NM ELSE CONCAT(NOD_LU.RCSTN_NO, '. ', NOD_LU.LRMP_NOD_NM) END
	            	 END
            	, '') AS LU_LRMP_NOD_NM
          , IFNULL(
	            CASE WHEN EV_DTL_DV_CD IN ('ST', 'ET', 'UD', 'UG') THEN ''
	                 ELSE CASE WHEN IFNULL(NOD_TC.RCSTN_NO, '') = '' THEN NOD_TC.LRMP_NOD_NM ELSE CONCAT(NOD_TC.RCSTN_NO, '. ', NOD_TC.LRMP_NOD_NM) END
	            	 END
            	, '') AS TC_LRMP_NOD_NM
          , DATE_FORMAT(T.SMT_DTM, '%m. %d.') AS SMT_DTM
	FROM (
        	SELECT
                    EE.EV_ID
                  , MAX(EE.EV_DV_CD) EV_DV_CD
                  , MAX(EE.EV_DTL_DV_CD) EV_DTL_DV_CD
                  , MAX(EE.EV_NM) EV_NM
                  , MAX(EER.SMT_DTM) SMT_DTM
                  , (SELECT COUNT(EV_ID) FROM LMS_LRM.EA_EV_TS_RNGE WHERE EV_ID = EE.EV_ID) AS LU_CNT
                  , SUM(CASE WHEN IFNULL(EEQA.IANS_NTE_CANS_YN, 'N') = 'N' THEN 1 ELSE 0 END) AS QST_CNT
                  , SUM(CASE WHEN EEQA.IANS_NTE_CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CA_QST_CNT
              FROM LMS_LRM.EA_EV EE
              JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
              JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQA.USR_ID = EER.USR_ID
              WHERE EE.OPT_TXB_ID = #{optTxbId}
            <choose>
                <when test='evDvCd != null and evDvCd neq "" '>
                    <choose>
                        <when test ='"DE".equals(evDvCd)'>
                        AND EE.EV_DV_CD = 'DE'           --  DIY 평가 : DE
                        </when>
                        <when test ='"IS".equals(evDvCd)'>
                        AND EE.EV_DV_CD = 'DE'           --  DIY 평가 : DE
                        AND EE.EV_DTL_DV_CD = 'IS'       --  오답유사
                        </when>
                        <when test ='"AE".equals(evDvCd)'>
                        AND EE.EV_DV_CD = 'AE'           --  AI 맞춤 학습 : AE
                        </when>
                        <when test ='"SE".equals(evDvCd)'>
                        AND EE.EV_DV_CD IN ('SE','TE')   -- 교과 평가 : SE,TE
                        </when>
                    </choose>
                </when>
            </choose>
              AND EER.USR_ID = #{usrId}
              AND EER.EV_CMPL_YN = 'Y'
              AND EEQA.CANS_YN = 'N'
              GROUP BY EE.EV_ID
        ) T
        LEFT JOIN LMS_LRM.EA_EV_TS_RNGE EETR ON (EETR.EV_ID = T.EV_ID and EETR.TS_RNGE_SEQ_NO = 1)
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = EETR.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = EETR.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = EETR.TC_LRMP_NOD_ID
        WHERE 1=1
        <if test='luLrmpNodId != null and luLrmpNodId neq ""'>
        	AND EETR.LU_LRMP_NOD_ID = #{luLrmpNodId}
        </if>
        <if test='tcLrmpNodId != null and tcLrmpNodId neq ""'>
            AND EETR.TC_LRMP_NOD_ID = #{tcLrmpNodId}
        </if>
        <if test='isIansNteCmpl != null and isIansNteCmpl neq ""'>
            <![CDATA[
            AND T.QST_CNT < 1
            ]]>
        </if>
        ORDER BY
        <choose>
            <when test='"DATE".equals(sorting)'>
                T.SMT_DTM DESC
            </when>
            <when test='"LU".equals(sorting)'>
            	NOD_LU.RCSTN_ORDN, NOD_TC.RCSTN_ORDN, T.SMT_DTM DESC
            </when>
            <when test='"NM".equals(sorting)'>
                T.EV_NM
            </when>
        </choose>
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}


        /* 학생 오답노트 - 김상민 - EaLansNteStu-Mapper.xml - 학생 오답노트 시험지별 조회 */
    </select>

    <select id="selectLansNteTsPaperQpList" parameterType="com.aidt.api.ea.evcom.lansnte.dto.CreateQuesSimilarDiyDto"
            resultType="hashMap">

        SELECT
	            EE.EV_ID                            AS evId,                -- 평가 ID
	            EEQ.QTM_ID                          AS qtmId,               -- 문항 ID
		        ROW_NUMBER() OVER(ORDER BY EEQ.QTM_ORDN) AS qtmOrdn, 		-- 문항순번
	            EEQA.SMT_ANW_VL                     AS smtAnwVl,            -- 제출 답변 값
	            EEQA.XPL_TM_SCNT                    AS xplTmScnt,           -- 풀이 시간 초수
	            QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
	            QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
	            QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
	            QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
	            EEQ.QP_LLU_ID                   AS qpLluId,             -- 단원 ID
	            EEQ.QP_LLU_NM                   AS qpLluNm,             -- 단원 명
	            EEQ.QP_TC_ID                    AS qpTcId,              -- 차시 ID
	            EEQ.QP_TC_NM                    AS qpTcNm,              -- 차시 명
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		 -- 템플릿 xml 내용
	            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
	            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
	            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
	            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
	            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
	            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
	            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
	            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
	            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
	            '' AS answerUrl,		-- 정답 url
	            '' AS explainUrl,		-- 해설 url
	            '' AS questionUrl, 		-- 질문 url
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl, -- HTML(ZIP) url
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType	-- 문항 등록 형태(hpw, html)
		      , IFNULL(BNCM.CRCL_CTN_ELM2_CD, '') 	AS eduCrsCnCd    	-- 교육과정콘텐츠표준CD
		      , IFNULL(BEA.EDU_CRS_ACH_BS_CD, '') 	AS eduCrsAchBsCd  	-- 교육과정성취기준CD
		      , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    		-- 문항 키워드명
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode -- 형판코드
       		  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName -- 형판명
			  , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
			  , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
        FROM LMS_LRM.EA_EV EE
        JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
        JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EEQA.EV_ID = EEQ.EV_ID AND EEQA.QTM_ID = EEQ.QTM_ID AND EEQA.USR_ID = #{usrId} AND EEQA.IANS_NTE_CANS_YN = 'N'
        JOIN LMS_CMS.QP_QTM QQ       ON QQ.QP_QTM_ID  = EEQ.QTM_ID
        JOIN LMS_CMS.QP_QTM_CN QQC   ON QQC.QP_QTM_ID = EEQ.QTM_ID
        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID  = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ  ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID  = QPQ.QP_PSSG_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01  ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02  ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	    LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
        LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = QQ.QP_QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX'
        LEFT JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID
        WHERE EE.EV_ID = #{evId}

        /* 학생 오답노트 - 김상민 - EaLansNteStu-Mapper.xml - 학생	오답노트 시험지 별 오답 문제 풀기 오답 문항 조회 > 수정 완 */

    </select>

    <select id="selectQtmListFromEvId" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto">

		SELECT EQ.EV_ID	AS evId
			  , EQ.QTM_ID AS qtmId
			  , EQ.TPC_ID AS tpcId
			  , QQ.QP_DFFD_CD AS qtmDffdCd
		FROM LMS_LRM.EA_EV_QTM EQ
		JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = EQ.QTM_ID
		JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
		WHERE EQ.EV_ID = #{evId}
		AND EQA.USR_ID = #{usrId}
		AND EQA.CANS_YN = 'N'
		AND EQA.IANS_NTE_CANS_YN = 'N'
		ORDER BY EQ.QTM_ORDN

        /* 오답노트 - 박원희 - EaLansNteStu-Mapper.xml - 시험지별 - 유사문제 출제 - 평가의 오답 문항 리스트 조회 - selectQtmListFromEvId */

    </select>

     <!-- 오답유사 > 오답 유사문항ID 리스트 조회-->
    <select id="selectIansSmlrQtmList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto">

    	SELECT
				#{qtmOrdn} qtmOrdn, SMR.SRC_QTM_ID AS srcQtmId, SMR.RLT_QTM_ID AS qtmId, SMR.RLT_QTM_TP_CD AS rltQtmTpCd, SMR.TPC_ID AS tpcId
			  , ROW_NUMBER() OVER(ORDER BY SMR.RLT_QTM_TP_CD DESC, RAND()) rowNo
		FROM (
			SELECT  RLT_Q.SRC_QTM_ID, RLT_Q.RLT_QTM_ID, MAX(RLT_Q.RLT_QTM_TP_CD) RLT_QTM_TP_CD, #{tpcId} as TPC_ID
			FROM LMS_CMS.BC_RLT_QTM_MPN RLT_Q
			WHERE RLT_Q.SRC_QTM_ID = #{qtmId}
			AND RLT_Q.TXB_ID = #{txbId}
			AND RLT_Q.RLT_QTM_TP_CD IN ('SI', 'TW')	-- 쌍둥이 우선순위 높음
			AND RLT_Q.DEL_YN = 'N'
			GROUP BY RLT_Q.SRC_QTM_ID, RLT_Q.RLT_QTM_ID
		UNION ALL
			SELECT #{qtmId} SRC_QTM_ID, AI_CTN.CTN_CD RLT_QTM_ID, 'A2' RLT_QTM_TP_CD, AI_CTN.KMMP_NOD_ID AS TPC_ID
			FROM LMS_CMS.BC_AI_LRN_ATV_CTN AI_CTN
			WHERE AI_CTN.KMMP_NOD_ID = #{tpcId}
			AND AI_CTN.CTN_TP_CD = 'QU'
			AND AI_CTN.DEL_YN = 'N'
			AND EXISTS(SELECT 1
 					   FROM LMS_CMS.BC_KMMP_NOD BKN
 					   JOIN LMS_CMS.BC_KMMP BK ON BK.KMMP_ID = BKN.KMMP_ID
 					   WHERE BKN.KMMP_NOD_ID = AI_CTN.KMMP_NOD_ID
 					   AND BKN.DEL_YN = 'N'
 					   AND BK.TXB_ID = #{txbId}
 					   AND BK.DEL_YN = 'N')
			AND EXISTS (SELECT 1 FROM LMS_CMS.QP_QTM WHERE QP_QTM_ID = AI_CTN.CTN_CD AND QP_DFFD_CD = #{qtmDffdCd})
			GROUP BY AI_CTN.KMMP_NOD_ID, AI_CTN.CTN_CD
		UNION ALL
			SELECT #{qtmId} SRC_QTM_ID, BNCM.CTN_ID RLT_QTM_ID, 'A3' RLT_QTM_TP_CD, BNCM.TPC_ID
			FROM LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM
			WHERE BNCM.TPC_ID = #{tpcId}
			AND BNCM.CRCL_CTN_TP_CD = 'EX'
 			AND EXISTS(SELECT 1
 					   FROM LMS_CMS.BC_KMMP_NOD BKN
 					   JOIN LMS_CMS.BC_KMMP BK ON BK.KMMP_ID = BKN.KMMP_ID
 					   WHERE BKN.KMMP_NOD_ID = BNCM.TPC_ID
 					   AND BKN.DEL_YN = 'N'
 					   AND BK.TXB_ID = #{txbId}
 					   AND BK.DEL_YN = 'N')
  			AND EXISTS (SELECT 1 FROM LMS_CMS.QP_QTM WHERE QP_QTM_ID = BNCM.CTN_ID AND QP_DFFD_CD = #{qtmDffdCd})
 			GROUP BY BNCM.TPC_ID, BNCM.CTN_ID
		) SMR
		WHERE SMR.RLT_QTM_ID NOT IN
        <foreach item="item" index ="index" collection="qtmIdNotIn" open="(" separator="," close=")">
            #{item.qtmId}
        </foreach>
		LIMIT 1

        /* 평가 문통 공통 - 박원희 - EaQtmCom-Mapper.xml - selectIansSmlrQtmList - 오답유사 > 문항별 유사문항 리스트 조회  */

    </select>


    <select id="selectIansSmlrCrtEvQtmList" resultType="hashMap">

	        SELECT
	        	   QQ.QP_QTM_ID AS qtmId
	        	 , QQ.QP_DFFD_CD AS difficultyCode
	        	 , DFFD.QP_UNIF_CD_NM AS difficultyName
	        	 , LLU.QP_LLU_ID AS lluId
	        	 , LLU.LLU_NM AS lluNm
	        	 , TPC.QP_TPC_LU_ID AS topicId
	        	 , TPC.QP_TPC_LU_NM AS topicNm
	        	 , QTM.tpcId AS topicIdKmmp
	 		FROM (
	 			    SELECT
	 			    	   qtmId, MAX(tpcId) tpcId, MAX(qtmOrdn) qtmOrdn
	 			    FROM (
		            <foreach item="item" index ="index" collection="smrlQtmList" separator="union all">
			        		SELECT #{item.qtmOrdn} as qtmOrdn, #{item.srcQtmId} as srcQtmId, #{item.qtmId} as qtmId, #{item.rltQtmTpCd} as rltQtmTpCd, #{item.tpcId} as tpcId
		            </foreach>
		            ) QTM
		            GROUP BY qtmId
			) QTM
	 		JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID			= QTM.qtmId
	        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    	= QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    	= QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   	= QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_LLU LLU ON LLU.QP_LLU_ID 		= QQA.QP_LLU_ID
			JOIN LMS_CMS.QP_TPC_LU TPC ON TPC.QP_TPC_LU_ID 	= QQA.QP_TPC_LU_ID
			LEFT JOIN LMS_CMS.QP_WRK_CD DFFD ON DFFD.QP_LCL_CD = '02' AND DFFD.QP_UNIF_CD = QQ.QP_DFFD_CD
			ORDER BY QTM.qtmOrdn

        /* 오답노트 - 박원희 - EaLansNteStu-Mapper.xml - 시험지별 - 유사문제 > 조회된 유사문항의 평가문항 등록정보 리스트 - selectIansSmlrCrtEvQtmList */

    </select>


     <!-- 오답노트 > 단원별 > 오답문제풀기 > 체크된 단원의 문항 리스트 조회-->
    <select id="selectIansQtmInfoFromEvQtmList" resultType="hashMap">
        SELECT
            E.EV_ID 							AS evId,
        	QQ.QP_QTM_ID                       	AS qtmId,  				-- 문항Id
            ROW_NUMBER() OVER() 				AS qtmOrdn,				-- 문항순번
            QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
            QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
            QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
            QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
            QLL.QP_LLU_ID                		AS lluId,    			-- 대단원 ID
            QLL.LLU_NM                    		AS lluNm,    			-- 대단원 명
            QTL.QP_TPC_LU_ID                    AS topicId,    			-- 토픽(차시) ID
            QTL.QP_TPC_LU_NM                    AS topicNm,    			-- 토픽(차시) 명
	        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
	        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
	        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
	        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
	        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
	        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
	        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
	        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
	        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
	        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
	        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
	        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
	        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
	        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		 -- 템플릿 xml 내용
            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
            '' AS answerUrl,		-- 정답 url
            '' AS explainUrl,		-- 해설 url
            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
            	 ELSE ''
            	 END AS htmlZipUrl, -- HTML(ZIP) url
            '' AS questionUrl, 		-- 질문 url
	        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
	        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
	        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType	-- 문항 등록 형태(hpw, html)
	      , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    		-- 문항 키워드명
          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	      , IFNULL(BNCM.CRCL_CTN_ELM2_CD, '') 	AS eduCrsCnCd    	-- 교육과정콘텐츠표준CD
	      , IFNULL(BEA.EDU_CRS_ACH_BS_CD, '') 	AS eduCrsAchBsCd  	-- 교육과정성취기준CD
          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode -- 형판코드
      	  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName -- 형판명
		  , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
		  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
		  , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
        FROM (
            <foreach item="item" index ="index" collection="qtmIdList" separator="union">
	        		SELECT E.EV_ID, EQ.QTM_ID, EQ.TPC_ID
	        		FROM LMS_LRM.EA_EV E
			        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
			        WHERE E.OPT_TXB_ID = #{optTxbId}
			        AND E.EV_ID = #{item.evId}
			        AND EQ.QTM_ID = #{item.qtmId}
            </foreach>
		) E
        JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID    	   = E.QTM_ID
        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID = QPQ.QP_PSSG_ID
        LEFT JOIN LMS_CMS.QP_LLU QLL ON QLL.QP_LLU_ID = QQA.QP_LLU_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	    LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
        LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = QQ.QP_QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX'
        LEFT JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID


        /* 오답노트 - 박원희 - EaLansNteStu-Mapper.xml - selectIansQtmInfoFromEvQtmList - 단원별 오답노트 문항 리스트 조회 */
    </select>

    <select id="selectLansNteLuListNew" parameterType="com.aidt.api.ea.evcom.lansnte.dto.EaLansNteReqDto"
            resultType="hashMap">

	SELECT
			E.LRMP_NOD_ID 		AS lluNodId 		-- 대단원 ID
		  , MAX(
		    CASE WHEN IFNULL(E.RCSTN_NO, '') = '' THEN E.LRMP_NOD_NM
		  		 ELSE CONCAT(E.RCSTN_NO, '. ', E.LRMP_NOD_NM)
		  		 END
		    )	AS lluNodNm			-- 대단원 명
		  , MAX(E.LRMP_NOD_NM)	AS lrmpNodNm
		  , COUNT(DISTINCT E.QTM_ID) AS iansCnt 			-- 오답 문항 개수
		  , SUM(CASE WHEN XPL_ST_CD = '11' THEN 1 ELSE 0 END) AS misCnt -- 실수로틀린 문항 개수
	FROM (
				SELECT
						L_NOD1.LRMP_NOD_ID
					  , EQA.QTM_ID
					  , MAX(EQA.XPL_ST_CD) XPL_ST_CD
					  , MAX(L_NOD1.LRMP_NOD_NM) LRMP_NOD_NM
					  , MAX(L_NOD1.RCSTN_ORDN) RCSTN_ORDN
					  , MAX(L_NOD1.RCSTN_NO) RCSTN_NO
				FROM LMS_LRM.CM_OPT_TXB OT
				JOIN LMS_LRM.EA_EV E ON E.OPT_TXB_ID = OT.OPT_TXB_ID
		        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = ER.USR_ID
				JOIN LMS_CMS.BC_KMMP_NOD K_NOD5 ON K_NOD5.KMMP_NOD_ID = EQ.TPC_ID
				JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_NOD5.URNK_KMMP_NOD_ID
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_NOD4 ON L_NOD4.OPT_TXB_ID = OT.OPT_TXB_ID AND L_NOD4.LRMP_NOD_ID = NOD_MPN.LRMP_NOD_ID
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_NOD1 ON L_NOD1.OPT_TXB_ID = OT.OPT_TXB_ID AND L_NOD1.LRMP_NOD_ID = L_NOD4.LLU_NOD_ID
				WHERE OT.OPT_TXB_ID = #{optTxbId}
				AND IFNULL(E.EV_DTL_DV_CD, '') NOT IN ('ST','UD') -- 학기초진단, 단원진단 평가 제외
				AND ER.USR_ID = #{usrId}
				AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
				AND EQA.CANS_YN = 'N'
				AND EQA.IANS_NTE_CANS_YN = 'N'
				AND L_NOD4.LU_NO_USE_YN = 'Y' -- 2024-11-19 넘버링(단원번호) 사용안하는 단원(특수단원) 제외
				AND L_NOD1.LU_NO_USE_YN = 'Y' -- 2024-11-19 넘버링(단원번호) 사용안하는 단원(특수단원) 제외
				GROUP BY L_NOD1.LRMP_NOD_ID, EQA.QTM_ID
		) E
		GROUP BY LRMP_NOD_ID
		ORDER BY MAX(RCSTN_ORDN)


        /* 학생 오답노트 - 박원희 - EaLansNteStu-Mapper.xml - selectLansNteLuListNew - 학생 오답노트 단원별 조회 */

    </select>

    <select id="selectLansNteLuQtmList" parameterType="com.aidt.api.ea.evcom.lansnte.dto.EaLansNteReqDto"
            resultType="hashMap">


		SELECT
			  	L_NOD1.LRMP_NOD_ID		AS lluNodId
			  , MAX(E.EV_ID)				AS evId
			  , MAX(EQ.TPC_ID)				AS tpcId
			  , (SELECT QP_DFFD_CD FROM LMS_CMS.QP_QTM QQ WHERE QQ.QP_QTM_ID = EQA.QTM_ID) qtmDffdCd
			  , EQA.QTM_ID				AS qtmId
			  , MAX(EQA.XPL_ST_CD) 		AS xplStCd
			  , MAX(L_NOD1.LRMP_NOD_NM)	AS lluNodNm -- 단원 정보
		FROM LMS_LRM.CM_OPT_TXB OT
		JOIN LMS_LRM.EA_EV E ON E.OPT_TXB_ID = OT.OPT_TXB_ID
        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
		JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = ER.USR_ID
		JOIN LMS_CMS.BC_KMMP_NOD K_NOD5 ON K_NOD5.KMMP_NOD_ID = EQ.TPC_ID
		JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_NOD5.URNK_KMMP_NOD_ID
		JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_NOD4 ON L_NOD4.OPT_TXB_ID = OT.OPT_TXB_ID AND L_NOD4.LRMP_NOD_ID = NOD_MPN.LRMP_NOD_ID
		JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_NOD1 ON L_NOD1.OPT_TXB_ID = OT.OPT_TXB_ID AND L_NOD1.LRMP_NOD_ID = L_NOD4.LLU_NOD_ID
		WHERE OT.OPT_TXB_ID = #{optTxbId}
		AND IFNULL(E.EV_DTL_DV_CD, '') NOT IN ('ST','UD') -- 학기초진단, 단원진단 평가 제외
		AND ER.USR_ID = #{usrId}
		AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
		AND EQA.CANS_YN = 'N'
		AND EQA.IANS_NTE_CANS_YN = 'N'
		AND L_NOD4.LU_NO_USE_YN = 'Y' -- 2024-11-19 넘버링(단원번호) 사용안하는 단원(특수단원) 제외
		AND L_NOD1.LU_NO_USE_YN = 'Y' -- 2024-11-19 넘버링(단원번호) 사용안하는 단원(특수단원) 제외
		GROUP BY L_NOD1.LRMP_NOD_ID, EQA.QTM_ID

        /* 학생 오답노트 - 박원희 - EaLansNteStu-Mapper.xml - selectLansNteLuQtmList - 학생 오답노트 단원별 문항 리스트 조회 */

    </select>


    <!-- 오답노트 단원별 > 오답유사 DIY평가 생성 > 오답 문항 리스트 조회 > 학급분석에서 사용 -->
    <select id="selectEvIansQtmIdList" resultType="hashMap">

	        SELECT
	        	   QQ.QP_QTM_ID AS qtmId
	        	 , MAX(QQ.QP_DFFD_CD) AS difficultyCode
	        	 , MAX(DFFD.QP_UNIF_CD_NM) AS difficultyName
	        	 , MAX(LLU.QP_LLU_ID) AS lluId
	        	 , MAX(LLU.LLU_NM) AS lluNm
	        	 , MAX(TPC.QP_TPC_LU_ID) AS topicId
	        	 , MAX(TPC.QP_TPC_LU_NM) AS topicNm
				 , (
						SELECT
								TPC_ID
						FROM (
								SELECT BEQM.TPC_ID, 'A1' QTM_TP_CD
								FROM LMS_CMS.BC_EVSH BE
								JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM ON BEQM.EVSH_ID = BE.EVSH_ID
								WHERE BE.TXB_ID = #{txbId}
								AND BE.DEL_YN = 'N'
								AND BEQM.QTM_ID = QQ.QP_QTM_ID
								AND BEQM.DEL_YN = 'N'
						UNION ALL
								SELECT K_ATV.KMMP_NOD_ID AS TPC_ID,  'A2' QTM_TP_CD
								FROM LMS_CMS.BC_AI_LRN_ATV_CTN K_ATV
								JOIN LMS_CMS.BC_KMMP_NOD K_NOD ON K_NOD.KMMP_NOD_ID = K_ATV.KMMP_NOD_ID
								JOIN LMS_CMS.BC_KMMP K_MP ON K_MP.KMMP_ID = K_NOD.KMMP_ID
								WHERE K_ATV.CTN_CD = QQ.QP_QTM_ID
								AND K_ATV.CTN_TP_CD = 'QU'
								AND K_NOD.DEL_YN = 'N'
								AND K_MP.DEL_YN = 'N'
								AND K_MP.TXB_ID =  #{txbId}
						) TPC
						GROUP BY TPC.TPC_ID
						ORDER BY MAX(TPC.QTM_TP_CD) LIMIT 1
				   ) AS topicIdKmmp
	 		FROM LMS_CMS.QP_QTM QQ
	        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    	= QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    	= QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   	= QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_LLU LLU ON LLU.QP_LLU_ID 		= QQA.QP_LLU_ID
			JOIN LMS_CMS.QP_TPC_LU TPC ON TPC.QP_TPC_LU_ID 	= QQA.QP_TPC_LU_ID
			LEFT JOIN LMS_CMS.QP_WRK_CD DFFD ON DFFD.QP_LCL_CD = '02' AND DFFD.QP_UNIF_CD = QQ.QP_DFFD_CD
	        WHERE QQ.DEL_YN ='N'
	        AND QQ.QP_QTM_ID IN
			<foreach item="item" index ="index" collection="qtmIdList" open="(" separator="," close=")">
				#{item.qtmId}
			</foreach>
	        GROUP BY QQ.QP_QTM_ID
	        ORDER BY QQ.QP_DFFD_CD DESC, QQ.QP_QTM_ID

        /* EaLansNteStu-Mapper.xml - selectEvIansQtmIdList - 오답노트 단원별 > 오답유사 DIY평가 생성 > 오답 문항 리스트 조회  > 학급분석에서 사용 - 박원희 */

    </select>


	<!--    오답노트 단원별 > 오답유사 DIY평가 생성      -->
	<insert id="insertEvFromSmlrEv" useGeneratedKeys="true" keyProperty="evId">

		INSERT INTO LMS_LRM.EA_EV (
			  OPT_TXB_ID, USR_ID
			, EV_DV_CD, EV_DTL_DV_CD, EV_NM
			, TXM_PTME_SETM_YN, TXM_STR_DTM, TXM_END_DTM
			, XPL_TM_SETM_YN, XPL_TM_SCNT
			, QST_CNT, FNL_QST_CNT
			, LCKN_YN
			, RTXM_PMSN_YN, USE_YN, DEL_YN
			, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES (
		       #{optTxbId}, #{usrId}
			 , #{evDvCd}
			 , CASE WHEN #{evDvCd} = 'DE' THEN 'IS' ELSE NULL END
			 , #{evNm}
			 , 'N', NULL, NULL
			 , 'N', NULL
			 , #{qstCnt}, #{qstCnt}
			 , 'N'
			 , 'Y', 'Y', 'N'
			 , #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		;

        /* EaLansNteStu-Mapper.xml - insertEvFromSmlrEv - 오답노트 단원별 > 오답유사 > 평가 등록 - 박원희 */
	</insert>

	<!--    오답노트 시험지별 > 오답유사 DIY평가 시험범위 등록     -->
	<insert id="insertEvTsRngeTsPaperEvId">

		INSERT INTO LMS_LRM.EA_EV_TS_RNGE(
				EV_ID, TS_RNGE_SEQ_NO
			  , LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
			  , MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
			  , SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
			  , TC_OPT_TXB_ID, TC_LRMP_NOD_ID, TC_LRMP_NOD_NM
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT #{evId}, ROW_NUMBER() OVER(ORDER BY TS_RNGE_SEQ_NO)
			  , LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
			  , MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
			  , SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
			  , TC_OPT_TXB_ID, TC_LRMP_NOD_ID, TC_LRMP_NOD_NM
		      , #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM LMS_LRM.EA_EV_TS_RNGE
		WHERE EV_ID = #{evIdOld}
		ORDER BY TS_RNGE_SEQ_NO;

        /* EaLansNteStu-Mapper.xml - insertEvTsRngeTsPaperEvId - 오답노트 시험지별 > 오답유사 > 기존평가의 평가시험범위 생성 - 박원희 */
	</insert>


	<!--    오답노트 단원별 > 오답유사 DIY평가 시험범위 등록     -->
	<insert id="insertEvTsRngeFromSmlrEv">

		INSERT INTO LMS_LRM.EA_EV_TS_RNGE(
				EV_ID, TS_RNGE_SEQ_NO, LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT #{evId}, ROW_NUMBER() OVER(ORDER BY MAX(RCSTN_ORDN)), #{optTxbId}, LRMP_NOD_ID, MAX(LRMP_NOD_NM)
		     , #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID IN
		<foreach item="item" index ="index" collection="crtLluList" open="(" separator="," close=")">
			#{item.lluNodId}
		</foreach>
		GROUP BY LRMP_NOD_ID;

        /* EaLansNteStu-Mapper.xml - insertEvTsRngeFromSmlrEv - 오답노트 단원별 > 오답유사 > 평가시험범위 생성 - 박원희 */
	</insert>

	<!--    오답노트 단원별 > 오답유사 DIY평가 문항리스트 등록     -->
	<insert id="insertEvQtmFromSmlrEv">

		INSERT INTO LMS_LRM.EA_EV_QTM
		(	   EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM
			 , QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM, DEL_YN, DEL_DTM
			 , TPC_ID
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="crtEaEvQtmList" index="index" item="item" separator=",">
		(
			  #{evId}, #{item.qtmId}, #{index}+1, #{item.difficultyCode}, #{item.difficultyName}
			, #{item.lluId}, #{item.lluNm}, #{item.topicId}, #{item.topicNm}, 'N', NULL
			, #{item.topicIdKmmp}
			, #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;

        /* EaLansNteStu-Mapper.xml - insertEvQtmFromSmlrEv - 오답노트 단원별 > 오답유사 > 평가문항 생성 - 박원희 */
	</insert>

	<!--    오답노트 단원별 > 오답유사 DIY평가 난이도구성 등록     -->
	<insert id="insertEvDffdFromSmlrEv">

		INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN( EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		SELECT
		     #{evId}, QQ.QP_DFFD_CD, COUNT(QQ.QP_QTM_ID), #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM LMS_CMS.QP_QTM QQ
		WHERE QQ.QP_QTM_ID IN
		<foreach item="item" index ="index" collection="crtEaEvQtmList" open="(" separator="," close=")">
			#{item.qtmId}
		</foreach>
		GROUP BY QQ.QP_DFFD_CD
		ORDER BY QQ.QP_DFFD_CD DESC
		;

        /* EaLansNteStu-Mapper.xml - insertEvDffdFromSmlrEv - 오답노트 단원별 > 오답유사 > 평가난이도구성 생성 - 박원희 */
	</insert>

	<!--    오답노트 단원별 > 오답유사 > 평가 결과 등록 -> 학생전용   -->
	<insert id="insertEvRsFromSmlrEvStu">

		INSERT INTO LMS_LRM.EA_EV_RS (
				EV_ID, USR_ID, TXM_STR_YN, EV_CMPL_YN
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES(
				#{evId}, #{usrId}, 'N', 'N'
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
		);

        /* EaLansNteStu-Mapper.xml - insertEvRsFromSmlrEv - 오답노트 단원별 > 오답유사 > 평가결과 생성 > 학생전용 - 박원희 */
	</insert>

	<!--    오답노트 단원별 > 오답유사 > 평가 결과 등록 -> 교사전용    -->
	<insert id="insertEvRsFromSmlrEvTcr">

		INSERT INTO LMS_LRM.EA_EV_RS (
				EV_ID, USR_ID, TXM_STR_YN, EV_CMPL_YN
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT
		       #{evId}, USR.USR_ID, 'N', 'N'
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM LMS_LRM.CM_OPT_TXB OT
		JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
		WHERE OT.OPT_TXB_ID = #{optTxbId}
		AND USR.USR_TP_CD = 'ST'
		;

        /* EaLansNteStu-Mapper.xml - insertEvRsFromSmlrEv - 오답노트 단원별 > 오답유사 > 평가결과 생성 > 교사전용 - 박원희 */
	</insert>




    <!-- 오답노트 > 시험지별 > 대단원 리스트 -->
    <select id="selectWrongAnwBestLluList" resultType="hashMap">

		select t1.lrmp_nod_id
		     , t1.rcstn_ordn
		     , case when t1.lu_no_use_yn = 'Y' then concat(t1.lrmp_num,'. ', t1.lrmp_nod_nm)
		            else t1.lrmp_nod_nm
		        end as lrmp_nod_nm
		     , t1.lrmp_num
		     , t1.lu_no_use_yn
		from (
		    select a.lrmp_nod_id
		         , a.lrmp_nod_nm
		         , a.rcstn_ordn
		         , a.lu_no_use_yn
		         , row_number() over(order by a.lu_no_use_yn desc, a.rcstn_ordn) as lrmp_num
		      from lms_lrm.tl_sbc_lrn_nod_rcstn a
		     where a.opt_txb_id = #{optTxbId}
		       and a.dpth = 1
		       and a.use_yn = 'Y'
		       and a.lu_eps_yn = 'Y'
		       and exists (select 1 from  lms_lrm.ea_ev_ts_rnge z where z.LU_OPT_TXB_ID = a.opt_txb_id and z.lu_lrmp_nod_id=a.lrmp_nod_id )
		       and a.lu_no_use_yn= 'Y'
		 ) t1
 		 order by t1.rcstn_ordn

        /* EaLansNteStu-Mapper.xml - selectWrongAnwBestLluList - 오답노트 > 시험지별 > 대단원 리스트 - 박원희 */

    </select>

    <!-- 오답노트 > 시험지별 > 오답 BEST 학생리스트 조회 요청 -->
    <select id="selectWrongAnwBestStuList" resultType="hashMap">

		        SELECT
			             EQ.QTM_ID 			 AS qtmId       -- 문항 ID
			            ,USR.USR_ID 		 AS usrId     	-- 사용자 ID
		        FROM LMS_LRM.EA_EV E
		        JOIN (
		        	   SELECT OT.OPT_TXB_ID, U.USR_ID, COUNT(1) OVER() USR_CNT
				       FROM LMS_LRM.CM_OPT_TXB OT
				       JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
				       WHERE OT.OPT_TXB_ID =  #{optTxbId}
				       AND U.USR_TP_CD = 'ST'
				       GROUP BY OT.OPT_TXB_ID, U.USR_ID
		        ) USR ON USR.OPT_TXB_ID = E.OPT_TXB_ID
		        JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
		        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		        JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = USR.USR_ID
		        WHERE E.OPT_TXB_ID =  #{optTxbId}
                AND E.EV_DTL_DV_CD NOT IN ('ST', 'UD')
        		AND E.DEL_YN ='N'
        		AND ER.EV_CMPL_YN = 'Y'
		        AND EQ.DEL_YN = 'N'
		        AND EQA.CANS_YN ='N'
		        AND EQ.TPC_ID != 0
		        <if test='qtmId != null and qtmId neq ""'>
		        AND EQ.QTM_ID = #{qtmId}
		        </if>
		        GROUP BY EQ.QTM_ID, USR.USR_ID

        /* EaLansNteStu-Mapper.xml - selectWrongAnwBestStuList - 오답노트 > 시험지별 > 오답 BEST > 학생리스트 조회 요청 - 박원희 */

    </select>

    <!-- 오답노트 > 시험지별 > 맞힌문항 조회 요청 -->
    <select id="selectIansNoteCansYQtmList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto">
		 SELECT	EE.EV_ID 							AS evId
	          , EEQ.QTM_ORDN 						AS qtmNo
	          , EE.EV_NM 							AS evNm
	          , EEQ.QTM_ID 							AS qtmId
	          , EEQ.QTM_ORDN 						AS qtmOrdn
	          , EEQ.QTM_DFFD_DV_CD 					AS qtmDffdDvCd	-- 문항난이도코드
	          , EEQ.DEL_YN 							AS delYn
	          , EEQ.DEL_DTM 						AS delDtm
	          , EEQA.USR_ID 						AS usrId
			  , IFNULL(EEQA.IANS_NTE_SMT_ANW_VL, '')		AS smtAnwVl		-- 오답노트 제출답변
	          , IFNULL(EEQA.QST_XPL_CN, '')			AS qstXplCn		-- 문제 풀이 내용
	          , EEQA.IANS_NTE_CANS_YN				AS cansYn		-- 오답노트 정답 여부
	          , EEQA.XPL_TM_SCNT 					AS xplTmScntNm	-- 풀이시간
	          , EEQA.CRTR_ID 						AS crtrId
	          , EEQA.CRT_DTM 						AS crtDtm
	          , EEQA.MDFR_ID 						AS mdfrId
	          , EEQA.MDF_DTM 						AS mdfDtm
	          , EEQA.DB_ID 							AS dbId
		   FROM LMS_LRM.EA_EV EE
	 INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
	 INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
	 INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQA.USR_ID = EER.USR_ID AND EEQ.QTM_ID = EEQA.QTM_ID
	      WHERE 1 = 1
	        AND EE.OPT_TXB_ID 	= #{optTxbId}
	        AND EE.EV_ID 		= #{evId}
	        AND EER.USR_ID		= #{usrId}
	        AND EER.EV_CMPL_YN	= 'Y'				-- 평가완료여부
	        AND EEQA.CANS_YN 	= 'N'				-- 정답여부
	        AND EEQA.IANS_NTE_CANS_YN = 'Y'			-- 오답노트 정답 여부
	      ORDER BY EEQ.QTM_ORDN
	      /* EaLansNteStu-Mapper.xml - selectIansNoteCansYQtmList - 오답노트 > 시험지별 > 맞힌문항 > 오답노트 학습 맞힌문항 조회- 윤다솔*/
    </select>
</mapper>