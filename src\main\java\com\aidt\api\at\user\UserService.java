package com.aidt.api.at.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.aidt.api.at.mapper.UserMapper;
import com.aidt.api.at.user.dto.TokenDto;
import com.aidt.api.at.user.dto.UserCreateDto;
import com.aidt.api.at.user.dto.UserCreateRstDto;
import com.aidt.api.at.user.dto.UsrDto;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.exception.BizException;

@Service
public class UserService {

	@Autowired
	private UserMapper userMapper;

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	/**
	 * 회원의 token, usr data 검증 및 생성
	 * 
	 * @param dto
	 */
	@Transactional
	public UserCreateRstDto saveCheckTokenUsr(UserCreateDto dto) {
		if (dto == null) {
			throw new BizException("UserCreateDto null...");
		}

		if (!StringUtils.hasText(dto.getKerisUsrId())) {
			throw new BizException("UserCreateDto.kerisUsrId is null or empty...");
		} else if (!StringUtils.hasText(dto.getLectureCode())) {
			throw new BizException("UserCreateDto.lectureCode is null or empty...");
		} else if (!StringUtils.hasText(dto.getOptTxbId())) {
			throw new BizException("UserCreateDto.optTxbId is null or empty...");
		} else if (!StringUtils.hasText(dto.getClaId())) {
			throw new BizException("UserCreateDto.claId is null or empty...");
		} else if (!StringUtils.hasText(dto.getUsrTpCd())) {
			throw new BizException("UserCreateDto.usrTpCd is null or empty...");
		}

		// 토큰 등록 여부 조회
		String tokenUsrId = userMapper.selectTokenUsrId(dto);
		String usrId = null;
		boolean usrNew = false;

		// token 존재하는 경우
		if (StringUtils.hasText(tokenUsrId)) {
			// usr 미등록 상태
			if (userMapper.selectUsrCheckCnt(tokenUsrId) == 0) {
				insertUsr(dto, tokenUsrId);

				usrNew = true;
			}
			// usr 등록 상태 keris 동의여부 update
			else {
				updateUsrKerisTermAgrYn(dto, tokenUsrId);
			}

			usrId = tokenUsrId;
		}
		// token 미등록 상태
		else {
			/**
			 * 생성되려는 token에 중복된 login_id가 존재하는 경우 throw -- 2025.03.19 장애없이 해당 로그인 사용자의 액션으로
			 * 넘어갈 수 있게 수정 - loginId는 lecture_code와 keris_usr_id 조합으로 다른 사용자일 수 없다.
			 */
			usrId = userMapper.selectTokenLoginIdConfirm(makeLoginId(dto.getLectureCode(), dto.getKerisUsrId()));
			/*
			 * if(userMapper.selectTokenLoginIdConfirm(makeLoginId(dto.getLectureCode(),
			 * dto.getKerisUsrId())) > 0) { throw new
			 * BizException("중복된 login_id가 존재하여 등록처리 실패하였습니다. 생성하려는 login_id = " +
			 * makeLoginId(dto.getLectureCode(), dto.getKerisUsrId())); }
			 */

			if (StringUtils.hasText(usrId)) {
				if (userMapper.selectUsrCheckCnt(usrId) == 0) {
					insertUsr(dto, usrId);

					usrNew = true;
				}
				// usr 등록 상태 keris 동의여부 update
				else {
					updateUsrKerisTermAgrYn(dto, usrId);
				}
			} else {
				/**
				 * 기존 정책 방어 로직 (cm_usr만 등록 되어 있고 cm_token엔 없는 경우)
				 */
				usrId = userMapper.selectUsrUsrId(dto);

				// cm_usr 존재하는 경우 해당 usrId로 token 등록
				if (StringUtils.hasText(usrId)) {
					insertToken(dto, usrId);

					// keris 동의여부 update
					updateUsrKerisTermAgrYn(dto, usrId);
				}
				// cm_usr 미등록 상태
				else {
					usrId = insertTokenMakeUsrId(dto);

					if (StringUtils.hasText(usrId)) {
						insertUsr(dto, usrId);

						usrNew = true;

					} else {
						throw new BizException("usrId is null or empty...");
					}
				}
			}
		}

		return UserCreateRstDto.builder().usrId(usrId).usrNew(usrNew).build();
	}

	/**
	 * cm_usr 등록
	 * 
	 * @param dto
	 * @param usrId
	 */
	@Transactional
	public void insertUsr(UserCreateDto dto, String usrId) {
		userMapper.insertUsr(UsrDto.builder().usrId(usrId).kerisUsrId(dto.getKerisUsrId()).usrTpCd(dto.getUsrTpCd())
				.usrStCd("1").claId(dto.getClaId()).flnStCd("NM").ntrYn("N").kerisTermAgrYn(dto.getKerisTermAgrYn())
				.kerisTermAgrDt(dto.getKerisTermAgrDt()).lrnrVelTpCd("NM").crtrId(usrId).mdfrId(usrId).dbId(DB_ID)
				.build());
	}

	/**
	 * loginId 규칙에 따른 생성 : lectureCode + "-" + kerisUsrId
	 * 
	 * @param lectureCode
	 * @param kerisUsrid
	 * @return
	 */
	private String makeLoginId(String lectureCode, String kerisUsrid) {
		if (StringUtils.hasText(lectureCode) && StringUtils.hasText(kerisUsrid)) {
			return lectureCode + "-" + kerisUsrid;
		} else {
			throw new BizException("loginId make required value is null or empty...");
		}
	}

	/**
	 * login 비밀번호 규칙에 따른 생성 : loginId + "_!@12"
	 * 
	 * @param loginId
	 * @return
	 */
	private String makePwd(String loginId) {
		if (StringUtils.hasText(loginId)) {
			return loginId + "_!@12";
		} else {
			throw new BizException("loginId is null or empty...");
		}
	}

	/**
	 * cm_token DB insert용 TokenDto set
	 * 
	 * @param dto
	 * @param usrId
	 * @return
	 */
	private TokenDto setTokenDto(UserCreateDto dto, String usrId) {
		String loginId = makeLoginId(dto.getLectureCode(), dto.getKerisUsrId());

		return TokenDto.builder().usrId(usrId).kerisUsrId(dto.getKerisUsrId()).loginId(loginId).pwd(makePwd(loginId))
				.optTxbId(dto.getOptTxbId()).txbId(BcCmUtil.getTxbID(DB_ID)).claId(dto.getClaId()).build();
	}

	/**
	 * cm_token usrId 존재 시 등록용
	 * 
	 * @param dto
	 * @param usrId
	 */
	@Transactional
	public void insertToken(UserCreateDto dto, String usrId) {
		try {
			userMapper.insertToken(setTokenDto(dto, usrId));
		} catch (Exception e) {
			if (e instanceof DuplicateKeyException) {
				throw new BizException("TOKEN_DUP");
			} else {
				throw new BizException("insertTokenMakeUsrId error... >>> " + e.getMessage());
			}
		}
	}

	/**
	 * cm_token usrId 내부 생성 등록용
	 * 
	 * @param dto
	 * @return
	 */
	@Transactional
	public String insertTokenMakeUsrId(UserCreateDto dto) {
		TokenDto tokenDto = setTokenDto(dto, null);

		try {
			userMapper.insertTokenMakeUsrId(tokenDto);
		} catch (Exception e) {
			if (e instanceof DuplicateKeyException) {
				throw new BizException("TOKEN_DUP");
			} else {
				throw new BizException("insertTokenMakeUsrId error... >>> " + e.getMessage());
			}
		}

		return tokenDto.getUsrId();
	}

	/**
	 * keris 동의여부 update
	 * 
	 * @param dto
	 * @param usrId
	 */
	@Transactional
	public void updateUsrKerisTermAgrYn(UserCreateDto dto, String usrId) {
		if (StringUtils.hasText(dto.getKerisTermAgrYn()) && StringUtils.hasText(usrId)) {
			userMapper.updateUsrKerisTermAgrYn(UsrDto.builder().usrId(usrId).kerisTermAgrYn(dto.getKerisTermAgrYn())
					.kerisTermAgrDt(dto.getKerisTermAgrDt()).build());
		}
	}

	/**
	 * cm_usr에 usrId 값이 존재하지 않는 경우 생성용
	 * 
	 * @param dto
	 * @param usrId
	 * @return
	 * 
	 *         정상로직을 탄 경우 있을 수 없는 경우이나 기존 로직 존재함에 따라 생성
	 */
	@Transactional
	public UserCreateRstDto saveCheckUsr(UserCreateDto dto, String usrId) {
		if (!StringUtils.hasText(dto.getKerisUsrId())) {
			throw new BizException("UserCreateDto.kerisUsrId is null or empty...");
		} else if (!StringUtils.hasText(dto.getClaId())) {
			throw new BizException("UserCreateDto.claId is null or empty...");
		} else if (!StringUtils.hasText(dto.getUsrTpCd())) {
			throw new BizException("UserCreateDto.usrTpCd is null or empty...");
		}

		if (!StringUtils.hasText(usrId)) {
			throw new BizException("usrId is null or empty...");
		}

		boolean usrNew = false;

		if (userMapper.selectUsrCheckCnt(usrId) == 0) {
			insertUsr(dto, usrId);

			usrNew = true;
		}

		return UserCreateRstDto.builder().usrId(usrId).usrNew(usrNew).build();
	}
}
