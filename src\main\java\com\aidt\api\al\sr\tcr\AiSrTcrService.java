package com.aidt.api.al.sr.tcr;

import static java.util.stream.Collectors.groupingBy;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.aidt.api.al.sr.cm.AiSrService;
import com.aidt.api.al.sr.dto.pf.AiSrPfAtvNmResDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultReqDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultResDto;
import com.aidt.api.al.sr.dto.sbc.AiSrAchCmtDto;
import com.aidt.api.al.sr.dto.sbc.AiSrQtmDto;
import com.aidt.api.al.sr.dto.sbc.AiSrTcrLrnReqDto;
import com.aidt.api.al.sr.dto.sbc.AiSrTcrLrnResDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataReqDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataResDto;
import com.aidt.common.util.WebFluxUtil;

import lombok.extern.slf4j.Slf4j;

import com.aidt.api.bc.cm.BcCmUtil;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 16:48:10
 * @modify date 2024-05-24 16:48:10
 * @desc
 */
@Slf4j
@Service
public class AiSrTcrService {

    private final AiSrService aiSrService;

    private final WebFluxUtil webFluxUtil;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    public AiSrTcrService(AiSrService aiSrService, WebFluxUtil webFluxUtil) {
        this.aiSrService = aiSrService;
        this.webFluxUtil = webFluxUtil;
    }

    public List<AiSrPfAtvNmResDto> getPfmcAtvList(String txbId) {
        return aiSrService.getPfmcAtvList(txbId);
    }

    public List<AiSrPfResultResDto.StudentResDto> getPfmcAtvResultList(AiSrPfResultReqDto reqDto) {
        List<AiSrPfResultDto> resultList = aiSrService.getPfmcAtvResultList(reqDto);

        Map<String, List<AiSrPfResultDto>> groupingByLvlCd = resultList.stream().collect(groupingBy((AiSrPfResultDto::getLvlCd)));

        return reqDto.getStudents().stream().map(s -> {
            if(groupingByLvlCd.get(s.getLvlCd()).isEmpty()) {
                groupingByLvlCd.put(s.getLvlCd(), resultList.stream().filter(r -> Objects.equals(r.getLvlCd(), s.getLvlCd())).collect(Collectors.toList()));
            }
            List<AiSrPfResultDto> aiSrPfResults = groupingByLvlCd.get(s.getLvlCd());
            AiSrPfResultResDto.StudentResDto dto = s.toDto(aiSrPfResults.get(0));
            aiSrPfResults.remove(0);
            return dto;
        }).collect(Collectors.toList());
    }

    public Map<String, AiSrTcrLrnResDto> getResult(AiSrTcrLrnReqDto reqDto) {
        Map<String, AiSrTcrLrnResDto> resultMap = new HashMap<>();
        List<String> stuIdList = reqDto.getStuIdList();

        if(reqDto.getStuIdList() == null || reqDto.getStuIdList().isEmpty()) {
            return null;
        }

        List<AiSrQtmDto> aiSrQtmDtos = aiSrService.selectQtmListByNodeId(stuIdList, reqDto.getLrnIdList(), reqDto.getOptTxbId());
        Map<String, List<AiSrQtmDto>> groupMap = aiSrQtmDtos.stream().collect(groupingBy(AiSrQtmDto::getUsrId));

        for(String stuId :  stuIdList) {
            List<AiSrQtmDto> list = groupMap.get(stuId);
            if(list != null && !list.isEmpty()) {
                AiSrTcrLrnResDto resDto = AiSrTcrLrnResDto.builder().usrId(stuId).aiSrQtmDtoList(list).build();

                if(list.size() > 2) {
                    AiSrQtmDto second = list.get(1);

                    ListIterator<AiSrQtmDto> iterator = list.listIterator();
                    while(iterator.hasNext()) {
                        AiSrQtmDto next = iterator.next();
                        if(iterator.nextIndex() > 2) {
                            if(!(Objects.equals(second.getCorrectRate(), next.getCorrectRate())
                                    && Objects.equals(second.getCorrectCnt(), next.getCorrectCnt()))) {
                                iterator.remove();
                            }
                        }
                    }
                }
                resultMap.put(stuId, resDto);
            }
        }

        Map<String, List<AiSrQtmDto>> bsCdCollect = aiSrQtmDtos.stream().collect(groupingBy(AiSrQtmDto::getQpAchBsCd));

        Map<String, List<AiSrAchCmtDto>> map = new HashMap<>();
        bsCdCollect.keySet().forEach(s -> map.put(s, aiSrService.getAchCmt(s)));

        resultMap.keySet().forEach(r -> {
            AiSrTcrLrnResDto aiSrTcrLrnResDto = resultMap.get(r);
            if(!aiSrTcrLrnResDto.isAll0()) {
                aiSrTcrLrnResDto.getAiSrQtmDtoList().forEach(a -> {
                    if(Integer.parseInt(a.getCorrectCnt()) > 0) {
                        List<AiSrAchCmtDto> aiSrAchCmtDtos = map.get(a.getQpAchBsCd());
                        if(aiSrAchCmtDtos != null && !aiSrAchCmtDtos.isEmpty()) {
                            aiSrTcrLrnResDto.addComment(aiSrAchCmtDtos.get(0));
                            aiSrAchCmtDtos.remove(0);
                        }
                    }
                });
            } else {
                aiSrTcrLrnResDto.setComments(null);
                String collect = aiSrTcrLrnResDto.getAiSrQtmDtoList().stream().map(s -> String.format("'%s'", s.getLuLrmpNodNm())).distinct().collect(Collectors.joining(", "));
                AiSrAchCmtDto aiSrAchCmtDto = aiSrService.getAchCmt("00-00-00").get(0);
                if(aiSrAchCmtDto != null && !StringUtils.isEmpty(aiSrAchCmtDto.getAchBsCmt())) {
                    aiSrAchCmtDto.setAchBsCmt(aiSrAchCmtDto.getAchBsCmt().replace("#input#", collect));
                }
                aiSrTcrLrnResDto.addComment(aiSrAchCmtDto);
            }
        });

        return resultMap;
    }

    public AiSrUserDataResDto getUserFilePath(AiSrUserDataReqDto reqDto) {
        AiSrUserDataDto dto = aiSrService.getSrUserDataFilePath(reqDto);
        if(dto != null && StringUtils.isNotBlank(dto.getAnnxFleId())) {
            String cdnPath = dto.getAnnxFlePthNm();
            String jsonStr = BcCmUtil.s3JsonFileReaderString(BUCKET_NAME, cdnPath);
            return dto.toResDto(jsonStr);
        }
        return null;
    }

    public int insertUserFile(AiSrUserDataReqDto reqDto) {
        AiSrUserDataDto srUserFile = aiSrService.getSrUserFile(reqDto);
        if(srUserFile == null) {
            return aiSrService.insertSrUserFile(reqDto);
        } else{
            return aiSrService.updateSrUserFile(reqDto);
        }
    }
}
