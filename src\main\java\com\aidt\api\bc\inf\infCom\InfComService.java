package com.aidt.api.bc.inf.infCom;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcUserInfoDto;
import com.aidt.api.bc.inf.infCom.dto.InfComDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-02-11 09:30:00
 * @modify date 2025-02-11 09:30:00
 * @desc 알림공통 Service
 */

@Slf4j
@Service
public class InfComService {
	 
	private final String MAPPER_NAMESPACE = "api.bc.inf.com.";

    @Autowired
    private CommonDao commonDao;
    
    @Autowired
	private BcCmService bcCmService;
    
    
    
    /**
     * 다른학급저장 알림등록
     * @param dto
     * @return
     */
    @SuppressWarnings("unchecked")
	@Transactional
    public int insertInfmOtherCla(InfComDto dto) {
    	log.debug("InfComDto : {} ", dto);
    	int result = 0;    	
    	
    	if("CB".equals(dto.getInfmClCd())) {    		
    		// 학급게시판일때 id조회
    		String claId = commonDao.select(MAPPER_NAMESPACE + "selectClaId", dto);  
    		dto.setClaId(claId);
    	}    	
    	
    	Map<String, Object> userInfo = this.selectUserInfo(dto);				
		List<String> stuList = (List<String>) userInfo.get("stuList");
		String tcrUsrId = (String) userInfo.get("tcrUsrId");
		List<BcUserInfoDto> userList = (List<BcUserInfoDto>) userInfo.get("userList");    
		
		// 알림 메시지 설정
	    Map<String, String> infmData = getInfmMessage(dto, stuList, tcrUsrId);
	    if (infmData.isEmpty()) return 0;  
	    
	    // 알림 마스터 등록
	    dto.setInfmTpCd(infmData.get("infmTpCd"));
	    dto.setInfmNm(infmData.get("infmNm"));
	    dto.setInfmCn(infmData.get("infmCn"));
	    result += commonDao.insert(MAPPER_NAMESPACE + "insertInfmMst", dto);
		
	    // 알림 상세 등록
	    List<InfComDto> list = createInfmDtlList(dto, stuList, tcrUsrId, userList);
	    result += commonDao.insert(MAPPER_NAMESPACE + "insertInfmDtlOtherCla", list);
	    return result;
    }
   
    /**
     * 알림 메시지 설정
     * @param dto
     * @param stuList
     * @param tcrUsrId
     * @return
     */
    private Map<String, String> getInfmMessage(InfComDto dto, List<String> stuList, String tcrUsrId) {
        Map<String, String> map = new HashMap<>();
        if (stuList.isEmpty() && !"CB".equals(dto.getInfmClCd())) return map;

        switch (dto.getInfmClCd()) {
            case "SE":  // 평가
                map.put("infmTpCd", "LR");
                map.put("infmNm", "ER".equals(dto.getInfmDtlClCd()) ? "평가 등록" : "평가 수정");
                map.put("infmCn", "ER".equals(dto.getInfmDtlClCd()) ? "[평가] 새로운 평가가 등록되었습니다." : "[평가] 평가 내용이 변경되었습니다. 확인하세요.");
                break;

            case "CB":  // 학급게시판
                map.put("infmTpCd", "AN");
                map.put("infmNm", "학급게시판 등록");
                map.put("infmCn", "[필독] " + dto.getClaBlbdTitlNm());
                stuList.add(tcrUsrId);
                break;

            case "AS":  // 과제
                if ("AR".equals(dto.getInfmDtlClCd())) {
                    map.put("infmTpCd", "LR");
                    map.put("infmNm", "[과제] 새로운 과제 등록");
                    map.put("infmCn", "[일반 과제] 새로운 과제가 등록되었습니다.");
                } else {
                	// 과제 알림대상
                    List<String> asnInfmTrgtList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnInfmTrgt", dto);                    
                    if (!asnInfmTrgtList.isEmpty()) {
                        map.put("infmTpCd", "LR");
                        map.put("infmNm", "[과제] 내용 수정");
                        map.put("infmCn", "[일반 과제] 과제 내용이 변경되었습니다. 확인하세요.");
                    }
                }
                break;
        }
        return map;
    }
    

    /**
     * 알림 상세 정보 생성
     * @param dto
     * @param stuList
     * @param tcrUsrId
     * @param userList
     * @return
     */
    private List<InfComDto> createInfmDtlList(InfComDto dto, List<String> stuList, String tcrUsrId, List<BcUserInfoDto> userList) {
	    List<InfComDto> list = new ArrayList<>();
	    String infmMvCn = "";
	
	    switch (dto.getInfmClCd()) {
	        case "SE": // 평가
	            infmMvCn = "{\"filePath\":\"EA_EV_STU_MAIN\",\"layout\":\"default\",\"params\":{\"evId\":" + dto.getEvId() + "}}";
	            list.addAll(createInfmObjects(dto, stuList, tcrUsrId, infmMvCn));
	            break;
	
	        case "CB": // 학급게시판
	            for (BcUserInfoDto user : userList) {
	                InfComDto inf = new InfComDto();
	                inf.setInfmMsgId(dto.getInfmMsgId());
	                inf.setOptTxbId(dto.getOptTxbId());
	                inf.setTcrUsrId(tcrUsrId);
	                inf.setInfmObjUsrId(user.getUsrId());
	                inf.setUsrId(dto.getUsrId());
	                inf.setDbId(dto.getDbId());
	                inf.setInfmMvCn("TE".equals(user.getUsrTpCd()) 
	                    ? "{\"filePath\":\"BC_CLA_BLBD_TCR_DTL\",\"layout\":\"default\",\"params\":{\"claBlbdId\":" + dto.getClaBlbdId() + "}}"
	                    : "{\"filePath\":\"BC_CLA_BLBD_STU_DTL\",\"layout\":\"default\",\"params\":{\"claBlbdId\":" + dto.getClaBlbdId() + "}}");
	                list.add(inf);
	            }
	            break;
	
	        case "AS": // 과제
	            infmMvCn = "AR".equals(dto.getInfmDtlClCd()) 
	            		? "{\"filePath\":\"EA_ASN_STU_MAIN\",\"layout\":\"default\",\"params\":{\"asnId\":" + dto.getAsnId() + "}}"
	            		: "{\"filePath\":\"EA_ASN_GENERAL_STU_DETAIL\",\"layout\":\"default\",\"params\":{\"asnId\":" + dto.getAsnId() + "}}";
	            list.addAll(createInfmObjects(dto, stuList, tcrUsrId, infmMvCn));
	            break;
	    }
	    return list;
    }
	
	
    /**
     * 학생 대상 알림 객체 생성
     * @param dto
     * @param stuList
     * @param tcrUsrId
     * @param infmMvCn
     * @return
     */
	private List<InfComDto> createInfmObjects(InfComDto dto, List<String> stuList, String tcrUsrId, String infmMvCn) {
	    List<InfComDto> list = new ArrayList<>();
	    for (String st : stuList) {
	        InfComDto d = new InfComDto();
	        d.setInfmMsgId(dto.getInfmMsgId());
	        d.setOptTxbId(dto.getOptTxbId());
	        d.setTcrUsrId(tcrUsrId);
	        d.setInfmObjUsrId(st);
	        d.setUsrId(dto.getUsrId());
	        d.setInfmMvCn(infmMvCn);
	        list.add(d);
	    }
	    return list;
	}
	    
	/**
	 * 유저정보 조회
	 * @param dto
	 * @return
	 */
    public Map<String, Object> selectUserInfo(InfComDto dto) {
      	BcUserInfoDto bcInfo = new BcUserInfoDto();
      	
    	bcInfo.setClaId(dto.getClaId());
    	bcInfo.setOptTxbId(dto.getOptTxbId());    	
    	List<BcUserInfoDto> userList = bcCmService.selectStuInfoList(bcInfo);    	
    	
		List<String> stuList = userList.stream()
			    .filter(user -> "ST".equals(user.getUsrTpCd()))
			    .map(user -> user.getUsrId()) 
			    .collect(Collectors.toList()); 
		
		String tcrUsrId = userList.stream()
			    .filter(user -> "TE".equals(user.getUsrTpCd())) 
			    .map(user -> user.getUsrId()) 
			    .findFirst() 
			    .orElse(null);

		 Map<String, Object> returnMap = new HashMap<>();
		 returnMap.put("userList", userList);
		 returnMap.put("stuList", stuList);
		 returnMap.put("tcrUsrId", tcrUsrId);
		 return returnMap; 
    }
    
    
    /**
     * 평가 알림등록
     * @param dto
     * @return
     */
    public int insertEvInfm(InfComDto dto) {
    	log.debug("평가 등록/수정 입력 파라미터 :: {} ", dto);
    	int result = 0;
    	
    	String infmCn = "";
    	String infmMvCn = "";
    	if("ER".equals(dto.getInfmDtlClCd())) {    		
    		infmCn = "[평가] 새로운 평가가 등록되었습니다.";    	    	
    		dto.setInfmNm("평가 등록");
    	} else {    		
    		infmCn = "[평가] 평가 내용이 변경되었습니다. 평가 내용을 확인해 주세요.";    	    	
    		dto.setInfmNm("평가 수정");
    	}    	
    	infmMvCn = "{\"filePath\":\"EA_EV_STU_MAIN\",\"layout\":\"default\",\"params\":{\"evId\":" + dto.getEvId() + "}}";
    	dto.setInfmTpCd("LR");
		dto.setInfmClCd("SE");
		dto.setInfmCn(infmCn);
		dto.setInfmMvCn(infmMvCn);		
    	
    	result += commonDao.insert(MAPPER_NAMESPACE + "insertInfmMst", dto);    		    		    		
		result += commonDao.insert(MAPPER_NAMESPACE + "insertEvInfmDtl", dto);    
    	return result;
    }
    
    /**
     * 원클릭학습설정 > 콘텐트 일괄추천 알림등록
     * @param param
     * @return
     */
    @Transactional
    public int insertRcmCtnInfm(List<InfComDto> param) {
    	int result = 0;
    	for(InfComDto dto : param) {    		    	
    		log.debug("콘텐츠 일괄추천 입력 파라미터 : {} ", param);
    		result += commonDao.insert(MAPPER_NAMESPACE + "insertInfmMst", dto);    		    		    		
    		result += commonDao.insert(MAPPER_NAMESPACE + "insertRcmCtnInfmDtl", dto);    		
    	}    	
    	return result;
    }
    
    /**
     * 알림삭제
     * @param map
     * @return
     */
	public int deleteInfCom(Map<String, Object> map) {   		
		log.debug("알림 삭제 파라미터 : {} " , map);			
		int result = 0;
		result += commonDao.delete(MAPPER_NAMESPACE + "deleteInfComMst", Map.of("map", map));
		result += commonDao.delete(MAPPER_NAMESPACE + "deleteInfCom", Map.of("map", map));
		return result;
	}
}

