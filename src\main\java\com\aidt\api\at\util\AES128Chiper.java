package com.aidt.api.at.util;

import java.security.Key;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;

/**
 * 
 * <pre>
 * 
 * @Description: - Application context 선언   클래스 
 * 
 *               <pre>
 *
 * <AUTHOR> kim kyoung ju
 * @Date : 2021.11.26
 * @version : 1.0.0
 * 
 * 
 * 
 * 
 * 작성자 : 김한결
 * 내용 : 케리스에서 개발한 loginId 암호화 모듈
 * CS 요청 시 전달파라미터 값 암호화 필요하여 해당 모듈을 Keris 로부터 받아서 적용함.
 */

public class AES128Chiper {
	
	
	private final String key = "AidtEncAes!@2410"; 
	private final String iv = "2024101616591221"; 

	/**
	 * key generation
	 * 
	 * @return Key
	 * @throws Exception
	 */
	public Key getAESKey() throws Exception {
		String iv = "";		
		Key keySpec = null;

		String key = this.key;   //별도 제공 예정
		iv = key.substring(0, 16);
		byte[] keyBytes = new byte[16];
		byte[] b = key.getBytes("UTf-8");

		int len = b.length;
		if (len > keyBytes.length) {
			len = keyBytes.length;
		}

		System.arraycopy(b, 0, keyBytes, 0, len);
		keySpec = new SecretKeySpec(keyBytes, "AES");

		return keySpec;

	}

	/**
	 * aes 128 암호화
	 * 
	 * @param str
	 * @return String
	 * @throws Exception
	 */
	public String encAES(String str) throws Exception {
		Key keySpec = getAESKey();
		String iv = this.iv; //별도 제공 예정
		Cipher c = Cipher.getInstance("AES/CBC/PKCS5Padding");
		c.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv.getBytes("UTF-8")));
		byte[] encrypted = c.doFinal(str.getBytes("UTF-8"));
		String encStr = new String(Base64.encodeBase64(encrypted));
		return encStr;

	}

	public String decAES(String encStr) throws Exception {
		Key keySpec = getAESKey();
		String iv = this.iv; //별도 제공 예정
		Cipher c = Cipher.getInstance("AES/CBC/PKCS5Padding");
		c.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv.getBytes("UTF-8")));
		byte[] byteStr = Base64.decodeBase64(encStr.getBytes("UTF-8"));
		String decStr = new String(c.doFinal(byteStr), "UTF-8");
		return decStr;
	}

}
