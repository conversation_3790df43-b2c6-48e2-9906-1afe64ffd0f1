package com.aidt.api.ea.ev.tcr;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.al.cmt.tcr.AiCmtUsrDataTcrService;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.bc.inf.infCom.InfComService;
import com.aidt.api.bc.inf.infCom.dto.InfComDto;
import com.aidt.api.ea.evcom.EaEvComService;
import com.aidt.api.ea.evcom.dto.EaEvComQtmInfoDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdCstnDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvEvIdReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvTsRngeDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvUpdateTxmSetmDto;
import com.aidt.api.util.RedisUtil;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 평가 관리 - 교사 Service
 */
@Slf4j
@Service
public class EaEvTcrService {

	private final String MAPPER_NAMESPACE = "api.ea.ev.tcr.";
	private final String MAPPER_NAMESPACE_COMM = "api.ea.evcom.";
    private final String DOMAIN_PATH = "/api/v1/content/";
	
    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;

	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private JwtProvider jwtProvider;
	
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
	
	@Autowired
    EaEvComService eaEvComService;
	
	@Autowired
	AiCmtUsrDataTcrService aiCmtUsrDataTcrService;
	
	@Autowired
    private InfComService infComService;
	
    @Autowired
    private RedisUtil redisUtil;
    
    @Autowired
	private BcCmService bcCmService;
	
    public String getLocalDomain() {
        String domain = "";
		try {
				BUCKET_NAME = BUCKET_NAME.replace("dev", "prd");

				if("local".equals(SERVER_ACTIVE)) {
					domain = "https://www-n-ele.aitextbook.co.kr";
				}
		}
        catch (Exception e) {
        	log.warn(e.getMessage());
        }  

		return domain;
    }	
	
	/**
	 * 교사 - 평가 목록 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvMainResDto>
	 */
	public List<Map<String, Object>> selectEvList(EaEvMainReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvList", evReqDto);
	}

	/**
	 * 교사 - 평가 편집 목록 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvMainResDto>
	 */
	public List<EaEvMainResDto> selectEvEditList(EaEvMainReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvEditList", evReqDto);
	}
		
	/**
	 * 교사 - 평가 추가 - 평가정보, 문항리스트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public EaEvMainResDto selectEvAddQtmList(EaEvQtmIdReqDto evReqDto) {
		// 평가리포트 메인
		EaEvMainResDto evResDto = commonDao.select(MAPPER_NAMESPACE + "selectEvInfo", evReqDto);
		
		if(evResDto == null)
		{
			evResDto = new EaEvMainResDto();
		}
		
		//문항리스트
    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	evReqDto.setBucketUrl(objUrl);
    	
    	List<EaEvComQtmInfoDto> qtmIdListDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAddQtmList", evReqDto);
		evResDto.setQtmIdList(qtmIdListDto);

		return evResDto;
	}
	
	/**
	 * 교사 - 평가 추가 - 유사 문항리스트 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvQtmInfoDto>
	 */
	public List<EaEvComQtmInfoDto> selectSmlrQtmList(EaEvQtmIdReqDto evReqDto) {
		//문항리스트
    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	evReqDto.setBucketUrl(objUrl);

		return commonDao.selectList(MAPPER_NAMESPACE + "selectSmlrQtmList", evReqDto);
	}
	
	/**
	 * 교사 - 평가 추가/수정 - 학습수준별 학생정보 조회
	 *
	 * @param evReqDto
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> selectEvLrnLvStuList(EaEvEvIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvLrnLvStuList", evReqDto);
	}

	
	/**
	 * 교사 - 평가정보 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public EaEvMainResDto selectEvInfo(EaEvEvIdReqDto evReqDto) {
		// 평가리포트 메인
		return commonDao.select(MAPPER_NAMESPACE + "selectEvInfo", evReqDto);
	}
	
	/**
	 * 교사 - 평가 수정 - 평가정보/평가난이도정보/평가시험범위정보/평가문항정보 조회
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public EaEvMainResDto selectEvUpdateInfo(EaEvEvIdReqDto evReqDto) {
		//문항리스트
    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	evReqDto.setBucketUrl(objUrl);
    	
		// 평가 정보
        EaEvMainResDto evResDto = commonDao.select(MAPPER_NAMESPACE + "selectEvInfo", evReqDto);
		
		// 평가난이도정보
		//List<EaEvDffdCstnDto> dffdList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvDffdCstnInfo", evReqDto);
		List<EaEvDffdCstnDto> dffdList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvDffdCstnInfoByActiveQtm", evReqDto);
		evResDto.setDffdList(dffdList);
		// 평가시험범위정보
		List<EaEvTsRngeDto> tsRngeList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvTsRngeInfo", evReqDto);
		evResDto.setTsRngeList(tsRngeList);
		// 평가문항정보
		List<EaEvComQtmInfoDto> qtmIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmInfoList", evReqDto);
		evResDto.setQtmIdList(qtmIdList);
		
		return evResDto;
	}
	
	
	/**
	 * 교사 - 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public EaEvMainResDto selectEvRptList(EaEvEvIdReqDto evReqDto) {
		// 평가리포트 메인
		EaEvMainResDto evResDto = commonDao.select(MAPPER_NAMESPACE + "selectEvRptList", evReqDto);
		
		//우리반 오답 BEST
		List<EaEvRptUsrsQtmResDto> qtmIansBestListDto = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptIansBestNewList", evReqDto);
		evResDto.setQtmIansBestList(qtmIansBestListDto);

		//우리반 강점/약점
		List<EaEvRptUsrsQtmResDto> stpnWkpnListDto = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStpnWkpnList", evReqDto);
		evResDto.setStpnWkpnList(stpnWkpnListDto);
		
		//정오답현황
		List<EaEvRptUsrsQtmResDto> qtmOxListDto = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptOxList", evReqDto);
		evResDto.setQtmOxList(qtmOxListDto);

		//학생별현황
		List<EaEvRptUsrsQtmResDto> qtmUsrRptListDto = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUsersList", evReqDto);
		evResDto.setQtmUsrRptList(qtmUsrRptListDto);

		return evResDto;
	}
	/**
	 * 교사 - 수학 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
    public Map<String, Object> selectEvRptNewList(EaEvEvIdReqDto evReqDto) {
        // 평가리포트 메인
    	Map<String, Object> evResDto = commonDao.select(MAPPER_NAMESPACE + "selectEvRptList", evReqDto);
        
        String evDtlDvCd = "";
        if(evResDto != null && !evResDto.isEmpty())
        {
            evDtlDvCd = evResDto.get("evDtlDvCd").toString();
        }
        
        //정오답현황
        evResDto.put("qtmOxList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptOxList", evReqDto));

        
        //내용체계 영역별 현황 > 수학(학기초) 해당
        if(evDtlDvCd.equals("ST")) {
            //교육표준체계 내용영역별 현황
        	evResDto.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEduAreaList", evReqDto));
        }
        //주제유형별 분석 현황 > 수학(단원진단) 해당 => 내용체계 영역별과 화면 동일 (토픽으로 조회)
        else if(evDtlDvCd.equals("UD")) {
            //주제유형별 분석 현황 =>
        	evResDto.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEduAreaThemeTpcList", evReqDto));
        }
        //단원평가 전용 > 단원성취도 분석 > 수학 (단원평가)
        else if(evDtlDvCd.equals("UG")) {
            //차시별 분석
        	evResDto.put("ugAchdChList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUgAchdChList", evReqDto));
            //성취기준별 분석
            evResDto.put("ugAchdBsList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUgAchdBsList", evReqDto));
        }
        //학기말 전용 > 나의학습성장 그래프 > 수학/영어 (학기말)  > 내용체계영역의 학기초/학기말 정답률 그래프
        else if(evDtlDvCd.equals("ET")) {
            List<Map<String, Object>> myLrnGrthList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptMyLrnGrtMaList", evReqDto);
            //교육표준체계 내용영역별 현황 - 수학만
            evResDto.put("eduAreaList", myLrnGrthList);
            //나의학습성장 그래프 - 수학/영어
            evResDto.put("myLrnGrthList", myLrnGrthList);
        }
        
        // 학생별, 우리반 오답BEST는 학기말 제외
        if(!evDtlDvCd.equals("ET")) {
            //학생별현황
        	evResDto.put("qtmUsrRptList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUsersList", evReqDto));
            //우리반 오답 BEST
        	evResDto.put("qtmIansBestList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptIansBestNewList", evReqDto));
        }

        return evResDto;
    }	
	/**
	 * 교사 - 수학 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public Map<String, Object> selectEvRptMaList(EaEvEvIdReqDto evReqDto) {
		Map<String, Object> rptInfo = new HashMap<>();
		
    	//평가 정보 조회
        Map<String, Object> evInfo = commonDao.select(MAPPER_NAMESPACE + "selectEvRptList", evReqDto);

        //평가정보
        rptInfo.put("evInfo", evInfo);
        
        String evDtlDvCd = "";
    	if(evInfo != null && !evInfo.isEmpty())
		{
    		if(evInfo.get("evDvCd").toString().equals("SE")) {    			
	    		evDtlDvCd = evInfo.get("evDtlDvCd").toString();
    			evReqDto.setTrmDvCd(evInfo.get("trmDvCd").toString());
	    		
	    		
		    	//내용체계 영역별 현황 > 수학(학기초) 해당
		    	if(evDtlDvCd.equals("ST")) {
		        	////교육표준체계 내용영역별 현황
		    		//rptInfo.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEduAreaList", evReqDto));
		        	//문항플랫폼 내용영역별 현황
		    		rptInfo.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEduAreaQpList", evReqDto));
		    	}
		    	// 20240809 단원진단 주제유형별은 토픽매핑 불가하여 제거됨
//		    	//주제유형별 분석 현황 > 수학(단원진단) 해당 => 내용체계 영역별과 화면 동일 (토픽으로 조회)
//		    	else if(evDtlDvCd.equals("UD")) {
//		        	//주제유형별 분석 현황 =>
//		    		rptInfo.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEduAreaThemeTpcList", evReqDto));
//				}
		    	//단원평가 전용 > 단원성취도 분석 > 수학 (단원평가)
		    	else if(evDtlDvCd.equals("UG")) {
		        	//차시별 분석
		    		rptInfo.put("ugAchdChList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUgAchdChList", evReqDto));
		        	//성취기준별 분석
		    		rptInfo.put("ugAchdBsList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUgAchdBsList", evReqDto));
		    	}
		    	//학기말 전용 > 나의학습성장 그래프 > 수학/영어 (학기말)  > 내용체계영역의 학기초/학기말 정답률 그래프
		    	else if(evDtlDvCd.equals("ET")) {
		    		//교육표준체계 내용영역별 현황
		    		rptInfo.put("eduAreaList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEduAreaQpList", evReqDto));

		    		//나의학습성장 그래프 
		        	rptInfo.put("myLrnGrthList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptMyLrnGrtMaList", evReqDto));
		    	}
    		}
		}    	
    	
    	

		return rptInfo;
	}
	
    /**
     * 교사 - 평가 리포트 > 단원평가 > 단원성취도 조회 요청
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> selectEvRptUgAchdList(EaEvEvIdReqDto evReqDto) {
        if(evReqDto.getTabIndex() == 0)
        {
        	//차시별 분석
        	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUgAchdChList", evReqDto);
        }
        else
        {
        	//성취기준별 분석
        	return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUgAchdBsList", evReqDto);
        }
    }	
	
	
	/**
	 * 교사 - 영어 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public Map<String, Object> selectEvRptEnList(EaEvEvIdReqDto evReqDto) {
		Map<String, Object> rptInfo = new HashMap<>();
		
    	//평가 정보 조회
        Map<String, Object> evInfo = commonDao.select(MAPPER_NAMESPACE + "selectEvRptList", evReqDto);
        
        //평가정보
        rptInfo.put("evInfo", evInfo);
        
        String evDtlDvCd = "";
    	if(evInfo != null && !evInfo.isEmpty())
		{
    		if(evInfo.get("evDvCd").toString().equals("SE")) {  
    			evDtlDvCd = evInfo.get("evDtlDvCd").toString();
    			evReqDto.setTrmDvCd(evInfo.get("trmDvCd").toString());
    			
    			//영역별 분석 > 영어 (학기초, 차기평가, 단원평가, 학기말)
		    	rptInfo.put("enArelyAnList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptEnArelyAnList", evReqDto));
		
		    	//학기말 전용 > 나의학습성장 그래프 > 수학/영어 (학기말)  > 내용체계영역의 학기초/학기말 정답률 그래프
		    	if(evDtlDvCd.equals("ET")) {
		        	//나의학습성장 그래프
		        	rptInfo.put("myLrnGrthList", commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptMyLrnGrthEnList", evReqDto));
		    	}    		
    		}	
    	
		}    	
    	
   	
    	
		return rptInfo;
	}
	
	/**
	 * 교사 - 평가 리포트 - 학생별 정오답현황 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvRptUsrsQtmResDto>
	 */
	public List<EaEvRptUsrsQtmResDto> selectEvRptOXList(EaEvEvIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptOxList", evReqDto);
	}
	
	/**
	 * 교사 - 평가 리포트 - 학생별 정오답현황 NEW 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvRptUsrsQtmResDto>
	 */
	public Map<String, Object> selectEvRptOxNewList(EaEvEvIdReqDto evReqDto) {
		Map<String, Object> res = new HashMap<>();
		List<EaEvRptUsrsQtmResDto> clsAvgList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptOxClsAvgList", evReqDto);
		
		res.put("evId", evReqDto.getEvId());
		res.put("clsAvgList", clsAvgList);
		
		List<EaEvRptUsrsQtmResDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptOxStuList", evReqDto);
		
		Map<String, List<EaEvRptUsrsQtmResDto>> group = list.stream().collect(Collectors.groupingBy(EaEvRptUsrsQtmResDto::getUsrId));
		Collection<List<EaEvRptUsrsQtmResDto>> values = group.values();
		List<List<EaEvRptUsrsQtmResDto>> valueList = new ArrayList<List<EaEvRptUsrsQtmResDto>>(values);
		
		// 학생 rowNo별 정렬
		valueList.sort((a,b) -> {
			int rowNoA = a.get(0).getRowNo();
			int rowNoB = b.get(0).getRowNo();
			return Integer.compare(rowNoA, rowNoB);
		});
		
		res.put("stuList", valueList);
		
		return res;
	}
	
	/**
	 * 교사 - 평가 리포트 - 학생별현황 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvRptUsrsQtmResDto>
	 */
	public List<EaEvRptUsrsQtmResDto> selectEvRptUsrRptList(EaEvEvIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptUsersList", evReqDto);
	}
	
	/**
	 * 교사 - 평가 리포트 - 우리반 오답 BEST NEW 조회 요청
	 *
	 * @param evReqDto
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> selectEvRptIansBestNewList(EaEvEvIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptIansBestNewList", evReqDto);
	}

	/**
	 * 교사 - 평가 리포트 - 우리반 오답 BEST 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvRptUsrsQtmResDto>
	 */
	public List<EaEvRptUsrsQtmResDto> selectEvRptIansBestList(EaEvEvIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptIansBestList", evReqDto);
	}	

	/**
	 * 교사 - 평가 리포트 - 우리반 강점/약점 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvRptUsrsQtmResDto>
	 */
	public List<EaEvRptUsrsQtmResDto> selectEvRptStpnWkpnList(EaEvEvIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvRptStpnWkpnList", evReqDto);
	}

	/**
	 * 교사 - 학습창 연동 - 학습창 진입하여 문제풀이 시 학생들의 모니터링 정보 조회
	 *
	 * @param evReqDto
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> selectEvTcrLwStusSmtAnwList(EaEvQtmIdReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvTcrLwStusSmtAnwList", evReqDto);
	}	

	/**
	 * 교사 - 평가 추가, 수정 시 다른 학급 정보 조회
	 *
	 * @param evReqDto
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> selectEvTcrMyClsInfoList(EaEvMainReqDto evReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvTcrMyClsInfoList", evReqDto);
	}	
	
	
	
	
	/**
	 * 교사 - 평가 정보 등록 (교사평가, DIY평가)
	 *
	 * @param evReqDto
	 * @return Map<String, Object>
	 */
	@Transactional
	public Map<String, Object> insertEv(EaEvSaveReqDto evReqDto) {
		// 2024.12.12 null 체크 추가 IllegalArgumentException
		Map<String, Object> map = new HashMap<>();
		
		map.put("status", "400");
		if(evReqDto == null) {
			map.put("message", "등록할 평가 정보가 없습니다.");
			return map;
		}
		
		if(evReqDto.getDffdList() == null || evReqDto.getDffdList().isEmpty()) {
			map.put("message", "등록할 평가 난이도 정보가 없습니다.");
			return map;
		}
		
		if(evReqDto.getTsRngeList() == null || evReqDto.getTsRngeList().isEmpty()) {
			map.put("message", "등록할 평가 시험범위 정보가 없습니다.");
			return map;
		}		
		
		if(evReqDto.getQtmIdList() == null || evReqDto.getQtmIdList().isEmpty()) {
			map.put("message", "등록할 평가 문항 정보가 없습니다.");
			return map;
		}
		
		if(evReqDto.getStuList() == null || evReqDto.getStuList().isEmpty()) {
			map.put("message", "등록할 평가 대상 정보가 없습니다.");
			return map;
		}	
		
		int res = commonDao.insert(MAPPER_NAMESPACE + "insertEv", evReqDto);
		map.put("evId", evReqDto.getEvId());
	
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvDffd", evReqDto);
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRnge", evReqDto);
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvQtm", evReqDto);
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvRs", evReqDto);

		map.put("status", "200");
		map.put("message", "평가가 저장되었습니다.");

		return map;
	}
	
	/**
	 * 교사 - 평가 응시설정 수정 요청
	 *
	 * @param evReqDto
	 * @return int
	 */
	public int updateEvTxmSetm(EaEvUpdateTxmSetmDto evReqDto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateEvTxmSetm", evReqDto);
	}
	
	/**
	 * 교사 - 평가 정보 업데이트 요청 (교사평가)
	 *
	 * @param evReqDto
	 * @return int
	 */
	@Transactional
	public int updateEv(EaEvSaveReqDto evReqDto) {
	
		int res = commonDao.update(MAPPER_NAMESPACE + "updateEv", evReqDto);
		
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvDffd", evReqDto);
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvTsRnge", evReqDto);
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvQtm", evReqDto);
		
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvDffd", evReqDto);
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRnge", evReqDto);
		res += commonDao.insert(MAPPER_NAMESPACE + "insertEvQtm", evReqDto);
		
		return res;
		
	}
	

	/**
	 * 교사 - 평가 정보 삭제 (교사평가)
	 *
	 * @param evReqDto
	 * @return int
	 */
	@Transactional
	public Map<String,String> deleteEv(EaEvEvIdReqDto evReqDto) {
		Map<String, String> map = new HashMap<>();
		map.put("status", "400");
		
		EaEvMainResDto evResDto = commonDao.select(MAPPER_NAMESPACE + "selectEvInfo", evReqDto);
		if( evResDto == null) {
			map.put("message", "평가 정보를 확인할 수 없습니다.");
			return map;
		}
		
		if( !"TE".equals(evResDto.getEvDvCd()) ) {
			map.put("message", "교사 평가만 삭제 가능합니다.");
			return map;
		}
		
		if( evResDto.getTxmCmplCnt() > 0 ) {
			map.put("message", "평가 응시완료 학생이 존재합니다.");
			return map;
		}
		
		int res = 0;
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvDffd", evResDto);  // 문제 난이도
    	res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvTsRnge", evResDto);// 문제 범위
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvQtm", evResDto);   // 문제
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEaEvRs", evResDto);  // 문제결과
		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEaEv", evResDto);	   // 평가
		
		map.put("status", "200");
		map.put("message", "평가가 삭제 되었습니다.");
		
		return map;
	}
	
	
	/**
	 * 교사 - 평가 정보 업데이트 요청 (교사평가)
	 *
	 * @param evReqDto
	 * @return List<Map<String, Object>>
	 */
	@Transactional
	public List<Map<String, Object>> saveOtherClaEv(EaEvSaveReqDto evReqDto) {
		
		int res = 0;

		long currEvId = evReqDto.getEvId();
		long otherEvId = 0L;
		
		List<Map<String, Object>> evIdList = new ArrayList<Map<String, Object>>();
		
		if(evReqDto.getOtherClaType() == null) {
			return evIdList;
		}
		for (EaEvSaveReqDto dto : evReqDto.getOtherClaList()) 
		{			
			if(dto.getOtherOptTxbId() == null || "".equals(dto.getOtherOptTxbId())){
				return evIdList;
			}
		}
		
		
		String otherClaType = evReqDto.getOtherClaType();
		Map<String, Object> mapEv = new HashMap<>();
		
		for (EaEvSaveReqDto dto : evReqDto.getOtherClaList()) 
		{
			mapEv = null;
			otherEvId = 0L;
			Map<String, Object> resultMap = new HashMap<>();
			resultMap.put("optTxbId", dto.getOtherOptTxbId()); // 운영교과서ID 추가
			
			evReqDto.setOtherOptTxbId(dto.getOtherOptTxbId());
			evReqDto.setOtherTxbId(dto.getOtherTxbId());
			evReqDto.setOtherClaId(dto.getOtherClaId()); 
			
			if("TE".equals(evReqDto.getEvDvCd())) 
			{
				mapEv = commonDao.select(MAPPER_NAMESPACE + "selectEvTcrOtherClaEvInfoTe", evReqDto);
			}
			else if("SE".equals(evReqDto.getEvDvCd())) 
			{
				mapEv = commonDao.select(MAPPER_NAMESPACE + "selectEvTcrOtherClaEvInfoSe", evReqDto);
			}
			if(mapEv != null) 
			{
				otherEvId = (long) mapEv.get("otherEvId");
			}
			
			if(otherEvId > 0L) 
			{
				evReqDto.setOtherEvId(otherEvId);
				evReqDto.setEvId(currEvId);
				
				res += commonDao.update(MAPPER_NAMESPACE + "updateEvOtherCla", evReqDto);
				resultMap.put("evId", otherEvId); // 수정된 evID 추가
				
				Map<String, Object> map = new HashMap<>();
				map.put("evId", evReqDto.getEvId());
				
				if("qtm".equals(otherClaType)) {
					res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvDffdOtherCla", evReqDto);
					res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvTsRngeOtherCla", evReqDto);
					res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvQtmOtherCla", evReqDto);
					
					res += commonDao.insert(MAPPER_NAMESPACE + "insertEvDffdOtherCla", evReqDto);
					res += commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRngeOtherCla", evReqDto);
					res += commonDao.insert(MAPPER_NAMESPACE + "insertEvQtmOtherCla", evReqDto);

				}
				// 알림등록(평가 수정)
				this.insertEvInfm(evReqDto,"EM");
			}	
			else 
			{
				
				evReqDto.setOtherEvId(currEvId);// 원본 평가ID 추가 
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvOtherCla", evReqDto);
				resultMap.put("evId", evReqDto.getEvId()); // 알림용 등록된 evID 추가
				
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvRsOtherCla", evReqDto);
			
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvDffd", evReqDto);
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRnge", evReqDto);
				res += commonDao.insert(MAPPER_NAMESPACE + "insertEvQtm", evReqDto);
				
				// 알림등록(평가 등록)
				this.insertEvInfm(evReqDto,"ER");
				
			}
			
			CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        	
        	CmClaCpLogDto logDto = CmClaCpLogDto.builder()
        						   .optTxbId(userDetails.getOptTxbId()) // 로그인한 운영교과서ID
        						   .cpOptTxbId(dto.getOtherOptTxbId()) // 수정하는 운영교과서ID
        						   .cpDvCd("EV")
        						   .cpPrcsYn("Y")
        						   .backendFlePth("EaEvTcrService.saveOtherClaEv")
        						   .kerisUsrId(userDetails.getKerisUsrId())
        						   .crtrId(userDetails.getUsrId())
        						   .build();
        	
        	bcCmService.insertClaCpLog(logDto);
			
			evIdList.add(resultMap); // list에 추가
		}
		
//		// 현재평가(복제대상) CLA_EV_ID 업데이트
//		evReqDto.setEvId(currEvId);
//		res += commonDao.update(MAPPER_NAMESPACE + "updateEvClaEvId", evReqDto);
		
		return evIdList;				
	}
	
	/**
	 * 평가 다른학급 저장시 알림추가
	 * @param evReqDto
	 * @param infmDtlClCd
	 */
	public void insertEvInfm(EaEvSaveReqDto evReqDto, String infmDtlClCd) {
		InfComDto infComDto = new InfComDto();
		infComDto.setInfmClCd("SE");
		infComDto.setInfmDtlClCd(infmDtlClCd);
		infComDto.setOptTxbId(evReqDto.getOtherOptTxbId());		
		infComDto.setEvId("EM".equals(infmDtlClCd) ? evReqDto.getOtherEvId() : evReqDto.getEvId());		
		infComDto.setClaId(evReqDto.getOtherClaId());
		infComDto.setUsrId(evReqDto.getUsrId());
		infComService.insertInfmOtherCla(infComDto);
	}
	
	/**
	 * 교사 - 학생 평가결과 초기화
	 *
	 * @param List<evReqDto>
	 * @return int
	 */
	
	public Map<String,String> updateEvRs(List<EaEvEvIdReqDto> evReqDtoList) {
		Map<String,String> result = new HashMap<>(); 
		 
		if (evReqDtoList.isEmpty()) {
			result.put("status","400");
			result.put("massage","잘못된 요청입니다.");
            return result;
        }
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
			
		EaEvQtmIdReqDto dto = new EaEvQtmIdReqDto();
		dto.setEvId(evReqDtoList.get(0).getEvId()); // TODO 평가ID
		dto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		dto.setUsrId(userDetails.getUsrId()); // TODO API 호출 선생님ID
		
		Map<String, Object> evInfo = commonDao.select(MAPPER_NAMESPACE + "selectEaEvInfo", dto);
		
        if(evInfo.isEmpty()) {
        	result.put("status","204");
			result.put("massage","평가 정보를 확인할 수 없습니다.");
			return result;
        } 
        
        String rtxmPmsnYn = evInfo.get("rtxmPmsnYn").toString(); // 재응시 여부
        String evDvCd = evInfo.get("evDvCd").toString();   // 평가 종류 SE 교과평가, TE 선생님평가
        String evDtlDvCd = evInfo.get("evDtlDvCd").toString();   // 교과평가 종류 
        
        int res = 0;
        
        for(EaEvEvIdReqDto evReqDto :  evReqDtoList) {
        	evReqDto.setOptTxbId(userDetails.getOptTxbId());
        	res += initEaEvRs(evReqDto, rtxmPmsnYn, evDvCd, evDtlDvCd);
        }
        
    	result.put("status","200");
		return result;
	}
	
	/**
	 * 교사 - 평가리포트 평가결과 초기화
	 * @param evReqDto
	 * @return int
	 */
	@Transactional
	public int initEaEvRs(EaEvEvIdReqDto evReqDto, String rtxmPmsnYn, String evDvCd, String evDtlDvCd) {
		int res = 0;

		res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvQtmAnw", evReqDto); // 평가 문항답변 초기화
		
		if("Y".equals(rtxmPmsnYn)) { // 재응시 여부
	        res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvRsRtxm", evReqDto); // 평가 재응시 결과 초기화
	        res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvQtmAnwRtxm", evReqDto); // 평가 재응시 문항답변 초기화
		}

		if("FO".equals(evDtlDvCd) || "TO".equals(evDtlDvCd) ){ // 형성평가, 차시평가
	        res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvSppNtnRs", evReqDto); // 평가 보충심화 결과 초기화
	        res += commonDao.delete(MAPPER_NAMESPACE + "deleteEvSppNtnQtmAnw", evReqDto); // 평가 보충심화 문항답변 초기화
		}

        res += commonDao.update(MAPPER_NAMESPACE + "updateEvRs", evReqDto); // 평가결과 초기화
        
        // 유저테이블 학습자 수준 컬럼 업데이트
		eaEvComService.updateUsrLrnrVelTpCd(evReqDto.getUsrId());
		
		// 단원별 학습자 수준 테이블 업데이트
    	Map<String, Object> luLv = new HashMap<>();
    	luLv.put("usrId", evReqDto.getUsrId());
    	luLv.put("optTxbId", evReqDto.getOptTxbId());
    	luLv.put("dbId", DB_ID);
		
		List<Map<String, Object>> luLvList = new ArrayList<>();
		luLvList = eaEvComService.selectLluLrnrLvList(luLv);
    	if(null != luLvList) {
    		luLv.put("luLvList", luLvList);
			eaEvComService.updateTlLuLrnrLv(luLv);
    	}
        
        
        if("SE".equals(evDvCd)) {
        	// 해당 학생의 ai 코멘트를 초기화시킨다
            aiCmtUsrDataTcrService.deleteAiCmtUsrData(Long.toString(evReqDto.getEvId()), evReqDto.getUsrId(), "ST");
        	
        	// 학습목차 select 캐시초기화
        	String searchKey = "longCache:tl:" + evReqDto.getOptTxbId() + ":txbTcList:" + evReqDto.getUsrId();
    	    redisUtil.redisKeyDeleteArray(searchKey);
        } 
			
		return res;
	}


	// 신규 학생 > 기존 평가 반영
	@Transactional
	public int insertEvRsNewStu(EaEvSaveReqDto eaEvSaveReqDto) {
		int cnt = commonDao.insert(MAPPER_NAMESPACE +"insertEvRsNewStu" , eaEvSaveReqDto);
		log.debug("##### 신규 학생 평가 등록 건수 = " + String.valueOf(cnt));

		return cnt;
	}

}
