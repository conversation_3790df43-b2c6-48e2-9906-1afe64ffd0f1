package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSbcLrnAtvWbDto {
	/** 차시노드ID(차시) */
    @Parameter(name="차시노드ID")
    private String lrmpNodId;
    /** 활동ID */
    @Parameter(name="활동ID")
    private String lrnAtvId;
    /** 총건수 */
    @Parameter(name="총건수")
    private String wkbTotCnt;
    /** 완료건수 */
    @Parameter(name="완료건수")
    private String wkbFinCnt;
    /** 진행상태코드 */
    @Parameter(name="진행상태코드")
    private String wkbLrnStCd;
    /** 표시여부 */
    @Parameter(name="표시여부")
    private String showYn;
    /** 표시여부 */
    @Parameter(name="총 학습 시간")
    private String lrnTmScnt;
}
