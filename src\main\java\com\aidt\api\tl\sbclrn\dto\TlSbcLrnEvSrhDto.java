package com.aidt.api.tl.sbclrn.dto;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:53:20
 * @modify date 2024-01-05 10:53:20
 * @desc TlSbcLrnEvSrhDto 교과학습 평가현황 조회 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlSbcLrnEvSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 교사 ID */
    @Parameter(name="교사 ID")
    private String tcrUsrId;

    /** 학생 ID */
    @Parameter(name="학생 ID")
    private String usrId;

    /** 평가 ID */
    @NotNull(message = "{field.required}")
    @Parameter(name="평가 ID", required = true)
    private int evId;
}
