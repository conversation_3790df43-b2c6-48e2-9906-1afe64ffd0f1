package com.aidt.api.tl.sbclrn.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-12 16:20:19
 * @modify date 2024-04-12 16:20:19
 * @desc TlSbcLrnAtvEvDto 교과학습 활동상태 평가정보
 */
@Data
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvEvDto {

    /** 차시노드ID(차시) */
    @Parameter(name="차시노드ID")
    private String lrmpNodId;
    /** 활동ID */
    @Parameter(name="활동ID")
    private String lrnAtvId;
    /** 학습상태코드 */
    @Parameter(name="학습상태코드")
    private String lrnStCd;
    /** 평가ID */
    @Parameter(name="평가ID")
    private String evId;
    /** 평가상세구분코드 */
    @Parameter(name="평가상세구분코드")
    private String evDtlDvCd;
    /** 평가상세구분코드명 */
    @Parameter(name="평가상세구분코드명")
    private String evDtlDvNm;
    /** 평가명 */
    @Parameter(name="평가명")
    private String evNm;
    /** 최종문제수 */
    @Parameter(name="최종문제수")
    private String fnlQstCnt;
    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;
    /** 재응시허용여부 */
    @Parameter(name="재응시허용여부")
    private String rtxmPmsnYn;
    /** 풀이시간초수 */
    @Parameter(name="풀이시간초수")
    private String xplTmScnt;
    /** 풀이시간설정여부 */
    @Parameter(name="풀이시간설정여부")
    private String xplTmSetmYn;
    /** 평가완료여부 */
    @Parameter(name="평가완료여부")
    private String evCmplYn;
    /** 추가평가ID */
    @Parameter(name="추가평가ID")
    private String extrEvId;
    /** 평가문항목록 */
    @Parameter(name="평가문항목록")
    private List<TlSbcLrnAtvEvQtmDto> evQtmList;
    /** 보충심화 문항목록 */
    @Parameter(name="보충심화 문항목록")
    private List<TlSbcLrnAtvEvQtmDto> extrEvQtmList;
}
