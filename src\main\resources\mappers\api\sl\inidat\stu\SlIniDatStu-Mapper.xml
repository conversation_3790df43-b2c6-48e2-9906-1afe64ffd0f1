<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.sl.inidat.stu">
    <!-- 학생, 특별학습 정보 -->
    <select id="selectSpLrnList" parameterType="com.aidt.api.sl.inidat.dto.SlIniDatCondDto" resultType="Map">
       SELECT
            IFNULL(U.LRNR_VEL_TP_CD, 'BA') AS LRNR_VEL_TP_CD,
            IFNULL(S.SP_LRN_ID,'') AS SP_LRN_ID,
            IFNULL(S.SP_LRN_DFFD, '00') AS SP_LRN_DFFD,
            IFNULL(R.RCM_YN, '') AS RCM_YN
        FROM LMS_LRM.CM_USR U   /* CM_유저 */
        LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN S /* SL_특별학습재구성 */ 
            ON S.OPT_TXB_ID = #{optTxbId}
            AND S.USE_YN = 'Y'
        LEFT JOIN LMS_LRM.SL_STU_RCM_LRN R /* SL_학생별추천학습 */
            ON S.OPT_TXB_ID = R.OPT_TXB_ID
            AND S.SP_LRN_ID = R.SP_LRN_ID
            AND U.USR_ID = R.LRN_USR_ID
        WHERE U.USR_ID = #{stuUsrId}
        AND S.SP_LRN_ID IS NOT NULL
        AND IFNULL(RCM_YN, '') = ''

        /** 특별학습 김형준 SlIniDatStu-Mapper.xml - selectSpLrnList */
    </select>
    
    <select id="selectSlCnt" parameterType="com.aidt.api.sl.inidat.dto.SlIniDatCondDto" resultType="int">
      	SELECT COUNT(*) 
      	FROM LMS_LRM.SL_SP_LRN_RCSTN 
      	WHERE OPT_TXB_ID = #{optTxbId}
        AND USE_YN = 'Y'

        /** 특별학습 김형준 SlIniDatStu-Mapper.xml - selectSlCnt */
    </select>

    <!-- 추천학습 등록 -->
    <insert id="insertRcmLrnDat" parameterType="com.aidt.api.sl.inidat.dto.SlIniDatRcmLrnDto">
        INSERT INTO LMS_LRM.SL_STU_RCM_LRN  /* SL_학생별추천학습 */
        (OPT_TXB_ID     /* 운영교과서ID */
        ,SP_LRN_ID      /* 특별학습ID */
        ,LRN_USR_ID     /* 학습사용자ID */
        ,RCM_YN         /* 추천여부 */
        ,CRTR_ID        /* 생성자ID */
        ,CRT_DTM        /* 생성일시 */
        ,MDFR_ID        /* 수정자ID */
        ,MDF_DTM        /* 수정일시 */
        ,DB_ID          /* 데이터베이스ID */
        )
        VALUES
        (#{optTxbId}
        ,#{spLrnId}
        ,#{lrnUsrId}
        ,#{rcmYn}
        ,#{crtrId}
        ,NOW()
        ,#{mdfrId}
        ,NOW()
        ,#{dbId})
        ON DUPLICATE KEY UPDATE
         RCM_YN = #{rcmYn}
        ,MDFR_ID = #{lrnUsrId}
        ,MDF_DTM = NOW()



        /** 특별학습 김형준 SlIniDatTcr-Mapper.xml - insertRcmLrnDat */
    </insert>

</mapper>