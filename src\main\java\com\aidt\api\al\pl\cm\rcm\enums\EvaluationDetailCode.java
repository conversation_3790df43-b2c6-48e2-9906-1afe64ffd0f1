package com.aidt.api.al.pl.cm.rcm.enums;

import java.util.Arrays;
import java.util.EnumSet;

import lombok.Getter;

@Getter
public enum EvaluationDetailCode {

	C1("C1", "맞춤학습1"),
	C2("C2", "맞춤학습2"),
	C3("C3", "집중학습"),
	DE("DE", "심화평가(심화학습)"),
	ET("ET", "학년/학기 말 평가"),
	FO("FO", "형성 평가"),
	IS("IS", "오답유사"),
	OV("OV", "AI 맞춤 진단"),
	SI("SI", "유사평가(보충학습)"),
	ST("ST", "학년/학기 초 진단"),
	TO("TO", "차시 평가"),
	UD("UD", "단원 진단"),
	UG("UG", "단원 평가");

	private final String code;
	private final String desc;

	EvaluationDetailCode(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static EvaluationDetailCode getEvaluationDetailCode(String code) {
		return Arrays.stream(values())
			.filter(x -> x.getCode().equalsIgnoreCase(code))
			.findAny()
			.orElse(null);
	}

	public boolean isNationalStandard() {
		return EnumSet.of(EvaluationDetailCode.FO, EvaluationDetailCode.TO).contains(this);
	}

	public static boolean isNationalStandard(String code) {
		var evaluationDetailCode = getEvaluationDetailCode(code);
		return evaluationDetailCode != null && evaluationDetailCode.isNationalStandard();
	}

}
