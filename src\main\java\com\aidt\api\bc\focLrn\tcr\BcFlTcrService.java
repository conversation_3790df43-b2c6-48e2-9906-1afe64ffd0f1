package com.aidt.api.bc.focLrn.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.common.CommonDao;

import com.aidt.api.bc.focLrn.dto.BcFlDto;

@Service
public class BcFlTcrService {
	
	private final String MAPPER_NAMESPACE = "api.bc.focLrn.";

    @Autowired
    private CommonDao commonDao;
    
    /**
     * 집중학습 내용 저장
     * 
     * @param saveDto 저장데이터 목록
     * @return
     */
    @Transactional
    public int saveFocLrnInfo(BcFlDto saveDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "saveFocusLrnInfo", saveDto);
    }
    
    /**
     * 집중학습 내용 조회
     * 
     * @param 운영교과 아이디
     * @return
     */
    @Transactional
    public BcFlDto selectFocLrnInfo(BcFlDto bcflDto) {
    	return commonDao.select(MAPPER_NAMESPACE + "selectFocLrnInfo",  bcflDto);
    }
}
