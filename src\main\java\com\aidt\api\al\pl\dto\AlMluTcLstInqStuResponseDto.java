package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc [AI맞춤학습 단원차시조회 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlMluTcLstInqStuResponseDto {
	
	@Parameter(name="지식맵 중단원 노드 ID")	//학습맵
	private String lrmpNodId;

	@Parameter(name="지식맵 중단원 노드명")
	private String lrmpNodNm;

	@Parameter(name="지식맵 대단원 노드 ID")
	private String lluLrmpNodId;

	@Parameter(name="지식맵 대단원 노드명")
	private String lluLrmpNodNm;

	@Parameter(name="차시 사용여부")
	private String tcUseYn;

	@Parameter(name="평가 ID")
	private String evId;

	@Parameter(name="평가 상세코드")
	private String evDtlDvCd;

	@Parameter(name="평가 상세이름")
	private String evCmplYnNm;

	@Parameter(name="평가 완료여부")
	private String evCmplYn;

	@Parameter(name="맞힌 정답수")
	private int cansCnt;

	@Parameter(name="학습자 수준")
	private String lrnrVelTpCd;

	@Parameter(name="학습일자")
	private String mdfDtm;

	@Parameter(name="토픽숙련도")
	private String TpcAvn;

	@Parameter(name="평가구분코드")
	private String evDvCd;

	@Parameter(name="운영교과서Id")
	private String optTxbId;

	@Parameter(name="학습맵 중단원 노드 ID")	//학습맵
	private String kmmpNodId;

	@Parameter(name="지식맵 중단원 노드명")	//학습맵
	private String kmmpNodNm;

	@Parameter(name="지식맵 대단원 노드Id")	//학습맵
	private String lluKmmpNodId;

	@Parameter(name="지식맵 대단원 노드명")	//학습맵
	private String lluKmmpNodNm;

	@Parameter(name="지식맵 중단원 노드Id")	//학습맵
	private String mluKmmpNodId;

	@Parameter(name="지식맵 중단원 노드명")	//학습맵
	private String mluKmmpNodNm;

	@Parameter(name="지식맵 차시 노드Id")	//학습맵
	private String tcKmmpNodId;
	private List<String> tcKmmpNodIds;

	@Parameter(name="지식맵 차시 노드명")	//학습맵
	private String tcKmmpNodNm;


	@Parameter(name="지식맵 차시 정보")
	private List<AlPlMluLstInqEnElStuResponseDto> aiKmmpNodList;

	@Parameter(name="지식맵 중단원 노드Id")
	private String mKmmpNodId;

	@Parameter(name="지식맵 중단원 노드명")
	private String mKmmpNodNm;

	@Parameter(name="마지막 평가 정보")
	private AlPlQtmTpcProfDto mLastEaEvInfo;

	@Parameter(name="평가 내역 존재 여부")
	private String alPlEaEvUseYn;	//최초 진입여부 사용될 예정

	@Parameter(name="맞춤학습1 총 문항수")
	private int qtmC1Cnt;

	@Parameter(name="맞춤학습2 총 문항수")
	private int qtmC2Cnt;

	@Parameter(name="맞춤학습1 문항 정답수")
	private int c1CansCnt;

	@Parameter(name="맞춤학습2 문항 정답수")
	private int c2CansCnt;

	@Parameter(name="총 문항 수")
	private int qtmCnt;

	@Parameter(name="맞춤학습1 평균 정답률")
	private double avgC1;

	@Parameter(name="맞춤학습2 평균 정답률")
	private double avgC2;

	@Parameter(name="토픽 지식맵 노드 ID")
	private String tpcKmmpNodId;

	@Parameter(name="토픽 지식맵 노드명")
	private String tpcKmmpNodNm;

	@Parameter(name="유저 ID")
	private String usrId;

	@Parameter(name="문항 Id")
	private String qtmId;


	// 토픽 숙련도...
	@Parameter(name="컨텐츠난이도구분코드")
	private String ctnDffdDvCd;

	@Parameter(name="컨텐츠난이도구분코드이름")
	private String ctnDffdDvNm;

	@Parameter(name="컨텐츠유형코드")
	private String ctnTpCd;

	@Parameter(name="컨텐츠유형코드이름")
	private String ctnTpNm;

	@Parameter(name="문항별 정답여부")
	private String cansYn;

	@Parameter(name="토픽별 AI예측점수")
	private Double aiPredAvgCansRt;

	@Parameter(name="토픽별 실제점수")
	private Double aiPredAvgScr;

	@Parameter(name="문항별 풀이횟수")
	private Integer txmPn;

	@Parameter(name="문항순서")
	private Integer qtmOrdn;


	@Parameter(name="토픽숙련도(취약:01, 보통:02, 완벽:03)")
	private String tpcAvn;

	@Parameter(name="토픽숙련도(취약:01, 보통:02, 완벽:03)")
	private String c1TpcAvn;

	@Parameter(name="토픽숙련도(취약:01, 보통:02, 완벽:03)")
	private String c2TpcAvn;

	@Parameter(name = "학습일 순서")
	private String mdfDtmOrder;

	@Parameter(name = "썸네일이미지주소")
	private String luImgPth;

	@Parameter(name = "단원완료여부")
	private String luevCmplYn;

	@Parameter(name = "진단평가문항수")
	private String ovQtmCnt;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="재구성순서")
	private String rcstnOrdn;
}
