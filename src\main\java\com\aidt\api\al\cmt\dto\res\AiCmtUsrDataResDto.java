package com.aidt.api.al.cmt.dto.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-31 13:50:21
 * @modify date 2024-07-31 13:50:21
 * @desc
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiCmtUsrDataResDto {

    private String aiCmtNo;

    private String cmtCn;

    private String mdfDtm;

}
