package com.aidt.api.appevent.event.adminstat;

import java.time.LocalDateTime;

import com.aidt.base.message.application.AbstractAppEvent;
import com.aidt.base.message.messagequeue.AbstractPayload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 운영 교과서 데이터 등록 Event
 * - 변수 추가 및 수정 시 관리자 kafka 개발 담당자 전달 필수
 */
public class OptTxbInsertEvent extends AbstractAppEvent {

	public OptTxbInsertEvent(OptTxbInsertPayload payload) {
		super(payload);
	}

	@Builder
	@Getter
	@ToString
	public static class OptTxbInsertPayload extends AbstractPayload {

		@Schema(description = "운영교과서ID")
		private String optTxbId;

		@Schema(description = "교과서ID")
		private Long txbId;

		@Schema(description = "학급ID")
		private String claId;

		@Schema(description = "KERIS강의코드")
		private String kerisLectCd;

		@Schema(description = "생성자ID")
		private String crtrId;

		@Schema(description = "생성일시")
		private LocalDateTime crtDtm;

		@Schema(description = "수정자ID")
		private String mdfrId;

		@Schema(description = "수정일시")
		private LocalDateTime mdfDtm;
	}
}
