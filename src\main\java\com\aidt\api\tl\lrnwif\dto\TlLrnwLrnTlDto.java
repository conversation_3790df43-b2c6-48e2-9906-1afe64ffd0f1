package com.aidt.api.tl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwLrnTlDto 학습도구정보Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
// @JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLrnwLrnTlDto {

    /** 학습도구유형코드 */
    @Parameter(name="학습도구유형코드")
    private String lrnTlTpCd;
    /** 학습도구유형명 */
    @Parameter(name="학습도구유형명")
    private String lrnTlTpNm;
    /** 학습도구기능코드 */
    @Parameter(name="학습도구기능코드")
    private String lrnTlKnCd;
    /** 학습도구명 */
    @Parameter(name="학습도구명")
    private String lrnTlKnNm;
    // /** 아이콘CDN경로명 */
    // @Parameter(name="아이콘CDN경로명")
    // private String icnCdnPthNm;
    // /** 아이콘파일유형 (PDF,PNG,썸네일,....) */
    // @Parameter(name="아이콘파일유형")
    // private String icnFleTpCd;
}
