package com.aidt.api.tl.cmtxb.dto;


import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-29 10:14:58
 * @modify date 2024-02-29 10:14:58
 * @desc [TlCmTxbLastTcSrh 학습예정 차시 조회조건 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbLastTcSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 사용자구분 */
    @Parameter(name="사용자구분")
    private String usrDvCd;

    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    
}
