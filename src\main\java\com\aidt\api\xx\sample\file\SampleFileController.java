package com.aidt.api.xx.sample.file;
import java.io.IOException;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aidt.api.xx.sample.file.dto.SampleFileDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/bc/sample/file")
@Tag(name="[xx] Sample.File" , description="Sample File")
public class SampleFileController {
	
	@Autowired
	private SampleFileService fileService;

	@GetMapping("/healthcheck")
	public String download()  {
		return "ok";
	}

	/**
	 * 파일 다운로드
	 *
	 * @param fileDto
	 * @return ResponseEntity<Resource>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@GetMapping("/download")
	public ResponseEntity<Resource> download(SampleFileDto fileDto) throws IllegalStateException, IOException {
		if (ObjectUtils.isEmpty(fileDto)){return null;}
		log.debug(fileDto.toString());
		return fileService.download(fileDto);
	}

	/**
	 * 파일 업로드
	 * 
	 * @param file
	 * @return ResponseEntity<String>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@PostMapping(value = "/upload", consumes = { MediaType.APPLICATION_JSON_VALUE,MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseDto<List<SampleFileDto>> upload(String addPath, List<MultipartFile> files)
			throws IllegalStateException, IOException {
		if (null == files || files.isEmpty()) {
			//throw new CustomException(ExceptionMessage.RUNTIME_EXCEPTION, "file not found"); // file null
			throw new RuntimeException("file not found");
			// return Response.multi(storefiles);
		}
		String path = "";
		List<SampleFileDto> storefiles = null;

		if (!addPath.isBlank()) {
			path = "/" + addPath;
		}
		log.debug("#### UPLOAD FILE LIST ####");
		log.debug("# 추가 경로 : " + path);
		// files.forEach(file -> {
		// log.debug("# NAME:{}} SIZE:{} TYPE:{} ",file.getOriginalFilename(),
		// file.getSize() , file.getContentType());
		// });
		//files.stream().forEach(System.out::println);
		files.stream().forEach(file -> log.debug(String.valueOf(file)));
		storefiles = fileService.storeFiles(files, path);
		log.debug(storefiles.toString());

		return Response.ok(storefiles);
	}
}
// 배포용