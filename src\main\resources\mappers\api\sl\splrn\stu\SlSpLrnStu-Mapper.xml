<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.sl.splrn.stu">
    <!-- 특별학습 목록화면 -->
    <select id="selectSpLrnList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto">
        SELECT   
               MAX(M.OPT_TXB_ID) AS OPT_TXB_ID                                                 /*운영교과서 ID */
              ,M.SP_LRN_ID                                                    /*특별학습 ID */
              ,MAX(M.SP_LRN_NM)  AS SP_LRN_NM                                                  /*특별학습명 */
              ,MAX(B.LRN_GOAL_CN) AS LRN_GOAL_CN                                                 /*특별학습 목표 */
              ,MAX(M.RCSTN_ORDN) AS RCSTN_ORDN                                                   /*재구성 순서 */
              ,MAX(UF2.fle_pth_nm) AS TA_PATH                                                      /*태블릿 썸네일 PATH */
              ,MAX(UF3.fle_pth_nm) AS PC_PATH                                                      /*피씨 썸네일 PATH */
        FROM LMS_LRM.SL_SP_LRN_RCSTN M /* SL_특별학습재구성 */
        <if test='usrTpCd != "TE"'>
              LEFT JOIN LMS_LRM.SL_STU_RCM_LRN R /* SL_학생별추천학습 */
                      ON M.OPT_TXB_ID = R.OPT_TXB_ID
                     AND M.SP_LRN_ID = R.SP_LRN_ID
                     AND R.LRN_USR_ID = #{usrId}
            </if>
        LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM2 /* BC_특별학습썸네일파일매핑 */
        	ON M.SP_LRN_ID = FM2.SP_LRN_ID
        	 AND FM2.THB_TML_TP_CD = 'TA'
        LEFT JOIN LMS_CMS.BC_UPL_FLE UF2 /* BC_업로드파일 */
        	 ON FM2.UPL_FLE_ID = UF2.UPL_FLE_ID 
        	 AND UF2.DEL_YN = 'N'
        LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM3 /* BC_특별학습썸네일파일매핑 */
             ON M.SP_LRN_ID = FM3.SP_LRN_ID
             AND FM3.THB_TML_TP_CD = 'PC'
        LEFT JOIN LMS_CMS.BC_UPL_FLE UF3 /* BC_업로드파일 */
             ON FM3.UPL_FLE_ID = UF3.UPL_FLE_ID 
             AND UF3.DEL_YN = 'N'            
        LEFT JOIN LMS_CMS.BC_SP_LRN B
               ON M.SP_LRN_ID = B.SP_LRN_ID
               AND B.USE_YN = 'Y'
               AND B.DEL_YN = 'N'
        WHERE M.OPT_TXB_ID =  #{optTxbId}    
        <if test='usrTpCd != "TE"'>
          AND R.RCM_YN = 'Y'
          </if>                                      /* 운영교과서ID */
          AND M.USE_YN = 'Y'
        GROUP BY M.SP_LRN_ID
        ORDER BY RCSTN_ORDN ASC, SP_LRN_ID ASC
        /** 특별학습 김형준 SlSpLrnStu-Mapper.xml - selectSpLrnList */
    </select>

    <!-- 상위노드별 학습상태 조회 -->
    <select id="selectUrnkPgrsDto" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto">
        WITH RECURSIVE RCS AS (
            SELECT 
                   R1.SP_LRN_ID
                  ,R2.SP_LRN_NOD_ID
                  ,R2.URNK_SP_LRN_NOD_ID
                  -- ,CAST('' AS CHAR(30)) AS URNK_SP_LRN_NOD_ID2
                  -- ,R2.DPTH
                  ,R2.LWS_YN
            FROM LMS_LRM.SL_SP_LRN_RCSTN R1 /* SL_특별학습재구성 */
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.DPTH = 1
                       -- AND R2.CSTN_CMPL_YN = 'Y'
            WHERE R1.OPT_TXB_ID = #{optTxbId} 
              AND R1.USE_YN = 'Y'
            UNION 
            SELECT 
                   R2.SP_LRN_ID
                  ,R2.SP_LRN_NOD_ID
                  ,R2.URNK_SP_LRN_NOD_ID
                  -- ,IF(R2.DPTH = 2, R2.SP_LRN_NOD_ID, R2.URNK_SP_LRN_NOD_ID) AS URNK_SP_LRN_NOD_ID2 
                  -- ,R2.DPTH
                  ,R2.LWS_YN
            FROM RCS R1
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R1.SP_LRN_NOD_ID = R2.URNK_SP_LRN_NOD_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.CSTN_CMPL_YN = 'Y'
                )
        SELECT
               R.SP_LRN_ID                                                                     /* 특별학습ID */
              ,CASE
                  WHEN COUNT(1) = SUM(IF(P.LRN_ST_CD = 'CL', 1, 0)) THEN 'CL'                  /* DONE == ENTIRE 면 CL */
                  WHEN SUM(IF(P.LRN_ST_CD = 'CL', 1, 0)) = 0 THEN 'NL'                         /* DONE == 0 이면 NL */
                  WHEN COUNT(1)  <![CDATA[<>]]>  SUM(IF(P.LRN_ST_CD = 'CL', 1, 0)) THEN 'DL'   /* DONE 0 이상 AND DONE != ENTIRE 면 DL */
               END AS LRN_ST_CD
               ,SUM(IF(P.lrn_st_cd = 'CL', 1, 0)) AS done
        		,COUNT(1) AS entire
        FROM RCS R
            INNER JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                    ON C.SP_LRN_NOD_ID = R.SP_LRN_NOD_ID
                   AND C.DEL_YN = 'N'
            <if test='usrTpCd == "ST"'>
                LEFT JOIN LMS_LRM.SL_STU_RCM_LRN S /* SL_학생별추천학습 */
                        ON S.OPT_TXB_ID = #{optTxbId}
                       AND R.SP_LRN_ID = S.SP_LRN_ID
                       AND S.LRN_USR_ID = #{usrId}
                       AND S.RCM_YN = 'Y'  -- TODO 원클릭학습설정처리후 수정처리필요
            </if>
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST P /* SL_특별학습진행상태 */
                   ON P.OPT_TXB_ID = #{optTxbId}
                  AND P.SP_LRN_ID = R.SP_LRN_ID
                  AND P.LRN_USR_ID = #{usrId}
                  AND P.SP_LRN_CTN_ID = C.SP_LRN_CTN_ID
        WHERE R.LWS_YN = 'Y'
        GROUP BY C.SP_LRN_NOD_ID, R.SP_LRN_ID
        /** 특별학습 정은혜 SlSpLrnStu-Mapper.xml - selectUrnkPgrsDto */
    </select>
    
    <!--학습이 있는 리스트 -->
    <select id="selectRcmdList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnRcmdDto">
        SELECT
               A.SP_LRN_ID                                                        /* 특별학습ID */
              ,C.SP_LRN_NOD_ID                                                    /* 노드ID */
              ,B.SP_LRN_NOD_ID AS URNK_SP_LRN_NOD_ID                              /* 상위노드ID */
              ,B.SRT_ORDN                                                         /* 상위노드순서 */
              ,B.SP_LRN_NOD_NM                                                    /* 상위노드명 */
              ,D.SP_LRN_CTN_NM                                                    /* 컨텐츠명 */    
              ,D.SP_LRN_CTN_ID                                                    /* 컨텐츠ID */
              ,E.LRN_ST_CD                                                        /* 학습진행상태 DL:진행중 CL:완료 NL:미학습 */
        FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
            LEFT JOIN LMS_CMS.BC_SP_LRN_NOD B /* BC_특별학습노드 */
                   ON B.SP_LRN_ID = A.SP_LRN_ID
                  AND B.URNK_SP_LRN_NOD_ID IS NULL
                  AND B.DEL_YN = 'N'
            LEFT JOIN LMS_CMS.BC_SP_LRN_NOD C /* BC_특별학습노드 */
                   ON C.URNK_SP_LRN_NOD_ID = B.SP_LRN_NOD_ID
                  AND C.SP_LRN_ID = B.SP_LRN_ID
                  AND C.LWS_YN = 'Y'
                  AND C.DEL_YN = 'N' 
            LEFT JOIN LMS_CMS.BC_SP_LRN_CTN D /* BC_특별학습콘텐츠 */
                   ON D.SP_LRN_NOD_ID = C.SP_LRN_NOD_ID
                  AND D.DEL_YN = 'N'
            INNER JOIN LMS_LRM.SL_SP_LRN_PGRS_ST E /* SL_특별학습진행상태 */
                    ON E.OPT_TXB_ID = A.OPT_TXB_ID
                   AND E.SP_LRN_ID = A.SP_LRN_ID
                   AND E.SP_LRN_CTN_ID = D.SP_LRN_CTN_ID
                   AND E.LRN_USR_ID = #{userId}
        WHERE A.OPT_TXB_ID = #{optTxbId}                                        /* 운영교과서ID */
          AND A.USE_YN = 'Y'
        ORDER BY A.SP_LRN_ID ASC, B.SRT_ORDN ASC                                 /* 1순위: 특별학습ID, 2순위:상위노드정렬순서 */
        /** 특별학습 정은혜 SlSpLrnStu-Mapper.xml - selectRcmdList */
    </select>


    <!-- 단원 리스트 -->
    <select id="selectRcmdLluList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnRcmdDto">
        SELECT A.SP_LRN_ID
              ,MAX(A.RCSTN_ORDN) AS RCSTN_ORDN
              ,CASE
                   WHEN COUNT(1) = SUM(IF(S.LRN_ST_CD = 'CL', 1, 0)) THEN 'CL'                                                   
                   WHEN SUM(IF(S.LRN_ST_CD = 'CL', 1, 0)) = 0 THEN 'NL'                                                
                   WHEN COUNT(1)  <![CDATA[<>]]> SUM(IF(S.LRN_ST_CD = 'CL', 1, 0)) THEN 'DL' 
               END AS LRN_ST_CD
        FROM LMS_LRM.SL_SP_LRN_RCSTN A /* SL_특별학습재구성 */
            LEFT JOIN LMS_CMS.BC_SP_LRN_NOD N  /* BC_특별학습노드 */
                   ON A.SP_LRN_ID = N.SP_LRN_ID
                  AND N.DEL_YN = 'N'
                  AND N.URNK_SP_LRN_NOD_ID IS NOT NULL
            LEFT JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                   ON N.SP_LRN_NOD_ID = C.SP_LRN_NOD_ID
                  AND C.DEL_YN = 'N'
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST S /* SL_특별학습진행상태 */
                   ON C.SP_LRN_CTN_ID = S.SP_LRN_CTN_ID
                  AND S.LRN_USR_ID = #{userId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
          AND A.USE_YN = 'Y'
        GROUP BY A.SP_LRN_ID
        ORDER BY A.RCSTN_ORDN ASC
        /** 특별학습 김형준 SlSpLrnStu-Mapper.xml - selectRcmdLluList */
    </select>

    <!-- 상세 - 배너 -->
    <select id="selectSpLrnDtlBanrViewDto" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnDtlBanrViewDto">
        WITH RECURSIVE RCS AS (
            SELECT 
                   R1.SP_LRN_ID
                  ,R1.SP_LRN_NM
                  ,R2.SP_LRN_NOD_ID
                  ,R2.SP_LRN_NOD_NM
                  ,R2.URNK_SP_LRN_NOD_ID
                  ,R1.LRN_GOAL_CN
                  ,R2.DPTH
                  ,R2.LWS_YN
                  ,CAST(LPAD(R2.SRT_ORDN, 10, '0') AS CHAR(50)) AS SORT
            FROM LMS_LRM.SL_SP_LRN_RCSTN R1 /* SL_특별학습재구성 */
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.DPTH = 1
                       -- AND R2.CSTN_CMPL_YN = 'Y'
            WHERE R1.OPT_TXB_ID =  #{optTxbId}
              AND R1.USE_YN = 'Y'
              AND R1.SP_LRN_ID = #{spLrnId}
            UNION 
            SELECT 
                   R2.SP_LRN_ID
                  ,R1.SP_LRN_NM
                  ,R2.SP_LRN_NOD_ID
                  ,R2.SP_LRN_NOD_NM
                  ,IF(R2.DPTH <![CDATA[>]]> 2, R1.URNK_SP_LRN_NOD_ID, R1.SP_LRN_NOD_ID) AS URNK_SP_LRN_NOD_ID
                  ,R1.LRN_GOAL_CN
                  ,R2.DPTH
                  ,R2.LWS_YN
                  ,CONCAT(R1.SORT, LPAD(R2.SRT_ORDN, 10, '0')) AS SORT
            FROM RCS R1
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R1.SP_LRN_NOD_ID = R2.URNK_SP_LRN_NOD_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.CSTN_CMPL_YN = 'Y'
                )
        SELECT
               MAX(R.SP_LRN_ID) AS  SP_LRN_ID                                                   /* 특별학습ID */
              ,MAX(D.LRN_GOAL_CN) AS LRN_GOAL_CN                                                /* 특별학습목표 */
              ,MAX(R.SP_LRN_NM) AS SP_LRN_NM    	                                           /* 특별학습명 */
              ,MAX(D.CSTN_CN) AS CSTN_CN			                                             /* 특별학습 구성내용 */
              ,CASE
                   WHEN COUNT(1) = SUM(IF(B.LRN_ST_CD = 'CL', 1, 0)) THEN 'CL'                  /* DONE == ENTIRE 면 CL */
                   WHEN SUM(IF(B.LRN_ST_CD = 'CL', 1, 0)) = 0 THEN 'NL'                         /* DONE == 0 이면 NL */
                   WHEN COUNT(1)  <![CDATA[<>]]>  SUM(IF(B.LRN_ST_CD = 'CL', 1, 0)) THEN 'DL'   /* DONE  0이상 AND DONE != ENTIRE 면 DL */
               END AS LRN_ST_CD
              ,SUM(IF(B.LRN_ST_CD = 'CL', 1, 0))    AS DONE                                     /* 완료건수 */
              ,COUNT(1)                             AS ENTIRE                                   /* 전체 건수 */
        FROM RCS R
            INNER JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                    ON C.SP_LRN_NOD_ID = R.SP_LRN_NOD_ID
                   AND C.DEL_YN = 'N'
            LEFT JOIN LMS_CMS.BC_SP_LRN D /* BC_특별학습 */
                   ON D.SP_LRN_ID = R.SP_LRN_ID
                  AND D.USE_YN = 'Y'
                  AND D.DEL_YN = 'N'
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST B /* SL_특별학습진행상태 */
                    ON B.OPT_TXB_ID = #{optTxbId}
                  AND B.SP_LRN_ID = R.SP_LRN_ID
                  AND B.SP_LRN_CTN_ID = C.SP_LRN_CTN_ID
                  AND B.LRN_USR_ID = #{userId}
        WHERE R.LWS_YN = 'Y'
        GROUP BY URNK_SP_LRN_NOD_ID

        /** 특별학습 정은혜 SlSpLrnStu-Mapper.xml - selectSpLrnDtlBanrViewDto */
    </select>
    <!--썸네일 리스트-->
    <select id="selectBcSpLrnNodThbList"  resultType="com.aidt.api.sl.splrn.dto.SlSpLrnNodThbDto">
        SELECT M.*
        FROM (
            SELECT
                   A.SP_LRN_NOD_ID                                                 /* 특별학습노드ID */            
                  ,B1.FLE_TP_CD                 AS MO_FLE_TP_CD                    /* 모바일 파일유형 */
                  ,B1.FLE_PTH_NM                 AS MO_PATH                        /* 모바일 경로 */
                  ,B2.FLE_TP_CD                 AS TA_FLE_TP_CD                    /* 태블릿 파일유형 */
                  ,B2.FLE_PTH_NM                 AS TA_PATH                        /* 태블릿 경로 */
                  ,B3.FLE_TP_CD                 AS PC_FLE_TP_CD                    /* 피씨 파일유형 */
                  ,B3.FLE_PTH_NM                 AS PC_PATH                        /* 피씨 경로 */
            FROM LMS_CMS.BC_SP_LRN_NOD A /* BC_특별학습노드 */
                LEFT JOIN LMS_CMS.BC_SP_LRN_NOD_THB B1 /* BC_특별학습노드썸네일 */
                       ON A.SP_LRN_NOD_ID = B1.SP_LRN_NOD_ID
                      AND B1.TML_TP_CD = 'MO'
                      AND B1.DEL_YN = 'N'  
                LEFT JOIN LMS_CMS.BC_SP_LRN_NOD_THB B2 /* BC_특별학습노드썸네일 */
                       ON A.SP_LRN_NOD_ID = B2.SP_LRN_NOD_ID
                      AND B2.TML_TP_CD = 'TA'
                      AND B2.DEL_YN = 'N'  
                LEFT JOIN LMS_CMS.BC_SP_LRN_NOD_THB B3 /* BC_특별학습노드썸네일 */
                       ON A.SP_LRN_NOD_ID = B3.SP_LRN_NOD_ID
                      AND B3.TML_TP_CD = 'PC'
                      AND B3.DEL_YN = 'N'  
            WHERE A.SP_LRN_ID = #{spLrnId}                                        /*특별학습ID*/
              AND A.URNK_SP_LRN_NOD_ID IS NULL
              AND A.DEL_YN = 'N'
        ) M
        /** 특별학습 정은혜 SlSpLrnStu-Mapper.xml - selectBcSpLrnNodThbList */
    </select>
    <!-- 스크린샷 목록리스트 -->
    <select id="selectSpLrnScrsList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnScrsDto">
        SELECT
               B.SP_LRN_ID                                            /* 특별학습ID*/
              ,B.FLE_TP_CD                                            /* 파일 유형코드*/
              ,B.FLE_PTH_NM                                           /* 파일 경로*/            
              ,B.ALTN_TXT_CN                                          /*파일 대체텍스트*/
        FROM LMS_CMS.BC_SP_LRN_SCRS B /* BC_특별학습스크린샷 */
        WHERE B.SP_LRN_ID = #{spLrnId}                                /*특별학습ID*/
          AND B.DEL_YN = 'N'                                          /*삭제여부 N:부*/
        ORDER BY SRT_ORDN ASC, SP_LRN_SCRS_ID ASC
        /** 특별학습 정은혜 SlSpLrnStu-Mapper.xml - selectSpLrnScrsList */    
    </select>
    
    <!-- 특별학습 콘텐츠 리스트 select -->
    <select id="selectBcSpLrnCtnList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnCtnDto">
        SELECT 
               A.SP_LRN_NOD_ID                            /* 노드ID*/
              ,A.SP_LRN_CTN_ID                          /*콘텐츠ID*/ 
        FROM LMS_CMS.BC_SP_LRN_CTN A /* BC_특별학습콘텐츠 */
        WHERE A.SP_LRN_NOD_ID = #{spLrnNodId}       
        AND A.DEL_YN = 'N'
        /** 특별학습 정은혜 SlSpLrnStu-Mapper.xml - bcSplrnCtnList */
    </select>

    <!-- (v3.1)디테일 목록리스트 -->
    <select id="selectSlSpLrnDtlList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnDtlViewDto">
        SELECT 
             A.SP_LRN_ID                                                        /* 특별학습ID */
            ,B.SP_LRN_CTN_ID                                                    /* 콘텐츠ID */
            ,A.LWS_YN                                                           /* 최하위여부 */
            ,A.URNK_SP_LRN_NOD_ID                                               /* 상위노드ID */
            ,A.SP_LRN_NOD_ID                                                    /* 노드ID */
            ,A.SP_LRN_NOD_NM                                                    /* 노드명 */
            ,A.SRT_ORDN
            ,IF(C.LRN_ST_CD IS NULL, IF(A.LWS_YN = 'Y', 'NL', ''), C.LRN_ST_CD) AS LRN_ST_CD
        FROM LMS_CMS.BC_SP_LRN_NOD A /* BC_특별학습노드 */
            LEFT JOIN LMS_CMS.BC_SP_LRN_CTN B /* BC_특별학습콘텐츠 */
                               ON A.SP_LRN_NOD_ID = B.SP_LRN_NOD_ID
                              AND B.DEL_YN = 'N'
        	  LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST C /* SL_특별학습진행상태 */
                               ON C.OPT_TXB_ID = #{optTxbId}
                              AND C.SP_LRN_ID = A.SP_LRN_ID
                              AND C.SP_LRN_CTN_ID = B.SP_LRN_CTN_ID
                              AND C.LRN_USR_ID = #{userId}
        WHERE A.SP_LRN_ID = #{spLrnId}
        AND A.DEL_YN = 'N'
        AND ((A.LWS_YN = 'Y' AND B.SP_LRN_CTN_ID IS NOT NULL) 
             OR (A.LWS_YN <![CDATA[<>]]> 'Y'))  -- 최하위노드는 콘텐츠가 등록된 데이터만 가져오도록 수정
        ORDER BY A.SRT_ORDN ASC
        /** 특별학습 김형준 SlSpLrnStu-Mapper.xml - selectSlSpLrnDtlList */
    </select>
</mapper>
    
    
