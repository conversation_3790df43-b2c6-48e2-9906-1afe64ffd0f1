package com.aidt.api.bc.home.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-19 14:29:00
 * @modify 2024-06-19 14:29:00
 * @desc 교사 > 홈 > 우리반 수업 분석 response dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcClaLsnAnResDto {
	
	@Parameter(name="학생ID")
    private String usrId;
	
	@Parameter(name="학생번호")
    private String stuNo;
	
	@Parameter(name="학생명")
    private String usrNm;
	
	@Parameter(name="진도율")
    private String progPcnt;
	
	@Parameter(name="이전 진도율")
    private String prevProgPcnt;
	
	@Parameter(name="학습맵노드ID")
    private String lrmpNodId;
	
	@Parameter(name="학습활동ID")
    private String lrnAtvId;
	
	@Parameter(name="평가학습활동ID")
	private String lrnAtvIdEx;
	
	@Parameter(name="익힘학습활동ID")
	private String lrnAtvIdWb;
	
	@Parameter(name="학습활동명")
    private String lrnAtvNm;
	
	@Parameter(name="학습단계ID")
    private String lrnStpId;
	
	@Parameter(name="학습단계명")
    private String lrnStpNm;
	
	@Parameter(name="학습상태코드(NL:미학습/DL:학습중/CL:학습완료)")
    private String lrnStCd;
	
	@Parameter(name="학습시간초수")
    private String lrnTmScnt;
	
	@Parameter(name="평가수")
	private String evCnt;
	
	@Parameter(name="차시학습현황목록")
	private List<BcClaLsnAnResDto> tcLsnAnList;
	
	@Parameter(name="대단원노드ID")
    private String lluNodId;
	
	@Parameter(name="대단원노드명")
    private String lluNodNm;
	
	@Parameter(name="차시노드ID")
    private String tcNodId;
	
	@Parameter(name="차시노드명")
    private String tcNodNm;
	
	@Parameter(name="대단원순서")
    private String lluOrdn;
	
	@Parameter(name="차시순서")
    private String tcOrdn;
	
	@Parameter(name="대분류 잠금여부")
    private String lluLcknYn;
	
	@Parameter(name="차시 잠금여부")
    private String tcLcknYn;
	
	@Parameter(name="이전차시노드ID")
    private String prevTcNodId;
	
	@Parameter(name="이전차시잠금여부")
    private String prevLcknYn;
	
	@Parameter(name="다음차시노드ID")
    private String nextTcNodId;
	
	@Parameter(name="다음차시잠금여부")
    private String nextLcknYn;
	
	@Parameter(name="문항번호")
    private String qtmNo;
	
	@Parameter(name="형성평가/보충심화학습 구분(frmEv:형성평가/sppNtn:보충,심화학습)")
    private String fsDivCd;
	
	@Parameter(name="평가ID")
    private String evId;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
	@Parameter(name="문항ID")
    private String qtmId;
	
	@Parameter(name="정답여부")
    private String cansYn;
	
	@Parameter(name="학습수준 (NM:보통/FS:빠른/SL:느린)")
    private String lrnrVelTpCd;
	
	@Parameter(name="헤더ID")
    private String hdrId;
	
	@Parameter(name="문제여부")
    private String qstYn;
	
	@Parameter(name="학습사용자ID")
    private String lrnUsrId;
	
	@Parameter(name="단원번호사용여부")
	private String luNoUseYn;
	
	@Parameter(name="번호사용여부")
	private String tcLuNoUseYn;
	

    /** 재구성 번호 */
    @Parameter(name="단원재구성 번호")
    private int luRcstnNo;
    
    @Parameter(name="재구성 번호")
    private int tcRcstnNo;
    
    @Parameter(name="콘텐츠유형코드")
    private String ctnTpCd;

}
