package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LearningMgDto {
	
	@Parameter(name="학급ID")
	private String claId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="사용자명")
	private String usrNm;
	
	@Parameter(name="학생번호")
	private String stuNo;
	
	@Parameter(name="사용자 학습수준 (빠른:FS, 보통:NM, 느린:SL)")
	private String lrnrVelTpCd;
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="단원 지식맵 노드ID")
	private String luKmmpNodId;
	
	@Parameter(name="중단원 지식맵 노드ID")
	private String mluKmmpNodId;
	
	@Parameter(name="중단원 지식맵 노드명")
	private String mluKmmpNodNm;
	
	@Parameter(name="차시 지식맵 노드ID")
	private String tcKmmpNodId;
	private String tcKmmpNodNm;
	
	@Parameter(name="토픽 지식맵 노드ID")
	private String tpcKmmpNodId;
	private String tpcKmmpNodNm;
	
	@Parameter(name="진단평가완료여부")
	private String ovEvCmplYn;
	
	@Parameter(name="차시별학습완료여부")
	private String cvEvCmplYn;
	
	@Parameter(name="문항수")
	private Integer qtmCnt;
	
	@Parameter(name="정답수")
	private Integer cansYCny;
	
	@Parameter(name="평가풀이시간(초)")
	private Integer evTmScnt;
	
	@Parameter(name="생성일시")
	private String crtDtm;
	
	@Parameter(name="과목코드")
	private String sbjCd;
	
	@Parameter(name="평가ID")
	private String evId;
	
	@Parameter(name="진단평가 평가ID")
	private String ovEvId;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
	@Parameter(name="평가완료여부")
	private String evCmplYn;
	
	@Parameter(name="단원수")
	private Double luCnt;
	
	@Parameter(name="단원완료수")
	private Double luCmplCnt;
	
	@Parameter(name="토픽숙련도")
	private Double tpcAvn;
	
	@Parameter(name="학습시간")
	private int learningTm;
	
	@Parameter(name="차시사용여부")
	private String tcUseYn;
	
	@Parameter(name="차시수")
	private int tcCnt;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;

	@Parameter(name="학습진도율")
	private Double aiLrnPgrsRt;
	
	@Parameter(name="학습일시")
	private String lrnDtm;
}
