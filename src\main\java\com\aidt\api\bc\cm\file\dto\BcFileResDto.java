package com.aidt.api.bc.cm.file.dto;

import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcFileResDto {

	@Schema(description = "첨부파일ID")
	private Long annxFleId;

	@Schema(description = "첨부ID")
	private Long annxId;

	@Schema(description = "정렬순서")
	private int srtOrdn;

	@Schema(description = "문서뷰어ID")
	private String docViId;

	@Schema(description = "첨부파일명")
	private String annxFleNm;

	@Schema(description = "첨부파일원본명")
	private String annxFleOrglNm;

	@Schema(description = "첨부파일확장자명")
	private String annxFleFextNm;

	@Schema(description = "첨부파일사이즈")
	private long annxFleSze;

	@Schema(description = "첨부파일경로명")
	private String annxFlePthNm;

	@Schema(description = "사용여부")
	private String useYn;

	@Schema(description = "버킷URL")
	private String bucketUrl;

	@Schema(description = "등록자ID")
	private String crtrId;

	@Schema(description = "등록일시")
	private String crtDtm;

	@Schema(description = "수정자ID")
	private String mdfrId;

	@Schema(description = "수정일시")
	private String mdfDtm;

	@Schema(description = "DB 명")
	private String dbId;

	public static BcFileResDto of(BcAnnxFleDto dto) {
		return BcFileResDto.builder()
			.annxFleId(dto.getAnnxFleId())
			.annxId(dto.getAnnxId())
			.srtOrdn(dto.getSrtOrdn())
			.docViId(dto.getDocViId())
			.annxFleNm(dto.getAnnxFleNm())
			.annxFleOrglNm(dto.getAnnxFleOrglNm())
			.annxFleFextNm(dto.getAnnxFleFextNm())
			.annxFleSze(dto.getAnnxFleSze())
			.annxFlePthNm(dto.getAnnxFlePthNm())
			.useYn(dto.getUseYn())
			.bucketUrl(dto.getBucketUrl())
			.crtrId(dto.getCrtrId())
			.crtDtm(dto.getCrtDtm())
			.mdfrId(dto.getMdfrId())
			.mdfDtm(dto.getMdfDtm())
			.dbId(dto.getDbId())
			.build();
	}

}
