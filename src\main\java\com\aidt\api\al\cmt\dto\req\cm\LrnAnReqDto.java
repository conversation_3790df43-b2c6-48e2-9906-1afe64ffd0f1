package com.aidt.api.al.cmt.dto.req.cm;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-29 10:49:23
 * @modify date 2024-05-29 10:49:23
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LrnAnReqDto {

    @Parameter(name="내용영역명", required=true)
    private String inputText;

    @Parameter(name="학습 성장률", required=true)
    private Float growthRate;

}
