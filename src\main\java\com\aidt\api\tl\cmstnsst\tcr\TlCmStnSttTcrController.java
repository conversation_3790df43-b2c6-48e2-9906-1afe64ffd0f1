package com.aidt.api.tl.cmstnsst.tcr;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.cmstnsst.dto.TlCmLluStnSttSrhDto;
import com.aidt.api.tl.cmstnsst.dto.TlCmStnSttSrhDto;
import com.aidt.api.tl.cmstnsst.stu.TlCmStnSttStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-19 14:42:44
 * @modify date 2024-06-19 14:42:44
 * @desc TlCmStnSttTcr 교과학습 교육과정표준관련처리API(교사)
 */

@Slf4j
@Tag(name="[tl] 교과학습 교육과정표준관련처리API(교사)[TlCmStnSttTcr]", description="(교사용)교과학습 교육과정표준관련처리를 처리한다")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/tcr/cmstnsst")
public class TlCmStnSttTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlCmStnSttStuService tlCmStnSttStuService;

    /**
     * 교과학습 교육과정표준체계ID목록 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmEduCrsCnDto>>
     */
    @Operation(summary="교과학습 교육과정표준체계ID목록 조회", description="차시ID 또는 학습활동ID에 등록된 교육과정표준체계ID목록을 조회한다.")
    @PostMapping(value = "/selectTxbEduCrsCnCdList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectTxbEduCrsCnCdList(@Valid @RequestBody TlCmStnSttSrhDto srhDto) {
        log.debug("Entrance selectTxbEduCrsCnCdList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmStnSttStuService.selectTxbEduCrsCnCdList(srhDto));
    }
    /**
     * 교과학습 대단원-교육과정표준체계ID목록 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<String>>
     */
    @Operation(summary="교과학습 대단원-교육과정표준체계ID목록 조회", description="대단원에 등록된 교육과정표준체계ID목록을 조회한다.")
    @PostMapping(value = "/selectTxbLluEduCrsCnCdList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectTxbLluEduCrsCnCdList(@Valid @RequestBody TlCmLluStnSttSrhDto srhDto) {
        log.debug("Entrance selectTxbLluEduCrsCnCdList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmStnSttStuService.selectTxbLluEduCrsCnCdList(srhDto));
    }


}
