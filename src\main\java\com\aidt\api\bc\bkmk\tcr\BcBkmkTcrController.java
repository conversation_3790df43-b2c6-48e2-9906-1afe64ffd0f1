package com.aidt.api.bc.bkmk.tcr;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.bkmk.dto.BcBkmkDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:55:08
 * @modify 2024-01-05 17:55:08
 * @desc 북마크 Controller
 */

//@Slf4j
@Tag(name="[bc] 북마크[LwBkmkTcr]", description="북마크(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/bkmk")
public class BcBkmkTcrController {

	@Autowired
	private BcBkmkTcrService bcBkmkTcrService;

	@Autowired
	private JwtProvider jwtProvider;

	/**
	 * 북마크 목록 조회 요청
	 *
	 * @param bcBkmkDto : LwBkmkDto
	 * @return ResponseList<LwBkmkDto>
	 */
	@Tag(name="[bc] 북마크 목록 조회", description="북마크 목록을 조회한다.(학생)")
	@GetMapping(value = "/selectBkmkList")
	public ResponseDto<List<BcBkmkDto>> selectBkmkList(BcBkmkDto bcBkmkDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcBkmkDto.setOptTxbId(userDetails.getOptTxbId());
    	bcBkmkDto.setLrnUsrId(userDetails.getUsrId());
		return Response.ok(bcBkmkTcrService.selectBkmkList(bcBkmkDto));
	}

	/**
	 * 북마크 상세 조회 요청
	 *
	 * @param lwBkmkDto : LwBkmkDto
	 * @return ResponseList<LwBkmkDto>
	 */
	@Tag(name="[bc] 북마크 상세 조회", description="북마크 상세조회한다.(학생)")
	@GetMapping(value = "/selectBkmkDtl")
	public ResponseDto<BcBkmkDto> selectBkmkDtl(BcBkmkDto bcBkmkDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcBkmkDto.setOptTxbId(userDetails.getOptTxbId());
    	bcBkmkDto.setLrnUsrId(userDetails.getUsrId());
		return Response.ok(bcBkmkTcrService.selectBkmkDtl(bcBkmkDto));
	}

    /**
     * 북마크 저장
     *
     * @param bcBkmkDto : LwBkmkDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="북마크 저장", description="북마크를 저장한다.")
    @PostMapping(value = "/insertBkmk")
    public ResponseDto<Integer> insertBkmk(@Valid @RequestBody BcBkmkDto bcBkmkDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcBkmkDto.setOptTxbId(userDetails.getOptTxbId());
    	bcBkmkDto.setLrnUsrId(userDetails.getUsrId());
    	bcBkmkDto.setCrtrId(userDetails.getUsrId());
    	bcBkmkDto.setMdfrId(userDetails.getUsrId());
    	bcBkmkDto.setDbId(userDetails.getTxbId());
        return Response.ok(bcBkmkTcrService.insertBkmk(bcBkmkDto));
    }

    /**
     * 북마크 삭제
     *
     * @param bkmkIds : List<Integer>
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="북마크 삭제", description="북마크를 삭제한다.")
    @DeleteMapping(value = "/deleteBkmk")
    public ResponseDto<Integer> deleteBkmk(@RequestBody List<Integer> bkmkIds) {
    	BcBkmkDto bcBkmkDto = new BcBkmkDto();
    	bcBkmkDto.setBkmkIds(bkmkIds);
        return Response.ok(bcBkmkTcrService.deleteBkmk(bcBkmkDto));
    }
}
