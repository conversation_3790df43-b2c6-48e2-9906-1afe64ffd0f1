package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrEvResDto {
	
	@Parameter(name = "운영교과서ID")
	private String optTxbId;
	
	@Parameter(name = "사용자ID")
	private String usrId;
	
	@Parameter(name = "사용자명")
	private String usrNm;
	
	@Parameter(name = "학급ID")
	private String claId;
	
	@Parameter(name = "번호")
	private String rowNo;
	
	@Parameter(name = "평가 전체 개수")
	private String evCnt;
	
	@Parameter(name = "완료 과제 수")
	private String completedAssignments;
	
	@Parameter(name = "사용자별 평가완료 개수")
	private String evCmplCnt;
	
	@Parameter(name = "사용자별 미응시 개수")
	private String evNonTxmCnt;
	
	@Parameter(name = "사용자별 진행 개수")
	private String evPgrsCnt;
	
	@Parameter(name = "사용자 수준")
	private String lrnrVelTpCd;
	
	@Parameter(name = "진행율")
	private String progressRate;
	
	@Parameter(name = "번호")
	private String stuNo;
}
