package com.aidt.api.bc.home.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-19 14:29:00
 * @modify 2024-06-19 14:29:00
 * @desc 교사 > 홈 > 우리반 수업 분석 request dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcClaLsnAnReqDto {
	
	@Parameter(name="운영교과서")
    private String optTxbId;
	
	@Parameter(name="교과서")
	private String txbId;
	
	@Parameter(name="학습맵노드ID")
    private String lrmpNodId;
	
	@Parameter(name="이전 차시 ID")
	private String prevTcNodId;
	
	@Parameter(name="학급코드")
    private String claId;
	
}
