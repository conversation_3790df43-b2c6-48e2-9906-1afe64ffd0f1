package com.aidt.api.tl.inidat.dto;


import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-04 10:53:59
 * @modify date 2024-03-04 10:53:59
 * @desc TlIniDatSrhDto 교과학습초기데이터 작성조건
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlIniDatCondDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 교사사용자ID */
    @Parameter(name="교사사용자ID")
    private String tcrUsrId;
    
    /** 2학기교과서여부 */
    @Parameter(name="2학기교과서여부")
    private String secTrmYn;

}
