package com.aidt.api.bc.slpp.tcr;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.slpp.dto.BcSlppComandDto;
import com.aidt.api.bc.slpp.dto.BcSlppDto;
import com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto;
import com.aidt.api.bc.slpp.dto.BcSlppRoomDto;
import com.aidt.api.bc.slpp.dto.BcSlppTargetDto;
import com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:53:17
 * @modify 2024-01-05 17:53:17
 * @desc 교사_쪽지 Service
 */

//@Slf4j
@Service
public class BcSlppTcrService {

    private final String MAPPER_NAMESPACE = "api.bc.slpp.tcr.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 쪽지 목록 조회 서비스
     *
     * @param dto : BcSlppDtoPagingRequest
     * @return List<BcSlppDto>
     */
	@Transactional
    public List<BcSlppDto> selectSlppList(BcSlppPagingRequestDto dto) {
		updateCofmYnSlppList(dto);
		return commonDao.selectList(MAPPER_NAMESPACE + "selectSlppList", dto);
    }

	/**
	 * 쪽지 목록 조회 서비스 - 확인여부 업데이트
	 *
	 * @param dto: BcSlppDtoPagingRequest
	 */
	public void updateCofmYnSlppList(BcSlppPagingRequestDto dto) {
		commonDao.update(MAPPER_NAMESPACE + "updateCofmYnSlppList", dto);
	}

	/**
	 * 쪽지 등록 요청 서비스
	 *
	 * @param list : List<BcSlppDto>
	 * @return int
	 */
	@Transactional
	public int insertSlpp(List<BcSlppDto> list) {
		return commonDao.insert(MAPPER_NAMESPACE + "insertSlppList", list);
	}

    /**
     * 쪽지 등록 요청 서비스
     *
     * @param dto : BcSlppDto
     * @return int
     */
    @Transactional
	public int insertSlpp(BcSlppDto dto) {
    	return commonDao.insert(MAPPER_NAMESPACE + "insertSlpp", dto);
	}

	/**
	 * 쪽지 삭제 서비스
	 * @param list : List<BcSlppDto>
	 * @return int
	 */
	@Transactional
	public int deleteSlpp(List<BcSlppDto> list) {
		return commonDao.update(MAPPER_NAMESPACE + "deleteSlppList", list);
	}

	/**
	 * 쪽지 삭제 서비스
	 * @param dto : BcSlppDto
	 * @return int
	 */
	@Transactional
	public int deleteSlpp(BcSlppDto dto) {
		return commonDao.update(MAPPER_NAMESPACE + "deleteSlpp", dto);
	}

	/**
	 * 쪽지 삭제 서비스
	 * @param dto : BcSlppDto
	 * @return int
	 */
	@Transactional
	public int deleteAllSlpp(BcSlppComandDto dto) {
		return commonDao.update(MAPPER_NAMESPACE + "deleteAllSlpp", dto);
	}

	/////////////////////
	/**
	 * 교사 대화 목록
	 * @param tcrUsrId : String
	 * @return List<BcSlppRoomDto>
	 */
	@Transactional
	public List<BcSlppRoomDto> selectSlppRoomList(String tcrUsrId){
		List<BcSlppRoomDto> result = commonDao.selectList(MAPPER_NAMESPACE + "selectSlppRoomList", tcrUsrId);
		return result.stream().sorted(Comparator.comparing(BcSlppRoomDto::getCrtDtm).reversed()).collect(Collectors.toList());
	}

	/**
	 * 교사 학생 목록
	 * @param tcrUsrId : String
	 * @return List<BcSlppTargetDto>
	 */
	@Transactional
	public List<BcSlppTargetDto> selectSlppTargetList(String tcrUsrId){
		return commonDao.selectList(MAPPER_NAMESPACE + "selectSlppTargetList", tcrUsrId);
	}

	//////////////////////////////////////////
	/**
	 * 교사 대화가능 시간 설정
	 * @param dto : BcSlppTmSetm
	 * @return int
	 */
	@Transactional
	public int saveSlppTmSetm(BcSlppTmSetmDto dto){
		int result = updateSlppTmSetm(dto);
		if(result == 0){
			result = insertSlppTmSetm(dto);
		}
		return result;
	}

	/**
	 * 교사 대화가능 시간 설정 신규
	 * @param dto : BcSlppTmSetm
	 * @return int
	 */
	@Transactional
	public int insertSlppTmSetm(BcSlppTmSetmDto dto){
		return commonDao.insert(MAPPER_NAMESPACE + "insertSlppTmSetm4Save", dto);
	}

	/**
	 * 교사 대화가능 시간 설정 신규
	 * @param optTxbId : BcSlppTmSetm
	 * @return int
	 */
	@Transactional
	public int insertSlppTmSetm(String optTxbId){
		return commonDao.insert(MAPPER_NAMESPACE + "insertSlppTmSetm", optTxbId);
	}


	/**
	 * 교사 대화가능 시간 설정 갱신
	 * @param dto : BcSlppTmSetm
	 * @return int
	 */
	@Transactional
	public int updateSlppTmSetm(BcSlppTmSetmDto dto){
		return commonDao.update(MAPPER_NAMESPACE + "updateSlppTmSetm", dto);
	}
	/**
	 * 교사 대화 사용/미사용 설정
	 * @param dto : BcSlppTmSetm
	 * @return int
	 */
	@Transactional
	public int updateSlppUseYn(BcSlppTmSetmDto dto){
		return commonDao.update(MAPPER_NAMESPACE + "updateSlppUseYn", dto);
	}
	
	/**
	 * 교사 대화가능 시간 조회
	 * @param optTxbId : String
	 * @return int
	 */
	@Transactional
    public BcSlppTmSetmDto selectSlppTmSetm(String optTxbId) {
		BcSlppTmSetmDto dtoRes =commonDao.select(MAPPER_NAMESPACE + "selectSlppTmSetm", optTxbId);
		if(dtoRes == null) {
			dtoRes =commonDao.select(MAPPER_NAMESPACE + "selectSlppTmSetmDummy", optTxbId);
		}
		return dtoRes;
    }
	
	/**
	 * 읽지 않은 대화 존재여부 조회
	 * @param dto : BcSlppPagingRequestDto
	 * @return int
	 */
	public int selectSlppCofmCnt(BcSlppPagingRequestDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectSlppCofmCnt", dto);
	}
}
