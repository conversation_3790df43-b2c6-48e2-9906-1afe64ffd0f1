package com.aidt.api.sl.lrnwif.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.lrnwif.dto.SlLrnwAtvMetaDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwGetRequestDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwSaveRequestDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwTocAtvDto;
import com.aidt.api.sl.lrnwif.stu.SlLrnwifStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-08 15:24:20
 * @modify : date 2024-03-08 15:24:20
 * @desc : special student controller
 */
@Tag(name="[sl] 특별학습 학습창연계(교사) [SlLrnwifTcr]", description="학습창관련 연계데이터 처리(교사용)")
@Slf4j
@RequestMapping("/api/v1/sl/lrnwif/tcr")
@RestController
public class SlLrnwifTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private SlLrnwifStuService slSpLrnWifService;
	
	/**
	 * 특별학습 활동저장
	 * 
	 * @param slSpLrnAtvDto
	 * @return ResponseDto<Integer>	 */
	@Operation(summary="특별학습 콘텐츠 활동저장(교사)", description="학습창에서 사용자의 학습활동상태및 학습시간을 저장한다")
	@PostMapping(value = "/saveLrnCtnInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> saveSpLrnAtv(@Parameter(name="특별학습 활동 저장 DTO", required = true)@RequestBody SlLrnwSaveRequestDto slLrnwSaveRequestDto) {
		log.debug("Entrance saveSpLrnAtv");
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();

		return Response.ok(slSpLrnWifService.saveSpLrnAtv(slLrnwSaveRequestDto, securityUserDetailDto.getOptTxbId(), securityUserDetailDto.getUsrId(), securityUserDetailDto.getTxbId()));
	} 

	
	/**
	 * 특별학습 마지막시점 활동 조회 
	 * @param spLrnId
	 * @param spLrnNodId
	 * @param spLrnCtnId
	 * @return SlSpLrnLastPrgsDto
	 */
	@Operation(summary="특별학습 콘텐츠 활동조회(교사)", description="학습창에서 특별학습 콘텐츠활동정보를 조회한다.")
	@PostMapping(value = "/selectLrnCtnInfo",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<SlLrnwTocAtvDto> selectLrnCtnInfo(@RequestBody SlLrnwGetRequestDto reqeuestDto){
		log.debug("Entrance selectLrnCtnInfo");
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		return Response.ok(slSpLrnWifService.selectLastSlSpLrnPgrs(
				reqeuestDto, 
				securityUserDetailDto.getOptTxbId(), securityUserDetailDto.getUsrId(),securityUserDetailDto.getUsrTpCd()));
	}

	@Operation(summary="특별학습 결과물 조회(교사)", description="학습창에서 학생의 특별학습 콘텐츠활동정보를 조회한다.")
	@PostMapping(value = "/selectLastSlSpLrnPgrsResult",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<SlLrnwAtvMetaDto> selectLastSlSpLrnPgrsResult(@RequestBody SlLrnwGetRequestDto reqeuestDto){
		log.debug("Entrance selectLrnCtnInfo");
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		return Response.ok(slSpLrnWifService.selectLastSlSpLrnPgrsResult(
				reqeuestDto, 
				securityUserDetailDto.getOptTxbId()));
	}
	
	
}
