package com.aidt.api.al.cmt.dto.ett.cm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 15:10:58
 * @modify date 2024-05-24 15:10:58
 * @desc 영역별성취수준평가(영어) dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class N12Dto {

    private String lstngLvlCd;

    private String spkngLvlCd;

    private String rdngLvlCd;

    private String wrtngLvlCd;

    private String phncsLvlCd;

    private String wrdLvlCd;

    private String grmrLvlCd;

    private String alpbLvlCd;

    @Builder.Default
    private String usrTpCd = "ST";

    public String toMergeString() {
        return getNonNullString(spkngLvlCd) + getNonNullString(rdngLvlCd) + getNonNullString(wrtngLvlCd)
                + getNonNullString(phncsLvlCd) + getNonNullString(wrdLvlCd) + getNonNullString(grmrLvlCd)
                + getNonNullString(alpbLvlCd) + getNonNullString(lstngLvlCd);
    }

    private String getNonNullString(String value){
        return value == null ? "" : value;
    }
}