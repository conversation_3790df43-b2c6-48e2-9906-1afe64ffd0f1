package com.aidt.api.ea.evcom;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.aidt.api.ea.evcom.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.bc.mntr.stu.BcMntrStuService;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto;
import com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto;
import com.aidt.api.util.RedisUtil;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 14:00:오후 2:00
 * @modify date 2024-02-15 14:00:오후 2:00
 * @desc 평가(DIY 평가) 공통 -  Controller
 */

@Slf4j
@Tag(name="[ea] 평가 공통", description="평가 - 공통")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/cm/evcom")
public class EaEvComController {
    @Autowired
    private JwtProvider jwtProvider;

	@Autowired
	private BcCmService bcCmService;
	
	@Autowired
	private BcMntrStuService bcMntrStuService;

	@Autowired
    EaEvComService eaEvComService;


    @Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
    
    @Autowired
    private RedisUtil redisUtil;


    /**
     * 공통 코드 조회 요청
     * @param eaEvComDto
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="공통 코드 조회", description="공통 코드 조회")
    @PostMapping(value="/selectComCodList")
    public ResponseDto<List<Map<String,String>>> selectComCodList(@RequestBody EaEvComDto eaEvComDto) {
        log.debug("Entrance selectComCodList");
        return Response.ok(eaEvComService.selectComCodList(eaEvComDto));
    }
    /**
     * 단원 목록 조회 요청
     *
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="단원 목록 조회", description="단원 목록 조회")
    @PostMapping(value="/selectLuIdList")
    public ResponseDto<List<Map<String,String>>> selectLuIdList() {
        log.debug("Entrance selectLuIdList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        return Response.ok(eaEvComService.selectLuIdList(userDetails.getOptTxbId()));
    }

     /**
     * 중단원/소단원 학습맵노드 목록 조회 요청
     *
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="중단원/소단원 학습맵노드 목록 조회", description="중단원/소단원 학습맵노드 목록 조회")
    @PostMapping(value="/selectMluSluLrmpNodList")
    public ResponseDto<List<Map<String,String>>> selectMluSluLrmpNodList() {
        log.debug("Entrance selectMluSluLrmpNodList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        return Response.ok(eaEvComService.selectMluSluLrmpNodList(userDetails.getOptTxbId()));
    }
    
   /**
     * 중단원 목록 조회 요청
     *
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="중단원 목록 조회", description="중단원 목록 조회")
    @PostMapping(value="/selectMluLrmpNodId")
    public ResponseDto<List<Map<String,String>>> selectMluLrmpNodId(@RequestBody EaEvComDto eaEvComDto) {
        log.debug("Entrance selectMluLrmpNodId");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(eaEvComService.selectMluLrmpNodId(eaEvComDto));
    }

    /**
     * 차시 목록 조회 요청
     *
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="차시 목록 조회", description="차시 목록 조회")
    @PostMapping(value="/selectTcIdList")
    public ResponseDto<List<Map<String,String>>> selectTcIdList(@RequestBody EaEvComDto eaEvComDto) {
        log.debug("Entrance selectTcIdList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(eaEvComService.selectTcIdList(eaEvComDto));
    }

    
    /**
     * 평가 보충/심화 결과 등록 요청
     *
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="평가 보충/심화 결과 등록", description="평가 보충/심화 결과 등록")
    @PostMapping(value="/insertEaEvSppNtnQtmAnw")
    public ResponseDto<Map<String, String>> insertEaEvSppNtnQtmAnw(@Valid @RequestBody InsertEaEvQtmAnwReqDto param, HttpServletRequest request){
        log.debug("Entrance insertEaEvSppNtnQtmAnw");
        
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId());      //사용자 ID
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        param.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
        
        int result = eaEvComService.insertEaEvSppNtnQtmAnw(param);
        
        Map<String, String> map = new HashMap<>(); // 결과 리턴 map
        map.put("status", "400");
        
        if(result < 1){       
        	map.put("message", "평가 정보를 확인할 수 없습니다.");
        	return Response.ok(map);
        }
                

        try {
        	 // 학습자 수준 업데이트
        	eaEvComService.updateUsrLrnrVelTpCd(userDetails.getUsrId());
        	
        	// 풀이상태 문항 개별로 진행
//        	// 풀이상태 업데이트
//        	eaEvComService.updateEvQtmAnwXplStCd(userDetails.getUsrId(), param.getEvId());
        	
        	// 학습시간 업데이트
            Long evLrnTmScnt = 0L;
            if(param.getEvTmScnt() != null) {
            	evLrnTmScnt = (long)param.getEvTmScnt();
            }
	        BcCmLrnTmDto bcCmLrnTmDto = new BcCmLrnTmDto();
	        bcCmLrnTmDto.setEvLrnTmScnt(evLrnTmScnt);
            String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
            bcCmService.updateCmLrnTm(bcCmLrnTmDto, accessToken);        	
        }
        catch (Exception e) {
        	map.put("message", "학습 시간 정보 업데이트에 실패하였습니다"+ e.getMessage());
        	log.warn(e.getMessage());
        	return Response.ok(map);
        }
        
        map.put("status", "200");
        map.put("massage", "평가 보충/심화 결과 등록에 성공하였습니다.");
        return Response.ok(map);
    }
    
    /**
     * 평가 > 평가 결과 등록 요청
     *
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="평가 결과 등록", description="평가 결과 등록")
    @PostMapping(value="/insertEaEvQtmAnw")
    public ResponseDto<Map<String,String>> insertEaEvQtmAnw(
            @Valid @RequestBody InsertEaEvQtmAnwReqDto param, HttpServletRequest request){
        log.debug("Entrance insertEaEvQtmAnw");
        
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId());      //사용자 ID
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        param.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
        
        Map<String,String> result = eaEvComService.insertEaEvQtmAnw(param);
        if(result == null || !"200".equals(result.get("status"))){
        	result.put("message", "평가 정보를 확인할 수 없습니다.");
            return Response.ok(result);
        }
        
        try {
        	// 지도 이상 데이터 등록
        	String bcMntrStuMessage = "";
    		if("0".equals(result.get("cansCnt")))
    		{
    			bcMntrStuMessage = "평가 0점";                	
    		}
        	if(60 > param.getEvTmScnt()) {
        		if("".equals(bcMntrStuMessage))
        		{
        			bcMntrStuMessage = "풀이 시간 1분 미만";
        		}
        		else {
        			bcMntrStuMessage += " and 풀이 시간 1분 미만";
        		}
    		}
        	//학생이고 제출완료이고, 지도이상일 경우
        	if("ST".equals(userDetails.getUsrTpCd()) && "Y".equals(param.getEvCmplYn()) &&!"".equals(bcMntrStuMessage)){
            	BcMntrDto bcMntrDto = new BcMntrDto();
            	// 필요파라미터
            	bcMntrDto.setOptTxbId(userDetails.getOptTxbId());
            	bcMntrDto.setLrnUsrId(userDetails.getUsrId());
            	bcMntrDto.setGdeNeedCd("ERA"); // 고정값
            	bcMntrDto.setGdeNeedVl(param.getEvId().toString());
        		bcMntrDto.setGdeNeedInfo(bcMntrStuMessage);                	
               	
                bcMntrStuService.insertGdeNeed (bcMntrDto);        		
        	}

        	
        	// 학습자 수준 업데이트
        	eaEvComService.updateUsrLrnrVelTpCd(userDetails.getUsrId());
        	
        	// 단원별 학습자 수준 업데이트
        	Map<String, Object> luLv = new HashMap<>();
        	luLv.put("usrId", userDetails.getUsrId());
        	luLv.put("optTxbId", userDetails.getOptTxbId());
        	luLv.put("dbId", DB_ID);
        	
        	List<Map<String, Object>> luLvList = new ArrayList<>();
        	luLvList = eaEvComService.selectLluLrnrLvList(luLv);
        	if(null != luLvList) {
        		luLv.put("luLvList", luLvList);
        		
				eaEvComService.updateTlLuLrnrLv(luLv);
        	}
        	
        	
        	// 풀이상태 업데이트
        	eaEvComService.updateEvQtmAnwXplStCd(userDetails.getUsrId(), param.getEvId());
        	
        	// 학습시간 업데이트
            Long evLrnTmScnt = 0L;
            if(param.getEvTmScnt() != null) {
            	evLrnTmScnt = (long)param.getEvTmScnt();
            }
	        BcCmLrnTmDto bcCmLrnTmDto = new BcCmLrnTmDto();
	        if("OV".equals(param.getEvDtlDvCd())){
	        	bcCmLrnTmDto.setAiEvLrnTmScnt(evLrnTmScnt);
	        }else {
	        	bcCmLrnTmDto.setEvLrnTmScnt(evLrnTmScnt);
	        }
            String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
            bcCmService.updateCmLrnTm(bcCmLrnTmDto, accessToken);
			
			eaEvComService.saveMyhmPoint(jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader), param);
        }
        catch (Exception e) {
        	log.warn(e.getMessage());
        	result.put("status", "400");
        	result.put("success", "N");
        	result.put("message", e.getMessage());
        	
        	return Response.ok(result);
        }

        return Response.ok(result);
    }

    /**
     * 평가 결과 문항 개별 등록 요청
     *
     * @return ResponseDto<Map<String,String>>
     */
    @Operation(summary="평가 문항 개별답변 결과 등록", description="학습창에서 실시간으로 풀이한 평가 문항의 개별답변 결과 정보를 등록")
    @PostMapping(value="/insertEaEvQtmAnwEly")
    public ResponseDto<Map<String,String>> insertEaEvQtmAnwEly(@RequestBody InsertEaEvQtmAnwReqDto param, HttpServletRequest request){
        log.debug("Entrance insertEaEvQtmAnwEly");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId());      //사용자 ID
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        param.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
        
        Map<String,String> resultMap =  eaEvComService.insertEaEvQtmAnwEly(param);
        if(resultMap == null || !"200".equals(resultMap.get("status"))){       
        	resultMap.put("message", "평가 정보를 확인할 수 없습니다.");
            return Response.ok(resultMap); 
        }
        
        // AI 학습 시간 등록
		try {
			BcCmLrnTmDto bcCmLrnTmDto = new BcCmLrnTmDto();
			Long evLrnTmScnt = 0L;
			if (param.getXplTmScnt() != null) {
				evLrnTmScnt = (long) param.getXplTmScnt();
			}
			bcCmLrnTmDto.setAiEvLrnTmScnt(evLrnTmScnt);
            String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
            bcCmService.updateCmLrnTm(bcCmLrnTmDto, accessToken);
		} catch (Exception e) {
			resultMap.put("status", "400");
			resultMap.put("success", "N");
			resultMap.put("message", "AI 학습 시간 등록에 실패하였습니다." + e.getMessage());
			return Response.ok(resultMap);
		}
        
		resultMap.put("message", "평가 개별답변 결과 등록에 성공하였습니다.");
        return Response.ok(resultMap);
    }

    /**
     * 문항 개별 정답여부 조회
     *
     * @return ResponseDto<Map<String,String>>
     */
    @Operation(summary="문항 개별 정답여부 조회", description="문항 개별 정답여부 조회")
    @PostMapping(value="/selectQtmCansYn")
    public ResponseDto<Map<String,String>> selectQtmCansYn(@RequestBody InsertEaEvQtmAnwReqDto param){
        log.debug("Entrance selectQtmCansYn");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId());      //사용자 ID
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        param.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
        return Response.ok(eaEvComService.selectQtmCansYn(param));
    }


    /**
     * 오답노트 > 오답노트 결과 등록 요청
     *
     * @return ResponseList<Map<String,Object>>
     */
    @Operation(summary="오답노트 결과 등록", description="오답노트 결과 등록")
    @PostMapping(value="/insertLansNteQtmAnw")
    public ResponseDto<Map<String,Object>> insertLansNteQtmAnw(@RequestBody InsertEaEvQtmAnwReqDto param) throws Exception {
        log.debug("Entrance insertLansNteQtmAnw");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId());      //사용자 ID
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        return Response.ok(eaEvComService.insertLansNteQtmAnw(param));
    }

    /**
     * 학습창 평가 문항 노트 조회
     * @param eaEvComDto
     * @return
     */
    @Operation(summary="학습창 평가 문항 노트 조회", description="학습창 평가 문항 노트 조회")
    @PostMapping(value="/selectEaEvQtmNote")
    public ResponseDto<List<Map<String, Object>>> selectEaEvQtmNote(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEaEvQtmNote");
        return Response.ok(eaEvComService.selectEaEvQtmNote(eaEvComDto));
    }

    /**
     * 학습창 평가 보충/심화 문항정보 리스트 조회
     * @param eaEvComDto
     * @return
     */
    @Operation(summary="학습창 평가 보충/심화 문항정보 리스트 조회", description="학습창 평가 보충/심화 문항정보 리스트 조회")
    @PostMapping(value="/selectEaEvSppNtnQtmList")
    public ResponseDto<List<Map<String, Object>>> selectEaEvSppNtnQtmList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEaEvSppNtnQtmList");
        
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        
        return Response.ok(eaEvComService.selectEaEvSppNtnQtmList(eaEvComDto));
    }
    /**
     * 학습창 채점완료 - 평가 보충/심화 문항답변목록 조회
     * @param eaEvComDto
     * @return
     */
    @Operation(summary="학습창 채점완료 - 평가 보충/심화 문항답변목록 조회", description="학습창 채점완료 - 평가 보충/심화 문항답변목록 조회")
    @PostMapping(value="/selectEvSppNtnQtmAnwList")
    public ResponseDto<List<EaEvComQtmAnwDto>> selectEvSppNtnQtmAnwList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvSppNtnQtmAnwList");
        
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        
        return Response.ok(eaEvComService.selectEvSppNtnQtmAnwList(eaEvComDto));
    }
    
    
    /**
     * 학습창 평가 문항 리스트 조회
     *
     * @return ResponseDto<Map<String,Object>>
     */
    @Operation(summary="학습창 평가 문항 리스트 조회", description="학습창 평가 문항 리스트 조회")
    @PostMapping(value="/selectEaEvQtmList")
    public ResponseDto<Map<String,Object>> selectEaEvQtmList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEaEvQtmList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        return Response.ok(eaEvComService.selectEaEvQtmList(eaEvComDto));
    }

    /**
     * 문항정보 단건 조회
     *
     * @return ResponseDto<Map<String,Object>>
     */
    @Operation(summary="문항정보 단건 조회", description="문항정보 단건 조회")
    @PostMapping(value="/selectQtmInfo")
    public ResponseDto<Map<String,Object>> selectQtmInfo(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectQtmInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        eaEvComDto.setTxbId(userDetails.getTxbId()); // TODO DBID == 교과서ID
        return Response.ok(eaEvComService.selectQtmInfo(eaEvComDto));
    }


    /**
     * 학생 - 평가 리포트 - 응시회차 리스트 조회 요청
     *
     * @param eaEvComDto
     * @return ResponseDto<EaEvMainResDto>
     */
    @Operation(summary="학생 평가 리포트 응시회차리스트 조회", description="학생 평가 리포트 응시회차리스트 조회")
    @PostMapping(value="/selectEvTxmPnList")
    public ResponseDto<List<EaEvMainResDto>> selectEvTxmPnList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvTxmPnList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        return Response.ok(eaEvComService.selectEvTxmPnList(eaEvComDto));
    }

    /**
     * 공통 최대 응시회차 조회
     *
     * @param eaEvComDto
     * @return
     */
    @Operation(summary="공통 최대 응시회차 조회", description="학공통 최대 응시회차 조회")
    @PostMapping(value="/selectEvRtxmPn")
    public ResponseDto<List<EaEvMainResDto>> selectEvRtxmPn(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvRtxmPn");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        return Response.ok(eaEvComService.selectEvRtxmPn(eaEvComDto));
    }

    /**
     * 학생 - 평가리포트 조회 요청 > 평가 메인 정보
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    @Operation(summary="학생 평가리포트 평가메인 조회", description="학생 평가리포트 평가메인 조회")
    @PostMapping(value="/selectEvRptStuEvMainInfo")
    public ResponseDto<Map<String, Object>> selectEvRptStuEvMainList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance 평가메인");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        
        return Response.ok(eaEvComService.selectEvRptStuEvMainList(eaEvComDto));
    }
    
    /**
     * 학생 - 평가리포트 조회 요청 > 수학
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    @Operation(summary="학생 평가리포트 수학 조회", description="학생 평가리포트 수학 조회")
    @PostMapping(value="/selectEvRptStuMaList")
    public ResponseDto<Map<String, Object>> selectEvRptStuMaList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvRptStuMaList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        
        return Response.ok(eaEvComService.selectEvRptStuMaList(eaEvComDto));
    }
    
    /**
     * 학생 - 평가리포트 조회 요청 > 영어
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    @Operation(summary="학생 평가리포트 영어 조회", description="학생 평가리포트 영어 조회")
    @PostMapping(value="/selectEvRptStuEnList")
    public ResponseDto<Map<String, Object>> selectEvRptStuEnList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvRptStuEnList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        
        return Response.ok(eaEvComService.selectEvRptStuEnList(eaEvComDto));
    }
    
    /**
     * 학생 - 평가리포트 정오답현황 조회 요청
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    @Operation(summary="학생 평가리포트 > 정오답현황 조회", description="학생 평가 > 정오답현황 조회")
    @PostMapping(value="/selectEvRptStuQtmOxList")
    public ResponseDto<List<Map<String, Object>>> selectEvRptStuQtmOxList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvRptStuQtmOxList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        return Response.ok(eaEvComService.selectEvRptStuQtmOxList(eaEvComDto));
    }
    
    
    /**
     * 학생 평가리포트 > 단원평가 > 단원성취도 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    @Operation(summary="학생 평가리포트 > 단원평가 > 단원성취도 조회", description="학생 평가리포트 > 단원평가 > 단원성취도 조회")
    @PostMapping(value="/selectEvRptStuUgAchdList")
    public ResponseDto<List<Map<String, Object>>> selectEvRptStuUgAchdList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvRptStuUgAchdList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        return Response.ok(eaEvComService.selectEvRptStuUgAchdList(eaEvComDto));
    }

    /**
     * 학생 - 평가리포트 조회 요청
     *
     * @param eaEvComDto
     * @return Map<String, Object>
     */
    @Operation(summary="학생 평가리포트 > 단원평가 > 단원성취도분석 > 성쥐기준별 > 학습차시ID조회", description="학생 평가 > 단원평가 > 단원성취도분석 > 성쥐기준별 > 학습차시ID조회")
    @PostMapping(value="/selectEvRptStuUgAchdBsChList")
    public ResponseDto<List<Map<String, Object>>> selectEvRptStuUgAchdBsChList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvRptStuUgAchdBsChList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        return Response.ok(eaEvComService.selectEvRptStuUgAchdBsChList(eaEvComDto));
    }

    /**
     * 평가 채점완료 답변리스트 조회 요청 ==> 교사에서 호출하여 공통으로 전환
     *
     * @param eaEvComDto
     * @return ResponseDto<List<EaEvComQtmAnwDto>>
     */
    @Operation(summary="평가 채점완료 답변리스트 조회", description="평가 채점완료 답변리스트 조회")
    @PostMapping(value="/selectEvQtmAnwList")
    public ResponseDto<List<EaEvComQtmAnwDto>> selectEvQtmAnwList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
        log.debug("Entrance selectEvQtmAnwList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")
        {
            eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        return Response.ok(eaEvComService.selectEvQtmAnwList(eaEvComDto));
    }

    /**
     * 학습 리포트 - 선생,학생 main 공통 조회
     *
     * @return ResponseDto<Map<String,Object>>
     */
    @Operation(summary="학습 리포트 조회", description="선생,학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectEaLrnRptMainInfo")
    public ResponseDto<Map<String,Object>> selectEaLrnRptMainInfo(@RequestBody EaLrnRptReqDto param) {
        log.debug("Entrance selectEaLrnRptMainInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        if(param.getUsrId() == null || param.getUsrId().trim() == "") {
            param.setUsrId(userDetails.getUsrId());      //사용자 ID
        }
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        param.setClaId(userDetails.getClaId());      //학급 ID
        return Response.ok(eaEvComService.selectEaLrnRptMainInfo(param));
    }

    /**
     * 단원별 성취 현황 - 차시별 푼 문제 + 맞힌 문제 + 정답률 조회
     *
     * @return ResponseDto<Map<String,Object>>
     */
    @Operation(summary="단원별 성취 현황 - 차시별 푼 문제 + 맞힌 문제 + 정답률 조회", description="단원별 성취 현황 - 차시별 푼 문제 + 맞힌 문제 + 정답률 조회")
    @PostMapping(value="/selectTcList")
	public ResponseDto<List<Map<String,Object>>> selectTcList(@RequestBody EaLrnRptReqDto param) {
        log.debug("Entrance selectQpTcInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId());      //사용자 ID
        param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
        return Response.ok(eaEvComService.selectTcList(param));
    }

	/**
	 * 교사 - 평가 잠금여부 설정 요청
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
    
	@Operation(summary="평가 잠금여부 설정 요청", description="평가 잠금여부 설정 요청")
	@PostMapping(value="/updateEvLockYn",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateEvLockYn(@Valid @RequestBody EaEvUpdateLockUseYnDto evReqDto) {
		log.debug("Entrance updateEvLockYn");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		
		// 원클릭 학습설정 캐시 초기화
		String searchKey = "longCache:tl:" + userDetails.getOptTxbId() + ":txbTcList*";
	    redisUtil.redisKeyDeleteArray(searchKey);

		return Response.ok(eaEvComService.updateEvLockYn(evReqDto));
	}
	/**
	 * 교사 - 평가 사용여부 설정 요청
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	
	@Operation(summary="평가 사용여부 설정 요청", description="평가 사용여부 설정 요청")
	@PostMapping(value="/updateEvUseYn",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateEvUseYn(@Valid @RequestBody EaEvUpdateLockUseYnDto evReqDto) {
		log.debug("Entrance updateEvUseYn");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID

		// 원클릭 학습설정 캐시 초기화
		String searchKey = "longCache:tl:" + userDetails.getOptTxbId() + ":txbTcList*";
	    redisUtil.redisKeyDeleteArray(searchKey);
	    
		return Response.ok(eaEvComService.updateEvUseYn(evReqDto));
	}
	/**
	 * 교사 - 원클릭학습설정 대단원/차시 잠금/사용 여부 수정시 평가 적용
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Operation(summary="원클릭학습설정 대단원/차시 잠금/사용여부 평가 적용", description="원클릭학습설정 대단원/차시 잠금/사용여부 평가 적용")
	@PostMapping(value="/updateEvOneClickLrnSetm",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<List<Map<String, Object>>> updateEvOneClickLrnSetm(@Valid @RequestBody EaEvUpdateLockUseYnDto evReqDto) {
		log.debug("Entrance updateEvOneClickLrnSetm");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID

		return Response.ok(eaEvComService.updateEvOneClickLrnSetm(evReqDto));
	}
	
	/**
	 * 교사 - 원클릭학습설정 대단원/차시 잠금/사용 여부 수정시 평가 적용
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Operation(summary="원클릭학습설정 다른학급 대단원/차시 잠금/사용여부 평가 적용", description="원클릭학습설정 다른학급 대단원/차시 잠금/사용여부 평가 적용")
	@PostMapping(value="/updateOtherClaOneClickLrnSetm",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<List<Map<String, Object>>> updateOtherClaOneClickLrnSetm(@Valid @RequestBody EaEvUpdateLockUseYnDto evReqDto) {
		log.debug("Entrance updateOtherClaOneClickLrnSetm");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		return Response.ok(eaEvComService.updateOtherClaOneClickLrnSetm(evReqDto));
	}

	
	/**
	 * 교사 - 원클릭학습설정 대단원/차시 잠금/사용 여부 수정시 평가 적용
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Operation(summary="원클릭학습설정 AI학습 잠금/사용여부 평가 적용", description="원클릭학습설정 AI학습 잠금/사용여부 평가 적용")
	@PostMapping(value="/updateEvOneClickLrnSetmAL",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateEvOneClickLrnSetmAL(@Valid @RequestBody EaEvUpdateLockUseYnDto evReqDto) {
		log.debug("Entrance updateEvOneClickLrnSetmAL");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID

		return Response.ok(eaEvComService.updateEvOneClickLrnSetmAL(evReqDto));
	}


    /**
     * 교사 > 학생 분석 상세 > 학생 리스트 조회
     *
     * @param null
     * @return Map<String, Object>
     */
    @Operation(summary="학생 리스트 조회", description="학생 리스트  조회")
    @PostMapping(value="/selectUserList")
    public ResponseDto<List<Map<String, Object>>> selectUserList() {
        log.debug("Entrance selectUserList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        return Response.ok(eaEvComService.selectUserList(userDetails.getClaId()));
    }

    /**
     * LCMS > LMS 이관 후 LMS 평가 생성
     *
     * @param null
     * @return ResponseDto<Integer>
     */
    @Operation(summary="LCMS 교과평가 생성", description="LCMS 교과평가 생성")
    @PostMapping(value="/createLcmsEv")
    public ResponseDto<Integer> createLcmsEv() {
        log.debug("Entrance createLcmsEv");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		String usrId = "system"; //userDetails.getUsrId()  2024-08-28 변경
        return Response.ok(eaEvComService.createLcmsEv(usrId, userDetails.getOptTxbId(), userDetails.getTxbId(), userDetails.getClaId(), DB_ID));
    }
    
	/**
	 * 학생, 교사 로그인 시 평가결과 추가 등록
	 *
	 * @param String usrId, String optTxbId
	 * @return int
	 */
    @Operation(summary="학생, 교사 로그인 시 평가결과 추가 등록", description="학생, 교사 로그인 시 평가결과 추가 등록")
    @PostMapping(value="/insertEvRsLoginAddStu")
    public ResponseDto<Integer> insertEvRsLoginAddStu() {
        log.debug("Entrance createLcmsEv");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        return Response.ok(eaEvComService.insertEvRsLoginAddStu(userDetails.getUsrId(), userDetails.getOptTxbId()));
    }
    
    /**
     * 교사 메인 평가 성쥐기준별 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    @Operation(summary="교사 메인 평가 성쥐기준별 조회 ", description="교사 메인 평가 성쥐기준별 조회 ")
    @PostMapping(value="/selectEvRptTcrAchdBsList")
    public ResponseDto<List<Map<String, Object>>> selectEvRptTcrAchdBsList(long evlId) {
        log.debug("Entrance selectEvRptTcrAchdBsList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        EaEvComQtmReqDto eaEvComDto = new EaEvComQtmReqDto();
        eaEvComDto.setEvId(evlId);
        eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); 
        return Response.ok(eaEvComService.selectEvRptTcrAchdBsList(eaEvComDto));
    }
    /**
     * 교사 메인 평가 AI 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    @Operation(summary="교사 메인 평가 AI 조회 ", description="교사 메인 평가 AI 조회 ")
    @PostMapping(value="/selectEvRptTcrAiList")
    public ResponseDto<List<Map<String, Object>>> selectEvRptTcrAiList(long evlId) {
    	log.debug("Entrance selectEvRptTcrAchdBsList");
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	EaEvComQtmReqDto eaEvComDto = new EaEvComQtmReqDto();
    	eaEvComDto.setEvId(evlId);
    	eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); 
    	return Response.ok(eaEvComService.selectEvRptTcrAiList(eaEvComDto));
    }
    /**
     * 교사 메인 평가 현황 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    @Operation(summary="교사 메인 평가 현황 조회 ", description="교사 메인 평가 현황 조회 ")
    @PostMapping(value="/selectEvRptTcrState")
    public ResponseDto<List<Map<String, Object>>> selectEvRptTcrState(long evlId) {
    	log.debug("Entrance selectEvRptTcrAchdBsList");
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	EaEvComQtmReqDto eaEvComDto = new EaEvComQtmReqDto();
    	eaEvComDto.setEvId(evlId);
    	eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); 
    	return Response.ok(eaEvComService.selectEvRptTcrState(eaEvComDto));
    }

    /**
     * 현재 회차 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<String> 현재 회차
     */
    @Operation(summary="현재 회차 조회 ", description="현재 회차 조회 ")
    @PostMapping(value="/selectCurrentTxmPn")
    public ResponseDto<String> selectCurrentTxmPn(@RequestBody EaEvComQtmReqDto eaEvComDto) {
    	log.debug("Entrance selectCurrentTxmPn");
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        // 교사가 학생 정보 조회하는 경우 사용자 타입을 학생으로
        if ("TE".equals(userDetails.getUsrTpCd()) && eaEvComDto.getUsrId() != null && eaEvComDto.getUsrId().trim() != "") {
        	eaEvComDto.setUsrTpCd("ST");
        } else {
        	eaEvComDto.setUsrTpCd(userDetails.getUsrTpCd()); // 사용자 타입: 교사(TE), 학생(ST)
        }
    	// 교사는 학생의 평가 회차 정보 조회 가능
        if("TE".equals(userDetails.getUsrTpCd()) && (eaEvComDto.getUsrId() == null || eaEvComDto.getUsrId().trim() == "")) {
            eaEvComDto.setUsrId(userDetails.getUsrId());      //사용자 ID
        }
    	return Response.ok(eaEvComService.selectCurrentTxmPn(eaEvComDto));
    }
    
    /**
     * 평가완료 후 국가표준쳬계 교육과정에 포함된 문항들의 진척율 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    @Operation(summary="평가완료 후 국가표준쳬계 교육과정에 포함된 문항들의 진척율 조회 ", description="평가완료 후 국가표준쳬계 교육과정에 포함된 문항들의 진척율 조회 ")
    @PostMapping(value="/selectEvCmplCrclCtnElm2CdList")
    public ResponseDto<List<Map<String, Object>>> selectEvCmplCrclCtnElm2CdList(@RequestBody EaEvComQtmReqDto eaEvComDto) {
    	log.debug("Entrance selectEvCmplCrclCtnElm2CdList");
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	eaEvComDto.setUsrId(userDetails.getUsrId());      //사용자 ID
    	eaEvComDto.setOptTxbId(userDetails.getOptTxbId()); 
    	return Response.ok(eaEvComService.selectEvCmplCrclCtnElm2CdList(eaEvComDto));
    }

    /**
     * 평가 목록 학습창 호출 시 응시 정보 조회
     *
     * @param EaEvComQtmReqDto
     * @return List<Map<String, Object>>
     */
    @Operation(summary="평가 목록 학습창 호출 시 응시 정보 조회 ", description="평가 목록 학습창 호출 시 응시 정보 조회회 ")
    @PostMapping(value="/selectEvTxmInfo")
    public ResponseDto<InsertEaEvQtmAnwReqDto> selectEvTxmInfo(@RequestBody InsertEaEvQtmAnwReqDto eaEvComDto) {
    	log.debug("Entrance selectEvTxmInfo");
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

//		if(null == eaEvComDto.getUsrId() || "".equals(eaEvComDto.getUsrId().trim()))
//		{
//			eaEvComDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
//		}

    	eaEvComDto.setUsrId(userDetails.getUsrId());      //사용자 ID
    	eaEvComDto.setOptTxbId(userDetails.getOptTxbId());
    	return Response.ok(eaEvComService.selectEvTxmInfo(eaEvComDto));
    }

    /**
     * 평가 문항에 연결된 노트 정보 업데이트
     *
     * @param UpdateNoteReqDto
     * @return ResponseDto<Map<String, String>>
     */
    @Operation(summary="평가 문항에 연결된 노트 정보 업데이트", description="평가 문항에 연결된 노트 정보 업데이트")
    @PostMapping(value="/updateNoteStatus")
    public ResponseDto<Map<String, String>> updateNoteStatus(@RequestBody UpdateNoteReqDto param) {
        log.debug("Entrance updateNoteStatus");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        param.setUsrId(userDetails.getUsrId()); // 사용자 ID

        return Response.ok(eaEvComService.updateNoteStatus(param));
    }

}
