package com.aidt.api.al.pl.cm.rcm.helper;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.aidt.api.al.pl.cm.rcm.dto.EaAiEv;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvPredictProfile;
import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.api.bc.cm.textbook.dto.Textbook;
import com.aidt.api.bc.cm.textbook.service.BcTextbookQueryService;
import com.aidt.api.common.enums.SubjectCode;
import com.aidt.api.common.helper.JwtHelper;
import com.aidt.api.error.ApiErrorLogException;
import com.aidt.api.error.ErrorCode;
import com.aidt.api.util.WebClientUtil;
import com.aidt.common.CommonUserDetail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AiCenterHelper {

	@Value("${aidt.endpoint.aicenter.url}")
	private String aiUrl;
	@Value("${aidt.endpoint.aicenter.token}")
	private String token;

	private final WebClientUtil webFluxUtil;
	private final BcTextbookQueryService bcTextbookQueryService;
	private final JwtHelper jwtHelper;

	@Getter
	@NoArgsConstructor
	@AllArgsConstructor
	public static class AiCenterRequest {

		private List<AiPredictionRequest> data;

	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class AiPredictionRequest {

		private String usr_id; //사용자 아이디
		private Long qtm_id; //문항 아이디
		private String qp_dffd_cd; //문항 난이도
		private String lrnr_vel_tp_cd; //토픽 숙련도
		private Integer sbc_lrn_avg_ans_rt; //차시의 교과학습 평균 정답률
		private Integer pre_tc_ans_rt; //이전 차시의 평균 정답률

		public static AiPredictionRequest of(EaAiEv eaAiEv, EaAiEvPredictProfile predictProfile) {
			return AiPredictionRequest.builder()
				.usr_id(eaAiEv.getUsrId())
				.qtm_id(predictProfile.getQtmId())
				.qp_dffd_cd(predictProfile.getCtnDffdDvCd())
				.lrnr_vel_tp_cd(predictProfile.getLrnrVelTpCd())
				.sbc_lrn_avg_ans_rt(0)
				.pre_tc_ans_rt(0)
				.build();
		}

	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class AiCenterResponse {

		private boolean status; //요청 결과 성공 여부
		private String message; //요청 결과 상세 내용
		private List<AiPredictionCorrectAnswerRate> result; //응답 값

	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class AiPredictionCorrectAnswerRate {

		private String usr_id; //사용자 아이디
		private Long qtm_id; //문항 아이디
		private Double infer; //정답예측률

	}

	public List<AiPredictionCorrectAnswerRate> getAiPredictionCorrectAnswerRates(List<AiPredictionRequest> request,
		CommonUserDetail commonUserDetail) {

		var user = commonUserDetail != null ? commonUserDetail : jwtHelper.getCommonUserDetail();
		if (ObjectUtils.isEmpty(user)) {
			throw new BizException("사용자 정보를 찾을 수 없습니다.");
		}
		return getAiPredictionCorrectAnswerRates(request, getTextbook(user.getTxbId()));
	}

	public List<AiPredictionCorrectAnswerRate> getAiPredictionCorrectAnswerRates(List<AiPredictionRequest> request,
		Textbook textbook) {

		if (CollectionUtils.isEmpty(request)) {
			log.error("AI 정답 예측률 조회를 위한 파라미터가 잘못되었습니다.");
			throw new IllegalArgumentException("AI 정답 예측률 조회를 위한 파라미터가 잘못되었습니다.");
		}

		try {
			HttpHeaders headers = getAuthHeaders();
			var res = webFluxUtil.postWithError(aiUrl + generateTextbookPath(textbook)
				, headers
				, new AiCenterRequest(request)
				, AiCenterResponse.class);

			if (!res.status) {
				log.error("AI Center 요청에 실패하였습니다.: {}", res.getMessage());
				throw new ApiErrorLogException(ErrorCode.AI_CENTER, res.getMessage(), ErrorCode.TYPE_EXTERNAL);
			}
			return res.getResult();

		} catch (WebClientResponseException e) {
			String status = e.getStatusCode().toString();
			String body = e.getResponseBodyAsString();
			String message = String.format("status=%s, body=%s", status, body);
			log.error("AI Center 통신에 실패하였습니다. :: {}", message);
			throw new ApiErrorLogException(ErrorCode.AI_CENTER, message, ErrorCode.TYPE_EXTERNAL);
		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.AI_CENTER, e, ErrorCode.TYPE_EXTERNAL);
		}

	}

	private Textbook getTextbook(String textbookId) {
		return bcTextbookQueryService.getTextbook(textbookId);
	}

	private String generateTextbookPath(Textbook textbook) {

		return String.format("%s/%s%d/%s"
			, SubjectCode.getSubjectCode(textbook.getSbjCd()).getField().toLowerCase()
			, textbook.getSchlGrdCd()
			, textbook.getSgyCd() < 1 ? 1 : textbook.getSgyCd()
			, textbook.getAutrCd()
		).toLowerCase();

	}

	private HttpHeaders getAuthHeaders() {

		HttpHeaders headers = new HttpHeaders();
		headers.add("Content-Type", "application/json");
		headers.add("accept", "application/json");
		headers.add("Authorization", token);

		return headers;
	}

}
