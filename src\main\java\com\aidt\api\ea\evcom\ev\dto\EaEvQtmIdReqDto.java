package com.aidt.api.ea.evcom.ev.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 13:58:오후 1:58
 * @modify date 2024-03-05 13:58:오후 1:58
 * @desc   평가 공통 - 문항리스트 조회 결과 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvQtmIdReqDto {

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	
	
	@Parameter(name="학교반ID")
	private String claId;	

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="데이터베이스ID")
	private String dbId;
	
    @Parameter(name="문항 ID")
    private String qtmId;
	
    @Parameter(name="문항 순번")
    private int qtmOrdn;	
    
    @Parameter(name="원문항 ID")
    private String srcQtmId;
    
	@Parameter(name="토픽ID_지식맵")
	private String tpcId;    
	
	@Parameter(name="연관문항구분코드")
	private String rltQtmTpCd;	
    
	@Parameter(name="문항난이도CD")
	private String qtmDffdCd;

	@Parameter(name="대단원노드ID")
	private String lluNodId;	
	@Parameter(name="대단원노드명")
	private String lluNodNm;	
	
	@Parameter(name="오답문제생성여부")
	private String iansQtmCrtYn = "N";
	
	@Parameter(name="평가ID")
	private long evId;
	
	@Parameter(name="이전평가ID")
	private long evIdOld;	

	@Parameter(name="평가명")
	private String evNm;
	
	@Parameter(name="평가구분코드")
	private String evDvCd = "DE";

	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
	@Parameter(name="문제수")
	private int qstCnt;
	
	@Parameter(name="번호")
	private int rowNo;	
	
	@Parameter(name="오답문항수")
	private int iansQtmCnt;	
	
	@Parameter(name="버킷url")
	private String bucketUrl;
	
	@Parameter(name="문항 리스트")
	List<EaEvQtmIdReqDto> qtmIdList;
	
	@Parameter(name="문항 리스트")
	List<EaEvQtmIdReqDto> smrlQtmList;
	
	@Parameter(name="대단원 리스트")
	List<EaEvQtmIdReqDto> crtLluList;
	
	@Parameter(name="문항 등록 리스트")
	List<EaEvQtmDto> crtEaEvQtmList;
}
