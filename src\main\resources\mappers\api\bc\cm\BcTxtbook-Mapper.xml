<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.common.cm">

    <select id="getTextbook" parameterType="String" resultType="com.aidt.api.bc.cm.textbook.dto.Textbook">
        SELECT *
        FROM LMS_CMS.BC_TXB BT
        WHERE TXB_ID = #{txbId}
          AND USE_YN = 'Y'
          AND DEL_YN = 'N'
    </select>

</mapper>
