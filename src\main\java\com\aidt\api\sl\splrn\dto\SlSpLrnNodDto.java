package com.aidt.api.sl.splrn.dto;

import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-13 8:41:26
 * @modify : date 2024-02-13 8:41:26
 * @desc : 특별학습 노드 DTO
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnNodDto {

	@Parameter(name="특별학습ID")
	private String spLrnId;

	@Parameter(name="특별학습노드ID")
	private String spLrnNodId;

	@Parameter(name="특별학습 상위노드 ID")
	private String urnkSpLrnNodId;
	
	@Parameter(name="특별학습노드코드")
	private String spLrnNodCd;

	@Parameter(name="특별학습노드명")
	private String spLrnNodNm;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="깊이")
	private int dpth;

	@Parameter(name="최하위여부")
	private String lwsYn;

	@Parameter(name="첨부파일여부")
	private String annxFleYn;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성일시")
	private Date crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private Date mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;
	
	@Parameter(name="특별학습 노드의 콘텐츠")
	private List<SlSpLrnCtnDto> spLrnCtnList;
}
