package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "학급목록")
public class BcClaListDto {
	
	@Parameter(name="사용자ID")
    private String usrId;

	@Parameter(name="학급ID")
	private String claId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="반번호")
	private int claNo;

	@Parameter(name="반명")
	private String claNm;
	
	@Parameter(name="학교코드(학교ID)")
	private String schlCd;
	
	@Parameter(name="학교명")
	private String schlNm;

	@Parameter(name="학년")
	private String sgy;
	
	@Parameter(name="학년 반")
	private String classroomName;

}
