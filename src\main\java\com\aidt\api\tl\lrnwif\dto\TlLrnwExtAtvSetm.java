package com.aidt.api.tl.lrnwif.dto;

import java.util.List;

import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TlLrnwExtAtvSetm {
	/** 운영교과서 ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
	/** 클래스핑퐁사용여부 */
    @Parameter(name="클래스핑퐁사용여부")
    private String clsPpngUseYn;
    
    /** 클래스링크사용여부 */
    @Parameter(name="클래스링크사용여부")
    private String clsLinkUseYn;
    
    /** 클래스아카이브사용여부 */
    @Parameter(name="클래스아카이브사용여부")
    private String clsArchvUseYn;
    
    /** 우리반T셀파사용여부 */
    @Parameter(name="우리반T셀파사용여부")
    private String clsTshrpUseYn;
    
    /** 패들렛사용여부 */
    @Parameter(name="패들렛사용여부")
    private String padlUseYn;
    
    /** 캔바사용여부 */
    @Parameter(name="캔바사용여부")
    private String canvUseYn;
    
    /** 미리캔버스사용여부 */
    @Parameter(name="미리캔버스사용여부")
    private String mirCanvUseYn;
    
    /** 구글문서사용여부 */
    @Parameter(name="구글문서사용여부")
    private String gglDocUseYn;
    
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;

    /** dbID */
    @Parameter(name="dbID")
    private String dbId;
    
    /** 추가외부링크 */
    @Parameter(name="추가외부링크")
    private List<TlLrnwExtrLink> extrLink;
    
    /** 원클릭학습설정 다른 학급 리스트 */
    @Parameter(name="원클릭학습설정 다른 학급 리스트")
    private List<TlOneClkSetmClaDto> claList;
}
