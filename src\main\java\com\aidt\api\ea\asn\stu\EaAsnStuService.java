package com.aidt.api.ea.asn.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.al.pl.cm.mg.AlMgService;
import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto;
import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto.usrIdList;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto;
import com.aidt.api.ea.grpblbd.stu.dto.EaUserSessionDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 과제 - 학생 Service
 */
@Service
public class EaAsnStuService {

	private final String MAPPER_NAMESPACE = "api.ea.asn.stu.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private JwtProvider jwtProvider;
	
    @Value("${aidt.endpoint.lw_myhm_stu_point:}")
	private String endpoint_lw_myhm_stu_point;
    
    @Autowired
    private WebFluxUtil webFluxUtil;
    
	@Autowired
	private AlMgService alMgService;
	
	@Autowired
	private EaAsnComService eaAsnComService;
	


	// Return Object
	List<EaAsnStuDto> responseList = new ArrayList<>();

	/**
	 * 과제 목록 조회 - 학생
	 * @param eaAsnStuDto
	 * @return List<EaAsnStuDto>
	 */
	public List<EaAsnStuDto> selectAsnStuList(EaAsnStuDto eaAsnStuDto) {
		List<EaAsnStuDto> responseStuList = new ArrayList<>();

		List<EaUserSessionDto> userInfo = this.setSessionInfo();
		eaAsnStuDto.setStuUsrId(userInfo.get(0).getSessionUsrId());
		eaAsnStuDto.setOptTxbId(userInfo.get(0).getSessionOptTxbId());
		
		if(!StringUtils.isEmpty(eaAsnStuDto.getSearchOption())) {
    		//ai 지식맵ID 조회
			List<String> kmmpNodIds = alMgService.selectKmmpNodIdByLrmpNodId(eaAsnStuDto.getSearchOption());
			eaAsnStuDto.setAiSearchOptionList(kmmpNodIds);
    	}

		// 과제 목록 조회
		responseStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnList" ,eaAsnStuDto);	
		return responseStuList;
	}


	/**
	 * 과제 제출 - 학생
	 * @param eaAsnStuDto
	 * @return eaAsnStuDto
	 */
	public Map<String, Object> updateAsnStu(EaAsnStuDto eaAsnStuDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaAsnStuDto.setStuUsrId(userDetails.getUsrId());
		eaAsnStuDto.setMdfrId(userDetails.getUsrId());
		eaAsnStuDto.setOptTxbId(userDetails.getOptTxbId());
		Map<String, Object> result =  new HashMap<>();
		
		//과제 제출전 과제 확인
		EaAsnStuDto resEaAsnDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnDetail" ,eaAsnStuDto);
		if(resEaAsnDto == null || resEaAsnDto.getAsnId() == null) {
			result.put("resCd", "fail");
			result.put("resMsg", "과제 정보를 확인할 수 없습니다.");
			return result;
		}
	

		String smtYn = commonDao.select(MAPPER_NAMESPACE + "selectSmtYn", eaAsnStuDto);
		eaAsnStuDto.setSmtCmplYn(smtYn);
		//과제 제출 업데이트
		int check = commonDao.update(MAPPER_NAMESPACE + "updateStuAsn" ,eaAsnStuDto);
		result.put("check", check);

		return result;

	}
	
	/**
	 *  모둠 과제 제출 또는 수정
	 * @param eaAsnStuDto
	 * @return hashMap
	 */
	public Map<String, Object> updateAsnStuDetail(EaAsnStuDto eaAsnStuDto) {
		Map<String, Object> resMap = new HashMap<>();

		int cnt = 0;

			List<EaUserSessionDto> userInfo = this.setSessionInfo();
			eaAsnStuDto.setStuUsrId(userInfo.get(0).getSessionUsrId());
			eaAsnStuDto.setMdfrId(userInfo.get(0).getSessionUsrId());
			eaAsnStuDto.setOptTxbId(userInfo.get(0).getSessionOptTxbId());
			
			// 과제 상세 조회
			EaAsnStuDto grpDto = commonDao.select(MAPPER_NAMESPACE + "selectGrpStuDetail" ,eaAsnStuDto);
			if(grpDto == null || grpDto.getAsnId() == null) {
				resMap.put("result", "Fail");
				resMap.put("resultMsg", "과제 정보를 확인할 수 없습니다.");
				return resMap;
			}

			List<usrIdList> usrIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpInfo", eaAsnStuDto);
			eaAsnStuDto.setUsrIdList(usrIdList);

			String smtYn = commonDao.select(MAPPER_NAMESPACE + "selectSmtYn", eaAsnStuDto);
			eaAsnStuDto.setSmtCmplYn(smtYn);

			//  모둠 과제 제출 또는 수정
			cnt += commonDao.update(MAPPER_NAMESPACE + "updateAsnStuDetail", eaAsnStuDto);

			// 모둠 과제 제출 처리
			if(eaAsnStuDto.getGrpId() != 0) {
				// 모둠 과제 최초 제출일시 조회
				String fstSmtDtm = commonDao.select(MAPPER_NAMESPACE + "selectGrpFstSmtDtm", eaAsnStuDto);
				eaAsnStuDto.setFstSmtDtm(fstSmtDtm);

				// 모둠 과제 제출 처리
				cnt += commonDao.update(MAPPER_NAMESPACE + "updateGrpAsnYn", eaAsnStuDto);
			}

			// 모둠 과제일 경우
			// 제출 인원이 모둠장일 경우 EA_GRP_ASN_SMT 테이블 Update
			// 나중에 개발하자

			if(cnt > 0) {
				resMap.put("result", "Success");
			} else {
				resMap.put("result", "Fail");
			}
			

		

		return resMap;
	}
	
	/**
     * Myhome Api호출처리
     * @param accessToken
     * @param paramMap
     * @return
     */
    public Map<String, Object> callMyhmApi(String accessToken, Map<String, String> paramMap) {
        Map<String, Object> returnMap = new HashMap<>();

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Authorization", "Bearer " + accessToken);
        httpHeaders.add("Content-Type", "application/json");

        
        try {
            String jsonString = new ObjectMapper().writeValueAsString(paramMap);
            String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
            return CoreUtil.Json.jsonString2Map(post);
		} catch (JsonProcessingException e) {
	        return returnMap;
		}
    }

	/**
	 * Set Session User Information
	 * @param
	 * @return  List<EaGrpBlbdStuDto>
	 */
	private List<EaUserSessionDto> setSessionInfo() {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		EaUserSessionDto eaUserSessionDto = new EaUserSessionDto();

		eaUserSessionDto.setSessionClaId(userDetails.getClaId());
		eaUserSessionDto.setSessionOptTxbId(userDetails.getOptTxbId());
		eaUserSessionDto.setSessionRole(userDetails.getRole());
		eaUserSessionDto.setSessionTxbId(userDetails.getTxbId());
		eaUserSessionDto.setSessionUsrId(userDetails.getUsrId());
		eaUserSessionDto.setSessionUsrNm(userDetails.getUsrNm());
		eaUserSessionDto.setSessionUsrTpCd(userDetails.getUsrTpCd());

		List<EaUserSessionDto> userInfo = new ArrayList<>();
		userInfo.add(eaUserSessionDto);

		return userInfo;
	}

}

