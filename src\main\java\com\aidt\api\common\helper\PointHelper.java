package com.aidt.api.common.helper;

import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.common.util.WebFluxUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class PointHelper {

	@Value("${aidt.endpoint.lw_myhm_stu_point}")
	private String pointUrl;
	private final WebFluxUtil webFluxUtil;
	private final JwtHelper jwtHelper;

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class pointRequest {

		private String pntCd;
		private String pntChkBsVl;

	}

	public void saveAsync(@NotNull final String pointCode, @NotNull final Object targetId,
		@NotNull final String accessToken) {
		save(pointCode, targetId.toString(), accessToken);
	}

	public void save(@NotNull final String pointCode, @NotNull final Object targetId) {
		save(pointCode, targetId.toString(), null);
	}

	private void save(String pointCode, String targetId, String accessToken) {

		if (StringUtils.isAnyBlank(pointCode, targetId)) {
			log.error("포인트 적립에 대한 파라미터가 잘못되었습니다.");
			throw new IllegalArgumentException("포인트 적립에 대한 파라미터가 잘못되었습니다.");
		}

		try {
			HttpHeaders headers = getAuthHeaders(accessToken);
			webFluxUtil.post(pointUrl, headers,
				pointRequest.builder()
					.pntCd(pointCode)
					.pntChkBsVl(targetId)
					.build()
				, String.class);
		} catch (RuntimeException e) {
			log.error("포인트 저장에 실패했습니다.");
			throw new BizException("포인트 저장에 실패했습니다.");
		}

	}

	private HttpHeaders getAuthHeaders(String accessToken) {

		String authorizationToken = accessToken;

		if (StringUtils.isBlank(authorizationToken)) {
			authorizationToken = jwtHelper.getAccessToken();
			if (authorizationToken == null) {
				log.error("토큰 정보를 가져올 수 없습니다.");
				throw new BizException("토큰 정보를 가져올 수 없습니다.");
			}
		}

		if (!authorizationToken.startsWith("Bearer ")) {
			authorizationToken = "Bearer " + authorizationToken;
		}

		HttpHeaders headers = new HttpHeaders();
		headers.add("Content-Type", "application/json");
		headers.add("Authorization", authorizationToken);

		return headers;
	}

}
