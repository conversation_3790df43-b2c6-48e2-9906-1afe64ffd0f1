package com.aidt.api.al.pl.dto;

import java.math.BigInteger;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI 맞춤 이전 차시별 실제 풀이점수 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlPlBfTcFactXplScrDto {

		@Parameter(name="과목")
		private String sbjCd;
		
		@Parameter(name="학년")
		private String sgyCd;
				
		@Parameter(name="저자")
		private String autrCd;
		
		@Parameter(name="유저ID")
		private String usrId;
		
		@Parameter(name="현재 차시문항")
		private BigInteger qtmId;
		
		@Parameter(name="이전차시평균정답률")
		private float preTcAnsRt;
		
		@Parameter(name="현차시평균정답률")
		private float sbcLrnAvgAnsRt;

}
