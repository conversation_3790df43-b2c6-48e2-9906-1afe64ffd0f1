package com.aidt.api.al.pl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-23 10:00:32
 * @modify date 2024-02-23 10:00:32
 * @desc [학습목록조회 교과서PDF정보목록 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TxbPdfDto {
	@Parameter(name="페이지번호")
	private String pgeNo;
	
	@Parameter(name="페이지CDN경로")
	private String cdnPthNm;
	
}
