package com.aidt.api.al.pl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-23 10:00:32
 * @modify date 2024-02-23 10:00:32
 * @desc [학습목록조회 학습단계 > 활동 > 동영상 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class LrnVdsDto {
	@Parameter(name="영상주소")
	private String vdsAdr;
	
	@Parameter(name="자막주소")
	private String sttlSmi;
	
	@Parameter(name="대본주소")
	private String scrbFle;
	
	@Parameter(name="썸네일")
	private String vdsThb;
}
