package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcAnnxFleSaveDto {

	@Parameter(name = "업무구분 (과제:ASN / 학급게시판:CBB")
	private String actDvCd;

	@Parameter(name = "업무ID (과제:asnId / 학급게시판:claBlbdId")
	private Long taskId;

	@Parameter(name = "업무명")
	private String taskName;

	@Parameter(name = "첨부ID")
	private Long annxId;

	@Parameter(name = "삭제첨부파일ID")
	private Long[] deleteAnnxFleId;
}
