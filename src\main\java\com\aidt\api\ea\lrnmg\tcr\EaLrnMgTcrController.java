package com.aidt.api.ea.lrnmg.tcr;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgEaAsnStuDetailDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrDtlReqDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEaResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEvResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrReqDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrTalkResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrTxbReqDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrWriteResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 학생 분석", description="학생 분석")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/lrnmg")
public class EaLrnMgTcrController {
	@Autowired
	private JwtProvider jwtProvider;
	@Autowired
	EaLrnMgTcrService eaLrnMgTcrService;

	/**
	 * 학생 분석 조회 요청
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return ResponseList<EaLrnMgTcrResDto>
	 */
	@Tag(name="[ea] 교사 학생 분석 메인 조회", description="교사 학생 분석 메인 조회")
	@PostMapping(value = "/selectEaStuAnMainList")
	public ResponseDto<Map<String, Object>> selectEaStuAnMainList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAnMainList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		reqDto.setClaId(userDetails.getClaId()); // TODO 학급ID
		reqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAnMainList(reqDto));
	}
	
	/**
	 * 학생 분석 상세 조회 요청
	 *
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name="[ea] 교사 학생 분석 상세 조회", description="교사 학생 분석 상세 조회")
	@PostMapping(value = "/selectEaStuAnDtlList")
	public ResponseDto<List<Map<String, Object>>> selectEaStuAnDtlList(@RequestBody EaLrnMgTcrDtlReqDto reqDto) {
		log.debug("Entrance selectEaStuAnDtlList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		reqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		reqDto.setTcrUsrId(userDetails.getUsrId()); // TODO 교사 사용자ID
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAnDtlList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > Let's talk 대단원 리스트
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 Let's talk 대단원 조회", description = "교사 Let's talk 대단원 조회")
	@PostMapping(value = "/selectEaStuAmTcrTalkLesList")
	public ResponseDto<List<EaLrnMgTcrResDto>> selectEaStuAmTcrTalkLesList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrTalkLesList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrTalkLesList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > Let's talk 단원별 학생 정보
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 Let's talk 단원별 학생 정보", description = "교사 Let's talk 단원별 학생 정보")
	@PostMapping(value = "/selectEaStuAmTcrTalkUsrList")
	public ResponseDto<List<EaLrnMgTcrTalkResDto>> selectEaStuAmTcrTalkUsrList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrTalkUsrList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrTalkUsrList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > Let's write 대단원 리스트
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 Let's write 대단원 조회", description = "교사 Let's write 대단원 조회")
	@PostMapping(value = "/selectEaStuAmTcrWriteLesList")
	public ResponseDto<List<EaLrnMgTcrResDto>> selectEaStuAmTcrWriteLesList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrWriteLesList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrWriteLesList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > Let's write 단원별 학생 정보
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 Let's write 단원별 학생 정보", description = "교사 Let's write 단원별 학생 정보")
	@PostMapping(value = "/selectEaStuAmTcrWriteList")
	public ResponseDto<List<EaLrnMgTcrWriteResDto>> selectEaStuAmTcrWriteList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrWriteList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrWriteList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > 학생별 과제 정보 조회
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 학생별 과제 정보 조회", description = "교사 학생별 과제 정보 조회")
	@PostMapping(value = "/selectEaStuAmTcrEaList")
	public ResponseDto<List<EaLrnMgTcrEaResDto>> selectEaStuAmTcrEaList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrEaList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrEaList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > 학생별 과제 학생별 조회
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 학생별 과제 상세 조회", description = "교사 학생별 과제 상세 조회")
	@PostMapping(value = "/selectEaStuAmTcrEaDetail")
	public ResponseDto<List<EaLrnMgEaAsnStuDetailDto>> selectEaStuAmTcrEaDetail(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrEaDetail");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrEaDetail(reqDto));
	}


	/**
	 * 학습 관리 > 학습 현황 관리 > 종합현황 우리반수업, 추천학습 종합진도율 정보
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 종합현황(우리반수업, 선생님추천학습)", description = "학생별 우리반수업(개념), 선생님추천학습 진도율정보를 조회한다.")
	@PostMapping(value = "/selectEaStuSumRtTxbSpList")
	public ResponseDto<Object> selectEaStuSumRtTxbSpList() {
		log.debug("Entrance selectEaStuSumRtTxbSpList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuSumRtTxbSpList(userDetails.getOptTxbId(), userDetails.getClaId()));
	}

	/**
	 * 학습 관리 > 학습 현황 관리 > 종합현황 우리반수업 진도율 정보
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 우리반수업", description = "학생별 우리반수업(개념), 우리반수업(익힘), 형성평가, 마지막학습일을 차시별 진도율정보를 조회한다.")
	@PostMapping(value = "/selectEaStuTcTxbList")
	public ResponseDto<Object> selectEaStuTcTxbList(@RequestBody EaLrnMgTcrTxbReqDto reqDto) {
		log.debug("Entrance selectEaStuTcTxbList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		reqDto.setOptTxbId(userDetails.getOptTxbId());
		reqDto.setClaId(userDetails.getClaId());
		
		return Response.ok(eaLrnMgTcrService.selectEaStuTcTxbList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > 학생별 평가 정보 조회
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 학생별 평가 정보 조회", description = "교사 학생별 평가 정보 조회")
	@PostMapping(value = "/selectEaStuAmTcrEvList")
	public ResponseDto<List<EaLrnMgTcrEvResDto>> selectEaStuAmTcrEvList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrEvList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrEvList(reqDto));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > 학생별 평가 상세 조회
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 교사 학생별 평가 상세 조회", description = "교사 학생별 과제 상세 조회")
	@PostMapping(value = "/selectEaStuAmTcrEvDetail")
	public ResponseDto<List<Map<String, Object>>> selectEaStuAmTcrEvDetail(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAmTcrEaDetail");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAmTcrEvDetail(reqDto));
	}

	/**
	 * 학습 관리 > 학습 현황 관리 > 종합현황 추천학습 진도율 정보
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 선생님 추천 학습", description = "학생별 추천학습 진도율정보를 조회한다.")
	@PostMapping(value = "/selectEaStuRcmLrnList")
	public ResponseDto<Object> selectEaStuRcmLrnList() {
		log.debug("Entrance selectEaStuRcmLrnList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectEaStuRcmLrnList(userDetails.getOptTxbId(), userDetails.getClaId()));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > 종합현황 추천학습 진도율 정보 20241115
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 선생님 추천 학습", description = "학생별 추천학습 진도율정보를 조회한다.")
	@PostMapping(value = "/selectStuRcmLrnList")
	public ResponseDto<Object> selectStuRcmLrnList() {
		log.debug("Entrance selectEaStuRcmLrnList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectStuRcmLrnList(userDetails.getOptTxbId(), userDetails.getClaId()));
	}
	
	/**
	 * 학습 관리 > 학습 현황 관리 > 종합현황 추천학습 학생별 진도율 상세 20241115
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] 선생님 추천 학습", description = "학생별 추천학습 학생별 진도율 상세 정보제공한다.")
	@PostMapping(value = "/selectStuRcmDetailList")
	public ResponseDto<Object> selectStuRcmDetailList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuRcmLrnList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectStuRcmDetailList(reqDto));
	}
	
	
	/**
	 * 학습 관리 > 학습 현황 관리 > ai맞춤학습탭(영어)
	 * @param EaLrnMgTcrDtlReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name = "[ea] ai맞춤학습", description = "ai맞춤학습 영어 중단원 리스트 조회.")
	@PostMapping(value = "/selectAlEnOnlyMluList")
	public ResponseDto<List<Map<String, Object>>> selectAlEnOnlyMluList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectAlEnOnlyMluList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		return Response.ok(eaLrnMgTcrService.selectAlEnOnlyMluList(reqDto));
	}
	
	/**
	 * 학생 종합성취 정보
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return ResponseList<EaLrnMgTcrResDto>
	 */
	@Tag(name="[ea] 학생 종합성취 정보 조회", description="학생 종합성취 정보 조회")
	@PostMapping(value = "/selectEaStuAnLuList")
	public ResponseDto<List<Map<String, Object>>> selectEaStuAnLuList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectEaStuAnLuList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		reqDto.setClaId(userDetails.getClaId()); // TODO 학급ID
		reqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		
		return Response.ok(eaLrnMgTcrService.selectEaStuAnLuList(reqDto));
	}
	
	/**
	 * 학생 종합현황 지도 필요
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return ResponseList<EaLrnMgTcrResDto>
	 */
	@Tag(name="[ea] 학생 종합현황 지도 필요 조회", description="학생 종합현황 지도 필요 조회")
	@PostMapping(value = "/selectStuGdeNeedList")
	public ResponseDto<List<Map<String, Object>>> selectStuGdeNeedList(@RequestBody EaLrnMgTcrReqDto reqDto) {
		log.debug("Entrance selectStuGdeNeedList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		reqDto.setClaId(userDetails.getClaId()); // TODO 학급ID
		reqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		
		return Response.ok(eaLrnMgTcrService.selectStuGdeNeedList(reqDto));
	}
	
	

}
