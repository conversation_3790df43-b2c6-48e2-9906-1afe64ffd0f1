package com.aidt.api.al.pl.cm.rcm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import com.aidt.api.al.pl.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.cm.en.AlEnService;
import com.aidt.api.al.pl.common.AlCmUtil;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.common.CommonDao;
import com.aidt.common.JwtProvider;
import com.aidt.common.util.ConstantsExt;
import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AlLrnwService {
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
    
	private final String MAPPER_NAMESPACE = "api.al.pl.back.lrnw.";
	@Autowired private CommonDao commonDao;
	
	@Autowired private AlMyhmPointService alMyhmPointService;
	@Autowired private JwtProvider jwtProvider;
	
	@Autowired AiRcmTsshQtmService aiRcmTsshQtmService;
	@Autowired AiRcmTsshQtmEnService enService;
	@Autowired AlEnService alEnService;
	@Autowired AiRcmTsshQtmMaService maService;
	
	//컨텐츠 메타정보 조회 - 단건(학습창Depth에맞춰서 중복데이터 set해서 넘김)
    @Transactional(readOnly = true)
    public AlLrnwResponseDto selectLrnwInfo(AlLrnwReqDto dto) {
    	
    	//컨텐츠 조회
    	Map<String, Object> ctnMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnStpMeta", dto);

		AlLrnwResponseDto responseDto = new AlLrnwResponseDto();
		if (ctnMap == null) {
			responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("컨텐츠가 존재하지 않습니다.");
			return responseDto;
		}
    	
    	//학습도구 리스트
    	List<AlLrnwLrnTlDto> lrnTlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTlList", dto);

		if (lrnTlList == null) {
			responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("학습도구 리스트가 존재하지 않습니다.");
			return responseDto;
		}
    	
    	//컨텐츠메타데이터
    	AlLrnwCtnMtdDto ctnMtd = new AlLrnwCtnMtdDto();
    	ctnMtd.setCtnTpCd(ctnMap.get("CTN_TP_CD").toString());
    	ctnMtd.setLrnCofmTmScnt(ctnMap.get("LRN_RCNT_TM_SCNT").toString());
    	ctnMtd.setCtnDffdDvCd(ctnMap.get("CTN_DFFD_DV_CD").toString());
    	ctnMtd.setCmplBsCd(ctnMap.get("LRN_CMPL_CHK_BS_CD").toString());
    	ctnMtd.setAtvQtmCnt(ctnMap.get("LRN_ATV_TTL_QTM_CNT").toString());
    	ctnMtd.setOcrUseYn(ctnMap.get("OCR_USE_YN").toString());
    	ctnMtd.setFmlUseYn(ctnMap.get("MFRM_USE_YN").toString());
    	ctnMtd.setVdsLRsln("media/01_video_480.mp4"); // 고정값설정
        ctnMtd.setVdsMRsln("media/01_video_720.mp4"); // 고정값설정
        ctnMtd.setVdsHRsln("media/01_video_1080.mp4"); // 고정값설정
        ctnMtd.setSttlSmiFleNm("media/01_caption.smi"); // 고정값설정
        ctnMtd.setSttlVttFleNm("media/01_caption.vtt"); // 고정값설정
        ctnMtd.setScrbFleNm("media/01_script.txt"); // 고정값설정
        ctnMtd.setVceFleNm("media/01_audio.mp3"); // 고정값설정
        ctnMtd.setThbFleNm("images/poster.png"); // 고정값설정
        ctnMtd.setMetaDataId(ctnMap.get("AI_CTN_META_DATA_ID").toString());
        ctnMtd.setCtnCd(ctnMap.get("CTN_CD").toString());
        
        
        //학습활동
    	List<AlLrnwLrnAtvDto> lrnAtvList = new ArrayList<>();
    	AlLrnwLrnAtvDto lrnAtv = new AlLrnwLrnAtvDto();
    	lrnAtv.setLrnAtvId((Long) ctnMap.get("AI_LRN_ATV_ID"));
    	lrnAtv.setLrnAtvNm(ctnMap.get("LRN_ATV_NM").toString());
    	lrnAtv.setCtnTpCd(ctnMap.get("CTN_TP_CD").toString());
    	lrnAtv.setQtmId(ctnMap.get("CTN_CD").toString());
    	if(ctnMap.get("CTN_TP_CD").toString().equals("HT")) {
    		lrnAtv.setCtnUrl(AlCmUtil.makeFleCdnUrl(BUCKET_NAME, ctnMap.get("CDN_PTH_NM").toString()+"index.html"));
    	}else {
    		lrnAtv.setCtnUrl(AlCmUtil.makeFleCdnUrl(BUCKET_NAME, ctnMap.get("CDN_PTH_NM").toString()));
    	}
    	lrnAtv.setRcstnOrdn(ctnMap.get("SRT_ORDN").toString());
    	lrnAtv.setCtnMtd(ctnMtd);
    	lrnAtvList.add(lrnAtv);
    	
    	//학습단계
    	List<AlLrnwLrnStpDto> lrnStpList = new ArrayList<>();
    	AlLrnwLrnStpDto lrnStp = new AlLrnwLrnStpDto();
    	lrnStp.setLrnStpId(ctnMap.get("TPC_KMMP_NOD_ID").toString());
    	lrnStp.setLrnStpNm("개념영상 테스트");
    	lrnStp.setLrnStpOrdn(ctnMap.get("RCSTN_ORDN").toString());
    	lrnStp.setLrnStpDvCd("CL");
    	lrnStp.setLrnAtvList(lrnAtvList);
    	lrnStpList.add(lrnStp);
    	
    	//Responce
    	AlLrnwResponseDto response = new AlLrnwResponseDto();
    	response.setLluNodNo("1");
    	response.setLluNodId(ctnMap.get("LLU_KMMP_NOD_ID").toString());
    	response.setLluNodNm(ctnMap.get("LLU_KMMP_NOD_NM").toString());
    	response.setLluEspYn("Y");
    	response.setMluNodId(ctnMap.get("MLU_KMMP_NOD_ID").toString());
    	response.setMluNodNm(ctnMap.get("MLU_KMMP_NOD_NM").toString());
    	response.setMluEspYn("Y");
    	response.setTcNodId(ctnMap.get("TC_KMMP_NOD_ID").toString());
    	response.setTcNodNm(ctnMap.get("TC_KMMP_NOD_NM").toString());
    	response.setTcEspYn("Y");
    	response.setLrnTlList(lrnTlList);	//학습도구
    	response.setLrnStpList(lrnStpList);	//학습단계
    	response.setAtvTotCnt(1);
    	response.setStrAtvId((Long) ctnMap.get("AI_LRN_ATV_ID"));
    	response.setAtvEspYn("Y");
    	log.debug(response.toString());
    	return response;
    }
    
    //학습활동 저장
    @Transactional
    public int updateAiLrnAtvSt(AlLrnwReqDto dto, HttpServletRequest request) {    	
    	Map<String, Object> ctnMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnStpMeta", dto);
    	
    	Map<String, Object> ctnMap2 = new HashMap<String, Object>();
    	if(ctnMap == null) { // 이전학교급은 조회되지 않기 때문에 필요 데이터만 따로 추출.
    		ctnMap2 = commonDao.select(MAPPER_NAMESPACE + "selectLrnStpMetaPl", dto);
    	}
    	
    	dto.setLrnTmScnt(dto.getLrnTmScnt() == null ? 0 : dto.getLrnTmScnt());
    	if(ctnMap != null) {
    		dto.setKmmpNodId(ctnMap.get("MLU_KMMP_NOD_ID").toString());    		
    	} else {
    		dto.setKmmpNodId(dto.getLrmpNodId());   
    	}
    	dto.setDbId("DB_ID_AI");
    	dto.setLrnStCd("CL");//학습완료
    	
    	//마이홈 포인트 적립
		String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
		
		String pointCode = "";
		String pntChkBsVl = "";
		
		if(ctnMap != null) {
			pointCode = alMyhmPointService.getPointCode(
					ctnMap.get("SBJ_CD").toString()
					, ctnMap.get("SCHL_GRD_CD").toString()
					, ""
					, ""
					, AlConstUtil.AL_MYHM_CTN_VD);
			log.debug(">> pointCode:{}",pointCode);			
			pntChkBsVl = ctnMap.get("MLU_KMMP_NOD_ID").toString() + "_" + dto.getLrnAtvId();
			log.debug(">> pntChkBsVl:{}",pntChkBsVl);
		} else {
			pointCode = alMyhmPointService.getPointCode(
					ctnMap2.get("SBJ_CD").toString()
					, ctnMap2.get("SCHL_GRD_CD").toString()
					, ""
					, ""
					, AlConstUtil.AL_MYHM_CTN_VD);
			log.debug(">> pointCode:{}",pointCode);			
			pntChkBsVl = dto.getKmmpNodId() + "_" + dto.getLrnAtvId();
			log.debug(">> pntChkBsVl:{}",pntChkBsVl);
		}
		
		
		try {
			Map<String, Object> apiResult = alMyhmPointService.callMyhmApi(accessToken, Map.of(
			      "pntCd", pointCode,
			      "pntChkBsVl", pntChkBsVl));	//중단원_학습활동ID
			log.debug(">> apiResult:{}",apiResult);
		} catch (JsonProcessingException e) {
			throw new IllegalArgumentException("AI 마이홈 포인트 적립실패");
		}
    	
//    	if(dto.getLrnTmScnt() > 0 && dto.getLrnTmScnt() < Integer.parseInt(ctnMap.get("LRN_RCNT_TM_SCNT").toString())) {
//    		dto.setLrnStCd("DL");//학습중
//    	}
//    	else if(dto.getLrnTmScnt() >= Integer.parseInt(ctnMap.get("LRN_RCNT_TM_SCNT").toString())) {
//    		dto.setLrnStCd("CL");//학습완료
//    	}else{
//    		dto.setLrnStCd("NL");//미학습
//    	}
    	return commonDao.insert(MAPPER_NAMESPACE + "updateAiLrnAtvSt", dto);
    }
    
    
    
    //문항정보 조회
    @Transactional(readOnly = true)
    public String selectQtmInfo(AlLrnwReqDto dto) {
    	AiRcmTsshQtmDto qtmInfo = commonDao.select(MAPPER_NAMESPACE + "selectQtmInfo", dto);
    	String resultStr = "";
    	resultStr += qtmInfo.getTpcKmmpNodNm() + "/ ";//토픽명
    	resultStr += qtmInfo.getAiPredAvgCansRt() + "/ ";//토픽예측정답률
    	resultStr += qtmInfo.getAiPredAvgScr() + "/ ";//토픽실제점수
    	resultStr += qtmInfo.getTpcAvn() + "/ ";//토픽숙련도
    	resultStr += qtmInfo.getCtnDffdDvCd() + "/ ";//난이도
    	resultStr += qtmInfo.getAiPredCansRt();//문항예측정답률
    	return resultStr;
    }
    
    
    
    //학습창 HTML문항 정오 저장
    @Transactional
    public Integer updateEvQtmCansYn(AlLrnwHtmlTypeReqDto req) {
    	req.setDbId("AI_updateEvQtmCansYn");
    	return commonDao.update(MAPPER_NAMESPACE + "insertEaEvQtmAnw", req);
    }
    
    
    
    //AI학습창 이어하기 :: 평가ID or 중단원지식맵ID로 평가리스트를 조회한다.
    @Transactional
    public Map<String, Object> selectContinueLrnwInfo(AiRcmTsshQtmDto req){
    	Map<String, Object> resultMap = new HashMap<>();
    	
    	AiRcmTsshQtmDto txbInfo = commonDao.select(MAPPER_NAMESPACE + "selectTxbInfo", req);
    	req.setSbjCd(txbInfo.getSbjCd());
    	req.setSchlGrdCd(txbInfo.getSchlGrdCd());
    	
    	if(req.getEvId() == null) {
    		//영어 - 중단원이나 차시가 없으면 Exception
    		if(AlConstUtil.SBJ_EN.contains(txbInfo.getSbjCd())) {
//    			if(req.getMluKmmpNodId() == null || req.getMluKmmpNodId().equals("") || (req.getTcKmmpNodId() == null || req.getTcKmmpNodId().equals(""))) {
//    				throw new IllegalArgumentException("Invalid 'mluKmmpNodId' and 'tcKmmpNodId' in Request Parameter");
//				}
    			if(req.getMluKmmpNodId() == null || req.getMluKmmpNodId().equals("")) {
    				resultMap.put("errorStatus", "error");
    				resultMap.put("errorMessage", "Invalid 'mluKmmpNodId'  in Request Parameter");
    				return resultMap;
//    				throw new IllegalArgumentException("Invalid 'mluKmmpNodId'  in Request Parameter");
				}
    		}
    		//수학
    		else {
    			if(req.getMluKmmpNodId() == null || req.getMluKmmpNodId().equals("")) {
    				resultMap.put("errorStatus", "error");
    				resultMap.put("errorMessage", "Invalid 'mluKmmpNodId'  in Request Parameter");
    				return resultMap;
//    				throw new IllegalArgumentException("Invalid 'mluKmmpNodId'  in Request Parameter");
				}
    		}
    	}
    	else {
    		//평가아이디로 중단원조회
			AiRcmTsshQtmDto dto = commonDao.select(MAPPER_NAMESPACE + "selectMluKmmpNodId", req);

			if (dto == null) {
				resultMap.put("errorStatus", "error");
				resultMap.put("errorMessage", "등록된 평가 정보가 없습니다.");
				return resultMap;
//				throw new NullPointerException("등록된 평가 정보가 없습니다.");
			}

			if (dto.getMluKmmpNodId() == null || dto.getMluKmmpNodId().isEmpty()) {
				resultMap.put("errorStatus", "error");
				resultMap.put("errorMessage", "등록된 평가의 중단원 정보가 없습니다.");
				return resultMap;
//				throw new NullPointerException("등록된 평가의 중단원 정보가 없습니다.");
			}

			req.setMluKmmpNodId(dto.getMluKmmpNodId());
			if(AlConstUtil.SBJ_EN.contains(txbInfo.getSbjCd())) {
				if (dto.getTcKmmpNodId() == null || dto.getTcKmmpNodId().isEmpty()) {
					resultMap.put("errorStatus", "error");
					resultMap.put("errorMessage", "등록된 평가의 차시 정보가 없습니다.");
    				return resultMap;
//					throw new NullPointerException("등록된 평가의 차시 정보가 없습니다.");
				}

				req.setTcKmmpNodId(dto.getTcKmmpNodId());
			}
    	}

		//진행중인토픽
		AiRcmTsshQtmDto selectTpcPgrsRt = maService.selectTpcPgrsRt(req.getOptTxbId(), req.getMluKmmpNodId(), req.getUsrId());
    	
    	//수학은 현재 진행토픽 확인 토픽 내 평가만 반환한다.
    	if(AlConstUtil.SBJ_MA.contains(txbInfo.getSbjCd())) {
			AiRcmTsshQtmDto lastEvInfo = commonDao.select(MAPPER_NAMESPACE + "selectLastEvInfo", req);
			if (null != lastEvInfo && lastEvInfo.getEvCmplYn().equals("N")) {
				req.setTpcKmmpNodId(lastEvInfo.getTpcKmmpNodId());
			}
//			else if (null != lastEvInfo && lastEvInfo.getEvCmplYn().equals("Y") && selectTpcPgrsRt.getNextTpcKmmpNodId() != null && selectTpcPgrsRt.getNextTpcKmmpNodId() != "") {
//				/* 과제에서 평가 완료 후 AI 맞춤학습 하는 경우 평가 생성 필요하여 코드 추가(2025.01.22) */
//				AiRcmTsshQtmReqDto reqDto = new AiRcmTsshQtmReqDto();
//				reqDto.setMluKmmpNodId(req.getMluKmmpNodId());
//				reqDto.setOptTxbId(req.getOptTxbId());
//				reqDto.setUsrId(req.getUsrId());
//				reqDto.setSbjCd(req.getSbjCd());
//				 maService.selectAiMaRcmQtm(reqDto);
//			}
    	}
    	
    	//해당 단원 내의 모든 평가리스트
    	List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectAllEvListForLu", req);
    	//평가정보가 없는경우 진단평가 호출(과제호출방어)
    	if(list.isEmpty()) {
    		list = callAiRcmQtmOv(req);
    		if(list.isEmpty()) {
    			resultMap.put("errorStatus", "error");
    			resultMap.put("errorMessage", "진단평가 문항정보가 없습니다.");
				return resultMap;
//    			throw new IllegalArgumentException("진단평가 문항정보가 없습니다.");
    		}
    	}
    	
    	//평가리스트
    	Map<Integer, List<AiRcmTsshQtmDto>> evListMap = list.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getEvId));
    	
    	resultMap.put("evListMap", evListMap);
    	
    	//차시리스트
    	if(AlConstUtil.SBJ_EN.contains(txbInfo.getSbjCd())) {
    		List<AlLrnwEvInfoDto> tcCmplYnList = alEnService.tcCmplYnList(req);
        	resultMap.put("tcCmplYnList", tcCmplYnList);
        	
        	List<AiRcmTsshQtmDto> tcAvnList = alEnService.getTcAvn(req);
        	List<String> tc01List = new ArrayList<String>();
        	for (AiRcmTsshQtmDto tc : tcAvnList) {
        		log.info("================================="+tc.getTcAvn());
				if(tc.getTcAvn() < AlConstUtil.TPC_AVN_01) {
					tc01List.add(tc.getTcKmmpNodNm());
				}
			}
        	resultMap.put("tc01List", tc01List);
    	}

		resultMap.put("progTpcNm", selectTpcPgrsRt.getTpcKmmpNodNm());
		resultMap.put("progTpcId", selectTpcPgrsRt.getTpcKmmpNodId());
		resultMap.put("nextTpcKmmpNodNm", selectTpcPgrsRt.getNextTpcKmmpNodNm());
		resultMap.put("nextTpcKmmpNodId", selectTpcPgrsRt.getNextTpcKmmpNodId());
		resultMap.put("lrnwTpcCnt", selectTpcPgrsRt.getLrnwTpcCnt());
		resultMap.put("lrnwCmplTpcCnt", selectTpcPgrsRt.getLrnwCmplTpcCnt());
		
		resultMap.put("ctnTpCd", selectTpcPgrsRt.getCtnTpCd());
		resultMap.put("cdnPthNm", selectTpcPgrsRt.getCdnPthNm());
		if(selectTpcPgrsRt.getCdnPthNm()!=null && !"".equals(selectTpcPgrsRt.getCdnPthNm())) {
	    	resultMap.put("cdnPthNm2", selectTpcPgrsRt.getCdnPthNm() + "/index.html");
			resultMap.put("cdnPthNm3", selectTpcPgrsRt.getCdnPthNm() + "/media/01_video_720.mp4");
			resultMap.put("vdsLRsln", "media/01_video_480.mp4");
	        resultMap.put("vdsMRsln", "media/01_video_720.mp4");
	        resultMap.put("vdsHRsln", "media/01_video_1080.mp4");
	        resultMap.put("sttlSmiFleNm", "media/01_caption.smi");
	        resultMap.put("sttlVttFleNm", "media/01_caption.vtt");
	        resultMap.put("scrbFleNm", "media/01_script.txt");
	        resultMap.put("vceFleNm", "media/01_audio.mp3");
	        resultMap.put("thbFleNm", "images/poster.png");
		}else {
			resultMap.put("cdnPthNm2", "");
			resultMap.put("cdnPthNm3", "");
			resultMap.put("vdsLRsln", "");
	        resultMap.put("vdsMRsln", "");
	        resultMap.put("vdsHRsln", "");
	        resultMap.put("sttlSmiFleNm", "");
	        resultMap.put("sttlVttFleNm", "");
	        resultMap.put("scrbFleNm", "");
	        resultMap.put("vceFleNm", "");
	        resultMap.put("thbFleNm", "");
		}
    	
    	return resultMap;
    }
    
    
    
    
    //selectContinueLrnwInfo(AI학습창이어하기)에서 평가정보가 없을경우 진단평가 호출 및 정오정보 return
    public List<AiRcmTsshQtmDto> callAiRcmQtmOv(AiRcmTsshQtmDto req) {
    	List<AiRcmTsshQtmDto> list = new ArrayList<>();
    	
    	//문항추천로직 CALL
    	AiRcmTsshQtmReqDto callDto = new AiRcmTsshQtmReqDto();
    	callDto.setOptTxbId(req.getOptTxbId());
    	callDto.setUsrId(req.getUsrId());
    	callDto.setMluKmmpNodId(req.getMluKmmpNodId());
    	callDto.setRpetRcmYn("N");
    	callDto.setXplTmSetmYn("N");
    	callDto.setEvDvCd("AE");
    	callDto.setEvDtlDvCd(AlConstUtil.EV_DTL_DV_CD_OV);
    	String mluKmmpNodNm = commonDao.select(MAPPER_NAMESPACE + "selectMluKmmpNodNm", req.getMluKmmpNodId());
    	callDto.setEvNm(mluKmmpNodNm + " - AI 진단 평가");
    	callDto.setSbjCd(req.getSbjCd());
    	callDto.setSchlGrdCd(req.getSchlGrdCd());
    	
    	Integer evId = null;
    	if(AlConstUtil.SBJ_EN.contains(req.getSbjCd()) && AlConstUtil.SBJ_EN.contains(req.getSchlGrdCd())) {//초등영어
    		evId = enService.getEschEnQtmList(callDto).getEvId();
    	}else {
    		evId = aiRcmTsshQtmService.selectAiRcmTsshQtmList(callDto).getEvId();
    	}
    	
    	//해당평가 문항정보 조회
    	list = commonDao.selectList(MAPPER_NAMESPACE + "selectAllEvListForLu", callDto);
    	return list;
    }
    
    
    
    /**
     * 수학 마지막토픽 완료 후 단원 전체 정답수, 오답수를 조회한다.
     * */
    public Map<String, Integer> selectLuCansCnt(AiRcmTsshQtmDto req){
    	Map<String, Integer> returnMap = new HashMap<String, Integer>();
    	
    	AiRcmTsshQtmDto dto = commonDao.select(MAPPER_NAMESPACE + "selectLuCansCnt", req);
    	if(dto == null || dto.getUsrId() == null) {
    		throw new IllegalArgumentException("평가 정보가 없습니다.");
    	}
    	returnMap.put("luCansYCnt", dto.getCansYCnt());
    	returnMap.put("luCansNCnt", dto.getQtmCnt() - dto.getCansYCnt());
    	return returnMap;
    }

    /**
     * AI맞춤 개념영상 정보 조회 (레이어팝업)
     * */
	 @Cacheable(
			    cacheNames = "longCache",
			    key="'al:' + #req.optTxbId + ':selectVdInfo:' + #req.lrnAtvId",
			    cacheManager = "aidtCacheManager"
		    )
    public Map<String, Object> selectVdInfo(AlLrnwReqDto req){
    	Map<String, Object> resultMap = new LinkedHashMap<String, Object>();
    	
		Map<String, Object> selectVdAtvInfo = new HashMap<String, Object>();
		
		if("TL".equals(req.getLrnTpCd())) {
			selectVdAtvInfo = commonDao.select(MAPPER_NAMESPACE + "selectVdAtvInfoTL", req);		
		}
		else {
			selectVdAtvInfo = commonDao.select(MAPPER_NAMESPACE + "selectVdAtvInfo", req);		
		}
		
		
    	resultMap.put("lrnAtvId", selectVdAtvInfo.get("LRN_ATV_ID"));
    	resultMap.put("ctnTpCd", selectVdAtvInfo.get("CTN_TP_CD"));
		boolean isHtml = "HT".equals(selectVdAtvInfo.get("CTN_TP_CD"));
        resultMap.put("cdnPthNm", AlCmUtil.makeFleCdnUrl(BUCKET_NAME, selectVdAtvInfo.get("CDN_PTH_NM").toString(), isHtml));

		AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
		String path = selectVdAtvInfo.get("CDN_PTH_NM").toString();
		if (path.startsWith("/")) {
			path = path.substring(1, path.length());
		}

		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_video_480.mp4")) resultMap.put("vdsLRsln", "media/01_video_480.mp4");
        if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_video_720.mp4")) resultMap.put("vdsMRsln", "media/01_video_720.mp4");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_video_1080.mp4")) resultMap.put("vdsHRsln", "media/01_video_1080.mp4");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_caption.smi")) resultMap.put("sttlSmiFleNm", "media/01_caption.smi");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_caption.srt")) resultMap.put("sttlSmiFleNm2", "media/01_caption.srt");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_caption.vtt")) resultMap.put("sttlVttFleNm", "media/01_caption.vtt");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_script.txt")) resultMap.put("scrbFleNm", "media/01_script.txt");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_audio.mp3")) resultMap.put("vceFleNm", "media/01_audio.mp3");
		if (s3.doesObjectExist(BUCKET_NAME,path + "images/poster.png")) resultMap.put("thbFleNm", "images/poster.png");
		if (s3.doesObjectExist(BUCKET_NAME,path + "media/01_caption.txt")) resultMap.put("capnNm", "media/01_caption.txt");
		if (s3.doesObjectExist(BUCKET_NAME,path + "index.html")) resultMap.put("indexNm", "index.html");
        
    	return resultMap;
    }
    
    
}
