package com.aidt.api.tl.lrnwif.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwAtvSaveDto Dto 교과학습 활동저장Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TlLrnwAtvSaveDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    /** 차시ID */
    @Parameter(name="차시ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;
    /** 학습한 차시ID */
    @Parameter(name="학습한 차시ID")
    private String lrnTcId;
    /** 학습활동ID */
    @Parameter(name="학습활동ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrnAtvId;
    /** 학습상태(미학습:NL, 학습중:DL, 학습완료: CL) */
    @Parameter(name="학습상태(미학습:NL, 학습중:DL, 학습완료: CL) ", required = true)
    @NotBlank(message = "{field.required}")
    private String lrnStCd;
    /** 사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    /** 학습단계코드 */
    @Parameter(name="학습단계코드")
    private String lrnStpDvCd;
    /** 교사추가콘텐츠유무 */
    @Parameter(name="교사추가콘텐츠유무")
    private String tcrCtnYn;
    /** 학습시간초수 */
    @Parameter(name="학습시간초수")
    @Min(value = 0, message = "0 {field.min}")
    private int lrnTmScnt;
    /** 접속DB인스턴스ID */
    @Parameter(name="접속DB인스턴스ID")
    private String dbId;
}
