package com.aidt.api.tl.oneclksetm.tcr.dto;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-15 16:18:45
 * @modify date 2024-06-15 16:18:45
 * @desc [TlOneClkSetmFncUseDto 원클릭학습설정 기능사용설정 dto]
 */

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TlOneClkSetmFncUseDto {
    /** 운영교과서 ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 마이홈포인트사용여부 */
    @Parameter(name="마이홈포인트사용여부")
    private String myhmPntUseYn;

    /** 칭찬도장사용여부 */
    @Parameter(name="칭찬도장사용여부")
    private String prasStmpUseYn;

    /** 대화사용여부 */
    @Parameter(name="대화사용여부")
    private String dilgUseYn;

    /** 학급게시판첨삭허용여부 */
    @Parameter(name="학급게시판첨삭허용여부")
    private String claBlbdWrtPmsnYn;
    
    /** 학급게시판댓글허용여부 */
    @Parameter(name="학급게시판댓글허용여부")
    private String claBlbdUcwrPmsnYn;
    
    /** 내가만든평가사용여부 */
    @Parameter(name="내가만든평가사용여부")
    private String diyEvUseYn;
    
    /** 학습 리포트 사용 여부 */
    @Parameter(name="학습 리포트 사용 여부")
    private String lrnRptUseYn;

    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;

    /** dbID */
    @Parameter(name="dbID")
    private String dbId;
    
    /** 원클릭학습설정 다른 학급 리스트 */
    @Parameter(name="원클릭학습설정 다른 학급 리스트")
    private List<TlOneClkSetmClaDto> claList;
}
