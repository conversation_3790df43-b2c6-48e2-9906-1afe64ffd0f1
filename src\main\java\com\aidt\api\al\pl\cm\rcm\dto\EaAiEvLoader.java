package com.aidt.api.al.pl.cm.rcm.dto;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import com.aidt.api.bc.cm.textbook.dto.Textbook;
import com.aidt.api.common.enums.SchoolGradeCode;
import com.aidt.api.common.enums.SubjectCode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EaAiEvLoader {

	Integer evId;
	boolean isTopicCompleted;
	List<EaAiEv> eaAiEvs;
	Textbook textbook;

	public SchoolGradeCode getSchoolGradeCode() {
		return SchoolGradeCode.getSchoolGradeCode(textbook.getSchlGrdCd());
	}

	public SubjectCode getSubjectCode() {
		return SubjectCode.getSubjectCode(textbook.getSbjCd());
	}

	public EaAiEv getEaAiEv() {
		if (CollectionUtils.isEmpty(eaAiEvs)) {
			return null;
		}
		var eaAiEv = eaAiEvs.get(0);
		if (ObjectUtils.isEmpty(eaAiEv.getTextbook())) {
			eaAiEv.addTextbook(textbook);
		}
		return eaAiEv;
	}
}
