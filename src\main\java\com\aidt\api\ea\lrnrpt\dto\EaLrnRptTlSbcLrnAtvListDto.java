package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSbcLrnAtvListDto {
	
	/** 차시ID*/
    @Parameter(name="차시ID")
    private String lrmpTcNodId;
    
    /** 차시명*/
    @Parameter(name="차시명")
    private String lrmpTcNodNm;
	
	/** 교과공부 info : CL **/
	@Parameter(name="교과서 공부 총 개수")
	private int learningTotCount;
	
	@Parameter(name="교과서 공부 완료개수")
	private int learningCmplCount;
	
	@Parameter(name="교과서 공부 진척율")
	private double learningProgressRate;
	
	@Parameter(name="교과서 공부 학습 총시간")
	private int learningTmSum;
	
	/** 차시에 따른 교과 공부 학습 정보 list */
	@Parameter(name="교과 공부 학습 정보")
	private List<EaLrnRptTlSbcLrnAtvStaDto> eaLrnRptTlSbcLrnAtvStaDto;
	
	/** 수학익힘 info : WB **/
	@Parameter(name="수학 익힘 총 개수")
	private String mathTotCount;
	
	@Parameter(name="수학 익힘 완료개수")
	private String mathCmplCount;
	
	@Parameter(name="수학 익힘 진척율")
	private double mathCompletionRate;
	
	@Parameter(name="수학 익힘 학습 총시간")
	private String mathTmSum;
	
	/** 수학익힘 info 학습 정보 list */
	@Parameter(name="수학 익힘 학습 정보")
	private EaLrnRptTlSbcLrnAtvWbDto eaLrnRptTlSbcLrnAtvMathDtoList;
	
	/** 형성평가 info : EX **/
	@Parameter(name="형성 평가 총개수")
	private String eaTotalCount;
	
	@Parameter(name="형성 평가 맞힌 개수")
	private int eaCansCount;
	
	@Parameter(name="형성평가 학습 총시간")
	private String eaTmSum;
	
	@Parameter(name="형성평가 완료여부")
	private String eaCmplYn;
	
	@Parameter(name="평가ID 존재 여부")
	private String evIdCk;
	
	/** 차시에 따른 형성평가 학습 정보 list(학생) */
	@Parameter(name="형성평가 학습 정보")
	private EaLrnRptTlSbcLrnAtvEvDto eaLrnRptTlSbcLrnAtvEvDtoList;
	
	/** 차시에 따른 형성평가 학습 정보 list(교사) */
	@Parameter(name="형성평가 학습 정보")
	private EaLrnRptTlSbcLrnAtvEvTcrDto eaLrnRptTlSbcLrnAtvEvTcrData;
	
	@Parameter(name="총 학습시간(초)")
	private String learningTotalScnt;
	
	@Parameter(name="학습현황 형성평가 시간") 
    private String evActLrnScnt;
	
}
