package com.aidt.api.ea.evcom.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum EaEvPointCode {

	ST("ST", "EV_CC_01"),
	UD("UD", "EV_CC_02"),
	UG("UG", "EV_CC_03"),
	ET("ET", "EV_CC_04"),
	TO("TO", "SB_CC_02"),
	FO("FO", "SB_CC_02");

	private final String evDtlDvCd;
	private final String pointCd;

	EaEvPointCode(String evDtlDvCd, String pointCd) {
		this.evDtlDvCd = evDtlDvCd;
		this.pointCd = pointCd;
	}

	public static String getPointCode(String evDtlDvCd) {

		return Arrays.stream(values())
			.filter(e -> e.getEvDtlDvCd().equals(evDtlDvCd))
			.findAny()
			.map(EaEvPointCode::getPointCd)
			.orElse(null);
	}
}
