<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.asn.tcr">
	<!-- 과제 목록 조회 -->
	<select id="selectAsnTcrList" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/* EaStuAsnTcr-Mapper.xml - selectAsnTcrList */
		<include refid="api.ea.common.pagingHeader"/>
		SELECT  A.ASN_ID			-- 과제ID
			   ,A.OPT_TXB_ID 		-- 운영교과서ID
			   ,A.TCR_USR_ID 		-- 교사사용자ID
			   ,A.ASN_NM 			-- 과제명
			   ,A.ASN_CN 			-- 과제설명
			   ,A.ASN_TP_CD 		-- 과제유형코드
			   ,A.LRN_TP_CD 		-- 학습유형코드
			   ,A.ASN_LRN_TP		-- 유형코드
			   ,A.ASN_LRN_TP_NM		-- 유형코드이름
			   ,A.ASN_PTME_DV_CD 	-- 과제기간구분코드
			   ,A.STR_DTM 			-- 시작일시
			   ,A.STR_DTM_NM
			   ,A.END_DTM 			-- 종료일시
			   ,A.END_DTM_NM
			   ,A.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
			   ,A.DEL_YN 			-- 삭제여부
			   ,A.USE_YN  			-- 사용여부
			   ,A.LCKN_YN  			-- 잠금여부
		       ,A.ALL_STXQ_YN       -- 전체출제여부
			   ,A.CRTR_ID 			-- 생성자ID
			   ,A.CRT_DTM 			-- 생성일시
			   ,A.MDFR_ID 			-- 수정자ID
			   ,A.MDF_DTM 			-- 수정일시
			   ,A.SP_LRN_ID			-- 특별학습ID
			   ,A.LRN_STP_DV_CD		-- 학습단계구분코드
			   ,A.LU_NOD_ID			-- 단원노드ID
			   ,A.TC_NOD_ID			-- 차시노드ID
			--   ,A.ATV_CTN_ID		-- 활동콘텐츠ID
			   ,A.NOD_NM 			-- 단원명
			   ,A.TC_NM  			-- 차시명
			   ,A.NOD_NO			-- 단원넘버링
			   ,A.TC_NO				-- 차시넘버링
			   ,TOT_USR_CNT			-- 과제인원수(모둠일경우 모둠팀수)
			   ,STU_CMP_ASG_CNT		-- 과제별 제출완료인원
			   ,A.RCSTN_ORDN
		  FROM
			(
				SELECT  EA.ASN_ID			-- 과제ID
					   ,EA.OPT_TXB_ID 		-- 운영교과서ID
					   ,EA.TCR_USR_ID 		-- 교사사용자ID
					   ,EA.ASN_NM 			-- 과제명
					   ,EA.ASN_CN 			-- 과제설명
					   ,EA.ASN_TP_CD 		-- 과제유형코드
					   ,EA.LRN_TP_CD 		-- 학습유형코드
					   ,CASE
			   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL THEN EA.ASN_TP_CD
				   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL THEN EA.LRN_TP_CD
					   END AS ASN_LRN_TP	-- 유형코드
					   ,CASE
					    	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL
					  	      THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.ASN_TP_CD AND CM.URNK_CM_CD ='ASN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
					 	  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL
					 		  THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.LRN_TP_CD AND CM.URNK_CM_CD ='LRN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
					    END AS ASN_LRN_TP_NM	-- 유형코드이름
					   ,EA.ASN_PTME_DV_CD 	-- 과제기간구분코드
					   ,EA.STR_DTM 			-- 시작일시
					   ,CONCAT(DATE_FORMAT(EA.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.STR_DTM,'%h:%i')) AS STR_DTM_NM
					   ,EA.END_DTM 			-- 종료일시
					   ,CONCAT(DATE_FORMAT(EA.END_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.END_DTM,'%h:%i')) AS END_DTM_NM
					   ,EA.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
					   ,EA.DEL_YN 			-- 삭제여부
					   ,EA.USE_YN  			-- 사용여부
					   ,EA.LCKN_YN  		-- 잠금여부
				       ,EA.ALL_STXQ_YN       -- 전체출제여부
					   ,EA.CRTR_ID 			-- 생성자ID
					   ,EA.CRT_DTM 			-- 생성일시
					   ,EA.MDFR_ID 			-- 수정자ID
					   ,EA.MDF_DTM 			-- 수정일시
					   ,EAR.SP_LRN_ID		-- 특별학습ID
					   ,EAR.LRN_STP_DV_CD	-- 학습단계구분코드
					   ,EAR.LU_NOD_ID		-- 단원노드ID
					   ,EAR.TC_NOD_ID		-- 차시노드ID
				        ,CASE
							WHEN EA.LRN_TP_CD = 'TL' OR EA.ASN_TP_CD = 'GE' OR EA.ASN_TP_CD = 'GR' THEN TSLNR.LRMP_NOD_NM 
							<!-- WHEN EA.LRN_TP_CD = 'SL' THEN SSLR.SP_LRN_NM -->
							WHEN EA.LRN_TP_CD = 'AL' THEN AKNR.KMMP_NOD_NM
						 END NOD_NM 						-- 단원명
					    ,TS.LRMP_NOD_NM AS TC_NM  			-- 차시명
					    ,TSLNR.RCSTN_NO AS NOD_NO			-- 단원넘버링
					    ,TS.RCSTN_NO AS TC_NO				-- 차시넘버링
					    ,CASE
					   		WHEN EA.ASN_TP_CD ='GR'
					   			THEN (SELECT COUNT(EGAS.GRP_TEM_ID) FROM LMS_LRM.EA_GRP_ASN_SMT EGAS WHERE EGAS.ASN_ID = EA.ASN_ID)
					   		ELSE (SELECT COUNT(EAST.STU_USR_ID)  FROM LMS_LRM.EA_ASN_SMT EAST WHERE EAST.ASN_ID = EA.ASN_ID )
					     END AS TOT_USR_CNT
					    ,CASE
					   		WHEN EA.ASN_TP_CD ='GR'
					   			THEN (SELECT SUM(CASE WHEN EGAS2.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_GRP_ASN_SMT EGAS2 WHERE EGAS2.ASN_ID = EA.ASN_ID)
					   		ELSE (SELECT SUM(CASE WHEN EAST2.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_ASN_SMT EAST2 WHERE EAST2.ASN_ID = EA.ASN_ID)
					     END AS STU_CMP_ASG_CNT
					    ,TSLNR.RCSTN_ORDN
				  FROM LMS_LRM.EA_ASN EA	-- EA_과제
				  LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
				    ON EA.ASN_ID = EAR.ASN_ID
				   AND EAR.DEL_YN = 'N'
				  LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR	-- TL_교과학습노드재구성
				    ON EAR.OPT_TXB_ID = TSLNR.OPT_TXB_ID
				   AND EAR.LU_NOD_ID = TSLNR.LRMP_NOD_ID
				   AND TSLNR.USE_YN ='Y'
				   AND IFNULL(TSLNR.URNK_LRMP_NOD_ID, '') = ''
				  LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TS
				    ON EAR.OPT_TXB_ID = TS.OPT_TXB_ID
				   AND EAR.TC_NOD_ID = TS.LRMP_NOD_ID
				  -- TL_특별학습노드재구성
				  <!-- LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN SSLR 
				    ON EAR.OPT_TXB_ID = SSLR.OPT_TXB_ID
				   AND EAR.SP_LRN_ID = SSLR.SP_LRN_ID
				   AND SSLR.USE_YN ='Y' -->
				  LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR -- AI_지식맵노드재구성
			        ON EA.OPT_TXB_ID = AKNR.OPT_TXB_ID
			       AND EAR.LU_NOD_ID = AKNR.KMMP_NOD_ID
			       AND AKNR.DEL_YN ='N'
				 WHERE 1=1
				  <!--  AND EA.TCR_USR_ID = #{tcrUsrId} -->
				   AND EA.OPT_TXB_ID = #{optTxbId}
				   AND EA.DEL_YN = 'N'
				   AND EA.USE_YN = 'Y'
			   ) AS A
		WHERE 1=1
		<choose>
		    <!-- 진행중 탭  -->
    		<when test = 'tebCd != null and tebCd.equals("0")'>
				<![CDATA[
			    AND (A.STR_DTM  <=  NOW() AND A.END_DTM > NOW() AND  A.TOT_USR_CNT != A.STU_CMP_ASG_CNT AND A.LCKN_YN = 'N')
			    OR (A.ASN_PTME_DV_CD ='OT' AND A.TOT_USR_CNT != A.STU_CMP_ASG_CNT AND A.LCKN_YN = 'N')
			    ]]>
    		</when>
    		<!-- 완료 탭-->
    		<when test = 'tebCd != null and tebCd.equals("1")'>
			    AND A.END_DTM <![CDATA[<]]> NOW() AND A.LCKN_YN = 'N'
				OR A.TOT_USR_CNT = A.STU_CMP_ASG_CNT AND A.LCKN_YN = 'N'
    		</when>
		</choose>
		<if test = 'searchOption != null and !"".equals(searchOption)'>
		<!-- 
			    HAVING A.LU_NOD_ID IN (
	                #{searchOption}
	                <if test="aiSearchOptionList != null and aiSearchOptionList.size() > 0">
	                    , 
	                    <foreach item="aiId" collection="aiSearchOptionList" open="" separator="," close="">
	                        #{aiId}
	                    </foreach>
	                </if>
                )
                 -->
			<if test = 'asnTpOption != null and !"".equals(asnTpOption) and "LU".equals(asnTpOption)'>
				HAVING A.LU_NOD_ID IN (
			        #{searchOption}
			        <if test="aiSearchOptionList != null and aiSearchOptionList.size() > 0">
			            , 
			            <foreach item="aiId" collection="aiSearchOptionList" open="" separator="," close="">
			                #{aiId}
			            </foreach>
			        </if>
			    )
			 </if>
			 <if test = 'asnTpOption != null and !"".equals(asnTpOption) and "ASN".equals(asnTpOption)'>
				 HAVING A.ASN_LRN_TP = #{searchOption}
			 </if>
		</if>
		<choose>
		    <when test="sortOption == 'crtDtm'">
		     	ORDER BY A.CRT_DTM DESC
		    </when>
		    <when test="sortOption == 'luNodId'">
				ORDER BY
					CASE
				        WHEN A.NOD_NO IS NOT NULL THEN 1
				        WHEN A.NOD_NO IS NULL THEN 2
				        ELSE 3
				    END,
					CASE
				        WHEN A.RCSTN_ORDN IS NOT NULL THEN A.NOD_NO
				        WHEN A.NOD_NO IS NULL THEN
				            CASE
				                WHEN A.LRN_TP_CD = 'TL' THEN 1
				                WHEN A.LRN_TP_CD = 'AL' THEN 2
				                WHEN A.LRN_TP_CD = 'SL' THEN 3
				                WHEN A.ASN_TP_CD = 'GE' THEN 4
				                WHEN A.ASN_TP_CD = 'GR' THEN 5
				                ELSE 6
				            END
				    END
		    </when>
		    <otherwise>
		       ORDER BY
				    CASE
				        WHEN A.ASN_PTME_DV_CD = 'PT' THEN 1
				        WHEN A.ASN_PTME_DV_CD = 'OT' AND A.LU_NOD_ID IS NOT NULL THEN 2
				        WHEN A.ASN_PTME_DV_CD = 'OT' AND A.LU_NOD_ID IS NULL THEN 3
				        ELSE 4
				    END,
				    CASE
				        WHEN A.ASN_PTME_DV_CD = 'PT' THEN A.END_DTM
				        WHEN A.ASN_PTME_DV_CD = 'OT' AND A.LU_NOD_ID IS NOT NULL THEN A.LU_NOD_ID
				        WHEN A.ASN_PTME_DV_CD = 'OT' AND A.LU_NOD_ID IS NULL THEN
				            CASE
				                WHEN A.LRN_TP_CD = 'TL' THEN 1
				                WHEN A.LRN_TP_CD = 'AL' THEN 2
				                WHEN A.LRN_TP_CD = 'SL' THEN 3
				                WHEN A.ASN_TP_CD = 'GE' THEN 4
				                WHEN A.ASN_TP_CD = 'GR' THEN 5
				                ELSE 6
				            END
				    END
		    </otherwise>
  		</choose>
  		<include refid="api.ea.common.pagingFooter"/>
	</select>

	<!-- 과제 상세 조회 -->
	<select id="selectAsnTcrDetail" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/* EaStuAsnTcr-Mapper.xml - selectAsnTcrDetail */
		SELECT  EA.ASN_ID			-- 과제ID
			   ,EA.OPT_TXB_ID 		-- 운영교과서ID
			   ,EA.TCR_USR_ID 		-- 교사사용자ID
			   ,EA.ASN_NM 			-- 과제명
			   ,EA.ASN_CN 			-- 과제설명
			   ,EA.ASN_TP_CD 		-- 과제유형코드
			   ,EA.LRN_TP_CD 		-- 학습유형코드
			   ,CASE
		  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL THEN EA.ASN_TP_CD
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL THEN EA.LRN_TP_CD
			    END AS ASN_LRN_TP	-- 유형코드
			   ,EA.ASN_PTME_DV_CD 	-- 과제기간구분코드
			   ,EA.STR_DTM 			-- 시작일시
			   ,CONCAT(DATE_FORMAT(EA.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.STR_DTM,'%h:%i')) AS STR_DTM_NM
			   ,EA.END_DTM 			-- 종료일시
			   ,CONCAT(DATE_FORMAT(EA.END_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.END_DTM,'%h:%i')) AS END_DTM_NM
			   ,EA.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
			   ,EA.EV_MTHD_TP_CD	-- 평가방식유형코드
			   ,EA.PSC_SCR			-- 만점점수
			   ,EA.ANNX_ID 			-- 첨부ID 
			   ,EA.PKG_ASN_ID 		-- 묶음ID
			   ,EA.DEL_YN 			-- 삭제여부
			   ,EA.USE_YN  			-- 사용여부
			   ,EA.LCKN_YN  		-- 잠금여부
		       ,EA.ALL_STXQ_YN      -- 전체출제여부
			   ,EA.CRTR_ID 			-- 생성자ID
			   ,DATE_FORMAT(EA.CRT_DTM ,'%m. %d.') AS CRT_DTM
			   ,EA.MDFR_ID 			-- 수정자ID
			   ,EA.MDF_DTM 			-- 수정일시
			   ,EAR.LRN_STP_DV_CD	-- 학습단계구분코드
			   ,EAR.LU_NOD_ID		-- 단원노드ID
			   ,EAR.TC_NOD_ID		-- 차시노드ID
			   ,CASE
			   		WHEN EA.ASN_TP_CD ='GR'
			   			THEN (SELECT COUNT(EGAS.GRP_TEM_ID) FROM LMS_LRM.EA_GRP_ASN_SMT EGAS WHERE EGAS.ASN_ID = EA.ASN_ID)
			   		ELSE (SELECT COUNT(EAST.STU_USR_ID)  FROM LMS_LRM.EA_ASN_SMT EAST WHERE EAST.ASN_ID = EA.ASN_ID )
			     END AS TOT_USR_CNT
			    ,CASE
			   		WHEN EA.ASN_TP_CD ='GR'
			   			THEN (SELECT SUM(CASE WHEN EGAS2.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_GRP_ASN_SMT EGAS2 WHERE EGAS2.ASN_ID = EA.ASN_ID)
			   		ELSE (SELECT SUM(CASE WHEN EAST2.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_ASN_SMT EAST2 WHERE EAST2.ASN_ID = EA.ASN_ID)
			     END AS STU_CMP_ASG_CNT
			     ,CASE
			   		WHEN EA.ASN_TP_CD ='GR'
			   			THEN (SELECT SUM(CASE WHEN EGAS2.SMT_CMPL_YN = 'N' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_GRP_ASN_SMT EGAS2 WHERE EGAS2.ASN_ID = EA.ASN_ID)
			   		ELSE (SELECT SUM(CASE WHEN EAST2.SMT_CMPL_YN = 'N' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_ASN_SMT EAST2 WHERE EAST2.ASN_ID = EA.ASN_ID)
			     END AS STU_IMPRF_ASG_CNT
			     ,(SELECT EGAS.GRP_ID
			         FROM LMS_LRM.EA_GRP_ASN_SMT EGAS
			        WHERE EGAS.ASN_ID = EA.ASN_ID
			        LIMIT 1) AS GRP_ID
			     ,(SELECT COUNT(DISTINCT EGB.BLBD_ID)
			        FROM EA_GRP_BLBD EGB
			        WHERE EA.ASN_ID = EGB.ASN_ID) AS BLBD_CNT
			     ,CFUS.PRAS_STMP_USE_YN
			     ,CFUS.DILG_USE_YN
		    	 ,CASE
					WHEN EA.LRN_TP_CD = 'TL' OR EA.ASN_TP_CD = 'GE' OR EA.ASN_TP_CD = 'GR' THEN TSLNR.LRMP_NOD_NM 
					WHEN EA.LRN_TP_CD = 'AL' THEN AKNR.KMMP_NOD_NM
				  END NOD_NM 			-- 단원명
			  	,TS.LRMP_NOD_NM AS TC_NM  -- 차시명
			  	,TSLNR.RCSTN_NO AS NOD_NO			-- 단원넘버링
			  	,TS.RCSTN_NO AS TC_NO				-- 차시넘버링
			  	,(SELECT TSL.LRN_ATV_ID 
		            FROM TL_SBC_LRN_ATV_RCSTN TSL
		           INNER JOIN LMS_CMS.BC_LRN_STP S  
		              ON TSL.LRMP_NOD_ID  = S.LRMP_NOD_ID
	                 AND TSL.LRN_STP_ID = S.LRN_STP_ID 
	                 AND S.LRN_STP_DV_CD = 'EX'
	               WHERE EAR.TC_NOD_ID = TSL.LRMP_NOD_ID 
	                 AND EA.OPT_TXB_ID = TSL.OPT_TXB_ID LIMIT 1) AS LRN_ATV_ID
	            ,(SELECT TSL.EV_ID 
		          FROM TL_SBC_LRN_ATV_RCSTN TSL
		         INNER JOIN LMS_CMS.BC_LRN_STP S  
		            ON TSL.LRMP_NOD_ID  = S.LRMP_NOD_ID
	               AND TSL.LRN_STP_ID = S.LRN_STP_ID 
	               AND S.LRN_STP_DV_CD = 'EX'
	             WHERE EAR.TC_NOD_ID = TSL.LRMP_NOD_ID 
	               AND EA.OPT_TXB_ID = TSL.OPT_TXB_ID LIMIT 1) AS EV_ID
		  FROM LMS_LRM.EA_ASN EA	-- EA_과제
		  LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
		    ON EA.ASN_ID = EAR.ASN_ID
		   AND EAR.DEL_YN ='N'
		  LEFT JOIN LMS_LRM.CM_FNC_USE_SETM CFUS
		    ON EA.OPT_TXB_ID = CFUS.OPT_TXB_ID
		  LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR	-- TL_교과학습노드재구성
		    ON EAR.OPT_TXB_ID = TSLNR.OPT_TXB_ID
		   AND EAR.LU_NOD_ID = TSLNR.LRMP_NOD_ID
		   AND TSLNR.USE_YN ='Y'
		   AND IFNULL(TSLNR.URNK_LRMP_NOD_ID, '') = ''
		  LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TS
		    ON EAR.OPT_TXB_ID = TS.OPT_TXB_ID
		   AND EAR.TC_NOD_ID = TS.LRMP_NOD_ID
		  LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR -- AI_지식맵노드재구성
	        ON EA.OPT_TXB_ID = AKNR.OPT_TXB_ID
	       AND EAR.LU_NOD_ID = AKNR.KMMP_NOD_ID
	       AND AKNR.DEL_YN ='N'
	      LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AK
	        ON EA.OPT_TXB_ID = AK.OPT_TXB_ID
	       AND EAR.TC_NOD_ID = AK.KMMP_NOD_ID
	       AND AK.DEL_YN ='N'
		 WHERE 1=1
		   AND EA.DEL_YN = 'N'
		   AND EA.USE_YN = 'Y'
		   AND EA.OPT_TXB_ID = #{optTxbId}
<!-- 	   AND EA.TCR_USR_ID = #{tcrUsrId} -->
		   AND EA.ASN_ID = #{asnId}
	</select>


	<!-- 과제 상세 - 학생별 상세 목록-->
	<select id="selectAsnTcrDetailStuList" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/* EaAsnTcr-Mapper.xml - selectAsnTcrDetailStuList */
		SELECT
		       ROW_NUMBER() OVER (ORDER BY EAS.STU_USR_ID ) AS STU_NO
			  ,EAS.ASN_ID
			  ,EAS.STU_USR_ID
			  ,EAS.SMT_CN
			  ,EAS.ANNX_ID
			  ,EAS.SMT_CMPL_YN
			  ,EAS.SMT_DTM
			  ,EAS.MDF_DTM
			  ,CONCAT(DATE_FORMAT(EAS.SMT_DTM,'%m. %d. '),IF(TIME_FORMAT(EAS.SMT_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EAS.SMT_DTM,'%h:%i')) AS SMT_DTM_NM
			  ,CONCAT(DATE_FORMAT(EAS.MDF_DTM,'%m. %d. '),IF(TIME_FORMAT(EAS.MDF_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EAS.MDF_DTM,'%h:%i')) AS MDF_DTM_NM
			  ,EAS.SCR
			  ,EAS.FDBK_CN
			  ,EA.ASN_TP_CD
			  ,EA.LRN_TP_CD
			  ,EA.EV_MTHD_TP_CD
			  ,EA.PSC_SCR
			  ,EAR.LRN_STP_DV_CD
			  ,CASE
				  WHEN EA.EV_MTHD_TP_CD ='SC' AND EAS.SCR IS NOT NULL THEN 'Y'
				  ELSE 'N'
			   END  AS SCR_YN
			  ,TSLAR.EV_ID
			  ,CASE 
			  		WHEN EA.LRN_TP_CD = 'TL' THEN EE.FNL_QST_CNT
			  		WHEN EA.LRN_TP_CD = 'AL' THEN ( SELECT COUNT(EEQA.CANS_YN) AS QTM_CNT
													FROM LMS_LRM.EA_EV EE
													INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
													INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID 
													INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID and eaetr.TPC_KMMP_NOD_ID = EEQ.TPC_ID
													LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID and EEQ.QTM_ID = EEQA.QTM_ID
													WHERE EER.USR_ID = EAS.STU_USR_ID
													AND EE.EV_DV_CD = 'AE'
												 	AND EAETR.MLU_KMMP_NOD_ID = EAR.LU_NOD_ID)
			   END AS FNL_QST_CNT  -- 최종 문제 수
			  ,CASE 
			  		WHEN EA.LRN_TP_CD = 'TL' THEN EER.CANS_CNT
			  		WHEN EA.LRN_TP_CD = 'AL' THEN (
			  										 SELECT SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT
														FROM LMS_LRM.EA_EV EE
														INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
														INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID 
														INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID and eaetr.TPC_KMMP_NOD_ID = EEQ.TPC_ID
														LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID and EEQ.QTM_ID = EEQA.QTM_ID
														WHERE EER.USR_ID = EAS.STU_USR_ID
														AND EE.EV_DV_CD = 'AE'
													 	AND EAETR.MLU_KMMP_NOD_ID = EAR.LU_NOD_ID
			  										)
			   END AS CANS_CNT   -- 정답 수
			  ,EAS.CMPL_LRN_CNT
			  ,EAS.SCR
			  ,EA.TTL_LRN_CNT
			<!--   , COALESCE(
				        (
				          CASE
				            WHEN #{sbjCd} = 'MA' THEN
				              CASE
				                WHEN EA.LRN_TP_CD = 'AL' THEN 
				                  ROUND((EAS.CMPL_LRN_CNT / 
				                    (SELECT COUNT(AUTLO.TPC_KMMP_NOD_ID) 
				                     FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO
				                     WHERE AUTLO.USR_ID = EAS.STU_USR_ID 
				                       AND AUTLO.OPT_TXB_ID = EA.OPT_TXB_ID)) * 100)
				                ELSE ROUND((EAS.CMPL_LRN_CNT / EA.TTL_LRN_CNT) * 100)
				              END
				            ELSE
				              ROUND((EAS.CMPL_LRN_CNT / EA.TTL_LRN_CNT) * 100)
				          END
				        ),
				        0
      			) AS progress_rate-->
			  , COALESCE(
				        (
				          CASE
				            WHEN EA.LRN_TP_CD = 'AL' THEN
				              (SELECT AL.AI_LRN_PGRS_RT FROM LMS_LRM.AI_LRN_PGRS_PROF AL WHERE AL.MLU_KMMP_NOD_ID =EAR.LU_NOD_ID AND  AL.USR_ID = EAS.STU_USR_ID AND  AL.OPT_TXB_ID = EA.OPT_TXB_ID)
				            ELSE
				              ROUND((EAS.CMPL_LRN_CNT / EA.TTL_LRN_CNT) * 100)
				          END
				        ),
				        0
      			) AS progress_rate 
		  FROM LMS_LRM.EA_ASN_SMT EAS 	-- EA_과제제출
		 INNER JOIN LMS_LRM.EA_ASN EA
		    ON EAS.ASN_ID = EA.ASN_ID
		   AND EA.DEL_YN = 'N'
		   AND EA.OPT_TXB_ID = #{optTxbId}
<!-- 		   AND EA.TCR_USR_ID = #{tcrUsrId} -->
		 LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR
	        ON EAR.ASN_ID = EA.ASN_ID
	       AND EAR.DEL_YN ='N'
	  	  LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN TSLAR
	    	ON EA.OPT_TXB_ID = TSLAR.OPT_TXB_ID
	       AND EAR.TC_NOD_ID = TSLAR.LRMP_NOD_ID
	   	   AND TSLAR.CTN_TP_CD = 'EX'
	  	  LEFT JOIN LMS_LRM.EA_EV EE
	    	ON TSLAR.EV_ID = EE.EV_ID
	  	  LEFT JOIN LMS_LRM.EA_EV_RS EER
	    	ON EE.EV_ID = EER.EV_ID
	   	   AND EER.USR_ID = EAS.STU_USR_ID
		 WHERE 1=1
		   AND EA.USE_YN = 'Y'
		   AND EAS.ASN_ID = #{asnId}
		   <if test = 'smtCmplYn != null and !"".equals(smtCmplYn)'>
		   		AND EAS.SMT_CMPL_YN = #{smtCmplYn}
		   </if>
		   <if test = 'stuUsrId != null and !"".equals(stuUsrId)'>
		   		AND EAS.STU_USR_ID = #{stuUsrId}
		   </if>
	</select>

	<!-- 과제 상세 파일조회 -->
	<select id="selectAsnFile" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnFleDto">
		/* EaAsnTcr-Mapper.xml - selectAsnFile */
		SELECT
			   CAF.ANNX_FLE_ID					AS annxFileId
			  ,CAF.ANNX_ID						AS annxId
			  ,CAF.SRT_ORDN						AS srtOrdn
			  ,CAF.DOC_VI_ID				AS docViId
			  ,CAF.ANNX_FLE_NM				AS annxFileNm
			  ,CAF.ANNX_FLE_ORGL_NM		AS annxFileOrglNm
			  ,CAF.ANNX_FLE_FEXT_NM		AS annxFileFextNm
			  ,CAF.ANNX_FLE_SZE				AS annxFileSize
			  ,CAF.ANNX_FLE_PTH_NM			AS annxFilePathNm
			  ,CAF.USE_YN							AS useYn
		FROM
			LMS_LRM.CM_ANNX_FLE CAF
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>

	<!-- 과제 출제 - EA_과제 저장 -->
	<insert id="insertAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto"
            useGeneratedKeys="true" keyProperty="asnId" keyColumn="asn_Id">
        /* EaAsnTcr-Mapper.xml - insertAsnTcr */
        INSERT INTO
		LMS_LRM.EA_ASN (
			 OPT_TXB_ID
		    ,TCR_USR_ID
		    ,ASN_NM
		    ,ASN_CN
		    ,ASN_TP_CD
		    ,LRN_TP_CD
		    ,ASN_PTME_DV_CD
		    ,STR_DTM
		    ,END_DTM
		    ,FIN_AF_SMT_ABLE_YN
		    ,EV_MTHD_TP_CD
		    ,PSC_SCR
		    ,ANNX_ID
		    ,TTL_LRN_CNT
		    ,PKG_ASN_ID
		    ,DEL_YN
		    ,USE_YN
			,LCKN_YN
			,ALL_STXQ_YN
		    ,CRTR_ID
		    ,CRT_DTM
		    ,MDFR_ID
	  	    ,MDF_DTM
		    ,DB_ID
		)
		VALUES(
			 #{optTxbId}
			,#{tcrUsrId}
			,#{asnNm}
			,#{asnCn}
			,#{asnTpCd}
			,#{lrnTpCd}
			,#{asnPtmeDvCd}
			,#{strDtm}
			,#{endDtm}
			,#{flnAfSmtAbleYn}
			,#{evMthdTpCd}
			,#{pscScr}
			,#{annxId}
			,#{ttlLrnCnt}
			,#{pkgAsnId}
			,'N'
			,'Y'
			,'N'
			,#{allStxqYn}
			,#{crtrId}
			,NOW()
			,#{mdfrId}
			,NOW()
			,#{dbId}
		)
	</insert>

	<!-- 과제 출제 - EA_과제제출 저장 -->
	<insert id="insertAsnSmt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		  /* EaAsnTcr-Mapper.xml - insertAsnSmt */
		INSERT INTO
		LMS_LRM.EA_ASN_SMT  (
			   ASN_ID
			  ,STU_USR_ID
			  ,SMT_CMPL_YN
			  ,CMPL_LRN_CNT
			  ,CRTR_ID
			  ,CRT_DTM
			  ,MDFR_ID
			  ,MDF_DTM
			  ,DB_ID
		)
		VALUES
		<foreach collection="usrIdList" item="usrId" separator=",">
			(
				 #{asnId}
				,#{usrId}
				,'N'
				,#{cmplLrnCnt}
				,#{crtrId}
				,NOW()
				,#{mdfrId}
				,NOW()
				,#{dbId}
			)
		</foreach>
    </insert>

	<!-- 사용자의 가장 최근 게시글 작성/수정 시간과 현재 시각의 차이 (단위 초) -->
	<select id="selectUsrLstEaAsnTmDff" parameterType="String" resultType="java.lang.Long">
		SELECT TIMESTAMPDIFF(SECOND, EA_ASN.MDF_DTM, NOW()) as TIME_DIFF
		FROM LMS_LRM.EA_ASN EA_ASN
		WHERE EA_ASN.TCR_USR_ID = #{usrId}
		ORDER BY EA_ASN.MDF_DTM DESC
		LIMIT 1
	</select>

    <!-- 과제 출제 - EA_과제범위 저장(교과,AI) -->
	<insert id="insertAsnRnge" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
	/* EaAsnTcr-Mapper.xml - insertAsnRnge */
		INSERT INTO
		LMS_LRM.EA_ASN_RNGE  (
			   ASN_ID
		 	  ,ASN_RNGE_SEQ_NO
			  ,LRN_TP_CD
			  ,OPT_TXB_ID
			  ,SP_LRN_ID
			  ,LRN_STP_DV_CD
			  ,LU_NOD_ID
			  ,TC_NOD_ID
			  ,ATV_CTN_ID
			  ,DEL_YN
			  ,CRTR_ID
			  ,CRT_DTM
			  ,MDFR_ID
			  ,MDF_DTM
			  ,DB_ID
		)
		VALUES
			(
				 #{asnId}
				,#{asnRngeSeqNo}
				,#{lrnTpCd}
				,#{optTxbId}
				,#{spLrnId}
				,#{lrnStpDvCd}
				,#{luNodId}
				,#{tcNodId}
				,#{atvCtnId}
				,'N'
				,#{crtrId}
				,NOW()
				,#{mdfrId}
				,NOW()
				,#{dbId}
			)
	</insert>

    <!-- 과제 출제 - EA_과제범위 저장(특별) -->
	<insert id="insertSlAsnRnge" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
	/* EaAsnTcr-Mapper.xml - insertSlAsnRnge */
	INSERT INTO
		LMS_LRM.EA_ASN_RNGE  (
			   ASN_ID
			  ,ASN_RNGE_SEQ_NO
			  ,LRN_TP_CD
			  ,OPT_TXB_ID
			  ,SP_LRN_ID
			  ,LU_NOD_ID
			  ,DEL_YN
			  ,CRTR_ID
			  ,CRT_DTM
			  ,MDFR_ID
			  ,MDF_DTM
			  ,DB_ID
		)
		VALUES
		<foreach collection="luNodList" item="item" separator=",">
			(
				 #{asnId}
				,#{item.no}
				,#{lrnTpCd}
				,#{optTxbId}
				,#{spLrnId}
				,#{item.spLrnNodId}
				,'N'
				,#{crtrId}
				,NOW()
				,#{mdfrId}
				,NOW()
				,#{dbId}
			)
		</foreach>
	</insert>

	<!-- 과제 삭제 -->
	<delete id="deleteAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteAsn */
		DELETE FROM 
			LMS_LRM.EA_ASN
		 WHERE ASN_ID  = #{asnId}
	</delete>
	
		<!-- 과제제출 학생 평가 초기화 -->
	<update id="updateSmt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - updateSmt */
		UPDATE LMS_LRM.EA_ASN_SMT 
		   SET
				 SCR  = NULL 
				,FDBK_CN = NULL 
				,MDFR_ID = #{mdfrId}
				,MDF_DTM = NOW()
		 WHERE ASN_ID  = #{asnId}
	</update>
	
		<!-- 모둠과제제출 학생 평가 초기화 -->
	<update id="updateGrpSmt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - updateGrpSmt */
		 UPDATE LMS_LRM.EA_GRP_ASN_SMT 
		    SET
				 SCR  = NULL 
				,FDBK_CN = NULL 
				,MDFR_ID = #{mdfrId}
				,MDF_DTM = NOW()
		  WHERE ASN_ID  = #{asnId}
	</update>

	<!-- 과제 평가 저장 - 일반-->
	<update id="updateAsnEv" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - updateAsnEv */
	    UPDATE LMS_LRM.EA_ASN_SMT
	       SET
	     	   MDFR_ID = #{mdfrId}
			  <if test = '"SC".equals(evMthdTpCd) and scr != null and !"".equals(scr)'>
		   		,SCR =	#{scr}
		      </if>
			  <if test = '"FB".equals(evMthdTpCd) and fdbkCn != null and !"".equals(fdbkCn)'>
		   		,FDBK_CN = #{fdbkCn}
		      </if>
	   WHERE ASN_ID  = #{asnId}
	     AND STU_USR_ID = #{stuUsrId}
	</update>

	<!-- 과제 평가 저장 - 모둠 -->
	<update id="updateAsnGrpEv" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - updateAsnGrpEv */
	    UPDATE LMS_LRM.EA_GRP_ASN_SMT
	       SET
	     	  MDFR_ID = #{mdfrId}
		      <if test = '"SC".equals(evMthdTpCd) and scr != null and !"".equals(scr)'>
		   		,SCR =	#{scr}
		      </if>
			  <if test = '"FB".equals(evMthdTpCd) and fdbkCn != null and !"".equals(fdbkCn)'>
		   		,FDBK_CN = #{fdbkCn}
		      </if>
		   WHERE
		   		ASN_ID  = #{asnId}
		   AND
		   		GRP_ID = #{grpId}
		   	AND
		   		GRP_TEM_ID = #{grpTemId}
	</update>

	<!-- 과제 수정 -->
	<update id="updateAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - updateAsn */

		UPDATE LMS_LRM.EA_ASN
		   SET
		   	   MDFR_ID = #{mdfrId}
			  ,MDF_DTM = NOW()
			  <if test = 'asnNm != null and !"".equals(asnNm)'>
			  	,ASN_NM = #{asnNm}
			  </if>
			  <if test = 'asnCn != null and !"".equals(asnCn)'>
			  	,ASN_CN = #{asnCn}
			  </if>
			  <if test = 'evMthdTpCd != null and !"".equals(evMthdTpCd)'>
			  	,EV_MTHD_TP_CD = #{evMthdTpCd}
			  </if>
			  ,ANNX_ID = #{annxId}
	   		  ,PSC_SCR =	#{pscScr}
		  	  ,ASN_PTME_DV_CD = #{asnPtmeDvCd}
		      ,ALL_STXQ_YN = #{allStxqYn}
		  	  ,STR_DTM = #{strDtm}
		  	  ,END_DTM = #{endDtm}
		 WHERE ASN_ID  = #{asnId}
	</update>

	<!-- 일반,모둠 단원,차시 조회 -->
	<select id="selectNodId" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
	/** EaAsnTcr-Mapper.xml - selectNodId */
		SELECT ASN_ID
	       ,LU_NOD_ID
		   ,TC_NOD_ID
	   FROM LMS_LRM.EA_ASN_RNGE
	  WHERE ASN_ID = #{asnId}
	</select>

	<!-- 과제범위 수정 -->
	<update id="updateAsnRnge" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - updateAsnRnge */
		 UPDATE EA_ASN_RNGE
		    SET
				 LU_NOD_ID  = #{luNodId}
				,TC_NOD_ID  = #{tcNodId}
				,MDFR_ID = #{mdfrId}
				,MDF_DTM = NOW()
		  WHERE ASN_ID  = #{asnId}
	</update>
	
	<!-- 과제범위 삭제 -->
	<delete id="deleteAsnRnge" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteAsnRnge */
		 DELETE
		   FROM
			    LMS_LRM.EA_ASN_RNGE
		  WHERE ASN_ID = #{asnId}
	</delete>

	<!-- 과제제출 삭제 -->
	<delete id="deleteAsnSmt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteAsnSmt */
		 DELETE
		   FROM
			    LMS_LRM.EA_ASN_SMT
		  WHERE ASN_ID = #{asnId}
		  <if test = 'updateYn != null and !"".equals(updateYn) and asnTpCd != "GR"'>
			  	AND SMT_CMPL_YN = 'N'
		  </if>
	</delete>

	<!-- 과제 상세 - 과제 중복 학생 목록-->
	<select id="dplctAsnStuList" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		 /** EaAsnTcr-Mapper.xml - dplctAsnStuList */
		 SELECT EAS.STU_USR_ID
		   FROM LMS_LRM.EA_ASN_SMT EAS
		  INNER JOIN (
			  			 SELECT EAR.ASN_ID
						   FROM LMS_LRM.EA_ASN_RNGE EAR
						  INNER JOIN EA_ASN EA 
						     ON EAR.ASN_ID = EA.ASN_ID
						    AND EA.DEL_YN = 'N'
						  <if test = 'asnId != null and !"".equals(asnId)'>
						    AND EA.ASN_ID != #{asnId}
						  </if>
						  WHERE EAR.OPT_TXB_ID = #{optTxbId}
					     <if test = 'lrnTpCd != null and !"".equals(lrnTpCd) and "TL".equals(lrnTpCd)'>
							AND EAR.LRN_TP_CD = 'TL'
							AND EAR.LU_NOD_ID = #{luNodId}
						    AND EAR.TC_NOD_ID = #{tcNodId}
						    AND EAR.LRN_STP_DV_CD = #{lrnStpDvCd}
						 </if>
					     <if test = 'lrnTpCd != null and !"".equals(lrnTpCd) and "SL".equals(lrnTpCd)'>
							AND EAR.LRN_TP_CD = 'SL'
	 					    AND EAR.SP_LRN_ID = #{spLrnId}
	 					    AND EAR.LU_NOD_ID = #{luNodId}
	 					 </if>
						 <if test = 'lrnTpCd != null and !"".equals(lrnTpCd) and "AL".equals(lrnTpCd)'>
							AND EAR.LRN_TP_CD = 'AL'
 						    AND EAR.LU_NOD_ID = #{luNodId}
 						    AND EAR.TC_NOD_ID = #{tcNodId}
		 				 </if>
						  GROUP BY EAR.ASN_ID
		  			) A
		  	ON EAS.ASN_ID = A.ASN_ID
		 GROUP BY STU_USR_ID
	</select>

	<!-- 우리반수업 / AI맞춤학습 과제 출제 된 학생 수 카운트 -->
	<select id="compareAsnStuCount" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="int">
		/** EaAsnTcr-Mapper.xml - compareAsnStuCount */
		SELECT
			CASE
				WHEN
					(
						SELECT COUNT(DISTINCT EAS.STU_USR_ID)
					 	FROM LMS_LRM.EA_ASN_SMT EAS
						INNER JOIN (
							SELECT EAR.ASN_ID
							FROM LMS_LRM.EA_ASN_RNGE EAR
							INNER JOIN LMS_LRM.EA_ASN EA ON EAR.ASN_ID = EA.ASN_ID AND EA.DEL_YN = 'N'
							WHERE 1=1
								AND EAR.OPT_TXB_ID = #{optTxbId}
							    AND EAR.LRN_TP_CD = #{lrnTpCd}
							    AND EAR.LU_NOD_ID = #{luNodId}
								<if test="lrnTpCd == 'TL'">
									AND EAR.TC_NOD_ID = #{tcNodId}
									AND EAR.LRN_STP_DV_CD = #{lrnStpDvCd}
								</if>
							GROUP BY EAR.ASN_ID
					 	) A ON EAS.ASN_ID = A.ASN_ID
					)
					    >=
					(
						SELECT COUNT(DISTINCT USR_ID)
					 	FROM LMS_LRM.CM_USR CU
					 	WHERE 1=1
				    		AND CU.CLA_ID = #{optTxbId}
						  	AND CU.USR_TP_CD = 'ST'
					)
				THEN 1
				ELSE 0
			END AS compare_result
	</select>

	<!-- 해당 모둠의 모둠팀 ID 조회 (EA_모둠과제제출 테이블에 Insert 하기 위해) -->
	<select id="selectGrpId" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - selectGrpId */
		SELECT
			GRP_TEM_ID
		FROM
			LMS_LRM.EA_GRP_TMBR
		WHERE
			GRP_ID = #{grpId}
		GROUP BY GRP_TEM_ID
	</select>

	<!-- 해당 모둠 그룹의 모둠팀, 인원, 팀 수 조회 -->
	<select id="selectGrpInfo" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - selectGrpInfo */
		SELECT
			A.GRP_ID 				AS grpId
			, A.GRP_TEM_ID 		AS grpTemId
			, A.STU_USR_ID 		AS stuUsrId
			, A.GRP_TMGR_YN 	AS grpTmgrYn
			, B.TEM_CNT 			AS temCnt
		FROM
			LMS_LRM.EA_GRP_TMBR A
		LEFT JOIN
			LMS_LRM.EA_GRP B
		ON
			A.GRP_ID = B.GRP_ID
		WHERE
			A.GRP_ID = #{grpId}
	</select>

	<!-- 모둠 과제 출제 개별 저장 -->
	<insert id="insertGrpAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - insertGrpAsn */
		INSERT INTO LMS_LRM.EA_ASN_SMT (
			ASN_ID
			, STU_USR_ID
			, SMT_CMPL_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		) VALUES
		<foreach collection="grpUserInfo" item="temp" separator=",">
		(
			#{asnId}
			, #{temp.stuUsrId}
			, 'N'
			, #{tcrUsrId}
			, NOW()
			, #{tcrUsrId}
			, NOW()
			, #{dbId}
		)
		</foreach>
	</insert>

	<select id="selectTemCnt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="int">
		SELECT
			TEM_CNT AS temCnt
		FROM
			LMS_LRM.EA_GRP
		WHERE
			GRP_ID = #{grpId}
	</select>

	<!-- EA_모둠과제제출 테이블 저장 -->
	<insert id="insertGrpAsnSmt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - insertGrpAsnSmt */
		INSERT INTO LMS_LRM.EA_GRP_ASN_SMT (
			GRP_ID
			, GRP_TEM_ID
			, ASN_ID
			, SMT_CMPL_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
  			, MDF_DTM
  			, DB_ID
		) VALUES
		(
			#{grpId}
			, #{grpTemId}
			, #{asnId}
			, 'N'
			, #{tcrUsrId}
			, NOW()
			, #{tcrUsrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 모둠 게시판 Conut -->
	<select id="selectBoardCnt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="int">
		/** EaAsnTcr-Mapper.xml - selectBoardCnt */
		SELECT
			COUNT(GRP_TEM_ID)
		FROM
			LMS_LRM.EA_GRP_BLBD
		WHERE
			GRP_ID = #{originGrpId}
		AND
			ASN_ID = #{asnId}
	</select>

	<!-- 모둠별 게시판 생성 -->
	<insert id="insertGrpBoard" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - insertGrpBoard */
		INSERT INTO LMS_LRM.EA_GRP_BLBD (
			GRP_ID
			, GRP_TEM_ID
			, ASN_ID
			, BLBD_NM
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
  			, MDF_DTM
  			, DB_ID
		) VALUES
		(
			#{grpId}
			, #{grpTemId}
			, #{asnId}
			, '모둠 게시판'
			, #{tcrUsrId}
			, NOW()
			, #{tcrUsrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 모둠 과제 정보 수정 -->
	<update id="updateGrpAsn">
		/** EaAsnTcr-Mapper.xml - updateGrpAsn */
	    UPDATE
	    	LMS_LRM.EA_ASN
	    SET
	    	MDFR_ID = #{tcrUsrId}
			, MDF_DTM = NOW()
			, ANNX_ID = #{annxId}
	    	, ASN_NM = #{asnNm}
	    	, ASN_CN = #{asnCn}
	    	, EV_MTHD_TP_CD = #{evMthdTpCd}
	    	, ASN_PTME_DV_CD = #{asnPtmeDvCd}
	    	<if test="pscScr != null and pscScr != 0">
				, PSC_SCR = #{pscScr}
			</if>
			<if test = "asnPtmeDvCd == 'PT'">
			  	<!-- 기간 -->
			  	, STR_DTM = #{strDtm}
			  	, END_DTM = #{endDtm}
			</if>
			<if test = "asnPtmeDvCd == 'OT'">
			  	<!-- 상시 -->
			  	, STR_DTM = '9999-01-01 00:00:00'
			  	, END_DTM = '9999-12-31 23:59:59''
			</if>
		WHERE
			ASN_ID  = #{asnId}
		AND
			OPT_TXB_ID = #{optTxbId}
	</update>

	<!-- 모둠과제제출 삭제 -->
	<delete id="deleteGrpAsnSmt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteGrpAsnSmt */
		 DELETE FROM
			LMS_LRM.EA_GRP_ASN_SMT
		WHERE
			ASN_ID = #{asnId}
	</delete>
	
	<!-- 모둠 게시판 댓글 삭제 -->
	<delete id="deleteGrpBoardUcwr" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteGrpBoardUcwr */
		 DELETE FROM
			EA_GRP_BLBD_UCWR A
  		 WHERE 
  		 	A.BLBD_ID IN (SELECT B.BLBD_ID FROM EA_GRP_BLBD B WHERE B.ASN_ID = #{asnId})
	</delete>
	
	<!-- 모둠 게시판 게시글 삭제 -->
	<delete id="deleteGrpBoardBlwr" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteGrpBoardBlwr */
		DELETE FROM
	    	LMS_LRM.EA_GRP_BLBD_BLWR A
  		 WHERE 
  		 	A.BLBD_ID IN (SELECT B.BLBD_ID FROM EA_GRP_BLBD B WHERE B.ASN_ID = #{asnId})
	</delete>

	<!-- 모둠 게시판 삭제 -->
	<delete id="deleteGrpBoard" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteGrpBoard */
		 DELETE FROM
			LMS_LRM.EA_GRP_BLBD
		WHERE
			ASN_ID = #{asnId}
		AND
			GRP_ID = #{originGrpId}
	</delete>

	<!-- 모둠과제 상세 조회 -->
	<select id="selectGrpAsnDetail" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - selectGrpAsnDetail */
		SELECT
			<!-- COUNT(CASE A.SMT_CMPL_YN = 'Y' WHEN 1 THEN 0 END) 				AS completeGrpCnt
			, COUNT(CASE A.SMT_CMPL_YN = 'N' WHEN 1 THEN 0 END) 			AS inCompleteGrpCnt
			, COUNT(A.GRP_ID) 																		AS totalGrpCnt -->
			  S.completeGrpCnt
		    , S.inCompleteGrpCnt
		    , S.totalGrpCnt
			, B.GRP_ID			 																		AS grpId
			, B.GRP_NM 																				AS grpNm
			, CONCAT(DATE_FORMAT(A.FST_SMT_DTM,'%m. %d. '), IF(TIME_FORMAT(A.FST_SMT_DTM, '%p') = 'AM', '오전 ', '오후 '), DATE_FORMAT(A.FST_SMT_DTM,'%h:%i')) AS fstSmtDtm
			, CONCAT(DATE_FORMAT(C.STR_DTM,'%m. %d. '), IF(TIME_FORMAT(C.STR_DTM, '%p') = 'AM', '오전 ', '오후 '), DATE_FORMAT(C.STR_DTM,'%h:%i')) 		AS strDtm
			, CONCAT(DATE_FORMAT(C.END_DTM,'%m. %d. '), IF(TIME_FORMAT(C.END_DTM, '%p') = 'AM', '오전 ', '오후 '), DATE_FORMAT(C.END_DTM,'%h:%i')) 	AS endDtm
			, C.ASN_ID
			, C.ASN_NM 																				AS asnNm
			, C.ASN_CN 																				AS asnCn
			, DATE_FORMAT(C.CRT_DTM,'%m. %d. ') 										AS crtDtm
			, CASE
				WHEN NOW() <![CDATA[>]]> C.END_DTM
				THEN 'end'
				WHEN NOW() <![CDATA[<]]> C.STR_DTM
				THEN 'expected'
				WHEN C.STR_DTM <![CDATA[<]]> NOW() AND NOW() <![CDATA[<]]> C.END_DTM
				THEN 'proceeding'
			END AS tebCd
			, C.ASN_PTME_DV_CD 																	AS asnPtmeDvCd
			, C.ANNX_ID																				AS annxId
			, C.EV_MTHD_TP_CD
			, CFUS.PRAS_STMP_USE_YN
			, CFUS.MYHM_PNT_USE_YN
			, CFUS.DILG_USE_YN
			, EAR.LU_NOD_ID		-- 단원노드ID
			, EAR.TC_NOD_ID		-- 차시노드ID
			, TSLNR.LRMP_NOD_NM AS NOD_NM
		    , TS.LRMP_NOD_NM AS TC_NM  -- 차시명
			, TSLNR.RCSTN_NO AS NOD_NO			-- 단원넘버링
			, TS.RCSTN_NO AS TC_NO				-- 차시넘버링
		FROM
			LMS_LRM.EA_GRP_ASN_SMT A
		INNER JOIN
			LMS_LRM.EA_ASN C
		  ON
			A.ASN_ID = C.ASN_ID
		  AND 
		  	C.OPT_TXB_ID = #{optTxbId}
		 <!--  AND 
		  	C.TCR_USR_ID = #{tcrUsrId} -->
		LEFT JOIN
			LMS_LRM.EA_GRP B
		  ON
			A.GRP_ID = B.GRP_ID
	    LEFT JOIN
	    	LMS_LRM.CM_FNC_USE_SETM CFUS
		  ON
		  	C.OPT_TXB_ID = CFUS.OPT_TXB_ID
		LEFT JOIN 
			LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
	      ON 
	      	C.ASN_ID = EAR.ASN_ID
	     AND 
	     	EAR.DEL_YN ='N'
	    LEFT JOIN 
	    	LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR	-- TL_교과학습노드재구성
		  ON 
		   	C.OPT_TXB_ID = TSLNR.OPT_TXB_ID
		 AND 
		   	EAR.LU_NOD_ID = TSLNR.LRMP_NOD_ID
		 AND 
		   	TSLNR.USE_YN ='Y'
		 AND 
		 	IFNULL(TSLNR.URNK_LRMP_NOD_ID, '') = ''
	    LEFT JOIN 
	    	LMS_LRM.TL_SBC_LRN_NOD_RCSTN TS
	      ON 
	      	C.OPT_TXB_ID = TS.OPT_TXB_ID
	     AND 
	     	EAR.TC_NOD_ID = TS.LRMP_NOD_ID
	     LEFT JOIN
		    (SELECT
		        A.ASN_ID
			        ,COUNT(CASE WHEN A.SMT_CMPL_YN = 'Y' THEN 1 ELSE NULL END) AS completeGrpCnt
			        ,COUNT(CASE WHEN A.SMT_CMPL_YN = 'N' THEN 1 ELSE NULL END) AS inCompleteGrpCnt
			        ,COUNT(A.GRP_ID) AS totalGrpCnt
			    FROM LMS_LRM.EA_GRP_ASN_SMT A
			    GROUP BY A.ASN_ID
		    ) S 
		    ON S.ASN_ID = A.ASN_ID
	   WHERE
			A.ASN_ID = #{asnId}
	</select>

	<!-- 모둠 과제 삭제 --><!-- 미사용 -->
	<update id="deleteGrpAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - deleteGrpAsn */
		UPDATE LMS_LRM.EA_ASN
		SET
			DEL_YN = 'Y'
			, MDFR_ID = #{tcrUsrId}
			, MDF_DTM = NOW()
		WHERE
			ASN_ID = #{asnId}
	</update>

	<!-- 모둠별 현황 조회 -->
	<select id="selectCurrentGrpAsnList" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - selectCurrentGrpAsnList */
		SELECT
			A.GRP_ID 										AS grpId
			, A.GRP_TEM_ID 								AS grpTemId
			, A.ASN_ID 										AS asnId
			, B.GRP_TEM_NM 							AS grpTemNm
			, A.SMT_CMPL_YN
			,CASE
				WHEN A.SMT_CMPL_YN = 'Y'
				THEN '완료'
				WHEN A.SMT_CMPL_YN = 'N'
				THEN '미완료'
			END AS smtCmplYnNm
			, IF(A.SCR IS NOT NULL, 'Y', 'N')			AS scrYn
			, A.SMT_DTM
			, A.MDF_DTM
			, CONCAT(DATE_FORMAT(A.FST_SMT_DTM,'%m. %d. '), IF(TIME_FORMAT(A.FST_SMT_DTM, '%p') = 'AM', '오전 ', '오후 '), DATE_FORMAT(A.FST_SMT_DTM,'%h:%i')) 	AS FST_SMT_DTM
			, CONCAT(DATE_FORMAT(A.SMT_DTM,'%m. %d. '), IF(TIME_FORMAT(A.SMT_DTM, '%p') = 'AM', '오전 ', '오후 '), DATE_FORMAT(A.SMT_DTM,'%h:%i'))					AS SMT_DTM_NM
			,CONCAT(DATE_FORMAT(A.MDF_DTM,'%m. %d. '),IF(TIME_FORMAT(A.MDF_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(A.MDF_DTM,'%h:%i')) AS MDF_DTM_NM
			, A.SMT_CN 									AS smtCn
			, A.ANNX_ID 									AS annxId
			, A.SCR							AS scr
			, IF(A.SCR IS NOT NULL, 'Y', 'N')			AS scrYn
			, C.PSC_SCR 									AS pscScr
			, A.FDBK_CN 									AS fdbkCn
			, IFNULL((SELECT BLBD_ID FROM LMS_LRM.EA_GRP_BLBD F WHERE F.GRP_ID = A.GRP_ID AND F.GRP_TEM_ID = B.GRP_TEM_ID AND A.ASN_ID = #{asnId} AND F.ASN_ID = #{asnId}), 0) AS blbdId
			, C.EV_MTHD_TP_CD AS evMthdTpCd
			, 'GR' AS asnTpCd
		FROM
			LMS_LRM.EA_GRP_ASN_SMT A
		LEFT JOIN
			LMS_LRM.EA_GRP_TEM B
		ON
			A.GRP_ID = B.GRP_ID
		AND
			A.GRP_TEM_ID = B.GRP_TEM_ID
		LEFT JOIN
			LMS_LRM.EA_ASN C
		ON
			A.ASN_ID = C.ASN_ID
		AND 
			C.USE_YN = 'Y'
		WHERE
			A.ASN_ID = #{asnId}
		<if test = 'smtCmplYn != null and !"".equals(smtCmplYn)'>
			AND
				A.SMT_CMPL_YN = #{smtCmplYn}
		</if>
	</select>

	<select id="selectUsrNm" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - selectUsrNm */
		SELECT
			<!-- USR_NM -->
			 GRP_TMGR_YN
			, STU_USR_ID
		FROM
			LMS_LRM.EA_GRP_TMBR
		WHERE
			GRP_ID = #{grpId}
		AND
			GRP_TEM_ID = #{grpTemId}
	</select>

	<!-- 점수, 피드백 처리 for EA_GRP_ANS_SMT Table -->
	<update id="saveGrpScFbGrpAnsTb" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - saveGrpScFbGrpAnsTb */
		UPDATE LMS_LRM.EA_GRP_ASN_SMT
		SET
			 MDFR_ID = #{tcrUsrId}
			<if test = '"SC".equals(evMthdTpCd) and scr != null and !"".equals(scr)'>
	   			,SCR =	#{scr}
	      	</if>
		  	<if test = '"FB".equals(evMthdTpCd) and fdbkCn != null and !"".equals(fdbkCn)'>
	   			,FDBK_CN = #{fdbkCn}
	      	</if>
		WHERE
			ASN_ID = #{asnId}
		AND
			GRP_ID = #{grpId}
		AND
		 	GRP_TEM_ID = #{grpTemId}
	</update>

	<!-- 점수, 피드백 처리 for EA_ASN_SMT Table -->
	<update id="saveGrpScFbAsnSmtTb" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - saveGrpScFbAsnSmtTb */
		UPDATE LMS_LRM.EA_ASN_SMT
		SET
			 MDFR_ID = #{tcrUsrId}
			<if test = '"SC".equals(evMthdTpCd) and scr != null and !"".equals(scr)'>
	   			,SCR =	#{scr}
	      	</if>
		  	<if test = '"FB".equals(evMthdTpCd) and fdbkCn != null and !"".equals(fdbkCn)'>
	   			,FDBK_CN = #{fdbkCn}
	      	</if>
		WHERE
			ASN_ID = #{asnId}
		AND
			STU_USR_ID IN (
				SELECT
					STU_USR_ID
				FROM
					LMS_LRM.EA_GRP_TMBR
				WHERE
					GRP_ID = #{grpId}
				AND
					GRP_TEM_ID = #{grpTemId}
			)
	</update>

	<!-- 대상 설정 (해당 교사의 모둠 그룹 조회) -->
	<select id="selectGrpTarget" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnTcr-Mapper.xml - selectGrpTarget */
		SELECT
			GRP_ID			AS grpId
			, GRP_NM		AS grpNm
			, DEL_YN			AS delYn
			, TEM_CNT		AS temCnt
			, OPT_TXB_ID	AS optTxbId
			, CRTR_ID		AS crtrId
		FROM
			LMS_LRM.EA_GRP
		WHERE
			OPT_TXB_ID = #{optTxbId}
		<!-- AND
			CRTR_ID = #{tcrUsrId} -->
		AND
			DEL_YN = 'N'
		ORDER BY
			GRP_NM ASC
	</select>
	
    
    <!-- 우리반학습 학습 완료 조회 -->
    <select id="selectLrnCompt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /* EaAsnTcr-Mapper.xml - selectLrnCompt */
        SELECT COUNT(DISTINCT TA.LRN_ATV_ID) AS cmplCnt
       --     ,TC.LRN_STP_DV_CD 
		  FROM LMS_LRM.TL_SBC_LRN_ATV_ST TA  /* TL_교과학습활동상태 */
		 INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN TB /* TL_교과학습활동재구성 */
		    ON TA.OPT_TXB_ID = TB.OPT_TXB_ID
		   AND TA.LRMP_NOD_ID = TB.LRMP_NOD_ID
		   AND TA.LRN_ATV_ID = TB.LRN_ATV_ID
		 INNER JOIN LMS_CMS.BC_LRN_STP TC /* BC_학습단계 */
		    ON TB.LRMP_NOD_ID = TC.LRMP_NOD_ID
		   AND TB.LRN_STP_ID = TC.LRN_STP_ID
		   AND TC.DEL_YN = 'N'
		   AND TC.LRN_STP_DV_CD = #{lrnStpDvCd}
		 WHERE TA.LRMP_NOD_ID = #{tcNodId}	/* 차시ID */
		   AND TA.OPT_TXB_ID = #{optTxbId}
		   AND TA.LRN_USR_ID = #{stuUsrId}
		   AND TA.LRN_ST_CD = 'CL'
		 ORDER BY TA.CRT_DTM DESC
    </select>
    
    <!-- 우리반학습 평가 완료 조회 -->
    <select id="selectExCmplYn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="String">
		/* EaAsnTcr-Mapper.xml - selectExCmplYn */
		select IFNULL(er.ev_cmpl_yn,'N') from lms_lrm.ea_ev_rs er where er.EV_ID = #{evId} and er.USR_ID = #{stuUsrId};
    </select>
    
        <!-- 우리반학습 콘텐츠 수 조회 -->
    <select id="selectTtlLrnCnt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="int">
		/* EaAsnTcr-Mapper.xml - selectTtlLrnCnt */
		select COUNT(*) from LMS_LRM.tl_sbc_lrn_atv_rcstn R
		join LMS_CMS.bc_lrn_stp s
			on S.LRN_STP_ID = R.LRN_STP_ID 
			and S.LRMP_NOD_ID = R.LRMP_NOD_ID
		where R.OPT_TXB_ID = #{optTxbId}
		and R.LRMP_NOD_ID= #{tcNodId}
		and S.LRN_STP_DV_CD = #{lrnStpDvCd};
    </select>
    
    <!-- 해야하는 AI 맞춤학습 개수 조회 -->
    <select id="selectAiTotCnt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="int">
        /* EaAsnTcr-Mapper.xml - selectAiTotCnt */
        select count(*) from lms_lrm.AI_USRLY_TPC_LRN_ORDN lo where lo.USR_ID = #{stuUsrId} and lo.OPT_TXB_ID = #{optTxbId} and lo.LU_KMMP_NOD_ID = #{luNodId} and lo.LRN_YN = 'Y';
    </select>
    
    <!-- 완료한 AI 맞춤학습 개수 조회 -->
    <select id="selectAiCmplCnt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="int">
        /* EaAsnTcr-Mapper.xml - selectAiCmplCnt */
        SELECT count(*)
		  FROM LMS_LRM.EA_EV EE
		 INNER JOIN LMS_LRM.EA_EV_RS EER 
		    ON EE.EV_ID = EER.EV_ID
		   AND EER.USR_ID = #{stuUsrId}
		 INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
		 	ON EE.OPT_TXB_ID = EAETR.OPT_TXB_ID 
		    AND EE.EV_ID = EAETR.EV_ID 
		 WHERE EE.OPT_TXB_ID = #{optTxbId}
		   AND EE.EV_DV_CD = 'AE'
		   AND EAETR.MLU_KMMP_NOD_ID = #{luNodId}
		   and ee.EV_DTL_DV_CD = 'C1'
		   and EE.DEL_YN = 'N'
		   and eaetr.LUEV_CMPL_YN = 'Y'
		  order by EAETR.MLU_KMMP_NOD_ID , EAETR.TPC_KMMP_NOD_ID ;
    </select>
    
    <!-- AI 맞춤 학습 학습완료 조회 -->
    <select id="selectAiCompt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /* EaAsnTcr-Mapper.xml - selectAiCompt */
        SELECT  EE.EV_ID
		 	  , EER.EV_CMPL_YN
			  , EAETR.MLU_KMMP_NOD_ID
			  , EAETR.TC_KMMP_NOD_ID
			  , EAETR.LUEV_CMPL_YN
		  FROM LMS_LRM.EA_EV EE
		 INNER JOIN LMS_LRM.EA_EV_RS EER 
		    ON EE.EV_ID = EER.EV_ID 
		   AND EER.USR_ID = #{stuUsrId}
		 INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
		    ON EE.OPT_TXB_ID = EAETR.OPT_TXB_ID 
		    AND EE.EV_ID = EAETR.EV_ID
		 WHERE EE.OPT_TXB_ID = #{optTxbId}
		   AND EE.EV_DV_CD = 'AE'
		   AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
		   AND EAETR.MLU_KMMP_NOD_ID = #{luNodId}
    </select>
    
    <!-- 학습과제 완료 업데이트 -->
	<update id="updateLrnAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/* EaAsnTcr-Mapper.xml - updateLrnAsn */
		UPDATE LMS_LRM.EA_ASN_SMT
		SET
			 SMT_CMPL_YN = 'Y'
	   		, SMT_DTM = NOW()
	   		, CMPL_LRN_CNT = #{cmplLrnCnt}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE
			ASN_ID = #{asnId}
		AND
			STU_USR_ID IN
	    <foreach item="stuUsrId" collection="usrIdList" open="(" separator="," close=")">
	        #{stuUsrId}
	    </foreach>	
	</update>
	
	<!-- 원본 과제 묶음ID 업데이트 -->
	<update id="updatePkgAsnId" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/* EaAsnTcr-Mapper.xml - updatePkgAsnId */
		UPDATE LMS_LRM.EA_ASN
		   SET
				 PKG_ASN_ID  = #{pkgAsnId}
				,MDFR_ID = #{mdfrId}
				,MDF_DTM = NOW()
		 WHERE ASN_ID  = #{asnId}
		   AND ASN_TP_CD ='GE'
	</update>
	
	<!-- 묶음 과제 여부 조회 -->
    <select id="selectPkgAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /* EaAsnTcr-Mapper.xml - selectPkgAsn */
        SELECT EA.ASN_ID  
			  ,EA.PKG_ASN_ID
			  ,EA.OPT_TXB_ID 
			  ,EA.TCR_USR_ID 
			  ,CASE 
			   	WHEN (SELECT COUNT(EAST.STU_USR_ID)  FROM LMS_LRM.EA_ASN_SMT EAST WHERE EAST.ASN_ID = EA.ASN_ID ) = (SELECT SUM(CASE WHEN EAST2.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_ASN_SMT EAST2 WHERE EAST2.ASN_ID = EA.ASN_ID)
			   	THEN 'Y'
			   	ELSE 'N'
			  END AS PKG_CMP_YN
		  FROM EA_ASN EA 
		  LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
		    ON EA.ASN_ID = EAR.ASN_ID
		   AND EAR.DEL_YN ='N'
		  LEFT JOIN LMS_LRM.EA_GRP_ASN_SMT EGAS
		    ON EA.ASN_ID = EGAS.ASN_ID
		 WHERE 1=1
		   AND EA.PKG_ASN_ID = #{pkgAsnId}
<!-- 		   AND EA.TCR_USR_ID = #{tcrUsrId} -->
		   AND EA.ASN_TP_CD ='GE'
		   AND EA.DEL_YN ='N'
		   <if test = 'optTxbId != null and !"".equals(optTxbId)'>
		   		AND EA.OPT_TXB_ID = #{optTxbId}
		   </if>
    </select>
    
    <!-- 과제 제출 완료 여부 확인 -->
    <select id="selectPkgAsnCmp" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /* EaAsnTcr-Mapper.xml - selectPkgAsnCmp */
         SELECT  EA.ASN_ID			-- 과제ID
				 ,EA.OPT_TXB_ID 		-- 운영교과서ID
				 ,EA.TCR_USR_ID 		-- 교사사용자ID
				 ,CASE 
				   	WHEN (SELECT COUNT(EAST.STU_USR_ID)  FROM LMS_LRM.EA_ASN_SMT EAST WHERE EAST.ASN_ID = EA.ASN_ID ) = (SELECT SUM(CASE WHEN EAST2.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) FROM LMS_LRM.EA_ASN_SMT EAST2 WHERE EAST2.ASN_ID = EA.ASN_ID)
				   	THEN 'Y'
				   	ELSE 'N'
				  END AS PKG_CMP_YN
				 ,EA.PKG_ASN_ID
		  FROM LMS_LRM.EA_ASN EA	-- EA_과제
		  LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
		    ON EA.ASN_ID = EAR.ASN_ID
		   AND EAR.DEL_YN ='N'
		 WHERE 1=1
		   AND EA.DEL_YN = 'N'
		   AND EA.OPT_TXB_ID = #{optTxbId}
<!-- 		   AND EA.TCR_USR_ID = #{tcrUsrId} -->
		   AND EA.ASN_ID = #{asnId}
		   AND EA.ASN_TP_CD ='GE'
    </select>
    
    
    <select id="selectAtvIdList" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
	     /* EaAsnTcr-Mapper.xml - selectAtvIdList */
	      SELECT TB.LRMP_NOD_ID AS TC_NOD_ID  
	            ,TB.LRN_ATV_ID AS ATV_CTN_ID
	      	FROM lms_lrm.tl_sbc_lrn_atv_rcstn TB
	       INNER JOIN LMS_CMS.BC_LRN_STP TC /* BC_학습단계 */
		      ON TB.LRMP_NOD_ID = TC.LRMP_NOD_ID
		     AND TB.LRN_STP_ID = TC.LRN_STP_ID
		     AND TC.DEL_YN = 'N'
		     AND TC.LRN_STP_DV_CD = 'WB'
	       WHERE TB.LRMP_NOD_ID = #{tcNodId}
	         AND TB.OPT_TXB_ID = #{optTxbId}
     </select>

	<!-- 신규 학생 기존 과제 반영 -->
	<insert id="insertAsnSmtNewStu" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/* EaAsnTcr-Mapper.xml - insertAsnSmtNewStu */
		INSERT INTO LMS_LRM.EA_ASN_SMT (
			ASN_ID,
			STU_USR_ID,
			SMT_CMPL_YN,
			CMPL_LRN_CNT,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		SELECT
			EA.ASN_ID,
			#{stuUsrId},
			'N',
			0,
			EA.CRTR_ID,
			EA.CRT_DTM,
			EA.MDFR_ID,
			EA.MDF_DTM,
			EA.DB_ID
		FROM LMS_LRM.EA_ASN EA
		WHERE EA.OPT_TXB_ID = #{optTxbId}
		  AND EA.ALL_STXQ_YN = 'Y'
		  AND EA.DEL_YN = 'N'
		  AND EA.USE_YN = 'Y'
		  AND (
				(EA.ASN_PTME_DV_CD = 'PT' AND EA.END_DTM > NOW())
				OR (EA.ASN_PTME_DV_CD = 'OT')
			)
		  AND NOT EXISTS (
			SELECT 1
			FROM LMS_LRM.EA_ASN_SMT SMT
			WHERE SMT.STU_USR_ID = #{stuUsrId}
			  AND SMT.ASN_ID = EA.ASN_ID
		)
	</insert>
	
	<select id="selectOtherAsnCopyAnnxFleYn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAnsCopyFleDto" resultType="int">
        /** EaAsnCom-Mapper.xml - selectOtherAsnCopyAnnxFleYn */
        SELECT
			COUNT(a.asn_id) AS cnt
		FROM
			lms_lrm.ea_asn a
		WHERE
			a.asn_id != #{asnId}
		AND a.annx_id = #{annxId}
		AND a.del_yn = 'N'
    </select>

</mapper>