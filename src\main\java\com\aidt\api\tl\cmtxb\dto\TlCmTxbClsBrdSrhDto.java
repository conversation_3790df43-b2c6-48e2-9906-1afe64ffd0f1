package com.aidt.api.tl.cmtxb.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 09:12:02
 * @modify date 2024-05-24 09:12:02
 * @desc [TlCmTxbClsBrdSrhDto 클래스보드 url 조회 dto ]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbClsBrdSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** (차시)학습맵노드ID */
    @Parameter(name="(차시)학습맵노드ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;

    /** 학습활동ID */
    @Parameter(name="학습활동ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrnAtvId;


    /** 클래스보드대제목ID */
    @Parameter(name="클래스보드대제목ID")
    // TODO v3.1 용 @NotBlank(message = "{field.required}")
    private String clabdLrgsId;

    // /** 클래스보드소제목ID */
    // @Parameter(name="클래스보드소제목ID")
    // // TODO v3.1 용 @NotBlank(message = "{field.required}")
    // private String clabdSmlId;
}
