package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrTalkResDto {
	
	@Parameter(name = "운영교과서ID")
	private String optTxbId;
	
	@Parameter(name = "사용자ID")
	private String usrId;
	
	@Parameter(name = "사용자명")
	private String usrNm;
	
	@Parameter(name = "사용자번호")
	private String usrNo;
	
	@Parameter(name = "대단원Id")
	private String kmmpNodId;
	
	@Parameter(name = "대단원명")
	private String kmmpNodNm;
	
	@Parameter(name = "전체 토픽수")
	private String totTopicsCount;
	
	@Parameter(name = "완료 토픽수")
	private String complTopicsCount;
	
	@Parameter(name = "진척율")
	private String progressRate;
	
	@Parameter(name = "마지막 수정일")
	private String lastUpdated;
	
	@Parameter(name = "학습시간")
	private String lrnTime; 
	
	@Parameter(name = "학습 시작 시간")
	private String firstUpdated;
	
	@Parameter(name = "DL, CL 존재여부")
	private String dlClExist; 
	
	@Parameter(name = "학습 수준")
	private String lrnrVelTpCd;
}
