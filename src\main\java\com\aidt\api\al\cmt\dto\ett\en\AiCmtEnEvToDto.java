package com.aidt.api.al.cmt.dto.ett.en;

import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnEvToDto {

    private N12Dto n12;

}
