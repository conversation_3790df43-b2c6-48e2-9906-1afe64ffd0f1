//package com.aidt.api.al.pl.tcr;
//
//import javax.validation.Valid;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.aidt.api.al.pl.back.AlLrnwService;
//import com.aidt.api.al.pl.dto.AlLrnwReqDto;
//import com.aidt.api.al.pl.dto.AlLrnwResponseDto;
//import com.aidt.base.jwt.CommonUserDetail;
//import com.aidt.base.response.Response;
//import com.aidt.base.response.ResponseDto;
//import com.aidt.base.util.CoreUtil;
//
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * AI맞춤 학습창연계처리 서비스
// */
//
//@Slf4j
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/api/v1/al/pl/tcr/lrnw")
//@Tag(name="[al] AI맞춤 학습창연계처리 서비스", description="AI맞춤 학습창연계처리 서비스(학생)")
//public class AlLrnwTcrController {
//	
//	@Autowired
//	private AlLrnwService alLrnwService;
//	
//	@Tag(name="[al] AI맞춤 목차및 컨텐츠리스트 조회", description="AI맞춤 목차및 컨텐츠리스트 조회")
//    @PostMapping(value = "/selectLrnwInfo")
//    public ResponseDto<AlLrnwResponseDto> selectLrnwInfo(@Valid @RequestBody AlLrnwReqDto dto) {
//        log.debug("Entrance selectLrnTocAtvInfo");
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//        dto.setOptTxbId(userDetails.getOptTxbId());
//        dto.setUsrId(userDetails.getUsrId());
//        return Response.ok(alLrnwService.selectLrnwInfo(dto));
//    }
//	
//	@Tag(name="[al] AI맞춤 학습활동 저장", description="AI맞춤 학습활동 저장")
//    @PostMapping(value = "/updateAiLrnAtvSt")
//    public ResponseDto<Integer> updateAiLrnAtvSt(@Valid @RequestBody AlLrnwReqDto dto) {
//        log.debug("Entrance selectLrnTocAtvInfo");
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//        dto.setOptTxbId(userDetails.getOptTxbId());
//        dto.setUsrId(userDetails.getUsrId());
//        return Response.ok(alLrnwService.updateAiLrnAtvSt(dto));
//    }
//
//}
