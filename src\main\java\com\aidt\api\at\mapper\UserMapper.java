package com.aidt.api.at.mapper;

import com.aidt.api.at.dto.User;
import com.aidt.api.at.user.dto.TokenDto;
import com.aidt.api.at.user.dto.UserCreateDto;
import com.aidt.api.at.user.dto.UsrDto;

public interface UserMapper {

	public User.UserResponseDto selectUserDetail(String usrId);

	public String selectTokenUsrId(UserCreateDto dto);

	public int selectUsrCheckCnt(String usrId);

	public int insertUsr(UsrDto dto);

	public String selectUsrUsrId(UserCreateDto dto);

	public int insertToken(TokenDto dto);

	public int insertTokenMakeUsrId(TokenDto dto);

	public int updateUsrKerisTermAgrYn(UsrDto dto);

	public String selectTokenLoginIdConfirm(String loginId);
}