<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aidt.api.at.mapper.TokenMapper">

	<select id="selectUser" resultType="com.aidt.api.at.token.dto.Jwt$JwtUserDto">
		SELECT	  A.USR_ID
				, A.KERIS_USR_ID
				, CASE B.USR_TP_CD 
					WHEN 'ST' THEN 'stu'
					WHEN 'TE' THEN 'tcr'
					WHEN 'PA' THEN 'prt'
					WHEN 'AD' THEN 'mgr'
					ELSE ''
				END AS "ROLES"
		  FROM LMS_LRM.CM_TOKEN A, LMS_LRM.CM_USR B
		 WHERE A.USR_ID = B.USR_ID
		   AND A.LOGIN_ID = #{loginId}
		   AND A.PWD = #{password}
	</select>

	<select id="selectRefreshToken" resultType="com.aidt.api.at.token.dto.Jwt$JwtUserDto">
		SELECT	  A.USR_ID
				, B.REFRESH_TOKEN
		  FROM LMS_LRM.CM_TOKEN A, LMS_LRM.CM_USR B
		 WHERE A.USR_ID = B.USR_ID
		   AND B.REFRESH_TOKEN = #{refreshToken}
	</select>
	
	<select id="selectUsrRefreshToken" resultType="com.aidt.api.at.token.dto.Jwt$JwtUserDto">
		SELECT	  
			 urt.refresh_token
			,urt.usr_id
	  	FROM 
	  		lms_lrm.cm_usr_refresh_token urt
		 WHERE 
		 	urt.refresh_token = #{refreshToken}
		 LIMIT 1
	</select>

	<update id="updateRefreshToken">
		UPDATE LMS_LRM.CM_USR SET
			REFRESH_TOKEN = #{refreshToken}
         WHERE USR_ID = #{usrId}
	</update>
	
	<insert id="insertUsrRefreshToken">
		INSERT INTO LMS_LRM.CM_USR_REFRESH_TOKEN
		(
			 usr_id
			,refresh_token
			,keris_usr_id
		    ,reg_dtm
		)
		VALUES
		(
			 #{usrId}
			,#{refreshToken}
			,#{kerisUsrId}
		    ,NOW()
		)
	</insert>

	<update id="deleteRefreshToken">
		UPDATE LMS_LRM.CM_USR SET
			REFRESH_TOKEN = ''
         WHERE REFRESH_TOKEN = #{refreshToken}
	</update>
 
</mapper>