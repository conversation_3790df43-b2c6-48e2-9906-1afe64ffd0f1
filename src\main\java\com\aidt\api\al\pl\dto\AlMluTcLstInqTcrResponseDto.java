package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc [AI맞춤학습 단원차시조회 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlMluTcLstInqTcrResponseDto {
//	@Parameter(name="지식맵 중단원 노드명")
//	private String lrmpNodNm;
//	
//	@Parameter(name="지식맵 대단원 노드 ID")
//	private String lluLrmpNodId;
//	
//	@Parameter(name="지식맵 대단원 노드명")
//	private String lluLrmpNodNm;
	
	@Parameter(name="지식맵 중단원 사용여부")
	private String tcUseYn;
	
	@Parameter(name="평가 ID")
	private String evId;
	
	@Parameter(name="평가 상세코드")
	private String evDtlDvCd;
	
	@Parameter(name="평가 상세이름")
	private String evCmplYnNm;
	
	@Parameter(name="평가 완료여부")
	private String evCmplYn;
	
	@Parameter(name="맞힌 정답수")
	private int cansCnt;
	
	@Parameter(name="학습자 수준")
	private String lrnrVelTpCd;
	
	@Parameter(name="학습일자")
	private String mdfDtm;
	
	@Parameter(name="토픽숙련도")
	private Double TpcAvn;
	private String TpcAvnStr;
	
	@Parameter(name="평가구분코드")
	private String evDvCd;
	
	@Parameter(name="지식맵 차시 정보")
	private List<AlPlMluLstInqEnElStuResponseDto> aiKmmpNodList;
	
	@Parameter(name="지식맵중단원노드Id")
	private String mKmmpNodId;
	
	@Parameter(name="운영교과서Id")
	private String optTxbId;
	
	@Parameter(name="학습맵 중단원 노드 ID")	//학습맵
	private String kmmpNodId;
	
	@Parameter(name="지식맵 중단원 노드명")	//학습맵
	private String kmmpNodNm;
	
	@Parameter(name="지식맵 대단원 노드Id")	//학습맵
	private String lluKmmpNodId;
	
	@Parameter(name="지식맵 대단원 노드명")	//학습맵
	private String lluKmmpNodNm;
	
//	@Parameter(name="지식맵 중단원 노드Id")	//학습맵
//	private String mluKmmpNodId;
}
