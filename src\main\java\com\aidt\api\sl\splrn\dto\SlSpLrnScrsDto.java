package com.aidt.api.sl.splrn.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-21 15:41:26
 * @modify : date 2024-02-21 15:41:26
 * @desc : 특별학습 스크린샷 dto
 */


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnScrsDto {
	
	@Parameter(name="특별학습ID")
	private String spLrnId;

	@Parameter(name="파일유형코드")
	private String fleTpCd;
	
	@Parameter(name="파일경로")
	private String flePthNm;
	
	@Parameter(name="대체텍스트")
	private String altnTxtCn;

}
