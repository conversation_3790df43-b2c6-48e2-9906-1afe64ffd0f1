package com.aidt.api.bc.clablbd.stu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.tk.dto.AlTkDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto;
import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-07 13:42:10
 * @modify 2024-06-07 13:42:10
 * @desc 학급게시판 Controller
 */

@Slf4j
@Tag(name="[bc] 학급게시판 [BcClaBlbdTcr]", description="학급게시판(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/clablbd")
public class BcClaBlbdStuController {

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private BcClaBlbdStuService bcClaBlbdStuService;


	/**
	 * 학급게시판 조회
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<List<BcClaBlbdDto>>
	 */
	@Tag(name="[bc] 학급게시판 조회", description="학급게시판 조회")
	@GetMapping(value = "/selectClaBlbdList")
	public ResponseDto<List<BcClaBlbdDto>> selectClaBlbdList(BcClaBlbdDto bcClaBlbdDto) {

		// 2024-06-07 JWT토큰정보에서 운영교과서ID 추출 -> 추후 변경 필요
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		
		log.debug("Entrance selectClaBlbdList");
		return Response.ok(bcClaBlbdStuService.selectClaBlbdList(bcClaBlbdDto));
	}
	
	/**
	 * 학급게시판 상세 조회
	 *
	 * @param claBlbdId
	 * @return ResponseDto<BcClaBlbdDto>
	 */
	@Tag(name="[bc] 학급게시판 상세 조회", description="학급게시판 상세 조회")
	@GetMapping(value = "/getClaBlbdInfo/{claBlbdId}")
	public ResponseDto<BcClaBlbdDto> getClaBlbdInfo(@PathVariable("claBlbdId") Long claBlbdId) {
		
		BcClaBlbdDto bcClaBlbdDto = new BcClaBlbdDto();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		bcClaBlbdDto.setClaBlbdId(claBlbdId);
		
		log.debug("Entrance getClaBlbdInfo");
		return Response.ok(bcClaBlbdStuService.getClaBlbdInfo(bcClaBlbdDto));
	}
	
	
	/**
	 * 학급게시판 등록/수정/삭제
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 학급게시판 등록/수정/삭제", description="학급게시판 등록/수정/삭제(학생)")
	@PostMapping(value = "/saveClaBlbdInfo")
	public Map<String, Object> saveClaBlbdInfo(@Valid @RequestBody BcClaBlbdDto bcClaBlbdDto) {
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setDbId(userDetails.getTxbId());
		bcClaBlbdDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrTpCd(userDetails.getUsrTpCd());
		bcClaBlbdDto.setRqrdYn("N");
		
		log.debug("Entrance saveClaBlbdInfo");
		Map<String, Object> result = bcClaBlbdStuService.saveClaBlbdInfo(bcClaBlbdDto);
		return result;
	}
	
	/**
	 * 학급게시판 댓글 등록/수정/삭제
	 *
	 * @param BcClaBlbdUcwrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 학급게시판 댓글 등록/수정/삭제", description="학급게시판 댓글 등록/수정/삭제(학생)")
	@PostMapping(value = "/saveClaBlbdUcwrInfo")
	public ResponseDto<Integer> saveClaBlbdUcwrInfo(@Valid @RequestBody BcClaBlbdUcwrDto bcClaBlbdUcwrDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdUcwrDto.setDbId(userDetails.getTxbId());
		bcClaBlbdUcwrDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdUcwrDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdUcwrDto.setUsrId(userDetails.getUsrId());
	
		log.debug("Entrance saveClaBlbdUcwrInfo");
		return Response.ok(bcClaBlbdStuService.saveClaBlbdUcwrInfo(bcClaBlbdUcwrDto));
	}
	
    /**
     * 학생 목록 조회
     *
     * @param String
     * @return ResponseList<BcStuListDto>
     */
    @Operation(summary="학생 목록 조회", description="학생 목록을 조회한다.")
    @GetMapping(value = "/selectStuList/{usrId}")
    public ResponseDto<List<BcStuListDto>> selectStuList(@PathVariable("usrId") String usrId) {
    	return Response.ok(bcClaBlbdStuService.selectStuList(usrId));
    }
    
    /**
     * 학급게시판 운영교과서 체크
     * @param BcCbDto
     * @return
     */
	@Operation(summary="학급게시판 운영교과서 체크", description="학급게시판 운영교과서 체크")
	@PostMapping(value = "/selectOptTxbIdChk", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> selectOptTxbIdChk(@Valid @RequestBody BcClaBlbdDto bcClaBlbdDto) {

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		
		log.debug("Entrance selectOptTxbIdChk");
		return bcClaBlbdStuService.selectOptTxbIdChk(bcClaBlbdDto);
	}
	
}
