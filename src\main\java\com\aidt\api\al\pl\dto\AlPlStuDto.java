package com.aidt.api.al.pl.dto;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc AI맞춤학습 단원목차 테스트
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlPlStuDto {
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    @Parameter(name="LCMS교과서_지식맵_키")
    private String lrmpNodId;
    
    @Parameter(name="LCMS교과서_지식맵_부모_노드_키")
    private String urnkLrmpNodId;
    
    @Parameter(name="지식맵노드명")
    private String lrmpNodNm;
    
    @Parameter(name="상위노드명")
    private String upLuNm;
    
    @Parameter(name="사용여부")
    private String useYn;
    
    @Parameter(name="잠금여부")
    private String lcknYn;
    
    @Parameter(name="깊이")
    private String dpth;
    
    @Parameter(name="생성일시Timestamp")
    private Timestamp crtrId;
    
    @Parameter(name="생성자ID")
    private String crtDtm;
    
    @Parameter(name="재구성순서")
    private int rcstnOrdn;
    
    @Parameter(name="수정자ID")
    private String mdfrId;
    
    @Parameter(name="수정일시Timestamp")
    private Timestamp mdfDtm;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="교과서ID")
    private String txbId;

    @Parameter(name=" 로우갯수")
    private int rowCnt;
}
