package com.aidt.api.bc.guidMnl.mapper;

import com.aidt.api.bc.guidMnl.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
public interface ManualMapper {

    GuideManual selectGuideManualByTextbookId(@Param("textbookId") Long textbookId, @Param("role") String role);

    GuideManual selectGuideManualByNodeId(Long nodeId);

    GuideManual selectGuideManualByDbId(Long guidMnlId);

    GuideManual selectGuideManualById(Long id);

    String selectTextbookCode(Long textbookId);

    List<GuideManualNode> selectGuideManualNodeHierarchyByManualId(Long manualId);

    List<GuideManualNode> selectGuideManualNode3DepthAndUploadByManualId(Long manualId);

    List<Long> selectAllNodeIdsByManualId(Long manualId);

    List<Long> selectNodeUploadByManualId(Long manualId);

    GuideManualUpload selectGuideManualUploadByNodeId(Long nodeId);

    List<Map<String, Object>> selectGuideManuals(String role);

    GuideManualNode selectGuideManualNodeByNodeId(Long nodeId);

    List<GuideManualNode> selectSiblingNodes(GuideManualNode node);

    Integer selectGuidManualPdfFileByVerInfo(Long guidMnlId, Long txbId);

    int insertGuideManual(GuideManual manual);

    int insertGuideManualNode(GuideManualNode node);

    int multiInsertGuideManualNodes(List<GuideManualNode> nodes);

    int insertGuideManualContentUpload(GuideManualUpload upload);

    int insertGuidManualPdfFile(GuidManualPdfFile pdfUpload);

    int updateVersionGuidManualPdfFile(Long guidMnlId, Long txbId);

    int updateGuideManualNode(GuideManualNode node);

    int deleteGuideManualNodeUploads(GuideManualUpdate guideManualUpdateDto);

    int deleteGuideManual(GuideManualUpdate guideManualUpdateDto);

    int deleteGuideManualNodes(GuideManualUpdate guideManualUpdateDto);

    int forceDeleteGuideManual(Long manualId);

    int forceDeleteGuideManualNodes(List<Long> ids);

    int forceDeleteGuideManualContentUploads(List<Long> ids);

    List<GuidManualPdfFile> selectGuideManualPdfFiles(GuidManualPdfFile manualPdfFile);

    int updatePdfFileUseYn(GuidManualPdfFile manualPdfFile);

    GuidManualPdfFileDownload selectGuideManualFileData(GuidManualPdfFile manualPdfFile);

    GuideManual selectGuideManual1depthByTextbookId(Map<String, Object> param);

    int selectFileNode(GuideManualFileUploadReqDto guideManualFileUploadReqDto);

    String selectDbIdByTextbookId(Long textbookId);

    void updateFileNodeDelYn(GuideManualFileUploadReqDto guideManualFileUploadReqDto);

    void updateNodeDocViId(GuideManualFileUploadReqDto guideManualFileUploadReqDto);

    void updateFileNode(GuideManualUpload guideManualUpload);
}
