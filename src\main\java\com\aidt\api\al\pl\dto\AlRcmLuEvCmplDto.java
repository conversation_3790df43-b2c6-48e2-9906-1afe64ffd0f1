package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI 맞춤 문항추천 서비스
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlRcmLuEvCmplDto {
	
	@Parameter(name="평가ID", required = true)
	private Integer evId;
	
	@Parameter(name="유저ID", required = true)
    private String usrId;
	
	@Parameter(name="과목코드", required = true)
    private String sbjCd;
	
	@Parameter(name="학교급코드", required = true)
    private String schlGrdCd;
	
	@Parameter(name="토픽완료여부", required = true)
    private String tpcCmplYn;
	
	@Parameter(name="운영교과서")
    private String optTxbId;
	
	@Parameter(name="중단원지식맵노드ID")
	private String mluKmmpNodId;

	
}
