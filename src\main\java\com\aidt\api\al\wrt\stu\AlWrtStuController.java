package com.aidt.api.al.wrt.stu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.wrt.dto.AIWrtReqLogDto;
import com.aidt.api.al.wrt.dto.AlWrtAcpReqDto;
import com.aidt.api.al.wrt.dto.AlWrtReqDto;
import com.aidt.api.al.wrt.dto.AlWrtResDto;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-01 11:28:00
 * @modify 2024-06-01 11:28:00
 * @desc AI첨삭 학생 컨트롤러
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/wrt/stu")
@Tag(name="[al] AI첨삭-학생", description="AI첨삭-학생")
public class AlWrtStuController {
	
	@Autowired
    private AlWrtStuService alWrtStuService;
	
	@Autowired
	private BcCmService bcCmService;

	@Autowired
	private JwtProvider jwtProvider;
	
	/**
     * AI 첨삭 : 토픽 목록
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID
     * @return ResponseDto<List<AlWrtResDto>> - 해당 사용자의 첨삭토픽목록
     */
    @Operation(summary="AI 첨삭 : 토픽 목록")
    @PostMapping(value = "/selectWrtTpcList")
    public ResponseDto<List<AlWrtResDto>> selectWrtTpcList(@Valid @RequestBody AlWrtReqDto dto) {
    	
    	if (dto == null || StringUtils.isBlank(dto.getUsrId()) || StringUtils.isBlank(dto.getOptTxbId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(alWrtStuService.selectAlWrtTpcList(dto));
    }
    
    /**
     * AI 첨삭 : 최근학습정보
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID
     * @return ResponseDto<AlWrtResDto> - 해당 사용자의 최근학습정보
     */
    @Operation(summary="AI 첨삭 : 최근학습정보")
    @PostMapping(value = "/selectWrtRcLrnInfo")
    public ResponseDto<AlWrtResDto> selectWrtRcLrnInfo(@Valid @RequestBody AlWrtReqDto dto) {
    	
    	if (dto == null || StringUtils.isBlank(dto.getUsrId()) || StringUtils.isBlank(dto.getOptTxbId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(alWrtStuService.selectWrtRcLrnInfo(dto));
    }
    
    /**
     * AI 첨삭관리 : 생성(학생 학습시작)
     * @paramAlWrtReqDto - AI첨삭 학습 정보
     * @return ResponseDto<Integer> - insert cnt
     */
    @Operation(summary="AI 첨삭관리 : 생성(학생 학습시작)")
    @PostMapping(value = "/insertWrtMg")
    public ResponseDto<Integer> insertWrtMg(@Valid @RequestBody AlWrtReqDto dto) {
    	
    	if (dto == null 
    			|| StringUtils.isBlank(dto.getUsrId()) 
    			|| StringUtils.isBlank(dto.getOptTxbId()) 
    			|| StringUtils.isBlank(dto.getLluKmmpNodId()) 
    			|| StringUtils.isBlank(dto.getTpcKmmpNodId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(alWrtStuService.insertWrtMg(dto));
    }
    
    /**
     * AI 첨삭 : 상세
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID, 대단원노드ID, 토픽노드ID 
     * @return ResponseDto<AlWrtResDto> - 해당 단원, 해당 토픽의 상세
     */
    @Operation(summary = "AI 첨삭 : 최근학습정보")
    @PostMapping(value = "/selectWrtDtl")
    public ResponseDto<AlWrtResDto> selectWrtDtl(@Valid @RequestBody AlWrtReqDto dto) {
    	
    	if (dto == null
    			|| StringUtils.isBlank(dto.getStuId()) 
    			|| StringUtils.isBlank(dto.getOptTxbId())
    			|| StringUtils.isBlank(dto.getLluKmmpNodId())
    			|| StringUtils.isBlank(dto.getTpcKmmpNodId())
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(alWrtStuService.selectWrtDtl(dto));
    }
    
    /**
     * AI 첨삭관리 : 수정 (임시저장, 제출)
     * @paramAlWrtReqDto - AI첨삭 학습 정보
     * @return ResponseDto<Integer> - update cnt
     */
    @Operation(summary = "AI 첨삭관리 : 수정 (임시저장, 제출)")
    @PostMapping(value = "/updateWrtMg")
    public ResponseDto<Integer> updateWrtMg(@Valid @RequestBody AlWrtReqDto dto, HttpServletRequest request) {
    	
    	if (dto == null 
    			|| StringUtils.isBlank(dto.getUsrId()) 
    			|| StringUtils.isBlank(dto.getOptTxbId()) 
    			|| StringUtils.isBlank(dto.getLluKmmpNodId())
    			|| StringUtils.isBlank(dto.getTpcKmmpNodId())
    			|| StringUtils.isBlank(dto.getStuCansCn())
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
    	if (!StringUtils.equals(dto.getPgrsStCd(), "LN") && !StringUtils.equals(dto.getPgrsStCd(), "AP")) {
    		return Response.fail("Invalid state value.");
    	}
    	
    	// 공통 학습시간 추가
    	Long evLrnTmScnt = dto.getLrnTmScnt();
    	if (evLrnTmScnt > 0) {
    		BcCmLrnTmDto bcCmLrnTmDto = new BcCmLrnTmDto();
            bcCmLrnTmDto.setAiWrtngLrnTm(evLrnTmScnt);
			String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
    		bcCmService.updateCmLrnTm(bcCmLrnTmDto, accessToken);
    	}
        
     
    	
        return Response.ok(alWrtStuService.updateWrtMg(dto));
    }
    
    /**
     * AI 첨삭관리 : 수정 (AI 첨삭) - AI 첨삭을 프론트에서 했을때 사용하였으나 백엔드로 옮겨온 뒤로 사용X
     * @paramAlWrtReqDto - AI첨삭 학습 정보
     * @return ResponseDto<Integer> - update cnt
     */
    @Operation(summary = "AI 첨삭관리 : 수정 (AI 첨삭)")
    @PostMapping(value = "/updateWrtMgAiEdit")
    public ResponseDto<Integer> updateWrtMgAiEdit(@Valid @RequestBody AlWrtReqDto dto) {
    	
    	if (dto == null
    			|| StringUtils.isBlank(dto.getUsrId())
    			|| StringUtils.isBlank(dto.getStuId()) 
    			|| StringUtils.isBlank(dto.getOptTxbId()) 
    			|| StringUtils.isBlank(dto.getLluKmmpNodId())
    			|| StringUtils.isBlank(dto.getTpcKmmpNodId())
    			|| StringUtils.isBlank(dto.getAiAnnxCn())
    			|| StringUtils.isBlank(dto.getAiAnnxDtl())
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(alWrtStuService.updateWrtMgAiEdit(dto));
    }
    
    /**
     * 아키핀 api 호출
     * @param AlWrtReqDto
     * @return ResponseDto<AlWrtResDto>
     */
    @Operation(summary = "아키핀 api 호출 (실시간)")
    @PostMapping(value = "/callAiWrtAcp")
    public ResponseDto<Map<String, Object>> callAiWrtAcp(@Valid @RequestBody AlWrtReqDto dto) {
    	
    	if (dto == null || 
    			(StringUtils.isBlank(dto.getSubUrl()) 
    			&& StringUtils.isBlank(dto.getOptTxbId()))
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
    	AlWrtAcpReqDto acpDto = new AlWrtAcpReqDto();
    	acpDto.setTopic_id(dto.getTopicId());
    	acpDto.setUser_text(dto.getUserText());
    	acpDto.setError_details("true");
    	
        return Response.ok(alWrtStuService.callAcpApi(dto.getSubUrl(), acpDto));
    }
    
    /**
     * AI 첨삭관리 : 확인완료 및 다시쓰기 저장
     * @paramAlWrtReqDto - AI첨삭 학습 정보
     * @return ResponseDto<Integer> - update cnt
     */
    @Operation(summary = "AI 첨삭관리 : 확인완료 및 다시쓰기 저장")
    @PostMapping(value = "/updateWrtMgConfComp")
    public ResponseDto<Integer> updateWrtMgConfComp(@Valid @RequestBody AlWrtReqDto dto, HttpServletRequest request) {
    	
    	if (dto == null  
    			|| StringUtils.isBlank(dto.getUsrId()) 
    			|| StringUtils.isBlank(dto.getOptTxbId()) 
    			|| StringUtils.isBlank(dto.getLluKmmpNodId())
    			|| StringUtils.isBlank(dto.getTpcKmmpNodId())
    			) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(alWrtStuService.updateWrtMgConfComp(dto, request));
    }
    
    /**
     * 아키핀 결과 저장
     * 실시간으로 요청과 결과를 주고 받는 구조 => 2024.11.21 요청과 결과를 따로 받는 구조로 변경(requestAiWrtAcp)
     * @param AlWrtReqDto
     * @return ResponseDto<AlWrtResDto>
     */
    @Operation(summary = "아키핀 결과 저장")
    @PostMapping(value = "/updateAcpWrtRs")
    public ResponseDto<Map<String, Object>> updateAcpWrtRs(@Valid @RequestBody AlWrtReqDto dto, HttpServletRequest request) {
    	
    	if (dto == null || 
    			(  StringUtils.isBlank(dto.getLluKmmpNodId())
    			&& StringUtils.isBlank(dto.getTpcKmmpNodId()))
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	dto.setStuId(userDetails.getUsrId());
    	dto.setUsrId(userDetails.getUsrId());
    	dto.setOptTxbId(userDetails.getOptTxbId());
    	
    	alWrtStuService.updateAcpWrtRs(dto);
    	
        return Response.ok(new HashMap<String, Object>());
    }
    
    /**
     * 아키핀 AI 첨삭 요청
     * @param AlWrtReqDto
     * @return ResponseDto<AlWrtResDto>
     */
    @Operation(summary = "아키핀 AI 첨삭 요청")
    @PostMapping(value = "/requestAiWrtAcp")
    public ResponseDto<Map<String, Object>> requestAiWrtAcp(@Valid @RequestBody AlWrtReqDto dto) {
    	/*
    	if (dto == null || 
    			(StringUtils.isBlank(dto.getOptTxbId())
    			&& StringUtils.isBlank(dto.getStuId())
    			&& StringUtils.isBlank(dto.getLluKmmpNodId())
    			&& StringUtils.isBlank(dto.getTpcKmmpNodId())
    			)
    	) {
    		return Response.fail("Params does not exist");
    	}
        return Response.ok(alWrtStuService.requestAiWrtAcp(dto));
        */
    	
    	/**
    	 * 2025.02.24 아키핀 요청 로그 기능 추가
    	 */
    	boolean result = false;
    	Map<String, Object> rsMap = null;
    	AIWrtReqLogDto logDto = new AIWrtReqLogDto();
    	StringBuffer sb = null;
    	
    	if(dto == null) {
    		sb = new StringBuffer();
    		sb.append("### AlWrtReqDto dto >>> dto.getOptTxbId() is null or empty.");
    		
    		logDto.setRsYn("N");
			logDto.setRsMsg(sb.toString());
    	} else {
    		if(StringUtils.isBlank(dto.getOptTxbId())) {
    			sb = new StringBuffer();
    			sb.append("### AlWrtReqDto dto >>> dto.getOptTxbId() is null or empty.");
    		} else {
    			logDto.setOptTxbId(dto.getOptTxbId());
    		}
    		
    		if(StringUtils.isBlank(dto.getLluKmmpNodId())) {
    			if(sb == null) {
    				sb = new StringBuffer();
    			} else {
    				sb.append(" / ");
    			}
    			
    			sb.append("### AlWrtReqDto dto >>> dto.getLluKmmpNodId() is null or empty.");
    		} else {
    			logDto.setLluKmmpNodId(dto.getLluKmmpNodId());
    		}
    		
    		if(StringUtils.isBlank(dto.getTpcKmmpNodId())) {
    			if(sb == null) {
    				sb = new StringBuffer();
    			} else {
    				sb.append(" / ");
    			}
    			
    			sb.append("### AlWrtReqDto dto >>> dto.getTpcKmmpNodId() is null or empty.");
    		} else {
    			logDto.setTpcKmmpNodId(dto.getTpcKmmpNodId());
    		}
    		
    		if(StringUtils.isBlank(dto.getStuId())) {
    			if(sb == null) {
    				sb = new StringBuffer();
    			} else {
    				sb.append(" / ");
    			}
    			
    			sb.append("### AlWrtReqDto dto >>> dto.getStuId() is null or empty.");
    		} else {
    			logDto.setStuId(dto.getStuId());
    		}
    		
    		if(StringUtils.isBlank(dto.getSystemCode())) {
    			if(sb == null) {
    				sb = new StringBuffer();
    			} else {
    				sb.append(" / ");
    			}
    			
    			sb.append("### AlWrtReqDto dto >>> dto.getSystemCode() is null or empty.");
    		} else {
    			logDto.setSystemCd(dto.getSystemCode());
    		}
    		
    		logDto.setReqDvCd("ST");
    		
    		if(sb == null) {
    			rsMap = alWrtStuService.requestAiWrtAcp(dto, logDto);
    			
    			if(rsMap != null && "Y".equals(logDto.getRsYn())) {
    				result = true;
    			} else {
    				sb = new StringBuffer();
    				sb.append(logDto.getRsMsg());
    			}
    		} else {
    			logDto.setRsYn("N");
    			logDto.setRsMsg(sb.toString());
    		}
    	}
    	
    	try {
    		alWrtStuService.insertAiwriteReqLog(logDto);
    	} catch(Exception e) {
    		log.error(e.getMessage());
    	}
    	
    	if(result) {
    		return Response.ok(rsMap);
    	} else {
    		return Response.fail(sb != null ? sb.toString() : "fail...");
    	}
    }
}
