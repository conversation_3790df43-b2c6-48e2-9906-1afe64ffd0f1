package com.aidt.api.bc.chatbot.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-31 14:41:54
 * @modify date 2024-05-31 14:41:54
 * @desc [BcChatBotSrhDto 챗봇 키워드 검색 DTO]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCbSnroDto {
	
	@Parameter(name="시나리오ID")
	private int snroId;
	
    @Parameter(name="유저ID")
	private String usrId;
    
	@Parameter(name="교과서ID")
	private String txbId;
	
	@Parameter(name="단어")
	private String wdNm;
	
	@Parameter(name="단어 뜻")
	private String wdMeanCn;

    /** 시나리오 검색어 리스트 */
    @Parameter(name="시나리오 검색어 리스트")
    private List<String> srhWdList;
    
    private String srhWd;
    
    @Parameter(name="DB ID")
	private String dbId;
}
