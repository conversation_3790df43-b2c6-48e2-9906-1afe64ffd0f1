package com.aidt.api.ea.clamg.tcr;


import java.util.List;

import com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 전체 학급 관리", description="전체 학급 관리")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/clamg")
public class AllClaMgTcrController {

	@Autowired
    AllClaMgTcrService allClaMgTcrService;

	/**
	 * 운영 교과서에 존재 하는 단원 조회
	 *
	 * @param param
	 * @return ResponseDto<List<AllClaMgTcrResDto>>
	 */
	@Tag(name="[ea] 단원 조회", description="단원 조회")
	@PostMapping(value = "/selectOptTxbUnit")
	public ResponseDto<List<AllClaMgTcrResDto>> selectOptTxbUnit(@RequestBody AllClaMgTcrReqDto param) {
		log.debug("Entrance selectOptTxbUnit");
		return Response.ok(allClaMgTcrService.selectOptTxbUnit(param));
	}

	/**
	 * 해당 단원을 사용 하는 학급에 따른
	 * 진도율(교과 학습/AI 맞춤 학습), 성취율(단원 진단/AI 맞춤 진단/단원 평가), 학습자 수준 분포(느린/보통/빠른)
	 * 통계 조회
	 *
	 * @param param
	 * @return ResponseDto<List<AllClaMgTcrResDto>>
	 */
	@Tag(name="[ea] 통계 조회", description="통계 조회")
	@PostMapping(value = "/selectAllStats")
	public ResponseDto<List<AllClaMgTcrResDto>> selectAllStats(@RequestBody AllClaMgTcrReqDto param) {
		log.debug("Entrance selectAllStats");
		return Response.ok(allClaMgTcrService.selectAllStats(param));
	}

}
