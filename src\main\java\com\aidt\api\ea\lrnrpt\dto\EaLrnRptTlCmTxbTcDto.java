package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlCmTxbTcDto {

	/** 중단원ID*/
    @Parameter(name="중단원ID")
    private String lrmpNodId2;
    
    /** 중단원명*/
    @Parameter(name="중단원명")
    private String lrmpNodNm2;
    
    /** 소단원ID*/
    @Parameter(name="소단원ID")
    private String lrmpNodId3;
    
    /** 소단원명*/
    @Parameter(name="소단원명")
    private String lrmpNodNm3;
    
    /** 차시ID*/
    @Parameter(name="차시ID")
    private String lrmpNodId4;
    
    /** 차시명*/
    @Parameter(name="차시명")
    private String lrmpNodNm4;

    /** 재구성 순서 */
    @Parameter(name="재구성 순서")
    private int rcstnOrdn;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;
    
    @Parameter(name = "단원번호사용여부")
    private String luNoUseYn;
}
