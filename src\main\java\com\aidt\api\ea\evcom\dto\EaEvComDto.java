package com.aidt.api.ea.evcom.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 13:58:오후 1:58
 * @modify date 2024-02-15 13:58:오후 1:58
 * @desc   평가 공통 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvComDto {

    @Parameter(name="단원 학습맵 노드 ID")
    private String luLrmpNodId;

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;    

	@Parameter(name="교과서ID")
	private String txbId;	
	
    @Parameter(name="상위 공통 코드")
    private String urnkCmCd;


}
