package com.aidt.api.ea.asn.tcr;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.asn.tcr.dto.EaAsnFleDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

//@Slf4j
@Tag(name="[ea] 과제 - 교사", description="과제 - 교사")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/asn")
public class EaAsnTcrController {

	@Autowired
	private EaAsnTcrService eaAsnTcrService;

	@Autowired
	private JwtProvider jwtProvider;
	
	/**
     * 과제 목록 조회 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<List<eaAsnTcrDto>>
     */
    @Operation(summary="과제 목록 조회 (교사)", description="과제 목록 조회 (교사)")
    @PostMapping(value="/selectAsnTcrList")
	public ResponseDto<List<EaAsnTcrDto>> selectAsnTcrList(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	List<EaAsnTcrDto> eaAsnStuList = eaAsnTcrService.selectAsnTcrList(eaAsnTcrDto);
		return Response.ok(eaAsnStuList);
	}

    /**
     * 과제 상세 조회 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="과제 상세 조회 (교사)", description="과제 상세 조회 (교사)")
    @PostMapping(value="/selectAsnTcrDetail")
    public ResponseDto<Map<String, Object>> selectAsnTcrDetail(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	Map<String, Object> eaAsnTcrDt = eaAsnTcrService.selectAsnTcrDetail(eaAsnTcrDto);

    	return Response.ok(eaAsnTcrDt);
    }


    /**
     * 과제 상세 조회 - 학생별 현황 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="과제 상세 조회 - 학생별 현황 (교사)", description="과제 상세 조회 - 학생별 현황 (교사)")
    @PostMapping(value="/selectAsnTcrDetailStuList")
    public ResponseDto<Map<String, Object>> selectAsnTcrDetailStuList(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	Map<String, Object> eaStuDt = eaAsnTcrService.selectAsnTcrDetailStuList(eaAsnTcrDto);

    	return Response.ok(eaStuDt);
    }


    /**
     * 학생별 과제 상세 조회 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="학생별 과제 상세 조회 (교사)", description="과제 상세 조회 (교사)")
    @PostMapping(value="/selectStuAsnDetail")
    public ResponseDto<Map<String, Object>> selectStuAsnDetail(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	Map<String, Object> eaAsnTcrDt = eaAsnTcrService.selectStuAsnDetail(eaAsnTcrDto);

    	return Response.ok(eaAsnTcrDt);
    }

    /**
     * 과제 중복 학생 조회 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="과제 중복 학생 조회 (교사)", description="과제 중복 학생 조회 (교사)")
    @PostMapping(value="/selectDplctAsnStuList")
    public ResponseDto<Map<String, Object>> selectDplctAsnStuList(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	Map<String, Object> dplctAsnStu = eaAsnTcrService.dplctAsnStuList(eaAsnTcrDto);

    	return Response.ok(dplctAsnStu);
    }
    
    /**
     *  우리 반 수업 액티비티 조회 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary=" 우리 반 수업 액티비티 조회 (교사)", description=" 우리 반 수업 액티비티 조회 (교사)")
    @PostMapping(value="/selectAtvIdList")
    public ResponseDto<Map<String, Object>> selectAtvIdList(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	Map<String, Object> atvId = eaAsnTcrService.selectAtvIdList(eaAsnTcrDto);

    	return Response.ok(atvId);
    }


    /**
	 *  과제 출제 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 과제 출제 (교사)", description="과제 출제 (교사)")
	@PostMapping(value = "/insertAsnTcr")
	public ResponseDto<Map<String, Object>> insertAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
		return Response.ok(eaAsnTcrService.insertAsnTcr(eaAsnTcrDto));
	}

	/**
	 *  과제 수정 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 과제 수정 (교사)", description="과제 수정 (교사)")
	@PostMapping(value = "/updateAsnTcr")
	public ResponseDto<Integer> updateAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.updateAsnTcr(eaAsnTcrDto));
	}

	/**
	 *  과제 삭제 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 과제 삭제 (교사)", description="과제 삭제 (교사)")
	@PostMapping(value = "/deleteAsnTcr")
	public ResponseDto<Integer> deleteAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.deleteAsnTcr(eaAsnTcrDto));
	}


	/**
	 *  교사 평가 등록 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 교사 평가 등록 (교사)", description="교사 평가 등록 (교사)")
	@PostMapping(value = "/updateAsnEv")
	public ResponseDto<Integer> updateAsnEv(@RequestBody List<EaAsnTcrDto> evList) {

		return Response.ok(eaAsnTcrService.updateAsnEv(evList));
	}



    /**
	 *  모둠 과제 출제 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
//	@Tag(name="[ea] 모둠 과제 출제 (교사)", description="모둠 과제 출제 (교사)")
//	@PostMapping(value = "/inserGrptAsnTcr")
//	public ResponseDto<Map<String, Object>> insertGrpAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
//
//		return Response.ok(eaAsnTcrService.insertGrpAsnTcr(eaAsnTcrDto));
//	}

    /**
	 *  모둠 과제 출제  - 대상 설정 바뀔 때마다 모둠 그룹 재정의
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
//	@Tag(name="[ea] 모둠 과제 출제  - 대상 설정 바뀔 때마다 모둠 그룹 재정의", description="모둠 과제 출제  - 대상 설정 바뀔 때마다 모둠 그룹 재정의")
//	@PostMapping(value = "/selectGrpInfo")
//	public ResponseDto<Map<String, Object>> selectGrpInfo(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
//
//		return Response.ok(eaAsnTcrService.selectGrpInfo(eaAsnTcrDto));
//	}

    /**
	 *  모둠 과제 수정 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 모둠 과제 수정 (교사)", description="모둠 과제 수정 (교사)")
	@PostMapping(value = "/updateGrpAsnTcr")
	public ResponseDto<Map<String, Object>> updateGrpAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.updateGrpAsnTcr(eaAsnTcrDto));
	}

    /**
	 *  모둠 과제 삭제 - 교사
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 모둠 과제 수정 (교사)", description="모둠 과제 수정 (교사)")
	@PostMapping(value = "/deleteGrpAsnTcr")
	public ResponseDto<Map<String, Object>> deleteGrpAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.deleteGrpAsnTcr(eaAsnTcrDto));
	}

	/**
	 * 모둠 과제 상세 조회
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 모둠 과제 상세 조회 (교사)", description="모둠 과제 상세 조회 (교사)")
	@PostMapping(value = "/selectGrpAsnTcr")
	public ResponseDto<Map<String, Object>> selectGrpAsnTcr(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.selectGrpAsnTcr(eaAsnTcrDto));
	}

	/**
	 * 모둠 과제 삭제
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 모둠 과제 수정, 삭제 (교사)", description="모둠 과제 수정, 삭제 (교사)")
	@PostMapping(value = "/modifyGrpAsn")
	public ResponseDto<Map<String, Object>> modifyGrpAsn(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
    	eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());

		return Response.ok(eaAsnTcrService.modifyGrpAsn(eaAsnTcrDto));
	}

	/**
	 * 모둠 과제 점수, 피드백 입력
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Map<String, Object>>
	 */
	@Tag(name="[ea] 모둠 과제 점수, 피드백 입력 (교사)", description="모둠 과제 점수, 피드백 입력 (교사)")
	@PostMapping(value = "/saveGrpScFb")
	public ResponseDto<Map<String, Object>> saveGrpScFb(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
    	eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());

		return Response.ok(eaAsnTcrService.saveGrpScFb(eaAsnTcrDto));
	}

	/**
	 * 모둠 과제 출제 - 대상 설정 조회
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Map<String, Object>>
	 */
	@Tag(name="[ea] 모둠 과제 출제 - 대상 설정 조회 (교사)", description="모둠 과제 출제 - 대상 설정 조회 (교사)")
	@PostMapping(value = "/selectGrpTarget")
	public ResponseDto<Map<String, Object>> selectGrpTarget(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
    	eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());

		return Response.ok(eaAsnTcrService.selectGrpTarget(eaAsnTcrDto));
	}

    /**
     * 첨부 파일 조회
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="첨부 파일 조회", description="첨부 파일 조회")
    @PostMapping(value="/selectFile")
    public ResponseDto<Map<String, Object>> selectFile(@RequestBody EaAsnTcrDto eaAsnTcrDto) {
    	// 첨부파일 조회
    	Map<String, Object> result = eaAsnTcrService.selectFile(eaAsnTcrDto);

    	return Response.ok(result);
    }
    
   
    
    /**
	 *  모둠원 조회
	 *
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Map<String, Object>>
	 */
	@Tag(name="모둠원 조회", description="모둠원 조회")
	@PostMapping(value = "/selectGrpInfo")
	public ResponseDto<Map<String, Object>> selectGrpInfo(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.selectGrpInfo(eaAsnTcrDto));
	}
	
	/**
     * 과제 일괄 적용 조회 - 교사
     * @param eaAsnTcrDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="과제 일괄 적용 여부 조회 (교사)", description="과제 일괄 적용 여부 조회 (교사)")
    @PostMapping(value="/selectPkgAsn")
    public ResponseDto<Map<String, Object>> selectPkgAsn(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

    	Map<String, Object> dplctAsnStu = eaAsnTcrService.selectPkgAsn(eaAsnTcrDto);

    	return Response.ok(dplctAsnStu);
    }
	
	/**
	 * 과제 일괄 적용(등록,수정)
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Map<String, Object>>
	 */
	@Tag(name="[ea] 과제 일괄 적용(등록,수정) (교사)", description="과제 일괄 적용(등록,수정) (교사)")
	@PostMapping(value = "/updatePkgAsn")
	public ResponseDto<Map<String, Object>> updatePkgAsn(@RequestBody EaAsnTcrDto eaAsnTcrDto) {

		return Response.ok(eaAsnTcrService.updatePkgAsn(eaAsnTcrDto));
	}
}