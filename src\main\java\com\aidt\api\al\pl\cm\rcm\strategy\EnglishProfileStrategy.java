package com.aidt.api.al.pl.cm.rcm.strategy;

import java.util.EnumSet;

import org.springframework.stereotype.Component;

import com.aidt.api.al.pl.cm.rcm.adapter.AiQuestionProfileAdapter;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvLoader;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.api.common.enums.SubjectCode;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class EnglishProfileStrategy implements ProfileStrategy {

	private final AiQuestionProfileAdapter aiQuestionProfileAdapter;

	@Override
	public boolean isSupported(SubjectCode subjectCode) {
		return EnumSet.range(SubjectCode.EN, SubjectCode.CE2).contains(subjectCode);
	}

	@Override
	public void completeAiEvaluationTestRange(EaAiEvLoader eaAiEvLoader) {

		var eaAiEv = eaAiEvLoader.getEaAiEvs().stream()
			.findFirst().orElseThrow(() -> new IllegalArgumentException("AI 평가 정보를 찾을 수 없습니다."));

		eaAiEv.learningUnitEvaluationComplete();
		aiQuestionProfileAdapter.updateEaAiEvRange(eaAiEv);
	}

	@Override
	public void updateAiLearningLevel(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public void updateTopicProficiency(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public void updateTopicLearningOrder(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public void saveLearningProgressProfile(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public String getPointCode(EaAiEvLoader eaAiEvLoader) {

		var eaAiEv = eaAiEvLoader.getEaAiEv();

		var evaluationDetailCode = eaAiEv.getEvaluationDetailCode();

		if (EvaluationDetailCode.OV.equals(evaluationDetailCode)) {
			return "AI_CE_01";
		}

		String nodeName = eaAiEv.getTcKmmpNodNm();
		String gradeCd = eaAiEv.getTextbook().getSchlGrdCd();

		return String.format("AI_%sE_%s", gradeCd, nodeName2PointCode(nodeName));
	}

	private String nodeName2PointCode(String nodeName) {
		switch (nodeName.toUpperCase()) {
			case "듣기":
			case "LISTENING":
				return "01";
			case "말하기":
			case "SPEAKING":
				return "02";
			case "읽기":
			case "READING":
				return "03";
			case "쓰기":
			case "WRITING":
				return "04";
			case "파닉스":
			case "GRAMMAR":
				return "05";
			case "알파벳":
			case "VOCABULARY":
				return "06";
			default:
				return "";
		}
	}

}