<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.back.AlOneClick">

	<!-- 원클릭 -->
	<select id="selectLrmpKmmpMapListByOptTxb" parameterType="com.aidt.api.al.pl.dto.AlOneClickDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID,
			MAX(DPTH1.KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
			MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
			MAX(DPTH1.TC_EPS_YN) AS TC_EPS_YN,
			MAX(DPTH1.TC_USE_YN) AS TC_USE_YN,
			#{optTxbId} AS OPT_TXB_ID,
			LRMP.LLU_LRMP_NOD_ID AS LLU_LRMP_NOD_ID,
			MAX(LRMP.RCSTN_ORDN) AS RCSTN_ORDN,
			MAX(LRMP.LCKN_YN) AS LCKN_YN,
			MAX(LRMP.USE_YN) AS USE_YN
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
					ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
					AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
					AND DPTH2.DPTH = 2
				INNER JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN LKMAP
				    ON LKMAP.KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
				INNER JOIN (
					SELECT
						DPTH1.LRMP_NOD_ID AS LLU_LRMP_NOD_ID,
						MAX(DPTH1.LRMP_NOD_NM) AS LLU_LRMP_NOD_NM,
						MAX(DPTH1.RCSTN_ORDN) AS RCSTN_ORDN,
						MAX(DPTH1.LCKN_YN) AS LCKN_YN,
						MAX(DPTH1.USE_YN) AS USE_YN
					FROM
					    LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH1
					WHERE
					    DPTH1.DPTH = 1
					  	AND DPTH1.OPT_TXB_ID = #{optTxbId}
					GROUP BY
					    DPTH1.LRMP_NOD_ID
				) LRMP
				    ON LRMP.LLU_LRMP_NOD_ID = LKMAP.LRMP_NOD_ID
		WHERE
		    DPTH1.OPT_TXB_ID = #{optTxbId}
		GROUP BY
		    DPTH1.KMMP_NOD_ID, LRMP.LLU_LRMP_NOD_ID
		/* AlPlIniDat-Mapper.xml - selectLrmpKmmpMapListByOptTxb - 학습맵 지식맵 매핑정보 - 이혜인 */
	</select>

	<select id="selectLrmpTcListByLluNodId" parameterType="com.aidt.api.al.pl.dto.AlOneClickDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(DPTH1.LRMP_NOD_ID) AS LLU_LRMP_NOD_ID
		  , MAX(DPTH1.LRMP_NOD_NM) AS LLU_LRMP_NOD_NM
		  , MAX(DPTH2.LRMP_NOD_ID) AS MLU_LRMP_NOD_ID
		  , MAX(DPTH2.LRMP_NOD_NM) AS MLU_LRMP_NOD_NM
		  , DPTH4.LRMP_NOD_ID AS TC_LRMP_NOD_ID
		  , MAX(DPTH4.LRMP_NOD_NM) AS TC_LRMP_NOD_NM
		  , MAX(DPTH4.RCSTN_ORDN) AS RCSTN_ORDN
		  , MAX(DPTH4.LCKN_YN) AS LCKN_YN
		  , MAX(DPTH4.USE_YN) AS USE_YN
		FROM
			LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH1
				INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH2
				    ON DPTH1.LRMP_NOD_ID = DPTH2.URNK_LRMP_NOD_ID
				    AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
				    AND DPTH2.DPTH = 2
				INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH3
				    ON DPTH2.LRMP_NOD_ID = DPTH3.URNK_LRMP_NOD_ID
				    AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				    AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH4
				    ON DPTH3.LRMP_NOD_ID = DPTH4.URNK_LRMP_NOD_ID
				    AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				    AND DPTH4.DPTH = 4
		WHERE
		    DPTH1.DPTH = 1
		  	AND DPTH1.OPT_TXB_ID = #{optTxbId}
		  	AND DPTH1.LLU_NOD_ID = #{lluLrmpNodId}
		  	AND DPTH2.LU_EPS_YN = 'Y'
		GROUP BY
		    DPTH4.LRMP_NOD_ID
		/* AlPlIniDat-Mapper.xml - selectLrmpTcListByLluNodId - 학습맵 대단원별 차시 정보 - 강성현 */
	</select>
	
	<update id="updateAiKmmpNodRcstn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		UPDATE LMS_LRM.AI_KMMP_NOD_RCSTN
		   SET 
		   	   RCSTN_ORDN = #{rcstnOrdn}
		   	 , LCKN_YN = #{lcknYn}
		   	 , USE_YN = #{useYn}
		WHERE OPT_TXB_ID = #{optTxbId}
		  AND (KMMP_NOD_ID = #{lluKmmpNodId} OR URNK_KMMP_NOD_ID = #{lluKmmpNodId})
	</update>
	
	<select id="selectEvId" parameterType="com.aidt.api.al.pl.dto.AlOneClickDto" resultType="com.aidt.api.al.pl.dto.AlOneClickDto">
		SELECT
		    EE.EV_ID, EAETR.LLU_KMMP_NOD_ID
		FROM
		    LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			    ON EE.EV_ID = EAETR.EV_ID
			    AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE
		    EAETR.OPT_TXB_ID = #{optTxbId}
			<if test="lluKmmpNodIdList != null and lluKmmpNodIdList.size() > 0">
				AND EAETR.LLU_KMMP_NOD_ID IN
				<foreach collection="lluKmmpNodIdList" index="index" item="item" open="(" close=")" separator="," >
					#{item}
				</foreach>
			</if>
		GROUP BY
		    EE.EV_ID, EAETR.LLU_KMMP_NOD_ID
		/* AlPlIniDat-Mapper.xml - selectEvId - 지식맵 하위평가 잠금여부 조회 - 이혜인 */
	</select>
	
		<update id="updateAntClaAiRcstn" parameterType="com.aidt.api.al.pl.dto.AlOneClkSetmClaDto">
		UPDATE LMS_LRM.AI_KMMP_NOD_RCSTN A,					/* AI_지식맵노드재구성 */
				(SELECT KMMP_NOD_ID,
						RCSTN_ORDN,
						TC_EPS_YN,
						TC_USE_YN,
						LCKN_YN,
						USE_YN
				 FROM LMS_LRM.AI_KMMP_NOD_RCSTN				/* AI_지식맵노드재구성 */
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}) B
		SET A.TC_USE_YN = B.TC_USE_YN,
			A.TC_EPS_YN = B.TC_EPS_YN,
			A.RCSTN_ORDN = B.RCSTN_ORDN,
			A.LCKN_YN = B.LCKN_YN,
			A.USE_YN = B.USE_YN,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND A.KMMP_NOD_ID = B.KMMP_NOD_ID
	</update>
	
	
		<select id="selectAntClaEvId" parameterType="String" resultType="com.aidt.api.al.pl.dto.AlOneClickDto">
		SELECT
		    EE.EV_ID, EAETR.LLU_KMMP_NOD_ID, MAX(AKNR.LCKN_YN) AS LCKN_YN, MAX(AKNR.USE_YN) AS USE_YN 
		FROM
		    LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			    ON EE.EV_ID = EAETR.EV_ID
			    AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR 
				ON EAETR.MLU_KMMP_NOD_ID = AKNR.KMMP_NOD_ID
				AND EAETR.OPT_TXB_ID = AKNR.OPT_TXB_ID 
				AND AKNR.DPTH = 2
		WHERE
		    EAETR.OPT_TXB_ID = #{orgnOptTxbId}
			<if test="list != null">
				AND EAETR.LLU_KMMP_NOD_ID IN
				(SELECT kmmp_nod_id FROM
					lms_lrm.ai_kmmp_nod_rcstn
					WHERE opt_txb_id = #{orgnOptTxbId}
					AND dpth=1)
			</if>
		GROUP BY
		    EE.EV_ID, EAETR.LLU_KMMP_NOD_ID
		/* AlPlIniDat-Mapper.xml - selectAntClaEvId - 다른 학급에 저장시 평가 잠금여부 조회 - 김현혜 */
	</select>

</mapper>