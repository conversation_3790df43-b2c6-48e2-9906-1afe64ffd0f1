package com.aidt.api.ea.evcom.lrnRpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class LrnPtrnDto {

	@Parameter(name="학습시간")
	private int evTmScnt;
	
	@Parameter(name="성취도")
	private double cansRt;
	
	@Parameter(name="학습시간 비율")
	private double lrnRatio;
	
	@Parameter(name="월")
	private int month;
	
	@Parameter(name="요일")
	private String week;
	
	
	@Parameter(name="시간대")
	private String tm;
	
	@Parameter(name="당일 오답노트 해결 수")
	private String todayEsEvCnt;
	
	@Parameter(name="해결못한 오답노트 수")
	private String xIansCnt;
	
	@Parameter(name="전체문제 수")
	private int fnlQstCnt;
	
	
	@Parameter(name="맞힌문제 수")
	private int cansCnt;
	
	@Parameter(name="유저 수")
	private int userCount;
	
	
	

}
