<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.inidat.tcr">
    <!-- 
        담당운영교과서ID취득
     -->
    <select id="selectOptTxbId" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto" resultType="Map">
        SELECT
               A.OPT_TXB_ID    /* 운영교과서ID */
              ,A.TXB_ID        /* 교과서ID */
              ,SUM(IF(D.OPT_TXB_ID IS NULL, 0, 1)) AS CNT   /* 재구성데이터건수 */
              ,IF(C.TRM_DV_CD = '02', 'Y', 'N')    AS SEC_TRM_YN /* 2학기여부 */
        FROM LMS_LRM.CM_OPT_TXB A /* CM운영교과서 */
             INNER JOIN LMS_CMS.BC_TXB C /* BC_교과서 */
                   ON A.TXB_ID = C.TXB_ID
                   AND C.USE_YN = 'Y'
                   AND C.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D /* TL_교과학습노드재구성 */
                  ON A.OPT_TXB_ID = D.OPT_TXB_ID
        WHERE A.OPT_TXB_ID = #{optTxbId}
        GROUP BY A.OPT_TXB_ID, A.TXB_ID, C.TRM_DV_CD
    
        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - selectOptTxbId */
    </select>

    <!-- TL_교과학습노드재구성 데이터생성  -->
    <insert id="insertTlSbcLrnNodRcstn" parameterType="Map">
        INSERT INTO LMS_LRM.TL_SBC_LRN_NOD_RCSTN  /* TL_교과학습노드재구성 */
        (OPT_TXB_ID           /* 운영교과서ID */
        ,LRMP_NOD_ID          /* 학습맵노드ID */
        ,URNK_LRMP_NOD_ID     /* 상위학습맵노드ID */
        ,LLU_NOD_ID           /* 대단원노드ID */
        ,LRMP_NOD_NM          /* 학습맵노드명 */
        ,DPTH                 /* 깊이 */
        ,ORGL_ORDN            /* 원본순서 */
        ,WKB_STR_PGE_NO       /* 익힘책시작페이지번호 */
        ,WKB_END_PGE_NO       /* 익힘책종료페이지번호 */
        ,TXB_STR_PGE_NO       /* 교과서시작페이지번호 */
        ,TXB_END_PGE_NO       /* 교과서종료페이지번호 */
        ,RCSTN_ORDN           /* 재구성순서 */
        ,TRM_DV_CD
        ,LCKN_YN              /* 잠금여부 */
        ,USE_YN               /* 사용여부 */
--        ,ORGL_LRN_STR_DT      /* 원본학습시작일자 */
--        ,LRN_STR_DT           /* 학습시작일자 */
        ,LU_NO_USE_YN         /* 단원번호사용여부 */
        ,LU_EPS_YN            /* 단원노출여부 */
        ,CRTR_ID              /* 생성자ID */
        ,CRT_DTM              /* 생성일시 */
        ,MDFR_ID              /* 수정자ID */
        ,MDF_DTM              /* 수정일시 */
        ,DB_ID                /* 데이터베이스ID */
        )
        SELECT
               #{tgtOptTxbId}                           AS OPT_TXB_ID   /* 운영교과서ID */
              ,M.LRMP_NOD_ID                                            /* 학습맵노드ID */
              ,M.URNK_LRMP_NOD_ID                                       /* 상위학습맵노드ID */
              ,M.LLU_NOD_ID                                             /* 대단원ID */
              ,M.LRMP_NOD_NM                                            /* 학습맵노드명 */
              ,M.DPTH                                                   /* 깊이 */
              ,IF(M.LWS_YN = 'N', M.SRT_ORDN, M.NOD_NUM) AS ORGL_ORDN   /* 원본순서 */
              ,M.WKB_STR_PGE_NO                                         /* 익힘책시작페이지번호 */
              ,M.WKB_END_PGE_NO                                         /* 익힘책종료페이지번호 */
              ,M.TXB_STR_PGE_NO                                         /* 교과서시작페이지번호 */
              ,M.TXB_END_PGE_NO                                         /* 교과서종료페이지번호 */
              ,IF(M.LWS_YN = 'N', M.SRT_ORDN, M.NOD_NUM) AS RCSTN_ORDN  /* 재구성순서 */
              ,M.TRM_DV_CD
-- TODO              ,IF(M.LLU_NOD_ID = M.FST_NOD_ID, 'N', 'Y') AS LCKN_YN     /* 잠금여부 1단원제외하고 전체잠금*/
-- TOOD       ,'Y' AS LCKN_YN     /* 잠금여부 전체잠금 하고 학습예정일 설정 */
              ,'N' AS LCKN_YN     /* 잠금여부 1단원제외하고 전체잠금*/
              ,'Y'                                       AS USE_YN      /* 사용여부 */
--              ,M.TC_STR_DT                               AS ORGL_LRN_STR_DT
--              ,M.TC_STR_DT                               AS LRN_STR_DT
              ,M.LU_NO_USE_YN                                           /* 단원번호사용여부 */
              ,M.LU_EPS_YN                                              /* 단원노출여부 */
              ,'System'                           		 AS CRTR_ID     /* 생성자ID */
              ,NOW()                                     AS CRT_DTM     /* 생성일시 */
              ,'System'			                          AS MDFR_ID     /* 수정자ID */
              ,NOW()                                     AS MDF_DTM     /* 수정일시 */
              ,#{dbId}                                   AS DB_ID       /* 데이터베이스ID */
        FROM 
        (
            WITH RECURSIVE RCS AS
            (
                SELECT
                       CAST(LPAD(A.SRT_ORDN, 10,'0') AS CHAR(50)) AS SORT
                      ,A.LRMP_NOD_ID
                      ,A.URNK_LRMP_NOD_ID
                      ,B.TXB_ID
                      ,A.LRMP_NOD_NM
                      ,A.DPTH
                      ,A.SRT_ORDN
                      ,A.TRM_DV_CD
                      ,A.TXB_STR_PGE_NO
                      ,A.TXB_END_PGE_NO
                      ,A.WKB_STR_PGE_NO
                      ,A.WKB_END_PGE_NO
                      ,A.LWS_YN
                      ,A.LRMP_NOD_ID                             AS LLU_NOD_ID
                      ,A.LU_NO_USE_YN         /* 단원번호사용여부 */
                      ,A.LU_EPS_YN            /* 단원노출여부 */
                FROM LMS_CMS.BC_LRMP_NOD A /* BC_학습맵노드 */
                     JOIN LMS_CMS.BC_LRMP B /* BC_학습맵 */
                       ON A.LRMP_ID = B.LRMP_ID
                      AND B.LRN_TP_CD = 'TL'
                      AND B.DEL_YN = 'N'
                WHERE B.TXB_ID = #{tgtTxtId}
                AND IFNULL(A.URNK_LRMP_NOD_ID, '') = ''
                AND A.DEL_YN = 'N'
                AND A.ISPT_YN = 'Y'
                UNION ALL
                SELECT
                       CONCAT(T2.SORT, LPAD(T1.SRT_ORDN, 10, '0')) AS SORT
                      ,T1.LRMP_NOD_ID
                      ,T1.URNK_LRMP_NOD_ID
                      ,B.TXB_ID
                      ,T1.LRMP_NOD_NM
                      ,T1.DPTH
                      ,T1.SRT_ORDN
                      ,T1.TRM_DV_CD
                      ,T1.TXB_STR_PGE_NO
                      ,T1.TXB_END_PGE_NO
                      ,T1.WKB_STR_PGE_NO
                      ,T1.WKB_END_PGE_NO
                      ,T1.LWS_YN
                      ,T2.LLU_NOD_ID
                      ,T1.LU_NO_USE_YN         /* 단원번호사용여부 */
                      ,T1.LU_EPS_YN            /* 단원노출여부 */
                FROM LMS_CMS.BC_LRMP_NOD T1 /* BC_학습맵노드 */
                     JOIN LMS_CMS.BC_LRMP B /* BC_학습맵 */
                       ON T1.LRMP_ID = B.LRMP_ID
                      AND B.LRN_TP_CD = 'TL'
                      AND B.DEL_YN = 'N'
                     INNER JOIN RCS T2
                WHERE B.TXB_ID = #{tgtTxtId}
                AND B.TXB_ID = T2.TXB_ID
                AND T1.URNK_LRMP_NOD_ID = T2.LRMP_NOD_ID
                AND T1.DEL_YN = 'N'
                AND T1.ISPT_YN = 'Y'
            ) 
            SELECT R.LRMP_NOD_ID
                  ,R.URNK_LRMP_NOD_ID
                  ,R.LLU_NOD_ID
                  ,R.TXB_ID
                  ,R.LRMP_NOD_NM
                  ,R.DPTH
                  ,R.TXB_STR_PGE_NO
                  ,R.TXB_END_PGE_NO
                  ,R.WKB_STR_PGE_NO
                  ,R.WKB_END_PGE_NO
                  ,R.LWS_YN
                  ,R.SRT_ORDN
                  ,R.TRM_DV_CD
                  ,R.LU_NO_USE_YN
                  ,R.LU_EPS_YN
                  ,@NODNUM := IF(R.DPTH =1, 0, IF(R.LWS_YN !='Y', @NODNUM, @NODNUM + 1)) AS NOD_NUM
                  ,@FST_NOD_ID := IF(@FST_NOD_ID = '', R.LLU_NOD_ID, @FST_NOD_ID)        AS FST_NOD_ID /* 제1대단원ID 잠금여부설정용 */
                  ,@TC_CNT := IF(R.DPTH = 4, @TC_CNT + 1, @TC_CNT)                       AS TC_CNT
                  ,@TC_STR_DT := IF(R.DPTH = 4 AND (@NODNUM % 3) = 1 AND @TC_CNT != 1, DATE_ADD(@TC_STR_DT, INTERVAL +7 DAY), @TC_STR_DT)  AS TC_STR_DT
            FROM RCS R
                JOIN (SELECT @NODNUM := 0) N
                JOIN (SELECT @FST_NOD_ID  := '') F
                JOIN (SELECT @TC_STR_DT := DATE_ADD(#{baseYmd}, INTERVAL -WEEKDAY(#{baseYmd}) DAY)) Y
                JOIN (SELECT @TC_CNT :=0) Z
            WHERE R.TXB_ID = #{tgtTxtId}
            ORDER BY SORT ASC, LRMP_NOD_ID ASC
        ) M

        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - insertTlSbcLrnNodRcstn */
    </insert>

    <!-- TL_교과학습노드재구성 생성된 데이터의 대단원/중단원/소단원 학습시작일 수정 -->
    <update id="updateTlSbcLrnNodRcstn" parameterType="Map">
        UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */
            INNER JOIN (SELECT OPT_TXB_ID
                              ,LLU_NOD_ID
                              ,MIN(LRN_STR_DT) AS LRN_STR_DT
                        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN /* TL_교과학습노드재구성 */
                        WHERE OPT_TXB_ID = #{tgtOptTxbId}
                          AND DPTH = 4
                        GROUP BY OPT_TXB_ID, LLU_NOD_ID
                        ) B
                    ON A.LLU_NOD_ID = B.LLU_NOD_ID
        SET A.ORGL_LRN_STR_DT = B.LRN_STR_DT
           ,A.LRN_STR_DT = B.LRN_STR_DT
        WHERE A.OPT_TXB_ID = #{tgtOptTxbId}
        AND A.DPTH <![CDATA[<]]> 4
-- TODO        AND A.LCKN_YN = 'Y'

        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - updateTlSbcLrnNodRcstn */
    </update>
    
    <!-- TL_교과학습활동재구성 데이터조회  -->
    <select id="selectAtvRcstnDat" parameterType="map" resultType="com.aidt.api.tl.inidat.dto.TlIniDatLrnAtvDto">
    select
			#{tgtOptTxbId} AS OPT_TXB_ID
			,L.LRMP_NOD_ID
			,L.LRN_ATV_ID
			,L.LRN_STP_ID
			,L.CTN_CD
			,L.LRN_ATV_NM
			,L.CTN_TP_CD
			,L.USE_YN
			,L.LRN_STP_ID AS ORGL_LRN_STP_ID
			,L.NODNUM AS ORGL_ORDN
			,L.NODNUM AS RCSTN_ORDN
			,L.EV_ID AS EV_ID
		from ( select
				@NODNUM := IF (AA.LRMP_NOD_ID <![CDATA[<>]]> IFNULL(AA.previous_value, ''), 1, IF(AA.LRN_ATV_ID IS NULL, @NODNUM, @NODNUM+1)) as NODNUM
		    	,AA.*
		    	from ( select
		    			A.SRT_ORDN
		    			,LAG(A.LRMP_NOD_ID) OVER (ORDER BY
		    			B1.SRT_ORDN ASC,
		    			B2.SRT_ORDN ASC,
		    			B3.SRT_ORDN ASC,
		    			B.SRT_ORDN ASC,
		    			STP.STP_ORDN ASC,
		    			STP.SRT_ORDN ASC, A.SRT_ORDN ASC, A.LRN_ATV_ID ASC) AS previous_value
		    			,A.LRMP_NOD_ID
		    			,A.LRN_ATV_ID
		    			,A.LRN_STP_ID
		    			,A.CTN_CD
		    			,A.LRN_ATV_NM
		    			,A.CTN_TP_CD
		    			,A.USE_YN
		    			,case 
			    			when A.CTN_TP_CD='EX'
			    			then (select EV_ID 
			    					from LMS_LRM.EA_EV
		    						where OPT_TXB_ID = #{tgtOptTxbId}
		    						AND EVSH_CD = A.CTN_CD 
		    						order by CRT_DTM desc limit 1)
		    				else null 
		    			end as EV_ID
		    			from LMS_CMS.BC_LRN_ATV_CTN A
		    			inner join LMS_CMS.BC_LRMP_NOD B /* BC_학습맵노드 */
		    				on A.LRMP_NOD_ID =B.LRMP_NOD_ID
		    			inner JOIN LMS_CMS.BC_LRMP C on B.LRMP_ID =C.LRMP_ID
		    			inner join LMS_CMS.BC_LRMP_NOD B3 on B.URNK_LRMP_NOD_ID = B3.LRMP_NOD_ID
		    			inner join LMS_CMS.BC_LRMP_NOD B2 on B3.URNK_LRMP_NOD_ID = B2.LRMP_NOD_ID
		    			inner join LMS_CMS.BC_LRMP_NOD B1 on B2.URNK_LRMP_NOD_ID = B1.LRMP_NOD_ID
		    			LEFT JOIN (SELECT LRN_STP_ID
		    							,LRMP_NOD_ID
		    							,CASE LRN_STP_DV_CD
		    								WHEN 'CL' THEN 1
		    								WHEN 'EX' THEN 2
 		   								ELSE 3
		    							END STP_ORDN
 		   							,SRT_ORDN
 		   						FROM LMS_CMS.BC_LRN_STP
		    						WHERE DEL_YN = 'N') STP /* BC_학습단계 */
		    				ON A.LRMP_NOD_ID = STP.LRMP_NOD_ID
		    				AND A.LRN_STP_ID = STP.LRN_STP_ID
		    			where C.TXB_ID =#{tgtTxtId}
		    			AND C.LRN_TP_CD = 'TL'
		    			AND C.DEL_YN = 'N'
		    			and B.DEL_YN = 'N'
		    			AND B.ISPT_YN = 'Y'
		    			and A.del_yn='N'
		    			ORDER BY
		    				B1.SRT_ORDN ASC,
		    				B2.SRT_ORDN ASC,
		    				B3.SRT_ORDN ASC,
		    				B.SRT_ORDN ASC,
		    				STP.STP_ORDN ASC,
		    				STP.SRT_ORDN ASC, 
		    				A.SRT_ORDN ASC, 
		    				A.LRN_ATV_ID asc) AA
		    ) L
        WHERE L.LRN_ATV_ID IS NOT NULL
        
        /** 교과학습 김형준 TlIniDatTcr-Mapper.xml - selectAtvRcstnDat */
    </select>

    <!-- TL_교과학습활동재구성 데이터생성 
    <insert id="insertTlLrnAtvRcstn" parameterType="Map">
        INSERT INTO LMS_LRM.TL_SBC_LRN_ATV_RCSTN
        (OPT_TXB_ID       /* 운영교과서ID */
        ,LRMP_NOD_ID      /* 학습맵노드ID */
        ,LRN_ATV_ID       /* 학습활동ID */
        ,LRN_STP_ID       /* 학습단계ID */
        ,CTN_CD           /* 콘텐츠코드 */
        ,LRN_ATV_NM       /* 학습활동명 */
        ,CTN_TP_CD        /* 콘텐츠유형코드 */
        ,USE_YN           /* 사용여부 */
        ,ORGL_LRN_STP_ID  /* 원본학습단계ID */
        ,ORGL_ORDN        /* 원본순서 */
        ,RCSTN_ORDN       /* 재구성순서 */
        ,EV_ID            /* 평가ID */
        ,CRTR_ID          /* 생성자ID */
        ,CRT_DTM          /* 생성일시 */
        ,MDFR_ID          /* 수정자ID */
        ,MDF_DTM          /* 수정일시 */
        ,DB_ID            /* 데이터베이스ID */
        ) VALUES
        <foreach collection="atvList" index="index" item="atv" separator=",">
			(
			#{atv.optTxbId},
			#{atv.lrmpNodId},
			#{atv.lrnAtvId},
			#{atv.lrnStpId},
			#{atv.ctnCd},
			#{atv.lrnAtvNm},
			#{atv.ctnTpCd},
			#{atv.useYn},
			#{atv.orglLrnStpId},
			#{atv.orglOrdn},
			#{atv.rcstnOrdn},
			<if test="atv.evId == 0">NULL,</if>
            <if test="atv.evId != 0">#{atv.evId},</if>
			'System',
			NOW(),
			'System',
			NOW(),
			#{dbId}
			)
		</foreach>
        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - insertTlLrnAtvRcstn */
    </insert>
    -->
    
    <!-- TL_교과학습활동재구성 데이터생성  -->
    <insert id="insertTlLrnAtvRcstn" parameterType="Map">
        INSERT INTO LMS_LRM.TL_SBC_LRN_ATV_RCSTN
        (OPT_TXB_ID       /* 운영교과서ID */
        ,LRMP_NOD_ID      /* 학습맵노드ID */
        ,LRN_ATV_ID       /* 학습활동ID */
        ,LRN_STP_ID       /* 학습단계ID */
        ,CTN_CD           /* 콘텐츠코드 */
        ,LRN_ATV_NM       /* 학습활동명 */
        ,CTN_TP_CD        /* 콘텐츠유형코드 */
        ,USE_YN           /* 사용여부 */
        ,ORGL_LRN_STP_ID  /* 원본학습단계ID */
        ,ORGL_ORDN        /* 원본순서 */
        ,RCSTN_ORDN       /* 재구성순서 */
        ,EV_ID            /* 평가ID */
        ,CRTR_ID          /* 생성자ID */
        ,CRT_DTM          /* 생성일시 */
        ,MDFR_ID          /* 수정자ID */
        ,MDF_DTM          /* 수정일시 */
        ,DB_ID            /* 데이터베이스ID */
        )
        select
			#{tgtOptTxbId} AS OPT_TXB_ID
			,L.LRMP_NOD_ID
			,L.LRN_ATV_ID
			,L.LRN_STP_ID
			,L.CTN_CD
			,L.LRN_ATV_NM
			,L.CTN_TP_CD
			,L.USE_YN
			,L.LRN_STP_ID AS ORGL_LRN_STP_ID
			,L.NODNUM AS ORGL_ORDN
			,L.NODNUM AS RCSTN_ORDN
			,L.EV_ID AS EV_ID
			,'System'		   AS CRTR_ID
            ,NOW()           AS CRT_DTM
            ,'System'		   AS MDFR_ID
            ,NOW()           AS MDF_DTM
            ,#{dbId}         AS DB_ID
		from ( select
				@NODNUM := IF (AA.LRMP_NOD_ID <![CDATA[<>]]> IFNULL(AA.previous_value, ''), 1, IF(AA.LRN_ATV_ID IS NULL, @NODNUM, @NODNUM+1)) as NODNUM
		    	,AA.*
		    	from ( select
		    			A.SRT_ORDN
		    			,LAG(A.LRMP_NOD_ID) OVER (ORDER BY
		    			B1.SRT_ORDN ASC,
		    			B2.SRT_ORDN ASC,
		    			B3.SRT_ORDN ASC,
		    			B.SRT_ORDN ASC,
		    			(case when STP.LRN_STP_DV_CD = 'CL' then 1 WHEN STP.LRN_STP_DV_CD = 'EX' then 2 else 3 END),
		    			STP.SRT_ORDN ASC, A.SRT_ORDN ASC, A.LRN_ATV_ID ASC) AS previous_value
		    			,A.LRMP_NOD_ID
		    			,A.LRN_ATV_ID
		    			,A.LRN_STP_ID
		    			,A.CTN_CD
		    			,A.LRN_ATV_NM
		    			,A.CTN_TP_CD
		    			,A.USE_YN
		    			,case 
			    			when A.CTN_TP_CD='EX'
			    			then (select EV_ID 
			    					from LMS_LRM.EA_EV
		    						where OPT_TXB_ID = #{tgtOptTxbId}
		    						AND EVSH_CD = A.CTN_CD 
		    						order by CRT_DTM desc limit 1)
		    				else null 
		    			end as EV_ID
		    			from LMS_CMS.BC_LRN_ATV_CTN A
		    			inner join LMS_CMS.BC_LRMP_NOD B /* BC_학습맵노드 */
		    				on A.LRMP_NOD_ID =B.LRMP_NOD_ID
		    			inner JOIN LMS_CMS.BC_LRMP C on B.LRMP_ID =C.LRMP_ID
		    			inner join LMS_CMS.BC_LRMP_NOD B3 on B.URNK_LRMP_NOD_ID = B3.LRMP_NOD_ID
		    			inner join LMS_CMS.BC_LRMP_NOD B2 on B3.URNK_LRMP_NOD_ID = B2.LRMP_NOD_ID
		    			inner join LMS_CMS.BC_LRMP_NOD B1 on B2.URNK_LRMP_NOD_ID = B1.LRMP_NOD_ID
		    			LEFT JOIN LMS_CMS.BC_LRN_STP STP
							ON A.LRMP_NOD_ID = STP.LRMP_NOD_ID
							AND A.LRN_STP_ID = STP.LRN_STP_ID
							AND STP.DEL_YN = 'N'
		    			where C.TXB_ID =#{tgtTxtId}
		    			AND C.LRN_TP_CD = 'TL'
		    			AND C.DEL_YN = 'N'
		    			and B.DEL_YN = 'N'
		    			AND B.ISPT_YN = 'Y'
		    			and A.del_yn='N'
		    			ORDER BY
		    				B1.SRT_ORDN ASC,
		    				B2.SRT_ORDN ASC,
		    				B3.SRT_ORDN ASC,
		    				B.SRT_ORDN ASC,
		    				(case when STP.LRN_STP_DV_CD = 'CL' then 1 WHEN STP.LRN_STP_DV_CD = 'EX' then 2 else 3 END),
		    				STP.SRT_ORDN ASC, 
		    				A.SRT_ORDN ASC, 
		    				A.LRN_ATV_ID asc) AA
		    ) L
        WHERE L.LRN_ATV_ID IS NOT NULL

        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - insertTlLrnAtvRcstn */
    </insert>

    <!-- 
        학습활동클래스보드데이터 건수 취득
     -->
    <select id="countAtvClabd" parameterType="Map" resultType="int">
          SELECT straight_join
            COUNT(1)
        FROM 
        	( SELECT *
			    FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN
			    WHERE OPT_TXB_ID =  #{tgtOptTxbId}   -- 로그인세션정보의 운영교과서ID
    		) C 
            INNER JOIN LMS_CMS.BC_CLABD_LRGS A /* BC_클래스보드 대제목  */
                   ON A.LRN_ATV_ID = C.LRN_ATV_ID
            INNER JOIN LMS_CMS.BC_CLABD_SML B /* BC_클래스보드 소제목  */
                    ON A.CLABD_LRGS_ID =B.CLABD_LRGS_ID
        WHERE A.map_typ='TL'
          AND A.USE_YN = 'Y'
    
        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - countAtvClabd */
    </select>

    <!-- TL_교과학습활동클래스보드 데이터생성  -->
    <insert id="insertTlSbcLrnAtvClabd" parameterType="Map"> 
        INSERT INTO LMS_LRM.TL_SBC_LRN_ATV_CLABD /* TL_교과학습활동클래스보드 */
            (OPT_TXB_ID
            ,LRMP_NOD_ID
            ,LRN_ATV_ID
            ,CLABD_LRGS_ID
            ,CLABD_SML_ID
            ,CLABD_LRGS_NM
            ,CLABD_SML_NM
            ,CLABD_TYP
            ,CLABD_URL
            ,CLABD_ORDN
            ,CRTR_ID
            ,CRT_DTM
            ,MDFR_ID
            ,MDF_DTM
            ,DB_ID)
        SELECT C.OPT_TXB_ID  
              ,C.LRMP_NOD_ID
              ,A.LRN_ATV_ID
              ,A.CLABD_LRGS_ID
              ,B.CLABD_SML_ID
              ,A.CLABD_LRGS_NM
              ,B.CLABD_SML_NM
              ,B.CLABD_TYP
              ,NULL            AS CLABD_URL
              ,B.CLABD_ORDN
              ,'System'	       AS CRTR_ID
              ,NOW()           AS CRT_DTM
              ,'System'		   AS MDFR_ID
              ,NOW()           AS MDF_DTM
              ,#{dbId}         AS DB_ID
        FROM 
        	( SELECT *
			    FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN
			    WHERE OPT_TXB_ID =  #{tgtOptTxbId}   -- 로그인세션정보의 운영교과서ID
    		) C 
        	INNER JOIN LMS_CMS.BC_CLABD_LRGS A /* BC_클래스보드 대제목  */
                 ON A.LRN_ATV_ID = C.LRN_ATV_ID
            INNER JOIN LMS_CMS.BC_CLABD_SML B /* BC_클래스보드 소제목  */
                  ON A.CLABD_LRGS_ID =B.CLABD_LRGS_ID
        WHERE A.MAP_TYP='TL'
        AND A.USE_YN = 'Y'  
        /** 교과학습 강성희 TlIniDatTcr-Mapper.xml - insertTlSbcLrnAtvClabd */
    </insert>
    
	<!-- 원클릭학습설정 번호 노출 목차  조회 -->
	<select id="selectLrnSortList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
	select AA.* from (
		SELECT   a.OPT_TXB_ID,
         a.LU_NO_USE_YN,
         a.LU_EPS_YN,
         a.RCSTN_ORDN,
         a.DPTH,
         a.URNK_LRMP_NOD_ID,
         a.LRMP_NOD_ID,
         a.RCSTN_NO
		FROM     lms_lrm.tl_sbc_lrn_nod_rcstn a
		         INNER JOIN lms_cms.bc_lrmp_nod b
		         ON       a.LRMP_NOD_ID=b.LRMP_NOD_ID
		WHERE    a.OPT_TXB_ID = #{optTxbId}
		  AND a.DPTH =1
                  AND      b.LU_EPS_YN    ='Y'
                  AND      a.USE_YN       ='Y'
                  AND      a.LU_NO_USE_YN ='Y'
		UNION ALL
		SELECT   a.OPT_TXB_ID,
         a.LU_NO_USE_YN,
         a.LU_EPS_YN,
         a.RCSTN_ORDN,
         a.DPTH,
         node3.LRMP_NOD_ID URNK_LRMP_NOD_ID,
         a.LRMP_NOD_ID,
         a.RCSTN_NO
		FROM     lms_lrm.tl_sbc_lrn_nod_rcstn a
         INNER JOIN lms_cms.bc_lrmp_nod b
         ON       a.LRMP_NOD_ID=b.LRMP_NOD_ID
         INNER JOIN lms_cms.bc_lrmp_nod node1
         ON       node1.LRMP_NOD_ID=b.URNK_LRMP_NOD_ID
         INNER JOIN lms_cms.bc_lrmp_nod node2
         ON       node2.LRMP_NOD_ID=node1.URNK_LRMP_NOD_ID
         INNER JOIN lms_cms.bc_lrmp_nod node3
         ON       node3.LRMP_NOD_ID=node2.URNK_LRMP_NOD_ID
		WHERE    a.OPT_TXB_ID          = #{optTxbId}
		AND
      
                           a.DPTH =4
                  AND      b.LU_EPS_YN    ='Y'
                  AND      a.USE_YN       ='Y'
                  AND      a.LU_NO_USE_YN ='Y'
		) AA                  
	ORDER BY 
         DPTH,
         RCSTN_ORDN
         
		 /** 교과학습 노성용 TlLrnOneClkSetmTcr-Mapper.xml - selectLrnSortList */
	</select>
	
	<!-- 원클릭학습설정 노출 번호  저장 -->
	<update id="updateLrnSort" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
		UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN /* TL_교과학습노드재구성 */
		SET MDF_DTM = NOW()	                /* 수정일시 */
		   ,RCSTN_NO = #{rcstnNo}  		/* 재구성 노출 번호 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		/** 교과학습 노성용 TlLrnOneClkSetmTcr-Mapper.xml - updateLrnSort */
	</update>
	
	<!-- 로그인 시 해당 학습 시작일 잠금 해제 데이터 조회-->
    <select id="selectScdlMgLcknList" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto" resultType="Map">
		SELECT R.LRMP_NOD_ID
 		FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R /* TL_교과학습노드재구성 */
		WHERE R.OPT_TXB_ID = #{optTxbId}
		AND R.LRN_STR_DT <![CDATA[<=]]> CURDATE()

        /** 교과학습 김형준 TlIniDatTcr-Mapper.xml - selectScdlMgLcknList */
    </select>
	
	<!-- 로그인 시 해당 학습 시작일 잠금 해제 -->
    <update id="updateLrnScdlMgLckn" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto">
        UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN
		SET LCKN_YN = 'N'
			,LRN_STR_DT = NULL
			,MDF_DTM = NOW()
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}

        /** 교과학습 김형준 TlIniDatTcr-Mapper.xml - updateLrnScdlMgLckn */
    </update>
    
    <delete id="deleteLrnNodRcstn" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto">
		DELETE FROM LMS_LRM.tl_sbc_lrn_nod_rcstn WHERE OPT_TXB_ID = #{optTxbId}
		
		/** 교과학습 김형준 TlIniDatTcr-Mapper.xml - deleteLrnNodRcstn */
	</delete>
	
	<delete id="deleteLrnAtvRcstn" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto">
		DELETE FROM LMS_LRM.tl_sbc_lrn_atv_rcstn WHERE OPT_TXB_ID = #{optTxbId}
		
		/** 교과학습 김형준 TlIniDatTcr-Mapper.xml - deleteLrnAtvRcstn */
	</delete>
	
	<delete id="deleteLrnAtvClabd" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto">
		DELETE FROM LMS_LRM.tl_sbc_lrn_atv_clabd WHERE OPT_TXB_ID = #{optTxbId}
		
		/** 교과학습 김형준 TlIniDatTcr-Mapper.xml - deleteLrnAtvClabd */
	</delete>
	
	<delete id="deleteSpLrnRcstn" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto">
		DELETE FROM LMS_LRM.sl_sp_lrn_rcstn WHERE OPT_TXB_ID = #{optTxbId}
		
		/** 교과학습 김형준 TlIniDatTcr-Mapper.xml - deleteSpLrnRcstn */
	</delete>
	
	<!-- 
        외부활동설정 데이터 체크
     -->
    <select id="selectExtAtv" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto" resultType="int">
        SELECT COUNT(*) FROM LMS_LRM.cm_ext_atv_setm WHERE OPT_TXB_ID = #{optTxbId}
    
        /** 교과학습 김형준 TlIniDatTcr-Mapper.xml - selectExtAtv */
    </select>
    
    <!-- 외부활동 설정 데이터 생성  -->
    <insert id="insertExtAtv" parameterType="com.aidt.api.tl.inidat.dto.TlIniDatCondDto">
        INSERT INTO LMS_LRM.cm_ext_atv_setm
        (OPT_TXB_ID, CLS_PPNG_USE_YN, CLS_LINK_USE_YN, CLS_ARCHV_USE_YN, CLS_TSHRP_USE_YN, PADL_USE_YN, CANV_USE_YN, MIR_CANV_USE_YN, GGL_DOC_USE_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM)
        VALUES(#{optTxbId}, "N", "N", "N", "N", "N", "N", "N", "N", 'System', NOW(), 'System', NOW())

        /** 교과학습 김형준 TlIniDatTcr-Mapper.xml - insertExtAtv */
    </insert>

</mapper>