package com.aidt.api.bc.slpp.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.slpp.dto.BcSlppComandDto;
import com.aidt.api.bc.slpp.dto.BcSlppDto;
import com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto;
import com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:55
 * @modify 2024-01-05 17:52:55
 * @desc 학생_쪽지 Service
 */

//@Slf4j
@Service
public class BcSlppStuService {

    private final String MAPPER_NAMESPACE = "api.bc.slpp.stu.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 쪽지 목록 조회 서비스
     *
     * @param dto: BcSlppDtoPagingRequest
     * @return List<BcSlppDto>
     */
	@Transactional
    public List<BcSlppDto> selectSlppList(BcSlppPagingRequestDto dto) {
		updateCofmYnSlppList(dto);
		return commonDao.selectList(MAPPER_NAMESPACE + "selectSlppList", dto);
    }

	/**
	 * 쪽지 목록 조회 서비스 - 확인여부 업데이트
	 *
	 * @param dto: BcSlppDtoPagingRequest
	 */
	public void updateCofmYnSlppList(BcSlppPagingRequestDto dto) {
		commonDao.update(MAPPER_NAMESPACE + "updateCofmYnSlppList", dto);
	}

    /**
     * 쪽지 등록 요청 서비스
     *
     * @param dto : BcSlppDto
     * @return int
     */
    @Transactional
	public int insertSlpp(BcSlppDto dto) {
		return commonDao.insert(MAPPER_NAMESPACE + "insertSlpp", dto);	//학생 -> 교사 쪽지 입력
	}

    /**
     * 쪽지 삭제 서비스
     * @param list : List<BcSlppDto>
     * @return int
     */
    @Transactional
	public int deleteSlpp(List<BcSlppDto> list) {
		return commonDao.update(MAPPER_NAMESPACE + "deleteSlppList", list);
	}

	/**
	 * 쪽지 삭제 서비스
	 * @param dto : BcSlppDto
	 * @return int
	 */
	@Transactional
	public int deleteSlpp(BcSlppDto dto) {
		return commonDao.update(MAPPER_NAMESPACE + "deleteSlpp", dto);
	}

	/**
	 * 쪽지 삭제 서비스
	 * @param dto : BcSlppDto
	 * @return int
	 */
	@Transactional
	public int deleteAllSlpp(BcSlppComandDto dto) {
		return commonDao.update(MAPPER_NAMESPACE + "deleteAllSlpp", dto);
	}

	/**
	 * 학생 대화가능 시간 조회
	 * @param optTxbId : String
	 * @return int
	 */
	@Transactional
	public BcSlppTmSetmDto selectSlppTmSetm(String optTxbId) {
		
		BcSlppTmSetmDto dtoRes = commonDao.select(MAPPER_NAMESPACE + "selectSlppTmSetm", optTxbId);
		
		if(dtoRes == null) {
			dtoRes =commonDao.select(MAPPER_NAMESPACE + "selectSlppTmSetmDummy", optTxbId);
		}
		return dtoRes;
	}
	

	/**
	 * 읽지 않은 대화 존재여부 조회
	 * @param dto : BcSlppPagingRequestDto
	 * @return int
	 */
	public int selectSlppCofmCnt(BcSlppPagingRequestDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectSlppCofmCnt", dto);
	}
	
}
