<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.common.cm">

	<!-- 2024-07-10 학생목록 조회(KERIS API로 사용자명 가공) -->
	<select id="selectStuInfoList" parameterType="com.aidt.api.bc.cm.dto.BcUserInfoDto" resultType="com.aidt.api.bc.cm.dto.BcUserInfoDto">
		/** BcCm-Mapper.xml - selectStuInfoList */
		SELECT
			C.USR_ID
			, C.USR_NM
			, C.USR_TP_CD
			, C.STU_NO
			, C.LRNR_VEL_TP_CD
			, C.FLN_ST_CD
			, C.NTR_YN
			, C.KERIS_USR_ID
		FROM
			LMS_LRM.CM_CLA A
		INNER JOIN LMS_LRM.CM_OPT_TXB B		ON A.CLA_ID = B.CLA_ID
		INNER JOIN LMS_LRM.CM_USR C			ON B.CLA_ID = C.CLA_ID
		WHERE A.CLA_ID = #{claId}
		AND B.OPT_TXB_ID = #{optTxbId}
		AND C.USR_TP_CD = 'ST'
		UNION ALL
		SELECT
			D.USR_ID
			, D.USR_NM
			, D.USR_TP_CD
			, D.STU_NO
			, D.LRNR_VEL_TP_CD
			, D.FLN_ST_CD
			, D.NTR_YN
			, D.KERIS_USR_ID
		FROM
			LMS_LRM.CM_CLA A
		INNER JOIN LMS_LRM.CM_OPT_TXB B		ON A.CLA_ID = B.CLA_ID
		INNER JOIN LMS_LRM.CM_OPT_TCR C		ON B.OPT_TXB_ID = C.OPT_TXB_ID
		INNER JOIN LMS_LRM.CM_USR D			ON C.TCR_USR_ID = D.USR_ID
		WHERE A.CLA_ID = #{claId}
		AND B.OPT_TXB_ID = #{optTxbId}
		AND D.USR_TP_CD = 'TE'
	</select>
	
	<!-- 2024-07-10 학급목록 조회(KERIS API로 학급목록 가공) -->
	<select id="selectClaInfoList" parameterType="com.aidt.api.bc.cm.dto.BcClaListDto" resultType="com.aidt.api.bc.cm.dto.BcClaListDto">
		/** BcCm-Mapper.xml - selectClaInfoList */
		SELECT
			C.OPT_TXB_ID 			/* 운영교과서ID */
			, D.CLA_ID 				/* 학급 ID */
			, D.SGY 				/* 학년 */
			, D.CLA_NM /* 반명 */
			, D.CLA_NO				/* 반 */
			, D.SCHL_CD				/* 학교코드(학교ID) */
			, '학교명' AS SCHL_NM 	/* 학교명 */
		FROM
			LMS_LRM.CM_USR A
		INNER JOIN LMS_LRM.CM_OPT_TCR B ON A.USR_ID = B.TCR_USR_ID
		INNER JOIN LMS_LRM.CM_OPT_TXB C ON B.OPT_TXB_ID = C.OPT_TXB_ID
		INNER JOIN LMS_LRM.CM_CLA D		ON C.CLA_ID = D.CLA_ID
		WHERE A.USR_ID = #{usrId}
		AND A.USR_TP_CD = 'TE'
	</select>

	<!-- 학생 로그인시 UUID로 학급 구성 여부 조회 -->
	<select id="selectClaAndOptTxbCheck" parameterType="string" resultType="int">
		/** BcCm-Mapper.xml - selectClaAndOptTxbCheck */
		SELECT
			COUNT(*)
		FROM LMS_LRM.CM_USR A
		INNER JOIN LMS_LRM.CM_CLA B ON A.CLA_ID = B.CLA_ID
		INNER JOIN LMS_LRM.CM_OPT_TXB C ON B.CLA_ID = C.CLA_ID
		WHERE A.USR_ID = #{userId}
		AND A.USR_TP_CD = 'ST'
	</select>
	
	
	<!-- 표준체계ID별 데이터 저장(전입 / 전출에도 사용) -->
	<insert id="upsertNtlvEduCrsStnData" parameterType="com.aidt.api.at.token.dto.KerisLrnDataUpsertDto">
		/** BcCm-Mapper.xml - upsertKerisStuLrnInfo */
		INSERT INTO LMS_LRM.CM_NTLV_EDU_CRS_STN_SST 
		(USR_ID, CRCL_CTN_ELM2_CD, EDU_CRS_ACH_BS_CD, PGRS_RT, DEVR_LRMP_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES (
			#{usrId}
			, #{stnSstEduCrsId}
			, #{eduCrsAchBsCd}
			, #{pgrsRt}
			, #{devrLrmpId}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
		ON DUPLICATE KEY UPDATE
			EDU_CRS_ACH_BS_CD = VALUES(EDU_CRS_ACH_BS_CD),
			PGRS_RT = CASE WHEN PGRS_RT IS NULL THEN VALUES(PGRS_RT) ELSE PGRS_RT END,
			DEVR_LRMP_ID = VALUES(DEVR_LRMP_ID),
			MDF_DTM = NOW()
	</insert>
	
	
	<!-- 학생 로그인시 전입 UPSERT -->
	<insert id="upsertKerisStuLrnInfo" parameterType="com.aidt.api.at.token.dto.KerisLrnDataUpsertDto">
		/** BcCm-Mapper.xml - upsertKerisStuLrnInfo */
		INSERT INTO LMS_LRM.CM_NTLV_EDU_CRS_STN_SST 
		(USR_ID, CRCL_CTN_ELM2_CD, EDU_CRS_ACH_BS_CD, PGRS_RT, DEVR_LRMP_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES (
			#{usrId}
			, #{stnSstEduCrsId}
			, #{eduCrsAchBsCd}
			, #{pgrsRt}
			, #{devrLrmpId}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
		ON DUPLICATE KEY UPDATE
			EDU_CRS_ACH_BS_CD = VALUES(EDU_CRS_ACH_BS_CD),
			PGRS_RT = CASE WHEN PGRS_RT IS NULL THEN VALUES(PGRS_RT) ELSE PGRS_RT END,
			DEVR_LRMP_ID = VALUES(DEVR_LRMP_ID),
			MDF_DTM = NOW()
	</insert>
	
	<select id="selectNtlvEduCrsStnSst" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto" resultType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto">
		/** BcCm-Mapper.xml - selectNtlvEduCrsStnSst */
		SELECT
			 ss.usr_id
			,ss.crcl_ctn_elm2_cd
			,ss.opt_txb_id
		    ,ss.txb_id
		    ,ss.lrn_str_yn
		    ,ss.ev_scr
		    ,ss.lrn_cmpl_yn
		    ,ss.edu_crs_ach_bs_cd
		    ,ss.pgrs_rt
		FROM
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST ss
		WHERE
			ss.usr_id = #{usrId}
		AND ss.crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
	</select>
	
	<insert id="insertNtlvEduCrsStnSst" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto">
		/** BcCm-Mapper.xml - insertNtlvEduCrsStnSst */
		INSERT INTO LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		(
			 usr_id
			,crcl_ctn_elm2_cd
			,opt_txb_id
			,txb_id
		    ,lrn_str_yn
		    ,lrn_str_dtm
		    ,crtr_id
		    ,crt_dtm
		    ,mdfr_id
		    ,mdf_dtm
		    ,db_id
		)
		VALUES
		(
			 #{usrId}
			,#{crclCtnElm2Cd}
			,#{optTxbId}
			,#{txbId}
		    ,#{lrnStrYn}
		<if test="lrnStrYn != null and lrnStrYn.equals('Y'.toString())">
		    ,NOW()
		</if>
		<if test="lrnStrYn == null or !lrnStrYn.equals('Y'.toString())">
			,NULL
		</if>
		    ,#{crtrId}
		    ,NOW()
		    ,#{mdfrId}
		    ,NOW()
		    ,#{dbId}
		)
		ON DUPLICATE KEY UPDATE 
			 opt_txb_id = #{optTxbId}
			,txb_id = #{txbId}
			,lrn_str_yn = #{lrnStrYn}
		<if test="lrnStrYn != null and lrnStrYn.equals('Y'.toString())">
			,lrn_str_dtm = NOW()
		</if>
		<if test="lrnStrYn == null or !lrnStrYn.equals('Y'.toString())">
			,lrn_str_dtm = NULL
		</if>
			,mdfr_id = #{mdfrId}
			,mdf_dtm = NOW()
	</insert>
	
	<update id="updateNtlvEduCrsStnSstLrnStr" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto">
		/** BcCm-Mapper.xml - updateNtlvEduCrsStnSstLrnStr */
		UPDATE 
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		SET 
			 opt_txb_id = #{optTxbId}
			,txb_id = #{txbId}
			,lrn_str_yn = #{lrnStrYn}
		<if test="lrnStrYn != null and lrnStrYn.equals('Y'.toString())">
			,lrn_str_dtm = NOW()
		</if>
		<if test="lrnStrYn == null or !lrnStrYn.equals('Y'.toString())">
			,lrn_str_dtm = NULL
		</if>
			,mdfr_id = #{mdfrId}
			,mdf_dtm = NOW()
		WHERE
			usr_id = #{usrId}
		AND crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
	</update>
	
	<update id="updateNtlvEduCrsStnSstPgrsRt" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto">
		/** BcCm-Mapper.xml - updateNtlvEduCrsStnSstPgrsRt */
		UPDATE 
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		SET 
			 pgrs_rt = #{pgrsRt}
			,mdfr_id = #{mdfrId}
			,mdf_dtm = NOW()
		WHERE
			usr_id = #{usrId}
		AND crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
	</update>
	
	<update id="updateNtlvEduCrsStnSstEvScr" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto">
		/** BcCm-Mapper.xml - updateNtlvEduCrsStnSstEvScr */
		UPDATE 
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		SET 
			 ev_scr = #{evScr}
			,edu_crs_ach_bs_cd = #{eduCrsAchBsCd}
			,mdfr_id = #{mdfrId}
			,mdf_dtm = NOW()
		WHERE
			usr_id = #{usrId}
		AND crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
	</update>
	
	<select id="selectCrsStnLrnInfoCnt" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo" resultType="int">
		/** BcCm-Mapper.xml - selectCrsStnLrnInfoCnt */
		SELECT
			COUNT(li.crcl_ctn_elm2_cd) AS cnt
		FROM
			LMS_LRM.CM_CRS_STN_LRN_INFO li
		WHERE
			li.crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
		AND li.opt_txb_id = #{optTxbId}
	</select>
	
	<select id="selectCrsStnLrnInfo" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo" resultType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo">
		/** BcCm-Mapper.xml - selectCrsStnLrnInfo */
		SELECT
			 li.qst_cnt
			,li.ctn_cnt
		FROM
			LMS_LRM.CM_CRS_STN_LRN_INFO li
		WHERE
			li.crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
		AND li.opt_txb_id = #{optTxbId}
	</select>
	
	<select id="selectCrsStnLrnInfoQstCnt" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo" resultType="Long">
		/** BcCm-Mapper.xml - selectCrsStnLrnInfoQstCnt */
		SELECT
			IFNULL((
				SELECT 
					 COUNT(*) AS qtn_cnt
				FROM 
					LMS_LRM.EA_EV E    
					INNER JOIN LMS_CMS.bc_evsh bce 
						ON bce.EVSH_ID=E.EVSH_ID  
						AND bce.DEL_YN='N'
					INNER JOIN LMS_LRM.EA_EV_QTM EQ 
						ON EQ.EV_ID = E.EV_ID
			  		INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM2 
			  			ON EQ.QTM_ID = BNCM2.CTN_ID 
			  			AND BNCM2.CRCL_CTN_TP_CD = 'EX'
				WHERE 
					E.OPT_TXB_ID = #{optTxbId}
				AND E.EV_DV_CD = 'SE'
				AND E.EV_DTL_DV_CD IN ('FO','TO')
		     	AND E.USE_YN = 'Y'
		        AND BNCM2.CRCL_CTN_ELM2_CD IS NOT NULL
		        AND BNCM2.CRCL_CTN_ELM2_CD = #{crclCtnElm2Cd}
				GROUP BY BNCM2.CRCL_CTN_ELM2_CD
			), 0) AS qtn_cnt
	</select>
	
	<select id="selectCrsStnLrnInfoCtnCnt" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo" resultType="Long">
		/** BcCm-Mapper.xml - selectCrsStnLrnInfoCtnCnt */
		SELECT
			IFNULL((
				SELECT
					COUNT(ar.opt_txb_id) AS ctn_cnt
				FROM
					LMS_LRM.TL_SBC_LRN_ATV_RCSTN ar
				    INNER JOIN LMS_CMS.BC_LRN_STP ls
						ON ls.lrn_stp_id = ar.lrn_stp_id
						AND ls.lrmp_nod_id = ar.lrmp_nod_id
				        AND ls.del_yn = 'N'
					INNER JOIN LMS_CMS.BC_CTN_MTD cm
						ON cm.lrn_atv_id = ar.lrn_atv_id
				        AND cm.use_yn = 'Y'
				        AND cm.del_yn = 'N'
				        AND cm.ctn_tp_cd = 'HT'
					INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 mv
						ON mv.ctn_id = cm.lrn_atv_id
				        AND mv.crcl_ctn_tp_cd = 'TL'
				        AND mv.crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
				        AND mv.del_yn = 'N'
				WHERE
					ar.opt_txb_id = #{optTxbId}
				GROUP BY
					 ar.opt_txb_id
				    ,mv.crcl_ctn_elm2_cd
			), 0) AS ctn_ctn
	</select>
	
	<insert id="insertCrsStnLrnInfo" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo">
		/** BcCm-Mapper.xml - insertCrsStnLrnInfo */
		INSERT INTO LMS_LRM.CM_CRS_STN_LRN_INFO
		(
			 crcl_ctn_elm2_cd
			,opt_txb_id
			,txb_id
		    ,qst_cnt
		    ,ctn_cnt
		)
		VALUES
		(
			 #{crclCtnElm2Cd}
			,#{optTxbId}
			,#{txbId}
			,#{qstCnt}
		    ,#{ctnCnt}
		)
		ON DUPLICATE KEY UPDATE txb_id  = #{txbId}
							  , qst_cnt = #{qstCnt}
							  , ctn_cnt = #{ctnCnt}
	</insert>
	
	<select id="selectNtlvEduCrsStnSstPgrsRt" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto" resultType="Long">
		/** BcCm-Mapper.xml - selectNtlvEduCrsStnSstPgrsRt */
		SELECT 
			CASE WHEN COUNT(1) = 0 THEN 100 ELSE CEIL(SUM(IF(S.LRN_ST_CD = 'CL', 1, 0)) / COUNT(1) * 100) END AS RATE
		FROM 
			LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
            INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
             	ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
            	AND A.LRN_STP_ID = B.LRN_STP_ID
            	AND B.DEL_YN = 'N'
            INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                ON A.LRN_ATV_ID = D.LRN_ATV_ID
               	AND D.USE_YN= 'Y'
               	AND D.DEL_YN= 'N'
            INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 E /* BC_국가수준교육과정컨텐츠매핑 */
                ON E.CTN_ID = D.LRN_ATV_ID
               	AND E.CRCL_CTN_TP_CD = 'TL'  -- 교과
               	AND E.DEL_YN = 'N'
              	AND A.OPT_TXB_ID = #{optTxbId}
              	AND E.CRCL_CTN_ELM2_CD = #{crclCtnElm2Cd}
              	AND D.CTN_TP_CD='HT'
         	LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST S  /* TL_교과학습활동상태 */
             	ON S.OPT_TXB_ID = A.OPT_TXB_ID
                AND S.LRMP_NOD_ID = A.LRMP_NOD_ID
                AND S.LRN_ATV_ID = A.LRN_ATV_ID
                AND S.LRN_USR_ID = #{lrnUsrId}
           	GROUP BY E.CRCL_CTN_ELM2_CD, A.OPT_TXB_ID
	</select>
	
	<select id="saveNtlvEduCrsStnSstLrnCompCheck" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto" resultType="String">
		/** BcCm-Mapper.xml - saveNtlvEduCrsStnSstLrnCompCheck */
		SELECT
			CASE
				WHEN r.qst_check = 'Y' AND r.pgrs_check = 'Y' THEN 'Y'
	            ELSE 'N'
			END AS comp_yn
		FROM
			(
				SELECT
					  CASE
						WHEN IFNULL(li.qst_cnt, 0) = 0 OR ss.ev_scr IS NOT NULL THEN 'Y'
						ELSE 'N'
					  END AS qst_check
					 ,CASE
						WHEN IFNULL(li.ctn_cnt, 0) = 0 OR IFNULL(ss.pgrs_rt, 0) > 99 THEN 'Y'
						ELSE 'N'
					 END AS pgrs_check
				FROM
					LMS_LRM.CM_NTLV_EDU_CRS_STN_SST ss
					LEFT OUTER JOIN LMS_LRM.CM_CRS_STN_LRN_INFO li
						ON li.crcl_ctn_elm2_cd = ss.crcl_ctn_elm2_cd
						AND li.opt_txb_id = ss.opt_txb_id
				WHERE
					ss.usr_id = #{usrId}
				AND ss.crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
			) r
	</select>
	
	<update id="updateNtlvEduCrsStnSstLrnCmpl" parameterType="com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto">
		/** BcCm-Mapper.xml - updateNtlvEduCrsStnSstLrnCmpl */
		UPDATE 
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		SET 
			 lrn_cmpl_yn = #{lrnCmplYn}
		<if test="lrnCmplYn != null and lrnCmplYn.equals('Y'.toString())">
			,lrn_cmpl_dtm = NOW()
		</if>
		<if test="lrnCmplYn == null or !lrnCmplYn.equals('Y'.toString())">
			,lrn_cmpl_dtm = NULL
		</if>
			,mdfr_id = #{mdfrId}
			,mdf_dtm = NOW()
		WHERE
			usr_id = #{usrId}
		AND crcl_ctn_elm2_cd = #{crclCtnElm2Cd}
	</update>
	
	<insert id="insertKerisConnErr" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo">
		/** BcCm-Mapper.xml - insertKerisConnErr */
		INSERT INTO LMS_LRM.CM_KERIS_CONN_ERR
		(
			 req_log_id
			,lrn_usr_id
			,opt_txb_id
			,conn_url
			,prnr_id
			,api_ver
		    ,body_cn
		    ,rs_cd
		    ,rs_msg
		    ,err_cd
		    ,err_trms_id
		    ,err_trms_st_cd
		    ,reg_dtm
		)
		VALUES
		(
			 #{reqLogId}
			,#{lrnUsrId}
			,#{optTxbId}
			,#{connUrl}
			,#{prnrId}
			,#{apiVer}
		    ,#{bodyCn}
		    ,#{rsCd}
		    ,#{rsMsg}
		    ,#{errCd}
		    ,#{errTrmsId}
		    ,#{errTrmsStCd}
		    ,NOW()
		)
	</insert>
	
	<insert id="insertKerisConnLog" parameterType="com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo">
		/** BcCm-Mapper.xml - insertKerisConnLog */
		INSERT INTO LMS_LRM.CM_KERIS_CONN_LOG
		(
			 req_log_id
			,usr_id
			,crcl_ctn_elm2_cd
			,lrn_usr_id
			,opt_txb_id
			,act_cd
			,rs_yn
			,rs_cd
			,rs_msg
			,rs_body_cn			
			,conn_url
			,prnr_id
			,api_ver
		    ,body_cn
		    ,reg_dtm
		)
		VALUES
		(
			 #{reqLogId}
			,#{usrId}
			,#{crclCtnElm2Cd}
			,#{lrnUsrId}
			,#{optTxbId}
			,#{actCd}
			,#{rsYn}
			,#{rsCd}
			,#{rsMsg}
			,#{rsBodyCn}
			,#{connUrl}
			,#{prnrId}
			,#{apiVer}
		    ,#{bodyCn}
		    ,NOW()
		)
	</insert>
	
	<insert id="insertKerisFrontReqLog" parameterType="com.aidt.api.bc.cm.dto.BcCmKerisFrontReqLogDto" useGeneratedKeys="true" keyProperty="logId">
		/** BcCm-Mapper.xml - insertKerisFrontReqLog */
		INSERT INTO lms_lrm.cm_keris_front_req_log
		(
			 usr_id
			,crcl_ctn_elm2_cd
			,lrn_usr_id
			,opt_txb_id
			,act_cd
		    ,rs_yn
		    ,rs_msg
		    ,reg_dtm
		)
		VALUES
		(
			 #{usrId}
			,#{crclCtnElm2Cd}
			,#{lrnUsrId}
			,#{optTxbId}
			,#{actCd}
		    ,#{rsYn}
		    ,#{rsMsg}
		    ,NOW()
		)
	</insert>

	<!-- 학생 전출 시 삭제 처리, opt_txb_id, cla_id 앞에 "del_" prefix 처리 -->
	<update id="deleteKerisLrnOutUsr" parameterType="string">
		UPDATE LMS_LRM.CM_USR CU
		SET CU.CLA_ID = CONCAT('del_', CU.CLA_ID)
		,CU.MDF_DTM = now()
		WHERE CU.USR_ID = #{userId}
		AND CU.CLA_ID NOT LIKE 'del_%'
	</update>
	<update id="deleteKerisLrnOutToken" parameterType="string">
		UPDATE LMS_LRM.CM_TOKEN CT
		SET CT.CLA_ID = CONCAT('del_', CT.CLA_ID)
		,CT.OPT_TXB_ID = CONCAT('del_', CT.OPT_TXB_ID)
		WHERE CT.USR_ID = #{userId}
		AND CT.CLA_ID NOT LIKE 'del_%'
		AND CT.OPT_TXB_ID NOT LIKE 'del_%'
	</update>

	<update id="updateStudentName" parameterType="Map">
		UPDATE LMS_LRM.CM_USR CU
		SET CU.USR_NM = #{userName}
		WHERE CU.USR_ID = #{userId}
	</update>

	<resultMap id="KerisLrnDataResultMap" type="com.aidt.api.at.token.dto.KerisLrnDataDto">
        <result property="curriculum" column="STN_SST_EDU_CRS_ID"/>
        <result property="achievement_level" column="EDU_CRS_ACH_BS_CD"/>
        <result property="percent" column="PGRS_RT"/>
    </resultMap>
	
	<!-- 학생 로그인 후 전출데이터 추출 -->
	<select id="selectCmNtlvEduCrsStnSstList" parameterType="string" resultMap="KerisLrnDataResultMap">
		/** BcCm-Mapper.xml - selectCmNtlvEduCrsStnSstList */
		SELECT 
			CRCL_CTN_ELM2_CD AS STN_SST_EDU_CRS_ID
			, EDU_CRS_ACH_BS_CD
			, PGRS_RT
		FROM 
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		WHERE USR_ID = #{usrId}
		ORDER BY CRT_DTM
	</select>
	

	<!-- 알림 표시 여부 조회 -->
	<select id="selectCheckInfm" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectCheckInfm */
		SELECT COUNT(*) FROM LMS_LRM.CM_INFM
		WHERE OPT_TXB_ID = #{optTxbId}
		AND INFM_OBJ_USR_ID = #{usrId}
		AND COFM_YN = 'N'
	</select>

	<!-- 교과서학기코드로 교과서ID 조회 -->
	<select id="selectTxbIdForTxbTermCd" parameterType="string" resultType="string">
		/** BcCm-Mapper.xml - selectTxbIdForTxbTermCd */
		SELECT TXB_ID FROM LMS_CMS.BC_TXB WHERE TXB_CD = #{txbTermCd}
	</select>
	
	<!-- 서브도메인으로 교과서ID 조회 -->
	<select id="selectTxbIdForSubDomain" parameterType="string" resultType="string">
		/** BcCm-Mapper.xml - selectTxbIdForSubDomain */
		SELECT TXB_ID FROM LMS_CMS.BC_TXB WHERE TXB_SUB_URL = #{subDomain}
	</select>
	
	<!-- 서브도메인으로 파트너ID 조회 -->
	<select id="selectPartnerId" parameterType="string" resultType="string">
		/** BcCm-Mapper.xml - selectTxbIdForSubDomain */
		SELECT PRNR_ID FROM LMS_CMS.BC_TXB WHERE TXB_SUB_URL = #{subDomain}
	</select>

	<!-- 사용자 등록 조회 -->
	<select id="selectCheckCmUser" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectCheckCmUser */
		SELECT COUNT(*) FROM LMS_LRM.CM_USR WHERE USR_ID = #{usrId}
	</select>

	<!-- 사용자 등록 -->
<!--	<insert id="insertCmUsr" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertCmUsr */
		INSERT INTO LMS_LRM.CM_USR(
			USR_ID, LRNR_VEL_TP_CD, USR_TP_CD, FST_REG_DTM, USR_ST_CD
			, CLA_ID, FLN_ST_CD, NTR_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{usrId}
			, 'NM'
			, #{usrTpCd}
			, NOW()
			, '1'
			, #{claId}
			, 'SN'
			, 'N'
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
	</insert>-->

	<!-- 사용자 등록 -->
	<insert id="insertCmUsr" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertCmUsr */
		INSERT INTO LMS_LRM.CM_USR(
			USR_ID, KERIS_USR_ID, USR_NM, LRNR_VEL_TP_CD, USR_TP_CD, STU_NO, FST_REG_DTM, USR_ST_CD
		  , CLA_ID, FLN_ST_CD, NTR_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
				#{usrId}
				 , #{kerisUsrId}
				 , #{usrNm}
				 , 'NM'
				 , #{usrTpCd}
				 , #{userNumber}
				 , NOW()
				 , '1'
				 , #{claId}
				 , 'SN'
				 , 'N'
				 , #{usrId}
				 , NOW()
				 , #{usrId}
				 , NOW()
				 , #{dbId}
				 )
	</insert>

	<!-- 학급 정보 조회 -->
	<select id="selectCheckCmCla" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectCheckCmCla */
		SELECT COUNT(*) FROM LMS_LRM.CM_CLA WHERE CLA_ID = #{claId}
		AND DB_ID = #{dbId}
	</select>
	<!-- 학급 정보 조회 : 책임교사 정보  -->
	<select id="selectCheckCmClaGetChgTcrUsrId" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="String">
		/** BcCm-Mapper.xml - selectCheckCmClaGetChgTcrUsrId */
		SELECT ifnull(CHG_TCR_USR_ID,'N') FROM LMS_LRM.CM_CLA WHERE CLA_ID = #{claId}
	</select>

	<!-- 학급 정보 등록 -->
	<insert id="insertCmCla" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertCmCla */
		INSERT INTO LMS_LRM.CM_CLA(
			CLA_ID, SCHL_CD, SGY, CLA_NO, SCHL_NM, SCHL_GRD_CD, CLA_NM, 
			CLASSROOM_NM, SBJ_NM, CLASS_PERIOD, DAY_CD,
			CHG_TCR_USR_ID, KERIS_CLA_CD, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) 
		select
			#{claId}
			,#{schlCd}
		<if test="sgy == null">
			, case when CONVERT(SGY_CD, UNSIGNED)=0 then 1 else CONVERT(SGY_CD, UNSIGNED) end as SGY
		</if>
		<if test="sgy != null">
			, #{sgy}
		</if>
			, IFNULL(#{claNo}, '1')
			, #{schoolName}
			, SCHL_GRD_CD
			, IFNULL(#{claNm}, '1')
			, IFNULL(#{classroomName}, '-반')
			, #{subjectName}
			, #{classPeriod}
			, #{dayWeek}
			, #{chgTcrUsrId}
			, #{kerisClaCd}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		from lms_cms.bc_txb where TXB_ID=#{txbId}
	</insert>
	
	<!-- 학급 책임교사 업데이트  -->
	<update id="updateCmClaChgTcrUsrId" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - updateCmClaChgTcrUsrId */
		UPDATE  LMS_LRM.CM_CLA SET 	CHG_TCR_USR_ID=#{chgTcrUsrId} WHERE CLA_ID = #{claId}
		AND DB_ID = #{dbId}
	</update>
	
	<!-- 학급 이름 업데이트  -->
	<update id="updateCmClaChgClassroomName" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - updateCmClaChgClassroomName */
		UPDATE LMS_LRM.CM_CLA 
		SET CLASSROOM_NM = CONCAT(sgy, '학년 ', 
		                          CASE 
		                            WHEN RIGHT(#{classroomName}, 1) = '반' 
		                            THEN #{classroomName} 
		                            ELSE CONCAT(#{classroomName}, '반') 
		                          END)
		WHERE CLA_ID = #{claId}
		AND DB_ID = #{dbId};
	</update>
	
	<!-- 학급 정보 조회 : 학급 이름 일치 여부  -->
	<select id="selectCheckCmClaNmYn" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="String">
	/** BcCm-Mapper.xml - selectCheckCmClaNmYn */
  		SELECT 
		  CASE 
		    WHEN CLASSROOM_NM = 
		         CONCAT(sgy, '학년 ', 
		                CASE 
		                  WHEN RIGHT(#{classroomName}, 1) = '반' 
		                  THEN #{classroomName} 
		                  ELSE CONCAT(#{classroomName}, '반') 
		                END
		         ) 
		    THEN 'Y'
		    ELSE 'N'
		  END AS MATCH_RESULT
		FROM 
		  LMS_LRM.CM_CLA
		WHERE 
		  CLA_ID = #{claId};
	</select>

	<!-- 운영교과서 정보 조회 -->
	<select id="selectCheckCmOptTxb" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectCheckCmOptTxb */
		SELECT COUNT(*) FROM LMS_LRM.CM_OPT_TXB
		WHERE CLA_ID = #{claId}
		AND OPT_TXB_ID = #{optTxbId}
		AND DB_ID = #{dbId}
	</select>

	<!-- 운영교과서 정보 등록 -->
	<insert id="insertCmOptTxb" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertCmOptTxb */
		INSERT INTO LMS_LRM.CM_OPT_TXB(
			OPT_TXB_ID, TXB_ID, CLA_ID, KERIS_LECT_CD, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{optTxbId}
			, #{txbId}
			, #{claId}
			, #{kerisLectCd}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 운영교사 정보 조회 -->
	<select id="selectCheckCmOptTcr" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectCheckCmOptTcr */
		SELECT COUNT(*) FROM LMS_LRM.CM_OPT_TCR
		WHERE TCR_USR_ID = #{usrId}
		AND OPT_TXB_ID = #{optTxbId}
	</select>

	<!-- 운영교사 정보 등록 -->
	<insert id="insertCmOptTcr" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertCmOptTcr */
		INSERT INTO LMS_LRM.CM_OPT_TCR(
			OPT_TXB_ID, TCR_USR_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{optTxbId}
			, #{usrId}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
	</insert>


	<!-- 원클릭 학습설정 - 기능 사용 설정 조회 -->
	<select id="selectCmFncUseSetm" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectCmFncUseSetm */
		SELECT COUNT(*) FROM LMS_LRM.CM_FNC_USE_SETM
		WHERE OPT_TXB_ID = #{optTxbId}
	</select>


	<!-- 원클릭 학습설정 - 기능 사용 설정 등록 -->
	<insert id="insertCmFncUseSetm" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertCmFncUseSetm */
		INSERT INTO LMS_LRM.CM_FNC_USE_SETM(
			OPT_TXB_ID, MYHM_PNT_USE_YN, PRAS_STMP_USE_YN, DILG_USE_YN, CLA_BLBD_WRT_PMSN_YN, CLA_BLBD_UCWR_PMSN_YN, DIY_EV_USE_YN, LRN_RPT_USE_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES(
			#{optTxbId}
			, 'Y'
			, 'Y'
			, 'Y'
			, 'N'
			, 'N'
			, 'N'
			, 'N'
			, 'System'
			, NOW()
			, 'System'
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 메뉴 이동시마다 실시간 모니터링에 해당 메뉴 update -->
	<update id="updateStuCurLrnStMenu" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/** BcCm-Mapper.xml - updateStuCurLrnStMenu */
		UPDATE LMS_LRM.CM_STU_CUR_LRN_ST
		<set>
			<if test="curLrnMenuId != null">
				CUR_LRN_MENU_ID = #{curLrnMenuId}
			</if>
			,CUR_LRN_MENU_NM = #{curLrnMenuNm}
			,LRN_TP_CD = #{lrnTpCd}
			,LU_NOD_ID = #{luNodId}
			,TC_NOD_ID = #{tcNodId}
			,LRN_ATV_ID = #{lrnAtvId}
			,MDFR_ID = #{lrnUsrId}
			,MDF_DTM = NOW()
			,CUR_CONN_YN = 'Y'
		</set>
		WHERE
		LRN_USR_ID = #{lrnUsrId}
	</update>


	<!-- 학생 목록 API 조회 후 해당 데이터 등록 여부 조회 -->
	<select id="selectCheckSlppStu" parameterType="com.aidt.api.bc.cm.dto.BcSlppStuListDto" resultType="int">
		/** BcCm-Mapper.xml - selectCheckSlppStu */
		SELECT COUNT(*) FROM LMS_LRM.CM_CNV_STU_LST
		WHERE TPC_ID = #{tpcId}
		AND TXB_ID = #{txbId}
		AND TCR_USR_ID = #{tcrUsrId}
		AND STU_USR_ID = #{stuUsrId}
		AND CLA_ID = #{claId}
	</select>

	<!-- 대화학생 정보 등록 -->
	<insert id="insertSlppStu" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - insertSlppStu */
		INSERT INTO LMS_LRM.CM_CNV_STU_LST(
			TPC_ID, TBX_ID, TCR_USR_ID, STU_USR_ID, CLA_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{tpcId}
			, #{txbId}
			, #{tcrUsrId}
			, #{stuUsrId}
			, #{claId}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{txbId}
		)
	</insert>

	<!-- 메뉴 조회 -->
	<select id="selectMenu" parameterType="com.aidt.api.bc.cm.dto.MenuDto" resultType="com.aidt.api.bc.cm.dto.MenuDto">
		/** BcCm-Mapper.xml - selectMenu */
		SELECT
			MENU_ID,
			MENU_USR_TP_CD,
			MENU_NM,
			FLE_PTH_NM,
			FLE_NM,
			URNK_MENU_ID,
			SRT_ORDN,
			USE_YN,
			DEL_YN,
			DATE_FORMAT(CRT_DTM, '%Y-%m-%d') AS CRT_DTM,
			URNK_MENU_ID AS PARENT_MENU_ID,
			FLE_PTH_NM AS FILE_PATH,
			FLE_NM AS FILE_NM,
			SRT_ORDN AS MENU_ORDER
		FROM
			LMS_LRM.CM_MENU
		WHERE 1=1
		AND USE_YN = 'Y'
		AND DEL_YN = 'N'
		AND MENU_USR_TP_CD = #{menuUsrTpCd}
		<if test = 'urnkMenuId != null and !"".equals(urnkMenuId)'>
			AND URNK_MENU_ID = #{urnkMenuId}
		</if>
		<if test = 'txbId != null and !"".equals(txbId)'>
			AND TXB_ID = #{txbId}
		</if>
		ORDER BY URNK_MENU_ID, SRT_ORDN
	</select>

	<!-- 첨부파일 조회 -->
	<select id="selectAnnxFle" parameterType="Long" resultType="com.aidt.api.bc.cm.dto.BcAnnxFleDto">
		/** BcCm-Mapper.xml - selectFile */
		SELECT
			   B.ANNX_ID
			 , B.ANNX_FLE_ID
			 , B.SRT_ORDN
			 , B.DOC_VI_ID
			 , B.ANNX_FLE_NM
			 , B.ANNX_FLE_ORGL_NM
			 , B.ANNX_FLE_FEXT_NM
			 , B.ANNX_FLE_SZE
			 , B.ANNX_FLE_PTH_NM
			 , B.CRTR_ID
			 , B.DB_ID
		FROM
			LMS_LRM.CM_ANNX_FLE B
		WHERE
			B.USE_YN = 'Y'
		  AND B.ANNX_FLE_ID = #{annxFleId}
	</select>

	<!-- 첨부파일 목록 조회 -->
	<select id="selectFileList" parameterType="String" resultType="com.aidt.api.bc.cm.dto.BcAnnxFleDto">
		/** BcCm-Mapper.xml - selectFileList */
		SELECT
			A.ANNX_ID
			, B.ANNX_FLE_ID
			, B.SRT_ORDN
			, B.DOC_VI_ID
			, B.ANNX_FLE_NM
			, B.ANNX_FLE_ORGL_NM
			, B.ANNX_FLE_FEXT_NM
			, B.ANNX_FLE_SZE
			, B.ANNX_FLE_PTH_NM
		FROM
			LMS_LRM.CM_ANNX A
		INNER JOIN LMS_LRM.CM_ANNX_FLE B ON A.ANNX_ID = B.ANNX_ID
										 AND B.USE_YN = 'Y'
		WHERE
			A.USE_YN = 'Y'
		AND A.ANNX_ID = #{annxId}
	</select>
	
	
	<!-- 첨부파일 ANNX_ID 조회(평가 - 노트 > 첨부파일 ANNX_ID 조회) -->
	<select id="selectNoteFileID" parameterType="com.aidt.api.bc.cm.dto.BcCmSelectNoteFileReqDto" resultType="com.aidt.api.bc.cm.dto.BcCmSelectNoteFileResDto">
		/** BcCm-Mapper.xml - selectNoteFileID */	 
		SELECT
		  	AN.ANNX_ID	AS annxId 				-- 첨부ID
		  	, ANFILE.ANNX_FLE_ID  AS annxFileId	-- 첨부파일ID
		  	
		FROM lms_lrm.EA_EV_QTM_ANW AS QANW
		INNER JOIN lms_lrm.cm_annx_fle AS ANFILE on  ANFILE.ANNX_FLE_ID = QANW.ANNX_FLE_ID
		INNER JOIN lms_lrm.cm_annx AS AN on AN.ANNX_ID = ANFILE.ANNX_ID
		WHERE QANW.USR_ID = #{userId}
		AND QANW.EV_ID = #{evId}
		AND QANW.QTM_ID = #{qtmId}
	</select>
	
	
	<select id="selectAnnxFleDeleteList" parameterType="Long[]" resultType="com.aidt.api.bc.cm.dto.BcAnnxFleDto">
		/** BcCm-Mapper.xml - selectAnnxFleDeleteList */
		SELECT
			 af.annx_fle_id
			,af.annx_id
		    ,af.annx_fle_pth_nm
		FROM
			lms_lrm.cm_annx_fle af
		WHERE
			af.annx_fle_id IN 
		<foreach item="deleteAnnxFleId" index="index" collection="array" open="(" separator="," close=")">
    		#{deleteAnnxFleId}
  		</foreach>
	</select>

	<!-- 사용자 정보 조회 (학생) -->
	<select id="selectStuUser" parameterType="com.aidt.api.bc.cm.dto.BcLoginDto" resultType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - selectStuUser */
		SELECT
			A.USR_TP_CD						-- 사용자유형코드
			, A.USR_NM AS USER_NAME				-- 사용자명
			, C.SCHL_NM AS SCHOOL_NAME			-- 학교 이름
			, C.SCHL_CD AS USER_DIVISION		-- 학교코드
			, C.SCHL_GRD_CD						-- 학교급코드(E:초등, M:중등, H:고등)
			, (
				SELECT CD.CM_CD_NM FROM LMS_LRM.CM_CM_CD CD WHERE CD.URNK_CM_CD = 'SCHL_GRD_CD' AND CD.CM_CD = C.SCHL_GRD_CD
			) AS SCHL_GRD_CD_NM					-- 학교급코드명
			, CASE WHEN C.SGY=0 THEN 1 ELSE C.SGY END AS USER_GRADE -- 학년
			, C.CLA_NO AS USER_CLASS			-- 반
			, c.CHG_TCR_USR_ID 				-- 담임교사ID
			, (
				 SELECT USR_NM FROM  LMS_LRM.CM_USR where USR_ID=  c.CHG_TCR_USR_ID 
			) AS CHG_TCR_USR_NM					-- 담임교사명
			, '요일' AS DAY_WEEK					-- 요일(1:월, 2:화, 3:수, 4:목, 5:금)
			, '교시' AS CLASS_PERIOD				-- 교시
			, C.CLASSROOM_NM AS CLASSROOM_NAME		-- 교실
			, A.USR_GND_CD AS USER_GENDER		-- 학생 성별(1:남학생, 2:여학생)
			, A.CLA_ID AS CLASS_CODE 			-- 학급 코드
			, D.OPT_TXB_ID
			, D.TXB_ID
			, A.USR_ID AS TEMP_USR_ID
			, A.USR_ID
			, E.SBJ_CD							-- 과목 코드
			, E.TXB_NM AS SUBJECT_NAME   -- 과목명				-- 과목명
			, F.DILG_USE_YN AS SLPP_USE_YN		-- 대화사용여부
			, A.FLN_ST_CD						-- 감정상태
			, A.USR_ID AS uuid
			, E.TRM_DV_CD						-- 학기구분코드(00:공통, 01:1학기, 02:2학기)
			, #{subDomain} AS SUB_DOMAIN		-- 운영URL(서브도메인 : 교과서학기코드 및 교과서코드, 교과서ID 추출 가능)
			, A.KERIS_TERM_AGR_YN
		FROM
		 LMS_LRM.CM_OPT_TXB D		
		INNER JOIN LMS_LRM.CM_CLA C			ON D.CLA_ID = C.CLA_ID
		INNER  JOIN LMS_CMS.BC_TXB E 	ON D.TXB_ID = E.TXB_ID
		LEFT OUTER JOIN LMS_LRM.CM_FNC_USE_SETM F ON D.OPT_TXB_ID = F.OPT_TXB_ID
		JOIN
		(SELECT * FROM LMS_LRM.cm_usr A WHERE  USR_ID =  #{usrId}) A
		WHERE D.OPT_TXB_ID =#{optTxbId} AND C.CLA_ID = #{claId}
	</select>

	<select id="selectUserKerisId" parameterType="string" resultType="string">
		/** BcCm-Mapper.xml - selectUserKerisId */
		SELECT cu.KERIS_USR_ID FROM LMS_LRM.CM_USR cu WHERE USR_ID = #{usrId}
	</select>



	<!-- 사용자 정보 조회 (교사) -->
	<select id="selectTcrUser" parameterType="com.aidt.api.bc.cm.dto.BcLoginDto" resultType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - selectTcrUser */
		SELECT
			B.USR_TP_CD						-- 사용자유형코드
			, B.USR_NM AS USER_NAME				-- 사용자명
			, E.SCHL_NM AS SCHOOL_NAME			-- 학교 이름
			, E.SCHL_CD AS USER_DIVISION		-- 학교코드
			, E.SCHL_GRD_CD						-- 학교급코드(E:초등, M:중등, H:고등)
			, (
				SELECT CD.CM_CD_NM FROM LMS_LRM.CM_CM_CD CD WHERE CD.URNK_CM_CD = 'SCHL_GRD_CD' AND CD.CM_CD = E.SCHL_GRD_CD
			) AS SCHL_GRD_CD_NM					-- 학교급코드명
			,   CASE WHEN E.SGY=0 THEN 1 ELSE E.SGY END
			AS USER_GRADE				-- 학년
			, E.CLA_NO AS USER_CLASS			-- 반
			, E.CHG_TCR_USR_ID					-- 담임교사ID
			, (
				'담임교사명'
			) AS CHG_TCR_USR_NM					-- 담임교사명
			, '요일' AS DAY_WEEK					-- 요일(1:월, 2:화, 3:수, 4:목, 5:금)
			, '교시' AS CLASS_PERIOD				-- 교시
			, E.CLASSROOM_NM AS CLASSROOM_NAME		-- 교실
			, B.USR_GND_CD AS USER_GENDER				-- 학생 성별(1:남학생, 2:여학생)
			, E.CLA_ID AS CLASS_CODE 			-- 학급 코드
			, D.OPT_TXB_ID
			, D.TXB_ID
			, B.USR_ID AS TEMP_USR_ID
			, B.USR_ID
			, F.SBJ_CD							-- 과목 코드
			, F.TXB_NM AS SUBJECT_NAME   -- 과목명
			, G.DILG_USE_YN AS SLPP_USE_YN		-- 대화사용여부
			, B.FLN_ST_CD						-- 감정상태코드
			, B.USR_ID AS uuid
			, F.TRM_DV_CD						-- 학기구분코드(00:공통, 01:1학기, 02:2학기)
			, #{subDomain} AS SUB_DOMAIN		-- 운영URL(서브도메인 : 교과서학기코드 및 교과서코드, 교과서ID 추출 가능)
			, B.KERIS_TERM_AGR_YN
		FROM
		 LMS_LRM.CM_OPT_TXB D		
		INNER JOIN LMS_LRM.CM_CLA E			ON D.CLA_ID = E.CLA_ID
		INNER  JOIN LMS_CMS.BC_TXB F 	ON D.TXB_ID = F.TXB_ID
		LEFT OUTER JOIN LMS_LRM.CM_FNC_USE_SETM G ON D.OPT_TXB_ID = G.OPT_TXB_ID
		JOIN
		(SELECT * FROM LMS_LRM.cm_usr A WHERE  USR_ID =  #{usrId}) B
		WHERE D.OPT_TXB_ID =#{optTxbId} AND E.CLA_ID = #{claId}
	</select>

	<!-- 첨부 등록 -->
	<insert id="insertAnnx" parameterType="com.aidt.api.bc.cm.dto.BcAnnxFleDto" useGeneratedKeys="true" keyProperty="annxId">
		/** BcCm-Mapper.xml - insertAnnx */
		INSERT INTO LMS_LRM.CM_ANNX(
			USE_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{useYn}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 첨부파일 등록 -->
	<insert id="insertAnnxFle" parameterType="com.aidt.api.bc.cm.dto.BcAnnxFleDto" useGeneratedKeys="true" keyProperty="annxFleId">
		/** BcCm-Mapper.xml - insertAnnxFle */
		INSERT INTO LMS_LRM.CM_ANNX_FLE(
			ANNX_ID, SRT_ORDN, DOC_VI_ID, ANNX_FLE_NM, ANNX_FLE_ORGL_NM, ANNX_FLE_FEXT_NM, ANNX_FLE_SZE, ANNX_FLE_PTH_NM, USE_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{annxId}
			, (SELECT COALESCE(MAX(A.SRT_ORDN), 0) + 1 FROM LMS_LRM.CM_ANNX_FLE A WHERE ANNX_ID = #{annxId})
			, #{docViId}
			, #{annxFleNm}
			, #{annxFleOrglNm}
			, #{annxFleFextNm}
			, #{annxFleSze}
			, #{annxFlePthNm}
			, #{useYn}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>
	
	<!-- 첨부파일 삭제 -->
	<delete id="deleteAnnxFle" parameterType="com.aidt.api.bc.cm.dto.BcAnnxFleDto" >
		/** BcCm-Mapper.xml - deleteAnnxFle */
		DELETE FROM LMS_LRM.CM_ANNX_FLE
		WHERE ANNX_FLE_ID = #{annxFleId}
		AND DB_ID = #{dbId}
	</delete>

	<!-- 공통코드 조회 -->
	<select id="cmCdList" parameterType="com.aidt.api.bc.cm.dto.BcCmCdDto" resultType="hashMap">
		/** BcCmStu-Mapper.xml - cmCdList */
		SELECT CM_CD AS cmCd
			 , CM_CD_NM AS cmCdNm
		  FROM LMS_LRM.CM_CM_CD
		 WHERE URNK_CM_CD = #{urnkCmCd}
		   AND LMS_USE_YN = 'Y'
		   AND DEL_YN     = 'N'
		 ORDER BY SRT_ORDN
	</select>

	<!-- 원클릭 학습 설정 조회 -->
	<select id="selectFncUseSetmInfo" parameterType="com.aidt.api.bc.cm.dto.BcFncUseSetmDto" resultType="com.aidt.api.bc.cm.dto.BcFncUseSetmDto">
		/** BcCmStu-Mapper.xml - selectFncUseSetmInfo */
		SELECT OPT_TXB_ID
			 , MYHM_PNT_USE_YN
			 , PRAS_STMP_USE_YN
			 , DILG_USE_YN
			 , CLA_BLBD_WRT_PMSN_YN
			 , CLA_BLBD_UCWR_PMSN_YN
			 ,IFNULL(DIY_EV_USE_YN, 'Y') as DIY_EV_USE_YN
			,IFNULL(LRN_RPT_USE_YN, 'Y')as LRN_RPT_USE_YN
		  FROM LMS_LRM.CM_FNC_USE_SETM
		 WHERE OPT_TXB_ID = #{optTxbId}
	</select>
	
	
	
	
	<!-- 학습시간 업데이트 -->
	<insert id="insertCmLrnTm" parameterType="com.aidt.api.bc.cm.dto.BcCmLrnTmDto">
		/** BcCm-Mapper.xml - insertCmLrnTm */
		INSERT INTO LMS_LRM.CM_LRN_TM(
			 LRN_YR_DTM, USR_ID, OPT_TXB_ID, 
			 TXB_LRN_TM_SCNT, AI_LRN_TM_SCNT, SP_LRN_TM_SCNT, EV_LRN_TM_SCNT, AI_EV_LRN_TM_SCNT, 
			 AI_WRTNG_LRN_TM,AI_RDNG_LRN_TM,
			 CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES(
			DATE_FORMAT(now(), '%Y%m%d%H')
			,#{usrId}
			,#{optTxbId}
			,#{txbLrnTmScnt}
			,#{aiLrnTmScnt}
			,#{spLrnTmScnt}
			,#{evLrnTmScnt}
			,#{aiEvLrnTmScnt}
			,#{aiWrtngLrnTm}
			,#{aiRdngLrnTm}
			,#{usrId}
			,NOW()
			,#{usrId}
			, NOW()
			,#{dbId}
		)
		ON DUPLICATE KEY UPDATE TXB_LRN_TM_SCNT   = TXB_LRN_TM_SCNT + #{txbLrnTmScnt}
							  , AI_LRN_TM_SCNT    = AI_LRN_TM_SCNT + #{aiLrnTmScnt}
							  , SP_LRN_TM_SCNT    = SP_LRN_TM_SCNT + #{spLrnTmScnt}
							  , EV_LRN_TM_SCNT    = EV_LRN_TM_SCNT + #{evLrnTmScnt}
							  , AI_EV_LRN_TM_SCNT = AI_EV_LRN_TM_SCNT + #{aiEvLrnTmScnt}
							  , AI_WRTNG_LRN_TM   = AI_WRTNG_LRN_TM + #{aiWrtngLrnTm}
							  , AI_RDNG_LRN_TM    = AI_RDNG_LRN_TM + #{aiRdngLrnTm}
							  , MDFR_ID           = #{mdfrId}
							  , MDF_DTM           = NOW()
	</insert>
	<!-- 학습시간 업데이트 -->
	<update id="updateCmLrnTm" parameterType="com.aidt.api.bc.cm.dto.BcCmLrnTmDto">
		/** BcCm-Mapper.xml - updateCmLrnTm */
		UPDATE LMS_LRM.CM_LRN_TM
		SET
			TXB_LRN_TM_SCNT = TXB_LRN_TM_SCNT+#{txbLrnTmScnt}
			, AI_LRN_TM_SCNT = AI_LRN_TM_SCNT+ #{aiLrnTmScnt}
			, SP_LRN_TM_SCNT = SP_LRN_TM_SCNT+  #{spLrnTmScnt}
			, EV_LRN_TM_SCNT = EV_LRN_TM_SCNT + #{evLrnTmScnt}
			, AI_EV_LRN_TM_SCNT = AI_EV_LRN_TM_SCNT+ #{aiEvLrnTmScnt}
			, AI_WRTNG_LRN_TM = AI_WRTNG_LRN_TM+ #{aiWrtngLrnTm}
			, AI_RDNG_LRN_TM = AI_RDNG_LRN_TM+ #{aiRdngLrnTm}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE
			LRN_YR_DTM = DATE_FORMAT(now(), '%Y%m%d%H')
			AND USR_ID = #{usrId}
			AND OPT_TXB_ID = #{optTxbId}
	</update>
	
	
	
	<select id="selectASNCount" parameterType="com.aidt.api.bc.cm.dto.BcUserInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectASNCount */
		SELECT COUNT(*)
		  FROM LMS_LRM.EA_ASN_SMT EGAS -- EA_과제제출
		 INNER JOIN LMS_LRM.EA_ASN EA	-- EA_과제
		    ON EGAS.ASN_ID = EA.ASN_ID
		   AND EGAS.STU_USR_ID =#{usrId}
		   AND EA.OPT_TXB_ID = #{optTxbId}
		   AND EA.DEL_YN = 'N'
		 WHERE EA.ASN_PTME_DV_CD='PT'
		AND EGAS.SMT_CMPL_YN !='Y'
	    AND DATEDIFF( EA.END_DTM,CURDATE())<![CDATA[<=]]>1 AND DATEDIFF( EA.END_DTM,CURDATE()) > 0
	    
	</select>
	
	<select id="selectStudyChkCount" parameterType="com.aidt.api.bc.cm.dto.BcUserInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectStudyChkCount */
				
		SELECT COUNT(*)
		FROM   LMS_LRM.CM_LRN_TM a
		WHERE  USR_ID                    = #{usrId}
		AND    OPT_TXB_ID                = #{optTxbId}
		AND    SUBSTR( a.LRN_YR_DTM,1,8) > DATE_FORMAT(NOW(), '%Y%m%d')-2 
	    
	</select>
	<select id="selectStudyAIChkCount" parameterType="com.aidt.api.bc.cm.dto.BcUserInfoDto" resultType="int">
		/** BcCm-Mapper.xml - selectStudyAIChkCount */
				
		SELECT COUNT(*)
		FROM   LMS_LRM.CM_LRN_TM a
		WHERE  USR_ID                    = #{usrId}
		AND    OPT_TXB_ID                = #{optTxbId}
		AND   a.AI_EV_LRN_TM_SCNT >0
		AND    SUBSTR( a.LRN_YR_DTM,1,8) > DATE_FORMAT(NOW(), '%Y%m%d')-3 
	    
	</select>
	
	
	<!-- 알지오매쓰파일 -->
	<select id="selectAgomFle" parameterType="string" resultType="com.aidt.api.bc.cm.dto.BcAgomFleDto">
		/** BcCm-Mapper.xml - selectAgomFle  알지오매쓰파일 */
		SELECT
			AGOM_FLE_ID AS agomFleId
			,TXB_ID AS txbId
			,FLE_PTH_NM AS flePthNm
			,VD_DV_CD AS vdDvCd
		FROM lms_cms.bc_agom_fle A
		WHERE A.AGOM_FLE_ID = #{agomFleId}
	</select>
	
	
	
	
	
		<!-- 학습접근이력 금일 등록 체크  -->
	<select id="selectBcCmLrnApoHstChkDate" resultType="int" parameterType="com.aidt.api.bc.cm.dto.BcCmLrnApoHstDto">
		/** BcCm-Mapper.xml - selectBcCmLrnApoHstChkDate   */
		SELECT
			count(*) 
		FROM LMS_LRM.cm_lrn_apo_hst
		WHERE USR_ID = #{usrId}
		AND APO_REF_VL=#{apoRefVl}
		and APO_DTM>CURDATE()
	</select>

	<!-- 학습접근이력 데이터 조회 -->
	<select id="selectBcCmLrnApoHstData" resultType="com.aidt.api.bc.cm.dto.BcCmLrnApoHstDto" parameterType="com.aidt.api.bc.cm.dto.BcCmLrnApoHstDto">
		/** BcCm-Mapper.xml - selectBcCmLrnApoHstData   */
		SELECT lrn_apo_hst_id
		,apo_data
		FROM LMS_LRM.cm_lrn_apo_hst
		WHERE USR_ID = #{usrId}
		AND APO_REF_VL=#{apoRefVl}
		order by apo_dtm desc
		limit 1
	</select>

	<!-- 학습접근이력 등록  -->
	<insert id="insertBcCmLrnApoHst" parameterType="com.aidt.api.bc.cm.dto.BcCmLrnApoHstDto">
		/** BcCm-Mapper.xml - insertBcCmLrnApoHst */
		INSERT INTO LMS_LRM.cm_lrn_apo_hst(
			 USR_ID, APO_DTM, 
			 APO_REF_VL, APO_DATA,
			 CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES(
			#{usrId}			
			,NOW()
			,#{apoRefVl}
			,#{apoData}
			,#{usrId}
			,NOW()
			,#{usrId}
			, NOW()
			,#{dbId}
		)
	</insert>

	<!-- 개인정보 수집 동의 조회 -->
	<select id="selectUsrAgrYn" parameterType="String" resultType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - selectUsrAgrYn" */
		SELECT USR_ID, AGR_YN, AGR_YN_PRCS_DTM
		FROM LMS_LRM.CM_USR
		WHERE USR_ID = #{usrId}
	</select>
	<!--	개인정보 수집 동의 업데이트 쿼리 -->
	<update id="updateUsragrYn" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCm-Mapper.xml - updateusragreeYn */
		UPDATE LMS_LRM.CM_USR
		SET
			AGR_YN = #{agrYn},
			AGR_YN_PRCS_DTM = NOW(),
			MDFR_ID = #{usrId},
			MDF_DTM = NOW()
		WHERE USR_ID = #{usrId}
	</update>

	<insert id="insertFrontErrLog" parameterType="com.aidt.api.at.err.dto.FrontErrLogDto">
		/** BcCm-Mapper.xml - insertFrontErrLog */
		INSERT INTO lms_lrm.cm_front_err_log
		(
			 conn_dv_cd
			,front_conn_url
			,body_cn
			,usr_id
			,opt_txb_id
			,err_msg
			,system_cd
			,reg_dtm
		)
		VALUES
		(
			 #{connDvCd}			
			,#{frontConnUrl}
			,#{bodyCn}
			,#{usrId}
			,#{optTxbId}
			,#{errMsg}
			,#{systemCd}
			, NOW()
		)
	</insert>

	<insert id="insertKerisFrontLog" parameterType="com.aidt.api.bc.cm.dto.KerisFrontLogDto">
		/** BcCm-Mapper.xml - insertKerisFrontLog */
		INSERT INTO lms_lrm.cm_keris_front_log
		(
			 keris_usr_id
			,usr_id
			,opt_txb_id
			,conn_url
			,header_info
			,body_cn
			,rs_msg
			,front_req_tm
			,system_cd
			,reg_dtm
		)
		VALUES
		(
			 #{kerisUsrId}
			,#{usrId}
			,#{optTxbId}
			,#{connUrl}
			,#{headerInfo}
			,#{bodyCn}
			,#{rsMsg}
			,#{frontReqTm}
			,#{systemCd}
			,NOW()
		)
	</insert>
	
	<insert id="insertClaCpLog" parameterType="com.aidt.api.bc.cm.dto.CmClaCpLogDto">
		/** BcCm-Mapper.xml - insertClaCpLog */
		INSERT INTO lms_lrm.cm_cla_cp_log
		(
			 opt_txb_id
			,cp_opt_txb_id
			,cp_dv_cd
			,cp_prcs_yn
			,backend_fle_pth
			,keris_usr_id
			,crtr_id
			,reg_dtm
		)
		VALUES
		(
			 #{optTxbId}
			,#{cpOptTxbId}
			,#{cpDvCd}
			,#{cpPrcsYn}
			,#{backendFlePth}
			,#{kerisUsrId}
			,#{crtrId}
			,NOW()
		)
	</insert>
	
	<select id="selectFrontWebSocketInsYn" resultType="String">
		/** BcCm-Mapper.xml - selectFrontWebSocketInsYn" */
		SELECT 
			IFNULL(MAX(setm_vl_1), 'N') 
		FROM 
			lms_lrm.cm_cm_cd cc 
		WHERE 
			cc.urnk_cm_cd = 'WEB_SOCKET_LOG'
	</select>
	
	<insert id="insertFrontWebSocketLog" parameterType="com.aidt.api.at.err.dto.WebSocketLogDto">
		/** BcCm-Mapper.xml - insertFrontWebSocketLog */
		INSERT INTO lms_lrm.cm_web_socket_log
		(
			 opt_txb_id
			,usr_id
			,keris_usr_id
			,msg_tp_cd
			,session_id
			,rs_cd
			,rs_msg
			,conn_url
			,body_cn
			,usr_brws_info
			,usr_ip
			,reg_dtm
		)
		VALUES
		(
			 #{optTxbId}			
			,#{usrId}
			,#{kerisUsrId}
			,#{msgTpCd}
			,#{sessionId}
			,#{rsCd}
			,#{rsMsg}
			,#{connUrl}
			,#{bodyCn}
			,#{usrBrwsInfo}
			,#{usrIp}
			, NOW()
		)
	</insert>

	<insert id="insertBackErrLog" parameterType="com.aidt.api.at.err.dto.BackErrLogDto">
		/** BcCm-Mapper.xml - insertBackErrLog */
		INSERT INTO lms_lrm.cm_back_err_log (
		    api_url
			, err_cls_nm
			, err_msg
			, err_cd
			, err_src_cd
			, req_parm
			, usr_id
			, opt_txb_id
			, session_elapse_tm
			, reg_dtm
		)
		VALUES (
			#{apiUrl}
			, #{errClsNm}
			, #{errMsg}
			, #{errCd}
			, #{errSrcCd}
			, #{reqParm}
			, #{usrId}
			, #{optTxbId}
			, #{sessionElapseTm}
			, NOW()
		)
	</insert>

</mapper>
