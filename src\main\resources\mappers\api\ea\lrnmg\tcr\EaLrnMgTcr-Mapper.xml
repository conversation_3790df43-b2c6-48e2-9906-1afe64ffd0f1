<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.lrnmg.tcr">

    <!--    학생분석 메인  조회  com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrResDto  -->
	<select id="selectEaStuAnMainInfo" resultType="hashMap">
       /** EaLrnMgTcr-Mapper.xml - selectEaStuAnMainInfo - 박원희 - 학생분석 메인 조회*/

		SELECT COUNT(USR.USR_ID) AS allStuCnt 
			 , SUM(CASE WHEN USR.NTR_YN = 'Y' THEN 1 ELSE 0 END) 		  AS interestStuCnt -- 관심학생 count
			 , SUM(CASE WHEN USR.rtmAbnBhvTpCd IS NOT NULL THEN 1 ELSE 0 END) AS rtmAbnBhvCnt	-- 관심학생 count
			 , SUM(CASE WHEN USR.LRNR_VEL_TP_CD = 'FS' THEN 1 ELSE 0 END) AS fastStuCnt
			 , SUM(CASE WHEN USR.LRNR_VEL_TP_CD = 'NM' THEN 1 ELSE 0 END) AS normalStuCnt
			 , SUM(CASE WHEN USR.LRNR_VEL_TP_CD = 'SL' THEN 1 ELSE 0 END) AS slowStuCnt
		FROM (	 
				SELECT USR.USR_ID
					 , USR.STU_NO
				     , MAX(USR.NTR_YN) NTR_YN -- 관심학생 여부
					 , MAX(AL.LRNR_VEL_TP_CD) AL_LRNR_VEL_TP_CD -- ai학습수준
					 , USR.LRNR_VEL_TP_CD AS LRNR_VEL_TP_CD		-- 일반 레벨
					 , CSCLS.RTM_ABN_BHV_TP_CD AS rtmAbnBhvTpCd
					 , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'RTM_ABN_BHV_TP_CD' AND CM_CD = CSCLS.RTM_ABN_BHV_TP_CD) AS RTM_ABN_BHV_TP_NM
				FROM LMS_LRM.CM_USR USR 
				LEFT JOIN LMS_LRM.CM_STU_CUR_LRN_ST CSS ON CSS.LRN_USR_ID = USR.USR_ID
				LEFT JOIN LMS_LRM.AI_LRNR_LV AL ON AL.USR_ID = USR.USR_ID AND DEL_YN = 'N'
				LEFT JOIN LMS_LRM.CM_STU_CUR_LRN_ST CSCLS ON CSCLS.LRN_USR_ID = USR.USR_ID
				WHERE USR.CLA_ID = #{claId}
				AND USR.USR_TP_CD = 'ST'
				GROUP BY USR.USR_ID
		) USR
				
	</select>
	
    <!--    학생분석 메인 리스트 조회  com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrMainListDto  -->
	<select id="selectEaStuAnLrnStList" resultType="hashMap">
       /** EaLrnMgTcr-Mapper.xml - selectEaStuAnLrnStList - 박원희 - 학생분석 메인 리스트 조회*/

		SELECT 
				USR.USR_ID	AS usrId
			  , USR.STU_NO 
	  		  , ROW_NUMBER() OVER(ORDER BY MAX(USR.USR_NM), USR.USR_ID) AS rowNo
			  , MAX(USR.USR_NM) AS usrNm
			  , '' AS usrProfile
			  , CASE WHEN MAX(USR.NTR_YN) = 'Y' THEN 'ico-interest' ELSE '' END AS usrIcon -- 관심학생 아이콘
			  , MAX(USR.NTR_YN) AS ntrYn -- 관심학생
			  , IFNULL(LEFT(MAX(VEL.CM_CD_NM), 2), '-') AS lrnrVelTpNm -- 학습수준명 
			  , IFNULL(ROUND(MAX(EV.EV_CANS_RT), 1), 0) AS allrAchd -- 종합성취도 : 교과 + AI = 값 의 평균 ==> 평가의 정답률
			  , CASE WHEN IFNULL(MAX(SL.CMPL_TM_SCNT),0)+IFNULL(MAX(SP.LRN_TM_SCNT),0)+IFNULL(MAX(EV.EV_TM_SCNT),0) = 0 THEN '-'
			  		 ELSE DATE_FORMAT(SEC_TO_TIME(IFNULL(MAX(SL.CMPL_TM_SCNT),0)+IFNULL(MAX(SP.LRN_TM_SCNT),0)+IFNULL(MAX(EV.EV_TM_SCNT),0)), '%H시간 %i분') 
			  		 END AS allLrnTm -- 학습시간 (교과 개념학습 + 특별학습 + 평가(교가, 교과))
			  , CASE WHEN SUM(SL.CH_CMPL_CNT) > 0 THEN ROUND(SUM(SL.CH_CMPL_CNT)/MAX(SL.CH_CNT)*100, 1) ELSE -1 END AS txbLrnPgrsRt
			  , -1											AS aiLrnPgrsRt
			  , IFNULL(ROUND(MAX(EV.EV_PGRS_RT), 1), -1) 	AS evLrnPgrsRt
			  , IFNULL(ROUND(MAX(ASN.ASN_RT), 1), -1) 	  	AS asnLrnPgrsRt
		FROM (
				SELECT USR.USR_ID, MAX(USR.USR_NM) USR_NM, USR.STU_NO
					 , MAX(USR.NTR_YN) NTR_YN -- 관심학생
					 , (SELECT AI_LV.LRNR_VEL_TP_CD
					 	FROM LMS_LRM.AI_LRNR_LV AI_LV /* 사용자 학습 레벨 정보 */
					 	WHERE AI_LV.USR_ID = USR.USR_ID
					 	ORDER BY MDF_DTM DESC
					 	LIMIT 1
					 ) 	LRNR_VEL_TP_CD -- 학습수준	
				FROM LMS_LRM.CM_USR USR /* 사용자 정보 */
				WHERE USR.CLA_ID = #{claId}
				AND USR.USR_TP_CD = 'ST'
				GROUP BY USR.USR_ID
		) USR
		LEFT JOIN (  /* 교과학습 */
				SELECT OPT_TXB_ID, LRN_USR_ID
				     , MAX(ATV.CH_CNT) CH_CNT
				     , MAX(ATV.CMPL_TM_SCNT) CMPL_TM_SCNT
					 , SUM(ATV.CH_CMPL_CNT) CH_CMPL_CNT
				FROM (		
						SELECT NOD.OPT_TXB_ID, ATV.LRN_USR_ID, ATV.LRMP_NOD_ID
							 , MAX(NOD.CH_CNT) CH_CNT
						     , SUM(ATV.CMPL_TM_SCNT) CMPL_TM_SCNT -- 학습시간 (개)
							 , MAX(ATV.ATV_CNT) ATV_CNT
							 , MAX(ATV.CMPL_CNT) CMPL_CNT
							 , CASE WHEN MAX(ATV.ATV_CNT) = MAX(ATV.CMPL_CNT) THEN 1 ELSE 0 END AS CH_CMPL_CNT
						FROM ( /* 운영교과서의 사용 중인 차시 개수, 차수 개수, 자수별 학습활동 개수 */
							 	SELECT NOD.OPT_TXB_ID, NOD.LRMP_NOD_ID
							 		 , COUNT(NOD.LRMP_NOD_ID) OVER(PARTITION BY NOD.OPT_TXB_ID) CH_CNT
								FROM TL_SBC_LRN_NOD_RCSTN NOD
								WHERE NOD.OPT_TXB_ID = #{optTxbId}
								AND NOD.DPTH = '4'
								AND NOD.USE_YN = 'Y'
								AND EXISTS(SELECT 1 
										   FROM TL_SBC_LRN_NOD_RCSTN NOD_LU 
										   WHERE NOD_LU.OPT_TXB_ID = NOD.OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = NOD.LLU_NOD_ID AND NOD_LU.USE_YN = 'Y'
								)
						) NOD
						LEFT JOIN ( /* 사용자별 교과학습 학습활동 완료 개수 */
						  		SELECT ATV.OPT_TXB_ID, ATV.LRMP_NOD_ID
						  			 , CP.LRN_USR_ID
						  			 , COUNT(DISTINCT CASE WHEN ATV.USE_YN = 'Y' THEN ATV.LRN_ATV_ID ELSE NULL END)	AS ATV_CNT 
		 				  			 , COUNT(DISTINCT CASE WHEN ATV.USE_YN = 'Y' THEN CP.LRN_ATV_ID ELSE NULL END) 	AS CMPL_CNT 
		 				  			 , SUM(CP.LRN_TM_SCNT) 				AS CMPL_TM_SCNT -- 총 학습시간(교과 학습 개념 학습만)
						        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV  /* TL_교과학습활동재구성 */
						  		LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST CP ON CP.OPT_TXB_ID = ATV.OPT_TXB_ID 
						  			 AND CP.LRMP_NOD_ID = ATV.LRMP_NOD_ID 
						  			 AND CP.LRN_ATV_ID = ATV.LRN_ATV_ID 
						  			 AND CP.LRN_ST_CD = 'CL'
						        WHERE ATV.OPT_TXB_ID = #{optTxbId} 
						        AND EXISTS(SELECT 1 FROM LMS_CMS.BC_LRN_STP STP WHERE STP.LRN_STP_ID = ATV.LRN_STP_ID AND STP.LRN_STP_DV_CD = 'CL') /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EV: 평가) */
						        GROUP BY ATV.OPT_TXB_ID, ATV.LRMP_NOD_ID, CP.LRN_USR_ID
						) ATV ON ATV.OPT_TXB_ID = NOD.OPT_TXB_ID AND ATV.LRMP_NOD_ID = NOD.LRMP_NOD_ID
						GROUP BY NOD.OPT_TXB_ID, ATV.LRN_USR_ID, ATV.LRMP_NOD_ID
				) ATV
				GROUP BY ATV.OPT_TXB_ID, ATV.LRN_USR_ID
		) SL ON SL.LRN_USR_ID = USR.USR_ID
		LEFT JOIN ( /* 특별학습 학습시간*/
            SELECT
                   SPS.OPT_TXB_ID, SPS.LRN_USR_ID
                 , SUM(SPS.LRN_TM_SCNT) AS LRN_TM_SCNT
            FROM LMS_LRM.SL_SP_LRN_RCSTN SP
            JOIN LMS_LRM.SL_SP_LRN_PGRS_ST SPS ON SPS.OPT_TXB_ID = SP.OPT_TXB_ID AND SPS.SP_LRN_ID = SP.SP_LRN_ID
            WHERE SP.OPT_TXB_ID = #{optTxbId}
            AND SP.USE_YN = 'Y'
            AND SPS.LRN_ST_CD  = 'CL'-- 학습 완료	
            GROUP BY SPS.OPT_TXB_ID, SPS.LRN_USR_ID	
		) SP ON SP.LRN_TM_SCNT = USR.USR_ID
		LEFT JOIN ( /* 평가 진행률, 학습시간*/
				SELECT 
					   E.OPT_TXB_ID
					 , ER.USR_ID
					 , SUM(ER.CANS_CNT)/SUM(E.FNL_QST_CNT)*100 AS EV_CANS_RT -- 정답률
					 , CASE WHEN MAX(E.EV_CNT) > 0 THEN COUNT(ER.EV_ID)/MAX(E.EV_CNT)*100 END AS EV_PGRS_RT -- 진행률
					 , MAX(E.EV_CNT) EV_CNT
					 , COUNT(ER.EV_ID) CP_CNT
					 , SUM(ER.EV_TM_SCNT) EV_TM_SCNT
				FROM (
						SELECT E.OPT_TXB_ID, E.EV_ID, MAX(E.FNL_QST_CNT) FNL_QST_CNT
						     , COUNT(CASE WHEN E.USE_YN = 'Y' AND E.DEL_YN = 'N' THEN E.EV_ID ELSE NULL END) OVER() EV_CNT
						FROM LMS_LRM.EA_EV E
						WHERE E.OPT_TXB_ID = #{optTxbId}
						AND E.EV_DV_CD IN ('SE', 'TE', 'DE') -- AI평가는 제외
						GROUP BY E.OPT_TXB_ID, E.EV_ID
				) E
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID 
				WHERE ER.EV_CMPL_YN = 'Y'
				GROUP BY E.OPT_TXB_ID, ER.USR_ID
		) EV ON EV.USR_ID = USR.USR_ID
		LEFT JOIN ( /* 과제 진행률 */
				SELECT 
					   ASN.OPT_TXB_ID
					 , ASN_S.STU_USR_ID
					 , CASE WHEN MAX(ASN.ASN_CNT) > 0 THEN COUNT(ASN_S.ASN_ID)/MAX(ASN.ASN_CNT)*100 END AS ASN_RT
					 , MAX(ASN.ASN_CNT) EV_CNT
					 , COUNT(ASN_S.ASN_ID) CP_CNT
				FROM (
						SELECT ASN.OPT_TXB_ID, ASN.ASN_ID, COUNT(1) OVER() ASN_CNT
						FROM LMS_LRM.EA_ASN ASN
						WHERE ASN.OPT_TXB_ID = #{optTxbId}
						AND ASN.DEL_YN = 'N'
						AND ASN.USE_YN = 'Y'
						GROUP BY ASN.OPT_TXB_ID, ASN.ASN_ID
				) ASN
				JOIN LMS_LRM.EA_ASN_SMT ASN_S ON ASN_S.ASN_ID = ASN.ASN_ID 
				WHERE ASN_S.SMT_CMPL_YN = 'Y'
				GROUP BY ASN.OPT_TXB_ID, ASN_S.STU_USR_ID
		) ASN ON ASN.STU_USR_ID = USR.USR_ID
		LEFT JOIN LMS_LRM.CM_CM_CD VEL ON VEL.URNK_CM_CD = 'LRNR_VEL_TP_CD' AND VEL.CM_CD = USR.LRNR_VEL_TP_CD
		WHERE 1 = 1
    	<if test = '"i".equals(searchType)'>
    	AND USR.NTR_YN = 'Y'
    	</if>
    	<if test = '"f".equals(searchType)'>
    	AND USR.LRNR_VEL_TP_CD = 'FS'
    	</if>
    	<if test = '"n".equals(searchType)'>
    	AND USR.LRNR_VEL_TP_CD = 'NM'
    	</if>    	
    	<if test = '"s".equals(searchType)'>
    	AND USR.LRNR_VEL_TP_CD = 'SL'
    	</if>
    	GROUP BY USR.USR_ID
		ORDER BY USR.STU_NO, USR.USR_ID

	</select>
	
    <!--    학생분석 상세 교과학습 리스트 조회    -->
	<select id="selectEaStuAnDtlTxbList" resultType="hashMap">
       /** EaLrnMgTcr-Mapper.xml - selectEaStuAnDtlTxbList - 박원희 - 학생분석 상세 교과학습 조회*/
		SELECT NOD.OPT_TXB_ID AS optTxbId
		     , NOD.LRMP_NOD_ID AS lrmpNodId
			 , NOD.LRMP_NOD_NM AS lrmpNodNm
			 , CONCAT(NOD.RCSTN_ORDN, '.', NOD.LRMP_NOD_NM) AS lrmpNodNmView
 		     , NOD.LLU_NOD_ID AS luLrmpNodId
			 , NOD.DPTH AS dpth
			 , NOD.RCSTN_ORDN AS rcstnOrdn
			 , NOD.LLU_CNT AS luCnt
			 , NOD.CLU_CNT AS cluCnt
			 , NOD.cmplCluCnt
			 , NOD.LCKN_YN AS lcknYn
			 , CASE WHEN IFNULL(NOD.CLU_CNT, 0) = 0 OR IFNULL(NOD.cmplCluCnt, 0) = 0 THEN 0 ELSE ROUND(NOD.cmplCluCnt/NOD.CLU_CNT*100, 2) END AS cmplLluRt
			 , NOD.cmplCluRt
			 -- , ROW_NUMBER() OVER(PARTITION BY NOD.DPTH, NOD.LLU_NOD_ID ORDER BY NOD.cmplCluRt DESC, NOD.RCSTN_ORDN) AS cmplCluRtDescNo
			 -- , ROW_NUMBER() OVER(PARTITION BY NOD.DPTH, NOD.LLU_NOD_ID ORDER BY NOD.cmplCluRt, NOD.RCSTN_ORDN DESC) AS cmplCluRtAscNo
			 , RANK() OVER(PARTITION BY NOD.DPTH, NOD.LLU_NOD_ID ORDER BY NOD.cmplCluRt DESC) AS cmplCluRtDescNo
			 , RANK() OVER(PARTITION BY NOD.DPTH, NOD.LLU_NOD_ID ORDER BY NOD.cmplCluRt) AS cmplCluRtAscNo			 
			 , ROW_NUMBER() OVER(PARTITION BY NOD.LRMP_NOD_ID ORDER BY NOD.RCSTN_ORDN_ATV, NOD.LRN_ATV_ID) AS lrnAtvRowNo
			 , NOD.RCSTN_ORDN_ATV
			 , NOD.LRN_ATV_ID AS lrnAtvId
			 , NOD.LRN_ATV_NM AS lrnAtvNm
			 , NOD.LRN_USR_ID AS lrnUsrId
			 , NOD.LRN_ST_CD AS lrnStCd
			 , IFNULL((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'LRN_ST_CD' AND CM_CD = NOD.LRN_ST_CD), '-') AS lrnStNm 
			 , NOD.atvPgrsRt
			 , NOD.ATV_CNT AS atvCnt
			 , NOD.ATV_CMPL_CNT AS atvCmplCnt
			 , NOD.LRN_TM_SCNT AS lrnTmScnt
			 , IFNULL(DATE_FORMAT(NOD.START_DTM, '%Y.%m.%d'), '-') AS startDtm
			 , IFNULL(DATE_FORMAT(NOD.END_DTM, '%Y.%m.%d'), '-') AS endDtm
		FROM (
				SELECT NOD.OPT_TXB_ID
				     , NOD.LRMP_NOD_ID
					 , NOD.LRMP_NOD_NM
		 		     , NOD.LLU_NOD_ID
					 , NOD.DPTH
					 , NOD.RCSTN_ORDN
					 , NOD.LLU_CNT
					 , MAX(NOD.CLU_CNT) OVER(PARTITION BY NOD.LLU_NOD_ID) AS CLU_CNT
					 , SUM(ATV.CLU_CMPL_ROW) OVER(PARTITION BY NOD.LLU_NOD_ID) AS cmplCluCnt
			 		 , MAX(CASE WHEN IFNULL(ATV.ATV_CNT, 0) = 0 OR IFNULL(ATV.ATV_CMPL_CNT, 0) = 0 THEN 0 ELSE ROUND(ATV.ATV_CMPL_CNT/ATV.ATV_CNT*100, 2) END) OVER(PARTITION BY NOD.LRMP_NOD_ID) AS cmplCluRt
					 , NOD.LCKN_YN
					 , ATV.RCSTN_ORDN_ATV
					 , ATV.LRN_ATV_ID
					 , ATV.LRN_ATV_NM
					 , ATV.LRN_USR_ID
					 , ATV.LRN_ST_CD
					 , ATV.atvPgrsRt
					 , ATV.ATV_CNT
					 , ATV.ATV_CMPL_CNT
					 , ATV.LRN_TM_SCNT
		  			 , ATV.START_DTM
		  			 , ATV.END_DTM
	  			 FROM ( /* 운영교과서의 사용 중인 대단원, 차시 정보 */
					 	SELECT NOD.OPT_TXB_ID, NOD.LRMP_NOD_ID, NOD.LRMP_NOD_NM
					 	     , NOD.LLU_NOD_ID
					 	     , NOD.DPTH
					 	     , NOD.RCSTN_ORDN
					 	     , NOD.LCKN_YN
					 		 , COUNT(NOD.LRMP_NOD_ID) OVER(PARTITION BY NOD.OPT_TXB_ID, NOD.DPTH) LLU_CNT  -- 단원 총 개수 (대단원, 전체차시)
					 		 , COUNT(NOD.LRMP_NOD_ID) OVER(PARTITION BY NOD.OPT_TXB_ID, NOD.DPTH, NOD.LLU_NOD_ID) CLU_CNT  -- 대단원별 차시 개수
						FROM TL_SBC_LRN_NOD_RCSTN NOD
						WHERE NOD.OPT_TXB_ID = #{optTxbId}
						AND NOD.DPTH IN ('1', '4')
						AND NOD.USE_YN = 'Y'
				) NOD
				LEFT JOIN ( /*학습활동 정보, 사용자별 학습활동 완료 개수 */
						SELECT ATV.OPT_TXB_ID
				  		     , ATV.LRMP_NOD_ID, ATV.LRN_ATV_ID, ATV.LRN_ATV_NM
				  			 , ATV.RCSTN_ORDN_ATV
				  			 , ATV.LRN_USR_ID
				  			 , ATV.ATV_CNT  -- 차시별 활동 총 개수
				  			 , ATV.ATV_CMPL_CNT 
				  			 , CASE WHEN ATV.ATV_CNT = ATV.ATV_CMPL_CNT AND ATV_ROW_NO = 1 THEN 1 ELSE 0 END AS CLU_CMPL_ROW
				  			 , ATV.atvPgrsRt
				  			 , ATV.LRN_ST_CD
				  			 , ATV.LRN_TM_SCNT
				  			 , ATV.START_DTM
				  			 , ATV.END_DTM
						FROM (
						  		SELECT ATV.OPT_TXB_ID
						  		     , ATV.LRMP_NOD_ID, ATV.LRN_ATV_ID, MAX(ATV.LRN_ATV_NM) LRN_ATV_NM
						  			 , MAX(ATV.RCSTN_ORDN) RCSTN_ORDN_ATV
						  			 , CP.LRN_USR_ID
						  			 , ROW_NUMBER() OVER(PARTITION BY ATV.LRMP_NOD_ID ORDER BY ATV.LRMP_NOD_ID, ATV.RCSTN_ORDN) ATV_ROW_NO
						  			 , COUNT(ATV.LRN_ATV_ID) OVER(PARTITION BY ATV.LRMP_NOD_ID) ATV_CNT  -- 차시별 활동 총 개수
						  			 , SUM(CASE WHEN CP.LRN_ST_CD = 'CL' THEN 1 ELSE 0 END) OVER(PARTITION BY CP.LRN_USR_ID, CP.LRMP_NOD_ID) AS ATV_CMPL_CNT 
						  			 , MAX(CASE WHEN CP.LRN_ST_CD = 'CL' THEN '100%' 
						  			 			WHEN CP.LRN_ST_CD IS NOT NULL THEN '0%' 
						  			 			ELSE '-' END) AS atvPgrsRt
						  			 , MAX(CP.LRN_ST_CD) LRN_ST_CD
						  			 , MAX(CP.LRN_TM_SCNT) LRN_TM_SCNT
						  			 , MAX(CP.CRT_DTM) START_DTM
						  			 , MAX(CP.MDF_DTM) END_DTM
						  		FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV  /* TL_교과학습활동재구성 */
						  		LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST CP ON CP.OPT_TXB_ID = ATV.OPT_TXB_ID 
						  			AND CP.LRMP_NOD_ID = ATV.LRMP_NOD_ID 
						  			AND CP.LRN_ATV_ID = ATV.LRN_ATV_ID 
						  			AND CP.LRN_USR_ID = #{usrId}
						        WHERE ATV.OPT_TXB_ID = #{optTxbId}
						        AND ATV.USE_YN = 'Y'
						        AND EXISTS(SELECT 1 FROM LMS_CMS.BC_LRN_STP STP WHERE STP.LRN_STP_ID = ATV.LRN_STP_ID AND STP.LRN_STP_DV_CD = 'CL') /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EV: 평가) */
								GROUP BY ATV.OPT_TXB_ID, CP.LRN_USR_ID, ATV.LRMP_NOD_ID, ATV.LRN_ATV_ID
						) ATV
				) ATV ON ATV.OPT_TXB_ID = NOD.OPT_TXB_ID AND ATV.LRMP_NOD_ID = NOD.LRMP_NOD_ID
		) NOD
		ORDER BY NOD.LLU_NOD_ID, NOD.RCSTN_ORDN, NOD.RCSTN_ORDN_ATV
		
		
	</select>
	
    <!--    학생분석 상세 평가 리스트 조회    -->
	<select id="selectEaStuAnDtlEvList" resultType="hashMap">
       /** EaLrnMgTcr-Mapper.xml - selectEaStuAnDtlEvList - 박원희 - 학생분석 상세 평가 조회*/
	
		SELECT 
			  E.EV_ID				AS evId					-- 평가ID
			, COUNT(1) OVER() 		AS totalCnt 			-- 총 조회 갯수
			, E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
			, E.USR_ID				AS usrId				-- 사용자ID
			, E.EV_NM				AS evNm					-- 평가명
			, E.EV_DV_CD			AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
			, E.EV_DTL_DV_CD		AS evDtlDvCd			-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
			, IFNULL(EV_DTL.CM_CD_NM, '-') AS evDtlDvNm -- 평가상세구분명
			, CASE 	WHEN E.LCKN_YN = 'Y' THEN '잠금' 
					WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN '대기' -- 응시기간 도래전, 미잠금
					WHEN R.EV_CMPL_YN = 'Y' THEN '응시완료'
					WHEN IFNULL(R.EV_CMPL_YN, 'N') = 'N' THEN '미응시'
					ELSE '응시중' 
				   	END 				AS txmStNm			-- 평가상태 응시상태			
			/*
			, CASE WHEN E.TXM_PTME_SETM_YN = 'N' THEN '-'
				   ELSE CONCAT(DATE_FORMAT(E.TXM_STR_DTM, '%Y.%m.%d %p %H:%i'), ' ~ ', DATE_FORMAT(E.TXM_END_DTM, '%Y.%m.%d %p %H:%i'))
				   END AS txmPtmeView
			*/
			, CASE WHEN E.TXM_PTME_SETM_YN = 'N' THEN '-'
	        ELSE CONCAT(
	            DATE_FORMAT(E.TXM_STR_DTM, '%Y.%m.%d '),
	            CASE
	                WHEN HOUR(E.TXM_STR_DTM) <![CDATA[ < ]]> 12 THEN '오전'
	                ELSE '오후'
	            END,
	            DATE_FORMAT(E.TXM_STR_DTM, ' %h:%i'), ' ~ ',
	            DATE_FORMAT(E.TXM_END_DTM, '%Y.%m.%d '),
	            CASE
	                WHEN HOUR(E.TXM_END_DTM) <![CDATA[ < ]]> 12 THEN '오전'
	                ELSE '오후'
	            END,
	            DATE_FORMAT(E.TXM_END_DTM, ' %h:%i')
	        )
	    	END AS txmPtmeView -- 응시기간표시
			, E.TXM_PTME_SETM_YN 	AS txmPtmeSetmYn        -- 응시기간설정여부 
			, DATE_FORMAT(E.TXM_STR_DTM, '%Y-%m-%d %H:%i')	AS txmStrDtm -- 응시 시작일
			, DATE_FORMAT(E.TXM_END_DTM, '%Y-%m-%d %H:%i')	AS txmEndDtm -- 응시 종료일
			, DATE_FORMAT(R.SMT_DTM, '%Y-%m-%d') 			AS smtDtm -- 제출일시
			, IFNULL(R.TXM_STR_YN, 'N')			AS txmStrYn				-- 응시시작여부
			, IFNULL(R.EV_CMPL_YN, 'N')			AS evCmplYn				-- 평가완료여부
			, E.XPL_TM_SETM_YN  	AS xplTmSetmYn			-- 풀이시간설정여부
			, E.XPL_TM_SCNT			AS xPlTmScnt			-- 풀이시간 초수 전체초수
			, E.QST_CNT				AS qstCnt				-- 문제수
			, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수	
			, IFNULL(R.CANS_CNT, 0)	AS cansCnt				-- 정답수
			, E.LCKN_YN				AS lcknYn				-- 잠금여부
			, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
			, E.USE_YN				AS useYn				-- 사용여부
			, E.CRTR_ID				AS crTrId				-- 생성자
			, DATE_FORMAT(E.CRT_DTM, '%Y-%m-%d')  AS crtDtm -- 생성일
			, E.MDFR_ID				AS mdfrId				-- 수정자
			, E.MDF_DTM				AS mdfDtm				-- 수정일
			, E.DB_ID				AS dbId					-- DB ID
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = E.EV_ID AND R.USR_ID = #{usrId}
		LEFT JOIN LMS_LRM.CM_CM_CD EV_DTL ON EV_DTL.URNK_CM_CD = 'EV_DTL_DV_CD' AND EV_DTL.CM_CD = E.EV_DTL_DV_CD
		<if test = 'tbscDvCdEv.equals("0") or tbscDvCdEv == 0'>
		LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = T.TC_LRMP_NOD_ID	
		</if>	
		WHERE E.OPT_TXB_ID = #{optTxbId}
		<choose>
    	<when test = 'tbscDvCdEv.equals("1")  or tbscDvCdEv == 1'>
		AND E.EV_DV_CD = 'DE' -- DE:DIY평가
    	</when>
    	<otherwise>
		AND E.EV_DV_CD IN ('SE', 'TE') -- SE:교과평가, TE:교사평가 만			
		</otherwise>
		</choose>
		AND E.DEL_YN = 'N'
		AND E.USE_YN = 'Y'
    	<if test = 'srhLuLrmpNodId != ""'><!-- 대단원 dropdown -->
    	AND T.LU_LRMP_NOD_ID = #{srhLuLrmpNodId}
    	</if>
		<choose>
    	<when test = 'evExmState.equals("exam")'><!-- 진행중 > 응시중, 대기, 미응시, 잠금 상태 평가  -->
		AND ((E.TXM_PTME_SETM_YN = 'N' AND IFNULL(R.EV_CMPL_YN, 'N') = 'N')
			OR E.TXM_END_DTM > CURRENT_TIMESTAMP)
    	</when>
    	<when test = 'evExmState.equals("end")'><!-- 종료 > 응기시간종료, 응시완료 평가-->
    	AND E.LCKN_YN = 'N'
		AND (R.EV_CMPL_YN = 'Y' OR CURRENT_TIMESTAMP > E.TXM_END_DTM)
    	</when>
		</choose>
		<choose>
    	<when test = 'tbscDvCdEv.equals("1") or tbscDvCdEv == 1'><!-- DIY평가 -->
    	ORDER BY E.CRT_DTM DESC
    	</when>
    	<when test = 'srtDvCd == "1" or srtDvCd == 1'><!-- 정렬구분 - 단원순  -->
		ORDER BY CASE 	WHEN E.EV_DV_CD = 'TE' THEN 0
				   		ELSE 1 
				   		END
			   , EV_DTL.SRT_ORDN
			   , NOD_LU.RCSTN_ORDN -- 대단원 재구성 순서
			   , NOD_TC.RCSTN_ORDN -- 차시 재구성 순서
			   , E.MDF_DTM DESC -- 최신순
    	</when>
    	<when test = 'srtDvCd == "2" or srtDvCd == 1'><!-- 마감일순 -->
		ORDER BY CASE 	WHEN E.TXM_PTME_SETM_YN = 'Y' AND E.EV_DV_CD = 'TE' THEN 0
				   		ELSE 1
				   		END -- 마감일 있는데이터 > 교사평가 순
			    , CASE 	WHEN E.TXM_PTME_SETM_YN = 'Y' THEN E.TXM_END_DTM
				   		ELSE '9999-12-30 00:00:00' 
				   		END -- 마감일 있는데이터 > 마감일 근접순
			   	, CASE 	WHEN E.TXM_PTME_SETM_YN = 'N' AND E.EV_DV_CD = 'TE' THEN 0
				   		ELSE 1
				   		END -- 마감일 없는데이터 > 교사평가 순
				, E.MDF_DTM DESC -- 최신순    	
    	</when>
		</choose>
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}
		
	</select>
	
    <!--    학생분석 상세 과제 리스트 조회    -->
	<select id="selectEaStuAnDtlAsnList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEaDetailResDto">
       /** EaLrnMgTcr-Mapper.xml - selectEaStuAnDtlAsnList - 박원희 - 학생분석 상세 과제 조회*/

		SELECT A.OPT_TXB_ID			AS optTxbId
			 , COUNT(1) OVER() 		AS totalCnt 	-- 총 조회 갯수
		     , A.ASN_ID				AS asnId
			 , A.TCR_USR_ID			AS tcrUsrId 	-- 교사사용자ID
			 , A.ASN_NM				AS asnNm		-- 과제명
			 , A.ASN_CN				AS asnCn 		-- 과제설명
			 , A.ASN_TP_CD			AS asnTpCd	 	-- 과제유형코드
			 , A.LRN_TP_CD			AS lrnTpCd 		-- 학습유형코드
			 , CASE WHEN A.LRN_TP_CD IS NOT NULL THEN
				        (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = A.LRN_TP_CD AND CM.URNK_CM_CD ='LRN_TP_CD')
				    ELSE
					    (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = A.ASN_TP_CD AND CM.URNK_CM_CD ='ASN_TP_CD')
			   END 					AS asnLrnTpNm	-- 유형명			 
			 , A.ASN_PTME_DV_CD		AS asnPtmeDvCd 	-- 과제기간구분코드
			 , CASE WHEN A.ASN_PTME_DV_CD = 'PT' THEN CONCAT(A.strDtm, '~', A.endDtm)			 
			 		ELSE '-'
			 		END AS ASN_PTME_NM -- 기간
			 , A.smtDtm AS SMT_DTM -- 완료 일자
		 FROM (	   		
				SELECT A.OPT_TXB_ID
				     , A.ASN_ID
					 , A.TCR_USR_ID
					 , A.ASN_NM
					 , A.ASN_CN
					 , A.CRT_DTM
					 , A.ASN_TP_CD
					 , A.LRN_TP_CD
					 , A.ASN_PTME_DV_CD
					 , CONCAT(DATE_FORMAT(A.STR_DTM,'%Y.%m.%d '),IF(TIME_FORMAT(A.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(A.STR_DTM,'%h:%i')) AS strDtm
					 , CONCAT(DATE_FORMAT(A.END_DTM,'%Y.%m.%d '),IF(TIME_FORMAT(A.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(A.END_DTM,'%h:%i')) AS endDtm
					 , CASE WHEN A.ASN_TP_CD ='GR' THEN ( -- 모둠 제출완료시 LMS_LRM.EA_GRP_ASN_SMT 에도 반영되어서 모둠테이블 볼 필요 없음
					 		     SELECT MAX(DATE_FORMAT(ASM.SMT_DTM, '%Y-%m-%d'))
					 		     FROM LMS_LRM.EA_GRP_ASN_SMT ASM 
					 		     WHERE ASM.ASN_ID = A.ASN_ID
								 AND ASM.SMT_CMPL_YN = 'Y'
							)
					 		ELSE ( 
					 		     SELECT MAX(DATE_FORMAT(ASM.SMT_DTM, '%Y-%m-%d'))
					 		     FROM LMS_LRM.EA_ASN_SMT ASM 
					 		     WHERE ASM.ASN_ID = A.ASN_ID
								 AND ASM.STU_USR_ID = #{usrId}
								 AND ASM.SMT_CMPL_YN = 'Y'
							)
					   		END AS smtDtm
				FROM LMS_LRM.EA_ASN A
				WHERE A.OPT_TXB_ID = #{optTxbId}
				AND A.TCR_USR_ID = #{tcrUsrId}
				AND A.DEL_YN = 'N'
				AND A.USE_YN = 'Y'
		) A
		WHERE 1 = 1
    	<if test = '"Y".equals(ncmplYn)'><!-- 미완료 -->
    	AND A.smtDtm IS NULL
    	</if>		
		ORDER BY A.CRT_DTM DESC			
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}
	</select>
	
	
	<!-- Let's Talk 대단원 정보 조회 (드롭다운) -->
	<select id="selectEaStuAmTcrTalkLesList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrResDto">
		SELECT LLU.KMMP_NOD_ID AS kmmpNodId		-- 대단원노드ID(레슨ID)
		     , LLU.KMMP_NOD_NM 	AS kmmpNodNm		-- 대단원노드명(레슨명)
		     , TPC.TPC_KMMP_NOD_ID 	AS tpcKmmpNodId		-- 토픽노드ID
			 , LLU.LCKN_YN AS lcknYn					-- 잠금여부
			 , LLU.USE_YN AS useYn						-- 사용여부
		     , LLU.TC_USE_YN AS tcUseYn					-- 차시사용여부
		     , LLU.RCSTN_ORDN AS rcstnOrdn				-- 재구성 순서
		     , RANK() OVER (PARTITION BY LLU.KMMP_NOD_ID ORDER BY TPC.TPC_KMMP_NOD_ID) AS nodRank	-- 노드랭크
		 FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND TPC.DEL_YN = 'N'
		   AND LLU.LCKN_YN = 'N'
		 WHERE 1=1
		  and TPC.TW_WRT_DV_CD = 'T'								-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId} 				-- 파라미터: 운영교과서ID
		 group by LLU.KMMP_NOD_ID 
		   ORDER BY LLU.RCSTN_ORDN ASC, LLU.KMMP_NOD_ID asC, TPC.SRT_ORDN
		 /*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrTalkLesList */
	</select>
	
	<!-- 교사의 학급의 학생 정보 조회 -->
	<select id="selectEaStuAmTcrClassStuList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrReqDto">
		SELECT USR.USR_ID AS USR_ID
				, MAX(USR.USR_NM) AS USR_NM
				, MAX(USR.STU_NO) AS USR_NO
				, USR.LRNR_VEL_TP_CD AS LRNR_VEL_TP_CD	-- 학습 수준
				, MAX(USR.NTR_YN) AS NTR_YN -- 관심학생
				, CSCLS.RTM_ABN_BHV_TP_CD AS rtmAbnBhvTpCd	-- 관심학생
				, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'RTM_ABN_BHV_TP_CD' AND CM_CD = CSCLS.RTM_ABN_BHV_TP_CD) AS RTM_ABN_BHV_TP_NM
			FROM LMS_LRM.CM_USR USR /* 사용자 정보 */
			LEFT JOIN LMS_LRM.CM_STU_CUR_LRN_ST CSCLS ON CSCLS.LRN_USR_ID = USR.USR_ID
			WHERE USR.CLA_ID = #{claId} 
			AND USR.USR_TP_CD = 'ST'
			<if test = "searchType != null and searchType != ''">
		    	<if test = '"i".equals(searchType)'>
		    	AND CSCLS.RTM_ABN_BHV_TP_CD IS NOT NULL
		    	</if>
		    	<if test = '"f".equals(searchType)'>
		    	AND USR.LRNR_VEL_TP_CD = 'FS'
		    	</if>
		    	<if test = '"n".equals(searchType)'>
		    	AND USR.LRNR_VEL_TP_CD = 'NM'
		    	</if>    	
		    	<if test = '"s".equals(searchType)'>
		    	AND USR.LRNR_VEL_TP_CD = 'SL'
		    	</if>
	    	</if>
			GROUP BY USR.USR_ID
			ORDER BY USR_NO,  USR_ID
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrClassStuList */
	</select>
	
	<!-- 학생별 LET'S TALK 데이터 조회 -->
	<select id="selectEaStuAmTalkUsrInfo" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrTalkResDto">
		SELECT
			TALK.OPT_TXB_ID AS OPT_TXB_ID
			, TALK.KMMP_NOD_ID AS KMMP_NOD_ID
			, TALK.KMMP_NOD_NM AS KMMP_NOD_NM
			, TALK.USR_ID AS USR_ID
			, IFNULL(TALK.TOTAL_TOPICS, '-') AS TOT_TOPICS_COUNT
			, IFNULL(TALK.COMP_TOPICS, '-') AS COMPL_TOPICS_COUNT
			, IFNULL(TALK.COMP_TOPICS * 100 / TALK.TOTAL_TOPICS, '-') AS PROGRESS_RATE
			, IFNULL(DATE_FORMAT(TALK.LAST_UPDATED, '%Y.%m.%d'), '-') AS LAST_UPDATED
			, IFNULL(DATE_FORMAT(TALK.FIRST_UPDATED, '%Y.%m.%d'), '-') AS FIRST_UPDATED
			, IFNULL(TALK.LRN_TIME ,'-') AS LRN_TIME
			, IFNULL(TALK.DL_CL_EXIST, '-') AS DL_CL_EXIST  -- DL 또는 CL 존재 여부
		FROM
		(
			SELECT LLU.KMMP_NOD_ID AS KMMP_NOD_ID,  -- 대단원노드ID(레슨ID)
			       MAX(LLU.KMMP_NOD_NM) AS KMMP_NOD_NM,  -- 대단원노드명(레슨명)
				   COUNT(TPC.TPC_KMMP_NOD_ID) AS TOTAL_TOPICS,  -- 총 토픽 개수
				   SUM(CASE WHEN TK.LRN_ST_CD = 'CL' THEN 1 ELSE 0 END) AS COMP_TOPICS,  -- 완료된 토픽 개수
				   SUM(CASE WHEN TK.LRN_ST_CD = 'CL' THEN 1 ELSE 0 END) * 100 / COUNT(TPC.TPC_KMMP_NOD_ID) AS PROGRESS_RATE,  -- 진척율
				   MAX(TK.MDF_DTM) AS LAST_UPDATED,  -- 마지막 업데이트 날짜
				   MIN(TK.CRT_DTM) AS FIRST_UPDATED,  -- 처음 시작 날짜
				   TK.LRN_USR_ID AS LRN_USR_ID,
				   LLU.OPT_TXB_ID AS OPT_TXB_ID,
				   TIMESTAMPDIFF(SECOND, MIN(TK.CRT_DTM), MAX(TK.MDF_DTM)) AS LRN_TIME,  -- 학습 시간(분),
				   CASE WHEN SUM(CASE WHEN TK.LRN_ST_CD IN ('DL', 'CL') THEN 1 ELSE 0 END) > 0 THEN 'Y'
					ELSE 'N'	
				   END AS DL_CL_EXIST,  -- DL 또는 CL 존재 여부 체크
				   TK.LRN_USR_ID AS USR_ID
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
			INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
			    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
			   AND LLU.DPTH = 1
			   AND LLU.DEL_YN = 'N'
			   AND TPC.DEL_YN = 'N'
			   AND LLU.LCKN_YN = 'N'
			LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
			    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
			   AND TPC.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
			   AND TPC.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
			   AND TK.DEL_YN = 'N'
			  AND TK.LRN_USR_ID = #{usrId}  -- 파라미터: 학생ID
			WHERE TPC.TW_WRT_DV_CD = 'T'  -- 회화첨삭구분코드(T:talk/W:write)
			  AND LLU.OPT_TXB_ID = #{optTxbId}   -- 파라미터: 운영교과서ID
			  AND LLU.KMMP_NOD_ID =  #{kmmpNodId} 
			GROUP BY LLU.KMMP_NOD_ID
		) TALK 
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTalkUsrInfo */
	</select>
	
	<!-- Let's Write 대단원 정보 조회 (드롭다운) -->
	<select id="selectEaStuAmTcrWriteLesList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrResDto">
		SELECT LLU.KMMP_NOD_ID AS kmmpNodId		-- 대단원노드ID(레슨ID)
		     , LLU.KMMP_NOD_NM 	AS kmmpNodNm		-- 대단원노드명(레슨명)
		     , TPC.TPC_KMMP_NOD_ID 	AS tpcKmmpNodId		-- 토픽노드ID 
		     , TPC.TPC_KMMP_NOD_NM	as tpcKmmpNodNm				-- 토픽노드명
		     , LLU.TC_USE_YN AS tcUseYn					-- 차시사용여부
			 , LLU.USE_YN AS useYn						-- 사용여부
			 , LLU.LCKN_YN AS lcknYn					-- 잠금여부
		     , LLU.RCSTN_ORDN AS rcstnOrdn				-- 재구성 순서
		     , RANK() OVER (PARTITION BY LLU.KMMP_NOD_ID ORDER BY TPC.TPC_KMMP_NOD_ID) AS nodRank	-- 노드랭크
		 FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND TPC.DEL_YN = 'N'
		   AND LLU.USE_YN = 'Y'
		 WHERE 1=1
		  and TPC.TW_WRT_DV_CD = 'W'								-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId} 				-- 파라미터: 운영교과서ID
		 -- group by LLU.KMMP_NOD_ID 
		   ORDER BY LLU.RCSTN_ORDN ASC, LLU.KMMP_NOD_ID asC, TPC.SRT_ORDN
		 /*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrWriteLesList */
	</select>

    <!-- 학생별 우리반수업(개념) 진도율정보 목록조회 -->
    <select id="selectEaStuSumRtTxbSpList" parameterType="Map" resultType="Map">
        SELECT
             M.USR_ID                        
            ,((SUM(M.TL_CL_CNT) + M.TCR_CNT_CL_COUNT) / (SUM(M.TL_TOT_CNT) + M.TCR_CNT_TOT_COUNT) * 100) AS TL_RATE 			
        FROM 
        (
            SELECT 
                C.USR_ID
               ,SUM(IF(D.LRN_ST_CD = 'CL', 1, 0)) AS TL_CL_CNT
               ,COUNT(1)                          AS TL_TOT_CNT  /* 학습완료건수 */
               ,0 AS SL_CL_CNT
               ,0 AS SL_TOT_CNT
               ,COALESCE(MAX(TT.TCR_CNT_TOT_COUNT), 0) AS TCR_CNT_TOT_COUNT
        	   ,COALESCE(MAX(TT.TCR_CNT_CL_COUNT), 0) AS TCR_CNT_CL_COUNT
            FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  /* TL_교과학습노드재구성(대단원) */
               INNER JOIN TL_SBC_LRN_NOD_RCSTN T  /* TL_교과학습노드재구성(차시) */
                     ON R.OPT_TXB_ID = T.OPT_TXB_ID
                     AND R.LRMP_NOD_ID = T.LLU_NOD_ID
                     AND T.USE_YN = 'Y'
               INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
                     ON R.OPT_TXB_ID = A.OPT_TXB_ID
                     AND T.LRMP_NOD_ID = A.LRMP_NOD_ID
               INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.LRN_STP_DV_CD = 'CL' /* CL=개념, WB=익힘, EX=평가*/
                    AND B.DEL_YN = 'N'
               LEFT JOIN LMS_LRM.CM_USR C /* 사용자정보 */
                    ON C.CLA_ID = #{claId}
                    AND C.USR_TP_CD = 'ST'
               LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D/* TL_교과학습활동상태 */
                    ON A.OPT_TXB_ID = D.OPT_TXB_ID
                    AND A.LRMP_NOD_ID = D.LRMP_NOD_ID
                    AND A.LRN_ATV_ID = D.LRN_ATV_ID
                    AND C.USR_ID = D.LRN_USR_ID
               LEFT JOIN (
			   		SELECT 
					    C.USR_ID,
					    COUNT(DISTINCT M.TCR_REG_CTN_ID) AS TCR_CNT_TOT_COUNT,
					     COUNT(DISTINCT CASE WHEN T.LRN_USR_ID = C.USR_ID AND T.LRN_ST_CD = 'CL' THEN M.TCR_REG_CTN_ID END) AS TCR_CNT_CL_COUNT -- 'CL' 상태의 개수
					FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn C1
					        ON M.TCR_REG_CTN_ID = C1.TCR_REG_CTN_ID
					    INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
					        ON M.OPT_TXB_ID = R.OPT_TXB_ID
					        AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
					    INNER JOIN LMS_CMS.bc_lrn_stp S
					        ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
					        AND M.LRN_STP_ID = S.LRN_STP_ID
					        AND S.DEL_YN = 'N'
					    LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
					        ON M.OPT_TXB_ID = T.OPT_TXB_ID
					        AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
					    LEFT JOIN LMS_LRM.CM_USR C  /* 사용자정보 */
					        ON C.CLA_ID = #{claId}
					        AND C.USR_TP_CD = 'ST'
					WHERE M.OPT_TXB_ID = #{optTxbId}
					AND M.DEL_YN = 'N'
					AND M.USE_YN = 'Y'
					GROUP BY C.USR_ID
			   ) TT on TT.USR_ID = C.USR_ID
            WHERE R.OPT_TXB_ID = #{optTxbId}
            AND R.USE_YN = 'Y'
            GROUP BY C.USR_ID
            <!-- 
            UNION
            SELECT 
                   SL.USR_ID
                  ,0               AS TL_CL_CNT
                  ,0               AS TL_TOT_CNT
                  ,SUM(SL.FIN_CNT) AS SL_CL_CNT
                  ,COUNT(1)        AS SL_TOT_CNT  /* 학습완료건수 */
                  ,0 as TCR_CNT_TOT_COUNT
   			   	  ,0 as TCR_CNT_CL_COUNT
            FROM
            (
                SELECT A.SP_LRN_ID
                      ,C.SP_LRN_NOD_ID
                      ,D.SP_LRN_NOD_ID AS SP_LRN_NOD_ID2
                      ,F.USR_ID
                      ,IF(0 <![CDATA[<]]> COUNT(1) AND SUM(IF(G.LRN_ST_CD = 'CL', 1, 0)) = COUNT(1), 1, 0) FIN_CNT
                FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
                     INNER JOIN LMS_LRM.SL_STU_RCM_LRN B /* SL_학생별추천학습 */
                           ON B.OPT_TXB_ID = A.OPT_TXB_ID
                           AND A.SP_LRN_ID = B.SP_LRN_ID
                           AND B.RCM_YN = 'Y'
                     INNER JOIN LMS_CMS.BC_SP_LRN_NOD C  /* BC_특별학습노드 */
                           ON A.SP_LRN_ID = C.SP_LRN_ID
                           AND C.DPTH = 1
                           AND C.DEL_YN= 'N'
                     INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 */
                           ON C.SP_LRN_ID = D.SP_LRN_ID
                           AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
                           AND D.LWS_YN = 'Y'
                           AND D.CSTN_CMPL_YN = 'Y'
                           AND D.DEL_YN= 'N'
                     INNER JOIN LMS_CMS.BC_SP_LRN_CTN E /* BC_특별학습콘텐츠 */
                           ON D.SP_LRN_NOD_ID = E.SP_LRN_NOD_ID
                           AND E.DEL_YN = 'N'
                     LEFT JOIN LMS_LRM.CM_USR F /* 사용자정보 */
                          ON F.CLA_ID = #{claId}
                          AND B.LRN_USR_ID = F.USR_ID
                          AND F.USR_TP_CD = 'ST'
                     LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST G  /* SL_특별학습진행상태 */
                          ON G.OPT_TXB_ID = A.OPT_TXB_ID
                          AND A.SP_LRN_ID = G.SP_LRN_ID
                          AND G.SP_LRN_CTN_ID = E.SP_LRN_CTN_ID
                          AND F.USR_ID = G.LRN_USR_ID
                WHERE A.OPT_TXB_ID = #{optTxbId}
                AND A.USE_YN = 'Y'
                GROUP BY A.OPT_TXB_ID
                      ,A.SP_LRN_ID
                      ,D.SP_LRN_NOD_ID
                      ,F.USR_ID
            ) SL
            GROUP BY USR_ID
             -->
        ) M
        GROUP BY USR_ID
        ORDER BY USR_ID ASC
        /*학습 현황 관리 강성희 EaLrnMgTcr-Mapper.xml - selectEaStuSumRtTxbSpList */
    </select>
    
    <!-- 학생별 선생님추천학습 진도율정보 목록조회 -->
    <select id="selectEaStuSumRtSpList" parameterType="Map" resultType="Map">
        SELECT 
			IFNULL(SL.USR_ID, U.USR_ID) AS USR_ID,  -- CM_USR에서 모든 USR_ID 포함
			IFNULL(SUM(SL.SL_CL_CNT), 0) AS SL_CL_CNT,
	        IFNULL(SUM(SL.SL_TOT_CNT), 0) AS SL_TOT_CNT,
	        IFNULL((SUM(SL.SL_CL_CNT) / NULLIF(SUM(SL.SL_TOT_CNT), 0) * 100), 0) AS SL_RATE
		FROM
		(
			SELECT 
			       SL.USR_ID
			      ,SUM(SL.FIN_CNT) AS SL_CL_CNT
			      ,COUNT(1)        AS SL_TOT_CNT  /* 학습완료건수 */
			FROM
			(
			    SELECT A.SP_LRN_ID
			          ,C.SP_LRN_NOD_ID
			          ,D.SP_LRN_NOD_ID AS SP_LRN_NOD_ID2
			          ,F.USR_ID
			          ,IF(0 <![CDATA[<]]> COUNT(1) AND SUM(IF(G.LRN_ST_CD = 'CL', 1, 0)) = COUNT(1), 1, 0) FIN_CNT
			    FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
			         INNER JOIN LMS_LRM.SL_STU_RCM_LRN B /* SL_학생별추천학습 */
			               ON B.OPT_TXB_ID = A.OPT_TXB_ID
			               AND A.SP_LRN_ID = B.SP_LRN_ID
			               AND B.RCM_YN = 'Y'
			         INNER JOIN LMS_CMS.BC_SP_LRN_NOD C  /* BC_특별학습노드 */
			               ON A.SP_LRN_ID = C.SP_LRN_ID
			               AND C.DPTH = 1
			               AND C.DEL_YN= 'N'
			         INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 */
			               ON C.SP_LRN_ID = D.SP_LRN_ID
			               AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
			               AND D.LWS_YN = 'Y'
			               AND D.CSTN_CMPL_YN = 'Y'
			               AND D.DEL_YN= 'N'
			         INNER JOIN LMS_CMS.BC_SP_LRN_CTN E /* BC_특별학습콘텐츠 */
			               ON D.SP_LRN_NOD_ID = E.SP_LRN_NOD_ID
			               AND E.DEL_YN = 'N'
			         LEFT JOIN LMS_LRM.CM_USR F /* 사용자정보 */
			              ON F.CLA_ID = #{claId}
			              AND B.LRN_USR_ID = F.USR_ID 
			              AND F.USR_TP_CD = 'ST'
			         LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST G  /* SL_특별학습진행상태 */
			              ON G.OPT_TXB_ID = A.OPT_TXB_ID
			              AND A.SP_LRN_ID = G.SP_LRN_ID
			              AND G.SP_LRN_CTN_ID = E.SP_LRN_CTN_ID
			              AND F.USR_ID = G.LRN_USR_ID
			    WHERE A.OPT_TXB_ID = #{optTxbId}
			    AND A.USE_YN = 'Y'
			    GROUP BY A.OPT_TXB_ID
			          ,A.SP_LRN_ID
			          ,D.SP_LRN_NOD_ID
			          ,F.USR_ID
			) SL
			GROUP BY USR_ID
		) SL
		RIGHT JOIN (
		    SELECT USR_ID
		    FROM LMS_LRM.CM_USR
		    WHERE USR_TP_CD = 'ST'
		      AND CLA_ID = #{claId}
		) U ON SL.USR_ID = U.USR_ID  -- 모든 학생을 포함하는 RIGHT JOIN
		GROUP BY U.USR_ID
		ORDER BY U.USR_ID ASC
        /*학습 현황 관리 강성희 EaLrnMgTcr-Mapper.xml - selectEaStuSumRtSpList */
    </select>
    
    
    <!-- 학생별 우리반수업 개념학습,익힘책, 진도율정보 목록조회 -->
    <select id="selectEaStuTcTxbList" parameterType="Map" resultType="Map">
        SELECT 
               U.USR_ID
              ,U.USR_NM
              ,U.STU_NO
              ,M.LRNR_VEL_TP_CD
              ,M.CL_RATE
              ,M.WB_RATE
              ,M.CL_TOT_RATE	/* 선생님 콘텐츠 추가로 신규 진행율 추가 */
              ,M.CNT_TOT_COUNT
              ,M.CNT_USR_COUNT
              ,M.TOTAL_LRN_SUM	/* 선생님 콘텐츠 추가로 신규 학습시간 추가 */
              ,M.WB_YN
              ,M.EX_YN
              ,M.TOT_LRN_MIN
              ,M.EV_ID
              ,M.EX_CMPL_YN
              ,M.FNL_QST_CNT
              ,M.CANS_CNT
              ,M.EV_DTL_DV_CD
              ,M.TXM_STR_YN
              ,M.EV_TM_SCNT
        FROM LMS_LRM.CM_USR U /* 사용자정보 */
        LEFT JOIN (
        			SELECT
        					AA.*,	/* 기존 데이터 */
        					COALESCE(BB.COUNT_ROWS, 0) AS CNT_TOT_COUNT,	/* 선생님이 추가한 콘텐츠 총 개수 */
        					COALESCE(CC.COUNT_LRN_USR_ID, 0) AS CNT_USR_COUNT, /* 사용자별 학습한 선생님 콘텐츠 총 개수 */
        					(((CL_COUNT + COALESCE(CC.COUNT_LRN_USR_ID, 0)) / (CL_TOT_COUNT + COALESCE(BB.COUNT_ROWS, 0))) * 100) as CL_TOT_RATE,	/* 선생님 추천 콘텐츠 포함된 진행율 */
        					COALESCE(CC.TOTAL_LRN_TMSCNT, 0) AS TOTAL_LRN_TMSCNT, /* CC에서의 총 학습 시간 */
        					(AA.TOT_LRN_MIN + COALESCE(CC.TOTAL_LRN_TMSCNT, 0)) AS TOTAL_LRN_SUM	/* 선생님 콘텐츠 학습 시간 까지 포함한 총 시간 */
        			FROM 
        			(		
        			
				         SELECT 
				            C.USR_ID
				           ,TLLL.LRNR_VEL_TP_CD
				           -- ,CEIL(SUM(IF(B.LRN_STP_DV_CD = 'CL' AND D.LRN_ST_CD = 'CL', 1, 0)) / SUM(IF(B.LRN_STP_DV_CD = 'CL', 1, 0)) * 1000)/10 AS CL_RATE /* 개념학습 진도율*/
				           -- ,CEIL(SUM(IF(B.LRN_STP_DV_CD = 'WB' AND D.LRN_ST_CD = 'CL', 1, 0)) / SUM(IF(B.LRN_STP_DV_CD = 'WB', 1, 0)) * 1000)/10 AS WB_RATE /* 익힘학습 진도율*/
				           ,(SUM(IF(B.LRN_STP_DV_CD = 'CL' AND D.LRN_ST_CD = 'CL', 1, 0)) / SUM(IF(B.LRN_STP_DV_CD = 'CL', 1, 0)) * 100) AS CL_RATE /* 개념학습 진도율 */
	       				   , (SUM(IF(B.LRN_STP_DV_CD = 'WB' AND D.LRN_ST_CD = 'CL', 1, 0)) / SUM(IF(B.LRN_STP_DV_CD = 'WB', 1, 0)) * 100) AS WB_RATE /* 익힘학습 진도율 */
	       				   
	       				   , SUM(IF(B.LRN_STP_DV_CD = 'CL' AND D.LRN_ST_CD = 'CL', 1, 0)) AS CL_COUNT /* 개념학습 완료 개수 */
					       , SUM(IF(B.LRN_STP_DV_CD = 'CL', 1, 0)) AS CL_TOT_COUNT /* 개념학습 총 개수 */
					       
				           ,IF(0 <![CDATA[<]]> SUM(IF(B.LRN_STP_DV_CD = 'WB', 1, 0)), 'Y', 'N')                                                  AS WB_YN /* 익힘책존재여부 */
				           ,IF(0 <![CDATA[<]]> SUM(IF(B.LRN_STP_DV_CD = 'EX' AND E.EV_ID IS NOT NULL, 1, 0)), 'Y', 'N')                          AS EX_YN /* 평가존재여부*/
				           ,CEIL(SUM(IFNULL(D.LRN_TM_SCNT, 0)))                                                                                  AS TOT_LRN_MIN  /* 총학습시간(초) */
				           ,MAX(IFNULL(E.EV_ID, ''))                                                                                                  AS EV_ID  /* 평가ID */
				           ,IFNULL(MAX(F.EV_CMPL_YN), 'N')                                                                                            AS EX_CMPL_YN  /* 평가- 완료여부 */
				           ,IFNULL(MAX(E.FNL_QST_CNT), '')                                                                                            AS FNL_QST_CNT /* 평가- 총문제수*/
				           ,IFNULL(MAX(F.CANS_CNT), '')                                                                                               AS CANS_CNT /* 평가-정답수 */
				           ,IFNULL(MAX(E.EV_DTL_DV_CD), '')																							 AS EV_DTL_DV_CD
				           ,MAX(F.TXM_STR_YN )																										 AS TXM_STR_YN	/* 평가응시시작여부 */
				           ,IFNULL(MAX(F.EV_TM_SCNT), '')	 			AS EV_TM_SCNT /*평가 풀이시간*/		
				           FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  /* TL_교과학습노드재구성(대단원) */	
				           INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN T  /* TL_교과학습노드재구성(차시) */
				                 ON R.OPT_TXB_ID = T.OPT_TXB_ID
				                 AND R.LRMP_NOD_ID = T.LLU_NOD_ID
				                 AND T.USE_YN = 'Y'
				           INNER JOIN 
							       (SELECT 
								    	R.OPT_TXB_ID,
								        R.LRMP_NOD_ID AS LRMP_NOD_ID,
										R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
								        R.LRN_ATV_ID,
								        R.LRN_STP_ID,
								        R.CTN_CD,
								        R.LRN_ATV_NM,
								        R.CTN_TP_CD,
								        R.USE_YN,
								        R.CLS_BRD_URL,
								        R.ORGL_LRN_STP_ID,
								        R.ORGL_ORDN,
								        R.RCSTN_ORDN,
								        R.EV_ID,
								        R.CRTR_ID,
								        R.CRT_DTM,
								        R.MDFR_ID,
								        R.MDF_DTM,
								        R.DB_ID,
								        'N' AS TCR_CTN_YN
								    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
								    WHERE R.OPT_TXB_ID = #{optTxbId}
								      AND R.LRMP_NOD_ID = #{tcNodId}
								      
									UNION ALL
									
								    SELECT 
								        R.OPT_TXB_ID,
								        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
										R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
								        R.LRN_ATV_ID,
								        TTRCM.LRN_STP_ID,
								        R.CTN_CD,
								        R.LRN_ATV_NM,
								        R.CTN_TP_CD,
								        TTRCM.USE_YN,
								        R.CLS_BRD_URL,
								        R.ORGL_LRN_STP_ID,
								        R.ORGL_ORDN,
								        TTRCM.RCSTN_ORDN,
								        R.EV_ID,
								        R.CRTR_ID,
								        R.CRT_DTM,
								        R.MDFR_ID,
								        R.MDF_DTM,
								        R.DB_ID,
								        'Y' AS TCR_CTN_YN
								    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
								    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
								        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
								        and ttrcm.del_yn = 'N'
								    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
								        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
								    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
								      AND TTRCM.LRMP_NOD_ID = #{tcNodId}
								      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
								      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
				                 ON R.OPT_TXB_ID = A.OPT_TXB_ID
				                 AND T.LRMP_NOD_ID = A.LRMP_NOD_ID
				                 AND A.USE_YN = 'Y'
				           INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
				                ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
				                AND A.LRN_STP_ID = B.LRN_STP_ID
				                AND B.DEL_YN = 'N'
				           LEFT JOIN LMS_LRM.CM_USR C /* 사용자정보 */
				                ON C.CLA_ID = #{claId}
				                AND C.USR_TP_CD = 'ST'
				           LEFT JOIN LMS_LRM.TL_LU_LRNR_LV TLLL	/* 단원별 학습자 수준 */
				           		ON R.LRMP_NOD_ID = TLLL.LLU_NOD_ID
				           		AND C.USR_ID = TLLL.USR_ID
				           LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D/* TL_교과학습활동상태 */
				                ON A.OPT_TXB_ID = D.OPT_TXB_ID
				                AND A.ORGL_LRMP_NOD_ID = D.LRMP_NOD_ID
				                AND A.LRN_ATV_ID = D.LRN_ATV_ID
				                AND C.USR_ID = D.LRN_USR_ID
				           LEFT JOIN LMS_LRM.EA_EV E
				                ON A.EV_ID = E.EV_ID
				                AND E.USE_YN = 'Y'
				                AND E.DEL_YN = 'N'
				           LEFT JOIN LMS_LRM.EA_EV_RS F
				                ON A.EV_ID = F.EV_ID
				                AND F.USR_ID = C.USR_ID
				        WHERE R.OPT_TXB_ID = #{optTxbId}	
				        AND R.LRMP_NOD_ID = #{lluNodId}  /* 대단원ID */
				        AND T.LRMP_NOD_ID = #{tcNodId}  /* 차시ID */
				        AND R.USE_YN = 'Y'
				        AND R.DPTH = 1
				        GROUP BY T.LLU_NOD_ID, T.LRMP_NOD_ID, C.USR_ID, TLLL.LRNR_VEL_TP_CD
				    )AA
				    /* 신규로 선생님 콘텐츠 활동 추가 */
			    	LEFT JOIN 
					(
					    SELECT 
					        M.lrmp_nod_id,COUNT(M.TCR_REG_CTN_ID) AS COUNT_ROWS /* TCR_REG_CTN_ID의 개수 */
					    FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn C ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID AND C.TP_CD <![CDATA[<>]]> 'AT'
					    INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R ON M.OPT_TXB_ID = R.OPT_TXB_ID AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
					    WHERE M.OPT_TXB_ID = #{optTxbId}
					        AND R.LRMP_NOD_ID = #{tcNodId}
					        AND M.DEL_YN = 'N'
					        AND M.USE_YN = 'Y'
					    GROUP BY M.LRMP_NOD_ID
					) BB ON BB.LRMP_NOD_ID = #{tcNodId}
					
					LEFT JOIN 
					(
					    SELECT 
					         M.lrmp_nod_id,T.LRN_USR_ID, SUM(IF(T.LRN_ST_CD = 'CL', 1, 0)) AS COUNT_LRN_USR_ID, SUM(T.LRN_TM_SCNT) AS TOTAL_LRN_TMSCNT 
					    FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn C ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID AND C.TP_CD <![CDATA[<>]]> 'AT'
					    INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R ON M.OPT_TXB_ID = R.OPT_TXB_ID AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
					    LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T ON M.OPT_TXB_ID = T.OPT_TXB_ID AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
					    WHERE M.OPT_TXB_ID = #{optTxbId}
					        AND R.LRMP_NOD_ID = #{tcNodId}
					        AND M.DEL_YN = 'N'
					        AND M.USE_YN = 'Y'
					      GROUP BY M.lrmp_nod_id, T.LRN_USR_ID
					) CC ON CC.LRN_USR_ID = AA.USR_ID
		)M
             ON U.USR_ID = M.USR_ID
        WHERE U.CLA_ID = #{claId}
        AND U.USR_TP_CD = 'ST'
        ORDER BY U.STU_NO, U.USR_ID ASC
        /*학습 현황 관리 강성희 EaLrnMgTcr-Mapper.xml - selectEaStuTcTxbList */
    </select>

    <!-- 학생별 추천학습 건수/학습시간 목록조회 -->
    <select id="selectEaStuRcmLrnList" parameterType="Map" resultType="Map">
        SELECT 
               U.USR_ID
              ,U.USR_NM
              ,U.LRNR_VEL_TP_CD
              ,U.STU_NO
              ,M.CNT_RMD
              ,M.LRN_TM_SEC
              ,M.LAST_LRN_YMD
        FROM LMS_LRM.CM_USR U /* 사용자정보 */
             LEFT JOIN (
                SELECT IU.USR_ID
                      ,COUNT(1)                                  AS CNT_RMD /* 추천학습수 */
                      -- ,CEIL(SUM(IFNULL(IC.LRN_TM_SCNT, 0))) AS LRN_TM_MIN /* 학습시간(초) */
                      , IFNULL(IC.LRN_TM_SCNT, 0) AS LRN_TM_SEC
                      ,DATE_FORMAT(MAX(IC.MDF_DTM), '%m. %d.')  AS LAST_LRN_YMD /* 마지막학습일자 */
                FROM LMS_LRM.SL_SP_LRN_RCSTN IA /* SL_특별학습재구성 */
                     INNER JOIN LMS_LRM.SL_STU_RCM_LRN IB /* SL_학생별추천학습 */
                           ON IB.OPT_TXB_ID = IA.OPT_TXB_ID
                           AND IA.SP_LRN_ID = IB.SP_LRN_ID
                           AND IB.RCM_YN = 'Y'
                     INNER JOIN LMS_LRM.CM_USR IU /* 사용자정보 */
                           ON IU.CLA_ID = #{claId}
                           AND IU.USR_TP_CD = 'ST'
                           AND IB.LRN_USR_ID = IU.USR_ID
                     LEFT JOIN (
                        SELECT IIA.LRN_USR_ID 
                              -- ,IIG.LRN_TM_SCNT   AS LRN_TM_SCNT
                              ,SUM(IIG.LRN_TM_SCNT)   AS LRN_TM_SCNT
                              ,MAX(IIG.MDF_DTM)  AS MDF_DTM
                        FROM LMS_LRM.SL_STU_RCM_LRN IIA /* SL_학생별추천학습 */
                                 INNER JOIN LMS_CMS.BC_SP_LRN_NOD IIC /* BC_특별학습노드 */
                                       ON IIA.SP_LRN_ID = IIC.SP_LRN_ID
                                       AND IIC.DPTH = 1
                                       AND IIC.DEL_YN= 'N'
                                 INNER JOIN LMS_CMS.BC_SP_LRN_NOD IID /* BC_특별학습노드 */
                                       ON IIC.SP_LRN_ID = IID.SP_LRN_ID
                                       AND IIC.SP_LRN_NOD_ID = IID.URNK_SP_LRN_NOD_ID
                                       AND IID.DPTH = 2
                                       AND IID.CSTN_CMPL_YN = 'Y'
                                       AND IID.DEL_YN= 'N'
                                 INNER JOIN LMS_CMS.BC_SP_LRN_CTN IIE /* BC_특별학습콘텐츠 */
                                       ON IID.SP_LRN_NOD_ID = IIE.SP_LRN_NOD_ID
                                       AND IIE.DEL_YN = 'N'
                                 LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST IIG  /* SL_특별학습진행상태 */
                                      ON IIA.OPT_TXB_ID = IIG.OPT_TXB_ID
                                      AND IIA.SP_LRN_ID = IIG.SP_LRN_ID
                                      AND IIG.SP_LRN_CTN_ID = IIE.SP_LRN_CTN_ID
                                      AND IIA.LRN_USR_ID = IIG.LRN_USR_ID
                        WHERE IIA.OPT_TXB_ID = #{optTxbId}
                        AND IIA.RCM_YN = 'Y'
                        GROUP BY IIA.LRN_USR_ID
                     ) IC
                     ON IU.USR_ID = IC.LRN_USR_ID
                WHERE IA.OPT_TXB_ID = #{optTxbId}
                AND IA.USE_YN = 'Y'
                GROUP BY IU.USR_ID
             ) M
             ON U.USR_ID = M.USR_ID
        WHERE U.CLA_ID = #{claId}
        AND U.USR_TP_CD = 'ST'
        ORDER BY U.STU_NO, U.USR_ID ASC;

        /*학습 현황 관리 강성희 EaLrnMgTcr-Mapper.xml - selectEaStuRcmLrnList */
    </select>
	
	<!-- 학생별 추천학습 진도율정보 상세목록조회 -->
    <select id="selectEaStuRcmLrnDtlList" parameterType="Map" resultType="Map">
       SELECT 
			F.SP_LRN_NOD_ID
			,F.SP_LRN_ID
			,F.SP_LRN_NM
			,F.RCSTN_ORDN
			,F.USR_ID
			,IFNULL(COUNT(F.SP_LRN_CTN_ID),0) as TOT_CNT
			,IFNULL(SUM(F.LRN_ST_CD = 'CL'),0) as TOT_CNT_CL
		FROM
		(
			SELECT
				   A.SP_LRN_ID as SP_LRN_ID
		          ,A.SP_LRN_NM	as SP_LRN_NM
		          ,A.RCSTN_ORDN as RCSTN_ORDN
		          ,C.SP_LRN_NOD_ID	as SP_LRN_NOD_ID
		          ,B.LRN_USR_ID AS USR_ID
		          ,E.SP_LRN_CTN_ID as SP_LRN_CTN_ID
		--          ,G.SP_LRN_CTN_ID as SP_LRN_CTN_ID2
		          ,G.LRN_ST_CD as LRN_ST_CD
		          ,(SUM(IF(G.LRN_ST_CD = 'CL', 1, 0))) as SUM_COUNT
		          , IF(SUM(G.SUM_TOT), 1, 0) as SUM_COUNT2
		          ,B.LRN_USR_ID as LRN_USR_ID
		     FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
		     INNER JOIN LMS_LRM.SL_STU_RCM_LRN B /* SL_학생별추천학습 */
		           ON B.OPT_TXB_ID = A.OPT_TXB_ID
		           AND A.SP_LRN_ID = B.SP_LRN_ID
		           AND B.RCM_YN = 'Y'
		     INNER JOIN LMS_CMS.BC_SP_LRN_NOD C /* BC_특별학습노드 */
		           ON A.SP_LRN_ID = C.SP_LRN_ID
		           AND C.DPTH = 1
		           AND C.DEL_YN= 'N'
		     INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 */
		           ON C.SP_LRN_ID = D.SP_LRN_ID
		           AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
		           AND D.DPTH = 2
		           AND D.CSTN_CMPL_YN = 'Y'
		           AND D.DEL_YN= 'N'
		     INNER JOIN LMS_CMS.BC_SP_LRN_CTN E /* BC_특별학습콘텐츠 */
		           ON D.SP_LRN_NOD_ID = E.SP_LRN_NOD_ID
		           AND E.DEL_YN = 'N'
		     LEFT JOIN (
		     	 SELECT 
					 	R.OPT_TXB_ID,R.SP_LRN_ID, BC.SP_LRN_CTN_ID as SP_LRN_CTN_ID ,S.LRN_USR_ID ,S.LRN_ST_CD ,(SUM(IF(BC.SP_LRN_CTN_ID, 1, 0))) as SUM_TOT
					FROM LMS_CMS.BC_SP_LRN_NOD B
					INNER JOIN LMS_CMS.BC_SP_LRN_CTN BC ON B.SP_LRN_NOD_ID = BC.SP_LRN_NOD_ID AND BC.DEL_YN = 'N'
					LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN R ON B.SP_LRN_ID = R.SP_LRN_ID
					LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST S ON BC.SP_LRN_CTN_ID = S.SP_LRN_CTN_ID AND S.OPT_TXB_ID = #{optTxbId}
					WHERE 1=1
					AND R.OPT_TXB_ID = #{optTxbId}
					GROUP BY BC.SP_LRN_CTN_ID, S.LRN_USR_ID
		     ) G ON  A.OPT_TXB_ID = G.OPT_TXB_ID
		     	 AND A.SP_LRN_ID = G.SP_LRN_ID
		         AND G.SP_LRN_CTN_ID = E.SP_LRN_CTN_ID
		        AND B.LRN_USR_ID = G.LRN_USR_ID
		    WHERE A.OPT_TXB_ID = #{optTxbId}
		    AND A.USE_YN = 'Y'
		  	GROUP BY E.SP_LRN_CTN_ID, B.LRN_USR_ID, A.SP_LRN_ID, C.SP_LRN_NOD_ID
		 )F
		GROUP by F.USR_ID, F.SP_LRN_ID, F.SP_LRN_NOD_ID
		ORDER BY F.USR_ID ASC, F.RCSTN_ORDN ASC, F.SP_LRN_ID ASC, F.SP_LRN_NOD_ID ASC

        /*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuRcmLrnDtlList */
    </select>
    
    <!-- 학생별 추천학습 진도율정보 상세목록조회 -->
    <select id="selectEaStuRcmLrnDtlList2" parameterType="Map" resultType="Map">
        SELECT A.SP_LRN_ID
              ,A.SP_LRN_NM
              ,C.SP_LRN_NOD_ID
              ,B.LRN_USR_ID AS USR_ID
           --   ,IF(0 <![CDATA[<]]> COUNT(1) AND SUM(IF(G.LRN_ST_CD = 'CL', 1, 0)) = COUNT(1), 'Y', 'N') AS FIN_YN
              ,IF(SUM(IF(G.LRN_ST_CD = 'CL', 1, 0)) = COUNT(1), 'Y', 'N') AS FIN_YN
        FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
             INNER JOIN LMS_LRM.SL_STU_RCM_LRN B /* SL_학생별추천학습 */
                   ON B.OPT_TXB_ID = A.OPT_TXB_ID
                   AND A.SP_LRN_ID = B.SP_LRN_ID
                   AND B.RCM_YN = 'Y'
             INNER JOIN LMS_CMS.BC_SP_LRN_NOD C /* BC_특별학습노드 */
                   ON A.SP_LRN_ID = C.SP_LRN_ID
                   AND C.DPTH = 1
                   AND C.DEL_YN= 'N'
             INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 */
                   ON C.SP_LRN_ID = D.SP_LRN_ID
                   AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
                   AND D.DPTH = 2
                   AND D.CSTN_CMPL_YN = 'Y'
                   AND D.DEL_YN= 'N'
             INNER JOIN LMS_CMS.BC_SP_LRN_CTN E /* BC_특별학습콘텐츠 */
                   ON D.SP_LRN_NOD_ID = E.SP_LRN_NOD_ID
                   AND E.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST G /* SL_특별학습진행상태 */
                  ON  A.OPT_TXB_ID = G.OPT_TXB_ID
                  AND A.SP_LRN_ID = G.SP_LRN_ID
                  AND G.SP_LRN_CTN_ID = E.SP_LRN_CTN_ID
                  AND B.LRN_USR_ID = G.LRN_USR_ID
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.USE_YN = 'Y'
        GROUP BY B.LRN_USR_ID, A.SP_LRN_ID, C.SP_LRN_NOD_ID
        ORDER BY B.LRN_USR_ID ASC, A.RCSTN_ORDN ASC, A.SP_LRN_ID ASC, C.SP_LRN_NOD_ID ASC

        /*학습 현황 관리 강성희 EaLrnMgTcr-Mapper.xml - selectEaStuRcmLrnDtlList */
    </select>

	<!-- 학생별 LET'S Wrtie 데이터 조회 -->
	<select id="selectEaStuAmTcrWriteList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrWriteResDto">
		SELECT
			 WR.LLU_KMMP_NOD_ID AS LLU_KMMP_NOD_ID
			, WR.LLU_KMMP_NOD_NM AS LLU_KMMP_NOD_NM
			, WR.USR_ID AS USR_ID
			, IFNULL(WR.TOTAL_TOPICS, '-') AS TOT_TOPICS_COUNT
			, IFNULL(WR.COMP_TOPICS, '-') AS COMPL_TOPICS_COUNT
			, IFNULL(WR.COMP_TOPICS * 100 / WR.TOTAL_TOPICS, '-') AS PROGRESS_RATE
			-- , IFNULL(WR.TOTAL_TOPICS, '-') AS PROGRESS_RATE
			, IFNULL(DATE_FORMAT(WR.LAST_DTM, '%Y. %m. %d.'), '-') AS LAST_UPDATED
			, WR.WRT_ST_NM AS WRT_ST_NM
			, WR.EDIT_ST_NM AS EDIT_ST_NM
			, WR.AP_FAIL_YN AS AP_FAIL_YN
			, WR.PGRS_ST_CD AS PGRS_ST_CD
			, WR.TCR_SAV_DTM AS TCR_SAV_DTM
			, IFNULL(WR.LRN_TM_SCNT, '-') AS LRN_TM_SCNT
		FROM		
		(
			SELECT 
				TPC.LLU_KMMP_NOD_ID as LLU_KMMP_NOD_ID					-- 대단원 노드 ID(레슨ID)
				, LLU.KMMP_NOD_NM AS LLU_KMMP_NOD_NM 	-- 대단원 노드명 (레슨명)
				, COUNT(TPC.TPC_KMMP_NOD_ID) AS TOTAL_TOPICS 	-- 총 토픽 개수
				, SUM(CASE WHEN WRT.PGRS_ST_CD != 'LN' THEN 1 ELSE 0 END) AS COMP_TOPICS  -- 완료된 토픽 개수
				, CASE WHEN max(WRT.PGRS_ST_CD) in ('SM','EC','FC','AP') then 50
				       WHEN max(WRT.PGRS_ST_CD) = 'CC' then 100
				       ELSE '-'
				    END AS PROGRESS_RATE
				, CASE WHEN max(WRT.PGRS_ST_CD) IS NULL THEN '미제출'
						WHEN max(WRT.PGRS_ST_CD) IN ('LN') THEN '미제출'
				ELSE '제출'
				END WRT_ST_NM					-- 글쓰기 제출 여부
				, CASE WHEN max(WRT.PGRS_ST_CD) = 'CC' THEN 'O'
				ELSE 'X'-- 첨삭 확인완료
				END EDIT_ST_NM					-- 첨삭 확인 여부
				, CASE WHEN max(WRT.PGRS_ST_CD) = 'AP'
					THEN (CASE WHEN max(wrt.mdf_dtm) <![CDATA[ < ]]> now() - interval 10 minute THEN 'Y' ELSE 'N' END)
				ELSE 'N'
				END AS AP_FAIL_YN		-- 첨삭 확인 여부 NEW1
				, max(WRT.PGRS_ST_CD) AS PGRS_ST_CD
				, max(WRT.TCR_SAV_DTM) AS TCR_SAV_DTM	-- 선생님 저장 일자
				, IFNULL(DATE_FORMAT(IFNULL(max(WRT.STU_SAV_DTM), max(WRT.CRT_DTM)), '%Y. %m. %d.'), '-')  AS LAST_DTM		-- 마지막 학습일
				, LLU.TC_USE_YN					-- 차시사용여부
				, LLU.LCKN_YN					-- 잠금여부
				, LLU.USE_YN					-- 사용여부
				, max(WRT.STU_USR_ID) as USR_ID		
				, max(WRT.LRN_TM_SCNT) AS LRN_TM_SCNT	-- 학습 시간
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
			INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID
												AND LLU.DPTH = 1
												AND LLU.DEL_YN = 'N'
												AND TPC.DEL_YN = 'N'
			LEFT OUTER JOIN LMS_LRM.CM_WRT_MG WRT ON LLU.OPT_TXB_ID = WRT.OPT_TXB_ID
												AND TPC.LLU_KMMP_NOD_ID = WRT.LLU_KMMP_NOD_ID
												AND TPC.TPC_KMMP_NOD_ID = WRT.TPC_KMMP_NOD_ID
												AND WRT.DEL_YN = 'N'
												AND WRT.STU_USR_ID = #{usrId}
			WHERE TPC.TW_WRT_DV_CD = 'W'		 -- 회화첨삭구분코드(T:talk/W:write)
			AND LLU.OPT_TXB_ID = #{optTxbId}
			AND TPC.LLU_KMMP_NOD_ID = #{kmmpNodId}		
			ORDER BY LLU.RCSTN_ORDN, TPC.SRT_ORDN
		) WR
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrWriteList */
	</select>
	
	<!-- 교사의 과제 학급 조회 -->
	<select id="selectEaStuAmTcrEaList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEaResDto">
		SELECT ROW_NUMBER() OVER (ORDER BY A.USR_NM) AS ROW_NUM
		      ,A.USR_ID AS USR_ID
			  ,A.USR_NM AS USR_NM
			  ,A.STU_NO AS STU_NO
			  ,A.CLA_ID AS CLA_ID
			  ,A.TOTAL_ASSIGNMENTS AS TOTAL_ASSIGNMENTS
			  ,A.COMPLETED_ASSIGNMENTS AS COMPLETED_ASSIGNMENTS
			  ,A.ONGOING_ASSIGNMENTS AS ONGOING_ASSIGNMENTS
			  ,(TOTAL_ASSIGNMENTS - COMPLETED_ASSIGNMENTS)AS UNSUBMITTED_ASSIGNMENT
			  ,A.ASN_TP_CD AS ASN_TP_CD
			  ,A.LRN_TP_CD AS LRN_TP_CD
			  ,A.ASN_LRN_TP AS ASN_LRN_TP
			  ,CASE 
		          WHEN A.TOTAL_ASSIGNMENTS = 0 THEN '0%'
		          ELSE CONCAT(ROUND((A.COMPLETED_ASSIGNMENTS * 100) / A.TOTAL_ASSIGNMENTS, 2), '%')
		       END AS COMPLETION_PERCENTAGE
		FROM (
		    SELECT
			        U.USR_ID,
			        MAX(U.STU_NO) AS STU_NO,
			        MAX(U.USR_NM) AS USR_NM,
			        MAX(U.CLA_ID) AS CLA_ID,
			        COUNT(EA.ASN_ID) AS total_assignments,
			        COUNT(CASE
			            /*    WHEN EA.END_DTM  <![CDATA[ < ]]>  NOW() OR EGAS.SMT_CMPL_YN = 'Y' THEN EA.ASN_ID */
			            	WHEN EGAS.SMT_CMPL_YN = 'Y' THEN EA.ASN_ID
			            END) AS completed_assignments,
			        COUNT(CASE
			                WHEN (EA.STR_DTM <![CDATA[ <= ]]> NOW() AND EA.END_DTM > NOW() AND EGAS.SMT_CMPL_YN = 'N')  
		                    OR (EA.ASN_PTME_DV_CD = 'OT' AND EGAS.SMT_CMPL_YN = 'N') THEN EA.ASN_ID
			            END) AS ongoing_assignments,
			        MAX(EA.ASN_TP_CD) AS ASN_TP_CD,
			        MAX(EA.LRN_TP_CD) AS LRN_TP_CD,
			        CASE
			            WHEN MAX(EA.ASN_TP_CD) IS NOT NULL AND MAX(EA.LRN_TP_CD) IS NULL THEN MAX(EA.ASN_TP_CD)
			            WHEN MAX(EA.LRN_TP_CD) IS NOT NULL AND MAX(EA.ASN_TP_CD) IS NULL THEN MAX(EA.LRN_TP_CD)
			        END AS ASN_LRN_TP
		    FROM CM_USR U
		    LEFT JOIN EA_ASN_SMT EGAS
		      ON U.USR_ID = EGAS.STU_USR_ID
		    LEFT JOIN EA_ASN EA
		      ON EGAS.ASN_ID = EA.ASN_ID
		      AND EA.OPT_TXB_ID = #{optTxbId}
		     AND EA.DEL_YN = 'N'
		     AND EA.USE_YN ='Y'
		   WHERE 1=1
		  --   AND U.USR_ID = EGAS.STU_USR_ID
		     AND U.CLA_ID = #{claId}
		     AND U.USR_TP_CD = 'ST'
		   GROUP BY U.USR_ID
		) AS A
		ORDER BY A.STU_NO,  A.USR_ID	
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrEaList */
	</select>

	<!-- 학생별 평가 데이터 조회 -->
	<select id="selectEaStuAmTcrEvList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEvResDto">
		SELECT
			   USR.USR_ID AS USR_ID
			 , USR.USR_NM AS USR_NM
			 , USR.STU_NO AS STU_NO
			 , USR.LRNR_VEL_TP_CD
			 , ROW_NUMBER() OVER(ORDER BY USR.USR_NM) 	AS rowNo  	 	-- 번호
			 , USR.EV_CNT 				AS evCnt -- 평가 전체 개수
			 , USR.cmplCnt 				AS evCmplCnt   	-- 사용자별 평가완료 개수
			 , USR.EV_CNT - USR.cmplCnt	AS evNonTxmCnt 	-- 사용자별 미응시 개수
			 , USR.evPgrsCnt 			AS evPgrsCnt   	-- 사용자별 진행 개수	 
			 , CASE WHEN USR.EV_CNT > 0 THEN ROUND((USR.cmplCnt / USR.EV_CNT) * 100, 2) ELSE 0 END AS progressRate -- 완료율(%)
		FROM (
					SELECT USR.USR_ID
					     , MAX(USR.USR_NM) USR_NM
					     , MAX(OT.OPT_TXB_ID) OPT_TXB_ID
						 , MAX(USR.LRNR_VEL_TP_CD) LRNR_VEL_TP_CD
						 , MAX(USR.STU_NO) STU_NO
						 , COUNT(ER.EV_ID) EV_CNT
					     , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) AS cmplCnt -- 완료개수
					     , SUM(CASE WHEN IFNULL(ER.EV_CMPL_YN, 'N') = 'N' AND E.LCKN_YN = 'N' AND (E.TXM_PTME_SETM_YN = 'N' OR (CURRENT_TIMESTAMP BETWEEN E.TXM_STR_DTM AND E.TXM_END_DTM)) THEN 1 ELSE 0 END) evPgrsCnt -- 진행중
					FROM LMS_LRM.CM_OPT_TXB OT
					JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID AND USR.USR_TP_CD = 'ST'
					LEFT JOIN LMS_LRM.EA_EV E ON E.OPT_TXB_ID = OT.OPT_TXB_ID AND E.EV_DV_CD IN ('SE', 'TE') AND E.DEL_YN = 'N' AND E.USE_YN = 'Y'
					LEFT JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID AND ER.STU_EV_ABLE_YN = 'Y'
					WHERE OT.OPT_TXB_ID = #{optTxbId}
					GROUP BY USR.USR_ID	
		) USR
		
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrEvList */
	</select>
	
	<select id="selectEaStuAmTcrEaDetail" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgEaAsnStuDetailDto">
		/* 학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrEaDetail */
		<include refid="api.ea.common.pagingHeader"/>
		SELECT			
			 MAX(ASN.ASN_ID) AS ASN_ID	 
			,MAX(ASN.OPT_TXB_ID) AS OPT_TXB_ID
			,MAX(ASN.TCR_USR_ID) AS TCR_USR_ID
			,MAX(ASN.ASN_NM) AS ASN_NM
			,MAX(ASN.ASN_CN) AS ASN_CN
			,MAX(ASN.ASN_TP_CD) AS ASN_TP_CD
			,MAX(ASN.LRN_TP_CD) AS LRN_TP_CD
			,MAX(ASN.ASN_LRN_TP) AS ASN_LRN_TP
			,MAX(ASN.ASN_LRN_TP_NM) AS ASN_LRN_TP_NM
			,MAX(ASN.ASN_PTME_DV_CD) AS ASN_PTME_DV_CD
			,MAX(ASN.STR_DTM) AS STR_DTM	
			,MAX(ASN.STR_DTM_NM) AS STR_DTM_NM
			,MAX(ASN.END_DTM) AS END_DTM
			,MAX(ASN.END_DTM_NM) AS END_DTM_NM
			,MAX(ASN.FIN_AF_SMT_ABLE_YN) AS FIN_AF_SMT_ABLE_YN
			,MAX(ASN.EV_MTHD_TP_CD) AS EV_MTHD_TP_CD
			,MAX(ASN.PSC_SCR) AS PSC_SCR
			,MAX(ASN.DEL_YN) AS DEL_YN
			,MAX(ASN.CRTR_ID) AS CRTR_ID
			,MAX(ASN.CRT_DTM) AS CRT_DTM 
			,MAX(ASN.MDFR_ID) AS MDFR_ID
			,MAX(ASN.MDF_DTM) AS MDF_DTM
			,MAX(ASN.STU_USR_ID) AS STU_USR_ID
			,MAX(ASN.SMT_DTM) AS SMT_DTM
			,MAX(ASN.SMT_CN) AS SMT_CN 
			,MAX(ASN.ANNX_ID) AS ANNX_ID
			,MAX(ASN.SMT_CMPL_YN) AS SMT_CMPL_YN
			,MAX(ASN.SCR) AS SCR
			,MAX(ASN.FDBK_CN) AS FDBK_CN
			,MAX(ASN.SP_LRN_ID) AS SP_LRN_ID
			,MAX(ASN.LRN_STP_DV_CD) AS LRN_STP_DV_CD
			,MAX(ASN.LU_NOD_ID) AS LU_NOD_ID
			,MAX(ASN.TC_NOD_ID) AS TC_NOD_ID
		    ,MAX(ASN.NOD_NM) AS NOD_NM
			,MAX(ASN.RCSTN_ORDN) AS RCSTN_ORDN
			,MAX(ASN.GRP_ID) AS GRP_ID
		    ,MAX(ASN.GRP_TEM_ID) AS GRP_TEM_ID
		FROM
		(
			SELECT EA.ASN_ID			-- 과제ID
				  ,EA.OPT_TXB_ID 		-- 운영교과서ID
				  ,EA.TCR_USR_ID 		-- 교사사용자ID
				  ,EA.ASN_NM 			-- 과제명
				  ,EA.ASN_CN 			-- 과제설명
				  ,EA.ASN_TP_CD 		-- 과제유형코드
				  ,EA.LRN_TP_CD 		-- 학습유형코드
				  ,CASE
		   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL THEN EA.ASN_TP_CD
			   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL THEN EA.LRN_TP_CD
				   END AS ASN_LRN_TP	-- 유형코드
				  ,CASE
		   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL
			  	 	      THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.ASN_TP_CD AND CM.URNK_CM_CD ='ASN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
			   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL
			   			  THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.LRN_TP_CD AND CM.URNK_CM_CD ='LRN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
				   END AS ASN_LRN_TP_NM	-- 유형코드이름
				  ,EA.ASN_PTME_DV_CD 	-- 과제기간구분코드
				  ,EA.STR_DTM 			-- 시작일시
				  ,CONCAT(DATE_FORMAT(EA.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.STR_DTM,'%h:%i')) AS STR_DTM_NM
				  ,EA.END_DTM 			-- 종료일시
				  ,CONCAT(DATE_FORMAT(EA.END_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.END_DTM,'%h:%i')) AS END_DTM_NM
				  ,EA.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
				  ,EA.EV_MTHD_TP_CD		-- 평가방식유형코드
				  ,EA.PSC_SCR			-- 만점점수
				  ,EA.DEL_YN 			-- 삭제여부
				  ,EA.CRTR_ID 			-- 생성자ID
				  ,EA.CRT_DTM 			-- 생성일시
				  ,EA.MDFR_ID 			-- 수정자ID
				  ,EA.MDF_DTM 			-- 수정일시
				  ,EGAS.STU_USR_ID 		-- 학생사용자ID
				  ,CONCAT(DATE_FORMAT(EGAS.SMT_DTM,'%m. %d. '),IF(TIME_FORMAT(EGAS.SMT_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EGAS.SMT_DTM,'%h:%i')) AS SMT_DTM
				  -- 제출일시
				  ,EGAS.SMT_CN 			-- 제출내용
				  ,EGAS.ANNX_ID 		-- 첨부ID
				  ,EGAS.SMT_CMPL_YN 	-- 제출완료여부
				  ,EGAS.SCR 			-- 점수
				  ,EGAS.FDBK_CN 		-- 피드백내용
				  ,EAR.SP_LRN_ID		-- 특별학습ID
				  ,EAR.LRN_STP_DV_CD	-- 학습단계구분코드
				  ,EAR.LU_NOD_ID		-- 단원노드ID
				  ,EAR.TC_NOD_ID		-- 차시노드ID
			      ,CASE
				 		WHEN EA.LRN_TP_CD = 'TL'
				 			THEN CONCAT(TSLNR.LRMP_NOD_NM ,' > ',TS.LRMP_NOD_NM)
				 		WHEN EA.LRN_TP_CD = 'SL' THEN SSLR.SP_LRN_NM
				 		WHEN EA.LRN_TP_CD = 'AL'
				 			THEN CONCAT(AKNR.KMMP_NOD_NM ,' > ',AK.KMMP_NOD_NM)
				 		 WHEN EA.ASN_TP_CD = 'GE' AND EAR.LU_NOD_ID IS NOT NULL
		 			    	THEN CONCAT(TSLNR.LRMP_NOD_NM ,' > ',TS.LRMP_NOD_NM)
		 			    WHEN EA.ASN_TP_CD = 'GR' AND EAR.LU_NOD_ID IS NOT NULL
		 			    	THEN CONCAT(TSLNR.LRMP_NOD_NM ,' > ',TS.LRMP_NOD_NM)
				    END NOD_NM -- (단원,차시명)
				  ,TSLNR.RCSTN_ORDN
				  ,EGA.GRP_ID
		          ,EGA.GRP_TEM_ID 
			  FROM LMS_LRM.EA_ASN_SMT EGAS -- EA_과제제출
			 INNER JOIN LMS_LRM.EA_ASN EA	-- EA_과제
			    ON EGAS.ASN_ID = EA.ASN_ID
			   AND EGAS.STU_USR_ID = #{usrId}
			   AND EA.OPT_TXB_ID = #{optTxbId}
			   AND EA.DEL_YN = 'N'
			   AND EA.USE_YN = 'Y'
			  LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
			    ON EGAS.ASN_ID = EAR.ASN_ID
			    AND EAR.DEL_YN = 'N'
		      LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR	-- TL_교과학습노드재구성
	            ON EA.OPT_TXB_ID = TSLNR.OPT_TXB_ID
		       AND EAR.LU_NOD_ID = TSLNR.LRMP_NOD_ID
		       AND TSLNR.USE_YN ='Y'
		       AND IFNULL(TSLNR.URNK_LRMP_NOD_ID, '') = ''
	          LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TS
			    ON EAR.OPT_TXB_ID = TS.OPT_TXB_ID
			   AND EAR.TC_NOD_ID = TS.LRMP_NOD_ID
		      LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN SSLR -- TL_특별학습노드재구성
		        ON EA.OPT_TXB_ID = SSLR.OPT_TXB_ID
		       AND EAR.SP_LRN_ID = SSLR.SP_LRN_ID
		       AND SSLR.USE_YN ='Y'
		      LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR -- AI_지식맵노드재구성
		        ON EA.OPT_TXB_ID = AKNR.OPT_TXB_ID
		       AND EAR.LU_NOD_ID = AKNR.KMMP_NOD_ID
		       AND AKNR.DEL_YN ='N'
		      LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AK
		        ON EA.OPT_TXB_ID = AK.OPT_TXB_ID
		       AND EAR.TC_NOD_ID = AK.KMMP_NOD_ID
		       AND AK.DEL_YN ='N'
		       LEFT JOIN EA_GRP_ASN_SMT EGA
		        ON EA.ASN_ID = EGA.ASN_ID 
		      LEFT JOIN EA_GRP_TMBR EGT 
		        ON EGA.GRP_ID = EGT.GRP_ID 
		       AND EGA.GRP_TEM_ID = EGT.GRP_TEM_ID 
		       AND EGT.STU_USR_ID = EGAS.STU_USR_ID
	     ) ASN
		 WHERE 1=1
		<!-- 미완료 여부 -->
		<if test = 'ncmplYn != null and  ncmplYn.equals("Y")'>
			AND  ASN.SMT_CMPL_YN = 'N'
		</if>
		
		<!-- 단원 유형 구분 -->
		<if test = 'dropdown1 != null and dropdown1.equals("luNodId") and !dropdown2.equals("all")' >
			<!--  AND  ASN.LU_NOD_ID IN (#{dropdown2}, #{aiSearchOption})  -->
			AND  ASN.LU_NOD_ID IN (
			#{dropdown2}
		        <if test="aiSearchOptionList != null and aiSearchOptionList.size() > 0">
		            , 
		            <foreach item="aiId" collection="aiSearchOptionList" open="" separator="," close="">
		                #{aiId}
		            </foreach>
		        </if>
			)
		</if>
		
		<!-- 과제 유형 구분 -->
		<if test = 'dropdown1 != null and dropdown1.equals("lrnTpCd") and !dropdown2.equals("all")' >
			AND ASN_LRN_TP = #{dropdown2}
		</if>
		
		<!-- 진행상태 유형 구분 -->
		<if test = 'dropdown1 != null and dropdown1.equals("cmplCk") and !dropdown2.equals("all")' >
			<choose>
				<when test='dropdown2.equals("cmpl")'>
					 AND ASN.SMT_CMPL_YN = 'Y' -- 과제가 완료된 경우
   					 OR ASN.END_DTM <![CDATA[<]]> NOW() -- 과제기간이 끝난 경우
				</when>
				<when test='dropdown2.equals("ing")'>
					 AND ( (ASN.STR_DTM IS NULL AND ASN.END_DTM IS NULL) OR (NOW() BETWEEN ASN.STR_DTM AND ASN.END_DTM) )  
   					-- 기간이 없는 경우 -- 과제 기간이 진행 중인 경우
					 AND ASN.SMT_CMPL_YN = 'N' -- 과제가 완료되지 않은 경우
				</when>
				<otherwise></otherwise>
			</choose>
		</if>
		
	    GROUP BY ASN.ASN_ID
	    
  		<include refid="api.ea.common.pagingFooter"/>
	</select>
	
	<!--    학생분석 상세 평가 리스트 조회    -->
	<select id="selectEaStuAmTcrEvDetail" resultType="hashMap">
       /** EaLrnMgTcr-Mapper.xml - selectEaStuAmTcrEvDetail - 지향난 - 학생분석 상세 평가 조회*/
		SELECT
			EV_DATA.*
			, COUNT(1) OVER() 		AS totalCnt 			-- 총 조회 갯수
		FROM
		(
			SELECT 
				  E.EV_ID				AS evId					-- 평가ID
				, E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
				, E.USR_ID				AS usrId				-- 사용자ID
				, E.EV_NM				AS evNm					-- 평가명
				, E.EV_DV_CD			AS evDvCd				-- 평가구분코드
				, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
				, E.EV_DTL_DV_CD		AS evDtlDvCd			-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
				, IFNULL(EV_DTL.CM_CD_NM, '-') AS evDtlDvNm -- 평가상세구분명
				, CASE 	WHEN E.LCKN_YN = 'Y' THEN '잠금' 
						WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN '대기' -- 응시기간 도래전, 미잠금
						WHEN R.EV_CMPL_YN = 'Y' THEN '응시완료'
						WHEN IFNULL(R.EV_CMPL_YN, 'N') = 'N' THEN '미응시'
						ELSE '응시중' 
					   	END 				AS txmStNm			-- 평가상태 응시상태			
				/*
				, CASE WHEN E.TXM_PTME_SETM_YN = 'N' THEN '-'
					   ELSE CONCAT(DATE_FORMAT(E.TXM_STR_DTM, '%y. %m. %d. %p %H:%i'),'~',DATE_FORMAT(E.TXM_END_DTM, '%Y. %m. %d. %p %H:%i'))
					   END AS txmPtmeView
				*/
				, CASE WHEN E.TXM_PTME_SETM_YN = 'N' THEN '-'
		        ELSE CONCAT(
		            DATE_FORMAT(E.TXM_STR_DTM, '%m. %d. '),
		            CASE
		                WHEN HOUR(E.TXM_STR_DTM) <![CDATA[ < ]]> 12 THEN '오전'
		                ELSE '오후'
		            END,
		            DATE_FORMAT(E.TXM_STR_DTM, ' %h:%i'), '~',
		            DATE_FORMAT(E.TXM_END_DTM, '%m. %d. '),
		            CASE
		                WHEN HOUR(E.TXM_END_DTM) <![CDATA[ < ]]> 12 THEN '오전'
		                ELSE '오후'
		            END,
		            DATE_FORMAT(E.TXM_END_DTM, ' %h:%i')
		        )
		    	END AS txmPtmeView -- 응시기간표시
				, E.TXM_PTME_SETM_YN 	AS txmPtmeSetmYn        -- 응시기간설정여부 
				, DATE_FORMAT(E.TXM_STR_DTM, '%m. %d. %H:%i')	AS txmStrDtm -- 응시 시작일
				, DATE_FORMAT(E.TXM_END_DTM, '%m. %d. %H:%i')	AS txmEndDtm -- 응시 종료일
				, DATE_FORMAT(R.SMT_DTM, '%m. %d.') 			AS smtDtm -- 제출일시
				, IFNULL(R.TXM_STR_YN, 'N')			AS txmStrYn				-- 응시시작여부
				, IFNULL(R.EV_CMPL_YN, 'N')			AS evCmplYn				-- 평가완료여부
				, E.XPL_TM_SETM_YN  	AS xplTmSetmYn			-- 풀이시간설정여부
				, E.XPL_TM_SCNT			AS xPlTmScnt			-- 풀이시간 초수 전체초수
				, E.QST_CNT				AS qstCnt				-- 문제수
				, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수	
				, IFNULL(R.CANS_CNT, 0)	AS cansCnt				-- 정답수
				, E.LCKN_YN				AS lcknYn				-- 잠금여부
				, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
				, E.USE_YN				AS useYn				-- 사용여부
				, E.CRTR_ID				AS crTrId				-- 생성자
				, DATE_FORMAT(E.CRT_DTM, '%m. %d.')  AS crtDtm -- 생성일
				, E.MDFR_ID				AS mdfrId				-- 수정자
				, E.MDF_DTM				AS mdfDtm				-- 수정일
				, E.DB_ID				AS dbId					-- DB ID
				<if test = 'tbscDvCdEv.equals("0") or tbscDvCdEv == 0'>
				, NOD_LU.LLU_NOD_ID		AS lluNodId				-- 대단원 ID
				</if>
			FROM LMS_LRM.EA_EV E
			LEFT JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = E.EV_ID AND R.USR_ID = #{usrId}
			LEFT JOIN LMS_LRM.CM_CM_CD EV_DTL ON EV_DTL.URNK_CM_CD = 'EV_DTL_DV_CD' AND EV_DTL.CM_CD = E.EV_DTL_DV_CD
			<if test = 'tbscDvCdEv.equals("0") or tbscDvCdEv == 0'>
			LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
			LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
			LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = T.TC_LRMP_NOD_ID	
			</if>	
			
			WHERE E.OPT_TXB_ID = #{optTxbId}
			<choose>
		    	<when test = 'tbscDvCdEv.equals("1")  or tbscDvCdEv == 1'>
					AND E.EV_DV_CD = 'DE' -- DE:DIY평가
					AND E.USR_ID = #{usrId}
		    	</when >
		    	<when test= 'dropdown1 != null and dropdown1.equals("lrnTpCd") and dropdown2.equals("TE")'>
		    		/* AND E.EV_DV_CD IN ('SE', 'TE') -- SE:교과평가, TE:교사평가 만 */
		    		AND E.EV_DV_CD = 'TE' -- 교사 평가
		    	</when>
		    	<otherwise>
					-- AND E.EV_DV_CD = 'SE' -- SE:교과평가		
					AND E.EV_DV_CD IN ('SE', 'TE') -- SE:교과평가, TE:교사평가 만	
				</otherwise>
			</choose>
				AND E.DEL_YN = 'N'
				AND E.USE_YN = 'Y'
		
			<choose>
		    	<when test = 'tbscDvCdEv.equals("1") or tbscDvCdEv == 1'><!-- DIY평가 -->
		    		ORDER BY E.CRT_DTM DESC
		    	</when>
			</choose>
			
		) EV_DATA
		
		WHERE 1=1
		<!-- 미완료 여부 -->
		<choose>
		<when test = '!tbscDvCdEv.equals("1")  or !tbscDvCdEv == 1'>
			<if test = 'ncmplYn != null and  ncmplYn.equals("Y")'>
				AND ((EV_DATA.txmPtmeSetmYn = 'N' AND IFNULL(EV_DATA.evCmplYn, 'N') = 'N')
				OR EV_DATA.txmEndDtm > CURRENT_TIMESTAMP)
			</if>
			
			<!-- 단원 유형 구분 -->
			<if test = 'dropdown1 != null and dropdown1.equals("luNodId") and !dropdown2.equals("all")' >
				AND EV_DATA.lluNodId  = #{dropdown2}
			</if>
			
			<!-- 평가 유형 구분 -->
			<if test = 'dropdown1 != null and dropdown1.equals("lrnTpCd") and !dropdown2.equals("all")' >	
				<choose>
		    	<when test= 'dropdown2.equals("TE")'>
		    		AND EV_DATA.evDvCd = 'TE' -- 교사 평가
		    	</when>
		    	<otherwise>
					AND EV_DATA.evDtlDvCd = #{dropdown2}
				</otherwise>
			</choose>
			
			</if>
			
			<!-- 진행상태 유형 구분 -->
			<if test = 'dropdown1 != null and dropdown1.equals("cmplCk") and !dropdown2.equals("all")' >
				<choose>
					<when test='dropdown2.equals("cmpl")'> <!-- 종료 > 응기시간종료, 응시완료 평가-->
						 AND EV_DATA.lcknYn = 'N'
						 AND (EV_DATA.evCmplYn = 'Y' OR CURRENT_TIMESTAMP > EV_DATA.txmEndDtm)
					</when>
					<when test='dropdown2.equals("ing")'> <!-- 진행중 > 응시중, 대기, 미응시, 잠금 상태 평가  -->
						 AND EV_DATA.evCmplYn = 'N' -- 평가  완료된 경우
						 AND EV_DATA.txmStNm != '응시완료'
					</when>
					<otherwise></otherwise>
				</choose>
			</if>
		</when>
		<otherwise>
			<if test = 'ncmplYn != null and  ncmplYn.equals("Y")'>
				AND ((EV_DATA.txmPtmeSetmYn = 'N' AND IFNULL(EV_DATA.evCmplYn, 'N') = 'N')
				OR EV_DATA.txmEndDtm > CURRENT_TIMESTAMP)
			</if>
		</otherwise>
		</choose>
		
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}
		
	</select>
	
	<!--  종합현황 학습시간  -->
	<select id="selectLrnSumDataList" resultType="hashMap">
	/* 학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectLrnSumDataList */
        SELECT
        	U.USR_ID AS usrId,
		    U.USR_NM AS usrNm,
		    U.STU_NO AS stuNo,
		    SUM(T.evTmScnt) AS evTmScnt,
            COUNT(1)                                                     AS stdDate,  -- 학습일
            IFNULL(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%H'),'00')  AS evTmScntH,-- 학습 시간 시
            IFNULL(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%i'),'00')  AS evTmScntM,-- 학습 시간 분
            SUM(T.qstCnt)                                                AS qstCnt,   -- 문제 풀이 수
            IFNULL(ROUND(((SUM(T.cansCnt)/SUM(T.qstCnt))*100)),0)        AS cansRt    -- 정답률
        FROM LMS_LRM.CM_USR U
        LEFT JOIN
        (
            SELECT
                G.stdDate,
                G.LRN_USR_ID,
                SUM(G.evTmScnt) AS evTmScnt,
                SUM(G.qstCnt)   AS qstCnt,
                SUM(G.cansCnt)  AS cansCnt
            FROM
            (
            /*교과평가,AI평가,교사평가*/
            SELECT
                DATE_FORMAT(EER.SMT_DTM,'%Y%m%d')    AS stdDate,
                EER.USR_ID AS LRN_USR_ID,
                SUM(EER.EV_TM_SCNT) AS evTmScnt,
                SUM(EE.FNL_QST_CNT) AS qstCnt,
                SUM(EER.CANS_CNT)   AS cansCnt
            FROM LMS_LRM.EA_EV EE
            INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
            WHERE EE.OPT_TXB_ID = #{optTxbId}
            AND EE.USE_YN      = 'Y'
            AND EE.DEL_YN      = 'N'
            AND EER.EV_CMPL_YN = 'Y'
            GROUP BY EE.EV_DV_CD, DATE_FORMAT(EER.SMT_DTM,'%Y%m%d'), LRN_USR_ID

            UNION ALL

            /*교과학습*/
            SELECT
                DATE_FORMAT(ST.MDF_DTM,'%Y%m%d') AS stdDate,
                ST.LRN_USR_ID,
                SUM(ST.LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            from LMS_LRM.tl_sbc_lrn_atv_rcstn LAR
			    inner JOIN LMS_LRM.TL_SBC_LRN_ATV_ST st
			    	on LAR.OPT_TXB_ID = ST.OPT_TXB_ID
			    	and LAR.LRMP_NOD_ID = ST.LRMP_NOD_ID
			    	and LAR.LRN_ATV_ID = ST.LRN_ATV_ID
			    inner join lms_lrm.cm_usr cu 
			    		on st.LRN_USR_ID = cu.usr_id
					   and cu.usr_tp_cd = 'ST'
            WHERE ST.OPT_TXB_ID = #{optTxbId}
            and LAR.CTN_TP_CD <![CDATA[<>]]> 'EX'
         --   AND   LRN_ST_CD  = 'CL' -- 학습 완료
            GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d'), LRN_USR_ID
			
			/* 선생님 콘텐츠 추가 */
            UNION ALL

            SELECT 
            	 DATE_FORMAT(MDF_DTM,'%Y%m%d') AS stdDate,
	                LRN_USR_ID,
	                SUM(LRN_TM_SCNT)              AS evTmScnt,
	                0                             AS qstCnt,
	                0                             AS cansCnt
            FROM LMS_LRM.tl_tcr_reg_ctn_st
            WHERE OPT_TXB_ID =  #{optTxbId}
			GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d'), LRN_USR_ID
			
            UNION ALL

            /*특별학습*/
            SELECT
                DATE_FORMAT(MDF_DTM,'%Y%m%d') AS stdDate,
                LRN_USR_ID,
                SUM(LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            FROM LMS_LRM.SL_SP_LRN_PGRS_ST
            WHERE OPT_TXB_ID = #{optTxbId}
       --     AND   LRN_ST_CD  = 'CL'-- 학습 완료
            GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d'), LRN_USR_ID
            ) G GROUP BY G.stdDate, G.LRN_USR_ID, G.evTmScnt
        ) T  ON U.USR_ID = T.LRN_USR_ID
		WHERE 1=1
		AND U.CLA_ID = #{claId}
		AND U.USR_TP_CD = 'ST'
		GROUP BY U.USR_ID, U.USR_NM, U.STU_NO
		ORDER BY U.STU_NO, U.USR_ID
    </select>
    
    <!-- 종합현황 챌린지 -->
	<select id="selectEaStuLrnChlgList" resultType="hashMap">
		SELECT
			USR.USR_ID AS usrId, 
			MAX(USR.USR_NM) AS usrNm,
			MAX(USR.STU_NO) AS stuNo,
			MAX(LC.LRN_GOAL_DV_CD) AS lrnGoalDvCd,
			MAX(LC.LRN_GOAL_QST_CNT) AS lrnGoalQstCnt,	-- 학습목표문제수
			MAX(LC.LRN_ACV_QST_CNT) as lrnAcvQstCtn,		-- 학습달성문제수
			MAX(LC.LRN_GOAL_TM_SCNT) AS lrnGoalTmScnt,	-- 학습목표시간초수
			MAX(LC.LRN_ACV_TM_SCNT) as lrnAcvTmScnt,		-- 학습달성시간초수			
    		MAX(LC.LRN_GOAL_ST_CD) AS lrnGoalStCd, /* CL:달성완료, DL=진행중 NL=미진행 */ 
    		 -- 총 개수
		    (SELECT COUNT(*) 
		     FROM LMS_LRM.TL_LRN_CHLG AS LC2 
		     WHERE LC2.LRN_USR_ID = USR.USR_ID 
		       AND LC2.OPT_TXB_ID =  #{optTxbId} 
		       AND LC2.LRN_GOAL_ST_CD != 'QT'
		       AND YEARWEEK(LC2.LRN_STR_DT, 1) <![CDATA[ <= ]]> YEARWEEK(CURDATE(), 1) - 1
		       ) AS totalCount,       
		    -- CL 상태의 총 개수
		    (SELECT COUNT(*) 
		     FROM LMS_LRM.TL_LRN_CHLG AS LC2 
		     WHERE LC2.LRN_USR_ID = USR.USR_ID 
		       AND LC2.OPT_TXB_ID =  #{optTxbId}
		       AND LC2.LRN_GOAL_ST_CD = 'CL'
		       AND LC2.LRN_GOAL_ST_CD != 'QT'
		       AND YEARWEEK(LC2.LRN_STR_DT, 1) <![CDATA[ <= ]]> YEARWEEK(CURDATE(), 1) - 1
		       ) AS clCount
    	FROM LMS_LRM.CM_USR USR
    	LEFT JOIN LMS_LRM.TL_LRN_CHLG LC ON USR.USR_ID = LC.LRN_USR_ID AND LC.OPT_TXB_ID = #{optTxbId} 
    	 AND CURDATE() BETWEEN LC.LRN_STR_DT AND LC.LRN_END_DT 
    	WHERE 1=1
		AND USR.CLA_ID = #{claId}
		AND USR.USR_TP_CD = 'ST'
		GROUP BY USR.USR_ID
		ORDER BY stuNo, usrId
		    
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuLrnChlgList */
	</select>
	
	<!-- 종합분석 종합 성취 현황 -->
	<select id="selectEaStuAnLuList" resultType="hashMap">
	/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaStuAnLuList */
		SELECT
			U1.USR_ID AS usrId,
			U1.USR_NM AS usrNm,
			U1.STU_NO AS stuNo,
			COUNT(DISTINCT CASE WHEN B.CANS_RT > 0 THEN B.LRMP_NOD_ID END) AS lrmpNodCount,  -- LRMP_NOD_ID의 개수
		    SUM(B.CANS_RT) AS totalCansRtSum,  -- CANS_RT의 합
		    SUM(CASE WHEN B.APYCNT > 0 THEN 1 ELSE 0 END) AS cmplNodCount,  -- APYCNT가 0이 아닌 개수
		    CASE
		        WHEN SUM(CASE WHEN B.APYCNT > 0 THEN 1 ELSE 0 END) > 0 THEN
		            ROUND(SUM(B.CANS_RT) / SUM(CASE WHEN B.APYCNT > 0 THEN 1 ELSE 0 END),1)  -- totalCansRtSum을 nonZeroApyCnt로 나눈 백분율
		        ELSE 0
		    END AS totCansRt  -- totalCansRtSum을 nonZeroApyCnt로 나눈 퍼센트
		    /*
		    CASE
		        WHEN COUNT(DISTINCT CASE WHEN B.CANS_RT > 0 THEN B.LRMP_NOD_ID END) > 0 THEN 
		            ROUND(SUM(B.CANS_RT) / COUNT(DISTINCT CASE WHEN B.CANS_RT > 0 THEN B.LRMP_NOD_ID END), 1)  -- TOTALCANSRT의 평균
		        ELSE 0 
		    END AS totCansRt  -- LRMP_NOD_COUNT당 CANS_RT의 평균
		    */
		FROM LMS_LRM.CM_USR U1
		LEFT JOIN 
		(
			SELECT A.LRMP_NOD_ID,
		       -- A.RCSTN_ORDN,
		       U2.USR_ID as U2_USR_ID,
		       -- A.RCSTN_NO,
		       -- A.LRMP_NOD_NM,
		       SUM(CASE WHEN A.EV_CMPL_YN = 'Y' THEN A.FNL_QST_CNT ELSE 0 END) AS FNL_QST_CNT,
		       SUM(CASE WHEN A.EV_CMPL_YN = 'Y' THEN A.CANS_CNT ELSE 0 END) AS CANS_CNT,
		       SUM(CASE WHEN A.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) AS APYCNT,
		       CASE
		           WHEN SUM(CASE WHEN A.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) > 0 THEN
		               ROUND(SUM(CASE WHEN A.EV_CMPL_YN = 'Y' THEN A.CANS_RT ELSE 0 END) / 
		                     SUM(CASE WHEN A.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END), 1)
		           ELSE 0
			       END AS CANS_RT
			  FROM (
			      SELECT RSLNR.LRMP_NOD_ID,
			             RSLNR.LRMP_NOD_NM,
			             -- RSLNR.RCSTN_ORDN,
			             -- RSLNR.RCSTN_NO,
			             EE.EV_ID,
			             EE.OPT_TXB_ID,
			             EER.USR_ID,
			             EE.EV_DV_CD,
			             EE.EV_DTL_DV_CD,
			             EE.EV_NM,
			             EE.FNL_QST_CNT, /* 최종문제수 */
			             EETR.LU_LRMP_NOD_ID,
			             -- EETR.LU_LRMP_NOD_NM,
			             IFNULL(EER.CANS_CNT, 0) AS CANS_CNT,
			             EER.EV_CMPL_YN,
			             EER.CANS_RT,
			             EER.USR_ID AS ER_USR_ID
			        FROM TL_SBC_LRN_NOD_RCSTN RSLNR
			        JOIN EA_EV EE ON RSLNR.OPT_TXB_ID = EE.OPT_TXB_ID
			        JOIN EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			        JOIN (SELECT DISTINCT LU_LRMP_NOD_ID, EV_ID ,LU_LRMP_NOD_NM
			                      FROM EA_EV_TS_RNGE WHERE LU_OPT_TXB_ID = #{optTxbId}) EETR /* EA_평가시험범위 */
			          ON EE.EV_ID = EETR.EV_ID AND RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID
			          
			       WHERE RSLNR.OPT_TXB_ID = #{optTxbId}
			         AND RSLNR.DPTH = 1
			         AND RSLNR.LU_EPS_YN = 'Y'
			         AND RSLNR.USE_YN = 'Y'
			         AND ((EE.EV_DV_CD = 'AE' AND EE.EV_DTL_DV_CD = 'OV') 
			           OR (EE.EV_DV_CD = 'SE' AND EE.EV_DTL_DV_CD IN ('TO', 'FO', 'UG')))
			       ORDER BY RSLNR.RCSTN_ORDN
			  ) A
			  JOIN LMS_LRM.CM_USR U2 ON A.ER_USR_ID = U2.USR_ID
			GROUP BY A.LRMP_NOD_ID, U2.USR_ID
		) AS B ON B.U2_USR_ID = U1.USR_ID 
		WHERE 1=1
		AND U1.CLA_ID = #{claId}
		AND U1.USR_TP_CD = 'ST'
		GROUP BY U1.USR_ID, U1.USR_NM, U1.STU_NO
		ORDER BY U1.STU_NO, U1.USR_ID
	</select>
	
	<select id="selectStuRtmAbnBhvTpList"  resultType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/*학습 이상(지도필요) 관리 지향난 EaLrnMgTcr-Mapper.xml - selectStuRtmAbnBhvTpList */
		SELECT
			CSCLS.LRN_USR_ID AS LRN_USR_ID
			, CU.USR_NM AS LRN_USR_NM
			, CU.STU_NO AS STU_NO
			, CSCLS.OPT_TXB_ID AS OPT_TXB_ID
			, (SELECT COUNT(*) FROM LMS_LRM.CM_GDE_NEED G WHERE G.STU_USR_ID = CSCLS.LRN_USR_ID) AS totalCnt -- 지도필요 개수
		FROM LMS_LRM.CM_STU_CUR_LRN_ST CSCLS
		LEFT JOIN LMS_LRM.CM_USR CU ON CU.USR_ID = CSCLS.LRN_USR_ID AND CU.USR_TP_CD = 'ST'
		WHERE 1=1
		AND CSCLS.OPT_TXB_ID = #{optTxbId}
		ORDER BY CU.STU_NO, CSCLS.LRN_USR_ID
	</select>
	
	<select id="selectStuGdeNeedList"  resultType="hashMap">
		/*학습 이상(지도필요) 개별 히스토리 관리 지향난 EaLrnMgTcr-Mapper.xml - selectStuGdeNeedList */
		SELECT
			A.OPT_TXB_ID AS optTxbId 
			, A.STU_USR_ID AS usrId
			, A.GDE_NEED_CD AS gdeNeedCd
			, B.SETM_VL_1 AS gdeNeedNm
			, CASE
				WHEN A.GDE_NEED_CD IN ('ASA') THEN (SELECT ASN_NM FROM LMS_LRM.EA_ASN WHERE ASN_ID = A.GDE_NEED_VL)
				WHEN A.GDE_NEED_CD IN ('ETA', 'ERA') THEN (SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = A.GDE_NEED_VL)
			END AS gdeNeedVlNm
			, IFNULL(DATE_FORMAT(A.CRT_DTM, '%m. %d'), '-') AS crtDtm
		FROM LMS_LRM.CM_GDE_NEED A
		INNER JOIN LMS_LRM.CM_CM_CD B ON B.URNK_CM_CD = 'GDE_NEED_CD' AND A.GDE_NEED_CD  = B.CM_CD
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND A.STU_USR_ID = #{usrId}
		ORDER BY A.CRT_DTM DESC
	</select>
	
	
	<select id="selectStuRtmAbnBhvTpList2"  resultType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/*학습 이상(관심학생) 관리 지향난 EaLrnMgTcr-Mapper.xml - selectStuRtmAbnBhvTpList2 */
		SELECT T1.LRN_USR_ID AS lrnUsrId
			 , T1.LRN_USR_NM AS lrnUsrNm
			 , T1.STU_NO AS stuNo
			 , T1.OPT_TXB_ID AS optTxbId
			 , T1.RTM_ABN_BHV_TP_CD AS rtmAbnBhvTpCd
			 , T1.RTM_ABN_BHV_TP_NM AS rtmAbnBhvTpNm
			 , T1.GDE_NEED_TP
		  FROM ( SELECT CSCLS.LRN_USR_ID
					 , CU.USR_NM AS LRN_USR_NM
					 , CU.STU_NO
					 , CSCLS.OPT_TXB_ID
					 , CSCLS.RTM_ABN_BHV_TP_CD
					 , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'RTM_ABN_BHV_TP_CD' AND CM_CD = CSCLS.RTM_ABN_BHV_TP_CD) AS RTM_ABN_BHV_TP_NM
					 , CSCLS.GDE_NEED_TP
				  FROM LMS_LRM.CM_STU_CUR_LRN_ST CSCLS
				  LEFT JOIN LMS_LRM.CM_USR CU
						 ON CU.USR_ID = CSCLS.LRN_USR_ID
						AND CU.USR_TP_CD = 'ST'
				  LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR_LU
						 ON TSLNR_LU.LRMP_NOD_ID = CSCLS.LU_NOD_ID
						AND TSLNR_LU.OPT_TXB_ID = CSCLS.OPT_TXB_ID
				  LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR_TC
						 ON TSLNR_TC.LRMP_NOD_ID = CSCLS.TC_NOD_ID
						AND TSLNR_TC.OPT_TXB_ID = CSCLS.OPT_TXB_ID
				  LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN TSANR
						 ON TSANR.LRN_ATV_ID = CSCLS.LRN_ATV_ID
						AND TSANR.OPT_TXB_ID = CSCLS.OPT_TXB_ID
				 WHERE 1=1
				   AND CSCLS.OPT_TXB_ID = #{optTxbId}
			) T1
			ORDER BY T1.STU_NO, T1.LRN_USR_ID
	</select>
	
	<!--  ai맞춤학습 탭 중단원 리스트 조회 -->
	<select id="selectAlEnOnlyMluList"   resultType="hashMap">
		SELECT
			DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID
		  , DPTH1.KMMP_NOD_NM AS LLU_KMMP_NOD_NM
		  , DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		  , DPTH2.KMMP_NOD_NM AS MLU_KMMP_NOD_NM
		  , DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID
		  , DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM
		  , DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID
		  , DPTH5.KMMP_NOD_NM AS TPC_KMMP_NOD_NM
		  , DPTH1.TC_EPS_YN
		  , DPTH1.TC_USE_YN
		  , DPTH1.RCSTN_ORDN
		  , DPTH1.ORGL_ORDN
		  , IFNULL(AUTP.TPC_AVN, 0.5) TPC_AVN
		  , BALI.LU_IMG_PTH AS LU_IMG_PTH
		  , OV.TC_AVG_CANS_RT
		  , OVCNT.OV_QTM_CNT
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
					ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
					AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
					AND DPTH2.DPTH = 2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				LEFT OUTER JOIN (
					SELECT
					    USR_ID
					  , LU_KMMP_NOD_ID
					  , TC_KMMP_NOD_ID
					  , AVG(TPC_AVN) as TPC_AVN
					FROM
					    LMS_LRM.AI_USRLY_TPC_PROF
					WHERE
					    USR_ID = #{usrId}
						AND OPT_TXB_ID = #{optTxbId}
					GROUP BY
					    LU_KMMP_NOD_ID, TC_KMMP_NOD_ID
				) AUTP
				    ON AUTP.LU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
				    AND AUTP.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				LEFT OUTER JOIN (
					SELECT
					    EE.EV_ID
					  , EER.USR_ID
					  , EE.EV_DTL_DV_CD
					  , EER.EV_CMPL_YN
					  , EAETR.MLU_KMMP_NOD_ID
					  , EAETR.TC_KMMP_NOD_ID
					  , EAETR.TPC_KMMP_NOD_ID
					  , (SUM(EEQA.CANS_YN = 'Y') / COUNT(EEQ.QTM_ID)) * 100 AS TC_AVG_CANS_RT
					FROM
					    LMS_LRM.EA_EV EE
							INNER JOIN LMS_LRM.EA_EV_RS EER
							    ON EE.EV_ID = EER.EV_ID
							INNER JOIN LMS_LRM.EA_EV_QTM EEQ
							    ON EE.EV_ID = EEQ.EV_ID
							INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
							    ON EE.EV_ID = EEQA.EV_ID
							    AND EEQ.QTM_ID = EEQA.QTM_ID
							    AND EER.USR_ID = EEQA.USR_ID
							INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
							    ON EE.EV_ID = EAETR.EV_ID
							    AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
							    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
					WHERE
					    EER.USR_ID = #{usrId}
					  	AND EE.OPT_TXB_ID = #{optTxbId}
						AND EE.EV_DV_CD = 'AE'
						AND EER.EV_CMPL_YN = 'Y'
						AND EE.EV_DTL_DV_CD = 'OV'
					GROUP BY
					    EAETR.TC_KMMP_NOD_ID
				) OV
				    ON OV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
				    AND OV.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				LEFT OUTER JOIN (
					SELECT
						BE.MLU_NOD_ID
					  , COUNT(*) AS OV_QTM_CNT
					FROM
						LMS_CMS.BC_EVSH BE
							INNER JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM
								ON BE.EVSH_ID = BEQM.EVSH_ID
					WHERE
						BE.EVSH_TP_CD = 'AI'
					  	AND BE.DEL_YN = 'N'
					  	AND BE.USE_YN = 'Y'
					GROUP BY
						BE.MLU_NOD_ID
				) OVCNT
					ON OVCNT.MLU_NOD_ID = DPTH2.KMMP_NOD_ID
				LEFT OUTER JOIN LMS_CMS.BC_AI_LU_IMG BALI
					ON BALI.LU_NOD_ID = DPTH2.KMMP_NOD_ID
		WHERE
		    DPTH1.OPT_TXB_ID = #{optTxbId}
		  	AND DPTH1.DPTH = 1
		ORDER BY
		    DPTH1.RCSTN_ORDN, DPTH1.ORGL_ORDN, DPTH4.ORGL_ORDN
		/* 영어 중단원 리스트 - 지향난 - AlEn-Mapper.xml - selectAlOnlyMluList */
	</select>
	
	<!-- 학생별 추천학습 진도율정보 상세목록조회 -->
    <select id="selectStuRcmLrnTotList" parameterType="Map" resultType="Map">
	    SELECT 
		    F.USR_ID,
		    COUNT(DISTINCT F.SP_LRN_ID) AS SP_LRN_CNT,
		    SUM(F.TOT_CNT) AS TOTAL_CNT,
		    SUM(F.TOT_CNT_CL) AS TOTAL_CNT_CL,
		    CASE 
		        WHEN SUM(F.TOT_CNT) > 0 
		        THEN ROUND((SUM(F.TOT_CNT_CL) / SUM(F.TOT_CNT)) * 100, 2)
		        ELSE 0
		    END AS PROGRESS_RATE
		FROM (
		       SELECT 
					F.SP_LRN_NOD_ID
					,F.SP_LRN_ID
					,F.SP_LRN_NM
					,F.RCSTN_ORDN
					,F.USR_ID
					,IFNULL(COUNT(F.SP_LRN_CTN_ID),0) as TOT_CNT
					,IFNULL(SUM(F.LRN_ST_CD = 'CL'),0) as TOT_CNT_CL
				FROM
				(
					SELECT
						   A.SP_LRN_ID as SP_LRN_ID
				          ,A.SP_LRN_NM	as SP_LRN_NM
				          ,A.RCSTN_ORDN as RCSTN_ORDN
				          ,C.SP_LRN_NOD_ID	as SP_LRN_NOD_ID
				          ,B.LRN_USR_ID AS USR_ID
				          ,E.SP_LRN_CTN_ID as SP_LRN_CTN_ID
				--          ,G.SP_LRN_CTN_ID as SP_LRN_CTN_ID2
				          ,G.LRN_ST_CD as LRN_ST_CD
				          ,(SUM(IF(G.LRN_ST_CD = 'CL', 1, 0))) as SUM_COUNT
				          , IF(SUM(G.SUM_TOT), 1, 0) as SUM_COUNT2
				          ,B.LRN_USR_ID as LRN_USR_ID
				     FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
				     INNER JOIN LMS_LRM.SL_STU_RCM_LRN B /* SL_학생별추천학습 */
				           ON B.OPT_TXB_ID = A.OPT_TXB_ID
				           AND A.SP_LRN_ID = B.SP_LRN_ID
				           AND B.RCM_YN = 'Y'
				     INNER JOIN LMS_CMS.BC_SP_LRN_NOD C /* BC_특별학습노드 */
				           ON A.SP_LRN_ID = C.SP_LRN_ID
				           AND C.DPTH = 1
				           AND C.DEL_YN= 'N'
				     INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 */
				           ON C.SP_LRN_ID = D.SP_LRN_ID
				           AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
				           AND D.DPTH = 2
				           AND D.CSTN_CMPL_YN = 'Y'
				           AND D.DEL_YN= 'N'
				     INNER JOIN LMS_CMS.BC_SP_LRN_CTN E /* BC_특별학습콘텐츠 */
				           ON D.SP_LRN_NOD_ID = E.SP_LRN_NOD_ID
				           AND E.DEL_YN = 'N'
				     LEFT JOIN (
				     	 SELECT 
							 	R.OPT_TXB_ID,R.SP_LRN_ID, BC.SP_LRN_CTN_ID as SP_LRN_CTN_ID ,S.LRN_USR_ID ,S.LRN_ST_CD ,(SUM(IF(BC.SP_LRN_CTN_ID, 1, 0))) as SUM_TOT
							FROM LMS_CMS.BC_SP_LRN_NOD B
							INNER JOIN LMS_CMS.BC_SP_LRN_CTN BC ON B.SP_LRN_NOD_ID = BC.SP_LRN_NOD_ID AND BC.DEL_YN = 'N'
							LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN R ON B.SP_LRN_ID = R.SP_LRN_ID
							LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST S ON BC.SP_LRN_CTN_ID = S.SP_LRN_CTN_ID AND S.OPT_TXB_ID = #{optTxbId}
							WHERE 1=1
							AND R.OPT_TXB_ID = #{optTxbId}
							GROUP BY BC.SP_LRN_CTN_ID, S.LRN_USR_ID, S.LRN_ST_CD
				     ) G ON  A.OPT_TXB_ID = G.OPT_TXB_ID
				     	 AND A.SP_LRN_ID = G.SP_LRN_ID
				         AND G.SP_LRN_CTN_ID = E.SP_LRN_CTN_ID
				        AND B.LRN_USR_ID = G.LRN_USR_ID
				    WHERE A.OPT_TXB_ID = #{optTxbId}
				    AND A.USE_YN = 'Y'
				  	GROUP BY E.SP_LRN_CTN_ID, B.LRN_USR_ID, A.SP_LRN_ID, C.SP_LRN_NOD_ID, G.LRN_ST_CD
				 )F
				GROUP by F.USR_ID, F.SP_LRN_ID, F.SP_LRN_NOD_ID
				ORDER BY F.USR_ID ASC, F.RCSTN_ORDN ASC, F.SP_LRN_ID ASC, F.SP_LRN_NOD_ID ASC
		) F
		GROUP BY F.USR_ID
		ORDER BY F.USR_ID;
        /*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectStuRcmLrnTotList */
    </select>
	
	
	<!-- 학생별 추천학습 진도율정보 상세목록조회 -->
    <select id="selectStuRcmDetailList" parameterType="Map" resultType="Map">
       SELECT 
			F.SP_LRN_NOD_ID
			,F.SP_LRN_ID
			,MAX(F.SP_LRN_NM) AS SP_LRN_NM
			,MAX(F.RCSTN_ORDN) AS RCSTN_ORDN
			,MAX(F.USR_ID) AS USR_ID
			,IFNULL(COUNT(F.SP_LRN_CTN_ID),0) as TOT_CNT
			,IFNULL(SUM(F.LRN_ST_CD = 'CL'),0) as TOT_CNT_CL
		FROM
		(
			SELECT
				   A.SP_LRN_ID as SP_LRN_ID
		          ,MAX(A.SP_LRN_NM)	as SP_LRN_NM
		          ,MAX(A.RCSTN_ORDN) as RCSTN_ORDN
		          ,C.SP_LRN_NOD_ID	as SP_LRN_NOD_ID
		          ,B.LRN_USR_ID AS USR_ID
		          ,E.SP_LRN_CTN_ID as SP_LRN_CTN_ID		
		          ,MAX(G.LRN_ST_CD) as LRN_ST_CD
		          ,(SUM(IF(G.LRN_ST_CD = 'CL', 1, 0))) as SUM_COUNT
		          , IF(SUM(G.SUM_TOT), 1, 0) as SUM_COUNT2
		          ,B.LRN_USR_ID as LRN_USR_ID
		     FROM LMS_LRM.SL_SP_LRN_RCSTN A  /* SL_특별학습재구성 */
		     INNER JOIN LMS_LRM.SL_STU_RCM_LRN B /* SL_학생별추천학습 */
		           ON B.OPT_TXB_ID = A.OPT_TXB_ID
		           AND A.SP_LRN_ID = B.SP_LRN_ID
		           AND B.RCM_YN = 'Y'
		     INNER JOIN LMS_CMS.BC_SP_LRN_NOD C /* BC_특별학습노드 */
		           ON A.SP_LRN_ID = C.SP_LRN_ID
		           AND C.DPTH = 1
		           AND C.DEL_YN= 'N'
		     INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 */
		           ON C.SP_LRN_ID = D.SP_LRN_ID
		           AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
		           AND D.DPTH = 2
		           AND D.CSTN_CMPL_YN = 'Y'
		           AND D.DEL_YN= 'N'
		     INNER JOIN LMS_CMS.BC_SP_LRN_CTN E /* BC_특별학습콘텐츠 */
		           ON D.SP_LRN_NOD_ID = E.SP_LRN_NOD_ID
		           AND E.DEL_YN = 'N'
		     LEFT JOIN (
		     	 SELECT 
					 	MAX(R.OPT_TXB_ID) AS OPT_TXB_ID
					 	,MAX(R.SP_LRN_ID) AS SP_LRN_ID
					 	,BC.SP_LRN_CTN_ID as SP_LRN_CTN_ID 
					 	,S.LRN_USR_ID 
					 	,MAX(S.LRN_ST_CD) AS LRN_ST_CD
					 	,(SUM(IF(BC.SP_LRN_CTN_ID, 1, 0))) as SUM_TOT
					FROM LMS_CMS.BC_SP_LRN_NOD B
					INNER JOIN LMS_CMS.BC_SP_LRN_CTN BC ON B.SP_LRN_NOD_ID = BC.SP_LRN_NOD_ID AND BC.DEL_YN = 'N'
					LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN R ON B.SP_LRN_ID = R.SP_LRN_ID
					LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST S ON BC.SP_LRN_CTN_ID = S.SP_LRN_CTN_ID AND S.OPT_TXB_ID = #{optTxbId}
					WHERE 1=1
					AND R.OPT_TXB_ID = #{optTxbId}
					and S.LRN_USR_ID = #{usrId}
					GROUP BY BC.SP_LRN_CTN_ID, S.LRN_USR_ID
		     ) G ON  A.OPT_TXB_ID = G.OPT_TXB_ID
		     	 AND A.SP_LRN_ID = G.SP_LRN_ID
		         AND G.SP_LRN_CTN_ID = E.SP_LRN_CTN_ID
		        AND B.LRN_USR_ID = G.LRN_USR_ID
		    WHERE A.OPT_TXB_ID = #{optTxbId}
		    AND A.USE_YN = 'Y'
		  	GROUP BY E.SP_LRN_CTN_ID, B.LRN_USR_ID, A.SP_LRN_ID, C.SP_LRN_NOD_ID
		 )F
		WHERE F.USR_ID = #{usrId}
		GROUP by F.SP_LRN_ID, F.SP_LRN_NOD_ID
		ORDER BY MAX(F.RCSTN_ORDN) ASC, F.SP_LRN_ID ASC, F.SP_LRN_NOD_ID ASC

        /*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectStuRcmDetailList */
    </select>
    
    <!-- Let's Write 학생 단원별 학습자 수준 조회 -->
	<select id="selectEaWriLvStuList" resultType="com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrReqDto">
		SELECT USR.USR_ID AS USR_ID
				, MAX(USR.USR_NM) AS USR_NM
				, MAX(USR.STU_NO) AS USR_NO
				, MAX(BLKNM.LRMP_NOD_ID) as LRMP_NOD_ID -- 학습맵 대단원ID
				, NOD1.KMMP_NOD_ID -- 지식맵 대단원ID
				, MAX(TLLL.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD	-- 단원별 학생 학습 수준
				, MAX(USR.NTR_YN) AS NTR_YN -- 관심학생
				, CSCLS.RTM_ABN_BHV_TP_CD AS rtmAbnBhvTpCd	-- 관심학생
				, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'RTM_ABN_BHV_TP_CD' AND CM_CD = CSCLS.RTM_ABN_BHV_TP_CD) AS RTM_ABN_BHV_TP_NM
			FROM LMS_LRM.CM_USR USR /* 사용자 정보 */
			LEFT JOIN LMS_LRM.CM_STU_CUR_LRN_ST CSCLS ON CSCLS.LRN_USR_ID = USR.USR_ID
			LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN NOD1 ON CSCLS.OPT_TXB_ID = NOD1.OPT_TXB_ID AND NOD1.KMMP_NOD_ID = #{kmmpNodId}
			LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN NOD2 ON CSCLS.OPT_TXB_ID = NOD2.OPT_TXB_ID AND NOD2.URNK_KMMP_NOD_ID = NOD1.KMMP_NOD_ID
			LEFT JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN BLKNM ON BLKNM.KMMP_NOD_ID = NOD2.KMMP_NOD_ID AND BLKNM.AI_KMMP_DPTH = '2'	/* 지식맵 중단원ID로 학습맵 대단원ID 조회 */
			LEFT JOIN LMS_LRM.TL_LU_LRNR_LV TLLL ON BLKNM.LRMP_NOD_ID  = TLLL.LLU_NOD_ID AND USR.USR_ID = TLLL.USR_ID	/* 단원별 학습자 수준 정보 조회 */
			WHERE USR.CLA_ID = #{claId} 
			AND USR.USR_TP_CD = 'ST'
			GROUP BY USR.USR_ID
			ORDER BY USR_NO,  USR_ID
		/*학습 현황 관리 지향난 EaLrnMgTcr-Mapper.xml - selectEaWriLvStuList */
	</select>
    
</mapper>



