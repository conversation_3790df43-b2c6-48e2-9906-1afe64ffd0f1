package com.aidt.api.ea.evcom.ev.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 13:58:오후 1:58
 * @modify date 2024-03-05 13:58:오후 1:58
 * @desc   평가 공통 - 문항리스트 조회 결과 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvDffdsQpQtmReqDto {
	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	

	@Parameter(name="Diy평가생성")
	private String isDiy = "N";	
	
	/* 시험범위 리스트 정보(대단원 리스트) */
	@Parameter(name="대단원 리스트")
	List<EaEvTsRngeDto> tsRngeList;	

	/* 평가난이도분포도 정보 */
	@Parameter(name="평가난이도분포도 리스트")
	List<EaEvDffdCstnDto> dffdDsbList;
	
}
