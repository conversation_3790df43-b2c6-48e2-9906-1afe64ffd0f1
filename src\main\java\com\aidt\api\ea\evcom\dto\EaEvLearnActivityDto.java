package com.aidt.api.ea.evcom.dto;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import com.aidt.common.CommonUserDetail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EaEvLearnActivityDto {

	private Integer evId;
	private String optTxbId;
	private String lrmpNodId;
	private String lrnAtvId;
	private String lrnStCd;
	private String lrnUsrId;
	private int lrnTmScnt;
	private String dbId;

	public static EaEvLearnActivityDto of(EaEvAnswerReqDto eaEvAnswerReqDto, CommonUserDetail userDetail) {

		var learnActivityRequest = eaEvAnswerReqDto.getAtvSaveDto();

		if (ObjectUtils.anyNull(learnActivityRequest, userDetail)) {
			return null;
		}

		return EaEvLearnActivityDto.builder()
			.evId(eaEvAnswerReqDto.getEvId())
			.lrmpNodId(learnActivityRequest.getLrmpNodId())
			.lrnAtvId(learnActivityRequest.getLrnAtvId())
			.lrnStCd(learnActivityRequest.getLrnStCd())
			.lrnTmScnt(learnActivityRequest.getLrnTmScnt())
			.optTxbId(userDetail.getOptTxbId())
			.lrnUsrId(userDetail.getUsrId())
			.dbId(userDetail.getTxbId())
			.build();
	}

	public void addMapNodeAndActivityIds(String lrmpNodId, String lrnAtvId) {
		this.lrmpNodId = lrmpNodId;
		this.lrnAtvId = lrnAtvId;
	}

	public boolean isAnyNodeOrActivityIdBlank() {
		return StringUtils.isAnyBlank(lrmpNodId, lrnAtvId);
	}

}
