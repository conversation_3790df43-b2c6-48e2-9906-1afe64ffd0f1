package com.aidt.api.xx.sample.vue;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.xx.sample.vue.dto.SampleGridDataDto;
import com.aidt.api.xx.sample.vue.dto.SampleMenuDto;
import com.aidt.api.xx.sample.vue.dto.SampleUserDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample Mybatis Service
 */
@Service
public class SampleVueService {
	
    private final String MAPPER_NAMESPACE = "api.xx.sample.SampleVue.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 조회 서비스
     *
     * @param userId
     * @return UserDto
     */
    public SampleUserDto selectUser(String userId) {
        return commonDao.select(MAPPER_NAMESPACE + "selectUser", userId);
    }

    /**
	 * @param menuDto
	 * @return List<MenuDto>
	 */
	public List<SampleMenuDto> selectMenu(SampleMenuDto menuDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMenu", menuDto);
    }

    /**
	 * @param imsiGridDataDto
	 * @return List<ImsiGridDataDto>
	 */
	public List<SampleGridDataDto> selectCrudList(SampleGridDataDto imsiGridDataDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectCrud", imsiGridDataDto);
    }

    /**
	 * @param imsiGridDataDto
	 * @return ImsiGridDataDto
	 */
	public SampleGridDataDto selectCrud(String no) {
        return commonDao.select(MAPPER_NAMESPACE + "selectCrud", no);
    }

    /**
	 * @param imsiGridDataDto
	 * @return ImsiGridDataDto
	 */
	public int insertCrud(SampleGridDataDto imsiGridDataDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertCrud", imsiGridDataDto);
    }

    /**
	 * @param imsiGridDataDto
	 * @return ImsiGridDataDto
	 */
	public int updateCrud(SampleGridDataDto imsiGridDataDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "updateCrud", imsiGridDataDto);
    }

    /**
	 * @param imsiGridDataDto
	 * @return ImsiGridDataDto
	 */
	public int deleteCrud(String no) {
        return commonDao.insert(MAPPER_NAMESPACE + "deleteCrud", no);
    }

    

}