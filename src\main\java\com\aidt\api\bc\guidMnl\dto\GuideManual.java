package com.aidt.api.bc.guidMnl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuideManual {
    /** 매뉴얼 Master **/
    @Parameter(name="PK")
    private Long guidMnlId;

    @Parameter(name="교과서 ID")
    private Long txbId;

    @Parameter(name="사용자 타입(ST/TE)")
    private String usrTpCd;

    @Parameter(name="가이드매뉴얼 코드(외부제공용)")
    private String guidMnlCd;

    @Parameter(name="삭제여부")
    private String delYn;

    @Parameter(name="목차")
    private List<GuideManualNode> nodes;

    @Parameter(name="생성일")
    private String crtDtm;

    @Parameter(name="수정일")
    private String mdfDtm;

    private String dbId;
    private String crtrId;
    private String mdfrId;
}
