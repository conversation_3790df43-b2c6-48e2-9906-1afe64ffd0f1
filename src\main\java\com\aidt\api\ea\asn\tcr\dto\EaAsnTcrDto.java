package com.aidt.api.ea.asn.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 과제 - 교사 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaAsnTcrDto {

		//EA_과제
		@Parameter(name="과제ID")
		private Integer asnId;

		@Parameter(name="운영교과서ID")
		private String optTxbId;

		@Parameter(name="교사사용자ID")
		private String tcrUsrId;

		@Parameter(name="과제명")
		@Size(max=50, message = "25자 이하로 입력해주세요.")
		private String asnNm;

		@Parameter(name="과제설명")
		private String asnCn;

		@Parameter(name="과제유형코드")
		private String asnTpCd;

		@Parameter(name="학습유형코드")
		private String lrnTpCd;

		@Parameter(name="과제기간구분코드")
		private String asnPtmeDvCd;

		@Parameter(name="시작일시")
		private String strDtm;

		@Parameter(name="종료일시")
		private String endDtm;

		@Parameter(name="마감이후제출가능여부")
		private String flnAfSmtAbleYn;

		@Parameter(name="과제평가방식유형코드")
		private String evMthdTpCd;

		@Parameter(name="만점점수")
		private Integer pscScr;

		@Parameter(name="삭제여부")
		private String delYn;
		
		@Parameter(name="사용여부")
		private String useYn;

		@Parameter(name="잠금여부")
		private String lcknYn;

		@Parameter(name="전체출제여부")
		private String allStxqYn;
		
		@Parameter(name="생성자ID")
		private String crtrId;

		@Parameter(name="생성일시")
		private String crtDtm;

		@Parameter(name="수정자ID")
		private String mdfrId;

		@Parameter(name="수정일시")
		private String mdfDtm;

		@Parameter(name="데이터베이스ID")
		private String dbId;

		@Parameter(name="총학습수")
		private int ttlLrnCnt;

		//EA_과제범위
		@Parameter(name="과제범위순번")
		private int asnRngeSeqNo;

		@Parameter(name="학습단계구분코드")
		private String lrnStpDvCd;

		@Parameter(name="단원노드ID")
		private String luNodId;

		@Parameter(name="차시노드ID")
		private String tcNodId;

		@Parameter(name="활통콘텐츠ID")
		private String atvCtnId;

		@Parameter(name="특별학습ID")
		private String spLrnId;


		//EA_과제제출
		@Parameter(name="학생사용자ID")
		private String stuUsrId;

		@Parameter(name="제출일시")
		private String smtDtm;

		@Parameter(name="제출내용")
		private String smtCn;

		@Parameter(name="제출완료여부")
		private String smtCmplYn;

		@Parameter(name="점수")
		private Integer scr;

		@Parameter(name="피드백내용")
		private String fdbkCn;

		@Parameter(name="완료학습수")
		private int cmplLrnCnt;

		//EA_모둠과제제출
		@Parameter(name="모둠ID")
		private Integer grpId;

		@Parameter(name="모둠팀ID")
		private Integer grpTemId;


		//EA_모둠
		@Parameter(name="모둠명")
		private String grpClNm;

		@Parameter(name="팀수")
		private int temCnt;


		//EA_모둠팀
		@Parameter(name="모둠팀명")
		private String GrpTemNm;

		@Parameter(name="팀원수")
		private int tmbrCnt;


		//EA_모둠팀원
		@Parameter(name="사용자명")
		private String usrNm;

		@Parameter(name="모듬팀장여부")
		private String grpTmgrYn;

		//EA_모둠게시판
		@Parameter(name="게시판ID")
		private String BlbdId;

		@Parameter(name="게시판명")
		private String blbdNm;


		//추가
		@Parameter(name="정렬구분코드")
		private String tebCd;

		@Parameter(name="단원차시명")
		private String nodNm;

		@Parameter(name="기간")
		private String asnPtmeDv;

		@Parameter(name="학생이름")
		private String stuUsrNm;

		@Parameter(name="교과활동 List")
		private List<String> atvCtnList;

		@Parameter(name="전체학생 수")
		private int totUsrCnt;

		@Parameter(name="과제 완료 학생 수")
		private int stuCmpAsgCnt;

		@Parameter(name="과제 미완료 학생 수")
		private int stuImprfAsgCnt;

		@Parameter(name="유형코드")
		private String asnLrnTp;

		@Parameter(name="시작일시")
		private String strDtmNm;

		@Parameter(name="종료일시")
		private String endDtmNm;

		@Parameter(name="과제학습유형코드이름")
		private String asnLrnTpNm;

		@Parameter(name="학생ID List")
		private List<String> usrIdList;

		@Parameter(name="정렬조건")
		private String sortOption;

		@Parameter(name="조회조건")
		private String searchOption;
		
		@Parameter(name="ai조회조건")
		private List<String> aiSearchOptionList;
		
		@Parameter(name="유형 조회조건")
		private String asnTpOption;
	

		@Parameter(name="븍별학습노드 List")
		private List<NodIdInfo> luNodList;

		@Parameter(name="학습단계구분코드명")
		private String lrnStpDvCdNm;

		@Parameter(name="단원노드명")
		private String luNodIdNm;

		@Parameter(name="차시노드명")
		private String tcNodIdNm;

		@Parameter(name="활통콘텐츠명")
		private String atvCtnIdNm;

		@Parameter(name="기존모둠ID")
		private Integer originGrpId;

		@Parameter(name="모둠팀ID List")
		private List<Integer> grpTemIdList;

		@Parameter(name="모둠별게시판생성여부")
		private String createBoardYn;

		@Parameter(name="완료일시")
		private String smtDtmNm;

		@Parameter(name="번호")
		private String stuNo;

		@Parameter(name="점수여부")
		private String scrYn;

		@Parameter(name="과제완료모둠개수")
		private int completeGrpCnt;

		@Parameter(name="과제미완료모둠개수")
		private int inCompleteGrpCnt;

		@Parameter(name="과제대상모둠개수")
		private int totalGrpCnt;

		@Parameter(name="모둠명")
		private String grpNm;

		@Parameter(name="수정삭제구분")
		private String flag;

		@Parameter(name="최초제출일시")
		private String fstSmtDtm;

		@Parameter(name="모둠게시판여부")
		private int blbdYn;

		@Parameter(name="점수피드백구분자")
		private String target;

		@Parameter(name="모둠별게시판생성여부")
		private int createBlbdAll;

		@Parameter(name="평가ID")
		private Integer evId;

		@Parameter(name="최종 문제 수")
		private int fnlQstCnt;

		@Parameter(name="정답 수")
		private int cansCnt;

		@Parameter(name="진도율")
		private String progressRate;

		@Parameter(name="총 개수")
		private int totalCnt;

		private int pageNo;
		private int pageSize;

		@Parameter(name="수정여부")
		private String updateYn;

		@Parameter(name="수정일")
		private String mdfDtmNm;

		@Parameter(name="제출완료여부명")
		private String smtCmplYnNm;

		@Parameter(name="게시판개수")
		private int blbdCnt;

		@Parameter(name="칭찬도장사용여부")
		private String prasStmpUseYn;
		
		@Parameter(name="대화사용여부")
		private String dilgUseYn; 

		@Parameter(name="마이홈포인트사용여부")
		private String myhmPntUseYn;
		
	    @Parameter(name="사용자유형코드")
	    private String usrTpCd;
	    
	    @Parameter(name="학급ID")
		private String classId;
	    
	    @Parameter(name="학습완료수")
		private int cmplCnt;
	    
	    @Parameter(name="과목구분코드")
	  	private String sbjCd;

	    @Parameter(name="단원평가완료여부")
	    private String luevCmplYn;
	    
		@Parameter(name="단원번호")
		private String nodNo;
		
		@Parameter(name="차시번호")
		private String tcNo;
		
		@Parameter(name="차시이름")
		private String tcNm;
		
		@Parameter(name="학습활동ID")
		private String lrnAtvId;
		
		@Parameter(name="묶음과제ID")
		private String pkgAsnId;
		
		@Parameter(name="학급 List")
		private List<OptTxbInfo> optTxbList;
		
		@Parameter(name="묶음과제완료여부")
		private String pkgCmpYn;
		
		@Parameter(name="묶음과제여부")
		private String pkgAsnYn;
			
		@Parameter(name="차시잠금여부")
		private String tcLcknYn;
		
		@Parameter(name="추가 학생ID List")
		private List<String> addStuList;
		
		
		@Data
		public static class OptTxbInfo {
			@JsonProperty("optTxbId")
			private String optTxbId;

			@JsonProperty("claId")
			private String claId;
		}

		@Data
		public static class NodIdInfo {
			@JsonProperty("no")
			private String no;

			@JsonProperty("spLrnNodId")
			private String spLrnNodId;
		}

		private List<userName> userInfo;

		@Data
		public static class userName {
			@JsonProperty("usrNm")
			private String usrNm;

			@JsonProperty("grpTmgrYn")
			private String grpTmgrYn;

			@JsonProperty("stuUsrId")
			private String stuUsrId;
		}

		private List<grpUserInfo> grpUserInfo;

		@Data
		public static class grpUserInfo {
			@JsonProperty("grpId")
			private int grpId;

			@JsonProperty("grpTemId")
			private int grpTemId;

			@JsonProperty("stuUsrId")
			private String stuUsrId;

			@JsonProperty("grpTmgrYn")
			private String grpTmgrYn;

			@JsonProperty("temCnt")
			private int temCnt;
		}

		/** 파일 관련 VO **/
		@Parameter(name="첨부파일ID")
		private int annxFileId;

		@Parameter(name="첨부ID")
		private int annxId;

		@Parameter(name="정렬순서")
		private int srtOrdn;

		@Parameter(name="첨부파일명")
		private String annxFileNm;

		@Parameter(name="첨부파일원본명")
		private String annxFileOrglNm;

		@Parameter(name="첨부파일확장자명")
		private String annxFileFextNm;

		@Parameter(name="첨부파일사이즈")
		private int annxFileSize;

		@Parameter(name="첨부파일경로명")
		private String annxFilePathNm;
		
	    @Parameter(name="문서뷰어ID")
	    private String docViId;
		
	   public void setUsrIdList(List<String> usrIdList) {
	        this.usrIdList = usrIdList;
	    }
	   
		public void setAiSearchOptionList(List<String> aiSearchOptionList) {
		    this.aiSearchOptionList = aiSearchOptionList;
		}
		
}

