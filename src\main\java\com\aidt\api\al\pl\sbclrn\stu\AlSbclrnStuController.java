package com.aidt.api.al.pl.sbclrn.stu;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.sbclrn.dto.AlSbclrnStuResponseDto;
import com.aidt.api.al.pl.sbclrn.dto.AlSbclrnStuSrhDto;
import com.aidt.api.al.pl.sbclrn.dto.LrnAtvDto;
import com.aidt.api.al.pl.sbclrn.dto.LrnStpDto;
import com.aidt.api.al.pl.sbclrn.dto.LrnTlDto;
import com.aidt.api.al.pl.sbclrn.dto.TxbPdfDto;
import com.aidt.api.al.pl.sbclrn.dto.WkbPdfDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-20 
 * @modify date 2024-02-20 
 * @desc 학습창 학습목록 조회
 */
@Slf4j
@Tag(name="[al] 학습창 학습목록 조회[AlTlStu]", description="학습창 학습목록 조회")
@RestController
@RequestMapping("/api/v1/tl/stu/sbclrn")
public class AlSbclrnStuController {
    
    @Autowired
    private JwtProvider jwtProvider;
    
    /**
     * 학습목록 조회 임시 controller
     *
     * @param AlTlStuDto
     * @return responseLearningDto
     */
    @Operation(summary="학습목록 조회 임시", description="학습목록 임시 조회 컨트롤러")
    @PostMapping(value = "/if/lrw/selectLearningData")
    public AlSbclrnStuResponseDto selectLearningData(@RequestBody AlSbclrnStuSrhDto srhDto) {
    	log.debug("selectLearningData");
    	
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	srhDto.setOptTxbId(userDetails.getOptTxbId());
    	srhDto.setUserId(userDetails.getUsrId());
    	
    	
    	
        // TODO 임시데이터 set
        AlSbclrnStuResponseDto responseDto = new AlSbclrnStuResponseDto();
        
        //교과서 PDF 정보목록
        TxbPdfDto txbPdf = new TxbPdfDto();
        List<TxbPdfDto> txbPdfList = new ArrayList<>();
        txbPdf.setPgeNo("1");
        txbPdf.setCdnPthNm("");
        txbPdfList.add(txbPdf);
        
        //익힘책PDF정보목록
        WkbPdfDto wkbPdf = new WkbPdfDto();
        List<WkbPdfDto> wkbPdfList = new ArrayList<>();
        wkbPdf.setPgeNo("1");
        wkbPdf.setCdnPthNm("");
        
        //학습도구
        LrnTlDto lrnTl = new LrnTlDto();
        List<LrnTlDto> lrnTlList = new ArrayList<>();
        lrnTl.setLrnTlCd("");
        lrnTl.setLrnTlNm("");
        lrnTl.setIcnCdnPthNm("");
        lrnTl.setIcnFleTpCd("");
        lrnTlList.add(lrnTl);
        
        // 학습단계 > 활동
        LrnAtvDto lrnAtv = new LrnAtvDto();
        List<LrnAtvDto> lrnAtvList = new ArrayList<>();
        lrnAtv.setLrnAtvId("");
        lrnAtv.setLrnAtvNm("");
        lrnAtv.setCtnTpCd("");
        lrnAtv.setCtnUrl("");
        lrnAtv.setRcstnOrdn(0);
        lrnAtv.setLrnStCd("");
        lrnAtv.setLrnTmScnt("");
        lrnAtvList.add(lrnAtv);
        
        // 학습단계
        LrnStpDto lrnStp = new LrnStpDto();
        List<LrnStpDto> lrnStpList = new ArrayList<>();
        lrnStp.setLrnStpId("");
        lrnStp.setLrnStpNm("도입");
        lrnStp.setLrnStpOrdn(1);
        lrnStp.setLrnAtvList(lrnAtvList);
        lrnStpList.add(lrnStp);
        
        
        responseDto.setLluNodNo("");
        responseDto.setLluNodId("E3MATA01");
        responseDto.setLluNodNm("대단원");
        responseDto.setLluEpsYn("Y");
        responseDto.setMluNodId("");
        responseDto.setMluNodNm("");
        responseDto.setMluEpsYn("Y");
        responseDto.setSluNodId("");
        responseDto.setSluNodNm("");
        responseDto.setSluEpsYn("Y");
        responseDto.setTcNodId("E3MATA01B01C01D01");
        responseDto.setTcNodNm("색종이를 몇장씩가져올까요?");
        responseDto.setTcEpsYn("Y");
        responseDto.setLcknYn("N");
        responseDto.setAtvTotCnt("1");
        responseDto.setStrLrnAtvId("");
        responseDto.setTxbStrPgeNo("47"); 
        responseDto.setTxbEndPgeNo("49");
        responseDto.setTxbUseYn("Y");
        responseDto.setWkbStrPgeNo("");
        responseDto.setWkbEndPgeNo("");
        responseDto.setWkbUseYn("N");
        responseDto.setTxbPdfList(txbPdfList);
        responseDto.setWkbPdfList(wkbPdfList);
        responseDto.setLrnTlList(lrnTlList);
        responseDto.setLrnStpList(lrnStpList);

        return responseDto;
    }
}
