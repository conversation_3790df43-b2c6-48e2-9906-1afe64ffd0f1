package com.aidt.api.appevent.event.adminstat;

import java.time.LocalDateTime;

import com.aidt.base.message.application.AbstractAppEvent;
import com.aidt.base.message.messagequeue.AbstractPayload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 신규 회원 데이터 등록 Event
 * - 변수 추가 및 수정 시 관리자 kafka 개발 담당자 전달 필수
 */
public class UsrInsertEvent extends AbstractAppEvent {

	public UsrInsertEvent(UsrInsertPayload payload) {
		super(payload);
	}

	@Builder
	@Getter
	@ToString
	public static class UsrInsertPayload extends AbstractPayload {

		@Schema(description = "사용자ID")
		private String usrId;

		@Schema(description = "KERIS사용자ID")
		private String kerisUsrId;

		@Schema(description = "사용자명")
		private String usrNm;

		@Schema(description = "사용자유형코드")
		private String usrTpCd;

		@Schema(description = "최초등록일시")
		private LocalDateTime fstRegDtm;

		@Schema(description = "학급ID")
		private String claId;

		@Schema(description = "생성자ID")
		private String crtrId;

		@Schema(description = "생성일시")
		private LocalDateTime crtDtm;

		@Schema(description = "수정자ID")
		private String mdfrId;

		@Schema(description = "수정일시")
		private LocalDateTime mdfDtm;
	}
}
