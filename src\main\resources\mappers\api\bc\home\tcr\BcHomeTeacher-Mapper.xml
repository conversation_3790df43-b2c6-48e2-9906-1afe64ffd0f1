<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.home.tcr">

	<select id="getHomeSummaryAiCoaching"  resultType="com.aidt.api.bc.home.dto.HomeSummaryAiCoaching">
		WITH QEUSIOTON_ANSWER_BY_MLU_NOD AS (
			SELECT MAX(DPTH2.OPT_TXB_ID)            AS OPT_TXB_ID,
				   MAX(OV.EV_ID)                    AS EV_ID,
				   OV.USR_ID                        AS USR_ID,
				   DPTH2.KMMP_NOD_ID                AS MLU_KMMP_NOD_ID,
				   IFNULL(COUNT(OV.QTM_ID), 0)      AS QTM_CNT,
				   IFNULL(SUM(OV.CANS_YN = 'Y'), 0) AS CANS_Y_CNY
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
					 LEFT OUTER JOIN (
				SELECT EE.EV_ID
					 , EER.USR_ID
					 , EER.EV_CMPL_YN
					 , EAETR.OPT_TXB_ID
					 , EAETR.MLU_KMMP_NOD_ID
					 , EEQ.QTM_ID
					 , EEQA.CANS_YN
				FROM LMS_LRM.EA_EV EE
						 INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
						 INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
									ON EAETR.EV_ID = EE.EV_ID AND EAETR.OPT_TXB_ID = EE.OPT_TXB_ID
						 LEFT OUTER JOIN LMS_LRM.EA_EV_QTM EEQ
										 ON EE.EV_ID = EEQ.EV_ID and EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
						 LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
										 ON EE.EV_ID = EEQA.EV_ID
											 AND EEQ.QTM_ID = EEQA.QTM_ID
											 AND EEQA.USR_ID = EER.USR_ID
				WHERE EE.EV_DV_CD = 'AE'
				  AND EE.EV_DTL_DV_CD = 'OV'
				  AND EER.EV_CMPL_YN = 'Y'
			) OV on OV.OPT_TXB_ID = DPTH2.OPT_TXB_ID AND OV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
			WHERE DPTH2.OPT_TXB_ID = #{optTxbId}
			  AND DPTH2.DPTH = 2
			  AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
			GROUP BY DPTH2.KMMP_NOD_ID, OV.USR_ID
		)
		SELECT USR.USR_ID,
			   Q.EV_ID,
			   Q.QTM_CNT AS QUESTION_COUNT,
			   Q.CANS_Y_CNY AS CORRECT_COUNT
		FROM LMS_LRM.CM_USR USR
				 INNER JOIN LMS_LRM.CM_OPT_TXB COT2 ON USR.CLA_ID = COT2.CLA_ID
				 LEFT JOIN QEUSIOTON_ANSWER_BY_MLU_NOD Q ON Q.USR_ID = USR.USR_ID AND Q.OPT_TXB_ID = COT2.OPT_TXB_ID
		WHERE COT2.OPT_TXB_ID = #{optTxbId}
		  AND USR.USR_TP_CD = 'ST'
	</select>

</mapper>