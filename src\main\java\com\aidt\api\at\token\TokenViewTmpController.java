package com.aidt.api.at.token;

import java.security.Key;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.base.config.JwtConfig.JwtManager;
import com.aidt.base.exception.CustomException;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token")
public class TokenViewTmpController {

	@Autowired
	private JwtManager jwtManager;

	@Value("${spring.config.activate.on-profile}")
	private String onProile;

	@GetMapping(value = "/check")
	public ResponseDto<Object> check(@RequestParam("token") String token) {
		Map<String, Object> result = new HashMap<String, Object>();

		if ("local".equals(onProile) || "dev".equals(onProile)) {
			try {
				Key key = jwtManager.getJwtTokenProperties().getAccess().getKey();
				Jws<Claims> claims = Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);
				result.put("status", "토큰 만료전 상태");
				result.put("exp", "토큰 만료 예정 시간 [한국시간 기준] = " + DateFormatUtils.format(claims.getBody().getExpiration(),
						"yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Seoul")));
				result.put("issue", "토큰 발급 시간 [한국시간 기준]" + DateFormatUtils.format(claims.getBody().getIssuedAt(),
						"yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Seoul")));
			} catch (ExpiredJwtException eje) {
				result.put("status", "토큰 만료");
				result.put("msg", "토근 정보 [UTC 0기준] >>> " + eje.getMessage());
			} catch (CustomException ce) {
				result.put("status", "오류 발생");
				result.put("msg", ce.getMessage());
			}
		} else {
			result.put("msg", "지원하지 않는 서버입니다.");
		}

		return Response.ok(result);
	}

	@GetMapping(value = "/refreshCheck")
	public ResponseDto<Object> refreshCheck(@RequestParam("token") String token) {
		Map<String, Object> result = new HashMap<String, Object>();

		if ("local".equals(onProile) || "dev".equals(onProile)) {
			try {
				Key key = jwtManager.getJwtTokenProperties().getRefresh().getKey();
				Jws<Claims> claims = Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);
				result.put("status", "토큰 만료전 상태");
				result.put("exp", "토큰 만료 예정 시간 [한국시간 기준] = " + DateFormatUtils.format(claims.getBody().getExpiration(),
						"yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Seoul")));
				result.put("issue", "토큰 발급 시간 [한국시간 기준]" + DateFormatUtils.format(claims.getBody().getIssuedAt(),
						"yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("Asia/Seoul")));
			} catch (ExpiredJwtException eje) {
				result.put("status", "토큰 만료");
				result.put("msg", "토근 정보 [UTC 0기준] >>> " + eje.getMessage());
			} catch (CustomException ce) {
				result.put("status", "오류 발생");
				result.put("msg", ce.getMessage());
			}
		} else {
			result.put("msg", "지원하지 않는 서버입니다.");
		}

		return Response.ok(result);
	}
}
