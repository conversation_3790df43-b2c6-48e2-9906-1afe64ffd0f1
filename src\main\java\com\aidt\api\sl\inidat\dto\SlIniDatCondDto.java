package com.aidt.api.sl.inidat.dto;


import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-04 10:53:59
 * @modify date 2024-03-04 10:53:59
 * @desc SlIniDatSrhDto 특별학습초기데이터 작성조건
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlIniDatCondDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 교사사용자ID */
    @Parameter(name="교사사용자ID")
    private String tcrUsrId;
    
    /** 학생사용자ID */
    @Parameter(name="학생사용자ID")
    private String stuUsrId;
    
    /** 학급ID */
    @Parameter(name="학급ID")
    private String claId;
    
    /** DB ID*/
    @Parameter(name="DB ID")
    private String dbId;
}
