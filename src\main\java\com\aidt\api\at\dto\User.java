package com.aidt.api.at.dto;


import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class User {
	
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequestDto {
        @NotBlank(message = "{field.required}")
        private String usrId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class UserResponseDto {
        private String usrId;
        private String kerisUsrId;
        private String usrNm;
        private String optTxbId;
        private String txbId;
        private String claId;
        private String usrTpCd;
        private String role;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseDto {
        private String timestamp;
        private int code;
        private String status;
        private String message;
        private User.UserResponseDto data;
    }
}
