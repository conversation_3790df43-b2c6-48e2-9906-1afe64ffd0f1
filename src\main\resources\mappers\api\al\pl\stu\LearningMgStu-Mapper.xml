<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.stu.learningMgStu">
	
	<select id="selectStuTpcAvnUnsEvInfo" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
			MAX(DPTH1.ORGL_ORDN) AS ORGL_ORDN,
			MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
			MAX(DPTH2.TC_USE_YN) AS TC_USE_YN,
			MAX(DPTH2.USE_YN) AS USE_YN,
			MAX(DPTH2.LCKN_YN) AS LCKN_YN,
			MAX(DPTH5.ORGL_ORDN) AS ORGL_ORDN,
			DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			IFNULL(MAX(AUTP.TPC_AVN), 0.5) AS TPC_AVN,
			MAX(EV.EV_ID) AS EV_ID,
			IFNULL(MAX(EV.EV_DTL_DV_CD), MAX(OV.EV_DTL_DV_CD)) AS EV_DTL_DV_CD,
			DATE_FORMAT(
				(SELECT A.CRT_DTM
				FROM LMS_LRM.EA_EV_QTM_ANW A
				WHERE A.EV_ID = IFNULL(MAX(EV.EV_ID), MAX(OV.EV_ID))
				ORDER BY A.CRT_DTM DESC
				LIMIT 1), '%m-%d') AS CRT_DTM,
			MAX(TM.XPL_TM_SCNT) AS LEARNING_TM,
			MAX(OV.EV_ID) AS OV_EV_ID
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2 ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3 ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4 ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID AND DPTH2.OPT_TXB_ID = DPTH4.OPT_TXB_ID
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5 ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID AND DPTH2.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP 
		ON DPTH5.KMMP_NOD_ID = AUTP.TPC_ID 
		AND AUTP.USR_ID = #{usrId}
		LEFT OUTER JOIN (
			SELECT
				MAX(EER.USR_ID) AS USR_ID,
				EE.EV_ID,
				MAX(EE.EV_DV_CD) AS EV_DV_CD,
				MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
				MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
				MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
				MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
				IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS TPC_CMPL_YN
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
			WHERE EE.EV_DV_CD = 'AE'
			AND EE.EV_DTL_DV_CD = 'OV'
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EER.USR_ID = #{usrId}
			AND EER.EV_CMPL_YN = 'Y'
			GROUP BY EE.EV_ID 
			ORDER BY CAST(SUBSTRING(MAX(EE.EV_DTL_DV_CD), 2) AS UNSIGNED) DESC
		)OV ON DPTH2.OPT_TXB_ID = OV.OPT_TXB_ID AND AUTP.USR_ID = OV.USR_ID AND DPTH2.KMMP_NOD_ID = OV.MLU_KMMP_NOD_ID 
		LEFT OUTER JOIN (
			SELECT
				MAX(EER.USR_ID) AS USR_ID,
				MAX(EE.EV_ID) AS EV_ID,
				MAX(EE.EV_DV_CD) AS EV_DV_CD,
				MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
				MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
				MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
				MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
				IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS TPC_CMPL_YN,
				COUNT(EEQ.QTM_ID) AS QTM_CNT,
				COUNT(EEQA.QTM_ID) AS QTM_ANW_CNT,
				SUM(EER.EV_CMPL_YN = 'Y') EV_CMPL_YN_CNT
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID
			WHERE EE.EV_DV_CD = 'AE'
			AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EER.USR_ID = #{usrId}
			AND EER.EV_CMPL_YN = 'Y'
			GROUP BY EEQ.EV_ID 
			ORDER BY CAST(SUBSTRING(MAX(EE.EV_DTL_DV_CD), 2) AS UNSIGNED) DESC
		)EV ON DPTH2.OPT_TXB_ID = EV.OPT_TXB_ID AND AUTP.USR_ID = EV.USR_ID AND DPTH2.KMMP_NOD_ID = EV.MLU_KMMP_NOD_ID
		LEFT OUTER JOIN (
			SELECT
			    SUM(EEQA.XPL_TM_SCNT) AS XPL_TM_SCNT,
			    EAETR.MLU_KMMP_NOD_ID,
				MAX(EER.USR_ID) AS USR_ID,
				MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID  AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID
			WHERE EE.EV_DV_CD = 'AE'
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EER.USR_ID = #{usrId}
			AND EER.EV_CMPL_YN = 'Y'
			GROUP BY EAETR.MLU_KMMP_NOD_ID
		)TM ON DPTH2.OPT_TXB_ID = TM.OPT_TXB_ID AND AUTP.USR_ID = TM.USR_ID AND DPTH2.KMMP_NOD_ID = TM.MLU_KMMP_NOD_ID
		LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
			ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
			AND BALAC.CTN_TP_CD = 'QU'
			AND BALAC.DEL_YN = 'N'
		WHERE DPTH2.DPTH = 2
		AND DPTH2.OPT_TXB_ID = #{optTxbId}
		AND BALAC.KMMP_NOD_ID IS NOT NULL
		AND DPTH1.USE_YN = 'Y'
		GROUP BY DPTH5.KMMP_NOD_ID
		ORDER BY MAX(DPTH1.RCSTN_ORDN), MAX(DPTH1.ORGL_ORDN), MAX(DPTH2.RCSTN_ORDN), MAX(DPTH2.ORGL_ORDN), MAX(DPTH3.RCSTN_ORDN), MAX(DPTH3.ORGL_ORDN), MAX(DPTH4.RCSTN_ORDN), MAX(DPTH4.ORGL_ORDN), MAX(DPTH5.RCSTN_ORDN), MAX(DPTH5.ORGL_ORDN)
		/* AI 학생 학습리포트 - 단원별 토픽숙련도 및 최근평가 조회 - 이혜인 - learningMgStu-Mapper.xml - selectStuTpcAvnUnsEvInfo */
	</select>
	
	
	
</mapper>