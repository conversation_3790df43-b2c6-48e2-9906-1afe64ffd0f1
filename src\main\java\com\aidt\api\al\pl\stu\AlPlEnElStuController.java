package com.aidt.api.al.pl.stu;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.api.al.pl.dto.AlPlMluLstInqEnElStuResponseDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-23 
 * @modify date 2024-05-23 
 * @desc AI맞춤학습 단원차시조회
 */
@Slf4j
@Tag(name="[al] AI맞춤학습 단원차시조회", description="AI맞춤학습 단원차시조회 조회")
@RestController
@RequestMapping("/api/v1/al/stu/en")
public class AlPlEnElStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private AlPlEnElStuService enElStuService;
	
	//@Autowired
	//private JwtProvider jwtProvider;
	
    /**
     * 학생 중단원목록 조회 controller
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="중단원목록 조회", description="중단원 목록 조회 컨트롤러")
    @PostMapping(value = "/selectMluLstInqEnElStuList")
    public ResponseDto<List<AlMluTcLstInqStuResponseDto>> selectMluLstInqEnElStuList(@Valid @RequestBody AlMluTcLstInqStuReqDto reqDto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(reqDto.getUsrId() == null) {
    		reqDto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(reqDto.getOptTxbId() == null) {
    		reqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(enElStuService.selectMluLstInqEnElStuList(reqDto));
    }
    
    /**
     * 학생 중단원목록 조회 controller
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="상세페이지에서 중단원목록 조회", description="상세페이지에서 중단원 목록 조회 컨트롤러")
    @PostMapping(value = "/selectMluLstInqEnElStuDetailList")
    public ResponseDto<List<AlMluTcLstInqStuResponseDto>> selectMluLstInqEnElStuDetailList(@Valid @RequestBody AlMluTcLstInqStuReqDto reqDto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(reqDto.getUsrId() == null) {
    		reqDto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(reqDto.getOptTxbId() == null) {
    		reqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(enElStuService.selectMluLstInqEnElStuDetailList(reqDto));
    }
    
    /**
     * 학생 상세에서 차시 정보 조회 controller
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="학생 상세에서 차시 정보 조회", description="학생 상세에서 차시 정보 조회 컨트롤러")
    @PostMapping(value = "/selectAiTcKmmpNodList")
    public ResponseDto<List<AlPlMluLstInqEnElStuResponseDto>> selectAiTcKmmpNodList(@Valid @RequestBody AlMluTcLstInqStuReqDto reqDto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	log.info("222222 : " + reqDto.getMkLuLrmNodId()); 
    	if(reqDto.getUsrId() == null) {
    		reqDto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(reqDto.getOptTxbId() == null) {
    		reqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(enElStuService.selectAiTcKmmpNodList(reqDto));
    }
    
    /**
     * 학생 상세에서 차시별 토픽 정보 조회 controller
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="학생 상세에서 차시별 토픽 정보 조회", description="학생 상세에서 차시별 토픽 정보 조회 컨트롤러")
    @PostMapping(value = "/selectAiTpcInfoList")
    public ResponseDto<List<AlPlMluLstInqEnElStuResponseDto>> selectAiTpcInfoList(@Valid @RequestBody List<AlMluTcLstInqStuReqDto> reqDto) {
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	log.info("######### : selectAiTpcInfoList");
    	return Response.ok(enElStuService.selectAiTpcInfoList(reqDto));
    }
    
}
