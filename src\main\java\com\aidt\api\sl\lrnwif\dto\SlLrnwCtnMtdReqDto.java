package com.aidt.api.sl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-07 14:13:26
 * @modify : date 2024-03-07 14:13:26
 * @desc : SlLrnwCtnMtdReqDto 메타데이터 정보 Request
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwCtnMtdReqDto {
	
	@Parameter(name="콘텐츠 ID")
	private String spLrnCtnId;
	
	/* FL : 파일형  UL: 경로형 */
	@Parameter(name="파일관리유형코드")
	private String fleMgTpCd;
	
	@Parameter(name="CDN경로명")
	private String cdnPthNm;

}
