package com.aidt.api.tl.lrnwif.dto;

import java.util.List;
import java.util.Map;

import com.aidt.api.tl.common.dto.TlCrclCntStdInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwLrnPdfDto PDF정보Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLrnwLrnPdfDto {
    /** 페이지번호 */
    @Parameter(name="페이지번호")
    private String pgeNo;
    /** 첨부ID */
    @Parameter(name="첨부ID")
	private Integer annxId;
    /** 첨부파일ID */
    @Parameter(name="첨부파일ID")
	private Integer annxFleId;
    /** CDN경로명 */
    @Parameter(name="CDN경로명")
    private String cdnPthNm;
    /** 필기 CDN경로명 */
    @Parameter(name="필기 CDN경로명")
	private String tnteCdnPthNm;
    /** 필기 canvas objects */
    @Parameter(name="필기 canvas objects")
	private List<Map<String, Object>> canvasJsonData;
	/** 교육과정콘텐츠표준 정보 */
    @Parameter(name="교육과정콘텐츠표준 정보")
    private List<TlCrclCntStdInfoDto> crclCntStdInfoList;
}
