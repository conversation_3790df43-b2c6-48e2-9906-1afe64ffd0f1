<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="api.sl.splrn.tcr">
    <!-- 특별학습 과제 제출할 목록 다건 조회 -->
    <select id="selectSpLrnTcrEaSendDataList" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnEaAsnViewDto">
        SELECT A.OPT_TXB_ID
              ,A.SP_LRN_ID  
              ,A.SP_LRN_NM 
              ,B.SP_LRN_NOD_ID
              ,B.SP_LRN_NOD_NM
              ,B.SP_LRN_NOD_CD
              ,A.RCSTN_ORDN
              ,B.SRT_ORDN
              ,A.USE_YN
         FROM LMS_LRM.SL_SP_LRN_RCSTN A /* SL_특별학습재구성 */
             LEFT JOIN LMS_CMS.BC_SP_LRN_NOD B /* BC_특별학습노드 */
			        ON A.SP_LRN_ID = B.SP_LRN_ID 
			       AND B.DEL_YN = 'N'
         WHERE A.OPT_TXB_ID = #{optTxbId}
           AND IFNULL(B.URNK_SP_LRN_NOD_ID, '') = ''
		   AND A.USE_YN = 'Y'
         ORDER BY A.RCSTN_ORDN, B.SRT_ORDN, B.SP_LRN_NOD_ID ASC
        /* 특별학습 지향난 SlSpLrnTcr-Mapper.xml - selectSpLrnTcrEaSendDataList */
    </select>
</mapper>
