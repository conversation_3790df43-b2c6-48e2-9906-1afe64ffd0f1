package com.aidt.api.ea.evcom.ev.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 평가 관리 - 응시설정 수정 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvUpdateTxmSetmDto {

	/* 평가 정보 */
	@Parameter(name="평가ID", required = true)
	private long evId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="사용자ID", required = true)
	private String usrId;

	@Parameter(name="평가명")
	private String evNm;

	@Parameter(name="평가구분코드")
	private String evDvCd;

	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;

	@Parameter(name="응시기간설정여부", required = true)
	@NotBlank(message = "{field.required}")
	private String txmPtmeSetmYn;

	@Parameter(name="응시시작일시")
	private String txmStrDtm;

	@Parameter(name="응시종료일시")
	private String txmEndDtm;

	@Parameter(name="풀이시간설정여부", required = true)
	@NotBlank(message = "{field.required}")
	private String xplTmSetmYn;

	@Parameter(name="풀이시간초수(분 제외한 초수)")
	private int xplTmScnt;

	@Parameter(name="문제수")
	private int qstCnt;

	@Parameter(name="최종 문제수")
	private int fnlQstCnt;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="재응시허용여부", required = true)
	private String rtxmPmsnYn;

	@Parameter(name="사용여부")
	private String useYn;

	@Parameter(name="데이터베이스ID")
	private String dbId;
}
