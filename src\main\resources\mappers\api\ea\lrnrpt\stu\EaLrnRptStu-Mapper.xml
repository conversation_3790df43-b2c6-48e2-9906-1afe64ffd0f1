<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.lrnrpt.stu">

	<!-- 학습요약 조회 -->
	<select id="selectLrnSumm" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">
        SELECT
            COUNT(1)                                                     AS stdDate,  -- 학습일
            IFNULL(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%H'),'00')  AS evTmScntH,-- 학습 시간 시
            IFNULL(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%i'),'00')  AS evTmScntM,-- 학습 시간 분
            IFNULL(sum(T.qstCnt),0)                                                AS qstCnt,   -- 문제 풀이 수
            IFNULL(FORMAT((SUM(T.cansCnt)/SUM(T.qstCnt))*100, 1), 0)     AS cansRt    -- 정답률
        FROM(
            SELECT
                G.stdDate,
                SUM(G.evTmScnt) AS evTmScnt,
	            SUM(G.qstCnt) as qstCnt, -- 문제 풀이 수
                SUM(G.cansCnt)  AS cansCnt
            FROM
            (
            /* Let's Write */
            select
				DATE_FORMAT(cwm.stu_sav_dtm,'%Y%m%d')	AS stdDate,
			    SUM(cwm.LRN_TM_SCNT) 					AS evTmScnt,
			    0                             			AS qstCnt,
				0                             			AS cansCnt
			from
				cm_wrt_mg cwm 
			where
				1=1
				and cwm.OPT_TXB_ID = #{optTxbId}
				and cwm.STU_USR_ID = #{usrId}
				AND cwm.LRN_TM_SCNT IS NOT NULL 
	            <choose>
		            <when test='srhKn == "month"'>
			            AND cwm.stu_sav_dtm BETWEEN 
						    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
						    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		            AND cwm.stu_sav_dtm <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND cwm.stu_sav_dtm <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(cwm.stu_sav_dtm) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
			GROUP BY DATE_FORMAT(cwm.stu_sav_dtm,'%Y%m%d')
            UNION ALL
            /*교과평가,AI평가,교사평가*/
            SELECT
                DATE_FORMAT(EER.MDF_DTM,'%Y%m%d')    AS stdDate,
                SUM(EER.EV_TM_SCNT) AS evTmScnt,
                SUM(EE.FNL_QST_CNT) AS qstCnt,
                SUM(EER.CANS_CNT)   AS cansCnt
            FROM LMS_LRM.EA_EV EE -- EA 평가
            INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID = #{usrId})
            WHERE EE.OPT_TXB_ID = #{optTxbId}
            AND EE.USE_YN      = 'Y'
            AND EE.DEL_YN      = 'N'
            and eer.smt_dtm is not null
            <choose>
	            <when test='srhKn == "month"'>
		            AND EER.MDF_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	            AND EER.MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND EER.MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(EER.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(EER.MDF_DTM,'%Y%m%d')
            
            UNION ALL
            /*교과평가,AI평가,교사평가*/
            SELECT
                DATE_FORMAT(EER.smt_dtm,'%Y%m%d')    AS stdDate,
                0 AS evTmScnt,
                -- SUM(EE.FNL_QST_CNT) AS qstCnt,
                0	AS	qstCnt,
                -- SUM(EER.CANS_CNT)   AS cansCnt
                0   AS cansCnt
            FROM LMS_LRM.EA_EV EE -- EA 평가
            INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID = #{usrId})
            WHERE EE.OPT_TXB_ID = #{optTxbId}
            AND EE.USE_YN      = 'Y'
            AND EE.DEL_YN      = 'N'
            and eer.smt_dtm is not null
            <choose>
	            <when test='srhKn == "month"'>
		            AND EER.smt_dtm BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	            AND EER.smt_dtm <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND EER.smt_dtm <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(EER.smt_dtm) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(EER.smt_dtm,'%Y%m%d')
            
            UNION ALL
            /*교과학습*/
            SELECT
                DATE_FORMAT(ST.MDF_DTM,'%Y%m%d') AS stdDate,
                SUM(ST.LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            from LMS_LRM.tl_sbc_lrn_atv_rcstn tslar 
            inner JOIN LMS_LRM.TL_SBC_LRN_ATV_ST ST
            	on TSLAR.OPT_TXB_ID = ST.OPT_TXB_ID
            	and TSLAR.LRMP_NOD_ID = ST.LRMP_NOD_ID
            	and TSLAR.LRN_ATV_ID = ST.LRN_ATV_ID
            WHERE TSLAR.OPT_TXB_ID = #{optTxbId}
            AND   ST.LRN_USR_ID = #{usrId}
            and	  TSLAR.CTN_TP_CD <![CDATA[<>]]> 'EX'
          --  AND   ST.LRN_ST_CD  = 'CL' -- 학습 완료
            <choose>
	            <when test='srhKn == "month"'>
	                AND ST.MDF_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	                AND ST.MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND ST.MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(ST.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(ST.MDF_DTM,'%Y%m%d')
            
            UNION ALL
            /*교과학습*/
            SELECT
                DATE_FORMAT(ST.CRT_DTM,'%Y%m%d') AS stdDate,
                0             AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            from LMS_LRM.tl_sbc_lrn_atv_rcstn tslar 
            inner JOIN LMS_LRM.TL_SBC_LRN_ATV_ST ST
            	on TSLAR.OPT_TXB_ID = ST.OPT_TXB_ID
            	and TSLAR.LRMP_NOD_ID = ST.LRMP_NOD_ID
            	and TSLAR.LRN_ATV_ID = ST.LRN_ATV_ID
            WHERE TSLAR.OPT_TXB_ID = #{optTxbId}
            AND   ST.LRN_USR_ID = #{usrId}
            and	  TSLAR.CTN_TP_CD <![CDATA[<>]]> 'EX'
          --  AND   ST.LRN_ST_CD  = 'CL' -- 학습 완료
            <choose>
	            <when test='srhKn == "month"'>
	                AND ST.CRT_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	                AND ST.CRT_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND ST.CRT_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(ST.CRT_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(ST.CRT_DTM,'%Y%m%d')
            
            UNION ALL
            /* 교과학습 - 선생님 추가 컨텐츠 */
            select
        	DATE_FORMAT(MDF_DTM,'%Y%m%d') AS stdDate,
        	SUM(LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
        	FROM TL_TCR_REG_CTN_ST TTRCS
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID = #{usrId}
          --  AND   LRN_ST_CD  = 'CL' -- 학습 완료
            <choose>
	            <when test='srhKn == "month"'>
	                AND MDF_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	                AND MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d')
            
            UNION ALL
            /* 교과학습 - 선생님 추가 컨텐츠 */
            select
        	DATE_FORMAT(CRT_DTM,'%Y%m%d') AS stdDate,
        		0              				AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
        	FROM TL_TCR_REG_CTN_ST TTRCS
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID = #{usrId}
          --  AND   LRN_ST_CD  = 'CL' -- 학습 완료
            <choose>
	            <when test='srhKn == "month"'>
	                AND CRT_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	                AND CRT_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND CRT_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(CRT_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(CRT_DTM,'%Y%m%d')
            
            UNION ALL
            /*특별학습*/
            SELECT
                DATE_FORMAT(MDF_DTM,'%Y%m%d') AS stdDate,
                SUM(LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            FROM LMS_LRM.SL_SP_LRN_PGRS_ST
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID = #{usrId}
          --  AND   LRN_ST_CD  = 'CL'-- 학습 완료
            <choose>
	            <when test='srhKn == "month"'>
	                AND MDF_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	            AND MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d')
            
			UNION ALL
            /*특별학습*/
            SELECT
                DATE_FORMAT(CRT_DTM,'%Y%m%d') AS stdDate,
                0              				  AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            FROM LMS_LRM.SL_SP_LRN_PGRS_ST
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID = #{usrId}
           -- AND   LRN_ST_CD  = 'CL'-- 학습 완료
            <choose>
	            <when test='srhKn == "month"'>
	                AND CRT_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
	            </when>
	            <when test='srhKn == "week"'>
	            AND CRT_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	                AND CRT_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
	                AND DAYOFWEEK(#{smtDtm}) = 1
	            </when>
	            <when test='srhKn == "day"'>
	                AND DATE(CRT_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
	            </when>
            </choose>
            GROUP BY DATE_FORMAT(CRT_DTM,'%Y%m%d')
            ) G GROUP BY G.stdDate
        ) T


        /* 학습 리포트 공통 - 김상민 - EaLrnRptStu-Mapper.xml - 학습 리포트 학습 요약 조회 - selectLrnSumm */
    </select>
    
    <!-- 학습리포트 - 요일별 분석 -->
    <select id="selectAnPerWeek" resultType="com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto">
    	/*EaLrnRptStu - selectAnPerWeek 요일별 분석 */
    	SELECT
		    w.week,
		    COALESCE(SUM(G.evTmScnt), 0) AS evTmScnt
		from 
		    (SELECT 1 AS day_of_week, '일' AS week
		     UNION ALL
		     SELECT 2 AS day_of_week, '월'
		     UNION ALL
		     SELECT 3 AS day_of_week, '화'
		     UNION ALL
		     SELECT 4 AS day_of_week, '수'
		     UNION ALL
		     SELECT 5 AS day_of_week, '목'
		     UNION ALL
		     SELECT 6 AS day_of_week, '금'
		     UNION ALL
		     SELECT 7 AS day_of_week, '토') w 
	     left join (
				select
					TXB_LRN_TM_SCNT + AI_LRN_TM_SCNT + SP_LRN_TM_SCNT + EV_LRN_TM_SCNT + AI_EV_LRN_TM_SCNT + AI_WRTNG_LRN_TM as evTmScnt,
					CRT_DTM as stdDate,
					DAYOFWEEK(CRT_DTM) as day_of_week 
				from
					cm_lrn_tm
				where
					1=1
					and OPT_TXB_ID = #{optTxbId}
					and USR_ID = #{usrId}
				) G ON w.day_of_week = G.day_of_week
			GROUP BY
    			w.day_of_week, w.week
			ORDER BY
    			w.day_of_week;
    </select>
    
    <!-- 시간대별 분석 -->
    <select id="selectAnPerTm" resultType="com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto">
	    SELECT 
		    ts.tm,
    		IFNULL(clt.evTmScnt,0) AS evTmScnt
		FROM 
		    (SELECT '오전' AS tm
		     UNION ALL
		     SELECT '오후'
		     UNION ALL
		     SELECT '저녁'
		     UNION ALL
		     SELECT '새벽') AS ts
		LEFT JOIN (
	    	SELECT 
				CASE
			        WHEN HOUR(MDF_DTM) <![CDATA[>=]]> 8 AND HOUR(MDF_DTM) <![CDATA[<]]> 12 THEN '오전'
			        WHEN HOUR(MDF_DTM) <![CDATA[=]]> 12 AND MINUTE(MDF_DTM) <![CDATA[=]]> 0 THEN '오전'
			        WHEN HOUR(MDF_DTM) <![CDATA[>=]]> 12 AND HOUR(MDF_DTM) <![CDATA[<]]> 18 THEN '오후'
			        WHEN HOUR(MDF_DTM) <![CDATA[=]]> 18 AND MINUTE(MDF_DTM) <![CDATA[=]]> 0 THEN '오후'
			        WHEN HOUR(MDF_DTM) <![CDATA[>=]]> 18 AND HOUR(MDF_DTM) <![CDATA[<]]> 24 THEN '저녁'
			        WHEN HOUR(MDF_DTM) <![CDATA[=]]> 0 and MINUTE(MDF_DTM) <![CDATA[=]]> 0  THEN '저녁'
			        when HOUR(MDF_DTM) <![CDATA[=]]> 0 and MINUTE(MDF_DTM) <![CDATA[>]]> 0 then '새벽'
			        WHEN HOUR(MDF_DTM) <![CDATA[>=]]> 1 AND HOUR(MDF_DTM) <![CDATA[<]]> 8 THEN '새벽'
			    END AS tm,
			    SUM(TXB_LRN_TM_SCNT + AI_LRN_TM_SCNT + SP_LRN_TM_SCNT + EV_LRN_TM_SCNT + AI_EV_LRN_TM_SCNT + AI_WRTNG_LRN_TM) as evTmScnt
			from
				cm_lrn_tm clt
			where
				OPT_TXB_ID = #{optTxbId}
				and USR_ID = #{usrId}
			GROUP BY
			    tm
		) clt on ts.tm = clt.tm
		GROUP BY
			ts.tm, clt.evTmScnt
		ORDER BY
		    FIELD(ts.tm, '오전', '오후', '저녁', '새벽')
		    /* EaLrnRptStu-Mapper.xml - selectAnPerTm 시간대별 분석 */
    </select>
    
    <!-- 선호도 조회 -->
    <select id="selectLrnPref" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto" resultType="hashMap">
	    select 
			ifnull(sum(TXB_LRN_TM_SCNT + SP_LRN_TM_SCNT + AI_WRTNG_LRN_TM), 0) as txbLrnTmScnt , -- 개념학습시간
			ifnull(sum(EV_LRN_TM_SCNT + AI_LRN_TM_SCNT + AI_EV_LRN_TM_SCNT), 0) as evLrnTmScnt -- 문제풀이시간
		from lms_lrm.cm_lrn_tm clt
		where 
			USR_ID = #{usrId}
			and OPT_TXB_ID = #{optTxbId}
			/* EaLrnRptStu - selectLrnPref */
    </select>
    	
    <!-- 학습 챌린지 시간 조회 -->
    <select id="selectLrnChal" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto" resultType="hashMap">
	    SELECT LRN_GOAL_DV_CD as lrnGoalDvCd  -- HR= 시간, EA=문제플이수
		      ,LRN_GOAL_QST_CNT as lrnGoalQstCnt /* 목표문제풀이수*/ 
		      ,LRN_GOAL_TM_SCNT as lrnGoalTmScnt /* 목표시간초수 */
		      , ifnull(CASE LRN_GOAL_DV_CD
		            WHEN 'EV' THEN IF(LRN_GOAL_QST_CNT <![CDATA[<=]]> IFNULL(LRN_ACV_QST_CNT,0), 100, CEIL(IFNULL(LRN_ACV_QST_CNT,0)/LRN_GOAL_QST_CNT *1000)/10) 
		        ELSE IF(IFNULL(LRN_GOAL_TM_SCNT,0) <![CDATA[<=]]> LRN_ACV_TM_SCNT, 100, CEIL(IFNULL(LRN_ACV_TM_SCNT,0)/LRN_GOAL_TM_SCNT *1000)/10) 
		        END,0) rate  -- 달성율
		      ,LRN_GOAL_ST_CD as lrnGoalStCd  /* CL:달성완료, DL=진행중 NL=미진행 */
		FROM LMS_LRM.tl_lrn_chlg /* TL_학습챌린지 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRN_USR_ID = #{usrId}
		AND CURDATE() BETWEEN LRN_STR_DT AND LRN_END_DT limit 1;
	</select>
    
	<!-- 종합분석 단원별 성취 현황 -->
	<select id="selectAllAnLuList" resultType="com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto" >
	SELECT A.LRMP_NOD_ID
			 , A.RCSTN_ORDN
			 , A.RCSTN_NO
			 , A.LRMP_NOD_NM
			 , SUM(CASE WHEN EV_CMPL_YN='Y' THEN A.FNL_QST_CNT ELSE 0 END ) AS FNL_QST_CNT
			 , SUM(CASE WHEN EV_CMPL_YN='Y' THEN A.CANS_CNT ELSE 0 END ) AS CANS_CNT
			 , SUM(CASE WHEN EV_CMPL_YN='Y' THEN 1 ELSE 0 END ) AS APYCNT
			  ,	CASE WHEN  SUM(CASE WHEN EV_CMPL_YN='Y' THEN 1 ELSE 0 END ) > 0 THEN			   
			  ROUND((SUM(CASE WHEN EV_CMPL_YN='Y' THEN A.CANS_RT ELSE 0 END ) / SUM(CASE WHEN EV_CMPL_YN='Y' THEN 1 ELSE 0 END )),1)
			  	ELSE 0 END 	  AS CANS_RT
		  FROM (
				SELECT RSLNR.LRMP_NOD_ID 
					 , RSLNR.LRMP_NOD_NM 
					 , RSLNR.RCSTN_ORDN
					 , RSLNR.RCSTN_NO
					 , EE.EV_ID
					 , EE.OPT_TXB_ID 
					 , EER.USR_ID
					 , EE.EV_DV_CD 
					 , EE.EV_DTL_DV_CD 
					 , EE.EV_NM 
					 , EE.FNL_QST_CNT /*최종문제수*/
					 , EETR.LU_LRMP_NOD_ID 
					 , EETR.LU_LRMP_NOD_NM 
					 , IFNULL(EER.CANS_CNT, 0) AS CANS_CNT
					 ,eer.EV_CMPL_YN					   
					 , EER.CANS_RT 
				  FROM TL_SBC_LRN_NOD_RCSTN RSLNR
				 INNER JOIN (select distinct LU_LRMP_NOD_ID,LU_LRMP_NOD_NM, EV_ID from  EA_EV_TS_RNGE where LU_OPT_TXB_ID = #{optTxbId}) EETR /*EA_평가시험범위*/ON RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID 
				  LEFT OUTER JOIN EA_EV EE ON EETR.EV_ID = EE.EV_ID 
				  LEFT OUTER JOIN EA_EV_RS EER ON EE.EV_ID = EER.EV_ID AND EER.USR_ID =  #{usrId}
				 WHERE RSLNR.OPT_TXB_ID = #{optTxbId}
				   AND RSLNR.DPTH = 1 
		           AND RSLNR.LU_EPS_YN = 'Y'
		           AND RSLNR.USE_YN= 'Y'
		           AND RSLNR.LU_NO_USE_YN= 'Y'
				   AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
				   ORDER BY RSLNR.RCSTN_ORDN
				  ) A
		  GROUP BY A.LRMP_NOD_ID
		  /* EaLrnRptStu-Mapper.xml- selectAllAnLuList */
	</select>
	
	<!-- 학습성장분석 조회 -->
	<select id="selectEvLrnGrowAnList" resultType="com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto">
		select
			A.mon as month,
			A.evTmScnt,
			B.fnlQstCnt,
			B.cansCnt
		from (SELECT 
					months.mon,
					ifnull(sum(TXB_LRN_TM_SCNT + AI_LRN_TM_SCNT + SP_LRN_TM_SCNT + EV_LRN_TM_SCNT + AI_EV_LRN_TM_SCNT),0) as evTmScnt -- 학습시간
				from
					(SELECT 1 AS mon UNION ALL
				     SELECT 2 UNION ALL
				     SELECT 3 UNION ALL
				     SELECT 4 UNION ALL
				     SELECT 5 UNION ALL
				     SELECT 6 UNION ALL
				     SELECT 7 UNION ALL
				     SELECT 8 UNION ALL
				     SELECT 9 UNION ALL
				     SELECT 10 UNION ALL
				     SELECT 11 UNION ALL
				     SELECT 12) AS months
				    left join cm_lrn_tm clt
				    	on months.mon = MONTH(clt.MDF_DTM)
				    	and year(clt.MDF_DTM) = year(CURDATE())
				    	and clt.USR_ID = #{usrId}
				GROUP by
					months.mon
				order BY 
					months.mon) A
			inner join (select
							months.mon,
							ifnull(sum(ee.FNL_QST_CNT), 0) as fnlQstCnt,
							IFNULL(sum(eer.cans_cnt),0) as cansCnt
						from 
							(SELECT 1 AS mon UNION ALL
						     SELECT 2 UNION ALL
						     SELECT 3 UNION ALL
						     SELECT 4 UNION ALL
						     SELECT 5 UNION ALL
						     SELECT 6 UNION ALL
						     SELECT 7 UNION ALL
						     SELECT 8 UNION ALL
						     SELECT 9 UNION ALL
						     SELECT 10 UNION ALL
						     SELECT 11 UNION ALL
						     SELECT 12) AS months
						left join EA_EV_RS EER ON months.mon = MONTH(eer.SMT_DTM)
						    AND YEAR(eer.SMT_DTM) = YEAR(CURDATE())
						    AND eer.USR_ID = #{usrId}
						LEFT JOIN EA_EV ee ON ee.ev_id = eer.ev_id
						    AND ee.USE_YN = 'Y'
						    AND ee.DEL_YN = 'N'
						    AND eer.EV_CMPL_YN = 'Y'
						GROUP BY
						    months.mon
						ORDER BY
						    months.mon) B
				on A.mon = B.mon
	</select>
	
	<!-- 평가별 분석 목록 조회 -->
	<select id="selectAllAnEvList" resultType="com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto" >
		SELECT distinct RSLNR.LRMP_NOD_ID 
			 , RSLNR.RCSTN_ORDN
			 , RSLNR.LRMP_NOD_NM 
			 , EE.EV_ID
			 , EE.EV_DV_CD 
			 , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = ee.EV_DV_CD) AS EV_DV_NM -- 평가구분명
			 , EE.EV_DTL_DV_CD 
			 , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = ee.EV_DTL_DV_CD) AS EV_DTL_DV_NM -- 평가구분명
			 , EE.EV_NM 
			 , EE.FNL_QST_CNT /*최종문제수*/
			 , EETR.LU_LRMP_NOD_ID 
			 , EETR.LU_LRMP_NOD_NM 
			 , IFNULL(EER.CANS_CNT, 0) AS CANS_CNT  /*정답수*/
			 , EER.CANS_RT /*정답률*/
			 ,case when EE.EV_DV_CD='AE' then (select DISTINCT  MLU_KMMP_NOD_ID  from EA_AI_EV_TS_RNGE EAETR where ev_id=EE.EV_ID LIMIT 1)
			 else null end as KMMP_NOD_ID
		  FROM TL_SBC_LRN_NOD_RCSTN RSLNR
		 inner join (select  b.LU_LRMP_NOD_ID,max(b.LU_LRMP_NOD_NM) LU_LRMP_NOD_NM, b.ev_id from  lms_lrm.ea_ev_ts_rnge b where b.LU_OPT_TXB_ID = #{optTxbId} group by b.LU_LRMP_NOD_ID,  b.ev_id) EETR 
		 ON RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID -- 1단원
		 INNER JOIN EA_EV EE ON EETR.EV_ID = EE.EV_ID 
		 INNER JOIN EA_EV_RS EER ON EE.EV_ID = EER.EV_ID AND EER.USR_ID =#{usrId}
		 WHERE RSLNR.OPT_TXB_ID = #{optTxbId}
		   AND RSLNR.LLU_NOD_ID = #{lluNodId}  -- 1단원 
		   AND RSLNR.DPTH = 1
		   AND eer.EV_CMPL_YN='Y'
		   AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
		   ORDER BY RSLNR.RCSTN_ORDN
		   
        /** EaLrnRptStu-Mapper.xml - selectAllAnEvList */
	</select>
	
	<select id="selectAllAnAraList" resultType="com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto" >
	SELECT
			USR_ID,
			KMMP_NOD_NM, -- 영역명
			KMMP_NOD_ID , -- 영역코드
			count(cans_yn) as totAnsCnt, -- 푼 문제
			ROUND((SUM(CASE WHEN CANS_YN='Y' THEN 1 ELSE 0 END )/count(cans_yn)) * 100) as cansRt,
			SUM(CASE WHEN CANS_YN='Y' THEN 1 ELSE 0 END ) AS cansCnt,  -- 맞은문항수
			SUM(CASE WHEN CANS_YN!='Y' THEN 1 ELSE 0 END ) AS iansCnt   -- 틀린문항수
		FROM(
			select
	  		eer.USR_ID,
				eer.EV_CMPL_YN	,
				QP_CN_ARA_NM KMMP_NOD_NM, -- 영역명
				QP_CN_ARA_ID KMMP_NOD_ID, -- 영역코드
				eeqa.CANS_YN -- 정답 여부
			FROM   lms_lrm.ea_ev ee
			       INNER JOIN lms_lrm.ea_ev_rs eer on ee.ev_id = eer.ev_id
			       INNER JOIN lms_lrm.ea_ev_qtm_anw eeqa
			       ON     ee.EV_ID=eeqa.EV_ID  AND  eer.USR_ID=eeqa.USR_ID
			       inner join lms_cms.qp_qtm_an qta on eeqa.QTM_ID=qta.QP_QTM_ID
			WHERE eer.EV_CMPL_YN='Y'
			AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
			AND eer.USR_ID= #{usrId}  -- 회원 조건
			AND exists (select 1 from ea_ev_ts_rnge z where z.LU_LRMP_NOD_ID=  #{lluNodId} AND ee.ev_id=z.EV_ID)
		) AA GROUP by USR_ID,KMMP_NOD_NM,KMMP_NOD_ID
		order by (select SRT_ORDN from cm_cm_cd cd WHERE  cd.URNK_CM_CD='CRCL_CN_ARA' and cd.CM_CD=AA.KMMP_NOD_ID) 
		
        /** EaLrnRptStu-Mapper.xml - selectAllAnAraList */
	</select>
	
	<!-- 차시별 분석 목록 조회 -->
	<select id="selectAllAnTcList" resultType="com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto" >
	SELECT distinct 
			RSLNR.RCSTN_ORDN
			 , RSLNR.LRMP_NOD_ID 
	 		 , RSLNR.LRMP_NOD_NM 
			 , EE.EV_ID
			 , EE.EV_DV_CD
			 , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = EE.EV_DV_CD) AS EV_DV_NM -- 평가구분명
			 , EE.EV_DTL_DV_CD 
			 , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = EE.EV_DTL_DV_CD) AS EV_DTL_DV_NM -- 평가구
			 , EE.EV_NM 
			 , EE.FNL_QST_CNT /*최종문제수*/
			 , EETR.LU_LRMP_NOD_ID 
			 , EETR.LU_LRMP_NOD_NM 
			 , IFNULL(EER.CANS_CNT, 0) AS CANS_CNT  /*정답수*/
			 , EER.CANS_RT /*정답률*/
		  FROM TL_SBC_LRN_NOD_RCSTN RSLNR
		  inner join 
		   (select  max(LU_LRMP_NOD_ID) LU_LRMP_NOD_ID , max(LU_LRMP_NOD_NM) LU_LRMP_NOD_NM ,TC_LRMP_NOD_ID,  b.ev_id from  lms_lrm.ea_ev_ts_rnge b where b.LU_OPT_TXB_ID = #{optTxbId} group by TC_LRMP_NOD_ID,ev_id) EETR
		    ON RSLNR.LRMP_NOD_ID = EETR.TC_LRMP_NOD_ID -- 차시
		  INNER JOIN EA_EV EE ON EETR.EV_ID = EE.EV_ID 
		  INNER JOIN EA_EV_RS EER ON EE.EV_ID = EER.EV_ID AND EER.USR_ID =#{usrId}
		 WHERE RSLNR.OPT_TXB_ID = #{optTxbId}
		   AND RSLNR.LLU_NOD_ID = #{lluNodId} 
		   AND eer.EV_CMPL_YN='Y'
		   AND RSLNR.DPTH = 4
		   AND ((EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO')))
		   -- AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
		   
        /** EaLrnRptStu-Mapper.xml - selectAllAnTcList */
		   
	</select>
	
	<!-- 학습코칭 : 과제제출내역 조회 -->
	<select id="selectEaLrnAsnInfo" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">
        /** EaLrnRptStu-Mapper.xml - selectEaLrnAsnInfo */
         SELECT
	            EGAS.STU_USR_ID as stuUsrId,
		        COUNT(EA.ASN_ID) AS totalAssignments,
		        COUNT(CASE
		                WHEN EA.END_DTM <![CDATA[<]]> NOW() OR EGAS.SMT_CMPL_YN = 'Y' THEN EA.ASN_ID
		            END) AS completedAssignments,
		        COUNT(CASE
		                WHEN (EA.STR_DTM <![CDATA[<=]]> NOW() AND EA.END_DTM > NOW() AND EGAS.SMT_CMPL_YN = 'N') 
		                    OR (EA.ASN_PTME_DV_CD = 'OT' AND EGAS.SMT_CMPL_YN = 'N') THEN EA.ASN_ID
		            END) AS ongoingAssignments
	    FROM EA_ASN EA 
	   INNER JOIN EA_ASN_SMT EGAS
	      ON EGAS.ASN_ID = EA.ASN_ID
	     AND EGAS.STU_USR_ID =#{usrId}
	   WHERE 1=1
	     AND EA.OPT_TXB_ID =  #{optTxbId}
	     AND EA.DEL_YN = 'N'
	</select>
	
	<!-- 성취별 분석 목록 조회 -->
	<select id="selectAllAnAchList" resultType="com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto">
	
        /* EaLrnRptStu-Mapper.xml- selectAllAnAchList */
        
        SELECT
				max(bnccm.CRCL_ACH_BS_CD) as eduCrsAchBsCd,
				max(bnccm.CRCL_ACH_BS_NM) as eduCrsAchBsCnNm,
				count(1) as total,
				ROUND((SUM(CASE WHEN CANS_YN='Y' THEN 1 ELSE 0 END )/count(cans_yn)) * 100) as cansRt
		FROM lms_lrm.EA_EV ee
		join lms_lrm.EA_EV_RS EER on eer.EV_ID = ee.EV_ID 
        JOIN lms_lrm.ea_ev_qtm_anw eeqa ON ee.EV_ID=eeqa.EV_ID AND eer.USR_ID=eeqa.USR_ID
		join lms_cms.bc_ntnl_crcl_ctn_mpn_v2 bnccm on BNCCM.CTN_ID = eeqa.QTM_ID AND bnccm.CRCL_CTN_TP_CD='EX'
		where ee.OPT_TXB_ID = #{optTxbId}
		and exists(SELECT 1 FROM lms_lrm.ea_ev_ts_rnge where ev_id = ee.ev_id and lu_lrmp_nod_id = #{lluNodId})
		AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
		and eer.USR_ID = #{usrId}
		AND eer.EV_CMPL_YN = 'Y'
		GROUP by BNCCM.CRCL_ACH_BS_ID
		ORDER BY max(bnccm.CRCL_ACH_BS_CD),max(bnccm.CRCL_ACH_BS_NM)
		
	</select>
	
	<!-- AI분석 목록 조회 -->
	<select id="selectAllAnAiList" resultType="com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto">
        /* EaLrnRptStu-Mapper.xml- selectAllAnAiList */
        SELECT
       		 -- RSLNR.LRMP_NOD_ID 
			 -- , RSLNR.LRMP_NOD_NM 
			 -- , RSLNR.RCSTN_ORDN 
			 -- , EE.FNL_QST_CNT 
			 -- , EE.EV_ID 
			 -- , EEQ.QTM_ID
			 -- , EE.EVSH_ID
			 -- , EQA.CANS_YN /*정답여부*/
			 -- , 
			 EEQ.TPC_ID /* 토픽ID*/
			 , BKN.KMMP_NOD_NM /*지식맵노트명*/
			 , SUM(CASE WHEN EQA.CANS_YN='Y' THEN 1 ELSE 0 END) AS O_CNT 
			 , SUM(CASE WHEN EQA.CANS_YN='Y' THEN 0 ELSE 1 END) AS X_CNT
		  FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN RSLNR
		  inner join (select distinct b.LU_LRMP_NOD_ID,  b.ev_id from  lms_lrm.ea_ev_ts_rnge b WHERE LU_OPT_TXB_ID = #{optTxbId}) EETR  
		  /*EA_평가시험범위*/ON RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID 
		  JOIN LMS_LRM.EA_EV EE ON EETR.EV_ID = EE.EV_ID 
		  JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID 
		  JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EEQ.EV_ID AND EQA.QTM_ID = EEQ.QTM_ID  AND EQA.USR_ID = #{usrId}
		    INNER JOIN LMS_CMS.BC_KMMP_NOD BKN ON EEQ.TPC_ID=BKN.KMMP_NOD_ID
		 WHERE RSLNR.OPT_TXB_ID = #{optTxbId}
		   AND RSLNR.LLU_NOD_ID =  #{lluNodId} 
		   AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
		    GROUP BY EEQ.TPC_ID
		
	</select>
</mapper>