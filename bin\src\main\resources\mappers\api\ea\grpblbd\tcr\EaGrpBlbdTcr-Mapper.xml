<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.grpblbd.tcr">
	<!-- 모둠 과제 상세 조회 (모둠 구성원 조회) -->
	<select id="selectGrpDetail" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto" resultType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - selectGrpDetail */
		SELECT
			T2.GRP_ID					AS grpId
			, T2.GRP_TEM_ID			AS grpTemId
			, T3.STU_USR_ID			AS stuUsrId
			, T3.USR_NM				AS usrNm
			, T3.GRP_TMGR_YN		AS grpTmgrYn
			, T4.GRP_TEM_NM		AS grpTemNm
		FROM
		(
			SELECT
				T1.GRP_ID
				, T1.GRP_TEM_ID
			FROM
			(
				SELECT
					A.GRP_ID
					, A.GRP_TEM_ID
					, A.ASN_ID
					, A.SMT_DTM
					, A.SMT_CN
					, A.ANNX_ID
					, A.SMT_CMPL_YN
					, A.SCR
					, A.FDBK_CN
					, B.STU_USR_ID
					, B.GRP_TMGR_YN
					, B.DEL_YN
				FROM
					LMS_LRM.EA_GRP_ASN_SMT A
				RIGHT JOIN
					LMS_LRM.EA_GRP_TMBR B
				ON
					A.GRP_ID = B.GRP_ID
				AND
					A.GRP_TEM_ID = B.GRP_TEM_ID
				WHERE
					A.ASN_ID = #{asnId}
				AND
					A.GRP_ID = #{grpId}
				AND
					A.GRP_TEM_ID = #{grpTemId}
				GROUP BY
					A.GRP_Id, A.GRP_TEM_ID
			) T1
		) T2
		LEFT JOIN
			LMS_LRM.EA_GRP_TMBR T3
		ON
			T2.GRP_ID = T3.GRP_ID
		AND
			T2.GRP_TEM_ID = T3.GRP_TEM_ID
		LEFT JOIN
			LMS_LRM.EA_GRP_TEM T4
		ON
			T2.GRP_ID = T4.GRP_ID
		AND
			T2.GRP_TEM_ID = T4.GRP_TEM_ID
		ORDER BY
			T3.GRP_TMGR_YN DESC
	</select>

	<!-- 모둠 게시판 List 조회 -->
	<select id="selectGrpBlbdList" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto" resultType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - selectGrpBlbdList */
		SELECT
			@rownum := @rownum + 1 						AS rowNum
			, T1.COMMENT_CNT 									AS commentCnt
			, T1.BLWR_ID 												AS blwrId
			, T1.BLBD_ID 												AS blbdId
			, T1.BLWR_TITL_NM 										AS blwrTitlNm
			, IFNULL(T1.ANNX_ID, 0) 								AS annxId
			, T1.ANNLST_YN 											AS annlstYn
			, DATE_FORMAT(T1.CRT_DTM,'%Y.%m.%d') 		AS crtDtm
			, T1.CRTR_ID 												AS crtrId
		FROM
		(
			SELECT
				COUNT(CASE C.DEL_YN = 'N' WHEN 1 THEN 0 END) AS COMMENT_CNT
				, A.BLBD_ID
				, A.BLWR_ID
				, A.BLWR_TITL_NM
				, IF((A.ANNX_ID = 0 || A.ANNX_ID = NULL), NULL, A.ANNX_ID) AS ANNX_ID
				, A.ANNLST_YN
				, A.CRT_DTM
				, (SELECT USR_NM FROM LMS_LRM.CM_USR WHERE USR_ID = A.CRTR_ID) AS CRTR_ID
			FROM
				LMS_LRM.EA_GRP_BLBD_BLWR A
			LEFT JOIN
				LMS_LRM.EA_GRP_BLBD B
			ON
				B.BLBD_ID = A.BLBD_ID
			LEFT JOIN
				LMS_LRM.EA_GRP_BLBD_UCWR C
			ON
				C.BLWR_ID = A.BLWR_ID
			AND
				C.BLBD_ID = A.BLBD_ID
			WHERE
				B.GRP_ID = #{grpId}
			AND
				B.GRP_TEM_ID = #{grpTemId}
			AND
				B.ASN_ID = #{asnId}
			AND
				A.DEL_YN = 'N'
			GROUP BY
				A.BLWR_ID
			ORDER BY
				A.ANNLST_YN, A.CRT_DTM ASC
		) T1
		, (SELECT @rownum :=0) AS R
		-- WHERE
		--	T1.BLBD_ID = (SELECT BLBD_ID FROM LMS_LRM.EA_GRP_BLBD WHERE GRP_ID = '1' AND GRP_TEM_ID = '1')
		ORDER BY
			ROWNUM DESC
	</select>

	<!-- 모둠 게시판 상세 조회 -->
	<select id="selectBlbdDetail" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto" resultType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - selectBlbdDetail */
		SELECT
			BLWR_ID											AS blwrId
			, BLBD_ID											AS blbdId
			, BLWR_TITL_NM									AS blwrTitlNm
			, BLWR_CN											AS blwrCn
			, ANNLST_YN										AS annlstYn
			, IFNULL(ANNX_ID, -1) 							AS annxId
			, DATE_FORMAT(CRT_DTM,'%Y.%m.%d') 	AS crtDtm
			, CRTR_ID 											AS crtrId
			, (SELECT USR_NM FROM LMS_LRM.CM_USR WHERE USR_ID = A.CRTR_ID) AS crtrNm
		FROM
			LMS_LRM.EA_GRP_BLBD_BLWR A
		WHERE
			BLWR_ID = #{blwrId}
		AND
			BLBD_ID = #{blbdId}
		AND
			DEL_YN = 'N'
	</select>

	<!-- 모둠 게시판 댓글 조회 -->
	<select id="selectBlbdDetailComment" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto" resultType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - selectBlbdDetailComment */
		SELECT
			IF(T1.SRT_ORDN != 0, T1.URNK_UCWR_ID, T1.UCWR_ID) 	AS tempUcwrId
			, T1.UCWR_ID																AS ucwrId
			, T1.BLWR_ID																AS blwrId
			, T1.BLBD_ID																AS blbdId
			, T1.URNK_UCWR_ID														AS urnkUcwrId
			, T1.SRT_ORDN															AS srtOrdn
			, T1.UCWR_CN																AS ucwrCn
			, T1.CRTR_ID																AS crtrId
			, T1.CRTR_NM																AS crtrNm
			, T1.USR_TP_CD															AS usrTpCd
			, T1.DEL_YN																	AS delYn
			, T1.CRT_DTM																AS crtDtm
		FROM
		(
			SELECT
				UCWR_ID
				, BLWR_ID
				, BLBD_ID
				, URNK_UCWR_ID
				, SRT_ORDN
				, UCWR_CN
				, CRTR_ID
				, (SELECT USR_NM FROM LMS_LRM.CM_USR WHERE USR_ID = A.CRTR_ID) 	AS CRTR_NM
				, (SELECT USR_TP_CD FROM LMS_LRM.CM_USR WHERE USR_ID = A.CRTR_ID) 	AS USR_TP_CD
				, DEL_YN
				, DATE_FORMAT(CRT_DTM,'%Y.%m.%d') AS CRT_DTM
			FROM
				LMS_LRM.EA_GRP_BLBD_UCWR A
			WHERE
				BLWR_ID = #{blwrId}
			AND
				BLBD_ID = #{blbdId}
			ORDER BY
				UCWR_ID, SRT_ORDN, CRT_DTM
		) T1
	</select>

	<!-- 게시판 ID 조회 -->
	<select id="selectBlbdId" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto" resultType="int">
		/* EaGrpBlbdTcr-Mapper.xml - selectBlbdId */
		SELECT
			BLBD_ID
		FROM
			LMS_LRM.EA_GRP_BLBD
		WHERE
			GRP_ID = #{grpId}
		AND
			GRP_TEM_ID = #{grpTemId}
		AND
			ASN_ID = #{asnId}
	</select>

	<!-- 모둠 게시판 글 등록 -->
	<insert id="insertBlbd" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - insertBlbd */
		INSERT INTO LMS_LRM.EA_GRP_BLBD_BLWR (
			BLBD_ID
			, BLWR_TITL_NM
			, BLWR_CN
			, ANNLST_YN
			, ANNX_ID
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DEL_YN
			, DB_ID
		) VALUES (
			#{blbdId}
			, #{blwrTitlNm}
			, #{blwrCn}
			, 'Y'
			, #{annxId}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, 'N'
			, 'LMS_LRM'
		)
	</insert>

	<!-- 모둠 게시판 글 수정 -->
	<update id="updateBlbd" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - updateBlbd */
		UPDATE LMS_LRM.EA_GRP_BLBD_BLWR
		SET
			BLWR_TITL_NM = #{blwrTitlNm}
			, BLWR_CN = #{blwrCn}
			<if test="annxId != null and annxId != 0">
				, ANNX_ID = #{annxId}
			</if>
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
	</update>

	<!-- 모둠 게시판 글 삭제 -->
	<update id="deleteBlbd" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - deleteBlbd */
		UPDATE LMS_LRM.EA_GRP_BLBD_BLWR
		SET
			DEL_YN = 'Y'
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
	</update>

	<!-- 댓글 등록 -->
	<insert id="insertComment" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - insertComment */
		INSERT INTO LMS_LRM.EA_GRP_BLBD_UCWR (
			BLWR_ID
			, BLBD_ID
			, SRT_ORDN
			, UCWR_CN
			, DEL_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		) VALUES (
			#{blwrId}
			, #{blbdId}
			, 0
			, #{ucwrCn}
			, 'N'
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 댓글,답글 수정 -->
	<update id="updateComment" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - updateComment */
		 UPDATE LMS_LRM.EA_GRP_BLBD_UCWR A
		   SET
				 UCWR_CN  = #{ucwrCn}
				,MDFR_ID = #{usrId}
				,MDF_DTM = NOW()
		 WHERE
					BLBD_ID = #{blbdId}
				AND
					BLWR_ID = #{blwrId}
				AND
					UCWR_ID = #{ucwrId}
	</update>

	<!-- 댓글 삭제 -->
	<update id="deleteComment" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - deleteComment */
		UPDATE LMS_LRM.EA_GRP_BLBD_UCWR
		SET
			DEL_YN = 'Y'
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
		AND
			UCWR_ID = #{ucwrId}
	</update>

	<!-- 답글 등록 -->
	<insert id="insertReply" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - insertReply */
		INSERT INTO LMS_LRM.EA_GRP_BLBD_UCWR (
			BLWR_ID
			, BLBD_ID
			, URNK_UCWR_ID
			, SRT_ORDN
		 	, UCWR_CN
		  	, DEL_YN
		  	, CRTR_ID
		 	, CRT_DTM
		  	, MDFR_ID
		  	, MDF_DTM
		  	, DB_ID
		) VALUES (
			#{blwrId}
			, #{blbdId}
			, #{ucwrId}
			, (
				SELECT
					IFNULL(MAX(A.SRT_ORDN), 0) + 1
			   	FROM
					LMS_LRM.EA_GRP_BLBD_UCWR A
			   	WHERE
			   		A.BLWR_ID = #{blwrId}
				AND
					A.BLBD_ID = #{blbdId}
				AND
					A.URNK_UCWR_ID = #{ucwrId}
			   )
			 , #{ucwrCn}
			 , 'N'
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , 'LMS_LRM'
		)
	</insert>

	<!-- 답글 삭제 -->
	<update id="deleteReply" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - deleteReply */
		UPDATE LMS_LRM.EA_GRP_BLBD_UCWR
		SET
			DEL_YN = 'Y'
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
		AND
			UCWR_ID = #{ucwrId}
		AND
			URNK_UCWR_ID = #{urnkUcwrId}
	</update>

	<!-- 첨부 파일 조회 -->
	<select id="selectAsnFile" parameterType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto" resultType="com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto">
		/* EaGrpBlbdTcr-Mapper.xml - selectAsnFile */
		SELECT
			   CAF.ANNX_FLE_ID					AS annxFileId
			  ,CAF.ANNX_ID						AS annxId
			  ,CAF.SRT_ORDN						AS srtOrdn
			  ,CAF.ANNX_FLE_NM				AS annxFileNm
			  ,CAF.ANNX_FLE_ORGL_NM		AS annxFileOrglNm
			  ,CAF.ANNX_FLE_FEXT_NM		AS annxFileFextNm
			  ,CAF.ANNX_FLE_SZE				AS annxFileSize
			  ,CAF.ANNX_FLE_PTH_NM			AS annxFilePathNm
			  ,CAF.USE_YN							AS useYn
		FROM
			LMS_LRM.CM_ANNX_FLE CAF
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>
</mapper>