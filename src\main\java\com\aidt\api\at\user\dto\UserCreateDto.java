package com.aidt.api.at.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "TOKEN, USR 생성 DTO")
public class UserCreateDto {

	/*
	 * 공통 필요
	 */
	@Schema(description = "KERIS사용자ID")
	private String kerisUsrId;

	@Schema(description = "학급ID")
	private String claId;

	/*
	 * token 필요
	 */
	private String lectureCode;
	private String optTxbId;

	/*
	 * usr 필요
	 */
	@Schema(description = "사용자유형코드")
	private String usrTpCd;

	@Schema(description = "KERIS약관동의여부")
	private String kerisTermAgrYn;

	@Schema(description = "KERIS약관동의일자")
	private String kerisTermAgrDt;
}
