<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.inf.stu">

	<!-- 2024-06-17 조회시마다 알림 전체 읽음 처리 로직 추가 -->
	<update id="updateInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfStu-Mapper.xml - updateInfList */
		UPDATE LMS_LRM.CM_INFM
		SET
			COFM_YN = 'Y'
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE INFM_OBJ_USR_ID = #{usrId}
		AND (COFM_YN = 'N' OR COFM_YN IS NULL)
	</update>


	<!-- 알림 조회 -->
	<select id="selectInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto" resultType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfStu-Mapper.xml - selectInfList */
		SELECT
			A.INFM_ID as INFM_ID,
			A.OPT_TXB_ID as OPT_TXB_ID,
			A.TCR_USR_ID as TCR_USR_ID,
			A.INFM_MSG_ID as INFM_MSG_ID,
			A.INFM_OBJ_USR_ID as INFM_OBJ_USR_ID,
			A.COFM_YN as COFM_YN,
			A.CRTR_ID as CRTR_ID,
			DATE_FORMAT(A.CRT_DTM, '%m. %d. %p %h:%i') as CRT_DTM,
			A.MDFR_ID as MDFR_ID,
			A.MDF_DTM as MDF_DTM,
			A.DB_ID as DB_ID,
			B.INFM_TP_CD as INFM_TP_CD,
			B.INFM_CL_CD as INFM_CL_CD,
			B.INFM_DTL_CL_CD as INFM_DTL_CL_CD,
			B.INFM_NM as INFM_NM,
			B.INFM_CN as INFM_CN,
			A.INFM_MV_CN
		FROM
			LMS_LRM.CM_INFM A
		INNER JOIN
			LMS_LRM.CM_INFM_MSG B
		ON A.INFM_MSG_ID = B.INFM_MSG_ID
		<where>
			A.INFM_OBJ_USR_ID = #{usrId}
			<if test='infmTpCd != null and !infmTpCd.equals("")'>
				AND B.INFM_TP_CD = #{infmTpCd}
			</if>
		</where>
		ORDER BY A.CRT_DTM DESC
		<if test = 'pageSize != null and !"".equals(pageSize) and pageNo != null and !"".equals(pageNo)'>
			LIMIT #{pageSize}
			OFFSET #{pageOffset}
		</if>
	</select>

	<delete id="deleteInfMsg" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfStu-Mapper.xml - deleteInfMsg */
		DELETE FROM LMS_LRM.CM_INFM_MSG 
		WHERE INFM_MSG_ID IN (
								SELECT INFM_MSG_ID
								 FROM LMS_LRM.CM_INFM
								WHERE
									INFM_OBJ_USR_ID = #{usrId}
									AND DB_ID = #{dbId}
								<if test= 'infmId != 0'>
									AND INFM_ID = #{infmId}
								</if>
							)
	</delete>

	<delete id="deleteInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfStu-Mapper.xml - deleteInfList */
		DELETE FROM LMS_LRM.CM_INFM
		WHERE
			INFM_OBJ_USR_ID = #{usrId}
			AND DB_ID = #{dbId}
		<if test= 'infmId != 0'>
			AND INFM_ID = #{infmId}
		</if>
	</delete>

</mapper>