package com.aidt.api.bc.cm;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.cm.dto.BcCmCdDto;
import com.aidt.api.bc.cm.dto.BcCmWebDispDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-11-25 10:21:00
 * @create 2024-11-25 10:21:00
 * @desc 공통 웹전시 Controller
 */

@Slf4j
@Tag(name="[bc] 웹전시", description="공통 웹전시")
@RestController
@RequestMapping("/api/v1/bc/cm/webdisp")
public class BcCmWebDispController {

	@Autowired
	private BcCmWebDispService webDispService;
	
	@Autowired
	private JwtProvider jwtProvider;

    /**
     * 웹전시 페르소나 계정 복사 (부하테스트 관련 초기 데이터 생성용)
     *
     * @param BcCmCdDto
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="웹전시 페르소나 계정 복사", description="웹전시 페르소나 계정 복사")
    @PostMapping("/copyStuDataLoadTest")
    public ResponseDto<Map<String,Object>> copyStuDataLoadTest(@Valid @RequestBody BcCmWebDispDto reqDto) {
        Map<String,Object> rtnMap = new HashMap<String,Object>();

        int istCnt = webDispService.callStuDataCpLoadTest(reqDto);
        rtnMap.put("istCnt", istCnt);

        return Response.ok(rtnMap);
    }


    /**
     * 웹전시 페르소나 계정 복사
     *
     * @param BcCmCdDto
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="웹전시 페르소나 계정 복사", description="웹전시 페르소나 계정 복사")
    @PostMapping("/copyStuData")
    public ResponseDto<Map<String,Object>> copyStuData(@Valid @RequestBody BcCmWebDispDto reqDto) {
    	Map<String,Object> rtnMap = new HashMap<String,Object>();
    	
    	int istCnt = webDispService.callStuDataCp(reqDto);
    	rtnMap.put("istCnt", istCnt);
    	
        return Response.ok(rtnMap);
    }
    
    /**
     * 웹전시 페르소나 계정 복사
     *
     * @param BcCmCdDto
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="웹전시 페르소나 계정 복사", description="웹전시 페르소나 계정 복사")
    @PostMapping("/copyPersonaData")
    public ResponseDto<Map<String,String>> copyPersonaData(@Valid @RequestBody BcCmWebDispDto reqDto) {
    	Map<String,String> rtnMap = new HashMap<String,String>();
    	
    	webDispService.copyPersonaData(reqDto);
    	
        return Response.ok(rtnMap);
    }
    
    /**
     * 웹전시 페르소나 계정 요청데이터 insert
     *
     * @param BcCmCdDto
     * @return ResponseList<Map<String,String>>
     */
    @Operation(summary="웹전시 페르소나 계정 요청데이터 insert", description="웹전시 페르소나 계정 요청데이터 insert")
    @PostMapping("/insertCpReq")
    public ResponseDto<Map<String,Object>> insertCpReq(@Valid @RequestBody BcCmWebDispDto reqDto) {
    	Map<String,Object> rtnMap = new HashMap<String,Object>();
    	
    	int istCnt = webDispService.insertCpReq(reqDto);
    	rtnMap.put("istCnt", istCnt);
    	
        return Response.ok(rtnMap);
    } 
}
