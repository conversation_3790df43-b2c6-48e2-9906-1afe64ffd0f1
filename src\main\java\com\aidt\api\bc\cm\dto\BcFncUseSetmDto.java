package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "메뉴 공통")
public class BcFncUseSetmDto {

	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="마이홈포인트사용여부")
	private String myhmPntUseYn;
	
	@Parameter(name="칭찬도장사용여부")
	private String prasStmpUseYn;
	
	@Parameter(name="대화사용여부")
	private String dilgUseYn;
	
	@Parameter(name="학급게시판첨삭허용여부")
	private String claBlbdWrtPmsnYn;
	
	@Parameter(name="학급게시판댓글허용여부")
	private String claBlbdUcwrPmsnYn;
	
	/** 내가만든평가사용여부 */
    @Parameter(name="내가만든평가사용여부")
    private String diyEvUseYn;
    
    /** 학습 리포트 사용 여부 */
    @Parameter(name="학습 리포트 사용 여부")
    private String lrnRptUseYn;
}