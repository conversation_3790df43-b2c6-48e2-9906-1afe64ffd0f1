package com.aidt.api.al.pl.cm.rcm.strategy;

import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvLoader;
import com.aidt.api.common.enums.SubjectCode;

public interface ProfileStrategy {

	boolean isSupported(SubjectCode subjectCode);

	void completeAiEvaluationTestRange(EaAiEvLoader eaAiEvLoader);

	//todo: 각 전략 패턴으로 구현
	void updateAiLearningLevel(EaAiEvLoader eaAiEvLoader);

	void updateTopicProficiency(EaAiEvLoader eaAiEvLoader);

	void updateTopicLearningOrder(EaAiEvLoader eaAiEvLoader);

	void saveLearningProgressProfile(EaAiEvLoader eaAiEvLoader);

	String getPointCode(EaAiEvLoader eaAiEvLoader);

}