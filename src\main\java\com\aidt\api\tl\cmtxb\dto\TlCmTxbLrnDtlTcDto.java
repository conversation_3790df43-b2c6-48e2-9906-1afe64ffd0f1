package com.aidt.api.tl.cmtxb.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 10:43:39
 * @modify date 2024-04-02 10:43:39
 * @desc [학습상세현황 차시 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbLrnDtlTcDto {
        /** 차시ID*/
        @Parameter(name="차시ID")
        private String lrmpNodId;
        
        /** 차시명*/
        @Parameter(name="차시명")
        private String lrmpNodNm;

        /** 재구성 순서 */
        @Parameter(name="재구성 순서")
        private int rcstnOrdn;

        /** 잠금여부 */
        @Parameter(name="잠금여부")
        private String lcknYn;

        /** 사용여부 */
        @Parameter(name="사용여부")
        private String useYn;

        /** 활동 완료 수*/
        @Parameter(name="활동 완료 수")
        private int clCnt;

        /** 진행률*/
        @Parameter(name="진행률")
        private double pgrsRt;

        /** 활동 리스트*/
        @Parameter(name="활동 리스트")
        private List<TlCmTxbLrnDtlAtvDto> atvList;
}
