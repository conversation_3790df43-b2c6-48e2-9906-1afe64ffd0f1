package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "CM_KERIS_접속_오류")
public class BcCmKerisConnErr {

	@Schema(description = "오류ID")
	private Long errId;
	@Schema(description = "요청LogID")
	private Long reqLogId;
	@Schema(description = "학습사용자ID")
	private String lrnUsrId;
	@Schema(description = "운영교과서ID")
	private String optTxbId;
	@Schema(description = "접속URL")
	private String connUrl;
	@Schema(description = "파트너ID")
	private String prnrId;
	@Schema(description = "API버전")
	private String apiVer;
	@Schema(description = "BODY내용")
	private String bodyCn;
	@Schema(description = "BODY수정내용")
	private String bodyMdfCn;
	@Schema(description = "결과코드")
	private String rsCd;
	@Schema(description = "결과메시지")
	private String rsMsg;
	@Schema(description = "오류코드")
	private String errCd;
	@Schema(description = "오류전송대상ID")
	private String errTrmsId;
	@Schema(description = "오류전송대상상태코드")
	private String errTrmsStCd;
	@Schema(description = "등록일시")
	private String regDtm;
}
