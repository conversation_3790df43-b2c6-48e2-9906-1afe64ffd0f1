package com.aidt.api.tl.oneclksetm.stu;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm;
import com.aidt.api.tl.oneclksetm.tcr.TlOneClkSetmTcrController;
import com.aidt.api.tl.oneclksetm.tcr.TlOneClkSetmTcrService;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 16:07:44
 * @modify date 2024-02-15 16:07:44
 * @desc [TlOneClkSetmTcr Controller 원클릭학습설정]
 */
@Slf4j
@Tag(name="[tl] 원클릭학습설정[TlOneClkSetmStu]", description="원클릭학습설정")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/stu/oneclksetm")
public class TlOneClkSetmStuController {
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlOneClkSetmTcrService tlOneClkSetmTcrService;
    
    /**
     * 원클릭학습설정 외부활동설정 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmFncUseDto>>
     */
    @Operation(summary="원클릭학습설정 외부활동설정 조회", description="원클릭학습설정 외부활동설정 조회")
    @PostMapping(value = "/selectExtAtvSetm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLrnwExtAtvSetm> selectExtAtvSetm() {
        log.debug("Entrance selectExtAtvSetm");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectExtAtvSetm(srhDto));
    }
    
    /**
     * 원클릭학습설정 자료 게시물 상세 조회
     * 
     * @param mtrlDto
     * @return ResponseDto<List<TlOneClkSetmRegMtrlDto>>
     */
    @Operation(summary="원클릭학습설정 자료 게시물 상세 조회", description="원클릭학습설정 자료 게시물 상세 조회")
    @PostMapping(value = "/selectBlwrInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlOneClkSetmRegMtrlDto> selectBlwrInfo(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
        log.debug("Entrance selectBlwrInfo");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        mtrlDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectBlwrInfo(mtrlDto));
    }

}
