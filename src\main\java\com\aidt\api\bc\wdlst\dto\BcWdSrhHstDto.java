package com.aidt.api.bc.wdlst.dto;


import com.aidt.common.Paging.PagingRequestDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 단어장 dto
 */

@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcWdSrhHstDto extends PagingRequestDto{

	@Parameter(name="단어검색이력ID")
	private int wdSrhHsId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="영문단어ID")
	private int enWdId;

	@Parameter(name="영문단어명")
	private String wdNm;

	@Parameter(name="영문단어의미내용")
	private String wdMeanCn;

	@Parameter(name="품사구분코드")
	private String wdclDvCd;

	@Parameter(name="영문단어설명내용")
	private String engWdExplCn;
	
	@Parameter(name="검색단어")
	private String scWord;
	
	@Parameter(name="교과서")
    private String txbId;

	// 생성자ID
	private String crtrId;

	// 생성일시
	private String crtDtm;

	// 수정자ID
	private String mdfrId;

	// 수정일시
	private String mdfDtm;

	// 데이터베이스ID
	private String dbId;



}
