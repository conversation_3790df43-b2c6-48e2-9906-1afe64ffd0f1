/**
 * 
 */
package com.aidt.api.at.dto.dummy;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StuDto {
	    private String usrId;
	    private String usrName;
	    private String usrType;
	    private Long numb;
	    
		public StuDto(String authCode, String txbId, String usrName, Long numb) {
			this.usrId = authCode + txbId + numb;
			this.usrName = usrName;
			this.usrType="S";
			this.numb = numb + 1;
		}
		
		public StuDto(String authCode, String txbId, String usrName) {
			this.usrId = authCode + txbId + "t";
			this.usrName = usrName;
			this.usrType="T";
			
		}
	    @Override
	    public String toString() {
	        return "usrId: " + usrId + ", usrName: " + usrName + ", numb: " + numb ;
	    }
}


