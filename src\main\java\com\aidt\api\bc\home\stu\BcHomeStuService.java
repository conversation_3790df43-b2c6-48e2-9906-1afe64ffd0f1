package com.aidt.api.bc.home.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.home.stu.dto.BcHomeStuDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:51:19
 * @modify 2024-01-05 17:51:19
 * @desc 학생_홈 Service
 */

@Service
public class BcHomeStuService {

    private final String MAPPER_NAMESPACE = "api.bc.home.stu.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 홈 조회 서비스
     *
     * @param BcHomeStuDto
     * @return List<BcHomeStuDto>
     */
    public List<BcHomeStuDto> selectHomeList(String userId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectHomeList", userId);
    }
}
