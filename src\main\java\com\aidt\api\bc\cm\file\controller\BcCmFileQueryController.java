package com.aidt.api.bc.cm.file.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.cm.file.dto.BcFileResDto;
import com.aidt.api.bc.cm.file.service.BcCmFileQueryService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "[bc] 공통[BcCm] New", description = "공통")
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@RestController
@RequestMapping("/api/v1/bc/cm/common/file")
public class BcCmFileQueryController {

	private final BcCmFileQueryService bcCmFileQueryService;

	@Operation(summary = "[bc] 파일 조회 API", description = "##파일 조회 API")
	@GetMapping(value = "/{file-id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<BcFileResDto> getFile(@PathVariable(value = "file-id") Long annxFleId) {
		var file = bcCmFileQueryService.getFile(annxFleId);
		return Response.ok(file);
	}

}
