package com.aidt.api.tl.cmtxb.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-15 10:14:58
 * @modify date 2024-03-15 10:14:58
 * @desc [TlCmTxbPrevNextTc 이전차시 다음차시정보 dto]
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbPrevNextTcDto {

    /** 대단원No사용여부 */
    @Parameter(name="대단원No사용여부")
    private String lluNodNoUseYn;

    /** 대단원No */
    @Parameter(name="대단원No")
    private String lluNodNo;

    /** 대단원ID */
    @Parameter(name="대단원ID")
    private String lluNodId;

    /** 대단원명 */
    @Parameter(name="대단원명")
    private String lluNodNm;

    /** 차시No사용여부 */
    @Parameter(name="차시No사용여부")
    private String tcNodNoUseYn;

    /** 차시No */
    @Parameter(name="차시No")
    private String tcNodNo;

    /** 차시ID */
    @Parameter(name="차시ID")
    private String tcNodId;

    /** 차시명 */
    @Parameter(name="차시명")
    private String tcNodNm;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 이번차시설정여부 */
    @Parameter(name="이번차시설정여부")
    private String tcChkYn;

    /** 익힘콘텐츠존재여부 */
    @Parameter(name="익힘콘텐츠존재여부")
    private String wkbCtnYn;

    /** 형성평가존재여부 */
    @Parameter(name="형성평가존재여부")
    private String evCtnYn;
    
    /** 평가ID */
    @Parameter(name="평가ID")
    private String evId;

    /** 이전대단원No */
    @Parameter(name="이전대단원No")
    private String bfLluNodNo;

    /** 이전대단원ID */
    @Parameter(name="이전대단원ID")
    private String bfLluNodId;

    /** 이전대단원명 */
    @Parameter(name="이전대단원명")
    private String bfLluNodNm;

    /** 이전차시ID */
    @Parameter(name="이전차시ID")
    private String bfTcNodId;

    /** 이전차시명 */
    @Parameter(name="이전차시명")
    private String bfTcNodNm;

    /** 이전평가ID */
    @Parameter(name="이전평가ID")
    private String bfEvId;

    /** 다음잠금여부 */
    @Parameter(name="다음잠금여부")
    private String bfLcknYn;

    /** 다음대단원No */
    @Parameter(name="다음대단원No")
    private String afLluNodNo;

    /** 다음대단원ID */
    @Parameter(name="다음대단원ID")
    private String afLluNodId;

    /** 다음대단원명 */
    @Parameter(name="다음대단원명")
    private String afLluNodNm;

    /** 다음차시ID */
    @Parameter(name="다음차시ID")
    private String afTcNodId;

    /** 다음차시명 */
    @Parameter(name="다음차시명")
    private String afTcNodNm;

    /** 다음평가ID */
    @Parameter(name="다음평가ID")
    private String afEvId;

    /** 다음잠금여부 */
    @Parameter(name="다음잠금여부")
    private String afLcknYn;
    
}
