package com.aidt.api.util;

import java.time.Duration;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.DefaultUriBuilderFactory;

import io.netty.channel.ChannelOption;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
@Component
public class WebClientUtil {
	/*
	 * AIDT-BASE WebFluxUtil retry를 제외한 나머지 옵션 그대로 복사
	 */
	private final DefaultUriBuilderFactory defaultUriBuilderFactory = new DefaultUriBuilderFactory();

	private final HttpClient httpClient = HttpClient.create().option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000); // 30초

	private final HttpClient httpClientTimeout = HttpClient.create()
		.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000) //5초 (연결 타임아웃)
		.responseTimeout(Duration.ofSeconds(10)); // 10초 (응답 타임아웃)

	private WebClient webClient() {
		defaultUriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.VALUES_ONLY); // get Uri 인코딩 설정
		return WebClient.builder().uriBuilderFactory(defaultUriBuilderFactory)
				.codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(2 * 1024 * 1024))
				.clientConnector(new ReactorClientHttpConnector(httpClient)).build();
	}

	private WebClient webClientTimeout() {
		defaultUriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.VALUES_ONLY); // get Uri 인코딩 설정
		return WebClient.builder().uriBuilderFactory(defaultUriBuilderFactory)
			.codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(2 * 1024 * 1024))
			.clientConnector(new ReactorClientHttpConnector(httpClientTimeout)).build();
	}

	/**
	 * 비동기 요청
	 * 
	 * @param url
	 * @param jsonParam
	 */
	public void callAsync(String url, String jsonParam) {
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Content-Type", "application/json");

		log.debug("apiUrl >>> " + url);
		log.debug("jsonParam >>> " + jsonParam);
		
		/* Mono<String> value = */
		this.webClient().method(HttpMethod.POST).uri(url)
				.headers(headers -> headers.addAll(httpHeaders == null ? new HttpHeaders() : httpHeaders))
				.bodyValue(jsonParam).exchangeToMono(response -> {
					// 200
					if (response.statusCode().equals(HttpStatus.OK)) {
						return response.bodyToMono(String.class);
					} else {
						return Mono.just("{\"code\":\"" + response.statusCode() + "\",\"message\":\""
								+ response.statusCode().name() + "\"}");
					}
				});

		// log 확인용 (주석해제 시 동기화...)
		// String result = value.block();
		// log.debug("result >>> " + result);

	}
	
	/**
	 * AIDT-BASE WebFluxUtil 에서 retry 제외시킨 method
	 * 
	 * @param <T>
	 * @param <V>
	 * @param url
	 * @param requestHeaders
	 * @param requestDto
	 * @param responseClass
	 * @return
	 */
	public <T, V> T post(String url, HttpHeaders requestHeaders, V requestDto, Class<T> responseClass) {
        return this.webClient()
                .method(HttpMethod.POST)
                .uri(url)
                .headers(headers -> headers.addAll(requestHeaders == null ? new HttpHeaders() : requestHeaders))
                .bodyValue(requestDto)
                .retrieve()
                .onStatus(
                		httpStatus -> httpStatus != HttpStatus.OK,
                		clientResponse -> {
                			return clientResponse.createException().map(it -> new RuntimeException("code : " + clientResponse.statusCode()));
                			})
                .bodyToMono(responseClass)
                .onErrorMap(throwable -> {
                    return new RuntimeException(throwable.getMessage() + "\n" + throwable.getCause() + "\n" + throwable.getLocalizedMessage());
                	})
                .block();
    }

	public <T, V> T postWithError(String url, HttpHeaders requestHeaders, V requestDto, Class<T> responseClass) {
		return this.webClientTimeout()
				.method(HttpMethod.POST)
				.uri(url)
				.headers(headers -> headers.addAll(requestHeaders == null ? new HttpHeaders() : requestHeaders))
				.bodyValue(requestDto)
				.retrieve()
				.bodyToMono(responseClass)
				.onErrorMap(WebClientResponseException.class, e -> {
					log.error("WebClient 실패 - 상태: {}, 본문: {}", e.getStatusCode(), e.getResponseBodyAsString());
					return e;
				})
				.block(Duration.ofSeconds(20)); // 20초 (블록 타임아웃)
	}

}
