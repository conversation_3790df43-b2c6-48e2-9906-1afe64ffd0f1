package com.aidt.api.bc.inf.tcr;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.api.bc.inf.common.InfPathUtil;
import com.aidt.api.bc.inf.dto.BcInfDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:30
 * @modify 2024-01-05 17:52:30
 * @desc 알림 Service
 */

@Service
public class BcInfTcrService {

    private final String MAPPER_NAMESPACE = "api.bc.inf.tcr.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 알림 목록 조회 서비스
     *
     * @param BcInfDto
     * @return List<BcInfDto>
     */
    public List<BcInfDto> selectInfList(BcInfDto bcInfDto) {    	
        // 2024-06-17 조회시마다 알림 전체 읽음 처리 로직 추가
    	commonDao.update(MAPPER_NAMESPACE + "updateInfList", bcInfDto);
    	List<BcInfDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectInfList", bcInfDto);
    	for(BcInfDto d : list) {    		
    		String infmMvCn = InfPathUtil.mapFilePathToEnum(d.getInfmMvCn(), "R");
    		d.setInfmMvCn(infmMvCn);       		
    	}    
        return list;
    }

    /**
     * 알림 등록 서비스
     *
     * @param BcInfDto
     * @return Integer
     */
    public int insertInfList(BcInfDto bcInfDto) {
    	int result = 0;
    	if(bcInfDto == null || bcInfDto.getStuList() == null || bcInfDto.getStuList().size() == 0) {    		
    		return result;
    	}
    	commonDao.insert(MAPPER_NAMESPACE + "insertInfMsgList", bcInfDto);

    	for(BcStuListDto stu : bcInfDto.getStuList()) {
    		bcInfDto.setInfmObjUsrId(stu.getUsrId());
    		String json = InfPathUtil.mapFilePathToEnum(stu.getInfmMvCn(), "C");
    		bcInfDto.setInfmMvCn(json);
    		result += commonDao.insert(MAPPER_NAMESPACE + "insertInfList", bcInfDto);
    	}
    	return result;
    }

    /**
     * 알림 삭제 서비스
     *
     * @param BcInfDto
     * @return Integer
     */
    public int deleteInfList(BcInfDto bcInfDto) {
    	int result = 0;
    	result += commonDao.delete(MAPPER_NAMESPACE + "deleteInfMsg", bcInfDto);
    	result += commonDao.delete(MAPPER_NAMESPACE + "deleteInfList", bcInfDto);
    	return result;
    }
    
}
