package com.aidt.api.bc.tnte.dto;

import java.util.List;

import com.aidt.common.Paging.PagingRequestDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 노트
 */

@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcNewTnteDto extends PagingRequestDto{

	@Parameter(name="노트구분코드")
	private String tnteDvCd;
	
	@Parameter(name="노트구분코드명")
	private String tnteDvCdNm;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="단원노드ID")
	private String luNodId;
	
	@Parameter(name="단원명")
	private String lluNm;

	@Parameter(name="차시노드ID")
	private String tcNodId;
	
	@Parameter(name="차시명")
	private String tcNm;

	@Parameter(name="학습사용자ID")
	private String lrnUsrId;

	@Parameter(name="학습유형코드")
	private String lrnTpCd;
	
	@Parameter(name="학습유형코드명")
	private String lrnTpCdNm;
	
	@Parameter(name="노트상세학습활동목록")
	private List<BcNewTnteLrnAtvDto> lrnAtvList;
	
	@Parameter(name="등록일")
	private String crtDtm;
	
	@Parameter(name="조회정렬조건")
	private String srhSrt;

	@Parameter(name="정렬정보")
	private String srtInfo;

	@Parameter(name="저장여부(노트구분selectBox)")
	private String saveYn;
	
	@Parameter(name="평가ID")
	private Integer evId;
	
	@Parameter(name="토픽명")
	private String tpcNm;
	
	private int totalCnt;
	
	@Parameter(name="응시회차")
	private Integer txmPn;
	
	@Parameter(name="지식맵ID")
	private List<String> KmmpNodIdList;
	
	public void setKmmpNodIdList(List<String> KmmpNodIdList) {
	    this.KmmpNodIdList = KmmpNodIdList;
	}
	
	

}
