<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.evcom">

	<select id="selectEvRs" resultType="com.aidt.api.ea.evcom.dto.EaEvResult">
		SELECT
			E.EV_ID
			 , E.EV_DV_CD
			 , E.EV_DTL_DV_CD
			 , IFNULL(E.RTXM_PMSN_YN, 'N') AS RTXM_PMSN_YN -- 재응시 허용여부
			 , IFNULL(ER.EV_CMPL_YN, 'N')  	AS EV_CMPL_YN   -- 평가 완료 여부
			 , IFNULL(ERR.EV_CMPL_YN, 'N')	AS EV_RCMPL_YN  -- 재응시 완료 여부
			 , IFNULL(ERR.RTXM_PN, 0) 	  	AS TXM_PN 		-- 응시회차(신규 : 0, 재응시 : 1 부터)
			 , IFNULL(ESNR.EV_CMPL_YN, 'X')					AS sppNtnCmplYn -- 보충심화 완료여부 (X는 보충심화 없다는 뜻)
			 , IFNULL(ESNR.TXM_STR_YN, 'X')					AS sppNtnStrYn  -- 보충심화 시작여부
			 , BT.SCHL_GRD_CD				AS schlGrdCd
			 , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
					WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
					ELSE ''
			END AS sbjCd
			 , IFNULL((SELECT U.USR_TP_CD FROM LMS_LRM.CM_USR U WHERE U.USR_ID = #{usrId}), 'X') AS usrTpCd
		FROM LMS_LRM.EA_EV E
				 JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
				 JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
				 LEFT JOIN LMS_LRM.EA_EV_RS ER ON E.EV_ID = ER.EV_ID AND ER.USR_ID = #{usrId}
				 LEFT JOIN (
			SELECT
				ERR.EV_ID, ERR.USR_ID, ERR.EV_CMPL_YN, ERR.RTXM_PN
			FROM LMS_LRM.EA_EV_RS_RTXM ERR
			WHERE ERR.EV_ID = #{evId} AND ERR.USR_ID = #{usrId}
			ORDER BY ERR.RTXM_PN DESC
				LIMIT 1
		) ERR ON ERR.EV_ID = ER.EV_ID AND ERR.USR_ID = ER.USR_ID
				 LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS ESNR ON ESNR.EV_ID = ER.EV_ID AND ESNR.USR_ID = ER.USR_ID AND ESNR.TXM_PN = IFNULL(ERR.RTXM_PN, 0)
		WHERE E.EV_ID = #{evId}



		/* 평가 공통 - 박원희 - EaEv-Mapper.xml - 평가 결과 등록 전 재응시평가 확인 - selectEvRs */

	</select>

	<!--  평가 결과 update -->
	<insert id="upsertEaEvRs" parameterType="com.aidt.api.ea.evcom.dto.EaEvResult">
		INSERT INTO LMS_LRM.EA_EV_RS (
		EV_ID, USR_ID, TXM_STR_YN, EV_CMPL_YN
		<if test='"Y".equals(evCmplYn)'>
			, SMT_DTM
		</if>
		, EV_TM_SCNT, CANS_CNT, CANS_RT
		, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT
		E.EV_ID
		, #{usrId}
		, #{txmStrYn}
		, #{evCmplYn}
		<if test='"Y".equals(evCmplYn)'>
			, now()
		</if>
		, E.XPL_TM_SCNT_SUM
		, E.CANS_CNT_SUM
		, E.CANS_RT_SUM
		, #{usrId}
		, now()
		, #{usrId}
		, now()
		, #{dbId}
		FROM (
		SELECT
		E.EV_ID
		, SUM(EQA.XPL_TM_SCNT) AS XPL_TM_SCNT_SUM
		, SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT_SUM
		, ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 2) AS CANS_RT_SUM
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = #{usrId}
		WHERE E.EV_ID = #{evId}
		GROUP BY E.EV_ID
		) E
		ON DUPLICATE KEY UPDATE
		<if test='"Y".equals(evCmplYn)'>
			SMT_DTM    = NOW(),
		</if>
		EV_TM_SCNT = VALUES(EV_TM_SCNT),
		CANS_CNT   = VALUES(CANS_CNT),
		CANS_RT    = VALUES(CANS_RT),
		TXM_STR_YN = #{txmStrYn},
		EV_CMPL_YN = #{evCmplYn},
		MDFR_ID    = #{usrId},
		MDF_DTM    = NOW()

		/* 평가 공통 - EaEv-Mapper.xml - 평가 결과 update */

	</insert>

	<insert id="upsertReEaEvRs" parameterType="com.aidt.api.ea.evcom.dto.EaEvResult">

		INSERT INTO LMS_LRM.EA_EV_RS_RTXM (
		EV_ID, USR_ID, RTXM_PN, TXM_STR_YN, EV_CMPL_YN,
		<if test='"Y".equals(evCmplYn)'>
			SMT_DTM,
		</if>
		EV_TM_SCNT, CANS_CNT, CANS_RT,
		CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT
		E.EV_ID
		, #{usrId}
		, #{txmPn}
		, #{txmStrYn}
		, #{evCmplYn}
		<if test='"Y".equals(evCmplYn)'>
			, now()
		</if>
		, E.XPL_TM_SCNT_SUM
		, E.CANS_CNT_SUM
		, E.CANS_RT_SUM
		, #{usrId}
		, now()
		, #{usrId}
		, now()
		, #{dbId}
		FROM (
		SELECT
		E.EV_ID
		, SUM(EQA.XPL_TM_SCNT) AS XPL_TM_SCNT_SUM
		, SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT_SUM
		, ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 2) AS CANS_RT_SUM
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = #{usrId} AND EQA.RTXM_PN = #{txmPn}
		WHERE E.EV_ID = #{evId}
		GROUP BY E.EV_ID
		) E
		ON DUPLICATE KEY UPDATE
		<if test='"Y".equals(evCmplYn)'>
			SMT_DTM    = NOW(),
		</if>
		EV_TM_SCNT = VALUES(EV_TM_SCNT),
		CANS_CNT   = VALUES(CANS_CNT),
		CANS_RT    = VALUES(CANS_RT),
		TXM_STR_YN = #{txmStrYn},
		EV_CMPL_YN = #{evCmplYn},
		MDFR_ID    = #{usrId},
		MDF_DTM    = NOW()

		/* 평가 공통 - EaEv-Mapper.xml - 재응시 평가 결과 update */

	</insert>

	<insert id="upsertEaEvAnswerNote" parameterType="com.aidt.api.ea.evcom.dto.EaEvAnswerNote">
		INSERT INTO LMS_LRM.EA_EV_QTM_ANW (
		   EV_ID,
		   QTM_ID,
		   USR_ID,
		   ANNX_FLE_ID,
		   CRTR_ID,
		   CRT_DTM,
		   MDFR_ID,
		   MDF_DTM,
		   DB_ID
		)
		VALUES (
			#{evId},
			#{qtmId},
			#{usrId},
			#{annxFleId},
			#{usrId},
			NOW(),
			#{usrId},
			NOW(),
			#{dbId}
		) ON DUPLICATE KEY UPDATE
			ANNX_FLE_ID = VALUES (ANNX_FLE_ID)
			, MDFR_ID = VALUES (MDFR_ID)
			, MDF_DTM = VALUES (MDF_DTM)
	</insert>

	<insert id="upsertReEaEvAnswerNote" parameterType="com.aidt.api.ea.evcom.dto.EaEvAnswerNote">
		INSERT INTO LMS_LRM.EA_EV_QTM_ANW_RTXM (
			EV_ID,
			QTM_ID,
			USR_ID,
		    RTXM_PN,
			ANNX_FLE_ID,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		VALUES (
		   #{evId},
		   #{qtmId},
		   #{usrId},
		   #{txmPn},
		   #{annxFleId},
		   #{usrId},
		   NOW(),
		   #{usrId},
		   NOW(),
		   #{dbId}
			   ) ON DUPLICATE KEY UPDATE
			ANNX_FLE_ID = VALUES (ANNX_FLE_ID)
				, MDFR_ID = VALUES (MDFR_ID)
				, MDF_DTM = VALUES (MDF_DTM)
	</insert>

	<insert id="upsertEaEvAnswers" parameterType="com.aidt.api.ea.evcom.dto.EaEvResult">
		INSERT INTO LMS_LRM.EA_EV_QTM_ANW (
		EV_ID
		, QTM_ID
		, USR_ID
		, SMT_ANW_VL
		, QST_XPL_CN
		, CANS_YN
		, IANS_NTE_CANS_YN
		, XPL_TM_SCNT
		, XPL_ST_CD
		, HNT_COFM_YN
		, ANNX_FLE_ID
		, CRTR_ID
		, CRT_DTM
		, MDFR_ID
		, MDF_DTM
		, DB_ID
		)
		VALUES
		<foreach collection="answers" item="item" separator=",">
			(
			#{evId},        -- 평가 ID
			#{item.qtmId},  -- 문항 ID
			#{usrId},       -- 사용자 ID
			#{item.smtAnwVl},    -- 제출 답변 값
			#{item.qstXplCn},    -- 문제 풀이 내용
			IFNULL(#{item.cansYn}, 'N'), -- 정답 여부
			IFNULL(#{item.cansYn}, 'N'), -- 오답 노트 정답 여부
			IF(IFNULL(#{item.xplTmScnt},0) > 1800, 1800, #{item.xplTmScnt}),   -- 풀이 시간 초 수
			IFNULL(#{item.xplStCd}, '00'),   -- 풀이상태코드
			IFNULL(#{item.hntCofmYn}, 'N'),  -- 힌트확인여부
			#{item.annxFleId},   -- 첨부파일ID(메모)
			#{usrId},
			NOW(),
			#{usrId},
			NOW(),
			#{dbId}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		 SMT_ANW_VL		= VALUES(SMT_ANW_VL)
		, QST_XPL_CN		= VALUES(QST_XPL_CN)
		, CANS_YN			= VALUES(CANS_YN)
		, IANS_NTE_CANS_YN	= VALUES(IANS_NTE_CANS_YN)
		, XPL_TM_SCNT		= VALUES(XPL_TM_SCNT)
		, XPL_ST_CD			= VALUES(XPL_ST_CD)
		, HNT_COFM_YN 		= VALUES(HNT_COFM_YN)
		, ANNX_FLE_ID 		= VALUES(ANNX_FLE_ID)
		, MDFR_ID 			= VALUES(MDFR_ID)
		, MDF_DTM 			= VALUES(MDF_DTM)
		/* 평가 공통 - EaEv-Mapper.xml - 평가 문항 답변 등록 insert - upsertEaEvAnswers */
	</insert>

	<insert id="upsertReEaEvAnswers" parameterType="com.aidt.api.ea.evcom.dto.EaEvResult">
		INSERT INTO LMS_LRM.EA_EV_QTM_ANW_RTXM (
		  EV_ID
		, QTM_ID
		, USR_ID
		, RTXM_PN
		, SMT_ANW_VL
		, QST_XPL_CN
		, CANS_YN
		, XPL_TM_SCNT
		, XPL_ST_CD
		, HNT_COFM_YN
		, ANNX_FLE_ID
		, CRTR_ID
		, CRT_DTM
		, MDFR_ID
		, MDF_DTM
		, DB_ID
		)
		VALUES
		<foreach collection="answers" item="item" separator=",">
			(
			#{evId},        -- 평가 ID
			#{item.qtmId},  -- 문항 ID
			#{usrId},       -- 사용자 ID
			#{txmPn},       -- 재응시 번호
			#{item.smtAnwVl},    -- 제출 답변 값
			#{item.qstXplCn},    -- 문제 풀이 내용
			IFNULL(#{item.cansYn}, 'N'), -- 정답 여부
			IF(IFNULL(#{item.xplTmScnt},0) > 1800, 1800, #{item.xplTmScnt}),   -- 풀이 시간 초 수
			IFNULL(#{item.xplStCd}, '00'),   -- 풀이상태코드
			IFNULL(#{item.hntCofmYn}, 'N'),  -- 힌트확인여부
			#{item.annxFleId},   -- 첨부파일ID(메모)
			#{usrId},
			NOW(),
			#{usrId},
			NOW(),
			#{dbId}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		  SMT_ANW_VL		= VALUES(SMT_ANW_VL)
		, QST_XPL_CN		= VALUES(QST_XPL_CN)
		, CANS_YN			= VALUES(CANS_YN)
		, XPL_TM_SCNT		= VALUES(XPL_TM_SCNT)
		, XPL_ST_CD			= VALUES(XPL_ST_CD)
		, HNT_COFM_YN 		= VALUES(HNT_COFM_YN)
		, ANNX_FLE_ID 		= VALUES(ANNX_FLE_ID)
		, MDFR_ID 			= VALUES(MDFR_ID)
		, MDF_DTM 			= VALUES(MDF_DTM)
		/* 평가 공통 - EaEv-Mapper.xml - 평가 재응시 문항 답변 등록 insert - upsertReEaEvAnswers */
	</insert>

	<!--   평가완료 답변 제출 시 단원별 학습수준 조회    -->
	<select id="selectLluLrnrLvs"  resultType="com.aidt.api.ea.evcom.dto.EaEvLearnLevelDto$LearnUnit">

		SELECT
			LLU.LRMP_NOD_ID
			 , IFNULL(LV.LRNR_VEL_TP_CD, 'BA') AS LRNR_VEL_TP_CD
		FROM (
				 SELECT OPT_TXB_ID, LRMP_NOD_ID, RCSTN_ORDN
				 FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
				 WHERE OPT_TXB_ID = #{optTxbId}
				   AND DPTH = 1
			 ) LLU
				 LEFT JOIN (
			SELECT
				EV.USR_ID, EV.SCHL_GRD_CD, EV.SBJ_CD_TMP, EV.LLU_NOD_ID, EV.CANS_RT
				 , LB.LRNR_VEL_TP_CD, LB.LRN_LV_STR_VL, LB.LRN_LV_END_VL
			FROM (
					 SELECT
						 ER.USR_ID
						  , BT.SCHL_GRD_CD
						  , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
								 WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
								 ELSE ''
						 END SBJ_CD_TMP
						  , L_DPTH4.LLU_NOD_ID
						  , COUNT(EQA.QTM_ID)	AS QTM_CNT
						  , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT
						  , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS CANS_RT
					 FROM LMS_LRM.CM_OPT_TXB OT
							  JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
							  JOIN LMS_LRM.EA_EV E  ON E.OPT_TXB_ID = OT.OPT_TXB_ID
							  JOIN LMS_LRM.EA_EV_RS ER  ON ER.EV_ID = E.EV_ID
							  JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
							  JOIN LMS_CMS.BC_KMMP_NOD K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID
							  JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_DPTH5.URNK_KMMP_NOD_ID
							  JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = OT.OPT_TXB_ID AND L_DPTH4.LRMP_NOD_ID = NOD_MPN.LRMP_NOD_ID
							  JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.QTM_ID = EQ.QTM_ID
					 WHERE OT.OPT_TXB_ID = #{optTxbId}
					   AND ( 	(E.EV_DV_CD = 'SE' AND E.EV_DTL_DV_CD IN ('FO','TO','UG'))
						 OR
								(E.EV_DV_CD = 'AE' AND E.EV_DTL_DV_CD = 'OV')
						 )
					   AND ER.USR_ID = #{usrId}
					   AND ER.EV_CMPL_YN = 'Y'
					 GROUP BY ER.USR_ID, BT.SCHL_GRD_CD
							, CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
								   WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
								   ELSE ''
						 END
							, L_DPTH4.LLU_NOD_ID
				 ) EV
					 JOIN LMS_LRM.EA_LRN_LV_BS LB ON LB.SBJ_CD = EV.SBJ_CD_TMP AND LB.SCHL_GRD_CD = EV.SCHL_GRD_CD
			WHERE EV.CANS_RT BETWEEN LB.LRN_LV_STR_VL AND LB.LRN_LV_END_VL
		) LV ON LV.LLU_NOD_ID = LLU.LRMP_NOD_ID
		ORDER BY LLU.RCSTN_ORDN


		/* EaEv-Mapper.xml - selectLluLrnrLvs - 평가완료 답변 제출 시 단원별 학습수준 조회  */

	</select>

	<!--   평가완료 후 단원별 학습자 수준 업데이트     -->
	<insert id="updateTlLuLrnrLvs" parameterType="com.aidt.api.ea.evcom.dto.EaEvLearnLevelDto">
		INSERT INTO lms_lrm.tl_lu_lrnr_lv (
		OPT_TXB_ID, USR_ID, LLU_NOD_ID, LRNR_VEL_TP_CD, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="learnUnits" item="item" separator=",">
			(
			#{optTxbId},
			#{usrId},
			#{item.lrmpNodId},
			#{item.lrnrVelTpCd},
			#{usrId},
			now(),
			#{usrId},
			now(),
			#{dbId}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		LRNR_VEL_TP_CD = VALUES(LRNR_VEL_TP_CD)
		, MDFR_ID        = VALUES(MDFR_ID)
		, MDF_DTM        = VALUES(MDF_DTM)


		/* EaEv-Mapper.xml - updateTlLuLrnrLvs - 평가완료 답변등록 후 단원별 학습자 수준 업데이트  */

	</insert>



	<select id="selectEvLrnAtv" resultType="com.aidt.api.ea.evcom.dto.EaEvLearnActivityDto">
		SELECT
		    LRMP_NOD_ID
		  , LRN_ATV_ID
		FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN
		WHERE OPT_TXB_ID = #{optTxbId}
		AND CTN_TP_CD = 'EX'
		AND EV_ID = #{evId}
		/* 평가 공통 - EaEv-Mapper.xml - 평가 차시, 액티비티 조회 selectEvLrnAtv*/
	</select>


	<select id="selectEaEvQuestions" resultType="com.aidt.api.ea.evcom.dto.EaEvQuestionSolution">
		SELECT
			QCT.QP_QTM_ID                   AS qpQtmId,      -- 문항 아이디
			QQ.QP_QST_TYP_CD                AS qpQstTypCd,   -- 문항 유형 코드
			IFNULL(QCT.QP_JSON_DATA_CN,'')  AS qpJsonDataCn, -- json data
			IFNULL(QCT.PART_CANS_CNT,0)    AS partCansCnt,  -- 부분 정답 개수
			IFNULL(QCT.PLUR_CANS_CNT,0)    AS plurCansCnt   -- 복수 정답 개수
		FROM LMS_CMS.QP_QTM QQ
				 INNER JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID = QQ.QP_QTM_ID
		WHERE QQ.QP_QTM_ID IN
		<foreach collection="list" item="qtmId" open="(" close=")" separator=",">
			#{qtmId}
		</foreach>

		/* 평가 학습 채점 - EaEv-Mapper.xml - 평가 결과 문항별 정답 조회 - selectEaEvQuestions */
	</select>

	<select id="selectEaEvQuestionKeywords" resultType="com.aidt.api.ea.evcom.dto.EaEvQuestionSolution$EaEvQuestionKeyword">
		SELECT QAK.QP_QTM_ID		as qpQtmId			  -- 문항ID
			 , QAK.QP_KWD			as qpKwd		  -- 키워드
			 , QAK.KWD_CNT			as kwdCnt		  -- 키워드 수
			 , QAK.KWD_CASE_YN		as kwdCaseYn 	  -- 대소문자여부
			 , QAK.KWD_SPCE_SKIP_YN as kwdSpaceSkipYn -- 공백무시여부
		FROM LMS_CMS.QP_QTM QQ
				 INNER JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID = QQ.QP_QTM_ID
				 INNER JOIN LMS_CMS.QP_ANW_KWD QAK ON QCT.QP_QTM_ID = QAK.QP_QTM_ID
		WHERE QQ.QP_QTM_ID IN
		<foreach collection="list" item="qtmId" open="(" close=")" separator=",">
			#{qtmId}
		</foreach>

		/* 평가 학습 채점 - 조용진 - EaEvCom-Mapper.xml - 평가 결과 문항별 키워드 조회 - selectEaEvQuestionKeywords */
	</select>

	<select id="selectEaEvSppNtnQuestions" resultType="com.aidt.api.ea.evcom.dto.EaEvSppNtnResult$EaEvSppNtnQuestion">
		SELECT
		QQ.QP_QTM_ID AS qtmId
		, MAX(QM.tpcId) AS tpcId
		FROM (
		SELECT QM.RLT_QTM_TP_CD, QM.SRC_QTM_ID, QM.RLT_QTM_ID
		, (
		SELECT TPC_ID
		FROM LMS_CMS.BC_EVSH_QTM_MPN
		WHERE QTM_ID = QM.SRC_QTM_ID -- 연관문항에 토픽이 없어서 원천문항의 토릭을 가져옴
		AND DEL_YN = 'N'
		LIMIT 1
		) AS tpcId
		, EQ.QTM_ORDN
		FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		<choose>
			<when test="txmPn != null and txmPn > 0">
				JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EA ON EA.EV_ID = EQ.EV_ID AND EA.QTM_ID = EQ.QTM_ID AND EA.RTXM_PN = #{txmPn}
			</when>
			<otherwise>
				JOIN LMS_LRM.EA_EV_QTM_ANW EA ON EA.EV_ID = EQ.EV_ID AND EA.QTM_ID = EQ.QTM_ID
			</otherwise>
		</choose>
		JOIN LMS_CMS.BC_RLT_QTM_MPN QM ON QM.SRC_QTM_ID = EQ.QTM_ID
		WHERE E.EV_ID = #{evId}
		AND EQ.DEL_YN = 'N'
		AND EA.USR_ID = #{usrId}
		<choose>
			<when test = 'ntnEvCrtYn != null and "Y".equals(ntnEvCrtYn) and iansCnt != null and iansCnt == 0'>
				AND QM.RLT_QTM_TP_CD = 'DE'
			</when>
			<otherwise>
				AND EA.CANS_YN = 'N'
				AND QM.RLT_QTM_TP_CD IN ('SI', 'TW')
			</otherwise>
		</choose>
		AND QM.DEL_YN = 'N'
		AND QM.TXB_ID = (SELECT TXB_ID FROM LMS_LRM.CM_OPT_TXB WHERE OPT_TXB_ID = E.OPT_TXB_ID)
		) QM
		JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID 		   = QM.RLT_QTM_ID
		JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
		JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
		JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
		WHERE QQ.DEL_YN ='N'
		GROUP BY QQ.QP_QTM_ID
		ORDER BY MAX(QM.QTM_ORDN), MAX(QM.RLT_QTM_TP_CD) DESC, MAX(QQ.QP_DFFD_CD) DESC, QQ.QP_QTM_ID

		/* EaEvCom-Mapper.xml - selectEvSmlrQtmIdList - 평가 창 > 제출 > 보충/심화 문항ID 리스트 조회 - 박원희 */
	</select>

	<insert id="upsertEaEvSppNtnRs" parameterType="com.aidt.api.ea.evcom.dto.EaEvSppNtnResult">
		INSERT INTO LMS_LRM.EA_EV_SPP_NTN_RS (
		   EV_ID, USR_ID, TXM_PN, SPP_NTN_TP_CD, QST_CNT
		 , SMT_DTM, EV_TM_SCNT, CANS_CNT
		 , TXM_STR_YN, EV_CMPL_YN
		 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT
			E.EV_ID, #{usrId}, #{txmPn}, #{sppNtnTpCd}, #{qstCnt}
			 , CASE WHEN #{evCmplYn} = 'Y' THEN now() ELSE NULL END
			 , E.XPL_TM_SCNT_SUM
			 , E.CANS_CNT_SUM
			 , CASE WHEN E.XPL_TM_SCNT_SUM > 0 THEN 'Y' ELSE 'N' END
			 , #{evCmplYn}
			 , #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM (
				 SELECT
					 E.EV_ID
					  , SUM(EQA.XPL_TM_SCNT) AS XPL_TM_SCNT_SUM
					  , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT_SUM
				 FROM LMS_LRM.EA_EV E
						  LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = #{usrId} AND EQA.TXM_PN = #{txmPn}
				 WHERE E.EV_ID = #{evId}
				 GROUP BY E.EV_ID
			 ) E
			ON DUPLICATE KEY UPDATE
			   SMT_DTM    = VALUES(SMT_DTM)
			 , EV_TM_SCNT = VALUES(EV_TM_SCNT)
			 , CANS_CNT   = VALUES(CANS_CNT)
			 , TXM_STR_YN = VALUES(TXM_STR_YN)
			 , EV_CMPL_YN = VALUES(EV_CMPL_YN)
			 , MDFR_ID    = #{usrId}
			 , MDF_DTM    = NOW()

		/* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가보충심화결과 update */

	</insert>

	<insert id="upsertEaEvSppNtnAnswers" parameterType="com.aidt.api.ea.evcom.dto.EaEvSppNtnResult">

		INSERT INTO LMS_LRM.EA_EV_SPP_NTN_QTM_ANW (
			  EV_ID
			, USR_ID
			, TXM_PN
			, SPP_NTN_TP_CD
			, QTM_ID
			, QTM_ORDN
			, TPC_ID
			, SMT_ANW_VL
			, QST_XPL_CN
			, CANS_YN
			, XPL_TM_SCNT
			, XPL_ST_CD
			, HNT_COFM_YN
			, ANNX_FLE_ID
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		)
		VALUES
		<foreach collection="answers" index="index" item="item" separator=",">
			(
				  #{evId}
				, #{usrId}
				, #{txmPn}
				, #{sppNtnTpCd}
				, #{item.qtmId}
				, #{index}+1
				, #{item.tpcId}
				, #{item.smtAnwVl}
				, #{item.qstXplCn}
				, #{item.cansYn}
				, IF(IFNULL(#{item.xplTmScnt},0) > 1800, 1800, #{item.xplTmScnt})
				, #{item.xplStCd}
				, IFNULL(#{item.hntCofmYn}, 'N')
				, #{item.annxFleId}
				, #{usrId}, now(), #{usrId}, now(), #{dbId}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
			  MDFR_ID     = #{usrId}
			, MDF_DTM     = NOW()
			, ANNX_FLE_ID = VALUES(ANNX_FLE_ID)
			, SMT_ANW_VL  = VALUES(SMT_ANW_VL)
			, QST_XPL_CN  = VALUES(QST_XPL_CN)
			, CANS_YN     = VALUES(CANS_YN)
			, XPL_TM_SCNT = IF(IFNULL(VALUES(XPL_TM_SCNT), 0) > 1800, 1800, VALUES(XPL_TM_SCNT))
			, XPL_ST_CD 	= VALUES(XPL_ST_CD)
			, HNT_COFM_YN = IFNULL(VALUES(HNT_COFM_YN), 'N')

		/* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가 평가보충심화결과 답변 등록 */

	</insert>

	<update id="updateEvRsAnswerExplanationStatus">
		UPDATE LMS_LRM.EA_EV_QTM_ANW EQA_UP
			JOIN (
			SELECT
			E.EV_ID
			, E.USR_ID
			, E.QTM_ID
			, CASE WHEN E.CANS_YN = 'Y' AND DXT.PPE_XPL_TM_SCNT * 0.3 >= E.XPL_TM_SCNT THEN '02' -- 급하게 푼 문제
			WHEN E.CANS_YN = 'N' THEN
			CASE WHEN E.LRNR_VEL_TP_CD = 'FS' AND E.QTM_DFFD_DV_CD IN ('01', '02', '03') THEN '11' -- 실수로 예상되는 문제
			WHEN E.LRNR_VEL_TP_CD = 'NM' AND E.QTM_DFFD_DV_CD IN ('01', '02') THEN '11' -- 실수로 예상되는 문제
			ELSE '00'
			END
			ELSE '00' END AS XPL_ST_CD
			FROM (
			SELECT
			BT.SCHL_GRD_CD
			, BT.SBJ_CD
			, USR.USR_ID
			, USR.LRNR_VEL_TP_CD
			, CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
			WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
			ELSE ''
			END SBJ_CD_TMP
			, E.EV_ID
			, EQ.QTM_ID
			, EQ.QTM_DFFD_DV_CD
			, CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'CM'
			WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN QQA.QP_CN_ARA_ID
			ELSE ''
			END CN_ARA_ID
			, EQA.CANS_YN
			, EQA.XPL_TM_SCNT
			FROM LMS_LRM.EA_EV E
			JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
			JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
			JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
			JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
			JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = USR.USR_ID
			JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
			WHERE E.EV_ID = #{evId}
			AND USR.USR_ID = #{usrId}
			) E
			LEFT JOIN LMS_LRM.EA_SBJ_DFFD_PPE_XPL_TM DXT
			ON DXT.SBJ_CD = E.SBJ_CD_TMP
			AND DXT.SCHL_GRD_CD = E.SCHL_GRD_CD
			AND DXT.CN_ARA_CD = E.CN_ARA_ID
			AND DXT.DFFD_CD = E.QTM_DFFD_DV_CD
			) E ON E.EV_ID = EQA_UP.EV_ID AND E.USR_ID = EQA_UP.USR_ID AND E.QTM_ID = EQA_UP.QTM_ID
			SET EQA_UP.XPL_ST_CD = IFNULL(E.XPL_ST_CD, '00')
		WHERE EQA_UP.EV_ID = #{evId}
		  AND EQA_UP.USR_ID = #{usrId}

		/* EaEvCom-Mapper.xml - updateEvQtmAnwXplStCd - 평가완료 풀이상태 업데이트 - 박원희 */
	</update>



</mapper>