package com.aidt.api.at.err;

import com.aidt.api.at.err.dto.BackErrLogDto;
import com.aidt.common.CommonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BackErrLogService {

	@Autowired
	private CommonDao commonDao;

	@Transactional
	public void insertBackErrLog(BackErrLogDto dto) {
		commonDao.insert("api.bc.common.cm.insertBackErrLog", dto);
	}

}
