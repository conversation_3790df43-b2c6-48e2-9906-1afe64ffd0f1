package com.aidt.api.bc.wdlst.tcr;

import org.springframework.stereotype.Service;

//import java.util.List;
//import org.springframework.beans.factory.annotation.Autowired;
//import com.aidt.api.bc.wdlst.dto.BcWdLstDto;
//import com.aidt.api.bc.wdlst.dto.BcWdSrhHstDto;
//import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:55:46
 * @modify 2024-01-05 17:55:46
 * @desc 단어장 Service
 * BcWdLstStuService 와 동일하여 해당 서비스는 삭제 하고 BcWdLstStuService 사용
 */

@Service
public class BcWdLstTcrService {
//    private final String MAPPER_NAMESPACE = "api.bc.wdlst.tcr.";
//
//    @Autowired
//    private CommonDao commonDao;
//
//    /**
//     * 단어장 목록 조회 서비스
//     *
//     * @param BcWdLstDto
//     * @return List<LwWdLstDto>
//     */
//    public List<BcWdLstDto> selectWdLstList(BcWdLstDto bcWdLstDto) {
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectWdLstList", bcWdLstDto);
//    }
//    
//    /**
//     * 단어장 팝업 목록 조회 서비스
//     *
//     * @param BcWdLstDto
//     * @return List<LwWdLstDto>
//     */
//    public List<BcWdLstDto> selectPopWdLstList(BcWdLstDto bcWdLstDto) {
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectPopWdLstList", bcWdLstDto);
//    }
//    
//    /**
//     * 영어단어 관련검색어 조회 서비스
//     *
//     * @param BcWdLstDto
//     * @return List<LwWdLstDto>
//     */
//    public List<BcWdLstDto> srchWdLst(BcWdLstDto bcWdLstDto) {
//        return commonDao.selectList(MAPPER_NAMESPACE + "srchWdLst", bcWdLstDto);
//    }
//    
//    /**
//     * 영어단어 조회 서비스
//     *
//     * @param BcWdLstDto
//     * @return List<LwWdLstDto>
//     */
//    public List<BcWdLstDto> selectCmEnWdLst(BcWdLstDto bcWdLstDto) {
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectCmEnWdLst", bcWdLstDto);
//    }
//    
//    /**
//     * 단어장 단어 등록 서비스
//     *
//     * @param BcWdLstDto
//     * @return Integer
//     */
//    public int insertWdLstList(BcWdLstDto bcWdLstDto) {
//        return commonDao.insert(MAPPER_NAMESPACE + "insertWdLst", bcWdLstDto);
//    }
//    
//    /**
//     * 단어장 삭제 서비스
//     *
//     * @param BcWdLstDto
//     * @return List<LwWdLstDto>
//     */
//    public int deleteWdLstList(BcWdLstDto item) {
//        int result = 0;
//        for(int myWdId : item.getMyWdIds()) {
//            item.setMyWdId(myWdId);
//            result += commonDao.delete(MAPPER_NAMESPACE + "deleteWdLstList", item);
//        }
//        return result;
//    }
//    
//    /**
//     * 단어 검색이력 목록 조회 서비스
//     *
//     * @param BcWdSrhHstDto
//     * @return List<LwWdSrhHstDto>
//     */
//    public List<BcWdSrhHstDto> selectWdSrhHstList(BcWdSrhHstDto bcWdSrhHstDto) {
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectWdSrhHstList", bcWdSrhHstDto);
//    }
//    
//    /**
//     * 단어 검색이력 등록 서비스
//     *
//     * @param BcWdSrhHstDto
//     * @return Integer
//     */
//    public int insertWdSrhHstList(BcWdSrhHstDto bcWdSrhHstDto) {
//        
//        int getHstCnt = commonDao.select(MAPPER_NAMESPACE + "selectWdSrhHstCnt", bcWdSrhHstDto);
//        
//        if(getHstCnt > 0) {
//            return commonDao.update(MAPPER_NAMESPACE + "updateWdSrhHst", bcWdSrhHstDto);
//        }else {    
//            return commonDao.insert(MAPPER_NAMESPACE + "insertWdSrhHst", bcWdSrhHstDto);
//        }        
//    }
}

