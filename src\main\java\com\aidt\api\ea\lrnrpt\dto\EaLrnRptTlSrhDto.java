package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSrhDto {

    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="과목")
    private String sbjCd;
	
	@Parameter(name="사용자 분류")
    private String usrTpCd;
	
	@Parameter(name="차시ID")
    private String lrmpNodId;
	
	@Parameter(name="차시Nm")
    private String lrmpNodNm;
	
	@Parameter(name="학급 분류")
	private String schlGrdCd;
}
