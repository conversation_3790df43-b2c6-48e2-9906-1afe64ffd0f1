/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-14 13:29:43
 * @modify date 2024-06-14 13:29:43
 * @desc [TlOneClkSetmRcmCtnDto 원클릭학습설정 추천컨텐츠 재구성 dto]
 */
package com.aidt.api.tl.oneclksetm.tcr.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TlOneClkSetmRcmCtnDto {
    /** 운영교과서 ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 특별학습 ID */
    @Parameter(name="특별학습ID")
    private String spLrnId;

    /** 특별학습명 */
    @Parameter(name="특별학습명")
    private String spLrnNm;

    /** 특별학습목표 */
    @Parameter(name="특별학습목표")
    private String lrnGoalCn;

    /** 구성내용 */
    @Parameter(name="구성내용")
    private String cstnCn;

    /** 특별학습수준 */
    @Parameter(name="특별학습수준")
    private String spLrnDffd;

    /** 콘텐츠수 */
    @Parameter(name="콘텐츠수")
    private int ctnCnt;

    /** 학습중인 학생수 */
    @Parameter(name="학습중인 학생수")
    private int rcmStuCnt;

    /** 썸네일 PC경로 */
    @Parameter(name="썸네일 PC경로")
    private String pcPath;

    /** 썸네일 TABLET경로 */
    @Parameter(name="썸네일 TABLET경로")
    private String taPath;
    
    
    /** 학생리스트 */
    @Parameter(name="학생리스트")
    private List<TlOneClkSetmRcmStuDto> stuList;    
}
