package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "첨부파일 ANNX_ID 조회 Request DTO")
public class BcCmSelectNoteFileReqDto {
	
	@Parameter(name="사용자ID")
	private String userId;

	@Parameter(name="평가ID")
	private String evId;
	
	@Parameter(name="문항ID")
	private String qtmId;

}