package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptSlSpLrnDto {

	@Parameter(name="순번")
	private int idx;
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="사용자 분류")
    private String usrTpCd;
	
	@Parameter(name="과목")
    private String sbjCd;
	
	@Parameter(name="특별학습ID")
	private String spLrnId;
	
	@Parameter(name="특별학습명")
	private String spLrnNm;
	
	@Parameter(name="1DPTH의 NOD ID")
	private String fDpthSpLrnNodId;
	
	@Parameter(name="1DPTH의 NOD NM")
	private String fDpthSpLrnNodNm;
	
	@Parameter(name="마지막 NOD ID")
	private String lDpthSpLrnNodId;
	
	@Parameter(name="마지막 NOD NM")
	private String lDpthSpLrnNodNm;
	
	@Parameter(name="NOD의 마지막 DPTH")
	private String lDpth;
	
	@Parameter(name="마지막 노드 여부")
	private String lwsYn;
	
	@Parameter(name="콘텐츠 존재여부")
	private String cstnCmplYn;
	
	@Parameter(name="1DPTH NOD INFO")
	private List<EaLrnRptSlSpLrnNodFDto> eaLrnRptSlSpLrnNodFList;
	
	@Parameter(name="콘텐츠 상세정보")
	private List<EaLrnRptSlSpLrnContentDto> eaLrnRptSlSpLrnContentList;
}
