package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrDtlEvResDto {
	
	@Parameter(name="총조회갯수")
	private int totalCnt;

	@Parameter(name="번호")
	private int rowNo;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="사용자명")
	private String usrNm;
	
	@Parameter(name="프로필 이미지 명")
	private String usrProfile;
	
	@Parameter(name="사용자 icon")
	private String usrIcon;	
	
	@Parameter(name="프로필 이미지 명")
	private String profileImgNm;
	
	@Parameter(name="관심학생 여부")
	private String ntrYn;
	
	@Parameter(name="학습수준코드")
	private String lrnrVelTpCd;
	
	@Parameter(name="학습수준명")
	private String lrnrVelTpNm;
	
	@Parameter(name="종합성취도")
	private String allrAchd;
	
	@Parameter(name="총학습시간")
	private String allLrnTm;

	@Parameter(name="교과학습시간")
	private String txbLrnTm;

	@Parameter(name="AI학습시간")
	private String aiLrnTm;
	
	@Parameter(name="평가학습시간")
	private String evLrnTm;

	@Parameter(name="과제학습시간")
	private String asnLrnTm;
	
	@Parameter(name="교과학습진행율")
	private String txbLrnPgrsRt;
	
	@Parameter(name="AI학습진행율")
	private String aiLrnPgrsRt;
	
	@Parameter(name="평가진행율")
	private String evLrnPgrsRt;

	@Parameter(name="과제진행율")
	private String asnLrnPgrsRt;

}
