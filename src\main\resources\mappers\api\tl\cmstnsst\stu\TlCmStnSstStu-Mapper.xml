<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.cmstnsst.stu">
    <!-- 교육과정표준체계ID목록 취득-->
    <select id="selectTxbEduCrsCnCdList" parameterType="Map" resultType="com.aidt.api.tl.cmstnsst.dto.TlCmEduCrsCnDto">
        SELECT A.LRN_ATV_ID  /* 학습활동ID */
              , IFNULL(E.CRCL_CTN_ELM2_CD, '') AS EDU_CRS_CN_CD /* 교육과정콘텐츠표준ID */
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
              INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.DEL_YN = 'N'
              INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                   ON A.LRN_ATV_ID = D.LRN_ATV_ID
                   AND D.USE_YN= 'Y'
                   AND D.DEL_YN= 'N'
              INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 E /* BC_국가수준교육과정컨텐츠매핑 */
                   ON E.CTN_ID = D.LRN_ATV_ID
                   AND E.CRCL_CTN_TP_CD = 'TL'  -- 교과
                   AND E.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        AND A.USE_YN  = 'Y'
        /* 학습단계구분코드=개념학습, 평가, 익힘  ('CL', 'EX', 'WB') */
        AND B.LRN_STP_DV_CD IN (
        <foreach collection="stpDvList" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        AND B.DEL_YN = 'N'
        AND A.LRMP_NOD_ID = #{param.lrmpNodId}
        <if test='param.lrnAtvId != null and param.lrnAtvId !=""'>
            AND A.LRN_ATV_ID = #{param.lrnAtvId}
        </if>
        GROUP BY A.LRN_ATV_ID, D.CTN_META_DATA_ID, E.CRCL_CTN_ELM2_CD


        /* 교과학습 강성희 TlCmStnSstStu-Mapper.xml - selectTxbEduCrsCnCdList */
    </select>

    
    <!-- 대단원교육과정표준체계ID목록 취득-->
    <select id="selectTxbLluEduCrsCnCdList" parameterType="Map" resultType="String">
        SELECT DISTINCT IFNULL(E.CRCL_CTN_ELM2_CD, '') AS EDU_CRS_CN_CD /* 교육과정콘텐츠표준ID */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN M/* TL_교과학습노드재구성 (차시정보)*/
              INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
                    ON A.OPT_TXB_ID = #{param.optTxbId}
                    AND M.LRMP_NOD_ID = A.LRMP_NOD_ID
                    AND A.USE_YN  = 'Y'
              INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.DEL_YN = 'N'
              INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                   ON A.LRN_ATV_ID = D.LRN_ATV_ID
                   AND D.USE_YN= 'Y'
                   AND D.DEL_YN= 'N'
              INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 E /* BC_국가수준교육과정컨텐츠매핑 */
                   ON E.CTN_ID = D.LRN_ATV_ID
                   AND E.CRCL_CTN_TP_CD = 'TL'  -- 교과
                   AND E.DEL_YN = 'N'
        WHERE M.OPT_TXB_ID = #{param.optTxbId}
          AND M.LLU_NOD_ID = #{param.lrmpNodId} -- 대단원노드ID
          AND M.DPTH = 4
          AND M.USE_YN  = 'Y'
          /* 학습단계구분코드=개념학습, 평가, 익힘  ('CL', 'EX', 'WB') */
          AND B.LRN_STP_DV_CD IN (
          <foreach collection="stpDvList" index="index" item="item" separator=",">
              #{item}
          </foreach>
          )
          GROUP BY EDU_CRS_CN_CD
        /* 교과학습 강성희 TlCmStnSstStu-Mapper.xml - selectTxbLluEduCrsCnCdList */
    </select>

</mapper>
