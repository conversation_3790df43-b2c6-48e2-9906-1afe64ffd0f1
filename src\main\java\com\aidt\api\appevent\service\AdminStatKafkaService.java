package com.aidt.api.appevent.service;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.aidt.api.appevent.event.adminstat.ClaInsertEvent;
import com.aidt.api.appevent.event.adminstat.ClaInsertEvent.ClaInsertPayload;
import com.aidt.api.appevent.event.adminstat.LoginEvent;
import com.aidt.api.appevent.event.adminstat.OptTxbInsertEvent;
import com.aidt.api.appevent.event.adminstat.OptTxbInsertEvent.OptTxbInsertPayload;
import com.aidt.api.appevent.event.adminstat.OptTxbPridInsertEvent;
import com.aidt.api.appevent.event.adminstat.OptTxbPridInsertEvent.OptTxbPridInsertPayload;
import com.aidt.api.appevent.event.adminstat.UsrInsertEvent;
import com.aidt.api.appevent.event.adminstat.UsrInsertEvent.UsrInsertPayload;
import com.aidt.api.at.dto.User;
import com.aidt.api.at.token.dto.LoginEventDto;
import com.aidt.api.at.token.dto.OptTxbPridDto;
import com.aidt.api.bc.cm.dto.BcClaDto;
import com.aidt.api.bc.cm.dto.BcCmOptTxb;
import com.aidt.api.bc.cm.dto.BcCmUsrDto;
import com.aidt.base.exception.CustomException;
import com.aidt.base.exception.ExceptionMessage;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AdminStatKafkaService {

	private final String MAPPER_NAMESPACE = "AdminStat.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private ApplicationEventPublisher eventPublisher;

	/**
	 * 로그인 data KAFKA 등록 처리
	 */
	public void loginEvent(User.UserResponseDto userDetail) {
		try {
			if (userDetail == null) {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
						"###KAFKA### loginEvent userDetail is null...");
			}

			Long txbId = null;

			if (StringUtils.isNotBlank(userDetail.getTxbId())) {
				try {
					txbId = Long.parseLong(userDetail.getTxbId());
				} catch (NumberFormatException nfe) {
					log.error("###KAFKA### loginEvent 수집 NumberFormatException >>> " + nfe.getMessage());
				}
			}

			String schlCd = null;
			Long sgy = null;
			String schlGrdCd = null;
			String kerisClaCd = null;
			String txbNm = null;
			String autrCd = null;
			String sbjCd = null;
			LocalDateTime loginDtm = null;

			// 사용자 정보 외 추가 조회
			LoginEventDto leDto = commonDao.select(MAPPER_NAMESPACE + "selectLoginEventAddData", userDetail.getUsrId());

			if (leDto != null) {
				schlCd = leDto.getSchlCd();
				sgy = leDto.getSgy();
				schlGrdCd = leDto.getSchlGrdCd();
				kerisClaCd = leDto.getKerisClaCd();
				txbNm = leDto.getTxbNm();
				autrCd = leDto.getAutrCd();
				sbjCd = leDto.getSbjCd();
				loginDtm = leDto.getLoginDtm();
			}

			LoginEvent.LoginEventPayload payload = LoginEvent.LoginEventPayload.builder().usrId(userDetail.getUsrId())
					.kerisUsrId(userDetail.getKerisUsrId()).txbId(txbId).claId(userDetail.getClaId())
					.usrTpCd(userDetail.getUsrTpCd()).schlCd(schlCd).sgy(sgy).schlGrdCd(schlGrdCd)
					.kerisClaCd(kerisClaCd).txbNm(txbNm).autrCd(autrCd).sbjCd(sbjCd).loginDtm(loginDtm).build();

			eventPublisher.publishEvent(new LoginEvent(payload));
		} catch (CustomException e) {
			log.error("###KAFKA### loginEvent 수집 Exception >>> " + e.getMessage());
		}
	}

	/**
	 * 학급 data KAFKA 등록 처리
	 */
	public void claInsertEvent(String claId) {
		try {
			if (StringUtils.isNotBlank(claId)) {
				BcClaDto dto = commonDao.select(MAPPER_NAMESPACE + "selectCla", claId);

				if (dto == null) {
					throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
							"###KAFKA### claInsertEvent DB Search data is null... >>> claId = " + claId);
				}

				ClaInsertEvent.ClaInsertPayload payload = ClaInsertPayload.builder().claId(dto.getClaId())
						.schlCd(dto.getSchlCd()).sgy(dto.getSgy()).claNo(dto.getClaNo()).claNm(dto.getClaNm())
						.classroomNm(dto.getClassroomNm()).kerisClaCd(dto.getKerisClaCd()).crtrId(dto.getCrtrId())
						.crtDtm(dto.getCrtDtm()).mdfrId(dto.getMdfrId()).mdfDtm(dto.getMdfDtm()).build();

				eventPublisher.publishEvent(new ClaInsertEvent(payload));
			} else {
				throw new RuntimeException("###KAFKA### claInsertEvent claId value is null or empty...");
			}
		} catch (CustomException e) {
			log.error("###KAFKA### claInsertEvent 수집 Exception >>> " + e.getMessage());
		}
	}

	/**
	 * 운영교과서 data KAFKA 등록 처리
	 */
	public void optTxbInsertEvent(String optTxbId) {
		try {
			if (StringUtils.isNotBlank(optTxbId)) {
				BcCmOptTxb dto = commonDao.select(MAPPER_NAMESPACE + "selectOptTxb", optTxbId);

				if (dto == null) {
					throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
							"###KAFKA### optTxbInsertEvent DB Search data is null... >>> optTxbId = " + optTxbId);
				}

				OptTxbInsertEvent.OptTxbInsertPayload payload = OptTxbInsertPayload.builder()
						.optTxbId(dto.getOptTxbId()).txbId(dto.getTxbId()).claId(dto.getClaId())
						.kerisLectCd(dto.getKerisLectCd()).crtrId(dto.getCrtrId()).crtDtm(dto.getCrtDtm())
						.mdfrId(dto.getMdfrId()).mdfDtm(dto.getMdfDtm()).build();

				eventPublisher.publishEvent(new OptTxbInsertEvent(payload));
			} else {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
						"###KAFKA### optTxbInsertEvent optTxbId value is null or empty...");
			}
		} catch (CustomException e) {
			log.error("###KAFKA### optTxbInsertEvent 수집 Exception >>> " + e.getMessage());
		}
	}

	/**
	 * 회원 data KAFKA 등록 처리
	 */
	public void usrInsertEvent(String usrId) {
		try {
			if (StringUtils.isNotBlank(usrId)) {
				BcCmUsrDto dto = commonDao.select(MAPPER_NAMESPACE + "selectUsr", usrId);

				if (dto == null) {
					throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
							"###KAFKA### usrInsertEvent DB Search data is null... >>> usrId = " + usrId);
				}

				UsrInsertEvent.UsrInsertPayload payload = UsrInsertPayload.builder().usrId(dto.getUsrId())
						.kerisUsrId(dto.getKerisUsrId()).usrNm(dto.getUsrNm()).usrTpCd(dto.getUsrTpCd())
						.fstRegDtm(dto.getFstRegDtm()).claId(dto.getClaId()).crtrId(dto.getCrtrId())
						.crtDtm(dto.getCrtDtm()).mdfrId(dto.getMdfrId()).mdfDtm(dto.getMdfDtm()).build();

				eventPublisher.publishEvent(new UsrInsertEvent(payload));
			} else {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
						"###KAFKA### usrInsertEvent usrId value is null or empty...");
			}
		} catch (CustomException e) {
			log.error("###KAFKA### usrInsertEvent 수집 Exception >>> " + e.getMessage());
		}
	}

	/**
	 * 회원 data KAFKA 등록 처리 (목록 요청)
	 */
	public void usrInsertEvent(List<String> usrIds) {
		if (usrIds != null && usrIds.size() > 0) {
			for (String usrId : usrIds) {
				usrInsertEvent(usrId);
			}
		}
	}

	public void optTxbPridInsertEvent(String optTxbId, String optTxbPrid) {
		try {
			if (StringUtils.isBlank(optTxbPrid)) {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
						"optTxbPridInsertEvent optTxbId is null or empty...");
			}

			if (StringUtils.isBlank(optTxbPrid)) {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
						"optTxbPridInsertEvent optTxbPrid is null or empty...");
			}

			OptTxbPridDto paramDto = OptTxbPridDto.builder().optTxbId(optTxbId).optTxbPrid(optTxbPrid).build();
			OptTxbPridDto dto = commonDao.select(MAPPER_NAMESPACE + "selectOptTxbPrid", paramDto);

			if (dto == null) {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
						"###KAFKA### optTxbPridInsertEvent DB Search data is null... >>> optTxbId = " + optTxbId
								+ ", optTxbPrid = " + optTxbPrid);
			}

			OptTxbPridInsertEvent.OptTxbPridInsertPayload payload = OptTxbPridInsertPayload.builder()
					.optTxbId(dto.getOptTxbId()).optTxbPrid(dto.getOptTxbPrid()).kerisLectCd(dto.getKerisLectCd())
					.regDtm(dto.getRegDtm()).build();
			
			eventPublisher.publishEvent(new OptTxbPridInsertEvent(payload));
		} catch (CustomException e) {
			log.error("###KAFKA### optTxbPridInsertEvent 수집 Exception >>> " + e.getMessage());
		}
	}
}
