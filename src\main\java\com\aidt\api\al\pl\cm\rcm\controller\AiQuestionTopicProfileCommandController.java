package com.aidt.api.al.pl.cm.rcm.controller;

import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.cm.rcm.dto.AiQuestionTopicProfileReqDto;
import com.aidt.api.al.pl.cm.rcm.service.AiQuestionTopicProfileLoadService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "[al] AI 맞춤 문항토픽별 프로파일[new]", description = "[al] AI 맞춤 문항토픽별 프로파일[new]")
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@RestController
@RequestMapping("api/v1/al/pl/cm/qtmTpcProf")
public class AiQuestionTopicProfileCommandController {

	private final AiQuestionTopicProfileLoadService aiQuestionTopicProfileLoadService;


	@Operation(summary = "[ai] 프로파일 적재", description = "## 프로파일 적재")
	@PostMapping(value = "/profile/load", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Void> buildProfile(@Valid @RequestBody AiQuestionTopicProfileReqDto request) {
		aiQuestionTopicProfileLoadService.load(request.getEvId(), request.isTopicCompleted());
		return Response.ok();
	}

}
