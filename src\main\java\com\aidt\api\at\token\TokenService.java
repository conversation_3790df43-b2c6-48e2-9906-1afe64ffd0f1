package com.aidt.api.at.token;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.at.mapper.TokenMapper;
import com.aidt.api.at.token.dto.Jwt;

@Service
public class TokenService {

    @Autowired
    private TokenMapper tokenMapper;
  
	@Transactional(readOnly = true)
	public Jwt.JwtUserDto selectUser(Jwt.JwtRequestDto jwtRequestDto) {
		return tokenMapper.selectUser(jwtRequestDto);
	}

	@Transactional(readOnly = true)
	public Jwt.JwtUserDto selectRefreshToken(Jwt.JwtRefreshDto jwtRefreshDto) {
		return tokenMapper.selectRefreshToken(jwtRefreshDto);
	}
	
	@Transactional(readOnly = true)
	public Jwt.JwtUserDto selectUsrRefreshToken(Jwt.JwtRefreshDto jwtRefreshDto) {
		return tokenMapper.selectUsrRefreshToken(jwtRefreshDto);
	}

	@Transactional
	public int updateRefreshToken(Jwt.JwtRefreshDto refreshDto) {
		return tokenMapper.updateRefreshToken(refreshDto);
	}
	
	@Transactional
	public int insertUsrRefreshToken(Jwt.JwtRefreshDto refreshDto) {
		return tokenMapper.insertUsrRefreshToken(refreshDto);
	}

	@Transactional
	public int deleteRefreshToken(String refreshToken) {
		return tokenMapper.deleteRefreshToken(refreshToken);
	}
    
//    private final String MAPPER_NAMESPACE = "api.at.token.";
//
//    @Autowired
//    private CommonDao commonDao;
//
//
//    @Transactional(readOnly = true)
//    public Jwt.JwtUserDto selectUser(Jwt.JwtRequestDto jwtRequestDto) {
//        return commonDao.select(MAPPER_NAMESPACE + "selectUser", jwtRequestDto);
//    }
//
//    @Transactional(readOnly = true)
//    public Jwt.JwtUserDto selectRefreshToken(Jwt.JwtRefreshDto jwtRefreshDto) {
//        return commonDao.select(MAPPER_NAMESPACE + "selectRefreshToken", jwtRefreshDto);
//    }
//
//    @Transactional
//    public int updateRefreshToken(Jwt.JwtRefreshDto refreshDto) {
//        return commonDao.update(MAPPER_NAMESPACE + "updateRefreshToken", refreshDto);
//    }
//
//    @Transactional
//    public int deleteRefreshToken(String refreshToken) {
//        return commonDao.update(MAPPER_NAMESPACE + "deleteRefreshToken", refreshToken);
//    }
}
