##############################################################################################################
# common(default)
##############################################################################################################
project:
  # 프로젝트식별을 위해 사용됨. (pom.xml에서 값을 찾음)
  meta.group: "@project.meta.group@"
  meta.service: "@project.meta.service@"
  meta.basePathPrefix: "@project.meta.basePathPrefix@"
  groupId: "@project.groupId@"
  artifact: "@project.artifactId@"
  version: "@project.version@"
  name: "@project.name@"
  description: "@project.description@"
  build:
    finalName: "@project.build.finalName@"
    revision: "@project.current.build.revision@"
    branch: "@project.current.build.branch@"
    timestamp: "@project.current.build.timestamp@"

logging:
  file:
    name: ${project.meta.basePathPrefix}log/${project.name}
    max-size: 20MB
    max-history: 10
  level:
    org:
      springframework:
        security: DEBUG
    root: DEBUG # Level : ALL > TRACE > DEBUG > INFO > WARN > ERROR > FATAL > OFF
    jdbc:
      #connection : OFF  #열려있는 모든 번호와 연결 수립 및 해제 이벤트를 기록
      #audit: OFF #ResultSet을 제외한 모든 JDBC 호출 정보
      #resultset: OFF #ResultSet을 포함한 모든 JDBC 호출 정보
      #sqlonly: OFF #SQL문만을 로그로 남기며, PreparedStatement일 경우 관련된 argument 값으로 대체된 SQL문
      #sqltiming: OFF #SQL문과 해당 SQL을 실행시키는데 수행된 시간 정보
      #resultsettable: OFF #SQL 결과 조회된 데이터

server:
  port: 8080
  servlet:
    context-path: / # Root ContextPath
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  shutdown: graceful
  tomcat:
    max-http-form-post-size: 10GB
    uri-encoding: UTF-8
  error:
    include-exception: true
    include-stacktrace: always

spring:
  cloud.config.enabled: false
  application.name: ${project.name}
  profiles.active: local
  devtools:
    livereload.enabled: false # view단이 바뀌었을 때 자동으로 리로드
    restart.enabled: false # 컨트롤러,서비스 모델단이 바뀌었을 때 프로젝트 재시작 설정
  kafka:
    application-name: ${project.name}-${spring.profiles.active}-${server.meta.textbook.systemCode}
    bootstrap-servers: localhost:29092,localhost:29093,localhost:29094
    enabled: true
  servlet.multipart:
    enabled: true #멀티파트 업로드 지원여부 (default: true)
    file-size-threshold: 2KB
    max-file-size: 300MB #파일의 최대 사이즈 (default: 1MB)
    max-request-size: 315MB #요청의 최대 사이즈 (default: 10MB)
    location: ${project.meta.basePathPrefix}data/temp #업로드된 파일의 임시 저장 공간
  mvc:
    pathmatch.matching-strategy: ant-path-matcher # default:path_pattern_parser
    throw-exception-if-no-handler-found: true # Not Found 처리 (default:false)
  web:
    resources.add-mappings: false # 존재하지 않는 경로를 Resource 경로로 지정 (default:true)
  redis:
    enabled: true
    host: redisc-2l1q2-kr.vpc-cdb.gov-ntruss.com
    port: 6379
    timeout: 3000
    username: lmsred01
    password: lmsred01!@
    lettuce:
      pool:
        enabled: true


springdoc:
  default-consumes-media-type: application/json # request media type, default: application/json
  default-produces-media-type: application/json # response media type, default: */*
  api-docs:
    enabled: true
    path: /api-docs # default: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger # default: /swagger-ui.html
    operations-sorter: alpha # 컨트롤러에서 정의한 api 메서드 순서, alpha(알파벳 오름차순), method(http method 순)
    tags-sorter: alpha # 태그 정렬 기준
    disable-swagger-default-url: false # disable the swagger-ui default petstore url
    doc-expansion: none

aidt:
  messageSource:
    basenames:
      - classpath:messages/message
      - classpath:messages/validation
  security:
    enabled: true

---
##############################################################################################################
# stg, prod
##############################################################################################################
spring:
  cloud.config.enabled: true
  config:
    activate.on-profile: stg, prod
    import: optional:configserver:http://config.aitextbook.co.kr/
  kafka:
    bootstrap-servers: '' ##### TODO fill this
    enabled: true

---
##############################################################################################################
# dev
##############################################################################################################
server:
  meta.textbook:
    systemTag: "[함순애]초등영어3~4학년(dev)"
    systemCode: "e4engl0sh2"
    systemType: "aidt-api-lm"
    systemDesc: "학습관리(Learning Manage)"

spring:
  cloud.config.enabled: false
  config:
    activate.on-profile: dev
  #devtools:
  #  livereload.enabled: false # view단이 바뀌었을 때 자동으로 리로드
  #  restart.enabled: false # 컨트롤러,서비스 모델단이 바뀌었을 때 프로젝트 재시작 설정
  datasource.hikari:
    driver-Class-Name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
    jdbc-url: **********************************************************************
    username: LRM_WAS
    password: "@cjswo2024"
    maximum-pool-size: 200
  kafka:
    bootstrap-servers: ************:9092,************:9092,************:9092
    enabled: true
  redis:
    enabled: true
    host: redisc-2l1q2-kr.vpc-cdb.gov-ntruss.com
    port: 6379
    timeout: 3000
    username: lmsred01
    password: lmsred01!@
    lettuce:
      pool:
        enabled: true

ncloud:
  s3.bucket-name: lms-op-dev-sto-02
  s3.content-bucket-name: lms-op-dev-sto-02

aidt.endpoint:
  aicenter:
    url: https://knt-dev.aitextbook.co.kr/api/v1/predict/
    token: devkey1
  archipin: https://cht-tutor.aitextbook.co.kr:7261
  archipinTalk: https://cht-dialog.aitextbook.co.kr:7182
  archipinWrite: https://cht-correction.aitextbook.co.kr:7271
  claBoard: https://dev-csb.aitextbook.co.kr:8081

  #archipin:   http://cht-tutor-k.aitextbook.co.kr:7261
  #archipinTalk:   http://cht-dialog-k.aitextbook.co.kr:7182
  #archipinWrite:   https://cht-correction-k.aitextbook.co.kr:7271

  userDetailApi: http://aidt-api-lm.lms-eng.svc.cluster.local/api/v1/at/user
  lw_myhm_stu_point: http://aidt-api-lw.lms-eng.svc.cluster.local/api/v1/lw/myhm/stu/point

  viewerApi:
    url: https://v01.aitextbook.co.kr/streamdocs/
    id: api-admin
    password: "Cjsworydbr12#$"

---
##############################################################################################################
# local
##############################################################################################################
server:
  port: 8010
  meta.textbook:
    systemTag: "[함순애]초등영어3~4학년"
    systemCode: "e4engl0sh2"
    systemType: "aidt-api-lm"
    systemDesc: "학습관리(Learning Manage)"

spring:
  cloud.config.enabled: false
  config:
    activate.on-profile: local
  #devtools:
  #  livereload.enabled: false # view단이 바뀌었을 때 자동으로 리로드
  #  restart.enabled: false # 컨트롤러,서비스 모델단이 바뀌었을 때 프로젝트 재시작 설정
  datasource.hikari:
    driver-Class-Name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
    #jdbc-url: ************************************************
    jdbc-url: **********************************************************************
    username: LRM_WAS
    password: "@cjswo2024"
  kafka:
    #bootstrap-servers: localhost:29092,localhost:29093,localhost:29094
    bootstrap-servers: ************:9092,************:9092,************:9092
    enabled: false


ncloud:
  s3.bucket-name: lms-op-dev-sto-02
  s3.content-bucket-name: lms-op-dev-sto-02

aidt.endpoint:
  aicenter:
    url: https://knt-dev.aitextbook.co.kr/api/v1/predict/
    token: devkey1
  archipin: http://cht-tutor.aitextbook.co.kr:7261
  archipinTalk: http://cht-dialog.aitextbook.co.kr:7182
  archipinWrite: http://cht-correction.aitextbook.co.kr:7271
  claBoard: https://dev-csb.aitextbook.co.kr:8081

  #archipin:   http://cht-tutor-k.aitextbook.co.kr:7261
  #archipinTalk:   http://cht-dialog-k.aitextbook.co.kr:7182
  #archipinWrite:   http://cht-correction-k.aitextbook.co.kr:7271

  userDetailApi: http://localhost:8010/api/v1/at/user
  lw_myhm_stu_point: http://localhost:8020/api/v1/lw/myhm/stu/point

  viewerApi:
    url: https://v01.aitextbook.co.kr/streamdocs/
    id: api-admin
    password: "Cjsworydbr12#$"
##############################################################################################################

---
##############################################################################################################
# local-dev
##############################################################################################################
server:
  port: 8010
  meta.textbook:
    systemTag: "[함순애]초등영어3~4학년"
    systemCode: "e4engl0sh2"
    systemType: "aidt-api-lm"
    systemDesc: "학습관리(Learning Manage)"

spring:
  cloud.config.enabled: false
  config:
    activate.on-profile: local-dev
  #devtools:
  #  livereload.enabled: false # view단이 바뀌었을 때 자동으로 리로드
  #  restart.enabled: false # 컨트롤러,서비스 모델단이 바뀌었을 때 프로젝트 재시작 설정
  datasource.hikari:
    driver-Class-Name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
    #jdbc-url: ************************************************
    jdbc-url: **********************************************************************
    username: LRM_WAS
    password: "@cjswo2024"
  kafka:
    bootstrap-servers: ************:9092,************:9092,************:9092
    enabled: false



ncloud:
  s3.bucket-name: lms-op-dev-sto-02
  s3.content-bucket-name: lms-op-dev-sto-02

aidt.endpoint:
  aicenter:
    url: https://knt-dev.aitextbook.co.kr/api/v1/predict/
    token: devkey1
  archipin: http://cht-tutor.aitextbook.co.kr:7261
  archipinTalk: http://cht-dialog.aitextbook.co.kr:7182
  archipinWrite: http://cht-correction.aitextbook.co.kr:7271
  claBoard: https://dev-csb.aitextbook.co.kr:8081

  #archipin:   http://cht-tutor-k.aitextbook.co.kr:7261
  #archipinTalk:   http://cht-dialog-k.aitextbook.co.kr:7182
  #archipinWrite:   http://cht-correction-k.aitextbook.co.kr:7271

  userDetailApi: http://localhost:8010/api/v1/at/user
  lw_myhm_stu_point: http://localhost:8020/api/v1/lw/myhm/stu/point

  viewerApi:
    url: https://v01.aitextbook.co.kr/streamdocs/
    id: api-admin
    password: "Cjsworydbr12#$"


---
##############################################################################################################
# port
##############################################################################################################
server:
  port: 8010
spring:
  config:
    activate.on-profile: port
