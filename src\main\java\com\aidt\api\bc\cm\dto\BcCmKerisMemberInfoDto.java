package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCmKerisMemberInfoDto {
    private String user_id;      // 사용자 ID
    private String user_name;    // 사용자 이름
    private String user_number;  // 사용자 번호
}
