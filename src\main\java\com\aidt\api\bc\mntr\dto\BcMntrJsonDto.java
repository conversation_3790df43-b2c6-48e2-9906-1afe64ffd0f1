package com.aidt.api.bc.mntr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-03-18 17:58:46
 * @modify 2024-03-18 17:58:46
 * @desc 학생학습모니터링 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcMntrJsonDto{

	@Parameter(name="코드")
	private String cd;
	
	@Parameter(name="타입")
	private String type;
	
	@Parameter(name="날짜")
	private String date;
	
	@Parameter(name="내용")
	private String message;
	
	
}
