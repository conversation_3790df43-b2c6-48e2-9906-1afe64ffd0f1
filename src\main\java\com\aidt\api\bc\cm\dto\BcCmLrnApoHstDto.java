/**
 * 
 */
package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-08-02 17:59:47
 * @modify 2024-08-02 17:59:47
 * @desc CM_학습접근이력 dto
 */

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcCmLrnApoHstDto {
	@Parameter(name = "학습접근이력ID")
	private Long lrnApoHstId;
	@Parameter(name = "사용자ID")
	private String usrId;
	@Parameter(name = "접근참조값")
	private String apoRefVl;
	@Parameter(name = "접근참조값")
	private String apoData;

	private String crtrId;
	private String crtDtm;
	private String mdfrId;
	private String mdfDtm;
	private String dbId;
}
