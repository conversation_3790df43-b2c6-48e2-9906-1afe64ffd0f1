package com.aidt.api.ea.evcom.diy.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 10:22:오전 10:22
 * @modify date 2024-03-29 10:22:오전 10:22
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class SaveDiyEvDto {

    @Parameter(name="평가명")
    @NotNull
    private String evNm;

    @Parameter(name="평가 ID")
    private long evId;

    @Parameter(name="문제 수")
    @NotNull
    private int qstCnt;

    @Parameter(name="재응시 허용 여부")
    @NotNull
    private String rtxmPmsnYn;

    @Parameter(name="평가 완료 여부")
    @NotNull
    private String evCmplYn;

    @Parameter(name="EA 평가 시험 범위 리스트")
    @NotNull
    private List<SaveDiyEvDto> eaEvTsRngeList;

    @Parameter(name="EA 평가 난이도 구성 리스트")
    @NotNull
    private List<SaveDiyEvDto> eaEvDffdCstnList;

    @Parameter(name="출제 방식(AI:AI 자동 출제, SET:난이도 지정 출제)")
    @NotNull
    private String quesMethod;

    @Parameter(name="사용 여부")
    @Builder.Default
    private String useYn = "Y";

    @Parameter(name="삭제 여부")
    @Builder.Default
    private String delYn = "N";

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="교과서 ID")
    private String txbId;
    
    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="DB ID")
    private String dbId;

    @Parameter(name="시험 범위 순번")
    private int tsRngeSeqNo;

    @Parameter(name="단원 운영 교과서 ID")
    private String luOptTxbId;
    @Parameter(name="단원 학습맵 노드 ID")
    private String luLrmpNodId;
    @Parameter(name="단원 학습맵 노드명")
    private String luLrmpNodNm;
	@Parameter(name="대단원정렬순서")
	private int luRcstnOrdn;    
    
	@Parameter(name="중단원학습맵노드ID")
	private String mluLrmpNodId;	
	@Parameter(name="중단원학습맵노드명")
	private String mluLrmpNodNm;
	@Parameter(name="중단원정렬순서")
	private int mluRcstnOrdn;
	
	@Parameter(name="소단원학습맵노드ID")
	private String sluLrmpNodId;	
	@Parameter(name="소단원학습맵노드명")
	private String sluLrmpNodNm;	
	@Parameter(name="소단원정렬순서")
	private int sluRcstnOrdn;
    
    
    @Parameter(name="평가 난이도 구분 코드")
    private String evDffdDvCd;

    @Parameter(name="평가 난이도 분포 수")
    private int evDffdDsbCnt;

    @Parameter(name="문항 ID")
    private String qtmId;

    @Parameter(name="문항 플랫폼 난이도 구분 코드")
    private String qtmDffdDvCd;

    @Parameter(name="문항 플랫폼 난이도 명")
    private String qpDffdNm;

    @Parameter(name="문항 플랫폼 대단원 코드")
    private String qpLluId;

    @Parameter(name="문항 플랫폼 대단원 명")
    private String qpLluNm;

    @Parameter(name="문항 플랫폼 차시(토픽) 코드")
    private String qpTcId;

    @Parameter(name="문항 플랫폼 차시(토픽) 명")
    private String qpTcNm;

    @Parameter(name="문항 순서")
    private int qtmOrdn;





}
