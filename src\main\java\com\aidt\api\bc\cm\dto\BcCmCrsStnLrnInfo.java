package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "CM_과정표준학습정보")
public class BcCmCrsStnLrnInfo {

	@Schema(description = "교육과정콘텐츠2단계코드")
	private String crclCtnElm2Cd;
	@Schema(description = "교과서ID")
	private String txbId;
	@Schema(description = "문제수")
	private Long qstCnt;
	@Schema(description = "콘텐츠수")
	private Long ctnCnt;
	
	private String optTxbId;
}
