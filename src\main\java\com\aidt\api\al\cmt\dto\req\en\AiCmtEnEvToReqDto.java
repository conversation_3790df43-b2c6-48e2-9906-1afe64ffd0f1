package com.aidt.api.al.cmt.dto.req.en;

import javax.validation.Valid;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.en.AiCmtEnEvToDto;
import com.aidt.api.al.cmt.dto.req.cm.N12ReqDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnEvToReqDto {

    @Valid
    @Parameter(name="영역별 분석", required=true)
    private N12ReqDto area;

    private String evId;

    private String usrId;

    public AiCmtEnEvToDto toDto() {
        return AiCmtEnEvToDto.builder().n12(this.area.toDto(AiCmtLvlCalculator.MA_TYPE_50_80))
                .build();
    }

}
