
package com.aidt.api.tl.lrnwif.stu;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.BcS3FileCheckUtil;
import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto;
import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.common.dto.TlCrclCntStdInfoDto;
import com.aidt.api.tl.lrnwif.TlLrnwIfCacheService;
import com.aidt.api.tl.lrnwif.dto.TlLrnwAtvClabdDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnAtvDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnCtnDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnPdfDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnStpDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnTlDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocAtvDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebSaveDto;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto;
import com.aidt.common.CommonDao;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.CoreUtil.Json;
import com.aidt.common.util.WebFluxUtil;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:28:18
 * @modify date 2024-01-04 14:28:18
 * @desc TlLrnwIfStu Service 교과학습 학습창연계 서비스
 */
@Slf4j
@Service
public class TlLrnwIfStuService {

	private final String MAPPER_NAMESPACE = "api.tl.lrnwif.stu.";

	@Autowired
	private WebFluxUtil webFluxUtil;

	@Value("${spring.profiles.active}")
	private String serverActive;

	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private EaAsnComService eaAsnService;
	
	@Autowired
	private TlLrnwIfCacheService lrnwIfcacheService;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Value("${aidt.endpoint.lw_myhm_stu_point:}")
	private String endpoint_lw_myhm_stu_point;

	// private String pointUrl = "/api/v1/lw/myhm/stu/point";

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
	
	@Autowired
	private BcS3FileCheckUtil bcS3FileCheckUtil;

    /**
     * 교과학습 단원차시정보 조회 서비스
     *
     * @param srhDto 조회조건
     * @param usrDvCd  로그인사용자구분코드(S:학생, T:교사)
     * @return List<TlLrnwTocAtvDto>
     */
    @Transactional
    public TlLrnwTocAtvDto selectLrnTocAtvInfo(TlLrnwTocSrhDto srhDto, String usrDvCd, String txbId) {
    	String stpDvCd = srhDto.getLrnStpDvCd();
    	List<TlLrnwTocAtvDto> lrnList = lrnwIfcacheService.selectLrnTocAtvInfo(srhDto, usrDvCd);
    	
        // 단원정보조회
    	Stream<TlLrnwTocAtvDto> stream = lrnList.stream().filter(list -> list.getTcNodId().equals(srhDto.getLrmpNodId()));
        TlLrnwTocAtvDto rtnDto = stream.findFirst().orElse(null);
        TlLrnwTocAtvDto cntDto = new TlLrnwTocAtvDto();
        
        if(rtnDto == null || srhDto.getLrmpNodId() == null) {
        	return new TlLrnwTocAtvDto();
    	} else {
    		cntDto = commonDao.select(MAPPER_NAMESPACE + "selectAtvCnt", Map.of("param", srhDto));
    		if("".equals(cntDto.getAtvClCnt()) || cntDto.getAtvClCnt().isEmpty()) {
    			rtnDto.setTxbUseYn("N");
    		} else {
    			rtnDto.setTxbUseYn("Y");
    		}
    		
    		if("".equals(cntDto.getAtvWbCnt()) || cntDto.getAtvWbCnt().isEmpty()) {
    			rtnDto.setWkbUseYn("N");
    		} else {
    			rtnDto.setWkbUseYn("Y");
    		}
    		
    		if("WB".equals(srhDto.getLrnStpDvCd())) {
    			rtnDto.setAtvTotCnt(cntDto.getAtvWbCnt());
    		} else {
    			rtnDto.setAtvTotCnt(cntDto.getAtvClCnt());
    		}
    	}
        
        //외부활동설정
        TlLrnwExtAtvSetm extAtv = commonDao.select(MAPPER_NAMESPACE + "selectExtAtvSetm", Map.of("param", srhDto));
        if(extAtv != null) {
        	extAtv.setExtrLink(commonDao.selectList(MAPPER_NAMESPACE + "selectExtLink", Map.of("param", srhDto)));
            rtnDto.setExtAtvSetm(extAtv);
        }
        // 학습도구목록 조회
        List<TlLrnwLrnTlDto> tlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTlList", Map.of("param", srhDto, "usrDvCd", usrDvCd));
        rtnDto.setLrnTlList(tlList);
        // 표준체계교육과정ID목록
        List<String> crclStdIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectNtnlCrclStdList", Map.of("param", srhDto));
        
        List<String> crclStdIdListData = new ArrayList<String>();
        
		for (String crclStdId : crclStdIdList) {
			if (crclStdId != null && !crclStdId.isEmpty()) {
            	if(!"".equals(crclStdId)) {
            		crclStdIdListData.add(crclStdId);
            	}
			}
		}
        rtnDto.setCrclStdIdList(crclStdIdListData);

        // 마지막학습활동ID 목록조회
        if (srhDto.getLrnAtvId() != null && !"".equals(srhDto.getLrnAtvId())) {
        	if(srhDto.getLrnStpDvCd().equals("WB")) {
        		rtnDto.setWbStrAtvId(srhDto.getLrnAtvId());
        		rtnDto.setStrAtvId(this.getLastLrnAtvId(srhDto,"CL"));
        	} else {
        		rtnDto.setStrAtvId(srhDto.getLrnAtvId());
        		rtnDto.setWbStrAtvId(this.getLastLrnAtvId(srhDto,"WB"));
        	}
        	if(srhDto.getPreviewYn().equals("Y")) {
        		rtnDto.setLcknYn("Y"); // 학습상태가 저장되지 않게 하기 위함
        	}
        } else {
            rtnDto.setStrAtvId(this.getLastLrnAtvId(srhDto,"CL"));
            rtnDto.setWbStrAtvId(this.getLastLrnAtvId(srhDto,"WB"));
        }
        srhDto.setLrnStpDvCd(stpDvCd);
        
        // 학습단계/학습활동/동영상 정보조회
        List<Map<String, Object>> ctnMtdList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvCtnMtdList", Map.of("param", srhDto, "usrDvCd", usrDvCd));

        // 표준체계교육과정ID목록
        List<Map<String, String>> crclStdIdList2 = commonDao.selectList(MAPPER_NAMESPACE + "selectNtnlCrclStdList2", Map.of("param", srhDto));
        
        // 클래스보드 정보 체크
        List<Map<String, Object>> clabdInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectClabdInfo", Map.of("param", srhDto));
        
        // 미등록 클래스보드 정보 있을 경우
        if(clabdInfo.size() > 0) {
        	for(Map<String, Object> cla : clabdInfo) {
            	commonDao.insert(MAPPER_NAMESPACE + "insertTlSbcLrnAtvClabd", Map.of("param", srhDto,"lrnAtvId",cla.get("LRN_ATV_ID"), "clabdSmlId" ,cla.get("CLABD_SML_ID"), "txbId", txbId));
        	}
        }

        // 클래스보드정보 목록조회
        List<Map<String, Object>> clabdList = commonDao.selectList(MAPPER_NAMESPACE + "selectClabdList", Map.of("param", srhDto));
        
        if("T".equals(usrDvCd)) {
        	srhDto.setAllDatYn("Y");
        }else {
        	srhDto.setAllDatYn("N");
        }
        //수업자료 조회
        rtnDto.setBsMtrlList(commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlBsList", srhDto));
        rtnDto.setLmMtrlList(commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlLmList", srhDto));
        for(TlLsnMtrlDto lmsMtrl : rtnDto.getLmMtrlList()) {
        	if("VI".equals(lmsMtrl.getLsnMtrlTpCd())) {
        		lmsMtrl.setFlePthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, "/"+lmsMtrl.getFlePthNm()));
        	}
        }

        List<TlLrnwLrnStpDto> lrnStpList = new ArrayList<TlLrnwLrnStpDto>();
        List<TlLrnwLrnAtvDto> lrnAtvList = new ArrayList<TlLrnwLrnAtvDto>();
        String bfLrnStpId = null;
        String cuLrnStpId = null;
        String bfLrnAtvId = null;
        String cuLrnAtvId = null;

        for(Map<String, Object> mapDto: ctnMtdList) {
            cuLrnStpId = mapDto.get("LRN_STP_ID").toString();
            cuLrnAtvId = String.valueOf(mapDto.get("LRN_ATV_ID"));
            if (bfLrnStpId == null || !bfLrnStpId.equals(cuLrnStpId)) {
                TlLrnwLrnStpDto stpDto = new TlLrnwLrnStpDto();
                stpDto.setLrnStpId(mapDto.get("LRN_STP_ID").toString());
                stpDto.setLrnStpDvCd(mapDto.get("LRN_STP_DV_CD").toString());
                stpDto.setLrnStpCd(mapDto.get("LRN_STP_CD").toString());
                stpDto.setLrnStpNm(mapDto.get("LRN_STP_NM").toString());
                lrnStpList.add(stpDto);
            }
            if (bfLrnAtvId == null || !bfLrnAtvId.equals(cuLrnAtvId)) {
                TlLrnwLrnAtvDto atvDto = new TlLrnwLrnAtvDto();
                String atvCtnTpCd = mapDto.get("CTN_TP_CD").toString(); // QU=문항, HT=HTML5, PL=동영상, EX=평가지
                atvDto.setLrnStpId(cuLrnStpId);
                atvDto.setLrnAtvId(cuLrnAtvId);
                atvDto.setCtnTpCd(atvCtnTpCd);
                lrnAtvList.add(atvDto);
            }
            bfLrnStpId = cuLrnStpId;
            bfLrnAtvId = cuLrnAtvId;
        }

        // 학습단계정보
        lrnStpList.forEach(list1 -> {
            List<TlLrnwLrnAtvDto> atvList = new ArrayList<TlLrnwLrnAtvDto>();
            // 학습활동정보
            lrnAtvList.stream().filter(atv -> list1.getLrnStpId().equals(atv.getLrnStpId()))
                .forEach(list2 -> {
                    TlLrnwLrnAtvDto atvDto = new TlLrnwLrnAtvDto();
                    String lrnAtvId = String.valueOf(list2.getLrnAtvId());
                    Optional<Map<String, Object>> ctnMtd = ctnMtdList.stream().filter(dto3 -> list2.getLrnStpId().equals(dto3.get("LRN_STP_ID").toString())
                        && list2.getLrnAtvId().equals(dto3.get("LRN_ATV_ID").toString())).findFirst();
                    TlLrnwLrnCtnDto ctnDto = new TlLrnwLrnCtnDto();
                    ctnMtdList.stream().filter(dto4 -> list2.getLrnAtvId().equals(dto4.get("LRN_ATV_ID").toString()))
                            .forEach(list4 -> {
                                if (list4.get("CTN_META_DATA_ID") != null) {
                                    String ctnTpCd = list4.get("CTN_TP_CD") == null ? "": list4.get("CTN_TP_CD").toString();
                                    ctnDto.setMetaDataId(list4.get("CTN_META_DATA_ID") == null ? "": list4.get("CTN_META_DATA_ID").toString());
                                    ctnDto.setCtnTpCd(ctnTpCd);
                                    ctnDto.setCtnCd(list4.get("CTN_CD") == null ? "": list4.get("CTN_CD").toString());
                                    ctnDto.setLrnCofmTmScnt(list4.get("LRN_COFM_TM_SCNT") == null ? "": list4.get("LRN_COFM_TM_SCNT").toString());
                                    ctnDto.setCtnDffdDvCd(list4.get("CTN_DFFD_DV_CD") == null ? "": list4.get("CTN_DFFD_DV_CD").toString());
                                    ctnDto.setLrnTpCd(list4.get("LRN_TP_CD") == null ? "": list4.get("LRN_TP_CD").toString());
                                    ctnDto.setAtvQtmCnt(list4.get("ATV_QTM_CNT") == null ? "": list4.get("ATV_QTM_CNT").toString());
                                    // ctnDto.setCmplBsCd(list4.get("CMPL_BS_CD") == null ? "":list4.get("CMPL_BS_CD").toString());
                                    // ctnDto.setOcrUseYn(list4.get("OCR_USE_YN") == null ? "":list4.get("OCR_USE_YN").toString());
                                    // ctnDto.setFmlUseYn(list4.get("FML_USE_YN") == null ? "":list4.get("FML_USE_YN").toString());
                                    // ctnDto.setSttUseYn(list4.get("STT_USE_YN") == null ? "":list4.get("STT_USE_YN").toString());
                                    if (TlConstUtil.LRN_CTN_TP_CD_PL.equals(ctnTpCd)) { // 동영상은 아래 내용을 고정값으로 설정한다.
                                        // 2024.06.15 동영상이 고/중/저 화질등록이 필수였으나, 콘텐츠에 따라 일부 화질만 제공하는 것으로 변경됨
                                        // 동영상파일명이 공란이면 해당 화질미제공으로 설저한다.
                                        String v480p = list4.get("VDO_FLE_NM_480P") == null ? "": list4.get("VDO_FLE_NM_480P").toString(); /* 동영상파일명480P */
                                        String v720p = list4.get("VDO_FLE_NM_720P") == null ? "": list4.get("VDO_FLE_NM_720P").toString(); /* 동영상파일명720P */
                                        String v1280p = list4.get("VDO_FLE_NM_1280P") == null ? "": list4.get("VDO_FLE_NM_1280P").toString(); /* 동영상파일명1280P */
                                        ctnDto.setVdsLRsln(0 == v480p.trim().length() ? "" : "media/01_video_480.mp4"); // 고정값설정
                                        ctnDto.setVdsMRsln(0 == v720p.trim().length() ? "" : "media/01_video_720.mp4"); // 고정값설정
                                        ctnDto.setVdsHRsln(0 == v1280p.trim().length() ? "" : "media/01_video_1080.mp4"); // 고정값설정
                                        ctnDto.setSttlSmiFleNm("media/01_caption.smi"); // 고정값설정
                                        //ctnDto.setSttlVttFleNm("media/01_caption.vtt"); // 고정값설정
                                        ctnDto.setSttlVttFleNm(bcS3FileCheckUtil.isFileExists(BUCKET_NAME, ctnMtd.get().get("CDN_PTH_NM").toString() + "media/01_caption.vtt")? "media/01_caption.vtt":""); // 고정값설정
                                        ctnDto.setScrbFleNm("media/01_script.txt"); // 고정값설정
                                        ctnDto.setVceFleNm("media/01_audio.mp3"); // 고정값설정
                                        ctnDto.setThbFleNm("images/poster.png"); // 고정값설정
                                    }
                                }
                            });
                    // 클래스보드URL리스트 작성
                    List<TlLrnwAtvClabdDto> claList = new ArrayList<TlLrnwAtvClabdDto>();
                    clabdList.stream().filter(clabdMap -> lrnAtvId.equals((clabdMap.get("LRN_ATV_ID")).toString()))
                            .forEach(clabdMap2 -> {
                                TlLrnwAtvClabdDto claDto = new TlLrnwAtvClabdDto();
                                claDto.setClabdLrgsId(clabdMap2.get("CLABD_LRGS_ID") != null ? clabdMap2.get("CLABD_LRGS_ID").toString() : "");
                                claDto.setClabdSmlId(clabdMap2.get("CLABD_SML_ID") != null ? clabdMap2.get("CLABD_SML_ID").toString() : "");
                                claDto.setClabdLrgsNm(clabdMap2.get("CLABD_LRGS_NM") != null ? clabdMap2.get("CLABD_LRGS_NM").toString() : "");
                                claDto.setClabdSmlNm(clabdMap2.get("CLABD_SML_NM") != null ? clabdMap2.get("CLABD_SML_NM").toString() : "");
                                claDto.setClabdTyp(clabdMap2.get("CLABD_TYP") != null ? clabdMap2.get("CLABD_TYP").toString() : "");
                                claDto.setClabdUrl(clabdMap2.get("CLABD_URL") != null ? clabdMap2.get("CLABD_URL").toString() : "");
                                claList.add(claDto);
                            });
                    atvDto.setCtnMtd(ctnDto);
                    atvDto.setLrnAtvId(lrnAtvId);
                    atvDto.setLrnAtvNm(ctnMtd.get().get("LRN_ATV_NM").toString());
                    atvDto.setClsBrdUrl(ctnMtd.get().get("CLS_BRD_URL") == null ? "": ctnMtd.get().get("CLS_BRD_URL").toString());
                    atvDto.setClsbdList(claList);
                    atvDto.setCtnTpCd(ctnMtd.get().get("CTN_TP_CD") == null ? "": ctnMtd.get().get("CTN_TP_CD").toString());
                    atvDto.setLrnTmScnt(ctnMtd.get().get("LRN_TM_SCNT") == null ? "": ctnMtd.get().get("LRN_TM_SCNT").toString());
                    atvDto.setLrnStCd(ctnMtd.get().get("LRN_ST_CD").toString());
                    atvDto.setRcstnOrdn(ctnMtd.get().get("RCSTN_ORDN").toString());
                    atvDto.setTcrCtnYn(ctnMtd.get().get("TCR_CTN_YN").toString());
                    // atvDto.setCtnCd(ctnMtd.get().get("CTN_CD") == null ? "": ctnMtd.get().get("CTN_CD").toString());
                    atvDto.setEvId("");
                    atvDto.setQtmId("");

                    //학습창에서 형성평가 차시 평가 재응시여부 확인 하기 위해 추가
                    atvDto.setEvUseYn("");
                    atvDto.setEvDelYn("");
                    atvDto.setEvLcknYn("");
                    atvDto.setEvTxmStrDtm("");
                    atvDto.setEvTxmEndDtm("");
                    atvDto.setEvTxmPtmeSetmYn("");
                    atvDto.setEvRtxmPmsnYn("");
                    atvDto.setEvDtlDvNm("");

                    if(TlConstUtil.LRN_CTN_TP_CD_EX.equals(atvDto.getCtnTpCd())) { // 평가
                        atvDto.setEvId(ctnMtd.get().get("EV_ID") == null ? "": ctnMtd.get().get("EV_ID").toString());
                        
                        atvDto.setEvUseYn(ctnMtd.get().get("EV_USE_YN") == null ? "": ctnMtd.get().get("EV_DEL_YN").toString());
                        atvDto.setEvDelYn(ctnMtd.get().get("EV_DEL_YN") == null ? "": ctnMtd.get().get("EV_DEL_YN").toString());
                        atvDto.setEvLcknYn(ctnMtd.get().get("EV_LCKN_YN") == null ? "": ctnMtd.get().get("EV_LCKN_YN").toString());
                        atvDto.setEvTxmStrDtm(ctnMtd.get().get("EV_TXM_STR_DTM") == null ? "": ctnMtd.get().get("EV_TXM_STR_DTM").toString());
                        atvDto.setEvTxmEndDtm(ctnMtd.get().get("EV_TXM_END_DTM") == null ? "": ctnMtd.get().get("EV_TXM_END_DTM").toString());
                        atvDto.setEvTxmPtmeSetmYn(ctnMtd.get().get("EV_TXM_PTME_SETM_YN") == null ? "": ctnMtd.get().get("EV_TXM_PTME_SETM_YN").toString());
                        atvDto.setEvRtxmPmsnYn(ctnMtd.get().get("EV_RTXM_PMSN_YN") == null ? "": ctnMtd.get().get("EV_RTXM_PMSN_YN").toString());
                        atvDto.setEvDtlDvNm(ctnMtd.get().get("EV_DTL_DV_NM") == null ? "": ctnMtd.get().get("EV_DTL_DV_NM").toString());
                        
                    } else if(TlConstUtil.LRN_CTN_TP_CD_QU.equals(atvDto.getCtnTpCd())) { // 문항
                        atvDto.setQtmId(ctnMtd.get().get("CTN_CD") == null ? "": ctnMtd.get().get("CTN_CD").toString());
                    }
                    // atvDto.setCtnUrl(ctnMtd.get().get("CDN_PTH_NM") == null ? "": ctnMtd.get().get("CDN_PTH_NM").toString());
                    // 콘텐츠URL 작성처리
                    String ctnUrl = "";
                    if (ctnMtd.get().get("CDN_PTH_NM") != null) {
                    	if(ctnMtd.get().get("TCR_CTN_YN").toString().equals("Y") && ctnMtd.get().get("CTN_TP_CD").toString().equals("DM")) {
                    		ctnUrl = "https://v01.aitextbook.co.kr/streamdocs/view/sd?streamdocsId=" + ctnMtd.get().get("STR_FLE_NM").toString();
                    	} else if (ctnMtd.get().get("TCR_CTN_YN").toString().equals("Y") && (ctnMtd.get().get("CTN_TP_CD").toString().equals("BW") || ctnMtd.get().get("CTN_TP_CD").toString().equals("LK"))) {
                    		if("Y".equals(srhDto.getPreviewYn())) {
                    			ctnUrl = "/annLrnMtrlPopup?lrmpNodId="+ ctnMtd.get().get("LRMP_NOD_ID").toString() + "&tcrRegCtnId=" + ctnMtd.get().get("LRN_ATV_ID") + "&previewYn=Y";	
                    		}else {
                    			ctnUrl = "/annLrnMtrlPopup?lrmpNodId="+ ctnMtd.get().get("LRMP_NOD_ID").toString() + "&tcrRegCtnId=" + ctnMtd.get().get("LRN_ATV_ID");
                    		}
                    	}else {
                    		String strFleNm = ctnMtd.get().get("STR_FLE_NM") != null ? ctnMtd.get().get("STR_FLE_NM").toString() : "";
                            String tmpCdn = ctnMtd.get().get("CDN_PTH_NM").toString();
                    		if(ctnMtd.get().get("TCR_CTN_YN").toString().equals("Y") && ctnMtd.get().get("CTN_TP_CD").toString().equals("VI")) {
                    			tmpCdn = "/" + tmpCdn;
                    		}
                            ctnUrl = TlCmUtil.makeCtnCdnUrl(BUCKET_NAME, tmpCdn, strFleNm);
                    	}
                    } else { 
                    	if (ctnMtd.get().get("TCR_CTN_YN").toString().equals("Y") && ctnMtd.get().get("CTN_TP_CD").toString().equals("LK")) {
                    		if("Y".equals(srhDto.getPreviewYn())) {
                    			ctnUrl = "/annLrnMtrlPopup?lrmpNodId="+ ctnMtd.get().get("LRMP_NOD_ID").toString() + "&tcrRegCtnId=" + ctnMtd.get().get("LRN_ATV_ID") + "&previewYn=Y";	
                    		}else {
                    			ctnUrl = "/annLrnMtrlPopup?lrmpNodId="+ ctnMtd.get().get("LRMP_NOD_ID").toString() + "&tcrRegCtnId=" + ctnMtd.get().get("LRN_ATV_ID");
                    		}
                    	}
                	} 
                    atvDto.setCtnUrl(ctnUrl);
                    // 교육과정표준체계ID목록조회
                    List<String> crclCtnStdList2 = new ArrayList<String>();
                    crclStdIdList2.stream()
                        .filter(crclDto1 -> crclDto1.get("LRN_ATV_ID") != null && lrnAtvId.equals(String.valueOf(crclDto1.get("LRN_ATV_ID"))))
                            .forEach(crclDto2 -> {
                                if (crclDto2.get("EDU_CRS_CN_CD") != null) {
                                	if(!"".equals(crclDto2.get("EDU_CRS_CN_CD"))) {
                                		crclCtnStdList2.add(crclDto2.get("EDU_CRS_CN_CD").toString());
                                	}
                                }
                        });
                    atvDto.setCrclCtnStdIdList(crclCtnStdList2);
                    atvList.add(atvDto);
                    list1.setLrnAtvList(atvList);

                });
            });
        rtnDto.setLrnStpList(lrnStpList);
        return rtnDto;
    }

	/**
	 * 마지막 학습활동ID 취득처리
	 * 
	 * @param srhDto
	 * @return
	 */
	@Transactional(readOnly = true)
	private String getLastLrnAtvId(TlLrnwTocSrhDto srhDto, String stpDvCd) {
		String lstLrnAtvId = "";
		String nlLrnAtvId = "";
		String endLrnAtvId = "";
		srhDto.setLrnStpDvCd(stpDvCd);
		
		List<Map<String, Object>> mapList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvLst",
				Map.of("param", srhDto));
		
		if(mapList == null || mapList.isEmpty() || mapList.size() ==0) {
			lstLrnAtvId = "";
		} else {
			for (Map<String, Object> item : mapList) {
				String lastYn = item.get("LAST_YN").toString();
				String lrnStCd = item.get("LRN_ST_CD").toString();
				String lrnAtvId = item.get("LRN_ATV_ID").toString();
				
				endLrnAtvId = lrnAtvId;
				
				if ("".equals(nlLrnAtvId) && (TlConstUtil.LRN_ST_NL.equals(lrnStCd) || TlConstUtil.LRN_ST_DL.equals(lrnStCd))) {
					nlLrnAtvId = lrnAtvId;
				}
				
				if ("Y".equals(lastYn)) {
					// 마지막 학습한 상태가 미학습이거나 학습중인중인 경우
					if (TlConstUtil.LRN_ST_NL.equals(lrnStCd) || TlConstUtil.LRN_ST_DL.equals(lrnStCd)) {
						lstLrnAtvId = lrnAtvId;  
						break;
					} 
				}
			}
			
			if ("".equals(lstLrnAtvId)) {
				if(!"".equals(nlLrnAtvId)) {
					lstLrnAtvId = nlLrnAtvId;
				} else {
					lstLrnAtvId = endLrnAtvId;
				}
			}
		}

		return lstLrnAtvId;
	}

	/**
	 * 학습활동 저장처리
	 * 
	 * @param saveDto TlLrnwAtvSaveDto
	 * @param usrDvCd 로그인사용자구분코드(S:학생, T:교사)
	 * @return
	 */
	@Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="longCache", key="'tl:' + #saveDto.optTxbId + ':txbTcList:' + #saveDto.lrnUsrId", cacheManager="aidtCacheManager")
    })
	public Map<String, Object> saveLrnAtvInfo(TlLrnwAtvSaveDto saveDto, String usrDvCd) {
		Integer regCnt = 0;
		boolean checkAtvId = "Y".equals(saveDto.getTcrCtnYn()) ? true : false;
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		Map<String, Object> lrnAtvMap = new HashMap<String, Object>();

		if(!StringUtils.isNumeric(saveDto.getLrnAtvId())) {
			saveDto.setTcrCtnYn("Y");
		} else {
			saveDto.setTcrCtnYn("N");
			if(checkAtvId) {
				String orgnLrmpNodId = commonDao.select(MAPPER_NAMESPACE + "selectOrgnLrmpNodId", saveDto);
				if(orgnLrmpNodId != null) {
					saveDto.setLrnTcId(saveDto.getLrmpNodId());
					saveDto.setLrmpNodId(orgnLrmpNodId);
				}
			}
		}

		if("Y".equals(saveDto.getTcrCtnYn())) {
			lrnAtvMap = commonDao.select(MAPPER_NAMESPACE + "selectTcrCtnStInfo", saveDto);
		} else {
			lrnAtvMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvStInfo", saveDto);
		}

		long recCnt = lrnAtvMap.get("CNT") != null ? Long.valueOf(lrnAtvMap.get("CNT").toString()) : 0;
		String lrnStCd = lrnAtvMap.get("LRN_ST_CD") != null ? lrnAtvMap.get("LRN_ST_CD").toString()
				: TlConstUtil.LRN_ST_NL;
		String lrnStpDvCd = lrnAtvMap.get("LRN_STP_DV_CD") != null ? lrnAtvMap.get("LRN_STP_DV_CD").toString()
				: "";

		saveDto.setLrnStpDvCd(lrnStpDvCd);
		if (recCnt == 0) { // 등록처리
			saveDto.setDbId(DB_ID);
			if("Y".equals(saveDto.getTcrCtnYn())) {
				regCnt = commonDao.insert(MAPPER_NAMESPACE + "insertTcrCtn", saveDto);
			} else {
				regCnt = commonDao.insert(MAPPER_NAMESPACE + "insertLrnAtv", saveDto);
			}
		} else { // 갱신처리
			if("Y".equals(saveDto.getTcrCtnYn())) {
				regCnt = commonDao.update(MAPPER_NAMESPACE + "updateTcrCtn", saveDto);
			} else {
				regCnt = commonDao.update(MAPPER_NAMESPACE + "updateLrnAtv", saveDto);
			}
		}
		if("".equals(lrnStpDvCd)) {
			if("Y".equals(saveDto.getTcrCtnYn())) {
				lrnAtvMap = commonDao.select(MAPPER_NAMESPACE + "selectTcrCtnStInfo", saveDto);
			} else {
				lrnAtvMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvStInfo", saveDto);
			}
			lrnStpDvCd = lrnAtvMap.get("LRN_STP_DV_CD") != null ? lrnAtvMap.get("LRN_STP_DV_CD").toString()
					: "";
			saveDto.setLrnStpDvCd(lrnStpDvCd);
		}
		log.debug(">> regCnt:{}", regCnt);

		return saveEaEduStdInfo(rtnMap, lrnStCd, saveDto, usrDvCd, lrnStpDvCd);
	}
	
	/**
	 * 과제, 국가표준교육체계 저장처리
	 * 
	 * @param saveDto TlLrnwAtvSaveDto
	 * @param usrDvCd 로그인사용자구분코드(S:학생, T:교사)
	 * @return
	 */
	@Transactional
	public Map<String, Object> saveEaEduStdInfo(Map<String, Object> rtnMap, String lrnStCd, TlLrnwAtvSaveDto saveDto, String usrDvCd, String lrnStpDvCd) {

		List<String> eduList1 = new ArrayList<String>();
		List<String> eduList2 = new ArrayList<String>();
		List<Map<String, Object>> rateEduList = new ArrayList<Map<String, Object>>();
		//List<Map<String, Object>> rateEduTotList = new ArrayList<Map<String, Object>>();

		rtnMap.put("saved", "ok");
		rtnMap.put("atvEduStdList", eduList1);
		rtnMap.put("evEduStdList", eduList2);
		rtnMap.put("rateEduStdList", rateEduList);
		//rtnMap.put("rateEduTotList", rateEduTotList);
		rtnMap.put("asnId", "");

		// 학생이면서 학습완료될때 Rate취득
		if (TlConstUtil.USR_DIV_STU.equals(usrDvCd) && TlConstUtil.LRN_ST_CL.equals(saveDto.getLrnStCd())) {
			// 교육과정표준체계ID별 rate정보취득
			List<Map<String, Object>> eduStdList3 = commonDao.selectList(MAPPER_NAMESPACE + "selectNtnlCrclStdCn",
					Map.of("srchUnit", "CD", "optTxbId", saveDto.getOptTxbId(), "lrmpNodId", saveDto.getLrmpNodId(),
							"lrnAtvId", saveDto.getLrnAtvId(), "lrnUsrId", saveDto.getLrnUsrId()));

			eduStdList3.stream().forEach(dto -> {
				Map<String, Object> rtMap = new HashMap<String, Object>();
				rtMap.put("eduCrsCnCd", dto.get("EDU_CRS_CN_CD").toString());
				rtMap.put("rate", dto.get("RATE"));
				rateEduList.add(rtMap);
			});
			rtnMap.put("rateEduStdList", rateEduList);

		}

		// [임시] 2024-07-03 차시 기준 표준체계ID 리스트
//		if (TlConstUtil.USR_DIV_STU.equals(usrDvCd) && TlConstUtil.LRN_ST_CL.equals(saveDto.getLrnStCd())) {
//			// 교육과정표준체계ID별 rate정보취득
//			List<Map<String, Object>> eduStdList4 = commonDao.selectList(MAPPER_NAMESPACE + "selectNtnlCrclStdCn1",
//					Map.of("srchUnit", "CD", "optTxbId", saveDto.getOptTxbId(), "lrmpNodId", saveDto.getLrmpNodId(),
//							"lrnAtvId", saveDto.getLrnAtvId(), "lrnUsrId", saveDto.getLrnUsrId()));
//
//			eduStdList4.stream().forEach(dto -> {
//				Map<String, Object> rtMap = new HashMap<String, Object>();
//				rtMap.put("eduCrsCnCd", dto.get("EDU_CRS_CN_CD").toString());
//				rtMap.put("rate", dto.get("RATE"));
//				rateEduTotList.add(rtMap);
//			});
//			rtnMap.put("rateEduTotList", rateEduTotList);
//
//		}
		
		// 과제테이블에 완료건수 UPDATE
				String cmplLrnStpDvCd = "";
				String asnId = "";
				String smtCmplYn = "";

				if (TlConstUtil.LRN_ST_CL.equals(saveDto.getLrnStCd())) { // 상태가 완료로 갱신되는 경우
					Map<String, Object> chkMap = commonDao.select(MAPPER_NAMESPACE + "selectEvLrnCmplInfo", saveDto);
					if (chkMap != null && "N".equals(chkMap.get("SMT_CMPL_YN"))) {
						long lrnCnt = (long) chkMap.get("LRN_CNT"); // 완료학습수
						long ttlLrnCnt = (long) chkMap.get("TTL_LRN_CNT"); // 총학습수
						smtCmplYn = lrnCnt >= ttlLrnCnt ? "Y" : "";
						cmplLrnStpDvCd = chkMap.get("LRN_STP_DV_CD") == null ? "" : chkMap.get("LRN_STP_DV_CD").toString();
						asnId = chkMap.get("ASN_ID") == null ? "" : chkMap.get("ASN_ID").toString();
						commonDao.update(MAPPER_NAMESPACE + "updateEaAsnSmt", Map.of("param", saveDto, "asnId",
								chkMap.get("ASN_ID"), "cmplLrnCnt", lrnCnt, "smtCmplYn", smtCmplYn));
						//과제 알림 관련
						EaAsnStuDto eaDto = new EaAsnStuDto();
						eaDto.setAsnId(Integer.parseInt(chkMap.get("ASN_ID").toString()));
						eaDto.setAsnLrnTp("TL");
						eaDto.setAsnLrnTpNm("우리 반 수업");
						eaAsnService.selectAllSmt(eaDto);
					}
				}

		// 이미완료된 데이터 혹인 교사의 경우는 후속처리는 실행하지 않는다.
		if (TlConstUtil.USR_DIV_TCR.equals(usrDvCd) || TlConstUtil.LRN_ST_CL.equals(lrnStCd)) {
			return rtnMap;
		}
		

		/*
		 * // 학습활동완료후 교육과정표준체계ID목록 조회 List<Map<String, String>> eduStdList1 =
		 * commonDao.selectList(MAPPER_NAMESPACE + "selectNtnlCrclStdCn",
		 * Map.of("srchUnit","ATV", "optTxbId",saveDto.getOptTxbId(), "lrmpNodId",
		 * saveDto.getLrmpNodId(), "lrnAtvId", saveDto.getLrnAtvId(), "lrnUsrId",
		 * saveDto.getLrnUsrId())); // 해당 차시의 학습활동에 포함되어, 해당 교과과정에서 학습완료된 교육과정표준체계ID를 반환
		 * eduStdList1.stream().filter(dto -> dto.get("CNT_NL") != null &&
		 * "0".equals(String.valueOf(dto.get("CNT_NL")))) .forEach(dto -> {
		 * eduList1.add(dto.get("EDU_CRS_CN_CD").toString()); });
		 * rtnMap.put("atvEduStdList", eduList1);
		 */
		// 과제완료시 완료된 교육과정표준체계ID 목록 조회
		if (!"".equals(cmplLrnStpDvCd) && "Y".equals(smtCmplYn)) {
			List<Map<String, String>> eduStdList2 = commonDao.selectList(MAPPER_NAMESPACE + "selectNtnlCrclStdCn",
					Map.of("srchUnit", "STP", "optTxbId", saveDto.getOptTxbId(), "lrmpNodId", saveDto.getLrmpNodId(),
							"lrnStpDvCd", cmplLrnStpDvCd));
			// 해당 차시의 학습단계에 포함된 모든 교육과정표준체계ID를 반환
			eduStdList2.stream().forEach(dto -> {
				eduList2.add(dto.get("EDU_CRS_CN_CD").toString());
			});
			rtnMap.put("evEduStdList", eduList2);
			rtnMap.put("asnId", asnId);
		}

		return rtnMap;
	}

	/**
	 * 차시내 학습상태 정보를 취득
	 * 
	 * @return Map<String, Object> {LRN_STP_DV_CD: String (학습단계구분), CMPL_CL_YN:
	 *         String (개념학습완료여부), CMPL_EX_YN: String (평가완료여부), CMPL_WB_YN: String
	 *         (익힘완료책여부), LLU_NOD_NM: String (대단원노드ID), LLU_NOD_NM: String (대단원노드명),
	 *         LLU_COMPL_YN: String (대단원완료여부), ASN_ID: long (과제ID), ASN_smt_cmpl_yn:
	 *         (과제완료여부) }
	 */
	@Transactional(readOnly = true)
	public Map<String, Object> getLrmpNodInfo(TlLrnwAtvSaveDto saveDto) {
		Map<String, Object> rtnMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvStpDvInfo", saveDto);
		// 마이홈 포인트지급조건변경 대응 (24.05.31)
		// Map<String, Object> lluNodMap = commonDao.select(MAPPER_NAMESPACE +
		// "selectLrnLluNodInfo", saveDto);
		// Map<String, Object> asnMap = commonDao.select(MAPPER_NAMESPACE +
		// "selectEvLrnCmplInfo", saveDto);
		// rtnMap.putAll(lluNodMap);
		/*
		 * String asnSmtCmplYn = "N"; // 과제제출완료여부 if (asnMap != null &&
		 * asnMap.get("ASN_ID") != null) { asnSmtCmplYn =
		 * asnMap.get("smt_cmpl_yn").toString(); long asnId = (long)
		 * asnMap.get("ASN_ID"); // 과제ID rtnMap.put("ASN_ID", asnId); }
		 * rtnMap.put("ASN_smt_cmpl_yn", asnSmtCmplYn);
		 */
		return rtnMap;
	}

	/**
	 * Myhome Api호출처리
	 * 
	 * @param accessToken
	 * @param paramMap
	 * @return
	 * @throws JsonProcessingException
	 */
	public Map<String, Object> callMyhmApi(String accessToken, Map<String, String> paramMap)
			throws JsonProcessingException {

		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Authorization", "Bearer " + accessToken);
		httpHeaders.add("Content-Type", "application/json");

		String jsonString = new ObjectMapper().writeValueAsString(paramMap);
		String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
		return CoreUtil.Json.jsonString2Map(post);
	}

	/**
     * 교과서/익힘책 PDF목록취득
     * @param srhDto
     * @return
	 * @throws JsonProcessingException 
	 * @throws JsonMappingException 
     */
    @Transactional(readOnly = true)
    public List<TlLrnwLrnPdfDto> selectLrntxtwkbList(TlLrnwTxbWebDto srhDto) throws JsonMappingException, JsonProcessingException{
        // 교과서 PDF 목록취득

        List<TlLrnwLrnPdfDto> pdfData = commonDao.selectList(MAPPER_NAMESPACE + "selectLrntxtwkbList",  srhDto);

        // 2024-06-05 교육과정콘텐츠표준 정보 조회
        List<TlCrclCntStdInfoDto> crclCntStdInfoData = commonDao.selectList(MAPPER_NAMESPACE + "selectCrclCntStdInfoList",  srhDto);
        if(pdfData != null && pdfData.size() > 0 && crclCntStdInfoData != null && crclCntStdInfoData.size() > 0) {
        	pdfData.get(0).setCrclCntStdInfoList(crclCntStdInfoData);
        }
        
        
		for (TlLrnwLrnPdfDto dto : pdfData) {
//            String cdnPath = dto.getTnteCdnPthNm();
//            String jsonData = "";
//            if (!cdnPath.isEmpty()){
//            	if (BcCmUtil.isFileExists(BUCKET_NAME, cdnPath)) {
//            		try {
//            			dto.setCanvasJsonData(BcCmUtil.s3JsonFileReader(BUCKET_NAME, cdnPath));	
//            		}catch(IOException e) {
//                		dto.setAnnxFleId(0);
//                		dto.setAnnxId(0);
//                		dto.setTnteCdnPthNm("");
//            		}
//            	}else {
//            		dto.setAnnxFleId(0);
//            		dto.setAnnxId(0);
//            		dto.setTnteCdnPthNm("");
//            	}
//            }
            dto.setCdnPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getCdnPthNm()));
		}
		
        return pdfData;
    }

	/**
	 * 교과서/익힘책 필기 정보 저장
	 * 
	 * @param saveDto TlLrnwTxbWebSaveDto
	 * @return
	 */
	@Transactional
	public int saveLrntxtwkbInfo(TlLrnwTxbWebSaveDto saveDto) {
		Integer regCnt = 0;

		regCnt = commonDao.insert(MAPPER_NAMESPACE + "insertLrntxtwkb", saveDto);

		return regCnt;
	}
	
	 /**
     * 학생 학습활동 결과물 조회 서비스
     *
     * @param srhDto 조회조건
     * @param usrDvCd  로그인사용자구분코드(S:학생, T:교사)
     * @return List<TlLrnwTocAtvDto>
     */
    @Transactional(readOnly = true)
    public TlLrnwLrnAtvDto selectLrnTocAtvInfoResult(TlLrnwTocSrhDto srhDto, String usrDvCd) {


		// 학습단계/학습활동/동영상 정보조회
		Map<String, Object> mapDto = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvCtnMtdList",
				Map.of("param", srhDto, "usrDvCd", usrDvCd));

		// 클래스보드정보 목록조회
		List<Map<String, Object>> clabdList = commonDao.selectList(MAPPER_NAMESPACE + "selectClabdList",
				Map.of("param", srhDto));

		if ("T".equals(usrDvCd)) {
			srhDto.setAllDatYn("Y");
		} else {
			srhDto.setAllDatYn("N");
		}

		TlLrnwLrnAtvDto atvDto = new TlLrnwLrnAtvDto();
		String atvCtnTpCd = mapDto.get("CTN_TP_CD").toString(); // QU=문항, HT=HTML5, PL=동영상, EX=평가지
		atvDto.setLrnStpId(mapDto.get("LRN_STP_ID").toString());
		atvDto.setLrnAtvId(String.valueOf(mapDto.get("LRN_ATV_ID")));
		atvDto.setCtnTpCd(atvCtnTpCd);

		atvDto.setLrnAtvNm(mapDto.get("LRN_ATV_NM").toString());
		atvDto.setClsBrdUrl(mapDto.get("CLS_BRD_URL") == null ? "" : mapDto.get("CLS_BRD_URL").toString());
		
		atvDto.setCtnTpCd(mapDto.get("CTN_TP_CD") == null ? "" : mapDto.get("CTN_TP_CD").toString());
		atvDto.setLrnTmScnt(mapDto.get("LRN_TM_SCNT") == null ? "" : mapDto.get("LRN_TM_SCNT").toString());
		atvDto.setLrnStCd(mapDto.get("LRN_ST_CD").toString());
		atvDto.setRcstnOrdn(mapDto.get("RCSTN_ORDN").toString());
		
		TlLrnwLrnCtnDto ctnDto = new TlLrnwLrnCtnDto();

		if (mapDto.get("CTN_META_DATA_ID") != null) {
			String ctnTpCd = mapDto.get("CTN_TP_CD") == null ? "" : mapDto.get("CTN_TP_CD").toString();
			ctnDto.setMetaDataId(
					mapDto.get("CTN_META_DATA_ID") == null ? "" : mapDto.get("CTN_META_DATA_ID").toString());
			ctnDto.setCtnTpCd(ctnTpCd);
			ctnDto.setCtnCd(mapDto.get("CTN_CD") == null ? "" : mapDto.get("CTN_CD").toString());
			ctnDto.setLrnCofmTmScnt(
					mapDto.get("LRN_COFM_TM_SCNT") == null ? "" : mapDto.get("LRN_COFM_TM_SCNT").toString());
			ctnDto.setCtnDffdDvCd(mapDto.get("CTN_DFFD_DV_CD") == null ? "" : mapDto.get("CTN_DFFD_DV_CD").toString());
			ctnDto.setLrnTpCd(mapDto.get("LRN_TP_CD") == null ? "" : mapDto.get("LRN_TP_CD").toString());
			ctnDto.setAtvQtmCnt(mapDto.get("ATV_QTM_CNT") == null ? "" : mapDto.get("ATV_QTM_CNT").toString());

			if (TlConstUtil.LRN_CTN_TP_CD_PL.equals(ctnTpCd)) { // 동영상은 아래 내용을 고정값으로 설정한다.
				// 2024.06.15 동영상이 고/중/저 화질등록이 필수였으나, 콘텐츠에 따라 일부 화질만 제공하는 것으로 변경됨
				// 동영상파일명이 공란이면 해당 화질미제공으로 설저한다.
				String v480p = mapDto.get("VDO_FLE_NM_480P") == null ? ""
						: mapDto.get("VDO_FLE_NM_480P").toString(); /* 동영상파일명480P */
				String v720p = mapDto.get("VDO_FLE_NM_720P") == null ? ""
						: mapDto.get("VDO_FLE_NM_720P").toString(); /* 동영상파일명720P */
				String v1280p = mapDto.get("VDO_FLE_NM_1280P") == null ? ""
						: mapDto.get("VDO_FLE_NM_1280P").toString(); /* 동영상파일명1280P */
				ctnDto.setVdsLRsln(0 == v480p.trim().length() ? "" : "media/01_video_480.mp4"); // 고정값설정
				ctnDto.setVdsMRsln(0 == v720p.trim().length() ? "" : "media/01_video_720.mp4"); // 고정값설정
				ctnDto.setVdsHRsln(0 == v1280p.trim().length() ? "" : "media/01_video_1080.mp4"); // 고정값설정
				ctnDto.setSttlSmiFleNm("media/01_caption.smi"); // 고정값설정
				ctnDto.setSttlVttFleNm(bcS3FileCheckUtil.isFileExists(BUCKET_NAME, MapUtils.getString(mapDto, "CDN_PTH_NM", "") + "media/01_caption.vtt")? "media/01_caption.vtt":""); // 고정값설정
//				ctnDto.setSttlVttFleNm("media/01_caption.vtt"); // 고정값설정
				ctnDto.setScrbFleNm("media/01_script.txt"); // 고정값설정
				ctnDto.setVceFleNm("media/01_audio.mp3"); // 고정값설정
				ctnDto.setThbFleNm("images/poster.png"); // 고정값설정
				
				
			}
		}

		String lrnAtvId = (mapDto.get("LRN_ATV_ID").toString());

		// 클래스보드URL리스트 작성
		List<TlLrnwAtvClabdDto> claList = new ArrayList<TlLrnwAtvClabdDto>();
		clabdList.stream().filter(clabdMap -> lrnAtvId.equals(clabdMap.get("LRN_ATV_ID"))).forEach(clabdMap2 -> {
			TlLrnwAtvClabdDto claDto = new TlLrnwAtvClabdDto();
			claDto.setClabdLrgsId(
					clabdMap2.get("CLABD_LRGS_ID") != null ? clabdMap2.get("CLABD_LRGS_ID").toString() : "");
			claDto.setClabdSmlId(clabdMap2.get("CLABD_SML_ID") != null ? clabdMap2.get("CLABD_SML_ID").toString() : "");
			claDto.setClabdLrgsNm(
					clabdMap2.get("CLABD_LRGS_NM") != null ? clabdMap2.get("CLABD_LRGS_NM").toString() : "");
			claDto.setClabdSmlNm(clabdMap2.get("CLABD_SML_NM") != null ? clabdMap2.get("CLABD_SML_NM").toString() : "");
			claDto.setClabdTyp(clabdMap2.get("CLABD_TYP") != null ? clabdMap2.get("CLABD_TYP").toString() : "");
			claDto.setClabdUrl(clabdMap2.get("CLABD_URL") != null ? clabdMap2.get("CLABD_URL").toString() : "");
			claList.add(claDto);
		});
		atvDto.setCtnMtd(ctnDto);
		atvDto.setClsbdList(claList);
		

		// atvDto.setCtnUrl(ctnMtd.get().get("CDN_PTH_NM") == null ? "":
		// ctnMtd.get().get("CDN_PTH_NM").toString());
		// 콘텐츠URL 작성처리
		String ctnUrl = "";
		if (mapDto.get("CDN_PTH_NM") != null) {	
			if(mapDto.get("TCR_CTN_YN").toString().equals("Y") && mapDto.get("CTN_TP_CD").toString().equals("DM")) {
        		ctnUrl = "https://v01.aitextbook.co.kr/streamdocs/view/sd?streamdocsId=" + mapDto.get("STR_FLE_NM").toString();
        	} else if (mapDto.get("TCR_CTN_YN").toString().equals("Y") && mapDto.get("CTN_TP_CD").toString().equals("BW")) {
        		ctnUrl = "/annLrnMtrlPopup?lrmpNodId="+ mapDto.get("LRMP_NOD_ID").toString() + "&tcrRegCtnId=" + mapDto.get("LRN_ATV_ID");
        	} else {
        		String strFleNm = mapDto.get("STR_FLE_NM") != null ? mapDto.get("STR_FLE_NM").toString() : "";
                String tmpCdn = mapDto.get("CDN_PTH_NM").toString();
                ctnUrl = TlCmUtil.makeCtnCdnUrl(BUCKET_NAME, tmpCdn, strFleNm);
        	}
		}else { 
        	if (mapDto.get("TCR_CTN_YN").toString().equals("Y") && mapDto.get("CTN_TP_CD").toString().equals("LK")) {	                		
        		ctnUrl = "/annLrnMtrlPopup?lrmpNodId="+ mapDto.get("LRMP_NOD_ID").toString() + "&tcrRegCtnId=" + mapDto.get("LRN_ATV_ID");
        	}
    	} 
		atvDto.setCtnUrl(ctnUrl);
                    
        return atvDto;
    }
}
