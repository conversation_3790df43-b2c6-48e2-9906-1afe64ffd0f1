package com.aidt.api.al.fdbk.cm;

import java.util.List;
import java.util.Map;

import com.aidt.api.al.fdbk.dto.*;
import org.springframework.stereotype.Service;

import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:53:50
 * @modify date 2024-07-11 15:53:50
 * @desc
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class AiFdbkCmService {

    private final String MAPPER_NAMESPACE = "api.al.fdbk.cm.";

    private final CommonDao commonDao;

    public List<AiFdbkDto> selectFdbkList(Map<String, String> paramMap) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectFdbkList", paramMap);
    }

    public AiFdbkDto selectFdbk(AiFdbkDto dto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectFdbk", dto);
    }

    public int insertFdbk(AiFdbkDto dto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertFdbk", dto);
    }

    public int updateFdbk(AiFdbkDto dto) {
        return commonDao.update(MAPPER_NAMESPACE + "updateFdbk", dto);
    }

    public List<String> selectFdbkBsDataList(AiFdbkBsDataDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectFdbkBsDataList", dto);
    }

    public AiFdbkBsDataDto selectFdbkBsData(AiFdbkBsDataDto dto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectFdbkBsData", dto);
    }

    public List<AiFdbkAchRankDto> selectAchRankList(String optTxbId, String lrmpNodId) {

        return commonDao.selectList(MAPPER_NAMESPACE + "selectAchRankList",
                Map.of(
                        "optTxbId", optTxbId,
                        "lrmpNodId", lrmpNodId));
    }

    public List<AiFdbkCorrectRateDto> selectCorrectRateList(String optTxbId, String lrmpNodId) {

        return commonDao.selectList(MAPPER_NAMESPACE + "selectCorrectRateList",
                Map.of(
                        "optTxbId", optTxbId,
                        "lrmpNodId", lrmpNodId));
    }

    public List<AiFdbkGrowthRankDto> selectGrowthRankList(String optTxbId, String lrmpNodId, String stuId) {

        return commonDao.selectList(MAPPER_NAMESPACE + "selectGrowthRankList",
                Map.of(
                        "optTxbId", optTxbId,
                        "lrmpNodId", lrmpNodId,
                        "stuId", stuId
                ));
    }

}
