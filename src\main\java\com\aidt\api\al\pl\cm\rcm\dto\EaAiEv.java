package com.aidt.api.al.pl.cm.rcm.dto;

import com.aidt.api.al.pl.cm.rcm.enums.EvaluationCode;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.api.bc.cm.textbook.dto.Textbook;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"evId", "optTxbId", "lluKmmpNodId", "mluKmmpNodId", "tcKmmpNodId", "tpcKmmpNodId"})
public class EaAiEv {

	private Integer evId;
	private String optTxbId;
	private String evDvCd;
	private String evDtlDvCd;
	private String evCmplYn;
	private Integer lluKmmpNodId;
	private Integer mluKmmpNodId;
	private Integer tcKmmpNodId;
	private String tcKmmpNodNm;
	private Integer tpcKmmpNodId;
	private String luevCmplYn;
	private Double aiPredAvgCansRt;
	private Textbook textbook;
	private String usrId;

	public String getSbjCd() {
		return this.textbook.getSbjCd();
	}

	public String getSchlGrdCd() {
		return this.textbook.getSchlGrdCd();
	}

	public void addTextbook(Textbook textbook) {
		this.textbook = textbook;
	}

	public void learningUnitEvaluationComplete() {
		this.luevCmplYn = "Y";
	}

	public void learningUnitEvaluationError() {
		this.luevCmplYn = "X";
	}

	public void clearMidUnitChildIds() {
		this.tcKmmpNodId = null;
		this.tpcKmmpNodId = null;
	}

	public void changeLearningUnitEvaluationByTopicCompletion(boolean isTopicCompleted) {
		this.luevCmplYn = isTopicCompleted ? "Y" : "N";
	}

	public EvaluationCode getEvaluationCode() {
		return EvaluationCode.getEvaluationCode(this.evDvCd);
	}

	public EvaluationDetailCode getEvaluationDetailCode() {
		return EvaluationDetailCode.getEvaluationDetailCode(this.evDtlDvCd);
	}

	public boolean isPredictedHigh() {
		return this.aiPredAvgCansRt != null && this.aiPredAvgCansRt > 0.8;
	}
}
