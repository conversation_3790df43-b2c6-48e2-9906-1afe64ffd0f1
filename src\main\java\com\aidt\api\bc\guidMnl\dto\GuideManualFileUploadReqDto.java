package com.aidt.api.bc.guidMnl.dto;

import lombok.*;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@ToString
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuideManualFileUploadReqDto {
    private MultipartFile file;
    private Long guidMnlNodId;
    private Long guidMnlNodFleId;
    private String guidMnlVdSmyCn;
    private String contentType;
    private String mdfrId;
    private String docViId;
}
