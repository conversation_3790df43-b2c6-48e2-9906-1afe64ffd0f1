package com.aidt.api.al.pl.stu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-28
 * @modify date 2024-02-28
 * @desc AI맞춤학습 학생 단원차시조회
 */
@Slf4j
@Service
public class AlMluTcLstInqStuService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.stu.AlMluTcLstInqStu.";
	
	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private JwtProvider jwtProvider;
	
	/**
     * 학생 중단원목록 조회 작업중
     * 
     * @param 
     * @return List<AlMluTcLstInqStuResponseDto>
     */
	
	public List<AlMluTcLstInqStuResponseDto> selectMluLstStuInq(AlMluTcLstInqStuReqDto reqDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//		AlMluTcLstInqStuReqDto reqDto = new AlMluTcLstInqStuReqDto();
//		reqDto.setOptTxbId("eaevasn9c0f1b6a831opttxb0002");
		reqDto.setOptTxbId(userDetails.getOptTxbId());
//		reqDto.setUsrId(userDetails.getUsrId());
		
		List<AlMluTcLstInqStuResponseDto> mluList = commonDao.selectList(MAPPER_NAMESPACE + "selectMluLstInqStu", reqDto);
		log.debug(mluList.toString());
		return mluList;
	}
	
	/**
     * 학생 맞춤학습별 평균정답률 조회 작업중
     * 
     * @param 
     * @return List<AlMluTcLstInqStuResponseDto>
     */
	public AlMluTcLstInqStuResponseDto selectStpAvgCansRt(@Valid AlMluTcLstInqStuReqDto reqDto) {
		AlMluTcLstInqStuResponseDto res = commonDao.select(MAPPER_NAMESPACE + "selectStpAvgCansRt", reqDto);
		
		res.setAvgC1(((double)res.getC1CansCnt()/(double)res.getQtmC1Cnt())*100);
		res.setAvgC2(((double)res.getC2CansCnt()/(double)res.getQtmC2Cnt())*100);
		
		res.setC1TpcAvn(setTpcAvn(reqDto.getC1EvId(), reqDto.getUsrId()));
		res.setC2TpcAvn(setTpcAvn(reqDto.getC2EvId(), reqDto.getUsrId()));
		
		return res;
	}
		
		/**
		 * 토픽숙련도 판단
		 * 
		 *  @param String evId 평가ID
		 *  @param String usrId 사용자ID
		 * */
		public String setTpcAvn(String evId, String usrId) {
			
			AlMluTcLstInqStuResponseDto dto = new AlMluTcLstInqStuResponseDto();
			dto.setUsrId(usrId);
			dto.setEvId(evId);
			Double tpcAvnSum = 0.0;
			//해당평가에 포함되는 토픽에 대한 모든문항 정오를 조회
			List<AlMluTcLstInqStuResponseDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectQtmCansYnByTpcStu", dto);
			
			//토픽별 리스트 그룹
	        Map<String, List<AlMluTcLstInqStuResponseDto>> tpcByListMap = list.stream().collect(Collectors.groupingBy(AlMluTcLstInqStuResponseDto::getTpcKmmpNodId));
			for (String key : tpcByListMap.keySet()) {
				List<AlMluTcLstInqStuResponseDto> tpcByQtmList = tpcByListMap.get(key);
				
				Double tpcWgtNmvl = 0.0;
				int listCnt = 0;
				for (AlMluTcLstInqStuResponseDto tpcQtm : tpcByQtmList) {
					listCnt++;
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("01")) { tpcWgtNmvl += 0.7; }//최하
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("02")) { tpcWgtNmvl += 0.85; }//하
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("03")) { tpcWgtNmvl += 1.0; }//중
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("04")) { tpcWgtNmvl += 1.15; }//상
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("05")) { tpcWgtNmvl += 1.3; }//최상
					else { tpcWgtNmvl += 0.0; }
				}
				//토픽-문항 난이도별 실제점수 가중치
				tpcWgtNmvl = tpcWgtNmvl/listCnt;
				//토픽별 실제점수 :: 문항풀이이력이 없어도 초기등록시 0.5로 등록되어 있다.
				Double aiPredAvgScr = tpcByQtmList.get(0).getAiPredAvgScr();
				
				//토픽별 AI 예측점수
				Double aiPredAvgCansRt = tpcByQtmList.get(0).getAiPredAvgCansRt();
				//토픽별 문항풀이 횟수
				Integer txmPn = tpcByQtmList.get(0).getTxmPn();
				//토픽점수
				Double tpcAvn = 0.5;
				
				//토픽별 문항풀이가 없는경우 AI예측점수로 판단
				if(txmPn <= 1 && aiPredAvgScr == 0.5) {
					tpcAvn = aiPredAvgCansRt;
				}
				//토픽별 문항풀이 이력이 1건인 경우 실제점수7 : 예측점수3
				else if(txmPn == 1 && aiPredAvgScr == 0.5) {
					tpcAvn = ((tpcWgtNmvl * 7) + (aiPredAvgCansRt * 3)) / 10;
				}
				//실제점수(가중치)로 판단
				else {
					tpcAvn = tpcWgtNmvl;
				}
				
				tpcAvnSum += tpcAvn;
			}
			
			tpcAvnSum = tpcAvnSum / tpcByListMap.size();
			
			//토픽숙련도 SET
			if(tpcAvnSum < 0.5) {//취약
				dto.setTpcAvn("01");
			}
			else if(0.5 <= tpcAvnSum && tpcAvnSum < 0.8) {//보통
				dto.setTpcAvn("02");
			}
			else if(0.8 < tpcAvnSum) {//완벽
				dto.setTpcAvn("03");
			}
			return dto.getTpcAvn();
		}
		
		/**
	     * @param list 중복이 있는 list
	     * @param key  중복 여부를 판단하는 키값
	     * @param <T>  generic type
	     * @return list
	     * @desc list<Object> 특정 값 기준 중복 제거
	     */
	    public static <T> List<T> deduplication(final ArrayList<T> list, Function<? super T, ?> key) {
	    	return list.stream()
	        	.filter(deduplication(key))
	            .collect(Collectors.toList());
	    }
	    private static <T> Predicate<T> deduplication(Function<? super T, ?> key) {
	    	final Set<Object> set = ConcurrentHashMap.newKeySet();
	        return predicate -> set.add(key.apply(predicate));
	    }

}
