package com.aidt.api.al.fdbk.cm;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-12 14:15:06
 * @modify date 2024-07-12 14:15:06
 * @desc
 */
public class AiFdbkFixCmtService {

    private Map<String, String> case1 = new HashMap<>();

    private Map<String, String> case3 = new HashMap<>();

    private Map<String, String> case4 = new HashMap<>();

    @PostConstruct
    private void init() {
        case1 = Map.of(
            "UD", "진단평가 결과, #name 학생은 '#1'에서 우수한 성과를 보였습니다. 해당 토픽은 #name 학생의 정답률이 가장 높은 토픽입니다.\n" +
                    "반면, '#2'와 '#3'에서는 상대적으로 낮은 정답률을 보였습니다. 이 부분들은 추가적인 재학습이 필요하니, 좀 더 집중해서 공부하기를 추천합니다.\n" +
                    "부족한 학습을 채운 뒤 기본 개념을 확실히 다져야 다음 단원의 학습을 어려움 없이 이어나갈 수 있습니다.",
            "UDUG", "진단평가와 총괄평가 결과, #name 학생은 '#1'에서 우수한 성과를 보였습니다. 해당 토픽은 #name 학생의 정답률이 가장 높은 토픽입니다.\n" +
                    "반면, '#2'와 '#3'에서는 상대적으로 낮은 정답률을 보였습니다. 이 부분들은 추가적인 재학습이 필요하니, 좀 더 집중해서 공부하기를 추천합니다.\n" +
                    "부족한 학습을 채운 뒤 기본 개념을 확실히 다져야 다음 단원의 학습을 어려움 없이 이어나갈 수 있습니다.",
            "UG", "총괄평가 결과, #name 학생은 '#1'에서 우수한 성과를 보였습니다. 해당 토픽은 #name 학생의 정답률이 가장 높은 토픽입니다.\n" +
                        "반면, '#2'와 '#3'에서는 상대적으로 낮은 정답률을 보였습니다. 이 부분들은 추가적인 재학습이 필요하니, 좀 더 집중해서 공부하기를 추천합니다.\n" +
                        "부족한 학습을 채운 뒤 기본 개념을 확실히 다져야 다음 단원의 학습을 어려움 없이 이어나갈 수 있습니다.");

        case3 = Map.of(
            "FS", "꾸준한 자기주도 학습을 지속한다면 어려운 문제도 문제없이 해결할 수 있을 거예요. AI 추천 콘텐츠를 통해 학습을 이어가세요!",
            "NM", "오답 문제를 풀어보고, AI 추천 콘텐츠를 활용하여 실력을 향상해 보세요.",
            "SL", "고난도 문제에서 발생한 오답을 분석하고, 오답 유사 문제 풀기에 도전해 보세요.");

        case4 = Map.of(
            "MA", "천리길도 한 걸음부터! 수학은 꾸준한 학습이 필수적입니다.",
            "EN", "영어는 꾸준한 연습과 도전을 통해 더 나은 결과를 이룰 수 있습니다.");

    }

    public String getCase1(String evCd, String usrNm, String h1, String l1, String l2) {
        String s = case1.get(evCd);
        return s.replaceAll("#name", usrNm)
                .replaceAll("#1", h1)
                .replaceAll("#2", l1)
                .replaceAll("#3", l2);
    }

    public String getCase3(String lrnrVelTpCd) {
        return case3.get(lrnrVelTpCd);
    }

    public String getCase4(String sbjCd) {
        return case4.get(sbjCd);
    }


}
