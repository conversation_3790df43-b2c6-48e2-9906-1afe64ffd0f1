package com.aidt.api.ea.ev.stu;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 교과 평가 - 학생 Service
 */
@Service
public class EaEvStuService {

	private final String MAPPER_NAMESPACE = "api.ea.ev.stu.";
	private final String MAPPER_NAMESPACE_COMM = "api.ea.evcom.";

	@Autowired
	private CommonDao commonDao;

	/**
	 * 학생 - 평가 목록 조회 요청
	 *
	 * @param eaEvStuReqDto
	 * @return List<EaEvMainResDto>
	 */
	public List<Map<String, Object>> selectEvList(EaEvMainReqDto eaEvStuReqDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvList", eaEvStuReqDto);
	}

	/**
	 * 학생 - 평가 리포트 조회 요청
	 *
	 * @param eaEvStuReqDto
	 * @return List<EaEvMainResDto>
	 */
	public EaEvMainResDto selectEvRptList(EaEvMainReqDto eaEvStuReqDto) {
		EaEvMainResDto eaEvStuDto = commonDao.select(MAPPER_NAMESPACE + "selectEvRptList", eaEvStuReqDto);
		List<EaEvComQtmAnwDto> qtmAnwDto = commonDao.selectList(MAPPER_NAMESPACE_COMM + "selectEvQtmAnwList", eaEvStuReqDto);
		eaEvStuDto.setQtmAnwList(qtmAnwDto);

		return eaEvStuDto;
	}

}
