package com.aidt.api.tl.inidat.tcr;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.api.sl.inidat.tcr.SlIniDatTcrService;
import com.aidt.api.tl.inidat.dto.TlIniDatCondDto;
import com.aidt.api.tl.inidat.dto.TlIniDatLrnAtvDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-04 14:28:18
 * @modify date 2024-03-04 14:28:18
 * @desc TlIniDatTcr Service 교과학습 초기데이터작성 서비스
 */
@Slf4j
@Service
public class TlIniDatTcrService {
    private final String MAPPER_NAMESPACE = "api.tl.inidat.tcr.";

    @Autowired
    private CommonDao commonDao;
    @Autowired
    private SlIniDatTcrService slService;
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
    /**
     * 교과학습재구성데이터 등록처리
     * @param cndDto
     * @return
     */
    @Transactional
    public int registIniDat(TlIniDatCondDto cndDto) {
        int rtnVal = 0;
        // 1.운영교과서ID로 교과서ID및담당클래스를 조회한다.
        List<Map<String, Object>> optTxbIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectOptTxbId", cndDto);
        List<TlIniDatCondDto> tgtList = new ArrayList<TlIniDatCondDto>();
        List<Map<String, Object>> scdlMgLcknList = new ArrayList<Map<String, Object>>();
        String txbId = "";

        // 2.담당클래스에 재구성데이터가 생성되어있는지 체크한다.
        for(Map<String, Object> item: optTxbIdList) {
            if ("0".equals(item.get("CNT").toString())) {
                TlIniDatCondDto dto = new TlIniDatCondDto();
                dto.setOptTxbId(item.get("OPT_TXB_ID").toString());
                dto.setTcrUsrId(cndDto.getTcrUsrId());
                dto.setSecTrmYn(item.get("SEC_TRM_YN").toString());
                tgtList.add(dto);
                if ("".equals(txbId)) {
                    txbId = item.get("TXB_ID").toString();
                }
            }
        }
        // 외부활동설정 데이터 체크
        setExtAtv(cndDto);

        if (tgtList.size() == 0) { //재구성하지 않고 로그인 할 때
        	scdlMgLcknList = commonDao.selectList(MAPPER_NAMESPACE + "selectScdlMgLcknList", cndDto);
        	if(scdlMgLcknList.size() > 0) {
        		for(Map<String, Object> list : scdlMgLcknList) {
        			commonDao.update(MAPPER_NAMESPACE + "updateLrnScdlMgLckn",  Map.of("optTxbId", cndDto.getOptTxbId(), "lrmpNodId", list.get("LRMP_NOD_ID")));
        		}
        	}
            return rtnVal;
        }

        log.debug("##### 생성대상 TXB_ID = " + txbId);

        // 3. 처리2에서 생성되지 않은 것이 판명되면 재구성데이터를 생성한다.
        int currentYear = LocalDate.now().getYear();
        String baseYmd1 = String.valueOf(currentYear) + "0301"; // 1학기, 공통 교과서 학습기준일자
        String baseYmd2 = this.getSecTermYmd(currentYear); // 2학기 교과서 시작기준일자
        
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();

        for(TlIniDatCondDto tgtDto: tgtList) {
            String baseYmd = tgtDto.getSecTrmYn() != null && "Y".equals(tgtDto.getSecTrmYn()) ? baseYmd2 : baseYmd1;
            log.debug("##### 생성대상 운영교과서ID = " + tgtDto.getOptTxbId() + ",  교사ID = " + tgtDto.getTcrUsrId() + ", 기준일자=" + baseYmd);
            
            int insCnt1 = 0;
            try {
                // TL_교과학습노드재구성 데이터 작성
                insCnt1 = commonDao.insert(MAPPER_NAMESPACE + "insertTlSbcLrnNodRcstn",
                            Map.of("tgtOptTxbId", tgtDto.getOptTxbId(),"tgtTxtId", txbId, "tgtTcrUsrId", tgtDto.getTcrUsrId(), "dbId", DB_ID, "baseYmd", baseYmd));
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (TL_교과학습노드재구성 데이터 생성)");
				log.error(dke.getMessage());				
			}
            
            srhDto.setOptTxbId(cndDto.getOptTxbId());
            
            // TL_교과학습노드재구성 데이터 작성
//            int updCnt1 = commonDao.insert(MAPPER_NAMESPACE + "updateTlSbcLrnNodRcstn", Map.of("tgtOptTxbId", tgtDto.getOptTxbId()));
            log.debug("##### TL_교과학습노드재구성 건수 = " + String.valueOf(insCnt1));
            
//            List<TlIniDatLrnAtvDto> atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectAtvRcstnDat", Map.of("tgtOptTxbId", tgtDto.getOptTxbId(),"tgtTxtId", txbId));
//            int insCnt2 = commonDao.insert(MAPPER_NAMESPACE + "insertTlLrnAtvRcstn", Map.of("atvList", atvList, "dbId", DB_ID));
            
            int insCnt2 = 0;
            try {
            	// TL_교과학습활동재구성 데이터 작성
                insCnt2 = commonDao.insert(MAPPER_NAMESPACE + "insertTlLrnAtvRcstn",
                        Map.of("tgtOptTxbId", tgtDto.getOptTxbId(),"tgtTxtId", txbId, "tgtTcrUsrId", tgtDto.getTcrUsrId(), "dbId", DB_ID));
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (TL_교과학습활동재구성 데이터 생성)");
				log.error(dke.getMessage());
			}
            
            log.debug("##### TL_교과학습활동재구성 건수 = " + String.valueOf(insCnt2));

            int claCnt = commonDao.select(MAPPER_NAMESPACE + "countAtvClabd", Map.of("tgtOptTxbId", tgtDto.getOptTxbId()));
            if (0 < claCnt) {
            	int insCnt3 = 0;
                try {
                	// TL_교과학습활동클래스보드 데이터 작성
                    insCnt3 = commonDao.insert(MAPPER_NAMESPACE + "insertTlSbcLrnAtvClabd",
                            Map.of("tgtOptTxbId", tgtDto.getOptTxbId(),"tgtTxtId", txbId, "tgtTcrUsrId", tgtDto.getTcrUsrId(), "dbId", DB_ID));
    			} catch(DuplicateKeyException dke) {
    				log.error("Duplicate error (TL_교과학습활동클래스보드 데이터 생성)");
    				log.error(dke.getMessage());
    			}

                log.debug("##### TL_교과학습활동클래스보드 건수 = " + String.valueOf(insCnt3));

            }
        }
        updateLrnSortRcstnList(srhDto);
       
        return rtnVal;
    }
    /**
     * 2학기기준일자 취득
     * @param year 기준연도
     * @return
     */
	private String getSecTermYmd(int year) {

		// 8월의 마지막 날을 구함
        LocalDate lastDayOfAugust = LocalDate.of(year, 8, 31);

        // 마지막 날에서 가장 가까운 월요일을 구함
        LocalDate lastMonday = lastDayOfAugust.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        int day = lastMonday.getDayOfMonth();
        String baseYmd = String.valueOf(year) + "08" + (26 < day ? String.valueOf(day - 7) : String.valueOf(day));
		return  baseYmd;
	}
	
	/**
     * 원클릭학습설정 번호 노출 목록 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmTocDto>
     */
    @Transactional
    public int updateLrnSortRcstnList(TlOneClkSetmSrhDto srhDto) {
    	List<TlOneClkSetmTocDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnSortList", srhDto);
    	List<TlOneClkSetmTocDto> updatelist=new ArrayList<TlOneClkSetmTocDto>();
    	updateLrnSortRcstn(list, 1, null,updatelist);
    	
    	int updateCnt=0;
    	
    	for(TlOneClkSetmTocDto dto :  updatelist) {
    		updateCnt+=commonDao.update(MAPPER_NAMESPACE + "updateLrnSort", dto);
    	}
    	return updateCnt;
    }

    @Transactional
    private int updateLrnSortRcstn(List<TlOneClkSetmTocDto> list,int dpth,String urnkLrmpNodId,List<TlOneClkSetmTocDto> updatelist) {
    	log.debug("dpth : " + dpth + " urnkLrmpNodId : " + urnkLrmpNodId );
    	if(dpth>4) {
    		return 0;
    	}
    	int rcstnNo=0;
    	for(TlOneClkSetmTocDto dto :  list) {
    		if(dpth==1) {
    			if(dto.getDpth()==1) {
    				rcstnNo++;		
    				dto.setRcstnNo(rcstnNo);
    				updateLrnSortRcstn(list, 4, dto.getLrmpNodId(),updatelist);
    				
    				updatelist.add(dto);
    			}
    		}else {
    			if(dto.getUrnkLrmpNodId() != null) {
        			if(dto.getDpth()==dpth && dto.getUrnkLrmpNodId().equals(urnkLrmpNodId)) {
        				rcstnNo++;		
        				dto.setRcstnNo(rcstnNo);
        				if(dpth<4) {
    	    				int nextDpt = dto.getDpth()+1;
    	    				updateLrnSortRcstn(list, nextDpt , dto.getLrmpNodId(),updatelist);
        				}
        				updatelist.add(dto);
        			}	
    			}
    		}
    	}
    	return rcstnNo;
    }
    
    /**
     * 교과학습 초기화(단건)
     * 
     * @param srhDto
     * @return int
     */
    @Transactional
    public int iniSbcLrnDat(TlIniDatCondDto srhDto) {
    	int ctn = 0;
    	ctn += commonDao.delete(MAPPER_NAMESPACE + "deleteLrnNodRcstn", srhDto);
    	ctn += commonDao.delete(MAPPER_NAMESPACE + "deleteLrnAtvRcstn", srhDto);
    	ctn += commonDao.delete(MAPPER_NAMESPACE + "deleteLrnAtvClabd", srhDto);
    	ctn += commonDao.delete(MAPPER_NAMESPACE + "deleteSpLrnRcstn", srhDto);
    	
    	if(ctn <= 0) {
    		return ctn;
    	} else {
    		SlIniDatCondDto slDto = new SlIniDatCondDto();
    		slDto.setOptTxbId(srhDto.getOptTxbId());
    		slDto.setTcrUsrId(srhDto.getTcrUsrId());
    		this.registIniDat(srhDto);
    		slService.registIniDat(slDto);
    	}
    	
    	return ctn;
    }
    
    /**
     * 교과학습 초기화(다건)
     * 
     * @param srhDto
     * @return int
     */
    @Transactional
    public int iniSbcLrnDatTot(int txbId, String txbCd) {
    	int ctn = 0;
    	List<Map<String, Object>> optList = commonDao.selectList(MAPPER_NAMESPACE + "selectSbcLrnDatTot", Map.of("txbId", txbId, "txbCd", txbCd));
    	for(Map<String, Object> opt : optList) {
    		
    	}
    	return ctn;
    }
    
    /**
     * 외부활동설정
     * 
     * @param srhDto
     * @return int
     */
    @Transactional
    public int setExtAtv(TlIniDatCondDto shrDto) {
    	int cnt = 0;
    	int datCnt = commonDao.select(MAPPER_NAMESPACE + "selectExtAtv", shrDto);
    	
    	if(datCnt == 0) {
    		try {
            	// 외부활동설정 데이터 생성
    			cnt = commonDao.insert(MAPPER_NAMESPACE + "insertExtAtv", shrDto);
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (외부활동설정 데이터 생성)");
				log.error(dke.getMessage());
			}
    	}
    	
    	return cnt;
    }
}
