
package com.aidt.api.xx.sample.paging;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.xx.sample.paging.dto.SamplePagingDataDto;
import com.aidt.api.xx.sample.paging.dto.SamplePagingRequestDto;
import com.aidt.common.CommonDao;
import com.aidt.common.Paging.PagingResponseDto;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2024-01-14 20:46:27
 * @modify date 2024-01-14 20:46:27
 * @desc [ Paging 샘플 서비스]
 */
@RequiredArgsConstructor
@Service
public class SamplePagingService {

	private final String MAPPER_NAMESPACE = "api.xx.sample.SamplePaging.";
    
	private final CommonDao commonDao;

    /**
     * Paging 샘플 조회 서비스
     * 
     * @param requestDto
     * @return CommonPagingResponseDto
     */
    @Transactional(readOnly = true)
    public PagingResponseDto selectList(SamplePagingRequestDto requestDto) {
        List<SamplePagingDataDto> spd = commonDao.selectList(MAPPER_NAMESPACE + "selectList", requestDto);
        return new PagingResponseDto(
                requestDto.getPageNo(),     // 페이지 번호
                requestDto.getPageSize(),   // 페이지 사이즈 
                spd, // 데이터 
                spd.get(0).getTotalCnt()); // 데이터 전체 갯수( 조회 기준 )
    }
}
