package com.aidt.api.tl.lsnmtrl.stu;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:43:43
 * @modify date 2024-01-05 10:43:43
 * @desc TlLsnMtrlStu Service 수업자료서비스
 */
@Service
public class TlLsnMtrlStuService {
    private final String MAPPER_NAMESPACE = "api.tl.lsnmtrl.stu.";

    @Autowired
    private CommonDao commonDao;

//    /**
//     * 교과학습 학습자료정보조회
//     * 
//     * @param srhDto
//     * @return TlLsnMtrlInfoDto 기본자료목록과 학습자료목록을 반환한다.
//     */
//    @Transactional(readOnly = true)
//    public TlLsnMtrlInfoDto selectLsnMtrlInfo(TlLsnMtrlTocSrhDto srhDto) {
//        TlLsnMtrlInfoDto rtnDto = new TlLsnMtrlInfoDto();
//        srhDto.setAllDatYn("N");
//        List<TlLsnMtrlDto> bsMtrlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlBsList", srhDto);
//        List<TlLsnMtrlDto> lmMtrlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlLmList", srhDto);
//        rtnDto.setBsMtrlList(bsMtrlList);
//        rtnDto.setLmMtrlList(lmMtrlList);
//        return rtnDto;
//    }
}
