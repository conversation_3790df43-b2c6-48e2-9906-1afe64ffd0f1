package com.aidt.api.bc.inf.common;

import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class InfPathUtil {

	
	/**
	 * 알림 조회(mapDvCd : R) - 상수화된 데이터를 실제 컴포넌트 경로로 치환 
	 * 알림 등록(mapDvCd : C) - INFM_MV_CN(알림이동내역)컬럼의 filePath 값을 InfPathEnum 클래스에 상수화한 데이터로 치환
	 * @param infmMvCnJson
	 * @param mapDvCd
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static String mapFilePathToEnum(String infmMvCnJson, String mapDvCd) {    
		
		if (infmMvCnJson == null || mapDvCd == null) {
			log.error("Invalid input: infmMvCnJson or mapDvCd is null");
			return infmMvCnJson;
		}
				
		try {		
			String validJson = infmMvCnJson.replace("'", "\"");
			ObjectMapper objectMapper = new ObjectMapper();
			var infmMvCnMap = objectMapper.readValue(validJson, Map.class);
			
			// filePath 값 추출
			String filePath = (String) infmMvCnMap.get("filePath");
			String path;
			String lastFourChars = filePath.substring(filePath.length() - 4);
			
			// 알림 조회 
			if("R".equals(mapDvCd)) {
				// filePath 확장자가 .vue로 끝나면(기존데이터) 변환하지 않고 그대로 리턴
	            if(".vue".equals(lastFourChars)) {            	
	            	return infmMvCnJson;
	            }            
	            // Enum 매핑
	            InfPathEnum enumValue = InfPathEnum.valueOf(filePath);
	            path = enumValue.getPath();	            
	            log.debug("path : {}  ", path );	            	            
			} else {				
				// 알림 등록
				if(".vue".equals(lastFourChars)) {						
					path = InfPathEnum.fromPath(filePath);
					log.debug("enumName : {}  ", path);
				} else {
					path = filePath;
					log.debug("path2 : {}  ", path);
				}
			}					
			
			infmMvCnMap.put("filePath", path);
			String updatedJson = objectMapper.writeValueAsString(infmMvCnMap);	            
			log.debug("updatedJson : {} ", updatedJson);	
			return updatedJson;  
			
		} catch (Exception e) {          
            log.error("Error mapping filePath to Enum : " + e.getMessage());
            return infmMvCnJson;
		}	
	}	
}
