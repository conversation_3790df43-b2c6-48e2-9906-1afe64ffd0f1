package com.aidt.api.bc.bkmk.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcBkmkPagingRequest {
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;

    @Parameter(name="요청 크기")
    int pageSize;

    @Parameter(name="요청 차수")
    int pageNo;

    @Parameter(name="요청 db 오프셋")
    public int getPageOffset() {
        return pageSize * pageNo;
    }

    public static BcBkmkPagingRequest create(String lrnUsrId, int pageSize, int pageNo){
        return BcBkmkPagingRequest.builder()
                .lrnUsrId(lrnUsrId)
                .pageSize(pageSize)
                .pageNo(pageNo)
                .build();
    }
}
