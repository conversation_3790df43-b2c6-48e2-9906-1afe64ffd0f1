package com.aidt.api.tl.oneclksetm.tcr.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TlOneClkSetmRcmCtnDtlDto {
    /** 운영교과서 ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 특별학습 ID */
    @Parameter(name="특별학습ID")
    private String spLrnId;

    /** 학생 ID */
    @Parameter(name="학생ID")
    private String usrId;

    /** 학습자속도 */
    @Parameter(name="학습자속도")
    private String lrnrVelTpCd;

    /** 콘텐츠 수 */
    @Parameter(name="콘텐츠 수")
    private int entire;

    /** 학습완료 수 */
    @Parameter(name="학습완료 수")
    private int done;

    /** 콘텐츠 완료 수 */
    @Parameter(name="콘텐츠 완료 수")
    private int ctnDone;
    
    /** 평가 진행 여부 */
    @Parameter(name="평가 진행 여부")
    private String evYn;

    /** 추천데이터 존재 여부 */
    @Parameter(name="추천데이터 존재 여부")
    private String datYn;

    /** 추천여부 */
    @Parameter(name="추천여부")
    private String rcmYn;

    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;

    /** 특별학습명 */
    @Parameter(name="특별학습명")
    private String spLrnNm;

    /** dbId */
    @Parameter(name="dbId")
    private String dbId;
}
