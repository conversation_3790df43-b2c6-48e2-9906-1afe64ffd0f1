<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.cmt.stu">
	<!-- AI 코멘트 조회(평가/영어/영역별 평가) -->
	<select id="selectEnAraList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		<trim prefixOverrides="UNION ALL">
		<if test="lstngLvlCd != null and lstngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'LSTNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND LSTNG_LVL_CD = #{lstngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="spkngLvlCd != null and spkngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'SPKNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND SPKNG_LVL_CD = #{spkngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="rdngLvlCd != null and rdngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'RDNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND RDNG_LVL_CD = #{rdngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="wrtngLvlCd != null and wrtngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'WRTNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND WRTNG_LVL_CD = #{wrtngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="phncsLvlCd != null and phncsLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'PHNCS' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND PHNCS_LVL_CD = #{phncsLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		<if test="wrdLvlCd != null and wrdLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'WRD' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND WRD_LVL_CD = #{wrdLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		<if test="grmrLvlCd != null and grmrLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'GRMR' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND GRMR_LVL_CD = #{grmrLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		<if test="alpbLvlCd != null and alpbLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'ALPB' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND ALPB_LVL_CD = #{alpbLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		</trim>
	</select>

	<!-- AI 코멘트 조회(평가/영어/학기초평가) -->
	<select id="selectEnEvStList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectEnEvStList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN03"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN05"/>)
		</trim>
	</select>

	<!-- AI 코멘트 조회(평가/영어/단원평가) -->
	<select id="selectEnEvUgList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectEnEvUdList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN03"/>)
		<choose>
			<when test="existAiRcmCtn == true">
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN04"/>)
			</when>
			<otherwise>
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN05"/>)
			</otherwise>
		</choose>
		</trim>
	</select>

	<!-- AI 코멘트 조회(평가/영어/학기말총괄) -->
	<select id="selectEnEvEtList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectEnEvEtList */
		<trim prefixOverrides="UNION ALL">
		<if test="n13 != null">
			(<include refid="api.al.cmt.cm.selectN13"/>)
		</if>
		<if test="n14 != null">
		UNION ALL
			(<include refid="api.al.cmt.cm.selectN14"/>)
		</if>
		<if test="n15 != null">
		UNION ALL
			(<include refid="api.al.cmt.cm.selectN15"/>)
		</if>
		UNION ALL
			(<include refid="api.al.cmt.cm.selectN05"/>)
		</trim>
	</select>

	<!-- AI 코멘트 조회(AI맞춤학습/진단리포트) -->
	<select id="selectEnAiDgnRptList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectEnAiDgnRptList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN03"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN10"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN11"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN08"/>)
		</trim>
	</select>
	<!-- AI 코멘트 조회(AI맞춤학습/학습리포트) -->
	<select id="selectEnAiLrnRptList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectEnAiLrnRptList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN03"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN10"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN11"/>)
		<choose>
			<when test="isStudying == true">
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN08"/>)
			</when>
			<otherwise>
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN09"/>)
			</otherwise>
		</choose>
		</trim>
	</select>

	<!-- AI 코멘트 조회(AI 진단 리포트) -->
	<select id="selectEnRptLuList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectEnRptLuList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN10"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN11"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN06"/>)
		</trim>
	</select>
</mapper>