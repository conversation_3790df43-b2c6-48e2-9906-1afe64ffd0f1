package com.aidt.api.tl.lrnwif.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwLrnVdsDto 학습활동-콘텐츠정보Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLrnwLrnCtnDto {

    /** 메타데이터ID */
    @Parameter(name="메타데이터ID")
    private String metaDataId;
    /** 콘텐츠유형코드 */
    @Parameter(name="콘텐츠유형코드")
    private String ctnTpCd;
    /** 콘텐츠CD */
    @Parameter(name="콘텐츠CD")
    private String ctnCd;
    /** 학습인정시간 */
    @Parameter(name="학습인정시간")
    private String lrnCofmTmScnt;
    /** 난이도 */
    @Parameter(name="난이도")
    private String ctnDffdDvCd;
    /** 완료체크기준코드 삭제예정 */
    @Parameter(name="완료체크기준코드")
    private String cmplBsCd;
    /** 학습유형 */
    @Parameter(name="학습유형")
    private String lrnTpCd;
    /** 액티비티총문항수 */
    @Parameter(name="액티비티총문항수")
    private String atvQtmCnt;
    /** OCR사용여부  삭제예정 */
    @Parameter(name="OCR사용여부")
    private String ocrUseYn;
    /** 수식사용여부  삭제예정 */
    @Parameter(name="수식사용여부")
    private String fmlUseYn;
    /** STT사용여부  삭제예정 */
    @Parameter(name="STT사용여부")
    private String sttUseYn;
    /**저화질 */
    @Parameter(name="저화질")
    private String vdsLRsln;
    /** 기본화질 */
    @Parameter(name="기본화질")
    private String vdsMRsln;
    /** 고화질 */
    @Parameter(name="고화질")
    private String vdsHRsln;
    /** 자막SMI파일명 */
    @Parameter(name="자막SMI파일명")
    private String sttlSmiFleNm;
    /** 자막VTT파일명 */
    @Parameter(name="자막VTT파일명")
    private String sttlVttFleNm;
    /** 대본파일명 */
    @Parameter(name="대본파일명")
    private String scrbFleNm;
    /** 음성파일명 */
    @Parameter(name="음성파일명")
    private String vceFleNm;
    /** 썸네일파일명 */
    @Parameter(name="썸네일파일명")
    private String thbFleNm;
}
