package com.aidt.api.ea.evcom.dto;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.units.qual.N;

import com.aidt.common.CommonUserDetail;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EaEvLearnLevelDto {

	private String usrId;
	private String optTxbId;
	private String dbId;
	private List<LearnUnit> learnUnits;

	@Getter
	@NoArgsConstructor
	@AllArgsConstructor
	public static class LearnUnit {
		private String lrmpNodId;
		private String lrnrVelTpCd;
	}

	public EaEvLearnLevelDto(CommonUserDetail userDetail, String dbId) {
		if (ObjectUtils.anyNull(userDetail, dbId)) {
			throw new IllegalArgumentException("사용자 정보를 알 수 없어 단원별 학습 수준을 변경할 수 없습니다.");
		}
		this.usrId = userDetail.getUsrId();
		this.optTxbId = userDetail.getOptTxbId();
		this.dbId = dbId;
		this.learnUnits = null;
	}

	public void addLearnUnits(List<LearnUnit> units) {
		if (CollectionUtils.isNotEmpty(units)) {
			this.learnUnits = units;
		}
	}

}
