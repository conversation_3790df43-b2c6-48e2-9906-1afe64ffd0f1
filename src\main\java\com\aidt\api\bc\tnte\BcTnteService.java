package com.aidt.api.bc.tnte;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.tnte.dto.BcTnteDto;
import com.aidt.common.CommonDao;

//@Slf4j
@Service
public class BcTnteService {
	
	private final String MAPPER_NAMESPACE = "api.bc.tnte.cm.";
	
	@Autowired
    private CommonDao commonDao;
	
	/**
     * 필기 목록 조회 서비스
     *
     * @param BcTnteDto
     * @return List<BcTnteDto>
     */
    public List<BcTnteDto> selectTnteList(BcTnteDto bcTnteDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectTnteList", bcTnteDto);
    }
    
}
