package com.aidt.api.al.pl.cm.cache;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import io.swagger.v3.oas.annotations.Operation;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/api/v1/al/pl/cm/en")
public class cacheController {

    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private CommonDao commonDao;

    @Autowired
    private cacheService cacService;

    @Operation(summary = "맞춤학습 캐시 삭제", description = "진단평가 리스트 캐시 삭제")
    @PostMapping(value="/deleteCache")
    public ResponseDto<Integer> CacheDelete(@RequestBody AiRcmTsshQtmDto dto){
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
        if(dto.getUsrId() == null) {
            dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        if(dto.getOptTxbId() == null) {
            dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
        }
        return Response.ok(cacService.cacheDelete(dto));
    }
}
