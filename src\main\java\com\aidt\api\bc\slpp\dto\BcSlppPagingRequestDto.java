package com.aidt.api.bc.slpp.dto;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcSlppPagingRequestDto {

    @Parameter(name="조회사용자ID(확인체크목적)")
    private String crtrId;

    @Parameter(name="교사사용자ID")
    @NotNull
    private String tcrUsrId;

    @Parameter(name="학생사용자ID")
    @NotNull
    private String stuUsrId;

    @Parameter(name="송신사용자유형코드(학생/교사)")
    private String trnmUsrTpCd;

    @Parameter(name="단체발송구분코드(일대일/전체)")
    private String grupSndDvCd;

    @Parameter(name="쪽지내용")
    private String slppCn;

    @Parameter(name="요청 크기")
    int pageSize;

    @Parameter(name="요청 차수")
    int pageNo;

    @Parameter(name="요청 db 오프셋")
    public int getPageOffset() {
    	log.debug("**********************************pageSize * pageNo = " + pageSize * pageNo);
        return pageSize * pageNo;
    }

    // tcrUsrId, stuUsrId, pageSize, pageNo
    public static BcSlppPagingRequestDto create4SimpleRequest(
            String tcrUsrId,
            String stuUsrId,
            Integer pageSize,
            Integer pageNo
    ){
        return BcSlppPagingRequestDto.builder()
                .tcrUsrId(tcrUsrId)
                .stuUsrId(stuUsrId)
                .pageSize(pageSize)
                .pageNo(pageNo)
                .build();
    }

    // tcrUsrId, stuUsrId, pageSize, pageNo, slppCn(검색 목적)
    public static BcSlppPagingRequestDto create4SearchRequest(
            String tcrUsrId,
            String stuUsrId,
            Integer pageSize,
            Integer pageNo,
            String slppCn
    ){
        return BcSlppPagingRequestDto.builder()
                .tcrUsrId(tcrUsrId)
                .stuUsrId(stuUsrId)
                .pageSize(pageSize)
                .pageNo(pageNo)
                .slppCn(slppCn)
                .build();
    }
}
