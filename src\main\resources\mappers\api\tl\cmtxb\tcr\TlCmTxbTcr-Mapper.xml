<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.cmtxb.tcr">
    <!-- 학생 정보 조회-->
    <select id="selectTxbLrnDtlPst" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstDto">
        SELECT 
            U.USR_ID,        /* 학생 ID */
            U.USR_NM,        /* 학생 이름 */
            C.SGY,            /* 학년 */
            C.CLA_NO,        /* 반 */
            U.STU_NO        /* 학생 번호 */
        FROM LMS_LRM.CM_USR U         /* CM_사용자 */
            LEFT JOIN LMS_LRM.CM_CLA C    /* CM_학급 */
              ON U.CLA_ID = C.CLA_ID
        WHERE USR_ID = #{stuId}

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectTxbLrnDtlPst */
    </select>

    <!-- 대단원 정보 조회-->
    <select id="selectTxbLrnDtlPstLlu" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlLluDto">
        SELECT
            R.LRMP_NOD_NM,        /* 대단원명 */
            R.LRMP_NOD_ID,        /* 대단원ID */
            R.LCKN_YN            /* 잠금여부 */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R /* TL_교과학습노드재구성 */
        WHERE R.OPT_TXB_ID = #{optTxbId}
          AND R.DPTH = 1
          AND R.USE_YN = 'Y'
          AND R.LU_EPS_YN = 'Y'
        ORDER BY R.RCSTN_ORDN ASC

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectTxbLrnDtlPstLlu */
    </select>

    <!-- 차시 정보 조회-->
    <select id="selectTxbLrnDtlPstTc" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlTcDto">
        SELECT
            R.LRMP_NOD_ID,        /* 차시ID */
            R.LRMP_NOD_NM,        /* 차시명 */
            R.LCKN_YN            /* 잠금여부 */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R /* TL_교과학습노드재구성 */
        WHERE R.OPT_TXB_ID = #{optTxbId}
          AND R.DPTH = 4
          AND R.USE_YN = 'Y'
          AND R.LLU_NOD_ID = #{lluNodId}
          AND R.LU_EPS_YN = 'Y'
        ORDER BY R.RCSTN_ORDN ASC

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectTxbLrnDtlPstTc */
    </select>

    <!-- 활동목록 조회-->
    <select id="selectTxbLrnDtlPstAtv" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlAtvDto">
        SELECT
            A.LRN_ATV_ID,
            R.LRN_ATV_NM,
            A.LRN_ST_CD,
            R.CTN_TP_CD,
            A.CRT_DTM,
            A.MDF_DTM
        FROM LMS_LRM.TL_SBC_LRN_ATV_ST A /* TL_교과학습활동상태 */
            INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN R /* TL_교과학습활동재구성 */
               ON A.LRN_ATV_ID = R.LRN_ATV_ID
              AND R.OPT_TXB_ID = #{optTxbId}
              AND R.LRMP_NOD_ID = A.LRMP_NOD_ID
        WHERE R.LRMP_NOD_ID = #{lrmpNodId}
        AND A.LRN_USR_ID = #{stuId}
        ORDER BY R.RCSTN_ORDN ASC

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectTxbLrnDtlPstAtv */
    </select>

    <!--클래스보드 url update-->
    <update id="updateCmTxbClsBrdUrl" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdUdtDto">
        UPDATE LMS_LRM.TL_SBC_LRN_ATV_RCSTN        /* TL_교과학습활동재구성 */
        SET CLS_BRD_URL = #{clsBrdUrl},
            MDFR_ID = #{usrId},
            MDF_DTM = NOW()
        WHERE OPT_TXB_ID = #{optTxbId}
        AND LRMP_NOD_ID = #{lrmpNodId}
        AND LRN_ATV_ID = #{lrnAtvId}

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - updateCmTxbClsBrdUrl */
    </update>

    <!--클래스보드 url update v3.3-->
    <update id="updateCmTxbClabdUrl" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdUdtDto">
        UPDATE LMS_LRM.TL_SBC_LRN_ATV_CLABD /* TL_교과학습활동클래스보드 */
           SET CLABD_URL= #{clsBrdUrl}
              ,MDFR_ID = #{usrId}
              ,MDF_DTM = NOW()
        WHERE  OPT_TXB_ID= #{optTxbId}
          AND LRMP_NOD_ID= #{lrmpNodId}
          AND LRN_ATV_ID= #{lrnAtvId} 
          AND CLABD_LRGS_ID= #{clabdLrgsId}
          AND CLABD_SML_ID=#{clabdSmlId}

        /* 교과학습 강성희 TlCmTxbTcr-Mapper.xml - updateCmTxbClabdUrl */
    </update>

    <!-- 학생 별 총 학습시간 -->
    <select id="selectCmTxbTotLrnTm" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTotLrnTmDto">
        SELECT   MAX(T.OPT_TXB_ID) AS OPT_TXB_ID
                ,MAX(C.CLA_ID) AS CLA_ID
                ,U.USR_ID
                ,IFNULL(SUM(S.LRN_TM_SCNT),0) AS TOT_LRN_TM
        FROM LMS_LRM.CM_USR U                    /* CM_사용자 */
            LEFT JOIN LMS_LRM.CM_CLA C                /* CM_학급 */
              ON U.CLA_ID = C.CLA_ID
            LEFT JOIN LMS_LRM.CM_OPT_TXB T            /* CM_운영교과서 */
              ON C.CLA_ID = T.CLA_ID
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST S    /* TL_교과학습활동상태 */
              ON U.USR_ID = S.LRN_USR_ID
        WHERE T.OPT_TXB_ID = #{optTxbId}
          AND U.USR_TP_CD = 'ST'
        GROUP BY U.USR_ID
        ORDER BY U.STU_NO ASC

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectCmTxbTotLrnTm */
    </select>
    
    <!--차시선택 정보 확인-->
    <select id="selectCmTxbTcTm" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTcTmDto" resultType="int">
        SELECT
        	COUNT(1)
        FROM LMS_LRM.TL_SBC_LRN_TC_TM		/* TL_교과학습차시선택 */
        WHERE OPT_TXB_ID = #{optTxbId}

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectCmTxbTcTm */
    </select>
    
    <!-- 차시선택 정보 등록-->
    <insert id="insertCmTxbTcTm" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTcTmDto">
        INSERT INTO LMS_LRM.TL_SBC_LRN_TC_TM	/* TL_교과학습차시선택 */
            (OPT_TXB_ID                  /* 운영교과서ID */
            ,LLU_NOD_ID					 /* 대단원노드ID */
            ,LRMP_NOD_ID                 /* 학습맵노드ID */
            ,TC_TM_DTM					 /* 차시시간일시 */
            ,CRTR_ID                     /* 생성자ID */
            ,CRT_DTM                     /* 생성일시 */
            ,MDFR_ID                     /* 수정자ID */
            ,MDF_DTM                     /* 수정일시 */
            ,DB_ID                       /* 접속DB인스턴스ID */
            )
        VALUES
            (#{optTxbId}                                /* 운영교과서ID */
            ,#{lluNodId}								/* 대단원노드ID */
            ,#{lrmpNodId}                               /* 학습맵노드ID */
            ,NOW()										/* 차시시간일시 */
            ,#{mdfrId}                                  /* 생성자ID */
            ,NOW()                                      /* 생성일시 */
            ,#{mdfrId}                                  /* 수정자ID */
            ,NOW()                                      /* 수정일시 */
            ,#{dbId})                                   /* 접속DB인스턴스ID */

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - insertCmTxbTcTm */
    </insert>
    
    <!--차시선택 정보 수정-->
    <update id="updateCmTxbTcTm" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTcTmDto">
        UPDATE LMS_LRM.TL_SBC_LRN_TC_TM	/* TL_교과학습차시선택 */
           SET <if test='lrmpNodId.equals("")'>
           	  	LLU_NOD_ID = NULL
           	  	,LRMP_NOD_ID = NULL
              	,TC_TM_DTM = NULL
           	   </if>
           	   <if test='lrmpNodId != ""'>
           	    LLU_NOD_ID = #{lluNodId}
           	  	,LRMP_NOD_ID = #{lrmpNodId}
              	,TC_TM_DTM = NOW()
           	   </if>
              ,MDFR_ID = #{mdfrId}
              ,MDF_DTM = NOW()
        WHERE  OPT_TXB_ID= #{optTxbId}

        /* 교과학습 김형준 TlCmTxbTcr-Mapper.xml - updateCmTxbTcTm */
    </update>
    
    <!-- 활동 상태 조회 -->
	<select id="selectLrnAtvSt" parameterType="Map" resultType="Map">
		SELECT ATV_ST.LRN_USR_ID
			  ,SUM(ATV_ST.CL_CNT) AS CL_CNT
         FROM	(SELECT
         				XA.LRN_ATV_ID
         				,IF(XC.LRN_ST_CD = 'CL', 1, 0) AS CL_CNT
         				,XC.LRN_USR_ID
                FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN XA /* TL_교과학습활동재구성 */
               INNER JOIN LMS_CMS.BC_LRN_STP XB /* BC_학습단계 */
                     ON XA.LRMP_NOD_ID = XB.LRMP_NOD_ID
                     AND XA.LRN_STP_ID = XB.LRN_STP_ID
               LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST XC
                     ON XC.OPT_TXB_ID = XA.OPT_TXB_ID
                     AND XC.LRMP_NOD_ID = XA.LRMP_NOD_ID
                     AND XC.LRN_ATV_ID = XA.LRN_ATV_ID
                WHERE XA.OPT_TXB_ID = #{optTxbId}
                AND XA.LRMP_NOD_ID = #{lrmpNodId}
                AND XA.USE_YN = 'Y'
                AND XB.LRN_STP_DV_CD = 'CL' /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EX: 평가) */
                AND XB.DEL_YN = 'N'
                
                UNION ALL
      			 
      			 SELECT 
					 		M.TCR_REG_CTN_ID AS LRN_ATV_ID
					 		,IF(T.LRN_ST_CD = 'CL', 1, 0) AS CL_CNT
					 		,T.LRN_USR_ID
					FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
					INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
						ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
					INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
						ON M.OPT_TXB_ID = R.OPT_TXB_ID
						AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
					INNER JOIN LMS_CMS.bc_lrn_stp S
						ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
						AND M.LRN_STP_ID = S.LRN_STP_ID
						AND S.DEL_YN = 'N'
					LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
						ON M.OPT_TXB_ID = T.OPT_TXB_ID
						AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
					WHERE M.OPT_TXB_ID = #{optTxbId}
					AND M.LRMP_NOD_ID = #{lrmpNodId}
					AND M.DEL_YN = 'N'
					AND M.USE_YN = 'Y')ATV_ST
		group by ATV_ST.LRN_USR_ID
			
		/** 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectLrnAtvSt */
	</select>
	
	<!-- 시작 활동ID 조회 -->
	<select id="selectStrAtvId" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTcTmDto" resultType="String">
		select cast(ATV.LRN_ATV_ID as CHAR) as LRN_ATV_ID
		FROM(select A.LRN_ATV_ID,
					A.RCSTN_ORDN
			from lms_lrm.tl_sbc_lrn_atv_rcstn A
			inner join LMS_CMS.bc_lrn_stp S
				on A.LRMP_NOD_ID = S.LRMP_NOD_ID 
				and A.LRN_STP_ID = S.LRN_STP_ID
				and S.DEL_YN = 'N'
			where A.OPT_TXB_ID = #{optTxbId}
			and A.LRMP_NOD_ID = #{lrmpNodId}
			and A.USE_YN = 'Y'
			and S.LRN_STP_DV_CD = #{lrnStpDvCd}
			
			union all
			
			select R.TCR_REG_CTN_ID as LRN_ATV_ID,
					R.RCSTN_ORDN 
			from lms_lrm.tl_tcr_reg_ctn_mpn R
			inner join LMS_CMS.bc_lrn_stp S
				on R.LRMP_NOD_ID = S.LRMP_NOD_ID 
				and R.LRN_STP_ID = S.LRN_STP_ID
				and S.DEL_YN = 'N'
			where R.OPT_TXB_ID = #{optTxbId}
			and R.LRMP_NOD_ID = #{lrmpNodId}
			and R.DEL_YN = 'N'
			and S.LRN_STP_DV_CD = #{lrnStpDvCd}) ATV
		order by ATV.RCSTN_ORDN asc
		limit 1;
		/** 교과학습 김형준 TlCmTxbTcr-Mapper.xml - selectStrAtvId */
	</select>
</mapper>