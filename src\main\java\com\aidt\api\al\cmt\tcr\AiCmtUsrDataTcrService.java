package com.aidt.api.al.cmt.tcr;

import com.aidt.api.al.cmt.cm.AiCmtUsrDataService;
import com.aidt.api.al.cmt.dto.AiCmtUsrDataDto;
import com.aidt.api.al.cmt.dto.req.AiCmtUsrDataReqDto;
import com.aidt.api.al.cmt.dto.res.AiCmtEnResWrapperDto;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.api.al.cmt.dto.res.AiCmtUsrDataResDto;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:01:58
 * @modify date 2024-05-21 11:01:58
 * @desc
 */
@Service
@RequiredArgsConstructor
public class AiCmtUsrDataTcrService {

    private final AiCmtUsrDataService aiCmtUsrDataService;



    public <T>AiCmtEnResWrapperDto getOrInsertWrapper(String evId, String usrId, T reqDto, Function<T, AiCmtEnResWrapperDto> selectEnEvFunction) {
        boolean isSaved = this.selectIsSaved(evId, usrId, "TE");
        AiCmtEnResWrapperDto savedDto = this.selectAiCmtUsrDataToWrapper(evId, usrId, "TE");
        if(isSaved && savedDto != null) return savedDto;

        AiCmtEnResWrapperDto aiCmtEnResWrapperDto = selectEnEvFunction.apply(reqDto);
        this.insertAllAiCmtUsrWrapperDataAfterDelete(aiCmtEnResWrapperDto, evId, usrId, "TE");
        return aiCmtEnResWrapperDto;
    }

    public <T>List<AiCmtResDto> getOrInsertList(String evId, String usrId, T reqDto, Function<T, List<AiCmtResDto>> selectionEnEvListFunction) {
        List<AiCmtResDto> savedDataList = this.selectAiCmtUsrData(evId, usrId, "TE");
        if(!savedDataList.isEmpty()) return savedDataList;

        List<AiCmtResDto> aiCmtResDtoList = selectionEnEvListFunction.apply(reqDto);
        this.insertAiCmtUsrDataAfterDelete(aiCmtResDtoList, evId, usrId, "TE");
        return aiCmtResDtoList;
    }

    public List<AiCmtResDto> selectAiCmtUsrData(AiCmtUsrDataReqDto reqDto) {
        List<AiCmtUsrDataDto> aiCmtUsrData = aiCmtUsrDataService.selectAiCmtUsrData(reqDto);
        return aiCmtUsrData.stream().map(AiCmtUsrDataDto::toResDto).collect(Collectors.toList());
    }

    public List<AiCmtResDto> selectAiCmtUsrData(String evId, String usrId, String usrTpCd) {
        AiCmtUsrDataReqDto reqDto = AiCmtUsrDataReqDto.builder().evId(evId).usrId(usrId).usrTpCd(usrTpCd).build();
        List<AiCmtUsrDataDto> aiCmtUsrData = aiCmtUsrDataService.selectAiCmtUsrData(reqDto);
        return aiCmtUsrData.stream().map(AiCmtUsrDataDto::toResDto).collect(Collectors.toList());
    }

    public AiCmtEnResWrapperDto toWrapperDto(List<AiCmtResDto> list) {
        if(list.isEmpty()) return null;

        List<AiCmtResDto> total = list.stream().filter(s -> !Objects.equals(s.getAiCmtNo(), "12")).collect(Collectors.toList());
        List<AiCmtResDto> area = list.stream().filter(s -> Objects.equals(s.getAiCmtNo(), "12")).collect(Collectors.toList());

        return AiCmtEnResWrapperDto.builder().total(total).ara(area).build();
    }

    public AiCmtEnResWrapperDto selectAiCmtUsrDataToWrapper(String evId, String usrId, String usrTpCd) {
        AiCmtUsrDataReqDto reqDto = AiCmtUsrDataReqDto.builder().evId(evId).usrId(usrId).usrTpCd(usrTpCd).build();
        List<AiCmtResDto> list = this.selectAiCmtUsrData(reqDto);
        return this.toWrapperDto(list);
    }

    public void insertAiCmtUsrData(List<AiCmtResDto> aiCmtResDtoList, String evId, String usrId) {
        if(StringUtils.isEmpty(evId)) return;

        List<AiCmtUsrDataReqDto> aiCmtUsrDataReqDtoList = aiCmtResDtoList.stream().map(s ->
                AiCmtUsrDataReqDto.builder()
                        .evId(evId)
                        .usrId(usrId)
                        .aiCmtNo(s.getAiCmtNo())
                        .cmtCn(s.getCmtCn())
                        .usrTpCd("TE")
                        .cmtType(s.getCmtType()).build()).collect(Collectors.toList());

        aiCmtUsrDataService.insertAiCmtUsrData(aiCmtUsrDataReqDtoList);
    }

    public void insertAiCmtUsrDataAfterDelete(List<AiCmtResDto> aiCmtResDtoList, String evId, String usrId, String usrTpCd) {
        this.deleteAiCmtUsrData(evId, usrId, usrTpCd);
        this.insertAiCmtUsrData(aiCmtResDtoList, evId, usrId);
    }

    public void insertAllAiCmtUsrWrapperDataAfterDelete(AiCmtEnResWrapperDto wrapperDto, String evId, String usrId, String usrTpCd) {
        this.deleteAiCmtUsrData(evId, usrId, usrTpCd);
        this.insertAiCmtUsrData(wrapperDto.getTotal(), evId, usrId);
        this.insertAiCmtUsrData(wrapperDto.getAra(), evId, usrId);
    }

    public boolean selectIsSaved(String evId, String usrId, String usrTpCd) {
        if(StringUtils.isEmpty(evId)) return false;

        Map<String, String> evMap = Map.of(
                "evId", evId,
                "usrId", usrId,
                "usrTpCd", usrTpCd);
        return aiCmtUsrDataService.selectIsSaved(evMap);
    }

    public void deleteAiCmtUsrData(String evId, String usrId, String usrTpCd) {
        if(StringUtils.isNotEmpty(evId) && StringUtils.isNotEmpty(usrId) && StringUtils.isNotEmpty(usrTpCd)) {
            Map<String, String> evMap = Map.of(
                    "evId", evId,
                    "usrId", usrId,
                    "usrTpCd", usrTpCd);
            aiCmtUsrDataService.deleteAiCmtUsrData(evMap);
        }
    }
}
