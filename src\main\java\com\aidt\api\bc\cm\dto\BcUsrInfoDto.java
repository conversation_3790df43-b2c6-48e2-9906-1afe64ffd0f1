package com.aidt.api.bc.cm.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "KERIS정보조합")
public class BcUsrInfoDto {

	@Parameter(name = "개인식별번호")
	private String usrId;

	@Parameter(name = "사용자ID")
	private String user_id;

	@Parameter(name = "사용자명")
	private String usrNm;

	@Parameter(name = "교사사용자ID(교사UUID)")
	private String chgTcrUsrId;

	@Parameter(name = "유저타입")
	private String userType;

	@Parameter(name = "사용자유형코드")
	private String usrTpCd;

	@Parameter(name = "교사유형코드")
	private String tcrTpCd;

	@Parameter(name = "운영교과서ID")
	private String optTxbId;

	@Parameter(name = "학급ID")
	private String claId;

	@Parameter(name = "반명")
	private String claNm;

	@Parameter(name = "교과서학기코드")
	private String txbTermCd;

	@Parameter(name = "교과서ID")
	private String txbId;

	@Parameter(name = "관심여부")
	private String ntrYn;

	@Parameter(name = "등록자ID")
	private String crtrId;

	@Parameter(name = "수정자ID")
	private String mdfrId;

	@Parameter(name = "학교ID")
	private String schoolId;

	@Parameter(name = "학교명")
	private String schoolName;

	@Parameter(name = "학교구분코드")
	private String userDivision;

	@Parameter(name = "학교ID")
	private String schlCd;

	@Parameter(name = "학교구분코드(내부용)")
	private String schlGrdCd;

	@Parameter(name = "사용자명")
	private String userName;

	@Parameter(name = "학년")
	private String userGrade;

	@Parameter(name = "반")
	private String userClass;

	@Parameter(name = "번호")
	private String userNumber;

	@Parameter(name = "성별")
	private String userGender;

	@Parameter(name = "시간표")
	private List<BcScheduleInfoDto> scheduleList;

	@Parameter(name = "학생목록")
	private List<BcUsrInfoDto> stuList;

	@Parameter(name = "서버분기값")
	private String serverCheck;

	@Parameter(name = "강의코드")
	private String lectureCd;

	@Parameter(name = "사용자상태")
	private String userStatus;

	@Parameter(name = "데이터베이스ID")
	private String dbId;

	@Parameter(name = "서브도메인")
	private String subDomain;

	@Parameter(name = "동의여부")
	private String agrYn;

	@Parameter(name = "동의여부처리일시")
	private String agrYnPrcsDtm;

	// 사용자 ID(개인 식별코드)
	private String userId;

	// 학교급코드명
	private String schlGrdCdNm;

	// 학기구분코드(00:공통, 01:1학기, 02:2학기)
	private String trmDvCd;

	// 요일(1:월, 2:화, 3:수, 4:목, 5:금)
	private String dayWeek;

	// 과목코드(KO:국어, EN:영어, MA:수학, SO:사회, SC:과학, IN:정보)
	private String sbjCd;

	// 과목명
	private String subjectName;

	// 교시
	private String classPeriod;

	// 교실
	private String classroomName;

	// 학급코드
	private String classCode;

	// 대화사용여부
	private String slppUseYn;

	// 임시토큰ID
	private String tempUsrId;

	// 담임교사명
	private String chgTcrUsrNm;

	// 로그인 API 체크
	private String pointCheck;

	// 감정상태코드
	private String flnStCd;

	private String uuid;
	private String regId;
	private String regDt;
	private String role;

	private String kerisUsrId;
	private String kerisClaCd;
	private String kerisLectCd;
	// 학생의 경우 KERIS API를 호출 하기 위한 선생님 KERIS ID를 담아줌
	private String tcrKerisUsrId;
	
	// kafka 통신용
	private String kafkaClaId;
	private String kafkaOptTxbId;
	private String kafkaUsrId;
	private List<String> kafkaUsrIds;
	private boolean kafkaLectNone;
	
	// cm_cla insert 시 추가 필요 변수
	private Long sgy;
	private Long claNo;
	
	private String kerisTermAgrYn;

}
