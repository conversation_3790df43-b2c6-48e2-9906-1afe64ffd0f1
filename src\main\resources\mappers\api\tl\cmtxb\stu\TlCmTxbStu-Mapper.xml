<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.cmtxb.stu">
    
    <!-- 대단원 목록 조회 -->
    <select id="selectTxbLluList" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLluDto">
        SELECT
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* 학습맵노드ID */
              ,A.LRMP_NOD_NM  /* 학습맵노드명 */
              ,A.DPTH  /* 깊이 */
              ,A.RCSTN_ORDN  /* 재구성순서 */
              ,A.RCSTN_NO
              ,A.LCKN_YN  /* 잠금여부 */
              ,A.USE_YN  /* 사용여부 */
              ,A.LU_NO_USE_YN /* 단원번호사용여부 */
         FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */
         WHERE A.DPTH = 1
          AND A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
          	AND A.LU_EPS_YN = 'Y'
         <if test='lcknYn != null and lcknYn neq ""'>
            AND A.LCKN_YN = #{lcknYn}
         </if>
         <if test='useYn != null and useYn neq ""'>
            AND A.USE_YN= #{useYn}
         </if>
         ORDER BY RCSTN_ORDN ASC, LRMP_NOD_ID ASC

        /* 교과학습 김형준 TlCmTxbStu-Mapper.xml - selectTxbLluList */

    </select>
    
    <!-- 대단원 목록 조회 -->
    <select id="selectTxbTocLluList" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLluDto">
        SELECT
               MAX(A.OPT_TXB_ID) AS OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* 학습맵노드ID */
              ,MAX(A.LRMP_NOD_NM) AS LRMP_NOD_NM  /* 학습맵노드명 */
              ,MAX(A.DPTH) AS DPTH  /* 깊이 */
              ,MAX(A.RCSTN_ORDN) AS RCSTN_ORDN  /* 재구성순서 */
              ,MAX(A.RCSTN_NO) AS RCSTN_NO
              ,MAX(A.LCKN_YN) AS LCKN_YN  /* 잠금여부 */
              ,MAX(A.USE_YN) AS USE_YN /* 사용여부 */
              ,MAX(A.LU_NO_USE_YN) AS LU_NO_USE_YN /* 단원번호사용여부 */
              ,IF (0 <![CDATA[<]]> SUM(IF(D.LRN_STP_DV_CD = 'CL', 1, 0)) AND SUM(IF(D.LRN_STP_DV_CD = 'CL', 1, 0)) = SUM(IF(E.LRN_ST_CD = 'CL' OR F.LRN_ST_CD = 'CL', 1, 0)), 'Y', 'N')  AS LRN_CMPL_YN  /* 학습완료여부 */ 
         FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */

                LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B  /* TL_교과학습노드재구성(차시) */
                        ON A.OPT_TXB_ID = B.OPT_TXB_ID
                       AND A.LRMP_NOD_ID = B.LLU_NOD_ID 
                       AND B.USE_YN = 'Y'
                       AND B.DPTH = 4
                LEFT JOIN (
							SELECT * FROM
								(SELECT 
									AR.OPT_TXB_ID
									,AR.LRMP_NOD_ID
									,AR.LRN_ATV_ID
									,AR.LRN_STP_ID
									,AR.LRN_ATV_NM
									,AR.USE_YN
									,AR.RCSTN_ORDN
								FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN AR
								WHERE  AR.OPT_TXB_ID =  #{optTxbId} 

								UNION ALL

								SELECT
									M.OPT_TXB_ID
									,M.LRMP_NOD_ID
									,M.TCR_REG_CTN_ID AS LRN_ATV_ID
									,M.LRN_STP_ID
									,C.TCR_REG_CTN_NM AS LRN_ATV_NM
									,M.USE_YN
									,M.RCSTN_ORDN
								FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
								INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
									ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
								WHERE M.DEL_YN = 'N'
								AND M.USE_YN = 'Y' AND  M.OPT_TXB_ID =  #{optTxbId} ) ATV
							WHERE ATV.OPT_TXB_ID = #{optTxbId}) C  /* TL_교과학습활동재구성 */
                       ON  B.OPT_TXB_ID = C.OPT_TXB_ID
                      AND B.LRMP_NOD_ID = C.LRMP_NOD_ID
                      AND C.USE_YN = 'Y'
                LEFT JOIN LMS_CMS.BC_LRN_STP D /* BC_학습단계 */
                       ON C.LRN_STP_ID = D.LRN_STP_ID
                      AND C.LRMP_NOD_ID = D.LRMP_NOD_ID
                      AND D.LRN_STP_DV_CD = 'CL' 
                      AND D.DEL_YN = 'N'
                LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST E /* TL_교과학습활동상태 */
                       ON A.OPT_TXB_ID = E.OPT_TXB_ID
                      AND A.LRMP_NOD_ID = E.LRMP_NOD_ID
                      AND C.LRN_ATV_ID = E.LRN_ATV_ID
                      AND E.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/
                 LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st F
                		ON C.OPT_TXB_ID = F.OPT_TXB_ID
						AND C.LRN_ATV_ID = F.TCR_REG_CTN_ID
						AND F.LRN_USR_ID = #{lrnUsrId}
         WHERE A.DPTH = 1
          AND A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
          	AND A.LU_EPS_YN = 'Y'
         <if test='lcknYn != null and lcknYn neq ""'>
            AND A.LCKN_YN = #{lcknYn}
         </if>
         <if test='useYn != null and useYn neq ""'>
            AND A.USE_YN= #{useYn}
         </if>
            GROUP BY A.LRMP_NOD_ID
         ORDER BY RCSTN_ORDN ASC, LRMP_NOD_ID ASC

        /* 교과학습 김형준 TlCmTxbStu-Mapper.xml - selectTxbToCLluList */

    </select>

    <!-- 차시 목록 조회 -->
    <select id="selectTxbTcList" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLluDto">
        SELECT
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* 학습맵노드ID */
              ,A.LLU_NOD_ID
              ,A.URNK_LRMP_NOD_ID /*상위노드ID*/
              ,A.LRMP_NOD_NM  /* 학습맵노드명 */
              ,A.DPTH  /* 깊이 */ 
              ,A.RCSTN_ORDN  /* 재구성순서 */
              ,A.RCSTN_NO
              ,A.LCKN_YN  /* 잠금여부 */
              ,A.USE_YN  /* 사용여부 */
              ,A.LU_NO_USE_YN /* 단원번호사용여부 */
         FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */ 
         WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
         	AND A.LU_EPS_YN = 'Y'
         <if test='lcknYn != null and lcknYn neq ""'>
            AND A.LCKN_YN = #{lcknYn}
         </if>
         <if test='useYn != null and useYn neq ""'>
            AND A.USE_YN = #{useYn}
         </if>
         ORDER BY RCSTN_ORDN ASC, LRMP_NOD_ID ASC

        /* 교과학습 김형준 TlCmTxbStu-Mapper.xml - selectTxbTcList */

    </select>
    
    <!-- 차시 목록 조회(교과목차) -->
    <select id="selectTxbTocTcList" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbLluDto">
        SELECT
              MAX(A.OPT_TXB_ID) AS OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* 학습맵노드ID */
              ,MAX(A.LLU_NOD_ID) AS LLU_NOD_ID
              ,MAX(A.URNK_LRMP_NOD_ID) AS URNK_LRMP_NOD_ID /*상위노드ID*/
              ,MAX(A.LRMP_NOD_NM) AS LRMP_NOD_NM  /* 학습맵노드명 */
              ,MAX(A.DPTH) AS DPTH  /* 깊이 */ 
              ,MAX(A.RCSTN_ORDN) AS RCSTN_ORDN  /* 재구성순서 */
              ,MAX(A.RCSTN_NO) AS RCSTN_NO
              ,MAX(A.LCKN_YN) AS LCKN_YN /* 잠금여부 */
              ,MAX(A.USE_YN) AS USE_YN /* 사용여부 */
              ,MAX(A.LU_NO_USE_YN) AS LU_NO_USE_YN /* 단원번호사용여부 */

                ,IF(A.DPTH = 4 AND 0  <![CDATA[<]]>  SUM(IF(D.LRN_STP_DV_CD = 'CL', 1, 0)) AND SUM(IF(D.LRN_STP_DV_CD = 'CL', 1, 0))  <![CDATA[<=]]>  SUM(IF(D.LRN_STP_DV_CD = 'CL' AND (E.LRN_ST_CD = 'CL' OR F.LRN_ST_CD = 'CL'), 1, 0)), 'Y', 'N')  AS LRN_CMPL_YN  /* 학습완료여부 */ 
                ,IF(A.DPTH = 4 AND 0    <![CDATA[<]]>    SUM(IF(D.LRN_STP_DV_CD = 'CL', 1, 0)) AND SUM(IF(D.LRN_STP_DV_CD = 'CL', 1, 0))   <![CDATA[<=]]>   SUM(IF(D.LRN_STP_DV_CD = 'CL', IF(E.LRN_ST_CD = 'CL' OR F.LRN_ST_CD = 'CL', 1, 0), 0)), 'CL', IF(A.DPTH = 4 AND 0    <![CDATA[<]]>    SUM(IF(D.LRN_STP_DV_CD = 'CL', IF(E.LRN_ST_CD = 'CL' OR F.LRN_ST_CD = 'CL', 1, 0), 0)),'DL','NL'))  AS LRN_ST  /* 학습상태 */  
                ,IF(A.DPTH = 4 AND 0   <![CDATA[<]]>   SUM(IF(C.USE_YN = 'Y', 1, 0)),'Y','N')  AS LRN_ABLE_YN  /* 학습가능여부 */

              	,MAX(A.LU_EPS_YN) AS LU_EPS_YN 
              	,MAX(IFNULL((SELECT LRN_ATV_ID FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN WHERE OPT_TXB_ID=#{optTxbId} AND LRMP_NOD_ID = A.LRMP_NOD_ID AND EV_ID IS NOT NULL),''))  AS LRN_ATV_ID    
   				,MAX(IFNULL(V.EV_ID, ''))	AS EV_ID
   				,MAX(IFNULL(V.EV_DTL_DV_CD, ''))	AS EV_DTL_DV_CD
   				,MAX(IFNULL(V.LCKN_YN, '')) AS EV_LCKN_YN
   				,MAX(IFNULL(S.EVSH_TP_CD, ''))	AS EVSH_TP_CD
   				,MAX(IFNULL(V.USE_YN,'')) AS EV_USE_YN
   				,MAX(V.TXM_STR_DTM) as TXM_STR_DTM
   				,MAX(V.TXM_END_DTM) as TXM_END_DTM
   				,IF(MAX(R.EV_CMPL_YN) = 'Y', 'Y', IF(IFNULL(MAX(R.TXM_STR_YN),'N') = 'Y','D','N')) AS EV_CMPL_YN
   				,MAX(IFNULL(F1.FLE_PTH_NM, '')) AS PC_THB_PTH
   				,MAX(IFNULL(F2.FLE_PTH_NM, '')) AS TA_THB_PTH

         FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */ 

                LEFT JOIN (
            			SELECT * FROM
								(SELECT 
									AR.OPT_TXB_ID
									,AR.LRMP_NOD_ID
									,AR.LRN_ATV_ID
									,AR.LRN_STP_ID
									,AR.LRN_ATV_NM
									,AR.USE_YN
									,AR.RCSTN_ORDN
									,AR.EV_ID
									,AR.ORGL_LRMP_NOD_ID
								FROM (SELECT 
								    	R.OPT_TXB_ID,
								        R.LRMP_NOD_ID AS LRMP_NOD_ID,
										R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
								        R.LRN_ATV_ID,
								        R.LRN_STP_ID,
								        R.CTN_CD,
								        R.LRN_ATV_NM,
								        R.CTN_TP_CD,
								        R.USE_YN,
								        R.CLS_BRD_URL,
								        R.ORGL_LRN_STP_ID,
								        R.ORGL_ORDN,
								        R.RCSTN_ORDN,
								        R.EV_ID,
								        R.CRTR_ID,
								        R.CRT_DTM,
								        R.MDFR_ID,
								        R.MDF_DTM,
								        R.DB_ID,
								        'N' AS TCR_CTN_YN
								    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
								    WHERE R.OPT_TXB_ID = #{optTxbId}
								      
									UNION ALL
									
								    SELECT 
								        R.OPT_TXB_ID,
								        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
										R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
								        R.LRN_ATV_ID,
								        TTRCM.LRN_STP_ID,
								        R.CTN_CD,
								        R.LRN_ATV_NM,
								        R.CTN_TP_CD,
								        TTRCM.USE_YN,
								        R.CLS_BRD_URL,
								        R.ORGL_LRN_STP_ID,
								        R.ORGL_ORDN,
								        TTRCM.RCSTN_ORDN,
								        R.EV_ID,
								        R.CRTR_ID,
								        R.CRT_DTM,
								        R.MDFR_ID,
								        R.MDF_DTM,
								        R.DB_ID,
								        'Y' AS TCR_CTN_YN
								    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
								    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
								        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
								        and ttrcm.del_yn = 'N'
								    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
								        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
								    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
								      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
								      AND TTRC.LRN_ATV_ID IS NOT NULL) AR
								WHERE  AR.OPT_TXB_ID =  #{optTxbId}
								
								UNION ALL

								SELECT
									M.OPT_TXB_ID
									,M.LRMP_NOD_ID
									,M.TCR_REG_CTN_ID AS LRN_ATV_ID
									,M.LRN_STP_ID
									,C.TCR_REG_CTN_NM AS LRN_ATV_NM
									,M.USE_YN
									,M.RCSTN_ORDN
									,'' AS EV_ID
									,M.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID
								FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
								INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
									ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
									AND C.TP_CD <![CDATA[<>]]> 'AT'
								WHERE M.DEL_YN = 'N'
								AND M.USE_YN = 'Y' AND  M.OPT_TXB_ID =  #{optTxbId} ) ATV
							WHERE ATV.OPT_TXB_ID = #{optTxbId}) C  /* TL_교과학습활동재구성 */
                       ON A.OPT_TXB_ID = C.OPT_TXB_ID
                      AND A.LRMP_NOD_ID = C.LRMP_NOD_ID
                      AND C.USE_YN = 'Y'
                LEFT JOIN LMS_CMS.BC_LRN_STP D /* BC_학습단계 */
                       ON C.LRN_STP_ID = D.LRN_STP_ID
                      AND C.LRMP_NOD_ID = D.LRMP_NOD_ID
                      AND D.LRN_STP_DV_CD = 'CL'  
                      AND D.DEL_YN = 'N'
                LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST E /* TL_교과학습활동상태 */
                       ON A.OPT_TXB_ID = E.OPT_TXB_ID
                      AND C.ORGL_LRMP_NOD_ID = E.LRMP_NOD_ID
                      AND C.LRN_ATV_ID = E.LRN_ATV_ID
                      AND E.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/
                LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st F
                		ON C.OPT_TXB_ID = F.OPT_TXB_ID
						AND C.LRN_ATV_ID = F.TCR_REG_CTN_ID
						AND F.LRN_USR_ID = #{lrnUsrId}
             	LEFT JOIN LMS_LRM.EA_EV V
						ON V.EV_ID = C.EV_ID
						AND V.DEL_YN = 'N'
				LEFT JOIN LMS_CMS.BC_EVSH S
						ON V.EVSH_ID = S.EVSH_ID
						AND V.EVSH_CD = S.EVSH_CD
						AND S.USE_YN = 'Y'
						AND S.DEL_YN = 'N'
				LEFT JOIN LMS_LRM.EA_EV_RS R
						ON V.EV_ID = R.EV_ID
						AND R.USR_ID = #{lrnUsrId}
				LEFT JOIN LMS_CMS.BC_LRMP_NOD_THB_MPN T1
					ON A.LRMP_NOD_ID = T1.LRMP_NOD_ID
				LEFT JOIN LMS_CMS.BC_UPL_FLE F1
					ON T1.PC_THB_UPL_ID = F1.UPL_FLE_ID
					AND F1.DEL_YN = 'N'
				LEFT JOIN LMS_CMS.BC_LRMP_NOD_THB_MPN T2
					ON A.LRMP_NOD_ID = T2.LRMP_NOD_ID
				LEFT JOIN LMS_CMS.BC_UPL_FLE F2
					ON T2.TA_THB_UPL_ID = F2.UPL_FLE_ID
					AND F2.DEL_YN = 'N'

         WHERE A.OPT_TXB_ID =  #{optTxbId} /* 운영교과서ID */
           <if test='lcknYn != null and lcknYn neq ""'>
            AND A.LCKN_YN = #{lcknYn}
         </if>
         <if test='useYn != null and useYn neq ""'>
            AND A.USE_YN = #{useYn}
         </if>

            GROUP BY A.LRMP_NOD_ID
			having ((TXM_STR_DTM is null and TXM_END_DTM is null) or (TXM_STR_DTM <![CDATA[<]]> NOW() and NOW() <![CDATA[<]]> TXM_END_DTM))
         ORDER BY RCSTN_ORDN ASC, LRMP_NOD_ID ASC

        /* 교과학습 김형준 TlCmTxbStu-Mapper.xml - selectTxbTocTcList */

    </select>
    
    <!-- 진단평가 조회 -->
    <select id="selectLuEvList" parameterType="com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTcDto">
        SELECT 
			T1.OPT_TXB_ID  /* 운영교과서ID */
			,T1.LLU_NOD_ID
   			,T1.LRMP_NOD_ID AS LRMP_NOD_ID4 /* 학습맵노드ID */
   			,T1.URNK_LRMP_NOD_ID /*상위노드ID*/
   			,T1.LRMP_NOD_NM AS LRMP_NOD_NM4 /* 학습맵노드명 */
   			,T2.DPTH  /* 깊이 */
   			,T2.RCSTN_ORDN  /* 재구성순서 */
   			,T2.LCKN_YN /* 잠금여부 */
   			,T2.USE_YN  /* 사용여부 */
   			,T2.LU_NO_USE_YN /* 단원번호사용여부 */
   			,A.LRN_ATV_ID                
   			,E.EV_ID
   			,E.EV_DTL_DV_CD
   			,E.LCKN_YN AS EV_LCKN_YN
   			,S.EVSH_TP_CD
   			,IF(R.EV_CMPL_YN = 'Y', 'Y', IF(IFNULL(R.TXM_STR_YN,'N') = 'Y','D','N')) AS EV_CMPL_YN
		FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN T1
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN T2                                        
			ON T1.OPT_TXB_ID = T2.OPT_TXB_ID                                        
			AND T1.LRMP_NOD_ID = T2.LLU_NOD_ID
			AND T2.USE_YN = 'Y'
			AND T2.DPTH = 4
		LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN A
			ON A.OPT_TXB_ID = #{optTxbId}
			AND T2.LRMP_NOD_ID = A.LRMP_NOD_ID
			AND A.USE_YN = 'Y'
		LEFT JOIN LMS_LRM.EA_EV E
			ON E.EV_ID = A.EV_ID
			AND E.USE_YN = 'Y'
			AND E.DEL_YN = 'N'
		LEFT JOIN LMS_CMS.BC_EVSH S
			ON E.EVSH_ID = S.EVSH_ID
			AND E.EVSH_CD = S.EVSH_CD
			AND S.USE_YN = 'Y'
			AND S.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.EA_EV_RS R
			ON E.EV_ID = R.EV_ID
			AND R.USR_ID = #{lrnUsrId}
		WHERE T1.OPT_TXB_ID = #{optTxbId}
			AND T1.USE_YN = 'Y'
			AND T1.DPTH = 1
			AND A.EV_ID IS NOT NULL
			AND S.EVSH_TP_CD IN ('ST', 'ET')
			and ((E.TXM_STR_DTM is null and E.TXM_END_DTM is null) or (E.TXM_STR_DTM <![CDATA[<]]> NOW() and NOW() <![CDATA[<]]> E.TXM_END_DTM))
		ORDER BY T1.RCSTN_ORDN ASC, T1.LRMP_NOD_ID ASC

        /* 교과학습 김형준 TlCmTxbStu-Mapper.xml - selectLuEvList */

    </select>

    <!-- 모든 학습차시정보조회 -->
    <select id="selectAllTxbTcList" parameterType="Map" resultType="HashMap">
        SELECT
                 R.OPT_TXB_ID
                ,R.LRMP_NOD_ID
                ,MAX(R.LRMP_NOD_NM) AS LRMP_NOD_NM
                ,MAX(S.LRMP_NOD_ID) AS URNK_LRMP_NOD_ID
                ,MAX(S.LRMP_NOD_NM) AS URNK_LRMP_NOD_NM
                ,MAX(R.DPTH) AS DPTH
                ,MAX(R.LCKN_YN) AS LCKN_YN
                <if test='lrnUsrId != null and lrnUsrId != ""'>
                    ,SUM(IF(R.DPTH  <![CDATA[>]]>  1 AND A.OPT_TXB_ID IS NOT NULL, 1, 0) )         AS CNT_TOT
                    ,SUM(IF(R.DPTH  <![CDATA[>]]>  1 AND B.LRN_ST_CD = 'CL' OR F.LRN_ST_CD = 'CL', 1, 0))                AS CNT_CL /* 학습완료 */
                    ,SUM(IF(R.DPTH  <![CDATA[>]]>  1 AND B.LRN_ST_CD  <![CDATA[<>]]>  'CL' OR F.LRN_ST_CD  <![CDATA[<>]]>  'CL', 1, 0)) AS CNT_NL
                </if>
                ,MAX(IFNULL(S.RCSTN_NO,"")) AS NOD_NO /* 대단원NO */
                ,MAX(IFNULL(A.EV_ID, ''))  AS EV_ID
                ,MAX(S.LU_NO_USE_YN)  AS LLU_NOD_NO_USE_YN
                ,MAX(R.LU_NO_USE_YN)  AS TC_NOD_NO_USE_YN
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  /* TL_교과학습노드재구성 */
            LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN S
                   ON R.OPT_TXB_ID = S.OPT_TXB_ID
                  AND R.LLU_NOD_ID = S.LRMP_NOD_ID
                  AND S.USE_YN = 'Y'
                  AND S.DPTH = 1
                  AND S.LU_EPS_YN = 'Y'
                  AND S.OPT_TXB_ID =  #{optTxbId}
            LEFT JOIN (
							SELECT * FROM
								(SELECT 
									AR.OPT_TXB_ID
									,AR.LRMP_NOD_ID
									,AR.LRN_ATV_ID
									,AR.LRN_STP_ID
									,AR.LRN_ATV_NM
									,AR.USE_YN
									,AR.RCSTN_ORDN
									,AR.EV_ID
								FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN AR
									WHERE AR.OPT_TXB_ID = #{optTxbId}

								UNION ALL

								SELECT
									M.OPT_TXB_ID
									,M.LRMP_NOD_ID
									,M.TCR_REG_CTN_ID AS LRN_ATV_ID
									,M.LRN_STP_ID
									,C.TCR_REG_CTN_NM AS LRN_ATV_NM
									,M.USE_YN
									,M.RCSTN_ORDN
									,'' AS EV_ID
								FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
								INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
									ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
								WHERE M.DEL_YN = 'N'
								AND M.USE_YN = 'Y'  AND M.OPT_TXB_ID=#{optTxbId}) ATV
							WHERE ATV.OPT_TXB_ID = #{optTxbId}) A  /* TL_교과학습활동재구성 */
                   ON R.OPT_TXB_ID = A.OPT_TXB_ID
                  AND R.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND A.USE_YN = 'Y'
            <if test='lrnUsrId != null and lrnUsrId != ""'>
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST B /* TL_교과학습활동상태 */
                   ON A.OPT_TXB_ID = B.OPT_TXB_ID
                  AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
                  AND A.LRN_ATV_ID = B.LRN_ATV_ID
                  AND B.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/
            LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st F
                		ON A.OPT_TXB_ID = F.OPT_TXB_ID
						AND A.LRN_ATV_ID = F.TCR_REG_CTN_ID
						AND F.LRN_USR_ID = #{lrnUsrId}
            </if>
        WHERE R.OPT_TXB_ID = #{optTxbId}
          AND R.DPTH = '4' /* 차시만 취득*/
          AND R.USE_YN = 'Y'
          AND R.LU_EPS_YN = 'Y'
        GROUP BY OPT_TXB_ID, LRMP_NOD_ID
        ORDER BY S.RCSTN_ORDN ASC, R.RCSTN_ORDN ASC

        /* 교과학습 강성희 TlCmTxbStu-Mapper.xml - selectAllTxbTcList */
    </select>

    <!-- 마지막 학습노드ID 찾기 -->
    <select id="selectLastLrmpNodId" parameterType="Map" resultType="Map">
     SELECT IFNULL(ATV.LRMP_NOD_ID,'') AS LRMP_NOD_ID
	 	FROM (SELECT B.MDF_DTM
		  		,IFNULL(B.LRN_TC_ID, A.LRMP_NOD_ID) AS LRMP_NOD_ID
			FROM (
            		SELECT 
					    	R.OPT_TXB_ID,
					        R.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        R.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        R.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        R.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'N' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
					    WHERE R.OPT_TXB_ID = #{optTxbId}
					      
						UNION ALL
						
					    SELECT 
					        R.OPT_TXB_ID,
					        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        TTRCM.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        TTRCM.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        TTRCM.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'Y' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
					        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
					        and ttrcm.del_yn = 'N'
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
					        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
					    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
					      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
					      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
      		INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_ST B /* TL_교과학습활동상태 */
                 ON B.OPT_TXB_ID = #{optTxbId}
                 AND A.ORGL_LRMP_NOD_ID = B.LRMP_NOD_ID
                 AND A.LRN_ATV_ID = B.LRN_ATV_ID
                 AND B.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/
      		WHERE A.OPT_TXB_ID = #{optTxbId}
      		AND A.USE_YN = 'Y'

      		UNION ALL

      		SELECT 	T.MDF_DTM
		  			,M.LRMP_NOD_ID
			FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
				ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
				AND C.TP_CD <![CDATA[<>]]> 'AT'
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn_st T
				ON T.OPT_TXB_ID = #{optTxbId}
				AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
				AND T.LRN_USR_ID = #{lrnUsrId}
			WHERE M.OPT_TXB_ID = #{optTxbId}
			AND M.DEL_YN = 'N'
			AND M.USE_YN = 'Y') ATV
	ORDER BY IFNULL( ATV.MDF_DTM, date_add(now(), interval -1 year)) DESC
	LIMIT 1
        /* 교과학습 강성희 TlCmTxbStu-Mapper.xml - selectLastLrmpNodId */
    </select>

    <!-- 익힘책콘텐츠건수 취득 -->
    <select id="selectCountWkbCtn" parameterType="Map" resultType="Integer">
        SELECT COUNT(1)
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
            INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B /* TL_교과학습노드재구성-차시 */
                ON A.OPT_TXB_ID = B.OPT_TXB_ID
                AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
                AND B.DPTH = 4
                AND B.USE_YN = 'Y'
                AND B.LU_EPS_YN = 'Y'
            INNER JOIN LMS_CMS.BC_LRN_STP C /* BC_학습단계 */
                ON C.LRMP_NOD_ID = A.LRMP_NOD_ID
                AND C.LRN_STP_ID = A.LRN_STP_ID
                AND C.LRN_STP_DV_CD = 'WB' /* 학습단계 구분(WB: 익힘책) */
                AND C.DEL_YN = 'N'
            INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                ON D.LRN_ATV_ID = A.LRN_ATV_ID
                AND D.USE_YN = 'Y'
                AND D.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
          AND A.LRMP_NOD_ID =  #{lrmpNodId} /* LCMS교과서_지식맵_키 */
          AND A.USE_YN = 'Y'  /* 사용여부 */

        /* 교과학습 강성희 TlCmTxbStu-Mapper.xml - selectCountWkbCtn */
    </select>

    <!-- 평가ID 취득-->
    <select id="selectEvId" parameterType="Map" resultType="String">
        SELECT A.EV_ID
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
            JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
                ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                AND A.LRN_STP_ID = B.LRN_STP_ID
                AND B.LRN_STP_DV_CD = 'EX'
            JOIN LMS_LRM.EA_EV C /* EA_평가 */
                ON A.EV_ID = C.EV_ID
                AND A.OPT_TXB_ID = C.OPT_TXB_ID
        WHERE A.OPT_TXB_ID = #{optTxbId}
          AND A.LRMP_NOD_ID = #{lrmpNodId}
          AND C.EV_DV_CD = 'SE' /* SE=교과학습평가 */
          AND C.EV_DTL_DV_CD = 'TO' /* TO=차시평가 */
        LIMIT 1

        /* 교과학습 강성희 TlCmTxbStu-Mapper.xml - selectEvId */
    </select>

    <!-- 학습활동클래스보드URL 취득-->
    <select id="selectTxbClsBrdUrl" parameterType="Map" resultType="String">
        SELECT IFNULL(A.CLS_BRD_URL, '') AS CLS_BRD_URL /* 클래스보드URL */
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
          AND A.LRMP_NOD_ID = #{lrmpNodId} /* 학습맵노드ID */
          AND A.LRN_ATV_ID  = #{lrnAtvId} /* 학습활동ID */
        /* 교과학습 강성희 TlCmTxbStu-Mapper.xml - selectTxbClsBrdUrl */
    </select>

    <!-- 학습활동클래스보드URL 취득 v3.3 대응-->
    <select id="selectTxbClabdUrl" parameterType="Map" resultType="Map">
        SELECT 
             IFNULL(CLABD_LRGS_NM, '')   AS CLABD_LRGS_NM
            ,IFNULL(CLABD_SML_NM, '')    AS CLABD_SML_NM
            ,IFNULL(CLABD_TYP, '')       AS CLABD_TYP
            ,IFNULL(CLABD_URL, '')       AS CLABD_URL
        FROM LMS_LRM.TL_SBC_LRN_ATV_CLABD /* TL_교과학습활동클래스보드 */
        WHERE OPT_TXB_ID = #{optTxbId}
          AND LRN_ATV_ID = #{lrnAtvId} 
          AND CLABD_LRGS_ID = #{clabdLrgsId}
        ORDER BY CLABD_ORDN ASC
        /* 교과학습 강성희 TlCmTxbStu-Mapper.xml - selectTxbClabdUrl */

    </select>

    <!-- 차시 정보 조회 (학생 홈) -->
    <select id="selectTxbTcListHm" parameterType="Map" resultType="com.aidt.api.tl.cmtxb.dto.TlCmTxbTcHmDto">
        SELECT LNR.lrmp_nod_id                  AS lrmpNodId,
        LNR.lrmp_nod_nm                         AS lrmpNodNm,
        LAR.lrn_atv_id                          AS atvId,
        (SELECT BLS.lrn_stp_nm
        FROM lms_cms.bc_lrn_stp BLS
        WHERE BLS.lrn_stp_id = LAR.lrn_stp_id)  AS stpNm,
        LAR.lrn_stp_id                          AS stpId,
        LLU.lrmp_nod_nm                         AS lluNodNm,
        IFNULL(LLU.rcstn_no, 0)                 AS lluNo,
        LLU.lu_no_use_yn                        AS lluNoUseYn,
        IFNULL(LNR.rcstn_no, 0)                 AS lrmpNodNo,
        LNR.lu_no_use_yn                        AS lrmpNodNoUseYn
        FROM (
        select LRMP_NOD_ID
        ,min(LRN_ATV_ID) as lrn_atv_id
        ,min(rcstn_ordn) as rcstn_ordn
        ,MAX(opt_txb_id) as opt_txb_id
        <choose>
            <when test="sbjCd == 'MA'">
                ,min(LRN_STP_ID) as lrn_stp_id
            </when>
            <otherwise>
                ,lrn_stp_id
            </otherwise>
        </choose>
        from lms_lrm.tl_sbc_lrn_atv_rcstn
        WHERE opt_txb_id = #{optTxbId}
        AND ctn_tp_cd = 'HT'
        and use_yn = 'Y'
        group by LRMP_NOD_ID
        <if test="sbjCd neq 'MA'">
            ,lrn_stp_id
        </if>
        ) LAR
        INNER JOIN lms_lrm.tl_sbc_lrn_nod_rcstn LNR ON LAR.lrmp_nod_id = LNR.lrmp_nod_id  AND LNR.USE_YN = 'Y' and LNR.LCKN_YN = 'N'  AND LAR.opt_txb_id = LNR.opt_txb_id
        INNER JOIN lms_lrm.tl_sbc_lrn_nod_rcstn LLU ON LLU.lrmp_nod_id = LNR.llu_nod_id  AND LLU.USE_YN = 'Y' and LLU.LCKN_YN = 'N' AND LAR.opt_txb_id = LLU.opt_txb_id
        WHERE LAR.opt_txb_id = #{optTxbId}
        AND LNR.dpth = 4
        ORDER BY LLU.rcstn_ordn, LNR.rcstn_ordn, LAR.rcstn_ordn;
        /* 교과학습 조영일 TlCmTxbStu-Mapper.xml - selectTxbTcListHm */
    </select>

    <!-- 차시 PC 썸네일 조회 -->
    <select id="selectTxbTcThbFile" parameterType="Map" resultType="Map">
        select NTM.lrmp_nod_id as lrmpNodId, IFNULL(FLE.fle_pth_nm, '') as flePthNm from
        LMS_CMS.bc_lrmp_nod_thb_mpn NTM
        <if test='type.equals("pc")'>
        INNER JOIN LMS_CMS.bc_upl_fle FLE ON NTM.pc_thb_upl_id = FLE.upl_fle_id AND FLE.del_yn = 'N'
        </if>
        <if test='type.equals("ta")'>
        INNER JOIN LMS_CMS.bc_upl_fle FLE ON NTM.ta_thb_upl_id = FLE.upl_fle_id AND FLE.del_yn = 'N'
        </if>
        WHERE NTM.lrmp_nod_id in
        <foreach collection="lrmpNodIds" item="lrmpNodId" index="index" separator="," open="(" close=")">
            #{lrmpNodId}
        </foreach>
    </select>


</mapper>
