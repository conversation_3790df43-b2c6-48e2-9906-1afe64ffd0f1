package com.aidt.api.al.pl.sbclrn.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc [학습창 학습목록 조회 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlSbclrnStuResponseDto {
	@Parameter(name="대단원No")
	private String lluNodNo;
	
	@Parameter(name="대단원ID")
	private String lluNodId;
	
	@Parameter(name="대단원명")
	private String lluNodNm;
	
	@Parameter(name="대단원노출여부")
	private String lluEpsYn;
	
//	@Parameter(name="대단원사용여부")
//	private String lluUseYn;
	
	@Parameter(name="중단원ID")
	private String mluNodId;
	
	@Parameter(name="중단원명")
	private String mluNodNm;
	
	@Parameter(name="중단원노출여부")
	private String mluEpsYn;
	
//	@Parameter(name="중단원사용여부")
//	private String mluUseYn;
	
	@Parameter(name="소단원ID")
	private String sluNodId;
	
	@Parameter(name="소단원명")
	private String sluNodNm;
	
	@Parameter(name="소단원노출여부")
	private String sluEpsYn;
	
//	@Parameter(name="중단원사용여부")
//	private String sluUseYn;
	
	@Parameter(name="차시ID")
	private String tcNodId;
	
	@Parameter(name="차시명")
	private String tcNodNm;
	
	@Parameter(name="차시노출여부")
	private String tcEpsYn;
	
	@Parameter(name="차시잠금여부")
	private String lcknYn;
	
//	@Parameter(name="차시사용여부")
//	private String tcUseYn;
	
	@Parameter(name="총페이지")
	private String atvTotCnt;
	
	@Parameter(name="현재페이지")
	private String strLrnAtvId;
	
	@Parameter(name="교과시작페이지")
	private String txbStrPgeNo;
	
	@Parameter(name="교과종료페이지")
	private String txbEndPgeNo;
	
	@Parameter(name="교과학습사용여부")
	private String txbUseYn;
	
	@Parameter(name="익힘시작페이지")
	private String wkbStrPgeNo;
	
	@Parameter(name="익힘종료페이지")
	private String wkbEndPgeNo;
	
	@Parameter(name="익힘학습사용여부")
	private String wkbUseYn;
	
	@Parameter(name="교과서PDF정보목록")
	private List<TxbPdfDto> txbPdfList;
	
	@Parameter(name="익힘책PDF정보목록")
	private List<WkbPdfDto> wkbPdfList;
	
	@Parameter(name="학습도구목록")
	private List<LrnTlDto> lrnTlList;
	
	@Parameter(name="학습단계")
	private List<LrnStpDto> lrnStpList;
    
}
