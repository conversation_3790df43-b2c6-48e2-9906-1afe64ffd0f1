package com.aidt.api.ea.evcom.adapter;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.aidt.api.ea.evcom.dto.EaEvAnswerNote;
import com.aidt.api.ea.evcom.dto.EaEvLearnActivityDto;
import com.aidt.api.ea.evcom.dto.EaEvResult;
import com.aidt.api.ea.evcom.dto.EaEvSppNtnResult;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class EaEvAnswerAdapter {

	private final String MAPPER_NAMESPACE = "api.ea.evcom.";
	private final CommonDao commonDao;

	public EaEvResult getEaEvRs(Integer evId, String usrId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectEvRs", Map.of("evId", evId, "usrId", usrId));
	}

	public void upsertEaEvRs(EaEvResult dto) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaEvRs", dto);
	}

	public void upsertReEaEvRs(EaEvResult dto) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertReEaEvRs", dto);
	}

	public void upsertEvaEvAnswers(EaEvResult dto) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaEvAnswers", dto);
	}

	public void upsertReEaEvAnswers(EaEvResult dto) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertReEaEvAnswers", dto);
	}

	public void upsertEaEvAnswerNote(EaEvAnswerNote dto) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaEvAnswerNote", dto);
	}

	public void upsertReEaEvAnswerNote(EaEvAnswerNote dto) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertReEaEvAnswerNote", dto);
	}

	public void updateAnswerExplanationStatus(Integer evId, String usrId) {
		commonDao.update(MAPPER_NAMESPACE + "updateEvRsAnswerExplanationStatus", Map.of("evId", evId, "usrId", usrId));
	}

	public EaEvLearnActivityDto getNodeOrActivityId(Integer evId, String optTxbId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectEvLrnAtv", Map.of("evId", evId, "optTxbId", optTxbId));
	}

	public void upsertEaEvSppNtnRs(EaEvSppNtnResult eaEvSppNtnResult) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaEvSppNtnRs", eaEvSppNtnResult);
	}

	public void upsertEaEvSppNtnAnswers(EaEvSppNtnResult eaEvSppNtnResult) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaEvSppNtnAnswers", eaEvSppNtnResult);
	}

}