package com.aidt.api.bc.cm.constants;

import lombok.Getter;

@Getter
public enum BcCmKerisApiEnum {

    // 학생 관련 API URL
    STU_NAME("/aidt_userinfo/student/name"),
    STU_SCHOOL_NAME("/aidt_userinfo/student/school_name"),
    STU_SCHOOL_ID("/aidt_userinfo/student/school_id"),
    STU_DIVISION("/aidt_userinfo/student/division"),
    STU_GRADE("/aidt_userinfo/student/grade"),
    STU_CLASS("/aidt_userinfo/student/class"),
    STU_NUMBER("/aidt_userinfo/student/number"),
    STU_SCHEDULE("/aidt_userinfo/student/schedule"),
    STU_GENDER("/aidt_userinfo/student/gender"),
    STU_CLASS_MEMBER("/aidt_userinfo/student/class_member"),
    STU_ALL("/aidt_userinfo/student/all"),

    // 교사 관련 API URL
    STU_ALL_NAME("/aidt_userinfo/student/name"),
    TCR_NAME("/aidt_userinfo/teacher/name"),
    TCR_SCHOOL_NAME("/aidt_userinfo/teacher/school_name"),
    TCR_SCHOOL_ID("/aidt_userinfo/teacher/school_id"),
    TCR_SCHEDULE("/aidt_userinfo/teacher/schedule"),
    TCR_CLASS_LIST("/aidt_userinfo/teacher/class_list"),
    TCR_CLASS_MEMBER("/aidt_userinfo/teacher/class_member"),
    TCR_ALL("/aidt_userinfo/teacher/all"),
	
	// DASH BOARD
	INITIALIZED("/aidt_dashboard/initialized"),
	PROGRESSED("/aidt_dashboard/curriculum_progressed"),
	SCORE("/aidt_dashboard/curriculum_score"),
	COMPLETED("/aidt_dashboard/curriculum_completed"),
	TERMINATED("/aidt_dashboard/terminated");

    private final String url;

    BcCmKerisApiEnum(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return url;
    }
}
