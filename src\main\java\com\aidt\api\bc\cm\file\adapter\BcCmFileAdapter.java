package com.aidt.api.bc.cm.file.adapter;

import java.util.List;

import org.springframework.stereotype.Component;

import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class BcCmFileAdapter {

	private final String MAPPER_NAMESPACE = "api.bc.common.cm.";
	private final CommonDao commonDao;

	public void saveAnnx(BcAnnxFleDto bcAnnxFleDto) {
		commonDao.insert(MAPPER_NAMESPACE + "insertAnnx", bcAnnxFleDto);
	}

	public BcAnnxFleDto getAnnxFile(Long annxFleId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectAnnxFle", annxFleId);
	}

	public void saveAnnxFile(List<BcAnnxFleDto> bcAnnxFleDtos) {
		bcAnnxFleDtos.forEach(this::saveAnnxFile);
	}

	public void saveAnnxFile(BcAnnxFleDto bcAnnxFleDto) {
		commonDao.insert(MAPPER_NAMESPACE + "insertAnnxFle", bcAnnxFleDto);
	}

	public void removeAnnxFile(Long annxFleId, String dbId) {
		commonDao.delete(MAPPER_NAMESPACE + "deleteAnnxFle", BcAnnxFleDto.builder()
			.annxFleId(annxFleId)
			.dbId(dbId)
			.build());
	}
}