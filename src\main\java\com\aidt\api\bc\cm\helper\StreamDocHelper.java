package com.aidt.api.bc.cm.helper;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class StreamDocHelper {

	@Value("${aidt.endpoint.viewerApi.url:https://v01.aitextbook.co.kr/streamdocs/}")
	private String url;

	@Value("${aidt.endpoint.viewerApi.id:api-admin}")
	private String id;

	@Value("${aidt.endpoint.viewerApi.password:Cjsworydbr12#$}")
	private String pwd;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String bucketName;

	//todo: 프로퍼티 파일로 변환
	private final String cdnUrl = "https://aidtcdn-op-dev.aitextbook.co.kr/";

	private final WebFluxUtil webFluxUtil;

	private final List<String> SUPPORTED_EXTENSIONS = Arrays.asList(
		// PDF 관련 확장자
		"pdf",
		// MS Office 문서
		"doc", "docx", "xls", "xlsx", "ppt", "pptx",
		// 한글 문서
		"hwp", "hwpx",
		// Open Document 문서
		"odt",
		// 이미지 파일
		"jpg", "jpeg", "jpe", "png", "bmp", "tiff", "tif", "gif");

	@Getter
	@AllArgsConstructor
	@NoArgsConstructor
	public static class Token {

		private String accessToken;
		private String refreshToken;
	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class StreamDocRequest {

		private String docType;
		private String name;
		private String externalResource;

	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class StreamDocResponse {

		private String streamdocsId;
		private String alink;
		@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
		private LocalDateTime createdAt;
		private boolean crypted;
		private boolean deleted;
		private String docName;
		private String externalId;
		private long fileSize;
		private String givenName;
		private boolean hasPassword;
		@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
		private LocalDateTime lastAccessedAt;
		private boolean originExists;

		private String type;
		@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
		private LocalDateTime updatedAt;
	}

	private HttpHeaders login() {
		HttpHeaders headers = new HttpHeaders();
		headers.add("Content-Type", "application/json");
		var authUrl = String.format(url + "v4/auth?id=%s&password=%s",
			URLEncoder.encode(id, StandardCharsets.UTF_8),
			URLEncoder.encode(pwd, StandardCharsets.UTF_8));
		Token Token = webFluxUtil.post(authUrl, headers, "", Token.class);
		headers.add("Cookie", "accessToken=" + Token.getAccessToken());
		return headers;
	}

	public String save(String s3Path) {
		if (!validation(s3Path)) {
			return null;
		}
		HttpHeaders headers = login();
		var res = webFluxUtil.post(url + "v4/documents/external-resources", headers,
			StreamDocRequest.builder()
				.externalResource(s3Path2CdnPath(s3Path))
				.name("")
				.docType("")
				.build()
			, StreamDocResponse.class);

		return res.getStreamdocsId();
	}

	private boolean validation(String s3Path) {
		String extension = FilenameUtils.getExtension(s3Path);
		return SUPPORTED_EXTENSIONS.contains(extension);
	}

	private String s3Path2CdnPath(String s3Path) {
		return (bucketName.contains("dev") ? cdnUrl : cdnUrl.replace("-op-dev", "")) + s3Path;

	}

}
