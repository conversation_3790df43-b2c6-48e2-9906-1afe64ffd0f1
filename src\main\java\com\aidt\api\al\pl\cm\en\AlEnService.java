package com.aidt.api.al.pl.cm.en;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.common.AlCmUtil;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlLrnwEvInfoDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto;
import com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-11
 * @desc AI맞춤 단원리스트 영어
 */
@Slf4j
@Service
public class AlEnService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.en.";
	private final String MAPPER_NAMESPACE2 = "api.al.pl.back.aiRcmTsshQtm.";
	@Autowired private CommonDao commonDao;
	@Autowired AiRcmTsshQtmCommService commService;
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
	//중단원리스트
	public List<AiRcmTsshQtmDto> selectMluTcList(AiRcmTsshQtmDto req) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectMluTcList", req);
	}
	public List<AiRcmTsshQtmDto> selectAeEvInfoList(AiRcmTsshQtmDto req) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAeEvInfoList", req);
	}
	
	/**
     * 영어 중단원리스트
     * 
     * @param 
     * @return List<AlMluTcLstInqStuResponseDto>
     */
	@Cacheable(
			cacheNames = "shortCache",
			key = "'al:' + #req.optTxbId + ':selectEnMluList:' + #req.usrId",
			condition = "#saveYN != null",
			cacheManager = "aidtCacheManager"
	)
	public Map<String,Object> selectEnMluList(AiRcmTsshQtmDto req, String saveYN) {
		Map<String,Object> resultMap = new HashMap<>();
		
		List<AiRcmTsshQtmDto> mluTcList = this.selectMluTcList(req);
		List<AiRcmTsshQtmDto> aeEvInfoList = this.selectAeEvInfoList(req);
		req.setSbjCd("EN");
		List<AiRcmTsshQtmDto> ccptVdList = commService.selectCcptVdList(req);
		
		// 최근학습리스트
		List<AiRcmTsshQtmDto> recentLearningList = aeEvInfoList;
		// mdfDtmOrder 기준으로 정렬
//        Collections.sort(recentLearningList, new Comparator<AiRcmTsshQtmDto>() {
//            public int compare(AiRcmTsshQtmDto o1, AiRcmTsshQtmDto o2) {
//                return Integer.compare(Integer.parseInt(o1.getMdfDtmOrder()), Integer.parseInt(o2.getMdfDtmOrder()));
//            }
//        });
        
        //evId 기준으로 정렬
        Collections.sort(recentLearningList, new Comparator<AiRcmTsshQtmDto>() {
            public int compare(AiRcmTsshQtmDto o1, AiRcmTsshQtmDto o2) {
                return Integer.compare(o2.getEvId(), o1.getEvId());
            }
        });
		
		List<Map<String,Object>> mluList = new ArrayList<>();
		List<String> mluStr = new ArrayList<>();
		for (AiRcmTsshQtmDto mlu : mluTcList) {//1dpth
			if(mluStr.contains(mlu.getMluKmmpNodId())) {
				continue;
			}
			//단원완료여부
			boolean luevCmplYn = true;
			
			//중단원맵
			Map<String, Object> mluMap = new HashMap<>();
			mluMap.put("luImgPth", mlu.getLuImgPth());
			mluMap.put("lluKmmpNodId", mlu.getLluKmmpNodId());
			mluMap.put("lluKmmpNodNm", mlu.getLluKmmpNodNm());
			mluMap.put("mluKmmpNodId", mlu.getMluKmmpNodId());
			mluMap.put("mluKmmpNodNm", mlu.getMluKmmpNodNm());
			mluMap.put("tcEpsYn", mlu.getTcEpsYn());
			mluMap.put("tcUseYn", mlu.getTcUseYn());
			mluMap.put("useYn", mlu.getUseYn());
			mluMap.put("lcknYn", mlu.getLcknYn());
			mluMap.put("rcstnOrdn", mlu.getRcstnOrdn());
			mluMap.put("orglOrdn", mlu.getOrglOrdn());
			mluMap.put("ovQtmCnt", mlu.getOvQtmCnt());
			
			//단원 학습자수준
	    	String lrnrVelTpCd = selectEnLuLrnrVelTpCd(mlu.getMluKmmpNodId(), req.getUsrId());
	    	mluMap.put("lrnrVelTpCd", lrnrVelTpCd);
			
			//진단평가
			for (AiRcmTsshQtmDto ug : aeEvInfoList) {
				if(mlu.getMluKmmpNodId().equals(ug.getMluKmmpNodId())
						&& ug.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
					Map<String, String> ugMap = new HashMap<>();
					ugMap.put("evId", ug.getEvId().toString());
					ugMap.put("evDtlDvCd", ug.getEvDtlDvCd());
					ugMap.put("evCmplYn", ug.getEvCmplYn());
					ugMap.put("mdfDtm", ug.getMdfDtm());
					mluMap.put(ug.getEvDtlDvCd(), ugMap);
				}
			}//ev end
			
			//차시리스트
			List<Map<String, Object>> tcList = new ArrayList<>();
			List<String> tcStr = new ArrayList<>();
			//개념영상리스트
			List<Map<String, Object>> vdMapList = new ArrayList<>();
			
			for (AiRcmTsshQtmDto tc : mluTcList) {
				if(tcStr.contains(tc.getTcKmmpNodId())) {
					continue;
				}
				
				
				if (mlu.getMluKmmpNodId().equals(tc.getMluKmmpNodId())) {
					
					Map<String, Object> tcMap = new HashMap<>();
					tcMap.put("tcKmmpNodId", tc.getTcKmmpNodId());
					tcMap.put("tcKmmpNodNm", tc.getTcKmmpNodNm());
					
					
					if (tc.getTpcAvn() < AlConstUtil.TPC_AVN_01) {
						tcMap.put("tcAvn", "01");
					} else if (tc.getTpcAvn() >= AlConstUtil.TPC_AVN_01 && tc.getTpcAvn() < AlConstUtil.TPC_AVN_03) {
						tcMap.put("tcAvn", "02");
					} else {
						tcMap.put("tcAvn", "03");	
					}

					// 진단평가 차시별 평균점수가 0점인 경우에 개념영상을 제공함(2024.12.05)
					if (null != tc.getTcAvgCansRt() && 0 == tc.getTcAvgCansRt()) {
						//개념영상 추가
						List<AiRcmTsshQtmDto> newCcptVdList = new ArrayList<>(ccptVdList);
						newCcptVdList.removeIf(AiRcmTsshQtmDto -> !(AiRcmTsshQtmDto.getMluKmmpNodId().equals(tc.getMluKmmpNodId())));
						for (AiRcmTsshQtmDto vd : newCcptVdList) {
							Map<String, Object> vdMap = new HashMap<>();
							vdMap.put("tpcStat", vd.getTpcStat());
							vdMap.put("aiLrnAtvId", vd.getAiLrnAtvId());
							vdMap.put("aiLrnAtvNm", vd.getAiLrnAtvNm());
							vdMap.put("cdnPthNm", AlCmUtil.makeFleCdnUrl(BUCKET_NAME, vd.getCdnPthNm())+ "images/poster.png");
							vdMap.put("lrnStCd", vd.getLrnStCd());
							vdMap.put("tcKmmpNodNm", vd.getTcKmmpNodNm());
							vdMapList.add(vdMap);
						}
					}
					
					//차시 플래그
					if (tc.getTcAvgCansRt() != null) {
						String tcAvgCansRtStr = "";
						if (tc.getTcAvgCansRt() < 50) {
							tcAvgCansRtStr = "기초";
						} else if (tc.getTcAvgCansRt() >= 90) {
							tcAvgCansRtStr = "심화";
						} else {
							tcAvgCansRtStr = "기본";
						}
						tcMap.put("tcAvgCansRtStr", tcAvgCansRtStr);
						tcMap.put("tcAvgCansRt", tc.getTcAvgCansRt());
					}
					
					List<String> tpc01 = new ArrayList<>();
					List<String> tpc02 = new ArrayList<>();
					List<String> tpc03 = new ArrayList<>();
					for (AiRcmTsshQtmDto tpc : mluTcList) {
						if (tc.getTcKmmpNodId().equals(tpc.getTcKmmpNodId())) {
							if (tc.getTpcAvn() < AlConstUtil.TPC_AVN_01) {
								tpc01.add(tpc.getTpcKmmpNodNm());
							} else if (tc.getTpcAvn() > AlConstUtil.TPC_AVN_01 && tc.getTpcAvn() < AlConstUtil.TPC_AVN_03) {
								tpc03.add(tpc.getTpcKmmpNodNm());
							} else {
								tpc02.add(tpc.getTpcKmmpNodNm());	
							}
						}
					}
					tcMap.put("tpc01", tpc01);
					tcMap.put("tpc02", tpc02);
					tcMap.put("tpc03", tpc03);
					
					String tcevCmplYn = "";
					String luevCmplBs = "";
					
					// 초등의 경우 C2까지만(2025.01.09 수정 요청)
					if (req.getSchlGrdCd() != null && req.getSbjCd() != null) {
						if (req.getSchlGrdCd().equals(AlConstUtil.SCHL_GRD_CD_ESCH)) {
							luevCmplBs = "C2";
						}
						
						// 중고등의 경우 C2까지만
						if (req.getSchlGrdCd().equals(AlConstUtil.SCHL_GRD_CD_MCLS) || req.getSchlGrdCd().equals(AlConstUtil.SCHL_GRD_CD_HGH)) {
							luevCmplBs = "C2";
						}
						
					}
					
					for (AiRcmTsshQtmDto evInfo : aeEvInfoList) {
						if (tc.getMluKmmpNodId().equals(evInfo.getMluKmmpNodId())
								&& tc.getTcKmmpNodId().equals(evInfo.getTcKmmpNodId())
								&& !evInfo.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {

							//단원완료여부 모든차시가 완료되어야 한다. 
							if (!evInfo.getLuevCmplYn().equals("Y")) {
								luevCmplYn = false;
							}
							//차시완료여부
							tcevCmplYn = evInfo.getLuevCmplYn();
							
							// 보통 학습자 && 맞춤2 정답갯수에 맞춰 C3 여부 결정
//							if (lrnrVelTpCd.equals("NM") && evInfo.getEvDtlDvCd().equals("C2") && evInfo.getEvCmplYn().equals("Y") && req.getSchlGrdCd() != null && req.getSbjCd() != null){
//								luevCmplBs = commService.enLuevCmplBsChk(req.getUsrId(), lrnrVelTpCd, req.getOptTxbId(), mlu.getMluKmmpNodId(), tc.getTcKmmpNodId(), req.getSchlGrdCd(), req.getSbjCd());
//							}
							
							Map<String, String> evMap = new HashMap<>();
							evMap.put("evId", evInfo.getEvId().toString());
							evMap.put("evDtlDvCd", evInfo.getEvDtlDvCd());
							evMap.put("evCmplYn", evInfo.getEvCmplYn());
							evMap.put("mdfDtm", evInfo.getMdfDtm());
							tcMap.put(evInfo.getEvDtlDvCd(), evMap);
						}
					}//ev end
					tcMap.put("luevCmplBs", luevCmplBs);
					tcMap.put("tcevCmplYn", tcevCmplYn);
					tcList.add(tcMap);
					tcStr.add(tc.getTcKmmpNodId());
				}
			}//tc end
			mluMap.put("luevCmplYn", luevCmplYn ? "Y" : "N");
			mluMap.put("tcLIst", tcList);
			mluMap.put("vdList", vdMapList);
			
			mluList.add(mluMap);
			
			mluStr.add(mlu.getMluKmmpNodId());
		}//mlu end
		
		resultMap.put("mluList", mluList);
		resultMap.put("recentLearningList", recentLearningList);
		
		log.debug("resultMapList: " + resultMap);
		return resultMap;
	}
	
	/**
     * AI 진단평가리포트
     * 
     * @param 
     * @return Map<String,Object>
     */
	public Map<String,Object> selectAlPlEvQtmAnwList(AlPlEaEvMainReportDto req) {
		Map<String,Object> resultMap = new HashMap<>();
		
		//평가 답변 정보
		List<AlPlEaEvComQtmAnwDto> qtmAnwList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlPlEvQtmAnwList", req);
		List<AiRcmTsshQtmDto> tcRptList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlEvTcRptList", req);
		resultMap.put("qtmAnwList", qtmAnwList);
		resultMap.put("tcRptList", tcRptList);
		
		return resultMap;
	}
	
	/**
	 * AI 쌓인 오답문항개수 & 단원평가 여부
	 * 
	 * @param 
	 * @return Map<String,Object>
	 */
	@Cacheable(
			cacheNames = "shortCache",
			key = "'al:' + #req.optTxbId + ':selectIansQtmSeUgCntEn:' + #req.usrId",
			condition = "#saveYN != null",
			cacheManager = "aidtCacheManager"
	)
	public Map<String,Object> selectIansQtmSeUgCnt(AlMluTcLstInqStuReqDto req, String saveYN) {
		Map<String,Object> resultMap = new HashMap<>();
		
		
		int iansQtmCnt = commonDao.select(MAPPER_NAMESPACE + "selectIansQtmCnt", req);
		List<AlMluTcLstInqStuResponseDto> evSeUg = commonDao.selectList(MAPPER_NAMESPACE + "selectEvSeUg", req);
		resultMap.put("iansQtmCnt", iansQtmCnt);
		if(evSeUg.size() > 0) {
			resultMap.put("evSeUg", evSeUg.get(0));
		} else {
			resultMap.put("evSeUg", null);		
		}
		
		return resultMap;
	}
	
	/**
	 * 차시숙련도 계산후 반환
	 * @param optTxbId
	 * @param mluKmmpNodId
	 * @param usrId
	 * */
	public List<AiRcmTsshQtmDto> getTcAvn(AiRcmTsshQtmDto req){
		List<AiRcmTsshQtmDto> tcAvnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTcAvnList", req);
    	for (AiRcmTsshQtmDto tc : tcAvnList) {
    		//토픽점수
			Double tcAvn = 0.5;
			
			//토픽별 문항풀이가 없는경우 AI예측점수로 판단
			if(tc.getTxmPn() <= 1 && tc.getAiPredAvgScr() == 0.5) {
				tcAvn = tc.getAiPredAvgCansRt();
			}
			//토픽별 문항풀이 이력이 1건인 경우 실제점수7 : 예측점수3
			else if(tc.getTxmPn() == 1 && tc.getAiPredAvgScr() == 0.5) {
				tcAvn = ((tc.getTcAvn() * 7) + (tc.getAiPredAvgCansRt() * 3)) / 10;
			}
			//실제점수(가중치)로 판단
			else {
				tcAvn = tc.getTcAvn();
			}
			tc.setTcAvn(tcAvn);
		}
    	return tcAvnList;
	}
	
	
	/**
	 * AI 영어 레포트
	 * 
	 * @param optTxbId
	 * @param mluKmmpNodId
	 * @param usrId
	 * @param evDtlDvCd
	 * @return Map<String,Object>
	 */
	public Map<String,Object> selectReportTcAvnInfo(AiRcmTsshQtmDto req){
    	Map<String,Object> resultMap = new HashMap<String, Object>();
    	
    	Integer tcTotalCnt = 0;//전체 영역
    	Integer tpcTotalCnt = 0;//전체 토픽수
    	List<String> tc03List = new ArrayList<String>();
    	List<String> tc0102List = new ArrayList<String>();
    	
    	//영역(차시)별 숙련도
    	List<AiRcmTsshQtmDto> tcAvnList = getTcAvn(req);
    	for (AiRcmTsshQtmDto tc : tcAvnList) {
    		tpcTotalCnt += tc.getTpcKmmpNodCnt();
    		//토픽점수
			Double tcAvn = tc.getTcAvn();
			
			if(tcAvn == null) {
				tc0102List.add(tc.getTcKmmpNodNm());
				tcTotalCnt++;
			}else if(tcAvn < AlConstUtil.TPC_AVN_01) {
				tc0102List.add(tc.getTcKmmpNodNm());
				tcTotalCnt++;
			}else if(tcAvn > AlConstUtil.TPC_AVN_03) {
				tc03List.add(tc.getTcKmmpNodNm());
				tcTotalCnt++;
			}else {
				tc0102List.add(tc.getTcKmmpNodNm());
				tcTotalCnt++;
			}
		}
    	resultMap.put("tc03List", tc03List);
    	resultMap.put("tc0102List", tc0102List);
    	resultMap.put("tcTotalCnt", tcTotalCnt);
    	resultMap.put("tpcTotalCnt", tpcTotalCnt);
    	
    	//차시완료여부
    	List<AlLrnwEvInfoDto> tcCmplYnList = tcCmplYnList(req);
    	for (AlLrnwEvInfoDto tc : tcCmplYnList) {
    		//AI코멘트 API호출 파라미터용 차시명
			String tcNmLower = tc.getTcKmmpNodNm().toLowerCase();
			tc.setTcKmmpNodNm2(tcNmLower);
			
			if(tcNmLower.trim().equals("알파벳")) {tc.setTcKmmpNodNm2("alphabet");}
			if(tcNmLower.trim().equals("문법")) {tc.setTcKmmpNodNm2("grammar");}
			if(tcNmLower.trim().equals("듣기")) {tc.setTcKmmpNodNm2("listening");}
			if(tcNmLower.trim().equals("파닉스")) {tc.setTcKmmpNodNm2("phonics");}
			if(tcNmLower.trim().equals("읽기")) {tc.setTcKmmpNodNm2("reading");}
			if(tcNmLower.trim().equals("말하기")) {tc.setTcKmmpNodNm2("speaking");}
			if(tcNmLower.trim().equals("단어")) {tc.setTcKmmpNodNm2("word");}
			if(tcNmLower.trim().equals("쓰기")) {tc.setTcKmmpNodNm2("writing");}
			if(tcNmLower.trim().equals("어휘")) {tc.setTcKmmpNodNm2("vocabulary");}
			
		}
    	resultMap.put("tcCmplYnList", tcCmplYnList);
    	
    	//단원 학습자수준
    	String lrnrVelTpCd = selectEnLuLrnrVelTpCd(req.getMluKmmpNodId(), req.getUsrId());
    	resultMap.put("lrnrVelTpCd", lrnrVelTpCd);
    	
    	//강약점 토픽
    	List<AiRcmTsshQtmDto> tpcList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcList", req);
    	String tpc03 = Collections.max(tpcList, Comparator.comparing(AiRcmTsshQtmDto::getTpcAvn)).getTpcKmmpNodNm();
    	String tpc01 = Collections.min(tpcList, Comparator.comparing(AiRcmTsshQtmDto::getTpcAvn)).getTpcKmmpNodNm();
    	resultMap.put("tpc03", tpc03);
    	resultMap.put("tpc01", tpc01);
    	
    	return resultMap;
    }
	
	
	//차시별 완료여부
    public List<AlLrnwEvInfoDto> tcCmplYnList(AiRcmTsshQtmDto req){
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectTcCmplYnList", req);
    }
	
	
	/**
	 * 영어는 사용자수준이 차시별로 저장되기떄문에 단원 전체의 사용자수준이 필요한경우 해당서비스에서 계산
	 * @param mluKmmpNodId
	 * @param usrId
	 * @return lrnrVelTpCd
	 * 
	 * */
	public String selectEnLuLrnrVelTpCd(String mluKmmpNodId, String usrId) {
		String lrnrVelTpCd = "NM";
		
		AiRcmTsshQtmDto req = new AiRcmTsshQtmDto();
		req.setMluKmmpNodId(mluKmmpNodId);
		req.setUsrId(usrId);
		List<AiRcmTsshQtmDto> tcCansYCntList = commonDao.selectList(MAPPER_NAMESPACE + "selectEnLuLrnrVelTpCd", req);
		
		Integer tcCnt = tcCansYCntList.size();
		Integer ugYCnt = 0;
		if(!tcCansYCntList.isEmpty()) {
			for (AiRcmTsshQtmDto tc : tcCansYCntList) {
				ugYCnt += tc.getUgMmYCnt();
			}

			// 초등영어3 김태은 1단원 진단평가의 경우 차시 전체 개수로 계산
			if(mluKmmpNodId.equals("20634")) {
				if(ugYCnt >= AlConstUtil.EN_EXCEPTION_LRNR_VEL_FS) {
					lrnrVelTpCd = "FS";
				}
				else if(ugYCnt >= AlConstUtil.EN_EXCEPTION_LRNR_VEL_NM && ugYCnt < AlConstUtil.EN_EXCEPTION_LRNR_VEL_FS) {
					lrnrVelTpCd = "NM";
				}
				else if(ugYCnt >= AlConstUtil.EN_EXCEPTION_LRNR_VEL_SL && ugYCnt < AlConstUtil.EN_EXCEPTION_LRNR_VEL_NM) {
					lrnrVelTpCd = "SL";
				}
			} else {
				if(ugYCnt == AlConstUtil.EN_LRNR_VEL_FS * tcCnt) {
					lrnrVelTpCd = "FS";
				}
				else if(ugYCnt == AlConstUtil.EN_LRNR_VEL_NM * tcCnt) {
					lrnrVelTpCd = "NM";
				}
				else if(ugYCnt == AlConstUtil.EN_LRNR_VEL_SL * tcCnt) {
					lrnrVelTpCd = "SL";
				}
			}
		}
		
		return lrnrVelTpCd;
	}

	/**
	 * 단원 평가 여부 
	 * 
	 * @return 
	 * */
	public Object selectEvSeUg(@Valid AlMluTcLstInqStuReqDto dto) {
		List<AlMluTcLstInqStuResponseDto> evSeUg = commonDao.selectList(MAPPER_NAMESPACE + "selectEvSeUg", dto);
		if (evSeUg.size() > 0) {
			return evSeUg.get(0);			
		} else {
			return null;
		}
	}
	
	
}