<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdminStat">

	<select id="selectLoginEventAddData" resultType="com.aidt.api.at.token.dto.LoginEventDto">
		SELECT
			 c.schl_cd
			,c.sgy
			,c.schl_grd_cd
		    ,c.keris_cla_cd
			,txb.txb_nm
		    ,txb.autr_cd
			,txb.sbj_cd
			,NOW() AS login_dtm
		FROM
			lms_lrm.cm_token t
		    INNER JOIN lms_lrm.cm_cla c
				ON c.cla_id = t.cla_id
		    INNER JOIN lms_cms.bc_txb txb
				ON txb.txb_id = t.txb_id
		WHERE
			t.usr_id = #{usrId}
	</select>
	
	<select id="selectCla" resultType="com.aidt.api.bc.cm.dto.BcClaDto">
		SELECT
			 c.cla_id
			,c.schl_cd
		    ,c.sgy
		    ,c.cla_no
		    ,c.cla_nm
		    ,c.classroom_nm
		    ,c.keris_cla_cd
		    ,c.crtr_id
		    ,c.crt_dtm
		    ,c.mdfr_id
		    ,c.mdf_dtm
		FROM
			lms_lrm.cm_cla c
		WHERE
			c.cla_id = #{claId}
	</select>
	
	<select id="selectOptTxb" resultType="com.aidt.api.bc.cm.dto.BcCmOptTxb">
		SELECT
			 ot.opt_txb_id
			,ot.txb_id
		    ,ot.cla_id
		    ,ot.keris_lect_cd
		    ,ot.crtr_id
		    ,ot.crt_dtm
		    ,ot.mdfr_id
		    ,ot.mdf_dtm
		FROM
			lms_lrm.cm_opt_txb ot
		WHERE
			ot.opt_txb_id = #{optTxbId}
	</select>
	
	<select id="selectUsr" resultType="com.aidt.api.bc.cm.dto.BcCmUsrDto">
		SELECT
			 u.usr_id
			,u.keris_usr_id
		    ,u.usr_nm
		    ,u.usr_tp_cd
		    ,u.fst_reg_dtm
		    ,u.cla_id
		    ,u.crtr_id
			,u.crt_dtm
			,u.mdfr_id
			,u.mdf_dtm
		FROM
			cm_usr u
		WHERE
			u.usr_id = #{usrId}
	</select>
	
	<select id="selectOptTxbPrid" resultType="com.aidt.api.at.token.dto.OptTxbPridDto">
		SELECT
			 otp.opt_txb_id
			,otp.opt_txb_prid
		    ,otp.keris_lect_cd
		    ,otp.crtr_id
			,otp.reg_dtm
		FROM
			cm_opt_txb_prid otp
		WHERE
			otp.opt_txb_id = #{optTxbId}
		ANd otp.opt_txb_prid = #{optTxbPrid}
	</select>
	
</mapper>