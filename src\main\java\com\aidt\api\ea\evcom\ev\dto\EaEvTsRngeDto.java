package com.aidt.api.ea.evcom.ev.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-25
 * @modify date 2024-01-25
 * @desc 평가 관리 - 교사 평가시험범위 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvTsRngeDto {

	@Parameter(name="평가ID")
	private long evId;
	
	@Parameter(name="시험범위순번")
	private int tsRngeSeqNo;
	
	@Parameter(name="대단원학습맵노드ID", required = true)
    @NotBlank(message = "{field.required}")	
	private String luLrmpNodId;	
	@Parameter(name="대단원학습맵노드명")
	private String luLrmpNodNm;
	@Parameter(name="대단원정렬순서")
	private int luRcstnOrdn;

	@Parameter(name="중단원학습맵노드ID")
	private String mluLrmpNodId;	
	@Parameter(name="중단원학습맵노드명")
	private String mluLrmpNodNm;
	@Parameter(name="중단원정렬순서")
	private int mluRcstnOrdn;
	
	@Parameter(name="소단원학습맵노드ID")
	private String sluLrmpNodId;	
	@Parameter(name="소단원학습맵노드명")
	private String sluLrmpNodNm;	
	@Parameter(name="소단원정렬순서")
	private int sluRcstnOrdn;
	
	@Parameter(name="차시학습맵노드ID")
	private String tcLrmpNodId;
	
	@Parameter(name="차시학습맵노드명")
	private String tcLrmpNodNm;
	
	@Parameter(name="토픽ID")
	private String tpcId;
}
