package com.aidt.api.ea.evcom.lansnte.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 13:09:오후 1:09
 * @modify date 2024-03-29 13:09:오후 1:09
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLansNteResDto {
    @Parameter(name="평가 ID")
    private long evId;

    @Parameter(name="평가명")
    private String evNm;

    @Parameter(name="평가구분코드(나의 평가, 내가 만든 평가, AI맞춤학습)")
    private String evDvCd;

    @Parameter(name="평가구분명(나의 평가, 내가 만든 평가, AI맞춤학습)")
    private String evDvNm;

	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;

    @Parameter(name="실수로 틀린 문제")
    private int misCnt;

    @Parameter(name="몰라서 틀린 문제")
    private int dkCnt;

    @Parameter(name="평가 일시")
    private String smtDtm;

    @Parameter(name="문제 수")
    private int qstCnt;

    @Parameter(name="오답노트 정답 문제 수")
    private int caQstCnt;

    @Parameter(name="문항 ID")
    private String qtmId;

    @Parameter(name="단원 학습맵 노드 아이디")
    private String lulrmpNodId;

    @Parameter(name="단원 학습맵 노드명")
    private String luLrmpNodNm;

    @Parameter(name="차시 학습맵 노드명")
    private String tcLrmpNodNm;

    @Parameter(name="문항 순서")
    private String qtmOrdn;

    @Parameter(name="문항 플랫폼 난이도 구분 코드")
    private String qtmDffdDvCd;

    @Parameter(name="문항 플랫폼 난이도 명")
    private String qpDffdNm;

    @Parameter(name="문항 플랫폼 대단원 ID")
    private String qpLluId;

    @Parameter(name="문항 플랫폼 대단원 명")
    private String qpLluNm;

    @Parameter(name="문항 플랫폼 차시 ID")
    private String qpTcId;

    @Parameter(name="문항 플랫폼 차시 명")
    private String 	qpTcNm;

    @Parameter(name="실수 OR 몰라서 구분")
    private String type;

    @Parameter(name="합계")
    private int totalCnt;


}
