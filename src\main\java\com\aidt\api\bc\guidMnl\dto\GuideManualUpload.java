package com.aidt.api.bc.guidMnl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;

import java.sql.Timestamp;

import com.aidt.api.bc.cm.BcCmUtil;

@ToString
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuideManualUpload {
    /** 매뉴얼 첨부파일 **/
    @Parameter(name="PK")
    private Long guidMnlNodFleId;

    @Parameter(name="매뉴얼 노드 ID")
    private Long guidMnlNodId;

    @Parameter(name="파일 위치")
    private String flePthNm;

    @Parameter(name="파일 원본 위치")
    private String orglFlePthNm;

    @Parameter(name="파일명")
    private String guidMnlNodFleNm;

    @Parameter(name="영상요약내용")
    private String vdSmyCn;

    @Parameter(name="파일 타입")
    private String fleTpCd;

    @Parameter(name="생성일")
    private String crtDtm;

    @Parameter(name="수정일")
    private String mdfDtm;

    @Parameter(name="삭제여부")
    private String delYn;

    private String dbId;
    private String crtrId;
    private String mdfrId;

    /**
     * 📌 cross origin error handling
     */
    private final static String DOMAIN_PATH = "/api/v1/content/";
    private String fleCdnPth;
    public String getFleCdnPth() {
        String url = BcCmUtil.END_POINT + "/";
        int index = this.flePthNm != null ? this.flePthNm.indexOf(url) : -1;
        if (index != -1) {
            String sliced = flePthNm.substring(index + url.length());
            return DOMAIN_PATH + sliced;
        }
        return this.flePthNm;
    }
}
