package com.aidt.api.ea.grpblbd.stu;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.asncom.dto.EaAsnFleDto;
import com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 모둠 게시판 - 학생", description="모둠 게시판 - 학생")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/stu/grpblbd")
public class EaGrpBlbdStuController {

	@Autowired
	private EaGrpBlbdStuService eaGrpBlbdStuService;

    /**
     * 모둠 게시판 List 조회
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 List 조회 (학생)", description="모둠 게시판 List 조회 (학생)")
    @PostMapping(value="/selectGrpBlbdList")
    public ResponseDto<Map<String, Object>> selectGrpBlbdList(@RequestBody EaGrpBlbdStuDto eaGrpBlbdStuDto) {
    	// 모둠 게시판 List 조회
    	Map<String, Object> result = eaGrpBlbdStuService.selectGrpBlbdList(eaGrpBlbdStuDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 상세, 댓글 조회
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 상세, 댓글 조회 (학생)", description="모둠 게시판 상세, 댓글 조회 (학생)")
    @PostMapping(value="/selectBlbdDetail")
    public ResponseDto<Map<String, Object>> selectBlbdDetail(@RequestBody EaGrpBlbdStuDto eaGrpBlbdStuDto) {
    	// 모둠 게시판 상세 조회
    	Map<String, Object> result = eaGrpBlbdStuService.selectBlbdDetail(eaGrpBlbdStuDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 글 등록, 수정, 삭제
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 글 등록, 수정, 삭제 (학생)", description="모둠 게시판 글 등록, 수정, 삭제 (학생)")
    @PostMapping(value="/saveBlbd")
    public ResponseDto<Map<String, Object>> saveBlbd(@RequestBody EaGrpBlbdStuDto eaGrpBlbdStuDto) {
    	// 모둠 게시판 글쓰기, 글수정
    	Map<String, Object> result = eaGrpBlbdStuService.saveBlbd(eaGrpBlbdStuDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 댓글 등록, 수정, 삭제
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 댓글 등록, 수정, 삭제 (학생)", description="모둠 게시판 댓글 등록, 수정, 삭제 (학생)")
    @PostMapping(value="/saveComment")
    public ResponseDto<Map<String, Object>> saveComment(@RequestBody EaGrpBlbdStuDto eaGrpBlbdStuDto) {
    	// 모둠 게시판 댓글 등록, 수정, 삭제
    	Map<String, Object> result = eaGrpBlbdStuService.saveComment(eaGrpBlbdStuDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 답글 등록, 수정, 삭제
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 답글 등록, 수정, 삭제 (학생)", description="모둠 게시판 답글 등록, 수정, 삭제 (학생)")
    @PostMapping(value="/saveReply")
    public ResponseDto<Map<String, Object>> saveReply(@RequestBody EaGrpBlbdStuDto eaGrpBlbdStuDto) {
    	// 모둠 게시판 답글 등록, 수정, 삭제
    	Map<String, Object> result = eaGrpBlbdStuService.saveReply(eaGrpBlbdStuDto);

    	return Response.ok(result);
    }

    /**
     * 파일 조회
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<List<EaGrpBlbdStuDto>>
     */
    @Operation(summary="파일 조회 ", description="파일 조회")
    @PostMapping(value="/selectFile")
    public ResponseDto<List<EaAsnFleDto>> selectFile(@RequestBody EaGrpBlbdStuDto eaGrpBlbdStuDto) {

    	List<EaAsnFleDto> fileList = eaGrpBlbdStuService.selectFile(eaGrpBlbdStuDto);
    	return Response.ok(fileList);
    }
}
