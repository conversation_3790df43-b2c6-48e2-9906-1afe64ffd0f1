package com.aidt.api.tl.cmstnsst.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 09:12:02
 * @modify date 2024-05-24 09:12:02
 * @desc [TlCmStnSttSrhDto 대단원 교육과정표준체계목록 조회 dto ]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmLluStnSttSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** (대단원)학습맵노드ID */
    @Parameter(name="(대단원)학습맵노드ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;

    /** 학습단계구분_교과포함여부 */
    @Parameter(name="학습단계구분_교과포함여부(default: 'Y')")
    private String lrnStpDvClYn;

    /** 학습단계구분_익힘포함여부 */
    @Parameter(name="학습단계구분_익힘포함여부(default: 'Y')")
    private String lrnStpDvExYn;

    /** 학습단계구분_평가포함여부 */
    @Parameter(name="학습단계구분_평가포함여부(default: 'Y')")
    private String lrnStpDvWbYn;
}
