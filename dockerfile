# Base Stage
ARG BASE_REGISTRY_ENDPOINT
# ENV BASE_REGISTRY_ENDPOINT=${BASE_REGISTRY_ENDPOINT}

FROM ${BASE_REGISTRY_ENDPOINT} AS base
# FROM lms-comm-ncr-01.ncr.gov-ntruss.com/amazoncorretto:11-alpine-jdk AS base
LABEL MAINTAINER alohaken <<EMAIL>>
USER root

ARG APPLICATION_NAME
ARG PROJECT
ARG APP_SHORT_NAME
ARG BUILD_ENV
ARG NAMESPACE
ARG BUILD_CLUSTER
ARG COMMIT_HASH

ENV APPLICATION_NAME=${APPLICATION_NAME}
ENV PROJECT=${PROJECT}
ENV APP_SHORT_NAME=${APP_SHORT_NAME}
ENV BUILD_ENV=${BUILD_ENV}
ENV NAMESPACE=${NAMESPACE}
ENV BUILD_CLUSTER=${BUILD_CLUSTER}
ENV COMMIT_HASH=${COMMIT_HASH}

# Shared directories and files
RUN mkdir -p /aidt.log /whatap /jmx
RUN mkdir -p /app/log/${NAMESPACE}/${APP_SHORT_NAME}
RUN mkdir -p /app/upload/${NAMESPACE}/${APP_SHORT_NAME}
RUN touch /app/log/${NAMESPACE}/aidt-api-lm.meta

WORKDIR /workspace

# Development Stage
FROM base AS dev
COPY /workspace/${PROJECT}/target/${APP_SHORT_NAME}-local-1.0.0-SNAPSHOT.jar /app.jar
COPY /workspace/utils/whatap/agent/whatap.agent-2.2.33.jar /whatap/
COPY /workspace/utils/whatap/agent/paramkey.txt /whatap/
COPY /workspace/utils/whatap/conf/ /whatap/
ENV JVM_OPTS="-Xms2048m -Xmx4096m"
ENV JAVA_OPTS="$JVM_OPTS"
ENTRYPOINT ["sh", "-c", "java -Xms2048m -Xmx4096m -jar $JAVA_OPTS -javaagent:/whatap/whatap.agent-2.2.33.jar -Dwhatap.config=whatap_${K8S_BUILD_CLUSTER}.conf -Dspring.application.name=${SPRING_APPLICATION_NAME} -Dspring.profiles.active=dev /app.jar"]

# Staging Stage
FROM base AS stg
COPY /workspace/utils/prom/jmx_prometheus_javaagent-1.1.0.jar /jmx/
COPY /workspace/utils/prom/conf/${BUILD_ENV}/prometheus-config.yaml /jmx/
COPY /workspace/utils/whatap/agent/whatap.agent-2.2.33.jar /whatap/
COPY /workspace/utils/whatap/agent/paramkey.txt /whatap/
COPY /workspace/utils/whatap/conf/ /whatap/
COPY /workspace/${PROJECT}/target/${APP_SHORT_NAME}-local-1.0.0-SNAPSHOT.jar /app.jar
ENV JVM_BASE_HEAP_OPTS="-Xms4g -Xmx4g"
ENV JVM_TUNE_HEAP_OPTS="-XX:MaxMetaspaceSize=512m -XX:ReservedCodeCacheSize=256m"
ENV JVM_TUNE_GC_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:InitiatingHeapOccupancyPercent=45 -XX:ParallelGCThreads=2 -XX:ConcGCThreads=1 -XX:G1HeapRegionSize=16m"
ENV JVM_TUNE_STR_OBJ_OPTS="-XX:+UseStringDeduplication"
ENV JVM_TUNE_OOM_OPTS="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof -XX:+ExitOnOutOfMemoryError"
ENV JVM_TUNE_MONITORING_OPTS="-Xlog:gc*:file=/tmp/gc-%t.log:time,uptime,level,tags -XX:NativeMemoryTracking=summary"
ENV JVM_BASE_OPTS="$JVM_BASE_HEAP_OPTS $JVM_TUNE_GC_OPTS"
ENV JVM_TUNE_OPTS="$JVM_BASE_HEAP_OPTS $JVM_TUNE_HEAP_OPTS $JVM_TUNE_GC_OPTS $JVM_TUNE_STR_OBJ_OPTS $JVM_TUNE_OOM_OPTS $JVM_TUNE_MONITORING_OPTS"
ENV JMX_OPTS="-javaagent:/jmx/jmx_prometheus_javaagent-1.1.0.jar=8090:/jmx/prometheus-config.yaml"
ENV JAVA_OPTS="$JVM_BASE_OPTS $JMX_OPTS"
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -javaagent:/whatap/whatap.agent-2.2.33.jar -Dwhatap.config=whatap_${K8S_BUILD_CLUSTER}.conf -jar -Dspring.application.name=${SPRING_APPLICATION_NAME} -Dspring.profiles.active=stg /app.jar"]

# Production Stage
FROM base AS prod
COPY /workspace/utils/prom/jmx_prometheus_javaagent-1.1.0.jar /jmx/
COPY /workspace/utils/prom/conf/${BUILD_ENV}/prometheus-config.yaml /jmx/
COPY /workspace/utils/whatap/agent/whatap.agent-2.2.33.jar /whatap/
COPY /workspace/utils/whatap/agent/paramkey.txt /whatap/
COPY /workspace/utils/whatap/conf/ /whatap/
COPY /workspace/${PROJECT}/target/${APP_SHORT_NAME}-local-1.0.0-SNAPSHOT.jar /app.jar
ENV JVM_BASE_HEAP_OPTS="-Xms6g -Xmx6g"
ENV JVM_TUNE_HEAP_OPTS="-XX:MaxMetaspaceSize=512m -XX:ReservedCodeCacheSize=256m"
ENV JVM_TUNE_GC_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:InitiatingHeapOccupancyPercent=45 -XX:ParallelGCThreads=2 -XX:ConcGCThreads=1 -XX:G1HeapRegionSize=16m"
ENV JVM_TUNE_STR_OBJ_OPTS="-XX:+UseStringDeduplication"
ENV JVM_TUNE_OOM_OPTS="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof -XX:+ExitOnOutOfMemoryError"
ENV JVM_TUNE_MONITORING_OPTS="-Xlog:gc*:file=/tmp/gc-%t.log:time,uptime,level,tags -XX:NativeMemoryTracking=summary"
ENV JVM_BASE_OPTS="$JVM_BASE_HEAP_OPTS $JVM_TUNE_GC_OPTS $JVM_TUNE_HEAP_OPTS"
ENV JVM_TUNE_OPTS="$JVM_BASE_HEAP_OPTS $JVM_TUNE_HEAP_OPTS $JVM_TUNE_GC_OPTS $JVM_TUNE_STR_OBJ_OPTS $JVM_TUNE_OOM_OPTS $JVM_TUNE_MONITORING_OPTS"
ENV JMX_OPTS="-javaagent:/jmx/jmx_prometheus_javaagent-1.1.0.jar=8090:/jmx/prometheus-config.yaml"
ENV JAVA_OPTS="$JVM_TUNE_OPTS $JMX_OPTS"
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -javaagent:/whatap/whatap.agent-2.2.33.jar -Dwhatap.config=whatap_${K8S_BUILD_CLUSTER}.conf -jar -Dspring.application.name=${SPRING_APPLICATION_NAME} -Dspring.profiles.active=prod /app.jar"]

# Webdisplay Stage : whatap
FROM base AS webdisplay
COPY /workspace/utils/whatap/agent/whatap.agent-2.2.33.jar /whatap/
COPY /workspace/utils/whatap/agent/paramkey.txt /whatap/
COPY /workspace/utils/whatap/conf/ /whatap/
COPY /workspace/${PROJECT}/target/${APP_SHORT_NAME}-local-1.0.0-SNAPSHOT.jar /app.jar
ENTRYPOINT ["sh", "-c", "java -Xms2048m -Xmx4096m -jar -javaagent:/whatap/whatap.agent-2.2.33.jar -Dwhatap.config=whatap_${K8S_BUILD_CLUSTER}.conf -Dspring.application.name=${SPRING_APPLICATION_NAME} -Dspring.profiles.active=prod /app.jar"]
