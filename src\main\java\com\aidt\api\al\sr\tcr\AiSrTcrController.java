package com.aidt.api.al.sr.tcr;

import java.util.List;
import java.util.Map;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.sr.dto.pf.AiSrPfAtvNmResDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultReqDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultResDto;
import com.aidt.api.al.sr.dto.sbc.AiSrTcrLrnReqDto;
import com.aidt.api.al.sr.dto.sbc.AiSrTcrLrnResDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataReqDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataResDto;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 16:36:34
 * @modify date 2024-05-24 16:36:34
 * @desc
 */
//@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/sr/tcr")
@Tag(name="[al] AI 교과평어", description="AI 교과평어")
public class AiSrTcrController {

    private final AiSrTcrService aiSrTcrService;

    private final JwtProvider jwtProvider;

    @Operation(summary="수행평가/활동명 목록", description="수행평가/활동명 목록")
    @GetMapping(value = "/pf/atv")
    public ResponseDto<List<AiSrPfAtvNmResDto>> getPfmcAtvList() {
        String txbId = jwtProvider.getCommonUserDetail().getTxbId();

        // 2 -> 237
        // 3 -> 251
        // 4 -> 241, 262
        // 6 -> 244, 242
        if("1".equalsIgnoreCase(txbId)) txbId="254";
        if("3".equalsIgnoreCase(txbId)) txbId="251";
        if("4".equalsIgnoreCase(txbId)) txbId="241";
        if("6".equalsIgnoreCase(txbId)) txbId="244";

        return Response.ok(aiSrTcrService.getPfmcAtvList(txbId));
    }

    @Operation(summary="수행평가/학생별 결과목록", description="수행평가/학생별 결과목록")
    @PostMapping(value = "/pf/results", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiSrPfResultResDto.StudentResDto>> getPfmcAtvResultList(@RequestBody AiSrPfResultReqDto reqDto) {

        return Response.ok(aiSrTcrService.getPfmcAtvResultList(reqDto));
    }

    @Operation(summary="교과평가/학생별 결과목록", description="교과평가/학생별 결과목록")
    @PostMapping(value = "/sbc/results", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Map<String, AiSrTcrLrnResDto>> getLrnDataList(@RequestBody AiSrTcrLrnReqDto reqDto) {
        String optTxbId = jwtProvider.getCommonUserDetail().getOptTxbId();
        reqDto.setOptTxbId(optTxbId);

        return Response.ok(aiSrTcrService.getResult(reqDto));
    }

    @PostMapping(value = "/sbc/user-file-path", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiSrUserDataResDto> getUserFilePath(@RequestBody AiSrUserDataReqDto reqDto) {
        return Response.ok(aiSrTcrService.getUserFilePath(reqDto));
    }

    @PostMapping(value = "/sbc/user-file", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiSrUserDataResDto> saveUserFile(@RequestBody AiSrUserDataReqDto reqDto) {
        int i = aiSrTcrService.insertUserFile(reqDto);
        if(i > 0) {
            AiSrUserDataResDto resDto = aiSrTcrService.getUserFilePath(reqDto);
            return Response.ok(resDto);
        } else {
            return Response.ok(null);
        }
    }
}
