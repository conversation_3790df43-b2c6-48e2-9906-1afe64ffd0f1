package com.aidt.api.bc.cm.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "웹전시")
public class BcCmWebDispDto {

	@Parameter(name="학생목록")
	private List<String> stuList;

	@Parameter(name="복사번호")
	private int cpNo;
	
	@Parameter(name="교과서번호")
	private String txbId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="학생ID")
	private String stuId;

	@Parameter(name="리턴 생성된 계정 수")
	private Integer rtnRowCnt;
	
	@Parameter(name="리턴 메세지")
	private String rtnMsg;
	
	@Parameter(name="리턴 에러메세지")
	private String rtnErrNsg;
	
	@Parameter(name="복제요청ID")
	private int cpReqId;
	
	@Parameter(name="시작")
	private int startId;
	
	@Parameter(name="종료")
	private int endId;
	
	@Parameter(name="페르소나 ID")
	private String personaId;

}