package com.aidt.api.al.wrt.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import com.aidt.api.al.pl.cm.rcm.AlMyhmPointService;
import com.aidt.api.al.wrt.dto.AIWrtReqLogDto;
import com.aidt.api.al.wrt.dto.AIWrtRsLogDto;
import com.aidt.api.al.wrt.dto.AlWrtAcpReqDto;
import com.aidt.api.al.wrt.dto.AlWrtReqDto;
import com.aidt.api.al.wrt.dto.AlWrtResDto;
import com.aidt.api.util.WebClientUtil;
import com.aidt.common.CommonDao;
import com.aidt.common.JwtProvider;
import com.aidt.common.util.ConstantsExt;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-01 11:28:00
 * @modify 2024-06-01 11:28:00
 * @desc AI첨삭 학생 서비스
 */

@Service
public class AlWrtStuService {

	private final String MAPPER_NAMESPACE = "api.al.wrt.stu.";
	
	@Autowired
    private CommonDao commonDao;
	
	@Autowired
    private WebFluxUtil webFluxUtil;
	
	@Autowired
	private WebClientUtil  webClientUtil;
	
	@Value("${aidt.endpoint.archipinWrite:}")
	private String endpoint_archipin;
	
	@Autowired
	private JwtProvider jwtProvider;
	
	@Autowired 
	private AlMyhmPointService alMyhmPointService;
	
	//@Value("${server.meta.textbook.systemCode}")
	//private String systemCode;
	
	//@Value("${spring.profiles.active}")
	//private String SERVER_ACTIVE;
	
    /**
     * AI 첨삭 : 토픽 목록
     *
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID
     * @return List<AlWrtResDto> - 해당 사용자의 첨삭토픽목록
     */
    public List<AlWrtResDto> selectAlWrtTpcList(AlWrtReqDto alWrtReqDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAlWrtTpcList", alWrtReqDto);
    }
    
    /**
     * AI 첨삭 : 최근학습정보
     *
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID
     * @return AlWrtResDto - 해당 사용자의 최근학습정보
     */
    public AlWrtResDto selectWrtRcLrnInfo(AlWrtReqDto alWrtReqDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectWrtRcLrnInfo", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 생성(학생 학습시작)
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - insert cnt
     */
    @Transactional
	public int insertWrtMg(AlWrtReqDto alWrtReqDto) {
    	return commonDao.insert(MAPPER_NAMESPACE + "insertWrtMg", alWrtReqDto);
    }
    
    /**
     * AI 첨삭 : 상세
     *
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID, 대단원노드ID, 토픽노드ID 
     * @return AlWrtResDto - 해당 단원, 해당 토픽의 상세
     */
    public AlWrtResDto selectWrtDtl(AlWrtReqDto alWrtReqDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectWrtDtl", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 수정 (임시저장, 제출)
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMg(AlWrtReqDto alWrtReqDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateWrtMg", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 수정 (AI 첨삭)
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMgAiEdit(AlWrtReqDto alWrtReqDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateWrtMgAiEdit", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 확인완료 및 다시쓰기 저장
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMgConfComp(AlWrtReqDto alWrtReqDto, HttpServletRequest request) {
    	int rtnCnt = commonDao.update(MAPPER_NAMESPACE + "updateWrtMgConfComp", alWrtReqDto);
    	
    	if ("CC".equals(alWrtReqDto.getPgrsStCd())) {
    		//마이홈 포인트 적립
    		String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
    		String pointCode = "AI_ME_10";
    		
    		try {
    			alMyhmPointService.callMyhmApi(accessToken, 
    					Map.of(
    							"pntCd", pointCode,
    							"pntChkBsVl", alWrtReqDto.getTpcKmmpNodId()
    							)
    					);
    		} catch (JsonProcessingException e) {
    			throw new IllegalArgumentException("AI 마이홈 포인트 적립실패");
    		}
    	}
    	
    	return rtnCnt;
    }
    
    /**
     * 아키핀 API Call
     * @param sunUrl
     * @param dto
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> callAcpApi(String sunUrl, AlWrtAcpReqDto dto) {        
        Map<String, Object> returnMap = new HashMap<>();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");

        try {
            String jsonString = new ObjectMapper().writeValueAsString(dto);
            String post = webFluxUtil.post( endpoint_archipin + sunUrl, httpHeaders, jsonString, String.class);
            return CoreUtil.Json.jsonString2Map(post);
        } catch (JsonProcessingException e) {
        	returnMap = new HashMap<>();
        }

        return returnMap;
    }
    
    /**
     * 아키핀 API call log 용
     * 
     * @param sunUrl
     * @param dto
     * @param logDto
     * @return
     */
    public Map<String, Object> callAcpApiAddLog(String sunUrl, AlWrtAcpReqDto dto) {        
        Map<String, Object> returnMap = new HashMap<String, Object>();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");

        boolean result = false;
        
        returnMap.put("conn_url", endpoint_archipin + sunUrl);
        
        try {
            String jsonString = new ObjectMapper().writeValueAsString(dto);
            returnMap.put("body_cn", jsonString);

            // 2025.02.26 retry 없는 버전으로 변경
            //String post = webFluxUtil.post( endpoint_archipin + sunUrl, httpHeaders, jsonString, String.class);
            String post = webClientUtil.post( endpoint_archipin + sunUrl, httpHeaders, jsonString, String.class);
            
            result = true;
            
            returnMap.put("acp_rst", post);
            returnMap.put("acp_map_rst", CoreUtil.Json.jsonString2Map(post));
        } catch (JsonProcessingException e) {
        	returnMap.put("err_msg", e.getMessage());
        } 
        // 모든 장애에 대한 로그 기록을 위해 선언
        catch (Exception e) {
        	returnMap.put("err_msg", e.getMessage());
        }
        
        returnMap.put("result", result);

        return returnMap;
    }
    
    /**
     * 아키핀 결과 저장
     * @param sunUrl
     * @param dto
     * @return
     */
    @Transactional
    public Map<String, Object> updateAcpWrtRs(AlWrtReqDto alWrtReqDto) {        
        Map<String, Object> returnMap = new HashMap<>();
        /**
         * 1. 첨삭 상세 조회 및 검증
         */
        /* 1-1. 상세 조회 */
        AlWrtResDto wrtInfo = this.selectWrtDtl(alWrtReqDto);
        /* 1-2. 데이터 검증 */
        if ( wrtInfo == null ) {
        	// 데이터 없음
        	return null;
        }
        if (!StringUtils.equals(wrtInfo.getPgrsStCd(), "AP")) {
        	// 첨삭 상태가 AP인 경우에만 결과 처리
        	return null;
        }
        if (StringUtils.isBlank(wrtInfo.getStuCansCn())) {
        	// 작성된 첨삭 내용이 없음
        	return null;
        }
        /**
         * 2. 아키핀 첨삭 api call
         */
        /* 2-1. api 파라미터 set */
        AlWrtAcpReqDto acpDto = new AlWrtAcpReqDto();
        acpDto.setTopic_id(wrtInfo.getAcpId());
        acpDto.setUser_text(wrtInfo.getStuCansCn());
        /* 2-2. 첨삭 교정 api call */
        Map<String, Object> acpCrt = this.callAcpApi("/text_correction", acpDto);
        /* 2-3. 첨삭 피드백 api call */
        Map<String, Object> acpFb = this.callAcpApi("/get_feedback", acpDto);
        /* 2-4. 첨삭 결과 set */
        Map<String, Object> acpRstOri = (Map<String, Object>) acpCrt.get("res");
        Map<String, Object> acpfbRes = (Map<String, Object>) acpFb.get("res"); 
        acpRstOri.put("writing_feedback", acpfbRes.get("writing_feedback"));
        /**
         * 3. 결과 저장
         */
        /* 3-1. 데이터 정제 */
        Map<String, Object> acpRst = this.setAiEditData(acpRstOri);
        /* 3-2. java map -> json -> String 변환 */
        String acpRstStr = this.convertMapToJsonString(acpRst);
        String acpRstOriStr = this.convertMapToJsonString(acpRstOri);
        /* 3-3. 각 점수 set */
        Map<String, Integer> editScore = (Map<String, Integer>) acpRst.get("editScore");
        int grmrScr = (int) editScore.getOrDefault("grmrScr", 0);
        int cstnScr = (int) editScore.getOrDefault("cstnScr", 0);
        int exprScr = (int) editScore.getOrDefault("exprScr", 0);
        int vocScr = (int) editScore.getOrDefault("vocScr", 0);
        /* 3-4. 결과 update */
        AlWrtReqDto udtDto = new AlWrtReqDto();
        udtDto.setOptTxbId(alWrtReqDto.getOptTxbId());
        udtDto.setStuId(alWrtReqDto.getStuId());
        udtDto.setLluKmmpNodId(alWrtReqDto.getLluKmmpNodId());
        udtDto.setTpcKmmpNodId(alWrtReqDto.getTpcKmmpNodId());
        udtDto.setAiAnnxCn(acpRstStr);
        udtDto.setAiAnnxDtl(acpRstOriStr);
        udtDto.setCstnScr(cstnScr);
        udtDto.setExprScr(exprScr);
        udtDto.setVocScr(vocScr);
        udtDto.setGrmrScr(grmrScr);
        this.updateWrtMgAiEdit(udtDto);

        return returnMap;
    }
    
    
    
    public static Map<String, Object> setAiEditData(Map<String, Object> aiEdit) {
        List<Map<String, Object>> dataWords = new ArrayList<>();
        List<Map<String, Object>> detailList = new ArrayList<>();
        Map<String, Integer> editScore = new HashMap<>();
        Map<String, Object> wrtRst = new HashMap<>();

        // 1. Correction of words 데이터 설정
        List<Map<String, Object>> sentResults = null;
        
        try {
        	sentResults = (List<Map<String, Object>>) aiEdit.get("sent_results");
        } catch (Exception e) {
        	throw new RuntimeException("sent_results error >>> " + e.getMessage());
        }
        
        if (sentResults != null) {
            int editIdx = 1;

            for (Map<String, Object> entry : sentResults) {
                String sentence = entry.get("sentence") + " &nbsp;";
                String[] words = sentence.split(" ");
                Map<String, Object> tempObject = new HashMap<>();
                boolean tempBool = false;
                String tempIdx = "";
                List<Map<String, Object>> wordObjects = new ArrayList<>();
                int wordLen = words.length - 1;
                
                for (int j = 0; j < words.length; j++) {
                    String word = words[j];
                    boolean pushBool = false;
                    List<Map<String, Object>> writingResults = (List<Map<String, Object>>) entry.get("writing_results");
                    if (writingResults != null) {
                        for (Map<String, Object> result : writingResults) {
                            int errorStartIdx = (int) result.get("error_start_index");
                            int errorEndIdx = (int) result.get("error_end_index");
                            if (tempBool) {
                            	// tempObject 에 값이 있을 경우
                                if (j == errorEndIdx - 1) {
                                	// end index 일 경우 tempObject에 값 추가 후 push
                                    tempObject.put("origin", tempObject.get("origin") + " " + word);
                                    wordObjects.add(new HashMap<>(tempObject));
                                    pushBool = true;
                                    tempBool = false;
                                    tempIdx = "";
                                } else if (j < errorEndIdx && !tempIdx.equals(String.valueOf(j))) {
                                	// end index 보다 작을 경우 tempObject에 값 추가
                                    tempObject.put("origin", tempObject.get("origin") + " " + word);
                                    tempIdx = String.valueOf(j);
                                }
                            } else {
                            	// tempObject 에 값이 없을 경우
                            	if (j == errorStartIdx) {
                            		if (errorStartIdx < errorEndIdx) {
                            			// 오류문장 시작 index 와 종료 index가 다를 경우
                            			if (errorEndIdx - errorStartIdx == 1) {
                            				// 오류문장의 범위가 1일 경우 단어하나를 기준으로 push
                            				wordObjects.add(createWordObject(word, (String) result.get("correct_word"), editIdx, "visible"));
                                            pushBool = true;
                                            editIdx++;
                            			} else {
                            				// 오류문장의 범위가 1이상일 경우 여러단어를 기준으로 해야하므로 tempObject에 값 세팅
                            				tempObject = createWordObject(word, (String) result.get("correct_word"), editIdx, "visible");
                                            tempBool = true;
                                            tempIdx = String.valueOf(j);
                                            editIdx++;
                            			}
                            		} else if (errorStartIdx == errorEndIdx) {
                            			// 같을 경우 추가 이므로 원본에 " "로 push
                            			wordObjects.add(createWordObject("&nbsp;&nbsp;&nbsp;", (String) result.get("correct_word"), editIdx, "visible"));
                            			pushBool = true;
                            			editIdx++;
                            			
                            			// 같은 index의 오류문장이 없을 경우 현재 단어 push
                            			int sameIdxCnt = 0;
                            			for (Map<String, Object> rst : writingResults) {
                            				int rstStartIdx = (int) rst.get("error_start_index");
                                            int rstEndIdx = (int) rst.get("error_end_index");
                                            if (rstStartIdx == errorStartIdx || rstEndIdx == errorStartIdx) {
                                            	sameIdxCnt++;
                                            }
                            			}
                            			if (sameIdxCnt == 1) {
                            				wordObjects.add(createWordObject(word, null, 0, "disable"));
                            			}
                            		}
                            	}
                            }
                        }
                    }

                    if (!pushBool && !tempBool && wordLen != j) {
                    	// 오류가 없을 경우
                        wordObjects.add(createWordObject(word, null, 0, "disable"));
                    }
                }

                Map<String, Object> wordEntry = new HashMap<>();
                wordEntry.put("word", wordObjects);
                dataWords.add(wordEntry);
            }
        }

        // 2. Details 데이터 설정
        int idx = 0;
        if (sentResults != null) {
            for (Map<String, Object> entry : sentResults) {
                List<Map<String, Object>> writingResults = (List<Map<String, Object>>) entry.get("writing_results");

                if (writingResults != null) {
                    for (Map<String, Object> rst : writingResults) {
                        idx++;
                        if (rst.get("error_comments") != null) {
                        	
                        	Map<String, Object> detailEntry = new HashMap<>();
                            detailEntry.put("editIdx", idx);
                            detailEntry.put("origin", rst.get("error_word"));
                            detailEntry.put("ai", rst.get("correct_word"));
                            detailList.add(detailEntry);
                        	
                        	String[] krStr = ((String) rst.get("error_comments")).split("\"kr\"");
                        	if (krStr.length > 1) {
                        		String[] dtlPulArr = krStr[1].split("\"");
                        		if (dtlPulArr.length > 2) {
                        			String[] dtlArr = dtlPulArr[1].split(":");
                            		if (dtlArr.length > 1) {
                            			detailEntry.put("errorType", dtlArr[0]);
                                        detailEntry.put("errorComments", dtlArr[1]);
                            		}
                        		}
                        	}
                        }
                    }
                }
            }
        }

        // 3. 첨삭 총평 데이터 설정
        Map<String, Object> writingFeedback = null;
        
        try {
        	writingFeedback = (Map<String, Object>) aiEdit.get("writing_feedback");
        } catch (Exception e) {
        	throw new RuntimeException("writing_feedback error >>> " + e.getMessage());
        }
        		
        if (writingFeedback != null) {
//            editScore.put("grmrScr", (int) writingFeedback.getOrDefault("grammar", Map.of("score", 0)).get("score"));
//            editScore.put("cstnScr", (int) writingFeedback.getOrDefault("organization", Map.of("score", 0)).get("score"));
//            editScore.put("exprScr", (int) writingFeedback.getOrDefault("relevance", Map.of("score", 0)).get("score"));
//            editScore.put("vocScr", (int) writingFeedback.getOrDefault("word_choice", Map.of("score", 0)).get("score"));
        	// grammar
        	Map<String, Object> grammar = (Map<String, Object>) writingFeedback.getOrDefault("grammar", Map.of("score", 0));
        	editScore.put("grmrScr", (int) grammar.getOrDefault("score", 0));

        	// organization
        	Map<String, Object> organization = (Map<String, Object>) writingFeedback.getOrDefault("organization", Map.of("score", 0));
        	editScore.put("cstnScr", (int) organization.getOrDefault("score", 0));

        	// relevance
        	Map<String, Object> relevance = (Map<String, Object>) writingFeedback.getOrDefault("relevance", Map.of("score", 0));
        	editScore.put("exprScr", (int) relevance.getOrDefault("score", 0));

        	// word_choice
        	Map<String, Object> wordChoice = (Map<String, Object>) writingFeedback.getOrDefault("word_choice", Map.of("score", 0));
        	editScore.put("vocScr", (int) wordChoice.getOrDefault("score", 0));

            wrtRst.put("totalCmt", writingFeedback.get("overall_comment"));
            List<String> wrtRstRvws = new ArrayList<>();

            addImprovement(wrtRstRvws, writingFeedback, "grammar");
            addImprovement(wrtRstRvws, writingFeedback, "organization");
            addImprovement(wrtRstRvws, writingFeedback, "relevance");
            addImprovement(wrtRstRvws, writingFeedback, "word_choice");

            wrtRst.put("wrtRstRvws", wrtRstRvws);
        }

        // 4. 총 단어 수 설정
        int totalWords = (int) aiEdit.getOrDefault("total_words", 0);

        // 5. 데이터 리턴
        Map<String, Object> result = new HashMap<>();
        result.put("dataWords", dataWords);
        result.put("detailList", detailList);
        result.put("editScore", editScore);
        result.put("wrtRst", wrtRst);
        result.put("total_words", totalWords);

        return result;
    }

    private static Map<String, Object> createWordObject(String origin, String ai, int editIdx, String state) {
        Map<String, Object> wordObject = new HashMap<>();
        wordObject.put("focus", false);
        wordObject.put("state", state);
        wordObject.put("origin", origin);
        wordObject.put("ai", ai);
        wordObject.put("editIdx", editIdx);
        return wordObject;
    }

    private static void addImprovement(List<String> wrtRstRvws, Map<String, Object> feedback, String key) {
        Map<String, Object> section = (Map<String, Object>) feedback.get(key);
        if (section != null && section.containsKey("improvements")) {
            wrtRstRvws.add((String) section.get("improvements"));
        }
    }
    
    public static String convertMapToJsonString(Map<String, Object> map) {
        ObjectMapper objectMapper = new ObjectMapper(); // Jackson의 ObjectMapper 사용

        try {
            // Map을 JSON 형식의 문자열로 변환
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "{}"; // 변환 실패 시 빈 JSON 반환
        }
    }
    
    
    @SuppressWarnings("unchecked")
    public Map<String, Object> requestAiWrtAcp(AlWrtReqDto alWrtReqDto, AIWrtReqLogDto logDto) {        
    	Map<String, Object> returnMap = new HashMap<>();
        /**
         * 1. 첨삭 상세 조회 및 검증
         */
        /* 1-1. 상세 조회 */
        AlWrtResDto wrtDtl = this.selectWrtDtl(alWrtReqDto);
        /* 1-2. 데이터 검증 */
        if ( wrtDtl == null ) {
        	logDto.setRsYn("N");
        	logDto.setRsMsg("Query ID >>> selectWrtDtl <<< select 결과 null.");
        	
        	// 데이터 없음
        	return null;
        }
        if (!StringUtils.equals(wrtDtl.getPgrsStCd(), "AP")) {
        	logDto.setRsYn("N");
        	logDto.setRsMsg("pgrs_st_cd (상태코드)가 AP가 아닌 오류... >>> getPgrsStCd = " + wrtDtl.getPgrsStCd());
        	
        	// 첨삭 상태가 AP인 경우에만 결과 처리
        	return null;
        }
        if (StringUtils.isBlank(wrtDtl.getStuCansCn())) {
        	logDto.setRsYn("N");
        	logDto.setRsMsg("stu_cans_cn (학생정답내용)의 값이 null or empty.");
        	
        	// 작성된 첨삭 내용이 없음
        	return null;
        }
        /**
         * 2. 첨삭 결과 요청 api 호출
         */
        /* 2-1. 파라미터 set */
        AlWrtAcpReqDto acpDto = new AlWrtAcpReqDto();
        acpDto.setTopic_id(wrtDtl.getAcpId());
        acpDto.setUser_text(wrtDtl.getStuCansCn());
    	acpDto.setError_details("true");
    	
    	/*
    	 * 2025.02.24 <<< alWrtReqDto.getSystemCode() >>>
    	 * - 운영/웹전시 config 값으로 구분 할 수 없음 
    	 * - 프론트에서 전달 받은 값으로 대체 
    	 * -> https://abc.aditbook.co.kr 접속한 경우 abc값 전달
    	 * -> localhost, ip 또는 서브 도메인을 가져오지 않하는 경우 dev
    	 */
    	/*
    	String systemCd = "dev";
    	if (!"local".equals(SERVER_ACTIVE) && !"dev".equals(SERVER_ACTIVE)) {
    		systemCd = systemCode + "-w";
    	}
    	*/
    	
    	Map<String, String> wrtInfo = new HashMap<String, String>();
    	wrtInfo.put("systemCode", alWrtReqDto.getSystemCode());
    	wrtInfo.put("stuId", alWrtReqDto.getStuId());
    	wrtInfo.put("optTxbId", alWrtReqDto.getOptTxbId());
    	wrtInfo.put("lluKmmpNodId", alWrtReqDto.getLluKmmpNodId());
    	wrtInfo.put("tpcKmmpNodId", alWrtReqDto.getTpcKmmpNodId());
    	acpDto.setWrtInfo(wrtInfo);
    	/* 2-2. 첨삭 결과 요청 api 호출*/
    	//returnMap = this.callAcpApi("/combine_wrt", acpDto);
    	
    	Map<String, Object> rst = this.callAcpApiAddLog("/combine_wrt", acpDto);
    	
    	if(rst == null) {
    		logDto.setRsYn("N");
        	logDto.setRsMsg("callAcpApiAddLog Method result null.");
    	} else {
    		returnMap = (HashMap<String, Object>) rst.get("acp_map_rst");
    		
    		if(MapUtils.getBooleanValue(rst, "result")) {
    			logDto.setRsYn("Y");
    			logDto.setRsMsg(MapUtils.getString(rst, "acp_rst"));
    		} else {
    			logDto.setRsYn("N");
    			logDto.setRsMsg(MapUtils.getString(rst, "err_msg"));
    		}
    		
    		logDto.setConnUrl(MapUtils.getString(rst, "conn_url"));
    		logDto.setBodyCn(MapUtils.getString(rst, "body_cn"));
    	}
    	
    	return returnMap;
    }
    
    @Transactional
    public void insertAiwriteReqLog(AIWrtReqLogDto dto) {
    	commonDao.insert(MAPPER_NAMESPACE + "insertAiwriteReqLog", dto);
    }
    
    @Transactional
    public void insertAiwriteRsLog(AIWrtRsLogDto dto) {
    	commonDao.insert(MAPPER_NAMESPACE + "insertAiwriteRsLog", dto);
    }
}
