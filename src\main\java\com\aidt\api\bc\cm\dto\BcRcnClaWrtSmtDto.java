package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "우리 반 최근 첨삭 제출 목록")
public class BcRcnClaWrtSmtDto {

    @Schema(description = "토픽 노드 ID")
    private String tpcKmmpNodId;

    @Schema(description = "학생 ID")
    private String usrId;

    @Schema(description = "학생 이름")
    private String stuNm;

    @Schema(description = "첨삭 상태")
    private String pgrsStCd;

    @Schema(description = "평가 제출 일자")
    private String stuSavDtm;

    @Schema(description = "차시 이름, 단원 번호 포함")
    private String fmtTcNm;

    @Schema(description = "단원 이름, 단원 번호 포함")
    private String fmtLuNm;

    @Schema(description = "첨삭중 개수")
    private int iPgrCnt;

    @Schema(description = "미첨삭 개수")
    private int nPgrCnt;

}
