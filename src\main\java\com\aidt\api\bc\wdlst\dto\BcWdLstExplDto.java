package com.aidt.api.bc.wdlst.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 단어장 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcWdLstExplDto {

	@Parameter(name="나의단어설명ID")
	private int myWdExplId;
	
	@Parameter(name="나의단어장ID")
	private int myWdId;
	
	@Parameter(name="품사구분코드")
	private String wdclDvCd;
	
	@Parameter(name="영문단어의미내용")
	private String engWdMeanCn;
	
	@Parameter(name="예문내용")
	private String exsnCn;
	
	@Parameter(name="예문해석내용")
	private String exsnIntpCn;
	
	@Parameter(name="단어음성CDN경로명")
	private String wdVceCdnPthNm;
	
	@Parameter(name="예문음성CDN경로명")
	private String exsnVceCdnPthNm;
	
	@Parameter(name="이미지CDN경로명")
	private String imgCdnPthNm;
	
	@Parameter(name="대체텍스트명")
	private String altnTxtNm;
	
	// 단어정렬필드
	private String srhSrt;

	// 생성자ID
	private String crtrId;

	// 생성일시
	private String crtDtm;

	// 수정자ID
	private String mdfrId;

	// 수정일시
	private String mdfDtm;

	// 데이터베이스ID
	private String dbId;
	
	private int pageNo;
	private int pageSize;
	private int totalCnt;
	private int startPageNo;
	private int dataSize;

}
