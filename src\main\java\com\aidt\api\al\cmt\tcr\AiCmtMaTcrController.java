package com.aidt.api.al.cmt.tcr;

import com.aidt.api.al.cmt.dto.req.AiCmtUsrDataReqDto;
import com.aidt.api.al.cmt.dto.req.ma.AiCmtMaEvEtReqDto;
import com.aidt.api.al.cmt.dto.req.ma.AiCmtMaEvStReqDto;
import com.aidt.api.al.cmt.dto.req.ma.AiCmtMaEvUdReqDto;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 10:58:14
 * @modify date 2024-05-21 10:58:14
 * @desc 수학 코멘트 노출 : 학년/학기 초 진단,  학기/학년 말 총괄 평가
 */
//@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/cmt/ma/tcr")
@Tag(name="[al] AI 코멘트(수학)", description="AI 코멘트(수학)")
public class AiCmtMaTcrController {

    private final AiCmtTcrService aiCommentTcrService;

    private final AiCmtUsrDataTcrService aiCmtUsrDataTcrService;

    private final JwtProvider jwtProvider;

    @Operation(summary="평가/학기초")
    @PostMapping(value = "/ev/st", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getMaEvStComment(@Valid @RequestBody AiCmtMaEvStReqDto reqDto) {
        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
        return Response.ok(aiCmtUsrDataTcrService.getOrInsertList(reqDto.getEvId(), userDetail.getUsrId(), reqDto, aiCommentTcrService::selectMaEv));
    }

    @Operation(summary="평가/학기말 총괄평가")
    @PostMapping(value = "/ev/et", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getMaEvEtComment(@Valid @RequestBody AiCmtMaEvEtReqDto reqDto) {
        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
        return Response.ok(aiCmtUsrDataTcrService.getOrInsertList(reqDto.getEvId(), userDetail.getUsrId(), reqDto, aiCommentTcrService::selectMaEv));
    }

    /* 지금 사용 안 함 */
//    @Operation(summary="평가/단원진단")
//    @PostMapping(value = "/ev/ud", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<List<AiCmtResDto>> getMaEvUdComment(@Valid @RequestBody AiCmtMaEvUdReqDto reqDto) {
//        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
//        return Response.ok(aiCmtUsrDataTcrService.getOrInsertList(reqDto.getEvId(), userDetail.getUsrId(), reqDto, aiCommentTcrService::selectMaEv));
//    }
}
