package com.aidt.api.at.token.dto;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "관리자 통계 KAFKA 연동 로그인 추가 데이터 조회")
public class LoginEventDto {

	@Schema(description = "cm_cla.학교코드")
	private String schlCd;

	@Schema(description = "cm_cla.학년")
	private Long sgy;

	@Schema(description = "cm_cla.학교급코드")
	private String schlGrdCd;

	@Schema(description = "cm_cla.KERIS반코드")
	private String kerisClaCd;

	@Schema(description = "lms_cms.bc_txb.교과서명")
	private String txbNm;

	@Schema(description = "lms_cms.bc_txb.저자코드")
	private String autrCd;

	@Schema(description = "lms_cms.bc_txb.과목코드")
	private String sbjCd;

	@Schema(description = "로그인일시")
	private LocalDateTime loginDtm;
}
