package com.aidt.api.bc.guidMnl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuideManualNode {
    /** 매뉴얼 노드 **/
    @Parameter(name="PK")
    private Long guidMnlNodId;

    @Parameter(name="매뉴얼 ID")
    private Long guidMnlId;

    @Parameter(name="primeVue 사용을 위한 key 적용")
    private String key;

    @Parameter(name="탭구분코드")
    private String tabDvCd;

    @Parameter(name="상위 노드 ID")
    private Long urnkGuidMnlNodId;

    @Parameter(name="뎁스")
    private int dpth;

    @Parameter(name="문서뷰어 ID")
    private String docViId;

    @Parameter(name="가이드목차 코드(외부제공용)")
    private String guidMnlNodCd;

    @Parameter(name="leaf 여부")
    private String lwsYn;

    @Parameter(name="노드명(제목)")
    private String guidMnlNodNm;

    @Parameter(name="상세내용(3뎁스)")
    private String guidMnlNodCn;

    @Parameter(name="영상요약(3뎁스)")
    private String guidMnlVdSmyCn;

    @Parameter(name="탭구분코드(3뎁스)")
    private String guidMnlTabDvCd;

    @Parameter(name="첨부파일(3뎁스)")
    private GuideManualUpload upload;

    @Parameter(name="표시순서")
    private int srtOrdn;

    @Parameter(name="삭제여부")
    private String delYn;

    @Parameter(name="자식노드")
    private List<GuideManualNode> children;

    @Parameter(name="생성일")
    private String crtDtm;

    @Parameter(name="수정일")
    private String mdfDtm;

    private String dbId;
    private String crtrId;
    private String mdfrId;
}
