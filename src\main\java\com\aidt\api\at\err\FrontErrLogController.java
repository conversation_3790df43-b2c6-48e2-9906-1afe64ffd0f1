package com.aidt.api.at.err;

import javax.servlet.http.HttpServletRequest;

import com.aidt.api.util.ErrorLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.at.err.dto.FrontErrLogDto;
import com.aidt.api.at.err.dto.WebSocketLogDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "[at] FRONT", description = "FRONT 장애 로그 등록 Controller")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token/front")
public class FrontErrLogController {

	@Autowired
	private FrontErrLogService frontErrLogService;

	@Autowired
	private ErrorLogUtil errorLogUtil;

	@Value("${server.meta.textbook.systemCode}")
	private String SYSTEM_CODE;

	@Operation(summary = "Front 장애", description = "Front 장애 등록요청 처리")
	@PostMapping(value = "/errInsert")
	public ResponseDto<Object> errInsert(@RequestBody FrontErrLogDto dto) {
		dto.setSystemCd(SYSTEM_CODE);
		return Response.ok(frontErrLogService.insertFrontErrLog(dto));
	}
	
	@Operation(summary = "WEB Socket 장애", description = "WEB Socket 장애 등록요청 처리")
	@PostMapping(value = "/socketErrInsert")
	public ResponseDto<Object> socketErrInsert(@RequestBody WebSocketLogDto dto, HttpServletRequest req) {
		String ip = errorLogUtil.getIP(req);
		dto.setUsrIp(ip);
		frontErrLogService.insertFrontWebSocketLog(dto);
		return Response.ok(true);
	}

//	private String getIP(HttpServletRequest request) {
//		String ip = request.getHeader("X-Forwarded-For");
//
//		if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
//			ip = request.getHeader("Proxy-Client-IP");
//		}
//
//		if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
//			ip = request.getHeader("WL-Proxy-Client-IP");
//		}
//
//		if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
//			ip = request.getHeader("HTTP_CLIENT_IP");
//		}
//
//		if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
//			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
//		}
//
//		if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
//			ip = request.getRemoteAddr();
//		}
//
//		return ip;
//	}

}
