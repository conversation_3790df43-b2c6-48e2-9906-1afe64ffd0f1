package com.aidt.api.at.token.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebAgencyDto {

    @NotNull(message = "KerisDto 필수 값입니다.")
    private KerisDto kerisDto;

    @NotEmpty(message = "UserIdList 값이 필요합니다.")
    private List<String> userIdList; // 학생 userId 리스트

    private String txbId;
    private String claId;
    private String optTxbId;
    private String userId;
    private String schoolId;
    private String authCode;

}

