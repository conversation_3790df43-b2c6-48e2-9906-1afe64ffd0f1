package com.aidt.api.sl.splrn.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnEaSendDto {
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="특별학습ID")
	private String spLrnId;
	
	@Parameter(name="특별학습명")
	private String spLrnNm;

	@Parameter(name="특별학습노드ID")
	private String spLrnNodId;
	
	@Parameter(name="특별학습노드Nm")
	private String spLrnNodNm;
	
//	@Parameter(name="특별학습콘텐츠Id")
//	private String spLrnCtnId;
//	
//	@Parameter(name="특별학습콘텐츠Nm")
//	private String spLrnCtnNm;
	
	@Parameter(name="일치여부 결과값")
	private int checkCount;
	
	@Parameter(name="특별학습노드목록")
	private List<SlSpLrnNodDto> spLrnNodList;
	
//	@Parameter(name="특별학습콘텐츠목록")
//	private List<BcSpLrnCtnDto> spLrnCtnList;
	
}
