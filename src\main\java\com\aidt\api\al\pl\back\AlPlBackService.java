//package com.aidt.api.al.pl.back;
//
//import java.util.List;
//
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto;
//import com.aidt.api.xx.sample.mybatis.dto.SampleMybatisSaveDto;
//import com.aidt.base.dao.CommonDao;
//
///**
// * <AUTHOR>
// * @email <EMAIL>
// * @create date 2023-12-12 22:42:32
// * @modify date 2023-12-12 22:42:32
// * @desc Sample Mybatis Service
// */
//@Service
//public class AlPlBackService {
//
//    private final String MAPPER_NAMESPACE = "api.al.sample.mybatis.";
//
//    @Autowired
//    private CommonDao commonDao;
//
//    /**
//     * 마이바티스 조회 서비스
//     *
//     * @param mybatisDto
//     * @return List<MybatisDto>
//     */
//    public List<SampleMybatisDto> selectList(String userId) {
//    	//테스트
//    	//깃테스트
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectList", userId);
//    }
//
//    /**
//     * 마이바티스 등록 서비스
//     *
//     * @param mybatisDtoList
//     * @return int
//     */
//    @Transactional
//    public int insertList(List<SampleMybatisDto> mybatisDtoList) {
//        int cnt = 0;
//        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
//            cnt += commonDao.insert(MAPPER_NAMESPACE + "insertList", mybatisDto);
//        }
//        return cnt;
//    }
//
//    /**
//     * 마이바티스 수정 서비스
//     *
//     * @param mybatisDtoList
//     * @return int
//     */
//    @Transactional
//    public int updateList(List<SampleMybatisDto> mybatisDtoList) {
//        int cnt = 0;
//        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
//            cnt += commonDao.update(MAPPER_NAMESPACE + "updateList", mybatisDto);
//        }
//        return cnt;
//    }
//
//    /**
//     * 마이바티스 삭제 서비스
//     *
//     * @param mybatisDtoList
//     * @return int
//     */
//    @Transactional
//    public int deleteList(List<SampleMybatisDto> mybatisDtoList) {
//        int cnt = 0;
//        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
//            cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteList", mybatisDto);
//        }
//        return cnt;
//    }
//
//    /**
//     * 마이바티스 저장 서비스
//     *
//     * @param MybatisSaveDto
//     * @return int
//     */
//    @Transactional
//    public int save(SampleMybatisSaveDto mybatisSaveDto) {
//        int cnt = 0;
//        boolean valid = true;
//
//        List<SampleMybatisDto> insertList = mybatisSaveDto.getInsert();
//        List<SampleMybatisDto> updateList = mybatisSaveDto.getUpdate();
//        List<SampleMybatisDto> deleteList = mybatisSaveDto.getDelete();
//
//        // 등록 데이터 검증
//        if (!insertList.isEmpty()) {
//            for (SampleMybatisDto insertDto : insertList) {
//                if (StringUtils.isEmpty(insertDto.getUsrId().trim())) {
//                    valid = false;
//                    break;
//                }
//            }
//            if (valid)
//                cnt += insertList(insertList); // 등록
//        }
//
//        // 수정 데이터 검증
//        valid = true;
//        if (!updateList.isEmpty()) {
//            for (SampleMybatisDto updateDto : updateList) {
//                if (StringUtils.isEmpty(updateDto.getUsrId().trim())) {
//                    valid = false;
//                    break;
//                }
//            }
//            if (valid)
//                cnt += updateList(updateList); // 수정
//        }
//
//        // 삭제 데이터 검증
//        valid = true;
//        if (!deleteList.isEmpty()) {
//            for (SampleMybatisDto deleteDto : deleteList) {
//                if (StringUtils.isEmpty(deleteDto.getUsrId().trim())) {
//                    valid = false;
//                    break;
//                }
//            }
//            if (valid)
//                cnt += deleteList(deleteList); // 삭제
//        }
//
//        return cnt;
//    }
//
//    /**
//     * 마이바티스 저장 서비스
//     *
//     * @param MybatisSaveDto
//     * @return int
//     */
//    @Transactional
//    public int bulkInsert(List<SampleMybatisDto> mybatisDtoList) {
//        return commonDao.insert(MAPPER_NAMESPACE + "bulkInsert", mybatisDtoList);
//    }
//}
