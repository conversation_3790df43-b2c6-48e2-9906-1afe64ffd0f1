package com.aidt.api.at.token.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "Auth정보")
public class AtUsrInfoDto {

	@Parameter(name="개인식별번호")
	private String usrId;

	@Parameter(name="사용자유형코드")
	private String usrTpCd;

	@Parameter(name="학급ID")
	private String claId;

	@Parameter(name="교과서학기코드")
	private String txbTermCd;

	@Parameter(name="교과서ID")
	private String txbId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="사용자상태")
	private String userStatus;

	@Parameter(name="강의코드")
	private String lectureCode;

	@Parameter(name="학급코드")
	private String classCode;

	@Parameter(name="로그인ID")
	private String loginId;
	
	@Parameter(name="데이터베이스ID")
	private String dbId;

	@Parameter(name="KERIS사용자ID")
	private String kerisUsrId;
	
	@Parameter(name="KERIS약관동의여부")
	private String kerisTermAgrYn;
	
	@Parameter(name="KERIS약관동의일자")
	private String kerisTermAgrDt;

	/*

	@Parameter(name="유저타입")
	private String userType;

	@Parameter(name="사용자명")
	private String usrNm;

	@Parameter(name="교사사용자ID(교사UUID)")
	private String chgTcrUsrId;

	@Parameter(name="교사유형코드")
	private String tcrTpCd;

	@Parameter(name="반명")
	private String claNm;

	@Parameter(name="교사사용자ID(교사UUID)")
	private String chgTcrUsrId;

	@Parameter(name="관심여부")
	private String ntrYn;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="학교ID")
	private String schoolId;

	@Parameter(name="학교명")
	private String schoolName;

	@Parameter(name="학교구분코드")
	private String userDivision;

	@Parameter(name="학교구분코드(내부용)")
	private String schlGrdCd;

	@Parameter(name="사용자명")
	private String userName;

	@Parameter(name="학년")
	private String userGrade;

	@Parameter(name="반")
	private String userClass;

	@Parameter(name="번호")
	private String userNumber;

	@Parameter(name="성별")
	private String userGender;

	@Parameter(name="학생목록")
	private List<AtUsrInfoDto> stuList;*/

}
