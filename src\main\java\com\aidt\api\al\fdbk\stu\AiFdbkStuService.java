package com.aidt.api.al.fdbk.stu;

import com.aidt.api.al.fdbk.dto.AiFdbkGrowthRankDto;
import org.springframework.stereotype.Service;

import com.aidt.api.al.fdbk.cm.AiFdbkCmService;
import com.aidt.api.al.fdbk.dto.AiFdbkDto;
import com.aidt.api.al.fdbk.dto.req.AiFdbkStuReqDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:53:50
 * @modify date 2024-07-11 15:53:50
 * @desc
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiFdbkStuService {

    private final AiFdbkCmService aiFdbkCmService;

    public AiFdbkDto getFdbk(AiFdbkStuReqDto reqDto) {
        AiFdbkDto aiFdbkDto = aiFdbkCmService.selectFdbk(reqDto.toDto());
        return aiFdbkDto;
    }

    public List<AiFdbkGrowthRankDto> getGrowthRankList(String optTxbId, String lrmpNodId, String stuId) {
        return aiFdbkCmService.selectGrowthRankList(optTxbId, lrmpNodId, stuId);
    }
}
