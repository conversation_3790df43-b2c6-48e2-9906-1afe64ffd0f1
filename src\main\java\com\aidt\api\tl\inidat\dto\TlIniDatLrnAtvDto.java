package com.aidt.api.tl.inidat.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlIniDatLrnAtvDto {
	/** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 차시ID */
    @Parameter(name="차시ID")
    private String lrmpNodId;
    
    /** 학습활동ID */
    @Parameter(name="학습활동ID")
    private String lrnAtvId;
    
    /** 학습단계ID */
    @Parameter(name="학습단계ID")
    private String lrnStpId;
    
    /** 콘텐츠 구분 */
    @Parameter(name="콘텐츠구분")
    private String ctnCd;
    
    /** 학습활동명 */
    @Parameter(name="학습활동명")
    private String lrnAtvNm;
    
    /** 콘텐츠 타입 코드 */
    @Parameter(name="콘텐츠 타입 코드")
    private String ctnTpCd;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;
    
    /** 원본학습단계ID */
    @Parameter(name="원본학습단계ID")
    private String orglLrnStpId;
    
    /** 원본순서 */
    @Parameter(name="원본순서")
    private int orglOrdn;
    
    /** 재구성순서 */
    @Parameter(name="재구성순서")
    private int rcstnOrdn;
    
    /** 평가ID */
    @Parameter(name="평가ID")
    private int evId;

}
