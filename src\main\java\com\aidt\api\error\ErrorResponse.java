package com.aidt.api.error;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponse {
    private String errorStatus = "ERROR_PROF";
    private String errorCode;
    private String errorMessage;

    public ErrorResponse returnErrorResponse(String errorCode, String errorMessage) {
        ErrorResponse response = new ErrorResponse();
        response.errorCode = errorCode;
        response.errorMessage = errorMessage;

        return response;
    }
}
