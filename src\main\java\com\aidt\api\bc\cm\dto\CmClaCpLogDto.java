package com.aidt.api.bc.cm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "CM_학급_복제_로그")
public class CmClaCpLogDto {

	@Schema(description = "운영교과서ID")
	private String optTxbId;
	
	@Schema(description = "복제운영교과서ID")
	private String cpOptTxbId;
	
	@Schema(description = "복제구분코드 (OCK [원클릭] / ASN [과제] / EV [평가] / BLBD [학급게시판]")
	private String cpDvCd;
	
	@Schema(description = "복제처리여부 (대상이지만 복제가 되지 않는 경우 N")
	private String cpPrcsYn;
	
	@Schema(description = "BACKEND파일경로 (service class.method 명")
	private String backendFlePth;
	
	@Schema(description = "token kerisUsrId")
	private String kerisUsrId;
	
	@Schema(description = "token usrId")
	private String crtrId;
}
