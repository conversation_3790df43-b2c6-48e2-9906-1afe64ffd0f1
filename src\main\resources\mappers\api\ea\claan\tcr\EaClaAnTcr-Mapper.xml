<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.claan.tcr">
    <select id="selectClaCnt" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="int">
        SELECT COUNT(1)
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectClaCnt - 학급 인원 조회 */
    </select>

    <select id="selectOptTxbIdList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto">
        SELECT OPT_TXB_ID FROM LMS_LRM.CM_OPT_TXB
        WHERE CLA_ID IN( SELECT CLA.CLA_ID FROM LMS_LRM.CM_CLA CLA
                         INNER JOIN( SELECT SCHL_CD,SGY FROM LMS_LRM.CM_OPT_TXB OT
                                     INNER JOIN LMS_LRM.CM_CLA CC ON OT.CLA_ID=CC.CLA_ID
                                     WHERE OPT_TXB_ID =#{optTxbId}
                         )T ON (CLA.SCHL_CD = T.SCHL_CD AND CLA.SGY=T.SGY)
        )
        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectOptTxbIdList - 학년별 운영 교과서 ID 조회 */
    </select>

    <select id="selectLluNodIdList"  parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
        resultType="hashMap">
        SELECT
            ORGL_ORDN   AS orglOrdn,
            LRMP_NOD_ID AS lrmpNodId,
            CONCAT(ORGL_ORDN,".",LRMP_NOD_NM)  AS lrmpNodNm
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID = #{optTxbId}
        AND URNK_LRMP_NOD_ID IS NULL
        AND USE_YN = 'Y'
        ORDER BY RCSTN_ORDN
        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectLluNodIdList - 대단원 목록 조회 */
    </select>



    <select id="selectStuTimeEvResInfo" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
        resultType="hashMap">
        SELECT
        IFNULL(TIME_FORMAT(SEC_TO_TIME(ROUND(SUM(T.evTmScnt)/#{claCnt})),'%H'),'00')  		 AS evTmScntAvg,    -- 누적 평균 시간
        IFNULL(TIME_FORMAT(SEC_TO_TIME(ROUND(SUM(T.evTmScnt)/count(T.stdDate))),'%H'),'00')  AS evTmScntDayAvg, -- 일 평균 시간
        IFNULL(ROUND((SUM(T.cansCnt)/SUM(T.qstCnt))*100),0)									 AS cansRtAvg,		-- 평균 정답 률
        IFNULL(ROUND(SUM(T.qstCnt)/#{claCnt}),0)											 AS qstCntAvg       -- 평균 문제 풀이 수
        FROM(
            SELECT
                G.stdDate,
                SUM(G.evTmScnt) AS evTmScnt,
                SUM(G.qstCnt)	AS qstCnt,
                SUM(G.cansCnt)  AS cansCnt
            FROM
            (
                /*교과평가,AI평가,교사평가*/
                SELECT
                    DATE_FORMAT(EER.SMT_DTM,'%Y%m%d')   AS stdDate,  -- 학습 일
                    SUM(EER.EV_TM_SCNT) 				AS evTmScnt, -- 평가 시간
                    SUM(EE.FNL_QST_CNT)                 AS qstCnt,	 -- 문제 수
                    SUM(EER.CANS_CNT)					AS cansCnt	 -- 정답 수
                FROM LMS_LRM.EA_EV EE
                INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID IN (
                SELECT U.USR_ID
                FROM LMS_LRM.CM_OPT_TXB OT
                JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
                WHERE OT.OPT_TXB_ID = #{optTxbId}
                AND U.USR_TP_CD = 'ST'
                ))
                WHERE EE.OPT_TXB_ID = #{optTxbId}
                AND EE.USE_YN      = 'Y'
                AND EE.DEL_YN      = 'N'
                AND EER.EV_CMPL_YN = 'Y'
                AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(EER.SMT_DTM),0)  <![CDATA[<=]]>  MONTH(NOW())
                GROUP BY  DATE_FORMAT(EER.SMT_DTM,'%Y%m%d')
        
                UNION ALL
        
                /*교과학습*/
                SELECT
                    DATE_FORMAT(MDF_DTM,'%Y%m%d')   AS stdDate,
                    SUM(LRN_TM_SCNT) 				AS evTmScnt,
                    0								AS qstCnt,
                    0								AS cansCnt
                FROM LMS_LRM.TL_SBC_LRN_ATV_ST
                WHERE OPT_TXB_ID = #{optTxbId}
                AND   LRN_USR_ID IN (
                SELECT U.USR_ID
                FROM LMS_LRM.CM_OPT_TXB OT
                JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
                WHERE OT.OPT_TXB_ID = #{optTxbId}
                AND U.USR_TP_CD = 'ST'
                )
                AND   LRN_ST_CD  = 'CL' -- 학습 완료
                AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(MDF_DTM),0)  <![CDATA[<=]]>   MONTH(NOW())
                GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d')
        
                UNION ALL
        
                /*특별학습*/
                SELECT
                    DATE_FORMAT(MDF_DTM,'%Y%m%d')	AS stdDate,
                    SUM(LRN_TM_SCNT) 				AS evTmScnt,
                    0 				                AS qstCnt,
                    0								AS cansCnt
                FROM LMS_LRM.SL_SP_LRN_PGRS_ST
                WHERE OPT_TXB_ID = #{optTxbId}
                AND   LRN_USR_ID IN (
                SELECT U.USR_ID
                FROM LMS_LRM.CM_OPT_TXB OT
                JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
                WHERE OT.OPT_TXB_ID = #{optTxbId}
                AND U.USR_TP_CD = 'ST'
                )
                AND   LRN_ST_CD  = 'CL'-- 학습 완료
                AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(MDF_DTM),0)  <![CDATA[<=]]>   MONTH(NOW())
                GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d')
                /*AI학습은 추후 테이블 생성 될 예정 최익재님이 쿼리 제공*/
            ) G GROUP BY G.stdDate
        ) T

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectStuTimeEvResInfo - 학습 시간 + 평가 결과 조회 */
    </select>

    <select id="selectGoodAndBadTcTop3" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
        SELECT TT.* FROM(
            SELECT
                T.QP_TC_ID                             AS qpTcId,
                T.QP_TC_NM                             AS qpTcNm,
                (SUM(T.CANS_YN)/COUNT(T.QP_TC_ID))*100 AS cansRt
            FROM(
                SELECT
                    EEQ.QP_TC_ID,
                    EEQ.QP_TC_NM,
                    CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END CANS_YN
                FROM LMS_LRM.EA_EV EE
                INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID IN (
                SELECT U.USR_ID
                FROM LMS_LRM.CM_OPT_TXB OT
                JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
                WHERE OT.OPT_TXB_ID = #{optTxbId}
                AND U.USR_TP_CD = 'ST'
                ))
                INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EE.EV_ID = EEQ.EV_ID AND EEQ.DEL_YN = 'N')
                INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID)
                WHERE EE.OPT_TXB_ID = #{optTxbId}
                AND EE.USE_YN      = 'Y'
                AND EE.DEL_YN      = 'N'
                AND EER.EV_CMPL_YN = 'Y'
                AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(EER.SMT_DTM),0)  <![CDATA[<=]]>  MONTH(NOW())
            ) T
            GROUP BY T.QP_TC_ID,T.QP_TC_NM
			<choose>
	    	<when test = 'sorting != null and sorting.equals("DESC")'><!-- 정렬구분 -->
	        ORDER BY (sum(T.CANS_YN)/count(T.QP_TC_ID))*100 DESC
		    </when> 
			<otherwise>  
	        ORDER BY (sum(T.CANS_YN)/count(T.QP_TC_ID))*100 ASC
			</otherwise>    
			</choose>   
        ) TT LIMIT 3

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectGoodAndBadTcTop3 - 강점,취약 차시 top3  조회 */
    </select>
    
    <select id="selectClaAnUsrsAchd" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
            
		SELECT 
				A.USR_ID	AS usrId
			  , A.USR_NM 	AS usrNm
			  , ROUND(IFNULL(A.EV_CANS_RT,0)) AS y -- achRt 		-- 성취도 : 평가 정답률
			  , ROUND(Percent_rank() OVER(ORDER BY A.allLrnTm) * 100) AS x -- lrnRt -- 학습률  -- 랭킹 백분율은 학생들이 전부 학습시간 많을경우 문제 있음
			  -- , CASE WHEN A.allLrnTm = 0 THEN 0 ELSE ROUND(CUME_DIST() OVER(ORDER BY A.allLrnTm) * 100) END AS x -- lrnRt -- 학습률
		FROM (		
				SELECT 
						USR.USR_ID
					  , USR.USR_NM
					  , EV.EV_CANS_RT -- 성취도 : 평가 정답률
					  , IFNULL(SL.CMPL_TM_SCNT,0)+IFNULL(SP.LRN_TM_SCNT,0)+IFNULL(EV.EV_TM_SCNT,0) AS allLrnTm -- 학습시간 (교과 개념학습 + 특별학습 + 평가(교가, 교과))
				FROM (
						SELECT USR.USR_ID, MAX(USR.USR_NM) USR_NM
						FROM LMS_LRM.CM_USR USR /* 사용자 정보 */
						WHERE USR.CLA_ID = #{claId}
						AND USR.USR_TP_CD = 'ST'
						GROUP BY USR.USR_ID
				) USR
				LEFT JOIN (   /* 사용자별 교과학습 학습시간 */
			  		SELECT ST.OPT_TXB_ID
			  			 , ST.LRN_USR_ID
			  			 , SUM(ST.LRN_TM_SCNT) 				AS CMPL_TM_SCNT -- 총 학습시간(교과 학습 개념 학습만)
			        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV  /* TL_교과학습활동재구성 */
			  		JOIN LMS_LRM.TL_SBC_LRN_ATV_ST ST ON ST.OPT_TXB_ID = ATV.OPT_TXB_ID  /* TL_교과학습활동상태 */
			  		     AND ST.LRMP_NOD_ID = ATV.LRMP_NOD_ID 
			  		     AND ST.LRN_ATV_ID = ATV.LRN_ATV_ID 
			        WHERE ATV.OPT_TXB_ID = #{optTxbId}       
			        AND EXISTS(SELECT 1 FROM LMS_CMS.BC_LRN_STP STP WHERE STP.LRN_STP_ID = ATV.LRN_STP_ID AND STP.LRN_STP_DV_CD = 'CL') /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EV: 평가) */
			  		AND ST.LRN_ST_CD = 'CL' /* 학습상태 - 완료 */
			        GROUP BY ST.OPT_TXB_ID, ST.LRN_USR_ID
				) SL ON SL.LRN_USR_ID = USR.USR_ID
				LEFT JOIN ( /* 특별학습 학습시간*/
		            SELECT
		                   SPS.OPT_TXB_ID, SPS.LRN_USR_ID
		                 , SUM(SPS.LRN_TM_SCNT) AS LRN_TM_SCNT
		            FROM LMS_LRM.SL_SP_LRN_RCSTN SP
		            JOIN LMS_LRM.SL_SP_LRN_PGRS_ST SPS ON SPS.OPT_TXB_ID = SP.OPT_TXB_ID AND SPS.SP_LRN_ID = SP.SP_LRN_ID
		            WHERE SP.OPT_TXB_ID = #{optTxbId}
		            AND SPS.LRN_ST_CD  = 'CL'-- 학습 완료	
		            GROUP BY SPS.OPT_TXB_ID, SPS.LRN_USR_ID	
				) SP ON SP.LRN_TM_SCNT = USR.USR_ID
				LEFT JOIN ( /* 평가 목록에서 평가 진행한 정답률, 학습시간 계산*/
						SELECT 
							   E.OPT_TXB_ID
							 , ER.USR_ID
							 , SUM(ER.CANS_CNT)/SUM(E.FNL_QST_CNT)*100 AS EV_CANS_RT -- 정답률
							 , SUM(ER.CANS_CNT) CANS_CNT -- 총 접답수
							 , SUM(E.FNL_QST_CNT) FNL_QST_CNT -- 총 문항수
							 , SUM(ER.EV_TM_SCNT) EV_TM_SCNT -- 총 평가 시간
						FROM LMS_LRM.EA_EV E
						JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
						WHERE E.OPT_TXB_ID = #{optTxbId}
						AND E.EV_DV_CD IN ('SE', 'TE', 'DE') -- AI평가는 제외
						AND ER.EV_CMPL_YN = 'Y'
						GROUP BY E.OPT_TXB_ID, ER.USR_ID
				) EV ON EV.USR_ID = USR.USR_ID
				WHERE 1 = 1
		) A

        /* 학급 분석 - 박원희 - EaClaAnTcr-Mapper.xml - selectClaAnUsrsAchd - 우리반 학습분포도 */
        
    </select>
    
    <select id="selectFstLrnDt" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
    	SELECT 
		    COUNT(*) * 8 * 3600 AS minCrtDtm
		FROM (
		    SELECT 
		        CURDATE() - INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY AS day
		    FROM 
		        (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a
		    CROSS JOIN 
		        (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b
		    CROSS JOIN 
		        (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c
		) AS all_days
		WHERE 
		    day BETWEEN STR_TO_DATE(
		    	(select
					DATE_FORMAT(min(CRT_DTM), '%Y%m%d') as minCrtDtm
				from
					CM_LRN_TM CLT
				where
					OPT_TXB_ID = #{optTxbId}), '%Y%m%d') AND CURDATE()
		    AND WEEKDAY(day) <![CDATA[<]]> 5;
    </select>
    
    <select id="selectClaAnUsrsAchd2" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
	    SELECT
		    T1.usr_id as usrId,  
		    CAST(IFNULL(MAX(T1.LRNSCNT), 0) AS DOUBLE) AS ACNT, -- 학습시간
			IFNULL(T2.EV_CANS_RT, 0) as y,
		    T1.USR_NM AS usrNm, -- 유저 이름
		    DATE_FORMAT(T1.MDFDTM, '%Y-%m-%d') AS mdfdtm, -- 최근 학습일
		    CASE WHEN T1.LRNR_VEL_TP_CD = 'FS' THEN '빠른'  
		    	 WHEN T1.LRNR_VEL_TP_CD = 'NM' THEN '보통'
		    	 WHEN T1.LRNR_VEL_TP_CD = 'BA' THEN '-'		    	 
		    ELSE '느린'  
			END AS lrnrVelNm		   
		from 
		(SELECT
		        SUM(clt.TXB_LRN_TM_SCNT + clt.AI_LRN_TM_SCNT + clt.SP_LRN_TM_SCNT + clt.EV_LRN_TM_SCNT + clt.AI_EV_LRN_TM_SCNT + clt.AI_WRTNG_LRN_TM + clt.AI_RDNG_LRN_TM) AS LRNSCNT,
		        clt.USR_ID,
		        cu.usr_nm,
		        cu.LRNR_VEL_TP_CD,
		        MAX(clt.MDF_DTM) AS MDFDTM,
		        clt.opt_txb_id
		     from lms_lrm.cm_opt_txb cot2
		     inner join lms_lrm.cm_usr cu 
		     	on cu.cla_id = cot2.cla_id
		     	and cu.usr_tp_cd = 'ST'
		     inner join lms_lrm.cm_lrn_tm clt 
		     	on clt.opt_txb_id = cot2.opt_txb_id
		     	and clt.usr_id = cu.usr_id
		     WHERE
		        1=1
		        AND clt.OPT_TXB_ID = #{optTxbId}
		     GROUP BY clt.USR_ID, cu.usr_nm, cu.LRNR_VEL_TP_CD, clt.opt_txb_id) T1
		left join 
		(
			SELECT 
					 E.OPT_TXB_ID
					, ER.USR_ID
					, SUM(ER.CANS_CNT)/SUM(E.FNL_QST_CNT)*100 AS EV_CANS_RT -- 정답률
					, SUM(ER.CANS_CNT) CANS_CNT -- 총 접답수
					, SUM(E.FNL_QST_CNT) FNL_QST_CNT -- 총 문항수
					 , SUM(ER.EV_TM_SCNT) EV_TM_SCNT -- 총 평가 시간
			FROM LMS_LRM.EA_EV E
			JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
			WHERE E.OPT_TXB_ID = #{optTxbId}
			AND (
			(E.ev_dv_cd = 'SE' and E.ev_dtl_dv_cd in ('FO', 'UG'))    -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
		    or (E.ev_dv_cd = 'AE' and E.ev_dtl_dv_cd = 'OV') )
			AND ER.EV_CMPL_YN = 'Y'
			GROUP BY E.OPT_TXB_ID, ER.USR_ID
		) as T2
		ON T2.USR_ID = T1.USR_ID
		and T2.opt_txb_id = #{optTxbId}
		GROUP by  T1.USR_ID, T2.USR_ID, T2.EV_CANS_RT,T1.MDFDTM, T1.LRNR_VEL_TP_CD
  	  /* EaClaAnTcr-Mapper.xml 정은혜 selectClaAnUsrsAchd2 우리반 학습 분포도 가져오기*/
    </select>
    
    <select id="selectClaDfclRatioList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
	    SELECT
    		T1.usr_id as usrId,  
		    -- 난이도 '하' 정답율
		    IFNULL(
		        (SUM(CASE WHEN T1.cans_yn = 'Y' AND T1.QTM_DFFD_DV_CD IN ('01', '02') THEN 1 ELSE 0 END) * 100.0) /
		        NULLIF(SUM(CASE WHEN T1.QTM_DFFD_DV_CD IN ('01', '02') THEN 1 ELSE 0 END), 0), 
		        0
		    ) AS lowAnsRate,
		    -- 난이도 '중' 정답율
		    IFNULL(
		        (SUM(CASE WHEN T1.cans_yn = 'Y' AND T1.QTM_DFFD_DV_CD = '03' THEN 1 ELSE 0 END) * 100.0) /
		        NULLIF(SUM(CASE WHEN T1.QTM_DFFD_DV_CD = '03' THEN 1 ELSE 0 END), 0), 
		        0
		    ) AS mediumAnsRate,
		    -- 난이도 '상' 정답율
		    IFNULL(
		        (SUM(CASE WHEN T1.cans_yn = 'Y' AND T1.QTM_DFFD_DV_CD IN ('04', '05') THEN 1 ELSE 0 END) * 100.0) /
		        NULLIF(SUM(CASE WHEN T1.QTM_DFFD_DV_CD IN ('04', '05') THEN 1 ELSE 0 END), 0), 
		        0
		    ) AS highAnsRate
		FROM
		    (
			select MAX(eeq.EV_ID) AS EV_ID, MAX(eeq.QP_DFFD_NM) AS QP_DFFD_NM, MAX(eeq.QTM_DFFD_DV_CD) AS QTM_DFFD_DV_CD, eeq.QTM_ID, CU.USR_ID, MAX(eeqa.CANS_YN) AS CANS_YN, MAX(CU.USR_NM) AS USR_NM, MAX(CU.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD
			from lms_lrm.ea_ev_qtm_anw eeqa
			inner join lms_lrm.ea_ev ee 
				on ee.EV_ID = eeqa.EV_ID 
				and OPT_TXB_ID = #{optTxbId}
				and  ee.EV_DTL_DV_CD IN ('FO', 'UG', 'OV')
			LEFT join lms_lrm.ea_ev_qtm eeq 
				on eeq.EV_ID = ee.EV_ID -- and eeq.QTM_DFFD_DV_CD IN ('01', '02', '03', '04', '05')
				and eeq.qtm_id = eeqa.qtm_id
			LEFT JOIN LMS_LRM.CM_USR CU 
			 	ON eeqa.USR_ID = CU.USR_ID
			 	and CU.USR_TP_CD = 'ST'
			 	and eeqa.USR_ID IN 
			 	<foreach item="item" index="index" collection="usrsAchdList" open="(" separator="," close=")">
		 	   #{item.usrId}
				</foreach>
			group by CU.usr_id,  eeq.QTM_ID 
		) as T1
		WHERE
			1=1
			and T1.USR_ID IN
			<foreach item="item" index="index" collection="usrsAchdList" open="(" separator="," close=")">
		 	   #{item.usrId}
			</foreach>
		group by T1.USR_ID
		/* EaClaAnTcr-Mapper.xml 정은혜 selectClaDfclRatioList 우리반 학습 분포도 난이도별 가져오기*/
    </select>
    

    <select id="selectAchStatByUnitMyClass" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
        SELECT
            TSLNR.LRMP_NOD_ID             AS lrmpNodId,  -- 단원 ID
            CONCAT(TSLNR.ORGL_ORDN,'단원') AS lrmpNodOrdn,-- 단원 순서
            TSLNR.LRMP_NOD_NM             AS lrmpNodNm,  -- 단원 명
            COUNT(QTM.CANS_YN) 			  AS totCnt,     -- 학습 이력 카운트
            IFNULL(ROUND((COUNT(CASE WHEN QTM.CANS_YN='Y' THEN 1 END )/COUNT(TSLNR.LRMP_NOD_ID))*100),0) AS cansRat -- 단원 별 성취율(정답률)
        FROM ( SELECT ORGL_ORDN, LRMP_NOD_ID, LRMP_NOD_NM <!--단원 조회 -->
                FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID = #{optTxbId}
                AND URNK_LRMP_NOD_ID = ''
                AND USE_YN = 'Y'
                ORDER BY RCSTN_ORDN
        ) TSLNR
        LEFT JOIN
        (<!--우리반 단원별  정오답 조회-->
        SELECT
            EEQ.QP_LLU_ID ,
            EEQA.CANS_YN
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER
        ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'
        ) AND EER.EV_CMPL_YN='Y')
        INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N')
        INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=EER.USR_ID)
        WHERE EE.OPT_TXB_ID =#{optTxbId}
        AND   EE.DEL_YN ='N'
        AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
        AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> MONTH(NOW())
        )QTM ON TSLNR.LRMP_NOD_ID=QTM.QP_LLU_ID
        GROUP BY TSLNR.LRMP_NOD_ID, TSLNR.LRMP_NOD_NM

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectAchStatByUnitMyClass - 단원별 성취 현황 우리반 평균  조회 */
    </select>

    <select id="selectAchStatByUnitSchTot" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
        SELECT
            TSLNR.LRMP_NOD_ID             AS lrmpNodId,  -- 단원 ID
            CONCAT(TSLNR.ORGL_ORDN,'단원') AS lrmpNodOrdn,-- 단원 순서
            TSLNR.LRMP_NOD_NM             AS lrmpNodNm,  -- 단원 명
            COUNT(QTM.CANS_YN) 			  AS totCnt,     -- 학습 이력 카운트
            IFNULL(ROUND((COUNT(CASE WHEN QTM.CANS_YN='Y' THEN 1 END )/COUNT(TSLNR.LRMP_NOD_ID))*100),0) AS cansRat -- 단원 별 성취율(정답률)
        FROM ( SELECT ORGL_ORDN, LRMP_NOD_ID, LRMP_NOD_NM <!--단원 조회 -->
                FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID = #{optTxbId}
                AND URNK_LRMP_NOD_ID = ''
                AND USE_YN = 'Y'
                ORDER BY RCSTN_ORDN
        ) TSLNR
        LEFT JOIN
        (<!--학년 전체 단원별  정오답 조회-->
        SELECT
            EEQ.QP_LLU_ID ,
            EEQA.CANS_YN
            FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER
        ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID IN
        <foreach item="item" index ="index" collection="optTxbIdList" open="(" separator="," close=")">
            #{item.optTxbId} <!-- 학년별 운영 교과서 아이디-->
        </foreach>
        AND U.USR_TP_CD = 'ST'
        ) AND EER.EV_CMPL_YN='Y')
        INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N')
        INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=EER.USR_ID)
        WHERE EE.OPT_TXB_ID IN
        <foreach item="item" index ="index" collection="optTxbIdList" open="(" separator="," close=")">
            #{item.optTxbId} <!-- 학년별 운영 교과서 아이디-->
        </foreach>
        AND   EE.DEL_YN ='N'
        AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
        AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> MONTH(NOW())
        )QTM ON TSLNR.LRMP_NOD_ID=QTM.QP_LLU_ID
        GROUP BY TSLNR.LRMP_NOD_ID, TSLNR.LRMP_NOD_NM

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectAchStatByUnitSchTot - 단원별 성취 현황 학년 전체 평균  조회 */
    </select>

    <select id="selectLluMyClassAvg" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
        SELECT
            COUNT(CASE WHEN EEQA.CANS_YN ='Y' THEN 1 END ) AS cansCnt,                                 -- 정답 수
            COUNT(EEQA.CANS_YN) 						   AS qstCnt,                                  -- 문제 수
            IFNULL(ROUND((COUNT(CASE WHEN EEQA.CANS_YN ='Y' THEN 1 END )/COUNT(EEQA.CANS_YN))*100),0) AS cansRat -- 우리반 성취율(정답률)
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER
        ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'
        ) AND EER.EV_CMPL_YN='Y')
        INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N' AND EEQ.QP_LLU_ID = #{lluNodId})
        INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=EER.USR_ID)
        WHERE EE.OPT_TXB_ID =#{optTxbId}
        AND   EE.DEL_YN ='N'
        AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
        AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> MONTH(NOW())

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectLluMyClassAvg - 단원별 상세 현황 > 우리반 평균  조회 */
    </select>

    <select id="selectLluTotAvg" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="int">
        SELECT
            IFNULL(ROUND((COUNT(CASE WHEN EEQA.CANS_YN ='Y' THEN 1 END )/COUNT(EEQA.CANS_YN))*100),0) AS cansRat -- 전체 성취율(정답률)
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER
        ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID IN
        <foreach item="item" index ="index" collection="optTxbIdList" open="(" separator="," close=")">
            #{item.optTxbId} <!-- 학년별 운영 교과서 아이디-->
        </foreach>
        AND U.USR_TP_CD = 'ST'
        ) AND EER.EV_CMPL_YN='Y')
        INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N' AND EEQ.QP_LLU_ID = #{lluNodId})
        INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=EER.USR_ID)
        WHERE EE.OPT_TXB_ID IN
        <foreach item="item" index ="index" collection="optTxbIdList" open="(" separator="," close=")">
            #{item.optTxbId} <!-- 학년별 운영 교과서 아이디-->
        </foreach>
        AND   EE.DEL_YN ='N'
        AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
        AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> MONTH(NOW())

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectLluTotAvg - 단원별 상세 현황 > 전체 평균  조회 */
    </select>


    <select id="selectLevelUnitAvgList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
        SELECT
            LEV.CM_CD_NM AS levelNm,
            IFNULL(ROUND((COUNT(CASE WHEN QTM.CANS_YN='Y' THEN 1 END )/COUNT(QTM.CANS_YN))*100),0) AS cansRat -- 난이도 별 성취율(정답률)
        FROM ( SELECT CM_CD, CM_CD_NM, SRT_ORDN FROM lms_lrm.cm_cm_cd
        WHERE URNK_CM_CD ='QTM_PLTFM_DFFD_DV_CD'
        AND   SRT_ORDN <![CDATA[<>]]>0
        ORDER BY SRT_ORDN
        ) LEV
        LEFT JOIN
        (
        SELECT
            EEQ.QTM_DFFD_DV_CD ,
            EEQA.CANS_YN
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER
        ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'
        ) AND EER.EV_CMPL_YN='Y')
        INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N' AND EEQ.QP_LLU_ID = #{lluNodId})
        INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=EER.USR_ID)
        WHERE EE.OPT_TXB_ID =#{optTxbId}
        AND   EE.DEL_YN ='N'
        AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
        AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> MONTH(NOW())
        )QTM ON LEV.CM_CD = QTM.QTM_DFFD_DV_CD
        GROUP BY LEV.CM_CD, LEV.CM_CD_NM
        ORDER BY LEV.SRT_ORDN

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectLevelUnitAvgList - 단원별 상세 현황 > 난이도별 평균 조회 */
    </select>

    <select id="selectWrongAnwBestList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
            
        SELECT  ROW_NUMBER() OVER(ORDER BY E.cansNcnt DESC, E.qpLluId, E.qtmId) AS rowNum
	          , E.srtOrdn      -- 단원 순서
	          , E.qtmId        -- 문항 ID
	          , E.tpcId        -- 토픽 ID
	          , E.qpLluId      -- 대단원 ID
	          , E.qpLluNm      -- 대단원 명
	          , E.qpTcId       -- 차시 ID
	          , E.qpTcNm       -- 토픽 명
	          , E.qtmDffdDvCd  -- 문항 난이도 코드
	          , E.cansNcnt     -- 오답자 수
	          , E.totCnt       -- 풀이자 수
	          , E.cansNrt      -- 오답율
	          , QC.QP_QST_CN  AS qpQstCn     -- 문항 내용
	          , E.lluNodId 	   -- 대단원 노드 ID
        FROM(
		        SELECT  
			             EQ.QTM_ID 			 AS qtmId       -- 문항 ID
			            ,max(EQ.QTM_ORDN)	 AS srtOrdn     -- 단원 순서
			            ,EQ.TPC_ID			 AS tpcId		-- 토픽 ID
			            ,EQ.QTM_DFFD_DV_CD 	 AS qtmDffdDvCd -- 문항 난이도 코드
			            ,EQ.QP_LLU_ID  	 	 AS qpLluId     -- 대단원 ID
						,EQ.QP_LLU_NM		 AS qpLluNm     -- 대단원 명
						,EQ.QP_TC_ID		 AS qpTcId      -- 차시 ID
                        ,( SELECT km.KMMP_NOD_NM
                            FROM lms_cms.bc_kmmp_nod km
                            WHERE km.kmmp_nod_id = EQ.TPC_ID ) AS qpTcNm -- 토픽명
			            ,COUNT(DISTINCT CASE WHEN EQA.CANS_YN = 'N' THEN EQA.USR_ID END) AS cansNcnt   -- 오답자 수
			            ,MAX(USR.USR_CNT) 	 AS totCnt     -- 풀이자 수
			            ,ROUND(COUNT(CASE WHEN EQA.CANS_YN ='N' THEN 1 END)/MAX(USR.USR_CNT)*100,0) AS cansNrt     -- 오답율
			            ,(select l_dpth4.llu_nod_id
                            from lms_cms.bc_kmmp_nod k_dpth5
                           inner join lms_cms.bc_kmmp_nod k_dpth4
                              on k_dpth4.kmmp_nod_id = k_dpth5.urnk_kmmp_nod_id
                           inner join lms_cms.bc_lrmp_kmmp_nod_mpn nod_mpn 
                              on nod_mpn.kmmp_nod_id = k_dpth4.kmmp_nod_id         
                           inner join lms_lrm.tl_sbc_lrn_nod_rcstn l_dpth4 
                              on l_dpth4.opt_txb_id = #{optTxbId}
                             and l_dpth4.lrmp_nod_id = nod_mpn.lrmp_nod_id
                           where k_dpth5.kmmp_nod_id = EQ.TPC_ID
                           limit 1) as lluNodId       
		        FROM LMS_LRM.EA_EV E
		        JOIN (
		        	   SELECT OT.OPT_TXB_ID, U.USR_ID, COUNT(1) OVER() USR_CNT
				       FROM LMS_LRM.CM_OPT_TXB OT
				       JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
				       WHERE OT.OPT_TXB_ID = #{optTxbId}
				       AND U.USR_TP_CD = 'ST' 
				       GROUP BY OT.OPT_TXB_ID, U.USR_ID
		        ) USR ON USR.OPT_TXB_ID = E.OPT_TXB_ID 
		        JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
		        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		        JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = USR.USR_ID
		        WHERE E.OPT_TXB_ID = #{optTxbId}
                AND E.EV_DTL_DV_CD NOT IN ('ST', 'UD')
        		AND E.DEL_YN ='N'
        		AND ER.EV_CMPL_YN = 'Y'
		        AND EQ.DEL_YN = 'N'
	        <if test='qpLluId != null and qpLluId neq ""'>
	            AND EQ.QP_LLU_ID = #{qpLluId}
	        </if>		        
		        AND EQA.CANS_YN ='N'
		        AND EQ.TPC_ID != 0
		        -- 년월 필터가 왜 필요했을까
		        -- AND CONCAT(YEAR(NOW()), LPAD(MONTH(NOW()), 2, '0')) >= DATE_FORMAT(ER.SMT_DTM,'%Y%m')
        		GROUP BY EQ.QTM_ID, EQ.QTM_DFFD_DV_CD, EQ.QP_LLU_ID, EQ.QP_LLU_NM, EQ.QP_TC_ID, EQ.QP_TC_NM, EQ.TPC_ID
        ) E
        LEFT JOIN LMS_CMS.QP_QTM_CN QC ON QC.QP_QTM_ID = E.qtmId

        /* 학급 분석 - 박원희 - EaClaAnTcr-Mapper.xml - selectWrongAnwBestList - 학급분석 > 오답 BEST 조회 */
    </select>


    <insert id="insertEaEv" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto"
            useGeneratedKeys="true" keyProperty="evId" keyColumn="ev_id">

        INSERT INTO LMS_LRM.EA_EV
        (
        OPT_TXB_ID, USR_ID, EV_DV_CD, EV_NM, TXM_PTME_SETM_YN, XPL_TM_SETM_YN, QST_CNT, FNL_QST_CNT, RTXM_PMSN_YN, USE_YN,
        DEL_YN, LCKN_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES
        (
        #{optTxbId},
        #{usrId},
        'TE',
        #{evNm},
        #{txmPtmeSetmYn},
        #{xplTmSetmYn},
        #{qstCnt},
        #{qstCnt}, -- FNL_QST_CNT
        #{rtxmPmsnYn},
        #{useYn},
        #{delYn},
        #{lcknYn},
        #{usrId},
        now(),
        #{usrId},
        now(),
        #{dbId}
        )


        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml -  EA_EV 테이블 등록 */
    </insert>

    <insert id="insertEaEvTsRnge" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto">

        INSERT INTO LMS_LRM.EA_EV_TS_RNGE
        (
        EV_ID, TS_RNGE_SEQ_NO, LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM,
        CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES
        (
        #{evId},
        #{tsRngeSeqNo},
        #{optTxbId},
        #{luLrmpNodId},
        (SELECT LRMP_NOD_NM  FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID =#{optTxbId} AND LRMP_NOD_ID = #{luLrmpNodId}),
        #{usrId},
        now(),
        #{usrId},
        now(),
        #{dbId}
        )


        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - EA_EV_TS_RNGE 평가 시험 범위 등록 */
    </insert>

    <insert id="insertEaEvDffdCstn" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto">

        INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN
        (
        EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID,
        CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        VALUES
        (
        #{evId},
        #{evDffdDvCd},-- 최상,상,중,하,최하 코드 값
        #{evDffdDsbCnt},
        #{usrId},
        now(),
        #{usrId},
        now(),
        #{dbId}
        )

        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - EA_EV_DFFD_CSTN 평가 난이도 등록 */
    </insert>

    <insert id="insertEaEvQtm" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto">
        INSERT INTO LMS_LRM.EA_EV_QTM
        (EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM, QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM,
        DEL_YN, DEL_DTM, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
        VALUES
        (
        #{evId},
        #{qtmId},
        #{qtmOrdn},
        #{qtmDffdDvCd},
        (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD ='QTM_PLTFM_DFFD_DV_CD' AND CM_CD = #{qtmDffdDvCd}),
        #{qpLluId},
        #{qpLluNm},
        #{qpTcId},
        #{qpTcNm},
        #{delYn},
        NOW(),
        #{usrId},
        NOW(),
        #{usrId},
        NOW(),
        #{dbId}
        )
        /* 학급 분석 - 김상민 - EaClaAnTcr-Mapper.xml - EA_EV_QTM 평가 문항 등록 */
    </insert>


    <select id="selectClaAnSimilarQpQtm" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto"
            resultType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto">
            
	        SELECT
	        		  ROW_NUMBER() OVER(ORDER BY RAND()) AS qtmOrdn
			        , QQ.QP_QTM_ID		AS qtmId		-- 문항 ID
			        , QQ.QP_DFFD_CD		AS qtmDffdDvCd	-- 문항 난이도 구분 코드
			        , QWC.QP_UNIF_CD_NM AS qpDffdNm     -- 문항 플랫폼 난이도 명
			        , QL.QP_LLU_ID 		AS qpLluId      -- 문항 플랫폼 대단원 ID
			        , QL.LLU_NM         AS qpLluNm      -- 문항 플랫폼 대단원 명
			        , QTL.QP_TPC_LU_ID	AS qpTcId		-- 토픽(차시) ID
			        , QTL.QP_TPC_LU_NM  AS qpTcNm		-- 토픽(차시) 명
	 		FROM (
					SELECT QM.RLT_QTM_TP_CD, QM.SRC_QTM_ID, QM.RLT_QTM_ID
					FROM LMS_CMS.BC_RLT_QTM_MPN QM
					WHERE QM.SRC_QTM_ID = #{qtmId}
					AND QM.RLT_QTM_TP_CD IN ('SI', 'TW')				
			) QM
	        JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID 		   = QM.RLT_QTM_ID
	        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
	        LEFT JOIN LMS_CMS.QP_LLU QL ON QL.QP_LLU_ID    = QQA.QP_LLU_ID
	        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID = QQA.QP_TPC_LU_ID
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC ON QWC.QP_LCL_CD = '02' AND QWC.QP_UNIF_CD = QQ.QP_DFFD_CD
	        WHERE QQ.DEL_YN ='N'
	        -- AND QQ.QP_DFFD_CD = #{qtmDffdDvCd} -- 난이도
	        -- AND QQ.QP_QST_TYP_CD IN('10','20','30','40','50','60','61','62')-- 10:자유 선지, 20~50:2~5지 선택, 60~62:단답 유순,무순,묶음형
	        LIMIT 1
	        
        /* 학급분석 - 박원희 - EaClaAnTcr-Mapper.xml - selectClaAnSimilarQpQtm -  오답 유사 문항 조회 */
    </select>


    <select id="selectLearDistChart" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto"
            resultType="hashMap">
        SELECT
            CU.USR_NM AS usrNm,
            SUM(TT.x) AS x,
            SUM(TT.y) AS y
        FROM (
            /*학생별 평가 성취도, 학습시간 */
            SELECT
                T.USR_ID,
                IFNULL(ROUND((COUNT(CASE WHEN T.CANS_YN='Y' THEN 1 END )/COUNT(T.CANS_YN))*100),0) AS x,
                SUM(T.EV_TM_SCNT) AS y
            FROM(
                SELECT
                    EER.USR_ID,
                    EEQA.CANS_YN,
                    EER.EV_TM_SCNT
                FROM LMS_LRM.EA_EV EE
                INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID IN (
                SELECT U.USR_ID
                FROM LMS_LRM.CM_OPT_TXB OT
                JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
                WHERE OT.OPT_TXB_ID = #{optTxbId}
                AND U.USR_TP_CD = 'ST'
                ))
                INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EE.EV_ID = EEQ.EV_ID AND EEQ.DEL_YN = 'N')
                INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID)
                WHERE EE.OPT_TXB_ID = #{optTxbId}
                AND EE.USE_YN      = 'Y'
                AND EE.DEL_YN      = 'N'
                AND EER.EV_CMPL_YN = 'Y'
                AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(EER.SMT_DTM),0)  <![CDATA[<=]]>  MONTH(NOW())
    
            ) T
    
            GROUP BY T.USR_ID
    
    
            UNION ALL
    
            /*학생별 교과 학습 학습 시간*/
            SELECT
                LRN_USR_ID   	   AS USR_ID,
                0				   AS x,
                SUM(LRN_TM_SCNT)   AS y
            FROM LMS_LRM.TL_SBC_LRN_ATV_ST
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID IN (
            SELECT U.USR_ID
            FROM LMS_LRM.CM_OPT_TXB OT
            JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
            WHERE OT.OPT_TXB_ID = #{optTxbId}
            AND U.USR_TP_CD = 'ST'
            )
            AND   LRN_ST_CD  = 'CL' -- 학습 완료
            AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(MDF_DTM),0) <![CDATA[<=]]> MONTH(NOW())
            GROUP BY LRN_USR_ID
    
            UNION ALL
    
            /*학생별 특별학습 학습시간*/
            SELECT
                LRN_USR_ID   	 AS USR_ID,
                0				 AS x,
                SUM(LRN_TM_SCNT) AS y
            FROM LMS_LRM.SL_SP_LRN_PGRS_ST
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID IN (
            SELECT U.USR_ID
            FROM LMS_LRM.CM_OPT_TXB OT
            JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
            WHERE OT.OPT_TXB_ID = #{optTxbId}
            AND U.USR_TP_CD = 'ST'
            )
            AND   LRN_ST_CD  = 'CL'-- 학습 완료
            AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(MDF_DTM),0) <![CDATA[<=]]> MONTH(NOW())
            GROUP BY LRN_USR_ID
        )TT
        INNER JOIN LMS_LRM.CM_USR CU ON CU.USR_ID = TT.USR_ID
        GROUP BY TT.USR_ID
        ORDER BY CU.STU_NO

        /* 학급분석 - 김상민 - EaClaAnTcr-Mapper.xml - selectLearDistChart -  우리반 학습 분포도 > 학생별 성취도,학습 시간 조회 */
    </select>

	<select id="selectLrnSumList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
		SELECT 
                R.CLA_NM            /* 반명 */
                ,R.OPT_TXB_ID
                ,R.RCSTN_ORDN            /* 재구성순서 */
                ,R.LRMP_NOD_NM            /* 대단원 명 */
                ,R.LRMP_NOD_ID            /* 대단원 ID */
                ,R.TL_CL_CNT            /* 교과 완료 카운트 */
                ,R.TOTAL_LLU_NOD_CNT                          AS TL_TOT_CNT              /* 교과 총 카운트 */
		FROM 
		(
		SELECT
			C.CLA_NM
                ,R.OPT_TXB_ID
                ,R.RCSTN_ORDN
                ,R.LRMP_NOD_NM               /* 대단원 명 */
                ,R.LRMP_NOD_ID            /* 대단원 ID */
				,SUM(IF(D.LRN_ST_CD = 'CL', 1, 0)) AS TL_CL_CNT /* 학습완료건수 */
				,(SELECT COUNT(*)
			     FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN T
			     WHERE T.OPT_TXB_ID = R.OPT_TXB_ID
			       AND T.LLU_NOD_ID = R.LRMP_NOD_ID
			       AND T.DPTH = 4  
			     ) AS TOTAL_LLU_NOD_CNT
				,C.CLA_NO
		FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  /* TL_교과학습노드재구성(대단원) */
        INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN T  /* TL_교과학습노드재구성(차시) */
               ON R.OPT_TXB_ID = T.OPT_TXB_ID
               AND R.LRMP_NOD_ID = T.LLU_NOD_ID
               AND T.DPTH = 4
               AND T.USE_YN = 'Y'
        INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
                ON R.OPT_TXB_ID = A.OPT_TXB_ID
               AND T.LRMP_NOD_ID = A.LRMP_NOD_ID
        INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
                ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
               AND A.LRN_STP_ID = B.LRN_STP_ID
               AND B.LRN_STP_DV_CD = 'CL' /* CL=개념, WB=익힘, EX=평가*/
               AND B.DEL_YN = 'N'
        INNER JOIN LMS_LRM.CM_OPT_TXB F  /* 운영교과서 - 학급아이디 */
              	ON F.OPT_TXB_ID = R.OPT_TXB_ID
        INNER JOIN LMS_LRM.CM_CLA C /* 반번호 정보 */
                ON C.CLA_ID = F.CLA_ID
        INNER JOIN LMS_LRM.CM_USR E /* 학생 유저 정보 */
              	ON E.CLA_ID = C.CLA_ID
               AND E.USR_TP_CD = 'ST'
        LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D/* TL_교과학습활동상태 */
               ON A.OPT_TXB_ID = D.OPT_TXB_ID
              AND B.LRMP_NOD_ID = D.LRMP_NOD_ID
              AND A.LRN_ATV_ID = D.LRN_ATV_ID
              AND E.USR_ID = D.LRN_USR_ID
        LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN R2
               ON R2.LRMP_NOD_ID = D.LRMP_NOD_ID
              AND R2.USE_YN = 'Y'
        WHERE R.OPT_TXB_ID = #{optTxbId}
	    AND R.USE_YN = 'Y'
		AND R.DPTH = 1
		AND R.LU_EPS_YN = 'Y'
		GROUP BY R.OPT_TXB_ID, B.LRMP_NOD_ID, C.CLA_NO , D.LRN_USR_ID
		) AS R
		GROUP BY R.LRMP_NOD_ID
		ORDER BY  R.CLA_NO ,R.RCSTN_ORDN

        /* 전체학급관리 정은혜 EaClaAnTcr-Mapper.xml - selectLrnSumList 교과 학급 반별 진도율 평균*/
	</select>
	
	<select id="selectSlSumList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
		SELECT 
		    A.SP_LRN_ID,        /* 추천학습 ID */
		    A.SP_LRN_NM,        /* 대단원명 */
		    A.CLA_NO,           /* 반번호 */
		    A.CLA_NM,           /* 반명 */
		    A.OPT_TXB_ID,       
		    COUNT(1) AS TOTAL_CNT,    									    /* 추천학습 총 카운트 */
		    SUM(CASE WHEN A.LRN_ST_CD = 'CL' THEN 1 ELSE 0 END) AS CL_CNT /* 완료한 카운트 */
		FROM (
		    SELECT
		        A.SP_LRN_ID,
		        C.SP_LRN_NOD_ID AS URNK_NOD_ID,
		        E1.SP_LRN_CTN_ID,
		        A.SP_LRN_NM,
		        F.CLA_NO,
		        F.CLA_NM,
		        A.OPT_TXB_ID,
		        G.LRN_ST_CD
		    FROM LMS_LRM.SL_SP_LRN_RCSTN A                     /* 특별학습 재구성 */
		    INNER JOIN LMS_LRM.SL_STU_RCM_LRN B
		        ON B.OPT_TXB_ID = A.OPT_TXB_ID
		        AND A.SP_LRN_ID = B.SP_LRN_ID
		        AND B.RCM_YN = 'Y'
		    INNER JOIN LMS_CMS.BC_SP_LRN_NOD C                     /* 특별학습 노드 - 차시 */
		        ON B.SP_LRN_ID = C.SP_LRN_ID
		        AND C.DPTH = 1
		        AND C.DEL_YN = 'N'
		    INNER JOIN LMS_CMS.BC_SP_LRN_NOD D                     /* 특별학습 노드 - 2DPTH */
		        ON C.SP_LRN_ID = D.SP_LRN_ID
		        AND C.SP_LRN_NOD_ID = D.URNK_SP_LRN_NOD_ID
		        AND D.LWS_YN = 'Y'
		        AND D.CSTN_CMPL_YN = 'Y'
		        AND D.DEL_YN = 'N'
		    INNER JOIN LMS_CMS.BC_SP_LRN_CTN E1                     /* 특별학습 콘텐츠 */
		        ON D.SP_LRN_NOD_ID = E1.SP_LRN_NOD_ID
		        AND E1.DEL_YN = 'N'
		    INNER JOIN LMS_LRM.CM_CLA F                     /* 학급 */
		        ON F.CLA_ID = #{optTxbId}
		    INNER JOIN LMS_LRM.CM_USR H                     /* 사용자 */
		        ON H.CLA_ID = F.CLA_ID
		        AND H.USR_TP_CD = 'ST'
		    LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST G                     /* 특별학습 진행상태 */
		        ON G.OPT_TXB_ID = A.OPT_TXB_ID
		        AND A.SP_LRN_ID = G.SP_LRN_ID
		        AND G.SP_LRN_CTN_ID = E1.SP_LRN_CTN_ID
		        AND H.USR_ID = G.LRN_USR_ID
		    WHERE A.OPT_TXB_ID = #{optTxbId}
		      AND A.USE_YN = 'Y'
		    GROUP BY A.SP_LRN_ID, C.SP_LRN_NOD_ID, E1.SP_LRN_CTN_ID, F.CLA_NO,  A.OPT_TXB_ID, G.LRN_ST_CD
			) AS A
			GROUP BY A.SP_LRN_ID, A.CLA_NO, A.OPT_TXB_ID

        /* 전체학급관리 정은혜 EaClaAnTcr-Mapper.xml - selectSlSumList 추천학습 반별 진도율 평균*/
	</select>
	
	<select id="selectAlSumList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
			SELECT 
		    Z.CLA_NO,          /* 반번호 */
		    Z.MLU_KMMP_NOD_ID,/* 단원 ID */
		    Z.LLU_KMMP_NOD_NM,
		    COUNT(Z.USR_ID) AS USER_CNT, /* 유저의 총 수 */
		    SUM(Z.LU_CNT) AS TOTAL_LU_CNT, /* 전체 단원 수 */
		    SUM(CASE WHEN Z.LUEV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) AS TOTAL_LU_CMPL_CNT /* 전체 완료 수 */
		FROM 
		(
		    SELECT 
		        EER.USR_ID,
		        F.CLA_NO,          /* 반번호 */
		        (SELECT COUNT(*) 
		         FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR 
		         WHERE DPTH = 2 
		           AND OPT_TXB_ID = '248-mae34p110' 
		           AND DEL_YN = 'N') AS LU_CNT, /* 총 단원 수 */
		        EAETR.LUEV_CMPL_YN, 
		        EAETR.MLU_KMMP_NOD_ID,
		        EAETR.LLU_KMMP_NOD_NM
		    FROM LMS_LRM.EA_EV EE
            INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		    INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
		        ON EE.EV_ID = EAETR.EV_ID 
		        AND EAETR.AI_TS_RNGE_SEQ_NO = 1
		    INNER JOIN LMS_LRM.CM_CLA F 
		        ON F.CLA_ID = #{claId}
		    INNER JOIN LMS_LRM.CM_USR H 
		        ON F.CLA_ID = H.CLA_ID
		    WHERE EE.OPT_TXB_ID = #{optTxbId}
		      AND EE.EV_DV_CD = 'AE'
		      and EER.USR_ID = H.USR_ID
		    GROUP BY EER.USR_ID, F.CLA_NO, EAETR.MLU_KMMP_NOD_ID, EAETR.LUEV_CMPL_YN
		) Z
		GROUP BY Z.CLA_NO, Z.MLU_KMMP_NOD_ID
		ORDER BY Z.CLA_NO, Z.MLU_KMMP_NOD_ID;

        /* 전체학급관리 정은혜 EaClaAnTcr-Mapper.xml - selectAlSumList AI 반별 진도율 평균 (수정예정)*/
	</select>
	
	<select id="selectEaCansSumList" parameterType="com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto" resultType="hashMap">
		/* 전체학급관리 강성현 EaClaAnTcr-Mapper.xml selectEaCansSumList 학급 성취도, 학습수준 */
		SELECT
		    U.CLA_ID, /* 학급 ID */
		    C.CLA_NM, /* 반 이름 */
		    COUNT(U.USR_ID) as STU_CNT, /* 총학생수 */
			round(avg(ev.ud_cans_rt), 1) as UD_CANS_RT, /* 단원진단 성취도 비율 */
			round(avg(ev.ug_cans_rt), 1) as UG_CANS_RT, /* AI맞춤진단 성취도 비율 */
			round(avg(ev.ov_cans_rt), 1) as OV_CANS_RT, /* 단원평가진단 성취도 비율 */
			SUM(CASE WHEN ALLV.LRNR_VEL_TP_CD = 'SL' then 1 else 0 end) as LRNR_VEL_TP_CD_SL_CNT, /* 느린 학습자 수준 분포 */
			SUM(CASE WHEN ALLV.LRNR_VEL_TP_CD = 'NM' then 1 else 0 end) as LRNR_VEL_TP_CD_NM_CNT, /* 보통 학습자 수준 분포 */
			SUM(CASE WHEN ALLV.LRNR_VEL_TP_CD = 'FS' then 1 else 0 end) as LRNR_VEL_TP_CD_FS_CNT /* 빠른 학습자 수준 분포 */
		FROM LMS_LRM.CM_USR U          
		inner JOIN LMS_LRM.CM_CLA C     
		ON U.CLA_ID = C.CLA_ID    
		AND U.USR_TP_CD = 'ST'
		AND (C.SCHL_GRD_CD = 'M' OR C.SCHL_GRD_CD = 'H')
		INNER JOIN LMS_LRM.AI_LRNR_LV as ALLV on U.USR_ID = ALLV.USR_ID
		and ALLV.DEL_YN = 'N'
		inner join (
            select
                a.OPT_TXB_ID
                 ,node.ev_id
                 ,node.llu_KMMP_NOD_ID
                 ,node.mlu_KMMP_NOD_ID
                 ,node.SLU_KMMP_NOD_ID
                 ,node.TC_KMMP_NOD_ID
                 ,node.TpC_KMMP_NOD_ID
                 ,e.USR_ID
                 , case when a.ev_dtl_dv_cd = 'UD' then count(c.qtm_id) else 0 end ud_fnl_qst_cnt
                 , case when a.ev_dtl_dv_cd = 'UG' then count(c.qtm_id) else 0 end ug_fnl_qst_cnt
                 , case when a.ev_dtl_dv_cd = 'OV' then count(c.qtm_id) else 0 end ov_fnl_qst_cnt
                 , case when a.ev_dtl_dv_cd = 'UD' then sum(case when d.cans_yn = 'Y' then 1 else 0 end) else 0 end ud_cans_cnt
                 , case when a.ev_dtl_dv_cd = 'UG' then sum(case when d.cans_yn = 'Y' then 1 else 0 end) else 0 end ug_cans_cnt
                 , case when a.ev_dtl_dv_cd = 'OV' then sum(case when d.cans_yn = 'Y' then 1 else 0 end) else 0 end ov_cans_cnt
                 , case when a.ev_dtl_dv_cd = 'UD' then round(sum(case when d.cans_yn = 'Y' then 1 else 0 end)/count(c.qtm_id) * 100, 1) else 0 end as ud_cans_rt
                 , case when a.ev_dtl_dv_cd = 'UG' then round(sum(case when d.cans_yn = 'Y' then 1 else 0 end)/count(c.qtm_id) * 100, 1) else 0 end as ug_cans_rt
                 , case when a.ev_dtl_dv_cd = 'OV' then round(sum(case when d.cans_yn = 'Y' then 1 else 0 end)/count(c.qtm_id) * 100, 1) else 0 end as ov_cans_rt
                 ,a.ev_dtl_dv_cd
                 ,d.cans_yn
                 ,c.qtm_id
            from lms_lrm.ea_ev a
                     inner join lms_lrm.ea_ev_qtm c
                                on a.ev_id = c.ev_id and a.usr_id = c.CRTR_ID
                     inner join lms_lrm.ea_ev_qtm_anw d
                                on a.ev_id = d.ev_id
                                    and c.qtm_id = d.qtm_id
                                    and a.usr_id = d.usr_id
                     inner join lms_lrm.ea_ev_rs e
                                on a.ev_id = e.ev_id
                                    and d.usr_id = e.usr_id
                                    and a.usr_id = e.usr_id
                                    and a.opt_txb_id = '242-enh12j207'
                                    and a.EV_DTL_DV_CD in ('UD', 'UG', 'OV')
                                    and e.ev_cmpl_yn = 'Y'
                     inner join (
                select b.crtr_id, b.ev_id, blknmd.llu_KMMP_NOD_ID
                     ,blknmd.mlu_KMMP_NOD_ID
                     ,blknmd.SLU_KMMP_NOD_ID
                     ,blknmd.TC_KMMP_NOD_ID
                     ,blknmd.TpC_KMMP_NOD_ID
                from lms_lrm.ea_ev_ts_rnge b
                         inner join lms_cms.bc_lrmp_kmmp_nod_mpn_dtl blknmd
                                    on b.lu_lrmp_nod_id = blknmd.LLU_LRMP_NOD_ID
                         INNER JOIN LMS_LRM.AI_LRNR_LV as ALLV on b.CRTR_ID = ALLV.USR_ID
                    and ALLV.DEL_YN = 'N'
                    and allv.LU_KMMP_NOD_ID = blknmd.tc_KMMP_NOD_ID
                    and blknmd.llu_KMMP_NOD_ID = 23852
                group by b.ev_id
            ) node on node.ev_id = a.ev_id and node.crtr_id = e.usr_id
            group by e.usr_id
		) ev on u.USR_ID = ev.USR_ID and ALLV.OPT_TXB_ID = ev.OPT_TXB_ID and allv.LU_KMMP_NOD_ID = ev.tc_KMMP_NOD_ID
		group by u.CLA_ID;
	
	
	</select>
	
</mapper>