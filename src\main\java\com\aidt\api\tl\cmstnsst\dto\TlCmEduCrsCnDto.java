package com.aidt.api.tl.cmstnsst.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-19 13:18:20
 * @modify date 2024-06-19 13:18:20
 * @desc [TlCmEduCrsCnDto 교육과정표준체계ID dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmEduCrsCnDto {
     /** 학습활동ID */
     @Parameter(name="학습활동ID")
     private String lrnAtvId;
 
    /** 교육과정내용코드 */
    @Parameter(name="교육과정내용코드")
    private String eduCrsCnCd;
}
