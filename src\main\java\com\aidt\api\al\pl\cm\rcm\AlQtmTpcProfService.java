package com.aidt.api.al.pl.cm.rcm;

import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.aidt.api.error.ApiErrorLogException;
import com.aidt.api.error.ErrorCode;
import com.aidt.api.util.WebClientUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.al.pl.dto.AlRcmLuEvCmplDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.util.ConstantsExt;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Slf4j
@Service
public class AlQtmTpcProfService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.stu.alPlQtmTpcProf.";
	
	@Autowired private CommonDao commonDao;
	@Autowired private AiRcmTsshQtmCommService aiRcmTsshQtmCommService;
	@Autowired private AlMyhmPointService alMyhmPointService;
	
	@Autowired private JwtProvider jwtProvider;
	@Autowired private WebFluxUtil webFluxUtil;
	@Autowired private WebClientUtil webClientUtil;

	@Autowired
	private AiRcmTsshQtmCommService commService;
	
	@Value("${aidt.endpoint.aicenter.url}")
	private String endpoint_kntApp;
	
	@Value("${aidt.endpoint.aicenter.token}")
	private String authorization_kntApp;
	
	@Value("${spring.config.activate.on-profile}")
	private String profileName;
	
	//교과서정보
	public AlPlQtmTpcProfDto txbInfo(AlPlQtmTpcProfDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectBcTxbInfo", dto);
	}
	
	//평가완료여부
	public AiRcmTsshQtmDto selectEvCmplInfo(AlPlQtmTpcProfDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectEvCmplInfo", dto);
	}
	
	//완료된 평가정보
	public List<AiRcmTsshQtmDto> selectEvQtmTpcProf(AlPlQtmTpcProfDto dto) {
		List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmTpcProf", dto);
		
		//문항중복방어
		Set<String> seenNames = new HashSet<>();
		
		list.stream()
        .filter(AiRcmTsshQtmDto -> seenNames.add(dto.getQtmId()))
        .collect(Collectors.toList());
		
		return list;
	}
	
	
	//단원 내 문항리스트
	public List<AiRcmTsshQtmDto> selectMluQtmList(AlPlQtmTpcProfDto dto) {
		List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectMluQtmList", dto);
		
		//문항중복방어
		Set<String> seenNames = new HashSet<>();
		
		list.stream()
        .filter(AiRcmTsshQtmDto -> seenNames.add(dto.getQtmId()))
        .collect(Collectors.toList());
		
		return list;
	}
	
	
	
//	/**
//     * AI 맞춤 사용자별 문항 프로파일 데이터 적재 호출
//     * 
//     * @param AlPlQtmTpcProfDto
//     * @return Map<String, Object>
//     */
//	@Transactional
//	public int selectUsrlyQtmProf(AlPlQtmTpcProfDto dto, HttpServletRequest request) {
////		AlPlQtmTpcProfDto txbInfo = commonDao.select(MAPPER_NAMESPACE + "selectBcTxbInfo", dto);
////		dto.setTxbId(txbInfo.getTxbId());
////		dto.setAutrCd(txbInfo.getAutrCd());
////		dto.setAutrNm(txbInfo.getAutrNm());
////		dto.setSbjCd(txbInfo.getSbjCd());
////		dto.setSchlGrdCd(txbInfo.getSchlGrdCd());
////		dto.setSgyCd(txbInfo.getSgyCd() > 0 ? txbInfo.getSgyCd() : 1);
//		
//		log.debug(dto.toString());
//		int cnt = 0;
////		List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmTpcProf", dto);
////		if(ObjectUtils.isEmpty(list)){
////			throw new IllegalArgumentException("평가정보가 없습니다.");
////		}
////		if(list.get(0).getEvCmplYn().equals("N")) {
////			throw new IllegalArgumentException("완료되지 않은 평가입니다.");
////		}
//		
//		//마이홈 포인트 적립
//		//if(!profileName.equals("local")){
////			dto.setCtnTpCd(AlConstUtil.AL_MYHM_CTN_EV);
////			String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
////			String pointCode = alMyhmPointService.getPointCode(dto.getSbjCd(), dto.getSchlGrdCd(), list.get(0).getTcKmmpNodNm(), list.get(0).getEvDtlDvCd(), dto.getCtnTpCd());
////			if(pointCode.equals("")) {
////				throw new IllegalArgumentException("AI 마이홈 포인트코드 없음");
////			}else{
////				log.debug(">> pointCode:{}",pointCode);
////				try {
////					Map<String, Object> apiResult = alMyhmPointService.callMyhmApi(accessToken, Map.of(
////					      "pntCd", pointCode,
////					      "pntChkBsVl", dto.getEvId()));
////					log.debug(">> apiResult:{}",apiResult);
////				} catch (JsonProcessingException e) {
////					log.debug("AI 마이홈 포인트 적립실패");
////					//throw new IllegalArgumentException("AI 마이홈 포인트 적립실패");
////				}
////			}
//		//}
//		 
//		
//		//AI Center API호출 및 문항,토픽 예측값 Update
////		dto.setLrnrVelTpCd(list.get(0).getLrnrVelTpCd());
////		postAiCenterPredict(list, dto);
//		
//		// 학습자수준 판단
////		AiRcmTsshQtmDto reqDto = new AiRcmTsshQtmDto();
////		reqDto.setOptTxbId(dto.getOptTxbId());
////		reqDto.setUsrId(dto.getUsrId());
////		reqDto.setEvDtlDvCd(list.get(0).getEvDtlDvCd()); 
////		reqDto.setMluKmmpNodId(list.get(0).getMluKmmpNodId());
////		reqDto.setSbjCd(dto.getSbjCd());
////		reqDto.setSchlGrdCd(dto.getSchlGrdCd());
////		log.debug(reqDto.toString());
////		aiRcmTsshQtmCommService.updateLrnrVelTpCd(reqDto);
//				
//		//토픽숙련도 판단 :: 수학은 AI평가만 저장, 그외과목은 모두저장
////		if((AlConstUtil.SBJ_MA.contains(dto.getSbjCd()) && list.get(0).getEvDvCd().equals("AE"))
////				|| (!AlConstUtil.SBJ_MA.contains(dto.getSbjCd()))){
////			aiRcmTsshQtmCommService.setTpcAvn(dto.getEvId(), dto.getUsrId());
////		}
//		
//		//사용자별 토픽 학습순서 저장
////		aiRcmTsshQtmCommService.updateAiUsrlyTpcLrnOrdn(
////				dto.getUsrId()
////				, list.get(0).getOptTxbId()
////				, dto.getSbjCd()
////				, list.get(0).getEvDtlDvCd()
////				, list.get(0).getMluKmmpNodId());
//		
//		
//		//AI_문항별풀이통계 저장
////		aiRcmTsshQtmCommService.insertAiQtmlyXplStas(list);
//		
//		if(true) {
//			throw new IllegalArgumentException("테스트 강제Throw");
//		}
//		return cnt;
//	}
	
	
	/**
	 * 마이홈 포인트
	 * */
	public void myhmCallService(AlPlQtmTpcProfDto dto, HttpServletRequest request) {
		//마이홈 포인트 적립
		dto.setCtnTpCd(AlConstUtil.AL_MYHM_CTN_EV);
		String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
		String pointCode = alMyhmPointService.getPointCode(dto.getSbjCd(), dto.getSchlGrdCd(), dto.getTcKmmpNodNm(), dto.getEvDtlDvCd(), dto.getCtnTpCd());
		if(pointCode.equals("")) {
			throw new IllegalArgumentException("AI 마이홈 포인트코드 없음");
		}else{
			log.debug(">> pointCode:{}",pointCode);
			try {
				Map<String, Object> apiResult = alMyhmPointService.callMyhmApi(accessToken, Map.of(
				      "pntCd", pointCode,
				      "pntChkBsVl", dto.getEvId()));
				log.debug(">> apiResult:{}",apiResult);
			} catch (Exception e) {
				throw new ApiErrorLogException(ErrorCode.MY_HOME, e, ErrorCode.TYPE_INTERNAL);
			}
		}
	}
	
	
	/**
     * AI center 예측점수 API 호출하여 문항리스트 Merge
     * 
     * @param List<AiRcmTsshQtmDto>
     * @param AiRcmTsshQtmReqDto
     * @return List<AiRcmTsshQtmDto>
     */
	@SuppressWarnings("unchecked")
	@Transactional
	public List<AiRcmTsshQtmDto> postAiCenterPredict(List<AiRcmTsshQtmDto> list, AlPlQtmTpcProfDto dto) {
		if(authorization_kntApp == null || authorization_kntApp.equals("")) {
			authorization_kntApp = "devkey1";
		}
		if(endpoint_kntApp == null || endpoint_kntApp.equals("")) {
			endpoint_kntApp = "https://knt-dev.aitextbook.co.kr/api/v1/predict/";
		}
		
		//과목명
		String course = "";
		if(AlConstUtil.SBJ_EN.contains(dto.getSbjCd())) {
			course = "en";
		}else if(AlConstUtil.SBJ_MA.contains(dto.getSbjCd())) {
			course = "ma";
		}
		//공통학년의경우 1set
		dto.setSgyCd(dto.getSgyCd().toString().equals("0") ? 1 : dto.getSgyCd());
		//학년
		String grade = dto.getSchlGrdCd().toLowerCase() + dto.getSgyCd().toString();
		//저자코드
		String author = dto.getAutrCd().toLowerCase();
		
		String pointUrl = endpoint_kntApp + course +"/" + grade + "/" + author;
		log.debug("END POINT :: " + pointUrl);
		
		ObjectMapper objMapper = new ObjectMapper();
		ObjectNode rootNode = objMapper.createObjectNode();
		ArrayNode dataArray = objMapper.createArrayNode();
		
		for (AiRcmTsshQtmDto item : list) {
		    ObjectNode dataNode = objMapper.createObjectNode();
		    dataNode.put("usr_id", dto.getUsrId());
		    dataNode.put("qtm_id", item.getQtmId().toString());
		    dataNode.put("qp_dffd_cd", item.getCtnDffdDvCd());	//문항난이도
		    dataNode.put("lrnr_vel_tp_cd", item.getLrnrVelTpCd() == null ? "NM" : item.getLrnrVelTpCd());	//학습자수준(영어는차시별, 수학은단원별)
			dataNode.put("sbc_lrn_avg_ans_rt", 0); //해당 차시 평균 정답률
		    dataNode.put("pre_tc_ans_rt", 0); 	  //이전 차시 평균 정답률
		    dataArray.add(dataNode);
		}
		rootNode.set("data", dataArray);

		HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        httpHeaders.add("accept", "application/json");
        httpHeaders.add("authorization", authorization_kntApp);

		log.debug("AI Center 요청 전 데이터 체크 : {}", rootNode);
        
		try {
			String jsonString = objMapper.writeValueAsString(rootNode);
//			String aiCenterRes = webFluxUtil.post(pointUrl, httpHeaders, jsonString, String.class);
			String aiCenterRes = webClientUtil.postWithError(pointUrl, httpHeaders, jsonString, String.class);

			Map<String, Object> resMap = CoreUtil.Json.jsonString2Map(aiCenterRes);
			List<Map<String, Object>> aiCenterResList = (List<Map<String, Object>>)resMap.get("result");
			boolean status = (boolean)resMap.get("status");
			
			if(!status) {
				throw new ApiErrorLogException("");
			}
			
			if(!ObjectUtils.isEmpty(aiCenterResList)){
				// 성능 이슈로 인해 for 루프로 수정
				Map<Integer, Map<String, Object>> resultMap = new HashMap<>();
				for (Map<String, Object> res : aiCenterResList) {
					Integer qtmId = Integer.parseInt(res.get("qtm_id").toString());
					resultMap.put(qtmId, res);
				}

				// list 업데이트
				for (AiRcmTsshQtmDto aiRcmTsshQtmDto : list) {
					Map<String, Object> result = resultMap.get(aiRcmTsshQtmDto.getQtmId());
					if (result != null) {
						aiRcmTsshQtmDto.setAiPredCansRt(Double.parseDouble(result.get("infer").toString()));
						aiRcmTsshQtmDto.setKmmpNodId(aiRcmTsshQtmDto.getTpcKmmpNodId());
						aiRcmTsshQtmDto.setUsrId(dto.getUsrId());
						//aiRcmTsshQtmDto.setLrnrVelTpCd(dto.getLrnrVelTpCd());
					}
				}
				//사용자별 문항 프로파일 테이블에 예측정답률 데이터 update		
				commonDao.update(MAPPER_NAMESPACE + "insertAiUsrlyQtmProf", list);
				
				List<AiRcmTsshQtmDto> deduplicatedList = new ArrayList<>(list.stream()
		                .collect(Collectors.toMap(
		                		AiRcmTsshQtmDto::getKmmpNodId, // 중복 제거 기준 키
		                        item -> item,   // 값 그대로 유지
		                        (existing, replacement) -> existing // 중복 시 기존 값 유지
		                ))
		                .values());
				
				commonDao.update(MAPPER_NAMESPACE + "insertUsrlyTpcProf", deduplicatedList);
			}
		} catch (WebClientResponseException e) {
			String status = e.getStatusCode().toString();
			String body = e.getResponseBodyAsString();
			String message = "status=" + status + ", body=" + body;

			throw new ApiErrorLogException(ErrorCode.AI_CENTER, message, ErrorCode.TYPE_EXTERNAL);
		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.AI_CENTER, e, ErrorCode.TYPE_EXTERNAL);
		}
		return list;
	}

	public int selectKmmpNodCount(AiRcmTsshQtmDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodCount", dto);
	}
	
	/**
	 * 평가완료시 사용자별 문항,토픽 프로파일 데이터 적재
	 * @param dto
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@Transactional
	public Map<String, Object> selectUsrlyQtmProf(@Valid AlPlQtmTpcProfDto dto, HttpServletRequest request) {
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		List<String> successList = new ArrayList<String>();
		List<String> failList = new ArrayList<String>();

		log.debug(dto.toString());
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		dto.setUsrId(securityUserDetailDto.getUsrId());
		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		dto.setTxbId(securityUserDetailDto.getTxbId());

		//교과정보 조회
		AlPlQtmTpcProfDto txbInfo = this.txbInfo(dto);
		dto.setTxbId(txbInfo.getTxbId());
		dto.setAutrCd(txbInfo.getAutrCd());
		dto.setAutrNm(txbInfo.getAutrNm());
		dto.setSbjCd(txbInfo.getSbjCd());
		dto.setSchlGrdCd(txbInfo.getSchlGrdCd());
		dto.setSgyCd(txbInfo.getSgyCd() > 0 ? txbInfo.getSgyCd() : 1);

		//평가완료체크
		AiRcmTsshQtmDto evInfo = this.selectEvCmplInfo(dto);
		if(null == evInfo){
			throw new ApiErrorLogException("존재하지 않는 평가ID");
		}
		if(null == evInfo.getEvCmplYn() || !evInfo.getEvCmplYn().equals("Y")) {
			throw new ApiErrorLogException("완료되지 않은 평가입니다.");
		}

		/* 운영교과서에 맞는 지식맵 노드 id가 파라미터 값으로 넘어왔는지 검증(2024.10.30) */
		int nodeCount = this.selectKmmpNodCount(evInfo);
		if (nodeCount < 1) {
			throw new ApiErrorLogException("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
		}

		//1.단원완료여부 분리
		try {
			AlRcmLuEvCmplDto ev = new AlRcmLuEvCmplDto();
			ev.setUsrId(dto.getUsrId());
			ev.setEvId(Integer.parseInt(dto.getEvId()));
			ev.setSbjCd(dto.getSbjCd());
			ev.setSchlGrdCd(dto.getSchlGrdCd());
			ev.setOptTxbId(dto.getOptTxbId());
			ev.setTpcCmplYn(dto.getTpcCmplYn());
			resultMap = commService.updateLuEvCmplYn(ev);

			if (resultMap.get("errorMessage") != null) {
				throw new ApiErrorLogException("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
			}

			successList.add("1.updateLuEvCmplYn");
		} catch (NumberFormatException e) {
			failList.add("1.updateLuEvCmplYn");
			throw new ApiErrorLogException(ErrorCode.LU_EV_CMP_YN, e, ErrorCode.TYPE_INTERNAL);
		}

		//2.학습자수준 판단
		try {
			AiRcmTsshQtmDto reqDto = new AiRcmTsshQtmDto();
			reqDto.setOptTxbId(dto.getOptTxbId());
			reqDto.setUsrId(dto.getUsrId());
			reqDto.setEvDtlDvCd(evInfo.getEvDtlDvCd());
			reqDto.setMluKmmpNodId(evInfo.getMluKmmpNodId());
			reqDto.setSbjCd(dto.getSbjCd());
			reqDto.setSchlGrdCd(dto.getSchlGrdCd());
			log.debug(reqDto.toString());
			commService.updateLrnrVelTpCd(reqDto);
			successList.add("2.updateLrnrVelTpCd");
		} catch (Exception e) {
			failList.add("2.updateLrnrVelTpCd");
			throw new ApiErrorLogException(ErrorCode.LRNR_LEVEL_TP_CD, e, ErrorCode.TYPE_INTERNAL);
		}

		//3-1.AI Center API호출 및 문항,토픽 예측값 Update - 진단평가일 경우 단원 내 모든 문항 update
		if(evInfo.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
			dto.setMluKmmpNodId(evInfo.getMluKmmpNodId());
			List<AiRcmTsshQtmDto> selectMluQtmList = this.selectMluQtmList(dto);
			try {
				this.postAiCenterPredict(selectMluQtmList, dto);
				successList.add("3-1.postAiCenterPredict");
			} catch (Exception e) {
				failList.add("3-1.postAiCenterPredict");
				throw new ApiErrorLogException(ErrorCode.AI_CENTER, e, ErrorCode.TYPE_EXTERNAL);
			}
		}
		//단원 내 문항에 평가지 문항이 포함되어있지 않는경우가 있으므로 평가문항리스트는 항상 호출한다.
		List<AiRcmTsshQtmDto> evList = this.selectEvQtmTpcProf(dto);
		//3-2.AI Center API호출 및 문항,토픽 예측값 Update - 방금 푼 평가에 대해 update
		try {
			if(evList.get(0).getEvDvCd().equals("DE")) {
				successList.add("3-2.postAiCenterPredict - DE");
			}else {
				dto.setLrnrVelTpCd(evList.get(0).getLrnrVelTpCd());
				this.postAiCenterPredict(evList, dto);
				successList.add("3-2.postAiCenterPredict");
			}
		} catch (Exception e) {
			failList.add("3-2.postAiCenterPredict");
			throw new ApiErrorLogException(ErrorCode.AI_CENTER, e, ErrorCode.TYPE_EXTERNAL);
		}

		//4.토픽숙련도 판단
		try {
			commService.setTpcAvn(dto.getSbjCd(), dto.getUsrId(), dto.getEvId(), evInfo.getMluKmmpNodId(), dto.getOptTxbId(), evInfo.getEvDtlDvCd());
			successList.add("4.setTpcAvn");
		} catch (Exception e) {
			failList.add("4.setTpcAvn");
			throw new ApiErrorLogException(ErrorCode.TPC_AVN, e, ErrorCode.TYPE_INTERNAL);
		}

		//5.사용자별 토픽 학습순서 저장
		try {
			commService.updateAiUsrlyTpcLrnOrdn(
					evList
					, dto.getUsrId()
					, evInfo.getOptTxbId()
					, dto.getSbjCd()
					, evInfo.getEvDtlDvCd()
					, evInfo.getMluKmmpNodId());
			successList.add("5.updateAiUsrlyTpcLrnOrdn");
		} catch (Exception e) {
			failList.add("5.updateAiUsrlyTpcLrnOrdn");
			throw new ApiErrorLogException(ErrorCode.AI_USRLY_TPC_LRN_ORDN, e, ErrorCode.TYPE_INTERNAL);
		}

		//6.AI_문항별풀이통계 저장
		try {
			if(evInfo.getEvDvCd().equals("DE")) {
				successList.add("6.insertAiQtmlyXplStas - DE");
			}else {
				commService.insertAiQtmlyXplStas(evList);
				successList.add("6.insertAiQtmlyXplStas");
			}
		} catch (Exception e) {
			failList.add("6.insertAiQtmlyXplStas");
			throw new ApiErrorLogException(ErrorCode.AI_QTMLY_XPL_STAS, e, ErrorCode.TYPE_INTERNAL);
		}

		//7.마이홈
		try {
			dto.setTcKmmpNodNm(evList.get(0).getTcKmmpNodNm());
			dto.setEvDtlDvCd(evList.get(0).getEvDtlDvCd());
			this.myhmCallService(dto, request);
			successList.add("7.myhmCallService");
		} catch (Exception e) {
			failList.add("7.myhmCallService");
			throw new ApiErrorLogException(ErrorCode.MY_HOME, e, ErrorCode.TYPE_INTERNAL);
		}

		//8.과제완료처리 및 알림API
		try {
			dto.setMluKmmpNodId(evInfo.getMluKmmpNodId());
			commService.updateAsnCmpl(dto);
			successList.add("8.updateAsnCmpl");
		} catch (Exception e) {
			failList.add("8.updateAsnCmpl");
			throw new ApiErrorLogException(ErrorCode.ASN_CMPL, e, ErrorCode.TYPE_INTERNAL);
		}

		//9.학습진도율
		try {
			commService.updateLrnPrgsProf(dto);
			successList.add("9.updateLrnPrgsProf");
		} catch (Exception e) {
			failList.add("9.updateLrnPrgsProf");
			throw new ApiErrorLogException(ErrorCode.LRN_PRGS_PROF, e, ErrorCode.TYPE_INTERNAL);
		}

		resultMap.put("successList", successList);
		resultMap.put("failList", failList);
		
		return resultMap;
	}
    
}
