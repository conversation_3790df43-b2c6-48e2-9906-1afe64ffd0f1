package com.aidt.api.bc.tnte.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:59:47
 * @modify 2024-01-05 17:59:47
 * @desc 공지사항 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcAnnxApiCallDto {
	@Parameter(name="순번")
	private String filePath;
	@Parameter(name="순번2")
	private Long annxId;


}
