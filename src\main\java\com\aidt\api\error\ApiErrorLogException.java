package com.aidt.api.error;

import lombok.Getter;

@Getter
public class ApiErrorLogException extends RuntimeException {
	private static final long serialVersionUID = 20250616L;

	private final ErrorCode errorCode;
	private final long startTime;
	private final String errSrcCd;

	public ApiErrorLogException(String msg) {
		this(msg, ErrorCode.TYPE_INTERNAL);
	}

	public ApiErrorLogException(String msg, String errSrcCd) {
		super(msg);
		this.errorCode = ErrorCode.DEFAULT;
		this.startTime = System.currentTimeMillis();
		this.errSrcCd = errSrcCd;
	}

	public ApiErrorLogException(ErrorCode code, String msg, String errSrcCd) {
		super(msg);
		this.errorCode = code;
		this.startTime = System.currentTimeMillis();
		this.errSrcCd = errSrcCd;
	}

	public ApiErrorLogException(ErrorCode errorCode) {
		this(errorCode, ErrorCode.TYPE_INTERNAL);
	}

	public ApiErrorLogException(ErrorCode errorCode, String errSrcCd) {
		super(errorCode.getMessage());
		this.errorCode = errorCode;
		this.startTime = System.currentTimeMillis();
		this.errSrcCd = errSrcCd;
	}

	public ApiErrorLogException(ErrorCode errorCode, Throwable cause) {
		this(errorCode, cause, ErrorCode.TYPE_INTERNAL);
	}

	public ApiErrorLogException(ErrorCode errorCode, Throwable cause, String errSrcCd) {
		super(errorCode.getMessage(), cause);
		this.errorCode = errorCode;
		this.startTime = System.currentTimeMillis();
		this.errSrcCd = errSrcCd;
	}

}
