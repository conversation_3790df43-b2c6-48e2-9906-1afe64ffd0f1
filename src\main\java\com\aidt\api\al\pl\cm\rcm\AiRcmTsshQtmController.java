package com.aidt.api.al.pl.cm.rcm;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.aidt.api.al.pl.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI 맞춤 문항추천
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/cm/rcmQtm")
@Tag(name="[al] AI맞춤 문항추천", description="AI맞춤 문항추천")
public class AiRcmTsshQtmController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private AiRcmTsshQtmService aiRcmTsshQtmService;
	
	@Autowired
	private AiRcmTsshQtmCommService commService;
	
	@Autowired
	private AiRcmTsshQtmMaService maService;
	
	@Autowired
	private AiRcmTsshQtmEnService enService;
	
	/**
	 * AI 맞춤 문항추천
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 맞춤 문항추천", description="AI 맞춤 문항추천")
    @PostMapping(value = "/selectAiRcmTsshQtmList")
    public ResponseDto<AiRcmTsshQtmResponseDto> selectAiRcmTsshQtmList(@Valid @RequestBody AiRcmTsshQtmReqDto dto, HttpServletRequest req) {
		log.debug(dto.toString());
		AiRcmTsshQtmResponseDto returnDto = new AiRcmTsshQtmResponseDto();
		
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        if(dto.getEvNm() == null) {
        	dto.setEvNm("AI 맞춤 학습");
        }
        
        dto.setTxbId(securityUserDetailDto.getTxbId());
        
        //3.8v에서는 주석해제
        //수학 맞춤학습1,2는 3.8v 로직분리
		if(AlConstUtil.SBJ_MA.contains(dto.getSbjCd()) && dto.getEvDtlDvCd() != null && (dto.getEvDtlDvCd().equals("C1") || dto.getEvDtlDvCd().equals("C2"))){
        	return Response.ok(maService.selectAiMaRcmQtm(dto));
        }
        
		//단원 내 같은평가 중복요청 체크
		if(dto.getEvDvCd().equals("AE") && dto.getEvDtlDvCd() != null) {
			int aeEvCheck = commService.selectAeEvCheck(dto);
			if(aeEvCheck > 0) {
				returnDto.setErrorStatus("error");
				returnDto.setErrorMessage("이미 생성된 평가입니다.");
				return Response.ok(returnDto);
//				return Response.fail("이미 생성된 평가입니다.");
			}
		}
		
        //학습맵중단원노드ID를 지식맵중단원노드ID로 치환
        if(dto.getLrmpKmmpDvCd() != null && dto.getLrmpKmmpDvCd().equals("LRMP")) {
  			if(dto.getMluLrmpNodId() == null) {
  				returnDto.setErrorStatus("error");
				returnDto.setErrorMessage("Invalid 'mluLrmpNodId' in Request Parameter");
				return Response.ok(returnDto);
//  				throw new IllegalArgumentException("Invalid 'mluLrmpNodId' in Request Parameter");
  			}
  			String mluKmmpNodId = commService.convertLrmpIdToKmmpId(dto.getOptTxbId(), dto.getMluLrmpNodId());
  			if(mluKmmpNodId == null) {
  				returnDto.setErrorStatus("error");
				returnDto.setErrorMessage("매핑되는 지식맵이 없습니다.");
				return Response.ok(returnDto);
//  				throw new IllegalArgumentException("매핑되는 지식맵이 없습니다.");
  			}
  			dto.setMluKmmpNodId(mluKmmpNodId);
  		}
      	
        //DIY
        if(AlConstUtil.SBJ_EN.contains(dto.getSbjCd()) && dto.getEvDvCd().equals("DE")) {
        	returnDto.setErrorStatus("error");
			returnDto.setErrorMessage("DIY 미제공 과목입니다.");
			return Response.ok(returnDto);
//        	return Response.fail("DIY 미제공 과목입니다.");
        }
        //초등영어만 로직분리
        if(AlConstUtil.SBJ_EN.contains(dto.getSbjCd()) && dto.getSchlGrdCd().equals("E")) {//초등영어
        	return Response.ok(enService.getEschEnQtmList(dto));
        }else {//나머지
        	return Response.ok(aiRcmTsshQtmService.selectAiRcmTsshQtmList(dto));
        }
    }
	
	
	/**
	 * AI맞춤 수학 개별문항 추천 - 수학 2024.07.03 변경로직(3.8v)
	 * 
	 * */
	@Tag(name="[al] AI맞춤 수학 개별문항 추천", description="AI맞춤 수학 개별문항 추천")
    @PostMapping(value = "/selectAiMaRcmQtm")
    public ResponseDto<AiRcmTsshQtmResponseDto> selectAiMaRcmQtm(@Valid @RequestBody AiRcmTsshQtmReqDto req) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(req.getOptTxbId() == null) {
			req.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(req.getUsrId() == null) {
        	req.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectAiMaRcmQtm(req));
	}
	
	
	
	/**
	 * 단원 내 오답문항 풀기 - DIY
	 * 맞춤학습1,2,선택학습의 오답문항을 DIY평가로 출제
	 * 
	 * @return 
	 * */
	@Tag(name="[al] 수학 단원 내 오답문항 풀기", description="맞춤학습1,2,선택학습의 오답문항을 DIY평가로 출제")
    @PostMapping(value = "/selectIansQtmTxm")
    public ResponseDto<AiRcmTsshQtmResponseDto> selectIansQtmTxm(@Valid @RequestBody AiRcmTsshQtmReqDto dto) {
		log.debug(dto.toString());
		
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	return Response.ok(maService.selectIansQtmTxm(dto));
    }
	
    
    /**
	 * AI 추천시험지/문항 이력 저장
	 * 
	 * @return 
	 * */
	@Tag(name="[al] 풀이내역 저장", description="풀이내역 저장")
    @PostMapping(value = "/updateAiRcmTsshQtmHist")
    public void updateAiRcmTsshQtmHist(@Valid @RequestBody AiRcmTsshQtmResponseDto dto) {
		log.debug(dto.toString());
		
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
        dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        aiRcmTsshQtmService.updateAiRcmTsshQtmHist(dto);
    }

	//@RequestMapping("/at/token/updateAiLrnPgrsProf")
//	@Scheduled(fixedRate = 30000)
//	public void updateAiLrnPgrsProf() {
//		List<AlPlQtmTpcProfDto> targetList = commService.selectEvTarget();
//		for (AlPlQtmTpcProfDto info : targetList) commService.updateLrnPrgsProf(info);
//	}
	
	
}
