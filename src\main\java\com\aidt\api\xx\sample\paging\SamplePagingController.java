package com.aidt.api.xx.sample.paging;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.xx.sample.paging.dto.SamplePagingRequestDto;
import com.aidt.common.Paging.PagingResponseDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2024-01-14 20:46:27
 * @modify date 2024-01-14 20:46:27
 * @desc [ Paging 샘플 컨트롤]
 */
@Slf4j
@Tag(name="[xx] Sample.Paging" , description="Sample Paging")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/sample/paging")
public class SamplePagingController {
    private final SamplePagingService pagingService;

    @Operation(summary="Paging", description = "Paging Test")
    @GetMapping("test01")
    public ResponseDto<PagingResponseDto> pagingTest01(SamplePagingRequestDto requestDto) {
        log.debug("Entrance selectList");
        log.debug(requestDto.toString());
        return Response.ok(pagingService.selectList(requestDto));
    }

/* result Json   	
Response body
Download
{
  "timestamp": "2024-01-14T23:25:23.1159787",
  "code": 200,
  "status": "OK",
  "data": {
    "pageNo": 3,        // 페이지 번호
    "pageSize": 30,     // 페이지에 뿌려지 Item 갯수
    "totalCnt": 1061,   // 해당 조건의 데이터 전체 갯수
    "startPageNo": 1,   // 페이지 시작 번호
    "endPageNo": 36,    // 페이지 마지막 번호
    "dataSize": 30,     // 조회 된 데이터 갯수 
    "data": [   // 데이터 
      {
        "totalCnt": 1061,
        "idx": 1177,
        "usrId": "USR0085",
        "usrNm": "Emma",
        "cntry": "Ireland",
        "phone": "LG G3",
        "color": "Violet",
        "price": "918900",
        "quan": "12",
        "dt": "2023-12-13",
        "cdt": "2023-12-13",
        "udt": null
      },
    ]
}
 */
}