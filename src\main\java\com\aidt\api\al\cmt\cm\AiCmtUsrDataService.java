package com.aidt.api.al.cmt.cm;

import com.aidt.api.al.cmt.dto.AiCmtUsrDataDto;
import com.aidt.api.al.cmt.dto.req.AiCmtUsrDataReqDto;
import com.aidt.common.CommonDao;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-27 14:46:33
 * @modify date 2024-05-27 14:46:33
 * @desc
 */
@Service
public class AiCmtUsrDataService {

    private final String MAPPER_NAMESPACE = "api.al.cmt.cm.";

    private final CommonDao commonDao;

    public AiCmtUsrDataService(CommonDao commonDao) {
        this.commonDao = commonDao;
    }

    public List<AiCmtUsrDataDto> selectAiCmtUsrData(AiCmtUsrDataReqDto reqDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAiCmtUsrData", reqDto);
    }

    public int insertAiCmtUsrData(List<AiCmtUsrDataReqDto> dtos) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertAiCmtUsrData", dtos);
    }

    public boolean selectIsSaved(Map<String, String> map) {
        return commonDao.select(MAPPER_NAMESPACE + "selectIsSaved", map);
    }

    public void deleteAiCmtUsrData(Map<String, String> map) {
        commonDao.delete(MAPPER_NAMESPACE + "deleteAiCmtUsrData", map);
    }

}
