package com.aidt.api.bc.focLrn.tcr;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.focLrn.dto.BcFlDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name="[bc] 선생님 집중 학습 관련 정보", description="선생님 집중 학습 관련 정보")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/tcr/focLrn")
public class BcFlTcrController {

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private BcFlTcrService bcFlTcrService;
    
    /** DB-ID */
    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;
    
    @Operation(summary="집중 학습 정보 저장", description="선생님이 공유한 화면 정보를 저장한다.")
    @PostMapping(value = "/saveFocusLrnInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> saveFocLrnData(@Valid @RequestBody BcFlDto saveDto){
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setDbId(DB_ID);
        saveDto.setUsrId(userDetails.getUsrId());
        return Response.ok(bcFlTcrService.saveFocLrnInfo(saveDto));
	}
    
    @Operation(summary="집중 학습 정보", description="선생님이 공유한 화면 정보를 가져온다.")
    @PostMapping(value = "/selectFocLrnInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BcFlDto selectFocLrnInfo() {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        BcFlDto schDto = new BcFlDto();
        schDto.setOptTxbId(userDetails.getOptTxbId());
        return bcFlTcrService.selectFocLrnInfo(schDto);
    }
}
