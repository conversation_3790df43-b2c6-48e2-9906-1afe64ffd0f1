package com.aidt.api.bc.cm;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aidt.api.al.pl.dto.AlPlStuDto;
import com.aidt.api.al.pl.stu.AlPlIniDatStuService;
import com.aidt.api.at.token.dto.KerisLrnDataUpsertDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdCopyFleDto;
import com.aidt.api.bc.clablbd.tcr.BcClaBlbdTcrService;
import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.api.bc.cm.dto.BcAnnxFleS3Dto;
import com.aidt.api.bc.cm.dto.BcAnnxFleSaveDto;
import com.aidt.api.bc.cm.dto.BcClaListDto;
import com.aidt.api.bc.cm.dto.BcCmCdDto;
import com.aidt.api.bc.cm.dto.BcCmLrnApoHstDto;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.api.bc.cm.dto.BcFncUseSetmDto;
import com.aidt.api.bc.cm.dto.BcLoginDto;
import com.aidt.api.bc.cm.dto.BcS3Dto;
import com.aidt.api.bc.cm.dto.BcSlppStuListDto;
import com.aidt.api.bc.cm.dto.BcUserInfoDto;
import com.aidt.api.bc.cm.dto.BcUsrInfoDto;
import com.aidt.api.bc.cm.dto.MenuDto;
import com.aidt.api.bc.inf.dto.BcInfDto;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.ea.asn.tcr.EaAsnTcrService;
import com.aidt.api.ea.asn.tcr.dto.EaAnsCopyFleDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.ev.tcr.EaEvTcrService;
import com.aidt.api.ea.evcom.EaEvComService;
import com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto;
import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.api.sl.inidat.stu.SlIniDatStuService;
import com.aidt.api.sl.inidat.tcr.SlIniDatTcrService;
import com.aidt.api.tl.inidat.dto.TlIniDatCondDto;
import com.aidt.api.tl.inidat.tcr.TlIniDatTcrService;
import com.aidt.base.exception.CustomException;
import com.aidt.base.exception.ExceptionMessage;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AccessControlList;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.GroupGrantee;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.Permission;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import com.aidt.api.bc.cm.dto.BcCmSelectNoteFileReqDto;
import com.aidt.api.bc.cm.dto.BcCmSelectNoteFileResDto;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:47
 * @modify 2024-01-05 17:52:47
 * @desc 공통 Controller
 */

@Slf4j
@Tag(name = "[bc] 공통[BcCm]", description = "공통")
@RestController
@RequestMapping("/api/v1/bc/cm/common")
public class BcCmController {

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Autowired
	private BcCmService bcCmService;

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	EaEvComService eaEvComService;

	@Autowired
	private EaAsnTcrService eaAsnTcrService;

	@Autowired
	private EaEvTcrService eaEvTcrService;

	@Autowired
	private AlPlIniDatStuService alPlIniDatStuService;

	@Autowired
	private SlIniDatTcrService slIniDatTcrService; //교사용 서비스

	@Autowired
	private TlIniDatTcrService tlIniDatTcrService; //교사용 서비스

	@Autowired
	private SlIniDatStuService slIniDatStuService;

	@Autowired
	private BcClaBlbdTcrService bcClaBlbdTcrService;

	// 2024-07-10 학생목록 조회(KERIS API로 사용자명 가공)
	@GetMapping(value = "/selectStuInfoList")
	public List<BcUserInfoDto> selectCommonStuList(BcUserInfoDto bcUserInfoDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcUserInfoDto.setClaId(userDetails.getClaId());
		bcUserInfoDto.setOptTxbId(userDetails.getOptTxbId());
		return bcCmService.selectStuInfoList(bcUserInfoDto);
	}

	// 2024-07-10 학급목록 조회(KERIS API로 학급목록 가공)
	@GetMapping(value = "/selectClaInfoList")
	public List<BcClaListDto> selectClaInfoList(BcClaListDto bcClaListDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaListDto.setUsrId(userDetails.getUsrId());
		bcClaListDto.setOptTxbId(userDetails.getOptTxbId());
		return bcCmService.selectClaInfoList(bcClaListDto);
	}

	// 2024-07-01 표준체계ID별 데이터 저장(전입 / 전출에도 사용)
	@PostMapping(value = "/upsertNtlvEduCrsStnData")
	public void upsertNtlvEduCrsStnData(@RequestBody
	KerisLrnDataUpsertDto kerisLrnDataUpsertDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		kerisLrnDataUpsertDto.setUsrId(userDetails.getUsrId());
		kerisLrnDataUpsertDto.setDbId(DB_ID);
		/*
		 * 2025.01.20 불필요 소스 추후 url 삭제 예정
		 */
		// bcCmService.upsertNtlvEduCrsStnData(kerisLrnDataUpsertDto);
	}

	// 배포 확인용
	@PostMapping(value = "/pushCheck")
	public int pushcheck(@RequestBody
	BcUsrInfoDto bcUsrInfoDto) {
		return 99999;
	}

	// 알림 표시 여부 조회
	@GetMapping(value = "/checkInfm")
	public int checkInfm(BcUsrInfoDto bcUsrInfoDto) {
		return bcCmService.checkInfm(bcUsrInfoDto);
	}

	// 2024-06-20 교사가 로그인시 초기 데이터 구성(USR_ID, LECTURE_CODE로 판별)
	@PostMapping(value = "/checkLectureUserInfo")
	public int checkLectureTcrUserInfo(@RequestBody
	BcUsrInfoDto bcUsrInfoDto) {

		log.debug("#####################");
		log.debug("#####################");
		log.debug("####### checkLectureUserInfo #########");
		log.debug("#####################");
		log.debug("#####################");
		log.debug("#####################");

		return bcCmService.checkLectureTcrUserInfo(bcUsrInfoDto);
	}

	// 메뉴 이동시마다 실시간 모니터링에 해당 메뉴 update
	@Operation(summary = "메뉴이동시 실시간 모니터링 update", description = "메뉴이동시 실시간 모니터링 update")
	@PutMapping(value = "/updateStuCurLrnStMenu")
	public void updateStuCurLrnStMenu(@RequestBody
	BcMntrDto bcMntrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcMntrDto.setMdfrId(userDetails.getUsrId());
		bcMntrDto.setLrnUsrId(userDetails.getUsrId());
		bcCmService.updateStuCurLrnStMenu(bcMntrDto);
	}

	// 교과서학기코드로 교과서ID 조회
	@GetMapping(value = "/getTxbIdForTxbCd/{txbTermCd}")
	public String txbIdForTxbCd(@PathVariable("txbTermCd")
	String txbTermCd) {
		return bcCmService.getTxbIdForTxbCd(txbTermCd);
	}

	// 2024-06-10 기능 사용 설정 초기데이터 생성(KERIS를 통한 로그인시 교사정보 등록 후 로직이 들어가있음, 임시 버전임)
	@PostMapping(value = "/checkFncUseSetm")
	public void checkFncUseSetm(@RequestBody
	BcUsrInfoDto bcUsrInfoDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcUsrInfoDto.setUsrId(userDetails.getUsrId());
		bcUsrInfoDto.setMdfrId(userDetails.getUsrId());
		bcUsrInfoDto.setCrtrId(userDetails.getUsrId());
		bcUsrInfoDto.setDbId(DB_ID);
		bcUsrInfoDto.setOptTxbId(userDetails.getOptTxbId());
		bcCmService.checkFncUseSetm(bcUsrInfoDto);
	}

	// 대화 학생 목록 정보 저장
	@PostMapping(value = "/checkSlppStuList")
	public void checkSlppStuList(@RequestBody
	List<BcSlppStuListDto> bcSlppStuListDto) {
		bcCmService.checkSlppStuList(bcSlppStuListDto);
	}

	/**
	 * Header 메뉴 조회
	 *
	 * @param
	 * @return ResponseDto<List<MenuDto>>
	 */
	@Tag(name = "[bc] 메뉴 조회", description = "메뉴 조회")
	@GetMapping(value = "/selectMenu/{urnkMenuId}")
	public ResponseDto<List<MenuDto>> selectMenu(@PathVariable("urnkMenuId")
	String urnkMenuId, @RequestParam(defaultValue = "")
	String studentMode) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		String usrTpCd = userDetails.getUsrTpCd();
		String menuTpCd = "";
		if ("TE".equals(usrTpCd)) {
			menuTpCd = "03";
		} else if ("ST".equals(usrTpCd)) {
			menuTpCd = "02";
		}

		if ("true".equals(studentMode)) {
			menuTpCd = "02";
		}

		MenuDto menuDto = new MenuDto();
		menuDto.setMenuUsrTpCd(menuTpCd);
		if ("08000".equals(urnkMenuId)) {
			menuDto.setUrnkMenuId(urnkMenuId);
		}

		// 교과서ID = DB_ID (교과서에 따른 메뉴명 분기)
		//menuDto.setTxbId(userDetails.getTxbId());

		List<MenuDto> listMenuDb = bcCmService.selectMenu(menuDto);

		List<MenuDto> listMenu = new ArrayList<MenuDto>();

		String engTxbIds = "241,242,244,245,246,251,256,257,259,261,262,266"; //  영어 
		String diyEvls = "62,82"; //diy 메뉴 
		for (MenuDto dto : listMenuDb) {
			if (diyEvls.indexOf(dto.getMenuId()) == -1 || engTxbIds.indexOf(userDetails.getTxbId()) == -1) {
				listMenu.add(dto);
			}
		}
		return Response.ok(listMenu);
	}

	/**
	 * 첨부파일 목록 조회
	 *
	 * @param annxId
	 * @return ResponseDto<ObjectListing>
	 */
	@Tag(name = "[bc] 첨부파일 목록 조회", description = "첨부파일 목록 조회")
	@GetMapping(value = "/selectFileList/{annxId}")
	public ResponseDto<List<BcAnnxFleDto>> selectFileList(@PathVariable("annxId")
	String annxId) {
		return Response.ok(bcCmService.selectFileList(annxId));
	}

	/**
	 * 로그인 정보
	 *
	 * @param String
	 * @return ResponseDto<UserDto>
	 */
	@Operation(summary = "로그인 정보", description = "로그인 정보 조회")
	@GetMapping(value = "/user/{usrId}")
	public ResponseDto<BcUsrInfoDto> selectUser(@PathVariable("usrId")
	String usrId) {

		log.debug("##################");
		log.debug("##################");
		log.debug("########사용자 정보 조회 시작222222222222222222222222");
		log.debug("##################");
		log.debug("##################");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		BcLoginDto loginInfo = new BcLoginDto();
		loginInfo.setUsrId(userDetails.getUsrId());
		loginInfo.setOptTxbId(userDetails.getOptTxbId());
		loginInfo.setClaId(userDetails.getClaId());
		loginInfo.setSubDomain(DB_ID);

		String usrTpCd = userDetails.getUsrTpCd();
		BcUsrInfoDto userInfo = new BcUsrInfoDto();
		userInfo.setServerCheck(SERVER_ACTIVE);

		if ("TE".equals(usrTpCd)) {
			userInfo = bcCmService.selectTcrUser(loginInfo);
		} else {
			userInfo = bcCmService.selectStuUser(loginInfo);
		}

		return Response.ok(userInfo);
	}

	// 알림 등록(공통)
	@PostMapping(value = "/insertCmInfm")
	public void insertCmInfm(@RequestBody
	BcInfDto bcInfDto) {
		// 각 코드별 알림 등록 구분
		bcCmService.insertCmInfm(bcInfDto);
	}

	/**
	 * File Upload
	 *
	 * @param bucketName
	 * @param path
	 * @param files
	 * @return ResponseDto<List<FileDto>>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Tag(name = "[bc] S3 File Upload Multi", description = "Ncloud S3 File 여러개 등록")
	@PostMapping(value = "/fileUpload", consumes = {MediaType.APPLICATION_JSON_VALUE,
		MediaType.MULTIPART_FORM_DATA_VALUE})
	public ResponseDto<List<BcAnnxFleDto>> fileUploadMulti(List<MultipartFile> files, String taskName, String annxId, String selectNoteFileID, String Gubun)
		throws IllegalStateException, IOException {
		// validation
		Optional.ofNullable(taskName).orElseThrow(() -> new NoSuchElementException("Bucket name is empty"));
		Optional.ofNullable(files).orElseThrow(() -> new FileNotFoundException("File Not Found!"));

		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		// upload local file
		try {
			List<BcS3Dto.FileUploadResponse> fileUploadResponseList = new ArrayList<>();
			ObjectMetadata metadata = null;
			for (MultipartFile file : files) {
				// File Meta
				metadata = new ObjectMetadata();
				metadata.setContentLength(file.getSize());

				metadata.setContentType(file.getContentType());

				int lastDotIndex = file.getOriginalFilename().lastIndexOf(".");
				String extension = file.getOriginalFilename().substring(lastDotIndex + 1);
				// file Object Name + 확장자 추가 처리 
				String newFileName = UUID.randomUUID().toString() + "." + extension.trim().toLowerCase();

				// bucketName 조합
				String fnBucketName = BUCKET_NAME + "/" + DB_ID + "/lms/" + taskName;
				s3.putObject(fnBucketName, newFileName, file.getInputStream(), metadata);

				log.info("Object {} has been created.", file.getOriginalFilename());
				fileUploadResponseList.add(BcS3Dto.FileUploadResponse.builder()
					.originFileName(file.getOriginalFilename())
					.newFileName(newFileName)
					.contentType(file.getContentType())
					.fileSize(file.getSize())
					.build());

				// 업로드된 파일 read 권한 부여
				AccessControlList accessControlList = s3.getObjectAcl(fnBucketName, newFileName);
				accessControlList.grantPermission(GroupGrantee.AllUsers, Permission.Read);
				s3.setObjectAcl(fnBucketName, newFileName, accessControlList);

			};

			// disconnect
			s3.shutdown();
			
			if(Gubun != null && Gubun.equals("Note")) //평가-노트일 경우
				return Response.ok(bcCmService.saveS3UploadAfUseNote(fileUploadResponseList, annxId, taskName, selectNoteFileID));
			else 
				return Response.ok(bcCmService.saveS3UploadAf(fileUploadResponseList, annxId, taskName));

		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	/**
	 * Delete File
	 *
	 * @param bucketName
	 * @param newFileName
	 * @return ResponseDto<ObjectListing>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Tag(name = "[bc] S3 File Delete", description = "Ncloud S3 File 삭제")
	@PostMapping(value = "/deleteFile")
	public ResponseDto<ObjectListing> deleteFile(@RequestBody
	BcAnnxFleS3Dto bcAnnxFleS3Dto)
		throws IllegalStateException, IOException {
		Optional.ofNullable(bcAnnxFleS3Dto.getTaskName())
			.orElseThrow(() -> new NoSuchElementException("Bucket name is empty"));
		Optional.ofNullable(bcAnnxFleS3Dto.getFileName())
			.orElseThrow(() -> new NoSuchElementException("New file name is empty"));

		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
		try {

			// bucketName 조합
			String fnBucketName = BUCKET_NAME + "/" + DB_ID + "/lms/" + bcAnnxFleS3Dto.getTaskName();

			// request delete file
			s3.deleteObject(fnBucketName, bcAnnxFleS3Dto.getFileName());

			// disconnect
			s3.shutdown();

			// s3삭제 후 첨부테이블 삭제
			int rsltCnt = bcCmService.deleteS3Af(bcAnnxFleS3Dto.getAnnxFleId());
			if (rsltCnt > 0) {
				log.info("Object {} has been deleted.\n", bcAnnxFleS3Dto.getFileName());
			} else {
				log.info("Object {} has been deleted.\n", bcAnnxFleS3Dto.getFileName());
			}

			return Response.ok();
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	@Tag(name = "[bc] S3 File Upload Multi & 삭제 (과제, 학급게시판용)", description = "Ncloud S3 File 여러개 등록 및 삭제")
	@PostMapping(value = "/fileSave", consumes = {MediaType.APPLICATION_JSON_VALUE,
		MediaType.MULTIPART_FORM_DATA_VALUE})
	public ResponseDto<Object> fileUploadDeleteMultiSave(@RequestPart(value = "files", required = false)
	List<MultipartFile> files, @RequestPart("dto")
	BcAnnxFleSaveDto dto)
		throws IllegalStateException, IOException {

		AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		if (dto.getAnnxId() != null && dto.getAnnxId() > 0 && dto.getTaskId() != null) {
			/*
			 * annxId 값 존재 시 다른학급저장이 실행된 된 과제 또는 학급게시판 중 동일 annxId가 존재 시
			 * 해당 annxId 기준 물리 및 Db 복사 후 신규 annxId를 return 한다.
			 */
			boolean copy = false;

			// 과제
			if ("ASN".equals(dto.getActDvCd())) {
				copy = eaAsnTcrService.selectOtherAsnCopyAnnxFleYn(
					EaAnsCopyFleDto.builder().asnId(dto.getTaskId()).annxId(dto.getAnnxId()).build());
			}
			// 학급게시판
			else if ("CBB".equals(dto.getActDvCd())) {
				copy = bcClaBlbdTcrService.selectOtherClaBlbdCopyAnnxFleYn(
					BcClaBlbdCopyFleDto.builder().claBlbdId(dto.getTaskId()).annxId(dto.getAnnxId()).build());
			} else {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,
					"actDvCd = " + dto.getActDvCd() + " 지원하지 않는 코드 입니다.");

			}

			if (copy) {
				// 새로운 annxId 발급 및 삭제건 반영하여 등록 처리
				dto.setAnnxId(bcCmService.saveS3CopyAnnxFle(s3, dto.getAnnxId(), dto.getDeleteAnnxFleId()));
			}
			// 다른 학급 복사된 건이 아닌 경우 해당 annxFleId 기준 삭제 처리 진행
			else {
				bcCmService.deleteS3AnnxFle(s3, dto.getDeleteAnnxFleId());
			}
		}

		Long annxId = null;

		if (files != null) {
			ObjectMetadata metadata = null;
			String ext = null;
			int extIdx = -1;
			String annxFleNm = null;

			List<BcAnnxFleDto> fileList = new ArrayList<BcAnnxFleDto>();
			BcAnnxFleDto fleDto = null;

			String annxFlePthNm = DB_ID + "/lms/" + dto.getTaskName();

			for (MultipartFile file : files) {
				ext = null;

				metadata = new ObjectMetadata();
				metadata.setContentLength(file.getSize());
				metadata.setContentType(file.getContentType());

				extIdx = file.getOriginalFilename().lastIndexOf(".");
				if (extIdx > -1) {
					ext = file.getOriginalFilename().substring(extIdx, file.getOriginalFilename().length())
						.toLowerCase();
				}

				annxFleNm = UUID.randomUUID().toString();

				if (StringUtil.isNotBlank(ext)) {
					annxFleNm += ext;
				}

				try {
					s3.putObject(BUCKET_NAME + "/" + annxFlePthNm, annxFleNm, file.getInputStream(), metadata);

					fleDto = new BcAnnxFleDto();
					fleDto.setAnnxFleNm(annxFleNm);
					fleDto.setAnnxFleOrglNm(file.getOriginalFilename());
					fleDto.setAnnxFleFextNm(file.getContentType());
					fleDto.setAnnxFleSze(file.getSize());
					fleDto.setAnnxFlePthNm(annxFlePthNm + "/" + annxFleNm);
					fleDto.setUseYn("Y");

					if (bcCmService.getSupportedExtensions().contains(ext.replaceAll("\\.", ""))) {
						try {
							fleDto.setDocViId(
								bcCmService.callStreamDocsIdApi(
									BcCmUtil.makeFleCdnUrl(BUCKET_NAME, fleDto.getAnnxFlePthNm())));
						} catch (JsonProcessingException jpe) {
							log.error("docViId request JsonProcessingException error... >>> {}",
								jpe.getMessage());
						} catch (CustomException ce) {
							log.error("docViId request CustomException error... >>> {} ", ce.getMessage());
						}
					}

					fileList.add(fleDto);

				} catch (AmazonS3Exception ase) {
					log.error("file upload AmazonS3Exception error... >>> {}", ase.getMessage());
				} catch (SdkClientException sce) {
					log.error("file upload SdkClientException error... >>> {}", sce.getMessage());
				}
			}

			annxId = bcCmService.saveS3UploadAfAsnCbb(dto.getAnnxId(), fileList);
		} else {
			annxId = dto.getAnnxId();
		}

		s3.shutdown();

		return Response.ok(annxId);
	}

	/**
	 * Download File
	 *
	 * @param bucketName
	 * @param newFileName
	 * @return ResponseDto<ObjectListing>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Tag(name = "[bc] S3 File Download", description = "Ncloud S3 File 다운로드")
	@GetMapping(value = "/fileDown")
	public ResponseEntity<?> downloadFile(BcAnnxFleS3Dto bcAnnxFleS3Dto)
		throws IllegalStateException, IOException {

		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
		try {

			// bucketName 조합
			String fnBucketName = BUCKET_NAME + "/" + DB_ID + "/lms/" + bcAnnxFleS3Dto.getTaskName();

			// get File
			S3Object s3Object = s3.getObject(fnBucketName, bcAnnxFleS3Dto.getFileName());

			// get inputStream
			S3ObjectInputStream s3ObjectInputStream = s3Object.getObjectContent();

			// convert to byte array
			byte[] bytes = IOUtils.toByteArray(s3ObjectInputStream);

			// file name urlencoding
			String fileName = "";
			String fnAnnxFleOrglNm = bcAnnxFleS3Dto.getFnAnnxFleOrglNm();
			if (StringUtils.isNotBlank(fnAnnxFleOrglNm)) {
				fileName = URLEncoder.encode(fnAnnxFleOrglNm, "UTF-8").replaceAll("\\+", "%20");
			} else {
				fileName = URLEncoder.encode(bcAnnxFleS3Dto.getOriginFileName(), "UTF-8").replaceAll("\\+", "%20");
			}

			// Set Header
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			httpHeaders.setContentLength(bytes.length);
			httpHeaders.setContentDispositionFormData("attachment", fileName);

			// inputStream.close();
			s3ObjectInputStream.close();

			// disconnect
			s3.shutdown();

			return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
		} catch (AmazonS3Exception e) {
			return ResponseEntity.internalServerError().build();
		} catch (SdkClientException e) {
			return ResponseEntity.internalServerError().build();
		}
	}

	/**
	 * LCMS Download File
	 *
	 * @param bucketName
	 * @param newFileName
	 * @return ResponseDto<ObjectListing>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Tag(name = "[bc] LCMS S3 File Download", description = "LCMS Ncloud S3 File 다운로드")
	@GetMapping(value = "/lcmsFileDown")
	public ResponseEntity<?> lcmsDownloadFile(BcAnnxFleS3Dto bcAnnxFleS3Dto)
		throws IllegalStateException, IOException {

		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
		try {

			// bucketName 조합
			// String fnBucketName = BUCKET_NAME+"/"+DB_ID+"/lcms/"+bcAnnxFleS3Dto.getTaskName();
			//String fnBucketName = BUCKET_NAME+bcAnnxFleS3Dto.getTaskName();

			String fnBucketName = BUCKET_NAME + bcAnnxFleS3Dto.getTaskName();

			// get File
			S3Object s3Object = s3.getObject(fnBucketName, bcAnnxFleS3Dto.getFileName());

			// get inputStream
			S3ObjectInputStream s3ObjectInputStream = s3Object.getObjectContent();

			// convert to byte array
			byte[] bytes = IOUtils.toByteArray(s3ObjectInputStream);

			// file name urlencoding
			String fileName = "";
			String fnAnnxFleOrglNm = bcAnnxFleS3Dto.getFnAnnxFleOrglNm();
			if (StringUtils.isNotBlank(fnAnnxFleOrglNm)) {
				fileName = URLEncoder.encode(fnAnnxFleOrglNm, "UTF-8").replaceAll("\\+", "%20");
			} else {
				fileName = URLEncoder.encode(bcAnnxFleS3Dto.getOriginFileName(), "UTF-8").replaceAll("\\+", "%20");
			}

			// Set Header
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			httpHeaders.setContentLength(bytes.length);
			httpHeaders.setContentDispositionFormData("attachment", fileName);

			// inputStream.close();
			s3ObjectInputStream.close();

			// disconnect
			s3.shutdown();

			return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
		} catch (AmazonS3Exception e) {
			return ResponseEntity.internalServerError().build();
		} catch (SdkClientException e) {
			return ResponseEntity.internalServerError().build();
		}
	}

	/**
	 * 사용자 정보 전달
	 *
	 * @param BcTnteDto
	 * @return ResponseList<BcTnteUserInfoDto>
	 */
	@Operation(summary = "사용자 정보", description = "사용자 정보")
	@GetMapping("/user-info")
	public ResponseDto<BcUserInfoDto> userInfo() {
		return Response.ok(this.bcCmService.getUserInfo());
	}

	/**
	 * 공통코드 조회
	 *
	 * @param BcCmCdDto
	 * @return ResponseList<Map<String,String>>
	 */
	@Operation(summary = "공통코드 조회", description = "공통코드 조회")
	@PostMapping("/selectCmCdList")
	public ResponseDto<List<Map<String, String>>> cmCdList(@Valid
	@RequestBody
	BcCmCdDto bcCmCdDto) {
		log.debug("Entrance selectComCodList");
		return Response.ok(bcCmService.cmCdList(bcCmCdDto));
	}

	/**
	 * 원클릭 학습 설정 조회
	 *
	 * @param BcFncUseSetmDto
	 * @return ResponseList<BcFncUseSetmDto>
	 */
	@Tag(name = "[bc] 원클릭 학습 설정 조회", description = "원클릭 학습 설정 조회")
	@GetMapping(value = "/selectFncUseSetmInfo")
	public ResponseDto<List<BcFncUseSetmDto>> selectFncUseSetmInfo(BcFncUseSetmDto bcFncUseSetmDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcFncUseSetmDto.setOptTxbId(userDetails.getOptTxbId());
		log.debug("Entrance selectFncUseSetmInfo");
		return Response.ok(bcCmService.selectFncUseSetmInfo(bcFncUseSetmDto));
	}

	/**
	 *  학습시간 업데이트 
	 * @param bcCmLrnTmDto
	 */
	@Tag(name = "[bc] 학습시간 update", description = "학습시간 update")
	@PostMapping(value = "/updateCmLrnTm")
	public void updateCmLrnTm(@RequestBody
	BcCmLrnTmDto bcCmLrnTmDto, HttpServletRequest request) {

		String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
		bcCmService.updateCmLrnTm(bcCmLrnTmDto, accessToken);
	}

	/**
	 * 사용자 상태 코드 
	 * @param bcUserInfoDto
	 * @return
	 */
	@Tag(name = "[bc] 사용자 상태 코드 Comment", description = "사용자 상태 코드 Comment")
	@GetMapping(value = "/selectUserCommentCode")
	public ResponseDto<Map<String, Object>> selectUserCommentCode() {

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		BcUserInfoDto bcUserInfoDto = new BcUserInfoDto();
		bcUserInfoDto.setUsrId(userDetails.getUsrId());
		bcUserInfoDto.setClaId(userDetails.getClaId());
		bcUserInfoDto.setOptTxbId(userDetails.getOptTxbId());

		Date now = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("HH");

		int nowTime = Integer.parseInt(sdf.format(now));
		String nowTimeCode = "T2408";
		if (nowTime >= 8 && nowTime < 12) {
			nowTimeCode = "T0812";
		} else if (nowTime >= 12 && nowTime < 18) {
			nowTimeCode = "T1218";
		} else if (nowTime >= 18 && nowTime < 24) {
			nowTimeCode = "T1824";
		}

		String commentCode = "";
		int asnCnt = bcCmService.selectASNCount(bcUserInfoDto);
		if (asnCnt > 0) {
			commentCode = "CK_ASN";
		} else {
			if (bcCmService.selectStudyChkCount(bcUserInfoDto) == 0) {
				commentCode = "CK_STUDY";
			} else {
				if (bcCmService.selectStudyAIChkCount(bcUserInfoDto) == 0) {
					commentCode = "CK_STUDYAI";
				}
			}
		}

		//CK_EVL :  평가 체크
		//CK_EVLPROG :  평가 진도체크

		Map<String, Object> resultMap = new HashMap<>();

		resultMap.put("nowTimeCode", nowTimeCode);
		resultMap.put("commentCode", commentCode);

		return Response.ok(resultMap);
	}

	/**
	 * 알지오매쓰 파일 상세 정보 
	 * @return
	 */
	@Tag(name = "[bc] 알지오매쓰 파일 정보", description = "알지오매쓰 파일 정보")
	@GetMapping(value = "/selectAgomFle")
	public ResponseDto<Map<String, Object>> selectAgomFle(String agomFleId) {

		Map<String, Object> resultMap = new HashMap<>();

		resultMap.put("agomFle", bcCmService.selectAgomFle(agomFleId));

		return Response.ok(resultMap);
	}

	/**
	 * 
	 * @param bcCmLrnTmDto
	 */
	@Tag(name = "[bc] 일자별 학습 접근 이력 등록", description = "일자별 학습 접근 이력 등록")
	@PostMapping(value = "/insertLrnApoHst")
	public ResponseDto<Map<String, Object>> insertLrnApoHst(@RequestBody
	BcCmLrnApoHstDto cmLrnApoHstDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		Map<String, Object> resultMap = new HashMap<>();
		cmLrnApoHstDto.setUsrId(userDetails.getUsrId());
		cmLrnApoHstDto.setDbId(DB_ID);
		resultMap.put("insertCnt", bcCmService.insertBcCmLrnApoHst(cmLrnApoHstDto));

		return Response.ok(resultMap);
	}

	/**
	* 
	* @param bcCmLrnTmDto
	*/
	@Tag(name = "[bc] 일자별 학습 접근 이력 확인", description = "일자별 학습 접근  이력 확인")
	@PostMapping(value = "/selectBcCmLrnApoHstChkDate")
	public ResponseDto<Map<String, Object>> selectBcCmLrnApoHstChkDate(@RequestBody
	BcCmLrnApoHstDto cmLrnApoHstDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		Map<String, Object> resultMap = new HashMap<>();
		cmLrnApoHstDto.setUsrId(userDetails.getUsrId());
		cmLrnApoHstDto.setDbId(DB_ID);
		resultMap.put("apoCount", bcCmService.selectBcCmLrnApoHstChkDate(cmLrnApoHstDto));

		return Response.ok(resultMap);
	}

	/**
	 * 학습접근이력 데이터 조회
	 * @param bcCmLrnTmDto
	 */
	@Tag(name = "[bc] 일자별 학습 접근 이력 확인", description = "일자별 학습 접근  이력 확인")
	@PostMapping(value = "/selectBcCmLrnApoHstData")
	public ResponseDto<BcCmLrnApoHstDto> selectBcCmLrnApoHstData(@RequestBody
	BcCmLrnApoHstDto cmLrnApoHstDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		cmLrnApoHstDto.setUsrId(userDetails.getUsrId());
		cmLrnApoHstDto.setDbId(DB_ID);

		return Response.ok(bcCmService.selectBcCmLrnApoHstData(cmLrnApoHstDto));
	}

	/**
	 * 사용자 개인정보 수집 동의 상태 조회
	 * @return
	 */
	@Tag(name = "[bc] 사용자 개인정보 동의 조회", description = "사용자의 개인정보 수집 상태를 조회")
	@GetMapping("/selectUsrAgrYn")
	public BcUsrInfoDto selectUsrAgrYn() {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		return bcCmService.selectUsrAgrYn(userDetails.getUsrId());
	}

	/**
	 * 사용자 개인정보 수집 동의 상태 업데이트
	 * @param bcUsrInfoDto
	 * @return
	 */
	@Tag(name = "[bc] 사용자 개인정보 동의 업데이트", description = "사용자의 개인정보 수집 상태 업데이트.")
	@PutMapping(value = "/updateUsragrYn")
	public ResponseEntity<BcUsrInfoDto> updateUsragrYn(@RequestBody
	BcUsrInfoDto bcUsrInfoDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcUsrInfoDto.setUsrId(userDetails.getUsrId());
		bcCmService.updateUsragrYn(bcUsrInfoDto);
		return ResponseEntity.ok(bcUsrInfoDto);
	}

	//초기 데이터 처리 로직 
	/**
	 * LCMS > LMS 이관 후 LMS 평가 생성
	 *
	 * @param null
	 * @return ResponseDto<Integer>
	 */
	@Operation(summary = "교과평가 초기 데이터 생성", description = "교과평가 초기 데이터 생성")
	@PostMapping(value = "/initDataLMS")
	public ResponseDto<Integer> initDataLMS() {
		log.debug("Entrance initDataLMS");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		//시험 등록 
		String usrId = "system"; //userDetails.getUsrId()  2024-08-28 변경
		eaEvComService.createLcmsEv(usrId, userDetails.getOptTxbId(), userDetails.getTxbId(), userDetails.getClaId(),
			DB_ID);

		//교과
		TlIniDatCondDto srhtto = new TlIniDatCondDto();
		srhtto.setOptTxbId(userDetails.getOptTxbId());
		srhtto.setTcrUsrId(userDetails.getUsrId());
		tlIniDatTcrService.registIniDat(srhtto);

		//특별학습
		SlIniDatCondDto srhDto = new SlIniDatCondDto();
		srhDto.setOptTxbId(userDetails.getOptTxbId());
		srhDto.setTcrUsrId(userDetails.getUsrId());
		slIniDatTcrService.registIniDat(srhDto);

		//AI  초기화
		AlPlStuDto dto = new AlPlStuDto();
		dto.setOptTxbId(userDetails.getOptTxbId());
		dto.setUsrId("system");
		alPlIniDatStuService.registIniDat(dto);

		//학생일경우

		if ("ST".equals(userDetails.getUsrTpCd())) {
			// 세션정보에서 설정
			SlIniDatCondDto srhDtoStu = new SlIniDatCondDto();
			srhDtoStu.setOptTxbId(userDetails.getOptTxbId());
			srhDtoStu.setStuUsrId(userDetails.getUsrId());
			slIniDatStuService.insertRcmLrnDat(srhDtoStu);

			EaAsnTcrDto eaAsnTcrDto = new EaAsnTcrDto();
			eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
			eaAsnTcrDto.setStuUsrId(userDetails.getUsrId());
			eaAsnTcrService.insertAsnSmtNewStu(eaAsnTcrDto);

			EaEvSaveReqDto eaEvSaveReqDto = new EaEvSaveReqDto();
			eaEvSaveReqDto.setOptTxbId(userDetails.getOptTxbId());
			eaEvSaveReqDto.setUsrId(userDetails.getUsrId());
			eaEvTcrService.insertEvRsNewStu(eaEvSaveReqDto);

		}
		// 기능 사용 설정 초기데이터 
		BcUsrInfoDto bcUsrInfoDto = new BcUsrInfoDto();
		bcUsrInfoDto.setUsrId(userDetails.getUsrId());
		bcUsrInfoDto.setMdfrId(userDetails.getUsrId());
		bcUsrInfoDto.setCrtrId(userDetails.getUsrId());
		bcUsrInfoDto.setDbId(DB_ID);
		bcUsrInfoDto.setOptTxbId(userDetails.getOptTxbId());
		bcCmService.checkFncUseSetm(bcUsrInfoDto);

		return Response.ok(1);
	}
	
	
	/**
	 * 평가 - 노트 > 첨부파일 ANNX_ID 조회
	 * @param String
	 * @return ResponseDto<BcCmSelectNoteFileResDto>
	 */
	@Tag(name = "[bc] 첨부파일 목록 조회", description = "첨부파일 목록 조회")
	@PostMapping(value = "/selectNoteFileID")
	public ResponseDto<BcCmSelectNoteFileResDto> selectNoteFileID(@RequestBody BcCmSelectNoteFileReqDto bcCmSelectNoteFileReqDto) {
		return Response.ok(bcCmService.selectNoteFileID(bcCmSelectNoteFileReqDto));
	}
	
}
