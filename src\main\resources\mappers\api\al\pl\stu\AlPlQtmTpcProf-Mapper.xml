<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.stu.alPlQtmTpcProf">

	<select id="selectEvCmplInfo" parameterType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT EE.EV_ID
			 , EE.OPT_TXB_ID
			 , EE.EV_DV_CD
			 , EE.EV_DTL_DV_CD
			 , EER.USR_ID
			 , EER.EV_CMPL_YN
			 , EAETR.MLU_KMMP_NOD_ID
		FROM LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID AND EE.USR_ID = EER.USR_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE EE.EV_ID = #{evId}
		AND EE.OPT_TXB_ID = #{optTxbId}
		AND EER.USR_ID = #{usrId}
		LIMIT 1
	</select>
	
	<!-- 평가 내 문항의 현토픽 평균, 이전토픽 평균점수를 같이 조회 -->
	<select id="selectEvQtmTpcProf" parameterType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT EE.EV_ID
			 , EE.EV_DV_CD
			 , EE.EV_DTL_DV_CD
			 , EER.USR_ID
			 , IFNULL(ALL2.LRNR_VEL_TP_CD, 'NM') AS LRNR_VEL_TP_CD
			 , EER.EV_CMPL_YN
			 , EAETR.OPT_TXB_ID
			 , EAETR.LLU_KMMP_NOD_ID, EAETR.LLU_KMMP_NOD_NM
			 , EAETR.MLU_KMMP_NOD_ID, EAETR.MLU_KMMP_NOD_NM
			 , EAETR.TC_KMMP_NOD_ID, EAETR.TC_KMMP_NOD_NM
			 , EAETR.TPC_KMMP_NOD_ID, EAETR.TPC_KMMP_NOD_NM
			 , EEQ.QTM_ID
			 , EEQA.CANS_YN
			 , EEQ.QTM_DFFD_DV_CD AS CTN_DFFD_DV_CD
		FROM LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
			AND (
			       CASE WHEN EE.EV_DV_CD = 'DE' THEN 1 = 1
			           ELSE EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
			       END
			)
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID
		INNER JOIN LMS_LRM.CM_OPT_TXB COT ON EAETR.OPT_TXB_ID = COT.OPT_TXB_ID
		LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2 ON EER.USR_ID = ALL2.USR_ID AND ALL2.LU_KMMP_NOD_ID =
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
			EAETR.MLU_KMMP_NOD_ID
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			EAETR.TC_KMMP_NOD_ID
		</if>
		AND ALL2.OPT_TXB_ID = EAETR.OPT_TXB_ID 
		WHERE EE.EV_ID = #{evId}
		AND EE.OPT_TXB_ID = #{optTxbId}
		AND EEQA.USR_ID = #{usrId}
		/* AI center 예측점수 API 호출 - 이혜인 - AlPlQtmTpcProf-Mapper.xml - selectEvQtmTpcProf */
	</select>
	
	<insert id="insertAiUsrlyQtmProf" parameterType="java.util.List">
		INSERT INTO lms_lrm.ai_usrly_qtm_prof
			 ( USR_ID, OPT_TXB_ID, QTM_ID, TXM_PN, TPC_ID, QP_DFFD_DV_CD, LRNR_VEL_TP_CD
			 , LU_KMMP_NOD_ID, TC_KMMP_NOD_ID, CANS_YN, XPL_TM_SCNT
			 , AI_PRED_CANS_RT, AI_PRED_IANS_RN_CN, DIBS_YN, DEL_YN
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		 VALUES
		 <foreach collection="list" item="item" separator=",">
		 	 ( #{item.usrId}, #{item.optTxbId}, #{item.qtmId}, 1, #{item.tpcKmmpNodId}, #{item.ctnDffdDvCd}, #{item.lrnrVelTpCd}
			 , #{item.mluKmmpNodId}, #{item.tcKmmpNodId}, NULL, NULL
			 , #{item.aiPredCansRt}, NULL, NULL, 'N'
			 , #{item.usrId}, NOW(), #{item.usrId}, NOW(), 'DB_ID_AI' )
		 </foreach>
		  ON DUPLICATE KEY UPDATE
		  	  AI_PRED_CANS_RT = VALUES(AI_PRED_CANS_RT)
		  	, TXM_PN = TXM_PN + 1
		  	, MDFR_ID = VALUES(MDFR_ID)
		  	, MDF_DTM = NOW()
		/* AI center 예측점수 API 호출 - 이혜인 - AlPlQtmTpcProf-Mapper.xml - insertAiUsrlyQtmProf */
	</insert>
	
	<!-- 사용자별 토픽 프로파일 insert -->
    <insert id="insertUsrlyTpcProf" parameterType="java.util.List">
		INSERT INTO LMS_LRM.AI_USRLY_TPC_PROF
		(
			USR_ID
		  , OPT_TXB_ID
		  , TPC_ID
		  , LU_KMMP_NOD_ID
		  , TC_KMMP_NOD_ID
		  , AI_PRED_AVG_CANS_RT
		  , AI_PRED_AVG_SCR
		  , DEL_YN
		  , CRTR_ID
		  , CRT_DTM
		  , MDFR_ID
		  , MDF_DTM
		  , DB_ID
		)
		SELECT
		    TT.USR_ID
		  , TT.OPT_TXB_ID
		  , TT.TPC_ID
		  , TT.LU_KMMP_NOD_ID
		  , TT.TC_KMMP_NOD_ID
		  , TT.AI_PRED_CANS_RT / TT.TPCCNT AS AI_PRED_AVG_CANS_RT -- 예측 평균 정답률
		  , TT.NCNT / TT.TPCCNT AS AI_PRED_AVG_SCR -- 예측 평균 점수
		  , 'N' as DEL_YN
		  , 'AiTest'
		  , NOW()
		  , 'AiTest'
		  , NOW()
		  , 'xxx'
		FROM (
			SELECT
				AUQP.USR_ID,
				MAX(AUQP.OPT_TXB_ID) AS OPT_TXB_ID,
				AUQP.TPC_ID,
				MAX(AUQP.LU_KMMP_NOD_ID) AS LU_KMMP_NOD_ID,
				MAX(AUQP.TC_KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
				COUNT(AUQP.TPC_ID) AS TPCCNT,
				CASE WHEN NN.NCNT IS NULL THEN 0 ELSE NN.NCNT END AS NCNT,
				SUM(AUQP.AI_PRED_CANS_RT) AS AI_PRED_CANS_RT
			FROM LMS_LRM.AI_USRLY_QTM_PROF AUQP
				LEFT OUTER JOIN (
					SELECT
						MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
						EEQ.TPC_ID,
						EEQA.USR_ID,
						MAX(EEQA.CANS_YN) AS CANS_YN,
						COUNT(EEQA.CANS_YN) AS NCNT
					FROM LMS_LRM.EA_EV EE
						INNER JOIN LMS_LRM.EA_EV_QTM EEQ
						    ON EE.EV_ID = EEQ.EV_ID
						INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
						    ON EE.EV_ID = EEQA.EV_ID
						    AND EEQ.QTM_ID = EEQA.QTM_ID
					WHERE
					    EEQA.CANS_YN = 'Y'
					    AND EEQA.USR_ID = #{list[0].usrId}
					    AND EE.OPT_TXB_ID = #{list[0].optTxbId}
					GROUP BY EEQA.USR_ID, EEQ.TPC_ID
				) NN
				    ON NN.USR_ID = AUQP.USR_ID
				    AND NN.TPC_ID = AUQP.TPC_ID
			WHERE
			    AUQP.USR_ID = #{list[0].usrId}
				AND AUQP.OPT_TXB_ID = #{list[0].optTxbId}
				AND AUQP.TPC_ID IN
				<foreach collection="list" item="item" open="(" close=")" separator=",">
					#{item.kmmpNodId}
				</foreach>
			GROUP BY
			    AUQP.USR_ID, AUQP.TPC_ID
		) TT
		ON DUPLICATE KEY UPDATE
			AI_PRED_AVG_SCR = VALUES(AI_PRED_AVG_SCR),
			AI_PRED_AVG_CANS_RT = VALUES(AI_PRED_AVG_CANS_RT),
			MDFR_ID = 'AiTest',
			MDF_DTM = NOW()
		/* AI맞춤 사용자별 토픽 프로파일 insert  - 김현혜 - AlPlQtmTpcProf-Mapper.xml - insertUsrlyTpcProf */
	</insert>
	
	<update id="updateAiDgnEvPredAvgCansRt" parameterType="java.util.List">
		<foreach collection="list" item="item" separator=";">
	        UPDATE LMS_LRM.AI_USRLY_TPC_PROF
			   SET AI_DGN_EV_PRED_AVG_CANS_RT = AI_PRED_AVG_CANS_RT
		 	 WHERE USR_ID = #{usrId}
		 	   AND TPC_ID = #{tpcKmmpNodId}
		</foreach>
	</update>
	
	<select id="selectBcTxbInfo" parameterType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto" resultType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto">
		SELECT TXB_ID, AUTR_CD, AUTR_NM, SBJ_CD, SCHL_GRD_CD, SGY_CD
		FROM LMS_CMS.BC_TXB BT 
		WHERE TXB_ID = #{txbId}
		AND USE_YN = 'Y'
		AND DEL_YN = 'N'
	</select>
	
	
	<select id="selectMluQtmList" parameterType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(RCSTN.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(BT.SBJ_CD) AS SBJ_CD,
			MAX(BT.SCHL_GRD_CD) AS SCHL_GRD_CD,
			MAX(BT.SGY_CD) AS SGY_CD,
			MAX(BT.AUTR_NM) AS AUTR_NM,
			MAX(BT.AUTR_CD) AS AUTR_CD,
			MAX(RCSTN.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(RCSTN.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID,
			MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
			BALAC.CTN_CD AS QTM_ID,
			MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD,
			IFNULL(MAX(ALL2.LRNR_VEL_TP_CD), 'NM') AS LRNR_VEL_TP_CD
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN RCSTN
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3 ON RCSTN.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID AND RCSTN.OPT_TXB_ID = DPTH3.OPT_TXB_ID AND DPTH3.DPTH = 3
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4 ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID AND RCSTN.OPT_TXB_ID = DPTH4.OPT_TXB_ID AND DPTH4.DPTH = 4
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5 ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID AND RCSTN.OPT_TXB_ID = DPTH5.OPT_TXB_ID AND DPTH5.DPTH = 5
		INNER JOIN (
			SELECT KMMP_NOD_ID, CTN_CD
			FROM LMS_CMS.BC_AI_LRN_ATV_CTN
			WHERE DEL_YN = 'N' AND CTN_TP_CD IN ('EX', 'QU')
		) BALAC
		ON DPTH5.KMMP_NOD_ID = BALAC.KMMP_NOD_ID
		INNER JOIN LMS_CMS.QP_QTM QQ 
		ON BALAC.CTN_CD = QQ.QP_QTM_ID AND QQ.QP_DFFD_CD IS NOT NULL
		INNER JOIN LMS_LRM.CM_OPT_TXB COT
		ON RCSTN.OPT_TXB_ID = COT.OPT_TXB_ID
		INNER JOIN LMS_CMS.BC_TXB BT
		ON BT.TXB_ID = COT.TXB_ID
		AND BT.USE_YN = 'Y'
		LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2 ON ALL2.USR_ID = #{usrId} AND ALL2.LU_KMMP_NOD_ID = 
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
			RCSTN.KMMP_NOD_ID
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			DPTH4.KMMP_NOD_ID
		</if>
		AND ALL2.OPT_TXB_ID = #{optTxbId}
		WHERE RCSTN.OPT_TXB_ID = #{optTxbId}
		AND RCSTN.DPTH = 2
		AND RCSTN.KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY BALAC.CTN_CD
		ORDER BY MAX(DPTH5.KMMP_NOD_ID)
		/* AI맞춤 문항추천 - 이혜인 - AlPlQtmTpcProf-Mapper.xml - selectMluQtmList */
	</select>

	<select id="selectKmmpNodCount" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="int">
		select
			count(*)
		from
			lms_lrm.ai_kmmp_nod_rcstn aknr
		where OPT_TXB_ID = #{optTxbId}
		  and KMMP_NOD_ID = #{mluKmmpNodId}
	</select>

</mapper>