package com.aidt.api.al.pl.tcr;

import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqTcrReqDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc AI맞춤학습 단원목차 테스트
 */
@Slf4j
@Tag(name="[al] AI맞춤학습[AlPlTcr]", description="AI맞춤학습(교사용)")
@RestController
@RequestMapping("/api/v1/al/pl/tcr")
public class AlPlTcrController {
	@Autowired
    private JwtProvider jwtProvider;
    @Autowired
    private AlPlTcrService alPlTcrService;

    
    /**
     * AI맞춤학습 과제출제건수조회
     * 
     * @return ResponseDto<Integer>
     */
    @Tag(name="AI맞춤학습 과제출제건수조회", description="AI맞춤학습 과제출제건수조회")
    @PostMapping(value = "/selectCountEaAsnDataTcr")
    public ResponseDto<Integer> selectCountEaAsnDataTcr(@RequestBody AlMluTcLstInqTcrReqDto tcrDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        tcrDto.setOptTxbId(userDetails.getOptTxbId()); 

        return Response.ok(alPlTcrService.selectCountEaAsnDataTcr(tcrDto));
    }
    

}
