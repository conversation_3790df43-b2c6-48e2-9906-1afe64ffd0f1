package com.aidt.api.bc.cm;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.aidt.api.bc.cm.exception.BizException;
import com.amazonaws.SdkClientException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class BcS3FileCheckUtil {

	@Cacheable(cacheNames = "shortCache", key = "'cm:isFileExists:' + #path + ''", cacheManager = "aidtCacheManager")
	public boolean isFileExists(String bucketName, String path) {
		boolean result = false;

		if (StringUtils.hasText(bucketName) && StringUtils.hasText(path)) {
			// 불필요한 슬래시 제거
			if (path.startsWith("/")) {
				path = path.substring(1);
			}

			try {
				// 파일 메타데이터 조회 (파일이 없으면 예외 발생)
				BcCmUtil.getAmazonS3Client().getObjectMetadata(bucketName, path);

				result = true;
			} catch (SdkClientException se) {
				log.debug("SdkClientException >>> {}", se.getMessage());
			}
		} else {
			throw new BizException(
					"bucketName or path value is null... bucketName = " + bucketName + " / path = " + path);
		}

		return result;
	}
}
