package com.aidt.api.bc.tnte.stu;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.cm.mg.AlMgService;
import com.aidt.api.bc.tnte.dto.BcNewTnteDto;
import com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto;
import com.aidt.api.bc.tnte.dto.BcTnteDto;
import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:55
 * @modify 2024-01-05 17:52:55
 * @desc 필기 Service
 */

//@Slf4j
@Service
public class BcTnteStuService {

    private final String MAPPER_NAMESPACE = "api.bc.tnte.stu.";

    @Autowired
    private CommonDao commonDao;
    
    @Autowired
	private AlMgService alMgService;
    
    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    /**
     * 필기 조회 서비스
     *
     * @param BcTnteDto
     * @return List<BcTnteDto>
     */
    public List<BcTnteDto> selectTnteList(BcTnteDto bcTnteDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectTnteList", bcTnteDto);
    }

    /**
     * 필기 상세 조회 서비스
     *
     * @param BcTnteDto
     * @return BcTnteDto
     */
    public BcTnteDto selectTnteDtl(BcTnteDto bcTnteDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectTnteDtl", bcTnteDto);
    }

    /**
     * 필기 상세 조회 서비스(학습창)
     *
     * @param BcTnteDto
     * @return BcTnteDto
     */
    public BcTnteDto selectLayoutTnteDtl(BcTnteDto bcTnteDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectLayoutTnteDtl", bcTnteDto);
    }

    /**
     * 필기 등록 서비스
     *
     * @param BcTnteDto
     * @return int
     */
    @Transactional
    public int insertTnte(BcTnteDto bcTnteDto) {
    	int cnt = 0;
    	// KEY 꺼내서 등록되어있는지 확인 후 UPDATE로갈지 INSERT갈지 분기해야함
    	int tnteId = bcTnteDto.getTnteId();
    	int checkId = commonDao.select(MAPPER_NAMESPACE + "selectTnteCheck", bcTnteDto);
        
        if(tnteId == 0 && checkId == 0) {
            cnt = commonDao.insert(MAPPER_NAMESPACE + "insertTnte", bcTnteDto);
        }else {
            cnt = commonDao.insert(MAPPER_NAMESPACE + "updateTnte", bcTnteDto);
        }

    	if(cnt > 0) {
    		return bcTnteDto.getTnteId();
    	}else {
    		return checkId;
    	}
    }


    /**
     * 필기 삭제 서비스
     *
     * @param BcTnteDtoList
     * @return int
     */
    @Transactional
    public int deleteTnteList(BcTnteDto tnteDto) {


    	// 첨부파일 삭제 로직 진행 시작


    	// 첨부파일 삭제 로직 진행 종료

        return commonDao.delete(MAPPER_NAMESPACE + "deleteTnte", tnteDto);
    }

    /**
     * 확장자 추출
     *
     * @param originalFilename
     * @return String
     */
    //private String extractExt(String originalFilename) {
    //    int pos = originalFilename.lastIndexOf(".");
    //    return originalFilename.substring(pos + 1);
    //}


    // 빽단 API 호출 테스트용
    /*
    @Transactional
    public String testInsertTnte(HttpServletRequest req, MultipartFile file, BcTnteDto bcTnteDto) {

    	// S3 업로드 후

        String endPointUrl = "http://bc.chunjae.io/api/v1/bc/cm/common/saveFleTest";

        // 현재 Token 가져오기
        String currentToken = jwtProvider.resolveToken(req, ConstantsExt.accessTokenHeader);
        log.debug("Current Token : " + currentToken);

        // Header 설정
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Authorization", "Bearer " + currentToken);
        httpHeaders.add("Content-Type", "application/json");

        BcAnnxApiCallDto aa = new BcAnnxApiCallDto();
        aa.setAnnxId("22");
        aa.setFilePath("6666");

        String nn = "111";
        BcAnnxFleDto[] result = webFluxUtil.post(
                endPointUrl,
                httpHeaders,
                aa,
                BcAnnxFleDto[].class);

        log.debug("result == " + String.valueOf(result));

        if(result != null && result.length > 0) {
            for(BcAnnxFleDto bb : result) {
            	log.debug("bb.getOptTxbID() == " + bb.getAnnxId());
            	bcTnteDto.setOptTxbId(bb.getAnnxId());
            }
        }


        List<aaa> list = result.getAnnxList();
        for(aaa bb : list) {
        	log.debug("bb.getOptTxbID() == " + bb.getAnnxId());

        	bcTnteDto.setOptTxbId(bb.getAnnxId());
        }


    	commonDao.insert(MAPPER_NAMESPACE + "insertTnte", bcTnteDto);



    	return "";
    }


	*/
    
    
    
    
    
    
    
    
    
    
    
    
    // 2024-07-16 노트 구분 selectBox 조회(저장되어 있는 케이스만 보여줌)
    public List<BcNewTnteDto> selectNewStuTnteDvList(BcNewTnteDto bcNewTnteDto) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteDvList", bcNewTnteDto);
    }
    
    // 2024-07-16 노트 구분 상세 selectBox 조회(저장되어 있는 케이스만 보여줌)
    public List<BcNewTnteDto> selectNewStuTnteDvDtlList(BcNewTnteDto bcNewTnteDto) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteDvDtlList", bcNewTnteDto);
    }
    
    
    // 2024-07-16 노트 목록 조회(개선안 버전)
    public List<BcNewTnteDto> selectNewStuTnteList(BcNewTnteDto bcNewTnteDto) {
    	
    	if(!StringUtils.isEmpty(bcNewTnteDto.getLuNodId())) {
    		//ai 지식맵ID 조회
    		List<String> kmmpNodList = alMgService.selectKmmpNodIdByLrmpNodId(bcNewTnteDto.getLuNodId());
    		bcNewTnteDto.setKmmpNodIdList(kmmpNodList);
    	}
    	List<BcNewTnteDto> tnteList = commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteList", bcNewTnteDto);
    	if(tnteList != null && tnteList.size() > 0) {
    		
//    		for(BcNewTnteDto tnte : tnteList) {
    		tnteList.removeIf(tnte -> {
    			// 그룹핑해서 조회해온 데이터로 학습활동 목록 조회
    			BcNewTnteLrnAtvDto atvDto = new BcNewTnteLrnAtvDto();
    			atvDto.setOptTxbId(bcNewTnteDto.getOptTxbId());
    			atvDto.setLrnUsrId(bcNewTnteDto.getLrnUsrId());
    			atvDto.setLuNodId(tnte.getLuNodId());
    			atvDto.setTcNodId(tnte.getTcNodId());
    			
    			// 각각의 재구성 테이블 및 컨텐츠 테이블이 다름
    			List<BcNewTnteLrnAtvDto> atvList = new ArrayList<>();
    			if("DE".equals(tnte.getLrnTpCd()) || "SE".equals(tnte.getLrnTpCd()) || "TE".equals(tnte.getLrnTpCd())) {
        			// 노트 학습활동 목록 조회(우리반 평가, 내가 만든 평가, AI 맞춤 학습)
    				// 평가쪽은 토픽에서 썸네일을 갖고와야하는데 행방을 못찾고 있음
    				// 평가 일경우 재응시 회차 조회 조건 추가 11.22 
    				atvDto.setTxmPn(tnte.getTxmPn());
    				atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteEvAtvList", atvDto);
    			}else if("TL".equals(tnte.getLrnTpCd())) {
        			// 노트 학습활동 목록 조회(우리반 수업)
    				atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteAtvTeList", atvDto);
    			}else if("SL".equals(tnte.getLrnTpCd())) {
        			// 노트 학습활동 목록 조회(선생님 추천 학습)
    				atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteAtvSlList", atvDto);
    			}else if("AI".equals(tnte.getLrnTpCd()) || "AL".equals(tnte.getLrnTpCd()) || "AE".equals(tnte.getLrnTpCd())) {
        			// 노트 학습활동 목록 조회(AI 맞춤 학습)
    				atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectNewStuTnteAtvAiList", atvDto);
    			}
    			
    			// 콘텐츠 경로 가공
    			if(atvList != null && atvList.size() > 0) {
    				for(BcNewTnteLrnAtvDto atv : atvList) {
    					atv.setCdnPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, atv.getCdnPthNm()));
    					atv.setTnteCdnPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, atv.getTnteCdnPthNm()));
    				}
    				tnte.setLrnAtvList(atvList);
    				return false;
    			} else {
    				return true;
    			}
    		});
//    		}
    	}
    	
        return tnteList;
    }
    
    // 2024-07-19 노트 개선안 삭제
    @Transactional
    public int deleteNewStuTnte(BcNewTnteDto bcNewTnteDto) {
    	String tpCd = bcNewTnteDto.getLrnTpCd();
    	if("TL".equals(tpCd) || "SL".equals(tpCd)) {
    		if(bcNewTnteDto.getLrnAtvList() != null && bcNewTnteDto.getLrnAtvList().size() > 0) {
        		for(BcNewTnteLrnAtvDto atv : bcNewTnteDto.getLrnAtvList()) {
        			atv.setOptTxbId(bcNewTnteDto.getOptTxbId());
        			atv.setLrnUsrId(bcNewTnteDto.getLrnUsrId());
        			commonDao.delete(MAPPER_NAMESPACE + "deleteNewStuTnte", atv);
        		}
    		}
    	}else if("AI".equals(tpCd) || "SE".equals(tpCd) || "DE".equals(tpCd)) {
    		if(bcNewTnteDto.getLrnAtvList() != null && bcNewTnteDto.getLrnAtvList().size() > 0) {
        		for(BcNewTnteLrnAtvDto atv : bcNewTnteDto.getLrnAtvList()) {
        			atv.setOptTxbId(bcNewTnteDto.getOptTxbId());
        			atv.setLrnUsrId(bcNewTnteDto.getLrnUsrId());
        			commonDao.delete(MAPPER_NAMESPACE + "deleteNewStuForEvTnte", atv);
        		}
    		}
    	}
    	
    	return 1;
        // return commonDao.delete(MAPPER_NAMESPACE + "deleteNewStuTnte", bcNewTnteDto);
    }

    
   /**
    * 과목 코드 조회
    * @return
    */
    public String selectSbj(String optTxbId) {
    	return commonDao.select(MAPPER_NAMESPACE + "selectSbj", optTxbId);
    }

}
