package com.aidt.api.al.wrt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "CM_AIWRITE_요청_로그")
public class AIWrtReqLogDto {

	@Schema(description = "운영교과서ID")
	private String optTxbId;

	@Schema(description = "대단원지식맵노드ID")
	private String lluKmmpNodId;

	@Schema(description = "토픽지식맵노드ID")
	private String tpcKmmpNodId;

	@Schema(description = "학생ID")
	private String stuId;

	@Schema(description = "시스템코드")
	private String systemCd;

	@Schema(description = "요청구분코드")
	private String reqDvCd;

	@Schema(description = "결과여부")
	private String rsYn;

	@Schema(description = "결과메시지")
	private String rsMsg;

	@Schema(description = "접속URL")
	private String connUrl;

	@Schema(description = "BODY내용")
	private String bodyCn;
}
