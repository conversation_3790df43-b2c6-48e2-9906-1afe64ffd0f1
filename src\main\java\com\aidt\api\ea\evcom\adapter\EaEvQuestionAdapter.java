package com.aidt.api.ea.evcom.adapter;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.aidt.api.ea.evcom.dto.EaEvQuestionSolution;
import com.aidt.api.ea.evcom.dto.EaEvSppNtnResult;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class EaEvQuestionAdapter {

	private final String MAPPER_NAMESPACE = "api.ea.evcom.";
	private final CommonDao commonDao;

	public List<EaEvQuestionSolution> getEaEvQuestionSolution(Collection<Long> qtmIds) {
		if (CollectionUtils.isEmpty(qtmIds)) {
			return Collections.emptyList();
		}
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvQuestions", qtmIds);
	}

	public List<EaEvQuestionSolution.EaEvQuestionKeyword> getEaEvQuestionKeywords(Collection<Long> qtmIds) {
		if (CollectionUtils.isEmpty(qtmIds)) {
			return Collections.emptyList();
		}
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvQuestionKeywords", qtmIds);
	}

	public List<EaEvSppNtnResult.EaEvSppNtnQuestion> getSppNtnQuestions(EaEvSppNtnResult eaEvSppNtnResult) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvSppNtnQuestions", eaEvSppNtnResult);
	}
}