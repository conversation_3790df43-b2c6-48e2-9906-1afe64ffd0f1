package com.aidt.api.al.cmt.stu;

import com.aidt.api.al.cmt.cm.AiCmtService;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;
import com.aidt.api.al.cmt.dto.ett.en.*;
import com.aidt.api.al.cmt.dto.req.cm.AiCmtCmRptLuReqDto;
import com.aidt.api.al.cmt.dto.req.cm.QtmCntReqDto;
import com.aidt.api.al.cmt.dto.req.en.*;
import com.aidt.api.al.cmt.dto.req.ma.*;
import com.aidt.api.al.cmt.dto.res.AiCmtEnResWrapperDto;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:01:58
 * @modify date 2024-05-21 11:01:58
 * @desc
 */
@Service
public class AiCmtStuService {

    private final AiCmtService aiCmtService;

    public AiCmtStuService(AiCmtService aiCmtService) {
        this.aiCmtService = aiCmtService;
    }

    public List<AiCmtResDto> selectMaEv(AiCmtMaEvStReqDto reqDto) {
    	QtmCntReqDto qtmCnt = reqDto.getTotal().getQtmCnt();
    	if (qtmCnt == null || qtmCnt.getCorrectQtmCnt() == null || qtmCnt.getTotalQtmCnt() == null) {
            throw new NullPointerException("평가 채점 정보가 없습니다.");
        }
        List<AiCmtResDto> aiCmtResDtoList = aiCmtService.selectMaEvSt(reqDto.toDto());

        if(Objects.equals(qtmCnt.getTotalQtmCnt(), qtmCnt.getCorrectQtmCnt())) {
            aiCmtResDtoList = aiCmtResDtoList.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("4")
                              && !s.getAiCmtNo().equalsIgnoreCase("5")).collect(Collectors.toList());
        } else if(qtmCnt.getCorrectQtmCnt() == 0){
            aiCmtResDtoList = aiCmtResDtoList.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("1")).collect(Collectors.toList());
        }
        return aiCmtResDtoList;
    }

    public List<AiCmtResDto> selectMaEv(AiCmtMaEvUdReqDto reqDto) {
    	QtmCntReqDto qtmCnt = reqDto.getTotal().getQtmCnt();
    	if (qtmCnt == null || qtmCnt.getCorrectQtmCnt() == null || qtmCnt.getTotalQtmCnt() == null) {
            throw new NullPointerException("평가 채점 정보가 없습니다.");
        }
        return aiCmtService.selectMaEvUd(reqDto.toDto());
    }

    public List<AiCmtResDto> selectMaEv(AiCmtMaEvEtReqDto reqDto) {
        List<AiCmtResDto> aiCmtResDtoList = aiCmtService.selectMaEvEt(reqDto.toDto());

        QtmCntReqDto qtmCnt = reqDto.getTotal().getQtmCnt();
        if(Objects.equals(qtmCnt.getTotalQtmCnt(), qtmCnt.getCorrectQtmCnt())) {
            aiCmtResDtoList = aiCmtResDtoList.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("4")
                              && !s.getAiCmtNo().equalsIgnoreCase("5")).collect(Collectors.toList());
        } else if(qtmCnt.getCorrectQtmCnt() == 0){
            aiCmtResDtoList = aiCmtResDtoList.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("1")
                              && !s.getAiCmtNo().equalsIgnoreCase("15")).collect(Collectors.toList());
        }
        return aiCmtResDtoList;
    }

    public List<AiCmtResDto> selectMaAi(AiCmtMaAiLrnRptReqDto reqDto) {
        return aiCmtService.selectMaAiLrnRpt(reqDto.toDto());
    }

    public List<AiCmtResDto> selectMaAi(AiCmtMaAiDgnRptReqDto reqDto) {
        return aiCmtService.selectMaAiDgnRpt(reqDto.toDto());
    }

    public List<AiCmtResDto> selectRpt(AiCmtCmRptLuReqDto reqDto) {
        return aiCmtService.selectMaRptLu(reqDto.toDto());
    }

    public AiCmtEnResWrapperDto selectEnEv(AiCmtEnEvStReqDto reqDto) {
        AiCmtEnEvStDto dto = reqDto.toDto();
        N12Dto n12 = dto.getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        List<AiCmtResDto> total = aiCmtService.selectEnEvSt(dto);
        List<AiCmtResDto> resultTotal = this.tpcNameConvert(total, dto.getAraRankList());

        QtmCntReqDto qtmCnt = reqDto.getTotal().getQtmCnt();
        if(Objects.equals(qtmCnt.getTotalQtmCnt(), qtmCnt.getCorrectQtmCnt())) {
            resultTotal = resultTotal.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("5"))
                    .collect(Collectors.toList());
        }

        return AiCmtEnResWrapperDto.builder().total(resultTotal).ara(ara).build();
    }

    public AiCmtEnResWrapperDto selectEnEv(AiCmtEnEvEtReqDto reqDto) {
        AiCmtEnEvEtDto dto = reqDto.toDto();
        N12Dto n12 = dto.getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        List<AiCmtResDto> total = aiCmtService.selectEnEvEt(dto);

        boolean allCorrect = reqDto.getArea().isAllCorrect();
        if(allCorrect) {
            total = total.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("5")
                              && !s.getAiCmtNo().equalsIgnoreCase("4")).collect(Collectors.toList());
        }

        return AiCmtEnResWrapperDto.builder().total(total).ara(ara).build();
    }

    public AiCmtEnResWrapperDto selectEnEv(AiCmtEnEvUgReqDto reqDto) {
        AiCmtEnEvUgDto dto = reqDto.toDto();
        N12Dto n12 = dto.getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        List<AiCmtResDto> total = aiCmtService.selectEnEvUg(dto);
        List<AiCmtResDto> resultTotal = this.tpcNameConvert(total, dto.getAraRankList());

        QtmCntReqDto qtmCnt = reqDto.getTotal().getQtmCnt();
        if(Objects.equals(qtmCnt.getTotalQtmCnt(), qtmCnt.getCorrectQtmCnt())) {
            resultTotal = resultTotal.stream()
                    .filter(s -> !s.getAiCmtNo().equalsIgnoreCase("5")
                              && !s.getAiCmtNo().equalsIgnoreCase("4")).collect(Collectors.toList());
        }

        return AiCmtEnResWrapperDto.builder().total(resultTotal).ara(ara).build();
    }

    public List<AiCmtResDto> selectEnEv(AiCmtEnEvToReqDto reqDto) {
        N12Dto n12 = reqDto.toDto().getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }
        return ara;
    }

    public AiCmtEnResWrapperDto selectEnAi(AiCmtEnAiDgnRptReqDto reqDto) {
        AiCmtEnAiDgnRptDto dto = reqDto.toDto();
        N12Dto n12 = dto.getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        List<AiCmtResDto> total = aiCmtService.selectEnAiDgnRpt(dto);
        List<AiCmtResDto> resultTotal = this.tpcNameConvert(total, dto.getAraRankList());

        return AiCmtEnResWrapperDto.builder().total(resultTotal).ara(ara).build();
    }

    public AiCmtEnResWrapperDto selectEnAi(AiCmtEnAiLrnRptReqDto reqDto) {
        AiCmtEnAiLrnRptDto dto = reqDto.toDto();
        N12Dto n12 = dto.getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        List<AiCmtResDto> total = aiCmtService.selectEnAiLrnRpt(dto);
        List<AiCmtResDto> resultTotal = this.tpcNameConvert(total, dto.getAraRankList());

         return AiCmtEnResWrapperDto.builder().total(resultTotal).ara(ara).build();
    }

    public List<AiCmtResDto> selectEnRpt(AiCmtCmRptLuReqDto reqDto) {
        return aiCmtService.selectEnRptLu(reqDto.toDto());
    }

    private List<AiCmtResDto> tpcNameConvert(List<AiCmtResDto> total, List<String> araRankList) {
        if(araRankList.size() < 3) {
            return total.stream().filter(t -> !t.getAiCmtNo().equalsIgnoreCase("3")).collect(Collectors.toList());
        }

        return total.stream().map(t -> {
            if (t.getAiCmtNo().equalsIgnoreCase("3")) {
                String cmt = t.getCmtCn();
                cmt = cmt.replace("#강점1#와(과)", this.checkBatchimEnding(araRankList.get(0).replace("단어", "어휘력")) ? "#강점1#과" : "#강점1#와");
                cmt = cmt.replace("#약점1#와(과)", this.checkBatchimEnding(araRankList.get(araRankList.size() - 1).replace("단어", "어휘력")) ? "#약점1#과" : "#약점1#와");
                cmt = cmt.replace("#강점1#", String.format("#1#%s#2#", araRankList.get(0).replace("단어", "어휘력")));
                cmt = cmt.replace("#강점2#", String.format("#1#%s#2#", araRankList.get(1).replace("단어", "어휘력")));
                cmt = cmt.replace("#약점1#", String.format("#1#%s#2#", araRankList.get(araRankList.size() - 1).replace("단어", "어휘력")));
                cmt = cmt.replace("#약점2#", String.format("#1#%s#2#", araRankList.get(araRankList.size() - 2).replace("단어", "어휘력")));
                cmt = cmt.replace("#1#어휘력#2# 능력", "#1#어휘력#2#");
                t.setCmtCn(cmt);
                return t;
            }
            return t;
        }).collect(Collectors.toList());
    }

    private boolean checkBatchimEnding(String name) {
        if(StringUtils.isEmpty(name)) return false;
        char lastName = name.charAt(name.length() - 1);
        return (lastName - 0xAC00) % 28 > 0;
    }
}
