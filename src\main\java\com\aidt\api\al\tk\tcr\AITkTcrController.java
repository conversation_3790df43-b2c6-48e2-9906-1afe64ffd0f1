package com.aidt.api.al.tk.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.tk.dto.AlTkDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-03 11:01:00
 * @modify 2024-06-03 11:01:00
 * @desc AI회화 교사 컨트롤러
 */

@Slf4j
@Tag(name="[al] AI회화-교사", description="AI회화-교사")
@RestController
@RequestMapping("/api/v1/al/tk/tcr")
public class AITkTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private AlTkTcrService alTkTcrService;
	
    /**
     * AI 회화 토픽 목록 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI회화 토픽 목록 조회")
	@PostMapping(value = "/selectTkTcrList")
	public ResponseDto<List<AlTkDto>> selectTkTcrList(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
			alTkDto.setIsCmplLrn(alTkDto.getIsCmplLrn());
		}
		log.debug("selectTkTcrList alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkTcrList");
		return Response.ok(alTkTcrService.selectTkTcrList(alTkDto));
	}
	
	/**
     * AI 회화 나의 최근 학습 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI회화 나의 최근 학습 조회")
	@PostMapping(value = "/selectTkRcnLrnTcrInfo")
	public ResponseDto<List<AlTkDto>> selectTkRcnLrnTcrInfo(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkRcnLrnTcrInfo");
		return Response.ok(alTkTcrService.selectTkRcnLrnTcrInfo(alTkDto));
	}
	
	/**
     * AI 회화 상세 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI 회화 상세 조회")
	@PostMapping(value = "/selectTkTcrDetailInfo")
	public ResponseDto<List<AlTkDto>> selectTkTcrDetailInfo(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkTcrDetailInfo");
		return Response.ok(alTkTcrService.selectTkTcrDetailInfo(alTkDto));
	}
	
	/**
     * AI 회화 이전, 다음 단원 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI 회화 이전, 다음 단원 조회")
	@PostMapping(value = "/selectTkTcrPrevNextInfo")
	public ResponseDto<List<AlTkDto>> selectTkTcrPrevNextInfo(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkRcnLrnStuInfo");
		return Response.ok(alTkTcrService.selectTkTcrPrevNextInfo(alTkDto));
	}
	
	/**
     * AI 회화 학습 상태 insert
     * @param AlTkDto
     * @return ResponseDto<Integer>
     */
	@Tag(name="[al] AI 회화 학습 상태 insert")
	@PostMapping(value = "/insertTkTcrLrnStCd")
	public ResponseDto<Integer> insertTkTcrLrnStCd(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setOptTxbId(userDetails.getOptTxbId());
		alTkDto.setLrnUsrId(userDetails.getUsrId());
		alTkDto.setCrtrId(userDetails.getUsrId());
		alTkDto.setMdfrId(userDetails.getUsrId());
		alTkDto.setDbId(userDetails.getTxbId());

	    return Response.ok(alTkTcrService.insertTkTcrLrnStCd(alTkDto));
	}
	
	/**
     * AI 회화 학습 상태 update
     * @param AlTkDto
     * @return ResponseDto<Integer>
     */
	@Tag(name="[al] AI 회화 학습 상태 update")
	@PostMapping(value = "/updateTkTcrLrnStCd")
	public ResponseDto<Integer> updateTkTcrLrnStCd(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setOptTxbId(userDetails.getOptTxbId());
		alTkDto.setLrnUsrId(userDetails.getUsrId());
		alTkDto.setCrtrId(userDetails.getUsrId());
		alTkDto.setMdfrId(userDetails.getUsrId());
		alTkDto.setDbId(userDetails.getTxbId());

		return Response.ok(alTkTcrService.updateTkTcrLrnStCd(alTkDto));
	}
	
	/**
     * AI회화 아키핀 호출
     * @param BcCbDto
     * @return
     */
	@Operation(summary="AI회화 아키핀 호출", description="AI회화 아키핀 호출")
	@PostMapping(value = "/startDalogueApi", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> startDalogueApi(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setUserID(userDetails.getUsrId());
		//alTkDto.setUserID("kim");
		return alTkTcrService.startDalogueApi(alTkDto);
	}
	
	/**
	 * AI회화 아키핀 호출 - 이어하기
	 * @param BcCbDto
	 * @return
	 */
	@Operation(summary="AI회화 아키핀 호출 - 이어하기", description="AI회화 아키핀 호출 - 이어하기")
	@PostMapping(value = "/nextDalogueApi", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> nextDalogueApi(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setUserID(userDetails.getUsrId());
		//alTkDto.setUserID("kim");
		return alTkTcrService.nextDalogueApi(alTkDto);
	}
}
