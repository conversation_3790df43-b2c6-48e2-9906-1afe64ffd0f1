package com.aidt.api.ea.evcom.lansnte.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 13:09:오후 1:09
 * @modify date 2024-03-29 13:09:오후 1:09
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLansNteReqDto {
    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="DB ID")
    private String dbId;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="평가 ID")
    private long evId;

    @Parameter(name="평가 구분 코드")
    private String evDvCd;

    @Parameter(name="실수로 몰라서 타입")
    private String type;

    @Parameter(name="정렬")
    private String sorting;

    @Parameter(name="문항 ID")
    private String qtmId;

    @Parameter(name="단원 학습맵 노드 ID")
    private String luLrmpNodId;

    @Parameter(name="차시 학습맵 노드 ID")
    private String tcLrmpNodId;

    @Parameter(name="문항 플랫폼 대단원 ID")
    private String qpLluId;

    @Parameter(name="문항 플랫폼 난이도 구분 코드")
    private String qtmDffdDvCd;

	private int pageNo;
	private int pageSize;

    @Parameter(name="단원 별 오답 문항 list")
    private List<EaLansNteReqDto> qpList;

    @Parameter(name="오답노트 학습 완료된 것만 조회")
    private String isIansNteCmpl;

}
