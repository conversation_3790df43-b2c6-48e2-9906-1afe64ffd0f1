package com.aidt.api.bc.cm.tcr;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.aidt.api.bc.cm.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:47
 * @modify 2024-01-05 17:52:47
 * @desc 공통 Controller
 */

//@Slf4j
@Tag(name="[bc] 공통[BcCmTcr]", description="공통(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/common")
public class BcCmTcrController {

    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;

    @Autowired
    private BcCmTcrService bcCmTcrService;

    @Autowired
    private JwtProvider jwtProvider;


    /**
     * 교사 로그인 후 :: 담당 학생 실시간 모니터링 테이블 데이터 check
     *
     * @param
     * @return ResponseDto<UserDto>
     */
    @Operation(summary="담당 학생 실시간 모니터링 데이터 check", description="담당 학생 실시간 모니터링 데이터 check")
    @PostMapping(value = "/initStuCurLrnStCheck")
    public void stuCurLrnStCheck(@RequestBody List<BcStuListDto> stuList) {
        bcCmTcrService.stuCurLrnStCheck(stuList, DB_ID);
    }

    /**
     * 교사가 담당학생에 대한 관심여부 변경
     *
     * @param
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교사가 담당학생에 대한 관심여부 변경", description="교사가 담당학생에 대한 관심여부 변경한다.")
    @PutMapping(value = "/updateStuNtrYn")
    public ResponseDto<Integer> updateStuNtrYn(@RequestBody BcUsrInfoDto bcUsrInfoDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcUsrInfoDto.setMdfrId(userDetails.getUsrId());
        return Response.ok(bcCmTcrService.updateStuNtrYn(bcUsrInfoDto));
    }

    /**
     * 학생 목록 조회
     *
     * @param String
     * @return ResponseList<BcStuListDto>
     */
    @Operation(summary="학생 목록 조회", description="학생 목록을 조회한다.")
    @GetMapping(value = "/selectStuList/{usrId}")
    public ResponseDto<List<BcStuListDto>> selectStuList(@PathVariable("usrId") String usrId) {
        return Response.ok(bcCmTcrService.selectStuList(usrId));
    }
    
    /**
     * 학급 목록 조회
     *
     * @param String
     * @return ResponseList<BcStuListDto>
     */
    @Operation(summary="학급 목록 조회", description="학급 목록을 조회한다.")
    @GetMapping(value = "/selectClaList/{usrId}")
    public ResponseDto<List<BcClaListDto>> selectClaList(@PathVariable("usrId") String usrId) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	String kerisUsrId = userDetails.getKerisUsrId();
    	String txbId = userDetails.getTxbId();
        return Response.ok(bcCmTcrService.selectClaList(kerisUsrId, txbId));
    }

    /**
     * Third Party 사용 여부 조회
     *
     * @param String
     * @return ResponseList<Map<String, Object>>
     * @throws IOException 
     * @throws DatabindException 
     * @throws StreamReadException 
     */
    @Operation(summary="Third Party 사용 여부 조회", description="Third Party 사용 여부 조회.")
    @PostMapping(value = "/selectThirdPartyUseInfo")
    public ResponseDto<Map<String, Object>> selectThirdPartyUseInfo(@RequestBody Map<String, Object> params) throws StreamReadException, DatabindException, IOException {
        return Response.ok(bcCmTcrService.selectThirdPartyUseInfo(params.get("fileName").toString()));
    }
}
