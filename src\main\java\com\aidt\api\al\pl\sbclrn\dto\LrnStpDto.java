package com.aidt.api.al.pl.sbclrn.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-23 10:00:32
 * @modify date 2024-02-23 10:00:32
 * @desc [학습목록조회 학습단계 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class LrnStpDto {
	@Parameter(name="학습단계ID")
	private String lrnStpId;
	
	@Parameter(name="학습단계명")
	private String lrnStpNm;
	
	@Parameter(name="학습단계정렬순서")
	private int lrnStpOrdn;
	
	@Parameter(name="활동")
	private List<LrnAtvDto> lrnAtvList;
	
}
