package com.aidt.api.config;

import com.aidt.base.message.messagequeue.TopicNameAware;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

@ConditionalOnProperty(name = "spring.kafka.enabled", matchIfMissing = true, havingValue = "true")
@Configuration
public class MessageQueueConfig {

	/**
	 * AA 설계 의도 : 
	 * - Topic 관리는 업무 영역 단위별로 생성하는 것으로 기획 (단 데이터양이 많은 경우 별도 분리 고려)
	 * - partitions = 24, replicas = 2 초기 설계 추천값
	 * --> 성능에 따라 조절 필요 (AA와 상의 필수)
	 * ## topic 추가 시 AA와 상의 필요
	 */

    public static enum Topics implements TopicNameAware {
        ADMIN_STAT
    }

    /**
     * 관리자 통계용 Topic
     */
    @Bean
    public NewTopic topicAdminStat() {
        return TopicBuilder.name(Topics.ADMIN_STAT.name())
                .partitions(24)
                .replicas(2)
                .build();
    }
}
