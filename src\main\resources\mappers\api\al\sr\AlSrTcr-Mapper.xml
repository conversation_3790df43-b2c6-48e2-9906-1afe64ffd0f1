<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.sr.tcr">

	<!-- AI 교과평어 / 수행평가 활동명 목록 -->
	<select id="selectSrPfBsList" resultType="com.aidt.api.al.sr.dto.pf.AiSrPfAtvNmResDto">
		SELECT
			AI_PF_BS_DATA_ID,
			TRM_DV_CD,
			LU_NM,
			TC,
			ATV_NM,
			ATV_CN,
			SBC_CPBL
		FROM
			lms_lrm.ai_pf_bs_data
		where
			TXB_ID = #{txbId}
	</select>

	<!-- AI 교과평어 / 수행평가 결과 목록 -->
	<select id="selectSrPfResultList" parameterType="java.lang.String" resultType="com.aidt.api.al.sr.dto.pf.AiSrPfResultDto">
		SELECT
			AI_PF_CMT_ID,
			LVL_CD,
			PF_CMT
		FROM
			lms_lrm.ai_pf_cmt
		where
			AI_PF_BS_DATA_ID = #{aiPfBsDataId}
		ORDER BY RAND()
	</select>

	 <!-- AI 교과평어 / 교과평가 조회 -->
	<select id="selectQtmListByNodeId" resultType="com.aidt.api.al.sr.dto.sbc.AiSrQtmDto">
		/** AlSrTcr-Mapper.xml - selectQtmListByNodeId */
		SELECT
		    RR.USR_ID,
		    RR.CRCL_ACH_BS_ID as QP_ACH_BS_ID,
			BECA.EDU_CRS_ACH_BS_CD as QP_ACH_BS_CD,
		    RR.LU_LRMP_NOD_ID,
		    RCSTN.LRMP_NOD_NM as LU_LRMP_NOD_NM,
		    RR.CORRECT_CNT,
		    RR.INCORRECT_CNT,
		    RR.CORRECT_RATE
		FROM
			(SELECT
				R.USR_ID,
				R.CRCL_ACH_BS_ID,
				R.LU_LRMP_NOD_ID,
				SUM(IF(R.CANS_YN = 'Y', R.CANS_CNT, 0)) AS CORRECT_CNT,
				SUM(IF(R.CANS_YN = 'N', R.CANS_CNT, 0)) AS INCORRECT_CNT,
				ROUND(SUM(IF(R.CANS_YN = 'Y', R.CANS_CNT, 0)) / SUM(R.CANS_CNT) * 100, 2) AS CORRECT_RATE
			FROM
				(SELECT
					EEQA.USR_ID,
					BNCM.CRCL_ACH_BS_ID,
					EEQA.CANS_YN,
					EETR.LU_LRMP_NOD_ID,
					COUNT(*) AS CANS_CNT
				FROM
					LMS_LRM.EA_EV_QTM_ANW EEQA
					JOIN LMS_LRM.EA_EV EE ON EE.EV_ID = EEQA.EV_ID
					JOIN LMS_LRM.EA_EV_TS_RNGE EETR ON EE.EV_ID = EETR.EV_ID
					JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = EEQA.QTM_ID
					JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID AND EEQA.USR_ID = EER.USR_ID
				WHERE
					EEQA.USR_ID IN
					<foreach collection="userIds" index="index" item="item" open="(" close=")" separator="," >
						#{item}
					</foreach>
					<if test="nodeIds != null">
						AND EETR.LU_LRMP_NOD_ID IN
						<foreach collection="nodeIds" index="index" item="item" open="(" close=")" separator="," >
							#{item}
						</foreach>
					</if>
					AND EE.EV_DTL_DV_CD = 'UG' AND EE.EV_DV_CD = 'SE' AND EE.USE_YN = 'Y'
					AND BNCM.CRCL_CTN_TP_CD = 'EX'
					AND BNCM.CRCL_ACH_BS_ID IS NOT NULL
					AND EER.EV_CMPL_YN = 'Y'
				GROUP BY BNCM.CRCL_ACH_BS_ID, EEQA.USR_ID, EETR.LU_LRMP_NOD_ID, EEQA.CANS_YN) R
			GROUP BY R.CRCL_ACH_BS_ID, R.USR_ID, R.LU_LRMP_NOD_ID
		) RR
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN RCSTN ON RCSTN.OPT_TXB_ID = #{optTxbId} and RCSTN.LRMP_NOD_ID = RR.LU_LRMP_NOD_ID
		INNER JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BECA ON BECA.EDU_CRS_ACH_BS_ID = RR.CRCL_ACH_BS_ID
		ORDER BY CORRECT_RATE DESC, CORRECT_CNT DESC
	</select>

	<select id="selectAchCmtList" resultType="com.aidt.api.al.sr.dto.sbc.AiSrAchCmtDto">
		SELECT
			AI_ACH_CMT_ID,
			ACH_BS_CD,
			ACH_BS_CMT
		FROM
			LMS_LRM.AI_ACH_CMT
		WHERE
			ACH_BS_CD = #{achBsCd}
		ORDER BY RAND()
	</select>

	<select id="selectSrUserDataFilePath" resultType="com.aidt.api.al.sr.dto.sbc.AiSrUserDataDto">
		SELECT
			CAF.ANNX_FLE_PTH_NM,
			CAF.ANNX_FLE_ID,
			CAF.ANNX_ID,
			SUF.MDF_DTM
		FROM
			LMS_LRM.AI_SBC_FLT_USR_FLE SUF
			LEFT JOIN LMS_LRM.CM_ANNX_FLE CAF ON SUF.ANNX_FLE_ID = CAF.ANNX_FLE_ID
		WHERE
			SUF.OPT_TXB_ID = #{optTxbId}
		AND SUF.USR_ID = #{usrId}
		ORDER BY CAF.SRT_ORDN DESC LIMIT 1
	</select>

	<select id="selectSrUserFile" resultType="com.aidt.api.al.sr.dto.sbc.AiSrUserDataDto">
		SELECT
		    SUF.AI_SBC_FLT_USR_FLE_ID,
			SUF.OPT_TXB_ID,
			SUF.USR_ID,
			SUF.ANNX_ID,
			SUF.MDF_DTM
		FROM
			LMS_LRM.AI_SBC_FLT_USR_FLE SUF
		WHERE
			SUF.OPT_TXB_ID = #{optTxbId}
		  AND SUF.USR_ID = #{usrId}
	</select>

	<insert id="insertSrUserFile" parameterType="com.aidt.api.al.sr.dto.sbc.AiSrUserDataReqDto">
		/** AlSrTcr-Mapper.xml - insertSrUserData */
		INSERT INTO LMS_LRM.AI_SBC_FLT_USR_FLE(
			OPT_TXB_ID,
			USR_ID,
			ANNX_FLE_ID,
			ANNX_ID,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		) VALUES (
			#{optTxbId},
			#{usrId},
			#{annxFleId},
			#{annxId},
			#{usrId},
			NOW(),
			#{usrId},
			NOW(),
			'DB_ID'
		)
	</insert>

	<update id="updateSrUserFile" parameterType="com.aidt.api.al.sr.dto.sbc.AiSrUserDataReqDto">
		UPDATE LMS_LRM.AI_SBC_FLT_USR_FLE
		SET
			ANNX_FLE_ID = #{annxFleId}
		  , MDFR_ID = #{usrId}
		  , MDF_DTM = NOW()
		WHERE USR_ID = #{usrId}
		  AND OPT_TXB_ID = #{optTxbId}
	</update>
</mapper>