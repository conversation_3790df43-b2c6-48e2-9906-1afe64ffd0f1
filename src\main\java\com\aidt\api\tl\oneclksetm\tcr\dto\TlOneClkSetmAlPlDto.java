package com.aidt.api.tl.oneclksetm.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-19 14:41:54
 * @modify date 2024-02-19 14:41:54
 * @desc [TlOneClkSetmAlPlDto 원클릭학습설정 Ai맞춤학습 재구성 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlOneClkSetmAlPlDto {
    /** 원클릭학습설정 Ai맞춤학습 목차 저장 리스트 */
    @Parameter(name="원클릭학습설정 Ai맞춤학습 목차 저장 리스트")
    private List<TlOneClkSetmAlTocDto> alTocList;

    /** 원클릭학습설정 다른 학급 리스트 */
    @Parameter(name="원클릭학습설정 다른 학급 리스트")
    private List<TlOneClkSetmClaDto> claList;
}
