/**
 * 
 */
package com.aidt.api.at.token.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class KerisMemberDto {
	private String user_id;
	private String lecture_code;
	private String class_code;
	private List<String> memberIds;
	
}
