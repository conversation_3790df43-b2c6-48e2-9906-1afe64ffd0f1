package com.aidt.api.at.err;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.aidt.api.at.err.dto.EvQtmAnwRestoreDto;
import com.aidt.api.at.err.dto.FrontErrLogDto;
import com.aidt.api.at.mapper.RestoreMapper;
import com.aidt.api.ea.evcom.EaEvComService;

@Service
public class RestoreService {

	@Autowired
	private RestoreMapper restoreMapper;

	@Autowired
	private EaEvComService eaEvComService;

	@Autowired
	private FrontErrLogService FrontErrLogService;

	@Value("${server.meta.textbook.systemCode}")
	private String SYSTEM_CODE;

	@Async
	public void restoreUsrLrnVel() {
		List<String> list = selectUsrLrnrVelRestoreTargetList();

		insertRestoreLog(FrontErrLogDto.builder().connDvCd("RETOTST")
			.frontConnUrl("/api/v1/at/token/restore/usrLrnrVel")
			.bodyCn(String.valueOf(list == null || list.size() == 0 ? 0 : list.size())).systemCd(SYSTEM_CODE).build());

		if (list != null && list.size() > 0) {
			for (String usrId : list) {
				eaEvComService.updateUsrLrnrVelTpCd(usrId);

				FrontErrLogService.insertFrontErrLog(
					FrontErrLogDto.builder().connDvCd("REUSR").frontConnUrl("/api/v1/at/token/restore/usrLrnrVel")
						.bodyCn(usrId).usrId(usrId).systemCd(SYSTEM_CODE).build());
			}
		}

		insertRestoreLog(FrontErrLogDto.builder().connDvCd("RETOTED")
			.frontConnUrl("/api/v1/at/token/restore/usrLrnrVel")
			.bodyCn(String.valueOf(list == null || list.size() == 0 ? 0 : list.size())).systemCd(SYSTEM_CODE).build());
	}

	public List<String> selectUsrLrnrVelRestoreTargetList() {
		return restoreMapper.selectUsrLrnrVelRestoreTargetList();
	}

	public void insertRestoreLog(FrontErrLogDto dto) {
		restoreMapper.insertRestoreLog(dto);
	}

	@Async
	public void restoreEvXplStCd() {
		List<EvQtmAnwRestoreDto> list = selectEvQtmAnwRestoreTarget();

		insertRestoreLog(FrontErrLogDto.builder().connDvCd("XSC_RSTSTR_1")
			.frontConnUrl("/api/v1/at/token/restore/evXplStCd")
			.bodyCn(String.valueOf(list == null || list.size() == 0 ? 0 : list.size())).systemCd(SYSTEM_CODE).build());

		int cnt = 0;

		if (list != null && list.size() > 0) {
			for (EvQtmAnwRestoreDto dto : list) {
				if (StringUtils.hasText(dto.getChgXplStCd()) && !dto.getXplStCd().equals(dto.getChgXplStCd())) {
					updateEvQtmAnwXplStCd(dto);

					FrontErrLogService.insertFrontErrLog(
						FrontErrLogDto.builder().connDvCd("XSC_1")
							.frontConnUrl("/api/v1/at/token/restore/evXplStCd")
							.bodyCn(dto.getXplStCd() + " -> " + dto.getChgXplStCd() + " [ev_id = " + dto.getEvId()
								+ ", qtm_id = " + dto.getQtmId() + "]")
							.usrId(dto.getUsrId())
							.systemCd(SYSTEM_CODE).build());

					cnt++;
				}
			}
		}

		insertRestoreLog(FrontErrLogDto.builder().connDvCd("XSC_RSTEND_1")
			.frontConnUrl("/api/v1/at/token/restore/evXplStCd")
			.bodyCn(String.valueOf(list == null || list.size() == 0 ? 0 : list.size()) + " 중 " + String.valueOf(cnt)
				+ " update 진행.")
			.systemCd(SYSTEM_CODE).build());
	}

	public List<EvQtmAnwRestoreDto> selectEvQtmAnwRestoreTarget() {
		return restoreMapper.selectEvQtmAnwRestoreTarget();
	}

	public void updateEvQtmAnwXplStCd(EvQtmAnwRestoreDto dto) {
		restoreMapper.updateEvQtmAnwXplStCd(dto);
	}
}
