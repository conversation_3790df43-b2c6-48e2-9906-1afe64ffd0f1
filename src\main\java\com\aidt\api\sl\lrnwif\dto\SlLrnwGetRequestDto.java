package com.aidt.api.sl.lrnwif.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-06 9:36:26
 * @modify : date 2024-03-06 9:36:26
 * @desc : 특별학습 콘텐츠 활동조회- 마지막시점 조회 시 필요 파라미터 DTO
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwGetRequestDto {

	@Parameter(name="특별학습ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnId;

	@Parameter(name="특별학습상위노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnNodId;
	
	private String lrnAtvId;
	
	private String usrId;
}
