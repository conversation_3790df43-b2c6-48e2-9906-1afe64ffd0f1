package com.aidt.api.bc.ct.tcr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.ct.dto.BcCtDto;
import com.aidt.common.CommonDao;

@Service
public class BcCtTcrService {

	private final String MAPPER_NAMESPACE = "api.bc.ct.tcr.";

    @Autowired
    private CommonDao commonDao;
    
    /**
     * URL 전송목록 조회 서비스
     *
     * @param dto : BcCtDto
     * @return List<BcCtDto>
     */
	@Transactional
    public List<BcCtDto> selectUrlSendList(BcCtDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectUrlSendList", dto);
    }    
	
	/**
	 * URL 전송목록 등록 요청 서비스
	 *
	 * @param dto : BcCtDto
	 * @return int
	 */
	@Transactional
	public int insertUrlSend(BcCtDto dto) {
		// URL 중복체크 조회
		int index = commonDao.select(MAPPER_NAMESPACE + "selectUrlDupCheck", dto);
		
		// 기존에 저장된 URL과 중복이라면 이전 URL을 삭제후 새로 등록
		if(index > 0) {
			dto.setSeqNo(index);
			commonDao.delete(MAPPER_NAMESPACE + "deleteUrlSend", dto);
		}		
		
		// URL 전송목록 개수 조회
		int urlCount = commonDao.select(MAPPER_NAMESPACE + "selectUrlSendcount", dto);			
		
		// URL 저장시 10개를 초과하면 가장 오래된 것부터 삭제
		if(urlCount == 10) {
			commonDao.delete(MAPPER_NAMESPACE + "deleteFirstUrlSend", dto);
		}		

		return commonDao.insert(MAPPER_NAMESPACE + "insertUrlSend", dto);
	}	
	
	/**
	 * URL 전송목록 삭제 서비스
	 * @param dto : BcCtDto
	 * @return int
	 */
	@Transactional
	public int deleteUrlSend(BcCtDto dto) {
		return commonDao.delete(MAPPER_NAMESPACE + "deleteUrlSend", dto);
	}	
	
}
