package com.aidt.api.al.pl.cm.en;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * AI맞춤 단원리스트 영어
 */

//@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/cm/en")
@Tag(name="[al] AI맞춤 단원리스트 영어", description="AI맞춤 단원리스트 영어")
public class AlEnController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private AlEnService alEnService;
	
	@Autowired 
	private AiRcmTsshQtmCommService commService;
	
	/**
     * 영어 단원리스트 조회
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="영어 단원리스트 조회", description="영어 단원리스트 조회")
    @PostMapping(value = "/selectEnMluList")
    public ResponseDto<Map<String,Object>> selectEnMluList(@Valid @RequestBody AiRcmTsshQtmDto dto, @RequestParam(required = false) String saveYN) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(dto.getUsrId() == null) {
    		dto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(dto.getOptTxbId() == null) {
    		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(alEnService.selectEnMluList(dto, saveYN));
    }
    
    
    /**
	 * AI 영어 개념영상 리스트 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 영어 차시별 개념영상 리스트 조회", description="AI 맞춤 개념영상 리스트 조회")
    @PostMapping(value = "/selectEnCcptVdList")
    public ResponseDto<Map<String, List<AiRcmTsshQtmDto>>> selectEnCcptVdList(@Valid @RequestBody AiRcmTsshQtmDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		if(dto.getTxbId() == null) {
			dto.setTxbId(securityUserDetailDto.getTxbId());
		}

        dto.setSbjCd("EN");
        dto.setLrnrVelTpCdFlag("Y");
		return Response.ok(commService.selectCcptVdListByTc(dto));
    }
	
	
    
    /**
     * 영어 단원리스트 조회
     *
     * @param 
     * @return ResponseDto<Map<String,Object>>
     */
    @Operation(summary="영어 진단평가리포트 조회", description="영어 진단평가리포트 조회")
    @PostMapping(value = "/selectAlPlEvQtmAnwList")
    public ResponseDto<Map<String,Object>> selectAlPlEvQtmAnwList(@Valid @RequestBody AlPlEaEvMainReportDto dto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(dto.getUsrId() == null) {
    		dto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(dto.getOptTxbId() == null) {
    		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(alEnService.selectAlPlEvQtmAnwList(dto));
    }
    
    
    /**
     * 영어 단원리스트 조회
     *
     * @param 
     * @return ResponseDto<Map<String,Object>>
     */
    @Operation(summary="영어 진단평가리포트 조회", description="영어 진단평가리포트 조회")
    @PostMapping(value = "/selectReportTcAvnInfo")
    public ResponseDto<Map<String,Object>> selectReportTcAvnInfo(@Valid @RequestBody AiRcmTsshQtmDto dto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(dto.getUsrId() == null) {
    		dto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(dto.getOptTxbId() == null) {
    		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(alEnService.selectReportTcAvnInfo(dto));
    }
    
    /**
	 * AI 수학 오답갯수조회
	 * 
	 * @return 
	 * */
	//selectIansQtmCnt
	@Tag(name="[al] AI 오답갯수조회 & 단원평가 여부 조회", description="AI 오답갯수조회 & 단원평가 여부 조회")
    @PostMapping(value = "/selectIansQtmSeUgCnt")
    public ResponseDto<Map<String, Object>> selectIansQtmSeUgCnt(@Valid @RequestBody AlMluTcLstInqStuReqDto dto, @RequestParam(required = false) String saveYN) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(alEnService.selectIansQtmSeUgCnt(dto, saveYN));
    }
	
	/**
	 * 단원 평가 여부 
	 * 
	 * @return 
	 * */
	@Tag(name="[al] 단원평가 여부 조회", description="단원평가 여부 조회")
    @PostMapping(value = "/selectEvSeUg")
    public ResponseDto<Object> selectEvSeUg(@Valid @RequestBody AlMluTcLstInqStuReqDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(alEnService.selectEvSeUg(dto));
    }
	
	
}
