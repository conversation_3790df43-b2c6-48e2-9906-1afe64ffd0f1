package com.aidt.api.ea.evcom.ev.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-13
 * @modify date 2024-03-13
 * @desc 평가 관리 - 평가리포트 - 학생 현황 리스트 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvRptUsrsQtmResDto {

	@Parameter(name="평가ID")
	private long evId;

	@Parameter(name="평가구분코드")
	private String evDvCd;

	@Parameter(name="평가명")
	private String evNm;

	@Parameter(name="평가상세구분명")
	private String evDtlDvNm;

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="사용자명")
	private String usrNm;

	@Parameter(name="학생번호")
	private String stuNo;

	@Parameter(name="학습자속도유형코드")
	private String lrnrVelTpCd;

	@Parameter(name="학습자속도유형명")
	private String lrnrVelTpNm;

	@Parameter(name="학습자속도유형 Srt")
	private String lrnrVelTpSrt;

	@Parameter(name="문항ID")
	private long qtmId;

	@Parameter(name="보충심화여부")
	private String sppNtnYn;

	@Parameter(name="보충심화문항 개수")
	private long sppNtnQtmCnt;

	@Parameter(name="문항 순번")
	private int qtmOrdn;

	@Parameter(name="순번")
	private int rowNo;

	@Parameter(name="대화사용여부")
	private String dilgUseYn;

	@Parameter(name="평가완료여부")
	private String evCmplYn;

	@Parameter(name="평가완료일")
	private String smtDtm;

	@Parameter(name="문항풀이시간")
	private int xplTmScnt;

	@Parameter(name="평가시간명")
	private String xplTmScntNm;

	@Parameter(name="최종문제수")
	private String fnlQstCnt;

	@Parameter(name="응시완료 학생수")
	private int txmCmplStuCnt;

	@Parameter(name="평균정답률")
	private String cansRtAvg;

	@Parameter(name="정답여부")
	private String cansYn;

	@Parameter(name="정답수")
	private String cansCnt;

	@Parameter(name="정답률최소")
	private String cansRtMin;
	@Parameter(name="정답률최대")
	private String cansRtMax;

	@Parameter(name="정답률")
	private String cansRt;

	@Parameter(name="오답률")
	private String iansRt;

	@Parameter(name="오답순번")
	private int iansNo;

	@Parameter(name="약점순번")
	private int wkpnNo;

	@Parameter(name="강점순번")
	private int stpnNo;


	@Parameter(name="문항플랫폼 토픽명")
	private String qpTcNm;

	@Parameter(name="지식맵 토픽명 5Depth")
	private String tpcNm;

	@Parameter(name="메모 파일ID")
	private long annxFleId;

	@Parameter(name="풀이상태코드")
	private String xplStCd;

	@Parameter(name="문제유형코드")
	private String qpQstTypCd;

	@Parameter(name="마지막 응시회차")
	private int txmPnLast;
}
