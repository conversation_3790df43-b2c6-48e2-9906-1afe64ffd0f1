package com.aidt.api.ea.stumg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학생 관리 - 교사 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaStuMgTcrDto {

	@Parameter(name="순번")
	private int idx;
	
	@Parameter(name="운영 교과서 ID")
	private String optTxbId;
	
	@Parameter(name="학급ID")
	private String claId;	

	@Parameter(name="학급 인원")
	private int claCnt;
	
	@Parameter(name = "사용자ID")
	private String usrId;

	
}
