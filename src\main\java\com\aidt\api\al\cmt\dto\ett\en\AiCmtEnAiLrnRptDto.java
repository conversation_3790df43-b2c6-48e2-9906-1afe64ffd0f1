package com.aidt.api.al.cmt.dto.ett.en;

import java.util.List;

import com.aidt.api.al.cmt.dto.ett.cm.N02Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnAiLrnRptDto {

    private N02Dto n02;

    private String araLvlCd;

    private String strthTpc;

    private String wknsTpc;

    private String lrnrVelTpCd;

    private N12Dto n12;

    private Boolean isStudying;

    private List<String> araRankList;

    @Builder.Default
    private String usrTpCd = "ST";

}
