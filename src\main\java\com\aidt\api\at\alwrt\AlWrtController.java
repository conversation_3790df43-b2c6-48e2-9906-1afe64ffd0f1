package com.aidt.api.at.alwrt;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.wrt.dto.AIWrtRsLogDto;
import com.aidt.api.al.wrt.dto.AlWrtAcpReqDto;
import com.aidt.api.al.wrt.dto.AlWrtReqDto;
import com.aidt.api.al.wrt.dto.AlWrtResDto;
import com.aidt.api.al.wrt.stu.AlWrtStuService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email  <EMAIL>
 * @create date 2024-11-11 11:27:00
 * @decs AI 첨삭 Controller
 */
@Tag(name="[at] AI 첨삭", description = "AI 첨삭 결과")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token/alwrt")
public class AlWrtController {
	@Autowired
    private AlWrtStuService alWrtStuService;
	
	@Value("${server.meta.textbook.systemCode}")
	private String systemCode;
	
	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;
	
	@Operation(summary="AI 첨삭 결과저장", description="AI 첨삭 결과저장")
    @PostMapping(value = "/alWrtResult")
    public ResponseDto<String> alWrtResult(@RequestBody Map<String, Object> params) {
		/**
		 * 2025.02.24 아키핀 결과 로그 기능 추가
		 */
		boolean result = false;
		StringBuffer err = null;
		StringBuffer returnMsg = null;
		AIWrtRsLogDto logDto = new AIWrtRsLogDto();
		
		String rtnStr = "Success";
		
		try {
		
			/* 1. 데이터 검증 */
			Map<String, Object> resData = (Map<String, Object>) params.get("res");
			
			if(resData == null) {
				throw new RuntimeException("parameter res is null..");
			}
			
			try {
				logDto.setBodyCn(new ObjectMapper().writeValueAsString(resData));
			} catch (JsonProcessingException jpe) {
				logDto.setBodyCn(resData.toString());
			}
			
			/* 1-1. lms 첨삭 정보 확인 */
			Map<String, Object> wrtInfo = (Map<String, Object>) resData.get("wrtInfo");
			
			if(wrtInfo == null) {
				throw new RuntimeException("pareameter res.wrtInfo is null..");
			}
			
			/* 1-1-1. 첨삭 상세 조회 */
			AlWrtReqDto wrtInfoDto = new  AlWrtReqDto();
			wrtInfoDto.setOptTxbId((String)wrtInfo.get("optTxbId"));
			wrtInfoDto.setStuId((String)wrtInfo.get("stuId"));
			wrtInfoDto.setLluKmmpNodId((String)wrtInfo.get("lluKmmpNodId"));
			wrtInfoDto.setTpcKmmpNodId((String)wrtInfo.get("tpcKmmpNodId"));
			
			logDto.setOptTxbId(wrtInfoDto.getOptTxbId());
			logDto.setLluKmmpNodId(wrtInfoDto.getLluKmmpNodId());
			logDto.setTpcKmmpNodId(wrtInfoDto.getTpcKmmpNodId());
			logDto.setStuId(wrtInfoDto.getStuId());
			
	        AlWrtResDto wrtData = alWrtStuService.selectWrtDtl(wrtInfoDto);
	        /* 1-1-2. 첨삭 데이터 검증 */
	        if ( wrtData == null ) {
	        	err = new StringBuffer();
	        	err.append("Query ID >>> selectWrtDtl <<< select 결과 null.");
	        	
	        	returnMsg = new StringBuffer();
	        	returnMsg.append("[wrtInfo]No matching data found.");
	        	
	        	// 데이터 없음
	        	//return Response.fail("[wrtInfo]No matching data found.");
	        } else {
	        	/*
	        	if (!StringUtils.equals(wrtData.getPgrsStCd(), "AP") || StringUtils.isBlank(wrtData.getStuCansCn())) {
	            	// 첨삭 상태가 AP인 경우에만 결과 처리 or 작성된 첨삭 내용이 없음
	            	return Response.fail("[wrtInfo]This data is not valid for the result.");
	            }
	            */
	        	
	        	// 첨삭 상태가 AP인 경우에만 결과 처리
	        	if (!StringUtils.equals(wrtData.getPgrsStCd(), "AP")) {
	        		err = new StringBuffer();
	        		err.append("pgrs_st_cd (상태코드)가 AP가 아닌 오류... >>> getPgrsStCd = " + wrtData.getPgrsStCd());
	        		
	        		returnMsg = new StringBuffer();
	            	returnMsg.append("[wrtInfo]This data is not valid for the result.");
	            }
	        	
	        	// 작성된 첨삭 내용이 없음
	        	if (StringUtils.isBlank(wrtData.getStuCansCn())) {
	            	if(err == null) {
	            		err = new StringBuffer();
	            	} else {
	            		err.append(" / ");
	            	}
	            	
	            	err.append("stu_cans_cn (학생정답내용)의 값이 null or empty.");
	            	
	            	// AP와 동일한 데이터를 보낸다 (둘중에 하나) 이전코드 그대로 전달되도록..
	            	if(returnMsg == null) {
	            		returnMsg = new StringBuffer();
	                	returnMsg.append("[wrtInfo]This data is not valid for the result.");
	            	}
	            }
	        	
	        	/* 2. 첨삭 결과 데이터 검증 */
	            /* 2-1. 첨삭 교정 데이터 검증 */
	    		Map<String, Object> correction = (Map<String, Object>) resData.get("correction");
	    		if (correction == null || correction.get("sent_results") == null) {
	    			if(err == null) {
	    				err = new StringBuffer();
	    			} else {
	    				err.append(" / ");
	    			}
	    			
	    			err.append("correction data is null.");
	    			
	    			if(returnMsg == null) {
	    				returnMsg = new StringBuffer();
	    			} else {
	    				returnMsg.append(" / ");
	    			}
	    			
	    			returnMsg.append("[correction]The request is missing the required data.");
	    			
	    			// 첨삭 교정 데이터 없음
	            	//return Response.fail("[correction]The request is missing the required data.");
	    		}
	    		/* 2-1. 첨삭 피드백 데이터 검증 */
	    		Map<String, Object> feedback = (Map<String, Object>) resData.get("feedback"); 
	    		if (feedback == null || feedback.get("writing_feedback") == null) {
	    			if(err == null) {
	    				err = new StringBuffer();
	    			} else {
	    				err.append(" / ");
	    			}
	    			
	    			err.append("feedback data is null.");
	    			
	    			if(returnMsg == null) {
	    				returnMsg = new StringBuffer();
	    			} else {
	    				returnMsg.append(" / ");
	    			}
	    			
	    			returnMsg.append("[feedback]The request is missing the required data.");
	    			
	    			// 첨삭 교정 데이터 없음
	            	//return Response.fail("[feedback]The request is missing the required data.");
	    		}
	    		
	    		/* 3. 데이터 정제 */
	    		/* 3-1. correction, feedback 데이터 합치기 */
	    		correction.put("writing_feedback", feedback.get("writing_feedback"));
	    		/* 3-2. 데이터 정제 */
	    		Map<String, Object> acpRst = alWrtStuService.setAiEditData(correction);
	    		/* 3-3. java map -> json -> String 변환 */
	    		String acpRstStr = alWrtStuService.convertMapToJsonString(acpRst);
	            String acpRstOriStr = alWrtStuService.convertMapToJsonString(correction);
	            /* 3-4. 각 점수 set */
	            Map<String, Integer> editScore = (Map<String, Integer>) acpRst.get("editScore");
	            int grmrScr = (int) editScore.getOrDefault("grmrScr", 0);
	            int cstnScr = (int) editScore.getOrDefault("cstnScr", 0);
	            int exprScr = (int) editScore.getOrDefault("exprScr", 0);
	            int vocScr = (int) editScore.getOrDefault("vocScr", 0);
	            
	            if(err == null) {
		            /* 4. 결과 저장 */
		            AlWrtReqDto udtDto = new AlWrtReqDto();
		            udtDto.setOptTxbId(wrtInfoDto.getOptTxbId());
		            udtDto.setStuId(wrtInfoDto.getStuId());
		            udtDto.setLluKmmpNodId(wrtInfoDto.getLluKmmpNodId());
		            udtDto.setTpcKmmpNodId(wrtInfoDto.getTpcKmmpNodId()); 
		            udtDto.setAiAnnxCn(acpRstStr);
		            udtDto.setAiAnnxDtl(acpRstOriStr);
		            udtDto.setCstnScr(cstnScr);
		            udtDto.setExprScr(exprScr);
		            udtDto.setVocScr(vocScr);
		            udtDto.setGrmrScr(grmrScr);
		            alWrtStuService.updateWrtMgAiEdit(udtDto);
		            
		            result = true;
	            }
	        }
		} catch(Exception e) {
    		if(err == null) {
    			err = new StringBuffer();
    		} else {
    			err.append(" / ");
    		}
    		
    		err.append(e.getMessage());
    		
    		if(returnMsg == null) {
				returnMsg = new StringBuffer();
			} else {
				returnMsg.append(" / ");
			}
			
			returnMsg.append(e.getMessage());
    	}
        
        try {
        	logDto.setRsYn(result ? "Y" : "N");
           	logDto.setRsMsg(err != null ? err.toString() : null);
           	
        	alWrtStuService.insertAiwriteRsLog(logDto);
        } catch(Exception e) {
        	log.error(e.getMessage());
        }
        
        if(result) {
        	return Response.ok(rtnStr);
        } else {
        	return Response.fail(returnMsg != null ? returnMsg.toString() : "fail...");
        }
	}
	
	@Operation(summary="AI 첨삭 요청 테스트", description="AI 첨삭 요청 테스트")
    @PostMapping(value = "/alWrtReqTest")
    public ResponseDto<String> alWrtReqTest(@RequestBody Map<String, Object> params) {
		String rtnStr = "Success";
		
		String reqUrl = (String) params.get("reqUrl");
		if (StringUtils.isBlank(reqUrl)) { 
			return Response.fail("[reqUrl]The request is missing the required data.");
		}
		
		AlWrtReqDto wrtInfoDto = new  AlWrtReqDto();
		wrtInfoDto.setOptTxbId("266-enm01s105");
		wrtInfoDto.setStuId("7777c777-777a-7777-77c7-stu266000016");
		wrtInfoDto.setLluKmmpNodId("23476");
		wrtInfoDto.setTpcKmmpNodId("23488");
		AlWrtResDto wrtData = alWrtStuService.selectWrtDtl(wrtInfoDto);
		wrtInfoDto.setStuCansCn(wrtData.getStuCansCn());
		wrtInfoDto.setPgrsStCd("AP");
		wrtInfoDto.setLrnTmScnt(0);
		wrtInfoDto.setUsrId("7777c777-777a-7777-77c7-stu266000016");
		// 데이터 초기화
		alWrtStuService.updateWrtMg(wrtInfoDto);
		
		AlWrtAcpReqDto acpReqDto = new AlWrtAcpReqDto();
		acpReqDto.setTopic_id("8221105");
		acpReqDto.setUser_text(wrtData.getStuCansCn());
		Map<String, Object> wrtInfo = new HashMap<String, Object>();
		wrtInfo.put("stuId", "7777c777-777a-7777-77c7-stu266000016");
		wrtInfo.put("optTxbId", "266-enm01s105");
		wrtInfo.put("lluKmmpNodId", "23476");
		wrtInfo.put("tpcKmmpNodId", "23488");
		wrtInfo.put("systemCode", "dev");
		// 아키핀 데이터 전달
		alWrtStuService.callAcpApi(reqUrl, acpReqDto);
		
		return Response.ok(rtnStr);
	}
	
}
