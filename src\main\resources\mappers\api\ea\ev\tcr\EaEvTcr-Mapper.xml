<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.ev.tcr">

	<!--    교사화면 평가리스트 조회  com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto  -->
	<select id="selectEvList"
		resultType="hashMap">
		/** EaEvTcr-Mapper.xml - selectEvList */

		SELECT
			  E.EV_ID				AS evId					-- 평가ID
			, COUNT(1) OVER() 		AS totalCnt 			-- 총 조회 갯수
			, E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
			, E.EV_NM				AS evNm					-- 평가명
			, E.EV_DV_CD			AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
			, E.EV_DTL_DV_CD		AS evDtlDvCd			-- 평가상세구분코드 D1:학기초평가, D2: 단원평가, D3:차시평가, D4:학기말평가
			, EV_DTL.CM_CD_NM       AS evDtlDvNm 			-- 평가상세구분명
			, CASE 	WHEN E.LCKN_YN = 'Y' THEN 'N' -- 교사 잠금 평가 비활성
					WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN 'N' -- 대기
					WHEN IFNULL(E.txmCmplCnt, 0) = 0 AND CURRENT_TIMESTAMP > E.TXM_END_DTM THEN 'N' -- 응시학생 없는데 응시종료일이 지났음
				   	ELSE 'Y'
				  	END 			AS evActvYn				-- 평가활성여부 (응시예정 탭)
			, CASE 	WHEN E.LCKN_YN = 'Y' THEN '잠금'
					WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN '대기' -- 응시기간 도래전, 미잠금
					WHEN E.totalUser = E.txmCmplCnt OR CURRENT_TIMESTAMP > E.TXM_END_DTM THEN '종료'
					ELSE '응시 중'
				   	END 			AS txmStNm				-- 평가상태 응시상태
		    , CASE 	WHEN E.totalUser = E.txmCmplCnt OR CURRENT_TIMESTAMP > E.TXM_END_DTM THEN 'Y' ELSE 'N' END AS evEndYn
			, E.TXM_PTME_SETM_YN 	AS txmPtmeSetmYn        -- 응시기간설정여부
			, CONCAT(DATE_FORMAT(E.TXM_STR_DTM,'%m. %d. '),IF(TIME_FORMAT(E.TXM_STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_STR_DTM,'%h:%i')) AS txmStrDtm -- 응시 시작일
			, CONCAT(DATE_FORMAT(E.TXM_END_DTM,'%m. %d. '),IF(TIME_FORMAT(E.TXM_END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_END_DTM,'%h:%i')) AS txmEndDtm -- 응시 종료일
			, E.XPL_TM_SETM_YN  	AS xplTmSetmYn			-- 풀이시간설정여부
			, E.XPL_TM_SCNT			AS xplTmScntAll			-- 풀이시간 초수 전체초수
			, CAST(FLOOR(E.XPL_TM_SCNT/60) AS Unsigned Integer) AS xplTmMi		-- 풀이시간 분
			, E.XPL_TM_SCNT%60		AS xplTmScnt			-- 풀이시간 초수
			, E.totalUser			AS allStuCnt  			-- 전체 학습생 수
			, IFNULL(E.txmCmplCnt, 0) AS txmCmplCnt			-- 응시완료 학생 수
			, IFNULL(E.cansCnt, 0) 	AS cansCnt 				-- 정답수
			, CASE WHEN IFNULL(E.txmCmplCnt, 0) = 0 OR IFNULL(E.cansCnt, 0) = 0 THEN 0
				   ELSE ROUND(IFNULL(E.cansCnt, 0)/IFNULL(E.txmCmplCnt, 0), 1)
				   END 				AS avgCansCnt 			-- 평균 정답 수
			, E.QST_CNT				AS qstCnt				-- 문제수
			, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수
			, E.LCKN_YN				AS lcknYn				-- 잠금여부
			, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
			, E.USE_YN				AS useYn				-- 사용여부
			, T.LU_LRMP_NOD_ID		AS luLrmpNodId			-- 대단원 ID
			, CASE WHEN IFNULL(NOD_LU.RCSTN_NO, '') = '' THEN NOD_LU.LRMP_NOD_NM
				   ELSE CONCAT(NOD_LU.RCSTN_NO, '. ', NOD_LU.LRMP_NOD_NM)
				   END 				AS luLrmpNodNm 			-- 대단원명
			, T.TC_LRMP_NOD_ID		AS tcLrmpNodId			-- 차시 ID
			, CASE WHEN IFNULL(NOD_TC.RCSTN_NO, '') = '' THEN NOD_TC.LRMP_NOD_NM
				   ELSE CONCAT(NOD_TC.RCSTN_NO, '. ', NOD_TC.LRMP_NOD_NM)
				   END 				AS tcLrmpNodNm 			-- 차시명
 			, IFNULL(RR.RTXM_PN, 0) 						AS txmPn 		-- 응시회차 (교사 본인의 응시정보)
			, IFNULL(R_TCR.EV_CMPL_YN, 'N')					AS evCmplYn		-- 평가완료여부 (교사 본인의 응시정보)
			, IFNULL(RR.EV_CMPL_YN, 'N')					AS evCmplYnRtxm	-- 최종 재응시의 평가완료여부 (교사 본인의 응시정보)
			, IFNULL( (SELECT EV_CMPL_YN
					   FROM LMS_LRM.EA_EV_SPP_NTN_RS
					   WHERE EV_ID = E.EV_ID
					   AND USR_ID = R_TCR.USR_ID
					   AND TXM_PN = IFNULL(RR.RTXM_PN, 0)
					   LIMIT 1
			   ), 'X') AS sppNtnCmplYn -- 보충심화 완료여부 (교사 본인의 응시정보)
 			, (
 				   SELECT ATV.LRN_ATV_ID
	 			   FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV
	 			   WHERE ATV.OPT_TXB_ID = NOD_TC.OPT_TXB_ID
	 			   AND ATV.LRMP_NOD_ID = NOD_TC.LRMP_NOD_ID
	 			   AND ATV.EV_ID = E.EV_ID
	 			   AND ATV.USE_YN = 'Y'
	 			   ORDER BY ATV.RCSTN_ORDN DESC LIMIT 1
 			  ) AS lrnAtvId -- 학습활동 엑티비디 ID
		FROM (
		       SELECT E.EV_ID, E.OPT_TXB_ID
		           , MAX(OT.CLA_ID) CLA_ID
		           , MAX(E.EV_DV_CD) EV_DV_CD
		           , MAX(E.EV_DTL_DV_CD) EV_DTL_DV_CD
		           , MAX(E.EV_NM) EV_NM
		           , MAX(E.LCKN_YN) LCKN_YN
		           , MAX(E.USE_YN) USE_YN
		           , MAX(E.DEL_YN) DEL_YN
		           , MAX(E.RTXM_PMSN_YN) RTXM_PMSN_YN
		           , MAX(E.TXM_PTME_SETM_YN) TXM_PTME_SETM_YN, MAX(E.TXM_STR_DTM) TXM_STR_DTM, MAX(E.TXM_END_DTM) TXM_END_DTM
		           , MAX(E.XPL_TM_SETM_YN) XPL_TM_SETM_YN, MAX(E.XPL_TM_SCNT) XPL_TM_SCNT
		           , MAX(E.QST_CNT) QST_CNT, MAX(E.FNL_QST_CNT) FNL_QST_CNT
		           , MAX(E.MDF_DTM) MDF_DTM
			       , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) AS txmCmplCnt -- 응시완료 학생수
			       , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN ER.CANS_CNT ELSE 0 END) AS cansCnt -- 정답수
				   , SUM(CASE
						WHEN E.EV_DV_CD = 'SE' AND IFNULL(ER.STU_EV_ABLE_YN, 'Y') = 'Y' THEN 1
						WHEN E.EV_DV_CD = 'TE' AND E.ALL_STXQ_YN = 'Y' AND ER.STU_EV_ABLE_YN = 'Y' THEN 1
						WHEN E.EV_DV_CD = 'TE' AND E.ALL_STXQ_YN = 'N' AND ER.STU_EV_ABLE_YN = 'Y' THEN 1
						WHEN E.EV_DV_CD = 'TE' AND E.ALL_STXQ_YN IS NULL AND ER.STU_EV_ABLE_YN = 'Y' THEN 1
						ELSE 0
					END) AS totalUser
			   FROM LMS_LRM.CM_OPT_TXB OT
			   JOIN LMS_LRM.EA_EV E ON E.OPT_TXB_ID = OT.OPT_TXB_ID
			   LEFT JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID AND U.USR_TP_CD = 'ST'
			   LEFT JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID  AND ER.USR_ID = U.USR_ID
			   WHERE OT.OPT_TXB_ID = #{optTxbId}
			   AND E.EV_DV_CD IN ('SE', 'TE') -- SE:교과평가, TE:교사평가
			   AND E.DEL_YN = 'N'
			   AND E.USE_YN = 'Y'		-- 2024-11-08 사용중인 평가만 조회. 오주연님 요청
        	   GROUP BY E.OPT_TXB_ID, E.EV_ID
		) E
		LEFT JOIN LMS_LRM.EA_EV_RS R_TCR ON R_TCR.EV_ID = E.EV_ID AND R_TCR.USR_ID = #{usrId} -- 교사 문제보기 상태 확인용
		LEFT JOIN (
				SELECT
						RR.EV_ID, RR.USR_ID, MAX(RR.RTXM_PN) RTXM_PN
				FROM LMS_LRM.EA_EV_RS_RTXM RR
				WHERE EXISTS (SELECT 1 FROM LMS_LRM.EA_EV E2 WHERE E2.OPT_TXB_ID = #{optTxbId} AND E2.EV_DV_CD IN ('SE', 'TE') AND E2.DEL_YN = 'N' AND E2.EV_ID = RR.EV_ID)
				AND RR.USR_ID = #{usrId}
				GROUP BY RR.EV_ID, RR.USR_ID
		) RR_MAX ON RR_MAX.EV_ID = R_TCR.EV_ID AND RR_MAX.USR_ID = R_TCR.USR_ID
		LEFT JOIN LMS_LRM.EA_EV_RS_RTXM RR ON RR.EV_ID = RR_MAX.EV_ID AND RR.USR_ID = RR_MAX.USR_ID AND RR.RTXM_PN = RR_MAX.RTXM_PN
		LEFT JOIN LMS_LRM.CM_CM_CD EV_DTL ON EV_DTL.URNK_CM_CD = 'EV_DTL_DV_CD' AND EV_DTL.CM_CD = E.EV_DTL_DV_CD

		<choose>
			<when test = '!"".equals(srhLuLrmpNodId)'>
				LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD IN ('SE', 'TE') -- 교과평가, 선생님 평가(TE) 단원만 조회
			</when>
			<otherwise>
				LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD IN ('SE') -- 교과평가 단원만 조회
			</otherwise>
		</choose>
		
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_MLU ON NOD_MLU.OPT_TXB_ID = T.MLU_OPT_TXB_ID AND NOD_MLU.LRMP_NOD_ID = T.MLU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_SLU ON NOD_SLU.OPT_TXB_ID = T.SLU_OPT_TXB_ID AND NOD_SLU.LRMP_NOD_ID = T.SLU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = T.TC_LRMP_NOD_ID
		WHERE 1 = 1
		<if test = 'srhEvTp != null and !"".equals(srhEvTp)'><!-- 평가유형 ST, UD, FO, TO,	UG, ET-->
    	AND E.EV_DTL_DV_CD = #{srhEvTp}
    	</if>
    	<if test = '!"".equals(srhLuLrmpNodId)'><!-- 대단원 dropdown -->
    	AND T.LU_LRMP_NOD_ID = #{srhLuLrmpNodId}
    	</if>
		<choose>
    	<when test = '"0".equals(tbscDvCd)'><!-- 진행중 탭 > 노출설정 여부, 응시기간 종료전 -->
 		AND E.totalUser > IFNULL(E.txmCmplCnt, 0)
 		AND (E.TXM_PTME_SETM_YN = 'N' OR (E.TXM_PTME_SETM_YN = 'Y' AND CURRENT_TIMESTAMP BETWEEN E.TXM_STR_DTM AND E.TXM_END_DTM))
  	   	</when>
    	<when test = '"1".equals(tbscDvCd)'><!-- 종료 탭 > 응시완료 , 응시 가능기간 종료-->
		AND (E.totalUser = E.txmCmplCnt OR CURRENT_TIMESTAMP > E.TXM_END_DTM)
    	</when>
		</choose>
		<choose>
    	<when test = '"2".equals(srtDvCd)'><!-- 마감일순 -->
		ORDER BY CASE 	WHEN E.TXM_PTME_SETM_YN = 'Y' AND E.EV_DV_CD = 'TE' THEN 0
				   		ELSE 1
				   		END -- 마감일 있는데이터 > 교사평가 순
			   , CASE 	WHEN E.TXM_PTME_SETM_YN = 'Y' THEN E.TXM_END_DTM
				   		ELSE '9999-12-30 00:00:00'
				   		END -- 마감일 있는데이터 > 마감일 근접순
			   , CASE WHEN E.EV_DV_CD = 'SE' THEN
							 CASE WHEN E.EV_DTL_DV_CD = 'ST' THEN 0
							 	  WHEN E.EV_DTL_DV_CD = 'ET' THEN 99999
							 	  ELSE NOD_LU.RCSTN_ORDN
							 	  END
				   		ELSE -1
				   		END
			   , IF(E.EV_DV_CD = 'TE', E.MDF_DTM, NULL) DESC  -- 마감일 없는데이터 > 교사평가 최신순
			   , NOD_LU.RCSTN_ORDN 						-- 대단원 재구성 순서
			   , EV_DTL.SRT_ORDN 						-- 평가지유형 순서
			   , IFNULL(NOD_MLU.RCSTN_ORDN, 99999)		-- 중단원 재구성 순서
			   , IFNULL(NOD_SLU.RCSTN_ORDN, 99999)		-- 소단원 재구성 순서
			   , IFNULL(NOD_TC.RCSTN_ORDN, 99999)		-- 차시단원 재구성 순서
			   , E.EV_NM DESC							-- 평가지명 내림차순
    	</when>
    	<otherwise><!-- 정렬구분 - 단원순 - 기본값 srtDvCd.equals("1")  -->
		ORDER BY CASE WHEN E.EV_DV_CD = 'SE' THEN
							 CASE WHEN E.EV_DTL_DV_CD = 'ST' THEN 0
							 	  WHEN E.EV_DTL_DV_CD = 'ET' THEN 99999
							 	  ELSE NOD_LU.RCSTN_ORDN
							 	  END
				   		ELSE -1
				   		END
			   , IF(E.EV_DV_CD = 'TE', E.MDF_DTM, NULL) DESC
			   , NOD_LU.RCSTN_ORDN 						-- 대단원 재구성 순서
			   , EV_DTL.SRT_ORDN 						-- 평가지유형 순서
			   , IFNULL(NOD_MLU.RCSTN_ORDN, 99999)		-- 중단원 재구성 순서
			   , IFNULL(NOD_SLU.RCSTN_ORDN, 99999)		-- 소단원 재구성 순서
			   , IFNULL(NOD_TC.RCSTN_ORDN, 99999)		-- 차시단원 재구성 순서
			   , E.EV_NM DESC							-- 평가지명 내림차순
    	</otherwise>
		</choose>
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}

	</select>

	<!--    교사 평가 편집 리스트 조회 (정렬문제로 분리)    -->
	<select id="selectEvEditList"
		resultType="com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto">
		/** EaEvTcr-Mapper.xml - selectEvEditList - 교사 평가 편집 리스트 조회 */

		 SELECT
			  E.EV_ID				AS evId					-- 평가ID
			, E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
			, CASE WHEN E.EV_DV_CD = 'TE' OR E.EV_DTL_DV_CD IN ('ST','ET') THEN 0 ELSE NOD_LU.RCSTN_ORDN END AS editGrpOrdn
			, ROW_NUMBER() OVER(PARTITION BY CASE WHEN E.EV_DV_CD = 'TE' OR E.EV_DTL_DV_CD IN ('ST','ET') THEN 0 ELSE NOD_LU.RCSTN_ORDN END) AS editGrpOrdnRowNo
			, E.EV_NM				AS evNm					-- 평가명
			, E.EV_DV_CD			AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
			, E.EV_DTL_DV_CD		AS evDtlDvCd			-- 평가상세구분코드 D1:학기초평가, D2: 단원평가, D3:차시평가, D4:학기말평가
			, CASE 	WHEN E.EV_DV_CD = 'TE' THEN '-' ELSE EV_DTL.CM_CD_NM END AS evDtlDvNm -- 평가상세구분명
			, CASE 	WHEN E.LCKN_YN = 'Y' THEN '잠금'
					WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN '대기' -- 응시기간 도래전, 미잠금
					WHEN E.totalUser = IFNULL(E.txmCmplCnt, 0) OR CURRENT_TIMESTAMP > E.TXM_END_DTM THEN '응시 종료'
					-- WHEN IFNULL(E.txmCmplCnt, 0) = 0 THEN '대기'
					ELSE '응시 중'
				   	END 				AS txmStNm				-- 평가상태 응시상태
			, E.TXM_PTME_SETM_YN 	AS txmPtmeSetmYn        -- 응시기간설정여부
			, CONCAT(DATE_FORMAT(E.TXM_STR_DTM,'%m. %d. '),IF(TIME_FORMAT(E.TXM_STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_STR_DTM,'%h:%i')) AS txmStrDtm -- 응시 시작일
			, CONCAT(DATE_FORMAT(E.TXM_END_DTM,'%m. %d. '),IF(TIME_FORMAT(E.TXM_END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_END_DTM,'%h:%i')) AS txmEndDtm -- 응시 종료일
			, E.XPL_TM_SETM_YN  	AS xplTmSetmYn			-- 풀이시간설정여부
			, E.XPL_TM_SCNT			AS xplTmScntAll			-- 풀이시간 초수 전체초수
			, E.QST_CNT				AS qstCnt				-- 문제수
			, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수
			, E.totalUser			AS allStuCnt			-- 전체 학습생 수
			, IFNULL(E.txmCmplCnt, 0) AS txmCmplCnt			-- 응시완료자수
			, E.LCKN_YN				AS lcknYn				-- 잠금여부
			, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
			, E.USE_YN  	AS useYn				-- 사용여부
			, CASE WHEN IFNULL(NOD_LU.RCSTN_NO, '') = '' THEN NOD_LU.LRMP_NOD_NM
				   ELSE CONCAT(NOD_LU.RCSTN_NO, '. ', NOD_LU.LRMP_NOD_NM)
				   END AS luLrmpNodNm -- 대단원명
			, T.TC_LRMP_NOD_ID		AS tcLrmpNodId			-- 차시 ID
			, CASE WHEN IFNULL(NOD_TC.RCSTN_NO, '') = '' THEN NOD_TC.LRMP_NOD_NM
				   ELSE CONCAT(NOD_TC.RCSTN_NO, '. ', NOD_TC.LRMP_NOD_NM)
				   END AS tcLrmpNodNm -- 대단원명
			, E.TCR_BA_YN as tcrBaYn -- 교사 기본값(Y, N: 사용여부 ,NA : 사용여부 화면에서 변경불가)
		FROM (
			SELECT E.EV_ID, E.OPT_TXB_ID
		           , MAX(OT.CLA_ID) CLA_ID
		           , MAX(E.EV_DV_CD) EV_DV_CD
		           , MAX(E.EV_DTL_DV_CD) EV_DTL_DV_CD
		           , MAX(E.EV_NM) EV_NM
		           , MAX(E.LCKN_YN) LCKN_YN
		           , MAX(E.USE_YN) USE_YN
		           , MAX(E.DEL_YN) DEL_YN
		           , MAX(E.RTXM_PMSN_YN) RTXM_PMSN_YN
		           , MAX(E.TXM_PTME_SETM_YN) TXM_PTME_SETM_YN, MAX(E.TXM_STR_DTM) TXM_STR_DTM, MAX(E.TXM_END_DTM) TXM_END_DTM
		           , MAX(E.XPL_TM_SETM_YN) XPL_TM_SETM_YN, MAX(E.XPL_TM_SCNT) XPL_TM_SCNT
		           , MAX(E.QST_CNT) QST_CNT, MAX(E.FNL_QST_CNT) FNL_QST_CNT
		           , MAX(E.MDF_DTM) MDF_DTM
			       , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) AS txmCmplCnt -- 응시완료 학생수
			       , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN ER.CANS_CNT ELSE 0 END) AS cansCnt -- 정답수
			       , SUM(CASE WHEN IFNULL(ER.STU_EV_ABLE_YN, 'Y') = 'Y' THEN 1 ELSE 0 END) AS totalUser
			       , MAX(E.TCR_BA_YN) TCR_BA_YN
			   FROM LMS_LRM.CM_OPT_TXB OT
			   LEFT OUTER JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID AND U.USR_TP_CD = 'ST'
			   JOIN LMS_LRM.EA_EV E ON E.OPT_TXB_ID = OT.OPT_TXB_ID
			   LEFT JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID  AND ER.USR_ID = U.USR_ID
			   WHERE OT.OPT_TXB_ID = #{optTxbId}
			   AND E.EV_DV_CD IN ('SE', 'TE') -- SE:교과평가, TE:교사평가
			   AND E.DEL_YN = 'N'
        	   GROUP BY E.OPT_TXB_ID, E.EV_ID
		) E
		LEFT JOIN LMS_LRM.CM_CM_CD EV_DTL ON EV_DTL.URNK_CM_CD = 'EV_DTL_DV_CD' AND EV_DTL.CM_CD = E.EV_DTL_DV_CD
		LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_MLU ON NOD_MLU.OPT_TXB_ID = T.MLU_OPT_TXB_ID AND NOD_MLU.LRMP_NOD_ID = T.MLU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_SLU ON NOD_SLU.OPT_TXB_ID = T.SLU_OPT_TXB_ID AND NOD_SLU.LRMP_NOD_ID = T.SLU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = T.TC_LRMP_NOD_ID
		ORDER BY editGrpOrdn
			   , IF(E.EV_DV_CD = 'TE', E.MDF_DTM, NULL) DESC
			   , NOD_LU.RCSTN_ORDN 						-- 대단원 재구성 순서
			   , EV_DTL.SRT_ORDN 						-- 평가지유형 순서
			   , IFNULL(NOD_MLU.RCSTN_ORDN, 99999)		-- 중단원 재구성 순서
			   , IFNULL(NOD_SLU.RCSTN_ORDN, 99999)		-- 소단원 재구성 순서
			   , IFNULL(NOD_TC.RCSTN_ORDN, 99999)		-- 차시단원 재구성 순서
			   , E.EV_NM DESC							-- 평가명 내림차순
	</select>

    <!--    학습수준별 학생정보 조회  -->
	<select id="selectEvLrnLvStuList"  parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvEvIdReqDto" resultType="hashMap">
       /** EaEvTcr-Mapper.xml - selectEvLrnLvStuList - 학습수준별 학생정보 조회*/

        SELECT USR.USR_ID	AS usrId
		     , USR.USR_NM	AS usrNm
			 , IFNULL(USR.LRNR_VEL_TP_CD, 'BA') AS lrnrVelTpCd -- 학습수준유형코드
			 , COUNT(USR.USR_ID) OVER() AS usrTotalCnt  -- 전체 학생 수
			 , COUNT(USR.USR_ID) OVER(PARTITION BY IFNULL(USR.LRNR_VEL_TP_CD, 'BA')) AS lrnrVelCnt -- 학습수준유형별 학생수
			 , ROW_NUMBER() OVER(PARTITION BY IFNULL(USR.LRNR_VEL_TP_CD, 'BA') ORDER BY USR.USR_NM) AS srtOrdn
			 , IFNULL(ER.STU_EV_ABLE_YN, 'N') AS stuEvAbleYn
		FROM LMS_LRM.CM_USR USR
		LEFT JOIN LMS_LRM.EA_EV_RS ER ON ER.USR_ID = USR.USR_ID AND ER.EV_ID = #{evId}
		WHERE USR.CLA_ID = #{claId}
		AND USR.USR_TP_CD = 'ST'

	</select>


    <!--    평가정보 조회    -->
	<select id="selectEvInfo" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto">
       /** EaEvTcr-Mapper.xml - selectEvInfo - 평가정보 조회*/

		SELECT
			  E.EV_ID				AS evId					-- 평가ID
			, E.CLA_EV_ID			AS claEvId				-- 학급평가ID
			, E.EVSH_ID				AS evshId				-- 평가지ID
			, E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
			, E.EV_DV_CD			AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
			, E.EV_DTL_DV_CD		AS evDtlDvCd			-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = E.EV_DTL_DV_CD) evDtlDvNm -- 평가상세구분명
			, E.EV_NM				AS evNm					-- 평가명
			, E.TXM_PTME_SETM_YN 	AS txmPtmeSetmYn        -- 응시기간설정여부
			, DATE_FORMAT(E.TXM_STR_DTM, '%Y-%m-%d %H:%i')	AS txmStrDtm -- 응시 시작일
			, DATE_FORMAT(E.TXM_END_DTM, '%Y-%m-%d %H:%i')	AS txmEndDtm -- 응시 종료일
			, CONCAT(DATE_FORMAT(E.TXM_STR_DTM,'%Y.%m.%d '), IF(TIME_FORMAT(E.TXM_STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_STR_DTM,'%h:%i')) AS txmStrDtmCalender
			, CONCAT(DATE_FORMAT(E.TXM_END_DTM,'%Y.%m.%d '), IF(TIME_FORMAT(E.TXM_END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_END_DTM,'%h:%i')) AS txmEndDtmCalender
			, E.XPL_TM_SETM_YN  	AS xplTmSetmYn			-- 풀이시간설정여부
			, E.XPL_TM_SCNT			AS xplTmScnt			-- 풀이시간 초수
			, CAST(FLOOR(E.XPL_TM_SCNT/60) AS Unsigned Integer) AS xplTmMi		-- 풀이시간 분
			, E.QST_CNT				AS qstCnt				-- 문제수
			, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수
			, IFNULL(R.txmCmplCnt, 0) AS txmCmplCnt			-- 응시완료자수
			, E.LCKN_YN				AS lcknYn				-- 잠금여부
			, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
			, E.USE_YN				AS useYn				-- 사용여부
		FROM LMS_LRM.EA_EV E
		LEFT JOIN (
			  SELECT R.EV_ID
			       , SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END) AS txmCmplCnt -- 응시완료 학생수
			       , SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN CANS_CNT ELSE 0 END) AS cansCnt -- 정답수
			  FROM LMS_LRM.CM_USR U
			  JOIN LMS_LRM.EA_EV_RS R ON R.USR_ID = U.USR_ID
			  WHERE U.CLA_ID = #{claId}
			  AND U.USR_TP_CD = 'ST'
			  GROUP BY R.EV_ID
		) R ON R.EV_ID = E.EV_ID
		WHERE E.EV_ID = #{evId}

	</select>

    <!--    평가난이도분포도정보 조회    -->
	<select id="selectEvDffdCstnInfo" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvDffdCstnDto">
       /** EaEvTcr-Mapper.xml - selectEvDffdCstnInfo - 평가난이도분포도정보 조회*/

		SELECT
			  E.EV_ID				AS evId					-- 평가ID
			, E.EV_DFFD_DV_CD		AS evDffdDvCd			-- 난이도 코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD' AND CM_CD = E.EV_DFFD_DV_CD) evDffdDvCdNm -- 난이도명
			, E.EV_DFFD_DSB_CNT		AS evDffdDsbCnt			-- 난이도별 분포도 갯수
		FROM LMS_LRM.EA_EV_DFFD_CSTN E
		WHERE E.EV_ID = #{evId}
		ORDER BY E.EV_DFFD_DV_CD DESC
	</select>

	<!--    평가난이도분포도정보 조회 (삭제값 제외 )   -->
	<select id="selectEvDffdCstnInfoByActiveQtm" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvDffdCstnDto">
		/** EaEvTcr-Mapper.xml - selectEvDffdCstnInfoByActiveQtm - 평가난이도분포도정보 조회 (삭제값 제외) */
		SELECT
			E.EV_ID AS evId,
			E.EV_DFFD_DV_CD AS evDffdDvCd,
			(SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD' AND CM_CD = E.EV_DFFD_DV_CD) evDffdDvCdNm,
			COUNT(Q.QTM_ID) AS evDffdDsbCnt
		FROM LMS_LRM.EA_EV_DFFD_CSTN E
				 LEFT JOIN LMS_LRM.EA_EV_QTM Q
						   ON E.EV_ID = Q.EV_ID
							   AND E.EV_DFFD_DV_CD = Q.QTM_DFFD_DV_CD
							   AND Q.DEL_YN = 'N'
		WHERE E.EV_ID = #{evId}
		GROUP BY E.EV_ID, E.EV_DFFD_DV_CD
		ORDER BY E.EV_DFFD_DV_CD DESC;
    </select>

    <!--    평가시험범위정보 조회    -->
	<select id="selectEvTsRngeInfo" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvTsRngeDto">
       /** EaEvTcr-Mapper.xml - selectEvTsRngeInfo - 평가시험범위정보 조회*/

		SELECT
			  E.EV_ID				AS evId					-- 평가ID
			, E.TS_RNGE_SEQ_NO		AS tsRngeSeqNo			-- 시험범위순번
			, E.LU_LRMP_NOD_ID      AS luLrmpNodId          -- 대단원학습맵노드ID
			, E.LU_LRMP_NOD_NM      AS luLrmpNodNm          -- 대단원학습맵노드명
			, IFNULL(LLU.RCSTN_ORDN, '') 		AS luRcstnOrdn       -- 대단원정렬순서 (평가 수정화면에서 필요)
			, IFNULL(E.MLU_LRMP_NOD_ID, '')     AS mluLrmpNodId      -- 중단원학습맵노드ID
			, IFNULL(E.MLU_LRMP_NOD_NM, '')     AS mluLrmpNodNm      -- 중단원학습맵노드명
			, IFNULL(MLU.RCSTN_ORDN, '') 		AS mluRcstnOrdn      -- 중단원정렬순서 (평가 수정화면에서 필요)
			, IFNULL(E.SLU_LRMP_NOD_ID, '')     AS sluLrmpNodId      -- 소단원학습맵노드ID
			, IFNULL(E.SLU_LRMP_NOD_NM, '')     AS sluLrmpNodNm      -- 소단원학습맵노드명
			, IFNULL(SLU.RCSTN_ORDN, '') 		AS sluRcstnOrdn      -- 소단원정렬순서 (평가 수정화면에서 필요)
			, IFNULL(E.TC_LRMP_NOD_ID, '')      AS tcLrmpNodId       -- 차시단원학습맵노드ID
			, IFNULL(E.TC_LRMP_NOD_NM, '')      AS tcLrmpNodNm       -- 차시단원학습맵노드명
			, IFNULL(E.TPC_ID, '')              AS tpcId             -- 토픽ID
		FROM LMS_LRM.EA_EV_TS_RNGE E
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN LLU ON LLU.LRMP_NOD_ID = E.LU_LRMP_NOD_ID  AND LLU.OPT_TXB_ID = #{optTxbId}
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN MLU ON MLU.LRMP_NOD_ID = E.MLU_LRMP_NOD_ID AND MLU.OPT_TXB_ID = #{optTxbId}
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN SLU ON SLU.LRMP_NOD_ID = E.SLU_LRMP_NOD_ID AND SLU.OPT_TXB_ID = #{optTxbId}
		WHERE E.EV_ID = #{evId}
		ORDER BY E.TS_RNGE_SEQ_NO
	</select>

    <!--    교사 평가추가 > 난이도별 문항리스트 조회 > 단원 리스트로 지식맵 토픽ID 리스트 조회 -->
    <select id="selectLuSearchTpcIdList" resultType="hashMap">

		SELECT
		       K_NOD5.KMMP_NOD_ID AS tpcId
		FROM (
			<foreach item="item" index ="index" collection="tsRngeList" separator="union">
				SELECT
						L_NOD4.LRMP_NOD_ID AS NOD_ID_DPTH4
				FROM LMS_CMS.BC_LRMP BL
				JOIN LMS_CMS.BC_LRMP_NOD L_NOD4 ON L_NOD4.LRMP_ID = BL.LRMP_ID
				JOIN LMS_CMS.BC_LRMP_NOD L_NOD3 ON L_NOD3.LRMP_NOD_ID = L_NOD4.URNK_LRMP_NOD_ID
				JOIN LMS_CMS.BC_LRMP_NOD L_NOD2 ON L_NOD2.LRMP_NOD_ID = L_NOD3.URNK_LRMP_NOD_ID
				JOIN LMS_CMS.BC_LRMP_NOD L_NOD1 ON L_NOD1.LRMP_NOD_ID = L_NOD2.URNK_LRMP_NOD_ID
				WHERE BL.TXB_ID = #{txbId}
				AND L_NOD4.DPTH = 4
				AND IFNULL(L_NOD4.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
				AND IFNULL(L_NOD3.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
				AND IFNULL(L_NOD2.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
				AND IFNULL(L_NOD1.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
				<choose>
					<when test='item.luLrmpNodId != null and item.luLrmpNodId neq ""'>
				    AND L_NOD1.LRMP_NOD_ID = #{item.luLrmpNodId}
				    </when>
				    <otherwise>
				    AND L_NOD1.LRMP_NOD_ID = '0'
				    </otherwise>
				</choose>
	            <if test='item.mluLrmpNodId != null and item.mluLrmpNodId neq ""'>
					AND L_NOD2.LRMP_NOD_ID = #{item.mluLrmpNodId}
	         			</if>
	            <if test='item.sluLrmpNodId != null and item.sluLrmpNodId neq ""'>
					AND L_NOD3.LRMP_NOD_ID = #{item.sluLrmpNodId}
	         	</if>
		    </foreach>
		) L_NOD4
		JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.LRMP_NOD_ID = L_NOD4.NOD_ID_DPTH4
		JOIN LMS_CMS.BC_KMMP_NOD K_NOD4 ON K_NOD4.KMMP_NOD_ID = NOD_MPN.KMMP_NOD_ID
		JOIN LMS_CMS.BC_KMMP_NOD K_NOD5 ON K_NOD5.URNK_KMMP_NOD_ID = K_NOD4.KMMP_NOD_ID
		WHERE IFNULL(K_NOD4.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
		AND IFNULL(K_NOD5.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외

        /* 평가 - 박원희 - EaEvTcr-Mapper.xml - selectLuSearchTpcIdList - 난이도별 문항리스트 조회 > 단원 리스트로 지식맵 토픽ID 리스트 조회  */

    </select>

    <!--  평가 추가 문항 리스트 조회 -->
    <select id="selectAddQtmList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmInfoDto">

        SELECT
		        QQ.QP_QTM_ID                        AS qtmId,
		        ROW_NUMBER() OVER(ORDER BY QQ.QP_DFFD_CD, QQ.QP_QTM_ID) AS qtmOrdn, -- 문항순번
		        'N'                        			AS delYn, 				-- 삭제여부
		        NULL                        		AS delDtm,    			-- 삭제일
		        QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
		        QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
		        QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
		        QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
		        QLL.QP_LLU_ID                		AS lluId,    			-- 대단원 ID
		        QLL.LLU_NM                    		AS lluNm,    			-- 대단원 명
		        QTL.QP_TPC_LU_ID                    AS topicId,    			-- 토픽(차시) ID
		        QTL.QP_TPC_LU_NM                    AS topicNm,    			-- 토픽(차시) 명
		        K_NOD5.tpcID						AS topicIdKmmp, 		-- 지식맵 토픽ID
		        K_NOD5.tpcNm	 					AS topicNmKmmp, 		-- 지식맵 토픽명
			    K_NOD5.SRT_ORDN						AS topicOrdn,			-- 지식맵 토픽 정렬순서
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		-- 템플릿 xml 내용
	            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
	            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
	            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
	            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
	            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
	            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
	            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
	            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
	            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QPI.QP_PSSG_HTML_TMPL_CN, '') AS passageHtmlTemplate, -- 지문HtmlTemplate
		        IFNULL(QPI.QP_PSSG_TMPL_XML_CN, '') AS passageTemplateXml, 	-- 지문Template xml 내용
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType,	-- 문항 등록 형태(hpw, html)
		        '' AS questionUrl, 	-- 질문 url
		        '' AS answerUrl,	-- 정답 url
		        '' AS explainUrl,	-- 해설 url
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl -- HTML(ZIP) url
	          , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    -- 문항 키워드명
	      	  , ROUND(IFNULL(CLA.CANS_RT_CLA,0), 1) AS cansRt 	-- 정답률
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode -- 형판코드
       		  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName -- 형판명
			  , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
			  , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
		FROM (
	            <foreach item="item" index ="index" collection="qtmIdList" separator="union">
					SELECT
						    KMMP_NOD_ID AS tpcID
						  , KMMP_NOD_NM AS tpcNm
						  , #{item.qtmId} AS qtmId
						  , SRT_ORDN      AS SRT_ORDN
					FROM LMS_CMS.BC_KMMP_NOD K_NOD5
					WHERE K_NOD5.KMMP_NOD_ID = #{item.tpcId}
	            </foreach>
		) K_NOD5
        JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = K_NOD5.qtmId
        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    			= QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    			= QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   			= QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   	= QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID 	= QPQ.QP_PSSG_ID
        LEFT JOIN LMS_CMS.QP_LLU QLL ON QLL.QP_LLU_ID 			= QQA.QP_LLU_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  	= QQA.QP_TPC_LU_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	    LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
		LEFT JOIN (-- 반 평균정답률은 첫응시 데이터로
			SELECT EQ.TPC_ID
			     , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100) AS CANS_RT_CLA
		    FROM LMS_LRM.EA_EV E
		    JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		    JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
		    WHERE E.OPT_TXB_ID = #{optTxbId}
		    AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR AU WHERE AU.USR_ID = EQA.USR_ID AND AU.USR_TP_CD = 'ST' )
		    GROUP BY EQ.TPC_ID
		) CLA ON CLA.TPC_ID = K_NOD5.tpcID


        /* 평가 - 박원희 - EaEvTcr-Mapper.xml - selectAddQtmList - 평가 추가 문항 리스트 조회 */
    </select>


    <!--    유사 문항 리스트 조회-->
    <select id="selectSmlrQtmList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmInfoDto">

        SELECT
	            QQ.QP_QTM_ID                       	AS qtmId,  				-- 문항Id
	            ROW_NUMBER() OVER(ORDER BY QQ.QP_DFFD_CD DESC, QQ.QP_QTM_ID) AS qtmOrdn, -- 문항순번
	            'N'                        			AS delYn, 				-- 삭제여부
	            NULL                        		AS delDtm,    			-- 삭제일
	            QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
	            QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
	            QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
	            QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
	            QLL.QP_LLU_ID                AS lluId,    			-- 대단원 ID
	            QLL.LLU_NM                    		AS lluNm,    			-- 대단원 명
	            QTL.QP_TPC_LU_ID                    AS topicId,    			-- 토픽(차시) ID
	            QTL.QP_TPC_LU_NM                    AS topicNm,    			-- 토픽(차시) 명
		        TQ.TPC_ID							AS topicIdKmmp, 		-- 지식맵 토픽ID
		        IFNULL((SELECT KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD K_NOD5 WHERE K_NOD5.KMMP_NOD_ID = TQ.TPC_ID),'')	 AS topicNmKmmp, -- 지식맵 토픽명
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		-- 템플릿 xml 내용
	            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
	            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
	            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
	            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
	            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
	            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
	            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
	            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
	            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QPI.QP_PSSG_HTML_TMPL_CN, '') AS passageHtmlTemplate, -- 지문HtmlTemplate
		        IFNULL(QPI.QP_PSSG_TMPL_XML_CN, '') AS passageTemplateXml, 	-- 지문Template xml 내용
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType,	-- 문항 등록 형태(hpw, html)
		        '' AS questionUrl, 	-- 질문 url
		        '' AS answerUrl,	-- 정답 url
		        '' AS explainUrl,	-- 해설 url
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl -- HTML(ZIP) url
	          , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    -- 문항 키워드명
		      , ROUND(IFNULL(CLA.CANS_RT_CLA,0), 1) AS cansRt 	-- 정답률
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode -- 형판코드
	      	  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName -- 형판명
			  , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
			  , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
       FROM (
 				SELECT TQ.TPC_ID, TQ.QTM_ID
 				FROM (
					SELECT
							 K_ATV.KMMP_NOD_ID AS TPC_ID
					       , K_ATV.CTN_CD AS QTM_ID
					FROM LMS_CMS.BC_AI_LRN_ATV_CTN K_ATV
					WHERE K_ATV.CTN_TP_CD = 'QU' -- 문항
					AND K_ATV.KMMP_NOD_ID = #{tpcId}
				UNION ALL
					SELECT
						     B_EQM.TPC_ID
					       , B_EQM.QTM_ID
					FROM LMS_CMS.BC_EVSH_QTM_MPN B_EQM
					WHERE EXISTS(SELECT 1 FROM LMS_CMS.BC_EVSH WHERE EVSH_ID = B_EQM.EVSH_ID AND EVSH_TP_CD = 'DI') -- 교사 전용 문항
					AND B_EQM.TPC_ID = #{tpcId}
				) TQ
				GROUP BY TQ.TPC_ID, TQ.QTM_ID
		) TQ
        JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID 		   = TQ.QTM_ID
        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID = QPQ.QP_PSSG_ID
        LEFT JOIN LMS_CMS.QP_LLU QLL ON QLL.QP_LLU_ID = QQA.QP_LLU_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	    LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
		LEFT JOIN (-- 반 평균정답률은 첫응시 데이터로
			SELECT EQ.TPC_ID
			     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS CANS_RT_CLA
		    FROM LMS_LRM.EA_EV E
		    JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		    JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
		    WHERE E.OPT_TXB_ID = #{optTxbId}
		    AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR AU WHERE AU.USR_ID = EQA.USR_ID AND AU.USR_TP_CD = 'ST' )
		    GROUP BY EQ.TPC_ID
		) CLA ON CLA.TPC_ID = TQ.TPC_ID
        WHERE QQ.DEL_YN = 'N'
        <if test = '!"".equals(qtmDffdCd)'>
        AND QQ.QP_DFFD_CD = #{qtmDffdCd}
		</if>
        AND QQ.QP_QTM_ID != #{qtmId}


        /* 평가 - 박원희 - EaEvTcr-Mapper.xml - selectSmlrQtmList - 유사 문항 리스트 조회 */
    </select>

    <!--  평가추가/수정  문항 리스트 조회-->
    <select id="selectEvQtmInfoList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmInfoDto">
        SELECT
	            QQ.QP_QTM_ID                       	AS qtmId,  				-- 문항Id
	            ROW_NUMBER() OVER(ORDER BY Q.QTM_ORDN) AS qtmOrdn,			-- 문항순번
	            Q.DEL_YN                        	AS delYn,				-- 삭제여부
	            Q.DEL_DTM                        	AS delDtm,    			-- 삭제일
	            QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
	            QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
	            QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
	            QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
	            QLL.QP_LLU_ID                		AS lluId,    			-- 대단원 ID
	            QLL.LLU_NM                    		AS lluNm,    			-- 대단원 명
	            QTL.QP_TPC_LU_ID                    AS topicId,    			-- 토픽(차시) ID
	            QTL.QP_TPC_LU_NM                    AS topicNm,    			-- 토픽(차시) 명
	            Q.TPC_ID							AS topicIdKmmp,    		-- 토픽(지식맵 5뎁스) ID
	            (SELECT KN.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD KN WHERE KN.KMMP_NOD_ID = Q.TPC_ID) as topicNmKmmp, -- 토픽(지식맵 5뎁스) 명
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		-- 템플릿 xml 내용
	            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
	            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
	            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
	            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
	            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
	            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
	            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
	            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
	            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QPI.QP_PSSG_HTML_TMPL_CN, '') AS passageHtmlTemplate, -- 지문HtmlTemplate
		        IFNULL(QPI.QP_PSSG_TMPL_XML_CN, '') AS passageTemplateXml, 	-- 지문Template xml 내용
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType,	-- 문항 등록 형태(hpw, html)
		        '' AS questionUrl, 	-- 질문 url
		        '' AS answerUrl,	-- 정답 url
		        '' AS explainUrl,	-- 해설 url
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl -- HTML(ZIP) url
	          , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    -- 문항 키워드명
		      , ROUND(IFNULL(CLA.CANS_RT_CLA,0), 1) AS cansRt 	-- 정답률
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode -- 형판코드
	      	  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName -- 형판명
			  , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
			  , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
			  , QTL.SRT_ORDN 						AS topicOrdn -- 토픽정렬순번
        FROM LMS_LRM.EA_EV_QTM Q
        JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = Q.QTM_ID
        JOIN LMS_CMS.QP_QTM_CN QQC ON QQ.QP_QTM_ID     = QQC.QP_QTM_ID
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID = QPQ.QP_PSSG_ID
        LEFT JOIN LMS_CMS.QP_LLU QLL ON QLL.QP_LLU_ID = QQA.QP_LLU_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	    LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
		LEFT JOIN (-- 반 평균정담률은 첫응시 데이터로
			SELECT EQ.TPC_ID
			     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS CANS_RT_CLA
		    FROM LMS_LRM.EA_EV E
		    JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		    JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
		    WHERE E.OPT_TXB_ID = #{optTxbId}
		    AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR AU WHERE AU.USR_ID = EQA.USR_ID AND AU.USR_TP_CD = 'ST' )
		    GROUP BY EQ.TPC_ID
		) CLA ON CLA.TPC_ID = Q.TPC_ID
    	WHERE Q.EV_ID = #{evId}

        /* 평가 - 박원희 - EaEvTcr-Mapper.xml - selectEvQtmInfoList - 평가 문항 리스트 조회 */
    </select>


    <!--    교사 평가추가 > 난이도별 문항리스트 조회 > 예전 버전-->
    <select id="selectDffdDsbQtmIdList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmResDto">

            SELECT
                    Q.QP_QTM_ID        	AS qtmId       -- 문항 ID
                  , Q.QP_DFFD_CD        AS evDffdDvCd  -- 문항 난이도 구분 코드
                  , COUNT(Q.QP_QTM_ID) OVER(PARTITION BY Q.QP_DFFD_CD) dffdQtmCnt -- 난이도별 문항 수
                  , Q.dffdRandRowNo -- 난이도별 문항 순번
            FROM (
					SELECT
		            		QQ.QP_QTM_ID       	-- 문항 ID
		                  , QQ.QP_DFFD_CD  		-- 문항 난이도 구분 코드
		                  , ROW_NUMBER () OVER(PARTITION BY QQ.QP_DFFD_CD ORDER BY RAND()) AS dffdRandRowNo
					FROM LMS_CMS.BC_EVSH_QTM_MPN BM
		            JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = BM.QTM_ID
		            JOIN LMS_CMS.QP_QTM_AN QA ON QA.QP_QTM_ID = QQ.QP_QTM_ID
		            JOIN LMS_CMS.QP_QTM_CN QC ON QC.QP_QTM_ID = QQ.QP_QTM_ID
		            JOIN LMS_CMS.QP_CANS_TS QT ON QT.QP_QTM_ID = QQ.QP_QTM_ID
					WHERE BM.EVSH_ID IN (
		            <foreach item="item" index ="index" collection="tsRngeList" separator="union">
						SELECT BE.EVSH_ID
						FROM LMS_CMS.BC_EVSH BE
			            WHERE BE.DEL_YN = 'N'
			            AND BE.USE_YN = 'Y'
			            AND BE.LLU_NOD_ID = #{item.luLrmpNodId}
			            <if test='item.mluLrmpNodId != null and item.mluLrmpNodId neq ""'>
							AND BE.MLU_NOD_ID = #{item.mluLrmpNodId}
            			</if>
			            <if test='item.sluLrmpNodId != null and item.sluLrmpNodId neq ""'>
							AND BE.SLU_NOD_ID = #{item.sluLrmpNodId}
            			</if>
		            </foreach>
					)
		       		AND QQ.DEL_YN = 'N'
		       		AND QQ.QP_DFFD_CD IN
		            <foreach item="item" index ="index" collection="dffdDsbList" open="(" separator="," close=")">
		                #{item.evDffdDvCd}
		            </foreach>
            ) Q
            WHERE 21 > Q.dffdRandRowNo


        /* 평가 - 박원희 - EaEvTcr-Mapper.xml - selectDffdDsbQtmIdList - 난이도별 문항리스트 조회 */

    </select>


    <!--    교사 평가리포트 조회    -->
	<select id="selectEvRptList" resultType="hashMap">
       /** EaEvTcr-Mapper.xml - selectEvRptList */

		SELECT
			  MAX(E.EV_ID)					AS evId					-- 평가ID
			, MAX(E.OPT_TXB_ID)			AS optTxbId				-- 운영교과서ID
			, MAX(E.EV_NM)				AS evNm					-- 평가명
			, CASE 	WHEN MAX(E.LCKN_YN) = 'Y' THEN '잠금'
					WHEN MAX(E.TXM_STR_DTM) > CURRENT_TIMESTAMP THEN '대기' -- 응시기간 도래전, 미잠금
					WHEN MAX(USR.totalUser) = COUNT(R.USR_ID) OR CURRENT_TIMESTAMP > MAX(E.TXM_END_DTM) THEN '응시 종료'
					WHEN COUNT(R.USR_ID) = 0 THEN '대기'
					ELSE '응시 중'
				   	END 				AS txmStNm				-- 평가상태 응시상태
			, MAX(E.EV_DV_CD)			AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = MAX(E.EV_DV_CD)) evDvNm -- 평가구분명
			, MAX(E.EV_DTL_DV_CD)		AS evDtlDvCd			-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = MAX(E.EV_DTL_DV_CD)) evDtlDvNm -- 평가상세구분명
			, MAX(USR.totalUser)		AS allStuCnt  			-- 전체 학생 수
			, SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END)			AS txmCmplCnt			-- 평가완료 학생 수
			, SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN R.CANS_CNT ELSE 0 END)			AS cansCnt				-- 평가완료 학생의 총 정답 수
			, ROUND(SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END)/MAX(USR.totalUser)*100) 	AS avgTxmCmplRt -- 응시율
			, ROUND(SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN R.CANS_CNT ELSE 0 END)/(SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END)*MAX(E.FNL_QST_CNT))*100, 1) AS avgCansRt -- 평균 정답 율
			, ROUND(SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN R.CANS_CNT ELSE 0 END)/SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END), 1)			AS avgCansCnt -- 평균 정답 수 (소수둘째자리 반올림)
			, CAST(FLOOR(SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN R.EV_TM_SCNT ELSE 0 END)/SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END)/60) 	AS Unsigned Integer) AS xplTmMi -- 평균 풀이시간 분
			, ROUND((SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN R.EV_TM_SCNT ELSE 0 END)/SUM(CASE WHEN R.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END))%60)		AS xplTmScnt			-- 평균 풀이시간 초수
			, MAX(E.QST_CNT)			AS qstCnt				-- 문제수
			, MAX(E.FNL_QST_CNT)		AS fnlQstCnt			-- 최종문제수
			, IFNULL(MAX(NOD_LU.TRM_DV_CD), '')	AS trmDvCd		-- 단원의 학기코드
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = E.EV_ID AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR WHERE USR_ID = R.USR_ID AND USR_TP_CD = 'ST') AND R.STU_EV_ABLE_YN	 = 'Y' -- 평가 응시가능 인원만
		LEFT JOIN (	SELECT E2.EV_ID, COUNT(1) as totalUser
		   		FROM LMS_LRM.EA_EV E2
		   		JOIN LMS_LRM.EA_EV_RS R2 ON R2.EV_ID = E2.EV_ID
		   		JOIN LMS_LRM.CM_USR U2 ON U2.USR_ID = R2.USR_ID
		   		WHERE E2.EV_ID = #{evId}
		   		AND (E2.EV_DV_CD = 'SE' OR (E2.EV_DV_CD = 'TE' AND IFNULL(R2.STU_EV_ABLE_YN, 'N') = 'Y'))
		   		AND U2.USR_TP_CD = 'ST'
		   		GROUP BY E2.EV_ID
		) USR ON USR.EV_ID = E.EV_ID
		WHERE E.EV_ID = #{evId}
		GROUP BY R.EV_ID
	</select>

    <!-- 평가리포트 학기초진단 내용영역별(교육표준체계) 조회    -->
	<select id="selectEvRptEduAreaList" resultType="hashMap">

	    SELECT
	          E.EV_ID AS evId
	        , BNCM.CRCL_CTN_STD_CD AS eduAreaCd
	        , MAX(BNCM.CRCL_CTN_STD_NM) AS eduAreaNm
	        , ROW_NUMBER() OVER(ORDER BY MAX(BNCM.CRCL_CTN_STD_CD)) AS srtOrdn
	        , COUNT(EQA.QTM_ID) AS qtmCnt
	        , COUNT(DISTINCT EQA.USR_ID) AS usrCnt
	        , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS cansCnt
	        , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) AS cansRt
		FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
		JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
		JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
		JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = EQA.QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX' -- 국가수준 교육과정 컨텐츠 매핑
        WHERE E.EV_ID = #{evId}
        AND USR.USR_TP_CD = 'ST'
		AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
        GROUP BY E.EV_ID, BNCM.CRCL_CTN_STD_CD

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 내용영역별(교육표준체계) 조회 - selectEvRptEduAreaList*/

 	</select>

    <!-- 평가리포트 학기초진단 내용영역별 조회 > 수학   -->
	<select id="selectEvRptEduAreaQpList" resultType="hashMap">

	    SELECT
	          E.EV_ID AS evId
	        , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) srtOrdn
	        , QA.QP_CN_ARA_ID AS eduAreaCd
	        , MAX(QA.QP_CN_ARA_NM) AS eduAreaNm
	        , COUNT(EQA.QTM_ID) AS qtmCnt
	        , COUNT(DISTINCT EQA.USR_ID) AS usrCnt
	        , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS cansCnt
	        , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) AS cansRt
		FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
		JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
		JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
        JOIN LMS_CMS.QP_QTM_AN QA ON QA.QP_QTM_ID = EQA.QTM_ID
        LEFT JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA' AND CC.CM_CD = QA.QP_CN_ARA_ID
        WHERE E.EV_ID = #{evId}
        AND USR.USR_TP_CD = 'ST'
		AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
		AND QA.QP_CN_ARA_ID IS NOT NULL
        GROUP BY E.EV_ID, QA.QP_CN_ARA_ID

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 학기초진단 문항플랫폼 내용영역별 현황 조회 - 수학 - selectEvRptEduAreaQpList*/

 	</select>


     <!--   평가리포트 단원진단 > 주제유형별 분석 조회    -->
	<select id="selectEvRptEduAreaThemeTpcList" resultType="hashMap">

		SELECT	EV_ID 			AS evId
		      , ROW_NUMBER() OVER(ORDER BY RCSTN_ORDN) srtOrdn
		      , KMMP_NOD_ID 	AS eduAreaCd
		      , KMMP_NOD_NM 	AS eduAreaNm
			  , cansRt
		FROM (
				SELECT EQ.EV_ID
				     , K_DPTH5.KMMP_NOD_ID
				     , MAX(K_DPTH5.KMMP_NOD_NM) AS KMMP_NOD_NM
				     , MAX(K_DPTH5.RCSTN_ORDN) AS RCSTN_ORDN
					 , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
		        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = USR.USR_ID
				JOIN LMS_LRM.AI_KMMP_NOD_RCSTN K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID -- 토픽단원(5Depth)
				WHERE EQ.EV_ID = #{evId}
				AND USR.USR_TP_CD = 'ST'
				AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
	        	GROUP BY EQ.EV_ID, K_DPTH5.KMMP_NOD_ID
		) E


 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 > 단원진단 > 주제유형별 분석 조회  - selectEvRptEduAreaThemeTpcList*/

 	</select>

    <!--    평가리포트 단원평가 > 단원성취도분석 > 차시별 조회    -->
	<select id="selectEvRptUgAchdChList" resultType="hashMap">

		SELECT	E.EV_ID evId
		      , E.LRMP_NOD_ID lrmpNodId
		      , E.lrmpNodNm
		      , ROW_NUMBER() OVER(ORDER BY E.rcstnOrdn) rcstnOrdn
			  , E.qtmCnt, E.cansCnt, E.cansRt
		      , MAX(E.cansRt) OVER() cansRtMax
		      , MIN(E.cansRt) OVER() cansRtMin
		FROM (
				SELECT
					   E.EV_ID
					 , L_DPTH4.LRMP_NOD_ID
					 , MAX(L_DPTH4.LRMP_NOD_NM) lrmpNodNm
					 , MAX(L_DPTH4.RCSTN_ORDN) rcstnOrdn
				     , COUNT(EQA.QTM_ID) qtmCnt
				     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) cansCnt
				     , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
				FROM (
						SELECT E.EV_ID, E.OPT_TXB_ID
						     , EQ.QTM_ID
						     , MIN(NOD_MPN.LRMP_NOD_ID) AS LRMP_NOD_ID
						FROM LMS_LRM.EA_EV E
						JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
						JOIN LMS_CMS.BC_KMMP_NOD K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID
						JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_DPTH5.URNK_KMMP_NOD_ID
						WHERE E.EV_ID = #{evId}
						GROUP BY E.OPT_TXB_ID, E.EV_ID, EQ.QTM_ID
				) E
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = E.QTM_ID AND EQA.USR_ID = ER.USR_ID
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = E.OPT_TXB_ID AND L_DPTH4.LRMP_NOD_ID = E.LRMP_NOD_ID
				WHERE ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
				AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR USR WHERE USR.USR_ID = ER.USR_ID AND USR.USR_TP_CD = 'ST')
				GROUP BY E.EV_ID, L_DPTH4.LRMP_NOD_ID
		) E

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 단원평가 > 단원성취도분석 > 차시별 조회 - selectEvRptUgAchdChList*/

 	</select>

     <!--    학생 평가리포트 단원평가 > 단원성취도분석 > 성쥐기준별 조회    -->
	<select id="selectEvRptUgAchdBsList" resultType="hashMap">
		SELECT	evId, eduAchBsId, achNm
			  , ROW_NUMBER() OVER(ORDER BY eduAchBsId) srtOrdn
			  , qtmCnt, cansCnt, cansRt
		      , MAX(cansRt) OVER() cansRtMax
		      , MIN(cansRt) OVER() cansRtMin
		FROM (
				SELECT E.EV_ID AS evId
				     , BEA.EDU_CRS_ACH_BS_ID AS eduAchBsId
					 , MAX(BEA.EDU_CRS_ACH_BS_CN_NM) AS achNm
				     , COUNT(EQA.QTM_ID) qtmCnt
				     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) cansCnt
				     , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
				JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = EQA.QTM_ID
				JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID
				WHERE E.EV_ID = #{evId}
				AND USR.USR_TP_CD = 'ST'
				AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
				AND BNCM.CRCL_CTN_TP_CD = 'EX' -- 문항만
				GROUP BY E.EV_ID, BEA.EDU_CRS_ACH_BS_ID
		) E

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 단원평가 > 단원성취도분석 > 성쥐기준별 조회  - selectEvRptUgAchdBsList*/
 	</select>

     <!--    학생 평가리포트 나의학습성장분석 > 수학 > 학기말(현재평가), 학기초 조회    -->
	<select id="selectEvRptMyLrnGrtMaList" resultType="hashMap">
		SELECT
 			   CC.CM_CD 		AS eduAreaCd
 		     , MAX(CC.CM_CD_NM) AS eduAreaNm
 		     , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) AS srtOrdn
	         , ROUND(MAX(cansRt_st), 1) AS cansRt_st -- 학기초 영역별 정답률
	         , ROUND(MAX(cansRt), 1) AS cansRt -- 학기말 영역별 정답률
		FROM (
				SELECT
				        QQA_ST.QP_CN_ARA_ID
	 				  , SUM(CASE WHEN EQA_ST.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA_ST.QTM_ID)*100 AS cansRt_st
	 				  , 0 AS cansRt
				FROM LMS_LRM.EA_EV E_ST
				JOIN LMS_LRM.EA_EV_TS_RNGE ET_ST ON ET_ST.EV_ID = E_ST.EV_ID AND E_ST.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU_ST ON NOD_LU_ST.OPT_TXB_ID = ET_ST.LU_OPT_TXB_ID AND NOD_LU_ST.LRMP_NOD_ID = ET_ST.LU_LRMP_NOD_ID
				JOIN LMS_LRM.CM_OPT_TXB OT_ST ON OT_ST.OPT_TXB_ID = E_ST.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR_ST ON USR_ST.CLA_ID = OT_ST.CLA_ID
				JOIN LMS_LRM.EA_EV_RS ER_ST ON ER_ST.EV_ID = E_ST.EV_ID AND ER_ST.USR_ID = USR_ST.USR_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA_ST ON EQA_ST.EV_ID = E_ST.EV_ID AND EQA_ST.USR_ID = USR_ST.USR_ID
		        JOIN LMS_CMS.QP_QTM_AN QQA_ST ON QQA_ST.QP_QTM_ID = EQA_ST.QTM_ID
				WHERE E_ST.OPT_TXB_ID = #{optTxbId}
				AND E_ST.EV_DV_CD = 'SE'
				AND E_ST.EV_DTL_DV_CD = 'ST'
				AND IFNULL(NOD_LU_ST.TRM_DV_CD, '') = #{trmDvCd}
				AND USR_ST.USR_TP_CD = 'ST' -- 학생만
				AND ER_ST.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
				GROUP BY QQA_ST.QP_CN_ARA_ID
		UNION ALL
				SELECT  QQA.QP_CN_ARA_ID
			          , 0 AS cansRt_st -- 학기초 영역별 정답률
			          , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS cansRt -- 학기말 영역별 정답률
		        FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
		        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
		        WHERE E.EV_ID = #{evId}
		        AND USR.USR_TP_CD = 'ST'
				AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
		        GROUP BY QQA.QP_CN_ARA_ID
		) ARA
 		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA' AND CC.CM_CD = ARA.QP_CN_ARA_ID
		GROUP BY CC.CM_CD

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 나의학습성장분석 수학 조회 - selectEvRptMyLrnGrtMaList*/

 	</select>

     <!--    평가리포트 나의학습성장분석 > 영어 > 학기말(현재평가), 학기초 조회    -->
	<select id="selectEvRptMyLrnGrthEnList" resultType="hashMap">

		SELECT
 			   CC.CM_CD 		AS arelyCd
 		     , MAX(CC.CM_CD_NM) AS arelyNm
 		     , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) AS srtOrdn
	         , ROUND(MAX(cansRt_st), 1) AS cansRt_st -- 학기초 영역별 정답률
	         , ROUND(MAX(cansRt), 1) AS cansRt -- 학기말 영역별 정답률
		FROM (
				SELECT
				        QQA_ST.QP_CN_ARA_ID
	 				  , SUM(CASE WHEN EQA_ST.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA_ST.QTM_ID)*100 AS cansRt_st
	 				  , 0 AS cansRt
				FROM LMS_LRM.EA_EV E_ST
				JOIN LMS_LRM.EA_EV_TS_RNGE ET_ST ON ET_ST.EV_ID = E_ST.EV_ID AND E_ST.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU_ST ON NOD_LU_ST.OPT_TXB_ID = ET_ST.LU_OPT_TXB_ID AND NOD_LU_ST.LRMP_NOD_ID = ET_ST.LU_LRMP_NOD_ID
				JOIN LMS_LRM.CM_OPT_TXB OT_ST ON OT_ST.OPT_TXB_ID = E_ST.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR_ST ON USR_ST.CLA_ID = OT_ST.CLA_ID
				JOIN LMS_LRM.EA_EV_RS ER_ST ON ER_ST.EV_ID = E_ST.EV_ID AND ER_ST.USR_ID = USR_ST.USR_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA_ST ON EQA_ST.EV_ID = E_ST.EV_ID AND EQA_ST.USR_ID = USR_ST.USR_ID
		        JOIN LMS_CMS.QP_QTM_AN QQA_ST ON QQA_ST.QP_QTM_ID = EQA_ST.QTM_ID
				WHERE E_ST.OPT_TXB_ID = #{optTxbId}
				AND E_ST.EV_DV_CD = 'SE'
				AND E_ST.EV_DTL_DV_CD = 'ST'
				AND IFNULL(NOD_LU_ST.TRM_DV_CD, '') = #{trmDvCd}
				AND USR_ST.USR_TP_CD = 'ST' -- 학생만
				AND ER_ST.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
				GROUP BY QQA_ST.QP_CN_ARA_ID
		UNION ALL
				SELECT  QQA.QP_CN_ARA_ID
			          , 0 AS cansRt_st -- 학기초 영역별 정답률
			          , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS cansRt -- 학기말 영역별 정답률
		        FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
		        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
		        WHERE E.EV_ID = #{evId}
		        AND USR.USR_TP_CD = 'ST'
				AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
		        GROUP BY QQA.QP_CN_ARA_ID
		) ARA
 		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA' AND CC.CM_CD = ARA.QP_CN_ARA_ID
		GROUP BY CC.CM_CD

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml - 평가리포트 나의학습성장분석 영어 조회 - selectEvRptMyLrnGrthEnList*/

 	</select>

     <!--    평가리포트 영어 > 영역별 분석 조회    -->
	<select id="selectEvRptEnArelyAnList" resultType="hashMap">
		SELECT MAX(E.EV_ID) 			AS evId
		     , CC.CM_CD 		AS arelyCd
		     , MAX(CC.CM_CD_NM) AS arelyNm
		     , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) AS srtOrdn
		     , MAX(CC.SETM_VL_2) AS cmtType -- AI커멘트 영여영역 유형코드
		     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS cansCnt
		     , COUNT(EQA.QTM_ID) AS arelyCnt
 	         , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) AS cansRt
        FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
		JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
		JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
 		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA_CD' AND CC.CM_CD = QQA.QP_CN_ARA_ID
        WHERE E.EV_ID = #{evId}
		AND USR.USR_TP_CD = 'ST'
		AND ER.EV_CMPL_YN = 'Y'  -- 평가 완료한 학생만
		GROUP BY EQA.EV_ID, CC.CM_CD

 		/** 교사평가 - 박원희 - EaEvTcr-Mapper.xml -  평가리포트 영어 > 영역별 분석 조회  - selectEvRptEnArelyAnList*/

 	</select>

    <!--    교사 평가리포트 우리반 오답BEST NEW 조회    -->
	<select id="selectEvRptIansBestNewList" resultType="hashMap">
        /** EaEvTcr-Mapper.xml - 교사 평가리포트 우리반 오답BEST 조회 - selectEvRptIansBestNewList */

		SELECT
			 	EV_ID		AS evId					-- 평가ID
			  , ROW_NUMBER() OVER(ORDER BY IANS_CNT DESC, E.QTM_ORDN) 	AS iansNo -- 오답 많은 순번
			  , QTM_ID		AS qtmId				-- 문항ID
			  , QTM_ORDN	AS qtmOrdn				-- 문항순번
			  , E.QP_DFFD_NM AS qpDffdNm			-- 문항난이도
			  , txmCmplCnt  AS txmCmplStuCnt		-- 응시완료 학생수
			  , IANS_CNT 	AS iansCnt				-- 오답 갯수
			  , ROUND((IANS_CNT/txmCmplCnt)*100, 1)					AS iansRt -- 오답률
			  , TPC_ID												AS tpcId  -- 토픽ID(지식맵 5Depth)
			  , K_DPTH5.KMMP_NOD_NM									AS tpcNm  -- 토픽명(지식맵 5Depth)
			  , (SELECT QP_CN_ARA_NM FROM LMS_CMS.QP_QTM_AN WHERE QP_QTM_ID = E.QTM_ID) AS qpCnAraNm -- 문항플랫폼 내용영역
		FROM (
				SELECT
						E.EV_ID
					  , MAX(E.FNL_QST_CNT) AS FNL_QST_CNT -- 총 문제 수
					  , EQ.QTM_ID
					  , EQ.QTM_ORDN
					  , MAX(EQ.QP_DFFD_NM) AS QP_DFFD_NM
					  , COUNT(DISTINCT ER.USR_ID) AS txmCmplCnt -- 응시완료 학생수
					  , SUM(CASE WHEN EQA.CANS_YN = 'N' THEN 1 ELSE 0 END) IANS_CNT -- 오답 갯수
					  , MAX(EQ.TPC_ID) AS TPC_ID
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = ER.USR_ID
				WHERE E.EV_ID = #{evId}
				AND ER.EV_CMPL_YN = 'Y' -- 평가완료 학생만
				AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR USR WHERE USR.USR_ID = EQA.USR_ID AND USR.USR_TP_CD = 'ST' )
				GROUP BY E.EV_ID, EQ.QTM_ID, EQ.QTM_ORDN
		) E
		LEFT JOIN LMS_CMS.BC_KMMP_NOD K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = E.TPC_ID
		WHERE IANS_CNT > 0


	</select>

    <!--    교사 평가리포트 정오답현황 > 우리반 문항별 평균 데이터 리스트 조회    -->
	<select id="selectEvRptOxClsAvgList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto">
        /** EaEvTcr-Mapper.xml - 교사 평가리포트 정오답현황 > 우리반 문항별 평균 데이터 리스트 조회 - selectEvRptOxClsAvgList */

		SELECT
				ANW.EV_ID
			  , ANW.QTM_ID
			  , ANW.QTM_ORDN
			  , COUNT(ANW.QTM_ID) OVER(PARTITION BY ANW.SPP_NTN_YN) AS sppNtnQtmCnt
			  , ROW_NUMBER() OVER(PARTITION BY ANW.SPP_NTN_YN ORDER BY ANW.QTM_ORDN) ROW_NO
			  , ANW.EV_CMPL_YN
			  , ANW.SPP_NTN_YN
			  , CASE WHEN ANW.SPP_NTN_YN = 'Y' THEN '오답 유사/심화 문제'
				     ELSE (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = E.EV_DTL_DV_CD)  -- 평가상세구분명
				     END AS evDtlDvNm
			  , ROUND(MIN(itemsTotalCansUser/itemsTotalCmplUser*100) OVER(), 1) AS cansRtMin
			  , ROUND(MAX(itemsTotalCansUser/itemsTotalCmplUser*100) OVER(), 1) AS cansRtMax
			  , ROUND(itemsTotalCansUser/itemsTotalCmplUser*100, 1) AS cansRt
			  , DATE_FORMAT(SEC_TO_TIME(itemsTotalTime/itemsTotalCmplUser), '%i:%s') AS xplTmScntNm	-- 평가시간명
		FROM (
				SELECT
					    ER.EV_ID
					  , MAX(EQ.QTM_ORDN) QTM_ORDN
					  , 'N' SPP_NTN_YN
					  , MAX(ER.EV_CMPL_YN) EV_CMPL_YN
					  , EQA.QTM_ID
					  , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) 	as itemsTotalCansUser -- 문항별 전체정답 학생수
					  , COUNT(DISTINCT ER.USR_ID) 							as itemsTotalCmplUser -- 문항별 전체평가완료 학생수
					  , SUM(EQA.XPL_TM_SCNT)								as itemsTotalTime -- 문항별 전체 풀이시간
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID
				JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = EQA.EV_ID AND EQ.QTM_ID = EQA.QTM_ID
				WHERE E.EV_ID = #{evId}
				AND (E.EV_DV_CD = 'SE' OR (E.EV_DV_CD = 'TE' AND ER.STU_EV_ABLE_YN = 'Y'))
				AND ER.EV_CMPL_YN = 'Y'
				AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR USR WHERE USR.USR_ID = ER.USR_ID AND USR.USR_TP_CD = 'ST' )
				GROUP BY ER.EV_ID, EQA.QTM_ID
		UNION ALL
				SELECT
					    ER.EV_ID
					  , MAX(EQA.QTM_ORDN) QTM_ORDN
					  , 'Y' SPP_NTN_YN
					  , MAX(ER.EV_CMPL_YN) EV_CMPL_YN
					  , EQA.QTM_ID
					  , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)  	as itemsTotalCansUser -- 문항별 전체정답 학생수
					  , COUNT(DISTINCT ER.USR_ID)  								as itemsTotalCmplUser -- 문항별 전체평가완료 학생수
					  , SUM(EQA.XPL_TM_SCNT) 									as itemsTotalTime -- 문항별 전체 풀이시간
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_SPP_NTN_RS ER ON ER.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.TXM_PN = ER.TXM_PN
				WHERE E.EV_ID = #{evId}
				AND E.EV_DV_CD = 'SE'
				AND E.EV_DTL_DV_CD IN ('FO','TO')
				AND ER.TXM_PN = 0	-- 첫응시 데이터
				AND ER.EV_CMPL_YN = 'Y'
				AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR USR WHERE USR.USR_ID = ER.USR_ID AND USR.USR_TP_CD = 'ST' )
				GROUP BY ER.EV_ID, EQA.QTM_ID
		) ANW
		JOIN LMS_LRM.EA_EV E ON E.EV_ID = ANW.EV_ID

	</select>


    <!--    교사 평가리포트 정오답현황 > 학생별 문항 정오 현황 리스트 조회    -->
	<select id="selectEvRptOxStuList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto">
    /** EaEvTcr-Mapper.xml - 교사 평가리포트 정오답현황 > 학생별 문항 정오 현황 리스트 조회 - selectEvRptOxStuList */ 

	SELECT
	        E.EV_ID			AS evId
		  , E.EV_DV_CD		AS evDvCd
		  , USR.USR_ID 		AS usrId
		  , USR.USR_NM		AS usrNm
		  , IFNULL(EQA.QTM_ID, 0)			AS qtmId
		  , IFNULL(EQA.SPP_NTN_TP_CD,'')	AS sppNtnTpCd
		  , ROW_NUMBER() OVER(PARTITION BY USR.USR_ID ORDER BY EQA.SPP_NTN_TP_CD, EQA.QTM_ORDN) qtmOrdn
		  , ROW_NUMBER() OVER(PARTITION BY EQA.SPP_NTN_TP_CD, EQA.QTM_ORDN ORDER BY USR.USR_NM, USR.STU_NO) rowNo
		  , IFNULL(EQA.ANNX_FLE_ID, 0)		AS annxFleId
		  , IFNULL(EQA.XPL_ST_CD, '00')		AS xplStCd
		  , IFNULL(EQA.EV_CMPL_YN, 'N')		AS evCmplYn
		  , IFNULL(EQA.CANS_YN, 'N')		AS cansYn
		  , IFNULL(QQ.QP_QST_TYP_CD, '')	AS qpQstTypCd
		  , IFNULL(EQA.MAX_RTXM_PN, 0) 		AS txmPnLast   -- 마지막 응시회차
	FROM LMS_LRM.EA_EV E
	JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
	JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
	JOIN LMS_LRM.EA_EV_RS EER ON EER.EV_ID = E.EV_ID AND EER.USR_ID = USR.USR_ID
	LEFT JOIN (
			SELECT
				    ER.EV_ID
				  , ER.USR_ID
				  , 'A' SPP_NTN_TP_CD
				  , ER.EV_CMPL_YN
				  , EQ.QTM_ID
				  , EQ.QTM_ORDN
				  , EQA.CANS_YN
				  , EQA.ANNX_FLE_ID
				  , EQA.XPL_ST_CD
				  , IFNULL(MAX_RTXM.MAX_RTXM_PN, 0) as MAX_RTXM_PN
			FROM LMS_LRM.EA_EV_RS ER
			JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = ER.EV_ID
			JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.QTM_ID = EQ.QTM_ID			
			LEFT JOIN (
			    SELECT USR_ID, MAX(RTXM_PN) AS MAX_RTXM_PN
			    FROM LMS_LRM.EA_EV_RS_RTXM
			    WHERE EV_ID = #{evId} 
			    AND EV_CMPL_YN = 'Y'
			    GROUP BY USR_ID
			) MAX_RTXM ON MAX_RTXM.USR_ID = ER.USR_ID	-- 응시회차 최댓값
			WHERE ER.EV_ID = #{evId} 
			AND ER.EV_CMPL_YN = 'Y'
	UNION ALL
			SELECT
				    ER.EV_ID
				  , ER.USR_ID
				  , 'SN' AS SPP_NTN_TP_CD
				  , ER.EV_CMPL_YN
				  , EQA.QTM_ID
				  , EQA.QTM_ORDN
				  , EQA.CANS_YN
				  , EQA.ANNX_FLE_ID
				  , EQA.XPL_ST_CD
				  , IFNULL(MAX_RTXM.MAX_RTXM_PN, 0) as MAX_RTXM_PN
			FROM LMS_LRM.EA_EV_SPP_NTN_RS ER
			JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.TXM_PN = ER.TXM_PN
			LEFT JOIN (
			    SELECT USR_ID, MAX(RTXM_PN) AS MAX_RTXM_PN
			    FROM LMS_LRM.EA_EV_RS_RTXM
			    WHERE EV_ID = #{evId} 
			    AND EV_CMPL_YN = 'Y'
			    GROUP BY USR_ID
			) MAX_RTXM ON MAX_RTXM.USR_ID = ER.USR_ID	-- 응시회차 최댓값
			WHERE ER.EV_ID = #{evId} 
			AND ER.TXM_PN = 0	-- 첫 응시 데이터
			AND ER.EV_CMPL_YN = 'Y'
			AND (SELECT 1
			     FROM LMS_LRM.EA_EV
				 WHERE EV_ID = ER.EV_ID
				 AND EV_DV_CD = 'SE'
				 AND EV_DTL_DV_CD IN ('FO','TO')
			)
	) EQA ON EQA.EV_ID = EER.EV_ID AND EQA.USR_ID = EER.USR_ID
	LEFT JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = EQA.QTM_ID
	WHERE E.EV_ID = #{evId} 
	AND (E.EV_DV_CD = 'SE' OR (E.EV_DV_CD = 'TE' AND EER.STU_EV_ABLE_YN = 'Y'))
	AND USR.USR_TP_CD = 'ST' -- 학생만
	</select>
	

    <!--    교사 평가리포트 정오답현황 조회    -->
	<select id="selectEvRptOxList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto">
    /** EaEvTcr-Mapper.xml - 교사 평가리포트 정오답현황 조회 - selectEvRptOxList */

	SELECT
	        E.EV_ID			AS evId
		  , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = E.EV_DTL_DV_CD) evDtlDvNm -- 평가상세구분명
		  , E.USR_ID 		AS usrId
		  , E.USR_NM		AS usrNm
		  , E.STU_NO		AS stuNo
		  , E.QTM_ID		AS qtmId
		  , ROW_NUMBER() OVER(PARTITION BY E.USR_ID ORDER BY E.QTM_ORDN) qtmOrdn
		  , ROW_NUMBER() OVER(PARTITION BY E.QTM_ORDN ORDER BY E.USR_NM, E.STU_NO) rowNo
		  , IFNULL(E.ANNX_FLE_ID, 0)		AS annxFleId
		  , IFNULL(E.XPL_ST_CD, '00')		AS xplStCd
		  , E.EV_CMPL_YN	AS evCmplYn
		  , E.CANS_YN		AS cansYn
		  , E.XPL_TM_SCNT 	AS xplTmScnt
		  , itemsTotalCansUser
		  , itemsTotalCmplUser
		  , ROUND(MIN(itemsTotalCansUser/itemsTotalCmplUser*100) OVER(), 1) AS cansRtMin
		  , ROUND(MAX(itemsTotalCansUser/itemsTotalCmplUser*100) OVER(), 1) AS cansRtMax
		  , ROUND(itemsTotalCansUser/itemsTotalCmplUser*100, 1) AS cansRt
		  , itemsTotalTime
		  , DATE_FORMAT(SEC_TO_TIME(itemsTotalTime/itemsTotalCmplUser), '%i:%s') AS xplTmScntNm	-- 평가시간명
	FROM (
			SELECT
				    E.EV_ID
				  , E.EV_DTL_DV_CD
				  , USR.USR_ID
				  , USR.USR_NM
				  , USR.STU_NO
				  , EQ.QTM_ID
				  , EQ.QTM_ORDN
				  , ER.EV_CMPL_YN
				  , EQA.ANNX_FLE_ID
				  , EQA.XPL_ST_CD
				  , EQA.CANS_YN
				  , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' AND EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) OVER(PARTITION BY EQ.QTM_ORDN) as itemsTotalCansUser -- 문항별 전체정답 학생수
				  , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN 1 ELSE 0 END)  OVER(PARTITION BY EQ.QTM_ORDN) as itemsTotalCmplUser -- 문항별 전체평가완료 학생수
				  , EQA.XPL_TM_SCNT
				  , SUM(CASE WHEN ER.EV_CMPL_YN = 'Y' THEN EQA.XPL_TM_SCNT ELSE 0 END) OVER(PARTITION BY EQ.QTM_ORDN) itemsTotalTime -- 문항별 전체 풀이시간
			FROM LMS_LRM.EA_EV E
			JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
			JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
			JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
			LEFT JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = USR.USR_ID
			LEFT JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = USR.USR_ID
			WHERE E.EV_ID = #{evId}
			AND USR.USR_TP_CD = 'ST' -- 학생만
			AND EQ.DEL_YN = 'N'
	) E
	ORDER BY E.QTM_ORDN, E.USR_NM, E.STU_NO

	</select>


    <!--    교사 평가리포트 학생별현황 조회    -->
	<select id="selectEvRptUsersList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto">
       	/** EaEvTcr-Mapper.xml - selectEvRptUsersList (Incorrect Answer)*/
	   SELECT
		      E.EV_ID			AS evId			-- 평가ID
		    , E.EV_DV_CD		AS evDvCd
		    , E.EV_NM			AS evNm			-- 평가명
		    , E.FNL_QST_CNT	AS fnlQstCnt		-- 최종 문제 수
		    , ROW_NUMBER() OVER(ORDER BY USR.USR_NM, USR.STU_NO) AS rowNo -- 로우 순번
		    , USR.USR_ID		AS usrId		-- 학생ID
		    , USR.USR_NM		AS usrNm		-- 학생명
		    , USR.STU_NO		AS stuNo		-- 학생번호
			, USR.LRNR_VEL_TP_CD			AS lrnrVelTpCd	-- 학습자속도유형코드
			, IFNULL(LVTC.CM_CD_NM, '-')	AS lrnrVelTpNm	-- 학습자속도유형명
			, IFNULL(LVTC.SRT_ORDN, 0)		AS lrnrVelTpSrt	-- 학습자속도유형 Srt
		    , IFNULL(R.EV_CMPL_YN, 'N')		AS evCmplYn		-- 평가 완료 여부
		    , IFNULL(CFUS.DILG_USE_YN, 'N') AS dilgUseYn	-- 대화 사용 여부
		    , CASE WHEN R.EV_CMPL_YN = 'N' THEN ''
		      ELSE DATE_FORMAT(SEC_TO_TIME(R.EV_TM_SCNT), '%i분 %s초')
		      END 		AS xplTmScntNm						-- 평가시간명
		    , CASE WHEN R.EV_CMPL_YN = 'N' THEN ''
		      ELSE R.CANS_CNT
		      END 		AS cansCnt							-- 정답 수
		    , CASE WHEN R.EV_CMPL_YN = 'N' THEN ''
		      ELSE ROUND(R.CANS_CNT/E.FNL_QST_CNT*100, 1)
		      END		AS cansRt 							-- 정답률
		    , CASE WHEN R.EV_CMPL_YN = 'N' THEN ''
		      ELSE CONCAT(DATE_FORMAT(R.SMT_DTM,'%m. %d'), IF(TIME_FORMAT(R.SMT_DTM, '%p') = 'AM', ' 오전 ', ' 오후 '),DATE_FORMAT(R.SMT_DTM,'%h:%i'))
		      END		AS smtDtm 							-- 평가 제출(완료)일
		    , CASE WHEN R.EV_CMPL_YN = 'N' THEN ''
		      ELSE E.FNL_QST_CNT
		      END 		AS fnlQstCnt						-- 문제 수
		 FROM LMS_LRM.EA_EV E
		 JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		 JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
		 JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = E.EV_ID AND R.USR_ID = USR.USR_ID
	LEFT JOIN LMS_LRM.CM_FNC_USE_SETM CFUS ON CFUS.OPT_TXB_ID = E.OPT_TXB_ID
	LEFT JOIN (SELECT CM_CD, CM_CD_NM, SRT_ORDN FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'LRNR_VEL_TP_CD') LVTC ON USR.LRNR_VEL_TP_CD = LVTC.CM_CD -- 학습자속도유형 공통코드
		WHERE E.EV_ID = #{evId}
		  AND USR.USR_TP_CD = 'ST' -- 학생만
		  AND (E.EV_DV_CD = 'SE' OR (E.EV_DV_CD = 'TE' AND R.STU_EV_ABLE_YN = 'Y'))
	</select>



    <!--    교사 평가리포트 우리반 오답BEST 조회    -->
	<select id="selectEvRptIansBestList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto">
        /** EaEvTcr-Mapper.xml - selectEvRptIansBestList (Incorrect Answer)*/

		SELECT
			 	EV_ID		AS evId					-- 평가ID
			  , ROW_NUMBER() OVER(ORDER BY CANS_CNT, E.QTM_ORDN) AS iansNo -- 오답 많은 순번
			  , QTM_ID		AS qtmId				-- 문항ID
			  , QTM_ORDN	AS qtmOrdn				-- 문항순번
			  , txmCmplCnt  AS txmCmplStuCnt		-- 응시완료 학생수
			  , ROUND(SUM(CANS_CNT) OVER()/(FNL_QST_CNT*txmCmplCnt)*100, 1)	AS cansRtAvg -- 평균정답률
			  , CANS_CNT 	AS cansCnt				-- 정답 갯수
			  , ROUND(100 - ((CANS_CNT/txmCmplCnt)*100), 1)	AS iansRt -- 오답률
			  , ROUND((CANS_CNT/txmCmplCnt)*100, 1)			AS cansRt -- 정답률
			  , QP_TC_NM									AS qpTcNm -- 문항플랫폼 토픽명(4 Depth 차시명과 동일)
		FROM (
				SELECT
						E.EV_ID
					  , MAX(E.FNL_QST_CNT) AS FNL_QST_CNT -- 총 문제 수
					  , Q.QTM_ID
					  , Q.QTM_ORDN
					  , COUNT(R.USR_ID) AS txmCmplCnt -- 응시완료 학생수
					  , SUM(CASE WHEN A.CANS_YN = 'Y' THEN 1 ELSE 0 END) CANS_CNT -- 정답 갯수
					  , MAX(Q.QP_TC_NM) AS QP_TC_NM
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_QTM Q ON Q.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW A ON A.EV_ID = Q.EV_ID AND A.QTM_ID = Q.QTM_ID
				JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = Q.EV_ID AND R.USR_ID = A.USR_ID
				WHERE E.EV_ID = #{evId}
				AND Q.DEL_YN = 'N'
				AND R.EV_CMPL_YN = 'Y' -- 평가완료 학생만
				GROUP BY E.EV_ID, Q.QTM_ID, Q.QTM_ORDN
		) E

	</select>

    <!--    교사 평가리포트 우리반 강점/약점 조회    -->
	<select id="selectEvRptStpnWkpnList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto">
        /** EaEvTcr-Mapper.xml - selectEvRptStpnWkpnList (Incorrect Answer)*/

		SELECT
			 	EV_ID		AS evId					-- 평가ID
			  , QTM_ID		AS qtmId				-- 문항ID
			  , QTM_ORDN	AS qtmOrdn				-- 문항순번
			  , CANS_CNT 	AS cansCnt				-- 정답 갯수
			  , ROW_NUMBER() OVER(ORDER BY CANS_CNT, QTM_ORDN) AS wkpnNo -- 약점 순번
			  , ROW_NUMBER() OVER(ORDER BY CANS_CNT DESC, QTM_ORDN) AS stpnNo -- 강점 순번
			  , QP_TC_NM	AS qpTcNm -- 문항플랫폼 토픽명(4 Depth 차시명과 동일)
		FROM (
				SELECT
						E.EV_ID
					  , Q.QTM_ID
					  , Q.QTM_ORDN
					  , COUNT(R.USR_ID) AS txmCmplCnt -- 응시완료 학생수
					  , SUM(CASE WHEN A.CANS_YN = 'Y' THEN 1 ELSE 0 END) CANS_CNT -- 정답 갯수
					  , MAX(Q.QP_TC_NM) AS QP_TC_NM
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_QTM Q ON Q.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW A ON A.EV_ID = Q.EV_ID AND A.QTM_ID = Q.QTM_ID
				JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = Q.EV_ID AND R.USR_ID = A.USR_ID
				WHERE E.EV_ID = #{evId}
				AND Q.DEL_YN = 'N'
				AND R.EV_CMPL_YN = 'Y' -- 평가완료 학생만
				GROUP BY E.EV_ID, Q.QTM_ID, Q.QTM_ORDN
		) E

	</select>

	<!--  교사 - 학습창 연동 - 학습창 진입하여 문제풀이 시 학생들의 모니터링 정보 조회    -->
	<select id="selectEvTcrLwStusSmtAnwList"  resultType="hashMap">
		SELECT
				E.EV_ID			AS evId
			 , E.EV_DV_CD		AS evDvCd
			 , USR.USR_ID 		AS usrId
			 , USR.USR_NM 		AS usrNm
			 , USR.STU_NO		AS stuNo
			 , USR.KERIS_USR_ID AS kerisUsrId
			 , IFNULL(EQA.QTM_ID, 0)			AS qtmId
			 , IFNULL(EQA.SPP_NTN_TP_CD, '')	AS sppNtnTpCd
			 , IFNULL(EQA.CANS_YN, '-')		AS cansYn
			 , IFNULL(EQA.SMT_ANW_VL, '')		AS smtAnwVl
			 , IFNULL(EQA.XPL_TM_SCNT, 0)		AS xplTmScnt
			 , IFNULL(EQA.EV_CMPL_YN, 'N')		AS evCmplYn
			 , QQ.QP_QST_TYP_CD 				AS qpQstTypCd
		FROM LMS_LRM.EA_EV AS E
		JOIN LMS_LRM.CM_OPT_TXB AS OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_USR AS USR ON USR.CLA_ID = OT.CLA_ID
		<choose>
			<when test="evDtlDvCd == 'FO' or evDtlDvCd == 'TO'">
				LEFT JOIN (
					SELECT
						ER.EV_ID
						, ER.EV_CMPL_YN
						, ER.USR_ID
						, '' AS SPP_NTN_TP_CD
						, EQA.QTM_ID
						, EQA.CANS_YN
						, EQA.SMT_ANW_VL
						, EQA.XPL_TM_SCNT
						, EQA.ANNX_FLE_ID
						, EQA.XPL_ST_CD
					FROM LMS_LRM.EA_EV_RS AS ER
					LEFT JOIN LMS_LRM.EA_EV_QTM_ANW AS EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID
					WHERE 1=1
						AND ER.EV_ID = #{evId}
						AND er.STU_EV_ABLE_YN = 'Y'
						AND (EQA.QTM_ID = #{qtmId} OR EQA.QTM_ID IS NULL)
					UNION ALL
						SELECT
							ER.EV_ID
							, ER.EV_CMPL_YN
							, ER.USR_ID
							, EQA.SPP_NTN_TP_CD
							, EQA.QTM_ID
							, EQA.CANS_YN
							, EQA.SMT_ANW_VL
							, EQA.XPL_TM_SCNT
							, EQA.ANNX_FLE_ID
							, EQA.XPL_ST_CD
						FROM LMS_LRM.EA_EV_SPP_NTN_RS AS ER
						JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW AS EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.TXM_PN = ER.TXM_PN
						WHERE 1=1
							AND ER.EV_ID = #{evId}
							AND ER.TXM_PN = 0 	-- 첫 응시 데이터
							AND EQA.QTM_ID = #{qtmId}
				) AS EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
			</when>
			<otherwise>
				JOIN (
					SELECT
						ER.EV_ID
						, ER.EV_CMPL_YN
						, ER.USR_ID
						, '' AS SPP_NTN_TP_CD
						, EQA.QTM_ID
						, EQA.CANS_YN
						, EQA.SMT_ANW_VL
						, EQA.XPL_TM_SCNT
						, EQA.ANNX_FLE_ID
						, EQA.XPL_ST_CD
					FROM LMS_LRM.EA_EV_RS AS ER
					LEFT JOIN LMS_LRM.EA_EV_QTM_ANW AS EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID
					WHERE 1=1
						AND ER.EV_ID = #{evId}
						AND er.STU_EV_ABLE_YN = 'Y'
						AND (EQA.QTM_ID = #{qtmId} OR EQA.QTM_ID IS NULL)
				) AS EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = USR.USR_ID
			</otherwise>
		</choose>
		JOIN LMS_CMS.QP_QTM AS QQ ON QQ.QP_QTM_ID = #{qtmId}
		WHERE 1=1
			AND E.EV_ID = #{evId}
			AND USR.USR_TP_CD = 'ST' -- 학생만
		ORDER BY USR.STU_NO
		/* EaEvTcr-Mapper.xml - selectEvTcrLwStusSmtAnwList - 교사 - 학습창 연동 - 학습창 진입하여 문제풀이 시 학생들의 모니터링 정보 조회 - 박원희 */
	</select>

	<!--    평가 추가하기     -->
	<insert id="insertEv"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto"
		useGeneratedKeys="true" keyProperty="evId">
		/** EaEvTcr-Mapper.xml - insertEv */
		/** 평가정보 */

		INSERT INTO LMS_LRM.EA_EV (
			  OPT_TXB_ID, USR_ID
			, EV_DV_CD, EV_DTL_DV_CD, EV_NM, TXM_PTME_SETM_YN, TXM_STR_DTM, TXM_END_DTM, ALL_STXQ_YN
			, XPL_TM_SETM_YN, XPL_TM_SCNT, QST_CNT, FNL_QST_CNT, LCKN_YN, RTXM_PMSN_YN, USE_YN, DEL_YN
			, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT #{optTxbId}, #{usrId}
			 , #{evDvCd}, #{evDtlDvCd}, #{evNm}, #{txmPtmeSetmYn}, TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME)), TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19),DATETIME)), #{allStxqYn}
			 , #{xplTmSetmYn}, #{xplTmScnt}, #{qstCnt}, #{fnlQstCnt}, #{lcknYn}, #{rtxmPmsnYn}, #{useYn}, 'N'
			 , #{usrId}, now(), #{usrId}, now(), #{dbId}
		;
	</insert>

	<!--    평가 평가난이도구성 등록     -->
	<insert id="insertEvDffd"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvDffd */

		/** EA_평가난이도구성 등록*/
		INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN( EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		VALUES
		<foreach collection="dffdList" index="index" item="item" separator=",">
		(#{evId}, #{item.evDffdDvCd}, #{item.evDffdDsbCnt}, #{usrId}, now(), #{usrId}, now(), #{dbId})
		</foreach>
		;

	</insert>

	<!--    평가 시험범위 등록     -->
	<insert id="insertEvTsRnge"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvTsRnge */

		/** EA_평가시험범위 등록 */
		INSERT INTO LMS_LRM.EA_EV_TS_RNGE(
				EV_ID, TS_RNGE_SEQ_NO
			  , LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
			  , MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
			  , SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="tsRngeList" index="index" item="item" separator=",">
		(
				#{evId}, #{index}+1
              , #{optTxbId}, NULLIF(#{item.luLrmpNodId}, ''), #{item.luLrmpNodNm}
              , #{optTxbId}, NULLIF(#{item.mluLrmpNodId}, ''), #{item.mluLrmpNodNm}
			  , #{optTxbId}, NULLIF(#{item.sluLrmpNodId}, ''), #{item.sluLrmpNodNm}
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;
	</insert>

	<!--    평가 문항리스트 등록     -->
	<insert id="insertEvQtm"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvQtm */


		/** EA_문항리스트 등록 */
		INSERT INTO LMS_LRM.EA_EV_QTM
		(	   EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM
			 , QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM, DEL_YN, DEL_DTM
			 , TPC_ID
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="qtmIdList" index="index" item="item" separator=",">
		(
			  #{evId}, #{item.qtmId}, #{index}+1, #{item.difficultyCode}, #{item.difficultyName}
			, #{item.lluId}, #{item.lluNm}, #{item.topicId}, #{item.topicNm}, #{item.delYn}
			, CASE WHEN #{item.delYn} = 'Y' THEN now() ELSE NULL END
			, #{item.topicIdKmmp}
			, #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;
	</insert>

	<!--    평가 결과 등록     -->
	<insert id="insertEvRs"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvRs */

		/** EA_평가결과 - 학생 평가가능여부 등록 */
		INSERT INTO LMS_LRM.EA_EV_RS (
				EV_ID, USR_ID, SMT_DTM, EV_TM_SCNT, CANS_CNT, CANS_RT
			  , TXM_STR_YN, EV_CMPL_YN, STU_EV_ABLE_YN
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="stuList" index="index" item="item" separator=",">
		(
				#{evId}, #{item.usrId}, NULL, NULL, NULL, NULL
			  , 'N', 'N', #{item.stuEvAbleYn}
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		ON DUPLICATE KEY UPDATE
			  STU_EV_ABLE_YN = values(STU_EV_ABLE_YN)
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		;

	</insert>


	<!--    평가 응시설정 수정(응시기간, 풀이시간, 재응시허용여부)     -->
	<update id="updateEvTxmSetm"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateTxmSetmDto">
		/** EaEvTcr-Mapper.xml - updateEvTxmSetm - 평가 응시설정 수정 */

		UPDATE LMS_LRM.EA_EV SET
		  TXM_PTME_SETM_YN = #{txmPtmeSetmYn}
		, TXM_STR_DTM = TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME))
		, TXM_END_DTM = TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19),DATETIME))
		, XPL_TM_SETM_YN = #{xplTmSetmYn}
		, XPL_TM_SCNT = #{xplTmScnt}
		, RTXM_PMSN_YN = #{rtxmPmsnYn}
		, MDFR_ID = #{usrId}
		, MDF_DTM = NOW()
		WHERE EV_ID = #{evId}

	</update>

	<!--    평가 저장하기     -->
	<update id="saveEv"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto"
		useGeneratedKeys="true" keyProperty="evId">
		/** EaEvTcr-Mapper.xml - saveEv */
		/** 평가정보 저장*/

		INSERT INTO LMS_LRM.EA_EV (
			  OPT_TXB_ID, USR_ID
			, EV_DV_CD, EV_DTL_DV_CD, EV_NM, TXM_PTME_SETM_YN, TXM_STR_DTM, TXM_END_DTM
			, XPL_TM_SETM_YN, XPL_TM_SCNT, QST_CNT, FNL_QST_CNT, LCKN_YN, RTXM_PMSN_YN, USE_YN, DEL_YN
			, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT #{optTxbId}, #{usrId}
			 , #{evDvCd}, #{evDtlDvCd}, #{evNm}, #{txmPtmeSetmYn}, TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME)), TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19),DATETIME))
			 , #{xplTmSetmYn}, #{xplTmScnt}, #{qstCnt}, #{fnlQstCnt}, #{lcknYn}, #{rtxmPmsnYn}, #{useYn}, 'N'
			 , #{usrId}, now(), #{usrId}, now(), #{dbId}
		ON DUPLICATE KEY UPDATE
			  EV_NM = #{evNm}
			, TXM_PTME_SETM_YN = #{txmPtmeSetmYn}
			, TXM_STR_DTM = TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME))
			, TXM_END_DTM = TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19),DATETIME))
		 	, XPL_TM_SETM_YN = #{xplTmSetmYn}
		 	, XPL_TM_SCNT = #{xplTmScnt}
		 	, QST_CNT = #{qstCnt}
		 	, FNL_QST_CNT = #{fnlQstCnt}
		 	, RTXM_PMSN_YN = #{rtxmPmsnYn}
		 	, MDFR_ID = #{usrId}
		 	, MDF_DTM = now()


	</update>

	<!--    평가 수정하기     -->
	<update id="updateEv" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - updateEv */
		/** 평가정보 저장*/

		UPDATE LMS_LRM.EA_EV SET
			  EV_NM = #{evNm}
			, TXM_PTME_SETM_YN = #{txmPtmeSetmYn}
			, TXM_STR_DTM = TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME))
			, TXM_END_DTM = TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19), DATETIME))
		 	, XPL_TM_SETM_YN = #{xplTmSetmYn}
		 	, XPL_TM_SCNT = #{xplTmScnt}
		 	, QST_CNT = #{qstCnt}
		 	, FNL_QST_CNT = #{fnlQstCnt}
		 	, RTXM_PMSN_YN = #{rtxmPmsnYn}
		 	, MDFR_ID = #{usrId}
		 	, MDF_DTM = now()
		WHERE EV_ID = #{evId}

	</update>

	<!--  교사 - 평가 추가, 수정 시 다른 학급 정보 조회   -->
	<select id="selectEvTcrMyClsInfoList"  resultType="hashMap">
		 SELECT
		       OT.OPT_TXB_ID 	AS otherOptTxbId
		     , OT.TXB_ID		AS otherTxbId
		     , CU.KERIS_USR_ID	AS tcrUsrId
		     , C.CLA_ID			AS otherClaId
		     , C.SGY			AS grade
		     , C.CLASSROOM_NM 	AS claNm
		     , EE.CLA_EV_ID 	AS claEvId
		FROM LMS_LRM.CM_USR CU
		INNER JOIN LMS_LRM.CM_CLA C ON C.CLA_ID = CU.CLA_ID
		INNER JOIN LMS_LRM.CM_OPT_TXB OT ON OT.CLA_ID = C.CLA_ID
		LEFT JOIN LMS_LRM.EA_EV EE ON EE.OPT_TXB_ID = OT.OPT_TXB_ID AND EE.CLA_EV_ID > 0 AND EE.CLA_EV_ID = #{claEvId}
		WHERE CU.KERIS_USR_ID = #{tcrUsrId} AND CU.USR_TP_CD='TE'
		AND OT.TXB_ID = #{txbId}
		ORDER BY C.SGY, C.CLASSROOM_NM

        /* EaEvTcr-Mapper.xml - selectEvTcrMyClsInfoList -  교사 - 평가 추가, 수정 시 다른 학급 정보 조회 - 박원희 */

	</select>

	<!--    평가 수정하기 - 다른학급에 적용 - 교과평가 - 평가ID조회    -->
	<select id="selectEvTcrOtherClaEvInfoSe"  resultType="hashMap">
		SELECT E.EV_ID AS otherEvId
		FROM LMS_LRM.EA_EV E
		WHERE E.OPT_TXB_ID = #{otherOptTxbId}
		AND E.EV_DV_CD = 'SE'
		AND E.EVSH_ID = #{evshId}

        /* EaEvTcr-Mapper.xml - selectEvTcrOtherClaEvInfoSe -  교사 - 평가 추가, 수정 시 다른 학급 정보 조회 - 박원희 */

	</select>
	<!--    평가 수정하기 - 다른학급에 적용 - 교사평가 - 평가ID조회    -->
	<select id="selectEvTcrOtherClaEvInfoTe"  resultType="hashMap">
		SELECT E.EV_ID AS otherEvId
		FROM LMS_LRM.EA_EV E
		WHERE E.OPT_TXB_ID = #{otherOptTxbId}
		AND E.EV_DV_CD = 'TE'
		<choose>
		<when test = 'optTxbId.equals(otherOptTxbId)'>
		AND E.EV_ID = #{evId}
		</when>
		<otherwise>
		AND E.CLA_EV_ID = #{claEvId}
		</otherwise>
		</choose>

        /* EaEvTcr-Mapper.xml - selectEvTcrOtherClaEvInfoTe -  교사 - 평가 추가, 수정 시 다른 학급 정보 조회 - 박원희 */

	</select>

	<!--    평가 수정하기 - 다른학급에 적용     -->
	<update id="updateEvOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - updateEvOtherCla - 평가 다른학급에 적용 */

		UPDATE LMS_LRM.EA_EV SET
			  TXM_PTME_SETM_YN = #{txmPtmeSetmYn}
			, TXM_STR_DTM = TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME))
			, TXM_END_DTM = TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19),DATETIME))
			, XPL_TM_SETM_YN = #{xplTmSetmYn}
			, XPL_TM_SCNT = #{xplTmScnt}
			, RTXM_PMSN_YN = #{rtxmPmsnYn}
			<if test='otherClaType != null and "qtm".equals(otherClaType)'>
			, EV_NM = #{evNm}
			, QST_CNT = #{qstCnt}
			, FNL_QST_CNT = #{fnlQstCnt}
			</if>
			, CLA_EV_ID = #{evId}
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE EV_ID = #{otherEvId}
	</update>

	<!--    평가 수정하기 - 다른학급에 적용 후 > 복제된평가(본인평가에) CLA_EV_ID 업데이트 > 원평가는 무조건 수정 하여 사용안함   -->
	<update id="updateEvClaEvId"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - updateEvOtherCla - 평가 다른학급에 적용 */

		UPDATE LMS_LRM.EA_EV SET
			  CLA_EV_ID = #{evId}
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE EV_ID = #{evId}
	</update>

	<!--    평가 추가하기     -->
	<insert id="insertEvOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto"
		useGeneratedKeys="true" keyProperty="evId">

		INSERT INTO LMS_LRM.EA_EV (
			  OPT_TXB_ID, USR_ID
			, EV_DV_CD, EV_DTL_DV_CD, EV_NM, TXM_PTME_SETM_YN, TXM_STR_DTM, TXM_END_DTM
			, XPL_TM_SETM_YN, XPL_TM_SCNT, QST_CNT, FNL_QST_CNT, LCKN_YN, RTXM_PMSN_YN, USE_YN, DEL_YN
			, CLA_EV_ID
			, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT #{otherOptTxbId}, #{usrId}
			 , #{evDvCd}, #{evDtlDvCd}, #{evNm}, #{txmPtmeSetmYn}, TIMESTAMP(CONVERT(LEFT(#{txmStrDtm}, 19), DATETIME)), TIMESTAMP(CONVERT(LEFT(#{txmEndDtm}, 19),DATETIME))
			 , #{xplTmSetmYn}, #{xplTmScnt}, #{qstCnt}, #{fnlQstCnt}, #{lcknYn}, #{rtxmPmsnYn}, #{useYn}, 'N'
			 , #{otherEvId}
			 , #{usrId}, now(), #{usrId}, now(), #{dbId}
		;
	</insert>
	<!--    평가 결과 등록     -->
	<insert id="insertEvRsOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvRs */

		/** EA_평가결과 - 학생 평가가능여부 등록 */
		INSERT INTO LMS_LRM.EA_EV_RS (
				EV_ID, USR_ID, SMT_DTM, EV_TM_SCNT, CANS_CNT, CANS_RT
			  , TXM_STR_YN, EV_CMPL_YN, STU_EV_ABLE_YN
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
        SELECT #{evId}, USR.USR_ID, NULL, NULL, NULL, NULL
			 , 'N', 'N', 'Y'
			 , #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM LMS_LRM.CM_USR USR
		WHERE USR.CLA_ID = #{otherClaId}
		AND USR.USR_TP_CD = 'ST'
		;

	</insert>

	<!--    평가 평가난이도구성 등록     -->
	<insert id="insertEvDffdOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvDffd */

		/** EA_평가난이도구성 등록*/
		INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN( EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		VALUES
		<foreach collection="dffdList" index="index" item="item" separator=",">
		(#{otherEvId}, #{item.evDffdDvCd}, #{item.evDffdDsbCnt}, #{usrId}, now(), #{usrId}, now(), #{dbId})
		</foreach>
		;

	</insert>

	<!--    평가 시험범위 등록     -->
	<insert id="insertEvTsRngeOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvTsRngeOtherCla */

		/** EA_평가시험범위 등록 */
		INSERT INTO LMS_LRM.EA_EV_TS_RNGE(
				EV_ID, TS_RNGE_SEQ_NO
			  , LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
			  , MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
			  , SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="tsRngeList" index="index" item="item" separator=",">
		(
				#{otherEvId}, #{index}+1
			  , #{otherOptTxbId}, NULLIF(#{item.luLrmpNodId}, ''), #{item.luLrmpNodNm}
			  , #{otherOptTxbId}, NULLIF(#{item.mluLrmpNodId}, ''), #{item.mluLrmpNodNm}
			  , #{otherOptTxbId}, NULLIF(#{item.sluLrmpNodId}, ''), #{item.sluLrmpNodNm}
			  , #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;
	</insert>

	<!--    평가 문항리스트 등록     -->
	<insert id="insertEvQtmOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvQtm */


		/** EA_문항리스트 등록 */
		INSERT INTO LMS_LRM.EA_EV_QTM
		(	   EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM
			 , QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM, DEL_YN, DEL_DTM
			 , TPC_ID
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		VALUES
		<foreach collection="qtmIdList" index="index" item="item" separator=",">
		(
			  #{otherEvId}, #{item.qtmId}, #{index}+1, #{item.difficultyCode}, #{item.difficultyName}
			, #{item.lluId}, #{item.lluNm}, #{item.topicId}, #{item.topicNm}, #{item.delYn}
			, CASE WHEN #{item.delYn} = 'Y' THEN now() ELSE NULL END
			, #{item.topicIdKmmp}
			, #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
		;
	</insert>


	<!--    평가 시험지 삭제 (평가난이도 구성정보)    -->
	<delete id="deleteEvDffdOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - deleteEvDffdOtherCla */

        DELETE FROM LMS_LRM.EA_EV_DFFD_CSTN
        WHERE EV_ID = #{otherEvId}

	</delete>

	<!--    평가 시험지 삭제 (시험범위)    -->
	<delete id="deleteEvTsRngeOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - deleteEvTsRngeOtherCla */

        DELETE FROM LMS_LRM.EA_EV_TS_RNGE
        WHERE EV_ID = #{otherEvId}

	</delete>

	<!--    평가 시험지 삭제 (평가 문항)    -->
	<delete id="deleteEvQtmOtherCla"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - deleteEvQtmOtherCla */

        DELETE FROM LMS_LRM.EA_EV_QTM
        WHERE EV_ID = #{otherEvId}

	</delete>





	<!--    평가 시험지 삭제 (교사평가)    -->
	<delete id="deleteEv"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvEvIdReqDto">
		/** EaEvTcr-Mapper.xml - deleteEv */
        UPDATE LMS_LRM.EA_EV
        SET
            DEL_YN  = 'Y',
            MDFR_ID = #{usrId},
            MDF_DTM = NOW()
        WHERE EV_ID = #{evId}
	</delete>

	<!--    평가 시험지 삭제 (평가난이도 구성정보)    -->
	<delete id="deleteEvDffd"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - deleteEvDffd */

        DELETE FROM LMS_LRM.EA_EV_DFFD_CSTN
        WHERE EV_ID = #{evId}

	</delete>

	<!--    평가 시험지 삭제 (시험범위)    -->
	<delete id="deleteEvTsRnge"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - deleteEvTsRnge */

        DELETE FROM LMS_LRM.EA_EV_TS_RNGE
        WHERE EV_ID = #{evId}

	</delete>

	<!--    평가 시험지 삭제 (평가 문항)    -->
	<delete id="deleteEvQtm"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - deleteEvQtm */

        DELETE FROM LMS_LRM.EA_EV_QTM
        WHERE EV_ID = #{evId}

	</delete>



	<!-- 평가 초기화 전 평가정보 조회 -->
	<select id="selectEaEvInfo" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto" resultType="hashMap">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 초기화 전 평가정보 조회 - selectEaEvInfo 조용진 */

		SELECT E.EV_ID 						AS evID       -- 평가ID
			   , E.EV_DV_CD					AS evDvCd 	  -- 평가구분코드 SE 교과평가, TE 선생님평가
	   		   , IFNULL(E.EV_DTL_DV_CD, '') AS evDtlDvCd  -- 평가상세구분코드
			   , E.RTXM_PMSN_YN				AS rtxmPmsnYn -- 재응시허용여부
		FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_CLA CL ON CL.CLA_ID = OT.CLA_ID
	    INNER JOIN LMS_LRM.CM_OPT_TCR OTCR ON OTCR.OPT_TXB_ID = OT.OPT_TXB_ID
	    WHERE E.EV_ID = #{evId}
	    AND E.OPT_TXB_ID = #{optTxbId}
		AND OTCR.TCR_USR_ID  = #{usrId}
		AND E.DEL_YN = 'N'
	</select>

	<!-- 평가 초기화 (평가 결과) -->
	<update id="updateEvRs">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 결과 초기화 - updateEvRs 조용진 */

		UPDATE LMS_LRM.EA_EV E
		JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
		SET ER.TXM_STR_YN = 'N'   -- 응시시작여부
			, ER.EV_CMPL_YN = 'N' -- 완료여부
			, ER.SMT_DTM = NULL   -- 제출일시
			, ER.EV_TM_SCNT = NULL-- 풀이 평균시간초
			, ER.CANS_CNT = NULL  -- 정답 수
			, ER.CANS_RT = NULL   -- 정답률
			, ER.CRT_DTM = now()  -- 생성일
			, ER.MDF_DTM = now()  -- 수정일
		WHERE E.EV_ID = #{evId}
		AND ER.USR_ID = #{usrId}
	</update>

	<!-- 평가 초기화 (평가 문항답변) -->
	<delete id="deleteEvQtmAnw">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 문항답변 제거 - deleteEvQtmAnw 조용진 */

        DELETE FROM LMS_LRM.EA_EV_QTM_ANW
        WHERE EV_ID = #{evId}
        AND USR_ID = #{usrId}
	</delete>

	<!-- 평가 초기화 (평가 재응시 결과) -->
	<delete id="deleteEvRsRtxm">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 재응시 결과 제거 - deleteEvRsRtxm 조용진 */

        DELETE FROM LMS_LRM.EA_EV_RS_RTXM
        WHERE EV_ID = #{evId}
        AND USR_ID = #{usrId}
	</delete>

	<!-- 평가 초기화 (평가 재응시 문항답변) -->
	<delete id="deleteEvQtmAnwRtxm">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 재응시 문항답변 제거 - deleteEvQtmAnwRtxm 조용진 */

        DELETE FROM LMS_LRM.EA_EV_QTM_ANW_RTXM
        WHERE EV_ID = #{evId}
        AND USR_ID = #{usrId}
	</delete>

	<!-- 평가 초기화 (보충심화 결과) -->
	<delete id="deleteEvSppNtnRs">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 보충심화 결과 제거(형성,차시평가)- deleteEvSppNtnRs 조용진 */

        DELETE FROM LMS_LRM.EA_EV_SPP_NTN_RS
        WHERE EV_ID = #{evId}
        AND USR_ID = #{usrId}
	</delete>

	<!-- 평가 초기화 (보충심화 문항답변) -->
	<delete id="deleteEvSppNtnQtmAnw">
		/** EaEvTcr-Mapper.xml 평가리포트(교과) > 평가초기화 > 평가 보충심화 답변제출 제거(형성,차시평가) - deleteEvSppNtnQtmAnw 조용진 */

        DELETE FROM LMS_LRM.EA_EV_SPP_NTN_QTM_ANW
        WHERE EV_ID = #{evId}
        AND USR_ID = #{usrId}
	</delete>

	<!-- 평가 결과 삭제 -->
	<delete id="deleteEaEvRs">
		/** EaEvTcr-Mapper.xml 평가 결과 삭제 - deleteEaEvRs */
		DELETE FROM LMS_LRM.EA_EV_RS
        	  WHERE EV_ID = #{evId}
        	  <if test='usrId != null and !"".equals(usrId)'>
				AND USR_ID = #{usrId}
        	  </if>
	</delete>

	<!-- 평가 삭제 -->
	<delete id="deleteEaEv">
		/** EaEvTcr-Mapper.xml 평가 삭제 - deleteEaEv */
		DELETE FROM LMS_LRM.EA_EV
        	  WHERE EV_ID = #{evId}
        		<if test='usrId != null and !"".equals(usrId)'>
				AND USR_ID = #{usrId}
        	  </if>
	</delete>


	<!--    신규 학생 기존 평가 반영    -->
	<insert id="insertEvRsNewStu" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto">
		/** EaEvTcr-Mapper.xml - insertEvRsNewStu */
		INSERT INTO LMS_LRM.EA_EV_RS (
			EV_ID,
			USR_ID,
			TXM_STR_YN,
			EV_CMPL_YN,
			STU_EV_ABLE_YN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		SELECT
			EA.EV_ID,
			#{usrId},
			'N',
			'N',
			'Y',
			EA.CRTR_ID,
			EA.CRT_DTM,
			EA.MDFR_ID,
			EA.MDF_DTM,
			EA.DB_ID
		FROM LMS_LRM.EA_EV EA
		WHERE EA.OPT_TXB_ID = #{optTxbId}
		  AND EA.ALL_STXQ_YN = 'Y'
		  AND EA.DEL_YN = 'N'
		  AND EA.USE_YN = 'Y'
		  AND (
				(EA.TXM_PTME_SETM_YN = 'Y' AND EA.TXM_END_DTM > NOW())
				OR (EA.TXM_PTME_SETM_YN = 'N')
			)
		  AND NOT EXISTS (
			SELECT 1
			FROM LMS_LRM.EA_EV_RS RS
			WHERE RS.USR_ID = #{usrId}
			  AND RS.EV_ID = EA.EV_ID
		)
	</insert>

</mapper>