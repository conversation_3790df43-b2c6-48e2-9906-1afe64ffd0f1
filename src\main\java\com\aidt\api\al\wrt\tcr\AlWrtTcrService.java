package com.aidt.api.al.wrt.tcr;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.wrt.dto.AlWrtAcpReqDto;
import com.aidt.api.al.wrt.dto.AlWrtReqDto;
import com.aidt.api.al.wrt.dto.AlWrtResDto;
import com.aidt.common.CommonDao;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-02 18:19:00
 * @modify 2024-06-02 18:19:00
 * @desc AI첨삭 선생님 서비스
 */

@Service
public class AlWrtTcrService {

	private final String MAPPER_NAMESPACE = "api.al.wrt.tcr.";
	private final String MAPPER_NAMESPACE_STU = "api.al.wrt.stu.";
	
	@Autowired
    private CommonDao commonDao;
	
	@Autowired
    private WebFluxUtil webFluxUtil;
	
	@Value("${aidt.endpoint.archipinWrite:}")
	private String endpoint_archipin;
	
    /**
     * AI 첨삭 : 토픽 목록
     *
     * @param AlWrtReqDto - 운영교과서ID
     * @return List<AlWrtResDto> - 해당 사용자의 첨삭토픽목록
     */
    public List<AlWrtResDto> selectAlWrtTpcList(AlWrtReqDto alWrtReqDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAlWrtTpcList", alWrtReqDto);
    }
    
    /**
     * AI 첨삭 : 상세 목록
     *
     * @param AlWrtReqDto - 단원 ID, 토픽 ID, 클래스코드 
     * @return List<AlWrtResDto> - 해당 토픽의 첨삭 상세 목록
     */
    public List<AlWrtResDto> selectAlWrtDtlList(AlWrtReqDto alWrtReqDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAlWrtDtlList", alWrtReqDto);
    }
    
    /**
     * AI 첨삭 : 상세
     *
     * @param AlWrtReqDto - 사용자ID, 운영교과서ID, 대단원노드ID, 토픽노드ID 
     * @return AlWrtResDto - 해당 단원, 해당 토픽의 상세
     */
    public AlWrtResDto selectWrtDtl(AlWrtReqDto alWrtReqDto) {
        return commonDao.select(MAPPER_NAMESPACE_STU + "selectWrtDtl", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 수정 (AI 첨삭)
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMgAiEdit(AlWrtReqDto alWrtReqDto) {
    	return commonDao.update(MAPPER_NAMESPACE_STU + "updateWrtMgAiEdit", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 별점 수정
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMgScore(AlWrtReqDto alWrtReqDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateWrtMgScore", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 수정 (선생님 첨삭)
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMgTcrEdit(AlWrtReqDto alWrtReqDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateWrtMgTcrEdit", alWrtReqDto);
    }
    
    /**
     * AI 첨삭관리 : 수정 (선생님 AI 재요청 시 mdf 정보 update)
     *
     * @param AlWrtReqDto - AI첨삭 학습 정보
     * @return int - update cnt
     */
    @Transactional
	public int updateWrtMgAiReqa(AlWrtReqDto alWrtReqDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateWrtMgAiReqa", alWrtReqDto);
    }
    
    /**
     * 아키핀 API Call
     * @param sunUrl
     * @param dto
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> callAcpApi(String sunUrl, AlWrtAcpReqDto dto) {        
        Map<String, Object> returnMap = new HashMap<>();
        HttpHeaders httpHeaders = new HttpHeaders();
        // httpHeaders.add("Authorization", "Bearer " + accessToken);
        httpHeaders.add("Content-Type", "application/json");

        try {
            String jsonString = new ObjectMapper().writeValueAsString(dto);
            //String uriString = "https://wgi02.archipindev.com";

            String post = webFluxUtil.post( endpoint_archipin + sunUrl, httpHeaders, jsonString, String.class);
            return CoreUtil.Json.jsonString2Map(post);
        } catch (JsonProcessingException e) {
        	returnMap = new HashMap<>();
        }

        return returnMap;
    }
    
    /**
     * 교사 홈 : 선생님 첨삭 수
     *
     * @param AlWrtReqDto - 운영교과서ID 
     * @return int - 선생님 첨삭 미완료 수
     */
    public int selectTcrWrtCnt(AlWrtReqDto alWrtReqDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectTcrWrtCnt", alWrtReqDto);
    }
    
}
