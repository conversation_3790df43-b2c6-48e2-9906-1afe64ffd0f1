<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.stu.alPlEnElStu">
	<!-- 학생 중단원 목록 조회-->
	<select id="selectMluLstInqEnElStuList" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		SELECT
			A.KMMP_NOD_ID AS mKmmpNodId,          -- 중단원 지식맵 노드ID
			MAX(A.KMMP_NOD_NM) AS mKmmpNodNm, -- 중단원 지식맵 노드명
			MAX(A.TC_USE_YN) AS TC_USE_YN,    -- 차시 사용 여부
			MAX(A.URNK_KMMP_NOD_ID) AS LLU_LRMP_NOD_ID, -- 대단원 지식맵 노드id
			COALESCE(C.EV_DV_CD, '') AS EV_DV_CD,      -- 평가구분코드
			COALESCE(C.EV_DTL_DV_CD, '') AS EV_DTL_DV_CD,  -- 평가상세구분코드
			COALESCE(C.EV_CMPL_YN, 'N') AS EV_CMPL_YN,  -- 평가완료여부
			C.CANS_CNT,
			C.EV_ID,
			C.TPC_AVN,
			MAX(ALLV.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD,
			C.MDF_DTM
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN A
		LEFT JOIN (
			SELECT
				EV.EV_ID,
				TS.MLU_KMMP_NOD_ID,
				GROUP_CONCAT(
					CONCAT(IFNULL(AUTP.TPC_AVN, '02'), ':',
					(SELECT KMMP_NOD_NM FROM LMS_LRM.AI_KMMP_NOD_RCSTN WHERE KMMP_NOD_ID = AUTP.TPC_ID LIMIT 1))
				) AS TPC_AVN,
				MAX(EV.EV_DV_CD) AS EV_DV_CD,
				MAX(EV.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
				IFNULL(MAX(RS.EV_CMPL_YN), 'N') AS EV_CMPL_YN,
				MAX(TS.AI_TS_RNGE_SEQ_NO) AS AI_TS_RNGE_SEQ_NO,
				MAX(RS.CANS_CNT) AS CANS_CNT,
				MAX(RS.USR_ID) AS USR_ID,
				MAX(RS.MDF_DTM) AS MDF_DTM
			FROM LMS_LRM.EA_EV EV
			JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS ON EV.EV_ID = TS.EV_ID
			LEFT OUTER JOIN (
				SELECT AUTP.TPC_ID, AUTP.TPC_AVN, BLKNMD.MLU_KMMP_NOD_ID
				FROM  LMS_LRM.AI_USRLY_TPC_PROF AUTP
				JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN_DTL BLKNMD 
				ON AUTP.TPC_ID = BLKNMD.TPC_KMMP_NOD_ID
				WHERE AUTP.USR_ID = #{usrId}
			)AUTP ON TS.TPC_KMMP_NOD_ID = AUTP.TPC_ID	-- 토픽 지식맵 ID
			LEFT JOIN LMS_LRM.EA_EV_RS RS ON EV.EV_ID = RS.EV_ID
			WHERE EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
	 		AND EV.EV_DTL_DV_CD = 'OV'
			AND RS.USR_ID = #{usrId}
			AND EV.EV_DTL_DV_CD IS NOT NULL
			GROUP BY TS.MLU_KMMP_NOD_ID, EV.EV_DTL_DV_CD
		) C ON A.KMMP_NOD_ID = C.MLU_KMMP_NOD_ID
		LEFT JOIN LMS_LRM.AI_LRNR_LV ALLV ON ALLV.USR_ID = C.USR_ID AND ALLV.LU_KMMP_NOD_ID = A.KMMP_NOD_ID
		LEFT JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN_DTL DTL ON A.KMMP_NOD_ID = DTL.MLU_KMMP_NOD_ID
		WHERE A.DPTH = 2
		AND A.OPT_TXB_ID = #{optTxbId}
		<if test = 'mluLrmpNodId != null and !mluLrmpNodId.equals("")'>
			AND A.LRMP_NOD_ID = #{mluLrmpNodId}
		</if>
		GROUP BY A.KMMP_NOD_ID, C.EV_DV_CD, C.EV_DTL_DV_CD
		ORDER BY MAX(A.RCSTN_ORDN)
		
		/* AI 맞춤 학습 - 지향난 - AlPlEnElStu-Mapper.xml - 학생 중단원 목록 조회 - selectMluLstInqEnElStuList */
	</select>
	
	<select id="selectTcLrmpNodInfoList" resultType="com.aidt.api.al.pl.dto.AlPlMluLstInqEnElStuResponseDto" >
		SELECT
			MAX(AKNR.OPT_TXB_ID) AS OPT_TXB_ID,
			AKNR.KMMP_NOD_ID, -- 차시 지식맵 노드ID
			MAX(AKNR.KMMP_NOD_NM) AS KMMP_NOD_NM, -- 지식맵 노드명
			MAX(ALLV.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD, -- 학습자속도유형코드
			MAX(AKNR.DPTH) AS DPTH, -- 뎁스
			MAX(AKNR.TC_USE_YN) AS TC_USE_YN, -- 차시 사용 여부
			IFNULL(MAX(AUTR.TPC_AVN), 0.5) AS tpcAvn -- 토픽 결과
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR
		LEFT JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS ON TS.TC_KMMP_NOD_ID = AKNR.KMMP_NOD_ID
		LEFT JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTR ON AKNR.KMMP_NOD_ID = AUTR.TC_KMMP_NOD_ID 
												AND AUTR.LU_KMMP_NOD_ID = TS.MLU_KMMP_NOD_ID AND AUTR.USR_ID = #{usrId}
												AND TS.TPC_KMMP_NOD_ID = AUTR.TPC_ID
		LEFT JOIN LMS_LRM.AI_LRNR_LV ALLV ON ALLV.LU_KMMP_NOD_ID = AKNR.KMMP_NOD_ID AND ALLV.USR_ID = #{usrId}
		WHERE 1=1
		AND AKNR.DPTH = '4'		-- 4뎁스가 차시
		AND AKNR.OPT_TXB_ID = #{optTxbId} -- 운영교과서
	 	AND TS.MLU_KMMP_NOD_ID = #{mKluLrmpNodId} -- 지식맵 중단원 (임시주석)	 
		GROUP BY AKNR.KMMP_NOD_ID
	<!-- 	LIMIT 6	*/ -->
		/* AI 맞춤 학습 - 지향난 - AlPlEnElStu-Mapper.xml - 중단원학습맵에 따른 차시 정보 - selectTcLrmpNodInfoList */
	</select>
	
	<select id="selectAiPlQtmTpcProfList" resultType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto">
		SELECT
			MAX(EV.EV_ID) AS EV_ID,
			MAX(TS.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID, -- 중단원 지식맵 ID (학습맵에서 변경)
			TS.TC_KMMP_NOD_ID, -- 차시 지식맵 ID (학습맵에서 변경)
			MAX(EV.EV_DV_CD) AS EV_DV_CD, -- 평가구분코드
			EV.EV_DTL_DV_CD AS EV_DTL_DV_CD, -- 평가상세구분코드
			IFNULL(MAX(RS.EV_CMPL_YN), 'N') AS EV_CMPL_YN, -- 평가완료여부
			MAX(AKNR.KMMP_NOD_ID) AS TC_KMMP_NOD_ID, -- 차시 지식맵 ID
			MAX(ALLV.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD, -- 학습자속도유형코드
			MAX(TS.AI_TS_RNGE_SEQ_NO) AS AI_TS_RNGE_SEQ_NO,
			MAX(RS.CANS_CNT) AS CANS_CNT,
			MAX(RS.USR_ID) AS USR_ID,
			MAX(RS.MDF_DTM) AS MDF_DTM
		FROM LMS_LRM.EA_EV EV
		JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS on EV.EV_ID = TS.EV_ID
		LEFT JOIN LMS_LRM.EA_EV_RS RS ON EV.EV_ID = RS.EV_ID
		LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR ON TS.TC_KMMP_NOD_ID = AKNR.KMMP_NOD_ID  
		LEFT JOIN LMS_LRM.AI_LRNR_LV ALLV ON ALLV.LU_KMMP_NOD_ID = AKNR.KMMP_NOD_ID AND ALLV.USR_ID = #{usrId}
		WHERE EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
		AND EV.OPT_TXB_ID = #{optTxbId}
		AND RS.USR_ID = #{usrId}
		AND EV.EV_DTL_DV_CD IS NOT NULL
		AND TS.TC_KMMP_NOD_ID = #{tcKmmpNodId}	-- 차시 지식맵 ID
		AND TS.MLU_KMMP_NOD_ID = #{mKluLrmpNodId}	-- 중단원 지식맵 ID
		GROUP BY TS.TC_KMMP_NOD_ID, EV.EV_DTL_DV_CD
		ORDER BY MAX(RS.MDF_DTM) ASC
		/* AI 맞춤 학습 - 지향난 - AlPlEnElStu-Mapper.xml - 차시정보에 따른 맞춤학습 평가 정보 조회 - selectAiPlQtmTpcProfList */
	</select>
	
	<select id="selectAiTpcInfoList" resultType="com.aidt.api.al.pl.dto.AlPlTpcProfInfoStuResponseDto">
		SELECT AUTR.USR_ID
			 , AUTR.TPC_ID AS TPC_ID					-- 차시에 따른 토픽ID tpcId
			 , AUTR.LU_KMMP_NOD_ID AS MLU_KMMP_NOD_ID 	-- 중단원 지식맵 노드 ID mluKmmpNodId mKmmpNodId
			 , AUTR.TC_KMMP_NOD_ID AS TC_KMMP_NOD_ID	-- 차시 지식맵 노드 ID	tcKmmpNodId
			 , AUTR.AI_PRED_AVG_CANS_RT					-- AI예측평균정답률
			 , AUTR.AI_PRED_AVG_SCR						-- AI예측평균점수
			 , AUTR.TPC_AVN AS TPC_AVN					-- 토픽숙련도
			 , AKNR.KMMP_NOD_NM AS TPC_NM 				-- 토픽명	tpcNM
		FROM LMS_LRM.AI_USRLY_TPC_PROF AUTR
		LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR ON AUTR.TPC_ID = AKNR.KMMP_NOD_ID AND AKNR.DPTH = '5' AND AKNR.OPT_TXB_ID = #{optTxbId}
		WHERE 1=1
		AND AUTR.USR_ID = #{usrId}
		AND AUTR.LU_KMMP_NOD_ID = #{mKluLrmpNodId}	-- 지식맵 중단원 
		AND autr.TC_KMMP_NOD_ID = #{tcKmmpNodId} -- 지식맵 차시
		/* AI 맞춤 학습 - 지향난 - AlPlEnElStu-Mapper.xml - 차시정보에 따른 토픽 정보 전체 조회 - selectAiTpcInfoList */
	</select>

	<select id="selectTEaEvLastInfo" resultType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto">
		SELECT
			MAX(EV.EV_ID) AS EV_ID,
			MAX(TS.MLU_KMMP_NOD_ID) AS mluKmmpNodId, -- 중단원 지식맵 ID
			TS.TC_KMMP_NOD_ID AS tcKmmpNodId, -- 차시 지식맵 ID
			MAX(EV.EV_DV_CD) AS EV_DV_CD, -- 평가구분코드
			EV.EV_DTL_DV_CD AS EV_DTL_DV_CD, -- 평가상세구분코드
			IFNULL(MAX(RS.EV_CMPL_YN), 'N') AS EV_CMPL_YN, -- 평가완료여부
			MAX(AKNR.KMMP_NOD_ID) AS TC_KMMP_NOD_ID, -- 차시 지식맵 ID
			MAX(ALLV.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD,
			MAX(TS.AI_TS_RNGE_SEQ_NO) AS AI_TS_RNGE_SEQ_NO,
			MAX(RS.CANS_CNT) AS CANS_CNT,
			MAX(RS.USR_ID) AS USR_ID,
			MAX(RS.MDF_DTM) AS MDF_DTM
		FROM LMS_LRM.EA_EV EV
		JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS ON EV.EV_ID = TS.EV_ID
		LEFT JOIN LMS_LRM.EA_EV_RS RS ON EV.EV_ID = RS.EV_ID
		LEFT JOIN LMS_LRM.EA_AI_EV_TS_RNGE ATS ON EV.EV_ID = ATS.EV_ID AND TS.TC_KMMP_NOD_ID = ATS.TC_KMMP_NOD_ID
		LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR ON ATS.TC_KMMP_NOD_ID = AKNR.KMMP_NOD_ID
		LEFT JOIN LMS_LRM.AI_LRNR_LV ALLV ON ALLV.LU_KMMP_NOD_ID = AKNR.KMMP_NOD_ID AND ALLV.USR_ID = #{usrId}
		WHERE EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
		AND EV.OPT_TXB_ID = #{optTxbId}
		AND RS.USR_ID = #{usrId}
		AND EV.EV_DTL_DV_CD IS NOT NULL
		AND TS.MLU_KMMP_NOD_ID = #{mKluLrmpNodId}	-- 중단원 지식맵 ID
		GROUP BY TS.TC_KMMP_NOD_ID, EV.EV_DTL_DV_CD
		ORDER BY MAX(RS.MDF_DTM) DESC
		/* AI 맞춤 학습 - 지향난 - AlPlEnElStu-Mapper.xml - 차시정보에 따른 토픽 정보 전체 조회 - selectTEaEvLastInfo */
	</select>
	
</mapper>