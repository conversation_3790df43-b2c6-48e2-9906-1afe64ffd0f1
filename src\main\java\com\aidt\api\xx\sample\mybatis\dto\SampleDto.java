package com.aidt.api.xx.sample.mybatis.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class SampleDto {
    @Data
    public static class GetTest02Dto {
        int page;
        int per_page;
        int total;
        int total_pages;
        List<DataDto> data;
        SupportDto support;
    }

    @Data
    public static class DataDto {
        String id;
        String name;
        int year;
        String color;
        String pantone_value;
    }

    @Data
    public static class SupportDto {
        String url;
        String text;
    }

    @Data
    public static class RequestDto {
        String email;
        String password;
    }

    @Data
    public static class ResponseDto {
        String id;
        String token;
    }

    @Data
    public static class TokenRequestDto {
        String userId;
        String password;
    }

    @Data
    public static class TokenResponseDto {
        TokenResponseDataDto data;
    }

    @Data
    public static class TokenResponseDataDto {
        String accessToken;
        String refreshToken;
    }

    @Data
    @Builder
    public static class PostDto {
        private String usrId;
        private String usrNm;
        private String cntry;
        private String phone;
        private String color;
        private String price;
        private String quan;
    }

    @Data
    public static class ResponseDto02 {
        private String timestamp;
        private String code;
        private String status;
        private int data;
    }

    @Data
    public static class PutRequestDto {
        private String name;
        private String job;
    }

    @Data
    public static class PutResponseDto {
        private String name;
        private String job;
        private String updatedAt;
    }
    
    @Data
    public static class schoolDto {
    	private List<listSchoolDto> listSchool;
    	private List<listGradeDto> listGrade;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class listSchoolDto{
    	private String schoolLevelCode;
    	private String schoolLevelName;
    }
    
    @Data
    public static class listGradeDto{
    	private String gradeName;
    	private String schoolLevelCode;
    	private String gradeCode;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class responseTestDto {
    	private String OptTxbID;
    	private String kmmpNodId;
    	private String lrmpAchBsId;
    	private String lrnUsrId;
    	private int evId;
    	private String qtmId;
    }
    
    @Data
    @Builder
    public static class postItemListDto {
        private String schoolLevelCode;
        private String gradeCode;
        private String areaCode;
        private String termCode;
        private String subjectId;
        private String largeChapterId;
        private String mediumChapterId;
        private String smallChapterId;
        private String topicChapterId;
        private List<String> itemIds;
    }
    
    
    @Data
    public static class ResponseItemDto {
        private String relationType;
        private String itemId;
        private String sourceId;
        private String sourceName;
        private String sourceGubunCode;
        private String sourceGubunName;
        private String questionFormCode;
        private String questionFormName;
        private String difficultyCode;
        private String difficultyName;
        private String activityAreaCode;
        private String activityAreaName;
        private String contentAreaCode;
        private String contentAreaName;
        private String curriculumCompetencyCode;
        private String curriculumCompetencyName;
        private String achievementId;
        private String achievementCode;
        private String achievementName;
        private String schoolLevelCode;
        private String schoolLevelName;
        private String gradeCode;
        private String gradeName;
        private String termCode;
        private String termName;
        private String curriculumCode;
        private String curriculumName;
        private String areaCode;
        private String areaName;
        private String subjectId;
        private String subjectName;
        private String largeChapterId;
        private String largeChapterName;
        private String mediumChapterId;
        private String mediumChapterName;
        private String smallChapterId;
        private String smallChapterName;
        private String topicChapterId;
        private String topicChapterName;
        private String passageId;
        private String passage;
        private String passageHtml;
        private String question;
        private String questionHtml;
        private String choice1Html;
        private String choice2Html;
        private String choice3Html;
        private String choice4Html;
        private String choice5Html;
        private String answerLegend;
        private String answerPipe;
        private String answerJson;
        private String answer;
        private String answerHtml;
        private String explain;
        private String explainHtml;
    }
    
    @Data
    public static class archivementList{
    	private List<archivement> archivement;
    }
    
    @Data
    public static class archivement{
    	private String subjectId;
    	private String subjectName;
    	private String achievementId;
    	private String achievementCode;
    	private String achievementName;
    	
    }
}