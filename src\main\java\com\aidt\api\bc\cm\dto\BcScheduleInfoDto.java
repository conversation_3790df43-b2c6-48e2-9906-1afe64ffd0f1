package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "시간표")
public class BcScheduleInfoDto {

	@Parameter(name="요일코드")
	private String dayWeek;

	@Parameter(name="교시")
	private String classPeriod;

	@Parameter(name="과목 명")
	private String subjectName;

	@Parameter(name="교실 명")
	private String classroomName;

	@Parameter(name="학교 명")
	private String schoolName;


}
