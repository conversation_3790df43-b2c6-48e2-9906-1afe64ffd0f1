//package com.aidt.api.al.pl.tcr;
//
//import java.util.List;
//import java.util.Map;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.validation.Valid;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.aidt.api.al.pl.back.AiRcmTsshQtmCommService;
//import com.aidt.api.al.pl.back.AiRcmTsshQtmEnService;
//import com.aidt.api.al.pl.back.AiRcmTsshQtmMaService;
//import com.aidt.api.al.pl.back.AiRcmTsshQtmService;
//import com.aidt.api.al.pl.common.AlConstUtil;
//import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
//import com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto;
//import com.aidt.api.al.pl.dto.AiRcmTsshQtmResponseDto;
//import com.aidt.api.al.pl.dto.AlTpcMpnDto;
//import com.aidt.base.jwt.CommonUserDetail;
//import com.aidt.base.response.Response;
//import com.aidt.base.response.ResponseDto;
//import com.aidt.base.util.CoreUtil;
//
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * AI 맞춤 문항추천(선생님)
// */
//
//@Slf4j
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/api/v1/al/pl/tcr")
//@Tag(name="[al] AI맞춤 문항추천", description="AI맞춤 문항추천")
//public class AiTcrRcmTsshQtmController {
//	
//	@Autowired
//	private AiRcmTsshQtmService aiRcmTsshQtmService;
//	
//	@Autowired
//	private AiRcmTsshQtmCommService commService;
//	
//	@Autowired
//	private AiRcmTsshQtmMaService maService;
//	
//	@Autowired
//	private AiRcmTsshQtmEnService enService;
//	
//	/**
//	 * AI 맞춤 문항추천
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] AI 맞춤 문항추천", description="AI 맞춤 문항추천")
//    @PostMapping(value = "/selectAiRcmTsshQtmList")
//    public ResponseDto<AiRcmTsshQtmResponseDto> selectAiRcmTsshQtmList(@Valid @RequestBody AiRcmTsshQtmReqDto dto, HttpServletRequest req) {
//		log.debug(dto.toString());
//		
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//		if(dto.getOptTxbId() == null) {
//			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
//		}
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//        
//        //학습맵중단원노드ID를 지식맵중단원노드ID로 치환
//  		if(dto.getLrmpKmmpDvCd() != null && dto.getLrmpKmmpDvCd().equals("LRMP")) {
//  			if(dto.getMluLrmpNodId() == null) {
//  				throw new IllegalArgumentException("Invalid 'mluLrmpNodId' in Request Parameter");
//  			}
//  			String mluKmmpNodId = commService.convertLrmpIdToKmmpId(dto.getOptTxbId(), dto.getMluLrmpNodId());
//  			if(mluKmmpNodId == null) {
//  				throw new IllegalArgumentException("매핑되는 지식맵이 없습니다.");
//  			}
//  			dto.setMluKmmpNodId(mluKmmpNodId);
//  		}
//      
//    	//DIY
//        if(AlConstUtil.SBJ_EN.contains(dto.getSbjCd()) && dto.getEvDvCd().equals("DE")) {
//        	return Response.fail("DIY 미제공 과목입니다.");
//        }
//        //초등영어만 로직분리
//        if(AlConstUtil.SBJ_EN.contains(dto.getSbjCd()) && dto.getSchlGrdCd().equals("E")) {//초등영어
//        	return Response.ok(enService.getEschEnQtmList(dto));
//        }else {//나머지
//        	return Response.ok(aiRcmTsshQtmService.selectAiRcmTsshQtmList(dto));
//        }
//    }
//	
//	
//	/**
//	 * 단원 내 오답문항 풀기 - DIY
//	 * 맞춤학습1,2,선택학습의 오답문항을 DIY평가로 출제
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] 수학 단원 내 오답문항 풀기", description="맞춤학습1,2,선택학습의 오답문항을 DIY평가로 출제")
//    @PostMapping(value = "/selectIansQtmTxm")
//    public ResponseDto<AiRcmTsshQtmResponseDto> selectIansQtmTxm(@Valid @RequestBody AiRcmTsshQtmReqDto dto) {
//		log.debug(dto.toString());
//		
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//		if(dto.getOptTxbId() == null) {
//			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
//		}
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//    	return Response.ok(maService.selectIansQtmTxm(dto));
//    }
//	
//	
//	/**
//	 * AI 맞춤 개념영상 리스트 조회
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] AI 맞춤 개념영상 리스트 조회", description="AI 맞춤 개념영상 리스트 조회")
//    @PostMapping(value = "/selectCcptVdList")
//    public ResponseDto<List<AiRcmTsshQtmDto>> selectCcptVdList(@Valid @RequestBody AiRcmTsshQtmDto dto) {
//		//세션정보
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//		if(dto.getOptTxbId() == null) {
//			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
//		}
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//		return Response.ok(commService.selectCcptVdList(dto));
//    }
//	
//	/**
//	 * AI 영어 개념영상 리스트 조회
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] AI 영어 차시별 개념영상 리스트 조회", description="AI 맞춤 개념영상 리스트 조회")
//    @PostMapping(value = "/selectEnCcptVdList")
//    public ResponseDto<Map<String, List<AiRcmTsshQtmDto>>> selectEnCcptVdList(@Valid @RequestBody AiRcmTsshQtmDto dto) {
//		//세션정보
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//		if(dto.getOptTxbId() == null) {
//			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
//		}
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//        dto.setSbjCd("EN");
//		return Response.ok(commService.selectCcptVdListByTc(dto));
//    }
//	
//    
//    /**
//	 * AI 추천시험지/문항 이력 저장
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] 풀이내역 저장", description="풀이내역 저장")
//    @PostMapping(value = "/updateAiRcmTsshQtmHist")
//    public ResponseDto<Integer> updateAiRcmTsshQtmHist(@Valid @RequestBody AiRcmTsshQtmResponseDto dto) {
//		log.debug(dto.toString());
//		
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//        dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//    	return Response.ok(aiRcmTsshQtmService.updateAiRcmTsshQtmHist(dto));
//    }
//	
//	
//	/**
//	 * AI 개념맵 조회
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] AI 개념맵 조회", description="AI 개념맵 조회")
//    @PostMapping(value = "/selectTpcMpnList")
//    public ResponseDto<Map<String, List<AlTpcMpnDto>>> selectTpcMpnList(@Valid @RequestBody AlTpcMpnDto dto) {
//		//세션정보
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//		if(dto.getOptTxbId() == null) {
//			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
//		}
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//		return Response.ok(maService.selectTpcMpnList(dto));
//    }
//	
//	
//}
