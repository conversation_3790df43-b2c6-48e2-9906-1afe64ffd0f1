package com.aidt.api.al.pl.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto;
import com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 16:47:32
 * @modify date 2024-05-21 16:47:32
 * @desc AI맞춤학습 진단평가 리포트
 */
@Slf4j
@Tag(name="[al] AI맞춤학습[AlPlStu]", description="AI맞춤학습(학생용)")
@RestController
@RequestMapping("/api/v1/al/pl/stu")
public class AlPlStuReportController {
    
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
    private AlPlStuReportService alPlStuReportService;

	//@Autowired
    //private JwtProvider jwtProvider;

    /**
     * AI맞춤학습 진단 평가 리포트
     * 
     * @return ResponseDto<List<AlPlStuDto>>
     */
    @Operation(summary="AI맞춤학습 진단 평가 리포트 조회", description="AI맞춤학습 진단 평가 리포트 정보를 가져온다.")
    @PostMapping(value = "/selectAlEvCmStuRptList")
    public ResponseDto<AlPlEaEvMainReportDto> selectAlEvCmStuRptList(@RequestBody AlPlEaEvMainReportDto alPlEaEvMainReportDto) {
        log.debug("Entrance selectAlEvCmStuRptList");

        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
        
        AlPlEaEvMainReportDto result = alPlStuReportService.selectAlEvCmStuRptList(securityUserDetailDto.getOptTxbId(),
				securityUserDetailDto.getUsrId(), alPlEaEvMainReportDto);
    	return Response.ok(result);
    }
    
    /**
     * AI 학습단계별 문항,토픽 프로파일정보 조회(임시)
     * 
     * @param mluKmmpNodId 중단원지식맵노드ID
     * @return ResponseDto<List<AlPlStuDto>>
     */
    @Operation(summary="AI 학습단계별 문항,토픽 프로파일정보 조회", description="AI 학습단계별 문항,토픽 프로파일정보를 리포트화면에 그린다.(임시)")
    @PostMapping(value = "/selectEaEvQtmTpcProfInfo")
    public ResponseDto<List<AiRcmTsshQtmHistDto>> selectEaEvQtmTpcProfInfo(@RequestBody AiRcmTsshQtmHistDto dto) {
    	return Response.ok(alPlStuReportService.selectEaEvQtmTpcProfInfo(dto));
    }
    
    
}
