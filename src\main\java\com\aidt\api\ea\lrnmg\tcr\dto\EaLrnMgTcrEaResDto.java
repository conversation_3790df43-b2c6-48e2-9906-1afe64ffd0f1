package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrEaResDto {
	
	@Parameter(name = "운영교과서ID")
	private String optTxbId;
	
	@Parameter(name = "사용자ID")
	private String usrId;
	
	@Parameter(name = "사용자명")
	private String usrNm;
	
	@Parameter(name = "학급ID")
	private String claId;
	
	@Parameter(name = "번호")
	private String stuNo;
	
	@Parameter(name = "전체 과제 수")
	private String totalAssignments;
	
	@Parameter(name = "완료 과제 수")
	private String completedAssignments;
	
	@Parameter(name = "진행중인 과제 수")
	private String ongoingAssignments;
	
	@Parameter(name = "미제출 과제 수")
	private String unsubmittedAssignment;
	
	@Parameter(name = "과제유형코드")
	private String asnTpCd;
	
	@Parameter(name = "학습유형코드")
	private String lrnTpCd;
	
	@Parameter(name = "진해율")
	private String completionPercentage;
}
