package com.aidt.api.ea.evcom.lrnRpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 08:59:오전 8:59
 * @modify date 2024-04-02 08:59:오전 8:59
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptReqDto {
    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="DB ID")
    private String dbId;
    
    @Parameter(name="학습 리포트 조회 기간")
    private String srhKn;

    @Parameter(name="학습 리포트 조회 조건")
    private int smtDtm;

    @Parameter(name="정렬 코드")
    private String order;

    @Parameter(name="단원 학습맵 노드 ID")
    private String luLrmpNodId;

    @Parameter(name="학급 ID")
    private String claId;

}
