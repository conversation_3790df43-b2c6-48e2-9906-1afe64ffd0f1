package com.aidt.api.al.pl.cm.rcm.strategy;

import java.util.EnumSet;
import java.util.List;

import org.springframework.stereotype.Component;

import com.aidt.api.al.pl.cm.cache.cacheService;
import com.aidt.api.al.pl.cm.rcm.adapter.AiQuestionProfileAdapter;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEv;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvLoader;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationCode;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.api.common.enums.SubjectCode;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class MathProfileStrategy implements ProfileStrategy {

	private final AiQuestionProfileAdapter aiQuestionProfileAdapter;
	private final cacheService cacheService;

	@Override
	public boolean isSupported(SubjectCode subjectCode) {
		return EnumSet.range(SubjectCode.MA, SubjectCode.CM2).contains(subjectCode);
	}

	@Override
	public void completeAiEvaluationTestRange(EaAiEvLoader eaAiEvLoader) {

		var eaAiEvs = List.copyOf(eaAiEvLoader.getEaAiEvs());
		//AI 맞춤 진단 외에는 한 평가당 같은 토픽으로 구성되어 있다고 한다.
		var eaAiEv = eaAiEvs.stream()
			//.distinct() //혹시 몰라 평가번호, 운영교과서, 대단원, 중단원, 차시, 토픽의 노드 ID  EqualsAndHashCode 구현
			.findFirst().orElseThrow(() -> new IllegalArgumentException("AI 평가 정보를 찾을 수 없습니다."));

		//AI평가가 아니면 중단원 기준으로 전체 완료 처리
		if (!EvaluationCode.AE.equals(eaAiEv.getEvaluationCode())) {
			eaAiEv.clearMidUnitChildIds();
			eaAiEv.learningUnitEvaluationComplete();
			aiQuestionProfileAdapter.updateEaAiEvRange(eaAiEv);
			return;
		}

		//AI 맞춤 진단일 경우 AI 예측 평균 정답률 8할보다 클 경우 예외(X) 처리
		if (EvaluationDetailCode.OV.equals(eaAiEv.getEvaluationDetailCode())) {
			eaAiEvs.stream()
				.filter(EaAiEv::isPredictedHigh)
				.forEach(e -> {
					e.learningUnitEvaluationError();
					aiQuestionProfileAdapter.updateEaAiEvRange(e);
				});
			return;
		}
		//AI 맞춤 외는 토픽의 완료 여부를 따라 처리
		eaAiEv.changeLearningUnitEvaluationByTopicCompletion(eaAiEvLoader.isTopicCompleted());
		aiQuestionProfileAdapter.updateEaAiEvRange(eaAiEv);
	}

	@Override
	public void updateAiLearningLevel(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public void updateTopicProficiency(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public void updateTopicLearningOrder(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public void saveLearningProgressProfile(EaAiEvLoader eaAiEvLoader) {

	}

	@Override
	public String getPointCode(EaAiEvLoader eaAiEvLoader) {

		var evaluationDetailCode = eaAiEvLoader.getEaAiEv()
			.getEvaluationDetailCode();

		switch (evaluationDetailCode) {
			case OV:
				return "AI_CM_01";
			case C1:
			case C2:
				return "AI_CM_06";
			default:
				return "";
		}
	}
}