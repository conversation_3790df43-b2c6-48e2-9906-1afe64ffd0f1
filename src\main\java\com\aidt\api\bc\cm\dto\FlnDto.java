package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "감정상태")
public class FlnDto {

	@Parameter(name="감정상태코드")
	private String flnStCd;

	@Parameter(name="사용자ID")
	private String usrId;

	private String crtrId;
	private String crtDtm;
	private String mdfrId;
	private String mdfDtm;
	private String dbId;



}
