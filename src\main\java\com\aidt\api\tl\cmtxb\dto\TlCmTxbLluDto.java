package com.aidt.api.tl.cmtxb.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-14 10:13:33
 * @modify date 2024-02-14 10:13:33
 * @desc [TlCmTxbLluDto 교과학습 대단원, 차시 조회 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbLluDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    /** 학습맵 노드 ID */
    @Parameter(name="학습맵 노드 ID")
    private String lrmpNodId;

    /** 상위 노드 ID */
    @Parameter(name="상위 노드 ID")
    private String urnkLrmpNodId;
    
    /** 대단원 노드 ID */
    @Parameter(name="대단원 노드 ID")
    private String lluNodId;
    
    /** 학습맵 노드명 */
    @Parameter(name="학습맵 노드명")
    private String lrmpNodNm;
    
    /** 깊이 */
    @Parameter(name="깊이")
    private int dpth;
    
    /** 재구성 순서 */
    @Parameter(name="재구성 순서")
    private int rcstnOrdn;
    
    /** 재구성 번호 */
    @Parameter(name="재구성 번호")
    private int rcstnNo;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;
    
    /** 단원번호사용여부 */
    @Parameter(name="단원번호사용여부")
    private String luNoUseYn;

    /** 학습완료여부 */
    @Parameter(name="학습완료여부")
    private String lrnCmplYn;
    
    /** 학습가능여부 */
    @Parameter(name="학습가능여부")
    private String lrnAbleYn;
    
    /** 단원노출여부 */
    @Parameter(name="단원노출여부")
    private String luEpsYn;
    
    /** 학습상태 */
    @Parameter(name="학습상태")
    private String lrnSt;
    
    /** 활동ID*/
    @Parameter(name="활동ID")
    private String lrnAtvId;
    
    /** 평가ID*/
    @Parameter(name="평가ID")
    private String evId;

    /** 평가상세구분코드*/
    @Parameter(name="평가상세구분코드")
    private String evDtlDvCd;

    /** 평가사용여부*/
    @Parameter(name="평가사용여부")
    private String evUseYn;   
    
    /** 평가완료여부*/
    @Parameter(name="평가완료여부")
    private String evCmplYn;
    
    /** 평가잠금여부*/
    @Parameter(name="평가잠금여부")
    private String evLcknYn;   
    
    /** 평가지유형코드*/
    @Parameter(name="평가지유형코드")
    private String evshTpCd; 
    
    /** 썸네일 PC PATH*/
    @Parameter(name="썸네일 PC PATH")
    private String pcThbPth; 
    
    /** 썸네일 TA PATH*/
    @Parameter(name="썸네일 TA PATH")
    private String taThbPth; 

    /** 중단원, 차시*/
    @Parameter(name="중단원, 차시")
    private List<TlCmTxbTcDto> child;
}
