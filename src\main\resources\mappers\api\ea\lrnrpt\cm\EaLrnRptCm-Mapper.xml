<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.lrnrpt.cm">
	<!-- 학습리포트 > 학습현황 > Let's Talk -->
	<select id="selectTalkList" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTalkTpcDto">
		 SELECT LLU.KMMP_NOD_ID AS kmmpNodId		-- 대단원노드ID(레슨ID)
		     , LLU.KMMP_NOD_NM 	AS kmmpNodNm		-- 대단원노드명(레슨명)
		     , TPC.TPC_KMMP_NOD_ID 	AS tpcKmmpNodId		-- 토픽노드ID 
		     , TPC.TPC_KMMP_NOD_NM 	AS tpcKmmpNodNm		-- 토픽노드명
		     , COALESCE(TK.LRN_ST_CD, 'NL')	AS lrnStCd					-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
		     , LLU.TC_USE_YN AS tcUseYn					-- 차시사용여부
             , LLU.LCKN_YN AS lcknYn					-- 잠금여부
             , LLU.USE_YN AS useYn					    -- 사용여부
		     , LLU.RCSTN_ORDN AS rcstnOrdn				-- 재구성 순서
		     , TPC.SRT_ORDN	AS srtOrdn					-- 토픽 순서
		     , TK.CRT_DTM AS crtDtm				`		-- 토픽 생성일자
		     , DATE_FORMAT(TK.MDF_DTM, '%Y.%m.%d') AS mdfDtm						-- 토픽 업데이트 일자
		     , TIMESTAMPDIFF(SECOND, TK.CRT_DTM, TK.MDF_DTM) AS lrnTime  -- 학습 시간(분)
		     , RANK() OVER (PARTITION BY LLU.KMMP_NOD_ID ORDER BY TPC.TPC_KMMP_NOD_ID) AS nodRank	-- 노드랭크
		 FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND TPC.DEL_YN = 'N'
		   AND LLU.LCKN_YN = 'N'
		  LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
		    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND TPC.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND TPC.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = #{userId}							-- 파라미터: 학생ID
		 WHERE 1=1
		  and TPC.TW_WRT_DV_CD = 'T'								-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId} 				-- 파라미터: 운영교과서ID
		 ORDER BY LLU.RCSTN_ORDN ASC, LLU.KMMP_NOD_ID asC,  TPC.SRT_ORDN
		/*학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectTalkList */
	</select>
	
	<!-- 학습리포트 > 학습현황 > 선생님추천학습 -->
	<select id="selectSlSpLrnRptList" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptSlSpLrnContentDto">
		 WITH RECURSIVE RCS AS (
		    SELECT 
		        R1.SP_LRN_ID,		--  특별학습ID 
		        R1.SP_LRN_NM,       --  특별학습명 
		        R2.SP_LRN_NOD_ID as F_DPTH_SP_LRN_NOD_ID,	--  1DPTH SP_LRN_ID 
		        R2.SP_LRN_NOD_NM AS F_DPTH_SP_LRN_NOD_NM,	--  1DPTH SP_LRN_NM 
		        CAST(R2.SP_LRN_NOD_ID AS CHAR(30)) AS S_DPTH_SP_LRN_NOD_ID,	--  2DPTH SP_LRN_ID 
		        CAST(R2.SP_LRN_NOD_NM AS CHAR(100)) AS S_DPTH_SP_LRN_NOD_NM,	--  2DPTH SP_LRN_NM 
		        R2.SP_LRN_NOD_ID,
		        R2.SP_LRN_NOD_NM,   
		        CAST('' AS CHAR(30)) AS URNK_SP_LRN_NOD_ID2,
		        CAST('' AS CHAR(100)) AS URNK_SP_LRN_NOD_NM2,
		        R2.DPTH,
		        R2.LWS_YN,
		        R2.CSTN_CMPL_YN
		    FROM LMS_LRM.SL_SP_LRN_RCSTN R1 	--  SL_특별학습재구성 
		    INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 --  BC_특별학습노드 
		        ON R1.SP_LRN_ID = R2.SP_LRN_ID
		        AND R2.DEL_YN = 'N'
		        AND R2.DPTH = 1
		        AND R2.URNK_SP_LRN_NOD_ID IS NULL
		    WHERE R1.OPT_TXB_ID = #{optTxbId}
		      AND R1.USE_YN = 'Y'
		    
		    UNION ALL
		    
		    SELECT 
		        R2.SP_LRN_ID,		--  특별학습ID 
		        R1.SP_LRN_NM,       --  특별학습명 
		        R1.F_DPTH_SP_LRN_NOD_ID, --  1DPTH SP_LRN_ID 
		        R1.F_DPTH_SP_LRN_NOD_NM, --  1DPTH SP_LRN_NM 
		        CASE WHEN R2.DPTH = 2 THEN R2.SP_LRN_NOD_ID ELSE R1.S_DPTH_SP_LRN_NOD_ID END AS S_DPTH_SP_LRN_NOD_ID, -- 2DPTH 일 때만 값 설정
        		CASE WHEN R2.DPTH = 2 THEN R2.SP_LRN_NOD_NM ELSE R1.S_DPTH_SP_LRN_NOD_NM END AS S_DPTH_SP_LRN_NM,     -- 2DPTH 일 때만 값 설정
		        R2.SP_LRN_NOD_ID,	--  마지막 노드ID 
		        R2.SP_LRN_NOD_NM,   --  마지막 노드 명 
		        IF(R2.DPTH = 2, R2.SP_LRN_NOD_ID, R2.URNK_SP_LRN_NOD_ID) AS URNK_SP_LRN_NOD_ID2, --  마지막 노드의 상위 NOD ID 
		        IF(R2.DPTH = 2, R2.SP_LRN_NOD_NM, R1.SP_LRN_NOD_NM) AS URNK_SP_LRN_NOD_NM2, --  마지막 노드의 상위 NOD NM 
		        R2.DPTH,
		        R2.LWS_YN,
		        R2.CSTN_CMPL_YN
		    FROM RCS R1
		    INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 -- BC_특별학습노드
		        ON R1.SP_LRN_ID = R2.SP_LRN_ID
		        AND R1.SP_LRN_NOD_ID = R2.URNK_SP_LRN_NOD_ID
		        AND R2.DEL_YN = 'N'
		)
		SELECT
		    R.SP_LRN_ID,		--  특별학습ID 
			R.SP_LRN_NM,		--  특별학습명 
			R.F_DPTH_SP_LRN_NOD_ID,		--  1DPTH SP_LRN_ID 
			R.F_DPTH_SP_LRN_NOD_NM,		--  1DPTH SP_LRN_NM 
			R.S_DPTH_SP_LRN_NOD_ID,
			R.S_DPTH_SP_LRN_NOD_NM,
			R.SP_LRN_NOD_ID AS L_DPTH_SP_LRN_NOD_ID,		--  마지막 노드ID 
			R.SP_LRN_NOD_NM AS L_DPTH_SP_LRN_NOD_NM,		-- 마지막 노드 명 
			R.DPTH AS L_DPTH,			--  마지막 노드의 DPTH 
			R.LWS_YN,					--  마지막 노드인지 여부 
			R.CSTN_CMPL_YN,				--  마지막 노드의 콘텐츠 존재 여부 
		    CNT.SP_LRN_NOD_ID AS cSpLrnNodId,			--  마지막 노드의 콘텐츠 테이블의 NOD_ID 
		    CNT.SP_LRN_CTN_ID,			--  마지막 노드의 콘텐츠 ID 
		    CNT.SP_LRN_CTN_NM,			--  마지막 노드의 콘텐츠 NM 
		    CNT.CRT_DTM AS cCrtDtm,				--  마지막 노드의 콘텐츠 생성일자 
		    CNT.MDF_DTM	AS cMdfDtm,				--  마지막 노드의 콘텐츠 수정일자 
			IFNULL(P.LRN_ST_CD,"NL") AS LRN_ST_CD, 				--  PGRS 의 학습 상태값 NL(학습전), DL(학습중), CL(학습완료) 
			IFNULL(P.LRN_TM_SCNT,0) AS LRN_TM_SCNT,				--  PGRS 의 학습시간초수 
		    DATE_FORMAT(P.MDF_DTM, '%m. %d.') AS pMdfDtm,		 -- PGRS 의 수정일자 
		    P.MDF_DTM AS pMdfDtmFlag		 -- PGRS 의 수정일자 
		FROM RCS R
		INNER JOIN LMS_CMS.BC_SP_LRN_CTN CNT ON R.SP_LRN_NOD_ID = CNT.SP_LRN_NOD_ID AND CNT.DEL_YN = 'N'
		<if test="usrTpCd != null and usrTpCd == 'ST'">
		left JOIN LMS_LRM.SL_STU_RCM_LRN SSRL ON R.SP_LRN_ID = SSRL.SP_LRN_ID  
		    AND SSRL.OPT_TXB_ID = #{optTxbId} 
		    AND SSRL.LRN_USR_ID  = #{userId}
		</if>
		LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST P on CNT.SP_LRN_CTN_ID = P.SP_LRN_CTN_ID 
												and P.OPT_TXB_ID = #{optTxbId} 
												and P.LRN_USR_ID = #{userId}
		<if test="usrTpCd != null and usrTpCd == 'ST'">
												and SSRL.SP_LRN_ID = P.SP_LRN_ID
		</if>
		WHERE R.LWS_YN = 'Y'
		AND R.CSTN_CMPL_YN = 'Y'		--  마지막 노드에서 콘텐츠가 존재하는 것만 가져오기 
		AND R.DPTH <![CDATA[<=]]>  2
		<if test="usrTpCd != null and usrTpCd == 'ST'">
			AND SSRL.RCM_YN = 'Y'
		</if>
		/*학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectSlSpLrnRptList */
	</select>
	
	<!-- 우리반 수업 > 최근 학습활동ID 취득처리 (TL : selectLrnAtvStChk) -->
    <select id="selectTlRptAtvStChk" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvStChkDto">
        SELECT
                 R.OPT_TXB_ID
                ,R.LRMP_NOD_ID
                ,R.LRMP_NOD_NM
                ,IFNULL(S.LRMP_NOD_ID,'')                                                      AS URNK_LRMP_NOD_ID
                ,IFNULL(S.LRMP_NOD_NM,'')                                                      AS URNK_LRMP_NOD_NM
                ,R.DPTH                                                                        AS DEPTH
                ,SUM(IF(R.DPTH  <![CDATA[>]]>  1 AND A.OPT_TXB_ID IS NOT NULL, 1, 0) )         AS CNT_TOT
                ,SUM(IF(R.DPTH  <![CDATA[>]]>  1 AND B.LRN_ST_CD = 'CL', 1, 0))                AS CNT_CL /* 학습완료 */
                ,SUM(IF(R.DPTH  <![CDATA[>]]>  1 AND B.LRN_ST_CD  <![CDATA[<>]]>  'CL', 1, 0)) AS CNT_NL
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  /* TL_교과학습노드재구성 */
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN S
                    ON R.OPT_TXB_ID = S.OPT_TXB_ID
                    AND R.LLU_NOD_ID = S.LRMP_NOD_ID
                    AND S.USE_YN = 'Y'
                    AND S.DPTH = 1
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN A  /* TL_교과학습활동재구성 */
                    ON  R.OPT_TXB_ID = A.OPT_TXB_ID
                    AND R.LRMP_NOD_ID = A.LRMP_NOD_ID
                    AND A.USE_YN = 'Y'
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST B /* TL_교과학습활동상태 */
                    ON A.OPT_TXB_ID = B.OPT_TXB_ID
                    AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_ATV_ID = B.LRN_ATV_ID
                    AND B.LRN_USR_ID = #{userId} /* 학습사용자*/
        WHERE R.OPT_TXB_ID = #{optTxbId}
        AND R.DPTH = '4' /* 차시만 취득*/
        AND R.USE_YN = 'Y'
        AND R.LU_EPS_YN = 'Y'
        <if test='usrTpCd != "T"'>
            AND R.LCKN_YN = 'N'
        </if>
        GROUP BY OPT_TXB_ID, LRMP_NOD_ID
        ORDER BY S.RCSTN_ORDN ASC, R.URNK_LRMP_NOD_ID ASC, R.RCSTN_ORDN ASC

        /* 교과학습 지향난 EaLrnRptCm-Mapper.xml - selectTlRptAtvStChk */
    </select>
    
    <!-- 마지막 학습노드ID 찾기 (TL : selectLastLrmpNodId)-->
    <select id="selectLastLrmpNodIdRpt" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="String">
        SELECT A.LRMP_NOD_ID
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
        LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST B /* TL_교과학습활동상태 */
             ON A.OPT_TXB_ID= B.OPT_TXB_ID
             AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
             AND A.LRN_ATV_ID = B.LRN_ATV_ID
             AND B.LRN_USR_ID = #{userId} /* 학습사용자 */
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.USE_YN = 'Y'
        AND B.OPT_TXB_ID IS NOT NULL
        ORDER BY B.MDF_DTM DESC, A.LRN_ATV_ID DESC
        LIMIT 1
        /* 교과학습 지향난 EaLrnRptCm-Mapper.xml - selectLastLrmpNodIdRpt */
    </select>
    
	<!-- 우리반 수업 > 차시 목록 조회 (TL : selectTxbTcList)-->
    <select id="selectEaLrnRptTxbTcList" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlDto">
        SELECT
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* 학습맵노드ID */
              ,A.URNK_LRMP_NOD_ID /*상위노드ID*/
              ,A.LRMP_NOD_NM  /* 학습맵노드명 */
              ,A.DPTH  /* 깊이 */
              ,A.RCSTN_ORDN  /* 재구성순서 */
              ,A.LCKN_YN  /* 잠금여부 */
              ,A.USE_YN  /* 사용여부 */
              ,A.LU_NO_USE_YN	/* 단원번호사용여부 */
          FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */ 
         WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
         <if test='lcknYn != null and lcknYn neq ""'>
            AND A.LCKN_YN = #{lcknYn}
         </if>
         AND A.LU_EPS_YN = 'Y'
         AND A.USE_YN = 'Y'
         <!-- 
         <if test='useYn != null and useYn neq ""'>
            AND A.USE_YN = #{useYn}
         </if>
          -->
         ORDER BY RCSTN_ORDN ASC, LRMP_NOD_ID ASC

        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectEaLrnRptTxbTcList */

    </select>
    
    
    <!-- 우리반 수업 > 해당 차시의 교과서 공부 학습현황 조회 -->
    <select id="selectRptLrnTocStatList" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvStaDto">
          SELECT 
          	*
          FROM
	      (
	          SELECT 
	                A.LRMP_NOD_ID	 /* 학습맵노드ID */
	               ,A.LRN_ATV_ID	 /* 학습활동ID */
	               ,A.CTN_CD	      /* 콘텐츠코드 */
	               ,A.LRN_ATV_NM	 /* 학습활동명 */
	               ,A.CTN_TP_CD	 /* 콘텐츠유형코드 */
	               ,IFNULL(C.LRN_ST_CD, 'NL') AS LRN_ST_CD  /* 학습상태코드 */
	               ,C1.CM_CD_NM               AS LRN_ST_NM  /* 학습상태명 */
	               ,IFNULL(C.LRN_TM_SCNT, 0)  AS LRN_TM_SCNT  /* 학습시간(초) */
	               ,DATE_FORMAT(C.MDF_DTM, '%Y. %m. %d') AS LRN_DT /* 학습일자 */
	               ,'' AS LRN_ATV_THB_CDN_PATH  /* 콘텐츠썸네일CDN */
			       ,B.SRT_ORDN AS SRT_ORDN
			       ,A.RCSTN_ORDN AS RCSTN_ORDN
	          FROM ( SELECT 
					    	R.OPT_TXB_ID,
					        R.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        R.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        R.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        R.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'N' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
					    WHERE R.OPT_TXB_ID = #{optTxbId}
					      AND R.LRMP_NOD_ID = #{lrmpNodId}
					      
						UNION ALL
						
					    SELECT 
					        R.OPT_TXB_ID,
					        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        TTRCM.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        TTRCM.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        TTRCM.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'Y' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
					        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
					        and ttrcm.del_yn = 'N'
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
					        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
					    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
					      AND TTRCM.LRMP_NOD_ID = #{lrmpNodId}
					      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
					      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
	               INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
	                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
	                    AND A.LRN_STP_ID = B.LRN_STP_ID
	                    AND B.LRN_STP_DV_CD = 'CL' /* CL=개념*/
	                    AND B.DEL_YN = 'N'
	               LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C /* TL_교과학습활동상태 */
	                    ON A.OPT_TXB_ID = C.OPT_TXB_ID
	                    AND A.ORGL_LRMP_NOD_ID = C.LRMP_NOD_ID
	                    AND A.LRN_ATV_ID = C.LRN_ATV_ID
	                    AND C.LRN_USR_ID = #{userId}
	               LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
	                    ON C1.URNK_CM_CD = 'LRN_ST_CD'
	                    AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
	                    AND C1.LMS_USE_YN = 'Y'
	                    AND C1.DEL_YN = 'N'
	                    AND C1.CM_CD = IFNULL(C.LRN_ST_CD, 'NL')
	               LEFT JOIN LMS_LRM.CM_CM_CD C2 /* CM_공통코드 */
	                    ON C1.URNK_CM_CD = C2.CM_CD
	                    AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
	                    AND C2.LMS_USE_YN = 'Y'
	                    AND C2.DEL_YN = 'N'
	          WHERE A.OPT_TXB_ID = #{optTxbId}
	          AND A.LRMP_NOD_ID = #{lrmpNodId}
	          AND A.USE_YN = 'Y'
	          
	          UNION ALL
	          
	          SELECT
			        M.LRMP_NOD_ID
			       ,M.TCR_REG_CTN_ID AS LRN_ATV_ID 	/* 학습활동ID */
			       ,'' AS CTN_CD 					/* 콘텐츠코드 */
			       ,C.TCR_REG_CTN_NM AS LRN_ATV_NM 	/* 학습활동명 */
			       ,C.TP_CD AS CTN_TP_CD
			       ,IFNULL(T.LRN_ST_CD, 'NL') AS LRN_ST_CD /* 학습상태코드 */
			       ,C1.CM_CD_NM               AS LRN_ST_NM  /* 학습상태명 */
			       ,T.LRN_TM_SCNT AS LRN_TM_SCNT 			/* 학습시간(초) */
			       ,DATE_FORMAT(T.MDF_DTM, '%Y. %m. %d') AS LRN_DT /* 학습일자 */
			       ,'' AS LRN_ATV_THB_CDN_PATH  					/* 콘텐츠썸네일CDN */
			       ,S.SRT_ORDN AS SRT_ORDN
			       ,M.RCSTN_ORDN AS RCSTN_ORDN
			    FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			        ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
			        AND C.TP_CD <![CDATA[<>]]> 'AT'
			    INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			        ON M.OPT_TXB_ID = R.OPT_TXB_ID
			        AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
			    INNER JOIN LMS_CMS.bc_lrn_stp S
			        ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			        AND M.LRN_STP_ID = S.LRN_STP_ID
			        AND S.DEL_YN = 'N'
			    LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
			        ON M.OPT_TXB_ID = T.OPT_TXB_ID
			        AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
			        AND T.LRN_USR_ID = #{userId}
			    LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
			        ON C1.URNK_CM_CD = 'LRN_ST_CD'
			        AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
			        AND C1.LMS_USE_YN = 'Y'
			        AND C1.DEL_YN = 'N'
			        AND C1.CM_CD = IFNULL(T.LRN_ST_CD, 'NL')
			    WHERE M.OPT_TXB_ID = #{optTxbId}
			    AND M.LRMP_NOD_ID = #{lrmpNodId}
			    AND M.DEL_YN = 'N'
			    AND M.USE_YN = 'Y'
	     )ATV
	     ORDER BY ATV.SRT_ORDN ASC, ATV.RCSTN_ORDN ASC, ATV.LRN_ATV_ID ASC
          
        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectRptLrnTocStatList */
    </select>
   
   	<!-- 학습현황 > 우리반 수업 > 수학 익힘책(Summary) (TL : selectLrnAtvWkbSum) -->
   	<select id="selectLrnAtvWkbSumRpt" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="Map">
        SELECT
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* LCMS교과서_지식맵_키 */
              ,(SELECT CAST(NOD_NO AS CHAR(3))
                FROM (SELECT XXA.LRMP_NOD_ID
                            ,@RANK := @RANK +1 AS NOD_NO
                      FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN XXA
                          ,(SELECT @RANK :=0) XXB
                            WHERE XXA.OPT_TXB_ID = A.OPT_TXB_ID
                            AND IFNULL(XXA.URNK_LRMP_NOD_ID, '') = ''
                            AND XXA.USE_YN = 'Y'
                            ORDER BY XXA.RCSTN_ORDN ASC, XXA.LRMP_NOD_ID ASC
                            ) XA
                      WHERE XA.LRMP_NOD_ID = D.LRMP_NOD_ID)             AS NOD_NO /* 대단원NO */
              ,max(D.LRMP_NOD_NM) AS LRMP_NOD_NM1  /* 지식맵노드명(대단원) */
              -- ,A.USE_YN  /* 사용여부 */
               ,max(E.LRN_ATV_ID) as LRN_ATV_ID /* 학습활동ID */
              -- ,E.LRN_ATV_NM /* 학습활동명 */
              -- ,E.EV_ID
              -- ,E.RCSTN_ORDN
              -- ,F.LRN_STP_CD /* 학습단계코드 */
              -- ,F.LRN_STP_NM /* 학습단계명 */
              -- ,IFNULL(G.LRN_ST_CD, 'NL')                             AS LRN_ST_CD /* 학습상태코드 미학습(NL) */
              ,COUNT(1)                                                 AS WKB_TOT_CNT /* 총문제수 */
              ,SUM(IF(G.LRN_ST_CD = 'CL', 1, 0))                        AS WKB_FIN_CNT /* 학습활동완료건수*/
              ,IFNULL(SUM(G.LRN_TM_SCNT), 0)  										AS LRN_TM_SCNT	/* 총 학습 시간 */
              ,A.WKB_STR_PGE_NO /* 익힘책시작페이지번호 */
              ,A.WKB_END_PGE_NO /* 익힘책종료페이지번호 */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성-차시 */
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B /* TL_교과학습노드재구성- 소단원 */
                  ON A.OPT_TXB_ID = B.OPT_TXB_ID
                  AND A.URNK_LRMP_NOD_ID = B.LRMP_NOD_ID
                  AND B.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C /* TL_교과학습노드재구성- 중단원 */
                  ON B.OPT_TXB_ID = C.OPT_TXB_ID
                  AND B.URNK_LRMP_NOD_ID = C.LRMP_NOD_ID
                  AND C.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D /* TL_교과학습노드재구성- 대단원 */
                  ON C.OPT_TXB_ID = D.OPT_TXB_ID
                  AND C.URNK_LRMP_NOD_ID = D.LRMP_NOD_ID
                  AND D.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN E /* TL_교과학습활동재구성 */
                  ON E.OPT_TXB_ID = A.OPT_TXB_ID
                  AND E.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND E.USE_YN = 'Y'
             INNER JOIN LMS_CMS.BC_LRN_STP F /* BC_학습단계 */
                   ON E.LRMP_NOD_ID = F.LRMP_NOD_ID
                   AND E.LRN_STP_ID = F.LRN_STP_ID
                   AND F.LRN_STP_DV_CD = 'WB' /* 학습단계 구분(WB: 익힘책) */
                   AND F.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST G  /* TL_교과학습활동상태 */
                  ON G.OPT_TXB_ID = E.OPT_TXB_ID
                  AND G.LRMP_NOD_ID = E.LRMP_NOD_ID
                  AND G.LRN_ATV_ID = E.LRN_ATV_ID
                  AND G.LRN_USR_ID = #{userId}
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
	        AND A.LRMP_NOD_ID = #{lrmpNodId} /* LCMS교과서_지식맵_키 */
	        AND A.USE_YN = 'Y'  /* 사용여부 */
		GROUP BY
		    A.OPT_TXB_ID, 
		    A.LRMP_NOD_ID
        ORDER BY A.RCSTN_ORDN ASC

        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectLrnAtvWkbSumRpt */
    </select>
   	
	  
	<!-- 학습 현황 > 우리반 수업 평가정보조회( 학생 TL : selectLrnEvInf) -->
    <select id="selectLrnEvInfoRpt" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvEvDto">
        SELECT A.LRMP_NOD_ID                              /* 차시노드ID */
              ,A.LRN_ATV_ID                               /* 활동ID */
              ,B.EV_ID                                    /* 평가ID */
              ,B.EV_DTL_DV_CD                             /* 평가상세구분코드 */
              ,C2.CM_CD_NM               AS EV_DTL_DV_NM  /* 평가상세구분코드명 */
              ,B.EV_NM                                    /* 평가명 */
              ,B.FNL_QST_CNT                              /* 최종문제수 */
              ,B.LCKN_YN                                  /* 잠금여부 */
              ,B.RTXM_PMSN_YN                             /* 재응시허용여부 */
              ,IFNULL(D.LRN_TM_SCNT, 0)  AS LRN_TM_SCNT /* 학습시간(초) - 학습현황에서 사용된 */
              ,B.XPL_TM_SETM_YN                            /* 풀이시간설정여부 */
              ,CAST(IFNULL(B.XPL_TM_SCNT, 0)/60 AS UNSIGNED) AS XPL_TM_SCNT  /* 풀이시간초수 */
              ,IFNULL(C.EV_CMPL_YN, 'N')                     AS EV_CMPL_YN   /* 평가완료여부 */
              ,IFNULL(D.EXTR_EV_ID,'')                       AS EXTR_EV_ID   /*추가평가ID*/
              ,IFNULL(D.LRN_ST_CD, 'NL')					AS LRN_ST_CD /*학습상태코드*/
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A  /* TL_교과학습활동재구성 */
             INNER JOIN LMS_LRM.EA_EV B      /* EA_평가 */
                   ON A.OPT_TXB_ID = B.OPT_TXB_ID
                   AND A.EV_ID = B.EV_ID
             LEFT JOIN LMS_LRM.EA_EV_RS C    /* EA_평가결과 */
                  ON B.EV_ID = C.EV_ID
                  AND C.USR_ID = #{userId} /* 학생ID */
             LEFT JOIN LMS_LRM.CM_CM_CD C1
                  ON C1.CM_CD ='EV_DTL_DV_CD' 
                  AND C1.LMS_USE_YN = 'Y' 
                  AND C1.DEL_YN='N'
                  AND NOW() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
             LEFT JOIN LMS_LRM.CM_CM_CD C2
                  ON C1.CM_CD = C2.URNK_CM_CD
                  AND C2.LMS_USE_YN = 'Y' 
                  AND C2.DEL_YN='N'
                  AND NOW() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                  AND C2.CM_CD = B.EV_DTL_DV_CD
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D
                  ON A.OPT_TXB_ID = D.OPT_TXB_ID
                  AND A.LRMP_NOD_ID = D.LRMP_NOD_ID
             	   AND A.LRN_ATV_ID = D.LRN_ATV_ID
             	   AND D.LRN_USR_ID = #{userId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.LRMP_NOD_ID = #{lrmpNodId}
        AND A.CTN_TP_CD = 'EX'
        AND A.USE_YN = 'Y'
        AND B.DEL_YN = 'N'
        AND B.USE_YN = 'Y'
        ORDER BY A.RCSTN_ORDN ASC, A.LRMP_NOD_ID ASC
        LIMIT 1

        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectLrnEvInfoRpt */
    </select>
    
    <!-- 학습 현황 > 우리반 수업 평가문항목록조회( 학생 TL : selectLrnEvQtmList) -->
    <select id="selectLrnEvQtmRptList" parameterType="Map" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvEvQtmDto">
        SELECT A.EV_ID           /* 평가ID */
              ,B.QTM_ID          /* 문항ID */
              ,B.QTM_ORDN        /* 문항순서 */
              ,C.USR_ID          /* 사용자ID */
              ,IFNULL(C.CANS_YN, '') AS CANS_YN  /* 정답여부 */
              ,C.XPL_TM_SCNT		/* 문항별 풀이 시간 */
        FROM LMS_LRM.EA_EV A /* EA_평가 */
             INNER JOIN LMS_LRM.EA_EV_QTM B /* EA_평가문항 */
                   ON A.EV_ID = B.EV_ID
                   AND B.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.EA_EV_QTM_ANW C /* EA_평가문항답변 */
                  ON B.EV_ID = C.EV_ID
                  AND B.QTM_ID = C.QTM_ID
                  AND C.USR_ID = #{userId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.DEL_YN = 'N'
        AND A.USE_YN = 'Y'
        AND A.EV_ID = #{evId}
        ORDER BY B.QTM_ORDN ASC, B.QTM_ID ASC

        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectLrnEvQtmRptList */
    </select>
    
    <!-- 학습 현황 > 우리반 수업 평가문항목록조회( 교사 TL : selectLrnAtvEvSum) -->
    <select id="selectLrnAtvEvSumRpt" parameterType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto" resultType="Map">
        SELECT 
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* LCMS교과서_지식맵_키 */
                ,(SELECT CAST(NOD_NO AS CHAR(3))
                FROM (SELECT XXA.LRMP_NOD_ID
                            ,@RANK := @RANK +1 AS NOD_NO
                      FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN XXA  /* TL_교과학습노드재구성 */
                          ,(SELECT @RANK :=0) XXB
                      WHERE XXA.OPT_TXB_ID = A.OPT_TXB_ID
                      AND IFNULL(XXA.URNK_LRMP_NOD_ID, '') = ''
                      AND XXA.USE_YN = 'Y'
                      ORDER BY XXA.RCSTN_ORDN ASC, XXA.LRMP_NOD_ID ASC
                     ) XA
                WHERE XA.LRMP_NOD_ID = D.LRMP_NOD_ID)                   AS NOD_NO /* 대단원NO */
              ,D.LRMP_NOD_NM AS LRMP_NOD_NM1  /* 지식맵노드명(대단원) */
              ,A.USE_YN  /* 사용여부 */
              ,E.LRN_ATV_ID /* 학습활동ID */
              ,E.LRN_ATV_NM /* 학습활동명 */
              ,E.EV_ID
              ,E.RCSTN_ORDN
              ,IFNULL(F.LRN_STP_CD, F.LRN_STP_ID)                       AS LRN_STP_CD /* 학습단계코드 */
              ,F.LRN_STP_NM /* 학습단계명 */
              ,IFNULL(G.LRN_ST_CD, 'NL')                                AS LRN_ST_CD /* 학습상태코드 */
              ,IFNULL(H.FNL_QST_CNT, 0)                                 AS EV_TOT_CNT /* 총문제수 */
              ,IFNULL(H.XPL_TM_SETM_YN, 'N')                            AS XPL_TM_SETM_YN /* 풀이시간설정여부 */
              ,CAST(IFNULL(H.XPL_TM_SCNT, 0)/60 AS UNSIGNED)            AS XPL_TM_MIN /* 풀이시간(분) */
              ,IFNULL((SELECT XA.CANS_CNT
                       FROM LMS_LRM.EA_EV_RS XA /* EA_평가결과 */
                       WHERE XA.EV_ID = E.EV_ID
                           AND XA.EV_CMPL_YN = 'Y' /* 평가완료여부 */
                           AND XA.USR_ID = G.LRN_USR_ID),0)             AS EV_FIN_CNT /* 학습활동완료건수*/
               ,G.EXTR_EV_ID  /* 추가평가ID */
               ,RS.EV_TM_SCNT		/* 평가에 따른 풀이 시간 */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성-차시 */
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B /* TL_교과학습노드재구성- 소단원 */
                  ON A.OPT_TXB_ID = B.OPT_TXB_ID
                  AND A.URNK_LRMP_NOD_ID = B.LRMP_NOD_ID
                  AND B.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C /* TL_교과학습노드재구성- 중단원 */
                  ON B.OPT_TXB_ID = C.OPT_TXB_ID
                  AND B.URNK_LRMP_NOD_ID = C.LRMP_NOD_ID
                  AND C.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D /* TL_교과학습노드재구성- 대단원 */
                  ON C.OPT_TXB_ID = D.OPT_TXB_ID
                  AND C.URNK_LRMP_NOD_ID = D.LRMP_NOD_ID
                  AND D.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN E /* TL_교과학습활동재구성 */
                  ON E.OPT_TXB_ID = A.OPT_TXB_ID
                  AND E.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND E.USE_YN = 'Y'
             INNER JOIN LMS_CMS.BC_LRN_STP F /* BC_학습단계 */
                  ON E.LRMP_NOD_ID = F.LRMP_NOD_ID
                  AND E.LRN_STP_ID = F.LRN_STP_ID
                  AND F.LRN_STP_DV_CD = 'EX' /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EX: 평가) */
                  AND F.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST G  /* TL_교과학습활동상태 */
                  ON G.OPT_TXB_ID = E.OPT_TXB_ID
                  AND G.LRMP_NOD_ID = E.LRMP_NOD_ID
                  AND G.LRN_ATV_ID = E.LRN_ATV_ID
                  AND G.LRN_USR_ID = #{userId}
             LEFT JOIN LMS_LRM.EA_EV H /* EA_평가 */
                  ON H.EV_ID = E.EV_ID
                  AND H.OPT_TXB_ID = E.OPT_TXB_ID
             LEFT JOIN LMS_LRM.EA_EV_RS RS
             	  ON RS.EV_ID = E.EV_ID
             	  AND RS.USR_ID = G.LRN_USR_ID
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND A.LRMP_NOD_ID = #{lrmpNodId} /* LCMS교과서_지식맵_키 */
        AND A.USE_YN = 'Y'  /* 사용여부 */
        ORDER BY E.RCSTN_ORDN ASC

        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectLrnAtvEvSumRpt */
    </select>
      
    <!-- 학습 현황 > Let's Write -->
    <select id="selectWriteList"  resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptWriteTpcDto">
        SELECT
				TPC.LLU_KMMP_NOD_ID 					-- 대단원 노드 ID(레슨ID)
				, LLU.KMMP_NOD_NM AS LLU_KMMP_NOD_NM 	-- 대단원 노드명 (레슨명)
				, TPC.TPC_KMMP_NOD_ID					-- 토픽노드ID
				, TPC.TPC_KMMP_NOD_NM					-- 토픽노드명
				, WRT.PGRS_ST_CD						-- 진행상태
				, CASE WHEN PGRS_ST_CD IS NULL THEN '-'
						WHEN PGRS_ST_CD IN ('LN') THEN '미제출'
						WHEN PGRS_ST_CD IN ('EC', 'FC', 'CC', 'SM', 'AP') THEN '제출'
				END WRT_ST_NM					-- 글쓰기 제출 여부
				, CASE WHEN PGRS_ST_CD IS NULL THEN '-'
						WHEN PGRS_ST_CD = 'LN' THEN '-'
						WHEN PGRS_ST_CD IN ('SM', 'EC', 'AP') THEN '첨삭대기'
						WHEN PGRS_ST_CD = 'FC' THEN 'X'
						WHEN PGRS_ST_CD = 'CC' THEN 'O'
				END EDIT_ST_NM					-- 첨삭 확인 여부
		        , CASE WHEN PGRS_ST_CD IS NULL THEN '-' -- 글쓰기
		        	WHEN PGRS_ST_CD = 'LN' THEN '-'  -- 이어 하기
		        	WHEN PGRS_ST_CD IN ('SM', 'AP', 'EC') THEN '첨삭 대기'
		        	WHEN PGRS_ST_CD IN ('FC', 'CC') THEN '결과 보기'
		        END PGRS_ST_NM
				, LLU.TC_USE_YN					-- 차시사용여부
                , LLU.LCKN_YN					-- 잠금여부
                , LLU.USE_YN					-- 사용여부
				, IFNULL(DATE_FORMAT(IFNULL(WRT.STU_SAV_DTM, WRT.CRT_DTM), '%m. %d.'), '-')  AS LAST_DTM		-- 마지막 학습일
				, WRT.LRN_TM_SCNT AS LRN_TM_SCNT	-- 학습 시간
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
					ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID
					AND LLU.DPTH = 1
					AND LLU.DEL_YN = 'N'
					AND TPC.DEL_YN = 'N'
				/*	AND LLU.LCKN_YN = 'N' */
					AND LLU.USE_YN = 'Y'
		LEFT OUTER JOIN LMS_LRM.CM_WRT_MG WRT
					ON LLU.OPT_TXB_ID = WRT.OPT_TXB_ID
					AND TPC.LLU_KMMP_NOD_ID = WRT.LLU_KMMP_NOD_ID
					AND TPC.TPC_KMMP_NOD_ID = WRT.TPC_KMMP_NOD_ID
					AND WRT.DEL_YN = 'N'
					AND WRT.STU_USR_ID = #{userId}
		WHERE TPC.TW_WRT_DV_CD = 'W'		 -- 회화첨삭구분코드(T:talk/W:write)
		AND LLU.OPT_TXB_ID = #{optTxbId}  
		ORDER BY LLU.RCSTN_ORDN, TPC.SRT_ORDN
        /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectWriteList */
    </select>


	<select id="selectTpcRcmCtnList"  resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlTcpRcmCtnDto">
       SELECT
		 distinct
		   RCSTN.URNK_KMMP_NOD_ID AS LLU_KMMP_NOD_ID,
		   RCSTN.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
		   DPTH3.KMMP_NOD_ID AS SLU_KMMP_NOD_ID,
		   DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
		   DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
			   DPTH3.KMMP_NOD_NM AS SLU_KMMP_NOD_NM,
			   DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM,
			   DPTH5.KMMP_NOD_NM AS TPC_KMMP_NOD_NM
			 , BALAC.AI_LRN_ATV_ID
			 , BALAC.LRN_ATV_NM AS AI_LRN_ATV_NM
			 ,BALAC.CTN_TP_CD
			 , BACMD.CDN_PTH_NM
			 ,dpth5.KMMP_NOD_ID  -- topic
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN RCSTN
		inner join (
			SELECT KMMP_NOD_ID, KMMP_NOD_NM, URNK_KMMP_NOD_ID
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN
			WHERE DPTH = 3
			AND OPT_TXB_ID = #{optTxbId}
		)dpth3 on RCSTN.KMMP_NOD_ID = dpth3.URNK_KMMP_NOD_ID
		inner join (
			SELECT KMMP_NOD_ID, KMMP_NOD_NM, URNK_KMMP_NOD_ID, OPT_TXB_ID
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN
			WHERE DPTH = 4
			AND OPT_TXB_ID = #{optTxbId}
		)dpth4 on dpth3.KMMP_NOD_ID = dpth4.URNK_KMMP_NOD_ID
		inner join (
			SELECT KMMP_NOD_ID, KMMP_NOD_NM, URNK_KMMP_NOD_ID, OPT_TXB_ID
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN
			WHERE DPTH = 5
			AND OPT_TXB_ID = #{optTxbId}
		)dpth5 on dpth4.KMMP_NOD_ID = dpth5.URNK_KMMP_NOD_ID
		INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
		ON dpth5.KMMP_NOD_ID = BALAC.KMMP_NOD_ID
		AND BALAC.DEL_YN = 'N'
		AND (BALAC.CTN_TP_CD = 'PL' or BALAC.CTN_TP_CD = 'HT')
				INNER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD
		ON BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID
		WHERE 
			RCSTN.opt_txb_id = #{optTxbId}
			AND dpth5.KMMP_NOD_ID in
		<foreach item="item" collection="anRpotList" open="(" separator="," close=")">
			#{item.tpcId}
		</foreach>
        /* 학습 리포트 김한결 - 학습추천 재개발 EaLrnRptCm-Mapper.xml - selectTpcRcmCtnList */
    </select>
	      
    <!-- 학습리포트 > 학습현황 > AI 추천학습 > 수학 중단원 조회  -->
    <select id="selectAiPlMluInfo" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlMluInfoDto">
    	SELECT A.KMMP_NOD_ID AS M_KMMP_NOD_ID 	-- 중단원 지식맵 ID
			 , A.KMMP_NOD_NM AS M_KMMP_NOD_NM 	-- 중단원 지식맵 NM
		 	 , A.TC_USE_YN AS TC_USE_YN			-- 차시 사용 여부
             , A.USE_YN AS USE_YN			    -- 사용 여부
             , A.LCKN_YN AS LCKN_YN			    -- 잠금여부
		 	 , A.RCSTN_ORDN AS RCSTN_ORDN		-- 재구성 순서	
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN A
		WHERE A.DPTH = 2
		AND A.OPT_TXB_ID = #{optTxbId}
		AND A.DEL_YN = 'N'
		ORDER BY A.RCSTN_ORDN ASC, A.KMMP_NOD_ID ASC
		 /* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectAiPlMluInfo */
    </select>
    
    <!-- 학습리포트 > 학습현황 > AI 추천학습 > 수학 중단원에 따른 진단평가, 맞춤학습 토픽 정보 조회  -->
	<select id="selectAiPlTpcInfo" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlTpcInfoDto">
		SELECT 
			 A.KMMP_NOD_ID_DPTH_2 AS M_KMMP_NOD_ID		-- 중단원 지식맵 ID
			,A.KMMP_NOD_NM_DPTH_2 AS M_KMMP_NOD_NM		-- 중단원 지식맵 NM
			,A.KMMP_NOD_ID_DPTH_5 AS TPC_KMMP_NOD_ID	-- 토픽 지식맵 ID
			,A.KMMP_NOD_NM_DPTH_5 AS TPC_KMMP_NOD_NM	-- 토픽 지식맵 NM
			,A.DPTH AS DPTH								-- 토픽 DPTH
			,TPC.TPC_AVN AS TPC_AVN						-- 토픽 숙련도
			,TR.EV_ID as EV_ID							-- 평가에 따른 EV_ID
			,EV_INFO.EV_TM_SCNT as EV_TM_SCNT			-- 평가별 풀이 시간
			,EV_INFO.EV_DTL_DV_CD AS EV_DTL_EV_CD		-- 평가 상세 구분 코드(진단평가:OV, 맞춤학습:C1,C2)
			,EV_INFO.EV_CMPL_YN AS EV_CMPL_YN			-- 평가 완료 여부(진단평가, 맞춤학습)
			,EV_INFO.MDF_DTM AS EV_RS_MDF_DTM			-- 평가 마지막 학습일
		FROM (
		    WITH RECURSIVE M_KMMP_NOD_INFO AS (
		        SELECT KMMP_NOD_ID					-- 중단원 지식맵 ID
						, KMMP_NOD_NM				-- 중단원 지식맵 NM
						, URNK_KMMP_NOD_ID			-- 대단원 지식맵 ID
						, DPTH						
						, OPT_TXB_ID
						, KMMP_NOD_ID AS M_KMMP_ID	-- 중단원 지식맵 ID (고정)
						, KMMP_NOD_NM AS M_KMMP_NM	-- 중단원 지식맵 NM (고정)
		        FROM LMS_LRM.AI_KMMP_NOD_RCSTN
		        WHERE DPTH = 2
		        AND KMMP_NOD_ID = #{mKmmpNodId}
		        AND OPT_TXB_ID = #{optTxbId}
		        UNION ALL
		        SELECT CHILD.KMMP_NOD_ID			-- 중단원 이하의 지식맵 ID (소단원, 차시, 토픽)
						,CHILD.KMMP_NOD_NM			-- 중단원 이하의 지식맵 NM
						, CHILD.URNK_KMMP_NOD_ID	-- KMMP_NOD_ID의 상위 NOD_ID
						, CHILD.DPTH				-- DPTH (3, 4, 5 DPTH)
						, CHILD.OPT_TXB_ID
						, MINFO.M_KMMP_ID			-- 중단원 지식맵 ID
						, MINFO.M_KMMP_NM			-- 중단원 지식맵 NM
		        FROM LMS_LRM.AI_KMMP_NOD_RCSTN CHILD
		        JOIN M_KMMP_NOD_INFO MINFO ON MINFO.KMMP_NOD_ID = CHILD.URNK_KMMP_NOD_ID
		    )
		    SELECT 
				M.M_KMMP_ID AS KMMP_NOD_ID_DPTH_2,			-- 중단원 지식맵 ID
				M.M_KMMP_NM AS KMMP_NOD_NM_DPTH_2,			-- 중단원 지식맵 ID
		        M.KMMP_NOD_ID AS KMMP_NOD_ID_DPTH_5, 		-- 토픽 지식맵 ID
		        M.KMMP_NOD_NM AS KMMP_NOD_NM_DPTH_5, 		-- 토픽 지식맵 NM
		        M.DPTH 
		    FROM M_KMMP_NOD_INFO M
		    WHERE M.DPTH = 5		-- 토픽만 추축
		) A
		LEFT JOIN LMS_LRM.AI_USRLY_TPC_PROF TPC ON TPC.TPC_ID = A.KMMP_NOD_ID_DPTH_5 
												AND TPC.LU_KMMP_NOD_ID = A.KMMP_NOD_ID_DPTH_2
												AND TPC.USR_ID = #{userId}
		/* EV_ID 가져오기 위해 JOIN */ 
		LEFT JOIN LMS_LRM.EA_AI_EV_TS_RNGE TR ON TR.TPC_KMMP_NOD_ID = A.KMMP_NOD_ID_DPTH_5 
												AND TR.MLU_KMMP_NOD_ID = A.KMMP_NOD_ID_DPTH_2
												AND TR.OPT_TXB_ID = #{optTxbId}
		INNER JOIN (
		    SELECT
		        MAX(EV.EV_ID) AS EV_ID,
		        EAETR.MLU_KMMP_NOD_ID,
                MAX(EV.EV_DV_CD) AS EV_DV_CD,
		        EV.EV_DTL_DV_CD,
		        IFNULL(MAX(RS.EV_CMPL_YN), 'N') AS EV_CMPL_YN,
                MAX(RS.EV_TM_SCNT) AS EV_TM_SCNT,
                MAX(RS.MDF_DTM) AS MDF_DTM
		    FROM LMS_LRM.EA_EV EV
		    LEFT OUTER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EV.EV_ID = EAETR.EV_ID
		    LEFT JOIN LMS_LRM.EA_EV_RS RS ON EV.EV_ID = RS.EV_ID
		    WHERE 1=1
		    AND EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
		    AND EV.EV_DTL_DV_CD in ('OV', 'C1', 'C2')
		    AND RS.USR_ID = #{userId}
		    AND EV.EV_DTL_DV_CD IS NOT null
		    AND RS.EV_CMPL_YN = 'Y' -- EV_CMPL_YN이 'Y'인 경우만 필터링
		    GROUP BY MLU_KMMP_NOD_ID, EV_DTL_DV_CD
		) EV_INFO ON A.KMMP_NOD_ID_DPTH_2 = EV_INFO.MLU_KMMP_NOD_ID 
				  AND EV_INFO.EV_ID = TR.EV_ID
		ORDER BY EV_RS_MDF_DTM ASC
		
		/* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectAiPlTpcInfo */
	</select>
	
	<!-- 학습리포트 > 학습현황 > AI 추천학습 > 영어 중단원에 따른 영역별 진단평가, 맞춤학습 토픽 정보 조회  -->
	<select id="selectAlPlEaMHRptList" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlTcInfoDto">
		SELECT 
			 A.KMMP_NOD_ID_DPTH_2 AS M_KMMP_NOD_ID		-- 중단원 지식맵 ID
			,A.KMMP_NOD_NM_DPTH_2 AS M_KMMP_NOD_NM		-- 중단원 지식맵 NM
			,A.KMMP_NOD_ID_DPTH_4 AS TC_KMMP_NOD_ID		-- 차시 지식맵 ID
			,A.KMMP_NOD_NM_DPTH_4 AS TC_KMMP_NOD_NM		-- 차시 지식맵 NM
			,TR.TPC_KMMP_NOD_ID
			,TR.TPC_KMMP_NOD_NM
			,A.DPTH AS DPTH								-- 토픽 DPTH
			,TR.EV_ID AS EV_ID							-- 평가에 따른 EV_ID
			,EV_INFO.EV_TM_SCNT AS EV_TM_SCNT			-- 평가별 풀이 시간
			,EV_INFO.EV_DTL_DV_CD AS EV_DTL_EV_CD		-- 평가 상세 구분 코드(진단평가:OV, 맞춤학습:C1,C2)
			,EV_INFO.EV_CMPL_YN AS EV_CMPL_YN			-- 평가 완료 여부(진단평가, 맞춤학습)
			,EV_INFO.MDF_DTM AS EV_RS_MDF_DTM			-- 평가 마지막 학습일
			,COALESCE(EEQA.TOTAL_QTM, 0) AS TOTAL_QTM_COUNT		-- 총 풀이 문항수
			,COALESCE(EEQA.CANS_YN_Y, 0) AS CANS_YN_COUNT		-- 총 정답수
			,EV_INFO.CANS_CNT AS CANS_CNT						-- 평가 결과 테이블의 총 정답수
			,EEQA.XPL_TM_SCNT AS XPL_TM_SCNT					-- 총 풀이시간(문항테이블에서 합)
			,TR.LUEV_CMPL_YN AS LUEV_CMPL_YN					-- 단원평가완료여부(C1, C2 완료 여부 체크)
		--	,TPC_INFO.AVG_TPC_AVN AS AVG_TPC_AVN -- TPC_AVN 값들의 합의 평균과 색상 결정
			,IF(TPC_INFO.AVG_TPC_AVN <![CDATA[<]]> 1e-9, '0', FORMAT(TPC_INFO.AVG_TPC_AVN, 10)) AS AVG_TPC_AVN -- TPC_AVN 값들의 합의 평균과 색상 결정
		FROM
		(
			WITH RECURSIVE M_KMMP_NOD_INFO AS (
			    SELECT KMMP_NOD_ID					-- 중단원 지식맵 ID
						, KMMP_NOD_NM				-- 중단원 지식맵 NM
						, URNK_KMMP_NOD_ID			-- 대단원 지식맵 ID
						, DPTH						
						, OPT_TXB_ID
						, KMMP_NOD_ID AS M_KMMP_ID	-- 중단원 지식맵 ID (고정)
						, KMMP_NOD_NM AS M_KMMP_NM	-- 중단원 지식맵 NM (고정)
			    FROM LMS_LRM.AI_KMMP_NOD_RCSTN
			    WHERE DPTH = 2
			    AND KMMP_NOD_ID = #{mKmmpNodId}
			    AND OPT_TXB_ID =  #{optTxbId}
			    UNION ALL
			    SELECT CHILD.KMMP_NOD_ID			-- 중단원 이하의 지식맵 ID (소단원, 차시, 토픽)
						,CHILD.KMMP_NOD_NM			-- 중단원 이하의 지식맵 NM
						, CHILD.URNK_KMMP_NOD_ID	-- KMMP_NOD_ID의 상위 NOD_ID
						, CHILD.DPTH				-- DPTH (3, 4, 5 DPTH)
						, CHILD.OPT_TXB_ID
						, MINFO.M_KMMP_ID			-- 중단원 지식맵 ID
						, MINFO.M_KMMP_NM			-- 중단원 지식맵 NM
			    FROM LMS_LRM.AI_KMMP_NOD_RCSTN CHILD
			    JOIN M_KMMP_NOD_INFO MINFO ON MINFO.KMMP_NOD_ID = CHILD.URNK_KMMP_NOD_ID
			)
			SELECT 
				M.M_KMMP_ID AS KMMP_NOD_ID_DPTH_2,			-- 중단원 지식맵 ID
				M.M_KMMP_NM AS KMMP_NOD_NM_DPTH_2,			-- 중단원 지식맵 ID
			    M.KMMP_NOD_ID AS KMMP_NOD_ID_DPTH_4, 		-- 차시 지식맵 ID
			    M.KMMP_NOD_NM AS KMMP_NOD_NM_DPTH_4, 		-- 차시 지식맵 NM
			    M.DPTH 
			FROM M_KMMP_NOD_INFO M
			WHERE M.DPTH = 4		-- 차시만 추출
			)A 
			-- EV_ID 가져오기 위해 JOIN 
			LEFT JOIN LMS_LRM.EA_AI_EV_TS_RNGE TR ON TR.TC_KMMP_NOD_ID = A.KMMP_NOD_ID_DPTH_4 
													AND TR.MLU_KMMP_NOD_ID = A.KMMP_NOD_ID_DPTH_2
													AND TR.OPT_TXB_ID = #{optTxbId}
			INNER JOIN (
			    SELECT
			        EV.EV_ID,
			        EAETR.MLU_KMMP_NOD_ID,
			        EAETR.TC_KMMP_NOD_ID,
			        EV.EV_DV_CD,
			        EV.EV_DTL_DV_CD,
			        RS.CANS_CNT,
			        IFNULL(RS.EV_CMPL_YN, 'N') AS EV_CMPL_YN, 
			        RS.EV_TM_SCNT as EV_TM_SCNT, 
			        RS.MDF_DTM as MDF_DTM
			    FROM LMS_LRM.EA_EV EV
			    LEFT OUTER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EV.EV_ID = EAETR.EV_ID
			    LEFT JOIN LMS_LRM.EA_EV_RS RS ON EV.EV_ID = RS.EV_ID
			    WHERE 1=1
			    AND EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
			    AND EV.EV_DTL_DV_CD in ('OV', 'C1', 'C2')
			    AND RS.USR_ID = #{userId}
			    AND EV.EV_DTL_DV_CD IS NOT null
			   -- AND RS.EV_CMPL_YN = 'Y' -- EV_CMPL_YN이 'Y'인 경우만 필터링
			    GROUP BY TC_KMMP_NOD_ID, EV_DTL_DV_CD
			) EV_INFO ON A.KMMP_NOD_ID_DPTH_2 = EV_INFO.MLU_KMMP_NOD_ID 
			AND EV_INFO.EV_ID = TR.EV_ID
			LEFT JOIN (
			    SELECT
			        EV_ID,
			        USR_ID,
			        COUNT(*) AS TOTAL_QTM,
			        SUM(CASE WHEN CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_YN_Y,
			        SUM(XPL_TM_SCNT) as XPL_TM_SCNT
			    FROM LMS_LRM.EA_EV_QTM_ANW
			    GROUP BY EV_ID, USR_ID
			) EEQA ON EV_INFO.EV_ID = EEQA.EV_ID AND EEQA.USR_ID = #{userId}
			LEFT JOIN (
			    SELECT 
			        TC_KMMP_NOD_ID,
			        AVG(TPC_AVN) AS AVG_TPC_AVN
			    FROM LMS_LRM.AI_USRLY_TPC_PROF
			    WHERE USR_ID = #{userId}
			    and LU_KMMP_NOD_ID = #{mKmmpNodId}
			    GROUP BY TC_KMMP_NOD_ID
			) TPC_INFO ON TPC_INFO.TC_KMMP_NOD_ID = A.KMMP_NOD_ID_DPTH_4
			GROUP BY A.KMMP_NOD_ID_DPTH_4, EV_INFO.EV_DTL_DV_CD
			ORDER BY EV_RS_MDF_DTM ASC
		/* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectAlPlEaMHRptList */
	</select>
	
	
   <!-- 임시 삭제 예정 ================================================================================================================= -->
	<select id="selectTalkTpcList" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTalkDto">
		SELECT LLU.KMMP_NOD_ID									-- 대단원노드ID(레슨ID)
			 , TPC.TPC_KMMP_NOD_ID								-- 토픽노드ID 
			 , TPC.TPC_KMMP_NOD_NM								-- 토픽노드명
			 , IFNULL(TK.LRN_ST_CD,'NL') AS LRN_ST_CD			-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
			 , RANK() OVER (PARTITION BY LLU.KMMP_NOD_ID ORDER BY TPC.TPC_KMMP_NOD_ID) AS NOD_RANK	-- 노드랭크
			 , TK.CRT_DTM					-- 생성일자
			 , TK.MDF_DTM					-- 수정일자
		  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND TPC.DEL_YN = 'N'
		   AND LLU.LCKN_YN = 'N'
		  LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
		    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND TPC.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND TPC.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = '257stu001'						-- 파라미터: 학습사용자ID
		 WHERE TPC.TW_WRT_DV_CD = 'T'							-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = 'opt-cla-20240531-257'						-- 파라미터: 운영교과서ID
		   AND LLU.KMMP_NOD_ID = '20653'
		 ORDER BY LLU.RCSTN_ORDN , TPC.SRT_ORDN, NOD_RANK
		/*학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectTalkTpcList */
	</select>
	
	<select id="selectTalkList1" resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptTalkDto">
		SELECT A.KMMP_NOD_ID	-- 대단원
			 , A.KMMP_NOD_NM	
			 , A.LRN_ST_CD		-- 학습상태
			 , A.TC_USE_YN		-- 차시 사용 여부
		     , A.LCKN_YN
		     , A.USE_YN
			 , A.RCSTN_ORDN
			 , A.LRN_USR_ID
			 , A.OPT_TXB_ID
			 , A.DPTH
		  FROM (
				SELECT LLU.KMMP_NOD_ID									-- 대단원노드ID(레슨ID)
					 , LLU.KMMP_NOD_NM									-- 대단원노드명(레슨명)
					 , IFNULL(TK.LRN_ST_CD,'NL') AS LRN_ST_CD			-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
					 , LLU.TC_USE_YN									-- 차시 사용여부
                     , LLU.LCKN_YN									    -- 잠금여부
                     , LLU.USE_YN									    -- 사용여부
					 , LLU.RCSTN_ORDN									-- 차시 순서
					 , TK.LRN_USR_ID
					 , LLU.OPT_TXB_ID
					 , LLU.DPTH
				  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
				  LEFT JOIN LMS_LRM.CM_TK_MG TK			-- CM_회화관리
				    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
				   AND LLU.KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
				   AND TK.DEL_YN = 'N'
				   AND TK.LRN_USR_ID = #{userId}
				 WHERE 1=1
				  AND LLU.OPT_TXB_ID = #{optTxbId}
				  AND LLU.DPTH = '1'							-- 대단원을 조회
				 GROUP  BY LLU.KMMP_NOD_ID
		  ) A
		 
		 WHERE 1=1
		 ORDER BY A.RCSTN_ORDN ASC, A.KMMP_NOD_ID ASC
		/*학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectTalkList1 */
	</select>
	
	<!--  AI 추천학습 영어============================================================ -->
	<select id="selectMluTcList2"  resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlRcmTsshQtmDto">
        SELECT
            DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID
          , DPTH1.KMMP_NOD_NM AS LLU_KMMP_NOD_NM
          , DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID
          , DPTH2.KMMP_NOD_NM AS MLU_KMMP_NOD_NM
          , DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID
          , DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM
          , DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID
          , DPTH5.KMMP_NOD_NM AS TPC_KMMP_NOD_NM
          , DPTH1.TC_EPS_YN
          , DPTH1.TC_USE_YN
          , DPTH1.RCSTN_ORDN
          , DPTH1.ORGL_ORDN
          , DPTH1.USE_YN
          , DPTH1.LCKN_YN
          , IFNULL(AUTP.TPC_AVN, 0.5) TPC_AVN
          , BALI.LU_IMG_PTH AS LU_IMG_PTH
          , OV.TC_AVG_CANS_RT
          , OVCNT.OV_QTM_CNT
        FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
            INNER JOIN LMS_CMS.BC_KMMP_NOD BKN
                ON DPTH1.KMMP_NOD_ID = BKN.KMMP_NOD_ID
                AND BKN.LU_EPS_YN = 'Y'
            INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
                ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
                AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
                AND DPTH2.DPTH = 2
            INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
                ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
                AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
                AND DPTH3.DPTH = 3
            INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
                ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
                AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
                AND DPTH4.DPTH = 4
            INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
                ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
                AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
                AND DPTH5.DPTH = 5
            LEFT OUTER JOIN (
                SELECT USR_ID, LU_KMMP_NOD_ID, TC_KMMP_NOD_ID, AVG(TPC_AVN) as TPC_AVN
                FROM LMS_LRM.AI_USRLY_TPC_PROF
                WHERE USR_ID = #{userId}
                GROUP BY LU_KMMP_NOD_ID, TC_KMMP_NOD_ID
            ) AUTP
                ON AUTP.LU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
                AND AUTP.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
            LEFT OUTER JOIN (
                SELECT EE.EV_ID, EER.USR_ID, EE.EV_DTL_DV_CD, EER.EV_CMPL_YN
                     , EAETR.MLU_KMMP_NOD_ID, EAETR.TC_KMMP_NOD_ID, EAETR.TPC_KMMP_NOD_ID
                     , (SUM(EEQA.CANS_YN = 'Y') / COUNT(EEQ.QTM_ID)) * 100 AS TC_AVG_CANS_RT
                FROM LMS_LRM.EA_EV EE
                    INNER JOIN LMS_LRM.EA_EV_RS EER
                        ON EE.EV_ID =  EER.EV_ID
                    INNER JOIN LMS_LRM.EA_EV_QTM EEQ
                        ON EE.EV_ID = EEQ.EV_ID
                    INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
                        ON EE.EV_ID = EEQA.EV_ID
                        AND EEQ.QTM_ID = EEQA.QTM_ID
                    INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
                        ON EE.EV_ID = EAETR.EV_ID
                        AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
                WHERE EER.USR_ID = #{userId}
                AND EE.EV_DV_CD = 'AE'
                AND EER.EV_CMPL_YN = 'Y'
                AND EE.EV_DTL_DV_CD = 'OV'
                GROUP BY EAETR.TC_KMMP_NOD_ID
            ) OV
                ON OV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
                AND OV.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
            LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
                ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
                AND BALAC.CTN_TP_CD = 'QU'
                AND BALAC.DEL_YN = 'N'
            LEFT OUTER JOIN (
                SELECT
                    BE.MLU_NOD_ID
                  , COUNT(*) AS OV_QTM_CNT
                FROM LMS_CMS.BC_EVSH BE
                    INNER JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM
                        ON BE.EVSH_ID = BEQM.EVSH_ID
                WHERE BE.EVSH_TP_CD = 'AI'
                AND BE.DEL_YN = 'N'
                AND BE.USE_YN = 'Y'
                GROUP BY BE.MLU_NOD_ID
            ) OVCNT
                ON OVCNT.MLU_NOD_ID = DPTH2.KMMP_NOD_ID
            LEFT OUTER JOIN LMS_CMS.BC_AI_LU_IMG BALI
                ON BALI.LU_NOD_ID = DPTH2.KMMP_NOD_ID
        WHERE DPTH1.OPT_TXB_ID = #{optTxbId}
        AND DPTH1.DPTH = 1
        AND BALAC.KMMP_NOD_ID IS NOT NULL
        GROUP BY DPTH5.KMMP_NOD_ID
        ORDER BY DPTH1.RCSTN_ORDN, DPTH1.ORGL_ORDN, DPTH4.ORGL_ORDN
		/* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectMluTcList2 */
	</select>
	
	<select id="selectAeEvInfoList2"  resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlRcmTsshQtmDto">
		SELECT
            EE.EV_ID
          , MAX(EAETR.MLU_KMMP_NOD_ID) as MLU_KMMP_NOD_ID
          , MAX(EAETR.TC_KMMP_NOD_ID) as TC_KMMP_NOD_ID
          , MAX(EAETR.TC_KMMP_NOD_NM) as TC_KMMP_NOD_NM
          , IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS LUEV_CMPL_YN
          , MAX(EE.EV_DV_CD) as EV_DV_CD
          , MAX(EE.EV_DTL_DV_CD) as EV_DTL_DV_CD
          , MAX(EE.LCKN_YN) as LCKN_YN
          , MAX(EE.USE_YN) as USE_YN
          , MAX(EER.EV_CMPL_YN) as EV_CMPL_YN
          , MAX(EER.MDF_DTM) as MDF_DTM
          , ROW_NUMBER() OVER (ORDER BY EER.MDF_DTM DESC) AS mdfDtmOrder
		  , IFNULL(MAX(EEQA.TOTAL_QTM),0) AS TOTAL_QTM_COUNT		-- 총 풀이 문항수
          , IFNULL(MAX(EEQA.CANS_YN_Y),0) AS CANS_YN_COUNT		-- 총 정답수
          , IFNULL(MAX(EEQA.XPL_TM_SCNT),0) AS XPL_TM_SCNT					-- 총 풀이시간(문항테이블에서 합)
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		LEFT JOIN(
			SELECT
			        EV_ID,
			        USR_ID,
			        COUNT(*) AS TOTAL_QTM,
			        SUM(CASE WHEN CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_YN_Y,
			        SUM(XPL_TM_SCNT) as XPL_TM_SCNT
			    FROM LMS_LRM.EA_EV_QTM_ANW
			    WHERE USR_ID=#{userId}
			    GROUP BY EV_ID, USR_ID
		)EEQA ON EEQA.EV_ID = EER.EV_ID AND EEQA.USR_ID = #{userId}
		WHERE EAETR.OPT_TXB_ID = #{optTxbId}
		AND EER.USR_ID = #{userId}
		AND EE.EV_DV_CD = 'AE'
		GROUP BY EE.EV_ID
        ORDER BY MAX(EAETR.MLU_KMMP_NOD_ID), MAX(EAETR.TC_KMMP_NOD_ID), MAX(EE.EV_DTL_DV_CD)
		/* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectAeEvInfoList2 */
	</select>
	
	<select id="selectEnLuLrnrVelTpCd2"  resultType="com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlRcmTsshQtmDto">
		SELECT MAX(TS.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
			 , TS.TC_KMMP_NOD_ID
			 , COUNT(AN.CANS_YN) AS UG_MM_CNT
	 		 , SUM(AN.CANS_YN = 'Y') AS UG_MM_Y_CNT
		FROM LMS_LRM.EA_EV EV
		INNER JOIN LMS_LRM.EA_EV_RS ER
		ON EV.EV_ID = ER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS
		ON EV.EV_ID = TS.EV_ID
		INNER JOIN LMS_LRM.EA_EV_QTM QT
		ON EV.EV_ID = QT.EV_ID
		AND TS.TPC_KMMP_NOD_ID = QT.TPC_ID
		INNER JOIN LMS_CMS.QP_QTM QQ 
		ON QT.QTM_ID = QQ.QP_QTM_ID
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW AN
		ON EV.EV_ID = AN.EV_ID
		AND QT.QTM_ID = AN.QTM_ID
		WHERE ER.USR_ID =  #{usrId}
		AND TS.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		AND EV.EV_DTL_DV_CD = 'OV'
		and ER.EV_CMPL_YN = 'Y'
		GROUP BY TS.TC_KMMP_NOD_ID
		/* 학습 리포트 지향난 EaLrnRptCm-Mapper.xml - selectEnLuLrnrVelTpCd2 */
	</select>
	
	
	<select id="selectIansCnt"  resultType="com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto">
		select 
			sum(case 
				    when DATE(mdf_dtm) = CURDATE() then 1 
				    else 0
				end) as todayEsEvCnt,
			sum(case when IANS_NTE_CANS_YN = 'N' then 1 else 0 end) as xIansCnt
		from
			EA_EV_QTM_ANW
		where
			USR_ID = #{usrId}
		group by
			USR_ID
			/* EaLrnRptCm-Mapper.xml - selectIansCnt 학습리포트 오답노트 조회 */ 
	</select>
	
	
	
</mapper>