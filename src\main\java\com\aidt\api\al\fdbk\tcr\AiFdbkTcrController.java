package com.aidt.api.al.fdbk.tcr;

import com.aidt.api.al.fdbk.dto.*;
import com.aidt.api.al.fdbk.dto.req.AiFdbkBsDataReqDto;
import com.aidt.api.al.fdbk.dto.req.AiFdbkReqDto;
import com.aidt.api.al.fdbk.dto.req.AiFdbkStuReqDto;
import com.aidt.api.al.fdbk.dto.res.AiFdbkBsDataResDto;
import com.aidt.api.al.fdbk.stu.AiFdbkStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:52:33
 * @modify date 2024-07-11 15:52:33
 * @desc
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/fdbk/tcr")
@Tag(name="[al] AI 학습 피드백", description="학습 피드백")
public class AiFdbkTcrController {

    private final JwtProvider jwtProvider;

    private final AiFdbkTcrService aiFdbkTcrService;

    private final AiFdbkStuService aiFdbkStuService;

    @GetMapping("/ach-rank/{lrmpNodId}")
    public ResponseDto<List<AiFdbkAchRankDto>> getAchRankList(@PathVariable String lrmpNodId) {
        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        List<AiFdbkAchRankDto> achRankList = aiFdbkTcrService.getAchRankList(user.getOptTxbId(), lrmpNodId);
        return Response.ok(achRankList);
    }

    @GetMapping("/correct-rate/{lrmpNodId}")
    public ResponseDto<List<AiFdbkCorrectRateDto>> getCorrectRateList(@PathVariable String lrmpNodId) {
        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        List<AiFdbkCorrectRateDto> correctRateList = aiFdbkTcrService.getCorrectRateList(user.getOptTxbId(), lrmpNodId);
        return Response.ok(correctRateList);
    }

    @GetMapping("/growth-rank/{lrmpNodId}/{stuId}")
    public ResponseDto<List<AiFdbkGrowthRankDto>> getGrowthRankList(
            @PathVariable String lrmpNodId, @PathVariable String stuId) {
        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        List<AiFdbkGrowthRankDto> growthRankList = aiFdbkTcrService.getGrowthRankList(user.getOptTxbId(), lrmpNodId, stuId);
        return Response.ok(growthRankList);
    }

    @GetMapping("/growth-rank/{lrmpNodId}")
    public ResponseDto<List<AiFdbkGrowthRankDto>> getGrowthRankList(
            @PathVariable String lrmpNodId) {
        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        List<AiFdbkGrowthRankDto> growthRankList = aiFdbkTcrService.getGrowthRankList(user.getOptTxbId(), lrmpNodId, "");
        return Response.ok(growthRankList);
    }

    @PostMapping(value = "/feedback-bs-data", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiFdbkBsDataResDto>> getFdbkBsDataList(@RequestBody List<AiFdbkBsDataReqDto> reqDtoList) {

        List<AiFdbkBsDataResDto> fdbkList = aiFdbkTcrService.getFdbkBsDataList(reqDtoList);
        return Response.ok(fdbkList);
    }

    @PostMapping(value = "/save-feedback", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> saveFdbkList(
            @RequestBody List<AiFdbkReqDto> reqDtoList) {

        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        int cnt = aiFdbkTcrService.insertFdbk(reqDtoList, user.getOptTxbId(), user.getUsrId());
        return Response.ok(cnt);
    }

    @GetMapping(value = "/feedback/{lrmpNodId}")
    public ResponseDto<List<AiFdbkDto>> getFdbkList(@PathVariable String lrmpNodId) {

        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        List<AiFdbkDto> fdbkList = aiFdbkTcrService.getFdbkList(user.getOptTxbId(), lrmpNodId);
        return Response.ok(fdbkList);
    }

    @GetMapping(value = "/feedback/{lrmpNodId}/{usrId}")
    public ResponseDto<AiFdbkDto> getStuFdbk(
            @PathVariable String lrmpNodId, @PathVariable String usrId) {

        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        AiFdbkDto dto = aiFdbkStuService.getFdbk(
                AiFdbkStuReqDto.builder()
                        .lrmpNodId(lrmpNodId)
                        .optTxbId(user.getOptTxbId())
                        .usrId(usrId).build());
        return Response.ok(dto);
    }
}
