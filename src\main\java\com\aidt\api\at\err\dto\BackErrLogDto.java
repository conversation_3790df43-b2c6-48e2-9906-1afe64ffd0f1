package com.aidt.api.at.err.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "CM_백_에러_로그")
public class BackErrLogDto {

	@Schema(description = "API URL")
	private String apiUrl;

	@Schema(description = "오류 클래스 명")
	private String errClsNm;

	@Schema(description = "오류 메시지")
	private String errMsg;

	@Schema(description = "오류 코드")
	private String errCd;

	@Schema(description = "오류 원천 코드 (내부[INT] / 외부[EXT])")
	private String errSrcCd;

	@Schema(description = "요청 파라미터")
	private String reqParm;

	@Schema(description = "사용자 ID")
	private String usrId;

	@Schema(description = "운영 교과서 ID")
	private String optTxbId;

	@Schema(description = "세션 경과 시간")
	private Integer sessionElapseTm;


	public BackErrLogDto() {}

	public static BackErrLogDto of(String apiUrl,
								   String errClsNm,
								   String errMsg,
								   String errCd,
								   String errSrcCd,
								   String reqParm,
								   String usrId,
								   String optTxbId,
								   Long startTime
	) {
		BackErrLogDto dto = new BackErrLogDto();
		dto.setApiUrl(apiUrl);
		dto.setErrClsNm(errClsNm);
		dto.setErrMsg(errMsg);
		dto.setErrCd(errCd);
		dto.setErrSrcCd(errSrcCd);
		dto.setReqParm(reqParm);
		dto.setUsrId(usrId);
		dto.setOptTxbId(optTxbId);
		dto.setSessionElapseTm((int) (System.currentTimeMillis() - startTime));
		return dto;
	}

}
