package com.aidt.api.ea.evcom.helper;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Component;

import com.aidt.api.common.utils.HalfWidthUtils;
import com.aidt.api.ea.evcom.dto.EaEvAnswer;
import com.aidt.api.ea.evcom.dto.EaEvAnswerReqDto;
import com.aidt.api.ea.evcom.dto.EaEvQuestionSolution;
import com.aidt.api.ea.evcom.service.EaEvQuestionQueryService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EaEvScoringHelper {

	private final EaEvQuestionQueryService eaEvQuestionQueryService;

	public EaEvAnswer evaluateAnswer(EaEvAnswerReqDto.EaEvAnswerQtmDto answer) {
		if (ObjectUtils.isEmpty(answer)) {
			log.error("평가 문항 답변이 존재하지 않습니다.");
			return null;
		}
		return evaluateAnswers(List.of(answer)).get(0);
	}

	public List<EaEvAnswer> evaluateAnswers(List<EaEvAnswerReqDto.EaEvAnswerQtmDto> answers) {

		var qtmIds = answers.stream()
			.filter(answer -> !answer.isExternalQuestion())
			.map(EaEvAnswerReqDto.EaEvAnswerQtmDto::getQtmId)
			.collect(Collectors.toList());

		var questionSolutionMap = eaEvQuestionQueryService.getEaEvQuestionSolutionMap(qtmIds);

		var keywordQtmIds = questionSolutionMap.values().stream()
			.filter(questionSolution -> questionSolution.getQpQstTypCd().equals("85"))
			.map(EaEvQuestionSolution::getQpQtmId)
			.collect(Collectors.toList());

		var questionsKeywordMap = eaEvQuestionQueryService.getEaEvQuestionKeywordMap(keywordQtmIds);

		return answers.stream()
			.map(answer -> {

				var unescapedHtml = unescapeHtml(answer.getSmtAnwVl());
				var smtAnwVl = normalize2HalfWidth(unescapedHtml);

				var cansYn = answer.isExternalQuestion() ?
					answer.getExternalQuestionCorrect() :
					getCorrectYn(smtAnwVl, questionSolutionMap.get(answer.getQtmId()),
						questionsKeywordMap.get(answer.getQtmId()));

				return EaEvAnswer.builder()
					.qtmId(answer.getQtmId())
					.annxFleId(answer.getAnnxFleId())
					.xplTmScnt(answer.getXplTmScnt())
					.smtAnwVl(smtAnwVl)
					.cansYn(cansYn)
					.xplStCd(answer.getXplStCd())
					.hntCofmYn(answer.getHntCofmYn())
					.build();
			})
			.collect(Collectors.toList());
	}

	//기존 html 변환된 특수문자 처리 대체
	private String unescapeHtml(String str) {
		if (StringUtils.isBlank(str)) {
			return str;
		}
		return StringEscapeUtils.unescapeHtml4(str);
	}

	//전각 문자 반각 변환
	private String normalize2HalfWidth(String str) {
		if (StringUtils.isBlank(str)) {
			return str;
		}
		return HalfWidthUtils.normalize2HalfWidth(str.trim());
	}

	//기존 정오답 처리 대체
	private String getCorrectYn(String smtAnwVl, EaEvQuestionSolution questionSolution,
		List<EaEvQuestionSolution.EaEvQuestionKeyword> questionKeywords) {
		// 2024-11-26 서술형 체크로직 추가

		//제출 답변 없을 경우 오답 처리
		if (StringUtils.isBlank(smtAnwVl)) {
			return "N";
		}

		// 문통 정답정보 없을 경우
		if (ObjectUtils.isEmpty(questionSolution)) {
			return "N";
		}

		int partCansCnt; //부분정답 개수
		int plurCansCnt; //복수 정답 개수

		try {
			// 문통 정답JSON DATA 내용 없을 경우
			if (StringUtils.isBlank(questionSolution.getQpJsonDataCn())) {
				return "N";
			}

			String[] smtAnwVlArr = smtAnwVl.split("[|]");//제출 답변
			List<Map<String, Object>> jsonData = jsonToMap(questionSolution.getQpJsonDataCn());//JSON DATA 파싱

			//문항 유형 코드(10:자유 선지,20+30+40+50: 2~5지 선택,60+61+62:단답 유순형,단답 무슨형,단답 묶음형)
			String qpQstTypCd = questionSolution.getQpQstTypCd();

			partCansCnt = questionSolution.getPartCansCnt(); //부분 정답 개수
			plurCansCnt = questionSolution.getPlurCansCnt(); //복수 정답 개수

			// 서술형 문항
			if ("85".equals(qpQstTypCd)) {
				boolean keywordMatch = false;
				boolean answerMatch = false;
				int descriptiveKeywordCount = 0;
				String qstXplCn = "";

				qstXplCn = smtAnwVlArr[0]; //첫번째 데이터는 풀이과정

				// 풀이과정 없으면 오답
				if (qstXplCn.isEmpty()) {
					return "N";
				}

				List<Map<String, String>> answerList = (List<Map<String, String>>)jsonData.get(0)
					.get("answerList"); // 문통 정답

				//문제 풀이 과정 키워드 개수
				descriptiveKeywordCount =
					CollectionUtils.isNotEmpty(questionKeywords) ? questionKeywords.get(0).getKwdCnt() : 0;

				//문제 풀이 과정 키워드 개수가 0개 일때 키워드 입력했으면 정답으로 인정
				if (descriptiveKeywordCount == 0) {
					return "Y";
				}

				// 문제풀이과정 텍스트중 키워드와 일치하는 개수 확인
				int keywordMatchCount = checkKwd(qstXplCn, questionKeywords);

				//문제풀이과정 맞은 키워드 수 체크, 풀이과정키워드수가 문통키워드수보다 작으면 정답인정 안됨
				if (descriptiveKeywordCount > keywordMatchCount) {
					return "N";
				}

				keywordMatch = true;

				/* 문제 풀이과정만 존재하는 서술형일 경우 키워드 정답이면 정답으로 인정
				 * 키워드만 전달된 상태, 정답정보 없으므로 키워드 정답이면 정답, 키워드 일치 없으면 오답
				 *
				 * 	 데이터 케이스
				 *   1) 키워드존재, 정답 있음 -> 디폴트
				 *   2) 키워드존재, 정답 없음 -> 풀이만 존재
				 *   			 정답 "풀이 참고" 데이터 있음
				 *
				 *   3) 키워드없음, 정답 없음 -> 해당 케이스 현재 조건에서 선생님 문제출제 불가능
				 *
				 *   1번 케이스에서도 정답을 입력하지 않고 풀이과정만 제출해도 smtAnwVlArr.length: 1 이된다.
				 *
				 *   2번케이스 ) -> smtAnwVl에 | 없이 오는 문항
				 * */

				boolean isNotExistAnswerTag = !smtAnwVl.contains("|");
				// 2번 케이스 정답처리
				if (isNotExistAnswerTag) {
					return "Y";
				}

				// 정답 있을경우 정답 체크, 유사정답 체크(여러개일 수 있음)
				String capitalYn, ignoreSpaceYn, answer = "";
				String result;
				String smtAnwVlTmp;

				capitalYn = (String)jsonData.get(0).getOrDefault("capitalYn", "");            // N:대소문자 구분
				ignoreSpaceYn = (String)jsonData.get(0).getOrDefault("ignoreSpaceYn", "");    // Y:스페이스 체크

				for (Map<String, String> map : answerList) {
					answer = map.get("answer");
					answer = replaceAndLower(capitalYn, ignoreSpaceYn, answer);    //정답 대,소 문자, 스페이스 구분 처리
					answer = replaceAnswer(answer);                                        //특수 문자 변환 처리
					smtAnwVlTmp = replaceAndLower(capitalYn, ignoreSpaceYn,
						smtAnwVlArr[1].trim());        //답변 대,소 문자, 스페이스 구분 처리

					// 문통 정답이 없을 경우 정답 입력 있으면 정답으로 인정
					if (answer.isEmpty() && !smtAnwVlTmp.isEmpty()) {
						return "Y";
					}

					if (smtAnwVlTmp.isEmpty()) { // 정답 항목 입력없으면 오답
						return "N";
					}

					result = checkAnswer(smtAnwVlTmp, answer);                                //정답 확인
					if ("Y".equals(result)) {
						answerMatch = true;
						break;
					}
				}

				if (keywordMatch && answerMatch) {
					return "Y";
				} else
					return "N";
			}
			// 서술형이 아니면
			else {
				return scoringProcess(smtAnwVlArr, qpQstTypCd, partCansCnt, plurCansCnt, jsonData);
			}

		} catch (Exception e) {
			log.error("평가 문항 답변 정오답 처리에서 오류가 발생하였습니다. :: {})", e.getMessage());
			return "N";
		}
	}

	public int checkKwd(String qstXplCn, List<EaEvQuestionSolution.EaEvQuestionKeyword> questionKeywords) {
		int matchCount = 0;

		for (EaEvQuestionSolution.EaEvQuestionKeyword questionKeyword : questionKeywords) {
			String capitalYn = questionKeyword.getKwdCaseYn();            // Y, N:대소문자구분 유무
			String ignoreSpaceYn = questionKeyword.getKwdSpaceSkipYn();    // Y, N:공백제거 유무

			String tempQstXplCn = replaceAndLower(capitalYn, ignoreSpaceYn, qstXplCn);  // 학생 풀이입력 문자열
			String kwd = replaceAndLower(capitalYn, ignoreSpaceYn, questionKeyword.getQpKwd()); // 문통 키워드

			if (tempQstXplCn.contains(kwd)) {
				matchCount++;// 키워드 정답 수

				int k, q;
				int startIndex = -1;
				int lastIndex = -1;
				boolean foundMatch = false; // 매치 여부를 추적하는 변수

				for (q = 0; q < qstXplCn.length(); q++) { // 풀이 과정을 순회합니다.
					startIndex = -1; // 각 q에 대해 초기화
					for (k = 0; k < kwd.length(); k++) {
						char kwdChar = kwd.charAt(k);
						char qChar = qstXplCn.charAt(q);

						if ("N".equals(capitalYn)) { // 대소문자 구분여부
							qChar = Character.toLowerCase(qChar);
							kwdChar = Character.toLowerCase(kwdChar);
						}

						if ("Y".equals(ignoreSpaceYn)) {// 공백무시 옵션여부
							if (qChar == ' ' || qChar == '\t' || qChar == '\n' || qChar == '\r' ) {
								q++;
								k--;
								continue; // 키워드의 index는 유지, 풀이 문자열은 다음으로 진행
							}
						}
						if (kwdChar == qChar) {
							if (startIndex == -1) {
								startIndex = q; // 첫 번째 일치하는 인덱스 저장
							}
							if (k == kwd.length() - 1) {
								lastIndex = q; // 모든 글자를 만족했을 때 마지막 인덱스 저장
								foundMatch = true;
								break; // 내부 루프 종료
							}
							q++; // 다음 qChar로 이동
						} else {
							startIndex = -1;
							k = -1; // k를 초기화하여 kwd의 첫 글자로 되돌림
							break; // k가 초기화되었으므로 내부 루프 종료
						}
					}

					if (foundMatch) {
						qstXplCn = qstXplCn.substring(0, startIndex) + qstXplCn.substring(lastIndex + 1);
						break; // kwd를 모두 만족하면 외부 루프도 종료
					}
				}
			}
		}

		return matchCount;
	}

	@SuppressWarnings("unchecked")
	public String scoringProcess(String[] smtAnwVlArr, String qpQstTypCd, int partCansCnt, int plurCansCnt,
		List<Map<String, Object>> jsonData) {

		//2024-11-27 서술형 정답 체크 추가됨.
		//    	// 서술형은 항상 정답
		//    	if ("85".equals(qpQstTypCd)) {
		//    		return "Y";
		//    	}

		// 자유선지형의 경우 답의 갯수와 정답 갯수가 다르면 오답
		if ("10".equals(qpQstTypCd) && smtAnwVlArr.length != jsonData.size()) {
			return "N";
		}

		String capitalYn, ignoreSpaceYn, smtAnwVl, smtAnwVlReverse, answer, result = "";
		List<Map<String, String>> answerList;
		if (partCansCnt == 1) { //채점 할 정답 수 1개인 경우
			capitalYn = (String)jsonData.get(0).getOrDefault("capitalYn", "");       // N:대소문자 구분
			ignoreSpaceYn = (String)jsonData.get(0).getOrDefault("ignoreSpaceYn", "");   // Y:스페이스 체크
			answerList = (List<Map<String, String>>)jsonData.get(0).get("answerList");               // 정답 list
			smtAnwVl = smtAnwVlArr[0];                                                              // 제출 답변

			if (partCansCnt < plurCansCnt) {//부분 정답 수가 복수 정답 수 보다 적을 경우 복수 정답이 있음
				result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVl, result, answerList, qpQstTypCd);
			} else {//복수 정답이 아닌 경우
				answer = replaceAndLower(capitalYn, ignoreSpaceYn,
					answerList.get(0).get("answer"));   //문통 정답 대, 소 문자 구분 처리
				answer = replaceAnswer(answer);                                                        //문통 특수 문자 변환 처리
				smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn,
					smtAnwVl);                        //제출 답변 대, 소 문자 구분 처리
				result = checkAnswer(smtAnwVl, answer);                                                //정답 여부 확인
			}
		} else {//채점 할 정답 수 2개 이상인 경우
			// if (smtAnwVlArr.length < jsonData.size()) {//채점 할 정답 수 보다 제출 된 답변 수 가 적은 경우 오답 처리
			if (smtAnwVlArr.length != jsonData.size()) {//채점 할 정답 수와 제출 된 답변 수가 다르면 오답 처리
				result = "N";
			} else {
				String[] smtAnwVlArrReverse = smtAnwVlArr.clone();
				// 단답 무순형이면서 정답이 2개 이상인 경우 목록 정렬 후 정답 비교
				if ("61".equals(qpQstTypCd)) {
					jsonData.sort((a, b) -> {
						List<Map<String, String>> list1 = (List<Map<String, String>>)a.get("answerList");
						List<Map<String, String>> list2 = (List<Map<String, String>>)b.get("answerList");
						String ans1 = list1.get(0).get("answer");
						String ans2 = list2.get(0).get("answer");
						return ans1.compareTo(ans2);
					});
					Arrays.sort(smtAnwVlArr);
					Arrays.sort(smtAnwVlArrReverse, Comparator.reverseOrder());
				}

				// 단답 묶음형이면 가능한 답안조합 중 하나와 일치하면 정답 처리
				if ("62".equals(qpQstTypCd)) {
					boolean isMatch = false;
					int combCnt = plurCansCnt / partCansCnt; // 조합수
					for (int j = 0; j < combCnt; j++) {
						isMatch = true;
						for (int i = 0; i < jsonData.size(); i++) {
							capitalYn = (String)jsonData.get(i).getOrDefault("capitalYn", "");    // N:대소문자 구분
							ignoreSpaceYn = (String)jsonData.get(i).getOrDefault("ignoreSpaceYn", "");// Y:스페이스 체크
							answerList = (List<Map<String, String>>)jsonData.get(i).get("answerList");
							smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVlArr[i]);
							answer = replaceAnswer(
								replaceAndLower(capitalYn, ignoreSpaceYn, answerList.get(j).get("answer")));
							if ("N".equals(checkAnswer(smtAnwVl, answer))) {
								isMatch = false;
								break;
							}
						}
						if (isMatch) {
							break;
						}
					}
					return isMatch ? "Y" : "N";
				}

				// 그 외 유형
				for (int i = 0; i < jsonData.size(); i++) {
					capitalYn = (String)jsonData.get(i).getOrDefault("capitalYn", "");    // N:대소문자 구분
					ignoreSpaceYn = (String)jsonData.get(i).getOrDefault("ignoreSpaceYn", "");// Y:스페이스 체크
					answerList = (List<Map<String, String>>)jsonData.get(i).get("answerList");
					smtAnwVl = smtAnwVlArr[i];
					smtAnwVlReverse = smtAnwVlArrReverse[i];
					if (answerList.size() > 1) {//복수 정답인 경우
						result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVl, result, answerList, qpQstTypCd);
						if ("N".equals(result)) {//제출 된 답변 중 한 개라도 틀리면 오답 처리
							if ("61".equals(qpQstTypCd)) {// 단답무순형인 경우 한번 더 체크
								result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVlReverse, result, answerList,
									qpQstTypCd);
								if ("N".equals(result)) {
									return result;
								}
							} else {
								return result;
							}
						}
					} else {
						answer = replaceAndLower(capitalYn, ignoreSpaceYn,
							answerList.get(0).get("answer"));// 문통 정답 대, 소 문자 구분 처리
						answer = replaceAnswer(
							answer);                                                     // 문통 특수 문자 변환 처리
						smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn,
							smtAnwVl);                     // 제출 답변 대, 소 문자 구분 처리
						result = checkAnswer(smtAnwVl, answer);                                             // 정답 여부
						if ("N".equals(result)) {//제출 된 답변 중 한 개라도 틀리면 오답 처리
							if ("61".equals(qpQstTypCd)) {// 단답무순형인 경우 한번 더 체크
								result = getAnswerYn(capitalYn, ignoreSpaceYn, smtAnwVlReverse, result, answerList,
									qpQstTypCd);
								if ("N".equals(result)) {
									return result;
								}
							} else {
								return result;
							}
						}
					}
				}
			}
		}
		return result;
	}

	public String replaceAnswer(String answer) {
		if (answer != null) {
			answer = answer.trim();
		}
		if ("①".equals(answer)) {
			answer = "1";
		} else if ("②".equals(answer)) {
			answer = "2";
		} else if ("③".equals(answer)) {
			answer = "3";
		} else if ("④".equals(answer)) {
			answer = "4";
		} else if ("⑤".equals(answer)) {
			answer = "5";
		} else if ("ⓐ".equals(answer)) {
			answer = "a";
		} else if ("ⓑ".equals(answer)) {
			answer = "b";
		} else if ("ⓒ".equals(answer)) {
			answer = "c";
		} else if ("ⓓ".equals(answer)) {
			answer = "d";
		} else if ("ⓔ".equals(answer)) {
			answer = "e";
		} else if ("ⓕ".equals(answer)) {
			answer = "f";
		} else if ("㉮".equals(answer)) {
			answer = "가";
		} else if ("㉯".equals(answer)) {
			answer = "나";
		} else if ("㉰".equals(answer)) {
			answer = "다";
		} else if ("㉱".equals(answer)) {
			answer = "라";
		} else if ("㉲".equals(answer)) {
			answer = "마";
		} else if ("㉠".equals(answer)) {
			answer = "ㄱ";
		} else if ("㉡".equals(answer)) {
			answer = "ㄴ";
		} else if ("㉢".equals(answer)) {
			answer = "ㄷ";
		} else if ("㉣".equals(answer)) {
			answer = "ㄹ";
		} else if ("㉤".equals(answer)) {
			answer = "ㅁ";
		} else if ("㉥".equals(answer)) {
			answer = "ㅂ";
		} else if ("㉦".equals(answer)) {
			answer = "ㅅ";
		} else if ("㉧".equals(answer)) {
			answer = "ㅇ";
		} else if ("(A)".equals(answer)) {
			//answer = "A";
		} else if ("(B)".equals(answer)) {
			//answer = "B";
		} else if ("(C)".equals(answer)) {
			//answer = "C";
		} else if ("(D)".equals(answer)) {
			//answer = "D";
		} else if ("(E)".equals(answer)) {
			//answer = "E";
		}

		return answer;
	}

	private String getAnswerYn(String capitalYn, String ignoreSpaceYn, String smtAnwVl, String result,
		List<Map<String, String>> answerList, String qpQstTypCd) {
		String answer;
		for (Map<String, String> map : answerList) {
			answer = replaceAndLower(capitalYn, ignoreSpaceYn, map.get("answer")); //정답 대,소 문자, 스페이스 구분 처리
			answer = replaceAnswer(answer);                                      //특수 문자 변환 처리
			smtAnwVl = replaceAndLower(capitalYn, ignoreSpaceYn, smtAnwVl);          //답변 대,소 문자, 스페이스 구분 처리
			result = checkAnswer(smtAnwVl, answer);                               //정답 확인
			if ("Y".equals(result)) {
				return result;
			}
		}
		return result;
	}

	public String checkAnswer(String smtAnwVl, String answer) {
		if (StringUtils.isBlank(smtAnwVl)) {
			return "N";
		}
		//답안에 전각이 존재하면, 반각 변환 후, 비교
		return smtAnwVl.equals(normalize2HalfWidth(answer)) ? "Y" : "N";
		// if (sanitizeSingleQuote(smtAnwVl.trim()).equals(sanitizeSingleQuote(answer.trim()))) {
		// 	return "Y";
		// } else {
		// 	return "N";
		// }
	}

	// 어퍼스트로피(작은따옴표)가 특수문자나 유니코드로 들어가있는 경우 키보드로 입력 가능한 어퍼스트로피(')로 치환한다.
	private String sanitizeSingleQuote(String raw) {
		return raw.replace("’", "'").replace("\u0027", "'").replace("\\u0027", "'")
			.replace("＝", "=").replace("\u003d", "=").replace("\\u003d", "=");
	}

	public String replaceAndLower(String capitalYn, String ignoreSpaceYn, String val) {
		if ("N".equals(capitalYn)) {//대소문자 구분
			// 알파벳 특문이 소문자로 치환되지 않도록
			if (!("Ⓐ".equals(val) || "Ⓑ".equals(val) || "Ⓒ".equals(val) || "Ⓓ".equals(val) || "Ⓔ".equals(val)
				|| "Ⓕ".equals(val))) {
				val = val.toLowerCase();
			}
		}
		if ("Y".equals(ignoreSpaceYn)) {//스페이스 구분
			val = val.replaceAll(" ", "");
		}

		return val;
	}

	public List<Map<String, Object>> jsonToMap(String json) {
		List<Map<String, Object>> list = null;
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			list = objectMapper.readValue(json, new TypeReference<List<Map<String, Object>>>() {
			});
		} catch (JsonProcessingException e) {
			log.debug("JsonProcessingException ");
		}

		return list;
	}

}
