package com.aidt.api.al.pl.cm.rcm.aop;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class AiQuestionTopicAspect {


	@AfterThrowing(pointcut = "execution(* com.aidt.api.al.pl.cm.rcm.service.*TopicService.build*(..))", throwing = "throwable")
	public void error(JoinPoint joinPoint, Throwable throwable) {
		//todo: 에러 로그 기록
	}

}
