package com.aidt.api.tl.lsnmtrl.tcr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
//import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlInfoDto;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:43:43
 * @modify date 2024-01-05 10:43:43
 * @desc TlLsnMtrlTcr Service 수업자료서비스
 */
@Service
public class TlLsnMtrlTcrService {
    private final String MAPPER_NAMESPACE = "api.tl.lsnmtrl.tcr.";

    @Autowired
    private CommonDao commonDao;

    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    // /**
    // * 교과학습 수업자료 저장처리
    // * @param list List<TlLsnMtrlDto>
    // * @return
    // */
    // @Transactional
    // public int saveLsnMtrlDtl(List<TlLsnMtrlDto> list) {

    // int cnt = 0;
    // for(TlLsnMtrlDto dto : list) {
    // //분기하여 insert update delete 실행할것!
    // if ("I".equals(dto.getStatCd())) {
    // cnt += commonDao.insert(MAPPER_NAMESPACE + "insertLsnMtrl", dto);
    // } else if ("U".equals(dto.getStatCd())) {
    // cnt += commonDao.update(MAPPER_NAMESPACE + "updateLsnMtrl", dto);
    // } else {
    // cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteLsnMtrl", dto);
    // }
    // }
    // return cnt;
    // }

    // /**
    // * 교과학습 수업자료 삭제
    // * @param lsnMtrlDto
    // * @return int
    // */
    // @Transactional
    // public int deleteLsnMtrl(TlLsnMtrlDto lsnMtrlDto) {
    // return commonDao.delete(MAPPER_NAMESPACE + "deleteLsnMtrl", lsnMtrlDto);
    // }

    // /**
    // * 교과학습 수업자료 등록
    // * @param srhDto
    // * @return int
    // */
    // @Transactional
    // public int insertLsnMtrl(TlLsnMtrlTocSrhDto srhDto) {
    // return commonDao.insert(MAPPER_NAMESPACE + "insertLsnMtrl", srhDto);
    // }

    // /**
    // * 교과학습 수업자료 수정
    // * @param srhDto
    // * @return int
    // */
    // @Transactional
    // public int updateLsnMtrl(TlLsnMtrlDto lsnMtrlDto) {
    // return commonDao.insert(MAPPER_NAMESPACE + "updateLsnMtrl", lsnMtrlDto);
    // }

    /**
     * 교과학습 학습자료정보조회(우리반수업용)
     * 
     * @param srhDto
     * @return TlLsnMtrlInfoDto 기본자료목록과 학습자료목록을 반환한다.
     */
    @Transactional(readOnly = true)
    @Cacheable(
    		cacheNames = "longCache",
    		key = "'tl:' + #srhDto.optTxbId + ':selectLsnMtrlInfo'",
    		condition="''.equals(#srhDto.lrmpNodId)",
    		cacheManager = "aidtCacheManager"
    		)
    public TlLsnMtrlInfoDto selectLsnMtrlInfo(TlLsnMtrlTocSrhDto srhDto) {
        TlLsnMtrlInfoDto rtnDto = new TlLsnMtrlInfoDto();
        List<TlLsnMtrlDto> bsMtrlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlBsList", srhDto);
        List<TlLsnMtrlDto> lmMtrlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlLmList", srhDto);
        for(TlLsnMtrlDto bsMtrl : bsMtrlList) {
        	bsMtrl.setFlePthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME,bsMtrl.getFlePthNm()));
        }
        for(TlLsnMtrlDto lmsMtrl : lmMtrlList) {
        	if("VI".equals(lmsMtrl.getLsnMtrlTpCd())) {
        		lmsMtrl.setFlePthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, "/"+lmsMtrl.getFlePthNm()));
        	}
        }
        rtnDto.setBsMtrlList(bsMtrlList);
        rtnDto.setLmMtrlList(lmMtrlList);
        return rtnDto;
    }

//    /**
//     * 교과학습 학습자료정보조회(원클릭학습설정화면용)
//     * 
//     * @param srhDto
//     * @return TlLsnMtrlInfoDto 기본자료목록과 학습자료목록을 반환한다.
//     */
//    @Transactional(readOnly = true)
//    public TlLsnMtrlInfoDto selectLsnMtrlInfo2(TlLsnMtrlTocSrhDto srhDto) {
//        TlLsnMtrlInfoDto rtnDto = new TlLsnMtrlInfoDto();
//        srhDto.setAllDatYn("Y");
//        List<TlLsnMtrlDto> bsMtrlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlBsList", srhDto);
//        List<TlLsnMtrlDto> lmMtrlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnMtrlLmList", srhDto);
//        rtnDto.setBsMtrlList(bsMtrlList);
//        rtnDto.setLmMtrlList(lmMtrlList);
//        return rtnDto;
//    }

    /**
     * 기본학습자료 정보 저장처리
     * 
     * @param lsnMtrlDto
     * @return
     */
    public int saveLsnMtrl(TlLsnMtrlDto lsnMtrlDto) {
        if (lsnMtrlDto.getLsnMtrlNo() == null || "".equals(lsnMtrlDto.getLsnMtrlNo())) {
            return commonDao.insert(MAPPER_NAMESPACE + "insertLsnMtrl", lsnMtrlDto);

        } else {
            return commonDao.update(MAPPER_NAMESPACE + "updateLsnMtrl", lsnMtrlDto);
        }
    }
}
