package com.aidt.api.at.token;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import javax.annotation.PostConstruct;

import org.h2.util.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.aidt.api.at.token.dto.FeaturesCheckReqDto;
import com.aidt.api.at.token.dto.FeaturesCheckResDto;
import com.aidt.api.bc.cm.BcCmUtil;

@Service
public class FeaturesCheckService {

    @Value("${spring.config.activate.on-profile}")
    private String serverActive;

    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;
    
    public static String SYSTEM_LOAD_VER_NM = "";

    private String getMode() {
        switch (serverActive) {
            case "local":
                return "dev";
            case "dev":
                return "dev";
            case "stag":
                return "stg";
            case "stg":
                return "stg";
            case "prod":
                return "prod";
            default:
                return "prod";
        }
    }

    private String getTxbId() {

        String txbIdFromDBId = "";

        if (serverActive.equals("dev") || serverActive.equals("local")) {
            return txbIdFromDBId;
        }
        else{
            txbIdFromDBId = BcCmUtil.getTxbID(DB_ID);
        }

        return txbIdFromDBId;
    }

    public FeaturesCheckResDto selectCheckFeature(FeaturesCheckReqDto dto) {

        String txbId = dto.getTxbId();
        String path = "/content/json/check_feature";
        String mode = getMode();


        if(StringUtils.isNullOrEmpty(txbId)){

            txbId = getTxbId();

            if(StringUtils.isNullOrEmpty(txbId)) {
                FeaturesCheckResDto featureDto = new FeaturesCheckResDto();
                featureDto.setStatusCode("400");
                featureDto.setMessage("txbId is Null");
                return featureDto;
            }
        }

        path = path + "/" + mode + "/" + txbId + ".json";

        String url =  "https://aidtcdn.aitextbook.co.kr" + path;

        RestTemplate restTemplate = new RestTemplate();

        String ifNoneMatch = dto.getIfNoneMatch();
        String ifModifiedSince = dto.getIfModifiedSince();

        try {
            // HEAD 요청을 보내어 헤더만 가져오기
            HttpHeaders headers = restTemplate.headForHeaders(url);

            // CDN에서 반환된 ETag 및 Last-Modified 값 가져오기
            String cdnETag = headers.getFirst("ETag");
            String cdnLastModified = headers.getFirst("Last-Modified");

            // 클라이언트의 ETag 또는 Last-Modified와 비교하여 변경된 경우에만 파일 반환
            if (ifNoneMatch != null && ifNoneMatch.equals(cdnETag)) {
                FeaturesCheckResDto featureDto = new FeaturesCheckResDto();
                featureDto.setStatusCode("204");
                return featureDto;
            }
            if (ifModifiedSince != null && ifModifiedSince.equals(cdnLastModified)) {
                FeaturesCheckResDto featureDto = new FeaturesCheckResDto();
                featureDto.setStatusCode("204");
                return featureDto;
            }

            // 파일이 변경된 경우 새로운 파일을 클라이언트에게 내려줌
            FeaturesCheckResDto featureDto = restTemplate.getForObject(url, FeaturesCheckResDto.class);  // JSON 파일 다운로드
            featureDto.setEtag(cdnETag.replace("\"", ""));
            featureDto.setLastModified(cdnLastModified);
            featureDto.setStatusCode("200");

            return featureDto;

        } catch (Exception e) {
            FeaturesCheckResDto featureDto = new FeaturesCheckResDto();
            featureDto.setStatusCode("500");
            featureDto.setMessage(e.getMessage());
            return featureDto;
        }
    }
    
    @PostConstruct
	private void init() {
    	SYSTEM_LOAD_VER_NM = ZonedDateTime.now(ZoneId.of("Asia/Seoul")).format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm"));
	}
    
    public String getSystemLoadVerNm() {
		return SYSTEM_LOAD_VER_NM;
	}
}
