package com.aidt.api.bc.clablbd.stu;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.bc.clablbd.dto.BcClaBlbdDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto;
import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-07 13:42:10
 * @modify 2024-06-07 13:42:10
 * @desc 학급게시판 Service
 */

@Service
public class BcClaBlbdStuService {

    private final String MAPPER_NAMESPACE = "api.bc.clablbd.stu.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 학급게시판 조회
     *
     * @param BcClaBlbdDto
     * @return List<BcClaBlbdDto>
     */
    public List<BcClaBlbdDto> selectClaBlbdList(BcClaBlbdDto bcClaBlbdDto) {
    	
    	List<BcClaBlbdDto> usrIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectBlbdUsrList", bcClaBlbdDto);
    	if(bcClaBlbdDto.getSrchField() != null && !bcClaBlbdDto.getSrchField().isEmpty()) {

    		Map<String, String> kerisUsrIdToUsrIdMap = new HashMap<>();
    		for (BcClaBlbdDto dto : usrIdList) {
    			kerisUsrIdToUsrIdMap.put(dto.getKerisUsrId(), dto.getUsrId());
    		}
    		
    		String[] usrIds = bcClaBlbdDto.getUsrIds();
    		
    		for (int i = 0; i < usrIds.length; i++) {
    			String newUsrId = kerisUsrIdToUsrIdMap.get(usrIds[i]);
    			if (newUsrId != null) {
    				usrIds[i] = newUsrId;
    			}
    		}
    	}

    	List<BcClaBlbdDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectClaBlbdList", bcClaBlbdDto);
    	Integer claBlbdCnt = commonDao.select(MAPPER_NAMESPACE + "selectClaBlbdCnt", bcClaBlbdDto);
    	if (!list.isEmpty()) {
    		list.get(0).setBlbdCnt(claBlbdCnt);
		}
    	
    	// 학급게시판에 등록된 첨부파일 조회
    	for(BcClaBlbdDto item : list ) {
    		Long annxId = item.getAnnxId();
    		if(annxId != null && annxId > 0) {
            	List<BcAnnxFleDto> fleList = commonDao.selectList(MAPPER_NAMESPACE + "selectAnnxFleList", annxId);
            	item.setFleList(fleList);
        	}
    	}

        return list;
    }


    /**
     * 학급게시판 상세 조회
     *
     * @param BcClaBlbdDto
     * @return BcClaBlbdDto
     */
    public BcClaBlbdDto getClaBlbdInfo(BcClaBlbdDto bcClaBlbdDto) {
    	BcClaBlbdDto claBlbdInfo = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdInfo", bcClaBlbdDto);

    	// 학급게시판 댓글 목록
    	List<BcClaBlbdUcwrDto> ucwrList = commonDao.selectList(MAPPER_NAMESPACE + "selectClaBlbdUcwrList", bcClaBlbdDto);
    	if(ucwrList != null && ucwrList.size() > 0) {
    		claBlbdInfo.setUcwrList(ucwrList);
    	}
    	
    	Long annxId = claBlbdInfo.getAnnxId();
    	if(annxId != null && annxId > 0) {
        	// 첨부파일쪽 DB 확인 필요함 - API 통신으로 갖고와야 할 경우가 발생할 수 있음....
        	List<BcAnnxFleDto> fleList = commonDao.selectList(MAPPER_NAMESPACE + "selectAnnxFleList", annxId);
        	claBlbdInfo.setFleList(fleList);
    	}

    	return claBlbdInfo;
    }

	/**
	 * 유저가 5초 이내에 게시글을 등록했는지 확인합니다.
	 * @param String usrId
	 * @return boolean
	 */
	public boolean isUserPostingTooQuickly(String usrId) {
		Long timeDiff = commonDao.select(MAPPER_NAMESPACE + "selectUsrLstClaBldbTmDff", usrId);
		return timeDiff != null && timeDiff < 10;
	}

    /**
     * 학급게시판 등록/수정/삭제
     *
     * @param BcClaBlbdDto
     * @return Integer
     */
    @Transactional
    public Map<String, Object> saveClaBlbdInfo(BcClaBlbdDto bcClaBlbdDto) {
		if (isUserPostingTooQuickly(bcClaBlbdDto.getUsrId())) {
			throw new RuntimeException("게시글을 연속으로 등록할 수 없습니다.");
		}

		Map<String, Object> result = new HashMap<>();
    	
    	int check = 0;
    	String flag = "";
    	
        if (bcClaBlbdDto.getFlag().equals("insert")) {
        	check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdInfo", bcClaBlbdDto);
        	flag = "insert";
            if(check > 0) {
            	result.put("claBlbdId", bcClaBlbdDto.getClaBlbdId());
            }
            
        } else {
        	// 본인이 작성한 게시글만 수정, 삭제 가능
        	String claBlbdUsrCheck = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdUsrCheck", bcClaBlbdDto);
        	
        	if("Y".equals(claBlbdUsrCheck)) {
        		if (bcClaBlbdDto.getFlag().equals("update")) {
                    check = commonDao.update(MAPPER_NAMESPACE + "updateClaBlbdInfo", bcClaBlbdDto);
                    flag = "update";
                } else if (bcClaBlbdDto.getFlag().equals("delete")) {
                    check = commonDao.delete(MAPPER_NAMESPACE + "deleteClaBlbdInfo", bcClaBlbdDto);
                    flag = "delete";
                }
        	}
        }	
        	
        result.put("count", check);
        result.put("flag", flag);
        result.put("message", check > 0 ? "Success" : "Failure");
        
        return result;
    }

    /**
     * 학급게시판 댓글 등록/수정/삭제
     *
     * @param BcClaBlbdDto
     * @return Integer
     */
    @Transactional
    public int saveClaBlbdUcwrInfo(BcClaBlbdUcwrDto bcClaBlbdUcwrDto) {
    	int check = 0;
    	
    	
		if (bcClaBlbdUcwrDto.getFlag().equals("insert")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdUcwrInfo", bcClaBlbdUcwrDto);
		} else {
			// 본인이 작성한 게시글만 수정, 삭제 가능
			String claBlbdUsrCheck = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdUcwrUsrCheck", bcClaBlbdUcwrDto);
			
			if("Y".equals(claBlbdUsrCheck)) {
				if (bcClaBlbdUcwrDto.getFlag().equals("update")) {
					check = commonDao.insert(MAPPER_NAMESPACE + "updateClaBlbdUcwrInfo", bcClaBlbdUcwrDto);
				} else {
					check = commonDao.insert(MAPPER_NAMESPACE + "deleteClaBlbdUcwrInfo", bcClaBlbdUcwrDto);
				}

			}
			
		}
		
		
    	return check;
    }
    
    /**
	 * @param String
	 * @return List<BcStuListDto>
	 */
	public List<BcStuListDto> selectStuList(String usrId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectStuList", usrId);
    }
	
	@Transactional(readOnly = true)
	public Map<String, Object> selectOptTxbIdChk(BcClaBlbdDto bcClaBlbdDto) {
		Map<String, Object> returnMap = new HashMap<>();
		
		String optTxbId = bcClaBlbdDto.getOptTxbId();
		String chkOptTxbId = commonDao.select(MAPPER_NAMESPACE + "selectOptTxbIdChk", bcClaBlbdDto);

		if (chkOptTxbId == null) {
			returnMap.put("status", "error");
			returnMap.put("errorMessage", "학급게시판 정보를 찾을 수 없습니다.");
		} else if (chkOptTxbId.equalsIgnoreCase(optTxbId)) {
			returnMap.put("status", "success");
		}
		
		return returnMap;
	}
}
