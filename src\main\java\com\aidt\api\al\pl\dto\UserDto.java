package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class UserDto {

    /** 사용자ID  */
    private String userId;
    /** 사용자명 */
    private String userName;
    /** 학교이름 */
    private String schoolName;
    /** 학교구분코드[2:초등,3:중등,4:고등] */
    private String userDivision;
    /** 학년 */
    private String userGrade;
    /** 반 */
    private String userClass;
    /** 학생성별[1:남학생, 2:여학생] */
    private String userGender;
    private String regId;
    private String regDt;

    private String usrId;

    private String usrNm;

    private String optTxbId;

    private String txbId;

    private String claId;

    private String usrTpCd;

    private String role;
    
    //추천문항정보
    private List<String> userQueList;

}

