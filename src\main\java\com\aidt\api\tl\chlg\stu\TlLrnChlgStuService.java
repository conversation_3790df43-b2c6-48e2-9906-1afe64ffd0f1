
package com.aidt.api.tl.chlg.stu;


import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.time.LocalDate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.tl.chlg.dto.TlLrnChlgDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-09 14:28:18
 * @modify date 2024-04-09 14:28:18
 * @desc TlLrnChlgStu Service 챌린지설정 서비스
 */
@Service
public class TlLrnChlgStuService {
    private final String MAPPER_NAMESPACE = "api.tl.chlg.stu.";
    @Autowired
    private CommonDao commonDao;

    /**
     * 챌린지설정목록 조회 서비스
     * 
     * @param optTxbId 운영교과서ID
     * @param lrnUsrId 학습사용자ID
     * @return List<TlLrnChlgDto>
     */
    @Transactional(readOnly = true)
    public List<TlLrnChlgDto> selectLrnChlgList(String optTxbId, String lrnUsrId) {
        List<TlLrnChlgDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectChlgList",
                Map.of("optTxbId", optTxbId, "lrnUsrId", lrnUsrId));

        return list;
    }

    /**
     * 챌린지목록 저장처리
     * 
     * @param saveDto 저장데이터 목록
     * @return
     */
    @Transactional
    public int saveList(TlLrnChlgDto saveDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertLrnChlg", saveDto);
    }
    
    /**
     * 챌린지 중도포기
     * 
     * @param lrnGoalNo 학습목표번호
     * @return Integer
     * 
     */
    @Transactional
    public int updateLrnChlgQt(TlLrnChlgDto saveDto) {
        return commonDao.update(MAPPER_NAMESPACE + "updateLrnChlgQt", saveDto);
    }

//    /**
//     * 챌린지 업데이트
//     * 
//     * @param saveDto 저장데이터 목록
//     * @return
//     */
//    @Transactional
//	public int updateList(String optTxbId, String lrnUsrId) {
//    	TlLrnChlgDto chlgObj = commonDao.select(MAPPER_NAMESPACE + "selectGoalDvCd", //이번 주 챌린지 정보
//                Map.of("optTxbId", optTxbId, "lrnUsrId", lrnUsrId));
//    	
//    	LocalDate startDate = LocalDate.parse(chlgObj.getLrnStrDt().replace(".", "-"));
//    	LocalDate endDate = LocalDate.parse(chlgObj.getLrnEndDt().replace(".", "-"));
//    	
//    	LocalDate now = LocalDate.now();
//
//    	if((now.isAfter(startDate) || now.isEqual(startDate)) && (now.isBefore(endDate) || now.isEqual(endDate))) {
//    		int strDate = Integer.parseInt(chlgObj.getLrnStrDt().replace(".", ""))-1; // 이번 주 시작날짜 형변환
//        	
//        	String goalDvCd = chlgObj.getLrnGoalDvCd().toString();
//        	String lrnStrDt = chlgObj.getLrnStrDt().toString();
//
//        	return commonDao.update(MAPPER_NAMESPACE + "updateLrnChlg", Map.of("optTxbId", optTxbId, "usrId", lrnUsrId, "goalDvCd", goalDvCd, "lrnStrDt", lrnStrDt, "smtDtm", strDate));
//    	} else {
//    		return 0;
//    	}
//    	
//	}
}
