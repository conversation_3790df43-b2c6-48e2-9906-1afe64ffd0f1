package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-01 13:45:32
 * @modify date 2024-06-01 13:45:32
 * @desc [AI맞춤학습 토픽정보 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlPlTpcProfInfoStuResponseDto {
		@Parameter(name="운영교과서id")
		private String optTxbId;
		
		@Parameter(name="중단원 지식맵 노드Id")
		private String mluKmmpNodId;
		
		@Parameter(name="중단원 지식맵 노드명")
		private String mluKmmpNodNm;
		
		@Parameter(name="차시 지식맵 노드Id")
		private String kmmpNodId;
		
		@Parameter(name="차시 지식맵 노드명")
		private String kmmpNodNm;
		
		@Parameter(name="토픽Id")
		private String tpcId;
		
		@Parameter(name="토픽명")
		private String tpcNm;
		
		@Parameter(name="뎁스")
		private String dpth;
		
		@Parameter(name="차시 사용 여부")
		private String tcuseYn;
		
		@Parameter(name="토픽 숙련도")
		private Double tpcAvn;
		
		@Parameter(name="AI예측평균정답률")
	    private String aiPredAvgCansRt;
		
		@Parameter(name="AI예측평균점수")
	    private String aiPrefAvgScr;
		
}
