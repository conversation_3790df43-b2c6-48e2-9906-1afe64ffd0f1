package com.aidt.api.ea.asncom.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @email
 * @create date 2024-02-27
 * @modify date 2024-02-27
 * @desc 과제 - 파일 Dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaAsnFleDto {

	@Parameter(name="첨부파일ID")
	private int annxFileId;

	@Parameter(name="첨부ID")
	private int annxId;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="첨부파일명")
	private String annxFileNm;

	@Parameter(name="첨부파일원본명")
	private String annxFileOrglNm;

	@Parameter(name="첨부파일확장자명")
	private String annxFileFextNm;

	@Parameter(name="첨부파일사이즈")
	private long annxFileSize;

	@Parameter(name="첨부파일경로명")
	private String annxFilePathNm;

	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="문서뷰어ID")
    private String docViId;


}
