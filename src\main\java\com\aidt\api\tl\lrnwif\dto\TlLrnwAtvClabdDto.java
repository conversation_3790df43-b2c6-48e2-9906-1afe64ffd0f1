package com.aidt.api.tl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-48 14:46:01
 * @modify date 2024-06-48 14:46:01
 * @desc TlLrnwAtvClabdDto Dto 학습활동클래스보드정보
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TlLrnwAtvClabdDto {

    /** 클래스보드대제목ID */
    @Parameter(name="클래스보드대제목ID")
    private String clabdLrgsId;
    /** 클래스보드소제목ID */
    @Parameter(name="클래스보드소제목ID")
    private String clabdSmlId;
    /** 클래스보드대제목명 */
    @Parameter(name="클래스보드대제목명")
    private String clabdLrgsNm;
    /** 클래스보드소제목명 */
    @Parameter(name="클래스보드소제목명")
    private String clabdSmlNm;
    /** 클래스보드형태 */
    @Parameter(name="클래스보드형태")
    private String clabdTyp;
    /** 클래스보드URL */
    @Parameter(name="클래스보드URL")
    private String clabdUrl;
}
