package com.aidt.api.bc.cm.stu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.dto.BcUserInfoDto;
import com.aidt.api.bc.cm.dto.FlnDto;
import com.aidt.common.CommonDao;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-16 12:19:59
 * @modify 2024-01-16 12:19:59
 * @desc 첨부파일 테스트 service (DB)
 */
@Slf4j
@Service
public class BcCmStuService {

	// 임시경로
    private final String MAPPER_NAMESPACE = "api.bc.cm.stu.";

    @Autowired
    private CommonDao commonDao;

    @Autowired
    private WebFluxUtil webFluxUtil;

    @Value("${aidt.endpoint.lw_myhm_stu_point:}")
	private String endpoint_lw_myhm_stu_point;
    
    /**
	 * 로그인 성공 후 마이홈 포인트 API CALL
	 * @param
	 * @return Map<String, Object>
	 */
    public Map<String, Object> callMyhmApi(String accessToken, Map<String,String> paramMap) {

    	//String pointUrl = "https://lw.aitextbook.co.kr/api/v1/lw/myhm/stu/point";
        Map<String, Object> returnMap = new HashMap<>();

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Authorization", "Bearer " + accessToken);
        httpHeaders.add("Content-Type", "application/json");

        try {
            String jsonString = new ObjectMapper().writeValueAsString(paramMap);
            String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
            return CoreUtil.Json.jsonString2Map(post);
        } catch (JsonProcessingException e) {
        	log.debug("JsonProcessingException ");
        }

        return returnMap;
    }


    @Transactional
    public int updateFlnStCd(FlnDto flnDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateCrud", flnDto);
    }

    /**
     * 같은반 학생 목록
     * @param bcUserInfoDto
     * @return List<BcUserInfoDto>
     */
	public List<BcUserInfoDto> sameClaStuList(BcUserInfoDto bcUserInfoDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "sameClaStuList", bcUserInfoDto);
	}

}
