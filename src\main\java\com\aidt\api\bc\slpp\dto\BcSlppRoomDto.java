package com.aidt.api.bc.slpp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcSlppRoomDto {
    @Parameter(name="학생사용자ID")
    private String stuUsrId;

    @Parameter(name="학생사용자명")
    private String stuName;

    @Parameter(name="학생사용자이미지")
    private String stuImg;

    @Parameter(name="노출대화내용")
    private String slppCn;

    @Parameter(name="작성자")
    private String crtrId;

    @Parameter(name="작성일")
    private String crtDtm;

    @Parameter(name="확인여부")
    private String CofmYn;
}
