<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.xx.sample.SampleVue">

	<sql id="PAGING_HEADER">
		<if test="page != null and rowsPerPage != null">
			SELECT COUNT(*) OVER() as TOTAL_COUNT
				 , R.* FROM(
		</if>
	</sql>
	<sql id="PAGING_FOOTER">
		<if test="page != null and rowsPerPage != null">
			) R
			LIMIT #{rowsPerPage, jdbcType=INTEGER} OFFSET #{page, jdbcType=INTEGER}
		</if>
	</sql>
	<select id="selectMenu" resultType="com.aidt.api.xx.sample.vue.dto.SampleMenuDto">
	    SELECT 	  MENU_ID
				, PARENT_MENU_ID
				, MENU_NM
				, FILE_PATH
				, FILE_NM
				, USE_YN
				, MENU_ORDER
				, REG_ID
				, DATE_FORMAT(REG_DT, '%Y-%m-%d') AS REG_DT
		FROM TEMP_TI_MENU
		WHERE 1=1
		<if test = 'parentMenuId != null and !"".equals(parentMenuId)'>
			AND PARENT_MENU_ID = #{parentMenuId}
		</if>
		<if test = 'useYn != null and !"".equals(useYn)'>
			AND USE_YN = #{useYn}
		</if>
		ORDER BY PARENT_MENU_ID, MENU_ORDER
	</select>

	<select id="selectUser" resultType="com.aidt.api.xx.sample.vue.dto.SampleUserDto">

	</select>

	<!-- 목록조회 -->
	<select id="selectCrud" resultType="com.aidt.api.xx.sample.vue.dto.SampleGridDataDto">
		<include refid="PAGING_HEADER" />
	    SELECT 	  `NO`
				, USER_ID
				, USER_NAME
				, TEL
				, EMAIL
				, TEXT_DESC
				, REG_ID
				, DATE_FORMAT(REG_DT, '%Y-%m-%d') AS REG_DT
		FROM TEMP_IMSI_GRID_DATA
		WHERE 1=1
		<if test = 'no != null and !"".equals(no)'>
			AND NO = #{no}
		</if>
		<if test='userName != null and userName != ""'> AND USER_NAME LIKE CONCAT('%', #{userName}, '%')</if>
		<if test = 'orderBy != null and !"".equals(orderBy)'>
			ORDER BY #{orderBy} <if test="descending"> DESC</if>
		</if>

		<include refid="PAGING_FOOTER" />
	</select>

	<!-- 목록조회 건수-->
	<select id="countCrud" resultType="int">
	    SELECT COUNT(*) AS CNT
		FROM TEMP_IMSI_GRID_DATA
		WHERE 1=1
		<if test = 'no != null and !"".equals(no)'>
			AND NO = #{no}
		</if>
		<if test='userName != null and userName != ""'> AND USER_NAME LIKE CONCAT('%', #{userName}, '%')</if>
		ORDER BY REG_DT DESC
	</select>

	<!-- 등록 저장 -->
	<insert id="insertCrud" parameterType="com.aidt.api.xx.sample.vue.dto.SampleGridDataDto">
		INSERT INTO TEMP_IMSI_GRID_DATA(
			USER_ID, USER_NAME, TEL, EMAIL, TEXT_DESC, REG_ID, REG_DT
		) VALUES (
			#{userId},
			#{userName},
			#{tel},
			#{email},
			#{textDesc},
			#{regId},
			CURDATE()
		)
	</insert>

	<!-- 등록 저장 - Foreach -->
	<insert id="insertCrudForeach" parameterType="java.util.List">
		INSERT INTO TEMP_IMSI_GRID_DATA(
			USER_ID, USER_NAME, TEL, EMAIL, TEXT_DESC, REG_ID, REG_DT
		) VALUES
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.userId},
			#{item.userName},
			#{item.tel},
			#{item.email},
			#{item.textDesc},
			#{item.regId},
			CURDATE()
		)
		</foreach>
	</insert>

	<!-- 수정 -->
	<update id="updateCrud" parameterType="com.aidt.api.xx.sample.vue.dto.SampleGridDataDto">
   		UPDATE TEMP_IMSI_GRID_DATA SET
			USER_ID = #{userId},
			USER_NAME = #{userName},
			TEL = #{tel},
			EMAIL = #{email}
		WHERE NO = #{no}
    </update>

	<!-- 삭제 -->
	<delete id="deleteCrud" parameterType="com.aidt.api.xx.sample.vue.dto.SampleGridDataDto">
		DELETE FROM TEMP_IMSI_GRID_DATA WHERE NO = #{no}
	</delete>

</mapper>