package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 평가 및 AI문항추천이력 저장
 * */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiRcmTsshQtmHistDto{
	
	
	@Parameter(name="평가ID")
    private Integer evId;
	
	@Parameter(name="사용자ID")
    private String usrId;
	
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    @Parameter(name="대단원지식맵노드ID")
	private String lluKmmpNodId;
    
    @Parameter(name="중단원지식맵노드ID")
	private String mluKmmpNodId;
	
	@Parameter(name="차시지식맵노드ID")
	private String tcKmmpNodId;
	
    @Parameter(name="토픽ID")
	private String tpcKmmpNodId;
    private String tpcKmmpNodNm;
    
    @Parameter(name="문항ID")
	private Integer qtmId;
    
	@Parameter(name="컨텐츠난이도구분코드")
	private String ctnDffdDvCd;
    
    @Parameter(name="학습자속도유형코드")
	private String lrnrVelTpCd;
	
    @Parameter(name="제출답변값")
	private String smtAnwVl;
    
    @Parameter(name="정답여부")
	private String cansYn;
    
    @Parameter(name="풀이시간초수")
    private Integer xplTmScnt;
    
    @Parameter(name="평가완료여부")
    private String evCmplYn;
    
    @Parameter(name="평가구분코드")
    private String evDvCd;

    @Parameter(name="평가상세구분코드")
    private String evDtlDvCd;

    @Parameter(name="평가명")
    private String evNm;

    @Parameter(name="응시시작일시")
    private String txmStrDtm;

    @Parameter(name="응시종료일시")
    private String txmEndDtm;

    @Parameter(name="문제수")
    private Integer qstCnt;
    
    @Parameter(name="정답수")
    private Integer cansCnt;
    
    @Parameter(name="정답률")
    private Double cansRt;
    
    @Parameter(name="평가시간초수")
    private Integer evTmScnt;

    @Parameter(name="최종문제수")
    private Integer fnlQstCnt;

    @Parameter(name="잠금여부")
    private String lcknYn;

    @Parameter(name="재응시허용여부")
    private String rtxmPmsnYn;

    @Parameter(name="응시기간설정여부")
    private String txmPtmeSetmYn;

    @Parameter(name="풀이시간설정여부")
    private String xplTmSetmYn;

    @Parameter(name="사용여부")
    private String useYn;

    @Parameter(name="삭제여부")
    private String delYn;

    @Parameter(name="생성자 ID")
    private String crtrId;

    @Parameter(name="생성일시")
    private String crtDtm;

    @Parameter(name="수정자ID")
    private String mdfrId;

    @Parameter(name="수정일시")
    private String mdfDtm;

    @Parameter(name="데이터베이스ID")
    private String dbId;
    
    @Parameter(name="시험범위순번")
	private Integer tsRngeSeqNo;

	@Parameter(name="단원운영교과서ID")
	private String luOptTxbId;

	@Parameter(name="대단원학습맵노드ID")
	private String luLrmpNodId;

	@Parameter(name="대단원학습맵노드명")
	private String luLrmpNodNm;
	
	@Parameter(name="중단원운영교과서ID")
	private String mluOptTxbId;
	
	@Parameter(name="중단원학습맵노드ID")
	private String mluLrmpNodId;
	
	@Parameter(name="중단원학습맵노드명")
	private String mluLrmpNodNm;

	@Parameter(name="차시운영교과서ID")
	private String tcOptTxbId;

	@Parameter(name="차시학습맵노드ID")
	private String tcLrmpNodId;

	@Parameter(name="차시학습맵노드명")
	private String tcLrmpNodNm;
	
	@Parameter(name="문항순서")
	private Integer qtmOrdn;
	
	@Parameter(name="문항난이도구분코드")
	private String qtmDffdDvCd;
	
	@Parameter(name="문항플랫폼난이도명")
	private String qpDffdNm;
	
	@Parameter(name="문항플랫폼대단원ID")
	private Integer qpLluId;
	
	@Parameter(name="문항플랫폼대단원명")
	private String qpLluNm;
	
	@Parameter(name="문항플랫폼차시ID")
	private Integer qpTcId;
	
	@Parameter(name="문항플랫폼차시명")
	private String qpTcNm;
	
	@Parameter(name="AI진단추천방식코드")
	private String aiDgnRcmMthdCd;
	
	@Parameter(name="AI진단추천기준코드")
	private String aiDgnRcmBsCd;
	
	@Parameter(name="평균정답률")
	private Double avgCansRt;
	
	@Parameter(name="총풀이학생수")
	private Integer ttlXplStuCnt;
	
	@Parameter(name="찜여부")
	private String dibsYn;
	
	@Parameter(name="응시회차")
	private Integer txmPn;
	
	@Parameter(name="AI예측평균정답률 :: AI Center에서 update해주는 항목")
	private Double aiPredAvgCansRt;
	
	@Parameter(name="AI예측평균점수 :: 실제 토픽별 평균점수")
	private Double aiPredAvgScr;
	
	@Parameter(name="AI예측 문항 정답률 :: AI Center에서 update해주는 항목")
	private Double aiPredCansRt;
	
	@Parameter(name = "평가지ID")
	private Integer  evshId;
	@Parameter(name = "평가지코드")
	private String evshCd;
	@Parameter(name = "평가지구분코드")
	private String evshDvCd;
}
