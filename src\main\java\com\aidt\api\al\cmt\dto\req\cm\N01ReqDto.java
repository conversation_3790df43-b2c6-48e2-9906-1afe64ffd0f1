package com.aidt.api.al.cmt.dto.req.cm;

import javax.validation.Valid;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.cm.N01Dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 15:10:58
 * @modify date 2024-05-24 15:10:58
 * @desc 성취기준총평 req dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class N01ReqDto {

    @Valid
    @Parameter(name="전체 문항", required=true)
    private QtmCntReqDto qtmCnt;

    @Parameter(name="input 값", required=true)
    private String inputText;

    public N01Dto toDto(AiCmtLvlCalculator calculator) {
        return N01Dto.builder()
                .achLvlCd(calculator.calculate(this.qtmCnt))
                .inputText(this.inputText).build();
    }

}
