package com.aidt.api.sl.inidat.stu;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.api.sl.inidat.dto.SlIniDatRcmLrnDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-01 11:53:04
 * @modify date 2024-07-01 11:53:04
 * @desc SlIniDatStu  특별학습 초기데이터작성 Service
 */
@Slf4j
@Service
public class SlIniDatStuService {
	private final String MAPPER_NAMESPACE = "api.sl.inidat.stu.";
	
	@Autowired
    private CommonDao commonDao;
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
	
	/**
     * 특별학습 학생 추천학습 등록
     * @param cndDto
     * @return
     */
	@Transactional
	public int insertRcmLrnDat(SlIniDatCondDto srhDto) {

		List<Map<String, Object>> spLrnList = commonDao.selectList(MAPPER_NAMESPACE + "selectSpLrnList", srhDto);
		int slCnt = commonDao.select(MAPPER_NAMESPACE + "selectSlCnt", srhDto);
		SlIniDatRcmLrnDto rcmLrnDto = new SlIniDatRcmLrnDto();
		
		int cnt = 0;
		
		if (spLrnList != null && spLrnList.size() > 0 && slCnt == spLrnList.size()) {
			for(Map<String, Object> list: spLrnList) {
					if(!list.get("SP_LRN_ID").toString().equals("")) {
						rcmLrnDto.setOptTxbId(srhDto.getOptTxbId());
						rcmLrnDto.setLrnUsrId(srhDto.getStuUsrId());
						rcmLrnDto.setSpLrnId(list.get("SP_LRN_ID").toString());
						rcmLrnDto.setCrtrId(srhDto.getStuUsrId());
						rcmLrnDto.setMdfrId(srhDto.getStuUsrId());
						rcmLrnDto.setDbId(DB_ID);

						if(list.get("LRNR_VEL_TP_CD").toString().equals("SL")
							&& (list.get("SP_LRN_DFFD").toString().equals("LL") || list.get("SP_LRN_DFFD").toString().equals("ML"))) { // 느린학습자
							rcmLrnDto.setRcmYn("Y");
						} else if(list.get("LRNR_VEL_TP_CD").toString().equals("FS")
							&& list.get("SP_LRN_DFFD").toString().equals("HH")) { // 빠른학습자
							rcmLrnDto.setRcmYn("Y");
						} else if(((list.get("LRNR_VEL_TP_CD").toString().equals("NM")) || (list.get("LRNR_VEL_TP_CD").toString().equals("BA")))
							&& (list.get("SP_LRN_DFFD").toString().equals("MM") || list.get("SP_LRN_DFFD").toString().equals("HM"))) { //보통학습자
							rcmLrnDto.setRcmYn("Y");
						} else if(list.get("SP_LRN_DFFD").toString().equals("00")) { //난이도 공통
							rcmLrnDto.setRcmYn("Y");
						} else {
						
							rcmLrnDto.setRcmYn("N");
						}
						
						commonDao.insert(MAPPER_NAMESPACE + "insertRcmLrnDat", rcmLrnDto);
						cnt++;
					}
			}
		}
		
		log.debug("##### 추천학습등록 건수 = " + String.valueOf(cnt));
		return cnt;
	}

}
