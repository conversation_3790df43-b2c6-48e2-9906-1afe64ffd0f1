package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSbcLrnAtvEvDto {
	/** 차시노드ID(차시) */
    @Parameter(name="차시노드ID")
    private String lrmpNodId;
    /** 활동ID */
    @Parameter(name="활동ID")
    private String lrnAtvId;
    /** 평가ID */
    @Parameter(name="평가ID")
    private String evId;
    /** 평가상세구분코드 */
    @Parameter(name="평가상세구분코드")
    private String evDtlDvCd;
    /** 평가상세구분코드명 */
    @Parameter(name="평가상세구분코드명")
    private String evDtlDvNm;
    /** 평가명 */
    @Parameter(name="평가명")
    private String evNm;
    /** 최종문제수 */
    @Parameter(name="최종문제수")
    private String fnlQstCnt;
    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;
    /** 재응시허용여부 */
    @Parameter(name="재응시허용여부")
    private String rtxmPmsnYn;
    /** 풀이시간초수 */
    @Parameter(name="풀이시간초수")
    private String xplTmScnt;
    /** 풀이시간설정여부 */
    @Parameter(name="풀이시간설정여부")
    private String xplTmSetmYn;
    /** 평가완료여부 */
    @Parameter(name="평가완료여부")
    private String evCmplYn;
    /** 추가평가ID */
    @Parameter(name="추가평가ID")
    private String extrEvId;
    /** 평가문항목록 */
    @Parameter(name="평가문항목록")
    private List<EaLrnRptTlSbcLrnAtvEvQtmDto> evQtmList;
    /** 보충심화 문항목록 */
    @Parameter(name="보충심화 문항목록")
    private List<EaLrnRptTlSbcLrnAtvEvQtmDto> extrEvQtmList;
    
    @Parameter(name="활동상태") 
    private String lrnStCd;
    
    @Parameter(name="학습현황 학습시간") 
    private String lrnTmScnt;
    
}
