package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학습 리포트 - talk의 토픽 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptWriteTpcDto {
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="대단원 노드 ID")
	private String lluKmmpNodId;
	
	@Parameter(name="대단원 노드명")
	private String lluKmmpNodNm;
	
	@Parameter(name="토픽노드ID")
	private String tpcKmmpNodId;
	
	@Parameter(name="토픽노드명")
	private String tpcKmmpNodNm;
	
	@Parameter(name="학습상태")	//(NL:학습하기 DL:이어하기 CL:복습하기)
	private String pgrsStCd;
	
	@Parameter(name="차시사용여부")
	private String tcUseYn;
	
	@Parameter(name="글쓰기 제출 여부")
	private String wrtStNm;
	
	@Parameter(name="첨삭 확인 여부")
	private String editStNm;

	@Parameter(name="수정일자") //마지막 일자
	private String lastDtm;
	
	@Parameter(name="학습시간")
	private String lrnTmScnt;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="첨삭확인결과")
	private String pgrsStNm;

}
