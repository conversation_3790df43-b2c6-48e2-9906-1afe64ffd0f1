package com.aidt.api.tl.oneclksetm;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmStuDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto;
import com.aidt.api.util.RedisUtil;
import com.aidt.common.CommonDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TlOneclkCacheService {
	private final String MAPPER_NAMESPACE = "api.tl.oneclksetm.tcr.";

    @Autowired
    private CommonDao commonDao;
    
    @Autowired
    private EaAsnComService eaAsnComService;
    
    @Autowired
    private BcCmService bcCmService;
    
    @Autowired
    private RedisUtil redisUtil;

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':txbTcList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectAllTxbTcList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectTxbTcListHm'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectLrnTocAtvInfo'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectLrnTocList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectLrnAtvList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectLsnMtrlInfo'", cacheManager="aidtCacheManager")
    })
    public int updateAntClaSbclrnRcstn(String orgnOptTxbId, TlOneClkSetmClaDto claDto, List<TlOneClkSetmTocDto> ansDtoList, List<TlOneClkSetmTocDto> aiAnsDtoList, String mdfrId, String kerisUsrId, Map<String, Object> extcmpTxbInfo, List<Map<String, Object>> orglTxbInfo) {
 
//    	String searchKey = "longCache:tl:" + claDto.getOptTxbId() + ":txbTcList*";
//        redisUtil.redisKeyDeleteArray(searchKey);
		String detailLocationSearchKey = "shortCache:bc:" + claDto.getOptTxbId() + ":selectStuCurLrnStDtl*";
        redisUtil.redisKeyDeleteArray(detailLocationSearchKey);
        
        int cnt = 0;

        List<Map<String, Object>> antTxbInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbAtvInfo", Map.of("optTxbId",claDto.getOptTxbId()));
        
        for(Map<String, Object> orgl : orglTxbInfo) {
        	Map<String, Object> ant = antTxbInfo.stream().filter(list -> list.get("lrmp_nod_id").equals(orgl.get("lrmp_nod_id"))).findAny().orElse(null);
        	if(ant != null && (!orgl.get("use_yn").equals(ant.get("use_yn")) || !orgl.get("atv_cnt").equals(ant.get("atv_cnt")))) {
        			TlOneClkSetmTocDto antInfo = new TlOneClkSetmTocDto();
        			antInfo.setOptTxbId(claDto.getOptTxbId());
        			antInfo.setLluNodId(ant.get("llu_nod_id").toString());
        			antInfo.setLrmpNodId(ant.get("lrmp_nod_id").toString());
        			antInfo.setUseYn(orgl.get("use_yn").toString());
        			antInfo.setAtvCnt(Integer.parseInt(ant.get("atv_cnt").toString()));
        			antInfo.setAtvChnCnt(Integer.parseInt(orgl.get("atv_cnt").toString()));
        			ansDtoList.add(antInfo);
        	}
        }
        
        List<Map<String, Object>> orglAiEaInfo = new ArrayList<>();
    	orglAiEaInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbLluInfo", Map.of("orgnOptTxbId", orgnOptTxbId, "optTxbId", claDto.getOptTxbId(), "use", "Y"));
    	if(orglAiEaInfo != null && orglAiEaInfo.size() > 0) {
    		for(Map<String, Object> aiEa : orglAiEaInfo) {
    			TlOneClkSetmTocDto antInfo = new TlOneClkSetmTocDto();
    			antInfo.setOptTxbId(claDto.getOptTxbId());
    			antInfo.setLluNodId(aiEa.get("lrmp_nod_id").toString());
    			antInfo.setUseYn(aiEa.get("use_yn").toString());
    			aiAnsDtoList.add(antInfo);
    		}
    	}
    	
        claDto.setMdfrId(mdfrId);
        claDto.setOrgnOptTxbId(orgnOptTxbId);
        // 학습 목차 저장
        cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaLrnTocList", claDto);
        // 학습 활동 저장
        cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaLrnAtvList", claDto);
        // 수업자료
        commonDao.delete(MAPPER_NAMESPACE + "deleteAntClaRegMtrl", claDto);
        cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAntClaRegMtrl", claDto);
        
        // 교사 추가 콘텐츠 저장
        commonDao.delete(MAPPER_NAMESPACE + "deleteTcrCtn", Map.of("optTxbId", claDto.getOptTxbId(),"usrId", claDto.getMdfrId()));
        cnt += commonDao.insert(MAPPER_NAMESPACE + "insertTcrCtn", Map.of("optTxbId", claDto.getOptTxbId(),"usrId", claDto.getMdfrId(), "orgnOptTxbId", claDto.getOrgnOptTxbId()));
        
        // 과제
        eaAsnComService.callTlAsn(ansDtoList);
        
        //ai과제
		if (aiAnsDtoList != null) {
			if (aiAnsDtoList.size() > 0) {
				for (TlOneClkSetmTocDto aiAnsDto : aiAnsDtoList) {
					EaAsnTcrDto asnDto = new EaAsnTcrDto();
					asnDto.setOptTxbId(claDto.getOptTxbId());
					asnDto.setLuNodId(aiAnsDto.getLluNodId());
					asnDto.setUseYn(aiAnsDto.getUseYn());
					log.debug("ai과제 수정 ----------------------------------------------");
					eaAsnComService.updateAiAsnStatus(asnDto);
				}
			}
		}
		
		// 타 교과서 재구성
		if (extcmpTxbInfo.get("EXTCMP_TXB_ID") == null || extcmpTxbInfo.get("EXTCMP_TXB_ID").equals("")) {
			Map<String, Object> claExtcmpTxbInfo = new HashMap<>();
			claExtcmpTxbInfo = commonDao.select(MAPPER_NAMESPACE + "selectExtcmpTxbMpn", Map.of("optTxbId",claDto.getOptTxbId()));

			if(claExtcmpTxbInfo.get("EXTCMP_TXB_ID") != null || !claExtcmpTxbInfo.get("EXTCMP_TXB_ID").equals("")) {
				commonDao.update(MAPPER_NAMESPACE + "updateExtcmpCnt", Map.of("optTxbId",claDto.getOptTxbId(), "txbId", extcmpTxbInfo.get("TXB_ID"), "crtrId", claDto.getMdfrId()));
			}
		} else {
			Map<String, Object> claExtcmpTxbInfo = new HashMap<>();
			claExtcmpTxbInfo = commonDao.select(MAPPER_NAMESPACE + "selectExtcmpTxbMpn", Map.of("optTxbId",claDto.getOptTxbId()));

			if(claExtcmpTxbInfo.get("EXTCMP_TXB_ID") == null || claExtcmpTxbInfo.get("EXTCMP_TXB_ID").equals("")) {
				commonDao.insert(MAPPER_NAMESPACE + "insertExtcmpCnt", Map.of("optTxbId",claDto.getOptTxbId(), "txbId", extcmpTxbInfo.get("EXTCMP_TXB_ID"), "crtrId", claDto.getMdfrId()));
			} else {
				commonDao.update(MAPPER_NAMESPACE + "updateExtcmpCnt", Map.of("optTxbId",claDto.getOptTxbId(), "txbId", extcmpTxbInfo.get("EXTCMP_TXB_ID"), "crtrId", claDto.getMdfrId()));
			}
		}
		
    	CmClaCpLogDto logDto = CmClaCpLogDto.builder().optTxbId(orgnOptTxbId).cpOptTxbId(claDto.getOptTxbId()).cpDvCd("OCK").cpPrcsYn("Y")
				.backendFlePth("TlOneclkCacheService.updateAntClaSbclrnRcstn").kerisUsrId(kerisUsrId).crtrId(mdfrId).build();
        bcCmService.insertClaCpLog(logDto);
        
        return cnt;
    }
    
    @Transactional
    @Caching(evict = {
    	   @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':txbTcList'", cacheManager="aidtCacheManager"),
           @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectAllTxbTcList'", cacheManager="aidtCacheManager"),
           @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectTxbTcListHm'", cacheManager="aidtCacheManager"),
           @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectLrnTocAtvInfo'", cacheManager="aidtCacheManager"),
           @CacheEvict(cacheNames="longCache", key="'tl:' + #claDto.optTxbId + ':selectLrnTocList'", cacheManager="aidtCacheManager")
    })
    public int updateAntClaLrnScdlMg(String orgnOptTxbId, TlOneClkSetmClaDto claDto, List<TlOneClkSetmTocDto> ansDtoList, List<TlOneClkSetmTocDto> aiAnsDtoList, String mdfrId, String kerisUsrId, List<Map<String, Object>> orglTxbInfo) {
    	
//    	String searchKey = "longCache:tl:" + claDto.getOptTxbId() + ":txbTcList*";
//        redisUtil.redisKeyDeleteArray(searchKey);
		String detailLocationSearchKey = "shortCache:bc:" + claDto.getOptTxbId() + ":selectStuCurLrnStDtl*";
		redisUtil.redisKeyDeleteArray(detailLocationSearchKey);

		List<Map<String, Object>> antTxbInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbAtvInfo", Map.of("optTxbId",claDto.getOptTxbId()));
        int cnt = 0;
        
        for(Map<String, Object> orgl : orglTxbInfo) {
        	Map<String, Object> ant = antTxbInfo.stream().filter(list -> list.get("lrmp_nod_id").equals(orgl.get("lrmp_nod_id"))).findAny().orElse(null);
        	if(ant != null && (!orgl.get("lckn_yn").equals(ant.get("lckn_yn")) || !orgl.get("atv_cnt").equals(ant.get("atv_cnt")))) {
        			TlOneClkSetmTocDto antInfo = new TlOneClkSetmTocDto();
        			antInfo.setOptTxbId(claDto.getOptTxbId());
        			antInfo.setLluNodId(ant.get("llu_nod_id").toString());
        			antInfo.setLrmpNodId(ant.get("lrmp_nod_id").toString());
        			antInfo.setLcknYn(orgl.get("lckn_yn").toString());
        			ansDtoList.add(antInfo);
        	}
        }
        
        claDto.setMdfrId(mdfrId);
        claDto.setOrgnOptTxbId(orgnOptTxbId);
        // 학습일정관리 저장
        cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaLrnScdlMg", claDto);
        eaAsnComService.updateTlAsnLockStatus(ansDtoList);
        if(ansDtoList != null && !ansDtoList.isEmpty()) {
            for (TlOneClkSetmTocDto ansTocDto : ansDtoList) {
            	EaAsnTcrDto asnDto = new EaAsnTcrDto();
            	asnDto.setOptTxbId(claDto.getOptTxbId());
    	    	asnDto.setLuNodId(ansTocDto.getLluNodId());
    	    	asnDto.setTcNodId(ansTocDto.getLrmpNodId());
    	    	asnDto.setLcknYn(ansTocDto.getLcknYn());
    	    	asnDto.setTcLcknYn(ansTocDto.getTcLcknYn());
    	    	log.debug(String.valueOf(asnDto));
                log.debug("교과과제 수정 ----------------------------------------------");
//                eaAsnComService.updateTlAsnLockStatus(asnDto);
                log.debug("ai과제 수정 ----------------------------------------------");
                eaAsnComService.updateAiAsnLockStatus(asnDto);
            }
        }
        
    	CmClaCpLogDto logDto = CmClaCpLogDto.builder().optTxbId(orgnOptTxbId).cpOptTxbId(claDto.getOptTxbId()).cpDvCd("OCK").cpPrcsYn("Y")
				.backendFlePth("TlOneclkCacheService.updateAntClaLrnScdlMg").kerisUsrId(kerisUsrId).crtrId(mdfrId).build();
    	bcCmService.insertClaCpLog(logDto);
    	
        return cnt;
    }
    
    @Transactional(readOnly = true)
    @Cacheable(
    		cacheNames = "longCache",
    		key = "'sl:' + #optTxbId + ':selectRcmCtnStuList'",
    		cacheManager = "aidtCacheManager"
    		)
    public List<TlOneClkSetmRcmStuDto> selectRcmCtnStuList(String optTxbId) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectRcmCtnStuList",  Map.of("optTxbId", optTxbId));
    }
}
