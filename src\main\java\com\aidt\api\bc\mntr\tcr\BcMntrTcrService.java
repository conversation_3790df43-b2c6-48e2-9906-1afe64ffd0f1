package com.aidt.api.bc.mntr.tcr;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.clablbd.dto.BcClaBlbdDto;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.bc.mntr.dto.BcMntrJsonDto;
import com.aidt.common.CommonDao;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-03-18 17:43:34
 * @modify 2024-03-18 17:43:34
 * @desc 교사_실시간모니터링 Service
 */

@Service
public class BcMntrTcrService {

	private final String MAPPER_NAMESPACE = "api.bc.mntr.tcr.";

	@Autowired
	private CommonDao commonDao;
	
	/**
     * 실시간 모니터링 지도 필요 목록 조회 서비스
     *
     * @param BcMntrDto
     * @return List<BcMntrDto>
     */
	public List<BcMntrDto> selectGdeNeedList(BcMntrDto bcMntrDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectGdeNeedList", bcMntrDto);
	}
	
	/**
     * 실시간 모니터링 지도 필요 목록 확인 여부 수정
     *
     * @param bcMntrDto
     * @return Integer
     */
    public int updateGdeNeedCofmYn(BcMntrDto bcMntrDto) {
    	int check = 0;
			check = commonDao.insert(MAPPER_NAMESPACE + "updateGdeNeedCofmYn", bcMntrDto);
    	return check;
    }
	
}
