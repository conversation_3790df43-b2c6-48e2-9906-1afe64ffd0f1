package com.aidt.api.bc.cm;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.at.token.dto.KerisLrnDataDto;
import com.aidt.api.at.token.dto.KerisLrnDataUpsertDto;
import com.aidt.api.at.user.UserService;
import com.aidt.api.at.user.dto.UserCreateDto;
import com.aidt.api.at.user.dto.UserCreateRstDto;
import com.aidt.api.bc.cm.dto.BcAgomFleDto;
import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.api.bc.cm.dto.BcClaListDto;
import com.aidt.api.bc.cm.dto.BcCmCdDto;
import com.aidt.api.bc.cm.dto.BcCmCrsStnLrnInfo;
import com.aidt.api.bc.cm.dto.BcCmKerisConnErr;
import com.aidt.api.bc.cm.dto.BcCmKerisConnLog;
import com.aidt.api.bc.cm.dto.BcCmKerisFrontReqLogDto;
import com.aidt.api.bc.cm.dto.BcCmLrnApoHstDto;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto;
import com.aidt.api.bc.cm.dto.BcCmSelectNoteFileReqDto;
import com.aidt.api.bc.cm.dto.BcCmSelectNoteFileResDto;
import com.aidt.api.bc.cm.dto.BcFncUseSetmDto;
import com.aidt.api.bc.cm.dto.BcLoginDto;
import com.aidt.api.bc.cm.dto.BcS3Dto;
import com.aidt.api.bc.cm.dto.BcSlppStuListDto;
import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.api.bc.cm.dto.BcUserInfoDto;
import com.aidt.api.bc.cm.dto.BcUsrInfoDto;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.bc.cm.dto.KerisFrontLogDto;
import com.aidt.api.bc.cm.dto.MenuDto;
import com.aidt.api.bc.inf.dto.BcInfDto;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.tl.chlg.dto.TlLrnChlgDto;
import com.aidt.base.exception.CustomException;
import com.aidt.base.exception.ExceptionMessage;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import java.util.Comparator;
import java.util.HashSet;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-02-26 16:19:59
 * @modify 2024-02-26 16:19:59
 * @desc 첨부파일
 */
@Slf4j
@Service
public class BcCmService {

	private final String MAPPER_NAMESPACE = "api.bc.common.cm.";

	private final String MAPPER_NAMESPACE_CHLG = "api.tl.chlg.stu.";

	private final String MAPPER_NAMESPACE_TCR = "api.bc.cm.tcr.";

	private static final List<String> SUPPORTED_EXTENSIONS = Arrays.asList(
		// PDF 관련 확장자
		"pdf",
		// MS Office 문서
		"doc", "docx", "xls", "xlsx", "ppt", "pptx",
		// 한글 문서
		"hwp", "hwpx",
		// Open Document 문서
		"odt",
		// 이미지 파일
		"jpg", "jpeg", "jpe", "png", "bmp", "tiff", "tif", "gif");
	@Autowired
	private CommonDao commonDao;

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private WebFluxUtil webFluxUtil;

	@Autowired
	private UserService userService;

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Value("${aidt.endpoint.lw_myhm_stu_point:}")
	private String endpoint_lw_myhm_stu_point;

	@Value("${aidt.endpoint.viewerApi.url:https://v01.aitextbook.co.kr/streamdocs/}")
	private String endpoint_viewerApi_url;

	@Value("${aidt.endpoint.viewerApi.id:api-admin}")
	private String endpoint_viewerApi_id;

	@Value("${aidt.endpoint.viewerApi.password:Cjsworydbr12#$}")
	private String endpoint_viewerApi_password;

	public List<String> getSupportedExtensions() {
		return SUPPORTED_EXTENSIONS;
	}

	// 웹전시 연수 계정 관리용 메서드
	public int updateStudentName(String userId, String userName) {
		return commonDao.update(MAPPER_NAMESPACE + "updateStudentName", Map.of("userId", userId, "userName", userName));
	}

	// 2024-07-10 학생목록 조회(KERIS API로 사용자명 가공)
	public List<BcUserInfoDto> selectStuInfoList(BcUserInfoDto bcUserInfoDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectStuInfoList", bcUserInfoDto);
	}

	// 2024-07-10 학급목록 조회(KERIS API로 학급목록 가공)
	public List<BcClaListDto> selectClaInfoList(BcClaListDto bcClaListDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectClaInfoList", bcClaListDto);
	}

	// 2024-07-01 표준체계ID별 데이터 저장(전입 / 전출에도 사용)
	public void upsertNtlvEduCrsStnData(KerisLrnDataUpsertDto kerisLrnDataUpsertDto) {
		// 2025.01.13 DBA 명명 규칙 변경 요청에 따라 기존 STN_SST_EDU_CRS_ID -> CRCL_CTN_ELM2_CD 컬럼만
		// 변경 처리
		commonDao.insert(MAPPER_NAMESPACE + "upsertNtlvEduCrsStnData", kerisLrnDataUpsertDto);
	}

	// 학생 로그인시 UUID로 학급 구성 여부 조회
	public int getClaAndOptTxbCheck(String userId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectClaAndOptTxbCheck", userId);
	}

	// 학생 로그인시 전입 UPSERT
	public int upsertKerisStuLrnInfo(KerisLrnDataUpsertDto kerisReqListUpsertDto) {
		// 2025.01.13 DBA 명명 규칙 변경 요청에 따라 기존 STN_SST_EDU_CRS_ID -> CRCL_CTN_ELM2_CD 컬럼만
		// 변경 처리
		return commonDao.insert(MAPPER_NAMESPACE + "upsertKerisStuLrnInfo", kerisReqListUpsertDto);
	}

	// 국가수준교육과정표준체계 기본정보
	public BcCmNtlvEduCrsStnSstDto selectNtlvEduCrsStnSst(BcCmNtlvEduCrsStnSstDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectNtlvEduCrsStnSst", dto);
	}

	// 국가수준교육과정표준체계 학습시작 정보 등록
	@Transactional
	public int saveNtlvEduCrsStnSstLrnStr(BcCmNtlvEduCrsStnSstDto dto) {
		int result = 0;
		if (selectNtlvEduCrsStnSst(dto) == null) {
			result = commonDao.insert(MAPPER_NAMESPACE + "insertNtlvEduCrsStnSst", dto);
		} else {
			result = commonDao.update(MAPPER_NAMESPACE + "updateNtlvEduCrsStnSstLrnStr", dto);
		}

		if (StringUtil.isNotBlank(dto.getLrnStrYn()) && "Y".equals(dto.getLrnStrYn())) {
			BcCmCrsStnLrnInfo infoDto = new BcCmCrsStnLrnInfo();
			infoDto.setCrclCtnElm2Cd(dto.getCrclCtnElm2Cd());
			infoDto.setTxbId(dto.getTxbId());
			infoDto.setOptTxbId(dto.getOptTxbId());

			insertCrsStnLrnInfo(infoDto);
		}

		return result;
	}

	// 과정표준체게 별 문항수 콘텐츠 수 등록
	@Transactional
	public int insertCrsStnLrnInfo(BcCmCrsStnLrnInfo dto) {
		int result = 1;

		if ((Integer)commonDao.select(MAPPER_NAMESPACE + "selectCrsStnLrnInfoCnt", dto) == 0) {
			// 문항수
			dto.setQstCnt(commonDao.select(MAPPER_NAMESPACE + "selectCrsStnLrnInfoQstCnt", dto));
			// 콘텐츠 수
			dto.setCtnCnt(commonDao.select(MAPPER_NAMESPACE + "selectCrsStnLrnInfoCtnCnt", dto));

			result = commonDao.insert(MAPPER_NAMESPACE + "insertCrsStnLrnInfo", dto);
		}

		return result;
	}

	public BcCmCrsStnLrnInfo selectCrsStnLrnInfo(BcCmCrsStnLrnInfo dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectCrsStnLrnInfo", dto);
	}

	@Transactional
	public int saveNtlvEduCrsStnSstPgrsRt(BcCmNtlvEduCrsStnSstDto dto, String sstPgrsRt) {
		int result = 1;

		BcCmCrsStnLrnInfo infoDto = new BcCmCrsStnLrnInfo();
		infoDto.setCrclCtnElm2Cd(dto.getCrclCtnElm2Cd());
		infoDto.setTxbId(dto.getTxbId());
		infoDto.setOptTxbId(dto.getOptTxbId());

		insertCrsStnLrnInfo(infoDto);

		BcCmCrsStnLrnInfo info = selectCrsStnLrnInfo(infoDto);

		boolean dbUpdate = true;

		if (info == null || info.getCtnCnt() == null || info.getCtnCnt() == 0) {
			dto.setPgrsRt("100");
		} else {
			long sp = 0;

			if (StringUtils.isNotBlank(sstPgrsRt)) {
				try {
					sp = Long.parseLong(sstPgrsRt);
				} catch (NumberFormatException nfe) {
					log.error("진도율 원본 값 long 변환 오류 >>> sstPgrsRt = " + sstPgrsRt);
				}
			}

			long p = 0;
			Long pgrsRt = (Long)commonDao.select(MAPPER_NAMESPACE + "selectNtlvEduCrsStnSstPgrsRt", dto);

			p = (pgrsRt == null ? 0 : pgrsRt);

			if (p > sp) {
				dto.setPgrsRt(p > 99 ? "100" : Long.toString(p));
			} else {
				dto.setPgrsRt(Long.toString(sp));
				dbUpdate = false;
			}
		}

		if (dbUpdate) {
			result = commonDao.update(MAPPER_NAMESPACE + "updateNtlvEduCrsStnSstPgrsRt", dto);
		}

		return result;
	}

	@Transactional
	public int updateNtlvEduCrsStnSstEvScr(BcCmNtlvEduCrsStnSstDto dto) {
		BcCmCrsStnLrnInfo infoDto = new BcCmCrsStnLrnInfo();
		infoDto.setCrclCtnElm2Cd(dto.getCrclCtnElm2Cd());
		infoDto.setTxbId(dto.getTxbId());
		infoDto.setOptTxbId(dto.getOptTxbId());

		insertCrsStnLrnInfo(infoDto);

		return commonDao.update(MAPPER_NAMESPACE + "updateNtlvEduCrsStnSstEvScr", dto);
	}

	public boolean saveNtlvEduCrsStnSstLrnCompCheck(BcCmNtlvEduCrsStnSstDto dto) {
		String result = commonDao.select(MAPPER_NAMESPACE + "saveNtlvEduCrsStnSstLrnCompCheck", dto);
		return result == null || "N".equals(result) ? false : true;
	}

	@Transactional
	public int updateNtlvEduCrsStnSstLrnCmpl(BcCmNtlvEduCrsStnSstDto dto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateNtlvEduCrsStnSstLrnCmpl", dto);
	}

	@Transactional
	public int insertKerisConnErr(BcCmKerisConnErr dto) {
		return commonDao.insert(MAPPER_NAMESPACE + "insertKerisConnErr", dto);
	}

	@Transactional
	public int insertKerisConnLog(BcCmKerisConnLog dto) {
		return commonDao.insert(MAPPER_NAMESPACE + "insertKerisConnLog", dto);
	}

	@Transactional
	public Long insertKerisFrontReqLog(BcCmKerisFrontReqLogDto dto) {
		commonDao.insert(MAPPER_NAMESPACE + "insertKerisFrontReqLog", dto);
		return dto.getLogId();
	}

	@Transactional
	public boolean insertKerisFrontLog(KerisFrontLogDto dto) {
		return commonDao.insert(MAPPER_NAMESPACE + "insertKerisFrontLog", dto) > 0 ? true : false;
	}

	@Transactional
	public int deleteKerisStuLrnInfo(String userId) {
		int delUsrResult = commonDao.update(MAPPER_NAMESPACE + "deleteKerisLrnOutUsr", userId);
		int delTknResult = commonDao.update(MAPPER_NAMESPACE + "deleteKerisLrnOutToken", userId);
		return delTknResult + delUsrResult;
	}

	// 학생 로그인 후 전출데이터 추출
	public List<KerisLrnDataDto> selectCmNtlvEduCrsStnSstList(String userId) {
		// 2025.01.13 DBA 명명 규칙 변경 요청에 따라 기존 STN_SST_EDU_CRS_ID -> CRCL_CTN_ELM2_CD 변경처리
		// 및 기존 alias 처리
		return commonDao.selectList(MAPPER_NAMESPACE + "selectCmNtlvEduCrsStnSstList", userId);
	}

	// 알림 표시 여부 조회
	public int checkInfm(BcUsrInfoDto bcUsrInfoDto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectCheckInfm", bcUsrInfoDto);
	}

	// 2024-06-22 로그인시 초기 데이터 구성(USR_ID, LECTURE_CODE로 판별)
	@Transactional
	public int checkLectureTcrUserInfo(BcUsrInfoDto bcUsrInfoDto) {
		// 2025.02.10 cm_opt_txb.keris_lect_cd, cm_cla.keris_cla_cd 컬럼 추가에 따른 set 추가
		bcUsrInfoDto.setKerisLectCd(bcUsrInfoDto.getLectureCd());
		bcUsrInfoDto.setKerisClaCd(bcUsrInfoDto.getClassCode());

		boolean noneData = false;
		/*
		 * 2025.03.01 책장 관련 주석처리 if
		 * ("none".equals(bcUsrInfoDto.getLectureCd().toLowerCase())) { noneData = true;
		 * // 관리자 회원, 학급, 운영교과 등록 event 통계용 책장, 미리보기 제외 위한 set
		 * bcUsrInfoDto.setKafkaLectNone(noneData);
		 * 
		 * if (StringUtils.isBlank(bcUsrInfoDto.getUsrId())) { return 0; }
		 * 
		 * 
		 * // 2025.02.14 책장 외 미리보기 추가에 따른 수정 // - 책장 : lecture_code = "none", class_code
		 * != "none" // -> user_id = user_id.replaceAll("-", "_"); // -> lecture_code =
		 * "B-" + class_code; // 책장 if
		 * ("none".equals(bcUsrInfoDto.getLectureCd().toLowerCase())) {
		 * bcUsrInfoDto.setUsrId(bcUsrInfoDto.getUsrId().replaceAll("-", "_"));
		 * bcUsrInfoDto.setLectureCd("B-" + bcUsrInfoDto.getClassCode()); } }
		 */

		bcUsrInfoDto.setDbId(DB_ID);
		// 기본 데이터 설정(교과서ID, 강의코드, 운영교과서ID, 학급ID), DB_ID = 학기구분코드
		//			String txbId = this.getTxbIdForSubDomain(DB_ID);
		// 설정 세팅
		String txbId = BcCmUtil.getTxbID(DB_ID);

		if (StringUtils.isBlank(txbId)) {
			txbId = DB_ID;
			return -1;
		}
		String lectureCd = bcUsrInfoDto.getLectureCd();
		String optTxbId = DB_ID + "-" + lectureCd;
		String claId = DB_ID + "-" + lectureCd;

		bcUsrInfoDto.setTxbId(txbId);
		bcUsrInfoDto.setOptTxbId(optTxbId);
		bcUsrInfoDto.setClaId(claId);
		bcUsrInfoDto.setDbId(DB_ID);

		// @ 서버정보에서 교과서학기코드 추출
		bcUsrInfoDto.setTxbTermCd(DB_ID);
		bcUsrInfoDto.setDbId(DB_ID);

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcUsrInfoDto.setUsrId(userDetails.getUsrId());
		bcUsrInfoDto.setKerisUsrId(userDetails.getKerisUsrId());

		// 사용자 구분
		String usrTpCd = bcUsrInfoDto.getUserType();
		if ("T".equals(usrTpCd)) {
			bcUsrInfoDto.setUsrTpCd("TE");
		} else if ("S".equals(usrTpCd)) {
			bcUsrInfoDto.setUsrTpCd("ST");
		}

		UserCreateRstDto rstDto = userService
			.saveCheckUsr(
				UserCreateDto.builder().kerisUsrId(bcUsrInfoDto.getKerisUsrId())
					.usrTpCd(bcUsrInfoDto.getUsrTpCd()).claId(bcUsrInfoDto.getClaId()).build(),
				bcUsrInfoDto.getUsrId());

		if (rstDto != null && rstDto.isUsrNew()) {
			/** 관리자 회원 신규 등록 event 통계용 set */
			bcUsrInfoDto.setKafkaUsrId(bcUsrInfoDto.getUsrId());
		}

		// 담당 교사 정보 초기화
		bcUsrInfoDto.setChgTcrUsrId(null);

		// 교사인 경우 최초 데이터 구성 시작
		if ("T".equals(usrTpCd)) {
			// 교사인 경우 담당 교사 정보 세팅
			bcUsrInfoDto.setChgTcrUsrId(bcUsrInfoDto.getUsrId());
		}
		// 책임 교사 확인
		String dbChgTcrUsrId = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmClaGetChgTcrUsrId", bcUsrInfoDto);
		
		if("T".equals(usrTpCd)) {
			// 반 이름 일치 여부 확인
			String chkClaNmYn = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmClaNmYn", bcUsrInfoDto);
			
			// 반 이름 업데이트
			if (chkClaNmYn != null && bcUsrInfoDto.getClassroomName() != null && !"".equals(bcUsrInfoDto.getClassroomName())) {
				if ("N".equals(chkClaNmYn)) {
					commonDao.update(MAPPER_NAMESPACE + "updateCmClaChgClassroomName", bcUsrInfoDto);
				}
			}
		}
		
		// 카운트+ 책임교사 확인
		if (dbChgTcrUsrId == null) {
			String schlCd = "0000000000";

			if (StringUtils.isNotBlank(bcUsrInfoDto.getKerisLectCd()) && bcUsrInfoDto.getKerisLectCd().length() == 26) {
				String[] values = bcUsrInfoDto.getKerisLectCd().split("_");

				if (values != null && values.length == 3) {
					// 학교코드
					if (values[0] != null && values[0].length() == 11) {
						schlCd = values[0].substring(1, values[0].length());
					}
				}
			}

			bcUsrInfoDto.setSchlCd(schlCd);

			/*
			 * 2025.03.02 반명 keris api 데이터 중 classroom_name 변경에 따른 추가 외 3개 컬럼 추가 등록 -
			 * classroom_nm, sbj_nm, class_period, day_cd - schl_cd 컬럼 = 천재초등학교 고정값 ->
			 * schoolName 변경
			 */

			commonDao.insert(MAPPER_NAMESPACE + "insertCmCla", bcUsrInfoDto);

			/** 관리자 학급 신규 등록 event 통계용 set */
			bcUsrInfoDto.setKafkaClaId(bcUsrInfoDto.getClaId());
		} else {
			if (bcUsrInfoDto.getChgTcrUsrId() != null && !"".equals(bcUsrInfoDto.getChgTcrUsrId())) {
				if (dbChgTcrUsrId.equals("N")) {
					commonDao.update(MAPPER_NAMESPACE + "updateCmClaChgTcrUsrId", bcUsrInfoDto);
				}
			}
		}

		// CM_OPT_TXB - 운영교과서 정보 조회 및 저장
		int cmOptTxbCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmOptTxb", bcUsrInfoDto);
		if (cmOptTxbCnt == 0) {
			commonDao.insert(MAPPER_NAMESPACE + "insertCmOptTxb", bcUsrInfoDto);

			/** 관리자 운영교과서 신규 등록 event 통계용 set */
			bcUsrInfoDto.setKafkaOptTxbId(bcUsrInfoDto.getOptTxbId());
		}
		// 교사인 경우 최초 데이터 구성 시작
		if ("T".equals(usrTpCd)) {
			// CM_OPT_TCR - 교사사용자 정보 조회 및 저장
			int cmOptTxbTcrCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmOptTcr", bcUsrInfoDto);
			if (cmOptTxbTcrCnt == 0) {
				commonDao.insert(MAPPER_NAMESPACE + "insertCmOptTcr", bcUsrInfoDto);
			}
			// 책장 데이터는 처리안함
			if (!noneData) {
				// CM_USR - 교사가 담당하고 있는 학생들의 정보 조회 및 저장
				List<BcUsrInfoDto> stuList = bcUsrInfoDto.getStuList();
				if (stuList != null && stuList.size() > 0) {
					/** 관리자 회원 신규 등록 event 통계용 set */
					List<String> kafkaUsrIds = new ArrayList<String>();

					for (BcUsrInfoDto stuInfo : stuList) {
						if (StringUtils.isNotBlank(stuInfo.getUser_id())) {
							// 장애 발생 시에도 다음 비지니스 로직에 영향 없도록 try catch 추가
							try {
								UserCreateRstDto stuRstDto = userService.saveCheckTokenUsr(
									UserCreateDto.builder().kerisUsrId(stuInfo.getUser_id()).claId(claId)
										.lectureCode(lectureCd).optTxbId(optTxbId).usrTpCd("ST").build());

								if (stuRstDto != null && StringUtils.isNotBlank(stuRstDto.getUsrId())) {
									if (stuRstDto.isUsrNew()) {
										/** 관리자 회원 신규 등록 event 통계용 set */
										kafkaUsrIds.add(stuRstDto.getUsrId());
									}

									// 학습자인 경우 모니터링 데이터 추가
									BcStuListDto stuListDto = new BcStuListDto();
									stuListDto.setUsrId(stuRstDto.getUsrId());
									stuListDto.setOptTxbId(bcUsrInfoDto.getOptTxbId());
									stuListDto.setTxbId(bcUsrInfoDto.getTxbId());

									int stuCurLrnStCnt = commonDao.select(MAPPER_NAMESPACE_TCR + "selectStuCurLrnStCnt",
										stuListDto);
									if (stuCurLrnStCnt == 0) {
										stuCurLrnStCnt = commonDao.insert(MAPPER_NAMESPACE_TCR + "insertStuCurLrnSt",
											stuListDto);
									}
								}
							} catch (Exception e) {
								log.error(e.getMessage());
							}
						}
					}

					/** 관리자 회원 신규 등록 event 통계용 set */
					bcUsrInfoDto.setKafkaUsrIds(kafkaUsrIds);
				}
			}
		}

		return 1;
	}

	// 교과서학기코드로 교과서ID 조회
	public String getTxbIdForTxbCd(String txbTermCd) {
		return commonDao.select(MAPPER_NAMESPACE + "selectTxbIdForTxbTermCd", txbTermCd);
	}

	// 서브도메인으로 교과서ID 조회
	public String getTxbIdForSubDomain(String subDomain) {
		return commonDao.select(MAPPER_NAMESPACE + "selectTxbIdForSubDomain", subDomain);
	}

	// 2024-07-01 서브도메인으로 파트너ID 조회
	public String getPartnerId(String subDomain) {
		return commonDao.select(MAPPER_NAMESPACE + "selectPartnerId", subDomain);
	}

	// 2024-06-10 기능 사용 설정 초기데이터 생성(KERIS를 통한 로그인시 교사정보 등록 후 로직이 들어가있음, 임시 버전임)
	public void checkFncUseSetm(BcUsrInfoDto bcUsrInfoDto) {
		int cmFncUseSetmCnt = commonDao.select(MAPPER_NAMESPACE + "selectCmFncUseSetm", bcUsrInfoDto);
		if (cmFncUseSetmCnt == 0) {
			// 운영교과서 정보 등록
			commonDao.insert(MAPPER_NAMESPACE + "insertCmFncUseSetm", bcUsrInfoDto);
		}
	}

	// 대화 클릭시 대화 목록 갱신
	public void checkSlppStuList(List<BcSlppStuListDto> bcSlppStuListDto) {
		if (bcSlppStuListDto != null && bcSlppStuListDto.size() > 0) {
			for (BcSlppStuListDto stuInfo : bcSlppStuListDto) {
				// 학생 목록 API 조회 후 해당 데이터 등록 여부 조회
				int slppStuCheck = commonDao.select(MAPPER_NAMESPACE + "selectCheckSlppStu", stuInfo);
				if (slppStuCheck == 0) {

					// 등록된 데이터 없음
					commonDao.insert(MAPPER_NAMESPACE + "insertSlppStu", stuInfo);
				}

			}
		}
	}

	// 메뉴 이동시마다 실시간 모니터링에 해당 메뉴 update
	public void updateStuCurLrnStMenu(BcMntrDto bcMntrDto) {
		commonDao.update(MAPPER_NAMESPACE + "updateStuCurLrnStMenu", bcMntrDto);
	}

	/**
	 * 메뉴 조회
	 * 
	 * @param String
	 * @return List<MenuDto>
	 */
	public List<MenuDto> selectMenu(MenuDto menuDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectMenu", menuDto);
	}

	// 알림 등록(공통)
	public void insertCmInfm(BcInfDto bcInfDto) {

	}

	/**
	 * @param String
	 * @return List<BcAnnxFleDto>
	 */
	public List<BcAnnxFleDto> selectFileList(String annxId) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectFileList", annxId);
	}

	/**
	 * @param BcLoginDto
	 * @return BcUsrInfoDto
	 */
	public BcUsrInfoDto selectTcrUser(BcLoginDto bcLoginDto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectTcrUser", bcLoginDto);
	}

	/**
	 * @param BcLoginDto
	 * @return BcUsrInfoDto
	 */
	public BcUsrInfoDto selectStuUser(BcLoginDto bcLoginDto) {
		BcUsrInfoDto dto = commonDao.select(MAPPER_NAMESPACE + "selectStuUser", bcLoginDto);
		if (dto != null && dto.getChgTcrUsrId() != null && !"".equals(dto.getChgTcrUsrId())) {
			String tcrKerisId = commonDao.select(MAPPER_NAMESPACE + "selectUserKerisId", dto.getChgTcrUsrId());
			dto.setTcrKerisUsrId(tcrKerisId);
		}
		return dto;
	}
	
	
	
	/**
	 * S3 업로드 후 첨부, 첨부파일 테이블 저장
	 *
	 * @param FileDto, String, long
	 * @return List<BcAnnxFleDto>
	 */
	@Transactional
	public List<BcAnnxFleDto> saveS3UploadAf(List<BcS3Dto.FileUploadResponse> multiFileList, String annxId,
		String taskName) {
		
		if (taskName == null || StringUtils.isBlank(taskName)) {
			throw new IllegalArgumentException("taskName 정보가 없습니다.");
		}

		// 리턴
		List<BcAnnxFleDto> annxFleList = new ArrayList<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		// 기존에 등록해서 사용중인 첨부ID가 존재함
		for (BcS3Dto.FileUploadResponse fileDto : multiFileList) {

			// S3 업로드 후 첨부파일 테이블에 정보 저장
			BcAnnxFleDto bcAnnxFleDto = new BcAnnxFleDto();

			String bucketUrl = DB_ID + "/lms/" + taskName + "/" + fileDto.getNewFileName();

			int lastDotIndex = fileDto.getNewFileName().lastIndexOf(".");

			String extension = fileDto.getNewFileName().substring(lastDotIndex + 1).trim().toLowerCase();

			// 지원되는 확장자인 경우에만 로직 수행
			if (SUPPORTED_EXTENSIONS.contains(extension)) {
				try {
					String bucketUrlFull = BcCmUtil.makeFleCdnUrl(BUCKET_NAME, bucketUrl);
					String streamdocsId = callStreamDocsIdApi(bucketUrlFull);
					bcAnnxFleDto.setDocViId(streamdocsId);
				} catch (JsonProcessingException e) {
					log.error("JsonProcessingException" + e.getMessage());
				}
			} else {
				bcAnnxFleDto.setBucketUrl(bucketUrl);
			}

			bcAnnxFleDto.setAnnxFleOrglNm(fileDto.getOriginFileName());
			bcAnnxFleDto.setAnnxFleNm(fileDto.getNewFileName());
			bcAnnxFleDto.setAnnxFleSze(fileDto.getFileSize());
			bcAnnxFleDto.setAnnxFlePthNm(bucketUrl);
			bcAnnxFleDto.setAnnxFleFextNm(fileDto.getContentType());

			annxFleList.add(bcAnnxFleDto);
		}

		Long annxIdL = null;
		
		
		if (StringUtils.isBlank(annxId)) {
			BcAnnxFleDto annxDto = new BcAnnxFleDto();
			annxDto.setUseYn("Y");
			annxDto.setCrtrId(userDetails.getUsrId());
			annxDto.setMdfrId(userDetails.getUsrId());
			annxDto.setDbId(DB_ID);
			commonDao.insert(MAPPER_NAMESPACE + "insertAnnx", annxDto);
			annxIdL = annxDto.getAnnxId();
			if (annxIdL == null) {
				throw new IllegalArgumentException("첨부파일 정보가 없습니다.");
			}
		} else {
			annxIdL = (long)Integer.parseInt(annxId);
		}
		
		
		for (BcAnnxFleDto bcAnnxFleDto : annxFleList) {
			bcAnnxFleDto.setAnnxId(annxIdL);
			bcAnnxFleDto.setUseYn("Y");
			bcAnnxFleDto.setCrtrId(userDetails.getUsrId());
			bcAnnxFleDto.setMdfrId(userDetails.getUsrId());
			bcAnnxFleDto.setDbId(DB_ID);
			commonDao.insert(MAPPER_NAMESPACE + "insertAnnxFle", bcAnnxFleDto);
		}
		return annxFleList;
	}
	
	
	
	/**
	 * 평가 - 노트 > S3 업로드 후 첨부, 첨부파일 테이블 저장
	 *
	 * @param FileDto, String, long
	 * @return List<BcAnnxFleDto>
	 */
	@Transactional
	public List<BcAnnxFleDto> saveS3UploadAfUseNote(List<BcS3Dto.FileUploadResponse> multiFileList, String annxId,
		String taskName, String selectNoteFileID) {
		
		if (taskName == null || StringUtils.isBlank(taskName)) {
			throw new IllegalArgumentException("taskName 정보가 없습니다.");
		}

		// 리턴
		List<BcAnnxFleDto> annxFleList = new ArrayList<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		// 기존에 등록해서 사용중인 첨부ID가 존재함
		for (BcS3Dto.FileUploadResponse fileDto : multiFileList) {

			// S3 업로드 후 첨부파일 테이블에 정보 저장
			BcAnnxFleDto bcAnnxFleDto = new BcAnnxFleDto();

			String bucketUrl = DB_ID + "/lms/" + taskName + "/" + fileDto.getNewFileName();

			int lastDotIndex = fileDto.getNewFileName().lastIndexOf(".");

			String extension = fileDto.getNewFileName().substring(lastDotIndex + 1).trim().toLowerCase();

			// 지원되는 확장자인 경우에만 로직 수행
			if (SUPPORTED_EXTENSIONS.contains(extension)) {
				try {
					String bucketUrlFull = BcCmUtil.makeFleCdnUrl(BUCKET_NAME, bucketUrl);
					String streamdocsId = callStreamDocsIdApi(bucketUrlFull);
					bcAnnxFleDto.setDocViId(streamdocsId);
				} catch (JsonProcessingException e) {
					log.error("JsonProcessingException" + e.getMessage());
				}
			} else {
				bcAnnxFleDto.setBucketUrl(bucketUrl);
			}

			bcAnnxFleDto.setAnnxFleOrglNm(fileDto.getOriginFileName());
			bcAnnxFleDto.setAnnxFleNm(fileDto.getNewFileName());
			bcAnnxFleDto.setAnnxFleSze(fileDto.getFileSize());
			bcAnnxFleDto.setAnnxFlePthNm(bucketUrl);
			bcAnnxFleDto.setAnnxFleFextNm(fileDto.getContentType());

			annxFleList.add(bcAnnxFleDto);
		}

		Long annxIdL = null;
		
		
		if (!"0".equalsIgnoreCase(selectNoteFileID)) annxId = selectNoteFileID;	
		
		//부모 테이블에 insert
		if ("0".equalsIgnoreCase(selectNoteFileID) || StringUtils.isBlank(annxId)) {
			BcAnnxFleDto annxDto = new BcAnnxFleDto();
			annxDto.setUseYn("Y");
			annxDto.setCrtrId(userDetails.getUsrId());
			annxDto.setMdfrId(userDetails.getUsrId());
			annxDto.setDbId(DB_ID);
			commonDao.insert(MAPPER_NAMESPACE + "insertAnnx", annxDto);
			annxIdL = annxDto.getAnnxId();
			if (annxIdL == null) {
				throw new IllegalArgumentException("첨부파일 정보가 없습니다.");
			}
		} else {
			annxIdL = (long)Integer.parseInt(annxId);
		}
		
		
		if (!annxFleList.isEmpty()) {
		    BcAnnxFleDto lastDto = annxFleList.get(annxFleList.size() - 1);

		    lastDto.setAnnxId(annxIdL);
		    lastDto.setUseYn("Y");
		    lastDto.setCrtrId(userDetails.getUsrId());
		    lastDto.setMdfrId(userDetails.getUsrId());
		    lastDto.setDbId(DB_ID);

		    commonDao.insert(MAPPER_NAMESPACE + "insertAnnxFle", lastDto);
		}
		
		return annxFleList;
	}

	

	/**
	 * S3 삭제 후 첨부파일 테이블 삭제
	 *
	 * @param String
	 * @return int
	 */
	@Transactional
	public int deleteS3Af(Long annxFleId) {
		int rsltCnt = 0;
		// 첨부파일ID(첨부상세ID target 삭제)
		if (annxFleId > 0) {
			// 추후 어떤식으로 삭제할 예정인지 정하고 DB_ID도 추가할지에 대한 논의 필요함
			BcAnnxFleDto bcAnnxFleDto = new BcAnnxFleDto();
			bcAnnxFleDto.setAnnxFleId(annxFleId);
			bcAnnxFleDto.setDbId(DB_ID);
			rsltCnt = commonDao.delete(MAPPER_NAMESPACE + "deleteAnnxFle", bcAnnxFleDto);
		}
		return rsltCnt;
	}

	@Transactional
	public Long saveS3CopyAnnxFle(AmazonS3 s3, Long annxId, Long[] deleteAnnxFleId) {
		Long newAnnxId = null;
		List<BcAnnxFleDto> copyList = null;

		if (annxId != null && annxId > 0) {
			List<BcAnnxFleDto> list = selectFileList(String.valueOf(annxId));

			if (list != null && list.size() > 0) {
				List<BcAnnxFleDto> saveList = null;

				if (deleteAnnxFleId != null && deleteAnnxFleId.length > 0) {
					saveList = list.stream().filter(dto -> !Arrays.asList(deleteAnnxFleId).contains(dto.getAnnxFleId()))
						.collect(Collectors.toList());
				} else {
					saveList = list;
				}

				if (saveList != null && saveList.size() > 0) {
					copyList = new ArrayList<BcAnnxFleDto>();
					BcAnnxFleDto copyDto = null;

					String ext = null;
					String annxFleNm = null;
					String annxFlePthNm = null;

					for (BcAnnxFleDto dto : saveList) {
						if (StringUtil.isNotBlank(dto.getAnnxFleNm()) && StringUtil.isNotBlank(dto.getAnnxFlePthNm())) {
							ext = null;

							if (dto.getAnnxFleNm().lastIndexOf(".") > -1) {
								ext = dto.getAnnxFleNm().substring(dto.getAnnxFleNm().lastIndexOf("."),
									dto.getAnnxFleNm().length());
							}

							annxFleNm = UUID.randomUUID().toString();

							if (StringUtil.isNotBlank(ext)) {
								annxFleNm += ext;
							}

							annxFlePthNm = dto.getAnnxFlePthNm().replaceAll(dto.getAnnxFleNm(), "") + annxFleNm;

							if (BcCmUtil.copyS3File(s3, BUCKET_NAME, dto.getAnnxFlePthNm(), annxFlePthNm)) {
								copyDto = new BcAnnxFleDto();
								copyDto.setAnnxFleNm(annxFleNm);
								copyDto.setAnnxFleOrglNm(dto.getAnnxFleOrglNm());
								copyDto.setAnnxFleFextNm(dto.getAnnxFleFextNm());
								copyDto.setAnnxFleSze(dto.getAnnxFleSze());
								copyDto.setAnnxFlePthNm(annxFlePthNm);
								copyDto.setUseYn("Y");

								if (StringUtil.isNotBlank(dto.getDocViId()) && StringUtil.isNotBlank(ext)) {
									if (SUPPORTED_EXTENSIONS.contains(ext.replaceAll("\\.", ""))) {
										try {
											copyDto.setDocViId(
												callStreamDocsIdApi(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, annxFlePthNm)));
										} catch (JsonProcessingException jpe) {
											log.error("docViId request JsonProcessingException error... >>> {}",
												jpe.getMessage());
										} catch (CustomException ce) {
											log.error("docViId request CustomException error... >>> {} ",
												ce.getMessage());
										}
									}
								}

								copyList.add(copyDto);
							}
						}
					}
				}
			}
		}

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		BcAnnxFleDto annxDto = new BcAnnxFleDto();
		annxDto.setUseYn("Y");
		annxDto.setCrtrId(userDetails.getUsrId());
		annxDto.setMdfrId(userDetails.getUsrId());
		annxDto.setDbId(DB_ID);

		commonDao.insert(MAPPER_NAMESPACE + "insertAnnx", annxDto);

		if (annxDto.getAnnxId() == null || annxDto.getAnnxId() == 0) {
			throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR, "insertAnnx fail...");
		}

		newAnnxId = annxDto.getAnnxId();

		if (copyList != null && copyList.size() > 0) {
			for (BcAnnxFleDto bcAnnxFleDto : copyList) {
				bcAnnxFleDto.setAnnxId(newAnnxId);
				bcAnnxFleDto.setCrtrId(userDetails.getUsrId());
				bcAnnxFleDto.setMdfrId(userDetails.getUsrId());
				bcAnnxFleDto.setDbId(DB_ID);

				commonDao.insert(MAPPER_NAMESPACE + "insertAnnxFle", bcAnnxFleDto);
			}
		}

		return newAnnxId;
	}

	@Transactional
	public void deleteS3AnnxFle(AmazonS3 s3, Long[] deleteAnnxFleId) {
		if (deleteAnnxFleId != null && deleteAnnxFleId.length > 0) {
			List<BcAnnxFleDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectAnnxFleDeleteList",
				deleteAnnxFleId);

			if (list != null && list.size() > 0) {
				for (BcAnnxFleDto dto : list) {
					BcCmUtil.deleteS3File(s3, BUCKET_NAME, dto.getAnnxFlePthNm());
				}

				for (BcAnnxFleDto dto : list) {
					deleteS3Af(dto.getAnnxFleId());
				}
			}
		}
	}

	@Transactional
	public Long saveS3UploadAfAsnCbb(Long annxId, List<BcAnnxFleDto> list) {
		Long returnAnnxId = null;
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		if (annxId == null || annxId == 0) {
			BcAnnxFleDto annxDto = new BcAnnxFleDto();
			annxDto.setUseYn("Y");
			annxDto.setCrtrId(userDetails.getUsrId());
			annxDto.setMdfrId(userDetails.getUsrId());
			annxDto.setDbId(DB_ID);

			commonDao.insert(MAPPER_NAMESPACE + "insertAnnx", annxDto);

			if (annxDto.getAnnxId() == null || annxDto.getAnnxId() == 0) {
				throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR, "insertAnnx fail...");
			}

			returnAnnxId = annxDto.getAnnxId();
		} else {
			returnAnnxId = annxId;
		}

		if (list != null && list.size() > 0) {
			for (BcAnnxFleDto bcAnnxFleDto : list) {
				bcAnnxFleDto.setAnnxId(returnAnnxId);
				bcAnnxFleDto.setCrtrId(userDetails.getUsrId());
				bcAnnxFleDto.setMdfrId(userDetails.getUsrId());
				bcAnnxFleDto.setDbId(DB_ID);

				commonDao.insert(MAPPER_NAMESPACE + "insertAnnxFle", bcAnnxFleDto);
			}
		}

		return returnAnnxId;
	}

	/**
	 * 메인페이지(제출마감일까지 1일 미만개수
	 * 
	 * @param bcUserInfoDto
	 * @return
	 */
	public int selectASNCount(BcUserInfoDto bcUserInfoDto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectASNCount", bcUserInfoDto);
	}

	/**
	 * 24시간 내에 학습 이력 확인
	 * 
	 * @param bcUserInfoDto
	 * @return
	 */
	public int selectStudyChkCount(BcUserInfoDto bcUserInfoDto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectStudyChkCount", bcUserInfoDto);
	}

	/**
	 * 72시간 내에 AI 학습 이력 확인
	 * 
	 * @param bcUserInfoDto
	 * @return
	 */
	public int selectStudyAIChkCount(BcUserInfoDto bcUserInfoDto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectStudyAIChkCount", bcUserInfoDto);
	}

	// 확장자 추출
	// private String extractExt(String originalFilename) {
	// int pos = originalFilename.lastIndexOf(".");
	// return originalFilename.substring(pos + 1);
	// }

	/**
	 * 사용자 정보 서비스
	 *
	 * @param BcUserInfoDto
	 * @return List<BcTnteDto>
	 */
	public BcUserInfoDto getUserInfo() {
		CommonUserDetail securityUserDetail = jwtProvider.getCommonUserDetail();
		return BcUserInfoDto.builder().optTxbId(securityUserDetail.getOptTxbId()).txbId(securityUserDetail.getTxbId())
			.claId(securityUserDetail.getClaId()).usrId(securityUserDetail.getUsrId())
			.kerisUsrId(securityUserDetail.getKerisUsrId()).usrTpCd(securityUserDetail.getUsrTpCd()).build();
	}

	/**
	 * 공통코드 조회
	 * 
	 * @param BcUserInfoDto
	 * @return List<Map<String,String>>
	 */
	public List<Map<String, String>> cmCdList(BcCmCdDto bcCmCdDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "cmCdList", bcCmCdDto);
	}

	/**
	 * 원클릭 학습 설정 조회
	 *
	 * @param BcFncUseSetmDto
	 * @return ResponseList<BcFncUseSetmDto>
	 */
	public List<BcFncUseSetmDto> selectFncUseSetmInfo(BcFncUseSetmDto bcFncUseSetmDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectFncUseSetmInfo", bcFncUseSetmDto);
	}

	/**
	 * 학습 시간 정보 업데이트
	 *
	 * @param bcCmLrnTmDto
	 */
	//todo: 평가 제출 후, 비동기 처리를 위한 임시 처리.. 아래의 학습 시간 정보도 리팩토링 필요;;;
	@Transactional
	public void updateCmLrnTm(BcCmLrnTmDto bcCmLrnTmDto, String accessToken) {
		updateCmLrnTm(bcCmLrnTmDto, accessToken, null);
	}
	/**
	 * 학습 시간 정보 업데이트
	 * 
	 * @param bcCmLrnTmDto
	 */
	@Transactional
	public void updateCmLrnTm(BcCmLrnTmDto bcCmLrnTmDto, String accessToken, CommonUserDetail commonUserDetail) {

		boolean chkupdate = false;
		if (bcCmLrnTmDto.getTxbLrnTmScnt() == null) {
			bcCmLrnTmDto.setTxbLrnTmScnt(0L);
		} else {
			chkupdate = true;
		}
		if (bcCmLrnTmDto.getAiLrnTmScnt() == null) {
			bcCmLrnTmDto.setAiLrnTmScnt(0L);
		} else {
			chkupdate = true;
		}
		if (bcCmLrnTmDto.getSpLrnTmScnt() == null) {
			bcCmLrnTmDto.setSpLrnTmScnt(0L);
		} else {
			chkupdate = true;
		}
		if (bcCmLrnTmDto.getAiEvLrnTmScnt() == null) {
			bcCmLrnTmDto.setAiEvLrnTmScnt(0L);
		} else {
			chkupdate = true;
		}
		if (bcCmLrnTmDto.getEvLrnTmScnt() == null) {
			bcCmLrnTmDto.setEvLrnTmScnt(0L);
		} else {
			chkupdate = true;
		}

		if (bcCmLrnTmDto.getAiWrtngLrnTm() == null) {
			bcCmLrnTmDto.setAiWrtngLrnTm(0L);
		} else {
			chkupdate = true;
		}
		if (bcCmLrnTmDto.getAiRdngLrnTm() == null) {
			bcCmLrnTmDto.setAiRdngLrnTm(0L);
		} else {
			chkupdate = true;
		}

		if (chkupdate) {
			CommonUserDetail userDetails =  commonUserDetail != null ? commonUserDetail : jwtProvider.getCommonUserDetail();
			bcCmLrnTmDto.setMdfrId(userDetails.getUsrId());
			bcCmLrnTmDto.setCrtrId(userDetails.getUsrId());
			bcCmLrnTmDto.setOptTxbId(userDetails.getOptTxbId());
			bcCmLrnTmDto.setUsrId(userDetails.getUsrId());

			LocalDateTime now = LocalDateTime.now();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHH");
			String lrnYrDtm = now.format(formatter);
			bcCmLrnTmDto.setLrnYrDtm(lrnYrDtm);
			bcCmLrnTmDto.setDbId(DB_ID);
			if (commonDao.update(MAPPER_NAMESPACE + "updateCmLrnTm", bcCmLrnTmDto) == 0) {
				commonDao.update(MAPPER_NAMESPACE + "insertCmLrnTm", bcCmLrnTmDto);
			}

			// 첼린지 업데이트
			String lrnUsrId = userDetails.getUsrId();
			String optTxbId = userDetails.getOptTxbId();
			TlLrnChlgDto chlgObj = commonDao.select(MAPPER_NAMESPACE_CHLG + "selectGoalDvCdNow", // 이번 주 챌린지 정보
				Map.of("optTxbId", optTxbId, "lrnUsrId", lrnUsrId));
			if (chlgObj != null) {
				if ("QT".equals(chlgObj.getLrnGoalStCd()) || "CL".equals(chlgObj.getLrnGoalStCd())) { // 챌린지 중도 포기일시 또는
																										// 챌린지 성공일때
					return;
				}

				LocalDateTime startDt = chlgObj.getCrtDtm();
				int endDt = Integer.parseInt(chlgObj.getLrnEndDt().replace(".", "")); // 종료일

				String goalDvCd = chlgObj.getLrnGoalDvCd().toString();
				String lrnStrDt = chlgObj.getLrnStrDt().toString();
				String chlgCompl = "";

				TlLrnChlgDto acvChlg = commonDao.select(MAPPER_NAMESPACE_CHLG + "selectAcvClg", Map.of("optTxbId",
					optTxbId, "usrId", lrnUsrId, "goalDvCd", goalDvCd, "startDt", startDt, "endDt", endDt));
				if (chlgObj.getLrnGoalDvCd().equals("EA") && chlgObj.getLrnGoalQstCnt() <= acvChlg.getLrnAcvQstCnt()) {
					chlgCompl = "CL";
				} else if (chlgObj.getLrnGoalDvCd().equals("HR")
					&& chlgObj.getLrnGoalTmScnt() <= acvChlg.getLrnAcvTmScnt()) {
					chlgCompl = "CL";
				}

				commonDao.update(MAPPER_NAMESPACE_CHLG + "updateLrnChlg",
					Map.of("optTxbId", optTxbId, "usrId", lrnUsrId, "goalDvCd", goalDvCd, "lrnStrDt", lrnStrDt,
						"chlgCompl", chlgCompl, "lrnAcvQstCnt", acvChlg.getLrnAcvQstCnt(), "lrnGoalNo",
						chlgObj.getLrnGoalNo(), "lrnAcvTmScnt", acvChlg.getLrnAcvTmScnt()));
				if ("CL".equals(chlgCompl)) {
					try {
						callMyhmApi(accessToken,
							Map.of("pntCd", "AI_CC_01", "pntChkBsVl", lrnUsrId + chlgObj.getLrnGoalNo())); // 유저ID +
																																	// 챌린지번호로
																																	// 유일한 키
																																	// 생성
					} catch (Exception e) {
						log.error("마이홈API통신실패" + e.getMessage());
					}
				}
			}

		}
	}

	/**
	 * 마이홈 API 호출
	 * 
	 * @return Map<String, Object>
	 */
	public Map<String, Object> callMyhmApi(String accessToken, Map<String, String> paramMap)
		throws JsonProcessingException {
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Authorization", "Bearer " + accessToken);
		httpHeaders.add("Content-Type", "application/json");

		// point saving using lw-api sample
		String jsonString = new ObjectMapper().writeValueAsString(paramMap);
		String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
		return CoreUtil.Json.jsonString2Map(post);
	}

	/**
	 * 알지오매쓰 파일 조회
	 * 
	 * @param userId
	 * @return
	 */
	public BcAgomFleDto selectAgomFle(String agomFleId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectAgomFle", agomFleId);
	}

	/**
	 * 학습 접근 이력 등록
	 * 
	 * @param BcCmLrnApoHstDto
	 * @return
	 */
	public int selectBcCmLrnApoHstChkDate(BcCmLrnApoHstDto cmLrnApoHst) {

		if (StringUtils.isEmpty(cmLrnApoHst.getApoData())) {
			throw new IllegalArgumentException("Invalid apoData in BcCmLrnApoHstDto ");
		}
		if (StringUtils.isEmpty(cmLrnApoHst.getApoRefVl())) {
			throw new IllegalArgumentException("Invalid apoRefVl in BcCmLrnApoHstDto ");
		}
		if (StringUtils.isEmpty(cmLrnApoHst.getUsrId())) {
			throw new IllegalArgumentException("Invalid usrId in BcCmLrnApoHstDto ");
		}

		return commonDao.select(MAPPER_NAMESPACE + "selectBcCmLrnApoHstChkDate", cmLrnApoHst);
	}

	/**
	 * 학습 접근 이력 등록
	 * 
	 * @param BcCmLrnApoHstDto
	 * @return
	 */
	public int insertBcCmLrnApoHst(BcCmLrnApoHstDto cmLrnApoHst) {

		if (StringUtils.isEmpty(cmLrnApoHst.getApoData())) {
			throw new IllegalArgumentException("Invalid apoData in BcCmLrnApoHstDto ");
		}
		if (StringUtils.isEmpty(cmLrnApoHst.getApoRefVl())) {
			throw new IllegalArgumentException("Invalid apoRefVl in BcCmLrnApoHstDto ");
		}
		if (StringUtils.isEmpty(cmLrnApoHst.getUsrId())) {
			throw new IllegalArgumentException("Invalid usrId in BcCmLrnApoHstDto ");
		}

		return commonDao.insert(MAPPER_NAMESPACE + "insertBcCmLrnApoHst", cmLrnApoHst);
	}

	/**
	 * 학습접근이력 데이터 조회
	 * 
	 * @param BcCmLrnApoHstDto
	 * @return
	 */
	public BcCmLrnApoHstDto selectBcCmLrnApoHstData(BcCmLrnApoHstDto cmLrnApoHst) {

		if (StringUtils.isEmpty(cmLrnApoHst.getApoRefVl())) {
			throw new IllegalArgumentException("Invalid apoRefVl in BcCmLrnApoHstDto ");
		}
		if (StringUtils.isEmpty(cmLrnApoHst.getUsrId())) {
			throw new IllegalArgumentException("Invalid usrId in BcCmLrnApoHstDto ");
		}

		return commonDao.select(MAPPER_NAMESPACE + "selectBcCmLrnApoHstData", cmLrnApoHst);
	}

	/**
	 * 문서 뷰어 등록 API - streamdocs
	 * 
	 * @param String
	 * @return String
	 */
	public String callStreamDocsIdApi(String fileBucketUrl) throws JsonProcessingException {

		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Content-Type", "application/json");

		// 관리자 페이지 계정 - API 관리 계정
		String streamDocsId = endpoint_viewerApi_id;
		String streamDocsPw = endpoint_viewerApi_password;

		String streamDocsUrl = endpoint_viewerApi_url;

		String authUrl = String.format(streamDocsUrl + "v4/auth?id=%s&password=%s",
			URLEncoder.encode(streamDocsId, StandardCharsets.UTF_8),
			URLEncoder.encode(streamDocsPw, StandardCharsets.UTF_8));

		String streamDocsAuthPost = webFluxUtil.post(authUrl, httpHeaders, "", String.class);
		Map<String, Object> streamDocsAuthInfo = CoreUtil.Json.jsonString2Map(streamDocsAuthPost);

		httpHeaders.add("Cookie", "accessToken=" + streamDocsAuthInfo.get("accessToken"));

		String uploadUrl = streamDocsUrl + "v4/documents/external-resources";
		// String streamDocsBody = "{\"externalResource\":\"" + fileBucketUrl + "\"}";

		String streamDocsBody = "{\"externalResource\":\"" + fileBucketUrl + "\", \"docType\":\"\", \"name\":\"\" }";
		String streamDocsPost = webFluxUtil.post(uploadUrl, httpHeaders, streamDocsBody, String.class);

		Map<String, Object> streamDocsInfo = CoreUtil.Json.jsonString2Map(streamDocsPost);
		return (String)streamDocsInfo.get("streamdocsId");
	}

	/**
	 * 사용자 개인정보 수집 동의 상태 조회
	 * 
	 * @param usrId
	 * @return
	 */
	public BcUsrInfoDto selectUsrAgrYn(String usrId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectUsrAgrYn", usrId);
	}

	/**
	 * 사용자 개인정보 수집 동의 상태 업데이트
	 * 
	 * @param bcUsrInfoDto
	 */
	public void updateUsragrYn(BcUsrInfoDto bcUsrInfoDto) {
		commonDao.update(MAPPER_NAMESPACE + "updateUsragrYn", bcUsrInfoDto);
	}

	public boolean isTeacherExists(BcUsrInfoDto bcUsrInfoDto) {
		String dbChgTcrUsrId = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmClaGetChgTcrUsrId", bcUsrInfoDto);
		return dbChgTcrUsrId != null && !dbChgTcrUsrId.equals("N");
	}

	// 2025-02-10 초기 데이터 구성 웹전시용으로 분리 (USR_ID, LECTURE_CODE로 판별)
	@Transactional
	public int checkLectureTcrUserInfoWeb(BcUsrInfoDto bcUsrInfoDto) {
		/**
		 * log.debug("#####################"); log.debug("#####################");
		 * log.debug("####### 데이터 가공 시작!! #########");
		 * log.debug("#########"+bcUsrInfoDto+"##########");
		 * log.debug("#####################"); log.debug("#####################");
		 **/
		// int rsltCnt = 0;

		boolean noneData = false;
		if ("none".equals(bcUsrInfoDto.getLectureCd().toLowerCase())) {
			noneData = true;
			if (bcUsrInfoDto.getClassCode() == null || "".equals(bcUsrInfoDto.getClassCode())) {
				return 0;
			}
			bcUsrInfoDto.setLectureCd(bcUsrInfoDto.getClassCode());
			bcUsrInfoDto.setUsrId(bcUsrInfoDto.getUsrId().replaceAll("-", "_"));
		}

		bcUsrInfoDto.setDbId(DB_ID);
		// 기본 데이터 설정(교과서ID, 강의코드, 운영교과서ID, 학급ID), DB_ID = 학기구분코드
		//		String txbId = this.getTxbIdForSubDomain(DB_ID);
		// 설정 세팅
		/***********************************************************************
		 * 웹전시에서는 DTO의 txbId를 사용
		 **********************************************************************/
		String txbId = bcUsrInfoDto.getTxbId();
		String lectureCd = bcUsrInfoDto.getLectureCd();
		String optTxbId = DB_ID + "-" + lectureCd;
		String claId = DB_ID + "-" + lectureCd;

		bcUsrInfoDto.setTxbId(txbId);
		bcUsrInfoDto.setOptTxbId(optTxbId);
		bcUsrInfoDto.setClaId(claId);
		bcUsrInfoDto.setDbId(DB_ID);

		// @ 서버정보에서 교과서학기코드 추출
		bcUsrInfoDto.setTxbTermCd(DB_ID);
		bcUsrInfoDto.setDbId(DB_ID);

		/***********************************************************************
		 * 웹전시에서는 토큰확인 X
		 **********************************************************************/
		//		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcUsrInfoDto.setKerisUsrId(bcUsrInfoDto.getUsrId());

		// 사용자 구분
		String usrTpCd = bcUsrInfoDto.getUserType();
		if ("T".equals(usrTpCd)) {
			bcUsrInfoDto.setUsrTpCd("TE");

			/***********************************************************************
			 * 2025.01.22 선생님의 운영교과별 다중 로그인을 위해 추가 KerisService.loginAndUsrCheck 에서 적용된 아래
			 * 내용으로 인해 교사로 접근 시 세션의 usrId로 변경하여 대응 (교사 = 신규 추가된 keris_usr_id와 opt_txb_id로
			 * 조회하여 존재하지 않을 시 usr_id를 신규 생성)
			 **************************************************************************/
			bcUsrInfoDto.setUsrId(bcUsrInfoDto.getUsrId());
		} else if ("S".equals(usrTpCd)) {
			bcUsrInfoDto.setUsrTpCd("ST");
		} else if ("P".equals(usrTpCd)) {
			bcUsrInfoDto.setUsrTpCd("PA");
		}

		// CM_USR - 교사 사용자 정보 조회 및 저장
		int cmUsrCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmUser", bcUsrInfoDto);
		if (cmUsrCnt == 0) {
			commonDao.insert(MAPPER_NAMESPACE + "insertCmUsr", bcUsrInfoDto);
		}
		// 담당 교사 정보 초기화
		bcUsrInfoDto.setChgTcrUsrId(null);

		// 교사인 경우 최초 데이터 구성 시작
		if ("T".equals(usrTpCd)) {
			// 교사인 경우 담당 교사 정보 세팅
			bcUsrInfoDto.setChgTcrUsrId(bcUsrInfoDto.getUsrId());
		}
		// 책임 교사 확인
		String dbChgTcrUsrId = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmClaGetChgTcrUsrId", bcUsrInfoDto);
		// 카운트+ 책임교사 확인
		if (dbChgTcrUsrId == null) {
			commonDao.insert(MAPPER_NAMESPACE + "insertCmCla", bcUsrInfoDto);

		} else {
			if (bcUsrInfoDto.getChgTcrUsrId() != null && !"".equals(bcUsrInfoDto.getChgTcrUsrId())) {
				if (dbChgTcrUsrId.equals("N")) {
					commonDao.update(MAPPER_NAMESPACE + "updateCmClaChgTcrUsrId", bcUsrInfoDto);
				}
			}
		}

		// CM_OPT_TXB - 운영교과서 정보 조회 및 저장
		int cmOptTxbCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmOptTxb", bcUsrInfoDto);
		if (cmOptTxbCnt == 0) {
			commonDao.insert(MAPPER_NAMESPACE + "insertCmOptTxb", bcUsrInfoDto);
		}
		// 교사인 경우 최초 데이터 구성 시작
		if ("T".equals(usrTpCd)) {
			/*
			 * log.debug("#####################"); log.debug("#####################");
			 * log.debug("####### 교사 최초 데이터 구성 !! #########"); log.debug("#########" +
			 * usrTpCd + "##########"); log.debug("#####################");
			 * log.debug("#####################");
			 **/
			// CM_OPT_TCR - 교사사용자 정보 조회 및 저장
			int cmOptTxbTcrCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmOptTcr", bcUsrInfoDto);
			if (cmOptTxbTcrCnt == 0) {
				commonDao.insert(MAPPER_NAMESPACE + "insertCmOptTcr", bcUsrInfoDto);
			}
			// 책장 데이터는 처리안함
			if (!noneData) {
				// CM_USR - 교사가 담당하고 있는 학생들의 정보 조회 및 저장
				List<BcUsrInfoDto> stuList = bcUsrInfoDto.getStuList();
				if (stuList != null && stuList.size() > 0) {
					for (BcUsrInfoDto stuInfo : stuList) {
						stuInfo.setUsrId(stuInfo.getUser_id());
						int cmTcrToStuUsrCnt = commonDao.select(MAPPER_NAMESPACE + "selectCheckCmUser", stuInfo);
						if (cmTcrToStuUsrCnt == 0) {
							// 사용자 정보
							stuInfo.setKerisUsrId(stuInfo.getUsrId());
							stuInfo.setClaId(claId);
							stuInfo.setTxbId(txbId);
							stuInfo.setUsrTpCd("ST");
							stuInfo.setTxbTermCd(DB_ID);
							stuInfo.setDbId(DB_ID);
							stuInfo.setUsrNm(stuInfo.getUserName());
							stuInfo.setUserNumber(stuInfo.getUserNumber());
							commonDao.insert(MAPPER_NAMESPACE + "insertCmUsr", stuInfo);
						}
						// 학습자인 경우 모니터링 데이터 추가
						BcStuListDto stuListDto = new BcStuListDto();
						stuListDto.setUsrId(stuInfo.getUser_id());
						stuListDto.setOptTxbId(bcUsrInfoDto.getOptTxbId());
						stuListDto.setTxbId(bcUsrInfoDto.getTxbId());

						int stuCurLrnStCnt = commonDao.select(MAPPER_NAMESPACE_TCR + "selectStuCurLrnStCnt",
							stuListDto);
						if (stuCurLrnStCnt == 0) {
							stuCurLrnStCnt = commonDao.insert(MAPPER_NAMESPACE_TCR + "insertStuCurLrnSt", stuListDto);
						}

					}
				}
			}
		}

		return 1;
	}

	@Transactional
	public void insertClaCpLog(CmClaCpLogDto dto) {
		commonDao.insert(MAPPER_NAMESPACE + "insertClaCpLog", dto);
	}
	
	
	/**
	 * 평가 - 노트 > 첨부파일 ANNX_ID 조회
	 * @param String
	 * @return ResponseDto<BcCmSelectNoteFileResDto>
	 */
	public BcCmSelectNoteFileResDto selectNoteFileID(BcCmSelectNoteFileReqDto bcCmSelectNoteFileReqDto) {
	    return commonDao.select(MAPPER_NAMESPACE + "selectNoteFileID", bcCmSelectNoteFileReqDto);
	}

}