package com.aidt.api.bc.guidMnl.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuidManualPdfFile {
    @Parameter(name="PK")
    private Long guidMnlFleId;

    @Parameter(name="교과서 ID")
    @JsonProperty("textbookId")
    private Long txbId;

    @Parameter(name="메뉴얼 ID")
    @JsonProperty("guidManualId")
    private Long guidMnlId;

    @Parameter(name="문서뷰어 ID")
    private String docViId;

    @Parameter(name="파일명")
    private String fleNm;

    @Parameter(name="파일원본명")
    private String fleOrglNm;

    @Parameter(name="파일경로명")
    private String flePthNm;

    @Parameter(name="사용여부")
    private String useYn;

    @Parameter(name="버전정보")
    private int verInfo;

    @Parameter(name="생성일")
    private String crtDtm;

    @Parameter(name="수정일")
    private String mdfDtm;

    private String dbId;
    private String crtrId;
    private String mdfrId;
}
