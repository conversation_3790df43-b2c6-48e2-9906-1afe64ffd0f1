package com.aidt.api.bc.chatbot.stu;

import com.aidt.api.bc.chatbot.dto.*;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import kr.co.shineware.nlp.komoran.constant.DEFAULT_MODEL;
import kr.co.shineware.nlp.komoran.core.Komoran;
import kr.co.shineware.nlp.komoran.model.KomoranResult;
import kr.co.shineware.nlp.komoran.model.Token;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-01 14:28:18
 * @modify date 2024-06-01 14:28:18
 * @desc BcChatBotStu Service 챗봇 서비스
 */
@Slf4j
@Service
public class BcCbStuService {

    private final String MAPPER_NAMESPACE = "api.bc.chatbot.";
    
    @Autowired
    private WebFluxUtil webFluxUtil;

    @Autowired
    private CommonDao commonDao;

    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    @Value("${aidt.endpoint.archipin:}")
	private String endpoint_archipin;

    @Autowired
    private JwtProvider jwtProvider;

    @Transactional(readOnly = true)
    public Map<String, Object> callChatBotApi(BcCbDto bcChatBotDto) throws JsonMappingException, JsonProcessingException {        
        HttpHeaders httpHeaders = new HttpHeaders();
        // httpHeaders.add("Authorization", "Bearer " + accessToken);
        httpHeaders.add("Content-Type", "application/json");

    	String jsonString = new ObjectMapper().writeValueAsString(bcChatBotDto);
        String post = webFluxUtil.post(this.endpoint_archipin + bcChatBotDto.getSubUrl(), httpHeaders, jsonString, String.class);
		return CoreUtil.Json.jsonString2Map(post);
    }
    
    @Transactional(readOnly = true)
    public List<BcCbKeyWordDto> selectAiKeyWord(BcCbSrhAllDto bcChatBotDto) {      
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAiKeyWord",  bcChatBotDto);
    }

    @Transactional(readOnly = true)
    public List<BcCbKeyWordDto> selectKeyWord(BcCbSrhAllDto bcChatBotDto) {      
        return commonDao.selectList(MAPPER_NAMESPACE + "selectKeyWord",  bcChatBotDto);
    }
    
    @Transactional(readOnly = true)
    public List<BcCbWdDto> selectSnroList(BcCbSrhAllDto bcChatBotDto) {      
        return commonDao.selectList(MAPPER_NAMESPACE + "selectSnroList",  bcChatBotDto);
    }

    @Transactional(readOnly = true)
    public List<BcCbWdDto> selectDicWd(BcCbSrhAllDto bcChatBotDto) {     
        List<BcCbWdDto> data = commonDao.selectList(MAPPER_NAMESPACE + "selectDicWd",  bcChatBotDto);
        
        for(BcCbWdDto dt : data) {
        	dt.setEvFlePthNm(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dt.getEvFlePthNm()));
            dt.setWvFlePthNm(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dt.getWvFlePthNm()));
            dt.setImFlePthNm(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dt.getImFlePthNm()));
		}
        return data;
    }
    @Transactional(readOnly = true)
    public List<BcCbWdDto> selectMathWd(BcCbSrhAllDto bcChatBotDto) {     
        List<BcCbWdDto>  data = commonDao.selectList(MAPPER_NAMESPACE + "selectMathWd",  bcChatBotDto);
        
        for(BcCbWdDto dt : data) {
        	dt.setImFlePthNm(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dt.getImFlePthNm()));
		}
        
        return data;
    }

    /**
     * 챗봇 사용자 만족도 저장
     * 
     * @param saveDto 저장데이터 목록
     * @return
     */
    @Transactional
    public int saveCsatInfo(BcCbCsatDto saveDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertCsatInfo", saveDto);
    }

    /**
     * 챗봇 질문 내용 저장
     * 
     * @param saveDto 저장데이터 목록
     * @return
     */
    @Transactional
    public int saveUsrUseInfo(BcCbUsrUseDto saveDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertUsrUseInfo", saveDto);
    }

    @Transactional(readOnly = true)
    public List<BcCbUsrUseDto> selectUsrUseInfo(BcCbUsrUseDto bcChatBotDto) {      
        return commonDao.selectList(MAPPER_NAMESPACE + "selectUsrUseInfo",  bcChatBotDto);
    }
    
    @Transactional(readOnly = true)
    public BcCbUseYnDto selectCbUseYn(BcCbUseYnDto saveDto) {      
        return commonDao.select(MAPPER_NAMESPACE + "selectCbUseYn",  saveDto);
    }
	
    @Transactional
    public int updateCbUseYn(BcCbUseYnDto saveDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "updateCbUseYn", saveDto);
    }
    
    @Transactional(readOnly = true)
    public BcCbDataAllDto selectCbDataAll(BcCbSrhAllDto chatBotDto) {     
    	BcCbDataAllDto allData = new BcCbDataAllDto();
    	
       	List<BcCbWdDto> snroList = selectSnroList(chatBotDto);
    	allData.setSnroList(snroList);
    	if (!snroList.isEmpty() && !snroList.get(0).getWdNm().equals("")) {
    		String[] splitArray = snroList.get(0).getWdNm().split(",");
    		List<String> list = new ArrayList<>(Arrays.asList(splitArray));
    		chatBotDto.setCbSrhList(list);
    	}
    	
        List<BcCbWdDto> wdList = new ArrayList<>();
        
        if (chatBotDto.getSbjCd().equals("EN")) {
        	wdList = selectDicWd(chatBotDto);
        }else if (chatBotDto.getSbjCd().equals("MA")) {
        	wdList = selectMathWd(chatBotDto);
        }
        
        List<BcCbKeyWordDto> tlList = selectKeyWord(chatBotDto);
        List<BcCbKeyWordDto> aiList = selectAiKeyWord(chatBotDto);

        allData.setWdList(wdList);
        allData.setTlList(tlList);
        allData.setAiList(aiList);	
        
        return allData;
    }


    /**
     * 챗봇 형태소 분석
     * */
    public List<Token> analyzeWithDigitHandling(BcCbWdSrhDto chatBotDto) throws Exception {
        Komoran komoran = createKomoranWithUserDic();

        List<String> wordList = chatBotDto.getWdNmList();
        if (wordList == null || wordList.isEmpty()) {
            throw new IllegalArgumentException("wdNmList가 비어있습니다.");
        }

        List<Token> tokenList = restoreOriginalTokens(wordList.get(0), komoran);

        return tokenList;
    }

    private Komoran createKomoranWithUserDic() throws IOException {
        Komoran komoran = new Komoran(DEFAULT_MODEL.FULL);

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("komoran/"+userDetails.getTxbId()+"/komoran.dic");
        if (inputStream == null) {
            throw new FileNotFoundException("사전 파일을 찾을 수 없습니다: komoran/komoran.dic");
        }

        File tempDic = File.createTempFile("komoran-user", ".dic");
        tempDic.deleteOnExit();


        try (OutputStream out = new FileOutputStream(tempDic)) {
            inputStream.transferTo(out);
        }

        komoran.setUserDic(tempDic.getAbsolutePath());
        return komoran;
    }

    private List<Token> restoreOriginalTokens( String word, Komoran komoran) {
        String originalText = word;
        boolean isConverted = containsDigit(originalText);

        // 전처리 > 숫자를 한글로 변환
        String processedText = isConverted
                ? convertNumbersToKorean(originalText)
                : originalText;

        KomoranResult analyzeResultList = komoran.analyze(processedText);
        List<Token> tokenList = analyzeResultList.getTokenList();

        if(isConverted) {
            tokenList = tokenList.stream()
                    .map(token -> {
                        int beginIdx = token.getBeginIndex();
                        int endIdx = token.getEndIndex();
                        String restored = originalText.substring(beginIdx, endIdx);
                        return new Token(restored, token.getPos(), beginIdx, endIdx);
                    })
                    .collect(Collectors.toList());
        }
        return tokenList;
    }

    // 숫자 포함 여부 확인
    private boolean containsDigit(String input) {
        return input != null && input.chars().anyMatch(Character::isDigit);
    }

    // 숫자 → 한글 변환 함수
    private String convertNumbersToKorean(String input) {
        Map<Character, String> numberToKorean = Map.of(
                '0', "영",
                '1', "일",
                '2', "이",
                '3', "삼",
                '4', "사",
                '5', "오",
                '6', "육",
                '7', "칠",
                '8', "팔",
                '9', "구"
        );

        StringBuilder sb = new StringBuilder();

        for (char ch : input.toCharArray()) {
            if (Character.isDigit(ch)) {
                sb.append(numberToKorean.get(ch));
            } else {
                sb.append(ch);
            }
        }
        return sb.toString();
    }
    
    /**
     * 사용자의 학습 교과서 저자 정보 조회
     * @return 저자명, 저자코드
     */
    public Map<String, String> selectTxbAutrInfo() {
        CommonUserDetail commonUserDetail = jwtProvider.getCommonUserDetail();
        return commonDao.select(MAPPER_NAMESPACE + "selectTxbAutrInfo", commonUserDetail.getTxbId());
    }
    
    /**
     * 일상 대화 시나리오 조회
     *
     * @param srhCn 검색내용
     * @return 답변내용
     */
    public String selectDailyCnvSnro(String srhCn) {
        String decoded = URLDecoder.decode(srhCn, StandardCharsets.UTF_8);
        String escapeSrhCn = StringEscapeUtils.escapeHtml4(decoded);
        return commonDao.select(MAPPER_NAMESPACE + "selectDailyCnvSnro", escapeSrhCn);
    }

}
