package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlOneClkSetmAlTocDto {
	 /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    /** 지식맵노드ID */
    @Parameter(name="지식맵노드ID")
    private String kmmpNodId;
    /** 지식맵ID */
    @Parameter(name="지식맵ID")
    private String kmmpId;
    /** 지식맵노드명 */
    @Parameter(name="지식맵노드명")
    private String kmmpNodNm;
    /** 원본순서 */
    @Parameter(name="원본순서")
    private int orglOrdn;
    /** 사용여부 */
    @Parameter(name="사용여부")
    private String tcUseYn;
    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String tcEpsYn;
    /** 깊이 */
    @Parameter(name="깊이")
    private int dpth;
    /** 재구성순서 */
    @Parameter(name="재구성순서")
    private int rcstnOrdn;
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;
}
