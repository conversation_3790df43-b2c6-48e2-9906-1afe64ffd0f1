package com.aidt.api.al.pl.stu;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.al.pl.dto.AlPlStuDto;
import com.aidt.api.al.pl.dto.AlPlTcrDto;
import com.aidt.common.CommonDao;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/*
 * 로그인한 학생이 담당하는 담당클래스를 조회하여, AI맞춤학습 재구성 초기데이터를 작성한다.
 * */
@Slf4j
@Service
public class AlPlIniDatStuService {
	private final String MAPPER_NAMESPACE = "api.al.pl.stu.AlPlIniDat.";
	
	@Autowired
	private CommonDao commonDao;

	
	@Transactional
    public int registIniDat(AlPlStuDto cndDto) {
		log.debug("AlPlIniDatTcrService - usrId: "+cndDto.getUsrId()+ " , optTxbId: "+cndDto.getOptTxbId());
		int rtnVal = 0;
		
		//AI_지식맵노드재구성 및 교과서ID 조회
		AlPlStuDto stu = commonDao.select(MAPPER_NAMESPACE + "selectTxbId", cndDto);
		if(stu.getTxbId() == null || stu.getRowCnt() > 0) {
			log.debug("##### 이미 등록된 AI_지식맵노드재구성 데이터 = " + String.valueOf(stu.getRowCnt()));
			return rtnVal;
		}
		//AI_지식맵노드재구성 초기정보 등록
		stu.setUsrId(cndDto.getUsrId());
		int insCnt = 0;
		try {
			insCnt = commonDao.insert(MAPPER_NAMESPACE + "insertAiKmmpNodRcstn", stu);
		} catch(DuplicateKeyException dke) {
			log.error("Duplicate error (AI_지식맵노드재구성 초기정보 등록)");
			log.error(dke.getMessage());
		}

		log.debug("##### AI_지식맵노드재구성 등록건수 = " + String.valueOf(insCnt));
		
		
		List<AlPlQtmTpcProfDto> targetList = this.selectEvTarget(cndDto);
		for (AlPlQtmTpcProfDto info : targetList) this.updateLrnPrgsProf(info);
		
		return rtnVal;
	 }


	public void updateLrnPrgsProf(AlPlQtmTpcProfDto req) {
		double aiLrnPgrsRt = 0.0;
		AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
		dto.setUsrId(req.getUsrId());
		dto.setMluKmmpNodId(req.getMluKmmpNodId());
		dto.setOptTxbId(req.getOptTxbId());
		dto.setSchlGrdCd(req.getSchlGrdCd());
		dto.setSbjCd(req.getSbjCd());

		// 학습해야 할 차시 또는 토픽의 개수를 위한 리스트 선언
		List<AiRcmTsshQtmDto> lrnList = new ArrayList<>();

		// 진단평가 데이터가 있을 경우 학습진도율 10 부여
		if (AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
			lrnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEnLrnrVelTpCd", dto);
		} else if (AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
			lrnList = commonDao.selectList(MAPPER_NAMESPACE + "selectMaLuevCmplYn", dto);
		}

		if (lrnList.size() > 0) {
			aiLrnPgrsRt += 10.0;
			// 학습완료된 차시 또는 토픽 정보를 담기 위한 리스트 선언
			List<AiRcmTsshQtmDto> evCmplList;
			long evCmplYCnt = 0L;
			BigDecimal aiLrnPgrsRtNotOvDec = null;
			List<String> tpcKmmpNodIdList = new ArrayList<>();
			// 맞춤학습 학습진도율 부여
			if (AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
				long totalEvCmplYCnt = 0L;
				// 영어의 경우 전체 차시 개수를 돌면서 학습완료 단계(C2,C3)에 맞게 학습완료 여부를 조회해서 완료된 차시의 개수를 가져온다
				for (AiRcmTsshQtmDto tcDto : lrnList) {
					dto.setTcKmmpNodId(tcDto.getTcKmmpNodId());
					evCmplList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvCmplList", dto);

					//단원완료기준(초등영어도 맞춤학습2까지만 진행하는 것으로 변경되어 수정)
					String finalLuevCmplBs = "C2";
					evCmplYCnt = evCmplList.stream()
							.filter(evCmplDto -> "Y".equals(evCmplDto.getLuevCmplYn()) && finalLuevCmplBs.equals(evCmplDto.getEvDtlDvCd()))
							.count();
					totalEvCmplYCnt += evCmplYCnt;
				}
				aiLrnPgrsRtNotOvDec = BigDecimal.valueOf(totalEvCmplYCnt)
						.divide(BigDecimal.valueOf(lrnList.size()), 10, RoundingMode.HALF_UP)
						.multiply(BigDecimal.valueOf(0.9))
						.multiply(BigDecimal.valueOf(100))
						.setScale(1, RoundingMode.HALF_UP);
			} else if (AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
				for (AiRcmTsshQtmDto tpcDto : lrnList) {
					tpcKmmpNodIdList.add(tpcDto.getTpcKmmpNodId());
				}
				dto.setKmmpNodIdList(tpcKmmpNodIdList);
				evCmplList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvCmplList", dto);
				// 수학의 경우 전체 토픽 개수 대비 학습완료된 토픽의 개수를 가져온다
				evCmplYCnt = evCmplList.stream()
						.filter(evCmplDto -> "Y".equals(evCmplDto.getLuevCmplYn()))
						.count();

				aiLrnPgrsRtNotOvDec = BigDecimal.valueOf(evCmplYCnt)
						.divide(BigDecimal.valueOf(lrnList.size()), 10, RoundingMode.HALF_UP)
						.multiply(BigDecimal.valueOf(0.9))
						.multiply(BigDecimal.valueOf(100))
						.setScale(1, RoundingMode.HALF_UP);
			}

			double aiLrnPgrsRtNotOv = aiLrnPgrsRtNotOvDec.doubleValue();
			aiLrnPgrsRt += aiLrnPgrsRtNotOv;
		}
		dto.setAiLrnPgrsRt(aiLrnPgrsRt);
		dto.setDbId("DB_ID_AI");
		commonDao.update(MAPPER_NAMESPACE + "updateLrnPgrsProf", dto);
	}
	
	public List<AlPlQtmTpcProfDto> selectEvTarget(AlPlStuDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvTarget", dto);
	}
}
