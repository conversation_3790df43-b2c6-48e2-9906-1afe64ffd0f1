package com.aidt.api.al.cmt.stu;

import com.aidt.api.al.cmt.dto.req.cm.AiCmtCmRptLuReqDto;
import com.aidt.api.al.cmt.dto.req.en.*;
import com.aidt.api.al.cmt.dto.res.AiCmtEnResWrapperDto;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 10:58:14
 * @modify date 2024-05-21 10:58:14
 * @desc
 */
//@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/cmt/en/cm")
@Tag(name="[al] AI 코멘트(영어)", description="AI 코멘트(영어)")
public class AiCmtEnStuController {

    private final AiCmtStuService aiCommentStuService;

    private final AiCmtUsrDataStuService aiCmtUsrDataStuService;

    private final JwtProvider jwtProvider;

    @Operation(summary="평가/학기초")
    @PostMapping(value = "/ev/st", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnEvStComment(@Valid @RequestBody AiCmtEnEvStReqDto reqDto) {
         return Response.ok(aiCmtUsrDataStuService.getOrInsertWrapper(reqDto.getEvId(), reqDto.getUsrId() ,reqDto, aiCommentStuService::selectEnEv ));
    }

    @Operation(summary="평가/단원평가")
    @PostMapping(value = "/ev/ug", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnEvUgComment(@Valid @RequestBody AiCmtEnEvUgReqDto reqDto) {
            return Response.ok(aiCmtUsrDataStuService.getOrInsertWrapper(reqDto.getEvId(), reqDto.getUsrId(), reqDto, aiCommentStuService::selectEnEv ));
    }

    @Operation(summary="평가/학기말총괄")
    @PostMapping(value = "/ev/et", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnEvEtComment(@Valid @RequestBody AiCmtEnEvEtReqDto reqDto) {
        return Response.ok(aiCmtUsrDataStuService.getOrInsertWrapper(reqDto.getEvId(), reqDto.getUsrId(), reqDto, aiCommentStuService::selectEnEv ));
    }

    @Operation(summary="AI맞춤학습/AI진단리포트")
    @PostMapping(value = "/ai/dgn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnAiDgnRptComment(@Valid @RequestBody AiCmtEnAiDgnRptReqDto reqDto) {
        return Response.ok(aiCmtUsrDataStuService.getOrInsertWrapper(reqDto.getEvId(), reqDto.getUsrId(), reqDto, aiCommentStuService::selectEnAi ));
    }

    @Operation(summary="AI맞춤학습/AI학습리포트")
    @PostMapping(value = "/ai/lrn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnAiLrnRptComment(@Valid @RequestBody AiCmtEnAiLrnRptReqDto reqDto) {

        return Response.ok(aiCommentStuService.selectEnAi(reqDto));
    }

    @Operation(summary="학습리포트/단원별분석")
    @PostMapping(value = "/rpt/lu", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getEnRptLuComment(@Valid @RequestBody AiCmtCmRptLuReqDto reqDto) {

        return Response.ok(aiCommentStuService.selectEnRpt(reqDto));
    }

    //    @Operation(summary="평가/차시평가")
//    @PostMapping(value = "/ev/to", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<List<AiCmtResDto>> getEnEvToComment(@Valid @RequestBody AiCmtEnEvToReqDto reqDto) {
//        return Response.ok(aiCmtUsrDataStuService.getOrInsertList(reqDto.getEvId(), reqDto.getUsrId(), reqDto, aiCommentStuService::selectEnEv ));
//    }
}
