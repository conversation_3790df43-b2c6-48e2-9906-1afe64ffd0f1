package com.aidt.api.ea.evcom.diy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 08:24:오전 8:24
 * @modify date 2024-03-29 08:24:오전 8:24
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaDiyEvReqDto {

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="평가 완료 여부")
    private String evCmplYn;

    @Parameter(name="평가 ID")
    private long evId;

	private int pageNo;
	private int pageSize;
}
