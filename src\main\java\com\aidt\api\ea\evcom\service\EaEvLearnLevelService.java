package com.aidt.api.ea.evcom.service;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.api.common.helper.JwtHelper;
import com.aidt.api.ea.evcom.adapter.EaEvLearnLevelAdapter;
import com.aidt.api.ea.evcom.dto.EaEvLearnLevelDto;
import com.aidt.common.CommonUserDetail;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service
@RequiredArgsConstructor
public class EaEvLearnLevelService {

	private final EaEvLearnLevelAdapter eaEvLearnLevelAdapter;
	private final JwtHelper jwtHelper;
	@Value("${server.meta.textbook.systemCode}")
	private String dbId;

	public void saveAllLearnLevels() {
		saveAllLearnLevels(jwtHelper.getCommonUserDetail());
	}

	public void saveAllLearnLevels(CommonUserDetail commonUserDetail) {

		if (ObjectUtils.isEmpty(commonUserDetail)) {
			log.error("사용자 정보를 알 수 없어 학습 수준 업데이트를 실행할 수 없습니다.");
			throw new BizException("학습 수준 업데이트 실패");
		}
		saveLearnerLevel(commonUserDetail.getUsrId());
		saveLearnUnitLevels(commonUserDetail);
	}

	public void saveLearnerLevel(String usrId) {
		eaEvLearnLevelAdapter.saveLearnerLevel(usrId);
	}

	private void saveLearnUnitLevels(CommonUserDetail userDetail) {
		var learnLevel = new EaEvLearnLevelDto(userDetail, dbId);
		var learnUnitLevels = getLearnUnitLevels(learnLevel);

		//단원의 레벨 정보가 없으면 저장하지 않는다.
		if (CollectionUtils.isEmpty(learnUnitLevels)) {
			return;
		}

		learnLevel.addLearnUnits(learnUnitLevels);
		eaEvLearnLevelAdapter.upsertLearnUnitLevels(learnLevel);
	}

	private List<EaEvLearnLevelDto.LearnUnit> getLearnUnitLevels(EaEvLearnLevelDto learnLevel) {
		return eaEvLearnLevelAdapter.getLearnUnitLevels(learnLevel);

	}

}