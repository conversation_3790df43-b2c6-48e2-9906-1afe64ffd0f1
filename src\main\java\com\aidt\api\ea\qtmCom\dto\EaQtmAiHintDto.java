package com.aidt.api.ea.qtmCom.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 13:58:오후 1:58
 * @modify date 2024-02-15 13:58:오후 1:58
 * @desc   평가 공통 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaQtmAiHintDto {
    //문항 테이블
    @Parameter(name="문항 ID")
    private String qpQtmId;    
    
    @Parameter(name="문항 ID")
    private String qpHntId;    
    
    @Parameter(name="문항 순번")
    private int qpHntOrdn;
    
    @Parameter(name="힌트형태코드")
    private String qpHntTypCd;
    @Parameter(name="힌트형태명")
    private String qpHntTypNm;
    
    @Parameter(name="힌트콘텐츠내용")
    private String qpHntCtn;    

}
