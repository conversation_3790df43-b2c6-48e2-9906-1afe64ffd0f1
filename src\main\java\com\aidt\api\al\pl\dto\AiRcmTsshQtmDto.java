package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI 맞춤 문항추천 서비스
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiRcmTsshQtmDto {

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="문항추천 우선순위")
	private Integer rcmRnk;

	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="교과서ID")
	private String txbId;
	
	@Parameter(name="대단원 지식맵 노드ID")
	private String lluKmmpNodId;
	private String lluKmmpNodNm;

	@Parameter(name="중단원 지식맵 노드ID")
	private String mluKmmpNodId;
	private String mluKmmpNodNm;

	@Parameter(name="소단원 지식맵 노드ID")
	private String sluKmmpNodId;
	private String sluKmmpNodNm;

	@Parameter(name="차시 지식맵 노드ID")
	private String tcKmmpNodId;
	private String tcKmmpNodNm;

	@Parameter(name="토픽 지식맵 노드ID")
	private String tpcKmmpNodId;
	private String tpcKmmpNodNm;
	private Integer tpcKmmpNodCnt;
	
	@Parameter(name="연결토픽지식맵노드ID")
	private String lnkgTpcKmmpNodId;

	@Parameter(name="학습맵 노드ID")
	private String lrmpNodId;

	@Parameter(name="대단원 학습맵 노드ID")
	private String lluLrmpNodId;
	private String lluLrmpNodNm;

	@Parameter(name="중단원 학습맵 노드ID")
	private String mluLrmpNodId;

	@Parameter(name="소단원 학습맵 노드ID")
	private String sluLrmpNodId;

	@Parameter(name="차시 학습맵 노드ID")
	private String tcLrmpNodId;

	@Parameter(name="토픽별 AI예측점수")
	private Double aiPredAvgCansRt;

	@Parameter(name="토픽별 실제점수")
	private Double aiPredAvgScr;

	@Parameter(name="토픽별 예측/실제 gap")
	private Double tpcPredAtlGap;

	@Parameter(name="문항ID")
	private Integer qtmId;

	@Parameter(name="컨텐츠난이도구분코드")
	private String ctnDffdDvCd;

	@Parameter(name="컨텐츠난이도구분코드이름")
	private String ctnDffdDvNm;

	@Parameter(name="컨텐츠유형코드")
	private String ctnTpCd;
	
	@Parameter(name="컨텐츠유형코드이름")
	private String ctnTpNm;
	
	@Parameter(name="문항별 정답여부")
	private String cansYn;
	
	@Parameter(name="정답수")
	private Integer cansCnt;
	
	@Parameter(name="문항별 AI예측정답률")
	private Double aiPredCansRt;

	@Parameter(name="문항별 풀이횟수")
	private Integer txmPn;
	
	@Parameter(name="문항순서")
	private Integer qtmOrdn;
	
	@Parameter(name="문항수")
	private Integer qtmCnt;
	private Integer cansYCnt;
	private Integer qtmAnwCnt;
	private Integer evCmplYnCnt;

	
	@Parameter(name="형성평가 하 문항수")
	private int frLlCnt;
	@Parameter(name="형성평가 하 정답 문항수")
	private int frLlYCnt;
	
	@Parameter(name="형성평가 중 문항수")
	private int frMmCnt;
	@Parameter(name="형성평가 중 정답 문항수")
	private int frMmYCnt;
	
	@Parameter(name="총괄평가 중 문항수")
	private int ugMmCnt;
	@Parameter(name="총괄평가 중 정답 문항수")
	private int ugMmYCnt;
	
	
	@Parameter(name="토픽숙련도(취약:01, 보통:02, 완벽:03)")
	private Double tpcAvn;
	
	@Parameter(name="차시숙련도")
	private Double tcAvn;

	@Parameter(name="사용자 학습수준 (빠른:FS, 보통:NM, 느린:SL)")
	private String lrnrVelTpCd;

	@Parameter(name="평가ID")
	private Integer evId;
	
	@Parameter(name="평가완료여부")
	private String evCmplYn;
	
	@Parameter(name="토픽완료여부")
	private String tpcCmplYn;

	@Parameter(name="평가구분코드 SE:교과학습평가, TE:교사평가, DE:DIY평가, AE:AI평가")
	private String evDvCd;

	@Parameter(name="평가상세구분코드 UD:단원진단, C1:선택학습1, C2:선택학습2,ST:학기초진단, TO:차시평가, UG:단원총괄, ET:학기말평가")
	private String evDtlDvCd;
	
	@Parameter(name="평가상세코드리스트")
    private List<String> evDtlDvCdList;
	
	@Parameter(name="데이터베이스ID")
    private String dbId;
	
	@Parameter(name="과목")
	private String sbjCd;
	
	@Parameter(name="학교급코드")
	private String schlGrdCd;
	
	@Parameter(name="학년코드")
	private Integer sgyCd;
	
	@Parameter(name="저자코드")
	private String autrCd;
	
	@Parameter(name="저자명")
	private String autrNm;
	
	@Parameter(name="현재차시(토픽) 평균정답률")
	private float sbcLrnAvgAnsRt;
	
	@Parameter(name="이전차시(토픽) 평균정답률")
	private float preLrnAvgAnsRt;
	
	@Parameter(name="지식맵 노드ID")
	private String kmmpNodId;
	
	@Parameter(name="AI학습활동ID")
	private long aiLrnAtvId;
	private String aiLrnAtvNm;
	
	@Parameter(name="CDN경로명")
	private String cdnPthNm;
	
	@Parameter(name="CDN경로명-썸네일")
	private String cdnPthNmThumnail;
	
	@Parameter(name="토픽상태 BF:이전토픽, TPC:원본토픽")
	private String tpcStat;
	
	@Parameter(name="학습상태코드")
	private String lrnStCd;
	
	@Parameter(name="학습활동명")
	private String lrnAtvNm;
	
	@Parameter(name="연관문항유형코드")
	private String rltQtmTpCd;
	
	@Parameter(name="차시노출여부")
	private String tcEpsYn;
	
	@Parameter(name="차시사용여부")
	private String tcUseYn;
	
	@Parameter(name="재구성순서")
	private int rcstnOrdn;
	
	@Parameter(name="원본순서")
	private int orglOrdn;
	
	@Parameter(name="삭제여부")
	private String delYn;
	
	@Parameter(name="평균정답률")
	private Double avgCansRt;
	
	@Parameter(name="차시평균정답률")
	private Double tcAvgCansRt;
	private String tcAvgCansRtStr;
	
	@Parameter(name="평균정답률 - 정수표현")
	private Integer cansRt;
	
	@Parameter(name="학습일자")
	private String mdfDtm;
	
	@Parameter(name="학습일자 순서")
	private String mdfDtmOrder;
	
	@Parameter(name="단원평가완료여부")
	private String luevCmplYn;
	
	@Parameter(name="학습순서")
	private int lrnOrdn;
	
	@Parameter(name="토픽숙련도(문자)")
	private String tpcAvnStr;
	
	@Parameter(name = "썸네일이미지주소")
	private String luImgPth;
	
	@Parameter(name = "다음토픽ID")
	private String nextTpcKmmpNodId;
	
	@Parameter(name = "다음토픽명")
	private String nextTpcKmmpNodNm;
	
	@Parameter(name = "전체학습토픽수")
	private Integer lrnwTpcCnt;
	@Parameter(name = "학습한토픽수")
	private Integer lrnwCmplTpcCnt;
	
	@Parameter(name = "완료학습수")
	private Integer cmplLrnCnt;
	
	@Parameter(name = "진단평가문항수")
	private String ovQtmCnt;
	
	@Parameter(name = "학습여부")
	private String lrnYn;
	
	@Parameter(name="진단평가 토픽숙련도")
	private Double aiDgnEvTpcAvn;
	
	@Parameter(name = "AI진단평가예측평균정답률")
	private Double aiDgnEvPredAvgCansRt;
	
	@Parameter(name = "평가지ID")
	private Integer  evshId;
	@Parameter(name = "평가지코드")
	private String evshCd;
	@Parameter(name = "평가지구분코드")
	private String evshDvCd;

	
	private String totalQtmCount;
	private String cansYnCount;
	private String xplTmScnt;

	@Parameter(name = "개념영상 학습자수준코드 플래그")
	private String lrnrVelTpCdFlag;
	
	private List<String> allRpcPgrsRtList;

	@Parameter(name="심화토픽여부")
	private String ntnTpcYn;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;

	private List<String> kmmpNodIdList;

	private double aiLrnPgrsRt;
}
