package com.aidt.api.ea.evcom.diy.dto;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 10:06:오전 10:06
 * @modify date 2024-03-29 10:06:오전 10:06
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class UpdateDiyEvNmDto {
    @Parameter(name="평가명")
    @NotNull
    private String evNm;

    @Parameter(name="평가 ID")
    @NotNull
    private long evId;

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="DB ID")
    private String dbId;

}
