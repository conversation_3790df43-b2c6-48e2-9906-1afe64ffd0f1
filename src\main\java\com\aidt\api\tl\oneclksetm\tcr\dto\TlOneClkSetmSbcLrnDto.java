package com.aidt.api.tl.oneclksetm.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-19 14:41:54
 * @modify date 2024-02-19 14:41:54
 * @desc [TlOneClkSetmSbcLrnDto 원클릭학습설정 교과학습 재구성 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlOneClkSetmSbcLrnDto {
    /** 원클릭학습설정 학습목차 저장 리스트 */
    @Parameter(name="원클릭학습설정 학습목차 저장 리스트")
    private List<TlOneClkSetmTocDto> tocList;

    /** 원클릭학습설정 학습활동 저장 리스트 */
    @Parameter(name="원클릭학습설정 학습활동 저장 리스트")
    private List<TlOneClkSetmAtvDto> atvList;
    
    /** 원클릭학습설정 교사 추가 콘텐츠 저장 리스트 */
    @Parameter(name="원클릭학습설정 교사 추가 콘텐츠 저장 리스트")
    private List<TlOneClkSetmAtvDto> ctnList;
    
    /** 원클릭학습설정 과제 수정 리스트 */
    @Parameter(name="원클릭학습설정 과제 수정 리스트")
    private List<TlOneClkSetmTocDto> ansList;
    
    /** 원클릭학습설정 ai과제 수정 리스트 */
    @Parameter(name="원클릭학습설정 ai과제 수정 리스트")
    private List<TlOneClkSetmTocDto> aiAnsList;
    
    /** 원클릭학습설정 수업자료 리스트 */
    @Parameter(name="원클릭학습설정 수업자료 리스트")
    private List<TlOneClkSetmRegMtrlDto> mtrlList;

    /** 원클릭학습설정 다른 학급 리스트 */
    @Parameter(name="원클릭학습설정 다른 학급 리스트")
    private List<TlOneClkSetmClaDto> claList;
    
    /** 원클릭학습설정 타교과서 */
    @Parameter(name="원클릭학습설정 타교과서")
    private int extcmpTxb;
}
