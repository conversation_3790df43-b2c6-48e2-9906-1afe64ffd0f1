package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-19
 * @modify date 2024-06-19
 * @desc 학습 리포트 - ai 추천학습 중단원
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptAlPlMluInfoDto {
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="중단원 지식맵 ID")
	private String mKmmpNodId;
	
	@Parameter(name="중단원 지식맵 NM")
	private String mKmmpNodNm;
	
	@Parameter(name="차시 사용 여부")
	private String tcUseYn;
	
	@Parameter(name="재구성 순서")
	private String rcstnOrdn;
	
	@Parameter(name="원래 순서")
	private String orglOrdn;
	
	@Parameter(name="평가 id")
	private String evId;
	
	@Parameter(name="평가 풀이 시간")
	private String evTmScnt;
	
	@Parameter(name="상세구분코드")	//OV, C1, C2
	private String evDtlEvCd;
	
	@Parameter(name="평가 완료 여부")
	private String evCmplYn;
	
	@Parameter(name="마지막 수정일")
	private String evRsMdfDtm;
	
	@Parameter(name="진단평가 이미 알고 있는 토픽 개수")	//토픽 점수가 0.8점 초과
	private String tpcOvPass;
	
	@Parameter(name="진단평가 학습이 더 필요한 토픽 개수")	//토픽 점수가 0.8점 미만
	private String tpcOvWeak;
	
	@Parameter(name="맞춤학습1 이미 알고 있는 토픽 개수")	//토픽 점수가 0.8점 초과
	private String tpcC1Pass;
	
	@Parameter(name="맞춤학습1 학습이 더 필요한 토픽 개수")	//토픽 점수가 0.8점 미만
	private String tpcC1Weak;
	
	@Parameter(name="맞춤학습2 이미 알고 있는 토픽 개수")	//토픽 점수가 0.8점 초과
	private String tpcC2Pass;
	
	@Parameter(name="맞춤학습2 학습이 더 필요한 토픽 개수")	//토픽 점수가 0.8점 미만
	private String tpcC2Weak;
	
//	@Parameter(name="도전학습 이미 알고 있는 토픽 개수")	//토픽 점수가 0.8점 초과
//	private String tpcC3Pass;
	
//	@Parameter(name="도전학습 학습이 더 필요한 토픽 개수")	//토픽 점수가 0.8점 미만
//	private String tpcC3Weak;
	
	@Parameter(name="평가 정보")
	private List<EaLrnRptAlPlEaInfoDto> eaInfoList;
	
	@Parameter(name="토픽 정보")
	private List<EaLrnRptAlPlTpcInfoDto> tpcInfoList;
	
	@Parameter(name="차시 정보") //영어서 사용
	private EaLrnRptAlPlTcInfoDto tcInfoData;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
}
