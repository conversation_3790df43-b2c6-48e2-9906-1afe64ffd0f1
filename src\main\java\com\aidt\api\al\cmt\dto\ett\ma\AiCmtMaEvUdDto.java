package com.aidt.api.al.cmt.dto.ett.ma;

import com.aidt.api.al.cmt.dto.ett.cm.N02Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N05Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtMaEvUdDto {

    private N02Dto n02;

    private N05Dto n05;

    private Boolean existAiRcmCtn;

    @Builder.Default
    private String usrTpCd = "ST";

}
