package com.aidt.api.bc.wdlst.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 단어장 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcWdLstDto{

	@Parameter(name="나의단어장ID")
	private int myWdId;

	@Parameter(name="유저ID")
	private String usrId;

	@Parameter(name="영어단어ID")
	private int enWdId;

	@Parameter(name="사전단어ID")
	private int dicWdId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="학교급코드")
	private String schlGrdCd;

	@Parameter(name="영문단어명")
	private String wdNm;

	@Parameter(name="학년코드")
	private String sgyCd;

	@Parameter(name="품사종류코드")
	private String wdclKnCd;

	@Parameter(name="단어의미내용")
	private String wdMeanCn;

	@Parameter(name="예문내용")
	private String exsnCn;

	@Parameter(name="예문해석내용")
	private String exsnIntpCn;

	@Parameter(name="대표품사여부")
	private String rprsWdclYn;

	@Parameter(name="파일명")
	private String fleNm;

	@Parameter(name="정렬순서")
	private String srtOrdn;

	@Parameter(name="원본파일경로명")
	private String orglFlePthNm;

	@Parameter(name="파일경로명")
	private String flePthNm;

	@Parameter(name="파일유형코드")
	private String exsnFleTpCd;

	@Parameter(name="알파벳여부")
	private Boolean alphabetFlag;
	
	@Parameter(name="검색내용")
	private String scWord;

	@Parameter(name="정렬옵션")
	private String scOption;

	@Parameter(name="차순구분")
	private String scOrder;

	@Parameter(name="생성일자")
	private String fnCrtDtm;

	// 수정
	@Parameter(name="오디오파일경로")
	private String auFlePth;

	@Parameter(name="이미지파일경로")
	private String imFlePth;

	@Parameter(name="예문파일경로")
	private String meFlePth;

	@Parameter(name="대표품사 외 품사")
	private String wdclKnCdN;

	@Parameter(name="대표품사 외 설명")
	private String wdMeanCnN;

	@Parameter(name="오디오파일여부")
	private String isListening;

	@Parameter(name="교과서")
    private String txbId;
	
	// input(검색영역)
	private String srhWdTxt;

	@Parameter(name="나의단어장ID List")
	private int[] myWdIds;

	// 단어정렬필드
	private String srhSrt;

	// 생성자ID
	private String crtrId;

	// 생성일시
	private String crtDtm;

	// 수정자ID
	private String mdfrId;

	// 수정일시
	private String mdfDtm;

	// 데이터베이스ID
	private String dbId;

	private int pageNo;
	private int pageSize;
	private int totalCnt;
	private int startPageNo;
	private int dataSize;

}
