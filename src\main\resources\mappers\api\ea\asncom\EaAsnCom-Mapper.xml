<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.asncom">


	<!-- 과제 상세 조회 -->
	<select id="selectAsnDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaAsnCom-Mapper.xml - selectAsnDetail */
		SELECT EA.ASN_ID			-- 과제ID
			  ,EA.OPT_TXB_ID 		-- 운영교과서ID
			  ,EA.TCR_USR_ID 		-- 교사사용자ID
			  ,EA.ASN_NM 			-- 과제명
			  ,EA.ASN_CN 			-- 과제설명
			  ,EA.ASN_TP_CD 		-- 과제유형코드
			  ,EA.LRN_TP_CD 		-- 학습유형코드
			  ,CASE
	   	  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL THEN EA.ASN_TP_CD
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL THEN EA.LRN_TP_CD
			   END AS ASN_LRN_TP	-- 유형코드
			  ,CASE
		  	   	  WHEN EA.ASN_TP_CD IS NOT NULL AND EA.LRN_TP_CD IS NULL
		  	 	      THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.ASN_TP_CD AND CM.URNK_CM_CD ='ASN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
		   		  WHEN EA.LRN_TP_CD IS NOT NULL AND EA.ASN_TP_CD IS NULL
		   			  THEN (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD CM WHERE CM.CM_CD = EA.LRN_TP_CD AND CM.URNK_CM_CD ='LRN_TP_CD' AND CM.LMS_USE_YN ='Y' AND CM.DEL_YN='N')
			   END AS ASN_LRN_TP_NM	-- 유형코드이름
			  ,EA.ASN_PTME_DV_CD 	-- 과제기간구분코드
			  ,EA.STR_DTM 			-- 시작일시
			   ,CONCAT(DATE_FORMAT(EA.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.STR_DTM,'%h:%i')) AS STR_DTM_NM
			  ,EA.END_DTM 			-- 종료일시
			  ,CONCAT(DATE_FORMAT(EA.END_DTM,'%m. %d. '),IF(TIME_FORMAT(EA.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(EA.END_DTM,'%h:%i')) AS END_DTM_NM
			  ,CASE WHEN EA.ASN_PTME_DV_CD = 'PT' THEN DATEDIFF(EA.END_DTM, CURDATE())
				    ELSE ''
				END AS D_DAY
			  ,EA.FIN_AF_SMT_ABLE_YN -- 마감이후제출가능여부
			  ,EA.EV_MTHD_TP_CD 	-- 평가방식유형코드
		  	  ,EA.PSC_SCR 			-- 만점점수
		  	  ,EA.ANNX_ID AS TCR_ANNX_ID		-- 교사첨부ID
			  ,EA.DEL_YN 			-- 삭제여부
			  ,EA.CRTR_ID 			-- 생성자ID
			  ,DATE_FORMAT(EA.CRT_DTM ,'%m. %d.') AS CRT_DTM
			  -- 과제
			  ,EAS.STU_USR_ID 		-- 학생사용자ID
			  ,EAS.SMT_DTM 			-- 제출일시
			  ,EAS.MDF_DTM
			  ,CASE
		          WHEN EAS.MDF_DTM > EAS.SMT_DTM THEN
		              CONCAT(DATE_FORMAT(EAS.MDF_DTM, '%m. %d. '), 
		                     IF(TIME_FORMAT(EAS.MDF_DTM, '%p') = 'AM', '오전 ', '오후 '), 
		                     DATE_FORMAT(EAS.MDF_DTM, '%h:%i'))
		          ELSE
		              CONCAT(DATE_FORMAT(EAS.SMT_DTM, '%m. %d. '), 
		                     IF(TIME_FORMAT(EAS.SMT_DTM, '%p') = 'AM', '오전 ', '오후 '), 
		                     DATE_FORMAT(EAS.SMT_DTM, '%h:%i'))
		      END AS SMT_DTM_NM
			  ,EAS.SMT_CN 			-- 제출내용
			  ,EAS.ANNX_ID AS STU_ANNX_ID		-- 학생첨부ID
			  ,EAS.SMT_CMPL_YN 		-- 제출완료여부
			  ,EAS.SCR 				-- 점수
			  ,CASE
				  WHEN EA.EV_MTHD_TP_CD ='SC' AND EAS.SCR IS NOT NULL THEN 'Y'
				  ELSE 'N'
			   END  AS SCR_YN
			  ,EAS.FDBK_CN 			-- 피드백내용
			  -- 과제범위
			  ,EAR.SP_LRN_ID		-- 특별학습ID
			  ,EAR.LRN_STP_DV_CD	-- 학습단계구분코드
			  ,EAR.LU_NOD_ID		-- 단원노드ID
			  ,EAR.TC_NOD_ID		-- 차시노드ID
			  ,(SELECT TSL.EV_ID 
		          FROM TL_SBC_LRN_ATV_RCSTN TSL
		         INNER JOIN LMS_CMS.BC_LRN_STP S  
		            ON TSL.LRMP_NOD_ID  = S.LRMP_NOD_ID
	               AND TSL.LRN_STP_ID = S.LRN_STP_ID 
	               AND S.LRN_STP_DV_CD = 'EX'
	             WHERE EAR.TC_NOD_ID = TSL.LRMP_NOD_ID 
	               AND EA.OPT_TXB_ID = TSL.OPT_TXB_ID LIMIT 1) AS EV_ID
	    	  ,CASE
					WHEN EA.LRN_TP_CD = 'TL' OR EA.ASN_TP_CD = 'GE' OR EA.ASN_TP_CD = 'GR' 
						THEN (SELECT TSLNR.LRMP_NOD_NM FROM TL_SBC_LRN_NOD_RCSTN TSLNR WHERE TSLNR.OPT_TXB_ID = EA.OPT_TXB_ID AND TSLNR.LRMP_NOD_ID= EAR.LU_NOD_ID )
					WHEN EA.LRN_TP_CD = 'AL' 
						THEN (SELECT AKNR.KMMP_NOD_NM FROM AI_KMMP_NOD_RCSTN AKNR WHERE AKNR.OPT_TXB_ID=EA.OPT_TXB_ID AND AKNR.KMMP_NOD_ID = EAR.LU_NOD_ID )
				 END NOD_NM 			-- 단원명
			  ,(SELECT LRMP_NOD_NM FROM TL_SBC_LRN_NOD_RCSTN  WHERE OPT_TXB_ID=EA.OPT_TXB_ID AND LRMP_NOD_ID = EAR.TC_NOD_ID ) AS TC_NM  -- 차시명
			  ,(SELECT RCSTN_NO  FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE EAR.OPT_TXB_ID = OPT_TXB_ID AND EAR.LU_NOD_ID = LRMP_NOD_ID) AS NOD_NO -- 단원넘버링
			  ,(SELECT RCSTN_NO FROM TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID=EA.OPT_TXB_ID AND LRMP_NOD_ID = EAR.TC_NOD_ID ) AS TC_NO	-- 차시넘버링
			  ,(SELECT TSL.LRN_ATV_ID 
		          FROM TL_SBC_LRN_ATV_RCSTN TSL
		         INNER JOIN LMS_CMS.BC_LRN_STP S  
		            ON TSL.LRMP_NOD_ID  = S.LRMP_NOD_ID
	               AND TSL.LRN_STP_ID = S.LRN_STP_ID 
	               AND S.LRN_STP_DV_CD = 'EX'
	             WHERE EAR.TC_NOD_ID = TSL.LRMP_NOD_ID 
	               AND EA.OPT_TXB_ID = TSL.OPT_TXB_ID LIMIT 1) AS LRN_ATV_ID
	     FROM LMS_LRM.EA_ASN_SMT EAS -- EA_과제제출
	    INNER JOIN LMS_LRM.EA_ASN EA -- EA_과제
	       ON EAS.ASN_ID = EA.ASN_ID
	      AND EA.OPT_TXB_ID = #{optTxbId}
	      AND EA.DEL_YN = 'N'
	      AND EA.USE_YN = 'Y'
	      AND EA.LCKN_YN = 'N'
	     LEFT JOIN LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
	       ON EA.ASN_ID = EAR.ASN_ID
	      AND EAR.DEL_YN = 'N'
	    WHERE EAS.STU_USR_ID = #{stuUsrId}
	      AND EAS.ASN_ID = #{asnId}
	</select>



	<!-- 모둠 과제 상세 조회 -->
	<select id="selectAsnGrpStuDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaAsnCom-Mapper.xml - selectAsnGrpStuDetail */
		SELECT
			B.STU_USR_ID															AS stuUsrId
			, A.ASN_ID 																AS asnId
			, A.ASN_NM 															AS asnNm
			, A.ASN_PTME_DV_CD 												AS asnPtmeDvCd
			, A.TCR_USR_ID
			, CONCAT(DATE_FORMAT(A.STR_DTM,'%m. %d. '),IF(TIME_FORMAT(A.STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(A.STR_DTM,'%h:%i')) 		AS strDtmNm
			, CONCAT(DATE_FORMAT(A.END_DTM,'%m. %d. '),IF(TIME_FORMAT(A.END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(A.END_DTM,'%h:%i')) 	AS endDtmNm
			, A.ASN_CN 															AS asnCn
			, A.EV_MTHD_TP_CD 												AS evMthdTpCd
			, A.PSC_SCR 															AS pscScr
			, A.STR_DTM
			, A.END_DTM
			, B.SMT_DTM 															AS smtDtm
			, B.SMT_CN 															AS smtCn
			, B.SMT_CMPL_YN 													AS smtCmplYn
			, B.SMT_DTM
			, B.MDF_DTM
			,CASE
		          WHEN B.MDF_DTM > B.SMT_DTM THEN
		              CONCAT(DATE_FORMAT(B.MDF_DTM, '%m. %d. '), 
		                     IF(TIME_FORMAT(B.MDF_DTM, '%p') = 'AM', '오전 ', '오후 '), 
		                     DATE_FORMAT(B.MDF_DTM, '%h:%i'))
		          ELSE
		              CONCAT(DATE_FORMAT(B.SMT_DTM, '%m. %d. '), 
		                     IF(TIME_FORMAT(B.SMT_DTM, '%p') = 'AM', '오전 ', '오후 '), 
		                     DATE_FORMAT(B.SMT_DTM, '%h:%i'))
		     END AS SMT_DTM_NM
			, B.SCR 																	AS scr
			,CASE
				  WHEN A.EV_MTHD_TP_CD ='SC' AND B.SCR IS NOT NULL THEN 'Y'
				  ELSE 'N'
			  END  AS SCR_YN
			, B.FDBK_CN 															AS fdbkCn
			, DATEDIFF(A.END_DTM, A.STR_DTM) 							AS dateDiffStartEnd
			, DATEDIFF(A.END_DTM, NOW()) 								AS dateDiffEndNow
			, DATE_FORMAT(A.CRT_DTM,'%m. %d.') 					AS crtDtm
			, A.ANNX_ID												AS annxId
			, CFUS.MYHM_PNT_USE_YN
			, EAR.LU_NOD_ID		-- 단원노드ID
			, EAR.TC_NOD_ID		-- 차시노드ID
		--	, CONCAT(TSLNR.LRMP_NOD_NM ,' > ',TS.LRMP_NOD_NM) NOD_NM
		    , TSLNR.LRMP_NOD_NM AS NOD_NM
		    , TS.LRMP_NOD_NM AS TC_NM  -- 차시명
			, TSLNR.RCSTN_NO AS NOD_NO			-- 단원넘버링
			, TS.RCSTN_NO AS TC_NO				-- 차시넘버링
		FROM
			LMS_LRM.EA_ASN A
		LEFT JOIN
			LMS_LRM.EA_ASN_SMT B
		  ON
			A.ASN_ID = B.ASN_ID
		LEFT JOIN
	    	LMS_LRM.CM_FNC_USE_SETM CFUS
		  ON
		  	A.OPT_TXB_ID = CFUS.OPT_TXB_ID
		LEFT JOIN 
			LMS_LRM.EA_ASN_RNGE EAR	-- EA_과제범위
	      ON 
	      	A.ASN_ID = EAR.ASN_ID
	     AND 
	     	EAR.DEL_YN ='N'
		LEFT JOIN 
	    	LMS_LRM.TL_SBC_LRN_NOD_RCSTN TSLNR	-- TL_교과학습노드재구성
		  ON 
		   	A.OPT_TXB_ID = TSLNR.OPT_TXB_ID
		 AND 
		   	EAR.LU_NOD_ID = TSLNR.LRMP_NOD_ID
		 AND 
		   	TSLNR.USE_YN ='Y'
		 AND 
		 	IFNULL(TSLNR.URNK_LRMP_NOD_ID, '') = ''
	    LEFT JOIN 
	    	LMS_LRM.TL_SBC_LRN_NOD_RCSTN TS
	      ON 
	      	A.OPT_TXB_ID = TS.OPT_TXB_ID
	     AND 
	     	EAR.TC_NOD_ID = TS.LRMP_NOD_ID
		WHERE
			 	A.DEL_YN = 'N'
		  AND 
		      	A.USE_YN = 'Y'
		  AND 
		  		A.LCKN_YN = 'N'
		  AND	
				B.ASN_ID = #{asnId}
		  AND
				B.STU_USR_ID = #{stuUsrId}
	      AND A.OPT_TXB_ID = #{optTxbId}
	</select>

	<!-- 모둠 과제 상세 조회 (모둠 구성원 조회) -->
	<select id="selectGrpDetail" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/* EaAsnCom-Mapper.xml - selectGrpDetail */
		SELECT
			T2.GRP_ID					AS grpId
			, T2.GRP_TEM_ID			AS grpTemId
			, T3.STU_USR_ID			AS stuUsrId
			<!-- , T3.USR_NM				AS usrNm -->
			, T3.GRP_TMGR_YN		AS grpTmgrYn
			, T2.ANNX_ID			AS annxIdForSmt
			, T2.GRP_TEM_NM
		FROM
		(
			SELECT
				T1.GRP_ID
				, T1.GRP_TEM_ID
				, T1.ANNX_ID
				, T1.GRP_TEM_NM
			FROM
			(
				SELECT
					A.GRP_ID
					, A.GRP_TEM_ID
					, A.ASN_ID
					, A.SMT_DTM
					, A.SMT_CN
					, A.ANNX_ID
					, A.SMT_CMPL_YN
					, A.SCR
					, A.FDBK_CN
					, B.STU_USR_ID
					, B.GRP_TMGR_YN
					, B.DEL_YN
					, E.GRP_TEM_NM
				FROM
					LMS_LRM.EA_GRP_ASN_SMT A
				RIGHT JOIN
					LMS_LRM.EA_GRP_TMBR B
				ON
					A.GRP_ID = B.GRP_ID
				AND
					A.GRP_TEM_ID = B.GRP_TEM_ID
				LEFT JOIN 
			   		LMS_LRM.EA_GRP_TEM E
				  ON 
				 	A.GRP_ID = E.GRP_ID
				AND 
				    A.GRP_TEM_ID = E.GRP_TEM_ID
				WHERE
					A.ASN_ID = #{asnId}
				AND
					B.STU_USR_ID = #{stuUsrId}
			) T1
		) T2
		LEFT JOIN
			LMS_LRM.EA_GRP_TMBR T3
		ON
			T2.GRP_ID = T3.GRP_ID
		AND
			T2.GRP_TEM_ID = T3.GRP_TEM_ID
		ORDER BY
			T3.GRP_TMGR_YN DESC
	</select>



	<!-- 과제 상세 파일조회 -->
	<select id="selectAsnFile" parameterType="String" resultType="com.aidt.api.ea.asncom.dto.EaAsnFleDto">
		/* EaAsnCom-Mapper.xml - selectAsnFile */
		SELECT
			   CAF.ANNX_FLE_ID		AS annxFileId
			  ,CAF.ANNX_ID			AS annxId
			  ,CAF.SRT_ORDN			AS srtOrdn
			  ,CAF.DOC_VI_ID		AS docViId
			  ,CAF.ANNX_FLE_NM		AS annxFileNm
			  ,CAF.ANNX_FLE_ORGL_NM	AS annxFileOrglNm
			  ,CAF.ANNX_FLE_FEXT_NM	AS annxFileFextNm
			  ,CAF.ANNX_FLE_SZE		AS annxFileSize
			  ,CAF.ANNX_FLE_PTH_NM	AS annxFilePathNm
			  ,CAF.USE_YN			AS useYn
		FROM
			LMS_LRM.CM_ANNX_FLE CAF
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>

	<!-- 모둠 게시판 존재 여부 -->
	<select id="selectBlbdCnt" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="int">
		/* EaAsnCom-Mapper.xml - selectBlbdCnt */
		SELECT
			COUNT(BLBD_ID)
		FROM
			LMS_LRM.EA_GRP_BLBD
		WHERE
			GRP_ID = #{grpId}
		AND
			GRP_TEM_ID = #{grpTemId}
		AND
			ASN_ID = #{asnId}
	</select>

	<!-- 해당 모둠 그룹의 모둠팀, 인원, 팀 수 조회 -->
	<select id="selectGrpInfo" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/** EaAsnCom-Mapper.xml - selectGrpInfo */
		SELECT
			A.STU_USR_ID 		AS stuUsrId
		FROM
			LMS_LRM.EA_GRP_TMBR A
		LEFT JOIN
			LMS_LRM.EA_GRP B
		ON
			A.GRP_ID = B.GRP_ID
		WHERE
			A.GRP_ID = #{grpId}
		AND
			A.GRP_TEM_ID = #{grpTemId}
	</select>
	
	<!-- AI 학습창 파라미터 조회 -->
	<select id="selectAiIsRcm" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto">
		/** EaAsnCom-Mapper.xml - selectAiIsRcm */
		SELECT  EE.EV_ID
			  , EER.EV_CMPL_YN
		  FROM LMS_LRM.EA_EV EE
		 INNER JOIN LMS_LRM.EA_EV_RS EER 
		    ON EE.EV_ID = EER.EV_ID 
		   AND EER.USR_ID = #{stuUsrId}  
		 INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
		    ON EE.EV_ID = EAETR.EV_ID 
		 WHERE EE.OPT_TXB_ID = #{optTxbId}
		   AND EE.EV_DV_CD = 'AE'
		   AND EE.EV_DTL_DV_CD = 'OV'
		   AND EAETR.MLU_KMMP_NOD_ID = #{luNodId}		   
		   AND EE.USR_ID = #{stuUsrId} 
		 GROUP BY EE.EV_ID , EER.EV_CMPL_YN
		 ORDER BY EER.EV_CMPL_YN DESC
		 limit 1
 	</select>
 	
 	<!-- AI 평가생성여부 -->
	<select id="selectAiEvRcmYn" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="String">
		/** EaAsnCom-Mapper.xml - selectAiEvRcmYn */
		SELECT if(count(*) > 0, 'Y', 'N')
		  FROM LMS_LRM.EA_EV EE
		 INNER JOIN LMS_LRM.EA_EV_RS EER 
		    ON EE.EV_ID = EER.EV_ID
		   AND EER.USR_ID = #{stuUsrId}
		 INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
		 	ON EE.OPT_TXB_ID = EAETR.OPT_TXB_ID 
		    AND EE.EV_ID = EAETR.EV_ID 
		 WHERE EE.OPT_TXB_ID = #{optTxbId}
		   AND EE.EV_DV_CD = 'AE'
		   AND EAETR.MLU_KMMP_NOD_ID = #{luNodId}
		   and ee.EV_DTL_DV_CD = #{evDtlDvCd}
		   and EE.DEL_YN = 'N'
		 <if test = '!"OV".equals(evDtlDvCd)'>
	   		and eaetr.LUEV_CMPL_YN is null
	     </if>
		<if test = '"OV".equals(evDtlDvCd)'>
	   		and eer.EV_CMPL_YN = 'Y'
	     </if>
		  order by EAETR.MLU_KMMP_NOD_ID , EAETR.TPC_KMMP_NOD_ID 
 	</select>
 	
 	    <!-- 과제 전체 제출 여부 조회 -->
    <select id="selectAllSmt" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="String">
        /** EaAsnCom-Mapper.xml - selectAllSmt */
        SELECT 
        
                CASE 
                    WHEN SUM(CASE WHEN SMT_CMPL_YN  = 'N' THEN 1 ELSE 0 END) > 0 THEN 'N'
                    ELSE 'Y'
                END AS ALL_SMT_YN
        FROM EA_ASN_SMT EAS 
        WHERE ASN_ID = #{asnId}
    </select>
    
    <!-- 모둠과제 전체 제출 여부 조회 -->
    <select id="selectAllGrSmt" parameterType="com.aidt.api.ea.asn.stu.dto.EaAsnStuDto" resultType="String">
        /** EaAsnCom-Mapper.xml - selectAllGrSmt */
        SELECT 
                CASE 
                    WHEN SUM(CASE WHEN SMT_CMPL_YN  = 'N' THEN 1 ELSE 0 END) > 0 THEN 'N'
                    ELSE 'Y'
                END AS ALL_SMT_YN
         FROM EA_GRP_ASN_SMT A
        WHERE ASN_ID = #{asnId}
          AND GRP_ID = #{grpId}
    </select>
    
    <!-- 우리반 수업 과제 조회 -->
    <select id="selectTlAsn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /** EaAsnCom-Mapper.xml - selectTlAsn */
        SELECT A.ASN_ID 
              ,A.LU_NOD_ID
              ,A.TC_NOD_ID
              ,A.LRN_STP_DV_CD
              ,A.OPT_TXB_ID
		  FROM EA_ASN_RNGE A
		 INNER JOIN EA_ASN B
		    ON A.ASN_ID = B.ASN_ID
		   AND B.OPT_TXB_ID = #{optTxbId} 
		   AND B.DEL_YN = 'N'
		   AND (
		        CASE 
		            WHEN B.ASN_PTME_DV_CD = 'PT' THEN B.END_DTM > NOW()  
		            ELSE 1 = 1  
		        END
		    )
		 INNER JOIN EA_ASN_SMT C
		    ON A.ASN_ID = C.ASN_ID 
		   AND C.SMT_CMPL_YN = 'N'  
		 WHERE 1=1
		   AND A.LRN_STP_DV_CD = 'CL'
		   AND A.DEL_YN = 'N'
		   AND A.LRN_TP_CD = 'TL'
		 GROUP BY A.ASN_ID ,A.LU_NOD_ID,A.TC_NOD_ID,A.LRN_STP_DV_CD,A.OPT_TXB_ID
    </select>
    
    
	<!-- 총 학습수 업데이트 -->
	<update id="updateLrnCnt" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnCom-Mapper.xml - updateLrnCnt */
		UPDATE EA_ASN 
		   SET 
				TTL_LRN_CNT = #{ttlLrnCnt}
			  , MDFR_ID = #{mdfrId}
			  , MDF_DTM = NOW()
		 WHERE ASN_ID = #{asnId}
	</update>
	
	<!-- 과제 포함 학생 조회 -->
    <select id="selectAsnStu" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /** EaAsnCom-Mapper.xml - selectAsnStu */
        SELECT EAS.ASN_ID 
		      ,EAS.STU_USR_ID
		      ,EAS.SMT_CMPL_YN 
		      ,EAS.CMPL_LRN_CNT 
		  FROM EA_ASN_SMT EAS 
		 WHERE ASN_ID = #{asnId}
    </select>
    
    <!-- 완료 학습 수,완료 여부 업데이트 -->
	<update id="updateCmpl" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnCom-Mapper.xml - updateCmpl */
		UPDATE EA_ASN_SMT  
		   SET 
		   	   CMPL_LRN_CNT = #{cmplLrnCnt}
		   	  ,SMT_CMPL_YN = #{smtCmplYn}
			  ,MDF_DTM =NOW()
			  <if test = 'smtCmplYn != null and !"".equals(smtCmplYn) and "Y".equals(smtCmplYn)'>
	   			,SMT_DTM = NOW()
	      	  </if>
		 WHERE ASN_ID = #{asnId}
		   AND STU_USR_ID = #{stuUsrId}
	</update>
	
	<!-- 우리반 수업  과제 조회 -->
    <select id="selectTlAsnStatus" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /** EaAsnCom-Mapper.xml - selectTlAsnStatus */
        SELECT A.ASN_ID 
              ,A.LU_NOD_ID
              ,A.TC_NOD_ID
              ,A.LRN_STP_DV_CD
              ,A.OPT_TXB_ID
              ,B.USE_YN
		  FROM EA_ASN_RNGE A
		 INNER JOIN EA_ASN B
		    ON A.ASN_ID = B.ASN_ID
		   AND B.OPT_TXB_ID = #{optTxbId} 
		   AND B.DEL_YN = 'N'
		 WHERE 1=1
		   AND A.DEL_YN = 'N'
		   AND A.LRN_TP_CD = 'TL'
		 <!--   <if test = 'lrnStpDvCd != null and !"".equals(lrnStpDvCd)'>
		   	AND A.LRN_STP_DV_CD <![CDATA[<>]]> #{lrnStpDvCd}
		   </if> -->
    </select>
    
    <!-- 사용여부 업데이트 -->
	<update id="updateLrnUseYn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnCom-Mapper.xml - updateLrnUseYn */
		UPDATE EA_ASN 
		   SET 
				USE_YN = #{useYn}
			  , MDFR_ID = #{mdfrId}
			  , MDF_DTM = NOW()
		 WHERE ASN_ID = #{asnId}
	</update>
	
	<!-- AI 맞춤학습  과제 조회 -->
    <select id="selectAiAsnStatus" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto" resultType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
        /** EaAsnCom-Mapper.xml - selectAiAsnStatus */
        SELECT A.ASN_ID 
              ,A.LU_NOD_ID
              ,A.TC_NOD_ID
              ,A.LRN_STP_DV_CD
              ,A.OPT_TXB_ID
              ,B.USE_YN
		  FROM EA_ASN_RNGE A
		 INNER JOIN EA_ASN B
		    ON A.ASN_ID = B.ASN_ID
		   AND B.OPT_TXB_ID = #{optTxbId} 
		   AND B.DEL_YN = 'N'
		 WHERE A.LU_NOD_ID IN(
		 						 <foreach item="aiId" collection="aiSearchOptionList" open="" separator="," close="">
					                #{aiId}
					             </foreach>
		 					  ) 
		   AND A.DEL_YN = 'N'
		   AND A.LRN_TP_CD = 'AL'
    </select>
    
     <!-- 잠금 여부 업데이트 -->
	<update id="updateLrnLockYn" parameterType="com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto">
		/** EaAsnCom-Mapper.xml - updateLrnLockYn */
		UPDATE EA_ASN 
		   SET 
				LCKN_YN = #{lcknYn}
			  , MDFR_ID = #{mdfrId}
			  , MDF_DTM = NOW()
		 WHERE ASN_ID = #{asnId}
	</update>

</mapper>