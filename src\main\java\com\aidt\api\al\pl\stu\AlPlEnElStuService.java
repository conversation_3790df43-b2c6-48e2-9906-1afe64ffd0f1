package com.aidt.api.al.pl.stu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.api.al.pl.dto.AlPlMluLstInqEnElStuResponseDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.al.pl.dto.AlPlTpcProfInfoStuResponseDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-28
 * @modify date 2024-02-28
 * @desc AI맞춤학습 학생 단원차시조회
 */
@Slf4j
@Service
public class AlPlEnElStuService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.stu.alPlEnElStu.";
	
	@Autowired
	private CommonDao commonDao;
	
	//@Autowired 
	//private JwtProvider jwtProvider;
	
	/**
     * 목록에서 학생 중단원목록 조회 작업중
     * 
     * @param 
     * @return List<AlMluTcLstInqStuResponseDto>
     */
	public List<AlMluTcLstInqStuResponseDto> selectMluLstInqEnElStuList(AlMluTcLstInqStuReqDto reqDto) {
		//CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		//final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    //Date latestDate = null;
	    //String latestMdfDtm = null;
		String optTxbId = reqDto.getOptTxbId();
		String usrId = reqDto.getUsrId();
	    
		//학생 중단원 목록 조회(중단원에 따른 학습맵id, 지식맵id 등 정보조회)
		List<AlMluTcLstInqStuResponseDto> mluList = commonDao.selectList(MAPPER_NAMESPACE + "selectMluLstInqEnElStuList", reqDto);
		for(int i=0; i < mluList.size(); i++) {
			log.info(" ########################################### i " + i);
			//AlMluTcLstInqStuResponseDto mluItem = mluList.get(i);
			String evDtlDvCd = mluList.get(i).getEvDtlDvCd();	//평가상세구분코드 (UG:진단평가,C1:맞춤1,C2:맞춤2)
			String evDvCd = mluList.get(i).getEvDvCd();			//평가구분코드(AI)
			String mKluLrmpNodId = mluList.get(i).getMKmmpNodId();	//지식맵 중단원 노드Id
			log.info("mKmmpLuLrmpNodId   333333333333  : " + mKluLrmpNodId);
			if(evDtlDvCd.equals(AlConstUtil.EV_DTL_DV_CD_OV) && (evDvCd.equals("AE") || evDvCd.equals("DE") || evDvCd.equals("DIY"))) {
				//보완할 부분이 있는 경우(약점 존재할 경우)				
				//중단원별 차시 정보 조회 및 학습자 수준 정보 
				log.info("mKmmpLuLrmpNodId   33344444444444444444333333333  : " + mKluLrmpNodId);
				List<AlPlMluLstInqEnElStuResponseDto> tcLrmpNodInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTcLrmpNodInfoList", 
						Map.of("optTxbId", optTxbId, "usrId", usrId, "mKluLrmpNodId", mKluLrmpNodId));	  
				//List<AlPlMluLstInqEnElStuResponseDto> tcLrmpNodInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTcLrmpNodInfoList", reqDto);
				//차시별 토픽정보, 평가정보 저장
				if(tcLrmpNodInfoList.size() > 0) {
					for(int n=0; n < tcLrmpNodInfoList.size(); n++) {
						//AlMluTcLstInqStuReqDto dto = new AlMluTcLstInqStuReqDto();
						String mKLuLrmNodId = mKluLrmpNodId;	//중단원 지식맵 노드ID
						String tcKmmpNodId = tcLrmpNodInfoList.get(n).getKmmpNodId();	//차시 지식맵 노드ID
						//String mluLrmpNodId = tcLrmpNodInfoList.get(n).getMluLrmpNodId();	// 중단원 학습맵 노드ID
						//String tcLrmpNodId = tcLrmpNodInfoList.get(n).getTcLrmpNodId();	//차시 학습맵 노드ID
						
						/******* 목록에서 '평가 학습창에 사용될 버튼 데이터' *******/
						
						//차시에 따른 평가 완료 여부 확인(진단평가여부, 맞춤학습1, 2, 도전 완료 여부 확인)
						List<AlPlQtmTpcProfDto> aiPlQtmTpcProfList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPlQtmTpcProfList", 
								Map.of("optTxbId", optTxbId
										, "usrId", usrId
										, "tcKmmpNodId" , tcKmmpNodId
										, "mKluLrmpNodId" , mKLuLrmNodId
								));	//차시정보에 따른 맞춤학습 평가 정보 조회
						String finishCk = "N";
						if(aiPlQtmTpcProfList.size() > 0) {
							String ugCk = "N";
							String ugLrnrVelTpCdCk = "";	//진단평가 학습자 수준 정보
							String c1Ck = "N";
							String c2Ck = "N";
							for(int a=0; a < aiPlQtmTpcProfList.size(); a++) {
								String evDtlDvCdCk = aiPlQtmTpcProfList.get(a).getEvDtlDvCd();	//평가 종류
								String lrnrVelTpCdCheck = aiPlQtmTpcProfList.get(a).getLrnrVelTpCd();	//학습자 수준 체크
								String evCmplYnCheck = aiPlQtmTpcProfList.get(a).getEvCmplYn();	//학습 완료 여부
								if(evDtlDvCdCk.equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
									ugCk = evCmplYnCheck;	//ug의 학습완료 여부 체크
									ugLrnrVelTpCdCk = lrnrVelTpCdCheck;	//학습자 수준 저장								
								}else if(evDtlDvCdCk.equals("C1")) {
									c1Ck = evCmplYnCheck;
								}else if(evDtlDvCdCk.equals("C2")) {
									c2Ck = evCmplYnCheck;
								}
							}
							log.info("ugCk : " + ugCk + " | ugLrnrVelTpCdCk : " + ugLrnrVelTpCdCk);
							log.info("c1Ck : " + c1Ck + " | c2Ck : " + c2Ck );
							if(ugCk.equals("N")) {
								finishCk = "N";
							}else if(ugCk.equals("Y")) {
								if(ugLrnrVelTpCdCk != null) {
									if(ugLrnrVelTpCdCk.equals("FS")) {
										//진단평가완료 상태이면서 빠른 학습자일 경우는 C1, C2만 체크
										if(c1Ck.equals("Y")) {
											if(c2Ck.equals("Y")) {
												finishCk = "Y";	//ai맞춤학습 완료
											}
										}else {
											finishCk = "N"; //ai맞춤학습 미완료
										}
										
									}
								}
							}else {
								//진단평가완료 상태이면서 빠른 학습자가 아닐 경우 C1, C2만 체크
								if(c1Ck.equals("Y") || c2Ck.equals("Y")) {
									finishCk = "N";	//ai맞춤학습 미완료
								}else {
									finishCk = "N"; //ai맞춤학습 미완료
								}
							}
						}
						tcLrmpNodInfoList.get(n).setFinishTcYn(finishCk);
						/******* **************/
						//차시정보에 따른 토픽 정보 전체 조회
						List<AlPlTpcProfInfoStuResponseDto> selectAiTpcInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiTpcInfoList", 
						Map.of("optTxbId", optTxbId
								, "usrId", usrId
								, "mKluLrmpNodId" , mKluLrmpNodId
								, "tcKmmpNodId" , tcKmmpNodId
						));
						
						if(selectAiTpcInfoList.size() > 0) {
							AlPlMluLstInqEnElStuResponseDto responseDto = new AlPlMluLstInqEnElStuResponseDto();
							responseDto.setOptTxbId(optTxbId);
							responseDto.setKmmpNodId(tcKmmpNodId);	// 차시 지식맵정보
							responseDto.setAiPlTpcProfInfoList(selectAiTpcInfoList);	//차시에 따른 토픽 정보
						}
						tcLrmpNodInfoList.get(n).setAiPlTpcProfInfoList(selectAiTpcInfoList);	//차시에 따른 토픽 정보
					}
				}
				
				log.info("i : " + i);
				mluList.get(i).setAiKmmpNodList(tcLrmpNodInfoList);
			}
			
			//중단원에서 가장 최근에 수정된 중단원 조회
			String lastMdfDtm = mluList.get(i).getMdfDtm() != null ? mluList.get(i).getMdfDtm() : null;	//수정일자
			//String currentMdfDtm = "";
			log.info("lastMdfDtm : " +lastMdfDtm);
			//중단원별 마지막 토픽 정보 및 평가 id 정보 조회
			if (lastMdfDtm != null) {
				String mKlrmpNodIdData = mluList.get(i).getMKmmpNodId();	//중단원 지식맵
				log.info("mKlrmpNodIdData : " + mKlrmpNodIdData);
				log.info("mKmmpLuLrmpNodId : " + mKluLrmpNodId);
				List<AlPlQtmTpcProfDto> selectTEaEvLastInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectTEaEvLastInfo", 
				Map.of("optTxbId", optTxbId
						, "usrId", usrId
						, "mKluLrmpNodId" , mKlrmpNodIdData
				));
				if(selectTEaEvLastInfo.size() > 0) {
					mluList.get(i).setMLastEaEvInfo(selectTEaEvLastInfo.get(0));	//마지막으로 평가한 데이터 정보
				}else {
					mluList.get(i).setMLastEaEvInfo(null);	//마지막으로 평가한 데이터 정보
				}
                
			}else {
				mluList.get(i).setMLastEaEvInfo(null);	//마지막으로 평가한 데이터 정보
			}
		}
		
		log.info(mluList.toString());
		return mluList;
	}
	
	/**
     * 상세페이지에서 학생 중단원목록 조회 작업중
     * 
     * @param 
     * @return List<AlMluTcLstInqStuResponseDto>
     */
	public List<AlMluTcLstInqStuResponseDto> selectMluLstInqEnElStuDetailList(AlMluTcLstInqStuReqDto reqDto) {
		//CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//reqDto.setOptTxbId(userDetails.getOptTxbId());
		if(reqDto.getUsrId().equals("ene34l101-stu-0020")) {
			//reqDto.setOptTxbId("eaevasn9c0f1b6a831opttxb0002");
			//reqDto.setUsrId("eaevasnUserStu3200001");
		}
		
		List<AlMluTcLstInqStuResponseDto> mluList = commonDao.selectList(MAPPER_NAMESPACE + "selectMluLstInqEnElStuList", reqDto);
		for(int i=0; i < mluList.size(); i++) {
			//AlMluTcLstInqStuResponseDto mluItem = mluList.get(i);
			String evDtlDvCd = mluList.get(i).getEvDtlDvCd();
			String evDvCd = mluList.get(i).getEvDvCd();
			String mKmmpLuLrmpNodId = mluList.get(i).getMKmmpNodId();	//지식맵 중단원 노드Id
			
			if(evDtlDvCd.equals(AlConstUtil.EV_DTL_DV_CD_OV) && (evDvCd.equals("AE") || evDvCd.equals("DE") || evDvCd.equals("DIY"))) {
				//보완할 부분이 있는 경우(약점 존재할 경우)
				String optTxbId = reqDto.getOptTxbId();
				String usrId = reqDto.getUsrId();
				
				List<AlPlMluLstInqEnElStuResponseDto> tcLrmpNodInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTcLrmpNodInfoList", 
						Map.of("optTxbId", optTxbId, "usrId", usrId, "mKluLrmpNodId", mKmmpLuLrmpNodId));
				if(tcLrmpNodInfoList.size() > 0) {
					mluList.get(i).setAiKmmpNodList(tcLrmpNodInfoList);
				}
			}
		}
		log.info(mluList.toString());
		return mluList;
	}
	
	/**
     * 학생 상세에서 차시 정보 조회
     * 
     * @param 
     * @return List<AlPlMluLstInqEnElStuResponseDto>
     */
	public List<AlPlMluLstInqEnElStuResponseDto> selectAiTcKmmpNodList(@Valid AlMluTcLstInqStuReqDto reqDto) {
		// TODO Auto-generated method stub
		String optTxbId = reqDto.getOptTxbId();
		String usrId = reqDto.getUsrId(); 
		String mKluLrmpNodId = reqDto.getMkLuLrmNodId();	// 중단원 지식맵 노드ID
		//String mluLrmpNodId = reqDto.getMluLrmpNodId();	//중단원 학습맵 노드ID
		
		log.info("33333333333333 : " + mKluLrmpNodId);
		//중단원 정보를 가지고 차시 목록 조회(영어(
		List<AlPlMluLstInqEnElStuResponseDto> aiTcKmmpNodList = commonDao.selectList(MAPPER_NAMESPACE + "selectTcLrmpNodInfoList", 
				Map.of("optTxbId", optTxbId, "usrId", usrId, "mKluLrmpNodId", mKluLrmpNodId));
		//selectAiPlQtmTpcProfList
		
		if(aiTcKmmpNodList.size() > 0) {
			for(int i=0; i < aiTcKmmpNodList.size(); i++) {
				//String tcLrmpNodId = aiTcKmmpNodList.get(i).getTcLrmpNodId();	//차시 학습맵 ID
				String tcKmmpLrmpNodId = aiTcKmmpNodList.get(i).getKmmpNodId();	//차시 지식맵 ID
				List<AlPlQtmTpcProfDto> aiPlQtmTpcProfList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPlQtmTpcProfList", 
						Map.of("optTxbId", optTxbId
								, "usrId", usrId
								, "tcKmmpNodId" , tcKmmpLrmpNodId	//차시 지식맵id
								, "mKluLrmpNodId" , mKluLrmpNodId	//중단원 지식맵 id
						));
				String ugEvDtlDvCd = AlConstUtil.EV_DTL_DV_CD_OV;
				String ugEvCmplYn = "Y";
				if(aiPlQtmTpcProfList.size() > 0) {
					aiTcKmmpNodList.get(i).setAiPlQtmTpcProfList(aiPlQtmTpcProfList);
					if(aiPlQtmTpcProfList.get(0).getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
						ugEvDtlDvCd = aiPlQtmTpcProfList.get(0).getEvDtlDvCd();
						ugEvCmplYn = aiPlQtmTpcProfList.get(0).getEvCmplYn();
						aiTcKmmpNodList.get(i).setEvDtlDvCd(ugEvDtlDvCd);
						aiTcKmmpNodList.get(i).setEvCmplYn(ugEvCmplYn);
					}
				}else {
					aiTcKmmpNodList.get(i).setEvDtlDvCd(ugEvDtlDvCd);
					aiTcKmmpNodList.get(i).setEvCmplYn(ugEvCmplYn);
				}
			}
		}
		
		return aiTcKmmpNodList;
	}

	/**
     * 학생 상세에서 차시별 토픽 정보 조회
     * 
     * @param 
     * @return List<AlPlMluLstInqEnElStuResponseDto>
     */
	public List<AlPlMluLstInqEnElStuResponseDto> selectAiTpcInfoList(@Valid List<AlMluTcLstInqStuReqDto> reqDto) {
		List<AlPlMluLstInqEnElStuResponseDto> aiTcKmmpNodList = new ArrayList<>();
		if(reqDto.size() > 0) {
			for(int i=0; i < reqDto.size(); i++) {
				log.info("##############################################");
				String optTxbId = reqDto.get(i).getOptTxbId();
				String usrId = reqDto.get(i).getUsrId();
				String mKluLrmpNodId = reqDto.get(i).getMkLuLrmNodId();	//중단원 지식맵 정보 mkLuLrmNodId
				String tcKmmpNodId = reqDto.get(i).getTcKmmpNodId();	//차시 지식맵 정보 tcKmmpNodId
				String tckmmpNodNm	= reqDto.get(i).getTcKmmpNodNm();	//차시 지식맵 노드명
				List<AlPlTpcProfInfoStuResponseDto> selectAiTpcInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiTpcInfoList", 
				Map.of("optTxbId", optTxbId
						, "usrId", usrId
						, "mKluLrmpNodId" , mKluLrmpNodId
						, "tcKmmpNodId" , tcKmmpNodId
				));
				AlPlMluLstInqEnElStuResponseDto responseDto = new AlPlMluLstInqEnElStuResponseDto();
				responseDto.setOptTxbId(optTxbId);
				responseDto.setKmmpNodId(tcKmmpNodId);	// 차시 지식맵정보
				responseDto.setKmmpNodNm(tckmmpNodNm);	
				responseDto.setAiPlTpcProfInfoList(selectAiTpcInfoList);	//차시에 따른 토픽 정보
				aiTcKmmpNodList.add(responseDto); // 리스트에 추가
			}
		}
		return aiTcKmmpNodList;
	}
}
