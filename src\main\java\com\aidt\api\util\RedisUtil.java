package com.aidt.api.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RedisUtil {

	@Value("${server.meta.textbook.systemCode}")
	private String systemCode;
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	/**
	 * ex) key = "AAA:BBB:CCC:DDD" 인 경우
	 * 
	 * search key = 해당 key 값 = "AAA:BBB:CCC:DDD" 입력 해당 key 값 하위 모두 가져오고 싶은 경우 =
	 * "AAA:BBB:CCC*"
	 * 
	 * @param searchKey
	 * @return
	 */
	public List<String> redisKeyList(String searchKey) {
		List<String> list = null;

		if (StringUtils.isNotBlank(searchKey)) {
			String redisKey = systemCode + ":" + searchKey;
			
			list = new ArrayList<String>();
			ScanOptions so = ScanOptions.scanOptions().match(redisKey).build();

			Cursor<byte[]> keys = redisTemplate.getConnectionFactory().getConnection().scan(so);

			while (keys.hasNext()) {
				String key = new String(keys.next());
				list.add(key);
				log.debug("key >>> " + key);
			}

		}

		return list;
	}

	/**
	 * redis key 삭제
	 * 
	 * @param key
	 */
	public void redisKeyDelete(List<String> list) {
		if (list != null && list.size() > 0) {
			try {
				redisTemplate.delete(list);
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
	}

	/**
	 * redis key 하위 종속 key들 삭제
	 * 
	 * @param searchKey
	 */
	public void redisKeyDeleteArray(String searchKey) {
		List<String> list = redisKeyList(searchKey);

		if (list != null && list.size() > 0) {
			redisKeyDelete(list);
		}
	}
}
