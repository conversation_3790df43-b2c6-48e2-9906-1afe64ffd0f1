package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * AI 맞춤 사용자별 문항 토픽 프로파일
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlPlCtnDto {
	@Parameter(name="구분")
	private String acnDv;

	@Parameter(name="운영교과서ID ")
	private String optTxbId;

	@Parameter(name="학습맵노드ID")
	private String lrmpNodId;

	@Parameter(name="사용자ID")
	private String userId;

    @Parameter(name="순번")
    private int idx;

    private String lluNodId;
    private String lluNodNm;
    private String lluEpsYn;
    private String lluUseYn;
    private String mluNodId;
    private String mluNodNm;
    private String mluEpsYn;
    private String mluUseYn;
    private String sluNodId;
    private String sluNodNm;
    private String sluEpsYn;
    private String sluUseYn;
    private String tcNodId;
    private String tcNodNm;
    private String tcEpsYn;
    private String tcUseYn;
    private String atvTotCnt;
    private String strAtvId;
    private String txbStrPgeNo;
	private String txbEndPgeNo;
	private String txbUseYn;
	private String wkbStrPgeNo;
	private String wkbEndPgeNo;
	private String wkbUseYn;


    private List<TxbPdfList> txbPdfList;

    private WbLrn wbLrn;

    private List<LrnTl> lrnTlList;	//학습도구

    private List<LrnStp> lrnStpList;
    private List<LrnAtv> lrnAtvList;

    @Getter
    @Setter
    static public class TxbPdfList {

    	private String pgeNo;
    	private String cdnPthNm;


    }

    @Getter
    @Setter
    static public class WbLrn {

    	private String wbStrPgeNo;
    	private String wbEndPgeNo;
    	private String wvUseYn;
    }

    @Getter
    @Setter
    static public class LrnTl {

    	private String lrnTlTpCd;     //학습도구유형코드
    	private String lrnTlTpNm;	  //학습도구 유형명
    	private String lrnTlKnCd;     //학습도구코드
    	private String lrnTlKnNm;       //학습도구명
    	private String icnCdnPthNm;   //아이콘CDN경로명
    	private String icnFleTpCd;    //아이콘파일유형코드

    }

    @Getter
    @Setter
    static public class LrnStp {

    	private String lrnStpId;
    	private String lrnStpNm;
    	private int lrnStpOrdn;
    	private String lrnStpDvCd;
    	private List<LrnAtv> lrnAtvList;
    }

    @Getter
    @Setter
    static public class LrnAtv {

    	private String lrnAtvId;
    	private String lrnAtvNm;
    	private String ctnTpCd;
    	private String ctnUrl;
    	private int rcstnOrdn;
    	private String imgCdn;
    	private String qtmId;
    	private String evId;
    	private CtnMtd ctnMtd;
    	private String lrnStCd;//학습상태(미학습:NL, 학습중:DL, 학습완료: CL)
    }

    @Getter
    @Setter
    static public class CtnMtd {

    	private String metaDataId;
        private String ctnTpCd;
        private String ctnId;
        private String lrnCofmTmScnt;
        private String ctnDffdDvCd;
        private String cmplBsCd;
        private String lrnTpCd;
        private String atvQtmCnt;
        private String ocrUseYn;
        private String fmlUseYn;
        private String sttUseYn;
        private String vdsLRsln;
        private String vdsMRsln;
        private String vdsHRsln;
        private String sttlSmiFleNm;
        private String sttlVttFleNm;
        private String scrbFleNm;
        private String vceFleNm;
        private String thbFleNm;
    }

}
