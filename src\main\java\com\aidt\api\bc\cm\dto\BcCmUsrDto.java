package com.aidt.api.bc.cm.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "CM_사용자")
public class BcCmUsrDto {

	@Schema(description = "사용자ID")
	private String usrId;

	@Schema(description = "KERIS사용자ID")
	private String kerisUsrId;

	@Schema(description = "사용자명")
	private String usrNm;

	@Schema(description = "사용자유형코드")
	private String usrTpCd;

	@Schema(description = "최초등록일시")
	private LocalDateTime fstRegDtm;

	@Schema(description = "학급ID")
	private String claId;

	@Schema(description = "생성자ID")
	private String crtrId;

	@Schema(description = "생성일시")
	private LocalDateTime crtDtm;

	@Schema(description = "수정자ID")
	private String mdfrId;

	@Schema(description = "수정일시")
	private LocalDateTime mdfDtm;
}
