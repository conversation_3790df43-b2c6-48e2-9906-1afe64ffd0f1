package com.aidt.api.al.cmt.dto.req.ma;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvStDto;
import com.aidt.api.al.cmt.dto.req.cm.N01ReqDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc 수학 > 학기/학년초진단
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtMaEvStReqDto {

    @Parameter(name="학교급코드(초등:E,중등:M,고등:H)", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^(E|M|H)$", message="학교급코드(초등:E,중등:M,고등:H)")
    private String schlGrdCd;

    @Valid
    @Parameter(name="성취 수준 총평(가장 많이 맞힌 이전학년 내용영역)", required=true)
    private N01ReqDto total;

    @Valid
    @Parameter(name="AI 추천 콘텐츠 존재여부", required=true)
    private Boolean existAiRcmCtn;

    private String evId;

    private String usrId;

    public AiCmtMaEvStDto toDto() {
        AiCmtLvlCalculator calculator = "H".equalsIgnoreCase(this.schlGrdCd)
                ? AiCmtLvlCalculator.MA_TYPE_50_80 : AiCmtLvlCalculator.MA_TYPE_60_80;

        return AiCmtMaEvStDto.builder()
                .n01(this.total.toDto(calculator))
                .existAiRcmCtn(this.existAiRcmCtn)
                .build();
    }

}
