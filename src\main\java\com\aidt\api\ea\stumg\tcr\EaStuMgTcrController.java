package com.aidt.api.ea.stumg.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

//@Slf4j
@Tag(name="[ea] 학생 관리 - 교사", description="학생 관리 - 교사")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/stu/stumg")
public class EaStuMgTcrController {

	@Autowired
	EaStuMgTcrService eaStuMgTcrService;
	

	
}
