package com.aidt.api.appevent.event.adminstat;

import java.time.LocalDateTime;

import com.aidt.base.message.application.AbstractAppEvent;
import com.aidt.base.message.messagequeue.AbstractPayload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 로그인 Event - 변수 추가 및 수정 시 관리자 kafka 개발 담당자 전달 필수
 */
public class LoginEvent extends AbstractAppEvent {
	public LoginEvent(LoginEventPayload payload) {
		super(payload);
	}

	@Builder
	@Getter
	@ToString
	public static class LoginEventPayload extends AbstractPayload {

		@Schema(description = "cm_token.사용자ID")
		private String usrId;

		@Schema(description = "cm_token.KERIS사용자ID")
		private String kerisUsrId;

		@Schema(description = "cm_token.교과서ID")
		private Long txbId;
		
		@Schema(description = "cm_token.학급ID")
		private String claId;
		
		@Schema(description = "cm_usr.사용자유형코드")
		private String usrTpCd;
		
		@Schema(description = "cm_cla.학교코드")
		private String schlCd;

		@Schema(description = "cm_cla.학년")
		private Long sgy;
		
		@Schema(description = "cm_cla.학교급코드")
		private String schlGrdCd;

		@Schema(description = "cm_cla.KERIS반코드")
		private String kerisClaCd;

		@Schema(description = "lms_cms.bc_txb.교과서명")
		private String txbNm;

		@Schema(description = "lms_cms.bc_txb.저자코드")
		private String autrCd;
		
		@Schema(description = "lms_cms.bc_txb.과목코드")
		private String sbjCd;

		@Schema(description = "로그인일시 (java set)")
		private LocalDateTime loginDtm;
	}
}
