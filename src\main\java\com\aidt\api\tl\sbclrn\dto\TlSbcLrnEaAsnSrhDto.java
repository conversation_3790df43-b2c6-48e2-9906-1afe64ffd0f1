package com.aidt.api.tl.sbclrn.dto;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-16 11:16:27
 * @modify date 2024-04-16 11:16:27
 * @desc TlSbcLrnEaAsnSrhDto 교과학습 과제출제건수 조회 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlSbcLrnEaAsnSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 대단원노드ID */
    @NotNull(message = "{field.required}")
    @Parameter(name="대단원노드ID", required = true)
    private String lluNodId;

    /** 차시노드ID */
    @NotNull(message = "{field.required}")
    @Parameter(name="차시노드ID", required = true)
    private String tcNodId;

    /** 학습단계구분코드 */
    @NotNull(message = "{field.required}")
    @Parameter(name="학습단계구분코드", required = true)
    private String lrnStpDvCd;
}
