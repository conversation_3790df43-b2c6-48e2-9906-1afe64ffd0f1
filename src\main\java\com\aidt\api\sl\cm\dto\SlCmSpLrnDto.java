package com.aidt.api.sl.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-08 09:37:48
 * @modify date 2024-05-08 09:37:48
 * @desc [공통 특별학습 리스트 dto]
 */
public class SlCmSpLrnDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 특별학습ID */
    @Parameter(name="특별학습ID")
    private String spLrnId;

    /** 특별학습명 */
    @Parameter(name="특별학습명")
    private String spLrnNm;

    /** 재구성순서 */
    @Parameter(name="재구성순서")
    private String rcstnOrdn;

    /** pc 썸네일 경로 */
    @Parameter(name="pc 썸네일 경로")
    private String pcPath;

    /** tablet 썸네일 경로 */
    @Parameter(name="tablet 썸네일 경로")
    private String taPath;

    /** mobile 썸네일 경로 */
    @Parameter(name="mobile 썸네일 경로")
    private String moPath;
}
