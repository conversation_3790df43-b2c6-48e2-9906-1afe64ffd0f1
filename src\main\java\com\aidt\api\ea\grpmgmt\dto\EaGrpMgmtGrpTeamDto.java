package com.aidt.api.ea.grpmgmt.dto;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email 
 * @create date 2024-04-26 10:53:20
 * @modify date 2024-04-26 10:53:20
 * @desc EaGrpMgGrpDto 모둠관리 모둠 팀 정보 Dto
 */

 @Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtGrpTeamDto {
	 /** 모둠ID */
	 @Parameter(name = "모둠ID")
	 @NotBlank(message = "{field.required}")
	 private int grpId;

	 /** 모둠팀ID */
	 @Parameter(name = "모둠팀ID")
	 @NotBlank(message = "{field.required}")
	 private int grpTemId;

	 /** 모둠팀명 */
	 @Parameter(name = "모둠팀명")
	 @NotBlank(message = "{field.required}")
	 private String grpTemNm;

	 /** 팀원수 */
	 @Parameter(name = "팀원수")
	 private int tmbrCnt;
	 
	 /** 팀원수 */
	 private List<EaGrpMgmtGrpTeamStuDto> stuList;

	 /** 생성자ID */
	 @Parameter(name = "생성자ID")
	 private String crtrId;

	 /** 생성일시 */
	 @Parameter(name = "생성일시")
	 private String crtDtm;

	 /** 수정자ID */
	 @Parameter(name = "수정자ID")
	 private String mdfrId;

	 /** 수정일시 */
	 @Parameter(name = "수정일시")
	 private String MdfDtm;

	 /** 데이터베이스ID */
	 @Parameter(name = "데이터베이스ID")
	 private String dbId;
}