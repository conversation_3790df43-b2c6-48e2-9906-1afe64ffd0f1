package com.aidt.api.tl.lrnwif.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:53:20
 * @modify date 2024-01-05 10:53:20
 * @desc TlLrnwTocSrhDto 학습창 교과학습목차 조회조건 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlLrnwTocSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID")
//    @NotBlank(message = "{field.required}")
    private String lrmpNodId;

    /** 학습활동ID */
    @Parameter(name="학습활동ID")
    private String lrnAtvId;
    
    /** 사용자ID */
    @Parameter(name="학습사용자ID")
    private String usrId;

   /** 학습단계구분 (CL=개념학습, WB=익힘)*/
   @Parameter(name="학습단계구분(CL=개념학습, WB=익힘)")
//    @NotBlank(message = "{field.required}")
   private String lrnStpDvCd;

   /** 미리보기여부 */
   @Parameter(name="미리보기여부")
   private String previewYn;
   
   /** 원클릭진입확인 */
   @Parameter(name="원클릭진입확인")
   private String oneClkYn;
   
   /** 수업자료 모든데이터조회여부 */
   @Parameter(name="수업자료 모든데이터조회여부")
   private String allDatYn;

}
