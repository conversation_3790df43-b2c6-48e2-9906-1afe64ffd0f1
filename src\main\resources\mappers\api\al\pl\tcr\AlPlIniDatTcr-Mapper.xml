<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.tcr.AlPlIniDat">

	<select id="selectTxbId" parameterType="com.aidt.api.al.pl.dto.AlPlTcrDto" resultType="com.aidt.api.al.pl.dto.AlPlTcrDto">
		SELECT
			MAX(COT2.TXB_ID) AS TXB_ID,
			MAX(COT2.OPT_TXB_ID) AS OPT_TXB_ID,
			COUNT(AKNR.KMMP_NOD_ID) AS ROW_CNT
		FROM LMS_LRM.CM_OPT_TXB COT2 
		LEFT OUTER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR 
		ON COT2.OPT_TXB_ID = AKNR.OPT_TXB_ID 
		WHERE COT2.OPT_TXB_ID = #{optTxbId}
		/* AI_지식맵노드재구성 및 교과서ID 조회  - 이혜인 -  AlPlIniDatTcr-Mapper.xml - selectTxbId */
	</select>
	
	<insert id="insertAiKmmpNodRcstn" parameterType="com.aidt.api.al.pl.dto.AlPlTcrDto">
		INSERT INTO LMS_LRM.AI_KMMP_NOD_RCSTN
			 ( OPT_TXB_ID, KMMP_NOD_ID, URNK_KMMP_NOD_ID, KMMP_ID, KMMP_NOD_NM, DPTH, TC_EPS_YN, TC_USE_YN
			 , TXB_ID, ORGL_ORDN, RCSTN_ORDN, DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		 SELECT 
		 	   #{optTxbId}, BKN.KMMP_NOD_ID, BKN.URNK_KMMP_NOD_ID , BK.KMMP_ID, BKN.KMMP_NOD_NM, BKN.DPTH, 'Y', 'Y'
		 	   , BK.TXB_ID, BKN.SRT_ORDN, BKN.SRT_ORDN, 'N', #{usrId}, NOW(), #{usrId}, NOW(), 'AI_registIniDat'
		FROM LMS_CMS.BC_KMMP BK 
		INNER JOIN LMS_CMS.BC_KMMP_NOD BKN ON BKN.KMMP_ID = BK.KMMP_ID
		WHERE BK.TXB_ID = #{txbId}
		AND BK.DEL_YN = 'N'
		AND BKN.DEL_YN = 'N'
		AND BKN.TPC_DV_CD = 'IN'
		<!-- AND BKN.LU_EPS_YN = 'Y' -->
		/* AI_지식맵노드재구성 및 교과서ID 조회  - 이혜인 -  AlPlIniDatTcr-Mapper.xml - insertAiKmmpNodRcstn */
	</insert>
	
</mapper>