package com.aidt.api.ea.evcom.ev.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 평가 관리 - 메인리스트 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvUpdateLockUseYnDto {

	@Parameter(name = "운영교과서ID")
	private String optTxbId;

	@Parameter(name = "사용자ID")
	private String usrId;

	@Parameter(name = "연관단원전체업데이트여부")
	private String rltLuAllUpdYn;
	
	@Parameter(name = "대단원ID")
	private String lluNodId;	
	
	@Parameter(name = "차시단원ID")
	private String tluNodId;	
	
	@Parameter(name = "평가ID")
	private long evId;		
	
	@Parameter(name = "잠금여부")
	private String lcknYn;
	
	@Parameter(name = "사용여부")
	private String useYn;
	
	@Parameter(name = "다른학급 저장유무")
	private String otherClaSaveYn;

	@Parameter(name = "단원 리스트")
	private List<EaEvUpdateLockUseYnDto> luList;
	
	@Parameter(name = "평가 리스트")
	private List<EaEvUpdateLockUseYnDto> evList;
	
	@Parameter(name = "운영교과서ID 리스트")
	private List<EaEvUpdateLockUseYnDto> optTxbIdList;
}
