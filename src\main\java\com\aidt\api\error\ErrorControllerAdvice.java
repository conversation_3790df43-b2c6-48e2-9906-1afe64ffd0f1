package com.aidt.api.error;

import com.aidt.api.at.err.BackErrLogService;
import com.aidt.api.at.err.dto.BackErrLogDto;
import com.aidt.common.CommonUserDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class ErrorControllerAdvice {

    @Autowired
    private BackErrLogService backErrLogService;

    @ExceptionHandler(ApiErrorLogException.class)
    public ResponseEntity<ErrorResponse> handleException(HttpServletRequest request, ApiErrorLogException exception) {
        log.error("e:: ", exception);

        BackErrLogDto backErrLogDto = buildBackErrLogDto(request, exception);
        backErrLogService.insertBackErrLog(backErrLogDto);

        ErrorResponse error = new ErrorResponse().returnErrorResponse(
                exception.getErrorCode().getCode(),
                exception.getErrorCode().getMessage()
        );

        return ResponseEntity
                .status(HttpStatus.OK)
                .body(error);

    }

    private BackErrLogDto buildBackErrLogDto(HttpServletRequest request, ApiErrorLogException exception) {
        CommonUserDetail userDetail = (CommonUserDetail) RequestContextHolder.currentRequestAttributes()
                .getAttribute("userDetail", RequestAttributes.SCOPE_REQUEST);
        String usrId = (userDetail != null && userDetail.getUsrId() != null) ? userDetail.getUsrId() : "";
        String optTxbId = (userDetail != null && userDetail.getOptTxbId() != null) ? userDetail.getOptTxbId() : "";

        String errClsNm = Optional.ofNullable(exception.getCause())
                .map(Throwable::getStackTrace)
                .filter(stack -> stack.length > 0)
                .map(stack -> stack[0].getMethodName())
                .orElseGet(() -> {
                    StackTraceElement[] stack = exception.getStackTrace();
                    return (stack.length > 0) ? stack[0].getMethodName() : "UNKNOWN";
                });

        String causeMsg = Optional.ofNullable(exception.getCause())
                .map(Throwable::getMessage)
                .orElse(exception.getMessage());

        if (causeMsg.length() > 1000) {
            causeMsg = causeMsg.substring(0, 1000);
        }

        String requestBody;
        try {
            requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
            requestBody = "Error reading request body: " + e.getMessage();
        }

        ErrorCode errorCode = exception.getErrorCode();

        return BackErrLogDto.of(
                request.getRequestURI(),
                errClsNm,
                causeMsg,
                errorCode.getCode(),
                exception.getErrSrcCd(),
                requestBody,
                usrId,
                optTxbId,
                exception.getStartTime()
        );
    }

}
