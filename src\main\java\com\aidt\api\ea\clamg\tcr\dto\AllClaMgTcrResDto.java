package com.aidt.api.ea.clamg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-10-07
 * @modify date 2024-10-07
 * @desc 전체 학급 관리 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AllClaMgTcrResDto {

	@Parameter(name="운영 교과서 ID")
	private String optTxbId;

	@Parameter(name="선생님 USRID")
	private String tcrUsrId;
	
	@Parameter(name="대단원 ID")
	private String lluNodId;

	@Parameter(name="학습맵 노드 ID")
	private String lrmpNodId;

    @Parameter(name="학습맵 노드명")
    private String lrmpNodNm;

	@Parameter(name="지식맵노드 ID")
	private String kmmpNodId;

	@Parameter(name="지식맵상위노드 ID")
	private String urnkKmmpNodId;

	@Parameter(name="재구성 번호")
	private int rcstnNo;

//	@Parameter(name="단원 진단 성취도")
//	private String avgRtUd;

	@Parameter(name="AI 맞춤 진단 성취도")
	private String avgRtOv;

	@Parameter(name="단원 평가 성취도")
	private String avgRtUg;
	
	@Parameter(name="학급 ID")
	private String claId;

	@Parameter(name="학급명")
	private String claNm;
	
	@Parameter(name="학급 전체 인원")
	private int usrTotCnt;

	@Parameter(name="교과학습 진도율")
	private double tlRatio;
	
	/*MA:수학 / EN:영어*/
	@Parameter(name="교과서 과목코드")	
	private String sbjCd;
	
	@Parameter(name="ai 영어학습 고정 갯수")
	private int aeCnt;
	
	@Parameter(name="ai학습 진도율")
	private String aiRatio;

	@Parameter(name="학습자 수준 (느린)")
	private String lrnrLevSl;

	@Parameter(name="학습자 수준 (보통)")
	private String lrnrLevNm;

	@Parameter(name="학습자 수준 (빠른)")
	private String lrnrLevFs;

	@Parameter(name="학습자 수준 (없음)")
	private String lrnrLevBa;

}
