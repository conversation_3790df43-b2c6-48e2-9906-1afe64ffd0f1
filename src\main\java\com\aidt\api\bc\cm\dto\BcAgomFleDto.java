/**
 * 
 */
package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-07-29 17:59:47
 * @modify 2024-07-29 17:59:47
 * @desc 알지오 메쓰파일 dto
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcAgomFleDto {

	@Parameter(name = "교과서ID")
	private String txbId;

	@Parameter(name = "알지오매쓰파일ID")
	private String agomFleId;

	@Parameter(name = "파일경로")
	private String flePthNm;

	@Parameter(name = "유형")
	private String vdDvCd;

}
