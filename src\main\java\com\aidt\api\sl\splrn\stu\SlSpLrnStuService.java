package com.aidt.api.sl.splrn.stu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.sl.common.SlCmUtil;
import com.aidt.api.sl.splrn.dto.SlSpLrnDtlBanrViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnDtlViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnLastPrgsDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnNodThbDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnPgrsDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnRcmdDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnScrsDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * @author:eunhye11
 * @email:<EMAIL>
 * @create : date 2024-03-06 08:24:20
 * @modify :date 2024-04-22 16:02:00
 * @desc : special student service
 */
@Slf4j
@Service
public class SlSpLrnStuService {
	private String MAPPER_NAMESPACE = "api.sl.splrn.stu.";

	@Autowired
	private CommonDao commonDao;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	/**
	 * 특별학습 목록 조회
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return slSpLrnMainViewDtoList
	 */
	@Transactional(readOnly = true)
	public List<SlSpLrnMainViewDto> selectSpLrnList(String optTxbId, String userId, String usrTpCd) {
		// 특별학습 목록 내용 리스트
		List<SlSpLrnMainViewDto> slSpLrnMainDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectSpLrnList",
				Map.of("optTxbId", optTxbId, "usrId", userId, "usrTpCd", usrTpCd));

		// 학습진행 상태를 구하기 위한 리스트
		List<SlSpLrnMainViewDto> slUrnkPgrsDto = commonDao.selectList(MAPPER_NAMESPACE + "selectUrnkPgrsDto",
				Map.of("optTxbId", optTxbId,"usrId", userId, "usrTpCd", usrTpCd));
		// 목록 view 내용 setting
		List<SlSpLrnMainViewDto> slSpLrnMainViewDtoList = new ArrayList<SlSpLrnMainViewDto>();
		for (SlSpLrnMainViewDto mainDto : slSpLrnMainDtoList) {
			int done = 0; // 상위노드 별 완료 갯수
			int entire = 0; // 특별학습 별 상위노드 총 갯수
			int ctnDone = 0;
			int ctnEntire = 0;
			
			for (SlSpLrnMainViewDto pgrsDto : slUrnkPgrsDto) {
				if (mainDto.getSpLrnId().equals(pgrsDto.getSpLrnId())) {
					entire++;
					ctnDone = ctnDone + pgrsDto.getDone();
					ctnEntire += pgrsDto.getEntire();
					if ("CL".equals(pgrsDto.getLrnStCd())) {
						done++;
					}
				}
			}
			
			SlSpLrnMainViewDto dto = new SlSpLrnMainViewDto();

			dto.setCtnDone(ctnDone);
			dto.setCtnEntire(ctnEntire);
			dto.setDone(done); // 완료 학습수
			dto.setEntire(entire); // 전체 학습수
			dto.setOptTxbId(mainDto.getOptTxbId()); // 운영교과서ID
			dto.setSpLrnId(mainDto.getSpLrnId()); // 특별학습ID
			dto.setLrnGoalCn(mainDto.getLrnGoalCn()); // 특별학습 목표
			dto.setSpLrnNm(mainDto.getSpLrnNm()); // 특별학습 명
			// dto.setMoPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, mainDto.getMoPath())); // 썸네일 모바일 경로
			dto.setTaPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, mainDto.getTaPath())); // 썸네일 태블릿 경로
			dto.setPcPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, mainDto.getPcPath())); // 썸네일 피씨 경로
			dto.setLrnStCd(mainDto.getLrnStCd()); // 노드별 학습상태

			slSpLrnMainViewDtoList.add(dto);
		}

		return slSpLrnMainViewDtoList;
	}

	/**
	 * 툭별학습 추천 목록 조회
	 * 
	 * @param userId
	 * @param optTxbId
	 * @return slSpLrnRcmViewList
	 */
//	@Transactional(readOnly = true)
//	public List<SlSpLrnRcmdDto> selectSpLrnStuRcmList(String userId, String optTxbId, String usrTpCd) {
//
//		// 과제 리스트
//		List<SlSpLrnRcmdDto> rcmdEaAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmdEaAsnList",
//				Map.of("userId", userId, "optTxbId", optTxbId, "usrTpCd", usrTpCd));
//		// 학습진행상태 총 리스트
//		List<SlSpLrnRcmdDto> rcmdLrnStCdList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmdList",
//				Map.of("userId", userId, "optTxbId", optTxbId));
//
//		// 상위노드별 학습진행상태 총 리스트
//		List<SlSpLrnRcmdDto> rcmdUrnkList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmdUrnkList",
//				Map.of("userId", userId, "optTxbId", optTxbId));
//
//		// 최초진입시 1depth 리스트
//		List<SlSpLrnRcmdDto> rcmdFirstTimeList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmdFirstTimeList",
//				Map.of("optTxbId", optTxbId));
//
//		// 마지막 순서 가져오기
//		List<SlSpLrnRcmdDto> rcmdLastTimeList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmdLastTimeList",
//				Map.of("userId", userId, "optTxbId", optTxbId));
//
//		// 단원 리스트
//		List<SlSpLrnRcmdDto> rcmdLluList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmdLluList",
//				Map.of("userId", userId, "optTxbId", optTxbId));
//
//		// 총 추천 리스트
//		List<SlSpLrnRcmdDto> rcmdAllList = new ArrayList<SlSpLrnRcmdDto>();
//		// 총 추천 리스트 갯수
//		int rcmdAllCount = rcmdAllList.size();
//		// 1. 과제 전체 넣기
//		rcmdAllList.addAll(rcmdEaAsnList.subList(0, Math.min(rcmdEaAsnList.size(), 4)));
//		rcmdAllCount = rcmdAllList.size();
//
//		// 진행중인 학습 체크
//		boolean dlCheck = false;
//		// boolean nlCheck = false;
//		// // 과제가 들어간 총 추천 리스트가 4개 미만일 때
//		if (rcmdAllCount < 4) {
//			// 1. 학습상태가 없을 때 (최초진입)
//			if (rcmdLrnStCdList.size() == 0) { // 최초진입
//
//				if (rcmdEaAsnList.size() > 0) { // 1-1. 최초진입이고 과제가 있을 경우
//
//					for (SlSpLrnRcmdDto first : rcmdFirstTimeList) {
//
//						boolean addCheck = true;
//
//						for (SlSpLrnRcmdDto eaAsn : rcmdEaAsnList) {
//							if (eaAsn.getUrnkSpLrnNodId().equals(first.getUrnkSpLrnNodId())) { // 만약 조건이 일치한다면
//								addCheck = false; // 추가하지 않음
//								break; // 더 이상 반복할 필요 없음
//							}
//						}
//						log.debug(">> addCheck:{}",addCheck);
//					}
//					// 과제리스트 넣은 후 남은 index만큼 1depth 총 리스트에 추가
//				} else {// 1-2. 최초진입이고, 과제가 없을 경우 - 1뎁스 리스트 전체 표시
//					// 1depth 전체 총 리스트에 추가
//					rcmdAllList.addAll(rcmdFirstTimeList);
//				}
//				// 2.학습상태가 있을 경우
//			} else if (rcmdLrnStCdList.size() > 0) {
//
//				// 2-1. 과제가 있을 경우
//				if (rcmdEaAsnList.size() > 0) {
//					// urnkSpLrnNodId 중복 비교 변수 선언
//					String beforeUrnkSpLrnNodId = "";
//					String urnkId = "";
//					List<String> dlIdList = new ArrayList<String>();
//					List<String> clIdList = new ArrayList<String>();
//					int clCount = 1; // cl 갯수8
//					for (SlSpLrnRcmdDto urnk : rcmdUrnkList) {
//						if (rcmdAllList.size() >= 4) {
//							break;
//						}
//						SlSpLrnRcmdDto clObj = rcmdLluList.stream()
//								.filter(idx -> idx.getSpLrnId().equals(urnk.getSpLrnId())).collect(Collectors.toList())
//								.get(0);
//						boolean addCheck = true; // 중복 체크
//						if (urnk.getLrnStCd().equals("CL")) {
//							clCount++;
//						}
//						log.debug(">> addCheck:{}",addCheck);
//
//						for (SlSpLrnRcmdDto eaAsn : rcmdEaAsnList) {
//							int urnkCount = rcmdUrnkList.size(); // 특별학습 상위노드 총 갯수
//							if ("DL".equals(urnk.getLrnStCd())
//									&& (!beforeUrnkSpLrnNodId.equals(urnk.getUrnkSpLrnNodId()))) { // DL이 있을 때
//								dlCheck = true;
//								rcmdAllList.add(urnk);
//								dlIdList.add(urnk.getSpLrnId());
//								beforeUrnkSpLrnNodId = urnk.getUrnkSpLrnNodId();
//							} else if ("CL".equals(urnk.getLrnStCd()) && !clObj.getLrnStCd().equals("CL")) {
//								// 완료학습 다음 단계 조회
//								SlSpLrnRcmdDto rcmdNotDoneDto = commonDao.select(MAPPER_NAMESPACE + "selectNotDoneDto",
//										Map.of("userId", userId, "optTxbId", optTxbId, "spLrnId", urnk.getSpLrnId(),
//												"srtOrdn", urnk.getSrtOrdn()));
//								// 전체 총 리스트가 비어있을 경우
//								if (rcmdNotDoneDto != null && rcmdAllList.size() == 0) {
//									if (!eaAsn.getUrnkSpLrnNodId().equals(rcmdNotDoneDto.getSpLrnNodId())) {
//										rcmdAllList.add(rcmdNotDoneDto); // 완료 다음 학습 add
//										urnkId = rcmdNotDoneDto.getSpLrnNodId(); // add된 학습의 상위노드 id를 갖고있는다.
//									}
//
//								} else {
//									if (rcmdNotDoneDto != null
//											&& !eaAsn.getUrnkSpLrnNodId().equals(rcmdNotDoneDto.getSpLrnNodId())
//											&& !rcmdNotDoneDto.getSpLrnNodId().equals(urnkId)
//											&& !dlIdList.contains(rcmdNotDoneDto.getSpLrnId())
//											&& !clIdList.contains(rcmdNotDoneDto.getSpLrnId())) {// 과제리스트
//
//										// 상위노드ID와
//										// 상위노드별
//										rcmdAllList.add(rcmdNotDoneDto); // 완료 다음 학습 add
//										clIdList.add(rcmdNotDoneDto.getSpLrnId());
//										urnkId = rcmdNotDoneDto.getSpLrnNodId(); // add된 학습의 상위노드 id를 갖고있는다.
//									} else if (rcmdNotDoneDto == null
//											&& !eaAsn.getUrnkSpLrnNodId().equals(urnk.getUrnkSpLrnNodId())
//											&& !urnk.getUrnkSpLrnNodId().equals(urnkId)
//											&& !dlIdList.contains(urnk.getSpLrnId())
//											&& !clIdList.contains(urnk.getSpLrnId())) {
//										String curLrnId = clObj.getSpLrnId();
//										List<SlSpLrnRcmdDto> curList = rcmdUrnkList.stream()
//												.filter(idx -> idx.getSpLrnId().equals(curLrnId))
//												.filter(idx1 -> idx1.getLrnStCd().equals("NL"))
//												.collect(Collectors.toList());
//										Collections.sort(curList, (a, b) -> {
//											return a.getSrtOrdn().compareTo(b.getSrtOrdn());
//										});
//										SlSpLrnRcmdDto firstObj = curList.get(0);
//										rcmdAllList.add(firstObj);
//										clIdList.add(firstObj.getSpLrnId());
//									}
//								}
//
//							} else if ("CL".equals(urnk.getLrnStCd()) && clObj.getLrnStCd().equals("CL")) {
//								String curLrnId = clObj.getSpLrnId();
//								List<SlSpLrnRcmdDto> curList = rcmdUrnkList.stream()
//										.filter(idx -> idx.getSpLrnId().equals(curLrnId))
//										.collect(Collectors.toList());
//								Collections.sort(curList, (a, b) -> {
//									return a.getSrtOrdn().compareTo(b.getSrtOrdn());
//								});
//								SlSpLrnRcmdDto curObj = curList.get(curList.size() - 1);
//								if (!eaAsn.getUrnkSpLrnNodId().equals(curObj.getUrnkSpLrnNodId())
//										&& !clIdList.contains(curObj.getSpLrnId())) {
//									rcmdAllList.add(curObj);
//									clIdList.add(curObj.getSpLrnId());
//								}
//							}
//
//							if (!dlCheck && clCount == urnkCount) { // 학습이 전부 완료상태일 때: DL(진행중)이 없고, 상위노드 갯수만큼
//								// CL(완료)갯수가
//								// 있을 때
//								int subListNum = rcmdAllList.size();
//								log.info("2332 : " + eaAsn.getMdfDtm());
//								// Comparator를 사용하여 mdfDtm을 기준으로 내림차순으로 정렬
//								// Collections.sort(rcmdLastTimeList,
//								// Comparator.comparing(SlSpLrnRcmdDto::getMdfDtm,
//								// Comparator.nullsLast()).reversed());
//								Collections.sort(rcmdLastTimeList, (o1, o2) -> {
//									Date date1 = o1.getMdfDtm();
//									Date date2 = o2.getMdfDtm();
//
//									if (date1 == null && date2 == null) {
//										return 0;
//									} else if (date1 == null) {
//										return 1; // null 값을 가장 뒤로 보냄
//									} else if (date2 == null) {
//										return -1; // null 값을 가장 뒤로 보냄
//									} else {
//										return date2.compareTo(date1); // 내림차순 정렬
//									}
//								});
//
//								// 정렬된 rcmdLastTimeList 중 상위 4개의 요소를 rcmdAllList에 추가
//								rcmdAllList.addAll(
//										rcmdLastTimeList.subList(0, Math.min(4 - subListNum, rcmdLastTimeList.size())));
//							}
//						}
//					}
//					// } else if (rcmdEaAsnList.size() == 0) {
//					//
//					// 2-2.과제가 없을 때
//				} else if (rcmdEaAsnList.size() == 0) {
//					int clCount = 0; // cl 갯수
//					String urnkId = "";
//					List<String> dlIdList = new ArrayList<String>();
//					List<String> clIdList = new ArrayList<String>();
//					for (SlSpLrnRcmdDto urnk : rcmdUrnkList) { // 상위노드별 학습진행상태 총 리스트
//						if (rcmdAllList.size() >= 4) {
//							break;
//						}
//						SlSpLrnRcmdDto clObj = rcmdLluList.stream()
//								.filter(idx -> idx.getSpLrnId().equals(urnk.getSpLrnId())).collect(Collectors.toList())
//								.get(0);
//						int urnkCount = rcmdUrnkList.size(); // 특별학습 상위노드 총 갯수
//						if (urnk.getLrnStCd().equals("CL")) {
//							clCount++;
//						}
//						if ("DL".equals(urnk.getLrnStCd())) { // DL이 있을 때
//							dlCheck = true;
//							rcmdAllList.add(urnk);
//							dlIdList.add(urnk.getSpLrnId());
//						} else if ("CL".equals(urnk.getLrnStCd()) && !clObj.getLrnStCd().equals("CL")) {
//
//							// 완료학습 다음 단계 조회
//							SlSpLrnRcmdDto rcmdNotDoneDto = commonDao.select(MAPPER_NAMESPACE + "selectNotDoneDto",
//									Map.of("userId", userId, "optTxbId", optTxbId, "spLrnId", urnk.getSpLrnId(),
//											"srtOrdn", urnk.getSrtOrdn()));
//							// 전체 총 리스트가 비어있을 경우
//							if (rcmdNotDoneDto != null && rcmdAllList.size() == 0) {
//								rcmdAllList.add(rcmdNotDoneDto); // 완료 다음 학습 add
//								urnkId = rcmdNotDoneDto.getSpLrnNodId(); // add된 학습의 상위노드 id를 갖고있는다.
//							} else {
//								// urnkId와 완료학습 다음리스트의 상위노드가 같지 않을 때만 리스트 추가(중복방지)
//								if (rcmdNotDoneDto != null && !urnkId.equals(rcmdNotDoneDto.getSpLrnNodId())
//										&& !dlIdList.contains(rcmdNotDoneDto.getSpLrnId())
//										&& !clIdList.contains(rcmdNotDoneDto.getSpLrnId())) {
//									rcmdAllList.add(rcmdNotDoneDto);
//									urnkId = rcmdNotDoneDto.getSpLrnNodId();
//									clIdList.add(rcmdNotDoneDto.getSpLrnId());
//								} else if (rcmdNotDoneDto == null
//										&& !urnk.getUrnkSpLrnNodId().equals(urnkId)
//										&& !dlIdList.contains(urnk.getSpLrnId())
//										&& !clIdList.contains(urnk.getSpLrnId())) {
//									String curLrnId = clObj.getSpLrnId();
//									List<SlSpLrnRcmdDto> curList = rcmdUrnkList.stream()
//											.filter(idx -> idx.getSpLrnId().equals(curLrnId))
//											.filter(idx1 -> idx1.getLrnStCd().equals("NL"))
//											.collect(Collectors.toList());
//									Collections.sort(curList, (a, b) -> {
//										return a.getSrtOrdn().compareTo(b.getSrtOrdn());
//									});
//									SlSpLrnRcmdDto firstObj = curList.get(0);
//									rcmdAllList.add(firstObj);
//									clIdList.add(firstObj.getSpLrnId());
//								}
//
//							}
//						} else if ("CL".equals(urnk.getLrnStCd()) && clObj.getLrnStCd().equals("CL")) {
//							String curLrnId = clObj.getSpLrnId();
//							List<SlSpLrnRcmdDto> curList = rcmdUrnkList.stream()
//									.filter(idx -> idx.getSpLrnId().equals(curLrnId))
//									.collect(Collectors.toList());
//							Collections.sort(curList, (a, b) -> {
//								return a.getSrtOrdn().compareTo(b.getSrtOrdn());
//							});
//							SlSpLrnRcmdDto curObj = curList.get(curList.size() - 1);
//							if (!clIdList.contains(curObj.getSpLrnId())) {
//								rcmdAllList.add(curObj);
//								clIdList.add(curObj.getSpLrnId());
//							}
//						}
//						// else if ("CL".equals(urnk.getLrnStCd()) && clObj.getLrnStCd().equals("CL")) {
//						// int curIdx = rcmdLluList.indexOf(clObj);
//						// if (curIdx + 1 != rcmdLluList.size()
//						// && rcmdLluList.get(curIdx + 1).getLrnStCd().equals("NL")) {
//						// String nextLrnId = rcmdLluList.get(curIdx + 1).getSpLrnId();
//						// SlSpLrnRcmdDto nextObj = rcmdUrnkList.stream()
//						// .filter(idx -> idx.getSpLrnId().equals(nextLrnId))
//						// .collect(Collectors.toList()).get(0);
//						// if (!clIdList.contains(nextObj.getSpLrnId())) {
//						// rcmdAllList.add(nextObj);
//						// clIdList.add(nextObj.getSpLrnId());
//						// }
//						// }
//						// }
//						if (!dlCheck && clCount == urnkCount) { // 학습이 전부 완료상태일 때: DL(진행중)이 없고, 상위노드 갯수만큼 CL(완료)갯수가 있을 때
//							int subListNum = rcmdAllList.size();
//							// Comparator를 사용하여 mdfDtm을 기준으로 내림차순으로 정렬
//							// Collections.sort(rcmdLastTimeList,
//							// Comparator.comparing(SlSpLrnRcmdDto::getMdfDtm).reversed());
//							Collections.sort(rcmdLastTimeList, (o1, o2) -> {
//								Date date1 = o1.getMdfDtm();
//								Date date2 = o2.getMdfDtm();
//
//								if (date1 == null && date2 == null) {
//									return 0;
//								} else if (date1 == null) {
//									return 1; // null 값을 가장 뒤로 보냄
//								} else if (date2 == null) {
//									return -1; // null 값을 가장 뒤로 보냄
//								} else {
//									return date2.compareTo(date1); // 내림차순 정렬
//								}
//							});
//
//							// 정렬된 rcmdLastTimeList 중 상위 4개의 요소를 rcmdAllList에 추가
//							rcmdAllList.addAll(
//									rcmdLastTimeList.subList(0, Math.min(4 - subListNum, rcmdLastTimeList.size())));
//						}
//
//					}
//
//				}
//				// }
//				// 총 추천리스트가 비어있을 때
//			} else if (rcmdAllList.size() == 0) {
//				// 1depth 전체 노출
//				rcmdAllList.addAll(rcmdFirstTimeList);
//			}
//
//		}
//		return rcmdAllList;
//	}

	/**
	 * 특별학습 상세 - 배너내용
	 * 
	 * @param spLrnId
	 * @param userId
	 * @param optTxbId
	 * @return slSpLrnDtlBanrDto 상단 배너 내용
	 */
	@Transactional(readOnly = true)
	public SlSpLrnDtlBanrViewDto selectSpLrnDtlBanrViewDto(String spLrnId, String userId, String optTxbId) {
		// 상세내용
		List<SlSpLrnDtlBanrViewDto> slSpLrnDtlBanrList = commonDao.selectList(
				MAPPER_NAMESPACE + "selectSpLrnDtlBanrViewDto",
				Map.of("spLrnId", spLrnId, "userId", userId, "optTxbId", optTxbId));
		// 스크린샷
		List<SlSpLrnScrsDto> bcSpLrnScrsList = commonDao.selectList(MAPPER_NAMESPACE + "selectSpLrnScrsList", spLrnId);
		for (SlSpLrnScrsDto slSpLrnScrsDto : bcSpLrnScrsList) {
			slSpLrnScrsDto.setFlePthNm(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, slSpLrnScrsDto.getFlePthNm()));
		}
		int done = 0;
		int entire = 0;

		// 배너 내용 setting
		SlSpLrnDtlBanrViewDto banerDto = new SlSpLrnDtlBanrViewDto();
		for (SlSpLrnDtlBanrViewDto dtlDto : slSpLrnDtlBanrList) {
			if("CL".equals(dtlDto.getLrnStCd())) {
				done++;
			}
			entire++;
			banerDto.setScrsList(bcSpLrnScrsList); // 스크린샷 리스트
			banerDto.setSpLrnNm(dtlDto.getSpLrnNm()); // 특별학습 노드명
			banerDto.setLrnGoalCn(dtlDto.getLrnGoalCn()); // 특별학습 목표
			banerDto.setSpLrnNm(dtlDto.getSpLrnNm()); // 특별학습명
			banerDto.setSpLrnId(spLrnId); // 특별학습ID
		}
		banerDto.setDone(done); // 학습 완료 상태 갯수
		banerDto.setEntire(entire); // 특별학습별 콘텐츠 갯수
		// 진행상황
		// List<SlSpLrnLastPrgsDto> slSpLrnPgrsDtoList = commonDao.selectList(
		// MAPPER_NAMESPACE + "selectSlSpLrnPgrsStCdList",
		// Map.of("spLrnId", spLrnId, "userId", userId, "optTxbId", optTxbId));
		// slSpLrnDtlBanrDto.setPgrsList(slSpLrnPgrsDtoList);

		return banerDto;
	}

	/**
	 * (v3.1)특별학습 상세 - 상세리스트
	 * 
	 * @param spLrnId
	 * @param userId
	 * @param optTxbId
	 * @return selectSlSpLrnDtlViewList 상세목록리스트
	 */
	@Transactional(readOnly = true)
	public List<SlSpLrnDtlViewDto> selectSpDtlList(String spLrnId, String usrId, String optTxbId) {
		List<SlSpLrnDtlViewDto> totalSlSpLrnDtlList = commonDao.selectList(
				MAPPER_NAMESPACE + "selectSlSpLrnDtlList",
				Map.of("spLrnId", spLrnId, "userId", usrId, "optTxbId", optTxbId));

		List<SlSpLrnDtlViewDto> spLrnDtlViewList = totalSlSpLrnDtlList.stream() //1depth
				.filter(idx -> idx.getUrnkSpLrnNodId() == null).collect(Collectors.toList());

		List<SlSpLrnDtlViewDto> spLrnDtlList = totalSlSpLrnDtlList.stream()
				.filter(idx -> idx.getUrnkSpLrnNodId() != null).collect(Collectors.toList());

		// 썸네일 리스트 조회
		List<SlSpLrnNodThbDto> bcSpLrnNodThbList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcSpLrnNodThbList",
				spLrnId);

        spLrnDtlViewList.stream().forEach(idx -> { // 1depth
			if (idx.getLwsYn().equals("Y")) {
				List<SlSpLrnPgrsDto> pgrsList = new ArrayList<>();
				SlSpLrnPgrsDto pgrsDto = new SlSpLrnPgrsDto();
				pgrsDto.setSplrnCtnId(idx.getSplrnCtnId()); // 컨텐츠 아이디
				pgrsDto.setLrnStCd(idx.getLrnStCd()); // 특별학습 진행 상태
				pgrsDto.setOptTxbId(optTxbId); // 운영교과서ID
				pgrsDto.setLrnusrId(usrId); // userId
				pgrsList.add(pgrsDto);
				idx.setSlSpLrnPgrsList(pgrsList);
				idx.setDone((int) this.setDoneEntire(pgrsList).get("done"));
				idx.setEntire((int) this.setDoneEntire(pgrsList).get("entire"));
			} else {
				List<SlSpLrnPgrsDto> pgrsList = new ArrayList<>();
				spLrnDtlList.stream().filter(list1 -> list1.getUrnkSpLrnNodId().equals(idx.getSpLrnNodId()))
						.forEach(idx2 -> { // 2depth
							if (idx2.getLwsYn().equals("Y")) {
								SlSpLrnPgrsDto pgrsDto = this.setSpLrnPgrsDto(idx2.getSplrnCtnId(), idx2.getLrnStCd(),
										optTxbId, usrId);
								pgrsList.add(pgrsDto);
							} else {
								spLrnDtlList.stream()
										.filter(list2 -> list2.getUrnkSpLrnNodId().equals(idx2.getSpLrnNodId()))
										.forEach(idx3 -> { // 3depth
											if (idx3.getLwsYn().equals("Y")) {
												SlSpLrnPgrsDto pgrsDto = this.setSpLrnPgrsDto(idx3.getSplrnCtnId(),
														idx3.getLrnStCd(), optTxbId, usrId);
												pgrsList.add(pgrsDto);
											} else {
												spLrnDtlList.stream().filter(
														list3 -> list3.getUrnkSpLrnNodId().equals(idx3.getSpLrnNodId()))
														.forEach(idx4 -> { // 4depth
															if (idx4.getLwsYn().equals("Y")) {
																SlSpLrnPgrsDto pgrsDto = this.setSpLrnPgrsDto(
																		idx4.getSplrnCtnId(), idx4.getLrnStCd(),
																		optTxbId, usrId);
																pgrsList.add(pgrsDto);
															}
														});
											}
										});
							}
						});
				idx.setSlSpLrnPgrsList(pgrsList);
				idx.setDone((int) this.setDoneEntire(pgrsList).get("done"));
				idx.setEntire((int) this.setDoneEntire(pgrsList).get("entire"));
			}

			List<SlSpLrnNodThbDto> thbList = bcSpLrnNodThbList.stream()
					.filter(thb -> idx.getSpLrnNodId() != null
							&& idx.getSpLrnNodId().equals(thb.getSpLrnNodId()))
					.map(thb -> {
						SlSpLrnNodThbDto dto = new SlSpLrnNodThbDto();
						dto.setSpLrnNodId(thb.getSpLrnNodId()); // 특별학습 노드ID
						dto.setMoFleTpCd(thb.getMoFleTpCd()); // 모바일파일유형코드
						dto.setMoPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, thb.getMoPath())); // 모바일 경로
						dto.setPcFleTpCd(thb.getPcFleTpCd()); // 피씨모바일유형코드
						dto.setPcPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, thb.getPcPath())); // 피씨 경로
						dto.setTaFleTpCd(thb.getTaFleTpCd()); // 태블릿유형코드
						dto.setTaPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, thb.getTaPath())); // 태플릿 경로
						return dto;
					}).collect(Collectors.toList());
			idx.setBcSplrnNodThbList(thbList);
		});
        
        List<SlSpLrnDtlViewDto> result = spLrnDtlViewList.stream().filter(idx -> idx.getSlSpLrnPgrsList().size() > 0).collect(Collectors.toList());
        
		return result;
	}

	/**
	 * 상세목록 학습상태 셋팅
	 * 
	 * @param ctnId
	 * @param stCd
	 * @param userId
	 * @param optTxbId
	 * @return SlSpLrnPgrsDto
	 */
	private SlSpLrnPgrsDto setSpLrnPgrsDto(String ctnId, String stCd, String optTxbId, String usrId) {
		SlSpLrnPgrsDto pgrsDto = new SlSpLrnPgrsDto();
		pgrsDto.setSplrnCtnId(ctnId);
		pgrsDto.setLrnStCd(stCd);
		pgrsDto.setOptTxbId(optTxbId);
		pgrsDto.setLrnusrId(usrId);
		return pgrsDto;
	}

	/**
	 * 목록 별 컨텐츠 수, 완료 수
	 * 
	 * @param List<SlSpLrnPgrsDto>
	 * @return Map<String, Object>
	 */
	private Map<String, Object> setDoneEntire(List<SlSpLrnPgrsDto> list) {
		Map<String, Object> result = new HashMap<String, Object>();
		int done = list.stream().filter(idx -> idx.getLrnStCd().equals("CL")).collect(Collectors.toList()).size();
		int entire = list.size();
		result.put("done", done);
		result.put("entire", entire);
		return result;
	}

	// /**
	// * 특별학습 콘텐츠Id select
	// *
	// * @param spLrnNodId
	// * @return BcSpLrnCtnList 특별학습콘텐츠- 특별학습노드Id, 특별학습콘텐츠Id
	// */
	// @Transactional(readOnly = true)
	// public List<SlSpLrnCtnDto> selectBcSpLrnCtnList(String spLrnNodId) {
	// List<SlSpLrnCtnDto> bcSpLrnCtnList = commonDao.selectList(MAPPER_NAMESPACE +
	// "selectBcSpLrnCtnList",
	// spLrnNodId);
	// return bcSpLrnCtnList;
	// }

}
