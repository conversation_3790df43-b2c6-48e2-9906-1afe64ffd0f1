package com.aidt.api.al.pl.stu;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-20 
 * @modify date 2024-02-20 
 * @desc AI맞춤학습 단원차시조회
 */
//@Slf4j
@Tag(name="[al] AI맞춤학습 단원차시조회", description="AI맞춤학습 단원차시조회 조회")
@RestController
@RequestMapping("/api/v1/al/stu/mluTcLstInq")
public class AlMluTcLstInqStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private AlMluTcLstInqStuService mluTcLstInqStuService;
	
	//@Autowired 
	//private JwtProvider jwtProvider;
	
    /**
     * 학생 중단원목록 조회 controller 작업중
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="중단원목록 조회", description="중단원 목록 조회 컨트롤러")
    @PostMapping(value = "/selectMluLstInq")
    public ResponseDto<List<AlMluTcLstInqStuResponseDto>> selectMluLstInq(@Valid @RequestBody AlMluTcLstInqStuReqDto reqDto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(reqDto.getUsrId() == null) {
    		reqDto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(reqDto.getOptTxbId() == null) {
    		reqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(mluTcLstInqStuService.selectMluLstStuInq(reqDto));
    }
    
    /**
     * 학생 맞춤학습별 평균정답률 조회
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
    @Operation(summary="맞춤학습별 평균정답률 조회", description="맞춤학습별 평균정답률 조회 컨트롤러")
    @PostMapping(value = "/selectStpAvgCansRt")
    public ResponseDto<AlMluTcLstInqStuResponseDto> selectStpAvgCansRt(@Valid @RequestBody AlMluTcLstInqStuReqDto reqDto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(reqDto.getUsrId() == null) {
    		reqDto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	return Response.ok(mluTcLstInqStuService.selectStpAvgCansRt(reqDto));
    }
}
