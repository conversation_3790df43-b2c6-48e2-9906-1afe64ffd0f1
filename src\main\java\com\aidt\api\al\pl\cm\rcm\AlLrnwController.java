package com.aidt.api.al.pl.cm.rcm;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlLrnwHtmlTypeReqDto;
import com.aidt.api.al.pl.dto.AlLrnwReqDto;
import com.aidt.api.al.pl.dto.AlLrnwResponseDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI맞춤 학습창연계처리 서비스
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/cm/lrnw")
@Tag(name="[al] AI맞춤 학습창연계처리 서비스", description="AI맞춤 학습창연계처리 서비스(학생)")
public class AlLrnwController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private AlLrnwService alLrnwService;
	
	@Tag(name="[al] AI맞춤 목차및 컨텐츠리스트 조회", description="AI맞춤 목차및 컨텐츠리스트 조회")
    @PostMapping(value = "/selectLrnwInfo")
    public ResponseDto<AlLrnwResponseDto> selectLrnwInfo(@Valid @RequestBody AlLrnwReqDto dto) {
        log.debug("Entrance selectLrnTocAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        dto.setOptTxbId(userDetails.getOptTxbId());
        dto.setUsrId(userDetails.getUsrId());
        return Response.ok(alLrnwService.selectLrnwInfo(dto));
    }
	
	@Tag(name="[al] AI맞춤 학습활동 저장", description="AI맞춤 학습활동 저장(")
    @PostMapping(value = "/updateAiLrnAtvSt")
    public ResponseDto<Integer> updateAiLrnAtvSt(@Valid @RequestBody AlLrnwReqDto dto, HttpServletRequest request) {
        log.debug("Entrance selectLrnTocAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        dto.setOptTxbId(userDetails.getOptTxbId());
        dto.setUsrId(userDetails.getUsrId());
        return Response.ok(alLrnwService.updateAiLrnAtvSt(dto, request));
    }
	
	
	@Tag(name="[al] AI맞춤 학습창 내 문항별 정보조회(임시 테스트용)", description="AI맞춤 학습창 내 문항별 정보조회")
    @PostMapping(value = "/selectQtmInfo")
    public ResponseDto<String> selectQtmInfo(@Valid @RequestBody AlLrnwReqDto dto) {
        log.debug("selectQtmInfo");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId());
		dto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(alLrnwService.selectQtmInfo(dto));
    }
	
	
	@Tag(name="[al] AI맞춤 학습창 HTML 정오 저장", description="AI맞춤 학습창 내 HTML문항에 대한 정오를 저장한다.")
    @PostMapping(value = "/updateEvQtmCansYn")
	public ResponseDto<Integer> updateEvQtmCansYn(@Valid @RequestBody AlLrnwHtmlTypeReqDto req){
		log.debug("Entrance updateEvQtmCansYn");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        req.setUsrId(userDetails.getUsrId());
		return Response.ok(alLrnwService.updateEvQtmCansYn(req));
	}
	
	
	@Tag(name="[al] AI맞춤 학습창 이어하기", description="(AI학습창 실행시 최초1회 호출) AI맞춤 학습창 - 현재평가와 단원 내 이전평가 의 문항 정오정보를 조회한다.")
    @PostMapping(value = "/selectContinueLrnwInfo")
	public ResponseDto<Map<String, Object>> selectContinueLrnwInfo(@Valid @RequestBody AiRcmTsshQtmDto req) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(req.getOptTxbId() == null) {
			req.setOptTxbId(userDetails.getOptTxbId());
		}
		if(req.getUsrId() == null) {
			req.setUsrId(userDetails.getUsrId());
		}
		if(req.getTxbId() == null) {
			req.setTxbId(userDetails.getTxbId());
		}
		return Response.ok(alLrnwService.selectContinueLrnwInfo(req));
	}
	
	
	@Tag(name="[al] AI맞춤 학습창 단원 전체 정답수 조회", description="단원 전체 정답수와 오답수를 조회한다.")
    @PostMapping(value = "/selectLuCansCnt")
	public ResponseDto<Map<String, Integer>> selectLuCansCnt(@Valid @RequestBody AiRcmTsshQtmDto req) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(req.getOptTxbId() == null) {
			req.setOptTxbId(userDetails.getOptTxbId());
		}
		if(req.getUsrId() == null) {
			req.setUsrId(userDetails.getUsrId());
		}
		return Response.ok(alLrnwService.selectLuCansCnt(req));
	}
	
	
	
	@Tag(name="[al] AI맞춤 개념영상 정보 조회 (레이어팝업)", description="AI맞춤 개념영상 정보 조회 (레이어팝업)")
    @PostMapping(value = "/selectVdInfo")
	public ResponseDto<Map<String, Object>> selectVdInfo(@Valid @RequestBody AlLrnwReqDto req) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(req.getOptTxbId() == null) {
			req.setOptTxbId(userDetails.getOptTxbId());
		}
		
		return Response.ok(alLrnwService.selectVdInfo(req));
	}
	

}
