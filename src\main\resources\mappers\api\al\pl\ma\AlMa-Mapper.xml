<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.ma">
	
	
	<!-- 수학 중단원 목록 -->
	<select id="selectMluLstInqStu" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectMluLstInq */
		SELECT
		    DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID,
		    MAX(DPTH1.KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
		    DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
		    MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
		    DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
		    MAX(DPTH4.KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
		    DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
		    MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
		    MAX(DPTH1.TC_EPS_YN) AS TC_EPS_YN,
		    MAX(DPTH1.TC_USE_YN) AS TC_USE_YN,
		    MAX(DPTH1.RCSTN_ORDN) AS RCSTN_ORDN,
		    MAX(DPTH1.ORGL_ORDN) AS ORGL_ORDN,
		    MAX(DPTH1.USE_YN) AS USE_YN,
		    MAX(DPTH1.LCKN_YN) AS LCKN_YN,
		    IFNULL(MAX(AUTP.TPC_AVN), 0.5) AS TPC_AVN,
		    MAX(BALI.LU_IMG_PTH) AS LU_IMG_PTH,
		    MAX(OV.OV_QTM_CNT) AS OV_QTM_CNT,
		    IFNULL(MAX(ALL2.LRNR_VEL_TP_CD), 'NM') AS LRNR_VEL_TP_CD
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
		INNER JOIN LMS_CMS.BC_KMMP_NOD BKN
		    ON DPTH1.KMMP_NOD_ID = BKN.KMMP_NOD_ID
		    AND BKN.LU_EPS_YN = 'Y'
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
		    ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
		    AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		    AND DPTH2.DPTH = 2
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
		    ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
		    AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
		    AND DPTH3.DPTH = 3
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
		    ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
		    AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
		    AND DPTH4.DPTH = 4
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
		    ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
		    AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		    AND DPTH5.DPTH = 5
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
		    ON DPTH5.KMMP_NOD_ID = AUTP.TPC_ID
		    AND AUTP.USR_ID = #{usrId}
		    AND AUTP.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2
		    ON ALL2.USR_ID = #{usrId}
		    AND ALL2.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		    AND ALL2.LU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
		LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
		    ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
		    AND BALAC.CTN_TP_CD = 'QU'
		    AND BALAC.DEL_YN = 'N'
		LEFT OUTER JOIN (
		    SELECT
		        BE.MLU_NOD_ID,
		        COUNT(*) AS OV_QTM_CNT
		    FROM LMS_CMS.BC_EVSH BE
		    INNER JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM
		        ON BE.EVSH_ID = BEQM.EVSH_ID
		    WHERE BE.EVSH_TP_CD = 'AI'
		        AND BE.DEL_YN = 'N'
		        AND BE.USE_YN = 'Y'
		    GROUP BY BE.MLU_NOD_ID
		) OV
		    ON OV.MLU_NOD_ID = DPTH2.KMMP_NOD_ID
		LEFT OUTER JOIN LMS_CMS.BC_AI_LU_IMG BALI
		    ON BALI.LU_NOD_ID = DPTH2.KMMP_NOD_ID
		WHERE DPTH1.OPT_TXB_ID = #{optTxbId}
		    AND DPTH1.DPTH = 1
		    AND DPTH1.USE_YN = 'Y'
		    AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY DPTH1.KMMP_NOD_ID, DPTH2.KMMP_NOD_ID, DPTH4.KMMP_NOD_ID, DPTH5.KMMP_NOD_ID
		ORDER BY MAX(DPTH1.RCSTN_ORDN), MAX(DPTH1.ORGL_ORDN), 
         MAX(DPTH2.RCSTN_ORDN), MAX(DPTH2.ORGL_ORDN), 
         MAX(DPTH3.RCSTN_ORDN), MAX(DPTH3.ORGL_ORDN), 
         MAX(DPTH4.RCSTN_ORDN), MAX(DPTH4.ORGL_ORDN), 
         MAX(DPTH5.RCSTN_ORDN), MAX(DPTH5.ORGL_ORDN)
			
		/* AI맞춤 학생 중단원 목록 조회 - 김현혜 - ApPlStu-Mapper.xml - selectMluLstInqStu */
	</select>
	
	
	<!-- 학생 평가 정보 -->
	<select id="selectAeEvInfoList" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectAeEvInfoList */
		SELECT 
		    EE.EV_ID,
		    MAX(EAETR.MLU_KMMP_NOD_ID) MLU_KMMP_NOD_ID,
		    MAX(EE.EV_DV_CD) AS EV_DV_CD,
		    MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
		    MAX(EER.EV_CMPL_YN) AS EV_CMPL_YN,
		    IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS luevCmplYn,
		    MAX(EER.MDF_DTM) AS MDF_DTM,
		    ROW_NUMBER() OVER (PARTITION BY EE.EV_ID ORDER BY MAX(EER.MDF_DTM) DESC) AS mdfDtmOrder,
		    MAX(EE.LCKN_YN) AS LCKN_YN
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR 
		    ON EAETR.MLU_KMMP_NOD_ID = AKNR.KMMP_NOD_ID 
		    AND AKNR.DPTH = 2 
		    AND AKNR.OPT_TXB_ID = #{optTxbId}
		WHERE EAETR.OPT_TXB_ID = #{optTxbId}
			AND EER.USR_ID = #{usrId}
			AND EE.EV_DV_CD = 'AE'
			AND AKNR.USE_YN = 'Y'
			AND ee.EV_DTL_DV_CD in ('OV', 'C1', 'C2')
		GROUP BY EE.EV_ID
		ORDER BY MAX(EAETR.MLU_KMMP_NOD_ID), MAX(EE.EV_DTL_DV_CD)
		/* AI맞춤 학생 중단원 목록 조회 - 김현혜 - ApPlStu-Mapper.xml - selectAeEvInfoList */
	</select>
	
	<!-- 학생 맞춤학습별 평균정답률 조회-->
	<select id="selectStpAvgCansRt" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectStpAvgCansRt */
		select straight_join
			IFNULL(count(eeqa.qtm_id), 0) as qtmCnt,
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C1'), 0) as qtmC1Cnt,
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C1' and eeqa.CANS_YN = 'Y'), 0) as c1CansCnt, -- c1 맞춘갯수
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C2'), 0) as qtmC2Cnt,
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C2' and eeqa.CANS_YN = 'Y'), 0) as c2CansCnt -- c2 맞춘갯수
		FROM lms_lrm.ai_kmmp_nod_rcstn aknr
		inner join lms_lrm.EA_AI_EV_TS_RNGE EAETR 
		on EAETR.MLU_KMMP_NOD_ID = aknr.KMMP_NOD_ID 
		inner join LMS_LRM.ea_ev EE
		on ee.ev_id = EAETR.ev_id
		inner join LMS_LRM.ea_ev_rs eer
		on eer.ev_id = ee.ev_id
		inner join LMS_LRM.ea_ev_qtm_anw eeqa
		on eeqa.ev_id = ee.ev_id
		JOIN LMS_CMS.BC_AI_CTN_META_DATA MD
		ON eeqa.QTM_ID = MD.CTN_CD
		where eer.usr_id = #{usrId}
		and aknr.kmmp_nod_id = #{mkLuLrmNodId}

		/* AI맞춤 학생 맞춤학습별 평균정답률 조회 - 김현혜 - ApPlStu-Mapper.xml - selectStpAvgCansRt */
	</select>
	
	<!-- 학생 맞춤학습별 토픽숙련도 조회-->
	<select id="selectQtmCansYnByTpcStu" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectQtmCansYnByTpcStu */
		SELECT 
		    ANW.USR_ID,
		    QTM.TPC_ID AS TPC_KMMP_NOD_ID,
		    BALAC.CTN_CD AS QTM_ID,
		    MAX(ANW.CANS_YN) AS CANS_YN,
		    IFNULL(MAX(AUQP.TXM_PN), 0) AS TXM_PN,
		    MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD,
		    IFNULL(MAX(AUTP.AI_PRED_AVG_CANS_RT), 0) AS AI_PRED_AVG_CANS_RT,
		    IFNULL(MAX(AUTP.AI_PRED_AVG_SCR), 0.5) AS AI_PRED_AVG_SCR
		FROM LMS_LRM.EA_EV_QTM QTM
		INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC 
		    ON QTM.TPC_ID = BALAC.KMMP_NOD_ID
		    AND BALAC.DEL_YN = 'N'
		INNER JOIN LMS_CMS.QP_QTM QQ 
		    ON BALAC.CTN_CD = QQ.QP_QTM_ID
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW ANW 
		    ON QTM.QTM_ID = ANW.QTM_ID 
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP 
		    ON QTM.TPC_ID = AUTP.TPC_ID
		    AND ANW.USR_ID = AUTP.USR_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP 
		    ON BALAC.CTN_CD = AUQP.QTM_ID
		    AND ANW.USR_ID = AUQP.USR_ID
		WHERE QTM.EV_ID = #{evId}
		    AND ANW.USR_ID = #{usrId}
		    AND BALAC.CTN_TP_CD = 'QU'
		GROUP BY 
		    ANW.USR_ID,
		    QTM.TPC_ID,
		    BALAC.CTN_CD
		ORDER BY 
		    QTM.TPC_ID, 
		    BALAC.CTN_CD, 
		    MAX(ANW.CANS_YN)

		/* AI맞춤 학생 맞춤학습별 토픽숙련도 조회 - 김현혜 - ApPlStu-Mapper.xml - selectQtmCansYnByTpcStu */
	</select>
	
	<select id="selectTpcMpnList" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AlTpcMpnDto">
		SELECT
		    DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
		    MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
		    IFNULL(MAX(AUTP.TPC_AVN), 0.5) AS TPC_AVN,
		    ATKNM.LNKG_TPC_KMMP_NOD_ID,
		    MAX(LNKGTPC.KMMP_NOD_NM) AS LNKG_TPC_KMMP_NOD_NM,
		    MAX(ATKNM.TPC_LNKG_DV_CD) AS TPC_LNKG_DV_CD
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
		        ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
		        AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		        AND DPTH2.DPTH = 2
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
		        ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
		        AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
		        AND DPTH3.DPTH = 3
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
		        ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
		        AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
		        AND DPTH4.DPTH = 4
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
		        ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
		        AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		        AND DPTH5.DPTH = 5
		    LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
		        ON DPTH5.KMMP_NOD_ID = AUTP.TPC_ID
		        AND AUTP.USR_ID = #{usrId}
		    LEFT OUTER JOIN (
		        SELECT
		            ATKNM.MLU_KMMP_NOD_ID,
		            ATKNM.TPC_KMMP_NOD_ID,
		            ATKNM.TPC_LNKG_DV_CD,
		            ATKNM.LNKG_TPC_KMMP_NOD_ID
		        FROM LMS_CMS.BC_AI_TPC_KMMP_NOD_MPN ATKNM
		        GROUP BY ATKNM.MLU_KMMP_NOD_ID, ATKNM.TPC_KMMP_NOD_ID, ATKNM.LNKG_TPC_KMMP_NOD_ID, ATKNM.TPC_LNKG_DV_CD
		    ) ATKNM
		        ON DPTH2.KMMP_NOD_ID = ATKNM.MLU_KMMP_NOD_ID
		        AND DPTH5.KMMP_NOD_ID = ATKNM.TPC_KMMP_NOD_ID
		    LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
		        ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
		        AND BALAC.CTN_TP_CD = 'QU'
		        AND BALAC.DEL_YN = 'N'
		    LEFT OUTER JOIN LMS_CMS.BC_KMMP_NOD LNKGTPC
		        ON LNKGTPC.KMMP_NOD_ID = ATKNM.LNKG_TPC_KMMP_NOD_ID
		WHERE DPTH1.DPTH = 1
		    AND DPTH1.OPT_TXB_ID = #{optTxbId}
		    AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
		    AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY DPTH5.KMMP_NOD_ID, ATKNM.LNKG_TPC_KMMP_NOD_ID
		ORDER BY DPTH5.KMMP_NOD_ID
	
		
		/* AI맞춤 학생 수학 맞춤학습 개념맵 조회 - 이혜인 - ApPlStu-Mapper.xml - selectTpcMpnList */
	</select>
	
	<select id="selectTpcMpnOvList" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AlTpcMpnDto">
		SELECT
			MAX(DPTH4.kmmp_nod_id) AS TC_KMMP_NOD_ID,
			MAX(dpth4.kmmp_nod_nm) AS TC_KMMP_NOD_NM,
		    DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
		    MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
		    ifnull(MAX(AUTLO.AI_DGN_EV_TPC_AVN),0) AS AI_DGN_EV_TPC_AVN,
		     ifnull(MAX(AUTLO.AI_DGN_EV_PRED_AVG_CANS_RT),0) AS AI_DGN_EV_PRED_AVG_CANS_RT,
		   ifnull( MAX(AUTLO.LRN_YN),'N') AS LRN_YN,
		    ATKNM.LNKG_TPC_KMMP_NOD_ID,
		    MAX(LNKGTPC.KMMP_NOD_NM) AS LNKG_TPC_KMMP_NOD_NM,
		    MAX(ATKNM.TPC_LNKG_DV_CD) AS TPC_LNKG_DV_CD, MAX(LNKGTPC.SRT_ORDN) AS SRT_ORDN
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
		        ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
		        AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		        AND DPTH2.DPTH = 2
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
		        ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
		        AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
		        AND DPTH3.DPTH = 3
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
		        ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
		        AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
		        AND DPTH4.DPTH = 4
		    INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
		        ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
		        AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
		        AND DPTH5.DPTH = 5
		    LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO
		        ON DPTH5.KMMP_NOD_ID = AUTLO.TPC_KMMP_NOD_ID
		        AND AUTLO.USR_ID = #{usrId}
		    LEFT OUTER JOIN (
		        SELECT
		            ATKNM.MLU_KMMP_NOD_ID,
		            ATKNM.TPC_KMMP_NOD_ID,
		            ATKNM.TPC_LNKG_DV_CD,
		            ATKNM.LNKG_TPC_KMMP_NOD_ID
		        FROM LMS_CMS.BC_AI_TPC_KMMP_NOD_MPN ATKNM
		        GROUP BY ATKNM.TPC_KMMP_NOD_ID, ATKNM.LNKG_TPC_KMMP_NOD_ID, ATKNM.MLU_KMMP_NOD_ID, ATKNM.TPC_LNKG_DV_CD
		    ) ATKNM
		        ON DPTH2.KMMP_NOD_ID = ATKNM.MLU_KMMP_NOD_ID
		        AND DPTH5.KMMP_NOD_ID = ATKNM.TPC_KMMP_NOD_ID
		    LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
		        ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
		        AND BALAC.CTN_TP_CD = 'QU'
		        AND BALAC.DEL_YN = 'N'
		    LEFT OUTER JOIN LMS_CMS.BC_KMMP_NOD LNKGTPC
		        ON LNKGTPC.KMMP_NOD_ID = ATKNM.LNKG_TPC_KMMP_NOD_ID
		WHERE DPTH1.DPTH = 1
		    AND DPTH1.OPT_TXB_ID = #{optTxbId}
		    AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
		    AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY DPTH5.KMMP_NOD_ID, ATKNM.LNKG_TPC_KMMP_NOD_ID
		ORDER BY MAX(DPTH5.ORGL_ORDN), DPTH5.KMMP_NOD_ID, MAX(LNKGTPC.SRT_ORDN)
		/* AI맞춤 학생 수학 맞춤학습 개념맵 조회 - 이혜인 - ApPlStu-Mapper.xml - selectTpcMpnOvList */
	</select>
	
	<select id="selectTpcAvnOnlyOvList" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT AUTLO.USR_ID
			 , AUTLO.TPC_KMMP_NOD_ID
			 , MAX(AUTLO.AI_DGN_EV_TPC_AVN) AS AI_DGN_EV_TPC_AVN
		FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO
		WHERE AUTLO.OPT_TXB_ID = #{optTxbId}
		<if test="usrId != null and !usrId.equals('')">
			AND AUTLO.USR_ID = #{usrId}
		</if>
		<if test="mluKmmpNodId != null and !mluKmmpNodId.equals('')">
			AND AUTLO.LU_KMMP_NOD_ID = #{mluKmmpNodId}
		</if>
		GROUP BY AUTLO.USR_ID, AUTLO.TPC_KMMP_NOD_ID
		/* AI맞춤 진단평가 문항정보 조회 (토픽숙련도 계산 - 학습 현황 관리) - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectTpcAvnOnlyOvList */
	</select>
	
	<select id="selectIansQtmCnt" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="Integer">
		/* AI맞춤 쌓인 오답문항 개수 - 김현혜 - ApPlStu-Mapper.xml - selectIansQtmCnt */
		SELECT count(distinct EEQA.QTM_ID) FROM 
			LMS_LRM.EA_EV E
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
			ON E.EV_ID = EAETR.EV_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ 
			ON EEQ.EV_ID = E.EV_ID
			INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
			ON EEQA.QTM_ID = EEQ.QTM_ID 
			AND EEQA.EV_ID = EEQ.EV_ID 
			AND EEQA.USR_ID = #{usrId}
			WHERE 1=1 
			AND E.USR_ID = #{usrId}
			AND E.OPT_TXB_ID = #{optTxbId}
			AND EEQA.CANS_YN = 'N'
		/* AI맞춤 쌓인 오답문항 개수 - 김현혜 - ApPlStu-Mapper.xml - selectIansQtmCnt */
	</select>
	
	<select id="selectEvSeUg" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/* AI맞춤 단원진단평가여부 - 김현혜 - ApPlStu-Mapper.xml - selectEvSeUg */
		SELECT 
		    EE.EV_ID, 
		    MAX(EE.EV_NM) AS EV_NM, 
		    IFNULL(MAX(EER.EV_CMPL_YN), 'N') AS EV_CMPL_YN
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_EV_TS_RNGE EETR
		    ON EE.EV_ID = EETR.EV_ID
		LEFT JOIN LMS_LRM.EA_EV_RS EER
		    ON EE.EV_ID = EER.EV_ID
		    AND EER.USR_ID = #{usrId}
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN LRMP 
		    ON LRMP.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID 
		    AND LRMP.OPT_TXB_ID = #{optTxbId}
		    AND LRMP.DPTH = 1
		INNER JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN LKMAP 
		    ON LKMAP.LRMP_NOD_ID = LRMP.LRMP_NOD_ID
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN KMMP
		    ON KMMP.KMMP_NOD_ID = LKMAP.KMMP_NOD_ID
		    AND KMMP.DPTH = 2 
		    AND KMMP.KMMP_NOD_ID = LKMAP.KMMP_NOD_ID
		    AND KMMP.OPT_TXB_ID = #{optTxbId}
		    AND KMMP.KMMP_NOD_ID = #{mluKmmpNodId}
		WHERE EE.EV_DV_CD = 'SE' 
		    AND EE.EV_DTL_DV_CD = 'UG'
		    AND EE.DEL_YN = 'N'
		    AND EE.USE_YN = 'Y'
		    AND EE.EV_NM NOT LIKE '%검수용%'
		    AND EE.OPT_TXB_ID = #{optTxbId}
		GROUP BY EE.EV_ID
		/* AI맞춤 단원진단평가여부 - 김현혜 - ApPlStu-Mapper.xml - selectEvSeUg */
	</select>
	
	<select id="selectAlMaEvQtmAnwList" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
		/* AI맞춤 평가답변리스트 조회 - 김현혜 - ApPlStu-Mapper.xml - selectAlMaEvQtmAnwList */
		select
				MAX(EAETR.TPC_KMMP_NOD_NM) as qpTcNm,
				 MAX(e.EV_DTL_DV_CD) as EV_DTL_DV_CD,
				 MAX(aqxs.AVG_CANS_RT) as AVG_CANS_RT
			        , MAX(Q.EV_ID) as evId
			        , MAX((SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = Q.EV_ID)) AS evNm
			        , MAX(Q.QTM_ID) AS qtmId
			        , MAX(Q.QTM_ORDN) AS qtmOrdn
			        , MAX(QQ.QP_QST_TYP_CD) as QP_QST_TYP_CD
			        , ROW_NUMBER() OVER(PARTITION BY MAX(Q.EV_ID), MAX(QA.USR_ID) ORDER BY MAX(Q.QTM_ORDN)) AS qtmNo
			        , MAX(Q.QTM_DFFD_DV_CD) AS qtmDffdDvCd
			        , MAX(Q.DEL_YN) AS delYn
			        , MAX(Q.DEL_DTM) AS delDtm
			        , MAX(QA.USR_ID) AS usrId
			        , MAX(QA.SMT_ANW_VL) AS smtAnwVl
			        , MAX(QA.CANS_YN) AS cansYn
			        , MAX(QA.XPL_TM_SCNT) AS xplTmScnt 
			        , MAX(DATE_FORMAT(SEC_TO_TIME(QA.XPL_TM_SCNT), '%i:%s')) AS xplTmScntNm -- 풀이시간 초수
			        , ANY_VALUE(DATE_FORMAT(SEC_TO_TIME(avg(a.XPL_TM_SCNT)), '%i:%s')) as avgXplTmScnt
			        , MAX(QA.HNT_COFM_YN) as HNT_COFM_YN
			        , MAX(QA.XPL_ST_CD) as XPL_ST_CD
			        , MAX((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'XPL_ST_CD' AND CM_CD = QA.XPL_ST_CD)) XPL_ST_NM
			        <if test="null != evDtlDvCd and evDtlDvCd.equals('OV')">
			        	,  MAX(BQEV.FLE_PTH_NM) as FLE_PTH_NM
			        </if>
			        , MAX(QA.CRTR_ID) AS crtrId
			        , MAX(QA.CRT_DTM) AS crtDtm
			        , MAX(QA.MDFR_ID) AS mdfrId
			        , MAX(QA.MDF_DTM) AS mdfDtm
			        , MAX(QA.DB_ID) AS dbId
		        FROM LMS_LRM.EA_EV_QTM Q
		        JOIN 
		                LMS_LRM.EA_EV_QTM_ANW
		        QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID
		        LEFT JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = Q.QTM_ID
		        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
		        LEFT JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = Q.QTM_ID  
		        inner join lms_lrm.ea_ev e
		        on e.ev_id = Q.ev_id
		        inner join lms_lrm.EA_AI_EV_TS_RNGE EAETR 
		        on eaetr.ev_id = e.ev_id
		        and Q.TPC_ID = eaetr.TPC_KMMP_NOD_ID
				left outer join (
					SELECT
					    b.QTM_ID,
					    SUM(b.CANS_YN = 'Y')/COUNT(b.CANS_YN ) as AVG_CANS_RT
					FROM LMS_LRM.EA_EV a
					INNER JOIN LMS_LRM.EA_EV_QTM_ANW b
						ON a.EV_ID = b.EV_ID
					WHERE a.OPT_TXB_ID = #{optTxbId}
					GROUP BY b.QTM_ID
				)aqxs on aqxs.qtm_id = Q.qtm_id
		        left join (
		        	SELECT DISTINCT EEQA.XPL_TM_SCNT, EEQA.QTM_ID
					FROM LMS_LRM.EA_EV_QTM_ANW EEQA
					INNER JOIN LMS_LRM.EA_EV_QTM EEQ 
					ON EEQA.EV_ID = EEQ.EV_ID
					AND EEQA.QTM_ID = EEQ.QTM_ID
					INNER JOIN LMS_LRM.EA_EV EE 
					ON EEQ.EV_ID = EE.EV_ID
					WHERE OPT_TXB_ID = #{optTxbId}
		        )a on a.qtm_id = QA.qtm_id 
		        <if test="null != evDtlDvCd and evDtlDvCd.equals('OV')">
		        left join (select qtm_id, FLE_PTH_NM
		        	from lms_cms.BC_QTM_EXL_VD
		        	where VD_DV_CD ='DIR') BQEV
				on q.qtm_id = BQEV.qtm_id
				</if>
		        WHERE Q.EV_ID in
		        <foreach collection="evIds" item="id" open="(" close=")" separator=",">
				 	#{id}
				</foreach>
		        AND Q.DEL_YN = 'N'
		        AND QA.USR_ID = #{usrId}
		        group by QA.qtm_id
		        <if test="null != evDtlDvCd and evDtlDvCd.equals('OV')">
		        	ORDER by MAX(E.ev_dtl_dv_cd), MAX(Q.QTM_ORDN)
		        </if>
		        <if test="evDtlDvCd != 'OV'">
		        	ORDER by MAX(Q.ev_Id), MAX(EAETR.TPC_KMMP_NOD_NM), MAX(Q.QTM_ORDN)
		        </if>
		/* AI맞춤 평가답변리스트 조회 - 김현혜 - ApPlStu-Mapper.xml - selectAlMaEvQtmAnwList */
	</select>
	
	<select id="selectEvCansRt" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			    MAX(EAETR.MLU_KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
			    MAX(EE.EV_ID) AS EV_ID, 
			    MAX(EE.EV_NM) AS EV_NM, 
			     MAX(EE.EV_DV_CD) AS EV_DV_CD, 
			    EE.EV_DTL_DV_CD, 
			    MAX(EER.EV_CMPL_YN) AS EV_CMPL_YN,
			    CEIL((SUM(EEQA.CANS_YN = 'Y') / COUNT(EEQ.QTM_ID)) * 100) AS CANS_RT
		FROM LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EEQ.TPC_ID = EAETR.TPC_KMMP_NOD_ID
		WHERE EV_DV_CD = 'AE'
		AND EER.USR_ID = #{usrId}
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY EE.EV_DTL_DV_CD
	</select>
	
	<select id="selectTpcRptOV" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
	/* AI맞춤 학습리포트 - 진단 평가 학생별 토픽별 점수 조회 - 김현혜 - ApPlStu-Mapper.xml - selectTpcRptOV */
		select EER.USR_ID, EE.EV_ID
			 , EAETR.MLU_KMMP_NOD_ID
			 , QTM.TPC_ID AS TPC_KMMP_NOD_ID
			 , QTM.QTM_ID
			 , QQ.QP_DFFD_CD AS CTN_DFFD_DV_CD
			 , ANW.CANS_YN
			 , IFNULL(AUQP.TXM_PN, 0) AS TXM_PN
			 , IFNULL(AUTP.AI_PRED_AVG_CANS_RT, 0) AS AI_PRED_AVG_CANS_RT
		     , IFNULL(AUTP.AI_PRED_AVG_SCR, 0.5) AS AI_PRED_AVG_SCR
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		inner join lms_lrm.ea_ai_ev_ts_rnge eaetr on ee.EV_ID = eaetr.EV_ID 
		inner join LMS_LRM.EA_EV_QTM QTM on qtm.ev_id = ee.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW ANW ON QTM.QTM_ID = ANW.QTM_ID AND EE.EV_ID = ANW.EV_ID
		INNER JOIN LMS_CMS.QP_QTM QQ ON QTM.QTM_ID = QQ.QP_QTM_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP ON QTM.TPC_ID = AUTP.TPC_ID AND ANW.USR_ID = AUTP.USR_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP ON QTM.QTM_ID = AUQP.QTM_ID AND ANW.USR_ID = AUQP.USR_ID
		WHERE 1=1
		and EE.EV_DTL_DV_CD = 'OV'
		AND eaetr.OPT_TXB_ID = #{optTxbId}
		and eaetr.MLU_KMMP_NOD_ID  = #{mluKmmpNodId}
		GROUP BY EE.EV_ID, QTM.QTM_ID 
		ORDER BY MAX(QTM.TPC_ID), QTM.QTM_ID, MAX(ANW.CANS_YN)
	</select>
	
	<select id="selectTpcMpnRptList" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AlTpcMpnDto">
	/* AI맞춤 학습리포트 - 학생별 토픽 숙련도 조회 - 김현혜 - ApPlStu-Mapper.xml - selectTpcMpnRptList */
		select cu.STU_NO as stuNo, cu.usr_id, cu.usr_nm, cu.stu_no, IFNULL(allv.LRNR_VEL_TP_CD, 'NM') as LRNR_VEL_TP_CD, AKNR5.KMMP_NOD_ID as TPC_KMMP_NOD_ID
				 , AKNR5.KMMP_NOD_NM AS TPC_KMMP_NOD_NM
				 , IFNULL(AUTP.TPC_AVN, 0.5) AS TPC_AVN
				 , EV.EV_ID as OVEvId, ev.ev_cmpl_yn as OVCmplYn
				 , EV2.EV_ID as C1EvId, ev2.ev_cmpl_yn as C1EvCmplYn
			from LMS_LRM.CM_USR cu
			inner join LMS_LRM.AI_KMMP_NOD_RCSTN AKNR2
			on aknr2.OPT_TXB_ID = cu.CLA_ID
			and AKNR2.dpth = 2
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR3 -- 소단원
			ON AKNR2.OPT_TXB_ID = AKNR3.OPT_TXB_ID
			and aknr2.kmmp_nod_id = aknr3.urnk_kmmp_nod_id
			and aknr3.dpth = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR4 -- 차시
			ON AKNR3.OPT_TXB_ID = AKNR4.OPT_TXB_ID
			and aknr3.kmmp_nod_id = aknr4.urnk_kmmp_nod_id 
			and aknr4.dpth = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR5 -- 토픽
			ON AKNR5.OPT_TXB_ID = AKNR4.OPT_TXB_ID
			and aknr4.kmmp_nod_id = aknr5.urnk_kmmp_nod_id 
			and aknr5.dpth = 5
			left outer join lms_lrm.EA_EV EE2 
			on ee2.OPT_TXB_ID = cu.cla_id
			and ee2.usr_id = cu.usr_id
			LEFT OUTER JOIN (
						SELECT EE.EV_ID, EAETR.OPT_TXB_ID, EAETR.MLU_KMMP_NOD_ID, EAETR.TC_KMMP_NOD_ID, EER.USR_ID, eer.ev_cmpl_yn
						FROM LMS_LRM.EA_EV EE 
						INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
						ON EE.EV_ID = EAETR.EV_ID 
						inner join lms_lrm.EA_EV_RS EER 
						on ee.ev_id = eer.ev_id
						WHERE EE.EV_DTL_DV_CD = 'OV'
					)EV ON EV.OPT_TXB_ID = AKNR2.OPT_TXB_ID
					AND EV.MLU_KMMP_NOD_ID = AKNR2.KMMP_NOD_ID
					AND EV.TC_KMMP_NOD_ID = AKNR4.KMMP_NOD_ID
					and EV.USR_ID = cu.usr_id 
			LEFT OUTER JOIN (
						SELECT EE.EV_ID, EAETR.OPT_TXB_ID, EAETR.MLU_KMMP_NOD_ID, EAETR.TC_KMMP_NOD_ID, EER.USR_ID, eer.ev_cmpl_yn
						FROM LMS_LRM.EA_EV EE 
						INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
						ON EE.EV_ID = EAETR.EV_ID 
						inner join lms_lrm.EA_EV_RS EER 
						on ee.ev_id = eer.ev_id
						WHERE EE.EV_DTL_DV_CD = 'C1'
					)EV2 ON EV2.OPT_TXB_ID = AKNR2.OPT_TXB_ID
					AND EV2.MLU_KMMP_NOD_ID = AKNR2.KMMP_NOD_ID
					AND EV2.TC_KMMP_NOD_ID = AKNR4.KMMP_NOD_ID
					and EV2.USR_ID = cu.usr_id 
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
			ON AKNR5.KMMP_NOD_ID = AUTP.TPC_ID
			and autp.usr_id = cu.usr_id
			LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALLV
			ON ALLV.USR_ID = CU.USR_ID
			AND ALLV.LU_KMMP_NOD_ID = #{mluKmmpNodId}
			where cu.CLA_ID = #{optTxbId}
			and AKNR2.KMMP_NOD_ID = #{mluKmmpNodId}
			and cu.USR_TP_CD = 'ST'
			group by cu.usr_id, AKNR5.KMMP_NOD_ID
			order by MAX(cu.STU_NO), cu.usr_id
	</select>
	
	<select id="selectUsrEvTmScnt" parameterType="com.aidt.api.al.pl.dto.AlTpcMpnDto" resultType="com.aidt.api.al.pl.dto.AlTpcMpnDto">
	/* AI맞춤 학습리포트 - 학생별 총 학습시간 조회 - 김현혜 - ApPlStu-Mapper.xml - selectUsrEvTmScnt */
	SELECT u.usr_id, max(R2.MDF_DTM) mdfDtm,
		CAST(FLOOR(SUM(R2.EV_TM_SCNT) / 3600) AS UNSIGNED INTEGER) AS hours,
	    CAST(FLOOR((SUM(R2.EV_TM_SCNT) % 3600) / 60) AS UNSIGNED INTEGER) AS minutes,
	    CAST((SUM(R2.EV_TM_SCNT) % 60) AS UNSIGNED INTEGER) AS seconds
			   	FROM LMS_LRM.CM_USR U 
			   	inner join LMS_LRM.ea_ev e on E.USR_Id = U.USR_ID and E.opt_txb_id = u.cla_id
			   	inner JOIN LMS_LRM.EA_EV_RS R2 ON R2.USR_ID = U.USR_ID and E.ev_id = R2.ev_id
			   	WHERE U.CLA_ID = #{optTxbId}
			   	AND U.USR_TP_CD = 'ST'
			   	GROUP BY u.usr_id
	</select>
	
	<select id="selectOnlyMluList" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		select rcstn1.kmmp_nod_id, rcstn1.kmmp_nod_nm, rcstn3.kmmp_nod_id as tc_kmmp_nod_id, RCSTN3.kmmp_nod_nm as tc_kmmp_nod_nm
		from LMS_LRM.AI_KMMP_NOD_RCSTN rcstn1
		inner join LMS_LRM.ai_kmmp_nod_rcstn rcstn2
		on rcstn2.urnk_kmmp_nod_id = rcstn1.kmmp_nod_id
		and RCSTN2.dpth = 3
		inner join LMS_LRM.ai_kmmp_nod_rcstn rcstn3
		on rcstn3.urnk_kmmp_nod_id = rcstn2.kmmp_nod_id
		and RCSTN3.dpth = 4
		where rcstn1.dpth = 2
			and rcstn1.opt_txb_id = #{optTxbId}
			and rcstn1.lckn_yn = 'N'
			and rcstn1.del_yn = 'N'
		order by rcstn1.kmmp_nod_id
	</select>
	
	<select id="selectAlMaEvIdList" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
		SELECT EE.EV_ID 
		FROM  LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR 
		ON EAETR.EV_ID= EE.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_RS EER 
		ON EE.EV_ID = EER.EV_ID 
		WHERE EER.USR_ID = #{usrId}
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		AND EE.EV_DV_CD = 'AE'
		AND EER.EV_CMPL_YN = 'Y'
		and EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
		GROUP BY EE.EV_ID
		ORDER BY EE.EV_ID
	</select>
	

</mapper>