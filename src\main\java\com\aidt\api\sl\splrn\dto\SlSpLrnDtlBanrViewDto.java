package com.aidt.api.sl.splrn.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-21 15:41:26
 * @modify : date 2024-02-21 15:41:26
 * @desc : 특별학습 상세화면 상단 DTO
 */


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnDtlBanrViewDto {

	@Parameter(name="특별학습ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnId;
	
	@Parameter(name="특별학습명")
	private String spLrnNm;
	
	@Parameter(name="학습대상목표")
	private String lrnGoalCn;
	
	@Parameter(name="완료건수")
	private int done;
	
	@Parameter(name="전체건수")
	private int entire;
	
	@Parameter(name="노드별진행상태")
	private String lrnStCd;
	
	@Parameter(name="스크린샷리스트")
	private List<SlSpLrnScrsDto> scrsList;
	
//	@Parameter(name="진행상황리스트")
//	private List<SlSpLrnLastPrgsDto> pgrsList;
	
}
