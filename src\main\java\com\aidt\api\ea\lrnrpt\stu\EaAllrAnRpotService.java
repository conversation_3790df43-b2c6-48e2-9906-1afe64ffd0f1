/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 04:06:48
 * @modify 2024. 6. 22.
 * @desc 
 */
package com.aidt.api.ea.lrnrpt.stu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.claan.dto.BcClaanReqDto;
import com.aidt.api.bc.claan.dto.BcClaanResDto;
import com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto;
import com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto;
import com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto;
import com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotSrhDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlTcpRcmCtnDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 16:06:48
 * @modify 2024-06-22 16:06:48
 * @desc
 */
@Service
public class EaAllrAnRpotService {

	private final String MAPPER_NAMESPACE = "api.ea.lrnrpt.stu.";
	private final String MAPPER_NAMESPACE_CM = "api.ea.lrnrpt.cm.";
	private final String MAPPER_NAMESPACE_CLAAN = "api.bc.claan.tcr.";
	private final String DOMAIN_PATH = "/api/v1/content/";
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Autowired
	private CommonDao commonDao;

	/**
	 * 학습 요약 조회
	 * 
	 * @param param
	 * @return
	 */
	public Map<String, Object> selectEaLrnRptMainInfo(EaLrnRptReqDto param) {
		Map<String, Object> map = new HashMap<>();
		// 학습 요약 조회
		map.put("lrnSumm", commonDao.select(MAPPER_NAMESPACE + "selectLrnSumm", param));
		return map;
	}

	public Map<String, Object> selectLrnPref(EaLrnRptReqDto param) {
		Map<String, Object> map = new HashMap<>();
		// 학습 선호도
		map.put("lrnPref", commonDao.select(MAPPER_NAMESPACE + "selectLrnPref", param));
		return map;
	}

	public Map<String, Object> selectLrnChal(EaLrnRptReqDto param) {
		Map<String, Object> map = new HashMap<>();
		// 학습 챌린지
		map.put("lrnChal", commonDao.select(MAPPER_NAMESPACE + "selectLrnChal", param));
		return map;
	}

	public Map<String, Object> selectEaLrnAsnInfo(EaLrnRptReqDto param) {
		Map<String, Object> map = new HashMap<>();
		// 학습 패턴 - 과제제출내역 조회
		map.put("lrnAsn", commonDao.select(MAPPER_NAMESPACE + "selectEaLrnAsnInfo", param));
		return map;
	}

	public List<LrnPtrnDto> selectEvLrnGrowAnList(EaLrnRptReqDto param) {
		List<LrnPtrnDto> growList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvLrnGrowAnList", param);

		/**
		 * 월별 학습시간을 비율로 계산한다. 월최대 학습시간인 절대값 (secsPerMonth)을 기준으로 월별 학습시간을 비율로 계산한다.
		 */
		final Double secsPerMonth = 576000.0;

		for (LrnPtrnDto grow : growList) {

			if (grow.getEvTmScnt() > 0) {

				double evTmScnt = grow.getEvTmScnt();

				evTmScnt = (evTmScnt / secsPerMonth) * 100;

				double scale = Math.pow(10, 2);
				double roundedDouble = Math.round(evTmScnt * scale) / scale;

				grow.setLrnRatio(roundedDouble);

			}
		}

		return growList;
	}

	public List<EaLrnRptAlTcpRcmCtnDto> selectTpcRcmCtnList(EaAllrAnRpotSrhDto param) {

		List<EaLrnRptAlTcpRcmCtnDto> tpcRcmCtnList = commonDao.selectList(MAPPER_NAMESPACE_CM + "selectTpcRcmCtnList", param);
		
		
		for(EaLrnRptAlTcpRcmCtnDto ctn : tpcRcmCtnList) {
			String imgPath = DOMAIN_PATH +BUCKET_NAME + ctn.getCdnPthNm() + "images/poster.png";
			ctn.setCdnPthNm(imgPath);
		}
		
		return tpcRcmCtnList;
	}
	
	

	public LrnPtrnDto selectIansCnt(EaAllrAnRpotSrhDto param) {
		return commonDao.select(MAPPER_NAMESPACE_CM + "selectIansCnt", param);
	}
	
	public List<BcClaanResDto> selectCrsCnAchPerStuOrTcrList(BcClaanReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE_CLAAN + "selectCrsCnAchPerStuOrTcrList", dto);
	}
	
	public List<LrnPtrnDto> selectAnPerTm(EaLrnRptReqDto param) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAnPerTm", param);
	}

	public List<LrnPtrnDto> selectAnPerWeekInfo(EaLrnRptReqDto param) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAnPerWeek", param);
	}

	public List<EaAllrAnRpotDto> selectAllAnLuList(EaAllrAnRpotSrhDto srhDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAllAnLuList", srhDto);
	}

	public List<EaAllrAnRpotDto> selectAllAnEvList(EaAllrAnRpotSrhDto srhDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAllAnEvList", srhDto);
	}

	public List<EaAllrAnRpotDto> selectAllAnAraList(EaAllrAnRpotSrhDto srhDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAllAnAraList", srhDto);
	}

	public List<EaAllrAnRpotDto> selectAllAnTcList(EaAllrAnRpotSrhDto srhDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAllAnTcList", srhDto);
	}

	public List<EaAllrAnRpotDto> selectAllAnAchList(EaAllrAnRpotSrhDto srhDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAllAnAchList", srhDto);
	}

	public List<EaAllrAnRpotDto> selectAllAnAiList(EaAllrAnRpotSrhDto srhDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAllAnAiList", srhDto);
	}

}
