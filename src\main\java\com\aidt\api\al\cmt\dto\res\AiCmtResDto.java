package com.aidt.api.al.cmt.dto.res;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-27 17:27:10
 * @modify date 2024-05-27 17:27:10
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtResDto {

    private String aiCmtNo;

    private String cmtCn;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cmtType;

}
