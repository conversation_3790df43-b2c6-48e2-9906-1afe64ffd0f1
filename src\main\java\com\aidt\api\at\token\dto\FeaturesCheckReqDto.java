package com.aidt.api.at.token.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeaturesCheckReqDto {

    @Parameter(name="클라이언트 리소스 버전")
    private String ifNoneMatch;

    @Parameter(name="클라이언트 수정 시간")
    private String ifModifiedSince;

    @Parameter(name="교과서ID")
    private String txbId;
}
