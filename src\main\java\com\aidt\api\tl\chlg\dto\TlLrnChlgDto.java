package com.aidt.api.tl.chlg.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-09 15:52:25
 * @modify date 2024-04-09 15:52:25
 * @desc [TlLrnChlgDto 챌린지설정 dto]
 */
@Getter
@Setter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlLrnChlgDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    /** 학습목표번호 */
    @Parameter(name="학습목표번호")
    private int lrnGoalNo;
    /** 학습시작일자 */
    @Parameter(name="학습시작일자")
    private String lrnStrDt;
    /** 학습종료일자 */
    @Parameter(name="학습종료일자")
    private String lrnEndDt;
    /** 학습목표구분코드 */
    @Parameter(name="학습목표구분코드")
    private String lrnGoalDvCd;
    /** 학습목표문제수 */
    @Parameter(name="학습목표문제수")
    private int lrnGoalQstCnt;
    /** 학습목표시간(초) */
    @Parameter(name="학습목표시간(초)")
    private int lrnGoalTmScnt;
    /** 학습목표상태코드 */
    @Parameter(name="학습목표상태코드")
    private String lrnGoalStCd;
    /** 학습달성시간(초) */
    @Parameter(name="학습달성시간(초)")
    private int lrnAcvTmScnt;
    /** 학습달성문제수 */
    @Parameter(name="학습달성문제수")
    private int lrnAcvQstCnt;
    /** 코드명 */
    @Parameter(name="코드명")
    private String cmCdNm;
    /** 생성일시 */
    @Parameter(name="생성일시")
    private LocalDateTime crtDtm;
    /** 삭제여부 */
    @Parameter(name="삭제여부")
    private String delYn;
    /** DBID */
    @Parameter(name="DBID")
    private String dbId;
}
