package com.aidt.api.ea.grpmgmt.dto;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email 
 * @create date 2024-04-26 10:53:20
 * @modify date 2024-04-26 10:53:20
 * @desc EaGrpMgGrpDto 모둠관리 모둠 정보 Dto
 */

 @Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtGrpDto {
	 /** 모둠ID */
	    @Parameter(name = "모둠ID")
	    @NotBlank(message = "{field.required}")
	    private int grpId;

	    /** 모둠명 */
	    @Parameter(name = "모둠명")
	    @NotBlank(message = "{field.required}")
	    private String grpNm;

	    /** 팀수 */
	    @Parameter(name = "팀수")
	    private int temCnt;

	    /** 모둠명 */
	    @Parameter(name = "모둠그룹자동생성여부")
	    @NotBlank(message = "{field.required}")
	    private String grpGruAutoCrtYn;

	    /** 모둠명 */
	    @Parameter(name = "모둠팀장사용여부")
	    @NotBlank(message = "{field.required}")
	    private String grpTmgrUseYn;

	    /** 생성일시 */
	    @Parameter(name = "생성일시")
	    private String crtDtm;

	    /** 수정일시 */
	    @Parameter(name = "수정일시")
	    private String MdfDtm;

	    /** 진행중 과제 명 */
	    @Parameter(name = "진행중 과제 명")
	    private String asnNmIng;

	    /** 마감된 과제 명 */
	    @Parameter(name = "마감된 과제 명")
	    private String asnNmEnd;
	    
	    /** 마감된 상시 과제 명 */
	    @Parameter(name = "상시 과제 명")
	    private String asnNmOtY;
	    
	    /** 진행중인 상시 과제 명 */
	    @Parameter(name = "상시 과제 명")
	    private String asnNmOtN;

	    /** 진행중 과제 개수 */
	    @Parameter(name = "진행중 과제 개수")
	    private Integer asnNmIngCnt;

	    /** 마감된 과제 개수 */
	    @Parameter(name = "마감된 과제 개수")
	    private Integer asnNmEndCnt;
	    
	    /** 상시 과제 개수 */
	    @Parameter(name = "상시 과제 완료 개수")
	    private Integer asnNmOtYCnt;
	    /** 상시 과제 개수 */
	    @Parameter(name = "상시 과제 미완료 개수")
	    private Integer asnNmOtNCnt;
	    
	    /** 삭제여부 */
	    @Parameter(name = "삭제여부")
	    private String delYn;

	    /** 모둠 ID */
	    @Parameter(name = "모둠 ID")
	    private Integer grpTemId;

	    /** 모둠 명 */
	    @Parameter(name = "모둠 명")
	    private String grpTemNm;

	    /** 학생 ID */
	    @Parameter(name = "학생 ID")
	    private String usrId;

	    /** 학생 이름 */
	    @Parameter(name = "학생 이름")
	    private String usrNm;

	    /** 학습 레벨" */
	    // FS	LRNR_VEL_TP_CD	빠른학습자
	    // NM	LRNR_VEL_TP_CD	보통학습자
	    // SL	LRNR_VEL_TP_CD	느린학습자
	    @Parameter(name = "학습자속도유형코드")
	    private String lrnrVelTpCd;

	        /** 학습 레벨" */
	    // FS	LRNR_VEL_TP_CD	빠른학습자
	    // NM	LRNR_VEL_TP_CD	보통학습자
	    // SL	LRNR_VEL_TP_CD	느린학습자
	    @Parameter(name = "학습자속도유형명")
	    private String lrnrVelTpNm;

	    /** 모듬 팀장 여부 */
	    @Parameter(name = "모듬 팀장 여부")
	    private String grpTmgrYn;


	    /** 데이터베이스ID */
	    @Parameter(name = "데이터베이스ID")
	    private String dbId;
	    
	    /** 모둠 총 개수 */
	    @Parameter(name = "모둠 총 개수")
	    private Integer totalCnt;
	    
	    /** 과제 제출 개수 */
	    @Parameter(name = "과제 제출 개수")
	    private Integer smtCmplCnt;
	    
//	    private List<Map<String, Object>> grpTemList;
	    private List<EaGrpMgmtGrpTeamDto> grpTemList;
}