package com.aidt.api.ea.evcom.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EaEvAnswer {

	private Long qtmId;        //문항 아아디
	private Long annxFleId;    //노트 아이디
	private String smtAnwVl;   //제출 답변
	private String qstXplCn;   //풀이내용을 따로 저정안하는듯
	private String xplStCd;    //풀이상태 코드
	private String hntCofmYn;  //힌트확인여부
	private Integer xplTmScnt; //풀이 시간  
	private String cansYn;     //정답 여부

}
