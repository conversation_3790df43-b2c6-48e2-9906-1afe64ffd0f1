<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.home.tcr">

	<!-- 홈 조회 -->
	<select id="selectHomeList" resultType="com.aidt.api.bc.home.tcr.dto.BcHomeTcrDto">
		/** BcHomeTcr-Mapper.xml - selectHomeList */

	</select>

	<!-- 개별 학생 상태 조회 -->
	<select id="selectStuCurLrnSt" parameterType="Map" resultType="com.aidt.api.bc.home.tcr.dto.BcStuCurLrnStDtlDto">
		/** BcCmTcr-Mapper.xml - selectStuCurLrnSt */
		SELECT
			scls.LU_NOD_ID as luNodId
			 ,scls.TC_NOD_ID as tcNodId
			 ,scls.LRN_ATV_ID as lrnAtvId
			 ,scls.LRN_TP_CD as lrnTpCd
			 ,scls.CUR_LRN_MENU_ID as curLrnMenuId
			 ,scls.CUR_LRN_MENU_NM as curLrnMenuNm
			 ,scls.OPT_TXB_ID as optTxbId
		FROM
			LMS_LRM.CM_STU_CUR_LRN_ST as scls
		WHERE scls.OPT_TXB_ID = #{optTxbId}
		  AND scls.LRN_USR_ID = #{usrId}
	</select>

	<select id="selectStuCurLrnStPthTl" parameterType="Map" resultType="Map">
		/** BcCmTcr-Mapper.xml - selectStuCurLrnStPthTl */
		SELECT
			CASE
				WHEN tc.LU_NO_USE_YN = 'Y' THEN CONCAT(LPAD(IFNULL(tc.RCSTN_NO, tc.RCSTN_ORDN), 2, '0') , '. ' , tc.LRMP_NOD_NM)
				ELSE tc.lrmp_nod_nm
				END AS fmtTc,
			CASE
				WHEN lu.LU_NO_USE_YN = 'Y' THEN CONCAT(IFNULL(lu.RCSTN_NO, lu.RCSTN_ORDN) , '. ' , lu.LRMP_NOD_NM)
				ELSE lu.lrmp_nod_nm
				END AS fmtLu,
			stp.LRN_STP_NM as stpNm,
			atv.LRN_ATV_NM as atvNm
		FROM lms_lrm.tl_sbc_lrn_nod_rcstn tc
				 inner JOIN lms_lrm.tl_sbc_lrn_nod_rcstn lu on lu.LRMP_NOD_ID = tc.LLU_NOD_ID and lu.OPT_TXB_ID = tc.OPT_TXB_ID
				 left join lms_lrm.tl_sbc_lrn_atv_rcstn atv on atv.LRMP_NOD_ID = tc.lrmp_nod_id and atv.LRN_ATV_ID = #{atvId} and atv.OPT_TXB_ID = tc.OPT_TXB_ID
				 left join lms_cms.bc_lrn_stp stp on atv.LRN_STP_ID = stp.LRN_STP_ID
		WHERE tc.OPT_TXB_ID = #{optTxbId}
		  AND tc.LRMP_NOD_ID = #{tcNodId}
	</select>



	<select id="selectStuCurLrnStPthAl" parameterType="Map" resultType="Map">
		/** BcCmTcr-Mapper.xml - selectStuCurLrnStPthAl */
		SELECT
			concat(aknr.rcstn_ordn, ". " ,aknr.KMMP_NOD_NM) AS kmmpNodNm,  -- 대단원 노드명
			btwt.TPC_KMMP_NOD_NM AS tpcKmmpNodNm  -- 토픽 노드명
		FROM LMS_CMS.BC_TK_WRT_TPC btwt
				 INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN aknr
							ON aknr.KMMP_NOD_ID = btwt.LLU_KMMP_NOD_ID
		WHERE btwt.TPC_KMMP_NOD_ID = #{kmmpNodId}
		  AND aknr.OPT_TXB_ID = #{optTxbId}
	</select>

	<select id="selectStuCurLrnStPthAlLw" parameterType="Map" resultType="Map">
		select ev.ev_id as evId
			 ,ev.EV_NM as evNm
			 ,concat(tc.rcstn_ordn, '. ', tc.kmmp_nod_nm) as fmtTcNm
		from lms_lrm.ea_ev ev
				 INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN tc on tc.KMMP_NOD_ID = #{kmmpNodId} AND tc.opt_txb_id = #{optTxbId}
		where ev.EV_ID = #{evId}
	</select>

	<select id="selectStuCurLrnStPthAsn" parameterType="Map" resultType="Map">
		SELECT
			(select ccc.CM_CD_NM
			 from lms_lrm.cm_cm_cd ccc
			 where ccc.cm_cd = eaasn.ASN_TP_CD  AND ccc.URNK_CM_CD='ASN_TP_CD') AS asnTpNm,
			eaasn.ASN_NM AS asnNm
		FROM
			lms_lrm.ea_asn eaasn
		where
			eaasn.asn_id = #{asnId}
	</select>

	<select id="selectStuCurLrnStPthEv" parameterType="Map" resultType="Map">
		SELECT
			ev.ev_id AS evId,
			CASE
				WHEN llu.LU_NO_USE_YN = 'Y' THEN CONCAT(IFNULL(llu.RCSTN_NO, llu.RCSTN_ORDN) , '. ' , llu.LRMP_NOD_NM)
				ELSE llu.lrmp_nod_nm
				END AS fmtLuNm,
			CASE
				WHEN cd.CM_CD_NM IS NOT NULL THEN CONCAT('[', cd.CM_CD_NM, '] ', ev.ev_nm)
				ELSE ev.ev_nm
				END AS fmtEvNm,
			ev.ev_nm AS evNm
		FROM
			lms_lrm.ea_ev ev
				left join lms_lrm.cm_cm_cd cd on cd.URNK_CM_CD = 'EV_DTL_DV_CD' and ev.EV_DTL_DV_CD = cd.CM_CD
				left JOIN
			lms_cms.bc_evsh esh ON esh.EVSH_ID = ev.EVSH_ID
				left JOIN
			lms_lrm.tl_sbc_lrn_nod_rcstn llu ON llu.LRMP_NOD_ID = esh.LLU_NOD_ID AND llu.opt_txb_id = #{optTxbId}
		WHERE
			ev.EV_ID = #{evId}
		  AND ev.OPT_TXB_ID = #{optTxbId}
	</select>

	<select id="selectStuCurLrnStPthSlLw" parameterType="Map" resultType="Map">
		select A.sp_lrn_id
			 ,A.SP_LRN_NM as depth1
			 ,B2.sp_lrn_nod_nm as depth2
			 ,B.SP_LRN_NOD_NM as depth3
		from lms_cms.bc_sp_lrn_ctn C
				 inner join lms_cms.bc_sp_lrn_nod B on B.sp_lrn_nod_id = c.SP_LRN_NOD_ID
				 left join lms_cms.bc_sp_lrn_nod B2 on B2.sp_lrn_nod_id = B.URNK_SP_LRN_NOD_ID
				 inner join lms_lrm.sl_sp_lrn_rcstn A on A.SP_LRN_ID = b.SP_LRN_ID and A.OPT_TXB_ID = #{optTxbId}
		where C.SP_LRN_CTN_ID = #{lrnAtvId}
	</select>

	<select id="selectStuCurLrnStPthSl" parameterType="Map" resultType="Map">
		select sl.SP_LRN_NM as spLrnNm from LMS_LRM.SL_SP_LRN_RCSTN sl where sl.SP_LRN_ID = #{spLrnId} and sl.opt_txb_id = #{optTxbId}
	</select>

	<select id="selectRcnClaEvSmt" parameterType="com.aidt.api.bc.home.tcr.dto.BcRcnClaEvSmtReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcRcnClaEvSmtDto">
		/** BcCmTcr-Mapper.xml - selectRcnClaEvSmt */

		SELECT
			ee.ev_nm AS evNm,
			ee.ev_id AS evId,
			ee.EV_DV_CD AS evDvCd,
			ee.ev_dtl_dv_cd AS evDtlDvCd,
			(SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = EE.ev_dtl_dv_cd) AS evDtlDvNm,
			eer.SMT_DTM AS smtDtm,
			cu.USR_NM AS stuNm,
			cu.USR_ID AS usrId,
			CASE
				WHEN EE.EV_DTL_DV_CD in ('UD', 'UG', 'ST', 'ET') THEN '' -- 단원 진단(UD), 단원 평가(UG) 일 때, 단원명만 표기
				WHEN NOD_TC.LU_NO_USE_YN = 'Y' THEN CONCAT(LPAD(IFNULL(NOD_TC.RCSTN_NO, NOD_TC.RCSTN_ORDN), 2, '0') , '. ' , NOD_TC.LRMP_NOD_NM)
				ELSE NOD_TC.lrmp_nod_nm
				END AS fmtTcNm,
			CASE
				WHEN EE.EV_DTL_DV_CD in ('ST', 'ET') THEN '' -- 학년/학기 초 진단(ST) 또는 학년/학기 말 평가(ET) 시 평가 이름만 노출될 수 있도록
				WHEN NOD_LU.LU_NO_USE_YN = 'Y' THEN CONCAT(IFNULL(NOD_LU.RCSTN_NO, NOD_LU.RCSTN_ORDN) , '. ' , NOD_LU.LRMP_NOD_NM)
				ELSE null
				END AS fmtLuNm
		FROM
			lms_lrm.ea_ev_rs eer
				INNER JOIN
			lms_lrm.cm_usr cu on cu.usr_id = eer.USR_ID
				INNER JOIN
			lms_lrm.ea_ev ee on ee.ev_id = eer.ev_id
				LEFT JOIN
			LMS_LRM.CM_CM_CD EV_DTL ON EV_DTL.URNK_CM_CD = 'EV_DTL_DV_CD' AND EV_DTL.CM_CD = eE.EV_DTL_DV_CD
				LEFT JOIN
			LMS_LRM.EA_EV_TS_RNGE RNG ON RNG.EV_ID = EE.EV_ID AND EE.EV_DV_CD = 'SE'
				LEFT JOIN
			LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = RNG.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = RNG.LU_LRMP_NOD_ID
				LEFT JOIN
			LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = RNG.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = RNG.TC_LRMP_NOD_ID
		WHERE cu.CLA_ID = #{claId}
		  and eer.SMT_DTM is not null
		  and ee.EV_DTL_DV_CD != 'C1' and ee.EV_DTL_DV_CD != 'C2' and ee.EV_DTL_DV_CD != 'OV'
			and cu.usr_tp_cd = 'ST'
			and ee.ev_dtl_dv_cd IN ('ST','UD','FO','TO','UG','ET')
		ORDER BY
			eer.SMT_DTM desc
			LIMIT #{pageSize} OFFSET #{pageOffset}

	</select>

	<select id="selectRcnClaWrtSmt" parameterType="com.aidt.api.bc.home.tcr.dto.BcRcnClaWrtSmtReqDto" resultType="com.aidt.api.bc.cm.dto.BcRcnClaWrtSmtDto">
		/** BcCmTcr-Mapper.xml - selectRcnClaWrtSmt */

		select
			cu.USR_ID as usrId,
			cu.USR_NM as stuNm,
			cwm.PGRS_ST_CD as pgrsStCd,
			cwm.STU_SAV_DTM as stuSavDtm,
			cwm.tpc_kmmp_nod_id as tpcKmmpNodId,
			concat(llu.lluNo, '. ' ,llu.kmmp_nod_nm) as fmtLuNm,
			tc.KMMP_NOD_NM as fmtTcNm
		from lms_lrm.cm_wrt_mg cwm
		inner join lms_lrm.cm_usr cu on cwm.STU_USR_ID = cu.USR_ID
		inner join lms_lrm.ai_kmmp_nod_rcstn tc on tc.KMMP_NOD_ID = cwm.TPC_KMMP_NOD_ID
			and tc.OPT_TXB_ID = cwm.OPT_TXB_ID
			and tc.DEL_YN = 'N'
		inner join ( select kmmp_nod_id
			,kmmp_nod_nm
			,rank() OVER(order by RCSTN_ORDN) as lluNo
			from lms_lrm.ai_kmmp_nod_rcstn llu
			where llu.opt_txb_id = #{optTxbId}
			and llu.dpth = 1
			and llu.del_yn = 'N'
			and llu.use_yn = 'Y'
		) as llu on llu.KMMP_NOD_ID = cwm.LLU_KMMP_NOD_ID
		where cwm.OPT_TXB_ID = #{optTxbId}
			and cwm.pgrs_st_cd not in ('LN', 'CC', 'FC') -- 학습중(LN, 1), 확인완료(CC, 6), 피드백완료
		order by cwm.STU_SAV_DTM desc
		limit #{pageSize} offset #{pageOffset}
	</select>

	<select id="selectRcnClaWrtSmtCnt" parameterType="String" resultType="Map">
		/** BcCmTcr-Mapper.xml - selectRcnClaWrtSmtCnt */

		select
			SUM(CASE WHEN cwm.PGRS_ST_CD IN ('SM', 'AP') THEN 1 ELSE 0 END) AS nPgrCnt, -- 학습중
			SUM(CASE WHEN cwm.PGRS_ST_CD = 'EC' THEN 1 ELSE 0 END) AS iPgrCnt -- 첨삭완료
		from
			lms_lrm.cm_wrt_mg cwm
				inner join
			lms_lrm.cm_usr cu on cwm.STU_USR_ID = cu.USR_ID
				inner join
			lms_lrm.ai_kmmp_nod_rcstn llu on llu.KMMP_NOD_ID = cwm.LLU_KMMP_NOD_ID
				and llu.opt_txb_id = cwm.OPT_TXB_ID
				and llu.dpth = 1
				and llu.del_yn = 'N'
				inner join lms_lrm.ai_kmmp_nod_rcstn tc on tc.KMMP_NOD_ID = cwm.TPC_KMMP_NOD_ID
				and tc.OPT_TXB_ID = cwm.OPT_TXB_ID
				and tc.DEL_YN = 'N'
		where
			cwm.OPT_TXB_ID = #{optTxbId}
		  and cwm.pgrs_st_cd not in ('LN') -- 학습중(LN, 1), 확인완료(CC, 6)
		order by cwm.STU_SAV_DTM desc

	</select>

	<select id="selectWrtListByType" parameterType="Map" resultType="com.aidt.api.bc.cm.dto.BcRcnClaWrtSmtDto">
		/** BcCmTcr-Mapper.xml - selectWrtListByType */

		SELECT cwm.tpc_kmmp_nod_id as tpcKmmpNodId,
		cu.USR_NM as stuNm,
		cu.USR_ID as usrId,
		cwm.STU_SAV_DTM as stuSavDtm,
		concat(llu.RCSTN_ORDN, '. ' ,llu.kmmp_nod_nm) as fmtLuNm,
		concat(LPAD(tc.RCSTN_ORDN,2,'0'), '. ',tc.KMMP_NOD_NM) as fmtTcNm
		from
		lms_lrm.cm_wrt_mg cwm
		inner join
		lms_lrm.cm_usr cu
		on cwm.STU_USR_ID = cu.USR_ID
		and cu.CLA_ID = #{claId}
		inner join
		lms_lrm.ai_kmmp_nod_rcstn llu
		on llu.KMMP_NOD_ID = cwm.LLU_KMMP_NOD_ID
		and llu.opt_txb_id = cwm.OPT_TXB_ID
		and llu.dpth = 1
		and llu.lckn_yn = 'N'
		and llu.del_yn = 'N'
		inner join lms_lrm.ai_kmmp_nod_rcstn tc
		on tc.KMMP_NOD_ID = cwm.TPC_KMMP_NOD_ID
		and tc.OPT_TXB_ID = cwm.OPT_TXB_ID
		and tc.lckn_yn = 'N'
		and tc.DEL_YN = 'N'
		<if test="type == 'ing'">
			and cwm.PGRS_ST_CD IN ('EC') -- 첨삭완료(EC, 4)
		</if>
		<if test="type == 'no'">
			and cwm.PGRS_ST_CD IN ('SM', 'AP') -- AI첨삭중(AP, 2), 첨삭출(SM, 3)
		</if>
		order by cwm.STU_SAV_DTM desc
	</select>

	<select id="selectGDEStuList" parameterType="Map" resultType="Map">
		/** BcCmTcr-Mapper.xml - selectGDEStuList */

		select distinct STU_USR_ID as usrId
		, cgn.GDE_NEED_CD as gdeNeedCd
		from lms_lrm.cm_gde_need cgn
		where OPT_TXB_ID = #{optTxbId}
		and (CRT_DTM) > date_add(now(), interval - #{day} DAY)

	</select>

	<select id="selectLluAch" parameterType="Map" resultType="Map">
		/** BcCmTcr-Mapper.xml - selectLluAch */

		select b.lu_lrmp_nod_id
		,MAX(CASE WHEN llu.LU_NO_USE_YN = 'Y' THEN CONCAT(IFNULL(llu.RCSTN_NO, llu.RCSTN_ORDN) , '. ' , llu.LRMP_NOD_NM)
		ELSE llu.lrmp_nod_nm
		END) AS lrmpNodNm
		,round (sum(e.cans_cnt) / sum(a.fnl_qst_cnt) * 100, 1) as cansRt
		from lms_lrm.ea_ev_rs e
		inner join lms_lrm.cm_usr cu
		ON cu.usr_tp_cd = 'ST'
		AND cu.USR_ID=e.USR_ID
		inner join lms_lrm.ea_ev a
		on a.ev_id = e.ev_id
		inner join (
		select distinct b.lu_lrmp_nod_id, b.ev_id from lms_lrm.ea_ev_ts_rnge b
		where b.LU_OPT_TXB_ID = #{optTxbId}
		) b
		on e.ev_id = b.ev_id
		inner join lms_lrm.tl_sbc_lrn_nod_rcstn llu
		on llu.LRMP_NOD_ID = b.lu_lrmp_nod_id
		and llu.OPT_TXB_ID = a.opt_txb_id
		and llu.USE_YN = 'Y'

		where 1=1
		and (
		(a.ev_dv_cd = 'SE' and a.ev_dtl_dv_cd in ('FO', 'UG')) or -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
		(a.ev_dv_cd = 'AE' and a.ev_dtl_dv_cd = 'OV') -- AI추천학습(AE)의 AI 진단평가(OV)
		)
		and a.opt_txb_id = #{optTxbId}
		and e.ev_cmpl_yn = 'y'

		group by b.lu_lrmp_nod_id
		order by cansRt desc, llu.rcstn_ordn
	</select>

</mapper>