package com.aidt.api.tl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TlLrnwExtrLink {
	/** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    /** 추가링크이름 */
    @Parameter(name="추가링크이름")
    private String extrLinkNm;
    
    /** 추가링크번호 */
    @Parameter(name="추가링크번호")
    private int extrLinkNo;
    
    /** 추가링크URL */
    @Parameter(name="추가링크URL")
    private String extrLinkUrl;
    
    /** 기존url여부 */
    @Parameter(name="기존url여부")
    private String orgnLink;
    
    /** 삭제여부 */
    @Parameter(name="삭제여부")
    private String delYn;
    
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;
}
