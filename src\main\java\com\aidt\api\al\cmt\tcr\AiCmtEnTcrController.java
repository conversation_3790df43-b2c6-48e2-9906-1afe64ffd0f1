package com.aidt.api.al.cmt.tcr;

import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvEtReqDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvStReqDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvToReqDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvUgReqDto;
import com.aidt.api.al.cmt.dto.res.AiCmtEnResWrapperDto;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 10:58:14
 * @modify date 2024-05-21 10:58:14
 * @desc
 */
//@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/cmt/en/tcr")
@Tag(name="[al] AI 코멘트(영어)", description="AI 코멘트(영어)")
public class AiCmtEnTcrController {

    private final AiCmtTcrService aiCommentTcrService;

    private final AiCmtUsrDataTcrService aiCmtUsrDataTcrService;

    private final JwtProvider jwtProvider;

    @Operation(summary="평가/학기초")
    @PostMapping(value = "/ev/st", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnEvStComment(@Valid @RequestBody AiCmtEnEvStReqDto reqDto) {
        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
        return Response.ok(aiCmtUsrDataTcrService.getOrInsertWrapper(reqDto.getEvId(), userDetail.getUsrId() ,reqDto, aiCommentTcrService::selectEnEv ));
    }

    @Operation(summary="평가/단원평가")
    @PostMapping(value = "/ev/ug", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnEvUgComment(@Valid @RequestBody AiCmtEnEvUgReqDto reqDto) {
        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
        return Response.ok(aiCmtUsrDataTcrService.getOrInsertWrapper(reqDto.getEvId(), userDetail.getUsrId() ,reqDto, aiCommentTcrService::selectEnEv ));
    }

    @Operation(summary="평가/학기말총괄")
    @PostMapping(value = "/ev/et", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<AiCmtEnResWrapperDto> getEnEvEtComment(@Valid @RequestBody AiCmtEnEvEtReqDto reqDto) {
        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
        return Response.ok(aiCmtUsrDataTcrService.getOrInsertWrapper(reqDto.getEvId(), userDetail.getUsrId(), reqDto, aiCommentTcrService::selectEnEv ));
    }

    //    @Operation(summary="평가/차시평가")
//    @PostMapping(value = "/ev/to", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<List<AiCmtResDto>> getEnEvToComment(@Valid @RequestBody AiCmtEnEvToReqDto reqDto) {
//        CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
//        return Response.ok(aiCmtUsrDataTcrService.getOrInsertList(reqDto.getEvId(), userDetail.getUsrId(), reqDto, aiCommentTcrService::selectEnEv ));
//    }

}
