package com.aidt.api.ea.evcom.dto;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class EaEvAnswerNoteReqDto {

	@Schema(description = "평가 ID")
	@NotNull(message = "평가 번호가 존재하지 않습니다.")
	private Integer evId;

	@Schema(description = "문항 아이디")
	@NotNull(message = "문항 번호가 존재하지 않습니다.")
	private Long qtmId;

	@Schema(description = "첨부 파일 ID")
	@NotNull(message = "노트 파일이 존재하지 않습니다.")
	private Long annxFleId;

	//todo: 노트만 저장 API 분리 적용 시 삭제
	public static EaEvAnswerNoteReqDto of(EaEvAnswerReqDto eaEvAnswerReqDto) {
		return new EaEvAnswerNoteReqDto(eaEvAnswerReqDto.getEvId(), eaEvAnswerReqDto.getQtmId(),
			eaEvAnswerReqDto.getAnnxFleId());
	}

}
