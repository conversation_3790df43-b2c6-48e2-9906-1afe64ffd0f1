package com.aidt.api.sl.inidat.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-01 10:53:59
 * @modify date 2024-07-01 10:53:59
 * @desc SlIniDatRcmLrnDto 특별학습 학생 추천학습 등록
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlIniDatRcmLrnDto {
	/** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    /** 특별학습ID */
    @Parameter(name="특별학습ID")
    private String spLrnId;
    
    /** 추천여부 */
    @Parameter(name="추천여부")
    private String rcmYn;
    
    /** 학생사용자ID */
    @Parameter(name="학생사용자ID")
    private String lrnUsrId;
    
    /** 생성자ID */
    @Parameter(name="생성자ID")
    private String crtrId;
    
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;    
    
    /** DB ID*/
    @Parameter(name="DB ID")
    private String dbId;
}
