package com.aidt.api.sl.inidat.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-10 11:53:04
 * @modify date 2024-05-10 11:53:04
 * @desc SlIniDatTcr  특별학습 초기데이터작성 Service
 */
@Slf4j
@Tag(name="[sl] 특별학습초기데이터작성[SlIniDatTcr]", description = "로그인한 교사가 담당하는 담당클래스를 조회하여, 특별학습재구성 초기데이터를 작성한다.")
@RestController
@RequestMapping("/api/v1/sl/tcr/inidat")
public class SlIniDatTcrController {

    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private SlIniDatTcrService slIniDatTcrService; //교사용 서비스

    /**
     * 특별학습초기데이터작성 서비스
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="특별학습 초기데이터작성", description="해당 교과서에 매핑된 특별학습의 재구성데이터를 생성한다.")
    @PostMapping(value = "/registIniDat")
    public ResponseDto<Integer> registIniDat() {
        log.debug("Entrance registIniDat");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        SlIniDatCondDto srhDto = new SlIniDatCondDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setTcrUsrId(userDetails.getUsrId());

        return Response.ok(slIniDatTcrService.registIniDat(srhDto));
    }

 
}