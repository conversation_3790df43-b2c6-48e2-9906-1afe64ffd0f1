package com.aidt.api.at.token.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebAgencyWithParamsDto {

    // request
    private String txbId;
    private String authCode;
    private String teacherName;
    private List<String> studentNameList;
    private int studentCount;
    private boolean addExtra;

    private String userId;
    private String schoolId;
    private KerisDto kerisDto;

}

