package com.aidt.api.at.err;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token/restore")
public class UsrLrnrVelRestoreController {

	@Autowired
	private RestoreService restoreService;

	@PostMapping(value = "/usrLrnrVel")
	public ResponseDto<Object> usrLrnrVel() {
		restoreService.restoreUsrLrnVel();
		return Response.ok();
	}
	
	@PostMapping(value = "/evXplStCd")
	public ResponseDto<Object> evXplStCd() {
		restoreService.restoreEvXplStCd();
		return Response.ok();
	}
}
