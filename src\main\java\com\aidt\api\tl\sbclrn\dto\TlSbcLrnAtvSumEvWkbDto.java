package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-22 16:20:19
 * @modify date 2024-01-22 16:20:19
 * @desc TlSbcLrnAtvSumEvWkbDto 교과학습 활동상태 요약정보(평가/익힘책)
 */
@Data
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvSumEvWkbDto {
    /** 대단원번호 */
    @Parameter(name="대단원번호")
    private String nodNo;
    /** 학습맵노드ID(차시) */
    @Parameter(name="학습맵노드ID")
    private String lrmpNodId;
    /** 학습맵노드ID(대단원) */
    @Parameter(name="학습맵노드ID(대단원)")
    private String lluNodId;
    /** 학습맵노드명(대단원) */
    @Parameter(name="학습맵노드명(대단원)")
    private String lrmpNodNm1;
    /** (평가)학습활동ID */
    @Parameter(name="(평가)학습활동ID")
    private String evLrnAtvId;
    // /** (익힘책)학습활동ID */
    // @Parameter(name="(익힘책)학습활동ID")
    // private String wkbLrnAtvId;
    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    /** 익힘책시작페이지번호 */
    @Parameter(name="익힘책시작페이지번호")
    private String wkbStrPgeNo;
    /** 익힘책종료페이지번호 */
    @Parameter(name="익힘책종료페이지번호")
    private String wkbEndPgeNo;
    /** (평가)학습단계명 */
    @Parameter(name="(평가)학습단계명")
    private String evLrnStpNm;
    /** (평가)학습활동명 */
    @Parameter(name="(평가)학습활동명")
    private String evLrnAtvNm;
    /** (평가)학습상태(미학습, 학습중, 학습완료) */
    @Parameter(name="(평가)학습상태")
    private String evLrnStCd;
    /** (평가)평가상세구분코드 */
    @Parameter(name="(평가)평가상세구분코드")
    private String evDtlDvCd;
    /** (평가)총건수 */
    @Parameter(name="(평가)총건수")
    private String evTotCnt;
    @Parameter(name="(평가)완료건수")
    private String evFinCnt;
    /** (평가)평가ID */
    @Parameter(name="(평가)평가ID")
    private String evId;
    /** (평가)평가시간분 */
    @Parameter(name="(평가)평가ID")
    private String evXplTmMin;
    /** (평가)평가시간분 */
    @Parameter(name="(평가)풀이시간설정여부")
    private String evXplTmSetmYn;
    /** (평가)추가평가ID */
    @Parameter(name="(평가)추가평가ID")
    private String extrEvId;
    // /** (익힘책)학습단계명 */
    // @Parameter(name="(익힘책)학습단계명")
    // private String wkbLrnStpNm;
    // /** (익힘책)학습활동명 */
    // @Parameter(name="(익힘책)학습활동명")
    // private String wkbLrnAtvNm;
    /** (익힘책)학습활동ID */
    @Parameter(name="(익힘책)학습활동ID")
    private String wkbLrnAtvId;
    /** (익힘책)학습상태(미학습, 학습중, 학습완료) */
    @Parameter(name="(익힘책)학습상태")
    private String wkbLrnStCd;
    /** (익힘책)총건수 */
    @Parameter(name="(익힘책)총건수")
    private String wkbTotCnt;
    /** (익힘책)총건수 */
    @Parameter(name="(익힘책)완료건수")
    private String wkbFinCnt;
    // /** (익힘책)평가ID */
    // @Parameter(name="(익힘책)평가ID")
    // private String wkbEvId;
    /** 평가/익힘책존재여부 */
    @Parameter(name="평가/익힘책존재여부")
    private String showYn;
}
