package com.aidt.api.ea.claan.tcr.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-03 08:11:오전 8:11
 * @modify date 2024-05-03 08:11:오전 8:11
 * @desc 학급 분석 오답,오답유사 출제 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaClaAnTcrSaveDto {
    @Parameter(name="문항 리스트")
    @NotNull
    private List<EaClaAnTcrSaveDto> qtmList;

    @Parameter(name="단원 리스트")
    @NotNull
    private List<String> qpLluIdList;

    @Parameter(name="문항 난이도 구분 코드 리스트")
    @NotNull
    private List<String> evDffdDvCdList;

    @Parameter(name="평가지 명")
    @NotNull
    private String evNm;

    @Parameter(name="문제 수")
    @NotNull
    private int qstCnt;

    @Parameter(name="재응시 허용 여부")
    @NotNull
    private String rtxmPmsnYn;

    @Parameter(name="응시 기간 설정 여부")
    @NotNull
    private String txmPtmeSetmYn;

    @Parameter(name="풀이 시간 설정 여부")
    @NotNull
    private String xplTmSetmYn;

    @Parameter(name="잠금 여부")
    @NotNull
    private String lcknYn;

    @Parameter(name="사용 여부")
    @NotNull
    private String useYn;

    @Parameter(name="삭제 여부")
    @NotNull
    private String delYn;

    @Parameter(name="평가 완료 여부")
    private String evCmplYn;

    @Parameter(name="평가 ID")
    private long evId;

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="DB ID")
    private String dbId;

    @Parameter(name="등록 타입(wrong:오답 문제 출제,similar:오답 유사 문제 출제)")
    private String type;

    @Parameter(name="시험 범위 순번")
    private int tsRngeSeqNo;

    @Parameter(name="단원 운영 교과서 ID")
    private String luOptTxbId;

    @Parameter(name="단원 학습맵 노드 ID")
    private String luLrmpNodId;

    @Parameter(name="단원 학습맵 노드명")
    private String luLrmpNodNm;

    @Parameter(name="평가 난이도 구분 코드")
    private String evDffdDvCd;

    @Parameter(name="평가 난이도 분포 수")
    private int evDffdDsbCnt;

    @Parameter(name="문항 ID")
    private String qtmId;

    @Parameter(name="문항 플랫폼 난이도 구분 코드")
    private String qtmDffdDvCd;

    @Parameter(name="문항 플랫폼 대단원 코드")
    private String qpLluId;

    @Parameter(name="문항 플랫폼 대단원 명")
    private String qpLluNm;

    @Parameter(name="문항 플랫폼 차시(토픽) 코드")
    private String qpTcId;

    @Parameter(name="문항 플랫폼 차시(토픽) 명")
    private String qpTcNm;

    @Parameter(name="문항 순서")
    private int qtmOrdn;

    @Parameter(name="난이도 명")
    private String qpDffdNm;



}
