package com.aidt.api.bc.tnte.dto;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotBlank;

import com.aidt.common.Paging.PagingRequestDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 필기
 */

@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcTnteDto extends PagingRequestDto{

	@Parameter(name="필기ID", required = true)
	private int tnteId;

	@Parameter(name="필기구분코드", required = true)
	@NotBlank(message = "{field.required}")
	private String tnteDvCd;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="단원노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String luNodId;

	@Parameter(name="차시노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String tcNodId;

	@Parameter(name="학습활동ID", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnAtvId;

	@Parameter(name="학습사용자ID")
	private String lrnUsrId;
	
	@Parameter(name="kerisUsrID")
	private String kerisUsrId;	

	@Parameter(name="학습유형코드", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnTpCd;

	@Parameter(name="학습유형코드명")
	private String lrnTpCdNm;

	@Parameter(name="대단원명")
	private String lluNm;

	@Parameter(name="중단원명")
	private String mluNm;

	@Parameter(name="소단원명")
	private String sluNm;

	@Parameter(name="차시명")
	private String tcNm;

	@Parameter(name="학습활동명")
	private String lrnAtvNm;

	@Parameter(name="CDN경로", required = true)
	@NotBlank(message = "{field.required}")
	private String cdnPthNm;
	
	@Parameter(name="첨부ID")
	private String annxId;
	
	@Parameter(name="첨부파일ID")
	private String annxFleId; 

	@Parameter(name="조회정렬조건")
	private String srhSrt;

	@Parameter(name="정렬정보")
	private String srtInfo;

	@Parameter(name="등록시간")
	private String crtTm;

	@Parameter(name="정렬(단원순)")
	private String rcstnOrdn;

	@Parameter(name="필기삭제용리스트")
	private List<BcTnteDto> deleteTnteList;

	@Parameter(name="필기 canvas objects")
	private List<Map<String, Object>> canvasJsonData;
	
	@Parameter(name = "검색필드")
	private String scWord;

	// 삭제 id 필드
	private List<Integer> tnteIds;

	// 임시 필드
	private String imgData;

	// 삭제여부(등록/삭제 필드)
	private String delYn;

	// 생성자ID
	private String crtrId;

	// 생성일시
	private String crtDtm;

	// 수정자ID
	private String mdfrId;

	// 수정일시
	private String mdfDtm;

	// 데이터베이스ID
	private String dbId;

	private int totalCnt;
	
	private String curConnYn;
	private String usrNm;
	
	@Parameter(name="순번")
	private int rowNum;
	
	@Parameter(name="현재활동ID")
	private String curLrnAtvId;

	@Parameter(name="반ID")
	private String claId;
	
	@Parameter(name="학습상태코드")
	private String lrnStCd;
	
	@Parameter(name="학습시간")
	private String lrnTmScnt;
	
	@Parameter(name="특별학습ID")
	private String spLrnId;
}
