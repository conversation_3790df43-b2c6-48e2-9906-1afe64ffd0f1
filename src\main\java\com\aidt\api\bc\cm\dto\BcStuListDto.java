package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "학생목록")
public class BcStuListDto {

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="사용자명")
	private String usrNm;

	@Parameter(name="휴대전화번호")
	private String mobNo;

	@Parameter(name="이메일주소")
	private String emlAdr;

	@Parameter(name="학생번호")
	private String stuNo;

	@Parameter(name="총학생수")
	private int allStuCnt;

	@Parameter(name="학급ID")
	private String claId;

	@Parameter(name="교과서ID")
	private String txbId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="알림이동내용")
	private String infmMvCn;

	@Parameter(name="케리스 유저 아이디")
	private String kerisUsrId;

}
