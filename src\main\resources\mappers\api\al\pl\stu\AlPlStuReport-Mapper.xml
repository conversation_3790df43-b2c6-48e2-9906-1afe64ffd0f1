<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.stu.report">
	
	<!--    학생화면 평가리포트 조회    -->
    <select id="selectAlEvCmStuRptList" resultType="com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto">
        SELECT
			MAX(E.EV_ID) AS evId,  -- 평가ID
			MAX(E.OPT_TXB_ID) AS optTxbId,  -- 운영교과서ID
			MAX(E.USR_ID) AS usrId,  -- 사용자ID
			MAX(E.EV_DV_CD) AS evDvCd,  -- 평가구분코드
			MAX((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD)) AS evDvNm,  -- 평가구분명
			MAX(E.EV_DTL_DV_CD) AS evDtlDvCd,  -- 평가상세구분코드
			MAX((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = E.EV_DTL_DV_CD)) AS evDtlDvNm,  -- 평가상세구분명
			MAX(E.EV_NM) AS evNm,  -- 평가명
			MAX(E.TXM_PTME_SETM_YN) AS txmPtmeSetmYn,  -- 응시기간설정여부
			MAX(DATE_FORMAT(E.TXM_STR_DTM, '%y-%m-%d %H:%i')) AS txmStrDtm,  -- 응시 시작일
			MAX(DATE_FORMAT(E.TXM_END_DTM, '%y-%m-%d %H:%i')) AS txmEndDtm,  -- 응시 종료일
			MAX(DATE_FORMAT(R.SMT_DTM, '%Y-%m-%d')) AS smtDtm,  -- 제출일시
			MAX(CAST(FLOOR(R.EV_TM_SCNT/60) AS UNSIGNED INTEGER)) AS xplTmMi,  -- 풀이시간 분
			MAX(R.EV_TM_SCNT%60) AS xplTmScnt,  -- 풀이시간 초수
			MAX(CAST(FLOOR(R_AVG.EV_TM_SCNT_SUM/R_AVG.TOTAL_USR_CNT/60) AS UNSIGNED INTEGER)) AS avgXplTmMi,  -- 평균풀이시간 분
			MAX(CAST(FLOOR((R_AVG.EV_TM_SCNT_SUM/R_AVG.TOTAL_USR_CNT)%60) AS UNSIGNED INTEGER)) AS avgXplTmScnt,  -- 평균풀이시간 초
			MAX(E.QST_CNT) AS qstCnt,  -- 문제수
			MAX(R.CANS_CNT) AS cansCnt,  -- 정답수
			MAX(E.FNL_QST_CNT) AS fnlQstCnt,  -- 최종 문제 수
			MAX(ROUND(R.CANS_CNT/E.FNL_QST_CNT*100, 2)) AS cansRt,  -- 정답률 (소수둘째자리 반올림)
			MAX(ROUND(R_AVG.CANS_CNT_SUM/E.FNL_QST_CNT*100/R_AVG.TOTAL_USR_CNT, 2)) AS avgCansRt,  -- 평균 정답률 (소수둘째자리 반올림)
			MAX(E.CRTR_ID) AS crTrId,  -- 생성자
			MAX(E.CRT_DTM) AS crtDtm,  -- 생성일
			MAX(E.MDFR_ID) AS mdfrId,  -- 수정자
			MAX(E.MDF_DTM) AS mdfDtm,  -- 수정일
			MAX(E.DB_ID) AS dbId  -- DB ID
        FROM LMS_LRM.EA_EV E
        JOIN 
        <choose>
            <when test="txmPn != null and txmPn > 0">
                LMS_LRM.EA_EV_RS_RTXM
            </when>
            <otherwise>
                LMS_LRM.EA_EV_RS
            </otherwise>
        </choose>
        R ON R.EV_ID = E.EV_ID AND R.USR_ID = #{usrId}
        LEFT JOIN( -- 반 평균정담률은 첫응시 데이터로
	 			SELECT OT.OPT_TXB_ID
	 				 , SUM(R2.EV_TM_SCNT) EV_TM_SCNT_SUM
	 				 , SUM(R2.CANS_CNT) CANS_CNT_SUM
	 				 , COUNT(U.USR_ID) TOTAL_USR_CNT
			   	FROM LMS_LRM.CM_OPT_TXB OT
			   	JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
			   	JOIN LMS_LRM.EA_EV_RS R2 ON R2.USR_ID = U.USR_ID
			   	WHERE OT.OPT_TXB_ID = #{optTxbId}
			   	AND U.USR_TP_CD = 'ST'
			   	AND R2.EV_ID = #{evId} 
			   	AND R2.EV_CMPL_YN = 'Y'
			   	GROUP BY OT.OPT_TXB_ID
		) R_AVG ON R_AVG.OPT_TXB_ID = E.OPT_TXB_ID
		/*LEFT JOIN EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD = 'SE' -- 교과평가 단원만 조회*/
        WHERE E.EV_ID = #{evId}
        /* AI 맞춤 학습 - 지향난 - AlPlStuReport-Mapper.xml - 학생 진단 평가 리포트 조회 - selectAlEvCmStuRptList */

    </select>
	
	<!--    평가답변리스트 조회    -->
    <select id="selectAlPlEvQtmAnwList" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
        /** EaEvStu-Mapper.xml - selectAlPlEvQtmAnwList */

        SELECT
	          Q.EV_ID AS evId
	        , (SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = Q.EV_ID) AS evNm
	        , Q.QTM_ID AS qtmId
	        , Q.QTM_ORDN AS qtmOrdn
	        , ROW_NUMBER() OVER(PARTITION BY Q.EV_ID, QA.USR_ID ORDER BY Q.QTM_ORDN) AS qtmNo
	        , Q.QTM_DFFD_DV_CD AS qtmDffdDvCd
	        , Q.DEL_YN AS delYn
	        , Q.DEL_DTM AS delDtm
	        , QA.USR_ID AS usrId
	        , QA.SMT_ANW_VL AS smtAnwVl
	        , QA.CANS_YN AS cansYn
	        , QA.XPL_TM_SCNT AS xplTmScnt
	        , DATE_FORMAT(SEC_TO_TIME(QA.XPL_TM_SCNT), '%i:%s') AS xplTmScntNm
	        , QA.CRTR_ID AS crtrId
	        , QA.CRT_DTM AS crtDtm
	        , QA.MDFR_ID AS mdfrId
	        , QA.MDF_DTM AS mdfDtm
	        , QA.DB_ID AS dbId
	        , QTL.QP_TPC_LU_NM AS qpTcNm    			-- 토픽(차시) 명
        FROM LMS_LRM.EA_EV_QTM Q
        JOIN 
        <choose>
            <when test="txmPn != null and txmPn > 0">
                LMS_LRM.EA_EV_QTM_ANW_RTXM
            </when>
            <otherwise>
                LMS_LRM.EA_EV_QTM_ANW
            </otherwise>
        </choose>
        QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID
        LEFT JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = Q.QTM_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
        WHERE Q.EV_ID = #{evId}
        AND Q.DEL_YN = 'N'
        AND QA.USR_ID = #{usrId}
        ORDER BY Q.QTM_ORDN

        /* 평가 공통 - 지향난 - AlPlStuReport-Mapper.xml - 평가답변리스트 조회 - selectAlPlEvQtmAnwList*/
    </select>

    <!--    학생 평가리포트 강점/약점 조회    -->
	<select id="selectAlPlEvRptStuStpnWkpnList" parameterType="com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
		SELECT
			E.EV_ID	AS evId,
			E.QTM_ORDN AS qtmOrdn,
			E.XPL_TM_SCNT AS xplTmScnt,
			D_NO.cansYn,
			D_NO.rowNo AS qtmNo,
			IFNULL(E.TPCNM,'-') AS tpcNm
		FROM (
			SELECT 1 AS rowNo, 'N' AS cansYn UNION ALL
			SELECT 2 AS rowNo, 'N' AS cansYn UNION ALL
			SELECT 3 AS rowNo, 'N' AS cansYn UNION ALL
			SELECT 1 AS rowNo, 'Y' AS cansYn UNION ALL
			SELECT 2 AS rowNo, 'Y' AS cansYn UNION ALL
			SELECT 3 AS rowNo, 'Y' AS cansYn
		) D_NO		
		LEFT JOIN (
			SELECT
				MAX(A.EV_ID) AS EV_ID,
				MAX(A.QTM_ORDN) AS QTM_ORDN,
				MAX(A.QP_TC_NM) AS QP_TC_NM,
				MAX(A.CANS_YN) AS CANS_YN,
				MAX(A.XPL_TM_SCNT) AS XPL_TM_SCNT,
				MAX(A.qtmNo) AS qtmNo,
				A.TPCNM
			FROM (
				SELECT
					E.EV_ID,
					EEQ.QTM_ORDN,
					EEQ.QP_TC_NM,
					QA.CANS_YN,
					QA.XPL_TM_SCNT,
					ROW_NUMBER() OVER(PARTITION BY QA.CANS_YN ORDER BY QA.CANS_YN, QA.XPL_TM_SCNT, EEQ.QTM_ORDN) AS qtmNo,
					EAETR.TPC_KMMP_NOD_NM AS TPCNM
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_QTM EEQ ON EEQ.EV_ID = E.EV_ID
				JOIN LMS_LRM.EA_EV_QTM_ANW QA ON QA.EV_ID = EEQ.EV_ID AND QA.QTM_ID = EEQ.QTM_ID
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON E.EV_ID = EAETR.EV_ID AND EEQ.TPC_ID = EAETR.TPC_KMMP_NOD_ID
				WHERE E.EV_ID = #{evId}
				AND EEQ.DEL_YN = 'N'
				AND QA.USR_ID = #{usrId}
				ORDER BY QA.CANS_YN DESC
			) A
			GROUP BY A.TPCNM
		) E ON E.qtmNo = D_NO.rowNo AND E.CANS_YN = D_NO.cansYn
		ORDER BY MAX(D_NO.cansYn), MAX(D_NO.rowNo)
		
 		/** 평가공통 - 지향난 - AlPlStuReport-Mapper.xml - 학생 평가리포트 강점/약점 조회 - selectAlPlEvRptStuStpnWkpnList*/
 	</select>
 	
 	
 	 <!--    학생  영어 평가리포트 차시별 정답률 조회    -->
	<select id="selectAlEvTcStuRptList" resultType="com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto">
		SELECT
			MAX(EE.EV_ID) AS EV_ID,
			EAETR.TC_KMMP_NOD_ID AS TC_KMMP_NOD_ID,
			MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
			SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS SUM_CANS_YN,
			IFNULL(ROUND(SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) / COUNT(EAETR.TC_KMMP_NOD_ID) * 100), 0) AS tcCansRt,
			COUNT(EAETR.TC_KMMP_NOD_ID) AS qtmCnt
		FROM LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EEQ.TPC_ID = EAETR.TPC_KMMP_NOD_ID
		WHERE EER.USR_ID = #{usrId}
		AND EE.EV_DV_CD = 'AE'
		AND EE.ev_id = #{evId}
		AND EER.EV_CMPL_YN = 'Y'
		GROUP BY EAETR.TC_KMMP_NOD_ID 
		
		/* 학생 영어 평가리포트 차시별 정답률 조회 - 김현혜 - AlPlStuReport-Mapper.xml - selectAlEvTcStuRptList */
 	</select>
 	
 	<select id="selectEaEvQtmTpcProfInfo" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
 		SELECT
			EE.EV_ID,
			MAX(
			    CASE EE.EV_DTL_DV_CD
					WHEN 'OV' THEN '진단'
					WHEN 'C1' THEN '맞춤1'
					WHEN 'C2' THEN '맞춤2'
				END
			    ) AS EV_DTL_DV_CD,
			EEQ.QTM_ID AS QTM_ID,
			MAX(EEQ.QTM_ORDN) AS QTM_ORDN,
			MAX(EEQ.QTM_DFFD_DV_CD) AS QTM_DFFD_DV_CD,
			MAX(EEQ.QP_DFFD_NM) AS QP_DFFD_NM,
			MAX(EEQA.CANS_YN) AS CANS_YN,
			EAETR.TPC_KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
			MAX(EAETR.TPC_KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			MAX(AUTP.AI_PRED_AVG_CANS_RT) AS AI_PRED_AVG_CANS_RT,
			MAX(AUTP.AI_PRED_AVG_SCR) AS AI_PRED_AVG_SCR,
			MAX(AUQP.AI_PRED_CANS_RT) AS AI_PRED_CANS_RT
		FROM LMS_LRM.EA_EV EE 
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID AND EEQ.TPC_ID = EAETR.TPC_KMMP_NOD_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP ON EEQ.TPC_ID = AUTP.TPC_ID AND EER.USR_ID = AUTP.USR_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP ON EEQ.QTM_ID = AUQP.QTM_ID AND EER.USR_ID = AUQP.USR_ID
		WHERE EER.USR_ID = #{usrId}
		AND EE.EV_DV_CD = 'AE'
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		AND EER.EV_CMPL_YN = 'Y'
		GROUP BY EE.EV_ID, EAETR.TPC_KMMP_NOD_ID, EEQ.QTM_ID 
 		/* AI 학습단계별 문항,토픽 프로파일정보 조회  - 이혜인 -  AlLrnw-Mapper.xml - selectEaEvQtmTpcProfInfo */
 	</select>
 	
</mapper>