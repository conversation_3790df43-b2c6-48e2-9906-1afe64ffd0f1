package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * HTML 문항 타입 학습창 연계
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlLrnwHtmlTypeReqDto {
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="평가ID", required=true)
	private String evId;
	
	@Parameter(name="문항ID", required=true)
	private String qtmId;
	
	@Parameter(name="제출답변값")
	private String smtAnwVl;
	
	@Parameter(name="정답여부", required=true)
	private String cansYn;
	
	@Parameter(name="풀이시간초수")
	private Integer xplTmScnt; 
	
	@Parameter(name="DB ID")
	private String dbId;
}
