package com.aidt.api.ea.clamg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-10-07
 * @modify date 2024-10-07
 * @desc 전체 학급 관리 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AllClaMgTcrReqDto {

	@Parameter(name="교사 ID")
	private String tcrUsrId;

	@Parameter(name="운영 교과서 ID")
	private String optTxbId;

	@Parameter(name="교과서 ID")
	private String txbId;
	
	@Parameter(name="학급 ID")
	private String claId;

	@Parameter(name="대단원 ID")
	private String lluNodId;

	@Parameter(name="학습맵 노드ID")
	private String lrmpNodId;
	
	@Parameter(name="지식맵 노드ID")
	private String kmmpNodId; 
	
	@Parameter(name="지식맵상위노드 ID")
	private String urnkKmmpNodId;
	
	/*MA:수학 / EN:영어*/
	@Parameter(name="교과서 과목코드")	
	private String sbjCd;
	
	@Parameter(name="ai 영어학습 고정 갯수")
	private int aeCnt;
}
