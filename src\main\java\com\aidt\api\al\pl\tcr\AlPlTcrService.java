package com.aidt.api.al.pl.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.dto.AlMluTcLstInqTcrReqDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc AI맞춤학습 단원목차 테스트
 */
@Service
public class AlPlTcrService {

    private final String MAPPER_NAMESPACE = "api.al.pl.tcr.";

    @Autowired
    private CommonDao commonDao;
    
    /**
     * AI맞춤학습 과제출제건수조회
     * 
     * @param AlMluTcLstInqTcrReqDto
     * @return int
     */  

	public int selectCountEaAsnDataTcr(AlMluTcLstInqTcrReqDto tcrDto) {
		// TODO Auto-generated method stub
		return commonDao.select(MAPPER_NAMESPACE + "selectCountEaAsnDataTcr", tcrDto);
	}

}
