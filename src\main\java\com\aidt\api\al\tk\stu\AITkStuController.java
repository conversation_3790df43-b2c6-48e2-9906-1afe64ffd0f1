package com.aidt.api.al.tk.stu;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.tk.dto.AlTkDto;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-02 09:05:00
 * @modify 2024-06-02 09:05:00
 * @desc AI회화 학생 컨트롤러
 */

@Slf4j
@Tag(name="[al] AI회화-학생", description="AI회화-학생")
@RestController
@RequestMapping("/api/v1/al/tk/stu")
public class AITkStuController {
	
	@Autowired
	private AlTkStuService alTkStuService;
	
	@Autowired
	private BcCmService bcCmService;
	
	@Autowired
    private JwtProvider jwtProvider;
	
    /**
     * AI 회화 토픽 목록 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI회화 토픽 목록 조회")
	@PostMapping(value = "/selectTkStuList")
	public ResponseDto<List<AlTkDto>> selectTkStuList(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkStuList");
		return Response.ok(alTkStuService.selectTkStuList(alTkDto));
	}
	
    /**
     * AI 회화 나의 최근 학습 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI회화 나의 최근 학습 조회")
	@PostMapping(value = "/selectTkRcnLrnStuInfo")
	public ResponseDto<List<AlTkDto>> selectTkRcnLrnStuInfo(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkRcnLrnStuInfo");
		return Response.ok(alTkStuService.selectTkRcnLrnStuInfo(alTkDto));
	}
	
	/**
     * AI 회화 상세 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI 회화 상세 조회")
	@PostMapping(value = "/selectTkStuDetailInfo")
	public ResponseDto<List<AlTkDto>> selectTkStuDetailInfo(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkRcnLrnStuInfo");
		return Response.ok(alTkStuService.selectTkStuDetailInfo(alTkDto));
	}
	
	/**
     * AI 회화 이전, 다음 단원 조회
     * @param AlTkDto
     * @return ResponseList<AlTkDto>
     */
	@Tag(name="[al] AI 회화 이전, 다음 단원 조회")
	@PostMapping(value = "/selectTkStuPrevNextInfo")
	public ResponseDto<List<AlTkDto>> selectTkStuPrevNextInfo(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if(alTkDto.getOptTxbId() == null) {
			alTkDto.setOptTxbId(userDetails.getOptTxbId());
			alTkDto.setLrnUsrId(userDetails.getUsrId());
		}
		log.debug("alTkDto.toString() == "+ alTkDto.toString());
		log.debug("Entrance selectTkRcnLrnStuInfo");
		return Response.ok(alTkStuService.selectTkStuPrevNextInfo(alTkDto));
	}
	
	/**
     * AI 회화 학습 상태 insert
     * @param AlTkDto
     * @return ResponseDto<Integer>
     */
	@Tag(name="[al] AI 회화 학습 상태 insert")
	@PostMapping(value = "/insertTkStuLrnStCd")
	public ResponseDto<Integer> insertTkStuLrnStCd(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setOptTxbId(userDetails.getOptTxbId());
		alTkDto.setLrnUsrId(userDetails.getUsrId());
		alTkDto.setCrtrId(userDetails.getUsrId());
		alTkDto.setMdfrId(userDetails.getUsrId());
		alTkDto.setDbId(userDetails.getTxbId());

		return Response.ok(alTkStuService.insertTkStuLrnStCd(alTkDto));
	}
	
	/**
     * AI 회화 학습 상태 update
     * @param AlTkDto
     * @return ResponseDto<Integer>
     */
	@Tag(name="[al] AI 회화 학습 상태 update")
	@PostMapping(value = "/updateTkStuLrnStCd")
	public ResponseDto<Integer> updateTkStuLrnStCd(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setOptTxbId(userDetails.getOptTxbId());
		alTkDto.setLrnUsrId(userDetails.getUsrId());
		alTkDto.setCrtrId(userDetails.getUsrId());
		alTkDto.setMdfrId(userDetails.getUsrId());
		alTkDto.setDbId(userDetails.getTxbId());

		return Response.ok(alTkStuService.updateTkStuLrnStCd(alTkDto));
	}
	
	/**
     * AI 회화 학습 시간 update
     * @param AlTkDto
	 * @return 
     * @return ResponseDto<Integer>
     */
//	@Tag(name="[al] AI 회화 학습 시간 update")
//	@PostMapping(value = "/updateTkLrnTm")
//	public void updateTkLrnTm(@RequestBody AlTkDto alTkDto, HttpServletRequest request) {
//		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//		
//		// 공통 학습시간 추가
//		BcCmLrnTmDto bcCmLrnTmDto = new BcCmLrnTmDto();
//		bcCmLrnTmDto.setAiRdngLrnTm(alTkDto.getAiRdngLrnTm());
//		bcCmLrnTmDto.setLrnYrDtm(alTkDto.getLrnYrDtm());
//		bcCmLrnTmDto.setOptTxbId(userDetails.getOptTxbId());
//		bcCmLrnTmDto.setUsrId(userDetails.getUsrId());
//
//		String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
//		bcCmService.updateCmLrnTm(bcCmLrnTmDto, accessToken);
//	}
	
	/**
     * AI회화 아키핀 호출
     * @param BcCbDto
     * @return
     */
	@Operation(summary="AI회화 아키핀 호출", description="AI회화 아키핀 호출")
	@PostMapping(value = "/startDalogueApi", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> startDalogueApi(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setUserID(userDetails.getUsrId());
		//alTkDto.setUserID("kim");
		return alTkStuService.startDalogueApi(alTkDto);
	}
	
	/**
	 * AI회화 아키핀 호출 - 이어하기
	 * @param BcCbDto
	 * @return
	 */
	@Operation(summary="AI회화 아키핀 호출 - 이어하기", description="AI회화 아키핀 호출 - 이어하기")
	@PostMapping(value = "/nextDalogueApi", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> nextDalogueApi(@Valid @RequestBody AlTkDto alTkDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		alTkDto.setUserID(userDetails.getUsrId());
		//alTkDto.setUserID("kim");
		return alTkStuService.nextDalogueApi(alTkDto);
	}
	
	/**
	 * AI회화 마이홈 포인트 저장
	 *
	 * @param AlTkDto
	 * @return
	 */
	@Operation(summary="마이홈 API CALL(로그인)", description="마이홈 API CALL(로그인)")
	@PostMapping(value = "/myhmApiCall")
	public Map<String, Object> myhmApiCall(@RequestBody AlTkDto alTkDto, HttpServletRequest request) {
		String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
		
		String pntCdVal = "";
		
		if (!"E".equals(alTkDto.getSchlGrdCd())) {
			// 중등
			if ("1".equals(alTkDto.getTpcIndex())) {
				// 토픽1
				if ("M".equals(alTkDto.getSchlGrdCd())){
					pntCdVal = "AI_ME_08"; // 중등
				} else {
					pntCdVal = "AI_HE_08"; // 고등
				}
			} else {
				// 토픽1
				if ("M".equals(alTkDto.getSchlGrdCd())){
					pntCdVal = "AI_ME_09"; // 중등
				} else {
					pntCdVal = "AI_HE_09"; // 고등
				}
			}
		} else {
			// 초등은 레슨 기준으로 완료되어 토픽1 완료에 해당되는 코드
			pntCdVal = "AI_EE_08";
		}
		
		Map<String, Object> apiResult = alTkStuService.callMyhmApi(accessToken, Map.of(
				"pntCd", pntCdVal,
				"pntChkBsVl", alTkDto.getTpcKmmpNodId()));
	
		// return Response.ok(Map.of("savedPoint", apiResult));
		return apiResult;
	}
}
