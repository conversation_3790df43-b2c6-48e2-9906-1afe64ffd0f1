package com.aidt.api.at.token.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:59:47
 * @modify 2024-01-05 17:59:47
 * @desc 첨부파일 dto
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class KerisLrnOutDataDto {

	private String curriculum;
	private String achievement_level;
	private String percent;
	
}
