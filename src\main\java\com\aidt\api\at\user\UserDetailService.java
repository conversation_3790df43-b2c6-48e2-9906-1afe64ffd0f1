package com.aidt.api.at.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.at.dto.User;
import com.aidt.api.at.mapper.UserMapper;

@Service
public class UserDetailService {

    @Autowired
    private UserMapper userMapper;
    
    public User.UserResponseDto selectUserDetail(String usrId) {
    	return this.userMapper.selectUserDetail(usrId);
    }
    
    //@Autowired
    //private CommonDao commonDao;

    //private final String MAPPER_NAMESPACE = "api.at.user.";
    
    //public User.UserResponseDto selectUserDetail(String usrId) {
    //	return commonDao.select(MAPPER_NAMESPACE + "selectUserDetail", usrId);
    //}
}
