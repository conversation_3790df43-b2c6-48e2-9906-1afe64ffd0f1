package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSbcLrnAtvEvTcrDto {
	/** 대단원번호 */
    @Parameter(name="대단원번호")
    private String nodNo;
    /** 학습맵노드ID(차시) */
    @Parameter(name="학습맵노드ID")
    private String lrmpNodId;
    /** 학습맵노드명(대단원) */
    @Parameter(name="학습맵노드명(대단원)")
    private String lrmpNodNm1;
    /** (평가)학습활동ID */
    @Parameter(name="(평가)학습활동ID")
    private String evLrnAtvId;
    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    /** 익힘책시작페이지번호 */
    @Parameter(name="익힘책시작페이지번호")
    private String wkbStrPgeNo;
    /** 익힘책종료페이지번호 */
    @Parameter(name="익힘책종료페이지번호")
    private String wkbEndPgeNo;
    /** (평가)학습단계명 */
    @Parameter(name="(평가)학습단계명")
    private String evLrnStpNm;
    /** (평가)학습활동명 */
    @Parameter(name="(평가)학습활동명")
    private String evLrnAtvNm;
    /** (평가)학습상태(미학습, 학습중, 학습완료) */
    @Parameter(name="(평가)학습상태")
    private String evLrnStCd;
    /** (평가)총건수 */
    @Parameter(name="(평가)총건수")
    private String evTotCnt;
    @Parameter(name="(평가)완료건수")
    private String evFinCnt;
    /** (평가)평가ID */
    @Parameter(name="(평가)평가ID")
    private String evId;
    /** (평가)평가시간분 */
    @Parameter(name="(평가)평가ID")
    private String evXplTmMin;
    /** (평가)평가시간분 */
    @Parameter(name="(평가)풀이시간설정여부")
    private String evXplTmSetmYn;
    /** (평가)추가평가ID */
    @Parameter(name="(평가)추가평가ID")
    private String extrEvId;
    /** 평가/익힘책존재여부 */
    @Parameter(name="평가/익힘책존재여부")
    private String showYn;
    
    /** 평가문항목록 */
    @Parameter(name="평가문항목록")
    private List<EaLrnRptTlSbcLrnAtvEvQtmDto> evQtmList;
    
    /** 평가ID에 따른 풀이 시간 합 */
    @Parameter(name="평가ID에 따른 풀이 시간 합")
    private String evTmScnt;
}
