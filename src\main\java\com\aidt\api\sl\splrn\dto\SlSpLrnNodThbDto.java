package com.aidt.api.sl.splrn.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-13 8:41:26
 * @modify : date 2024-02-13 8:41:26
 * @desc : 특별학습 썸네일 DTO
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnNodThbDto {

	@Parameter(name="특별학습노드ID")
	private String spLrnNodId;

	@Parameter(name="모바일파일유형코드")
	private String moFleTpCd;
	
	@Parameter(name="모바일 파일경로")
	private String moPath;

	@Parameter(name="태블릿파일유형코드")
	private String taFleTpCd;
	
	@Parameter(name="태블릿 파일경로")
	private String taPath;
	
	@Parameter(name="피씨파일유형코드")
	private String pcFleTpCd;
	
	@Parameter(name="PC 파일경로")
    private String pcPath;

    
    
    

	
	

}
