<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.tcr">

	<!-- 교사 중단원 목록-->
	<select id="selectMluLstInq" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqTcrReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqTcrResponseDto">
		/** AlPlTcr-Mapper.xml - selectMluLstInq */
		SELECT
			A.KMMP_NOD_ID AS KMMP_NOD_ID,
			MAX(A.KMMP_NOD_NM) AS KMMP_NOD_NM,
			MAX(A.TC_USE_YN) AS TC_USE_YN,
			MAX(AA.KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(AA.KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
			COALESCE(C.EV_DV_CD, '') AS EV_DV_CD,
			COALESCE(C.EV_DTL_DV_CD, '') AS EV_DTL_DV_CD,
			COALESCE(C.EV_CMPL_YN, 'N') AS EV_CMPL_YN,
			MAX(C.CANS_CNT) AS CANS_CNT,
			MAX(C.EV_ID) AS EV_ID,
			C.TPC_AVN AS TPC_AVN_STR,
			MAX(ALLV.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD,
			MAX(C.MDF_DTM) AS MDF_DTM
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN A
		LEFT JOIN (
			SELECT KMMP_ID, KMMP_NOD_ID, KMMP_NOD_NM
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN
			WHERE DPTH = 1
			AND OPT_TXB_ID = #{optTxbId}
		) AA
		ON A.KMMP_ID = AA.KMMP_ID
		AND A.URNK_KMMP_NOD_ID = AA.KMMP_NOD_ID
		LEFT JOIN (
			SELECT
				MAX(EV.EV_ID) AS EV_ID,
				AUTP.MLU_KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
				GROUP_CONCAT(
					CONCAT(IFNULL(MAX(AUTP.TPC_AVN), 0.5), ':',
						(SELECT KMMP_NOD_NM
						FROM LMS_LRM.AI_KMMP_NOD_RCSTN
						WHERE KMMP_NOD_ID = AUTP.TPC_ID
						LIMIT 1)
					)
				) AS TPC_AVN,
				MAX(EV.EV_DV_CD) AS EV_DV_CD,
				EV.EV_DTL_DV_CD,
				IFNULL(MAX(RS.EV_CMPL_YN), 'N') AS EV_CMPL_YN,
				MAX(TS.TS_RNGE_SEQ_NO) AS TS_RNGE_SEQ_NO,
				MAX(RS.CANS_CNT) AS CANS_CNT,
				MAX(RS.USR_ID) AS USR_ID,
				MAX(RS.MDF_DTM) AS MDF_DTM
			FROM LMS_LRM.EA_EV EV
			JOIN LMS_LRM.EA_EV_TS_RNGE TS
			ON EV.EV_ID = TS.EV_ID
			LEFT OUTER JOIN (
				SELECT AUTP.TPC_ID, AUTP.TPC_AVN, BLKNMD.MLU_KMMP_NOD_ID
				FROM LMS_LRM.AI_USRLY_TPC_PROF AUTP
				JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN_DTL BLKNMD 
				ON AUTP.TPC_ID = BLKNMD.TPC_KMMP_NOD_ID
				WHERE AUTP.USR_ID = #{usrId}
			)AUTP
			ON TS.TPC_ID = AUTP.TPC_ID
			LEFT JOIN LMS_LRM.EA_EV_RS RS
			ON EV.EV_ID = RS.EV_ID
			WHERE EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
			AND RS.USR_ID = #{usrId}
			AND EV.EV_DTL_DV_CD IS NOT NULL
			GROUP BY AUTP.MLU_KMMP_NOD_ID, EV.EV_DTL_DV_CD
		) C ON A.KMMP_NOD_ID = C.MLU_KMMP_NOD_ID
		LEFT JOIN LMS_LRM.AI_LRNR_LV ALLV
		ON ALLV.USR_ID = C.USR_ID
		AND ALLV.LU_KMMP_NOD_ID = A.KMMP_NOD_ID
		WHERE A.DPTH = 2
		AND A.OPT_TXB_ID = #{optTxbId}
		AND A.DEL_YN = 'N'
		<if test = 'mkLuLrmNodId != null and !mkLuLrmNodId.equals("")'>
			AND A.KMMP_NOD_ID = #{mkLuLrmNodId}
		</if>
		GROUP BY A.KMMP_NOD_ID, C.EV_DV_CD, C.EV_DTL_DV_CD
		ORDER BY MAX(A.RCSTN_ORDN)
	</select>


<!-- AI맞춤학습 과제출제건수조회 -->
<select id="selectCountEaAsnDataTcr" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqTcrReqDto" resultType="int">
		/** AlPlTcr-Mapper.xml - selectCountEaAsnDataTcr */
		SELECT
			COUNT(1) AS CNT
		FROM LMS_LRM.EA_ASN EA /* EA_과제 */
		INNER JOIN LMS_LRM.EA_ASN_RNGE EB /* EA_과제범위 */
		ON EA.ASN_ID = EB.ASN_ID
			AND EA.OPT_TXB_ID = EB.OPT_TXB_ID
			AND EA.LRN_TP_CD = EB.LRN_TP_CD
		WHERE EA.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
			AND EA.LRN_TP_CD = 'AL' /* 학습 코드 */
			AND EA.DEL_YN='N'
			AND EB.LU_NOD_ID = #{mluLrmpNodId} /* 단원ID */
			AND EB.LRN_TP_CD = 'AL'
			AND EB.DEL_YN = 'N'
	</select>
</mapper>