package com.aidt.api.al.pl.cm.rcm;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.common.AlCmUtil;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmResponseDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-28
 * @modify date 2024-02-28
 * @desc AI맞춤 문항추천 - 수학 맞춤학습1,2,선택학습
 */
@Slf4j
@Service
public class AiRcmTsshQtmMaService {
    
    private final String MAPPER_NAMESPACE = "api.al.pl.back.aiRcmTsshQtm.";
    @Autowired private CommonDao commonDao;

    @Autowired private AiRcmTsshQtmCommService commService;
    
    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
    
    
    /**
     * 현재 진행중인 토픽
     * 
     * */
    public AiRcmTsshQtmDto selectTpcPgrsRt(String optTxbId, String mluKmmpNodId, String usrId) {
    	AiRcmTsshQtmDto resultDto = new AiRcmTsshQtmDto();
    	
    	boolean breakChk = false;
    	
    	AiRcmTsshQtmReqDto req = new AiRcmTsshQtmReqDto();
    	req.setOptTxbId(optTxbId);
    	req.setMluKmmpNodId(mluKmmpNodId);
    	req.setUsrId(usrId);
    	List<AiRcmTsshQtmDto> rpcPgrsRtList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcPgrsRt", req);
    	if(null == rpcPgrsRtList || rpcPgrsRtList.isEmpty()) {
    		return resultDto;
    	}
    	
    	Integer lrnwTpcCnt = rpcPgrsRtList.size();
     	Integer lrnwCmplTpcCnt = 0;
     	
     	String nextTpcKmmpNodId = "";
     	String nextTpcKmmpNodNm = "";
     	
     	List<String> allRpcPgrsRtList = new ArrayList<String>();
    	 
         for (AiRcmTsshQtmDto dto : rpcPgrsRtList) {
             //해당토픽이 완료된 경우 다음토픽
             if(dto.getEvId() != null && dto.getTpcCmplYn() != null && dto.getTpcCmplYn().equals("Y")) {
            	 lrnwCmplTpcCnt++;
                 continue;
             }
             //토픽완료여부 저장오류 방어로직 - 네트워크 등의 오류로 prof에서 토픽완료여부를 저장하지못한경우를 체크 
//             if(dto.getEvId() != null && !dto.getTpcCmplYn().equals("Y") 
//            		 && dto.getQtmCnt() == 3 && dto.getQtmAnwCnt() == 3 && dto.getEvCmplYnCnt() == 3) {
//            	//맞춤2까지 다푼경우 토픽완료여부 N update하고 conticue
//            	AiRcmTsshQtmDto luevDto = new AiRcmTsshQtmDto();
// 				luevDto.setUsrId(req.getUsrId());
// 				luevDto.setEvId(req.getEvId());
// 				luevDto.setLuevCmplYn("Y");
// 				luevDto.setOptTxbId(optTxbId);
// 				luevDto.setMluKmmpNodId(mluKmmpNodId);
// 				luevDto.setTcKmmpNodId(dto.getTcKmmpNodId());
// 				luevDto.setTpcKmmpNodId(dto.getTpcKmmpNodId());
//            	commService.updateLuevCmplYnQuery(luevDto);
//            	continue;
//             }
             
             if(breakChk) {
            	 nextTpcKmmpNodId = dto.getTpcKmmpNodId();
            	 nextTpcKmmpNodNm = dto.getTpcKmmpNodNm();
            	 breakChk = false;
            	 break;
             }
             resultDto = dto;
             breakChk = true;
         }
         
         // 전체 진행할 토픽 추가
         for (AiRcmTsshQtmDto dto : rpcPgrsRtList) {
        	 if(dto.getNtnTpcYn() != null) {
        		 if(dto.getNtnTpcYn().equals("Y")) {
        			 allRpcPgrsRtList.add("[심화] " + dto.getTpcKmmpNodNm());
        		 }
        		 else {
        			 allRpcPgrsRtList.add(dto.getTpcKmmpNodNm());
        		 }
        	 } else {
        		 allRpcPgrsRtList.add(dto.getTpcKmmpNodNm());        		 
        	 }
         }
         
         resultDto.setNextTpcKmmpNodId(nextTpcKmmpNodId);
         resultDto.setNextTpcKmmpNodNm(nextTpcKmmpNodNm);
         resultDto.setLrnwTpcCnt(lrnwTpcCnt);
         resultDto.setLrnwCmplTpcCnt(lrnwCmplTpcCnt);
         resultDto.setCdnPthNm(AlCmUtil.makeFleCdnUrl(BUCKET_NAME, resultDto.getCdnPthNm()));
         resultDto.setAllRpcPgrsRtList(allRpcPgrsRtList);
         
         return resultDto;
    }
    
    /**
     * 수학 3.7V 로직
     * 토픽별 맞춤학습 진행 개별문항별로 추천
     *  @param mluKmmpNodId
     * 
     * */
    @Transactional
    public AiRcmTsshQtmResponseDto selectAiMaRcmQtm(AiRcmTsshQtmReqDto req){
		/* 운영교과서에 맞는 지식맵 노드 id가 파라미터 값으로 넘어왔는지 검증(2024.10.30) */
    	AiRcmTsshQtmResponseDto responseDto = new AiRcmTsshQtmResponseDto();
		req.setKmmpNodId(req.getMluKmmpNodId());
		int nodeCount = commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodCount", req);
		if (nodeCount < 1) {
			responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
			return responseDto;
//			throw new Error("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
		}

        List<AiRcmTsshQtmDto> resultList = new ArrayList<>();
        
        AiRcmTsshQtmDto tpcDto = new AiRcmTsshQtmDto();
        AiRcmTsshQtmDto Q1bf = new AiRcmTsshQtmDto();
    	AiRcmTsshQtmDto Q2bf = new AiRcmTsshQtmDto();
    	AiRcmTsshQtmDto Q3bf = new AiRcmTsshQtmDto();
    	AiRcmTsshQtmDto Q4bf = new AiRcmTsshQtmDto();
    	AiRcmTsshQtmDto Q5bf = new AiRcmTsshQtmDto();
    	Integer cansCnt = 0;
    	
        //평가생성필요여부
        boolean evCrtChk = false;
        
        //토픽 진행순서 확인
        tpcDto = selectTpcPgrsRt(req.getOptTxbId(), req.getMluKmmpNodId(), req.getUsrId());
        if(tpcDto.getTpcKmmpNodId() == null) {
        	responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("해당 단원은 더이상 진행할 토픽이 없습니다.");
			return responseDto;
//            throw new NullPointerException("해당 단원은 더이상 진행할 토픽이 없습니다.");
        }
        
        //추천할 문항순서
        Integer qtmOrdn = 0;
        if(tpcDto.getEvId() == null) { 
        	qtmOrdn = 1; 
        	tpcDto.setEvDtlDvCd("C1"); 
        	evCrtChk = true; 
    	}
        else if(tpcDto.getEvDtlDvCd().equals("C1") && tpcDto.getQtmCnt() == 1) { qtmOrdn = 2; }
        else if(tpcDto.getEvDtlDvCd().equals("C1") && tpcDto.getQtmCnt() == 2) { qtmOrdn = 3; }
        else if(tpcDto.getEvDtlDvCd().equals("C1") && tpcDto.getQtmCnt() == 3) { 
        	qtmOrdn = 4; 
        	tpcDto.setEvDtlDvCd("C2"); 
        	evCrtChk = true; 
    	}
        else if(tpcDto.getEvDtlDvCd().equals("C2") && tpcDto.getQtmCnt() == 1) { qtmOrdn = 5; }
        else if(tpcDto.getEvDtlDvCd().equals("C2") && tpcDto.getQtmCnt() == 2) { qtmOrdn = 6; }
        
        //학습자 수준 조회
        String lrnrVelTpCd = commonDao.select(MAPPER_NAMESPACE + "selectLrnrVelTpCd", req);
        if(lrnrVelTpCd == null) {lrnrVelTpCd = "NM";}
        req.setLrnrVelTpCd(lrnrVelTpCd);
        
        //토픽 내 문항리스트
		//이전학교급 문항여부 N으로 처리
        req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
        List<AiRcmTsshQtmDto> qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcRcmQtmList", req);
        if(qtmList.isEmpty()) {
        	responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("토픽 내 등록된 문항이 없습니다.");
			return responseDto;
//        	throw new NullPointerException("토픽 내 등록된 문항이 없습니다.");
        }
        
        //이전문항정보
        List<Integer> qtmBfCheck = new ArrayList<Integer>();
        if(qtmOrdn > 1) {
        	List<AiRcmTsshQtmDto> bfDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectBfQtmInfo", req);
        	for (AiRcmTsshQtmDto bf : bfDtoList) {
        		qtmBfCheck.add(bf.getQtmId());
    		}
        	
        	if (bfDtoList != null && !bfDtoList.isEmpty()) {
        		cansCnt = bfDtoList.get(0).getCansCnt();
        	    Q1bf = bfDtoList.size() > 0 ? bfDtoList.get(0) : null;
                Q2bf = bfDtoList.size() > 1 ? bfDtoList.get(1) : null;
                Q3bf = bfDtoList.size() > 2 ? bfDtoList.get(2) : null;
                Q4bf = bfDtoList.size() > 3 ? bfDtoList.get(3) : null;
                Q5bf = bfDtoList.size() > 4 ? bfDtoList.get(4) : null;
        	}
        }
        
        //느린사용자
        if(lrnrVelTpCd.equals("SL")) {
            if(qtmOrdn == 1) {
                if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
                }else {
                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
                }
            }
        	if(qtmOrdn == 2) {
        		if(Q1bf == null) {
        			responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
        		
    			if(Q1bf.getCansYn().equals("Y")) {
    				if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
    					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
    				}else {
    					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
    				}
        		}else {
        			//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q1bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q1bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q1bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
        		}
            }
        	else if(qtmOrdn == 3) {
        		if(Q1bf == null || Q2bf == null) {
        			responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
        		
        		if(Q1bf.getCansYn().equals("Y") && Q2bf.getCansYn().equals("Y")){
        			if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
        			}else {
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
        			}
        		}
        		else if(Q1bf.getCansYn().equals("Y") && Q2bf.getCansYn().equals("N")){
        			//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q2bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q2bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q2bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
        		}
        		else if(Q1bf.getCansYn().equals("N") && Q2bf.getCansYn().equals("Y")){
        			qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
        		}
        		else if(Q1bf.getCansYn().equals("N") && Q2bf.getCansYn().equals("N")){
        			if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
        				//이전토픽 문항에서 0.8 가까운문항
						//연결토픽지식맵노드ID 유무에 따라 이전학교급 문항여부 처리
						req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
						req.setLnkgTpcKmmpNodId(tpcDto.getLnkgTpcKmmpNodId());
						List<AiRcmTsshQtmDto> bfTpcQtmList;
						if (req.getLnkgTpcKmmpNodId() != null) {
							bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcPreQtmList", req);
						} else {
							bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcRcmQtmList", req);
						}
        				if(!(bfTpcQtmList == null) && !bfTpcQtmList.isEmpty()) {
        					qtmList = bfTpcQtmList;
        				}
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
        			}else {
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
        			}
        		}
            }
        	//재학습
        	else if(qtmOrdn == 4) {
            	if(Q1bf == null || Q2bf == null || Q3bf == null) {
            		responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
        		if(cansCnt == 0) {
        			if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
        				//이전토픽 문항에서 0.8 가까운문항
						//연결토픽지식맵노드ID 유무에 따라 이전학교급 문항여부 처리
						req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
						req.setLnkgTpcKmmpNodId(tpcDto.getLnkgTpcKmmpNodId());
						List<AiRcmTsshQtmDto> bfTpcQtmList;
						if (req.getLnkgTpcKmmpNodId() != null) {
							bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcPreQtmList", req);
						} else {
							bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcRcmQtmList", req);
						}
        				if(!(bfTpcQtmList == null) && !bfTpcQtmList.isEmpty()) {
        					qtmList = bfTpcQtmList;
        				}
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
        			}else {
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
        			}
            	}else {
            		qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
            	}
            }
        	else if(qtmOrdn == 5) {
            	if(Q1bf == null || Q2bf == null || Q3bf == null || Q4bf == null) {
            		responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
            	if(Q4bf.getCansYn().equals("Y")) {
            		if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
            			if(cansCnt == 1) {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
            			}else {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
            			}
            		}else {
            			if(cansCnt == 1) {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
            			}else {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
            			}
            		}
            	}else {
            		if(tpcDto.getAiDgnEvTpcAvn() >= 0.6 && cansCnt == 0) {
            			//이전토픽 문항에서 0.8 가까운문항
						//연결토픽지식맵노드ID 유무에 따라 이전학교급 문항여부 처리
						req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
						req.setLnkgTpcKmmpNodId(tpcDto.getLnkgTpcKmmpNodId());
						List<AiRcmTsshQtmDto> bfTpcQtmList;
						if (req.getLnkgTpcKmmpNodId() != null) {
							bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcPreQtmList", req);
						} else {
							bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcRcmQtmList", req);
						}
        				if(!(bfTpcQtmList == null) && !bfTpcQtmList.isEmpty()) {
        					qtmList = bfTpcQtmList;
        				}
        				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
            		}
            		else {
            			//이전"문항"의 유사/쌍둥이 조회
            			Map<String, Object> params = new HashMap<>();
            		    params.put("qtmId", Q4bf.getQtmId());
            		    params.put("rltQtmTpCd", "SI");
            		    params.put("sbjGrd", "MA");
            		    params.put("optTxbId", tpcDto.getOptTxbId());
            			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

						//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
						if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
            				String bfCtnDffdDvCd = Q4bf.getCtnDffdDvCd();
            				Double aiPredCansRt = Q4bf.getAiPredCansRt();
            				
//            				qtmList.stream()
//        			            .sorted(Comparator
//        			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//        			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//        			            .collect(Collectors.toList());
            				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
            					    .sorted(Comparator
            					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
            					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
            					        )
            					    .collect(Collectors.toList());
                				qtmList = sortedList;
                				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

        			        
            			}else {
            				qtmList = smlrList;
        				}
            		}
            	}
            }
        	else if(qtmOrdn == 6) {
            	if(Q1bf == null || Q2bf == null || Q3bf == null || Q4bf == null || Q5bf == null) {
            		responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
            	if(Q5bf.getCansYn().equals("Y")) {
            		if(cansCnt == 1) {
            			qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
            		}else if(cansCnt == 2) {
            			if(tpcDto.getAiDgnEvTpcAvn() < 0.6 && !Q4bf.getCansYn().equals("Y")) {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
            			}else {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
            			}
            		}else if(cansCnt == 3) {
            			if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
            			}else {
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
            			}
            		}
            	}else {
            		if(cansCnt == 0) {
            			if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
            				//이전토픽 문항에서 1.0 가까운문항
							//연결토픽지식맵노드ID 유무에 따라 이전학교급 문항여부 처리
							req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
							req.setLnkgTpcKmmpNodId(tpcDto.getLnkgTpcKmmpNodId());
							List<AiRcmTsshQtmDto> bfTpcQtmList;
							if (req.getLnkgTpcKmmpNodId() != null) {
								bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcPreQtmList", req);
							} else {
								bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcRcmQtmList", req);
							}
            				if(!(bfTpcQtmList == null) && !bfTpcQtmList.isEmpty()) {
            					qtmList = bfTpcQtmList;
            				}
            				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
            			}else {
            				//이전"문항"의 유사/쌍둥이 조회
                			Map<String, Object> params = new HashMap<>();
                		    params.put("qtmId", Q5bf.getQtmId());
                		    params.put("rltQtmTpCd", "SI");
                		    params.put("sbjGrd", "MA");
                		    params.put("optTxbId", tpcDto.getOptTxbId());
                			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

							//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
							if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
                				String bfCtnDffdDvCd = Q5bf.getCtnDffdDvCd();
                				Double aiPredCansRt = Q5bf.getAiPredCansRt();
                				
//                				qtmList.stream()
//            			            .sorted(Comparator
//            			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//            			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//            			            .collect(Collectors.toList());
                				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
                					    .sorted(Comparator
                					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
                					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
                					        )
                					    .collect(Collectors.toList());
                    				qtmList = sortedList;
                    				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

            			        
                			}else {
                				qtmList = smlrList;
            				}
            			}
            		}else if(cansCnt == 1) {
            			if(Q4bf.getCansYn().equals("Y")) {
            				//이전"문항"의 유사/쌍둥이 조회
                			Map<String, Object> params = new HashMap<>();
                		    params.put("qtmId", Q5bf.getQtmId());
                		    params.put("rltQtmTpCd", "SI");
                		    params.put("sbjGrd", "MA");
                		    params.put("optTxbId", tpcDto.getOptTxbId());
                			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

							//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
							if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
                				String bfCtnDffdDvCd = Q5bf.getCtnDffdDvCd();
                				Double aiPredCansRt = Q5bf.getAiPredCansRt();
                				
//                				qtmList.stream()
//            			            .sorted(Comparator
//            			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//            			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//            			            .collect(Collectors.toList());
                				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
                					    .sorted(Comparator
                					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
                					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
                					        )
                					    .collect(Collectors.toList());
                    				qtmList = sortedList;
                    				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

            			        
                			}else {
                				qtmList = smlrList;
            				}
            			}else {
            				if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
            					//이전토픽 문항에서 1.0 가까운문항
								//연결토픽지식맵노드ID 유무에 따라 이전학교급 문항여부 처리
								req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
								req.setLnkgTpcKmmpNodId(tpcDto.getLnkgTpcKmmpNodId());
								List<AiRcmTsshQtmDto> bfTpcQtmList;
								if (req.getLnkgTpcKmmpNodId() != null) {
									bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcPreQtmList", req);
								} else {
									bfTpcQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiMaTpcRcmQtmList", req);
								}
                				if(!(bfTpcQtmList == null) && !bfTpcQtmList.isEmpty()) {
                					qtmList = bfTpcQtmList;
                				}
                				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
            				}else {
            					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 1.0)));
            				}
            			}
            		}else if(cansCnt == 2) {
            			//이전"문항"의 유사/쌍둥이 조회
            			Map<String, Object> params = new HashMap<>();
            		    params.put("qtmId", Q5bf.getQtmId());
            		    params.put("rltQtmTpCd", "SI");
            		    params.put("sbjGrd", "MA");
            		    params.put("optTxbId", tpcDto.getOptTxbId());
            			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

						//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
						if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
            				String bfCtnDffdDvCd = Q5bf.getCtnDffdDvCd();
            				Double aiPredCansRt = Q5bf.getAiPredCansRt();
            				
//            				qtmList.stream()
//        			            .sorted(Comparator
//        			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//        			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//        			            .collect(Collectors.toList());
            				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
            					    .sorted(Comparator
            					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
            					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
            					        )
            					    .collect(Collectors.toList());
                				qtmList = sortedList;
                				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

        			        
            			}else {
            				qtmList = smlrList;
        				}
            		}
            	}
            }
            	
        }
        
		//빠른사용자
		else if(lrnrVelTpCd.equals("FS")) {
			//모두 강점토픽인 경우 최취약토픽3개에 대해 최상 혹은 상 문항에서 출제
			if(tpcDto.getAiDgnEvTpcAvn() > AlConstUtil.TPC_AVN_03) {
				//qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("05") && !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04"));
				qtmList.stream()
	            .sorted(Comparator
	            	.comparing((AiRcmTsshQtmDto qtm) -> "04".equals(qtm.getCtnDffdDvCd()) || "05".equals(qtm.getCtnDffdDvCd()))  // 상,최상을 최우선정렬
	                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - 0.2 )))  // 이전문항 예측문항률에 가까운 순으로 정렬
	            .collect(Collectors.toList());

			}
			
			if(qtmOrdn == 1) {
				if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
				}else {
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
				}
			}
			else if(qtmOrdn == 2) {
				if(Q1bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q1bf.getCansYn().equals("Y")) {
					if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
					}else {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.2)));
					}
				}else {
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q1bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q1bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q1bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
    					    .sorted(Comparator
    					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
    					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
    					        )
    					    .collect(Collectors.toList());
        				qtmList = sortedList;
        				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));
        				
        			}else {
        				qtmList = smlrList;
    				}
				}
            }
			else if(qtmOrdn == 3) {
				if(Q1bf == null || Q2bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q1bf.getCansYn().equals("Y") && Q2bf.getCansYn().equals("Y")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.2)));
				}
				else if(Q1bf.getCansYn().equals("Y") && Q2bf.getCansYn().equals("N")){
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q2bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q2bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q2bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
				}
				else if(Q1bf.getCansYn().equals("N") && Q2bf.getCansYn().equals("Y")){
					if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
					}else {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
					}
				}
				else if(Q1bf.getCansYn().equals("N") && Q2bf.getCansYn().equals("N")){
					if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
					}else {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
					}
				}
            }
			else if(qtmOrdn == 4) {
				if(Q1bf == null || Q2bf == null || Q3bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
				}else {
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
				}
			}
			else if(qtmOrdn == 5) {
				if(Q1bf == null || Q2bf == null || Q3bf == null || Q4bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q4bf.getCansYn().equals("Y")) {
					if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
					}else {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
					}
				}else {
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q4bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q4bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q4bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
				}
			}
			else if(qtmOrdn == 6) {
				if(Q1bf == null || Q2bf == null || Q3bf == null || Q4bf == null || Q5bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q4bf.getCansYn().equals("Y") && Q5bf.getCansYn().equals("Y")){
					if(tpcDto.getAiDgnEvTpcAvn() < 0.6) {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
					}else {
						qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.2)));
					}
				}
				else if(Q4bf.getCansYn().equals("Y") && Q5bf.getCansYn().equals("N")){
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q5bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q5bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q5bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
				}
				else if(Q4bf.getCansYn().equals("N") && Q5bf.getCansYn().equals("Y")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
				}
				else if(Q4bf.getCansYn().equals("N") && Q5bf.getCansYn().equals("N")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
				}
            }
		}
		
		//보통사용자(NM)
		else {
			if(qtmOrdn == 1) {
				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
			}
			else if(qtmOrdn == 2) {
				if(Q1bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q1bf.getCansYn().equals("Y")) {
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
				}else {
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q1bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q1bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q1bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
				}
            }
			else if(qtmOrdn == 3) {
				if(Q1bf == null || Q2bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q1bf.getCansYn().equals("Y") && Q2bf.getCansYn().equals("Y")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.2)));
				}
				else if(Q1bf.getCansYn().equals("Y") && Q2bf.getCansYn().equals("N")){
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q2bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q2bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q2bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
				}
				else if(Q1bf.getCansYn().equals("N") && Q2bf.getCansYn().equals("Y")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
				}
				else if(Q1bf.getCansYn().equals("N") && Q2bf.getCansYn().equals("N")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
				}
            }
			else if(qtmOrdn == 4) {
				if(Q1bf == null || Q2bf == null || Q3bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
			}
			else if(qtmOrdn == 5) {
				if(Q1bf == null || Q2bf == null || Q3bf == null || Q4bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q4bf.getCansYn().equals("Y")) {
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
				}else {
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q4bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
					if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q4bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q4bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				
        				qtmList = smlrList;
    				}
				}
			}
			else if(qtmOrdn == 6) {
				if(Q1bf == null || Q2bf == null || Q3bf == null || Q4bf == null || Q5bf == null) {
					responseDto.setErrorStatus("error");
        			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
        			return responseDto;
//        			throw new NullPointerException("이전문항 정보가 없습니다.");
        		}
				if(Q4bf.getCansYn().equals("Y") && Q5bf.getCansYn().equals("Y")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.4)));
				}
				else if(Q4bf.getCansYn().equals("Y") && Q5bf.getCansYn().equals("N")){
					//이전"문항"의 유사/쌍둥이 조회
        			Map<String, Object> params = new HashMap<>();
        		    params.put("qtmId", Q5bf.getQtmId());
        		    params.put("rltQtmTpCd", "SI");
        		    params.put("sbjGrd", "MA");
        		    params.put("optTxbId", tpcDto.getOptTxbId());
        			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);

					//유사쌍둥이 없는 경우 또는 이전문항에 출제된 문항일 경우 이전문항의 난이도로 출제 및 해당 난이도가 없는 경우 이전문항 예측문항률에 가까운 순으로 정렬한다.(2024.10.24)
        			if(smlrList == null || smlrList.isEmpty() || qtmBfCheck.contains(smlrList.get(0).getQtmId())) {
        				String bfCtnDffdDvCd = Q5bf.getCtnDffdDvCd();
        				Double aiPredCansRt = Q5bf.getAiPredCansRt();
        				
//        				qtmList.stream()
//    			            .sorted(Comparator
//    			                .comparing((AiRcmTsshQtmDto qtm) -> !bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은문항이 우선정렬
//    			                .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt)))  // 이전문항 예측문항률에 가까운 순으로 정렬
//    			            .collect(Collectors.toList());
        				List<AiRcmTsshQtmDto> sortedList = qtmList.stream()
        					    .sorted(Comparator
        					        .comparing((AiRcmTsshQtmDto qtm) -> bfCtnDffdDvCd.equals(qtm.getCtnDffdDvCd()))  // 이전문항과 난이도가 같은 문항이 우선 정렬
        					        .thenComparing(qtm -> Math.abs(qtm.getAiPredCansRt() - aiPredCansRt))  // 이전문항 예측문항률에 가까운 순으로 정렬
        					        )
        					    .collect(Collectors.toList());
            				qtmList = sortedList;
            				qtmList.forEach(qtm -> log.debug(String.valueOf(qtm)));

    			        
        			}else {
        				qtmList = smlrList;
    				}
				}
				else if(Q4bf.getCansYn().equals("N") && Q5bf.getCansYn().equals("Y")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.6)));
				}
				else if(Q4bf.getCansYn().equals("N") && Q5bf.getCansYn().equals("N")){
					qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto -> Math.abs(AiRcmTsshQtmDto.getAiPredCansRt() - 0.8)));
				}
            }
		}
        
        //토픽 내 중복문항 제외하고 sort대로 첫번쨰문항 추출
        if(null == qtmList || qtmList.isEmpty()) {
        	responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
			return responseDto;
//        	throw new IllegalArgumentException("문항정보가 없습니다.");
        }
        else {
			for (int j = 0; j < qtmList.size(); j++) {
				if(!qtmBfCheck.contains(qtmList.get(j).getQtmId())) {
					resultList.add(qtmList.get(j));
					break;
				}
			}

			if(resultList.isEmpty()) {
				responseDto.setErrorStatus("error");
    			responseDto.setErrorMessage("이전문항 정보가 없습니다.");
    			return responseDto;
//				throw new IllegalArgumentException("문항정보가 없습니다.");
			}
		}
        
        //평가생성
        if(evCrtChk) {
		  	req.setQstCnt(3);
		  	req.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
		  	req.setEvDvCd("AE");
		  	req.setEvDtlDvCd(tpcDto.getEvDtlDvCd());
		  	req.setLrnrVelTpCd(lrnrVelTpCd);

			resultList.get(0).setMluKmmpNodNm(tpcDto.getMluKmmpNodNm());
			resultList.get(0).setTpcKmmpNodNm(tpcDto.getTpcKmmpNodNm());

			String evNm = tpcDto.getMluKmmpNodNm();
			evNm += " - ";
			evNm += tpcDto.getTpcKmmpNodNm();
			evNm += " - ";
			evNm += tpcDto.getEvDtlDvCd().equals("C1") ? "AI 맞춤 학습" : "AI 2차 학습";
			req.setEvNm(evNm);

			Integer evId = commService.insertEaEvHst(req, resultList);

			responseDto.setEvId(evId);
			responseDto.setQtmId(resultList.get(0).getQtmId().toString());
        }
        //평가에 문항추가
        else {
        	AiRcmTsshQtmHistDto dto = new AiRcmTsshQtmHistDto();
        	dto.setUsrId(req.getUsrId());
        	dto.setEvId(tpcDto.getEvId());
        	dto.setQtmId(resultList.get(0).getQtmId());

			// 2차 학습의 경우 qtmOrdn 값 조정
			if (qtmOrdn == 5 || qtmOrdn == 6) qtmOrdn = qtmOrdn - 3;

        	dto.setQtmOrdn(qtmOrdn);
        	dto.setQtmDffdDvCd(resultList.get(0).getCtnDffdDvCd());
        	dto.setTpcKmmpNodId(tpcDto.getTpcKmmpNodId());
        	dto.setDelYn("N");
        	
        	commService.insertEaEvQtm(dto);
        	
        	responseDto.setEvId(tpcDto.getEvId());
        	responseDto.setQtmId(resultList.get(0).getQtmId().toString());
        }
        log.debug("문항ID: " + resultList.get(0).getQtmId());
        
        return responseDto;
    }
    
    
  //3.8v에서는 주석처리 ~
//    /**
//     * 수학 AI맞춤학습1 - 총괄평가 이력을 토대로 학습자 문항추천
//     * 
//     *  @param List<AiRcmTsshQtmDto>
//     *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
//     *  @return List<AiRcmTsshQtmDto>
//     * */
//    public List<AiRcmTsshQtmDto> getMaC1QtmList(List<AiRcmTsshQtmDto> list, String lrnrVelTpCd, AiRcmTsshQtmReqDto dto){
//        List<AiRcmTsshQtmDto> resultList = new ArrayList<>();
//        List<Integer> qtmCheck = new ArrayList<>();
//        
//        //문항리스트를 토픽별로 매핑
//        Map<String, List<AiRcmTsshQtmDto>> tpcByListMap = list.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId));
//        //총괄평가의 정오정보
//        AiRcmTsshQtmReqDto temp = new AiRcmTsshQtmReqDto();
//        temp.setOptTxbId(dto.getOptTxbId());
//        temp.setUsrId(dto.getUsrId());
//        temp.setMluKmmpNodId(dto.getMluKmmpNodId());
//        temp.setEvDtlDvCd(AlConstUtil.EV_DTL_DV_CD_OV);
//        List<AiRcmTsshQtmDto> ugCansList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmCansInfo", temp);
//        if(ugCansList.isEmpty() || ugCansList.size() == 0) {
//            throw new IllegalArgumentException("이전 평가 정보가 없습니다.");
//        }
//        
//        //기존문항 중복방지
//        for (AiRcmTsshQtmDto ug : ugCansList) {
//            qtmCheck.add(ug.getQtmId());
//        }
//        
//        for (AiRcmTsshQtmDto ug : ugCansList) {
//            //토픽(ug.getTpcKmmpNodId()) 문항리스트
//            List<AiRcmTsshQtmDto> qtmList = new ArrayList<>(tpcByListMap.get(ug.getTpcKmmpNodId()));
//            
//            //빠른학습자
//            if(lrnrVelTpCd.equals("FS")) {
//                if(ug.getCansYn().equals("Y")) {
//                    //‘상’ 수준 문제에서 정답 예측값이 가장 높은 문항
//                    qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04"));
//                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                }else {
//                    //‘중‘ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                    qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                }
//            }
//            //보통학습자
//            else if(lrnrVelTpCd.equals("NM")) {
//                if(ug.getCansYn().equals("Y")) {
//                    //‘중‘ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                    qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                }else {
//                    //‘중’ 수준 문제에서 정답 예측값이 가장 높은 문항
//                    qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                }
//                
//            }
//            //느린학습자
//            else if(lrnrVelTpCd.equals("SL")) {
//                if(ug.getCansYn().equals("Y")) {
//                    //‘중‘ 수준 문제에서 정답 예측값이 가장 높은 문항
//                    qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                }else {
//                    //오답:‘하’ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                    qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02"));
//                    qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                }
//            }
//            //토픽 내 정오가 같은상황일때 중복 문항추천 방지
//            for (AiRcmTsshQtmDto qtm : qtmList) {
//                if(!qtmCheck.contains(qtm.getQtmId())) {
//                    resultList.add(qtm);
//                    qtmCheck.add(qtm.getQtmId());
//                    break;
//                }
//            }
//        }
//        
//        return resultList;
//    }
//    
//    /**
//     * 수학 AI맞춤학습2 - 총괄평가,맞춤학습1 이력을 토대로 학습자 문항추천
//     * 
//     *  @param List<AiRcmTsshQtmDto>
//     *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
//     *  @return List<AiRcmTsshQtmDto>
//     * */
//    public List<AiRcmTsshQtmDto> getMaC2QtmList(List<AiRcmTsshQtmDto> list, String lrnrVelTpCd, AiRcmTsshQtmReqDto dto){
//        List<AiRcmTsshQtmDto> resultList = new ArrayList<>();
//        List<Integer> qtmCheck = new ArrayList<>();
//        
//        //문항리스트를 토픽별로 매핑
//        Map<String, List<AiRcmTsshQtmDto>> tpcByListMap = list.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId));
//        //총괄평가 정오정보
//        AiRcmTsshQtmReqDto temp = new AiRcmTsshQtmReqDto();
//        temp.setOptTxbId(dto.getOptTxbId());
//        temp.setUsrId(dto.getUsrId());
//        temp.setMluKmmpNodId(dto.getMluKmmpNodId());
//        temp.setEvDtlDvCd(AlConstUtil.EV_DTL_DV_CD_OV);
//        List<AiRcmTsshQtmDto> ugCansList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmCansInfo", temp);
//        //맞춤학습1 정오정보
//        temp.setEvDtlDvCd("C1");
//        List<AiRcmTsshQtmDto> c1CansList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmCansInfo", temp);
//        
//        if(ugCansList.isEmpty() || ugCansList.size() == 0 || c1CansList.isEmpty() || c1CansList.size() == 0) {
//            throw new IllegalArgumentException("이전 평가 정보가 없습니다.");
//        }
//        //기존문항 중복방지
//        for (AiRcmTsshQtmDto ug : ugCansList) {
//            qtmCheck.add(ug.getQtmId());
//        }
//        for (AiRcmTsshQtmDto c1 : c1CansList) {
//            qtmCheck.add(c1.getQtmId());
//        }
//
//        for (AiRcmTsshQtmDto c1 : c1CansList) {
//            //토픽(ug.getTpcKmmpNodId()) 문항리스트
//            List<AiRcmTsshQtmDto> qtmList = new ArrayList<>(tpcByListMap.get(c1.getTpcKmmpNodId()));
//            
//            //빠른학습자
//            if(lrnrVelTpCd.equals("FS")) {
//                //상 난이도 
//                if(c1.getCtnDffdDvCd().equals("04")) {
//                    if(c1.getCansYn().equals("Y")) {
//                        //최상 또는 상에서 정답예측값이 가장 낮은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !(AiRcmTsshQtmDto.getCtnDffdDvCd().equals("05") || AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04")));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                    }else {
//                        //중 에서 예측 낮은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                    }
//                }
//                //중 난이도
//                else if(c1.getCtnDffdDvCd().equals("03")) {
//                    if(c1.getCansYn().equals("Y")) {
//                        //중에서 예측값 낮은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                    }else {
//                        //중 에서 예측 높은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                    }
//                }
//            }
//            //보통학습자
//            else if(lrnrVelTpCd.equals("NM")) {
//                for (AiRcmTsshQtmDto ug : ugCansList) {
//                    if(c1.getTpcKmmpNodId().equals(ug.getTpcKmmpNodId()) && c1.getQtmOrdn() == ug.getQtmOrdn()) {
//                        String ugCansYn = ug.getCansYn();
//                        
//                        if(ugCansYn.equals("Y") && c1.getCansYn().equals("Y")) {
//                            //‘상‘ 수준 문제에서 정답 예측값이 가장 높은 문항
//                            qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04"));
//                            qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                        }else if(ugCansYn.equals("Y") && c1.getCansYn().equals("N")) {
//                            //‘중’ 수준 문제에서 정답 예측값이 가장 높은 문항
//                            qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                            qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                        }else if(ugCansYn.equals("N") && c1.getCansYn().equals("Y")) {
//                            //‘중’ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                            qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                            qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                        }else if(ugCansYn.equals("N") && c1.getCansYn().equals("N")) {
//                            //‘하‘ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                            qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02"));
//                            qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                        }
//                    }
//                }
//            }
//            //느린학습자
//            else if(lrnrVelTpCd.equals("SL")) {
//                //중 난이도 
//                if(c1.getCtnDffdDvCd().equals("03")) {
//                    if(c1.getCansYn().equals("Y")) {
//                        //‘중’ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                    }else {
//                        //‘하‘ 수준 문제에서 정답 예측값이 가장 낮은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02"));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//                    }
//                }
//                //하 난이도
//                else if(c1.getCtnDffdDvCd().equals("02")) {
//                    if(c1.getCansYn().equals("Y")) {
//                        //‘중’ 수준 문제에서 정답 예측값이 가장 높은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                    }else {
//                        //‘최하’ 수준 문항 또는 ‘하’ 수준에서 정답 예측값이 가장 높은 문항
//                        qtmList.removeIf(AiRcmTsshQtmDto -> !(AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02") || AiRcmTsshQtmDto.getCtnDffdDvCd().equals("01")));
//                        qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//                    }
//                }
//            }
//            //토픽 내 정오가 같은상황일때 중복 문항추천 방지
//            for (AiRcmTsshQtmDto qtm : qtmList) {
//                if(!qtmCheck.contains(qtm.getQtmId())) {
//                    resultList.add(qtm);
//                    qtmCheck.add(qtm.getQtmId());
//                    break;
//                }
//            }
//        }
//        
//        return resultList;
//    }
//    
//    /**
//     * 수학 AI집중학습(선택학습)
//     * 
//     *  @param List<AiRcmTsshQtmDto>
//     *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
//     *  @return List<AiRcmTsshQtmDto>
//     * */
//    public List<AiRcmTsshQtmDto> getMaC3QtmList(List<AiRcmTsshQtmDto> list, String lrnrVelTpCd){
//        List<AiRcmTsshQtmDto> resultList = new ArrayList<>();
//        
//        //취약토픽 존재여부
//        Optional<AiRcmTsshQtmDto> vlnQtmList = list.stream()
//                .filter(AiRcmTsshQtmDto -> AiRcmTsshQtmDto.getTpcAvn() < AlConstUtil.TPC_AVN_01)
//                .findFirst();
//        
//        //취약토픽이 존재하는 경우 취약토픽 전체
//        if(vlnQtmList.isPresent()) {
//            list.removeIf(AiRcmTsshQtmDto -> AiRcmTsshQtmDto.getTpcAvn() >= AlConstUtil.TPC_AVN_01);
//        }
//        else {
//            //토픽점수가 낮은순으로 정렬
//            list.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredAvgScr)//토픽별 평균정답률
//                    .thenComparingDouble(AiRcmTsshQtmDto::getAiPredAvgCansRt));//토픽별 AI예측 평균 정답률
//        }
//        
//        //토픽별로 mapping
//        Map<String, List<AiRcmTsshQtmDto>> tpcByListMap = list.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId));
//        int keyCount = 0;
//        
//        //토픽당 2문제씩 출제
//        for( String key : tpcByListMap.keySet() ){
//            keyCount++;
//            List<AiRcmTsshQtmDto> tpcByList = tpcByListMap.get(key);
//            
//            //취약토픽이 존재하지 않는경우 최취약 토픽 3개
//            if(!vlnQtmList.isPresent() && keyCount > 3) { break; }
//            
//            //정답예측률이 낮은 순으로 정렬
//            tpcByList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//            
//            for (int i = 0; i < tpcByList.size(); i++) {
//                if(i < 2) {
//                    resultList.add(tpcByList.get(i));
//                }
//            }
//        }
//        
//        //느린사용자일 경우 최하문제이고 정오가 N인 문항이면 이전토픽 문항으로 대체
//        for (AiRcmTsshQtmDto dto : resultList) {
//            if(lrnrVelTpCd.equals("SL") && dto.getCtnDffdDvCd().equals("01") && dto.getCansYn().equals("N")) {}
//            //TODO 이전토픽문항 조회후 dto 대체
//        }
//    
//        return resultList;
//    }
//    //여기까지~
  //3.8v에서는 주석처리
    
    
    /**
     * 단원 내 오답문항 풀기 - DIY
     * 
     * @param AiRcmTsshQtmDto
     * @return AiRcmTsshQtmResponseDto
     */
    @Transactional
    public AiRcmTsshQtmResponseDto selectIansQtmTxm(AiRcmTsshQtmReqDto dto) {
		/* 운영교과서에 맞는 지식맵 노드 id가 파라미터 값으로 넘어왔는지 검증(2024.10.30) */
		dto.setKmmpNodId(dto.getMluKmmpNodId());
		AiRcmTsshQtmResponseDto responseDto = new AiRcmTsshQtmResponseDto();

		int nodeCount = commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodCount", dto);
		if (nodeCount < 1) {
			responseDto.setErrorStatus("error");
	        responseDto.setErrorMessage("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
	        return responseDto;
	    }

        
        //오답풀이횟수 조회
        Integer iansEvCount = commonDao.select(MAPPER_NAMESPACE + "selectIansEvCount", dto);
        if(iansEvCount > 0) {
            dto.setIansEvHstYn("Y");
        }
        List<AiRcmTsshQtmDto> qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectIansQtmList", dto);
        
        if(qtmList.isEmpty() || qtmList.size() <= 0) {
        	responseDto.setErrorStatus("error");
        	  responseDto.setErrorMessage("문항정보가 없습니다.");
        	  return responseDto;
//            throw new IllegalArgumentException("문항정보가 없습니다.");
        }
        
        dto.setEvDvCd("DE");
        dto.setEvDtlDvCd("C1");
        int evId = commService.insertEaEvHst(dto, qtmList);
        
        responseDto.setEvId(evId);
        return responseDto;
    }
    
    
}
