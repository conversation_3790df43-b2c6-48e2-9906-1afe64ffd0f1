package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptAlPlTcInfoDto {

	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="중단원 지식맵 ID")
	private String mKmmpNodId;
	
	@Parameter(name="중단원 지식맵 NM")
	private String mKmmpNodNm;
	
	@Parameter(name="차시 지식맵 ID")
	private String tcKmmpNodId;
	
	@Parameter(name="차시 지식맵 Nm")
	private String tcKmmpNodNm;
	
	@Parameter(name="차시 DPTH")
	private String dpth;
	
	@Parameter(name="토픽 지식맵 ID")
	private String tpcKmmpNodId;
	
	@Parameter(name="토픽 지식맵 Nm")
	private String tpcKmmpNodNm;
	
	@Parameter(name="토픽 숙련도")//영어 (토픽 평균)
	private String avgTpcAvn;
			
	@Parameter(name="평가 id")
	private String evId;
	
	@Parameter(name="평가 풀이 시간")
	private String evTmScnt;
	
	@Parameter(name="상세구분코드")	//OV, C1, C2
	private String evDtlEvCd;
	
	@Parameter(name="평가 완료 여부")
	private String evCmplYn;
	
	@Parameter(name="마지막 수정일")
	private String evRsMdfDtm;
	
	@Parameter(name="토픽 정보")
	private List<EaLrnRptAlPlTpcInfoDto> tpcListInfo;
	
	@Parameter(name="토픽 ID")
	private String tpCId;
	
	@Parameter(name="총 풀이 문항수")
	private String totalQtmCount;
	
	@Parameter(name="총 정답수")
	private String cansYnCount;
	
	@Parameter(name="평가 결과 테이블의 총 정답수")
	private String cansCnt;
	
	@Parameter(name="총 풀이시간합")
	private String xplTmScnt;
	
	@Parameter(name="단원평가완료여부")
	private String luevCmplYn;
	
	@Parameter(name="OV 정보 저장")
	private List<EaLrnRptAlPlOvInfoDto> ovInfoDto;
	
	@Parameter(name="맞춤학습 정보 저장")	//차시별 저장
	private List<EaLrnRptAlPlOvInfoDto> cDetailInfoDto;
	
	@Parameter(name="맞춤학습 정보 저장")
	private List<EaLrnRptAlPlTcInfoDto> tcCInfoTotDto;
	
}
