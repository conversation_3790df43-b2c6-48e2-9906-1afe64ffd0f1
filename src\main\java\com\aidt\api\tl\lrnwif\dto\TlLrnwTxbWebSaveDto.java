package com.aidt.api.tl.lrnwif.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email 
 * @create date 2024-03-13 10:53:20
 * @modify date 2024-03-13 10:53:20
 * @desc TlLrnwTxbWebDto 교과서/익힘책 canvas object 저장 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlLrnwTxbWebSaveDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    /** 사용자ID */
    @Parameter(name="학습사용자ID")
    private String usrId;
    
    /** 교과서익힘책구분코드 (TE=교과서, WO=익힘책)*/
    @Parameter(name="교과서익힘책구분코드 (TE=교과서, WO=익힘책)")
    @NotBlank(message = "{field.required}")
    private String txbWebDvCd;

    /** 페이지번호 */
    @Parameter(name="페이지번호")
    private String pgeNo;
    
    /** 첨부ID */
    @Parameter(name="첨부ID")
	private Integer annxId;
    
    /** 첨부파일ID */
    @Parameter(name="첨부파일ID")
	private Integer annxFleId;

    /** CDN경로명 */
    @Parameter(name="CDN경로명")
    private String cdnPthNm;

    /** 접속DB인스턴스ID */
    @Parameter(name="접속DB인스턴스ID")
    private String dbId;
}
