<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.fdbk.cm">

	<!-- AI 단원 피드백  -->
	<select id="selectFdbkList" resultType="com.aidt.api.al.fdbk.dto.AiFdbkDto">
		SELECT
			AI_FDBK_ID,
			OPT_TXB_ID,
			USR_ID,
			LRMP_NOD_ID,
			FDBK_CN,
			MDF_DTM
		FROM
			lms_lrm.ai_fdbk
		WHERE
			OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
	</select>

	<!-- AI 학생별 단원 피드백  -->
	<select id="selectFdbk" resultType="com.aidt.api.al.fdbk.dto.AiFdbkDto">
		SELECT
			AI_FDBK_ID,
			OPT_TXB_ID,
			USR_ID,
			LRMP_NOD_ID,
			FDBK_CN,
			MDF_DTM
		FROM
			lms_lrm.ai_fdbk
		WHERE
			OPT_TXB_ID = #{optTxbId}
		AND USR_ID = #{usrId}
		AND LRMP_NOD_ID = #{lrmpNodId}
	</select>

	<!-- AI 단원 피드백 저장 -->
	<insert id="insertFdbk" parameterType="com.aidt.api.al.fdbk.dto.AiFdbkDto">
		INSERT INTO lms_lrm.ai_fdbk(
			OPT_TXB_ID,
			USR_ID,
			LRMP_NOD_ID,
			FDBK_CN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		) VALUES (
			#{optTxbId},
			#{usrId},
			#{lrmpNodId},
			#{fdbkCn},
			#{tcrId},
			NOW(),
			#{tcrId},
			NOW(),
			'DB_ID'
		)
	</insert>

	<!-- AI 단원 피드백 수정 -->
	<update id="updateFdbk" parameterType="com.aidt.api.al.fdbk.dto.AiFdbkDto">
		UPDATE lms_lrm.ai_fdbk
		SET
			FDBK_CN = #{fdbkCn},
		  	MDFR_ID = #{tcrId},
		  	MDF_DTM = NOW()
		WHERE
		  	OPT_TXB_ID = #{optTxbId}
		AND USR_ID = #{usrId}
		AND LRMP_NOD_ID = #{lrmpNodId}
	</update>

	<!-- AI 피드백 기준 데이터 목록 -->
	<select id="selectFdbkBsData" resultType="com.aidt.api.al.fdbk.dto.AiFdbkBsDataDto">
		SELECT
			AI_FDBK_BS_DATA_ID,
			AI_FDBK_NO,
			AI_FDBK_DV_CD,
            FDBK_CN
		FROM
			lms_lrm.ai_fdbk_bs_data
		WHERE
			AI_FDBK_NO = #{fdbkNo}
		  AND AI_FDBK_DV_CD = #{fdbkDvCd}
		ORDER BY RAND() LIMIT 1
	</select>

	<!-- AI 피드백 기준 데이터 -->
	<select id="selectFdbkBsDataList" resultType="java.lang.String">
		<trim prefixOverrides="UNION ALL">
		<foreach item="no" index="index" collection="aiFdbkNoList">
			UNION ALL
			(SELECT
				FDBK_CN
			FROM
				lms_lrm.ai_fdbk_bs_data
			WHERE
				AI_FDBK_NO = #{no}
			  <if test='no eq "1"'>
				  AND AI_FDBK_DV_CD = #{aiFdbkDvCd}
			  </if>
			  <if test='no eq "2"'>
			  	<choose>
					<when test='aiFdbkDvCd eq null or aiFdbkDvCd eq ""'>
						AND AI_FDBK_DV_CD = '1'
					</when>
					<otherwise>
						AND AI_FDBK_DV_CD = '2'
					</otherwise>
				</choose>
			  </if>
			ORDER BY RAND() LIMIT 1)
		</foreach>
		</trim>
	</select>

	<!-- AI 피드백 정답률 -->
	<select id="selectCorrectRateList" parameterType="hashmap" resultType="com.aidt.api.al.fdbk.dto.AiFdbkCorrectRateDto">
		(SELECT /*+ JOIN_FIXED_ORDER() */
			 EQA.USR_ID
			  , EE.OPT_TXB_ID
		      , EETR.LU_LRMP_NOD_ID
			  , EE.EV_DTL_DV_CD
			  , ROUND(SUM(IF(EQA.CANS_YN = 'Y', 1, 0)) / COUNT(EQA.CANS_YN) * 100, 1) AS CORRECT_RATE
		 FROM LMS_LRM.EA_EV EE
				  JOIN LMS_LRM.EA_EV_TS_RNGE EETR ON EETR.EV_ID = EE.EV_ID
				  JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
				  JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EEQ.EV_ID AND EQA.QTM_ID = EEQ.QTM_ID
		 WHERE
			 EE.OPT_TXB_ID = #{optTxbId}
		   AND EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD = 'ST'
		 GROUP BY EQA.USR_ID, EE.OPT_TXB_ID, EETR.LU_LRMP_NOD_ID, EE.EV_DTL_DV_CD)
		UNION ALL
		(SELECT /*+ JOIN_FIXED_ORDER() */
			 EQA.USR_ID
			  , EE.OPT_TXB_ID
			  , RSLNR.LRMP_NOD_ID
			  , EE.EV_DTL_DV_CD
			  , ROUND(SUM(IF(EQA.CANS_YN = 'Y', 1, 0)) / COUNT(EQA.CANS_YN) * 100, 1) AS CORRECT_RATE
		 FROM LMS_LRM.EA_EV EE
				  JOIN LMS_LRM.EA_EV_TS_RNGE EETR ON EETR.EV_ID = EE.EV_ID
				  JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN RSLNR ON RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID AND EE.OPT_TXB_ID = RSLNR.OPT_TXB_ID
				  JOIN lms_lrm.ea_ev_qtm EEQ ON EE.EV_ID = EEQ.EV_ID
				  JOIN lms_lrm.ea_ev_qtm_anw EQA ON EQA.EV_ID = EEQ.EV_ID AND EQA.QTM_ID = EEQ.QTM_ID
		 WHERE
			 EE.OPT_TXB_ID = #{optTxbId}
		   AND RSLNR.LLU_NOD_ID = #{lrmpNodId}
		   AND
			 RSLNR.DPTH = 1
		   AND (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('UD', 'UG'))
		 GROUP BY EQA.USR_ID, EE.OPT_TXB_ID, RSLNR.LRMP_NOD_ID, EE.EV_DTL_DV_CD)
		UNION ALL
		(SELECT /*+ JOIN_FIXED_ORDER() */
			 EEQA.USR_ID
			  , EE.OPT_TXB_ID
			  , #{lrmpNodId} as LRMP_NOD_ID
			  , EE.EV_DTL_DV_CD
			  , ROUND(SUM(IF(EEQA.CANS_YN = 'Y', 1, 0)) / COUNT(EEQA.CANS_YN) * 100, 1) AS CORRECT_RATE
		 FROM LMS_LRM.EA_EV EE
				  INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
				  INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
							 ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID
		 WHERE EE.OPT_TXB_ID = #{optTxbId}
		   AND EE.EV_DV_CD = 'AE' AND EE.EV_DTL_DV_CD = 'OV'
		   AND exists (select * from ea_ev_ts_rnge z where z.EV_ID=EE.EV_ID AND   LU_LRMP_NOD_ID=#{lrmpNodId})
		 GROUP BY EEQA.USR_ID, EE.OPT_TXB_ID, EE.EV_DTL_DV_CD)
		ORDER BY CORRECT_RATE DESC
	</select>

	<!-- AI 피드백 토픽별 성취도 순위 -->
	<select id="selectAchRankList" parameterType="hashmap" resultType="com.aidt.api.al.fdbk.dto.AiFdbkAchRankDto">
		SELECT FRESULT.*,
		   RANK() OVER (
		   PARTITION BY FRESULT.USR_ID
		   ORDER BY FRESULT.CORRECT_RATE DESC, FRESULT.CORRECT_CNT DESC, FRESULT.INCORRECT_CNT ,FRESULT.TPC_ID DESC) SORT
		FROM (
			SELECT RESULT.USR_ID
				   , RESULT.OPT_TXB_ID
				   , RESULT.TPC_ID
			       , RESULT.LRMP_NOD_ID
				   , ANY_VALUE(RESULT.KMMP_NOD_NM) AS KMMP_NOD_NM
				   , SUM(IF(RESULT.CANS_YN = 'Y', 1, 0))                                   AS CORRECT_CNT
				   , SUM(IF(RESULT.CANS_YN = 'Y', 0, 1))                                   AS INCORRECT_CNT
				   , ROUND(SUM(IF(RESULT.CANS_YN = 'Y', 1, 0)) / COUNT(RESULT.CANS_YN) * 100, 1) AS CORRECT_RATE
				FROM (
						 (SELECT
							   EQA.USR_ID
							   , EE.OPT_TXB_ID
							   , RSLNR.LRMP_NOD_ID
							   , BEQMB.TPC_ID
							   , BKN.KMMP_NOD_NM
							   , EQA.CANS_YN
						  FROM LMS_LRM.EA_EV EE
							   JOIN LMS_LRM.EA_EV_TS_RNGE EETR ON EETR.EV_ID = EE.EV_ID
							   JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN RSLNR ON RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID and EE.OPT_TXB_ID = RSLNR.OPT_TXB_ID
							   JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
							   JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EEQ.EV_ID AND EQA.QTM_ID = EEQ.QTM_ID
							   JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQMB ON EE.EVSH_ID = BEQMB.EVSH_ID AND EEQ.QTM_ID = BEQMB.QTM_ID
							   JOIN LMS_CMS.BC_KMMP_NOD BKN ON BEQMB.TPC_ID = BKN.KMMP_NOD_ID
						WHERE EE.OPT_TXB_ID = #{optTxbId}
						AND RSLNR.LLU_NOD_ID = #{lrmpNodId}
						AND RSLNR.DPTH = 1
						AND EE.EV_DV_CD = 'SE' AND EE.EV_DTL_DV_CD = 'UG')
					UNION ALL
						 (SELECT
							   EEQA.USR_ID
							   , EE.OPT_TXB_ID
							   , #{lrmpNodId} as LRMP_NOD_ID
							   , EEQ.TPC_ID
							   , EAETR.TPC_KMMP_NOD_NM
							   , EEQA.CANS_YN
						 FROM LMS_LRM.EA_EV EE
								   INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
								   INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
								   INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID
								   INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
								       ON EE.EV_ID = EAETR.EV_ID
								              and eeq.TPC_ID = EAETR.TPC_KMMP_NOD_ID
								              and EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
						 WHERE EE.OPT_TXB_ID = #{optTxbId}
							AND EE.EV_DV_CD = 'AE' AND EE.EV_DTL_DV_CD = 'OV'
						   AND exists (select * from ea_ev_ts_rnge z where z.EV_ID=EE.EV_ID AND LU_LRMP_NOD_ID=#{lrmpNodId})
						 )
					 ) RESULT
			group by result.TPC_ID, result.USR_ID, result.OPT_TXB_ID, result.LRMP_NOD_ID) FRESULT
	</select>

	<select id="selectGrowthRankList" parameterType="hashmap" resultType="com.aidt.api.al.fdbk.dto.AiFdbkGrowthRankDto">
		SELECT RESULT.EV_DTL_DV_CD
			<if test="stuId != null and stuId != ''">
		     , RESULT.USR_ID
			</if>
			 , RESULT.QP_CL_GRU_ID
			 , ANY_VALUE(RESULT.QP_CL_GRU_NM) as QP_CL_GRU_NM
			 , #{optTxbId} AS OPT_TXB_ID
			 , #{lrmpNodId} AS LRMP_NOD_ID
			 , SUM(IF(RESULT.CANS_YN = 'Y', 1, 0))                                   AS CORRECT_CNT
			 , SUM(IF(RESULT.CANS_YN = 'Y', 0, 1))                                   AS INCORRECT_CNT
			 , COUNT(RESULT.CANS_YN)                                   				 AS TOTAL_CNT
			 , ROUND(SUM(IF(RESULT.CANS_YN = 'Y', 1, 0)) / COUNT(RESULT.CANS_YN) * 100, 1) AS CORRECT_RATE
		FROM (
				 (SELECT
					  EQA.USR_ID
					   , EQA.CANS_YN
					   , EE.EV_DTL_DV_CD
					   , QQA.QP_CL_GRU_ID
					   , QQD.QP_DMN_NM as QP_CL_GRU_NM
				  FROM LMS_LRM.EA_EV EE
				            inner join (select distinct b.LU_LRMP_NOD_ID,  b.ev_id
				                        from  lms_lrm.ea_ev_ts_rnge b
				                        where b.LU_OPT_TXB_ID=#{optTxbId}) EETR
						   ON EETR.EV_ID = EE.EV_ID
						   JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
						   JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EEQ.EV_ID AND EQA.QTM_ID = EEQ.QTM_ID
						   JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EEQ.QTM_ID
						   JOIN LMS_CMS.QP_QTM_DMN QQD ON QQD.QP_DMN_ID = QQA.QP_CL_GRU_ID
				  WHERE
					  EE.OPT_TXB_ID = #{optTxbId}
					<if test="stuId != null and stuId != ''">
					AND EQA.USR_ID = #{stuId}
					</if>
					AND EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD = 'ST')
				 UNION ALL
				 (SELECT
					  EQA.USR_ID
					   , EQA.CANS_YN
					   , EE.EV_DTL_DV_CD
					   , QQA.QP_CL_GRU_ID
					   , QQD.QP_DMN_NM as QP_CL_GRU_NM
				  FROM LMS_LRM.EA_EV EE
				            inner join (select distinct b.LU_LRMP_NOD_ID,  b.ev_id
				                        from  lms_lrm.ea_ev_ts_rnge b
				                        where b.LU_OPT_TXB_ID=#{optTxbId}) EETR
						   ON EETR.EV_ID = EE.EV_ID
						   JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN RSLNR ON RSLNR.LRMP_NOD_ID = EETR.LU_LRMP_NOD_ID and EE.OPT_TXB_ID = RSLNR.OPT_TXB_ID
						   JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
						   JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EEQ.EV_ID AND EQA.QTM_ID = EEQ.QTM_ID
						   JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EEQ.QTM_ID
						   JOIN LMS_CMS.QP_QTM_DMN QQD ON QQD.QP_DMN_ID = QQA.QP_CL_GRU_ID
				  WHERE EE.OPT_TXB_ID = #{optTxbId}
					AND RSLNR.LLU_NOD_ID = #{lrmpNodId}
					<if test="stuId != null and stuId != ''">
						AND EQA.USR_ID = #{stuId}
					</if>
					AND RSLNR.DPTH = 1
					AND EE.EV_DV_CD = 'SE' AND EE.EV_DTL_DV_CD IN ('UG', 'UD'))
				 UNION ALL
				 (
				 SELECT
					  EQA.USR_ID
					   , EQA.CANS_YN
					   , EE.EV_DTL_DV_CD
					   , QQA.QP_CL_GRU_ID
					   , QQD.QP_DMN_NM as QP_CL_GRU_NM
				  FROM LMS_LRM.EA_EV EE
						   INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
						   INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID
						   INNER JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EE.EV_ID = EQA.EV_ID AND EEQ.QTM_ID = EQA.QTM_ID
						   JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EEQ.QTM_ID
				  		   JOIN LMS_CMS.QP_QTM_DMN QQD ON QQD.QP_DMN_ID = QQA.QP_CL_GRU_ID
				  WHERE  EE.OPT_TXB_ID = #{optTxbId}
					 <if test="stuId != null and stuId != ''">
						AND EQA.USR_ID = #{stuId}
					</if>
					AND EE.EV_DV_CD = 'AE' AND EE.EV_DTL_DV_CD = 'OV'
					AND exists (select * from ea_ev_ts_rnge z where z.EV_ID=EE.EV_ID AND   LU_LRMP_NOD_ID=#{lrmpNodId})
					)
			 ) RESULT
		group by
			<if test="stuId != null and stuId != ''">
			 RESULT.USR_ID,
			</if>
			RESULT.QP_CL_GRU_ID,
		    RESULT.EV_DTL_DV_CD
		    /* AlFdbkCm-Mapper.xml - selectGrowthRankList 학습 성장 분석 */
	</select>

</mapper>