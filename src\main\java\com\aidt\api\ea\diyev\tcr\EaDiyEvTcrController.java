package com.aidt.api.ea.diyev.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.evcom.diy.dto.DeleteDiyEvDto;
import com.aidt.api.ea.evcom.diy.dto.EaDiyEvReqDto;
import com.aidt.api.ea.evcom.diy.dto.EaDiyEvResDto;
import com.aidt.api.ea.evcom.diy.dto.SaveDiyEvDto;
import com.aidt.api.ea.evcom.diy.dto.SaveEaEvRsRtxmDto;
import com.aidt.api.ea.evcom.diy.dto.UpdateDiyEvNmDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import com.aidt.api.ea.diyev.stu.EaDiyEvStuService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] DIY 평가 - 교사", description="DIY 평가 - 교사")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/diyev")
public class EaDiyEvTcrController {
	@Autowired
	private JwtProvider jwtProvider;
	@Autowired
	EaDiyEvStuService eaDiyEvStuService;

	/**
	 * 선생님 DIY 평가 목록 조회 요청
	 *
	 * @param eaDiyEvReqDto
	 * @return ResponseList<EaDiyEvResDto>
	 */
	@Tag(name="[ea] 선생님 DIY 평가 목록 조회", description="선생님 DIY 평가 목록 조회")
	@PostMapping(value="/selectDiyEvTcrList")
	public ResponseDto<List<EaDiyEvResDto>> selectDiyEvTcrList(@RequestBody EaDiyEvReqDto eaDiyEvReqDto) {
		log.debug("Entrance selectDiyEvTcrList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaDiyEvReqDto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		eaDiyEvReqDto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		return Response.ok(eaDiyEvStuService.selectDiyEvStuList(eaDiyEvReqDto));
	}

	/**
	 * 선생님 DIY 시험지 명 수정 요청
	 *
	 * @param dto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 선생님 DIY 시험지 명 수정", description="단건 수정")
	@PutMapping(value="/updateDiyEvTcrNm",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateDiyEvTcrNm(@Valid @RequestBody UpdateDiyEvNmDto dto) {
		log.debug("Entrance updateDiyEvTcrNm");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.updateDiyEvStuNm(dto));
	}

	/**
	 * 선생님 DIY 평가 삭제 요청
	 *
	 * @param dto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 선생님 DIY 평가 삭제", description="단건 삭제")
	@DeleteMapping(value="/deleteDiyEvTcr",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> deleteDiyEvTcr(@Valid @RequestBody DeleteDiyEvDto dto) {
		log.debug("Entrance deleteDiyEv");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.deleteDiyEvStu(dto));
	}


	/**
	 * 선생 DIY 평가 재응시 등록 요청
	 *
	 * @param dto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 선생 DIY 평가 재응시 등록", description="재응시 등록")
	@PostMapping(value="/saveEaEvRsRtxm", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> saveEaEvRsRtxm(@Valid @RequestBody SaveEaEvRsRtxmDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.saveEaEvRsRtxm(dto));
	}

	/**
	 * 선생 DIY 평가 등록 요청
	 *
	 * @param dto
	 * @return ResponseDto<Map<String,Object>>
	 */
	@Tag(name="[ea] 선생 DIY 평가 등록", description="DIY 시험지 등록")
	@PostMapping(value = "/insertEvtcr", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> insertEvtcr(@Valid @RequestBody EaEvSaveReqDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaDiyEvStuService.insertEvStu(dto));
	}
    
	
}
