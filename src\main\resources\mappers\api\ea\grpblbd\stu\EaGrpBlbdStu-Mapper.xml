<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.grpblbd.stu">
	<!-- 모둠 과제 상세 조회 (모둠 구성원 조회) -->
	<select id="selectGrpDetail" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - selectGrpDetail */
		SELECT
			T2.GRP_ID					AS grpId
			, T2.GRP_TEM_ID			AS grpTemId
			, T3.STU_USR_ID			AS stuUsrId
			, T3.USR_NM				AS usrNm
			, T3.GRP_TMGR_YN		AS grpTmgrYn
			, T4.GRP_TEM_NM		AS grpTemNm
		FROM
		(
			SELECT
				T1.GRP_ID
				, T1.GRP_TEM_ID
			FROM
			(
				SELECT
					A.GRP_ID
					, A.GRP_TEM_ID
					, A.ASN_ID
					, A.SMT_DTM
					, A.SMT_CN
					, A.ANNX_ID
					, A.SMT_CMPL_YN
					, A.SCR
					, A.FDBK_CN
					, B.STU_USR_ID
					, B.GRP_TMGR_YN
					, B.DEL_YN
				FROM
					LMS_LRM.EA_GRP_ASN_SMT A
				RIGHT JOIN
					LMS_LRM.EA_GRP_TMBR B
				ON
					A.GRP_ID = B.GRP_ID
				AND
					A.GRP_TEM_ID = B.GRP_TEM_ID
				WHERE
					A.ASN_ID = #{asnId}
				AND
					B.STU_USR_ID = #{usrId}
			) T1
		) T2
		LEFT JOIN
			LMS_LRM.EA_GRP_TMBR T3
		ON
			T2.GRP_ID = T3.GRP_ID
		AND
			T2.GRP_TEM_ID = T3.GRP_TEM_ID
		LEFT JOIN
			LMS_LRM.EA_GRP_TEM T4
		ON
			T2.GRP_ID = T4.GRP_ID
		AND
			T2.GRP_TEM_ID = T4.GRP_TEM_ID
		ORDER BY
			T3.GRP_TMGR_YN DESC
	</select>

	<!-- 모둠 게시판 List 조회 -->
	<select id="selectGrpBlbdList" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - selectGrpBlbdList */
		<include refid="api.ea.common.pagingHeader"/>
		SELECT
			@rownum := @rownum + 1 						AS rowNum
			, T1.COMMENT_CNT 									AS commentCnt
			, T1.BLWR_ID 												AS blwrId
			, T1.BLBD_ID 												AS blbdId
			, T1.BLWR_TITL_NM 										AS blwrTitlNm
			, IFNULL(T1.ANNX_ID, 0) 								AS annxId
			, T1.ANNLST_YN 											AS annlstYn
			, DATE_FORMAT(T1.CRT_DTM,'%m. %d.') 		AS crtDtm
			, T1.CRTR_ID 												AS crtrId
			, T1.CRTR_TP_CD
		FROM
		(
			SELECT
				IFNULL(SC.COMMENT_CNT, 0) AS COMMENT_CNT
				, A.BLBD_ID
				, A.BLWR_ID
				, A.BLWR_TITL_NM
				, A.ANNX_ID
				, A.ANNLST_YN
				, A.CRT_DTM
				, A.CRTR_ID
				, (SELECT CU.USR_TP_CD FROM CM_USR CU WHERE A.CRTR_ID = CU.USR_ID) AS CRTR_TP_CD
			FROM
				LMS_LRM.EA_GRP_BLBD_BLWR A
			LEFT JOIN
				LMS_LRM.EA_GRP_BLBD B
			ON
				B.BLBD_ID = A.BLBD_ID
			LEFT JOIN
				(
			        SELECT 
			            C.BLWR_ID, 
			            C.BLBD_ID, 
			            COUNT(CASE WHEN C.DEL_YN = 'N' THEN 1 ELSE 0 END) AS COMMENT_CNT
			        FROM 
			            LMS_LRM.EA_GRP_BLBD_UCWR C
			        GROUP BY 
			            C.BLWR_ID, C.BLBD_ID
			    ) SC
			ON
			    SC.BLWR_ID = A.BLWR_ID
			AND
			    SC.BLBD_ID = A.BLBD_ID
			WHERE
				B.GRP_ID = #{grpId}
			AND
				B.GRP_TEM_ID = #{grpTemId}
			AND
				B.ASN_ID = #{asnId}
			AND
				A.DEL_YN = 'N'
			ORDER BY
				A.ANNLST_YN, A.CRT_DTM ASC
		) T1
		, (SELECT @rownum :=0) AS R
		ORDER BY
			ROWNUM DESC
		<include refid="api.ea.common.pagingFooter"/>
	</select>

	<!-- 모둠 게시판 상세 조회 -->
	<select id="selectBlbdDetail" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - selectBlbdDetail */
		SELECT
			BLWR_ID											AS blwrId
			, BLBD_ID											AS blbdId
			, BLWR_TITL_NM									AS blwrTitlNm
			, BLWR_CN											AS blwrCn
			, ANNLST_YN										AS annlstYn
			, IFNULL(ANNX_ID, -1) 							AS annxId
			, DATE_FORMAT(CRT_DTM,'%m. %d.') 	AS crtDtm
			, CRTR_ID 											AS crtrId
			, (SELECT USR_TP_CD FROM LMS_LRM.CM_USR WHERE USR_ID = A.CRTR_ID) 	AS USR_TP_CD
		FROM
			LMS_LRM.EA_GRP_BLBD_BLWR A
		WHERE
			BLWR_ID = #{blwrId}
		AND
			BLBD_ID = #{blbdId}
		AND
			DEL_YN = 'N'
	</select>

	<!-- 모둠 게시판 댓글 조회 -->
	<select id="selectBlbdDetailComment" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - selectBlbdDetailComment */
		SELECT
			IF(T1.SRT_ORDN != 0, T1.URNK_UCWR_ID, T1.UCWR_ID) 	AS tempUcwrId
			, T1.UCWR_ID																AS ucwrId
			, T1.BLWR_ID																AS blwrId
			, T1.BLBD_ID																AS blbdId
			, T1.URNK_UCWR_ID														AS urnkUcwrId
			, T1.SRT_ORDN															AS srtOrdn
			, T1.UCWR_CN																AS ucwrCn
			, T1.CRTR_ID																AS crtrId
			, T1.USR_TP_CD															AS usrTpCd
			, T1.DEL_YN																	AS delYn
			, T1.CRT_DTM																AS crtDtm
			, T1.CRT_DTM_NM																AS crtDtmNm
		FROM
		(
			SELECT
				UCWR_ID
				, BLWR_ID
				, BLBD_ID
				, URNK_UCWR_ID
				, SRT_ORDN
				, UCWR_CN
				, CRTR_ID
				, (SELECT USR_TP_CD FROM LMS_LRM.CM_USR WHERE USR_ID = A.CRTR_ID) 	AS USR_TP_CD
				, DEL_YN
				, CRT_DTM
				, CONCAT(DATE_FORMAT(CRT_DTM,'%m. %d. '),IF(TIME_FORMAT(CRT_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(CRT_DTM,'%h:%i')) AS CRT_DTM_NM
			FROM
				LMS_LRM.EA_GRP_BLBD_UCWR A
			WHERE
				BLWR_ID = #{blwrId}
			AND
				BLBD_ID = #{blbdId}
			ORDER BY
				UCWR_ID, SRT_ORDN, CRT_DTM
		) T1
	</select>

	<!-- 게시판 ID 조회 -->
	<select id="selectBlbdId" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="int">
		/* EaGrpBlbdTcr-Mapper.xml - selectBlbdId */
		SELECT
			BLBD_ID
		FROM
			LMS_LRM.EA_GRP_BLBD
		WHERE
			GRP_ID = #{grpId}
		AND
			GRP_TEM_ID = #{grpTemId}
		AND
			ASN_ID = #{asnId}
	</select>

	<!-- 모둠 게시판 글 등록 -->
	<insert id="insertBlbd" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - insertBlbd */
		INSERT INTO LMS_LRM.EA_GRP_BLBD_BLWR (
			BLBD_ID
			, BLWR_TITL_NM
			, BLWR_CN
			, ANNLST_YN
			, ANNX_ID
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DEL_YN
			, DB_ID
		) VALUES (
			#{blbdId}
			, #{blwrTitlNm}
			, #{blwrCn}
			, 'N'
			, #{annxId}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, 'N'
			, 'LMS_LRM'
		)
	</insert>

	<!-- 모둠 게시판 글 수정 -->
	<update id="updateBlbd" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - updateBlbd */
		UPDATE LMS_LRM.EA_GRP_BLBD_BLWR
		SET
			BLWR_TITL_NM = #{blwrTitlNm}
			, BLWR_CN = #{blwrCn}
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
	   		,ANNX_ID =	#{annxId}
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
	</update>

	<!-- 모둠 게시판 글 삭제 -->
	<delete id="deleteBlbd" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - deleteBlbd */
		DELETE FROM 
			LMS_LRM.EA_GRP_BLBD_BLWR
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
	</delete>

	<!-- 댓글 등록 -->
	<insert id="insertComment" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - insertComment */
		INSERT INTO LMS_LRM.EA_GRP_BLBD_UCWR (
			BLWR_ID
			, BLBD_ID
			, SRT_ORDN
			, UCWR_CN
			, DEL_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		) VALUES (
			#{blwrId}
			, #{blbdId}
			, 0
			, #{ucwrCn}
			, 'N'
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 댓글,답글 수정 -->
	<update id="updateComment" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - updateComment */
		 UPDATE LMS_LRM.EA_GRP_BLBD_UCWR A
		   SET
				 UCWR_CN  = #{ucwrCn}
				,MDFR_ID = #{usrId}
				,MDF_DTM = NOW()
		 WHERE
					BLBD_ID = #{blbdId}
				AND
					BLWR_ID = #{blwrId}
				AND
					UCWR_ID = #{ucwrId}
	</update>

	<!-- 댓글 삭제 -->
	<delete id="deleteComment" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - deleteComment */
		DELETE FROM
			LMS_LRM.EA_GRP_BLBD_UCWR
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
		<if test = 'ucwrId != 0'>
			AND UCWR_ID = #{ucwrId}
		</if>
	</delete>

	<!-- 답글 등록 -->
	<insert id="insertReply" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - insertReply */
		INSERT INTO LMS_LRM.EA_GRP_BLBD_UCWR (
			BLWR_ID
			, BLBD_ID
			, URNK_UCWR_ID
			, SRT_ORDN
		 	, UCWR_CN
		  	, DEL_YN
		  	, CRTR_ID
		 	, CRT_DTM
		  	, MDFR_ID
		  	, MDF_DTM
		  	, DB_ID
		) VALUES (
			#{blwrId}
			, #{blbdId}
			, #{ucwrId}
			, (
				SELECT
					IFNULL(MAX(A.SRT_ORDN), 0) + 1
			   	FROM
					LMS_LRM.EA_GRP_BLBD_UCWR A
			   	WHERE
			   		A.BLWR_ID = #{blwrId}
				AND
					A.BLBD_ID = #{blbdId}
				AND
					A.URNK_UCWR_ID = #{ucwrId}
			   )
			 , #{ucwrCn}
			 , 'N'
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , 'LMS_LRM'
		)
	</insert>

	<!-- 답글 삭제 -->
	<update id="deleteReply" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - deleteReply */
		DELETE FROM
			 LMS_LRM.EA_GRP_BLBD_UCWR
		WHERE
			BLBD_ID = #{blbdId}
		AND
			BLWR_ID = #{blwrId}
		AND
			UCWR_ID = #{ucwrId}
		AND
			URNK_UCWR_ID = #{urnkUcwrId}
	</update>

 	<!-- 파일조회 -->
	<select id="selectFile" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
		/* EaGrpBlbdStu-Mapper.xml - selectFile */
		SELECT
			   CAF.ANNX_FLE_ID		AS annxFileId
			  ,CAF.ANNX_ID			AS annxId
			  ,CAF.SRT_ORDN			AS srtOrdn
			  ,CAF.DOC_VI_ID		AS docViId
			  ,CAF.ANNX_FLE_NM		AS annxFileNm
			  ,CAF.ANNX_FLE_ORGL_NM	AS annxFileOrglNm
			  ,CAF.ANNX_FLE_FEXT_NM	AS annxFileFextNm
			  ,CAF.ANNX_FLE_SZE		AS annxFileSize
			  ,CAF.ANNX_FLE_PTH_NM	AS annxFilePathNm
			  ,CAF.USE_YN			AS useYn
		FROM
			LMS_LRM.CM_ANNX_FLE CAF
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>
	
	<!-- 모둠 확인 -->
    <select id="selectGrpStuDetail" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
        /* EaGrpBlbdStu-Mapper.xml - selectGrpStuDetail */
	    SELECT EA.ASN_ID 
          FROM LMS_LRM.EA_ASN EA
         INNER JOIN LMS_LRM.EA_ASN_SMT EAS
            ON EA.ASN_ID = EAS.ASN_ID
           AND EAS.STU_USR_ID = #{usrId}
         INNER JOIN LMS_LRM.EA_GRP_ASN_SMT EGAS
            ON EA.ASN_ID = EGAS.ASN_ID
           AND GRP_ID = #{grpId}
		   AND GRP_TEM_ID = #{grpTemId}
         WHERE EA.ASN_ID = #{asnId}
           AND EA.OPT_TXB_ID = #{sessionOptTxbId}
    </select>
    
    <!-- 모둠 확인 -->
    <select id="selectGrpblbdStuDetail" parameterType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto" resultType="com.aidt.api.ea.grpblbd.stu.dto.EaGrpBlbdStuDto">
        /* EaGrpBlbdStu-Mapper.xml - selectGrpblbdStuDetail */
	       SELECT EA.ASN_ID 
	         FROM LMS_LRM.EA_ASN EA
	        INNER JOIN LMS_LRM.EA_ASN_SMT EAS
	           ON EA.ASN_ID = EAS.ASN_ID
	          AND EAS.STU_USR_ID = #{usrId}
	        INNER JOIN LMS_LRM.EA_GRP_ASN_SMT EGAS
	           ON EA.ASN_ID = EGAS.ASN_ID
	          AND GRP_ID = #{grpId}
			  AND GRP_TEM_ID = #{grpTemId}
			INNER JOIN LMS_LRM.EA_GRP_BLBD EGB
			   ON EGAS.ASN_ID = EGB.ASN_ID
			  AND EGB.BLBD_ID = #{blbdId}
			INNER JOIN LMS_LRM.EA_GRP_BLBD_BLWR EGBB
			   ON EGB.BLBD_ID = EGBB.BLBD_ID
			  AND EGBB.BLWR_ID = #{blwrId}
	        WHERE EA.ASN_ID = #{asnId}
	          AND EA.OPT_TXB_ID = #{sessionOptTxbId}
    </select>
</mapper>