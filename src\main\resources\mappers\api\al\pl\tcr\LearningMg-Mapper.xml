<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.tcr.learningMg">
	
	<select id="selectClaStuList" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT STU_NO
			, USR.USR_ID
			, USR_NM
			, IFNULL(ALL2.LRNR_VEL_TP_CD, 'NM') as LRNR_VEL_TP_CD
		FROM LMS_LRM.CM_USR USR
		INNER JOIN LMS_LRM.CM_OPT_TXB COT2 ON USR.CLA_ID = COT2.CLA_ID
		LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2
		ON ALL2.USR_ID = USR.USR_ID
		AND ALL2.OPT_TXB_ID = COT2.OPT_TXB_ID
		AND ALL2.LU_KMMP_NOD_ID = #{mluKmmpNodId}
		WHERE USR.CLA_ID = #{claId}
		AND USR.USR_TP_CD = 'ST'
		ORDER BY STU_NO
		/* AI 선생님 학습관리 - 학생리스트조회 - 이혜인 - learningMg-Mapper.xml - selectClaStuList */
	</select>
	
	<select id="selectOvInfo" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
			MAX(DPTH2.OPT_TXB_ID) AS OPT_TXB_ID,
			OV.USR_ID,
			DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
			IFNULL(MAX(OV.EV_CMPL_YN), '') AS OV_EV_CMPL_YN,
			IFNULL(MAX(OV.CRT_DTM), '') AS CRT_DTM,
			IFNULL(MAX(OV.EV_TM_SCNT), '') AS EV_TM_SCNT,
			IFNULL(COUNT(OV.QTM_ID), 0) AS QTM_CNT,
			IFNULL(SUM(OV.CANS_YN = 'Y'), 0) AS CANS_Y_CNY,
			MAX(OV.EV_ID) AS EV_ID,
			(
				SELECT COUNT(*)
				FROM (
					SELECT EAETR.TC_KMMP_NOD_ID
					FROM LMS_LRM.EA_EV_QTM EEQ
						INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
							ON EEQ.EV_ID = EAETR.EV_ID
							AND EEQ.TPC_ID = EAETR.TPC_KMMP_NOD_ID
					WHERE EEQ.EV_ID = MAX(OV.EV_ID)
					GROUP BY EAETR.TC_KMMP_NOD_ID
				) Z
			) AS TC_CNT
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
		LEFT OUTER JOIN (
			SELECT EE.EV_ID, EER.USR_ID, EE.EV_DTL_DV_CD, EER.EV_CMPL_YN
				 , EAETR.OPT_TXB_ID, EAETR.MLU_KMMP_NOD_ID, EER.EV_TM_SCNT, EEQA.CRT_DTM
				 , EEQ.QTM_ID, EEQA.CANS_YN
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EAETR.EV_ID = EE.EV_ID AND EAETR.OPT_TXB_ID = EE.OPT_TXB_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID and EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EEQA.USR_ID = EER.USR_ID
			WHERE EE.EV_DV_CD = 'AE'
			AND EE.EV_DTL_DV_CD = 'OV'
		)OV on OV.OPT_TXB_ID = DPTH2.OPT_TXB_ID AND OV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
		WHERE DPTH2.OPT_TXB_ID = #{optTxbId}
		AND DPTH2.DPTH = 2
		AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY DPTH2.KMMP_NOD_ID, OV.USR_ID
		/* AI 선생님 학습관리 - 진단평가 조회 - 이혜인 - learningMg-Mapper.xml - selectOvInfo */
	</select>
	
	<select id="selectOptTxbIdToAiLrnrLv" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
			ALL2.USR_ID
		  , ALL2.OPT_TXB_ID
		  , ALL2.LRNR_VEL_TP_CD
		  , DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		  , DPTH2.KMMP_NOD_NM AS MLU_KMMP_NOD_NM
		  , DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID
		  , DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM
		FROM
		    LMS_LRM.AI_LRNR_LV ALL2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
				    ON ALL2.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				    AND ALL2.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				    AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
				    ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
				    AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				    AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				    ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
				    AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				    AND DPTH2.DPTH = 2
		WHERE
		    ALL2.OPT_TXB_ID = #{optTxbId}
		ORDER BY
		    ALL2.USR_ID, ALL2.LU_KMMP_NOD_ID
	</select>
	
	<select id="selectStuAeEvInfoList" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
			MAX(DPTH2.OPT_TXB_ID) AS OPT_TXB_ID,
			IFNULL(MAX(ALL2.USR_ID), '') AS USR_ID,
			MAX(ALL2.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD,
			DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
			DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
			MAX(DPTH4.KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
			IFNULL(MAX(CV.LUEV_CMPL_YN), '') AS CV_EV_CMPL_YN,
			IFNULL(MAX(CV.QTM_CNT), '') AS QTM_CNT,
			IFNULL(MAX(CV.CANS_Y_CNY), '') AS CANS_Y_CNY,
			IFNULL(MAX(CV.CRT_DTM), '') AS CRT_DTM,
			IFNULL(MAX(CV.EV_TM_SCNT), '') AS EV_TM_SCNT,
			IFNULL(MAX(CV.EV_ID), '') AS EV_ID,
			IFNULL(MAX(CV.LRN_DTM), '') AS LRN_DTM
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
				    ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
				    AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				    AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
				    ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
				    AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				    AND DPTH4.DPTH = 4
				LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2
					ON ALL2.OPT_TXB_ID = DPTH2.OPT_TXB_ID
					AND ALL2.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				LEFT OUTER JOIN (
					SELECT
						MAX(EE.EV_ID) AS EV_ID,
						EE.USR_ID,
						MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
						COUNT(EEQ.QTM_ID) AS QTM_CNT,
						SUM(EEQA.CANS_YN = 'Y') AS CANS_Y_CNY,
						MAX(EAETR.OPT_TXB_ID) AS OPT_TXB_ID,
						MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
						EAETR.TC_KMMP_NOD_ID AS TC_KMMP_NOD_ID,
						MAX(EAETR.LUEV_CMPL_YN) AS LUEV_CMPL_YN,
						SUM(EEQA.XPL_TM_SCNT) AS EV_TM_SCNT,
						MAX(EEQA.CRT_DTM) AS CRT_DTM,
						MAX(EE.CRT_DTM) AS LRN_DTM
					FROM
					    LMS_LRM.EA_EV EE
							INNER JOIN LMS_LRM.EA_EV_RS EER
								ON EE.EV_ID = EER.EV_ID
							INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
							    ON EAETR.EV_ID = EE.EV_ID
							    AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
							INNER JOIN LMS_LRM.EA_EV_QTM EEQ
							    ON EE.EV_ID = EEQ.EV_ID
							    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
							LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
							    ON EE.EV_ID = EEQA.EV_ID
							    AND EEQ.QTM_ID = EEQA.QTM_ID
							    AND EEQA.USR_ID = EER.USR_ID
					WHERE
					    EE.EV_DV_CD = 'AE'
						AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
						AND EE.OPT_TXB_ID = #{optTxbId}
					GROUP BY EE.USR_ID, EAETR.TC_KMMP_NOD_ID
				) CV
				    ON CV.OPT_TXB_ID = DPTH2.OPT_TXB_ID
				    AND CV.MLU_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
				    AND CV.TC_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				    AND ALL2.USR_ID = CV.USR_ID
		WHERE
		    DPTH2.OPT_TXB_ID = #{optTxbId}
		  	AND DPTH2.DPTH = 2
		  	AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY
		    DPTH2.KMMP_NOD_ID, DPTH4.KMMP_NOD_ID, CV.USR_ID
		/* AI 선생님 학습관리 - 차시별 맞춤학습 조회 - 이혜인 - learningMg-Mapper.xml - selectStuAeEvInfoList */
	</select>
	
	<select id="selectStuAiRcmEvOvInfoList" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
			MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
			MAX(EER.EV_CMPL_YN) AS EV_CMPL_YN
		FROM
		    LMS_LRM.EA_EV EE
				INNER JOIN LMS_LRM.EA_EV_RS EER
				    ON EE.EV_ID = EER.EV_ID
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
				    ON EE.EV_ID = EAETR.EV_ID
				    AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE
		    EE.EV_DV_CD = 'AE'
			AND EE.EV_DTL_DV_CD = 'OV'
			AND EER.USR_ID = #{usrId}
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY
		    EE.EV_ID
		/* AI 선생님 학습관리 - 진단평가 완료여부 체크 - 이혜인 - learningMg-Mapper.xml - selectStuAiRcmEvInfoList */
	</select>
	
	<select id="selectStuAiRcmTcInfoList" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
			MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
			MAX(EER.EV_CMPL_YN) AS EV_CMPL_YN
		FROM
		    LMS_LRM.EA_EV EE
				INNER JOIN LMS_LRM.EA_EV_RS EER
					ON EE.EV_ID = EER.EV_ID
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
				    ON EE.EV_ID = EAETR.EV_ID
					AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE
		    EE.EV_DV_CD = 'AE'
			AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
			AND EER.USR_ID = #{usrId}
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND EAETR.TC_KMMP_NOD_ID = #{tcKmmpNodId}
		GROUP BY
		    EE.EV_ID
		/* AI 선생님 학습관리 - 맞춤학습 진행여부 체크 - 이혜인 - learningMg-Mapper.xml - selectStuAiRcmEvInfoList */
	</select>

	<select id="selectSbjCd" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT BT.SBJ_CD
		FROM LMS_LRM.CM_OPT_TXB COT2
		INNER JOIN LMS_CMS.BC_TXB BT ON COT2.TXB_ID = BT.TXB_ID
		WHERE COT2.OPT_TXB_ID = #{optTxbId}
	</select>

	<select id="selectStuAlProgRtMa" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT Z.USR_ID, Z.LU_CNT, COUNT(CASE WHEN Z.TPC_CNT = Z.TPC_CMPL_CNT THEN Z.MLU_KMMP_NOD_ID END) AS LU_CMPL_CNT
		FROM
		(
			SELECT
			    EER.USR_ID
				, (SELECT COUNT(*) FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR WHERE DPTH = 2 AND OPT_TXB_ID = #{optTxbId} AND DEL_YN = 'N') AS LU_CNT
				, MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
				, MAX(EER.EV_CMPL_YN) AS EV_CMPL_YN
				, MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD
				, (SELECT COUNT(*) FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO
				WHERE AUTLO.OPT_TXB_ID = EE.OPT_TXB_ID
				AND AUTLO.USR_ID = EER.USR_ID
				AND AUTLO.LU_KMMP_NOD_ID = EAETR.MLU_KMMP_NOD_ID
				AND AUTLO.LRN_YN = 'Y'
				) AS TPC_CNT
				, (SELECT COUNT(CMPL.TPC_KMMP_NOD_ID) FROM (
				SELECT
					MAX(EAETR2.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
				FROM LMS_LRM.EA_EV EE2
				INNER JOIN LMS_LRM.EA_EV_RS EER2 ON EE2.EV_ID = EER2.EV_ID
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR2 ON EE2.EV_ID = EAETR2.EV_ID
				INNER JOIN LMS_LRM.EA_EV_QTM EEQ2 ON EE2.EV_ID = EEQ2.EV_ID AND EAETR2.TPC_KMMP_NOD_ID = EEQ2.TPC_ID
				LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA2 ON EE2.EV_ID = EEQA2.EV_ID AND EEQ2.QTM_ID = EEQA2.QTM_ID
				WHERE
				EE2.EV_DV_CD = 'AE'
				AND EE2.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
				AND EE2.OPT_TXB_ID = #{optTxbId}
				AND EER2.USR_ID = EER.USR_ID
				AND EAETR2.MLU_KMMP_NOD_ID = EAETR.MLU_KMMP_NOD_ID
				GROUP BY
				EEQ2.EV_ID, EAETR2.TPC_KMMP_NOD_ID
				HAVING
				COUNT(CASE WHEN EAETR2.LUEV_CMPL_YN = 'Y' THEN 1 END) = COUNT(*)
				) CMPL) AS TPC_CMPL_CNT
			FROM  LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EAETR.AI_TS_RNGE_SEQ_NO = 1
			WHERE EE.OPT_TXB_ID = #{optTxbId}
			AND EE.EV_DV_CD = 'AE'
			AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
			GROUP BY EER.USR_ID, EAETR.MLU_KMMP_NOD_ID
		)Z
		GROUP BY Z.USR_ID
		/* AI 선생님 학습관리 - 종합현황 - 단원진도율 - 강성현 - learningMg-Mapper.xml - selectStuAlProgRtMa */
	</select>
	
	<select id="selectStuAlProgRtEn" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
	  	SELECT Z.USR_ID, Z.LU_CNT, COUNT(Z.LUEV_CMPL_YN) AS LU_CMPL_CNT
	  	FROM 
	  	(
		  	SELECT
		  	    EER.USR_ID,
				(SELECT COUNT(*) FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR WHERE DPTH = 2 AND OPT_TXB_ID = #{optTxbId} AND DEL_YN = 'N') AS LU_CNT,
				MAX(EAETR.LUEV_CMPL_YN) AS LUEV_CMPL_YN,
				EAETR.MLU_KMMP_NOD_ID
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EAETR.AI_TS_RNGE_SEQ_NO = 1
			WHERE EE.OPT_TXB_ID = #{optTxbId}
			AND EE.EV_DV_CD = 'AE'
			GROUP BY EER.USR_ID, EAETR.MLU_KMMP_NOD_ID
		)Z
		GROUP BY Z.USR_ID
		/* AI 선생님 학습관리 - 종합현황 - 단원진도율 - 이혜인 - learningMg-Mapper.xml - selectStuAlProgRtEn */
	</select>
	
	
	<select id="selectStuTpcListMa" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT USR.STU_NO
			, USR.USR_ID
			, USR_NM
			, IFNULL(ALL2.LRNR_VEL_TP_CD, 'NM') AS LRNR_VEL_TP_CD
			, TPC.MLU_KMMP_NOD_ID, TPC.MLU_KMMP_NOD_NM, TPC.TPC_KMMP_NOD_ID, TPC.TPC_KMMP_NOD_NM
			, AUTP.TPC_AVN 
			, TM.XPL_TM_SCNT
		FROM LMS_LRM.CM_USR USR
		INNER JOIN LMS_LRM.CM_OPT_TXB COT2 ON USR.CLA_ID = COT2.CLA_ID
		LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2
		ON ALL2.USR_ID = USR.USR_ID
		AND ALL2.OPT_TXB_ID = COT2.OPT_TXB_ID
		AND ALL2.LU_KMMP_NOD_ID = #{mluKmmpNodId}
		LEFT OUTER JOIN (
			SELECT
				MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
				MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
				MAX(DPTH2.TC_USE_YN) AS TC_USE_YN,
				DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
				MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
				MAX(DPTH2.OPT_TXB_ID) AS OPT_TXB_ID
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2 ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3 ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4 ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID AND DPTH2.OPT_TXB_ID = DPTH4.OPT_TXB_ID
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5 ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID AND DPTH2.OPT_TXB_ID = DPTH5.OPT_TXB_ID
			WHERE DPTH2.DPTH = 2
			AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
			AND DPTH2.OPT_TXB_ID = #{optTxbId}
			GROUP BY DPTH5.KMMP_NOD_ID
			ORDER BY MAX(DPTH1.ORGL_ORDN), MAX(DPTH2.ORGL_ORDN), MAX(DPTH3.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN), MAX(DPTH5.ORGL_ORDN)
		)TPC ON 1=1
		LEFT OUTER JOIN (
			SELECT
				SUM(EEQA.XPL_TM_SCNT) AS XPL_TM_SCNT,
				MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
				EER.USR_ID AS USR_ID,
				MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID
			FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID 
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EEQA.USR_ID = EER.USR_ID
			WHERE EE.EV_DV_CD = 'AE'
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND EER.EV_CMPL_YN = 'Y'
			GROUP BY EER.USR_ID
		)TM ON TPC.OPT_TXB_ID = TM.OPT_TXB_ID AND USR.USR_ID = TM.USR_ID AND TPC.MLU_KMMP_NOD_ID = TM.MLU_KMMP_NOD_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP 
		ON AUTP.TPC_ID = TPC.TPC_KMMP_NOD_ID AND AUTP.USR_ID = USR.USR_ID
		WHERE USR.CLA_ID = #{claId}
		AND USR.USR_TP_CD = 'ST'
		AND (SELECT COUNT(*) FROM LMS_CMS.BC_AI_LRN_ATV_CTN BALAC WHERE BALAC.CTN_TP_CD = 'QU' AND BALAC.DEL_YN = 'N' AND KMMP_NOD_ID = TPC.TPC_KMMP_NOD_ID) > 0
		ORDER BY USR.STU_NO
		/* AI 선생님 학습현황 - 수학 AI추천학습 학생,토픽리스트 조회 - 이혜인 - learningMg-Mapper.xml - selectStuTpcListMa */
	</select>
	
	<select id="selectLastLearnInfo" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		WITH RANKEDRESULTS AS (
		    SELECT 
		        EER.USR_ID,
		        EE.EV_ID,
				MAX(EE.EV_DV_CD) AS EV_DV_CD,
				MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
				ROW_NUMBER() OVER (
		            PARTITION BY EER.USR_ID
		            ORDER BY CAST(SUBSTRING(EE.EV_DTL_DV_CD, 2) AS UNSIGNED) DESC
		        ) AS RN
		        , DATE_FORMAT((SELECT A.CRT_DTM FROM LMS_LRM.EA_EV_QTM_ANW A WHERE A.EV_ID = EE.EV_ID ORDER BY A.CRT_DTM DESC LIMIT 1), '%m-%d') AS CRT_DTM
		    FROM LMS_LRM.EA_EV EE
		    INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		    INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		    INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
		    LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EEQA.USR_ID = EER.USR_ID
		    WHERE EE.EV_DV_CD = 'AE'
		    AND EE.OPT_TXB_ID = #{optTxbId}
		    AND EER.EV_CMPL_YN = 'Y'
		    AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		    GROUP BY EER.USR_ID, EE.EV_ID
		)
		SELECT
		    USR_ID, 
		    EV_ID, 
		    EV_DV_CD, 
		    EV_DTL_DV_CD, 
		    CRT_DTM
		FROM RANKEDRESULTS
		WHERE RN = 1
		ORDER BY USR_ID, CAST(SUBSTRING(EV_DTL_DV_CD, 2) AS UNSIGNED) DESC
		/* AI 선생님 학습현황 - 수학 AI추천학습 최근학습 조회 - 이혜인 - learningMg-Mapper.xml - selectLastLearnInfo */
	</select>
	
	<select id="selectStuOvEvId" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT 
		    EER.USR_ID,
		    EE.EV_ID,
			MAX(EE.EV_DV_CD) AS EV_DV_CD,
			MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
		LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EEQA.USR_ID = EER.USR_ID
		WHERE EE.EV_DV_CD = 'AE'
		AND EE.EV_DTL_DV_CD = 'OV'
		AND EE.OPT_TXB_ID = #{optTxbId}
		AND EER.EV_CMPL_YN = 'Y'
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY EER.USR_ID, EE.EV_ID
		/* AI 선생님 학습현황 - 수학 AI추천학습 - 진단평가ID 조회 - 이혜인 - learningMg-Mapper.xml - selectStuOvEvId */
	</select>
	
	<select id="selectStuXplTmScnt" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT EER.USR_ID
	        , SUM(EEQA.XPL_TM_SCNT) AS LEARNING_TM
	    FROM LMS_LRM.EA_EV EE
	    INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
	    INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
	    INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
	    LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID
	    WHERE EE.EV_DV_CD = 'AE'
	    AND EE.OPT_TXB_ID = #{optTxbId}
	    AND EER.EV_CMPL_YN = 'Y'
	    AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
	    GROUP BY EER.USR_ID
	    /* AI 선생님 학습현황 - 수학 AI추천학습 - 총 학습시간 조회 - 이혜인 - learningMg-Mapper.xml - selectStuXplTmScnt */
	</select>

	<select id="selectStuAlProgRt" parameterType="com.aidt.api.al.pl.dto.LearningMgDto" resultType="com.aidt.api.al.pl.dto.LearningMgDto">
		SELECT
		    Z.USR_ID,
		    MAX(Z.LU_CNT) AS LU_CNT,
		    COUNT(CASE WHEN Z.AI_LRN_PGRS_RT = 100 THEN 1 END) AS LU_CMPL_CNT,
			ROUND(AVG(Z.AI_LRN_PGRS_RT), 1) AS AI_LRN_PGRS_RT
		FROM
			(
				SELECT
					ALPP.USR_ID,
					(SELECT COUNT(*) FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR WHERE DPTH = 2 AND OPT_TXB_ID = #{optTxbId} AND DEL_YN = 'N') AS LU_CNT,
					ALPP.MLU_KMMP_NOD_ID,
					ALPP.AI_LRN_PGRS_RT
				FROM LMS_LRM.AI_LRN_PGRS_PROF ALPP
				WHERE ALPP.OPT_TXB_ID = #{optTxbId}
			) Z
		GROUP BY
		    Z.USR_ID
	</select>
	
</mapper>