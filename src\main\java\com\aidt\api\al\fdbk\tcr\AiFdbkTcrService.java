package com.aidt.api.al.fdbk.tcr;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.aidt.api.al.fdbk.dto.*;
import com.aidt.api.al.fdbk.dto.res.AiFdbkBsDataResDto;
import org.springframework.stereotype.Service;

import com.aidt.api.al.fdbk.cm.AiFdbkCmService;
import com.aidt.api.al.fdbk.dto.req.AiFdbkBsDataReqDto;
import com.aidt.api.al.fdbk.dto.req.AiFdbkReqDto;
import com.aidt.api.al.fdbk.dto.req.AiFdbkStuReqDto;
import com.aidt.api.al.fdbk.dto.res.AiFdbkStuResDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:53:50
 * @modify date 2024-07-11 15:53:50
 * @desc
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class AiFdbkTcrService {

    private final AiFdbkCmService aiFdbkCmService;

    public AiFdbkStuResDto getFdbk(AiFdbkStuReqDto reqDto) {
        return AiFdbkStuResDto.builder().comment("TEST COMMENT").build();
    }

    public List<AiFdbkAchRankDto> getAchRankList(String optTxbId, String lrmpNodId) {
        return aiFdbkCmService.selectAchRankList(optTxbId, lrmpNodId);
    }

    public List<AiFdbkCorrectRateDto> getCorrectRateList(String optTxbId, String lrmpNodId) {
        return aiFdbkCmService.selectCorrectRateList(optTxbId, lrmpNodId);
    }

    public List<AiFdbkGrowthRankDto> getGrowthRankList(String optTxbId, String lrmpNodId, String stuId) {
        return aiFdbkCmService.selectGrowthRankList(optTxbId, lrmpNodId, stuId);
    }

    public List<AiFdbkBsDataResDto> getFdbkBsDataList(List<AiFdbkBsDataReqDto> reqDtoList) {
        List<AiFdbkBsDataResDto> results = new ArrayList<>();

        for(AiFdbkBsDataReqDto reqDto : reqDtoList) {
            List<String> fdbkList = aiFdbkCmService.selectFdbkBsDataList(reqDto.toDto());

            results.add(AiFdbkBsDataResDto.builder()
                    .usrId(reqDto.getUsrId())
                    .fdbkList(fdbkList).build());
        }

        return results;
    }

    public List<AiFdbkDto> getFdbkList(String optTxbId, String lrmpNodId) {
        Map<String, String> param = Map.of("optTxbId", optTxbId, "lrmpNodId", lrmpNodId);
        return aiFdbkCmService.selectFdbkList(param);
    }

    public int insertFdbk(List<AiFdbkReqDto> reqDtoList, String optTxbId, String tcrId) {
        int cnt = 0;

        for(AiFdbkReqDto reqDto : reqDtoList) {
            AiFdbkDto dto = reqDto.toDto(optTxbId, tcrId);
            AiFdbkDto aiFdbkDto = aiFdbkCmService.selectFdbk(dto);

            if(aiFdbkDto != null) {
                cnt += aiFdbkCmService.updateFdbk(dto);
            } else {
                cnt += aiFdbkCmService.insertFdbk(dto);
            }
        }

        return cnt;
    }
}
