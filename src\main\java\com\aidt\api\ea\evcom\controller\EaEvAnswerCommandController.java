package com.aidt.api.ea.evcom.controller;

import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.evcom.dto.EaEvAnswerNoteReqDto;
import com.aidt.api.ea.evcom.dto.EaEvAnswerReqDto;
import com.aidt.api.ea.evcom.service.EaEvAnswerService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "[ea] 평가 공통 답안[new]", description = "평가 - 공통 답안")
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@RestController
@RequestMapping("/api/v1/ea/cm/evcom/")
public class EaEvAnswerCommandController {

	private final EaEvAnswerService eaEvAnswerService;

	@Operation(summary = "[ea] 평가 노트 저장", description = "##평가 노트 저장")
	@PostMapping(value = "/answer/note", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Void> saveAnswerNote(@Valid @RequestBody EaEvAnswerNoteReqDto eaEvAnswerNoteReqDto) {
		eaEvAnswerService.saveNote(eaEvAnswerNoteReqDto);
		return Response.ok();
	}

	@Operation(summary = "[ea] 평가 답안 저장", description = "##평가 답안 저장")
	@PostMapping(value = "/answer/save", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Void> saveAnswer(@Valid @RequestBody EaEvAnswerReqDto eaEvAnswerReqDto) {
		eaEvAnswerService.saveAnswer(eaEvAnswerReqDto);
		return Response.ok();
	}

	@Operation(summary = "[ea] 평가 답안 제출", description = "##평가 답안 제출")
	@PostMapping(value = "/answer/submit", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Void> submitAnswer(@Valid @RequestBody EaEvAnswerReqDto eaEvAnswerReqDto) {
		eaEvAnswerService.submitAnswer(eaEvAnswerReqDto);
		return Response.ok();
	}
}
