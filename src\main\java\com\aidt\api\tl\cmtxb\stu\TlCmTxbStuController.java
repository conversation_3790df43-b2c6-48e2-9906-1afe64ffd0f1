package com.aidt.api.tl.cmtxb.stu;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.aidt.api.tl.cmtxb.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-08 14:42:44
 * @modify date 2024-02-08 14:42:44
 * @desc TlCmTxbStu 교과학습 공통처리API(학생)
 */

@Slf4j
@Tag(name="[tl] 교과학습 공통처리API(학생)[TlCmtxbStu]", description="(학생용)교과학습 공통처리를 처리한다")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/stu/cmtxb")
public class TlCmTxbStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlCmTxbStuService tlCmTxbStuService;

    /**
     * 대단원 정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="대단원 정보 조회", description="대단원 정보 조회")
    @PostMapping(value = "/selectTxbLluList")
    public ResponseDto<List<TlCmTxbLluDto>> selectTxbLluList() {
        log.debug("Entrance selectTxbLluList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        TlCmTxbSrhDto srhDto = new TlCmTxbSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlCmTxbStuService.selectTxbLluList(srhDto));
    }

    /**
     * 차시 정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="차시 정보 조회", description="차시 정보 조회")
    @PostMapping(value = "/selectTxbTcList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlCmTxbLluDto>> selectTxbTcList(@RequestBody TlCmTxbSrhDto srhDto) {
        log.debug("Entrance selectTxbTcList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlCmTxbStuService.selectTxbTcList(srhDto));
    }

    /**
     * 수업예정 단원정보조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="수업예정 차시 정보 조회", description="수업예정 차시정보와 이전수업 차시정보를 취득한다.")
    @PostMapping(value = "/selectLastTxbTcList")
    public ResponseDto<Object> selectLastTxbTcList() {
        log.debug("Entrance selectLastTxbTcList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        TlCmTxbLastTcSrhDto srhDto = new TlCmTxbLastTcSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());
        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_STU);
        return Response.ok(tlCmTxbStuService.selectLastTxbTcList(srhDto));
    }

    /**
     * 이전차시 및 다음차시 정보조회
     * 
     * @param lrmpLodId 차시ID
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="이전차시 및 다음차시 정보조회", description="지정한 차시ID의 이전/다음 차시정보를 조회한다.")
    @PostMapping(value = "/selectTxbTcPrevNext")
    public ResponseDto<Object> selectTxbTcPrevNext(@Valid @RequestBody TlCmTxbPrevNextSrhDto srhDto) {
        log.debug("Entrance selectTxbTcPrevNext");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmTxbStuService.selectTxbTcPrevNext(srhDto));
    }


    /**
     * 학습활동클래스보드URL 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="학습활동클래스보드URL 조회", description="학습활동에 등록된 클래스보드URL을 조회한다.")
    @PostMapping(value = "/selectLrnAtvClsBrdUrl", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectLrnAtvClsBrdUrl(@Valid @RequestBody TlCmTxbClsBrdSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvClsBrdUrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmTxbStuService.selectTxbClsBrdUrl(srhDto));
    }

    /**
     * 학습활동클래스보드URL 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="학습활동클래스보드URL 조회 v3.3대응", description="학습활동에 등록된 클래스보드URL을 조회한다.")
    @PostMapping(value = "/selectLrnAtvClabdUrl", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectLrnAtvClabdUrl(@Valid @RequestBody TlCmTxbClsBrdSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvClabdUrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmTxbStuService.selectTxbClabdUrl(srhDto));
    }

    /**
     * 차시 정보 조회 (학생 홈)
     *
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="차시 정보 조회 (학생 홈)", description="차시 정보 조회 (학생 홈)")
    @PostMapping(value = "/selectTxbTcListHm", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlCmTxbTcHmDto>> selectTxbTcListHm(@RequestBody Map<String, String> req) {
        log.debug("Entrance selectTxbTcListHm");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        req.put("optTxbId", userDetails.getOptTxbId());
        req.put("lrnUsrId", userDetails.getUsrId());
        req.put("thbYn", req.get("thbYn") == null ? "N" : req.get("thbYn"));

        return Response.ok(tlCmTxbStuService.selectTxbTcListHm(req));
    }

    /**
     * 차시 정보 조회 (학생 홈)
     *
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="차시 정보 조회 (학생 홈)", description="차시 정보 조회 (학생 홈)")
    @PostMapping(value = "/selectBfOrAfTcList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlCmTxbTcHmDto>> selectBfOrAfTcList(@RequestBody Map<String, String> req) {
        log.debug("Entrance selectBfOrAfTcList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        req.put("optTxbId", userDetails.getOptTxbId());
        req.put("thbYn", req.get("thbYn") == null ? "N" : req.get("thbYn"));

        return Response.ok(tlCmTxbStuService.selectBeforeOrAfterTcListByLrmpNodId(req));
    }

}
