package com.aidt.api.tl.cmtxb.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 10:43:39
 * @modify date 2024-04-02 10:43:39
 * @desc [학습상세현황 조회 dto]
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbLrnDtlPstSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    @NotBlank(message = "{field.required}")
    private String optTxbId;

    /** 대단원ID*/
    @Parameter(name="대단원ID")
    private String lluNodId;

    /** 차시ID*/
    @Parameter(name="차시ID")
    private String lrmpNodId;

    /** 학생ID */
    @Parameter(name="학생ID")
    @NotBlank(message = "{field.required}")
    private String stuId;
}
