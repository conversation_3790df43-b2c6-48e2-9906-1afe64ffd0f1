package com.aidt.api.bc.chatbot.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
public class BcCbKeyWordDto{
	@Parameter(name="학습활동ID")
	private String lrnAtvId;
	@Parameter(name="학습활동명")
	private String lrnAtvNm;
	@Parameter(name="지식맵노드명(1depth명)")
	private String lrmpNodNm1;
	@Parameter(name="지식맵노드명(1depth)")
	private String lrmpNodId1;
	@Parameter(name="지식맵노드명(2depth명)")
	private String lrmpNodNm2;
	@Parameter(name="지식맵노드명(2depth)")
	private String lrmpNodId2;
	@Parameter(name="지식맵노드명(3depth명)")
	private String lrmpNodNm3;
	@Parameter(name="지식맵노드명(3depth)")
	private String lrmpNodId3;
	@Parameter(name="지식맵노드명(4depth명)")
	private String lrmpNodNm4;
	@Parameter(name="지식맵노드명(4depth)")
	private String lrmpNodId4;
	@Parameter(name="지식맵노드명(5depth명)")
	private String lrmpNodNm5;
	@Parameter(name="지식맵노드명(5depth)")
	private String lrmpNodId5;
	@Parameter(name="검색 키워드")
	private String kwdNm ;
	@Parameter(name="콘텐츠메타데이터ID")
	private String ctnMetaDataId ;
	@Parameter(name="구분코드")
	private String ctnTpCd ;
	@Parameter(name="구분코드명")
	private String ctnTpNm ;
	@Parameter(name="비디오파일경로")
	private String cdnPthNm ;
}
