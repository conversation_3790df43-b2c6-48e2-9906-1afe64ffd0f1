package com.aidt.api.ea.evcom.service;

import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.api.common.enums.SchoolGradeCode;
import com.aidt.api.common.enums.SubjectCode;
import com.aidt.api.ea.evcom.adapter.EaEvAnswerAdapter;
import com.aidt.api.ea.evcom.dto.EaEvResult;
import com.aidt.api.ea.evcom.dto.EaEvSppNtnResult;
import com.aidt.common.CommonUserDetail;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service
@RequiredArgsConstructor
public class EaEvSppNtnService {

	private final EaEvAnswerAdapter eaEvAnswerAdapter;
	private final EaEvSppNtnQueryService eaEvSppNtnQueryService;

	public void saveEaEvSppNtnRs(EaEvResult eaEvResult, CommonUserDetail userDetail) {

		//평가 제출 완료가 아닐 시,
		//형성/차시 평가가 아닐 시,
		//중고등 영어일 시에는 평가 보충 심화를 생성하지 않는다.
		if (!eaEvResult.isCompleted() ||
			!EvaluationDetailCode.isNationalStandard(eaEvResult.getEvDtlDvCd()) ||
			(!SchoolGradeCode.ELEMENTARY.equals(eaEvResult.getSchoolGradeCode()) && SubjectCode.EN.equals(
				eaEvResult.getSubjectCode()))) {
			return;
		}

		var eaEvSppNtnRs = EaEvSppNtnResult.of(eaEvResult, userDetail);

		var sppNtnQuestions = eaEvSppNtnQueryService.getSppNtnQuestion(eaEvSppNtnRs);

		if (CollectionUtils.isEmpty(sppNtnQuestions)) {
			log.info("평가 보충/심화 관련 문항이 없습니다. ::evId={},usrId={}", eaEvResult.getEvId(), userDetail.getUsrId());
			return;
		}

		var sppNtnAnswer = sppNtnQuestions.stream()
			.map(eaEvSppNtnQuestion -> EaEvSppNtnResult.EaEvSppNtnAnswer.builder()
				.qtmId(eaEvSppNtnQuestion.getQtmId())
				.tpcId(eaEvSppNtnQuestion.getTpcId())
				.build())
			.collect(Collectors.toList());

		eaEvSppNtnRs.addAnswer(sppNtnAnswer);

		eaEvAnswerAdapter.upsertEaEvSppNtnRs(eaEvSppNtnRs);
		eaEvAnswerAdapter.upsertEaEvSppNtnAnswers(eaEvSppNtnRs);

	}

}