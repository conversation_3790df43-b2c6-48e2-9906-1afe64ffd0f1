package com.aidt.api.xx.sample.vue;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.xx.sample.vue.dto.SampleGridDataDto;
import com.aidt.api.xx.sample.vue.dto.SampleMenuDto;
import com.aidt.api.xx.sample.vue.dto.SampleUserDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-01-05 10:42:32
 * @modify date 2023-01-05 10:42:32
 * @desc Sample Vue Service
 */
@Slf4j
@Tag(name="[xx] Sample.Vue" , description="Sample Vue")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/stu/sample/vue")
public class SampleVueController {

    private final SampleVueService vueService;

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="사용자 단건 조회", description = "Path Variable 방식 조회")
    @GetMapping(value = "/user/{userId}")
    public ResponseDto<SampleUserDto> selectUser(@PathVariable("userId") String userId) {
        log.debug("Entrance selectList");
        return Response.ok(vueService.selectUser(userId));
    }

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="Vue 메뉴 조회", description = "메뉴 조회")
    @GetMapping(value = "/selectMenu")
    public ResponseDto<List<SampleMenuDto>> selectMenu(SampleMenuDto menuDto) {
        log.debug("Entrance selectMenu");
        return Response.ok(vueService.selectMenu(menuDto));
    }

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="Vue 그리드 데이터 조회", description = "그리드 데이터 조회")
    @PostMapping(value = "/selectCrudList")
    public ResponseDto<List<SampleGridDataDto>> selectCrudList(@RequestBody SampleGridDataDto imsiGridDataDto) {
        log.debug("Entrance selectCrudList");
        return Response.ok(vueService.selectCrudList(imsiGridDataDto));
    }

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="Vue 그리드 데이터 상세 조회", description = "그리드 데이터 상세 조회")
    @GetMapping(value = "/crud/{no}")
    public ResponseDto<SampleGridDataDto> selectCrud(@PathVariable("no") String no) {
        log.debug("Entrance selectCrud");
        if (no != null && !no.isEmpty()) {
            return Response.ok(vueService.selectCrud(no));
        }
        return Response.ok();
    }

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="Vue 그리드 단건 저장", description = "그리드 단건 저장")
    @PostMapping(value = "/insertCrud")
    public ResponseDto<Integer> insertCrud(@RequestBody SampleGridDataDto imsiGridDataDto) {
        log.debug("Entrance insertCrud");
        imsiGridDataDto.setRegId("admin");
        return Response.ok(vueService.insertCrud(imsiGridDataDto));
    }

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="Vue 그리드 단건 수정", description = "그리드 단건 수정")
    @PutMapping(value = "/updateCrud")
    public ResponseDto<Integer> updateCrud(@RequestBody SampleGridDataDto imsiGridDataDto) {
        log.debug("Entrance updateCrud");
        return Response.ok(vueService.updateCrud(imsiGridDataDto));
    }

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    @Operation(summary="Vue 그리드 단건 삭제", description = "그리드 단건 삭제")
    @DeleteMapping(value = "/deleteCrud/{no}")
    public ResponseDto<Integer> deleteCrud(@PathVariable("no") String no) {
        log.debug("Entrance deleteCrud");
        return Response.ok(vueService.deleteCrud(no));
    }
}
