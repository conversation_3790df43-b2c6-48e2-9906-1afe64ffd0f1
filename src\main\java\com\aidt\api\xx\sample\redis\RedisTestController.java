package com.aidt.api.xx.sample.redis;


import com.aidt.common.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/sample/redis")
@Tag(name="[bc] Redis Test", description="Redis Test Controller")
@ConditionalOnExpression("'${spring.redis.enabled:false}'.equals('true')")
public class RedisTestController {
//    private final RedisTemplate redisTemplate;
//    private ObjectMapper objectMapper = new ObjectMapper();
//
//
//    @Operation(summary="Paging", description = "Paging Test")
//    @GetMapping("test01")
//    public Response.ResponseDto<Object> pagingTest01() {
//        ValueOperations<String, String> vop = redisTemplate.opsForValue();
//        vop.set("testString", "testStringValue");
//        return Response.ok();
//    }



}
