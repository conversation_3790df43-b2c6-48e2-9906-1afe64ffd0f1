package com.aidt.api.bc.home.dto;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HomeSummaryAiCoaching {

	private String usrId;
	private Integer evId;
	private Integer questionCount;
	private Integer correctCount;


	public boolean isComplete() {
		return !ObjectUtils.anyNull(evId, questionCount, correctCount);
	}

	//todo: 학습자 수준을 등록하는 것과 계산하는 기준이 왜 다른지 확인 필요. (영어는 차시 기준이라 애매함)
	// 차후, 학습자 수준 개편 시, 공통화 개선 필요
	public String getLearnerVelocityTypeCode() {
		if (!this.isComplete()) {
			return null;
		}
		double calc = (double)this.correctCount / this.questionCount;
		if (calc >= 0.9) {
			return "FS";
		} else if (calc >= 0.5) {
			return "NM";
		}
		return "SL";
	}

}
