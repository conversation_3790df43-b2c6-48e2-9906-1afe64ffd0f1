<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.cmt.cm">

	<!-- AI 코멘트 사용자 데이터 조회 -->
	<select id="selectAiCmtUsrData" resultType="com.aidt.api.al.cmt.dto.AiCmtUsrDataDto">
		SELECT
			AI_CMT_USR_DATA_ID,
			AI_CMT_NO,
			CMT_CN,
			CMT_TP as CMT_TYPE,
			MDF_DTM
		FROM
			LMS_LRM.AI_CMT_USR_DATA
		WHERE USR_ID = #{usrId}
		  AND EV_ID = #{evId}
		  AND USR_TP_CD = #{usrTpCd}
	</select>

	<!-- AI 코멘트 사용자 데이터 저장 -->
	<insert id="insertAiCmtUsrData" parameterType="com.aidt.api.al.cmt.dto.req.AiCmtUsrDataReqDto">
		INSERT INTO lms_lrm.ai_cmt_usr_data(
			EV_ID,
			AI_CMT_NO,
			CMT_TP,
			USR_ID,
			USR_TP_CD,
			CMT_CN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		) VALUES
		<foreach collection="list" index="index" item="cmt" separator=",">
			(
			#{cmt.evId},
			#{cmt.aiCmtNo},
			#{cmt.cmtType},
			#{cmt.usrId},
			#{cmt.usrTpCd},
			#{cmt.cmtCn},
			#{cmt.usrId},
			NOW(),
			#{cmt.usrId},
			NOW(),
			'DB_ID'
			)
		</foreach>
	</insert>

	<!-- AI 코멘트 사용자 데이터 삭제 -->
	<delete id="deleteAiCmtUsrData">
		DELETE
		FROM lms_lrm.ai_cmt_usr_data
		WHERE EV_ID = #{evId}
		  AND USR_ID = #{usrId}
		  AND USR_TP_CD = #{usrTpCd}
	</delete>

	<select id="selectIsSaved" parameterType="hashmap" resultType="boolean">
		SELECT IF(SAVED_DATE > EV_DATE, true, false) AS IS_SAVED
		FROM (
			SELECT (
				SELECT UD.CRT_DTM
				FROM LMS_LRM.AI_CMT_USR_DATA UD
				WHERE UD.EV_ID = #{evId} AND UD.USR_ID=#{usrId} AND UD.USR_TP_CD = #{usrTpCd}
				ORDER BY UD.CRT_DTM DESC LIMIT 1
			) AS SAVED_DATE,
					(
				SELECT RS.SMT_DTM
				FROM LMS_LRM.EA_EV EE JOIN LMS_LRM.EA_EV_RS RS ON RS.EV_ID = EE.EV_ID
				WHERE EE.EV_ID = #{evId}
				AND EV_CMPL_YN = 'Y'
				ORDER BY SMT_DTM DESC LIMIT 1
			) AS EV_DATE) AS D
	</select>
</mapper>