package com.aidt.api.bc.cm.textbook.service;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.api.bc.cm.textbook.adapter.BcTextbookAdapter;
import com.aidt.api.bc.cm.textbook.dto.Textbook;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true)
@Service
@RequiredArgsConstructor
public class BcTextbookQueryService {

	private final BcTextbookAdapter textbookAdapter;

	public Textbook getTextbook(String textbookId) {
		var textbook = textbookAdapter.getTextbook(textbookId);

		if (ObjectUtils.isEmpty(textbook)) {
			throw new BizException("교과서 정보를 찾을 수 없습니다.");
		}
		return textbook;
	}

}