package com.aidt.api.al.pl.cm.rcm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EaAiEvPredictProfile {

	private String optTxbId;
	private Integer lluKmmpNodId;
	private Integer mluKmmpNodId;
	private Integer sluKmmpNodId;
	private Integer tcKmmpNodId;
	private Integer tpcKmmpNodId;
	private Long qtmId;
	private String ctnDffdDvCd;
	private String lrnrVelTpCd;
	private String cansYn;
	private Double aiPredCansRt;

	public void addCorrectAnswerRate(Double correctAnswerRate) {
		this.aiPredCansRt = correctAnswerRate;
	}

}
