package com.aidt.api.sl.splrn.dto;

import java.util.Date;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-16 16:54:40
 * @modify : date 2024-02-16 16:54:40
 * @desc : 특별학습 추천 목록 리스트
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnRcmdDto {
	
	@Parameter(name="특별학습 ID")
	private String spLrnId;

	@Parameter(name="특별학습 상위노드ID")
	private String urnkSpLrnNodId;
	
	@Parameter(name="특별학습 노드ID")
	private String spLrnNodId;

	@Parameter(name="특별학습노드명")
	private String spLrnNodNm;

	@Parameter(name="콘텐츠ID")
	private String spLrnCtnId;

	@Parameter(name="콘텐츠명")
	private String spLrnCtnNM;
	
	@Parameter(name="재구성순서")
	private String rcstnOrdn;

	@Parameter(name="원본순서")
	private String srtOrdn;
	
	@Parameter(name="모바일 파일경로")
	private String moPath;

	@Parameter(name="태블릿 파일경로")
	private String taPath;

	@Parameter(name="PC 파일경로")
	private String pcPath;
	
	@Parameter(name="상태코드")
	private String lrnStCd;
	
	@Parameter(name="수정일시")
	private Date mdfDtm;
	
	/* 과제 체크 */
	@Parameter(name="과제ID")
	private String asnId;
	
	@Parameter(name="과제명")
	private String asnNm;
	
	@Parameter(name="학습유형코드")
	private String lrnTpCd;
	
	@Parameter(name="과제기간구분코드")
	private String asnPtmeDvCd;

	@Parameter(name="시작일시")
	private String strDtm;
	
	@Parameter(name="종료일시")
	private String endDtm;
	
	
}
