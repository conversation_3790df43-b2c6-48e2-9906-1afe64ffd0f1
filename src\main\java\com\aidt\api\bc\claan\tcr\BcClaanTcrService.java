package com.aidt.api.bc.claan.tcr;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.claan.dto.BcClaanReqDto;
import com.aidt.api.bc.claan.dto.BcClaanResDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-07-08 10:53:00
 * @modify 2024-07-08 10:53:00
 * @desc 학급분석 서비스
 */
@Service
public class BcClaanTcrService {
	
	private final String MAPPER_NAMESPACE = "api.bc.claan.tcr.";
	
	@Autowired
    private CommonDao commonDao;
	
	public BcClaanResDto selectLrnSmy(BcClaanReqDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectLrnSmy", dto);
	}
	
	public List<BcClaanResDto> selectLuAchdPst(BcClaanReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectLuAchdPst", dto);
	}
	
	public List<BcClaanResDto> selectEvAnTabList(BcClaanReqDto dto) {
		List<BcClaanResDto> evAnTabList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvAnTabList", dto); 
//		for (BcClaanResDto info : evAnTabList ) {
//			BcClaanReqDto pDto = new BcClaanReqDto();
//			pDto.setOptTxbId(dto.getOptTxbId());
//			pDto.setEvId(info.getEvId());
//			pDto.setAvgCansRt(info.getCansRt());
//			List<BcClaanResDto> stuList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnNeceStuList", pDto);
//			info.setTcAnStuList(stuList);
//		}
		return evAnTabList;
	}
	
	public List<BcClaanResDto> selectTcAnTabList(BcClaanReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTcAnTabList", dto);
	}
	
	public List<BcClaanResDto> selectAchBsAnTabList(BcClaanReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectAchBsAnTabList", dto);
	}
	
	public List<BcClaanResDto> selectPartAnTabList(BcClaanReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectPartAnTabList", dto);
	}
	
	public List<BcClaanResDto> selectCrsCnAchPstList(BcClaanReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectCrsCnAchPstList", dto);
	}
	
	public Map<String, Object> selectEaLrnPrefInfo(BcClaanReqDto dto){

        Map<String, Object> map = new HashMap<>();
        //학습패턴 - 선호도 조회
        map.put("lrnPref", commonDao.select(MAPPER_NAMESPACE + "selectLrnPref", dto));
        //학습패턴 - 학습코칭 > 챌린지 조회
        map.put("lrnChal", commonDao.select(MAPPER_NAMESPACE + "selectLrnChal", dto));
        //학습패턴 - 학습코칭 > 평균 챌린지 도전율 
        map.put("lrnChalRate", selectLrnChalRate(dto));        
        //학습패턴 - 학습코칭 > 과제 제출률
         map.put("lrnAsn", commonDao.select(MAPPER_NAMESPACE + "selectEaLrnAsnInfo", dto));
        //학습패턴 - 요일별 분석
        map.put("lrnWeekAn", commonDao.selectList(MAPPER_NAMESPACE + "selectAnPerWeek", dto));
        //학습패턴 - 시간대별 분석
        map.put("lrnTmAn", commonDao.selectList(MAPPER_NAMESPACE + "selectAnPerTm", dto));
        
        
        return map;
	}
	
	
	/**
	 * 평균 챌린지 도전율  
	 * @param dto
	 * @return
	 */
	public Double selectLrnChalRate(BcClaanReqDto dto){		       		
        Double rs = 0.0; 
        String optTxbId = dto.getOptTxbId();
                
        // 시작일을 3월 1일로 설정
        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), 3, 1);
        
        // 3월 1일이 포함된 주의 월요일로 이동
        startDate = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        
        LocalDate currentDate = LocalDate.now();  // 현재 날짜
        int weekNumber = 1;  // 누적 주차

        if (currentDate.isBefore(startDate)) {
            return 0.0;
        }
        
        while (startDate.plusWeeks(1).isBefore(currentDate)) {
        	DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String startDateStr = startDate.format(formatter);
        	
        	// 주의 마지막 날(일요일) 구하기
            LocalDate endDate = startDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
            String endDateStr = endDate.format(formatter);
            rs += (Double) commonDao.select(MAPPER_NAMESPACE + "selectLrnChalRate",Map.of("startDate", startDateStr, "endDate", endDateStr, "optTxbId", optTxbId));
            
            // 다음 주의 시작일로 이동
            startDate = startDate.plusWeeks(1);
            weekNumber++;
        }        

        return Math.round(rs/weekNumber * 10) / 10.0 ;        
	}
}
