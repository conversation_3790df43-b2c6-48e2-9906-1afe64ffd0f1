package com.aidt.api.ea.evcom.event;

import java.util.ArrayList;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.bc.mntr.stu.BcMntrStuService;
import com.aidt.api.common.helper.PointHelper;
import com.aidt.api.ea.evcom.enums.EaEvPointCode;
import com.aidt.api.ea.evcom.service.EaEvLearnLevelService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@AllArgsConstructor
public class EaEvAnswerSubmitEventHandler {

	private final BcCmService bcCmService;
	private final BcMntrStuService bcMntrStuService;
	private final EaEvLearnLevelService eaEvLearnLevelService;
	private final PointHelper pointHelper;

	@Async
	@TransactionalEventListener
	public void updateLearningTime(EaEvAnswerSubmitEvent event) {
		var eaEvRs = event.getEaEvRs();
		var user = event.getUserDetail();

		try {
			var eaEvAnswerReqDto = event.getEaEvAnswerReqDto();
			long evLrnTmScnt =
				eaEvAnswerReqDto.getEvTmScnt() != null ? Long.valueOf(eaEvAnswerReqDto.getEvTmScnt()) : 0L;

			BcCmLrnTmDto bcCmLrnTmDto = BcCmLrnTmDto.builder()
				.aiEvLrnTmScnt(eaEvRs.isAiEvAnswer() ? evLrnTmScnt : null)
				.evLrnTmScnt(eaEvRs.isAiEvAnswer() ? null : evLrnTmScnt)
				.build();

			bcCmService.updateCmLrnTm(bcCmLrnTmDto, event.getAccessToken(), event.getUserDetail());
		} catch (Exception e) {
			log.error("학습 시간 업데이트 실패:: evId={}, usrId={}", eaEvRs.getEvId(), user.getUsrId());
		}
	}

	@Async
	@TransactionalEventListener
	public void updateLearningLevel(EaEvAnswerSubmitEvent event) {
		var eaEvRs = event.getEaEvRs();
		var user = event.getUserDetail();

		try {
			//학습자 수준 및 단원별 학습 수준 업데이트
			eaEvLearnLevelService.saveAllLearnLevels(user);
		} catch (Exception e) {
			log.error("학습자 수준 및 단원별 학습 수준 업데이트 실패:: evId={}, usrId={}", eaEvRs.getEvId(), user.getUsrId());

		}

	}

	@Async
	@TransactionalEventListener
	public void saveGuideTeaching(EaEvAnswerSubmitEvent event) {
		var eaEvRs = event.getEaEvRs();
		var user = event.getUserDetail();

		try {
			var eaEvAnswerReqDto = event.getEaEvAnswerReqDto();
			//fixme: 재응시일시, 지도필요가 리업뎃해야하나 확인  필요?
			if (!user.getUsrTpCd().equals("ST") || !eaEvRs.isCompleted()) {
				return;
			}

			var messages = new ArrayList<String>();

			if (eaEvRs.hasNoCorrectAnswers()) {
				messages.add("평가 0점");
			}
			if (!eaEvAnswerReqDto.isOneMinuteExceeded()) {
				messages.add("풀이 시간 1분 미만");
			}

			if (CollectionUtils.isEmpty(messages)) {
				return;
			}

			bcMntrStuService.insertGdeNeed(BcMntrDto.builder()
				.optTxbId(user.getOptTxbId())
				.lrnUsrId(user.getUsrId())
				.gdeNeedCd("ERA")
				.gdeNeedVl(eaEvRs.getEvId().toString())
				.gdeNeedInfo(String.join("and", messages))
				.build());
		} catch (Exception e) {
			log.error("학습 지도 필요 등록 실패:: evId={}, usrId={}", eaEvRs.getEvId(), user.getUsrId());
		}
	}

	@Async
	@TransactionalEventListener
	public void savePoint(EaEvAnswerSubmitEvent event) {
		var eaEvRs = event.getEaEvRs();
		var user = event.getUserDetail();

		try {
			String pointCode = EaEvPointCode.getPointCode(eaEvRs.getEvDtlDvCd());

			if (!"SE".equalsIgnoreCase(eaEvRs.getEvDvCd()) || !eaEvRs.isCompleted() || StringUtils.isBlank(pointCode)) {
				return;
			}
			pointHelper.saveAsync(pointCode, eaEvRs.getEvId(), event.getAccessToken());
		} catch (Exception e) {
			log.error("평가 포인트 적립 실패:: evId={}, usrId={}", eaEvRs.getEvId(), user.getUsrId());
		}
	}
}
