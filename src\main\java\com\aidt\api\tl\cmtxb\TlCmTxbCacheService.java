package com.aidt.api.tl.cmtxb;

import com.aidt.api.tl.cmtxb.dto.TlCmTxbTcHmDto;
import com.aidt.common.CommonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class TlCmTxbCacheService {
    private final String MAPPER_NAMESPACE = "api.tl.cmtxb.stu.";

    @Autowired
    private CommonDao commonDao;

    @Transactional(readOnly = true)
    @Cacheable(
            cacheNames = "longCache",
            key = "'tl:' + #optTxbId + ':selectTxbTcListHm'",
            unless = "#result.size() == 0",
            cacheManager = "aidtCacheManager"
    )
    public List<TlCmTxbTcHmDto> selectTxbTcListHmQuery(String optTxbId, String sbjCd) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectTxbTcListHm", Map.of("optTxbId", optTxbId, "sbjCd", sbjCd));
    }

    @Transactional(readOnly = true)
    @Cacheable(
            cacheNames = "longCache",
            key = "'tl:' + #optTxbId + ':selectAllTxbTcList'",
            condition="#lrnUsrId.isEmpty() == true",
            unless = "#result.size() == 0",
            cacheManager = "aidtCacheManager"
    )
    public List<Map<String, Object>> selectAllTxbTcList(String optTxbId, String lrnUsrId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAllTxbTcList", Map.of("optTxbId", optTxbId, "lrnUsrId", lrnUsrId));
    }

}
