<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.ev.stu">

    <!--    학생화면 평가리스트 조회  com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto  -->
	<select id="selectEvList" resultType="hashMap">
       /** EaEvStu-Mapper.xml - selectEvList */

		SELECT 
			  E.EV_ID				AS evId					-- 평가ID
			, COUNT(1) OVER() 		AS totalCnt 			-- 총 조회 갯수
			, E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
			, E.USR_ID				AS usrId				-- 사용자ID
			, E.EV_NM				AS evNm					-- 평가명
			, E.EV_DV_CD			AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
			, E.EV_DTL_DV_CD		AS evDtlDvCd			-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
			, EV_DTL.CM_CD_NM       AS evDtlDvNm -- 평가상세구분명
			, CASE 	WHEN E.LCKN_YN = 'Y' THEN 'N' -- 잠금 평가 비활성
					WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN 'N' -- 대기
					WHEN IFNULL(R.EV_CMPL_YN, 'N') = 'N' AND CURRENT_TIMESTAMP > E.TXM_END_DTM THEN 'N' -- 응시시작 전, 응시기간 종료
				   	ELSE 'Y'
				  	END 			AS evActvYn				-- 평가활성여부 (응시예정 탭)
			, CASE 	WHEN E.LCKN_YN = 'Y' THEN '잠금' 
					WHEN E.TXM_STR_DTM > CURRENT_TIMESTAMP THEN '대기' -- 응시기간 도래전, 미잠금
					WHEN R.EV_CMPL_YN = 'Y' THEN '완료'
					WHEN IFNULL(R.EV_CMPL_YN, 'N') = 'N' THEN '미응시'
					ELSE '응시 중' 
				   	END 			AS txmStNm				-- 평가상태 응시상태			
			, E.TXM_PTME_SETM_YN 	AS txmPtmeSetmYn        -- 응시기간설정여부 
			, CONCAT(DATE_FORMAT(E.TXM_STR_DTM,'%m. %d. '),IF(TIME_FORMAT(E.TXM_STR_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_STR_DTM,'%h:%i')) AS txmStrDtm -- 응시 시작일
			, CONCAT(DATE_FORMAT(E.TXM_END_DTM,'%m. %d. '),IF(TIME_FORMAT(E.TXM_END_DTM, '%p') = 'AM', '오전 ', '오후 '),DATE_FORMAT(E.TXM_END_DTM,'%h:%i')) AS txmEndDtm -- 응시 종료일
			, DATE_FORMAT(R.SMT_DTM, '%y-%m-%d') 			AS smtDtm 		-- 제출일시
			, IFNULL(R.TXM_STR_YN, 'N')						AS txmStrYn		-- 응시시작여부
			, IFNULL(R.EV_CMPL_YN, 'N')						AS evCmplYn		-- 평가완료여부
			, IFNULL(RR.RTXM_PN, 0) 						AS txmPn 		-- 응시회차
			, IFNULL(RR.EV_CMPL_YN, 'N')					AS evCmplYnRtxm	-- 최종 재응시의 평가완료여부
			, IFNULL(ESNR.EV_CMPL_YN, 'X')					AS sppNtnCmplYn -- 보충심화 완료여부 (X는 보충심화 없다는 뜻)
			, IFNULL(ESNR.TXM_STR_YN, 'X')					AS sppNtnStrYn  -- 보충심화 시작여부
			/*
			, IFNULL( (SELECT EV_CMPL_YN 
					   FROM LMS_LRM.EA_EV_SPP_NTN_RS 
					   WHERE EV_ID = E.EV_ID 
					   AND USR_ID = R.USR_ID 
					   AND TXM_PN = IFNULL(RR.RTXM_PN, 0)
					   -- ORDER BY TXM_PN DESC
					   LIMIT 1
			   ), 'X') AS sppNtnCmplYn -- 보충심화 완료여부
			*/
			, E.XPL_TM_SETM_YN  	AS xplTmSetmYn			-- 풀이시간설정여부
			, E.XPL_TM_SCNT			AS xPlTmScntAll			-- 풀이시간 초수 전체초수
			, CAST(FLOOR(E.XPL_TM_SCNT/60) AS Unsigned Integer) AS xplTmMi	-- 풀이시간 분
			, E.XPL_TM_SCNT%60		AS xplTmScnt			-- 풀이시간 초수
			, E.QST_CNT				AS qstCnt				-- 문제수
			, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수	
			, IFNULL(RR.CANS_CNT, IFNULL(R.CANS_CNT, 0)) 		AS cansCnt	-- 정답수
			, E.LCKN_YN				AS lcknYn				-- 잠금여부
			, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
			, E.USE_YN				AS useYn				-- 사용여부
			, E.CRTR_ID				AS crTrId				-- 생성자
			, E.CRT_DTM				AS crtDtm				-- 생성일
			, E.MDFR_ID				AS mdfrId				-- 수정자
			, E.MDF_DTM				AS mdfDtm				-- 수정일
			, E.DB_ID				AS dbId					-- DB ID
			, T.LU_LRMP_NOD_ID		AS luLrmpNodId			-- 대단원 ID
			, CASE WHEN IFNULL(NOD_LU.RCSTN_NO, '') = '' THEN NOD_LU.LRMP_NOD_NM
				   ELSE CONCAT(NOD_LU.RCSTN_NO, '. ', NOD_LU.LRMP_NOD_NM) 
				   END 				AS luLrmpNodNm 			-- 대단원명
			, T.TC_LRMP_NOD_ID		AS tcLrmpNodId			-- 차시 ID
			, CASE WHEN IFNULL(NOD_TC.RCSTN_NO, '') = '' THEN NOD_TC.LRMP_NOD_NM
				   ELSE CONCAT(NOD_TC.RCSTN_NO, '. ', NOD_TC.LRMP_NOD_NM) 
				   END 				AS tcLrmpNodNm 			-- 대단원명
 			, (	   
 				   SELECT ATV.LRN_ATV_ID
	 			   FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV
	 			   WHERE ATV.OPT_TXB_ID = NOD_TC.OPT_TXB_ID
	 			   AND ATV.LRMP_NOD_ID = NOD_TC.LRMP_NOD_ID
	 			   AND ATV.EV_ID = E.EV_ID 
	 			   AND ATV.USE_YN = 'Y'
	 			   ORDER BY ATV.RCSTN_ORDN DESC LIMIT 1
 			  ) AS lrnAtvId -- 학습활동 엑티비디 ID
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = E.EV_ID AND R.USR_ID = #{usrId}
		LEFT JOIN (
				SELECT 
						RR.EV_ID, RR.USR_ID, MAX(RR.RTXM_PN) RTXM_PN
				FROM LMS_LRM.EA_EV_RS_RTXM RR
				WHERE EXISTS (SELECT 1 FROM LMS_LRM.EA_EV E2 WHERE E2.OPT_TXB_ID = #{optTxbId} AND E2.EV_DV_CD IN ('SE', 'TE') AND E2.DEL_YN = 'N' AND E2.USE_YN = 'Y' AND E2.EV_ID = RR.EV_ID)
				AND RR.USR_ID = #{usrId}
				GROUP BY RR.EV_ID, RR.USR_ID
		) RR_MAX ON RR_MAX.EV_ID = R.EV_ID AND RR_MAX.USR_ID = R.USR_ID
		LEFT JOIN LMS_LRM.EA_EV_RS_RTXM RR ON RR.EV_ID = RR_MAX.EV_ID AND RR.USR_ID = RR_MAX.USR_ID AND RR.RTXM_PN = RR_MAX.RTXM_PN		
		LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS ESNR ON ESNR.EV_ID = R.EV_ID AND ESNR.USR_ID = R.USR_ID AND ESNR.TXM_PN = IFNULL(RR_MAX.RTXM_PN, 0)		
		LEFT JOIN LMS_LRM.CM_CM_CD EV_DTL ON EV_DTL.URNK_CM_CD = 'EV_DTL_DV_CD' AND EV_DTL.CM_CD = E.EV_DTL_DV_CD
		
		<choose>
			<when test = '!"".equals(srhLuLrmpNodId)'>
				LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD IN ('SE', 'TE') -- 교과평가, 선생님 평가(TE) 단원만 조회
			</when>
			<otherwise>
				LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD IN ('SE') -- 교과평가 단원만 조회
			</otherwise>
		</choose>
		
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_MLU ON NOD_MLU.OPT_TXB_ID = T.MLU_OPT_TXB_ID AND NOD_MLU.LRMP_NOD_ID = T.MLU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_SLU ON NOD_SLU.OPT_TXB_ID = T.SLU_OPT_TXB_ID AND NOD_SLU.LRMP_NOD_ID = T.SLU_LRMP_NOD_ID		
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = T.TC_LRMP_NOD_ID		
		WHERE E.OPT_TXB_ID = #{optTxbId}
		AND (E.EV_DV_CD = 'SE' OR (E.EV_DV_CD = 'TE' AND IFNULL(R.STU_EV_ABLE_YN, 'N') = 'Y'))
		AND E.DEL_YN = 'N'
		AND E.USE_YN = 'Y'
		<if test = 'srhEvTp != null and !"".equals(srhEvTp)'><!-- 평가유형 ST, UD, FO, TO,	UG, ET-->
    	AND E.EV_DTL_DV_CD = #{srhEvTp}
    	</if>
    	<if test = '!"".equals(srhLuLrmpNodId)'><!-- 대단원 dropdown -->
    	AND T.LU_LRMP_NOD_ID = #{srhLuLrmpNodId}
    	</if>
		<choose>
    	<when test = '"0".equals(tbscDvCd)'><!-- 진행중 탭 > 노출설정 여부, 응시기간 종료전 -->
		AND (IFNULL(R.EV_CMPL_YN, 'N') = 'N' AND (E.TXM_PTME_SETM_YN = 'N' OR CURRENT_TIMESTAMP BETWEEN E.TXM_STR_DTM AND E.TXM_END_DTM ))
    	</when>
    	<when test = '"1".equals(tbscDvCd)'><!-- 완료 탭 > 응시완료 , 응시 가능기간 종료-->
		AND (R.EV_CMPL_YN = 'Y' OR CURRENT_TIMESTAMP > E.TXM_END_DTM)
    	</when>
		</choose>
		<choose>
    	<when test = '"2".equals(srtDvCd)'><!-- 마감일순 -->
		ORDER BY CASE 	WHEN E.TXM_PTME_SETM_YN = 'Y' AND E.EV_DV_CD = 'TE' THEN 0
				   		ELSE 1
				   		END -- 마감일 있는데이터 > 교사평가 순
			    , CASE 	WHEN E.TXM_PTME_SETM_YN = 'Y' THEN E.TXM_END_DTM
				   		ELSE '9999-12-30 00:00:00' 
				   		END -- 마감일 있는데이터 > 마감일 근접순
			   , CASE WHEN E.EV_DV_CD = 'SE' THEN 
							 CASE WHEN E.EV_DTL_DV_CD = 'ST' THEN 0
							 	  WHEN E.EV_DTL_DV_CD = 'ET' THEN 99999
							 	  ELSE NOD_LU.RCSTN_ORDN
							 	  END
				   		ELSE -1
				   		END
			   , IF(E.EV_DV_CD = 'TE', E.MDF_DTM, NULL) DESC  -- 교사평가 최신순
			   , NOD_LU.RCSTN_ORDN 						-- 대단원 재구성 순서
			   , EV_DTL.SRT_ORDN 						-- 평가지유형 순서
			   , IFNULL(NOD_MLU.RCSTN_ORDN, 99999)		-- 중단원 재구성 순서
			   , IFNULL(NOD_SLU.RCSTN_ORDN, 99999)		-- 소단원 재구성 순서
			   , IFNULL(NOD_TC.RCSTN_ORDN, 99999)		-- 차시단원 재구성 순서
			   , E.EV_NM DESC							-- 평가지명 내림차순
    	</when>
    	<otherwise> <!-- 정렬구분 - 단원순 - 기본값 srtDvCd.equals("1")  -->
		ORDER BY CASE WHEN E.EV_DV_CD = 'SE' THEN 
							 CASE WHEN E.EV_DTL_DV_CD = 'ST' THEN 0
							 	  WHEN E.EV_DTL_DV_CD = 'ET' THEN 99999
							 	  ELSE NOD_LU.RCSTN_ORDN
							 	  END
				   		ELSE -1
				   		END
			   , IF(E.EV_DV_CD = 'TE', E.MDF_DTM, NULL) DESC
			   , NOD_LU.RCSTN_ORDN 						-- 대단원 재구성 순서
			   , EV_DTL.SRT_ORDN 						-- 평가지유형 순서
			   , IFNULL(NOD_MLU.RCSTN_ORDN, 99999)		-- 중단원 재구성 순서
			   , IFNULL(NOD_SLU.RCSTN_ORDN, 99999)		-- 소단원 재구성 순서
			   , IFNULL(NOD_TC.RCSTN_ORDN, 99999)		-- 차시단원 재구성 순서
			   , E.EV_NM DESC							-- 평가지명 내림차순
    	</otherwise>
		</choose>
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}


	</select>
</mapper>