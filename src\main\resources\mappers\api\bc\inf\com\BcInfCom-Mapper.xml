<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.inf.com">
	
	
	
	
	
	<!-- 과제 알림 대상 학생 조회 -->
	<select id="selectAsnInfmTrgt" parameterType="com.aidt.api.bc.inf.infCom.dto.InfComDto" resultType="String">
		SELECT 
			STU_USR_ID AS usr_id 
		FROM LMS_LRM.EA_ASN_SMT 
		WHERE ASN_ID = #{asnId} 	 
	</select>
	
	<!-- 클래스ID 조회 -->
	<select id="selectClaId" parameterType="com.aidt.api.bc.inf.infCom.dto.InfComDto" resultType="String">
		SELECT CLA_ID FROM LMS_LRM.CM_OPT_TXB WHERE OPT_TXB_ID = #{optTxbId}
	</select>
	
	
	<!-- 다른학급 저장 알림상세 insert -->
	<insert id="insertInfmDtlOtherCla" parameterType="com.aidt.api.bc.inf.infCom.dto.InfComDto">
		/** BcInfCom-Mapper.xml - insertInfmDtlOtherCla */
		
		INSERT INTO LMS_LRM.CM_INFM (
			  OPT_TXB_ID
			, TCR_USR_ID
			, INFM_MSG_ID
			, INFM_OBJ_USR_ID		    
			, INFM_MV_CN 
			, COFM_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		)	
		VALUES
		<foreach collection="list" item="item" separator=",">
		(
		  	  #{item.optTxbId}
			, #{item.tcrUsrId}
			, #{item.infmMsgId}
			, #{item.infmObjUsrId}
			, #{item.infmMvCn}
			, 'N' 
			, #{item.usrId}
			, NOW()
			, #{item.usrId}
			, NOW()
			, (SELECT TXB_ID FROM LMS_LRM.CM_OPT_TXB WHERE OPT_TXB_ID = #{item.optTxbId})
		)
		</foreach>
	</insert>
	
	
	
	<!-- 평가 등록/수정 알림 상세 insert -->
	<insert id="insertEvInfmDtl" parameterType="com.aidt.api.bc.inf.infCom.dto.InfComDto">
		/** BcInfCom-Mapper.xml - insertEvInfmDtl */
		
		INSERT INTO LMS_LRM.CM_INFM (
			  OPT_TXB_ID
			, TCR_USR_ID
			, INFM_MSG_ID
			, INFM_OBJ_USR_ID		    
			, INFM_MV_CN 
			, COFM_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		)	
		SELECT 				
			  #{optTxbId}
			, #{usrId}
			, #{infmMsgId}
			, USR_ID
			, #{infmMvCn}
			, 'N' 
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		FROM LMS_LRM.EA_EV_RS WHERE EV_ID = #{evId} AND STU_EV_ABLE_YN = 'Y'
	</insert>
	
	
	<!-- 알림 마스터 insert -->
	<insert id="insertInfmMst" parameterType="com.aidt.api.bc.inf.infCom.dto.InfComDto" useGeneratedKeys="true" keyProperty="infmMsgId">		
		/** BcInfCom-Mapper.xml - insertInfmMst */
		
		INSERT INTO LMS_LRM.CM_INFM_MSG (
			  INFM_TP_CD
			, INFM_CL_CD
			, INFM_DTL_CL_CD
			, INFM_NM
			, INFM_CN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		) VALUES (
			  #{infmTpCd}
			, #{infmClCd}
			, #{infmDtlClCd}
			, #{infmNm}
			, #{infmCn}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()									
			, (SELECT TXB_ID FROM LMS_LRM.CM_OPT_TXB WHERE OPT_TXB_ID = #{optTxbId})			
		)
	</insert>
	
	
	<insert id="insertRcmCtnInfmDtl" parameterType="com.aidt.api.bc.inf.infCom.dto.InfComDto">
		/** BcInfCom-Mapper.xml - insertRcmCtnInfmDtl */
		
		INSERT INTO LMS_LRM.CM_INFM (
			  OPT_TXB_ID
			, TCR_USR_ID
			, INFM_MSG_ID
			, INFM_OBJ_USR_ID		    
			, INFM_MV_CN 
			, COFM_YN
			, CRTR_ID
			, CRT_DTM
			, MDFR_ID
			, MDF_DTM
			, DB_ID
		)	
		SELECT 	
			  #{optTxbId}
			, #{usrId}
			, #{infmMsgId}
			, inf.usr_id
			, #{infmMvCn}
			, 'N' 
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		FROM (
				/* 첫 접속시 데이터 없는 경우 */
				SELECT 	
					t1.USR_ID
				FROM (
						SELECT 
							USR_ID 
						FROM LMS_LRM.CM_USR 
						WHERE CLA_ID = (SELECT CLA_ID FROM LMS_LRM.CM_OPT_TXB WHERE OPT_TXB_ID = #{optTxbId})
						AND USR_TP_CD = 'ST'
				) AS t1 
				LEFT OUTER JOIN (
									SELECT 
										  R.SP_LRN_ID	 -- 특별학습ID
										, R.LRN_USR_ID	 -- 학습사용자ID
										, R.RCM_YN		 -- 추천여부
									FROM LMS_LRM.SL_STU_RCM_LRN R				/* SL_학생별추천학습 */
									WHERE 1=1
									AND R.SP_LRN_ID = #{spLrnId}
									AND R.OPT_TXB_ID = #{optTxbId}
								) AS t2
							ON t2.LRN_USR_ID = t1.USR_ID
				WHERE T2.LRN_USR_ID IS NULL 		
				UNION ALL 
				/*추천여부 N인 학생 조회*/
				SELECT 	
					LRN_USR_ID				-- 학습사용자ID	
				FROM LMS_LRM.SL_STU_RCM_LRN R				/* SL_학생별추천학습 */
				WHERE 1=1
				AND R.SP_LRN_ID = #{spLrnId}
				AND R.OPT_TXB_ID = #{optTxbId}
				and R.RCM_YN = 'N'
				ORDER BY 1				
		) as inf
			
	</insert>
	
	
	<!-- 알림 삭제  -->
	<delete id="deleteInfComMst" parameterType="Map">
		/** BcInfCom-Mapper.xml - deleteInfComMst */
		DELETE FROM LMS_LRM.CM_INFM_MSG 
		where INFM_MSG_ID in (
						select distinct INFM_MSG_ID 
						FROM LMS_LRM.CM_INFM
						WHERE
						<choose>
							<when test= 'map.infmClCd.equals("CB")'>	<!-- 학급게시판 -->			
								json_value(INFM_MV_CN, '$.params.claBlbdId') in (
								<foreach collection="map.param" item="item" separator=", ">
									#{item}
								</foreach>	
								) 												
							</when>							
							 <when test= 'map.infmClCd.equals("AS")'>	<!-- 과제 -->	
								<foreach collection="map.param" item="item" separator="or">
								(
								json_value(INFM_MV_CN, '$.params.asnId') = #{item.asnId}
								and infm_obj_usr_id = #{item.usrId}
								)
								</foreach>						
							</when>
							<when test= 'map.infmClCd.equals("SE")'> 	<!-- 평가 -->
								json_value(INFM_MV_CN, '$.params.evId') = #{map.evId}
								and TCR_USR_ID = #{map.tcrUsrId}						
							</when>				
							<when test= 'map.infmClCd.equals("RC")'>	<!-- 추천학습 -->
								<foreach collection="map.param" item="item" separator="or">
								(
								json_value(INFM_MV_CN, '$.params.spLrnId') = #{item.spLrnId}
								and infm_obj_usr_id = #{item.usrId}
								)
								</foreach>	 
							</when>							 							 
						</choose>					
					)
		
		
		
	</delete>
	
	
	
	<delete id="deleteInfCom" parameterType="Map">
		/** BcInfCom-Mapper.xml - deleteInfCom */
		
		DELETE FROM LMS_LRM.CM_INFM
		WHERE
		<choose>
			<when test= 'map.infmClCd.equals("CB")'>	<!-- 학급게시판 -->			
				json_value(INFM_MV_CN, '$.params.claBlbdId') in (
				<foreach collection="map.param" item="item" separator=", ">
					#{item}
				</foreach>	
				) 												
			</when>							
			 <when test= 'map.infmClCd.equals("AS")'>	<!-- 과제 -->	
				<foreach collection="map.param" item="item" separator="or">
				(
				json_value(INFM_MV_CN, '$.params.asnId') = #{item.asnId}
				and infm_obj_usr_id = #{item.usrId}
				)
				</foreach>						
			</when>
			<when test= 'map.infmClCd.equals("SE")'> 	<!-- 평가 -->
				json_value(INFM_MV_CN, '$.params.evId') = #{map.evId}
				and TCR_USR_ID = #{map.tcrUsrId}						
			</when>				
			<when test= 'map.infmClCd.equals("RC")'>	<!-- 추천학습 -->
				<foreach collection="map.param" item="item" separator="or">
				(
				json_value(INFM_MV_CN, '$.params.spLrnId') = #{item.spLrnId}
				and infm_obj_usr_id = #{item.usrId}
				)
				</foreach>	 
			</when>							 							 
		</choose>		
	</delete>
	
	
</mapper>