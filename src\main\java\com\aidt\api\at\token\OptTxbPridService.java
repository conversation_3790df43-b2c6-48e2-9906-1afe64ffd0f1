package com.aidt.api.at.token;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.at.token.dto.KerisDto;
import com.aidt.api.at.token.dto.OptTxbPridDto;
import com.aidt.common.CommonDao;

@Service
public class OptTxbPridService {

	@Autowired
	private CommonDao commonDao;

	@Cacheable(cacheNames = "longCache", key = "'bc:optTxbPrid:' + #dto.optTxbId + ':' + #dto.optTxbPrid", cacheManager = "aidtCacheManager")
	@Transactional
	public OptTxbPridDto insertOptTxbPrid(OptTxbPridDto dto, KerisDto kerisDto) {
		if (((Integer) commonDao.select("api.at.keris.selectOptTxbPridCnt", dto)) == 0) {
			commonDao.insert("api.at.keris.insertOptTxbPrid", dto);

			kerisDto.setKafkaOptTxbId(dto.getOptTxbId());
			kerisDto.setKafkaOptTxbPrid(dto.getOptTxbPrid());
		}

		return dto;
	}
}
