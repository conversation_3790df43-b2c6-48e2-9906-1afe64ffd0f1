package com.aidt.api.tl.sbclrn.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 16:14:31
 * @modify date 2024-01-04 16:14:31
 * @desc TlSbcLrnAtvStat Dto 학습활동현황상세Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvStatDto {
       /** 학습맵노드ID */
       @Parameter(name="학습맵노드ID")
       private String lrmpNodId;

       /** 학습활동ID */
       @Parameter(name="학습활동ID")
       private String lrnAtvId;

       /** 콘텐츠코드 */
       @Parameter(name="콘텐츠코드")
       private String ctnCd;

       /** 학습활동명 */
       @Parameter(name="학습활동명")
       private String lrnAtvNm;

       /** 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
       @Parameter(name="콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지)")
       private String ctnTpCd;

       /** 학습상태코드 */
       @Parameter(name="학습상태코드")
       private String lrnStCd;

       /** 학습상태명 */
       @Parameter(name="학습상태명")
       private String lrnStNm;

       /** 학습시간(초단위) */
       @Parameter(name="학습시간(초단위)")
       private int lrnTmScnt;

       /** 학습일자 */
       @Parameter(name="학습일자")
       private String lrnDt;

       /** 학습단계ID */
       @Parameter(name = "학습단계ID")
       private String lrnStpId;

       /** 학습단계명 */
       @Parameter(name = "학습단계명")
       private String lrnStpNm;

       /** (평가용)평가ID */
       @Parameter(name = "(평가용)평가ID")
       private String evId;
       /** (평가용)총문제수 */
       @Parameter(name = "(평가용)총문제수")
       private String fnlQstCnt;
       /** (평가용)정답수 */
       @Parameter(name = "(평가용)정답수")
       private String cansCnt;
       /** (평가용)평가완료여부 */
       @Parameter(name = "(평가용)평가완료여부")
       private String evCmplYn;
       /** (평가용)평가정답여부목록 */
       @Parameter(name = "(평가용)평가정답여부목록")
       private List<String> cansList;
}
