<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.tk.tcr">
	
	<!-- AI회화 레슨 목록 -->
	<select id="selectkmmpList" parameterType="com.aidt.api.al.tk.dto.AlTkDto" resultType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkTcr-Mapper.xml - selectLetsTalkStuList */
		SELECT LLU.KMMP_NOD_ID									-- 대단원노드ID(레슨ID)
			 , LLU.KMMP_NOD_NM									-- 대단원노드명(레슨명)
			 , MAX(TK.LRN_ST_CD) AS LRN_ST_CD				-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
			 , MAX(LLU.TC_USE_YN) AS TC_USE_YN			-- 차시사용여부
			 , MAX(LLU.LCKN_YN) AS LCKN_YN				-- 잠금여부
			 , MAX(LLU.USE_YN) AS USE_YN					-- 사용여부
			 , MAX(TPC.ACP_ID) AS ACP_ID					-- 아키핀아이디(dialogueID)
		  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND LLU.USE_YN = 'Y'
		   AND TPC.DEL_YN = 'N'
		  LEFT JOIN LMS_LRM.CM_TK_MG TK
		    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND LLU.KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = #{lrnUsrId}
		 WHERE TPC.TW_WRT_DV_CD = 'T'							-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId}
		 GROUP BY LLU.KMMP_NOD_ID, LLU.KMMP_NOD_NM
		 ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), MIN(TPC.SRT_ORDN)
	</select>
	
	<!-- AI회화 토픽 목록 -->
	<select id="selectTpcList" parameterType="com.aidt.api.al.tk.dto.AlTkDto" resultType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkTcr-Mapper.xml - selectTpcList */
		SELECT LLU.KMMP_NOD_ID									-- 대단원노드ID(레슨ID)
			 , LLU.LCKN_YN										-- 잠금여부
			 , TPC.TPC_KMMP_NOD_ID								-- 토픽노드ID 
			 , TPC.TPC_KMMP_NOD_NM								-- 토픽노드명
			 , TPC.ACP_ID										-- 아키핀아이디(dialogueID)
			 , IFNULL(TK.LRN_ST_CD,'NL') AS LRN_ST_CD			-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
			 , RANK() OVER (PARTITION BY LLU.KMMP_NOD_ID ORDER BY TPC.TPC_KMMP_NOD_ID) AS NOD_RANK	-- 노드랭크
		  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND LLU.USE_YN = 'Y'
		   AND TPC.DEL_YN = 'N'
		  LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
		    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND TPC.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND TPC.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = #{lrnUsrId}						-- 파라미터: 학습사용자ID
		 WHERE TPC.TW_WRT_DV_CD = 'T'							-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId}						-- 파라미터: 운영교과서ID
		   AND LLU.KMMP_NOD_ID = #{kmmpNodId}
		 ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.SRT_ORDN, NOD_RANK
	</select>
	
	<!-- AI회화 나의 최근 학습 조회 -->
	<select id="selectTkRcnLrnTcrInfo" parameterType="com.aidt.api.al.tk.dto.AlTkDto" resultType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkStu-Mapper.xml - selectTkRcnLrnTcrInfo */
		SELECT LLU.KMMP_NOD_ID									-- 대단원노드ID(레슨ID)
			 , LLU.KMMP_NOD_NM									-- 대단원노드명(레슨명)
			 , TPC.TPC_KMMP_NOD_ID								-- 토픽노드ID 
			 , TPC.TPC_KMMP_NOD_NM								-- 토픽노드명
			 , TPC.ACP_ID										-- 아키핀아이디(dialogueID)
			 , IFNULL(TK.LRN_ST_CD,'NL') AS LRN_ST_CD			-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
			 , LLU.TC_USE_YN
			 , LLU.LCKN_YN
			 , LLU.USE_YN
		  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND LLU.USE_YN = 'Y'
		   AND TPC.DEL_YN = 'N'
		   AND LLU.LCKN_YN = 'N'
		  LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
		    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND TPC.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND TPC.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = #{lrnUsrId}						-- 파라미터: 학습사용자ID
		 WHERE TPC.TW_WRT_DV_CD = 'T'							-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId}						-- 파라미터: 운영교과서ID
		   AND LLU.KMMP_NOD_ID = (
								 SELECT TK.LLU_KMMP_NOD_ID
								   FROM LMS_LRM.CM_TK_MG TK
								  WHERE TK.OPT_TXB_ID = #{optTxbId}
								    AND TK.LRN_USR_ID = #{lrnUsrId}
								  ORDER BY TK.MDF_DTM DESC
								  LIMIT 1
								 )
		<if test='schlGrdCd != null and !schlGrdCd.equals("E")'>
		 ORDER BY TK.MDF_DTM DESC
		 LIMIT 1
		</if>
	</select>
	
	<!-- AI회화 상세 조회 -->
	<select id="selectTkTcrDetailInfo" parameterType="com.aidt.api.al.tk.dto.AlTkDto" resultType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkStu-Mapper.xml - selectTkTcrDetailInfo */
		SELECT LLU.KMMP_NOD_ID									-- 대단원노드ID(레슨ID)
			 , LLU.KMMP_NOD_NM									-- 대단원노드명(레슨명)
			 , TPC.TPC_KMMP_NOD_ID								-- 토픽노드ID 
			 , TPC.TPC_KMMP_NOD_NM								-- 토픽노드명
			 , TPC.ACP_ID										-- 아키핀아이디(dialogueID)
			 , IFNULL(TK.LRN_ST_CD,'NL') AS LRN_ST_CD			-- 학습상태(NL:학습하기 DL:이어하기 CL:복습하기)
		  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
		 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
		    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
		   AND LLU.DPTH = 1
		   AND LLU.DEL_YN = 'N'
		   AND LLU.USE_YN = 'Y'
		   AND TPC.DEL_YN = 'N'
		   AND LLU.LCKN_YN = 'N'
		  LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
		    ON LLU.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND TPC.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND TPC.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = #{lrnUsrId}						-- 파라미터: 학습사용자ID
		 WHERE TPC.TW_WRT_DV_CD = 'T'							-- 회화첨삭구분코드(T:talk/W:write)
		   AND LLU.OPT_TXB_ID = #{optTxbId}						-- 파라미터: 운영교과서ID
		   AND LLU.KMMP_NOD_ID = #{kmmpNodId}
	</select>
	
	<!-- AI 회화 이전, 다음 단원 조회 -->
	<select id="selectTkTcrPrevNextInfo" parameterType="com.aidt.api.al.tk.dto.AlTkDto" resultType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkTcr-Mapper.xml - selectTkTcrPrevNextInfo */
		SELECT T1.LLU_KMMP_NOD_ID AS KMMP_NOD_ID
			 , T1.KMMP_NOD_NM
			 , T1.TPC_KMMP_NOD_ID
			 , T1.TPC_KMMP_NOD_NM
			 , T1.ACP_ID										-- 아키핀아이디(dialogueID)
			 , T1.TC_USE_YN
		     , T1.LCKN_YN
		     , T1.USE_YN
			 , T1.OPT_TXB_ID
			 , T1.SRT_ORDN
			 , T1.PREV_USE_YN
			 , T1.PREV_LLU_KMMP_NOD_ID
			 , T1.PREV_TPC_KMMP_NOD_ID
			 , T1.NEXT_USE_YN
			 , T1.NEXT_LLU_KMMP_NOD_ID
			 , T1.NEXT_TPC_KMMP_NOD_ID
			 , TK.LRN_ST_CD	
		  FROM (
			  	SELECT TPC.LLU_KMMP_NOD_ID 					-- 대단원노드ID(레슨ID)
					 , LLU.KMMP_NOD_NM						-- 대단원노드명(레슨명)
					 , TPC.TPC_KMMP_NOD_ID 					-- 토픽노드ID 
					 , TPC.TPC_KMMP_NOD_NM 					-- 토픽노드명
					 , TPC.ACP_ID							-- 아키핀아이디(dialogueID)
					 , LLU.TC_USE_YN 						-- 차시사용여부
					 , LLU.LCKN_YN 							-- 잠금여부
					 , LLU.USE_YN 							-- 사용여부
					 , LLU.OPT_TXB_ID
					 , TPC.SRT_ORDN
					 , LAG(LLU.TC_USE_YN) OVER (ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.LLU_KMMP_NOD_ID , TPC.SRT_ORDN) AS PREV_USE_YN
					 , LAG(TPC.LLU_KMMP_NOD_ID) OVER (ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.LLU_KMMP_NOD_ID , TPC.SRT_ORDN) AS PREV_LLU_KMMP_NOD_ID
					 , LAG(TPC.TPC_KMMP_NOD_ID) OVER (ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.LLU_KMMP_NOD_ID , TPC.SRT_ORDN) AS PREV_TPC_KMMP_NOD_ID
					 , LEAD(LLU.TC_USE_YN) OVER (ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.LLU_KMMP_NOD_ID , TPC.SRT_ORDN) AS NEXT_USE_YN
					 , LEAD(TPC.LLU_KMMP_NOD_ID) OVER (ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.LLU_KMMP_NOD_ID , TPC.SRT_ORDN) AS NEXT_LLU_KMMP_NOD_ID
					 , LEAD(TPC.TPC_KMMP_NOD_ID) OVER (ORDER BY IFNULL(LLU.RCSTN_ORDN, LLU.ORGL_ORDN), TPC.LLU_KMMP_NOD_ID , TPC.SRT_ORDN) AS NEXT_TPC_KMMP_NOD_ID
				  FROM LMS_LRM.AI_KMMP_NOD_RCSTN LLU
				 INNER JOIN LMS_CMS.BC_TK_WRT_TPC TPC
				    ON LLU.KMMP_NOD_ID = TPC.LLU_KMMP_NOD_ID 
				   AND LLU.DPTH = 1
				   AND LLU.DEL_YN = 'N'
				   AND LLU.USE_YN = 'Y'
				   AND TPC.DEL_YN = 'N'
				   AND LLU.LCKN_YN = 'N'
				   <if test='schlGrdCd != null and schlGrdCd.equals("E")'>
				   AND TPC.SRT_ORDN = 1
				   </if>
				 WHERE TPC.TW_WRT_DV_CD = 'T'							-- 회화첨삭구분코드(T:TALK/W:WRITE)
				   AND LLU.OPT_TXB_ID = #{optTxbId} 					-- 파라미터: 운영교과서ID
			 ) T1
		  LEFT OUTER JOIN LMS_LRM.CM_TK_MG TK
			ON T1.OPT_TXB_ID = TK.OPT_TXB_ID 
		   AND T1.LLU_KMMP_NOD_ID = TK.LLU_KMMP_NOD_ID 
		   AND T1.TPC_KMMP_NOD_ID = TK.TPC_KMMP_NOD_ID 
		   AND TK.DEL_YN = 'N'
		   AND TK.LRN_USR_ID = #{lrnUsrId}								-- 파라미터: 학생ID
		 WHERE T1.LLU_KMMP_NOD_ID = #{kmmpNodId}							-- 파라미터: 레슨ID
		   <if test='schlGrdCd != null and !schlGrdCd.equals("E")'>
		   AND T1.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}							-- 파라미터: 토픽ID
		   </if>
	</select>
	
	<!-- AI회화 학습상태 확인 -->
	<select id="selectTkTcrLrnStCdCheck" parameterType="com.aidt.api.al.tk.dto.AlTkDto" resultType="int">
		/** AlTkStu-Mapper.xml - selectTkStuLrnStCdCheck */
		SELECT COALESCE(SUM(TK.LLU_KMMP_NOD_ID), 0) AS LLU_KMMP_NOD_ID
		  FROM LMS_LRM.CM_TK_MG TK
		 WHERE TK.OPT_TXB_ID = #{optTxbId}
		   AND TK.LLU_KMMP_NOD_ID = #{kmmpNodId}
		   AND TK.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
		   AND TK.LRN_USR_ID = #{lrnUsrId}
	</select>
	
	<!-- AI 회화 학습 상태 insert -->
	<insert id="insertTkTcrLrnStCd" parameterType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkStu-Mapper.xml - insertTkTcrLrnStCd */
		INSERT INTO LMS_LRM.CM_TK_MG(
			OPT_TXB_ID, LLU_KMMP_NOD_ID, TPC_KMMP_NOD_ID, LRN_USR_ID, LRN_ST_CD, DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			  #{optTxbId}
			, #{kmmpNodId}
			, #{tpcKmmpNodId}
			, #{lrnUsrId}
			, 'NL'
			, 'N'
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>
	
	<!-- AI 회화 학습 상태 update -->
	<update id="updateTkTcrLrnStCd" parameterType="com.aidt.api.al.tk.dto.AlTkDto">
		/** AlTkStu-Mapper.xml - updateTkTcrLrnStCd */
		UPDATE LMS_LRM.CM_TK_MG
		   SET MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE OPT_TXB_ID = #{optTxbId} 
		   AND LLU_KMMP_NOD_ID = #{kmmpNodId}
		   AND TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
		   AND LRN_USR_ID = #{lrnUsrId}
	</update>
</mapper>