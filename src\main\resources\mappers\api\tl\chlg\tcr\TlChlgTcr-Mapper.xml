<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.chlg.tcr">
    <select id="selectChlgListTcr" parameterType="Map" resultType="com.aidt.api.tl.chlg.dto.TlLrnChlgDto">
        select chlg.lrn_usr_id
        ,chlg.LRN_GOAL_DV_CD -- 목표 구분 (HR: 시간, EA: 문제)
        ,chlg.LRN_GOAL_QST_CNT -- 목표 문제
        ,chlg.LRN_GOAL_TM_SCNT -- 목표 시간
        ,chlg.LRN_ACV_QST_CNT -- 달성 문제
        ,chlg.LRN_ACV_TM_SCNT -- 달성 시간
        ,chlg.LRN_GOAL_ST_CD -- 챌린지 상태 (NL: 진행중, CL: 완료, QT: 중도포기)
        from cm_usr usr
        inner join tl_lrn_chlg chlg on chlg.lrn_usr_id = usr.USR_ID
        and chlg.LRN_STR_DT between #{startDate} and #{endDate}
        and chlg.LRN_GOAL_ST_CD != 'QT'
        and chlg.DEL_YN = 'N'
        where usr.CLA_ID = #{claId}
        and usr.USR_TP_CD = 'ST'
    </select>

</mapper>