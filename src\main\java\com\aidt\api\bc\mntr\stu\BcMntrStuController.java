package com.aidt.api.bc.mntr.stu;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-05-01 11:00:29
 * @modify 2024-05-01 11:00:29
 * @desc 학생_실시간 모니터링 Controller
 */

//@Slf4j
@Tag(name="[bc] 실시간 모니터링[BcMntrStu]", description="실시간 모니터링(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/mntr")
public class BcMntrStuController {

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private BcMntrStuService bcMntrTcrService;

	/**
     * 실시간 모니터링 현재학습콘텐츠 저장(학생)
     *
     * @param BcMntrDto
     * @return ResponseList<BcMntrDto>
     */
	@Tag(name="[bc] 실시간 모니터링 현재학습콘텐츠 저장", description="실시간 모니터링 현재학습콘텐츠를 저장한다.(학생)")
	@PostMapping(value = "/updateStuLrnCtt")
	public ResponseDto<Integer> updateStuLrnCtt(@Valid @RequestBody BcMntrDto bcMntrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcMntrDto.setOptTxbId(userDetails.getOptTxbId());
		bcMntrDto.setLrnUsrId(userDetails.getUsrId());
		bcMntrDto.setMdfrId(userDetails.getUsrId());
		return Response.ok(bcMntrTcrService.updateStuLrnCtt(bcMntrDto));
	}
	
	/**
     * 실시간 모니터링 로그아웃 저장(학생)
     *
     * @param BcMntrDto
     * @return ResponseList<BcMntrDto>
     */
	@Tag(name="[bc] 실시간 모니터링 로그아웃 저장", description="실시간 모니터링 로그아웃 저장한다.(학생)")
	@PostMapping(value = "/updateStuCurConn")
	public ResponseDto<Integer> updateStuCurConn(@RequestBody BcMntrDto bcMntrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcMntrDto.setOptTxbId(userDetails.getOptTxbId());
		bcMntrDto.setLrnUsrId(userDetails.getUsrId());
		bcMntrDto.setMdfrId(userDetails.getUsrId());
		return Response.ok(bcMntrTcrService.updateStuCurConn(bcMntrDto));
	}
	
}
