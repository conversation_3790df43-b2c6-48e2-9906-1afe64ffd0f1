package com.aidt.api.bc.home.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.home.dto.HomeSummaryAiCoachingRes;
import com.aidt.api.bc.home.service.TeacherHomeQueryService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "[bc] 교사 홈", description = "교사 홈")
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@RestController
@RequestMapping("/api/v1/bc/tcr/home")
public class BcTeacherHomeQueryController {

	private final TeacherHomeQueryService teacherHomeQueryService;

	@Operation(summary = "AI 맞춤 학습 단원 별 요약 정보 API", description = "##AI 맞춤 학습 단원 별 요약 정보 API")
	@GetMapping(value = "/summary/ai-coaching/en/{mid-node-id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<HomeSummaryAiCoachingRes> getHomeSummaryAiCoaching(@PathVariable(value = "mid-node-id") Integer midNodeId) {
		var homeSummaryAiCoaching = teacherHomeQueryService.getHomeSummaryAiCoaching(midNodeId);
		return Response.ok(HomeSummaryAiCoachingRes.of(homeSummaryAiCoaching));
	}

}
