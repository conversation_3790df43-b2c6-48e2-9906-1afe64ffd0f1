package com.aidt.api.ea.ev.tcr;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.evcom.dto.EaEvComQtmInfoDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvEvIdReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvRptUsrsQtmResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvSaveReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvUpdateTxmSetmDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 평가 관리 - 교사", description="평가 관리 - 교사")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/ev/tcr")
public class EaEvTcrController {

	@Autowired
	EaEvTcrService eaEvTcrService;
	@Autowired
	private JwtProvider jwtProvider;
	
	
	/**
	 * 교사 - 평가 목록 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvMainResDto>
	 */
	@Tag(name="[ea] 평가 목록 조회", description="평가 목록 조회")
	@PostMapping(value = "/selectEvList")
	public ResponseDto<List<Map<String, Object>>> selectEvList(@Valid @RequestBody EaEvMainReqDto evReqDto) {
		log.debug("Entrance selectEvList");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvList(evReqDto));
	}

	/**
	 * 교사 - 평가 편집 목록 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvMainResDto>
	 */
	@Tag(name="[ea] 평가 편집 목록 조회", description="평가 편집 목록 조회")
	@PostMapping(value = "/selectEvEditList")
	public ResponseDto<List<EaEvMainResDto>> selectEvEditList(@Valid @RequestBody EaEvMainReqDto evReqDto) {
		log.debug("Entrance selectEvEditList");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvEditList(evReqDto));
	}
	
	
	/**
	 * 교사 - 평가 추가 평가기본/문항리스트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 평가추가 문항리스트 조회", description="평가추가 문항리스트 조회")
	@PostMapping(value = "/selectEvAddQtmList")
	public ResponseDto<EaEvMainResDto> selectEvAddQtmList(@Valid @RequestBody EaEvQtmIdReqDto evReqDto) {
		log.debug("Entrance selectEvAddQtmList");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID


		return Response.ok(eaEvTcrService.selectEvAddQtmList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 추가 유사 문항리스트 조회 요청
	 *
	 * @param evReqDto
	 * @return List<EaEvQtmInfoDto>
	 */
	@Tag(name="[ea] 평가추가 유사문항 리스트 조회", description="평가추가 유사문항 리스트 조회")
	@PostMapping(value = "/selectSmlrQtmList")
	public ResponseDto<List<EaEvComQtmInfoDto>> selectSmlrQtmList(@Valid @RequestBody EaEvQtmIdReqDto evReqDto) {
		log.debug("Entrance selectSmlrQtmList");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectSmlrQtmList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 추가/수정 - 학습수준별 학생정보 조회
	 *
	 * @param evReqDto
	 * @return ResponseDto<List<Map<String, Object>>>
	 */
	@Tag(name="[ea] 학습수준별 학생정보 조회", description="학습수준별 학생정보 조회")
	@PostMapping(value = "/selectEvLrnLvStuList")
	public ResponseDto<List<Map<String, Object>>> selectEvLrnLvStuList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvLrnLvStuList");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvLrnLvStuList(evReqDto));
	}
	
	
	/**
	 * 교사 - 평가정보 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 평가 정보 조회", description="평가 정보 조회")
	@PostMapping(value = "/selectEvInfo")
	public ResponseDto<EaEvMainResDto> selectEvInfo(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvInfo");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvInfo(evReqDto));
	}
	
	/**
	 * 교사 - 평가 수정 - 평가정보/평가난이도정보/평가시험범위정보/평가문항정보 조회
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 평가수정 평가정보 조회", description="평가수정 평가정보 조회")
	@PostMapping(value = "/selectEvUpdateInfo")
	public ResponseDto<EaEvMainResDto> selectEvUpdateInfo(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvUpdateInfo");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvUpdateInfo(evReqDto));
	}
	
	
	/**
	 * 교사 - 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 평가 리포트 조회", description="평가 리포트 조회")
	@PostMapping(value = "/selectEvRptList")
	public ResponseDto<EaEvMainResDto> selectEvRptList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptList");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvRptList(evReqDto));
	}

	/**
	 * 교사 - 평가 리포트 조회 NEW 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 수학 평가리포트 조회 NEW", description="수학 평가리포트 조회 NEW")
	@PostMapping(value = "/selectEvRptNewList")
	public ResponseDto<Map<String, Object>> selectEvRptNewList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptNewList");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvRptNewList(evReqDto));
	}

	/**
	 * 교사 - 수학 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 수학 평가리포트 조회", description="수학 평가리포트 조회")
	@PostMapping(value = "/selectEvRptMaList")
	public ResponseDto<Map<String, Object>> selectEvRptMaList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptMaList");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvRptMaList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 리포트 > 단원평가 > 단원성취도 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<getSecurityUserDetail>
	 */
	@Tag(name="[ea] 단원평가 단원성취도 조회 요청", description="단원평가 단원성취도 조회 요청")
	@PostMapping(value = "/selectEvRptUgAchdList")
	public ResponseDto<List<Map<String, Object>>> selectEvRptUgAchdList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptUgAchdList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptUgAchdList(evReqDto));
	}
	
	
	/**
	 * 교사 - 영어 평가 리포트 조회 요청
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	@Tag(name="[ea] 영어 평가리포트 조회", description="영어 평가리포트 조회")
	@PostMapping(value = "/selectEvRptEnList")
	public ResponseDto<Map<String, Object>> selectEvRptEnList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptEnList");

		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		
		return Response.ok(eaEvTcrService.selectEvRptEnList(evReqDto));
	}
	
	
	/**
	 * 교사 - 평가 리포트 - 우리반 강점/약점 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvRptUsrsQtmResDto>
	 */
	@Tag(name="[ea] 우리반 강점/약점 조회", description="우리반 강점/약점 조회")
	@PostMapping(value = "/selectEvRptStpnWkpnList")
	public ResponseDto<List<EaEvRptUsrsQtmResDto>> selectEvRptStpnWkpnList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptStpnWkpnList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptStpnWkpnList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 리포트 - 학생별현황 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvRptUsrsQtmResDto>
	 */
	@Tag(name="[ea] 학생별현황 조회", description="학생별현황 조회")
	@PostMapping(value = "/selectEvRptUsrRptList")
	public ResponseDto<List<EaEvRptUsrsQtmResDto>> selectEvRptUsrRptList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptUsrRptList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptUsrRptList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 리포트 - 학생별현황 조회 요청
	 *
	 * @param List<EaEvEvIdReqDto>
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 평가결과 초기화", description="학생별현황 평가 초기화")
	@PostMapping(value = "/updateEvRs")
	public ResponseDto<Map<String,String>> updateEvRs(@Valid @RequestBody List<EaEvEvIdReqDto> evReqDtoList) {
		log.debug("Entrance updateEvRs");
		return Response.ok(eaEvTcrService.updateEvRs(evReqDtoList));
	}
	
	
	/**
	 * 교사 - 평가 리포트 - 우리반 오답 BEST 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvRptUsrsQtmResDto>
	 */
	@Tag(name="[ea] 우리반 오답 BEST 조회", description="우리반 오답 BEST 조회")
	@PostMapping(value = "/selectEvRptIansBestList")
	public ResponseDto<List<EaEvRptUsrsQtmResDto>> selectEvRptIansBestList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptIansBestList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptIansBestList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 리포트 - 우리반 오답 BEST 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name="[ea] 우리반 오답 BEST NEW 조회", description="우리반 오답 BEST NEW 조회")
	@PostMapping(value = "/selectEvRptIansBestNewList")
	public ResponseDto<List<Map<String, Object>>> selectEvRptIansBestNewList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptIansBestNewList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptIansBestNewList(evReqDto));
	}
	
	
	/**
	 * 교사 - 평가 리포트 - 학생별 정오답현황 조회 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvRptUsrsQtmResDto>
	 */
	@Tag(name="[ea] 학생별 정오답현황 조회", description="학생별 정오답현황 조회")
	@PostMapping(value = "/selectEvRptOXList")
	public ResponseDto<List<EaEvRptUsrsQtmResDto>> selectEvRptOXList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptOXList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptOXList(evReqDto));
	}

	
	/**
	 * 교사 - 평가 리포트 - 학생별 정오답현황 조회 NEW 요청
	 *
	 * @param evReqDto
	 * @return ResponseList<EaEvRptUsrsQtmResDto>
	 */
	@Tag(name="[ea] 학생별 정오답현황 NEW 조회", description="학생별 정오답현황 NEW 조회")
	@PostMapping(value = "/selectEvRptOxNewList")
	public ResponseDto<Map<String, Object>> selectEvRptOxNewList(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance selectEvRptOxNewList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvRptOxNewList(evReqDto));
	}

	/**
	 * 교사 - 학습창 연동 - 학습창 진입하여 문제풀이 시 학생들의 모니터링 정보 조회
	 *
	 * @param evReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name="[ea] 교사 학습창 학생들 모니터링 정보 조회", description="교사 학습창 학생들 모니터링 정보 조회")
	@PostMapping(value = "/selectEvTcrLwStusSmtAnwList")
	public ResponseDto<List<Map<String, Object>>> selectEvTcrLwStusSmtAnwList(@Valid @RequestBody EaEvQtmIdReqDto evReqDto) {
		log.debug("Entrance selectEvTcrLwStusSmtAnwList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID

		return Response.ok(eaEvTcrService.selectEvTcrLwStusSmtAnwList(evReqDto));
	}
	
	/**
	 * 교사 - 평가 추가, 수정 시 다른 학급 정보 조회
	 *
	 * @param evReqDto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name="[ea] 교사 평가 추가, 수정 시 다른 학급 정보 조회", description="교사 평가 추가, 수정 시 다른 학급 정보 조회")
	@PostMapping(value = "/selectEvTcrMyClsInfoList")
	public ResponseDto<List<Map<String, Object>>> selectEvTcrMyClsInfoList(@Valid @RequestBody EaEvMainReqDto evReqDto) {
		log.debug("Entrance selectEvTcrMyClsInfoList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		evReqDto.setTcrUsrId(userDetails.getKerisUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 운영교과서ID
		evReqDto.setTxbId(userDetails.getTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		

		return Response.ok(eaEvTcrService.selectEvTcrMyClsInfoList(evReqDto));
	}

	
	/**
	 * 교사 - 평가 정보 등록 (교사평가, DIY평가)
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 평가 정보 등록 요청", description="평가 정보 등록 요청(교사평가, DIY평가)")
	@PostMapping(value = "/insertEv",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Map<String, Object>> insertEv(@Valid @RequestBody EaEvSaveReqDto evReqDto) {
		log.debug("Entrance insertEv");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setDbId(userDetails.getTxbId()); // TODO DBID == 교과서ID

		return Response.ok(eaEvTcrService.insertEv(evReqDto));
	}	
	
	/**
	 * 교사 - 평가 응시설정 수정 요청
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 평가 응시설정 수정 요청", description="평가 응시설정 수정 요청")
	@PostMapping(value = "/updateEvTxmSetm",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateEvTxmSetm(@Valid @RequestBody EaEvUpdateTxmSetmDto evReqDto) {
		log.debug("Entrance updateEvTxmSetm");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		return Response.ok(eaEvTcrService.updateEvTxmSetm(evReqDto));
	}
	
	/**
	 * 교사 - 평가 정보 업데이트 요청 (교사평가)
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 평가 정보 업데이트 요청", description="평가 정보 업데이트 요청(교사평가)")
	@PostMapping(value = "/updateEv",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> updateEv(@Valid @RequestBody EaEvSaveReqDto evReqDto) {
		log.debug("Entrance updateEv");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setDbId(userDetails.getTxbId()); // TODO DBID == 교과서ID
		return Response.ok(eaEvTcrService.updateEv(evReqDto));
	}	
	
	
	/**
	 * 교사 - 평가 정보 삭제 요청 (교사평가)
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 평가 정보 삭제 요청", description="평가 정보 삭제 요청(교사평가)")
	@PostMapping(value = "/deleteEv")
	public ResponseDto<Map<String,String>> deleteEv(@Valid @RequestBody EaEvEvIdReqDto evReqDto) {
		log.debug("Entrance deleteEv");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		return Response.ok(eaEvTcrService.deleteEv(evReqDto));
	}
	
	
	/**
	 * 교사 - 평가 - 다른학급에저장
	 *
	 * @param evReqDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[ea] 평가 정보 다른학급에저장 요청", description="평가 정보 다른학급에저장 요청")
	@PostMapping(value = "/saveOtherClaEv",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<List<Map<String, Object>>> saveOtherClaEv(@Valid @RequestBody EaEvSaveReqDto evReqDto) {
		log.debug("Entrance saveOtherClaEv");
		
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setTxbId(userDetails.getTxbId()); // TODO 교과서ID
		evReqDto.setClaId(userDetails.getClaId()); // TODO 학교반ID
		evReqDto.setDbId(userDetails.getTxbId()); // TODO DBID == 교과서ID
		return Response.ok(eaEvTcrService.saveOtherClaEv(evReqDto));
	}	
	
	
}
