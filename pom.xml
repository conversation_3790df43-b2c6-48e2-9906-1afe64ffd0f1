<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<properties>
		<project.meta.group>aidt</project.meta.group>
		<project.meta.service>aidt-api-lm</project.meta.service>
		<project.meta.basePathPrefix>/app/</project.meta.basePathPrefix>

		<java.version>11</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    	<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    	<maven.compiler.fullReCompile>false</maven.compiler.fullReCompile>
		<maven.test.skip>true</maven.test.skip>

		<project.current.build.name>${project.artifactId}</project.current.build.name>
		<project.current.build.revision/>
		<project.current.build.branch/>
		<project.current.build.timestamp/>
		<baseVersion>[1.0.0,]</baseVersion>
	</properties>

	<parent>
		<groupId>com.chunjae.aidt.boot</groupId>
		<artifactId>aidt-base-parent</artifactId>
		<version>x.x.x-SNAPSHOT</version>
	</parent>

	<groupId>com.chunjae.aidt</groupId>
	<artifactId>aidt-api-lm</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<name>${project.artifactId}</name>
	<packaging>jar</packaging>
	<description>천재교육 AIDT(AI디지털교과서) 학습관리 API</description>

	<scm>
	    <connection>scm:git:${aidt.git.url}/api-lm.git</connection>
	</scm>

  	<profiles>
		<profile>
			<id>local</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<deployTarget>local</deployTarget>
				<deployProfile>profile.local</deployProfile>
				<project.meta.basePathPrefix>/aidt.</project.meta.basePathPrefix>
			</properties>
		</profile>
		<profile>
			<id>dev</id>
			<properties>
				<deployTarget>dev</deployTarget>
				<deployProfile>profile.dev</deployProfile>
			</properties>
		</profile>
		<profile>
			<id>stg</id>
			<properties>
				<deployTarget>stg</deployTarget>
				<deployProfile>profile.stg</deployProfile>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<deployTarget>prod</deployTarget>
				<deployProfile>profile.prod</deployProfile>
			</properties>
		</profile>
	</profiles>

	<build>
		<finalName>${project.current.build.name}-${deployTarget}-${project.version}</finalName>

		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory>
			</resource>
			<resource>
				<directory>src/main/resources/profiles/${deployProfile}</directory>
				<includes>
					<include>**/*</include>
				</includes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
				<configuration>
					<!-- <shortRevisionLength>5</shortRevisionLength> -->
					<revisionOnScmFailure>noScm</revisionOnScmFailure>
					<buildNumberPropertyName>current.build.revision</buildNumberPropertyName>
					<scmBranchPropertyName>current.build.branch</scmBranchPropertyName>
					<timestampPropertyName>current.build.timestamp</timestampPropertyName>
					<timestampFormat>yyyy.MM.dd HH:mm:ss z</timestampFormat>
				</configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<Implementation-Revision>${project.current.build.revision}</Implementation-Revision>
							<Implementation-ScmBranch>${project.current.build.branch}</Implementation-ScmBranch>
							<Implementation-BuildTime>${project.current.build.timestamp}</Implementation-BuildTime>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

		</plugins>
	</build>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.chunjae.aidt</groupId>
				<artifactId>aidt-base-online</artifactId>
				<version>${baseVersion}</version>
			</dependency>
			<dependency>
				<groupId>com.chunjae.aidt</groupId>
				<artifactId>aidt-base</artifactId>
				<version>${baseVersion}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>

		<!-- devtools : 스프링 부트 개발 중 코드 변경 시, 자동 리로드 지원, 사용을 하지 않을 경우 주석처리 함 -->
		<!--
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
		</dependency>
		-->

		<dependency>
			<groupId>com.chunjae.aidt</groupId>
			<artifactId>aidt-base-online</artifactId>
		</dependency>

	</dependencies>

</project>
