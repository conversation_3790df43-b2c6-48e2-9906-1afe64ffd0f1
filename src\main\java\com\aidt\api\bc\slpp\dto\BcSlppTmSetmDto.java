package com.aidt.api.bc.slpp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcSlppTmSetmDto {
    @Parameter(name="운영교과서Id")
    private String optTxbId;
    @Parameter(name="월요일여부")
    private String monYn;
    @Parameter(name="화요일여부")
    private String tueYn;
    @Parameter(name="수요일여부")
    private String wedYn;
    @Parameter(name="목요일여부")
    private String thuYn;
    @Parameter(name="금요일여부")
    private String friYn;
    @Parameter(name="토요일여부")
    private String satYn;
    @Parameter(name="일요일여부")
    private String sunYn;
    @Parameter(name="쪽지가능시작시분")
    private String slppAbleStrHm;
    @Parameter(name="쪽지가능종료시분")
    private String slppAbleEndHm;
    @Parameter(name="공휴일여부")
    private String lglhYn;
    @Parameter(name="수정자아이디")
    private String mdfrId;
    @Parameter(name="대화사용여부")
    private String useYn;
}
