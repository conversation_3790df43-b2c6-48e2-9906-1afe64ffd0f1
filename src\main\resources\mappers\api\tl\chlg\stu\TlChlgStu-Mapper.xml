<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.chlg.stu">
    
    <!-- 챌린지 목록 조회 -->
    <select id="selectChlgList" resultType="com.aidt.api.tl.chlg.dto.TlLrnChlgDto">
        SELECT
             A.LRN_GOAL_NO                        /* 학습목표번호 */
            ,DATE_FORMAT(A.LRN_STR_DT, '%Y.%m.%d') AS LRN_STR_DT  /* 학습시작일자 */
            ,DATE_FORMAT(A.LRN_END_DT, '%Y.%m.%d') AS LRN_END_DT  /* 학습종료일자 */
            ,A.LRN_GOAL_DV_CD                     /* 학습목표구분코드 */
            ,A.LRN_GOAL_QST_CNT                   /* 학습목표문제수 */
            ,A.LRN_GOAL_TM_SCNT                   /* 학습목표시간(초) */
            ,A.LRN_GOAL_ST_CD                     /* 학습목표상태코드 */
            ,A.LRN_ACV_QST_CNT					/* 문제 풀이 수 */
            ,A.LRN_ACV_TM_SCNT                 /* 학습 시간 (초) */
            ,A.DEL_YN                             /* 삭제여부 */
            ,C.CM_CD_NM
        FROM LMS_LRM.TL_LRN_CHLG A           /* TL_학습챌린지 */
        LEFT JOIN LMS_LRM.CM_CM_CD C         /* CM_공통코드 */
            ON C.URNK_CM_CD = 'LRN_GOAL_ST_CD'
            AND A.LRN_GOAL_ST_CD = C.CM_CD
            AND C.LMS_USE_YN = 'Y'
            AND C.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.LRN_USR_ID = #{lrnUsrId}
        AND A.DEL_YN = 'N'
        AND A.LRN_GOAL_ST_CD != 'QT'
        ORDER BY A.LRN_GOAL_NO DESC

        /* 교과학습 강성희 TlChlgStu-Mapper.xml - selectChlgList */
    </select>
    <!-- 학습챌린지설정 등록처리-->
    <insert id="insertLrnChlg"  parameterType="com.aidt.api.tl.chlg.dto.TlLrnChlgDto">
        INSERT INTO LMS_LRM.TL_LRN_CHLG     /* TL_학습챌린지 */
            (OPT_TXB_ID
            ,LRN_USR_ID
            ,LRN_GOAL_NO
            ,LRN_STR_DT
            ,LRN_END_DT
            ,LRN_GOAL_DV_CD
            ,LRN_GOAL_QST_CNT
            ,LRN_GOAL_TM_SCNT
            ,LRN_GOAL_ST_CD
            ,DEL_YN
            ,CRTR_ID
            ,CRT_DTM
            ,MDFR_ID
            ,MDF_DTM
            ,DB_ID) 
        SELECT 
            #{optTxbId}
            ,#{lrnUsrId}
            ,IFNULL(MAX(LRN_GOAL_NO), 0) + 1
            ,#{lrnStrDt}
            ,#{lrnEndDt}
            ,#{lrnGoalDvCd}
            ,#{lrnGoalQstCnt}
            ,#{lrnGoalTmScnt}
            ,'NL'
            ,'N'
            ,#{lrnUsrId}
            ,NOW()
            ,#{lrnUsrId}
            ,NOW()
            , #{dbId}
        FROM LMS_LRM.TL_LRN_CHLG
        WHERE OPT_TXB_ID = #{optTxbId}
        AND LRN_USR_ID = #{lrnUsrId}
        AND DEL_YN = 'N' 

        /* 교과학습 강성희 TlChlgStu-Mapper.xml - insertLrnChlg */
    </insert>
    
    <!-- 챌린지 목표 구분 조회 -->
    <select id="selectGoalDvCd" resultType="com.aidt.api.tl.chlg.dto.TlLrnChlgDto">
        SELECT
           	A.LRN_GOAL_DV_CD                 /* 학습목표구분코드(HR:시간, EA:문제수) */
           	, A.LRN_GOAL_NO
           	,IFNULL(A.LRN_GOAL_QST_CNT, '')	AS LRN_GOAL_QST_CNT
           	,IFNULL(A.LRN_GOAL_TM_SCNT, '') AS LRN_GOAL_TM_SCNT
           	,DATE_FORMAT(A.LRN_STR_DT, '%Y.%m.%d') AS LRN_STR_DT  /* 학습시작일자 */
           	,DATE_FORMAT(A.LRN_END_DT, '%Y.%m.%d') AS LRN_END_DT  /* 학습종료일자 */
           	,A.LRN_GOAL_ST_CD /* 챌린지 상태 코드 */
        FROM LMS_LRM.TL_LRN_CHLG A           /* TL_학습챌린지 */
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.LRN_USR_ID = #{lrnUsrId}
        AND A.DEL_YN = 'N'
        ORDER BY A.LRN_GOAL_NO DESC
        LIMIT 1

        /* 교과학습 강성희 TlChlgStu-Mapper.xml - selectGoalDvCd */
    </select>
    <!-- 현재 챌린지 목표 구분 조회 -->
    <select id="selectGoalDvCdNow" resultType="com.aidt.api.tl.chlg.dto.TlLrnChlgDto">
    
        SELECT
    	A.LRN_GOAL_DV_CD                 /* 학습목표구분코드(HR:시간, EA:문제수) */
           	,A.LRN_GOAL_NO
           	,IFNULL(A.LRN_GOAL_QST_CNT, '')	AS LRN_GOAL_QST_CNT
           	,IFNULL(A.LRN_GOAL_TM_SCNT, '') AS LRN_GOAL_TM_SCNT
           	,DATE_FORMAT(A.LRN_STR_DT, '%Y.%m.%d') AS LRN_STR_DT  /* 학습시작일자 */
           	,DATE_FORMAT(A.LRN_END_DT, '%Y.%m.%d') AS LRN_END_DT  /* 학습종료일자 */
            ,A.LRN_GOAL_ST_CD /* 챌린지 상태 코드 */
           	,A.CRT_DTM /* 생성일시 */
        FROM LMS_LRM.TL_LRN_CHLG A           /* TL_학습챌린지 */
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.LRN_USR_ID = #{lrnUsrId}
        	<![CDATA[
        	AND DATE(A.LRN_END_DT)>= CURDATE() AND  DATE(A.LRN_STR_DT)<=CURDATE() 
           	]]>
        order by A.LRN_GOAL_NO  DESC 
        LIMIT 1
        /* 교과학습 노성용 TlChlgStu-Mapper.xml - selectGoalDvCdNow */
    </select>
    
     <!-- 챌린지 달성 시간, 문제 조회 -->
    <select id="selectAcvClg" resultType="com.aidt.api.tl.chlg.dto.TlLrnChlgDto" parameterType="Map">
        SELECT
            IFNULL(SUM(U.evTmScnt),0) AS LRN_ACV_TM_SCNT,-- 학습 시간 초
            IFNULL(SUM(U.qstCnt), 0) AS LRN_ACV_QST_CNT -- 문제 풀이 수
        FROM (
            /* 교과평가, AI평가, 교사평가 */
            SELECT
                0 AS evTmScnt,
                COUNT(*) as qstCnt
            FROM LMS_LRM.EA_EV_QTM_ANW EEQA
            INNER JOIN LMS_LRM.EA_EV_RS EER ON (EEQA.EV_ID = EER.EV_ID AND EER.USR_ID = EEQA.USR_ID)
            INNER JOIN LMS_LRM.EA_EV EE ON (EE.EV_ID = EEQA.EV_ID)
            WHERE EE.OPT_TXB_ID = #{optTxbId}
            AND EE.USE_YN = 'Y'
            AND EE.DEL_YN = 'N'
            AND EER.smt_dtm <![CDATA[>=]]> CAST(#{startDt} AS DATETIME)
            AND DATE(EER.smt_dtm) <![CDATA[<=]]> DATE(STR_TO_DATE(#{endDt}, '%Y%m%d'))
            AND EEQA.SMT_ANW_VL IS NOT NULL AND EEQA.SMT_ANW_VL != ''
            AND EEQA.USR_ID = #{usrId}

            UNION ALL

            /* 교과학습 */
            SELECT
                SUM(C.evTmScnt) AS evTmScnt,
                0 AS qstCnt
            FROM (
                SELECT
                    DATE_FORMAT(CLT.MDF_DTM, '%Y%m%d') AS stdDate,
                    (
                        CLT.TXB_LRN_TM_SCNT +
                        CLT.AI_LRN_TM_SCNT +
                        CLT.SP_LRN_TM_SCNT +
                        CLT.EV_LRN_TM_SCNT +
                        CLT.AI_EV_LRN_TM_SCNT +
                        CLT.AI_WRTNG_LRN_TM +
                        CLT.AI_RDNG_LRN_TM
                    ) AS evTmScnt,
                    '0' AS qstCnt
                FROM LMS_LRM.CM_LRN_TM CLT
                WHERE CLT.OPT_TXB_ID = #{optTxbId}
                AND CLT.USR_ID = #{usrId}
                AND CLT.MDF_DTM <![CDATA[>=]]> #{startDt}
                AND DATE(CLT.MDF_DTM) <![CDATA[<=]]> DATE(STR_TO_DATE(#{endDt}, '%Y%m%d'))
            ) C
        ) U

        /* 교과학습 조영일 TlChlgStu-Mapper.xml - selectAcvClg */
    </select>
    
    <!-- 챌린지 목표 업데이트 -->
    <update id="updateLrnChlg" parameterType="Map">
        UPDATE LMS_LRM.TL_LRN_CHLG 
		
		SET 
             <choose>
	            <when test='goalDvCd == "HR"'>
					LRN_ACV_TM_SCNT = #{lrnAcvTmScnt}
	            </when>
	            <when test='goalDvCd == "EA"'>
					LRN_ACV_QST_CNT = #{lrnAcvQstCnt}
	            </when>
			</choose>
			<if test='chlgCompl == "CL"'>
	            	,LRN_GOAL_ST_CD = 'CL'
	         </if>
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRN_USR_ID = #{usrId}
		AND LRN_GOAL_NO = #{lrnGoalNo}
		
        /* 교과학습 강성희 TlChlgStu-Mapper.xml - updateLrnChlg */
    </update>
    
    <!-- 챌린지 중도 포기 -->
    <update id="updateLrnChlgQt" parameterType="Map">
	    UPDATE LMS_LRM.TL_LRN_CHLG C
	    
		SET LRN_GOAL_ST_CD = 'QT'
		
		WHERE C.OPT_TXB_ID = #{optTxbId}
		AND C.LRN_USR_ID = #{lrnUsrId}
		AND C.LRN_GOAL_NO = #{lrnGoalNo}
		AND C.DEL_YN = 'N'
		
		/* 교과학습 조영일 TlChlgStu-Mapper.xml - updateLrnChlgQt */
    </update>

</mapper>