package com.aidt.api.common.helper;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.util.ConstantsExt;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtHelper {

	private final JwtProvider jwtProvider;

	public CommonUserDetail getCommonUserDetail() {
		return jwtProvider.getCommonUserDetail();
	}

	public String getAccessToken() {
		ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
		if (attributes != null) {
			HttpServletRequest sra = attributes.getRequest();
			return jwtProvider.resolveToken(sra, ConstantsExt.accessTokenHeader);
		}
		return null;
	}
}
