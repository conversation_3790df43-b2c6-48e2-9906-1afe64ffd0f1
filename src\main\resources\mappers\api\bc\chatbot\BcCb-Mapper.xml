<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.chatbot">
	
	<resultMap id="autrMap" type="Map">
		<result property="autrCd" column="autr_cd"/>
		<result property="autrNm" column="autr_nm"/>
		<result property="txbSubUrl" column="txb_sub_url"/>
 	</resultMap>
	
	<select id="selectAiKeyWord" parameterType="com.aidt.api.bc.chatbot.dto.BcCbSrhAllDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbKeyWordDto">
		SELECT 	
				  D1.KMMP_NOD_ID AS LRMP_NOD_ID1, D1.KMMP_NOD_NM AS LRMP_NOD_NM1
				, D2.KMMP_NOD_ID AS LRMP_NOD_ID2, D2.KMMP_NOD_NM AS LRMP_NOD_NM2
				, D3.KMMP_NOD_ID AS LRMP_NOD_ID3, D3.KMMP_NOD_NM AS LRMP_NOD_NM3
				, D4.KMMP_NOD_ID AS LRMP_NOD_ID4, D4.KMMP_NOD_NM AS LRMP_NOD_NM4
				, A.KMMP_NOD_ID AS LRMP_NOD_ID5, A.KMMP_NOD_NM AS LRMP_NOD_NM5
				, A.AI_LRN_ATV_ID AS LRN_ATV_ID, A.LRN_ATV_NM, A.CTN_TP_CD, A.CTN_TP_NM, A.KWD_NM
				, A.AI_CTN_META_DATA_ID CTN_META_DATA_ID, A.CDN_PTH_NM
		FROM 
		(
			SELECT 	A.KMMP_NOD_ID, A.KMMP_NOD_NM, A.URNK_KMMP_NOD_ID, 
					AC.AI_LRN_ATV_ID, AC.LRN_ATV_NM, AC.CTN_TP_CD, 
					CM.CM_CD_NM CTN_TP_NM, A.RCSTN_ORDN, AC.SRT_ORDN, A.OPT_TXB_ID, 
					MD.KWD_NM, MD.AI_CTN_META_DATA_ID, MD.CDN_PTH_NM
			FROM 	LMS_LRM.AI_KMMP_NOD_RCSTN A
			INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN AC
				ON A.KMMP_NOD_ID = AC.KMMP_NOD_ID
				AND A.DPTH = 5
				AND A.LCKN_YN 		= 'N'
				AND A.DEL_YN 		= 'N'
			INNER JOIN LMS_CMS.BC_AI_CTN_META_DATA MD
				ON AC.AI_LRN_ATV_ID = MD.AI_LRN_ATV_ID
				AND (
					<foreach item="keyword" index="index" collection="cbSrhList" separator=" OR ">
						<!-- MD.KWD_NM LIKE CONCAT('%', #{keyword}, '%') -->
						FIND_IN_SET(REPLACE(#{keyword}, ' ', ''), REPLACE(MD.KWD_NM, ' ', '')) > 0
					</foreach>
					)
			LEFT JOIN LMS_LRM.CM_CM_CD CM
				ON CM.URNK_CM_CD = 'CTN_TP_CD'
				AND CM.CM_CD = AC.CTN_TP_CD
			WHERE A.OPT_TXB_ID = #{optTxbId}
		) A
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN D4
			ON A.OPT_TXB_ID = D4.OPT_TXB_ID
			AND A.URNK_KMMP_NOD_ID = D4.KMMP_NOD_ID
			AND D4.DPTH = 4
			AND D4.LCKN_YN 		= 'N'
			AND D4.DEL_YN 		= 'N'
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN D3
			ON D4.OPT_TXB_ID = D3.OPT_TXB_ID
			AND D4.URNK_KMMP_NOD_ID = D3.KMMP_NOD_ID
			AND D3.DPTH = 3
			AND D3.LCKN_YN 		= 'N'
			AND D3.DEL_YN 		= 'N'
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN D2
			ON D3.OPT_TXB_ID = D2.OPT_TXB_ID
			AND D3.URNK_KMMP_NOD_ID = D2.KMMP_NOD_ID
			AND D2.DPTH = 2
			AND D2.LCKN_YN 		= 'N'
			AND D2.DEL_YN 		= 'N'
		INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN D1
			ON D2.OPT_TXB_ID = D1.OPT_TXB_ID
			AND D2.URNK_KMMP_NOD_ID = D1.KMMP_NOD_ID
			AND D1.DPTH = 1
			AND D1.LCKN_YN 		= 'N'
			AND D1.DEL_YN 		= 'N'
		ORDER BY D1.RCSTN_ORDN, D2.RCSTN_ORDN, D3.RCSTN_ORDN, D4.RCSTN_ORDN, A.RCSTN_ORDN, A.SRT_ORDN
		/** BcCb-Mapper-Mapper.xml - selectAiKeyWord */
	</select>

	<select id="selectKeyWord" parameterType="com.aidt.api.bc.chatbot.dto.BcCbSrhAllDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbKeyWordDto">
		SELECT 	 B.LRN_ATV_ID						/* 학습활동ID */
				,B.LRN_ATV_NM    					/* 학습활동명 */
				,F.LRMP_NOD_NM    AS LRMP_NOD_NM1  	/* 지식맵노드명(대단원) */
				,E.LRMP_NOD_NM    AS LRMP_NOD_NM2  	/* 지식맵노드명(중단원) */
				,D.LRMP_NOD_NM    AS LRMP_NOD_NM3  	/* 지식맵노드명(소단원) */
				,C.LRMP_NOD_NM    AS LRMP_NOD_NM4  	/* 지식맵노드명(차시) */
				,F.LRMP_NOD_ID    AS LRMP_NOD_ID1  	/* 지식맵노드명(대단원) */
				,E.LRMP_NOD_ID    AS LRMP_NOD_ID2  	/* 지식맵노드명(중단원) */
				,D.LRMP_NOD_ID    AS LRMP_NOD_ID3  	/* 지식맵노드명(소단원) */
				,C.LRMP_NOD_ID    AS LRMP_NOD_ID4  	/* 지식맵노드명(차시) */
				,BC.KWD_NM
				,BC.CTN_META_DATA_ID
		FROM 
			(
				SELECT A.OPT_TXB_ID, A.LRMP_NOD_ID, MIN(A.RCSTN_ORDN) RCSTN_ORDN
				FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A             /* TL_교과학습활동재구성 */
				INNER JOIN LMS_CMS.BC_LRN_STP B                   /* BC_학습단계 */
					ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
					AND A.LRN_STP_ID = B.LRN_STP_ID
				INNER JOIN LMS_CMS.BC_CTN_MTD C                   /* BC_콘텐츠메타데이터 */
					ON A.LRN_ATV_ID = C.LRN_ATV_ID
					AND C.USE_YN= 'Y'
					AND C.DEL_YN= 'N'
				INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D
					ON A.OPT_TXB_ID = D.OPT_TXB_ID
					AND A.LRMP_NOD_ID = D.LRMP_NOD_ID
					AND D.LCKN_YN = 'N'
					AND D.USE_YN = 'Y'
				WHERE 1 = 1
				AND A.OPT_TXB_ID      = #{optTxbId}
				AND A.USE_YN    = 'Y'
				AND B.LRN_STP_DV_CD IN ('CL', 'EX')     /* 학습단계구분코드=개념학습, 평가 */
				AND B.DEL_YN    = 'N'
				AND (
				<foreach item="keyword" index="index" collection="cbSrhList" separator=" OR ">
					<!-- C.KWD_NM LIKE CONCAT('%', #{keyword}, '%') -->
					FIND_IN_SET(REPLACE(#{keyword}, ' ', ''), REPLACE(C.KWD_NM, ' ', '')) > 0
				</foreach>
				)
				GROUP BY A.OPT_TXB_ID, A.LRMP_NOD_ID
			) A 
			INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN B
				ON A.OPT_TXB_ID = B.OPT_TXB_ID
				AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
				AND A.RCSTN_ORDN = B.RCSTN_ORDN
			INNER JOIN LMS_CMS.BC_CTN_MTD BC           	/* BC_콘텐츠메타데이터 */
				ON B.LRN_ATV_ID = BC.LRN_ATV_ID
				AND BC.USE_YN= 'Y'
				AND BC.DEL_YN= 'N'
			INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C 	/* TL_교과학습노드재구성-차시 */
				ON B.OPT_TXB_ID = C.OPT_TXB_ID
				AND B.LRMP_NOD_ID = C.LRMP_NOD_ID
				AND C.LCKN_YN = 'N'
				AND C.USE_YN = 'Y'
			LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D 	/* TL_교과학습노드재구성- 소단원 */
				ON C.OPT_TXB_ID = D.OPT_TXB_ID
				AND C.URNK_LRMP_NOD_ID = D.LRMP_NOD_ID
				AND D.USE_YN = 'Y'
			LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN E 	/* TL_교과학습노드재구성- 중단원 */
				ON D.OPT_TXB_ID = E.OPT_TXB_ID
				AND D.URNK_LRMP_NOD_ID = E.LRMP_NOD_ID
				AND E.USE_YN = 'Y'
			LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN F 	/* TL_교과학습노드재구성- 대단원 */
				ON E.OPT_TXB_ID = F.OPT_TXB_ID
				AND E.URNK_LRMP_NOD_ID = F.LRMP_NOD_ID
				AND F.USE_YN = 'Y'
		ORDER BY F.RCSTN_ORDN, E.RCSTN_ORDN, D.RCSTN_ORDN, C.RCSTN_ORDN
		/** BcCb-Mapper-Mapper.xml - selectKeyWord */
	</select>
	<select id="selectDicWd" parameterType="com.aidt.api.bc.chatbot.dto.BcCbSrhAllDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbWdDto">
		SELECT 	A.TXB_ID, 
				A.DIC_WD_ID, 
				A.WD_NM, 
				A.SCHL_GRD_CD, 
				B.WDCL_KN_CD, 
				F.CM_CD_NM 			WDCL_KN_NM, 
				B.WD_MEAN_CN, 
				B.EXSN_CN, 
				B.EXSN_INTP_CN,
				C.FLE_PTH_NM 		WV_FLE_PTH_NM, 
				C.EXSN_FLE_TP_CD 	WV_EXSN_FLE_TP_CD, 
				C.FLE_NM 			WV_FLE_NM, 
				C.ALTN_TXT_CN 		WV_ALTN_TXT_CN,
				D.FLE_PTH_NM 		EV_FLE_PTH_NM, 
				D.EXSN_FLE_TP_CD 	EV_EXSN_FLE_TP_CD, 
				D.FLE_NM 			EV_FLE_NM, 
				D.ALTN_TXT_CN 		EV_ALTN_TXT_CN,
				E.FLE_PTH_NM 		IM_FLE_PTH_NM, 
				E.EXSN_FLE_TP_CD 	IM_EXSN_FLE_TP_CD, 
				E.FLE_NM 			IM_FLE_NM, 
				E.ALTN_TXT_CN 		IM_ALTN_TXT_CN
		FROM 	LMS_CMS.BC_DIC_WD 	A 
		LEFT JOIN LMS_CMS.BC_DIC_WD_EXPL B
			ON A.DIC_WD_ID = B.DIC_WD_ID
			AND B.USE_YN = 'Y'
			AND B.DEL_YN = 'N'
		LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE C				/* 단어 오디오 파일 */
			ON B.DIC_WD_EXPL_ID = C.DIC_WD_EXPL_ID
			AND C.EXSN_FLE_TP_CD 	= 'WV'
			AND C.DEL_YN 			= 'N'
		LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE D
			ON B.DIC_WD_EXPL_ID = D.DIC_WD_EXPL_ID			/* 예문 오디오 파일 */
			AND D.EXSN_FLE_TP_CD 	= 'EV'
			AND D.DEL_YN 			= 'N'
		LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE E
			ON B.DIC_WD_EXPL_ID = E.DIC_WD_EXPL_ID			/* 이미지 파일 */
			AND E.EXSN_FLE_TP_CD 	= 'IM'
			AND E.DEL_YN 			= 'N'
		LEFT JOIN LMS_LRM.CM_CM_CD F
			ON B.WDCL_KN_CD 	= F.CM_CD
			AND F.URNK_CM_CD 	= 'WDCL_KN_CD'
		WHERE 1 = 1
			<!-- AND A.DIC_WD_ID 	= #{dicWdId} -->
			AND (
			<foreach item="wdNm" index="index" collection="cbSrhList" separator=" OR ">
            	<!--A.WD_NM = #{wdNm}-->
            	FIND_IN_SET(REPLACE(#{wdNm}, ' ', ''), REPLACE(A.SRH_WD, ' ', '')) > 0
        	</foreach>
			)
			AND A.TXB_ID 	= #{txbId}
			AND A.USE_YN 	= 'Y'
			AND A.DEL_YN 	= 'N'
		ORDER BY A.MDF_DTM DESC
		/* 추연도 BcCb-Mapper.xml - selectDicWd */
	</select>

	<select id="selectMathWd" parameterType="com.aidt.api.bc.chatbot.dto.BcCbSrhAllDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbWdDto">
		SELECT 	A.TXB_ID, 
				A.MATH_WD_ID 		AS DIC_WD_ID, 
				A.WD_NM, 
				A.SCHL_GRD_CD, 
				B.WD_MEAN_CN, 
				C.FLE_PTH_NM 		IM_FLE_PTH_NM, 
				C.EXSN_FLE_TP_CD 	IM_EXSN_FLE_TP_CD, 
				C.FLE_NM 			IM_FLE_NM, 
				C.ALTN_TXT_CN 		IM_ALTN_TXT_CN,
				CASE A.SCHL_GRD_CD 
					WHEN '00' 	THEN 0
					WHEN 'E' 	THEN 1
					WHEN 'M' 	THEN 2
					WHEN 'H' 	THEN 4
				ELSE 0 END as SORT_NO
		FROM 	LMS_CMS.BC_MATH_WD 	A 
		LEFT JOIN LMS_CMS.BC_MATH_WD_EXPL B
			ON A.MATH_WD_ID = B.MATH_WD_ID
			AND B.USE_YN = 'Y'
			AND B.DEL_YN = 'N'
		LEFT JOIN LMS_CMS.BC_MATH_WD_EXSN_FLE C				/* 이미지 파일 */
			ON B.MATH_WD_EXPL_ID = C.MATH_WD_EXPL_ID
			AND C.EXSN_FLE_TP_CD 	= 'IM'
			AND C.DEL_YN 			= 'N'
		WHERE 1 = 1
			<!-- AND A.DIC_WD_ID 	= #{dicWdId} -->
			AND (
			<foreach item="wdNm" index="index" collection="cbSrhList" separator=" OR ">
            	<!--REPLACE(A.WD_NM, ' ', '') = REPLACE(#{wdNm}, ' ', '') -->
            	FIND_IN_SET(REPLACE(#{wdNm}, ' ', ''), REPLACE(A.SRH_WD, ' ', '')) > 0
        	</foreach>
			)
			AND A.TXB_ID 	= #{txbId}
			AND A.USE_YN 	= 'Y'
			AND A.DEL_YN 	= 'N'
		ORDER BY SORT_NO DESC LIMIT 1
		/* 추연도 BcCb-Mapper.xml - selectMathWd */
	</select>
	
	<select id="selectUsrUseInfo" parameterType="com.aidt.api.bc.chatbot.dto.BcCbUsrUseDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbUsrUseDto">
		SELECT 	CB_USE_ID,
				QUE_CN,
				DATE_FORMAT(CRT_DTM, '%Y-%m-%d') 	CRT_DATE,
				DATE_FORMAT(CRT_DTM, '%H:%i') 		CRT_TIME
		FROM 	LMS_LRM.CM_CB_USR_USE_INFO A
		WHERE 	A.LRN_USR_ID = #{lrnUsrId}
			AND A.DB_ID 	=  #{dbId}
			AND A.CRT_DTM <![CDATA[ > ]]> DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 DAY), '%Y-%m-%d')
			AND REPLACE(A.QUE_CN, ' ', '') LIKE CONCAT('%', #{queCn}, '%')
		ORDER BY CB_USE_ID DESC LIMIT 5

		/** BcCb-Mapper-Mapper.xml - selectUsrUseInfo */
	</select>

	<!-- 챗봇 사용자 만족도 등록처리-->
	<insert id="insertCsatInfo"  parameterType="com.aidt.api.bc.chatbot.dto.BcCbCsatDto">
		INSERT INTO LMS_LRM.CM_CB_CSAT_INFO     /* CM_챗봇 만족도 */
			(
				LRN_USR_ID,
				CSAT_EV,
				CRTR_ID,
				CRT_DTM,
				MDFR_ID,
				MDF_DTM,
				DB_ID
			) 
		VALUES
		( 
			#{lrnUsrId}
			,#{csatEv}
			,#{lrnUsrId}
			,NOW()
			,#{lrnUsrId}
			,NOW()
			,#{dbId}
		)
        /* 추연도 BcCb-Mapper.xml - insertCsatInfo */
	</insert>

	<!-- 챗봇 사용자 질문 등록처리-->
	<insert id="insertUsrUseInfo"  parameterType="com.aidt.api.bc.chatbot.dto.BcCbCsatDto">
		INSERT INTO LMS_LRM.CM_CB_USR_USE_INFO     /* CM_챗봇 사용자 사용 내역 */
			(
				LRN_USR_ID,
				QUE_CN,
				ANW_CN,
				CRTR_ID,
				CRT_DTM,
				MDFR_ID,
				MDF_DTM,
				DB_ID
			) 
		VALUE
		(
			#{lrnUsrId}
			,#{queCn}
			,NULL
			,#{lrnUsrId}
			,NOW()
			,#{lrnUsrId}
			,NOW()
			,#{dbId}
		)
        /* 추연도 BcCb-Mapper.xml - insertUsrUseInfo */
	</insert>
	
	<!-- 챗봇 사용 가능 여부-->
	<select id="selectCbUseYn" parameterType="com.aidt.api.bc.chatbot.dto.BcCbUseYnDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbUseYnDto">
		SELECT	CB_USE_YN 
		FROM 	LMS_LRM.CM_FNC_USE_SETM
		WHERE 	OPT_TXB_ID = #{optTxbId};
	</select>
	
	<!-- 챗봇 사용 가능 여부 수정-->
	<update id="updateCbUseYn" parameterType="com.aidt.api.bc.chatbot.dto.BcCbUseYnDto">
		UPDATE	LMS_LRM.CM_FNC_USE_SETM
		SET		CB_USE_YN 	= #{cbUseYn}
		WHERE 	OPT_TXB_ID 	= #{optTxbId};
	</update>
	
	<!-- 시나리오 마스터 등록 처리 -->
	<insert id="insertSnroData" parameterType="com.aidt.api.bc.chatbot.dto.BcCbSnroDto">
		<selectKey keyProperty="snroId" order="AFTER" resultType="int">
			SELECT MAX(SNRO_ID) from LMS_CMS.BC_CB_SNRO
		</selectKey>
		
	    INSERT INTO LMS_CMS.BC_CB_SNRO
	    (
			SNRO_ID,
			TXB_ID,
			WD_NM,
			WD_MEAN_CN,
			USE_YN,
			DEL_YN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
	    )
	    VALUES
 	    (
 	    	(SELECT SNRO_ID from (SELECT IFNULL(MAX(SNRO_ID) + 1, 1) SNRO_ID FROM LMS_CMS.BC_CB_SNRO) as A)
	        ,#{txbId}
	        ,#{wdNm}
	        ,#{wdMeanCn}
	        ,'Y'
	        ,'N'
	        ,#{usrId}
	        ,NOW()
	        ,#{usrId}
	        ,NOW()
	        ,#{dbId}
	    )
	</insert>
	
	<insert id="insertSnroDetailData" parameterType="Map">
		INSERT INTO LMS_CMS.BC_CB_SNRO_SRH  (
			SNRO_SRH_ID,
			SNRO_ID,
			SRH_WD,
			USE_YN,
			DEL_YN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		VALUES
		(
			(SELECT SNRO_SRH_ID from (SELECT IFNULL(MAX(SNRO_SRH_ID) + 1, 1) SNRO_SRH_ID FROM LMS_CMS.BC_CB_SNRO_SRH) as A)
			,#{snroId}
			,#{srhWd}
	        ,'Y'
	        ,'N'
	        ,#{usrId}
	        ,NOW()
	        ,#{usrId}
	        ,NOW()
	        ,#{dbId}
		)
    </insert>
    
	<select id="selectSnroList" parameterType="com.aidt.api.bc.chatbot.dto.BcCbSrhAllDto" resultType="com.aidt.api.bc.chatbot.dto.BcCbWdDto">
		SELECT 	A.WD_NM, A.WD_MEAN_CN 
		FROM 	LMS_CMS.BC_CB_SNRO A
		INNER JOIN LMS_CMS.BC_CB_SNRO_SRH B
			ON A.SNRO_ID  = B.SNRO_ID 
		WHERE 1 = 1 
		AND A.TXB_ID  = #{txbId}
		AND (<foreach item="wdNm" index="index" collection="cbSrhList" separator=" OR ">
                REPLACE(B.SRH_WD, ' ', '') = REPLACE(#{wdNm}, ' ', '')
                OR
                REPLACE(A.WD_NM, ' ', '') = REPLACE(#{wdNm}, ' ', '')
            </foreach>)
		AND A.USE_YN  = 'Y'
		AND A.DEL_YN  = 'N'
		ORDER BY A.SNRO_ID DESC LIMIT 1
		/* 추연도 BcCb-Mapper.xml - selectMathWd */
	</select>
	
	<select id="selectTxbAutrInfo" parameterType="String" resultMap="autrMap">
		 /* BcCb-Mapper.selectTxbAutrInfo 교과서 저자 정보 조회 */
		 SELECT AUTR_CD
			,AUTR_NM
			,TXB_SUB_URL
		 FROM LMS_CMS.BC_TXB 
		 WHERE TXB_ID = #{txbId}
     </select>
	
	<select id="selectDailyCnvSnro" parameterType="String" resultType="String">
		/* BcCb-Mapper.selectDailySnro 일상 대화 시나리오 검색 */
		SELECT bcdcs.anw_cn
		FROM lms_cms.bc_cb_daily_cnv_snro bcdcs 
		    JOIN lms_cms.bc_cb_daily_cnv_snro_srh bcdcss ON bcdcss.cb_daily_cnv_snro_id = bcdcs.cb_daily_cnv_snro_id
		WHERE REPLACE(bcdcss.srh_cn, ' ', '') = REPLACE(#{srhCn}, ' ', '')
		    AND bcdcss.use_yn = 'Y'
		    AND bcdcs.del_yn = 'N'
		LIMIT 1
	</select>
	
</mapper>
