package com.aidt.api.al.cmt.cm;

import java.util.function.BiFunction;

import org.springframework.util.ObjectUtils;



/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-29 15:02:39
 * @modify date 2024-05-29 15:02:39
 * @desc
 */
public enum AiCmtLvlCalculator {
    MA_TYPE_50_80((c, t) -> ((float) c / t) * 100 >= 80.0 ? "HH" : ((float) c /t) * 100 >= 50.0 ? "MM" : "LL"),
    MA_TYPE_60_80((c, t) -> ((float) c / t) * 100 >= 80.0 ? "HH" : ((float) c /t) * 100 >= 60.0 ? "MM" : "LL"),
    MA_TYPE_80_100((c, t) -> ((float) c / t) * 100 >= 100.0 ? "HH" : ((float) c /t) * 100 >= 80.0 ? "MM" : "LL"),
    MA_TYPE_80_YN((c, t) -> ((float) c / t) * 100 >= 80 ? "Y":"N") ,
    ;

    private final BiFunction<Integer, Integer, String> expression;

    AiCmtLvlCalculator(BiFunction<Integer, Integer, String> expression) {
        this.expression = expression;
    }

    public String calculate(com.aidt.api.al.cmt.dto.req.cm.QtmCntReqDto reqDto) {
        if(ObjectUtils.isEmpty(reqDto)){return null;}
        if(reqDto.getCorrectQtmCnt() != null && reqDto.getTotalQtmCnt() != null) {
        	return expression.apply(reqDto.getCorrectQtmCnt(), reqDto.getTotalQtmCnt());        	
        } else {
        	return null;
        }
    }
}
