<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.back.aiRcmTsshQtm">

	<select id="selectAeEvCheck" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="int">
		SELECT
		    COUNT(*)
		FROM LMS_LRM.EA_EV EE 
			INNER JOIN LMS_LRM.EA_EV_RS EER
			    ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			    ON EAETR.EV_ID = EE.EV_ID
			    AND EAETR.OPT_TXB_ID = EE.OPT_TXB_ID
			    AND EE.EV_DV_CD = 'AE'
		WHERE EER.USR_ID = #{usrId}
		AND EE.OPT_TXB_ID = #{optTxbId}
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		<if test = 'tcKmmpNodId != null and !tcKmmpNodId.equals("")'>
			AND EAETR.TC_KMMP_NOD_ID = #{tcKmmpNodId}
		</if>
		<if test = 'tpcKmmpNodId != null and !tpcKmmpNodId.equals("")'>
			AND EAETR.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
		</if>
		AND EE.EV_DV_CD = 'AE'
		AND EE.EV_DTL_DV_CD = #{evDtlDvCd}
	</select>

	<!-- 학습자 수준 판단을 위한 형성평가, 총괄평가정보 조회 -->
	<select id="selectMluAvgScr" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT 
			<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
			   IFNULL(SUM(A.EV_DTL_DV_CD = 'FO' AND A.CTN_DFFD_DV_CD = '02'), 0) AS FR_LL_CNT
			 , IFNULL(SUM(A.EV_DTL_DV_CD = 'FO' AND A.CTN_DFFD_DV_CD = '02' AND A.CANS_YN = 'Y'), 0) AS FR_LL_Y_CNT
			 , IFNULL(SUM(A.EV_DTL_DV_CD = 'FO' AND A.CTN_DFFD_DV_CD = '03'), 0) AS FR_MM_CNT
			 , IFNULL(SUM(A.EV_DTL_DV_CD = 'FO' AND A.CTN_DFFD_DV_CD = '03' AND A.CANS_YN = 'Y'), 0) AS FR_MM_Y_CNT
			 , IFNULL(SUM(A.EV_DTL_DV_CD = 'OV' AND A.CTN_DFFD_DV_CD = '03'), 0) AS UG_MM_CNT
			 , IFNULL(SUM(A.EV_DTL_DV_CD = 'OV' AND A.CTN_DFFD_DV_CD = '03' AND A.CANS_YN = 'Y'), 0) AS UG_MM_Y_CNT
			</if>
			<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			   IFNULL(SUM(A.EV_DTL_DV_CD = 'OV'), 0) AS UG_MM_CNT
	 		 , IFNULL(SUM(A.EV_DTL_DV_CD = 'OV' AND A.CANS_YN = 'Y'), 0) AS UG_MM_Y_CNT
	 		</if>
		FROM (
			SELECT
			    EV.EV_ID
			  , EER.USR_ID
			  , EV.EV_DV_CD
			  , EV.EV_DTL_DV_CD
			  , TS.LLU_KMMP_NOD_ID
			  , TS.MLU_KMMP_NOD_ID
			  , QT.QTM_ID
			  , QQ.QP_DFFD_CD AS CTN_DFFD_DV_CD
			  , AN.CANS_YN
			FROM LMS_LRM.EA_EV EV
			INNER JOIN LMS_LRM.EA_EV_RS EER
				ON EV.EV_ID = EER.EV_ID
			LEFT OUTER JOIN LMS_LRM.EA_AI_EV_TS_RNGE TS
				ON EV.EV_ID = TS.EV_ID AND EV.OPT_TXB_ID = TS.OPT_TXB_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM QT
				ON EV.EV_ID = QT.EV_ID
				AND TS.TPC_KMMP_NOD_ID = QT.TPC_ID
			INNER JOIN LMS_CMS.QP_QTM QQ 
				ON QT.QTM_ID = QQ.QP_QTM_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW AN
				ON EV.EV_ID = AN.EV_ID
				AND QT.QTM_ID = AN.QTM_ID
				AND EER.USR_ID = AN.USR_ID
			WHERE EER.USR_ID = #{usrId}
			and EV.OPT_TXB_ID = #{optTxbId}
			<if test = 'mluKmmpNodId != null and !mluKmmpNodId.equals("")'> <!-- 중단원 -->
				AND TS.MLU_KMMP_NOD_ID = #{mluKmmpNodId} 
			</if>
			<if test = 'tcKmmpNodId != null and !tcKmmpNodId.equals("")'> <!-- 차시 -->
				AND TS.TC_KMMP_NOD_ID = #{tcKmmpNodId} 
			</if>
		) A
		/* AI맞춤 학습자수준 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectMluAvgScr */
	</select>
	
	<select id="selectEnTcCansYCntList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    MAX(RNGE.EV_ID) AS EV_ID
		  , SUM(ANW.CANS_YN = 'Y') AS CANS_CNT
		  , RNGE.TC_KMMP_NOD_ID
		FROM LMS_LRM.EA_EV EV
			INNER JOIN LMS_LRM.EA_EV_RS RS
				ON EV.EV_ID = RS.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE RNGE
				ON EV.EV_ID = RNGE.EV_ID
				AND EV.OPT_TXB_ID = RNGE.OPT_TXB_ID
			JOIN LMS_LRM.EA_EV_QTM QTM
				ON EV.EV_ID = QTM.EV_ID
				AND RNGE.TPC_KMMP_NOD_ID = QTM.TPC_ID
			JOIN LMS_LRM.EA_EV_QTM_ANW ANW
				ON EV.EV_ID = ANW.EV_ID
				AND QTM.QTM_ID = ANW.QTM_ID
				AND EV.USR_ID = ANW.USR_ID
		WHERE RS.EV_CMPL_YN = 'Y'
		AND EV.EV_DV_CD = 'AE'
		<choose>
			<when test="evDtlDvCdList != null">
				AND EV.EV_DTL_DV_CD IN
				<foreach collection="evDtlDvCdList" item="evDtlDvCd" index="index" open="(" close=")" separator=",">
					#{evDtlDvCd}
				</foreach>
			</when>
			<otherwise>
				AND EV.EV_DTL_DV_CD = #{evDtlDvCd}
			</otherwise>
		</choose>
		AND RS.USR_ID = #{usrId}
		AND EV.OPT_TXB_ID = #{optTxbId}
		AND RNGE.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		<if test = 'tcKmmpNodId != null and !tcKmmpNodId.equals("")'> <!-- 해당로우는 초등영어 A,B,C타입에만 사용 -->
			AND RNGE.TC_KMMP_NOD_ID = #{tcKmmpNodId} 
		</if>
		GROUP BY RNGE.TC_KMMP_NOD_ID
		/* AI맞춤 학습자수준 차시별 정답갯수 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectEnTcCansYCntList */
	</select>
	
	<insert id="updateLrnrVelTpCd" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		INSERT INTO LMS_LRM.AI_LRNR_LV
			 ( USR_ID, OPT_TXB_ID , LU_KMMP_NOD_ID
			 , LRNR_VEL_TP_CD, DEL_YN
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{usrId}
			 , #{optTxbId}
			 , #{mluKmmpNodId}
			 , #{lrnrVelTpCd}
			 , 'N'
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		ON DUPLICATE KEY UPDATE
		  	 LRNR_VEL_TP_CD = #{lrnrVelTpCd}
		   , MDFR_ID = #{usrId}
		   , MDF_DTM = NOW()
		/* AI맞춤 학습자수준 - 이혜인 - AiRcmTsshQtm-Mapper.xml - updateLrnrVelTpCd */
	</insert>
	
	<select id="selectLrnrVelTpCd" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="String">
		SELECT
		    COALESCE(LRNR_VEL_TP_CD , 'NM') AS LRNR_VEL_TP_CD
		FROM LMS_LRM.AI_LRNR_LV
		WHERE USR_ID = #{usrId}
		AND OPT_TXB_ID = #{optTxbId}
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
			AND LU_KMMP_NOD_ID = #{mluKmmpNodId}
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			AND LU_KMMP_NOD_ID = #{tcKmmpNodId}
		</if>
		AND DEL_YN = 'N'
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectLrnrVelTpCd */
	</select>
	
	<select id="selectQtmCansYnByTpc" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(ANW.USR_ID) AS USR_ID
		  , MAX(QTM.TPC_ID) AS TPC_KMMP_NOD_ID
		  , QTM.QTM_ID
		  , MAX(ANW.CANS_YN) AS CANS_YN
		  , IFNULL(MAX(AUQP.TXM_PN), 0) AS TXM_PN
		  , MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD
		  , IFNULL(MAX(AUTP.AI_PRED_AVG_CANS_RT), 0) AS AI_PRED_AVG_CANS_RT
		  , IFNULL(MAX(AUTP.AI_PRED_AVG_SCR), 0.5) AS AI_PRED_AVG_SCR
		  , MAX(EE.EV_ID) AS EV_ID
		  , MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD
		FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER
			    ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_EV_QTM QTM
			    ON EE.EV_ID = QTM.EV_ID
			INNER JOIN LMS_CMS.QP_QTM QQ
			    ON QTM.QTM_ID = QQ.QP_QTM_ID
			INNER JOIN LMS_LRM.EA_EV_QTM_ANW ANW
			    ON QTM.QTM_ID = ANW.QTM_ID
			    AND EER.USR_ID = ANW.USR_ID
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
			    ON QTM.TPC_ID = AUTP.TPC_ID
			    AND ANW.USR_ID = AUTP.USR_ID
			    AND AUTP.OPT_TXB_ID = EE.OPT_TXB_ID
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
			    ON QTM.QTM_ID = AUQP.QTM_ID
			    AND ANW.USR_ID = AUQP.USR_ID
			    AND AUQP.OPT_TXB_ID = EE.OPT_TXB_ID
		WHERE
		    EER.USR_ID = #{usrId}
		<if test = 'evDvCd != null and !evDvCd.equals("")'>
			AND EE.EV_DV_CD = 'AE'
		</if>
		<if test = 'evId != null'>
			AND EE.EV_ID = #{evId}
		</if>
		GROUP BY
		    QTM.QTM_ID
		ORDER BY
			MAX(QTM.TPC_ID), QTM.QTM_ID, MAX(ANW.CANS_YN)
		/* AI맞춤 토픽숙련도 판단 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectQtmCansYnByTpc */
	</select>
	
	<select id="selectMaQtmCansYnCntByEvId" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(QTM.TPC_ID) AS TPC_KMMP_NOD_ID
		  , SUM(CASE WHEN ANW.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_Y_CNT
		  , MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD
		FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_QTM QTM
			    ON EE.EV_ID = QTM.EV_ID
			INNER JOIN LMS_CMS.QP_QTM QQ
			    ON QTM.QTM_ID = QQ.QP_QTM_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW ANW
			    ON QTM.QTM_ID = ANW.QTM_ID
			    AND ANW.USR_ID = #{usrId}
		WHERE
		    EE.EV_DV_CD = 'AE'
			AND EE.EV_ID = #{evId}
		GROUP BY
		    EE.EV_ID
	</select>
	
	<update id="updateTpcAvn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		UPDATE
		    LMS_LRM.AI_USRLY_TPC_PROF
		SET
		    TPC_AVN = #{tpcAvn}
		  , MDFR_ID = #{usrId}
		  , MDF_DTM = NOW()
		WHERE USR_ID = #{usrId}
		AND TPC_ID = #{tpcKmmpNodId}
		AND OPT_TXB_ID = #{optTxbId}
	   /* AI맞춤 토픽숙련도 판단 - 이혜인 - AiRcmTsshQtm-Mapper.xml - updateTpcAvn */
	</update>
	
	<!-- 평가지 고정문항 리스트 -->
	<select id="selectEevshQtmList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(RCSTN.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(RCSTN.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(RCSTN.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID,
			MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
			BEQM.QTM_ID,
			MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD,
			MAX(QTMDFFD.CM_CD_NM) AS CTN_DFFD_DV_NM,
			MAX(BE.EVSH_ID) AS EVSH_ID,
			MAX(BE.EVSH_CD) AS EVSH_CD,
			MAX(BE.EVSH_TP_CD) AS EVSH_DV_CD
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN RCSTN
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
				ON RCSTN.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
				AND RCSTN.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				AND DPTH3.DPTH = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
				ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
				AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				AND DPTH4.DPTH = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
				ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
				AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
				AND DPTH5.DPTH = 5
			INNER JOIN LMS_CMS.BC_EVSH BE
			<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
				ON RCSTN.KMMP_NOD_ID = BE.MLU_NOD_ID
			</if>
			<if test = 'evDtlDvCd == "OV" and sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어(진단) -->
				ON RCSTN.KMMP_NOD_ID = BE.MLU_NOD_ID
			</if>
			<if test = 'evDtlDvCd != "OV" and sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어(맞춤) -->
				ON DPTH4.KMMP_NOD_ID = BE.TC_LU_ID
			</if>
				AND BE.DEL_YN = 'N'
				AND BE.USE_YN = 'Y'
			INNER JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM
				ON BE.EVSH_ID = BEQM.EVSH_ID
				AND DPTH5.KMMP_NOD_ID = BEQM.TPC_ID
			INNER JOIN LMS_CMS.QP_QTM QQ
				ON BEQM.QTM_ID = QQ.QP_QTM_ID
			INNER JOIN LMS_LRM.CM_CM_CD QTMDFFD
				ON QTMDFFD.URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD'
				AND QTMDFFD.CM_CD = QQ.QP_DFFD_CD
		WHERE RCSTN.OPT_TXB_ID = #{optTxbId}
		AND RCSTN.DPTH = 2
		AND RCSTN.KMMP_NOD_ID = #{mluKmmpNodId}
		<if test = 'tcKmmpNodId != null and tcKmmpNodId.length != 0'> <!-- 차시 -->
			AND DPTH4.KMMP_NOD_ID = #{tcKmmpNodId}
		</if>
		AND BE.EVSH_TP_CD = #{eschEnSetDv}
		GROUP BY BEQM.QTM_ID
		ORDER BY MAX(BEQM.QTM_EPS_ORDN)
		/* AI평가지문항 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectEevshQtmList */
	</select>
	
	<select id="selectBcRltQtmMpnList" parameterType="map" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			<choose>
				<when test = 'sbjGrd != null and sbjGrd.equals("MA")'> <!-- 수학 -->
					BRQM.RLT_QTM_ID AS QTM_ID,
					BRQM.RLT_QTM_TP_CD AS RLT_QTM_TP_CD,
					BALAC.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
					QQ.QP_DFFD_CD AS CTN_DFFD_DV_CD,
					QTMDFFD.CM_CD_NM AS CTN_DFFD_DV_NM,
					DPTH5.OPT_TXB_ID AS OPT_TXB_ID,
					DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
					DPTH3.KMMP_NOD_ID AS SLU_KMMP_NOD_ID,
					DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
					DPTH2.URNK_KMMP_NOD_ID AS LLU_KMMP_NOD_ID
				</when>
				<otherwise>
					MAX(BRQM.RLT_QTM_ID) AS QTM_ID,
					MAX(BRQM.RLT_QTM_TP_CD) AS RLT_QTM_TP_CD,
					MAX(BALAC.KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
					MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD,
					MAX(QTMDFFD.CM_CD_NM) AS CTN_DFFD_DV_NM,
					MAX(DPTH5.OPT_TXB_ID) AS OPT_TXB_ID,
					MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
					MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID,
					MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
					MAX(DPTH2.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID
				</otherwise>
			</choose>
		FROM LMS_CMS.BC_RLT_QTM_MPN BRQM
			INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
			    ON BRQM.SRC_QTM_ID = BALAC.CTN_CD
			    AND BALAC.DEL_YN = 'N'
			INNER JOIN LMS_CMS.QP_QTM QQ
			    ON BALAC.CTN_CD = QQ.QP_QTM_ID
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
				ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
				AND DPTH5.DPTH = 5
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
				ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
				AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
				AND DPTH4.DPTH = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
				ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
				AND DPTH3.OPT_TXB_ID = DPTH5.OPT_TXB_ID
				AND DPTH3.DPTH = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
				AND DPTH2.OPT_TXB_ID = DPTH5.OPT_TXB_ID
				AND DPTH2.DPTH = 2
			INNER JOIN LMS_LRM.CM_CM_CD QTMDFFD
				ON QTMDFFD.URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD'
				AND QTMDFFD.CM_CD = QQ.QP_DFFD_CD
		WHERE BRQM.RLT_QTM_ID > 0
		AND DPTH5.OPT_TXB_ID = #{optTxbId}
		<choose>
			<when test = 'sbjGrd != null and sbjGrd.equals("eschEn")'> <!-- 초등영어 -->
				AND RLT_QTM_TP_CD = #{rltQtmTpCd}
				AND BRQM.SRC_QTM_ID IN 
				<foreach collection="qtmList" item="item" index="index" open="(" close=")" separator=",">
					#{item.qtmId}
				</foreach>
				AND BRQM.DEL_YN = 'N'
				GROUP BY SRC_QTM_ID
			</when>
			<when test = 'sbjGrd != null and sbjGrd.equals("MA")'> <!-- 수학 -->
				AND RLT_QTM_TP_CD <![CDATA[<>]]> 'DE'
				AND BRQM.SRC_QTM_ID = #{qtmId}
				AND BRQM.DEL_YN = 'N'
				ORDER BY RLT_QTM_TP_CD
				LIMIT 1
			</when>
			<otherwise>
				AND RLT_QTM_TP_CD = #{rltQtmTpCd}
				AND BRQM.SRC_QTM_ID = #{qtmId}
				AND BRQM.DEL_YN = 'N'
				GROUP BY SRC_QTM_ID
			</otherwise>
		</choose>
		/* AI맞춤 연관문항 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectBcRltQtmMpnList */
	</select>
	
	<!-- AI맞춤 문항추천 리스트 -->
	<select id="selectAiRcmTsshQtmList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    0 AS RCM_RNK
		  , MAX(RCSTN.OPT_TXB_ID) AS OPT_TXB_ID
		  , #{usrId} AS USR_ID
		  , MAX(BT.SBJ_CD) AS SBJ_CD
		  , MAX(BT.SCHL_GRD_CD) AS SCHL_GRD_CD
		  , MAX(BT.SGY_CD) AS SGY_CD
		  , MAX(BT.AUTR_NM) AS AUTR_NM
		  , MAX(BT.AUTR_CD) AS AUTR_CD
		  , MAX(RCSTN.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID
		  , MAX(RCSTN.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
		  , MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID
		  , MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID
		  , MAX(DPTH5.KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
		  , BALAC.CTN_CD AS QTM_ID
		  , MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD
		  , MAX(QTMDFFD.CM_CD_NM) AS CTN_DFFD_DV_NM
		  , MAX(BALAC.CTN_TP_CD) AS CTN_TP_CD
		  , MAX(CTNTP.CM_CD_NM) AS CTN_TP_NM
		  , IFNULL(MAX(AUTP.AI_PRED_AVG_CANS_RT), 0) AS AI_PRED_AVG_CANS_RT
		  , IFNULL(MAX(AUTP.AI_PRED_AVG_SCR), 0.5) AS AI_PRED_AVG_SCR
		  , ABS(IFNULL(MAX(AUTP.AI_PRED_AVG_CANS_RT), 0) - IFNULL(MAX(AUTP.AI_PRED_AVG_SCR), 0.5)) AS TPC_PRED_ATL_GAP
		  , IFNULL(MAX(AUTP.TPC_AVN), 0.5) AS TPC_AVN
		  , IFNULL(MAX(AIQTM.TXM_PN), 0) AS TXM_PN
		  , IFNULL(MAX(AIQTM.CANS_YN), '') AS CANS_YN
		  , IFNULL(MAX(AIQTM.XPL_TM_SCNT), 0) AS XPL_TM_SCNT
		  , IFNULL(MAX(AIQTM.AI_PRED_CANS_RT), 0.5) AS AI_PRED_CANS_RT
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN RCSTN
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
				ON RCSTN.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
				AND RCSTN.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				AND DPTH3.DPTH = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
				ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
				AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				AND DPTH4.DPTH = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
				ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
				AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
				AND DPTH5.DPTH = 5
			INNER JOIN LMS_LRM.CM_OPT_TXB COT
				ON RCSTN.OPT_TXB_ID = COT.OPT_TXB_ID
			INNER JOIN LMS_CMS.BC_TXB BT
				ON BT.TXB_ID = COT.TXB_ID
				AND BT.USE_YN = 'Y'
			INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
				ON DPTH5.KMMP_NOD_ID = BALAC.KMMP_NOD_ID
				AND BALAC.DEL_YN = 'N'
			INNER JOIN LMS_CMS.QP_QTM QQ
				ON BALAC.CTN_CD = QQ.QP_QTM_ID
			INNER JOIN LMS_LRM.CM_CM_CD QTMDFFD
				ON QTMDFFD.URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD'
				AND QTMDFFD.CM_CD = QQ.QP_DFFD_CD
			INNER JOIN LMS_LRM.CM_CM_CD CTNTP
				ON CTNTP.URNK_CM_CD = 'CTN_TP_CD'
				AND CTNTP.CM_CD = BALAC.CTN_TP_CD
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
				ON RCSTN.KMMP_NOD_ID = AUTP.LU_KMMP_NOD_ID
				AND DPTH5.KMMP_NOD_ID = AUTP.TPC_ID
				AND AUTP.USR_ID = #{usrId}
				AND AUTP.OPT_TXB_ID = #{optTxbId}
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AIQTM
				ON RCSTN.KMMP_NOD_ID = AIQTM.LU_KMMP_NOD_ID
				AND BALAC.CTN_CD = AIQTM.QTM_ID
				AND AUTP.USR_ID = AIQTM.USR_ID
				AND AIQTM.OPT_TXB_ID = #{optTxbId}
		WHERE RCSTN.OPT_TXB_ID = #{optTxbId}
		AND RCSTN.DPTH = 2
		AND RCSTN.KMMP_NOD_ID = #{mluKmmpNodId}
		AND BALAC.CTN_TP_CD IN ('EX', 'QU')
		AND QQ.QP_DFFD_CD IS NOT NULL
		<if test = 'tcKmmpNodId != null and tcKmmpNodId.length != 0'> <!-- 차시 -->
			AND DPTH4.KMMP_NOD_ID = #{tcKmmpNodId}
		</if>
		<if test = 'tcKmmpNodIdList != null and tcKmmpNodIdList.length != 0'> <!-- 차시범위 --> 
			AND DPTH4.KMMP_NOD_ID IN
			<foreach collection="tcKmmpNodIdList" item="tc" index="index" open="(" close=")" separator=",">
				#{tc}
			</foreach>
		</if>
		<if test = 'tpcKmmpNodIdList != null and tpcKmmpNodIdList.length != 0'> <!-- 토픽범위 --> 
			AND DPTH5.KMMP_NOD_ID IN
			<foreach collection="tpcKmmpNodIdList" item="tpc" index="index" open="(" close=")" separator=",">
				#{tpc}
			</foreach>
		</if>
		GROUP BY BALAC.CTN_CD
		ORDER BY MAX(DPTH5.KMMP_NOD_ID)
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectAiRcmTsshQtmList */
	</select>
	
	<select id="selectEvQtmCansInfo" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    RNGE.EV_ID
		  , RNGE.MLU_KMMP_NOD_ID
		  , RNGE.TC_KMMP_NOD_ID
		  , RNGE.TPC_KMMP_NOD_ID
		  , QTM.QTM_ID
		  , QTM.QTM_ORDN
		  , QTM.QTM_DFFD_DV_CD AS CTN_DFFD_DV_CD
		  , ANW.CANS_YN
		FROM LMS_LRM.EA_AI_EV_TS_RNGE RNGE
			JOIN LMS_LRM.EA_EV_RS RS
				ON RS.EV_ID = RNGE.EV_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV EV
				ON RS.EV_ID = EV.EV_ID
			JOIN LMS_LRM.EA_EV_QTM QTM
				ON EV.EV_ID = QTM.EV_ID
				AND RNGE.TPC_KMMP_NOD_ID = QTM.TPC_ID
			JOIN LMS_LRM.EA_EV_QTM_ANW ANW
				ON EV.EV_ID = ANW.EV_ID
				AND QTM.QTM_ID = ANW.QTM_ID
		WHERE RS.EV_CMPL_YN = 'Y'
		AND RNGE.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		<if test = 'tcKmmpNodId != null and tcKmmpNodId.length != 0'> <!-- 차시 -->
			AND RNGE.TC_KMMP_NOD_ID = #{tcKmmpNodId}
		</if>
		<if test = 'evDvCd != null and !evDvCd.equals("")'>
			AND EV.EV_DV_CD = #{evDvCd}
		</if>
		<if test = 'evDtlDvCd != null and !evDtlDvCd.equals("")'>
			AND EV.EV_DTL_DV_CD = #{evDtlDvCd}
		</if>
		AND EV.OPT_TXB_ID = #{optTxbId}
		AND RS.USR_ID = #{usrId}
		ORDER BY QTM.QTM_ORDN
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectEvQtmCansInfo */
	</select>
	
	<insert id="insertEaEv" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		<selectKey keyProperty="evId" order="AFTER" resultType="integer">
			SELECT LAST_INSERT_ID() evId
		</selectKey>
		INSERT INTO LMS_LRM.EA_EV
			   ( OPT_TXB_ID, USR_ID, EV_DV_CD
			   <if test = 'evDtlDvCd != null and !evDtlDvCd.equals("")'>
			   , EV_DTL_DV_CD
			   </if>
			   , EV_NM
			   <if test = 'txmStrDtm != null and txmStrDtm.equals("Y")'>
			   , TXM_STR_DTM
			   </if>
			   <if test = 'txmEndDtm != null and txmEndDtm.equals("Y")'>
			   , TXM_END_DTM
			   </if>
			   <if test = 'xplTmSetmYn != null and xplTmSetmYn.equals("Y")'>
			   , XPL_TM_SCNT
			   </if>
			   , EVSH_DV_CD
			   , EVSH_ID
			   , EVSH_CD
			   , QST_CNT, FNL_QST_CNT, LCKN_YN, RTXM_PMSN_YN, TXM_PTME_SETM_YN, XPL_TM_SETM_YN
			   , USE_YN, DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		VALUES
			   ( #{optTxbId}
			   , #{usrId}
			   , #{evDvCd}
			   <if test = 'evDtlDvCd != null and !evDtlDvCd.equals("")'>
			   , #{evDtlDvCd}
			   </if>
			   , #{evNm}
			   <if test = 'txmStrDtm != null and txmStrDtm.equals("Y")'>
			   , #{txmStrDtm}<!-- 응시시작일시 -->   
			   </if>
			   <if test = 'txmEndDtm != null and txmEndDtm.equals("Y")'>
			   , #{txmEndDtm} <!-- 응시종료일시 -->
			   </if>
			   <if test = 'xplTmSetmYn != null and xplTmSetmYn.equals("Y")'>
			   , #{xplTmScnt} <!-- 풀이시간초수 -->  
			   </if>
			   , #{evshDvCd}
			   , #{evshId}
			   , #{evshCd}
			   , #{qstCnt}  
			   , #{qstCnt} <!-- 최종문제수 --> 
			   , #{lcknYn}      
			   , #{rtxmPmsnYn}  
			   , #{txmPtmeSetmYn}<!-- 응시기간설정여부 -->
			   , #{xplTmSetmYn} <!-- 풀이시간설정여부 --> 
			   , #{useYn}       
			   , #{delYn}       
			   , #{usrId}
			   , NOW()
			   , #{usrId}
			   , NOW()
			   , #{dbId} )
      	/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertEaEv */
	</insert>
	
	<insert id="insertEaAiEvTsRnge" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		INSERT INTO LMS_LRM.EA_AI_EV_TS_RNGE
			 ( EV_ID, AI_TS_RNGE_SEQ_NO, OPT_TXB_ID, LLU_KMMP_NOD_ID, LLU_KMMP_NOD_NM
			 , MLU_KMMP_NOD_ID, MLU_KMMP_NOD_NM, TC_KMMP_NOD_ID, TC_KMMP_NOD_NM, TPC_KMMP_NOD_ID, TPC_KMMP_NOD_NM
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{evId}
			 , (SELECT IFNULL( MAX(T.AI_TS_RNGE_SEQ_NO)+1,1) FROM LMS_LRM.EA_AI_EV_TS_RNGE T WHERE T.EV_ID = #{evId})
			 , #{optTxbId}
			 <if test="lluKmmpNodId != null">
				 , #{lluKmmpNodId}
				 , (SELECT A.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = #{lluKmmpNodId})
			 </if>
			 <if test="lluKmmpNodId == null and mluKmmpNodId != null">
				 , (SELECT A.URNK_KMMP_NOD_ID FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = #{mluKmmpNodId})
				 , (SELECT A.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = (SELECT B.URNK_KMMP_NOD_ID FROM LMS_CMS.BC_KMMP_NOD B WHERE B.KMMP_NOD_ID = #{mluKmmpNodId}))
			 </if>
			 <if test="mluKmmpNodId != null">
				 , #{mluKmmpNodId}
				 , (SELECT A.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = #{mluKmmpNodId})
			 </if>
			 <if test="lluKmmpNodId == null and mluKmmpNodId == null">
				, null
				, null
			 </if>
			 <if test="tcKmmpNodId != null">
				, #{tcKmmpNodId}
				, (SELECT A.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = #{tcKmmpNodId})
			 </if>
			 <if test="tcKmmpNodId == null and tpcKmmpNodId != null">
				, (SELECT A.URNK_KMMP_NOD_ID FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = #{tpcKmmpNodId})
				, (SELECT A.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = (SELECT B.URNK_KMMP_NOD_ID FROM LMS_CMS.BC_KMMP_NOD B WHERE B.KMMP_NOD_ID = #{tpcKmmpNodId}))
			 </if>
			 <if test="tpcKmmpNodId != null">
				, #{tpcKmmpNodId}
				, (SELECT A.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD A WHERE A.KMMP_NOD_ID = #{tpcKmmpNodId})
			 </if>
			 <if test="tcKmmpNodId == null and tpcKmmpNodId == null">
				, null
				, null
			 </if>
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertEaAiEvTsRnge */
	</insert>
	
	<insert id="insertEaAiToEvTsRnge" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		INSERT INTO LMS_LRM.EA_EV_TS_RNGE
		(
			EV_ID,
			TS_RNGE_SEQ_NO,
			LU_OPT_TXB_ID,
			LU_LRMP_NOD_ID,
			LU_LRMP_NOD_NM,
			MLU_OPT_TXB_ID,
			MLU_LRMP_NOD_ID,
			MLU_LRMP_NOD_NM,
			SLU_OPT_TXB_ID,
			SLU_LRMP_NOD_ID,
			SLU_LRMP_NOD_NM,
			TC_OPT_TXB_ID,
			TC_LRMP_NOD_ID,
			TC_LRMP_NOD_NM,
			TPC_ID,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		SELECT
			MAX(A.EV_ID) AS EV_ID,
			MAX(A.AI_TS_RNGE_SEQ_NO) AS TS_RNGE_SEQ_NO,
			MAX(A.OPT_TXB_ID) AS LU_OPT_TXB_ID,
			MAX(L1.LRMP_NOD_ID) AS LU_LRMP_NOD_ID,
			MAX(L1.LRMP_NOD_NM) AS LU_LRMP_NOD_NM,
			MAX(A.OPT_TXB_ID) AS MLU_OPT_TXB_ID,
			MAX(L2.LRMP_NOD_ID) AS MLU_LRMP_NOD_ID,
			MAX(L2.LRMP_NOD_NM) AS MLU_LRMP_NOD_NM,
			MAX(A.OPT_TXB_ID) AS SLU_OPT_TXB_ID,
			MAX(L3.LRMP_NOD_ID) AS SLU_LRMP_NOD_ID,
			MAX(L3.LRMP_NOD_NM) AS SLU_LRMP_NOD_NM,
			MAX(A.OPT_TXB_ID) AS TC_OPT_TXB_ID,
			MAX(L4.LRMP_NOD_ID) AS TC_LRMP_NOD_ID,
			MAX(L4.LRMP_NOD_NM) AS TC_LRMP_NOD_NM,
			A.TPC_KMMP_NOD_ID AS TPC_ID,
			MAX(A.CRTR_ID) AS CRTR_ID,
			MAX(A.CRT_DTM) AS CRT_DTM,
			MAX(A.MDFR_ID) AS MDFR_ID,
			MAX(A.MDF_DTM) AS MDF_DTM,
			MAX(A.DB_ID) AS DB_ID
		FROM
		    LMS_LRM.EA_AI_EV_TS_RNGE A
				INNER JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN G
					ON G.KMMP_NOD_ID = A.TC_KMMP_NOD_ID
				INNER JOIN LMS_CMS.BC_LRMP_NOD L4
					ON G.LRMP_NOD_ID = L4.LRMP_NOD_ID
				INNER JOIN LMS_CMS.BC_LRMP_NOD L3
					ON L4.URNK_LRMP_NOD_ID = L3.LRMP_NOD_ID
				INNER JOIN LMS_CMS.BC_LRMP_NOD L2
					ON L3.URNK_LRMP_NOD_ID = L2.LRMP_NOD_ID
				INNER JOIN LMS_CMS.BC_LRMP_NOD L1
					ON L2.URNK_LRMP_NOD_ID = L1.LRMP_NOD_ID
		WHERE
		    A.EV_ID = #{evId}
		GROUP BY
		    A.TPC_KMMP_NOD_ID
		    ON DUPLICATE KEY UPDATE
		    MDF_DTM = NOW()
		/* AI맞춤 평가생성시 학습맵 범위 등록 - AiRcmTsshQtm-Mapper.xml - insertEaAiToEvTsRnge */
	</insert>
	
	<insert id="insertEaEvRs" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		INSERT INTO LMS_LRM.EA_EV_RS
        	 ( EV_ID, USR_ID, EV_CMPL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
        VALUES 
        	 ( #{evId}
             , #{usrId}
             , 'N'
             , #{usrId}
             , now()
             , #{usrId}
             , now()
             , #{dbId} )
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertEaEvRs */
	</insert>
	
	<insert id="insertAiRcmTsshHst" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		INSERT INTO LMS_LRM.AI_RCM_TSSH_HST
			 ( USR_ID, EV_ID, EV_DV_CD, AI_DGN_RCM_MTHD_CD, AI_DGN_RCM_BS_CD, XPL_TM_SCNT
			 , DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		VALUES
			 ( #{usrId}
			 , #{evId}
			 , #{evDvCd}
			 , #{aiDgnRcmMthdCd}
			 , #{aiDgnRcmBsCd}
			 , NULL
			 , #{delYn}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertAiRcmTsshHst */
	</insert>
	
	<insert id="insertEaEvQtm" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		INSERT INTO LMS_LRM.EA_EV_QTM
			 ( EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM
			 , TPC_ID, DEL_YN, DEL_DTM, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{evId}
			 , #{qtmId}
			 , #{qtmOrdn}
			 , #{qtmDffdDvCd}
			 , #{qpDffdNm}
			 , #{tpcKmmpNodId}
			 , #{delYn}
			 , NULL
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		 ON DUPLICATE KEY UPDATE
		 	   MDFR_ID = #{usrId}
		 	 , MDF_DTM = NOW()
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertEaEvQtm */
	</insert>
	
	<insert id="insertAiRcmTsshQtmHst" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		INSERT INTO LMS_LRM.AI_RCM_TSSH_QTM_HST
			 ( USR_ID, EV_ID, QTM_ID, LRNR_VEL_TP_CD, QP_DFFD_DV_CD, CANS_YN, XPL_TM_SCNT
			 , AI_PRED_IANS_RN_CN, DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{usrId}
			 , #{evId}
			 , #{qtmId}
			 , #{lrnrVelTpCd}
			 , #{ctnDffdDvCd}
			 , NULL
			 , NULL
			 , NULL
			 , #{delYn}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		/* AI맞춤 문항추천 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertAiRcmTsshQtmHst */
	</insert>
	
	<select id="selectXplTmScnt" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmResponseDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		SELECT
		    EA.EV_ID
		  , EA.USR_ID
		  , QTM.QTM_ID
		  , ANW.SMT_ANW_VL
		  , ANW.CANS_YN
		  , ANW.XPL_TM_SCNT
		FROM
		    LMS_LRM.EA_EV EA
				JOIN LMS_LRM.EA_EV_QTM QTM
					ON EA.EV_ID = QTM.EV_ID
				LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW ANW
					ON QTM.EV_ID = ANW.EV_ID
					AND QTM.QTM_ID = ANW.QTM_ID
		WHERE
		    EA.EV_ID = #{evId}
			AND EA.USR_ID = #{usrId}
		/* AI 풀이내역 저장 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectXplTmScnt */
	</select>
	
	<update id="updateAiRcmTsshHst" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		UPDATE
		   LMS_LRM.AI_RCM_TSSH_HST
		SET
		   XPL_TM_SCNT = IFNULL(XPL_TM_SCNT, 0) + #{xplTmScnt}
		 , MDFR_ID = #{usrId}
		 , MDF_DTM = NOW()
		WHERE
		   USR_ID = #{usrId}
		   AND EV_ID = #{evId}
		/* AI 풀이내역 저장 - 이혜인 - AiRcmTsshQtm-Mapper.xml - updateAiRcmTsshHst */
	</update>
	
	<update id="updateAiRcmTsshQtmHst" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		UPDATE
		    LMS_LRM.AI_RCM_TSSH_QTM_HST
		SET
		   	CANS_YN = #{cansYn}
		  , XPL_TM_SCNT = #{xplTmScnt}
		  , MDFR_ID = #{usrId}
		  , MDF_DTM = NOW()
		WHERE
		    USR_ID = #{usrId}
			AND EV_ID = #{evId}
			AND QTM_ID = #{qtmId}
		/* AI 풀이내역 저장 - 이혜인 - AiRcmTsshQtm-Mapper.xml - updateAiRcmTsshQtmHst */
	</update>
	
	
	<select id="selectIansEvCount" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="integer">
		SELECT
		    COUNT(A.EV_ID)
		FROM (
			SELECT
			    EV.EV_ID
			FROM
			    LMS_LRM.EA_EV EV
					JOIN LMS_LRM.EA_EV_RS RS
						ON EV.EV_ID = RS.EV_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE RNGE
						ON EV.EV_ID = RNGE.EV_ID
			WHERE EV.EV_DV_CD = 'DE'
				AND EV.OPT_TXB_ID = #{optTxbId}
				AND RS.USR_ID = #{usrId}
				AND RS.EV_CMPL_YN = 'Y'
				AND RNGE.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			GROUP BY EV.EV_ID
		) A
		/* AI 오답문항 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectIansEvCount */
	</select>
	
	<select id="selectIansQtmList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(RNGE.LLU_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(RNGE.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(RNGE.TC_KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
			MAX(RNGE.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
			MAX(RNGE.EV_ID) AS EV_ID,
			QTM.QTM_ID,
			MAX(QTM.QTM_ORDN) AS QTM_ORDN,
			MAX(QTM.QTM_DFFD_DV_CD) AS CTN_DFFD_DV_CD,
			MAX(QTM.QP_DFFD_NM) AS CTN_DFFD_DV_NM,
			MAX(ANW.CANS_YN) AS CANS_YN,
			IFNULL(MAX(AUQP.AI_PRED_CANS_RT), 0.5) AS AI_PRED_CANS_RT
		FROM
		    LMS_LRM.EA_AI_EV_TS_RNGE RNGE
				JOIN LMS_LRM.EA_EV_RS RS
					ON RS.EV_ID = RNGE.EV_ID
				INNER JOIN LMS_LRM.EA_EV EV
					ON RS.EV_ID = EV.EV_ID
				JOIN LMS_LRM.EA_EV_QTM QTM
					ON EV.EV_ID = QTM.EV_ID
			<choose>
				<when test = 'iansEvHstYn != null and iansEvHstYn.equals("Y")'>
				</when>
				<otherwise>
					AND RNGE.TPC_KMMP_NOD_ID = QTM.TPC_ID
				</otherwise>
			</choose>
				JOIN LMS_LRM.EA_EV_QTM_ANW ANW
					ON EV.EV_ID = ANW.EV_ID
					AND QTM.QTM_ID = ANW.QTM_ID
				INNER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
					ON QTM.QTM_ID = AUQP.QTM_ID
					AND RNGE.MLU_KMMP_NOD_ID = AUQP.LU_KMMP_NOD_ID
					AND RNGE.TC_KMMP_NOD_ID = AUQP.TC_KMMP_NOD_ID
					AND AUQP.OPT_TXB_ID = #{optTxbId}
					AND RS.USR_ID = AUQP.USR_ID
		WHERE
		    RS.EV_CMPL_YN = 'Y'
			AND RNGE.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			<if test = 'tcKmmpNodId != null and tcKmmpNodId.length != 0'> <!-- 차시 -->
				AND RNGE.TC_KMMP_NOD_ID = #{tcKmmpNodId}
			</if>
			AND EV.OPT_TXB_ID = #{optTxbId}
			AND RS.USR_ID = #{usrId}
		<choose>
			<when test = 'iansEvHstYn != null and iansEvHstYn.equals("Y")'>
				AND EV.EV_DV_CD = 'DE'
				AND ANW.QTM_ID NOT IN (
								SELECT
								    ANW.QTM_ID
								FROM
								    LMS_LRM.EA_EV EV
										JOIN LMS_LRM.EA_AI_EV_TS_RNGE RNGE
											ON EV.EV_ID = RNGE.EV_ID
										JOIN LMS_LRM.EA_EV_QTM_ANW ANW
											ON EV.EV_ID = ANW.EV_ID
								WHERE
								    EV.EV_DV_CD = 'DE'
									AND EV.OPT_TXB_ID = #{optTxbId}
									AND RNGE.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
									AND ANW.CANS_YN = 'Y'
							)
			</when>
			<otherwise>
				AND EV.EV_DV_CD = 'AE'
				AND EV.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
				AND ANW.CANS_YN = 'N'
			</otherwise>
		</choose>
		GROUP BY QTM.QTM_ID
		/* AI 오답문항 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectIansQtmList */
	</select>
	
	<select id="selectCcptVdList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    'TPC' AS TPC_STAT
		  , MAX(RCSTN.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID
		  , MAX(RCSTN.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
		  , MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID
		  , MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID
		  , MAX(DPTH5.KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
		  , MAX(RCSTN.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM
		  , MAX(DPTH3.KMMP_NOD_NM) AS SLU_KMMP_NOD_NM
		  , MAX(DPTH4.KMMP_NOD_NM) AS TC_KMMP_NOD_NM
		  , MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM
		  , MAX(BALAC.AI_LRN_ATV_ID) AS AI_LRN_ATV_ID
		  <if test = 'sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H"))'>
			, MAX(BALAC.LRN_ATV_NM) AS AI_LRN_ATV_NM
		  </if>
		  <if test = '!(sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H")))'>
			, BALAC.LRN_ATV_NM AS AI_LRN_ATV_NM
		  </if>
		  , MAX(BACMD.CDN_PTH_NM) AS CDN_PTH_NM
		  , IFNULL(MAX(ALAS.LRN_ST_CD), 'NL') AS LRN_ST_CD
		  , MAX(ALAS.LRN_TM_SCNT) AS LRN_TM_SCNT
		  , MAX(AUTP.TPC_AVN) AS TPC_AVN
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN RCSTN
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON RCSTN.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND RCSTN.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				INNER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
					ON dpth5.KMMP_NOD_ID = AUTP.TPC_ID
		<!-- AND AUTP.TPC_AVN <![CDATA[<]]> 0.5  토픽숙련도 상관없이 모두 노출되도록 함 24.08.07 박지영팀장님확인--> 
					AND AUTP.USR_ID = #{usrId}
			<if test = 'lrnrVelTpCdFlag == null and sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
				INNER JOIN LMS_LRM.AI_LRNR_LV ALL2
					ON ALL2.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
					AND ALL2.USR_ID = #{usrId}
			</if>
				INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					ON dpth5.KMMP_NOD_ID = BALAC.KMMP_NOD_ID
					AND BALAC.DEL_YN = 'N'
					AND BALAC.EPS_YN = 'Y'
				INNER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD
					ON BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID
				LEFT OUTER JOIN LMS_LRM.AI_LRN_ATV_ST ALAS
					ON BALAC.AI_LRN_ATV_ID = ALAS.LRN_ATV_ID
		<!-- AND AUTP.USR_ID = ALAS.LRN_USR_ID -->
					AND ALAS.LRN_USR_ID = #{usrId}
		WHERE
		    RCSTN.OPT_TXB_ID = #{optTxbId}
			AND RCSTN.DPTH = 2
		<if test = 'mluKmmpNodId != null and !mluKmmpNodId.equals("")'>
			AND RCSTN.KMMP_NOD_ID = #{mluKmmpNodId}
		</if>
			AND (BALAC.CTN_TP_CD = 'PL' or BALAC.CTN_TP_CD = 'HT')
		<if test = 'lrnrVelTpCdFlag == null and sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			AND ALL2.LRNR_VEL_TP_CD = 'SL'
		</if>
		<if test = 'sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H"))'>
			GROUP BY BALAC.CTN_CD
		</if>
		<if test = '!(sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H")))'>
			GROUP BY BALAC.LRN_ATV_NM
		</if>
		UNION ALL
		SELECT
		    'PRE' AS TPC_STAT
		  , MAX(RCSTN.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID
		  , MAX(RCSTN.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
		  , MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID
		  , MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID
		  , MAX(BATKNM.LNKG_TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
		  , MAX(RCSTN.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM
		  , MAX(DPTH3.KMMP_NOD_NM) AS SLU_KMMP_NOD_NM
		  , MAX(DPTH4.KMMP_NOD_NM) AS TC_KMMP_NOD_NM
		  , MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM
		  , MAX(BALAC.AI_LRN_ATV_ID) AS AI_LRN_ATV_ID
		  <if test = 'sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H"))'>
			, MAX(BALAC.LRN_ATV_NM) AS AI_LRN_ATV_NM
		  </if>
		  <if test = '!(sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H")))'>
			, BALAC.LRN_ATV_NM AS AI_LRN_ATV_NM
		  </if>
		  , MAX(BACMD.CDN_PTH_NM) AS CDN_PTH_NM
		  , IFNULL(MAX(ALAS.LRN_ST_CD), 'NL') AS LRN_ST_CD
		  , MAX(ALAS.LRN_TM_SCNT) AS LRN_TM_SCNT
		  , MAX(AUTP.TPC_AVN) AS TPC_AVN
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN RCSTN
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON RCSTN.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND RCSTN.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				INNER JOIN LMS_CMS.BC_AI_TPC_KMMP_NOD_MPN BATKNM
					ON DPTH5.KMMP_NOD_ID = BATKNM.TPC_KMMP_NOD_ID
					AND BATKNM.TPC_LNKG_DV_CD = 'PR'
				INNER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
					ON dpth5.KMMP_NOD_ID = AUTP.TPC_ID
		<!-- AND AUTP.TPC_AVN <![CDATA[<]]> 0.5  토픽숙련도 상관없이 모두 노출되도록 함 24.08.07 박지영팀장님확인-->
					AND AUTP.USR_ID = #{usrId}
			<if test = 'lrnrVelTpCdFlag == null and sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
				INNER JOIN LMS_LRM.AI_LRNR_LV ALL2
					ON ALL2.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
					AND ALL2.USR_ID = #{usrId}
			</if>
				INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					ON BATKNM.LNKG_TPC_KMMP_NOD_ID = BALAC.KMMP_NOD_ID
					AND BALAC.DEL_YN = 'N'
					AND BALAC.EPS_YN = 'Y'
				INNER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD
					ON BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID
				LEFT OUTER JOIN LMS_LRM.AI_LRN_ATV_ST ALAS
					ON BALAC.AI_LRN_ATV_ID = ALAS.LRN_ATV_ID
					AND ALAS.LRN_USR_ID = #{usrId}
		<!-- AND AUTP.USR_ID = ALAS.LRN_USR_ID -->
		WHERE
		    RCSTN.OPT_TXB_ID = #{optTxbId}
			AND RCSTN.DPTH = 2
		<if test = 'mluKmmpNodId != null and !mluKmmpNodId.equals("")'>
			AND RCSTN.KMMP_NOD_ID = #{mluKmmpNodId}
		</if>
			AND (BALAC.CTN_TP_CD = 'PL' or BALAC.CTN_TP_CD = 'HT')
		<if test = 'lrnrVelTpCdFlag == null and sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			AND ALL2.LRNR_VEL_TP_CD = 'SL'
		</if>
		<if test = 'sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H"))'>
			GROUP BY BALAC.CTN_CD
		</if>
		<if test = '!(sbjCd != null and sbjCd.equals("EN") and schlGrdCd != null and (schlGrdCd.equals("M") or schlGrdCd.equals("H")))'>
			GROUP BY BALAC.LRN_ATV_NM
		</if>
		/* AI 개념영상 리스트 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectCcptVdList */
	</select>
	
	<select id="selectMluLrmpNodId" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="String">
		SELECT
		    DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN LKMAP
				    ON LKMAP.KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
				INNER JOIN (
					SELECT
						DPTH2.LRMP_NOD_ID AS MLU_LRMP_NOD_ID
					  , DPTH2.LRMP_NOD_NM AS MLU_LRMP_NOD_NM
					  , DPTH4.LRMP_NOD_ID AS TC_LRMP_NOD_ID
					  , DPTH4.LRMP_NOD_NM AS TC_LRMP_NOD_NM
					FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH2
						INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH3
							ON DPTH2.LRMP_NOD_ID = DPTH3.URNK_LRMP_NOD_ID
							AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
							AND DPTH3.DPTH = 3
						INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN DPTH4
							ON DPTH3.LRMP_NOD_ID = DPTH4.URNK_LRMP_NOD_ID
							AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
							AND DPTH4.DPTH = 4
					WHERE
						DPTH2.DPTH = 2 AND DPTH2.OPT_TXB_ID = #{optTxbId}
				) LRMP
					ON LRMP.TC_LRMP_NOD_ID = LKMAP.LRMP_NOD_ID 
		WHERE
		    LRMP.MLU_LRMP_NOD_ID = #{mluLrmpNodId} AND DPTH2.OPT_TXB_ID = #{optTxbId}
		LIMIT 1
		/* AI 학습맵중단원ID 지식맵중단원ID로 치환 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectMluLrmpNodId */
	</select>
	
	<insert id="insertAiQtmlyXplStas" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto">
		INSERT INTO LMS_LRM.AI_QTMLY_XPL_STAS
			 ( QTM_ID, LRNR_VEL_TP_CD, QP_DFFD_DV_CD, TPC_ID, LU_KMMP_NOD_ID, TC_KMMP_NOD_ID
			 , AVG_CANS_RT, TTL_XPL_STU_CNT, DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{qtmId}
			 , #{lrnrVelTpCd}
			 , #{ctnDffdDvCd}
			 , #{tpcKmmpNodId}
			 , #{mluKmmpNodId}
			 , #{tcKmmpNodId}
			 , #{avgCansRt}
			 , 1
			 , #{delYn}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		 ON DUPLICATE KEY UPDATE
		 	TTL_XPL_STU_CNT = TTL_XPL_STU_CNT + 1
		 	<if test = 'cansYn != null and cansYn.equals("Y")'>
		 		, AVG_CANS_RT = ((AVG_CANS_RT * TTL_XPL_STU_CNT) +1) / (TTL_XPL_STU_CNT +1)
		 	</if>
		 	<if test = 'cansYn != null and cansYn.equals("N")'>
		 		, AVG_CANS_RT = ((AVG_CANS_RT * TTL_XPL_STU_CNT) +0) / (TTL_XPL_STU_CNT +1)
		 	</if>
		/* AI 문항통계 저장 - 이혜인 - AiRcmTsshQtm-Mapper.xml - insertAiQtmlyXplStas */
	</insert>
	
	<select id="selectAiPredAvgCansRt03List" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    EE.EV_ID
		  , EER.USR_ID
		  , EEQ.QTM_ID
		  , EE.OPT_TXB_ID
		  , AUTP.LU_KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		  , AUTP.TC_KMMP_NOD_ID
		  , AUTP.TPC_ID AS TPC_KMMP_NOD_ID
		  , AUTP.AI_PRED_AVG_CANS_RT
		FROM LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER
			    ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ
			    ON EE.EV_ID = EEQ.EV_ID
			INNER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
			    ON EEQ.TPC_ID = AUTP.TPC_ID
			    AND EER.USR_ID = AUTP.USR_ID
		WHERE
		    EE.EV_ID = #{evId}
			AND EER.USR_ID = #{usrId}
			AND AUTP.AI_PRED_AVG_CANS_RT > 0.8
	</select>
	
	<update id="updateLuevCmplYn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		UPDATE
		    LMS_LRM.EA_AI_EV_TS_RNGE EAETR
		INNER JOIN LMS_LRM.EA_EV EE
			ON EE.EV_ID = EAETR.EV_ID
		INNER JOIN LMS_LRM.EA_EV_RS EER
			ON EE.EV_ID = EER.EV_ID
		SET EAETR.LUEV_CMPL_YN = #{luevCmplYn}
		  , EAETR.MDFR_ID = #{usrId}
		  , EAETR.MDF_DTM = NOW()
		WHERE
		    EER.USR_ID = #{usrId}
			AND EAETR.OPT_TXB_ID = #{optTxbId}
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		<if test = 'tcKmmpNodId != null and tcKmmpNodId.length != 0'> <!-- 차시 -->
			AND EAETR.TC_KMMP_NOD_ID = #{tcKmmpNodId}
		</if>
		<if test = 'tpcKmmpNodId != null and tpcKmmpNodId.length != 0'> <!-- 차시 -->
			AND EAETR.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
		</if>
	</update>
	
	<select id="selectEnLuevCmplYn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			EAETR.TC_KMMP_NOD_ID,
			MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
			IFNULL(MAX(EAETR.LUEV_CMPL_YN), '') AS LUEV_CMPL_YN,
			MAX(EER.USR_ID) AS USR_ID
		FROM LMS_LRM.EA_EV EV
			INNER JOIN LMS_LRM.EA_EV_RS EER
			    ON EV.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			    ON EV.EV_ID = EAETR.EV_ID
		WHERE
		    EER.USR_ID = #{usrId}
			AND EV.EV_DV_CD = 'AE'
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		GROUP BY EAETR.TC_KMMP_NOD_ID
	</select>
	<select id="selectMaLuevCmplYn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(AUTLO.USR_ID) AS USR_ID,
			MAX(AUTLO.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(AUTLO.LU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			AUTLO.TPC_KMMP_NOD_ID,
			MAX(AUTLO.LRN_ORDN) AS LRN_ORDN,
			IFNULL(MAX(EV.LUEV_CMPL_YN), 'N') AS LUEV_CMPL_YN
		FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO 
			LEFT OUTER JOIN (
				SELECT
				    EER.USR_ID
				  , EAETR.OPT_TXB_ID
				  , EAETR.MLU_KMMP_NOD_ID
				  , EAETR.TPC_KMMP_NOD_ID
				  , EAETR.LUEV_CMPL_YN
				FROM LMS_LRM.EA_EV EE
					INNER JOIN LMS_LRM.EA_EV_RS EER
					    ON EE.EV_ID = EER.EV_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					    ON EE.EV_ID = EAETR.EV_ID
			) EV
				ON EV.OPT_TXB_ID = AUTLO.OPT_TXB_ID
				AND EV.USR_ID = AUTLO.USR_ID
				AND AUTLO.LU_KMMP_NOD_ID = EV.MLU_KMMP_NOD_ID
				AND AUTLO.TPC_KMMP_NOD_ID = EV.TPC_KMMP_NOD_ID
		WHERE
		    AUTLO.USR_ID = #{usrId}
			AND AUTLO.OPT_TXB_ID = #{optTxbId}
			AND AUTLO.LU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND AUTLO.LRN_YN = 'Y'
		GROUP BY AUTLO.TPC_KMMP_NOD_ID
		ORDER BY MAX(AUTLO.LRN_ORDN)
	</select>
	
	<update id="updateSmtCmplYn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		UPDATE
		    LMS_LRM.EA_ASN_SMT EAS
		INNER JOIN LMS_LRM.EA_ASN_RNGE EAR
			ON EAS.ASN_ID = EAR.ASN_ID
		SET SMT_CMPL_YN = #{luevCmplYn}
		  , CMPL_LRN_CNT = #{cmplLrnCnt}
		  <if test = 'luevCmplYn != null and "Y".equals(luevCmplYn)'> , SMT_DTM = NOW() </if>
		WHERE
		    EAS.STU_USR_ID = #{usrId}
			AND EAR.LRN_TP_CD = 'AL'
			AND EAR.DEL_YN = 'N'
			AND EAR.LU_NOD_ID = #{mluKmmpNodId}
	</update>

	<!-- 같은 단원의 과제가 중복으로 발생한 경우가 있어, 데이터 보정 전 임시 처리로 최초의 발행한 과제만 불러오게 한다. -->
	<select id="selectSmtCmplAsnId" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="String">
		SELECT
		    EAS.ASN_ID
	    FROM LMS_LRM.EA_ASN_SMT EAS
	    	INNER JOIN LMS_LRM.EA_ASN_RNGE EAR
	    	    ON EAS.ASN_ID = EAR.ASN_ID
	    WHERE
	        EAS.STU_USR_ID = #{usrId}
			AND EAR.LRN_TP_CD = 'AL'
			AND EAR.LU_NOD_ID = #{mluKmmpNodId}
			AND SMT_CMPL_YN = 'Y'
			AND EAR.DEL_YN = 'N'
		ORDER BY ASN_ID
		LIMIT 1
	</select>
	
	<select id="selectAiPredAvgCansRtList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(BKN.SRT_ORDN) AS SRT_ORDN,
			MAX(DPTH2.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(DPTH2.ORGL_ORDN) AS ORGL_ORDN,
			MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			MAX(AUTP.AI_PRED_AVG_CANS_RT) AS AI_PRED_AVG_CANS_RT,
			MAX(AUTP.TPC_AVN) AS TPC_AVN
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
				ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
				AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				AND DPTH3.DPTH = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
				ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
				AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				AND DPTH4.DPTH = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
				ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
				AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
				AND DPTH5.DPTH = 5
			INNER JOIN LMS_CMS.BC_KMMP_NOD BKN
			    ON BKN.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
				ON AUTP.TPC_ID = DPTH5.KMMP_NOD_ID
				AND AUTP.USR_ID = #{usrId}
				AND AUTP.OPT_TXB_ID = #{optTxbId}
		<if test ='tpcAvnStr != null and tpcAvnStr.equals("03") and sbjCd != null and sbjCd.equals("MA")'>
			LEFT OUTER JOIN (
				SELECT
					EEQ.TPC_ID,
					MAX(EEQA.CANS_YN) AS CANS_YN
				FROM LMS_LRM.EA_EV EE
					INNER JOIN LMS_LRM.EA_EV_RS EER
						ON EE.EV_ID = EER.EV_ID
					INNER JOIN LMS_LRM.EA_EV_QTM EEQ
						ON EE.EV_ID = EEQ.EV_ID
					INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
						ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
						ON EE.EV_ID = EAETR.EV_ID AND EEQ.TPC_ID = EAETR.TPC_KMMP_NOD_ID AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
				WHERE
				    EE.OPT_TXB_ID = #{optTxbId}
					AND EE.EV_DV_CD = 'AE'
					AND EE.EV_DTL_DV_CD = 'OV'
					AND EER.USR_ID = EEQA.USR_ID
					AND EEQA.USR_ID = #{usrId}
				GROUP BY EEQ.TPC_ID
				ORDER BY MAX(EEQA.CANS_YN)
			) OVANSWER
			ON OVANSWER.TPC_ID = DPTH5.KMMP_NOD_ID
		</if>
			LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
				ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
				AND BALAC.CTN_TP_CD = 'QU'
				AND BALAC.DEL_YN = 'N'
		WHERE
		    DPTH2.OPT_TXB_ID = #{optTxbId}
			AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
			AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY BALAC.KMMP_NOD_ID
		<choose>
			<when test='tpcAvnStr != null and tpcAvnStr.equals("03") and sbjCd != null and !sbjCd.equals("MA")'>
				<!-- AND AUTP.TPC_AVN IS NOT NULL -->
				ORDER BY MAX(AUTP.TPC_AVN), MAX(DPTH2.ORGL_ORDN), MAX(DPTH3.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN), MAX(DPTH5.ORGL_ORDN)
				<!-- LIMIT 3 -->
			</when>
			<when test='tpcAvnStr != null and tpcAvnStr.equals("03") and sbjCd != null and sbjCd.equals("MA")'>
				ORDER BY CASE WHEN MAX(OVANSWER.CANS_YN) IS NULL THEN 1 WHEN MAX(OVANSWER.CANS_YN) = 'N' THEN 0 ELSE 2 END, MAX(AUTP.TPC_AVN), MAX(DPTH2.ORGL_ORDN), MAX(DPTH3.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN), MAX(DPTH5.ORGL_ORDN)
			</when>
			<otherwise>
				<!-- AND (AUTP.TPC_AVN <![CDATA[<]]> 0.8 OR AUTP.TPC_AVN IS NULL) -->
				ORDER BY MAX(DPTH2.ORGL_ORDN), MAX(DPTH3.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN), MAX(DPTH5.ORGL_ORDN)
			</otherwise>
		</choose>
	</select>
	
	<select id="selectLrnTpcCnt" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="int">
		SELECT
			COUNT(DISTINCT DPTH5.KMMP_NOD_ID)
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				INNER JOIN LMS_CMS.BC_KMMP_NOD BKN
				    ON BKN.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
				LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
					ON AUTP.TPC_ID = DPTH5.KMMP_NOD_ID
					AND AUTP.USR_ID = #{usrId}
					AND AUTP.OPT_TXB_ID = #{optTxbId}
				LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
					AND BALAC.CTN_TP_CD = 'QU'
					AND BALAC.DEL_YN = 'N'
					AND BALAC.KMMP_NOD_ID IS NOT NULL
		WHERE
		    DPTH2.OPT_TXB_ID = #{optTxbId}
			AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
			AND (AUTP.TPC_AVN <![CDATA[<]]> 0.8 AND AUTP.TPC_AVN IS NOT NULL)
	</select>
	
	<insert id="insertAiUsrlyTpcLrnOrdn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		INSERT INTO LMS_LRM.AI_USRLY_TPC_LRN_ORDN
			 ( USR_ID, OPT_TXB_ID, LU_KMMP_NOD_ID, TPC_KMMP_NOD_ID, LRN_ORDN
			 , AI_DGN_EV_TPC_AVN, AI_DGN_EV_PRED_AVG_CANS_RT, LRN_YN
			 , DEL_YN, NTN_TPC_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		VALUES
			 ( #{usrId}
			 , #{optTxbId}
			 , #{mluKmmpNodId}
			 , #{tpcKmmpNodId}
			 , #{lrnOrdn}
			 , #{aiDgnEvTpcAvn}
			 , #{aiDgnEvPredAvgCansRt}
			 , #{lrnYn}
			 , 'N'
			 , #{ntnTpcYn}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		ON DUPLICATE KEY UPDATE
		 	LRN_ORDN = #{lrnOrdn}
		  , AI_DGN_EV_TPC_AVN = #{aiDgnEvTpcAvn}
		  , AI_DGN_EV_PRED_AVG_CANS_RT = #{aiDgnEvPredAvgCansRt}
		  , LRN_YN = #{lrnYn}
		  , DEL_YN = 'N'
		  , NTN_TPC_YN = #{ntnTpcYn}
		  , MDFR_ID = #{usrId}
		  , MDF_DTM = NOW()
	</insert>
	
	<select id="selectTpcPgrsRt" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(AUTLO.LRN_ORDN) AS LRN_ORDN,
			IFNULL(MAX(AUTLO.AI_DGN_EV_TPC_AVN), 0.5) AS AI_DGN_EV_TPC_AVN,
			MAX(DPTH2.URNK_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(AUTLO.LU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
			MAX(DPTH3.KMMP_NOD_ID) AS SLU_KMMP_NOD_ID,
			MAX(DPTH4.KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
			AUTLO.TPC_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			MAX(EV.EV_ID) AS EV_ID,
			MAX(EV.EV_DV_CD) AS EV_DV_CD,
			MAX(EV.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
			IFNULL(MAX(EV.TPC_CMPL_YN), 'N') AS TPC_CMPL_YN,
			IFNULL(MIN(EV.QTM_CNT), 0) AS QTM_CNT,
			IFNULL(MIN(EV.QTM_ANW_CNT), 0) AS QTM_ANW_CNT,
			IFNULL(MIN(EV_CMPL_YN_CNT), 0) AS EV_CMPL_YN_CNT,
			MAX(AUTLO.USR_ID) AS USR_ID,
			MAX(AUTLO.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(BATKNM.LNKG_TPC_KMMP_NOD_ID) AS LNKG_TPC_KMMP_NOD_ID,
			MAX(VD.CDN_PTH_NM) AS CDN_PTH_NM,
			MAX(VD.CTN_TP_CD) AS CTN_TP_CD,
			MAX(AUTLO.NTN_TPC_YN) AS NTN_TPC_YN
		FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
			    ON DPTH2.KMMP_NOD_ID = AUTLO.LU_KMMP_NOD_ID
			    AND DPTH2.OPT_TXB_ID = AUTLO.OPT_TXB_ID
			    AND DPTH2.DPTH = 2
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
			    ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
			    AND DPTH3.OPT_TXB_ID = AUTLO.OPT_TXB_ID
			    AND DPTH3.DPTH = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
			    ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
			    AND DPTH4.OPT_TXB_ID = AUTLO.OPT_TXB_ID
			    AND DPTH4.DPTH = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
			    ON AUTLO.TPC_KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
			    AND DPTH5.OPT_TXB_ID = AUTLO.OPT_TXB_ID
			    AND DPTH5.DPTH = 5
			LEFT OUTER JOIN ( <!-- 해당토픽의 마지막 평가 -->
				SELECT
				    MAX(EER.USR_ID) AS USR_ID
				  , MAX(EE.EV_ID) AS EV_ID
				  , MAX(EE.EV_DV_CD) AS EV_DV_CD
				  , MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD
				  , MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID
				  , MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID
				  , MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
				  , IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS TPC_CMPL_YN
				  , COUNT(EEQ.QTM_ID) AS QTM_CNT
				  , COUNT(EEQA.QTM_ID) AS QTM_ANW_CNT
				  , SUM(EER.EV_CMPL_YN = 'Y') EV_CMPL_YN_CNT
				FROM LMS_LRM.EA_EV EE
					INNER JOIN LMS_LRM.EA_EV_RS EER
					    ON EE.EV_ID = EER.EV_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					    ON EE.EV_ID = EAETR.EV_ID
					INNER JOIN LMS_LRM.EA_EV_QTM EEQ
					    ON EE.EV_ID = EEQ.EV_ID
					    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
					LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
					    ON EE.EV_ID = EEQA.EV_ID
					    AND EEQ.QTM_ID = EEQA.QTM_ID
				WHERE
				    EE.EV_DV_CD = 'AE'
					AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
					AND EE.OPT_TXB_ID = #{optTxbId}
					AND EER.USR_ID = #{usrId}
					AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
				GROUP BY EEQ.EV_ID
				ORDER BY CAST(SUBSTRING(MAX(EE.EV_DTL_DV_CD), 2) AS UNSIGNED) DESC
			) EV
			    ON AUTLO.OPT_TXB_ID = EV.OPT_TXB_ID
			    AND AUTLO.USR_ID = EV.USR_ID
			    AND AUTLO.LU_KMMP_NOD_ID = EV.MLU_KMMP_NOD_ID
			    AND AUTLO.TPC_KMMP_NOD_ID = EV.TPC_KMMP_NOD_ID
			LEFT OUTER JOIN LMS_CMS.BC_AI_TPC_KMMP_NOD_MPN BATKNM
			    ON AUTLO.TPC_KMMP_NOD_ID = BATKNM.TPC_KMMP_NOD_ID
			    AND BATKNM.TPC_LNKG_DV_CD = 'PR'
			LEFT OUTER JOIN (
				SELECT
				    BACMD.CDN_PTH_NM
				  , BALAC.KMMP_NOD_ID
				  , BALAC.CTN_TP_CD
				FROM LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					INNER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD
					    ON BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID
				WHERE
				    BALAC.CTN_TP_CD = 'PL'
				    AND BALAC.DEL_YN = 'N'
			) VD
			    ON VD.KMMP_NOD_ID = AUTLO.TPC_KMMP_NOD_ID
		WHERE
		    AUTLO.OPT_TXB_ID = #{optTxbId}
			AND AUTLO.USR_ID = #{usrId}
			AND AUTLO.LU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND AUTLO.LRN_YN = 'Y'
		GROUP BY AUTLO.TPC_KMMP_NOD_ID
		ORDER BY MAX(AUTLO.LRN_ORDN)
		/* AI 수학 개별문항추천 - 토픽진행률 체크 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectTpcPgrsRt */
	</select>
	
	<select id="selectAiMaTpcRcmQtmList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		    AUTLO.LRN_ORDN
		  , AUTLO.LU_KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		  , AUTLO.TPC_KMMP_NOD_ID
		  , IFNULL(AUTLO.AI_DGN_EV_TPC_AVN, 0.5) AS AI_DGN_EV_TPC_AVN
		  , AUTLO.USR_ID
		  , AUTLO.OPT_TXB_ID
		  , BALAC.CTN_CD AS QTM_ID
		  , IFNULL(AUQP.AI_PRED_CANS_RT, 0.5) AS AI_PRED_CANS_RT
		  , QQ.QP_DFFD_CD AS CTN_DFFD_DV_CD
		  , QTMDFFD.CM_CD_NM AS CTN_DFFD_DV_NM
		  , BALAC.CTN_TP_CD
		FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO
			INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
			    ON AUTLO.TPC_KMMP_NOD_ID = BALAC.KMMP_NOD_ID
			    AND BALAC.DEL_YN = 'N'
			INNER JOIN LMS_CMS.QP_QTM QQ
			    ON BALAC.CTN_CD = QQ.QP_QTM_ID
			INNER JOIN LMS_LRM.CM_CM_CD QTMDFFD
				ON QTMDFFD.URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD'
				AND QTMDFFD.CM_CD = QQ.QP_DFFD_CD
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
			    ON AUTLO.LU_KMMP_NOD_ID = AUTP.LU_KMMP_NOD_ID
			    AND AUTLO.TPC_KMMP_NOD_ID = AUTP.TPC_ID
			    AND AUTP.USR_ID = AUTLO.USR_ID
				AND AUTP.OPT_TXB_ID = #{optTxbId}
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
			    ON AUTLO.USR_ID = AUQP.USR_ID
			    AND QQ.QP_QTM_ID = AUQP.QTM_ID
			    AND AUTP.TPC_ID = AUQP.TPC_ID
				AND AUQP.OPT_TXB_ID = #{optTxbId}
		WHERE
		    AUTLO.OPT_TXB_ID = #{optTxbId}
			AND AUTLO.USR_ID = #{usrId}
			AND AUTLO.LU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND AUTLO.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
			AND AUTLO.LRN_YN = 'Y'
			AND BALAC.CTN_TP_CD = 'QU'
		ORDER BY AUTLO.LRN_ORDN
		/* AI 수학 개별문항추천 - 토픽 문항리스트 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectAiMaTpcRcmQtmList */
	</select>

	<select id="selectAiMaTpcPreQtmList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(DPTH2.KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(BATKNM.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
			MAX(BATKNM.LNKG_TPC_KMMP_NOD_ID) AS LNKG_TPC_KMMP_NOD_ID,
			BALAC.CTN_CD AS QTM_ID,
			IFNULL(MAX(AUQP.AI_PRED_CANS_RT), 0.5) AS AI_PRED_CANS_RT,
			MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD,
			MAX(CCC.CM_CD_NM) AS CTN_DFFD_DV_NM,
			MAX(BALAC.CTN_TP_CD) AS CTN_TP_CD
		FROM
			LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
				INNER JOIN LMS_CMS.QP_QTM QQ
					ON BALAC.CTN_CD = QQ.QP_QTM_ID
				LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP					
					ON AUTP.USR_ID=#{usrId}
					AND AUTP.OPT_TXB_ID = #{optTxbId}
					AND BALAC.KMMP_NOD_ID = AUTP.TPC_ID
				LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
					ON AUQP.USR_ID=#{usrId}
					AND AUQP.OPT_TXB_ID = #{optTxbId}
					AND QQ.QP_QTM_ID = AUQP.QTM_ID
					AND AUTP.TPC_ID = AUQP.TPC_ID
				INNER JOIN LMS_CMS.BC_AI_TPC_KMMP_NOD_MPN BATKNM
					ON BALAC.KMMP_NOD_ID = BATKNM.LNKG_TPC_KMMP_NOD_ID
					AND BATKNM.TPC_LNKG_DV_CD = 'PR'
				INNER JOIN LMS_CMS.BC_KMMP_NOD DPTH5
					ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
					AND DPTH5.DPTH = 5
				INNER JOIN LMS_CMS.BC_KMMP_NOD DPTH4
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.KMMP_ID = DPTH5.KMMP_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_CMS.BC_KMMP_NOD DPTH3
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.KMMP_ID = DPTH4.KMMP_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_CMS.BC_KMMP_NOD DPTH2
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.KMMP_ID = DPTH3.KMMP_ID
					AND DPTH2.DPTH = 2
				INNER JOIN LMS_LRM.CM_CM_CD CCC
					ON CCC.CM_CD = QQ.QP_DFFD_CD
					AND CCC.URNK_CM_CD = 'QTM_PLTFM_DFFD_DV_CD'
		WHERE
			1 = 1
			AND BALAC.DEL_YN = 'N'
			AND BALAC.KMMP_NOD_ID = #{lnkgTpcKmmpNodId}
			AND BATKNM.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
			AND BALAC.CTN_TP_CD = 'QU'
		GROUP BY
			BALAC.CTN_CD
		/* AI 수학 개별문항추천 - 이전학교급 토픽 문항리스트 조회 - 강성현 - AiRcmTsshQtm-Mapper.xml - selectAiMaTpcPreQtmList */
	</select>
	
	<select id="selectBfQtmInfo" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			INFO.QTM_ORDN AS QTM_ORDN,
			INFO.USR_ID AS USR_ID,
			INFO.EV_ID AS EV_ID,
			INFO.EV_DV_CD AS EV_DV_CD,
			INFO.EV_DTL_DV_CD AS EV_DTL_DV_CD,
			INFO.OPT_TXB_ID AS OPT_TXB_ID,
			INFO.QTM_ID AS QTM_ID,
			IFNULL(INFO.QP_DFFD_CD, '') AS CTN_DFFD_DV_CD,
			IFNULL(INFO.AI_PRED_CANS_RT, 0.5) AS AI_PRED_CANS_RT,
			IFNULL(INFO.CANS_YN, 'N') AS CANS_YN,
			SUM.QTM_CNT,
			SUM.CANS_CNT
		FROM (
			SELECT
			    EEQ.QTM_ORDN
		      , EER.USR_ID
		      , EE.EV_ID
		      , EE.EV_DV_CD
		      , EE.EV_DTL_DV_CD
		      , EE.OPT_TXB_ID
		      , EEQ.QTM_ID
		      , EEQA.CANS_YN
		      , QQ.QP_DFFD_CD
		      , AUQP.AI_PRED_CANS_RT
			FROM
			    LMS_LRM.EA_EV EE
					INNER JOIN LMS_LRM.EA_EV_RS EER
					    ON EE.EV_ID = EER.EV_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					    ON EE.EV_ID = EAETR.EV_ID
					INNER JOIN LMS_LRM.EA_EV_QTM EEQ
					    ON EE.EV_ID = EEQ.EV_ID
					    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
					INNER JOIN LMS_CMS.QP_QTM QQ
					    ON EEQ.QTM_ID = QQ.QP_QTM_ID
					LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
					    ON EEQ.QTM_ID = AUQP.QTM_ID
					    AND AUQP.USR_ID = EER.USR_ID
					    AND AUQP.OPT_TXB_ID = #{optTxbId}
					LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
					    ON EE.EV_ID = EEQA.EV_ID
					    AND EEQ.QTM_ID = EEQA.QTM_ID
			WHERE
			    EER.USR_ID = #{usrId}
				AND EE.EV_DV_CD = 'AE'
				AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
				AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
				AND EAETR.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
			ORDER BY
			    EE.EV_ID, EEQ.QTM_ORDN
		) INFO,
		(
		    SELECT
		        COUNT(EEQA.CANS_YN) AS QTM_CNT
			  , SUM(CASE WHEN EEQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT
			FROM
			    LMS_LRM.EA_EV EE
					INNER JOIN LMS_LRM.EA_EV_RS EER
					    ON EE.EV_ID = EER.EV_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					    ON EE.EV_ID = EAETR.EV_ID
					INNER JOIN LMS_LRM.EA_EV_QTM EEQ
					    ON EE.EV_ID = EEQ.EV_ID
					    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
					LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
					    ON EE.EV_ID = EEQA.EV_ID
					    AND EEQ.QTM_ID = EEQA.QTM_ID
			WHERE
			    EER.USR_ID = #{usrId}
				AND EE.EV_DV_CD = 'AE'
				AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
				AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
				AND EAETR.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
			ORDER BY
				EE.EV_ID, EEQ.QTM_ORDN
		) SUM
		ORDER BY
			INFO.EV_DTL_DV_CD, INFO.QTM_ORDN
		/* AI 수학 개별문항추천 - 이전문항 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectBfQtmInfo */
	</select>
	
	<select id="selectEaEvInfoByEvId" parameterType="Integer" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
		   EE.EV_ID
		 , EE.EV_DV_CD
		 , EE.EV_DTL_DV_CD
		 , EER.USR_ID
		 , EER.EV_CMPL_YN
		 , EAETR.OPT_TXB_ID
		 , EAETR.LLU_KMMP_NOD_ID
		 , EAETR.LLU_KMMP_NOD_NM
		 , EAETR.MLU_KMMP_NOD_ID
		 , EAETR.MLU_KMMP_NOD_NM
		 , EAETR.TC_KMMP_NOD_ID
		 , EAETR.TC_KMMP_NOD_NM
		 , EAETR.TPC_KMMP_NOD_ID
		 , EAETR.TPC_KMMP_NOD_NM
		FROM
		    LMS_LRM.EA_EV EE
				INNER JOIN LMS_LRM.EA_EV_RS EER
					ON EE.EV_ID = EER.EV_ID
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					ON EE.EV_ID = EAETR.EV_ID
		WHERE EE.EV_ID = #{evId}
	</select>
	
	<select id="selectKmmpNodNm" parameterType="String" resultType="String">
		SELECT
		    UPPER(KMMP_NOD_NM) AS KMMP_NOD_NM
		FROM
		    LMS_CMS.BC_KMMP_NOD BKN
		WHERE
		    KMMP_NOD_ID = #{kmmpNodId}
		LIMIT 1
	</select>
	
	<select id="selectKmmpNodId" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto" resultType="String">
		select n.URNK_KMMP_NOD_ID from lms_cms.bc_kmmp_nod n where n.KMMP_NOD_ID = #{tpcKmmpNodId} and n.DPTH = 5 and n.DEL_YN ='N' limit 1;
	</select>
	
	<select id="selectTpcAvnList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(DPTH2.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(AUTP.USR_ID) AS USR_ID,
			DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID,
			MAX(DPTH5.KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			MAX(AUTP.TPC_AVN) AS TPC_AVN,
			MAX(AUTP.AI_PRED_AVG_CANS_RT) AS AI_PRED_AVG_CANS_RT,
			IFNULL(MAX(ALL2.LRNR_VEL_TP_CD), 'NM') AS LRNR_VEL_TP_CD
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				INNER JOIN LMS_CMS.BC_KMMP_NOD BKN
					ON BKN.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
				LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
					ON AUTP.TPC_ID = DPTH5.KMMP_NOD_ID
					AND AUTP.USR_ID = #{usrId}
					AND AUTP.OPT_TXB_ID = DPTH2.OPT_TXB_ID
				LEFT OUTER JOIN LMS_LRM.AI_LRNR_LV ALL2
					ON ALL2.USR_ID = #{usrId}
					AND ALL2.LU_KMMP_NOD_ID =
					<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
						DPTH2.KMMP_NOD_ID
					</if>
					<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
						DPTH4.KMMP_NOD_ID
					</if>
					AND ALL2.OPT_TXB_ID = DPTH2.OPT_TXB_ID
				LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
					AND BALAC.CTN_TP_CD = 'QU'
					AND BALAC.DEL_YN = 'N'
		WHERE
		    DPTH2.OPT_TXB_ID = #{optTxbId}
			AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
			AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY
		    DPTH5.KMMP_NOD_ID
		ORDER BY
			MAX(DPTH2.ORGL_ORDN), MAX(DPTH3.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN), MAX(DPTH5.ORGL_ORDN)
		/* AI 토픽숙련도 저장 - 단원 내 모든 토픽순서 조회 - 이혜인 - AiRcmTsshQtm-Mapper.xml - selectTpcAvnList */
	</select>

	<select id="selectKmmpNodCount" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="int">
		SELECT
			COUNT(*)
		FROM
			LMS_LRM.AI_KMMP_NOD_RCSTN AKNR
		WHERE
		    OPT_TXB_ID = #{optTxbId}
			AND KMMP_NOD_ID = #{kmmpNodId}
	</select>

	<select id="selectTxbInfo" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT SBJ_CD, SCHL_GRD_CD FROM LMS_CMS.BC_TXB BT WHERE TXB_ID = #{txbId}
		/* 과목조회 - 이혜인 -  AlLrnw-Mapper.xml - selectTxbInfo */
	</select>

	<select id="selectEnLrnrVelTpCd" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			ALL2.USR_ID,
			ALL2.OPT_TXB_ID,
			DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID,
			DPTH1.KMMP_NOD_NM AS LLU_KMMP_NOD_NM,
			DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
			DPTH2.KMMP_NOD_NM AS MLU_KMMP_NOD_NM,
			DPTH3.KMMP_NOD_ID AS SLU_KMMP_NOD_ID,
			DPTH3.KMMP_NOD_NM AS SLU_KMMP_NOD_NM,
			DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
			DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM,
			ALL2.LRNR_VEL_TP_CD
		FROM
			LMS_LRM.AI_LRNR_LV ALL2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON ALL2.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
					AND ALL2.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH4.URNK_KMMP_NOD_ID = DPTH3.KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
					ON DPTH3.URNK_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
					ON DPTH2.URNK_KMMP_NOD_ID = DPTH1.KMMP_NOD_ID
					AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		WHERE 1=1
		  AND ALL2.USR_ID = #{usrId}
		  AND ALL2.OPT_TXB_ID = #{optTxbId}
		  AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
		  AND ALL2.DEL_YN = 'N'
		ORDER BY
			DPTH1.RCSTN_ORDN, DPTH2.RCSTN_ORDN, DPTH3.RCSTN_ORDN, DPTH4.RCSTN_ORDN
	</select>

	<select id="selectEvCmplList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
			MAX(EAETR.LLU_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(EAETR.LLU_KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(EAETR.MLU_KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
			MAX(EAETR.TC_KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
			MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'>
			MAX(EE.EV_ID) AS EV_ID,
			EAETR.TPC_KMMP_NOD_ID,
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'>
			EE.EV_ID,
			MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
		</if>
			MAX(EAETR.TPC_KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			MAX(EAETR.LUEV_CMPL_YN) AS LUEV_CMPL_YN
		FROM
			LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER
				ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
				ON EE.EV_ID = EAETR.EV_ID
				AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE 1=1
			AND EE.EV_DV_CD = 'AE'
			AND EER.USR_ID = #{usrId}
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
			AND EAETR.TPC_KMMP_NOD_ID IN (
			<foreach collection="kmmpNodIdList" index="index" item="item" separator=",">
				#{item}
			</foreach>
			)
			GROUP BY EAETR.TPC_KMMP_NOD_ID
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			AND EAETR.TC_KMMP_NOD_ID = #{tcKmmpNodId}
			GROUP BY EE.EV_ID
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'>
			ORDER BY MAX(EAETR.TC_KMMP_NOD_ID), EAETR.TPC_KMMP_NOD_ID, MAX(EE.EV_DTL_DV_CD) DESC, MAX(EE.EV_ID)
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'>
			ORDER BY MAX(EAETR.TC_KMMP_NOD_ID), MAX(EAETR.TPC_KMMP_NOD_ID), MAX(EE.EV_DTL_DV_CD) DESC, EE.EV_ID
		</if>
	</select>

	<insert id="updateLrnPgrsProf" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		INSERT INTO LMS_LRM.AI_LRN_PGRS_PROF
		(
		  USR_ID
		, OPT_TXB_ID
		, MLU_KMMP_NOD_ID
		, AI_LRN_PGRS_RT
		, CRTR_ID
		, CRT_DTM
		, MDFR_ID
		, MDF_DTM
		, DB_ID
		) VALUES (
		  #{usrId}
		, #{optTxbId}
		, #{mluKmmpNodId}
		, #{aiLrnPgrsRt}
		, #{usrId}
		, NOW()
		, #{usrId}
		, NOW()
		, #{dbId}
		) ON DUPLICATE KEY UPDATE
		AI_LRN_PGRS_RT = #{aiLrnPgrsRt}
		, MDFR_ID = #{usrId}
		, MDF_DTM = NOW()
		/* AI맞춤 학습진도율 - 강성현 - AiRcmTsshQtm-Mapper.xml - updateLrnPgrsProf */
	</insert>

	<select id="selectEvTarget" resultType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto">
		SELECT
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(EAETR.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(BT.SCHL_GRD_CD) AS SCHL_GRD_CD,
			MAX(BT.SBJ_CD) AS SBJ_CD,
			MAX(ALPP.AI_LRN_PGRS_RT) AS AI_LRN_PGRS_RT
		FROM LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			 INNER JOIN LMS_LRM.EA_EV_RS EER
				ON EAETR.EV_ID = EER.EV_ID
				AND EER.USR_ID = EAETR.CRTR_ID
			 INNER JOIN LMS_LRM.CM_OPT_TXB COT2
				ON EAETR.OPT_TXB_ID = COT2.OPT_TXB_ID
			 INNER JOIN LMS_CMS.BC_TXB BT
				ON COT2.TXB_ID = BT.TXB_ID
			 LEFT OUTER JOIN LMS_LRM.AI_LRN_PGRS_PROF ALPP
				ON ALPP.USR_ID = EER.USR_ID
				AND ALPP.OPT_TXB_ID = EAETR.OPT_TXB_ID
				AND ALPP.MLU_KMMP_NOD_ID = EAETR.MLU_KMMP_NOD_ID
		WHERE
			ALPP.AI_LRN_PGRS_RT IS NULL
		GROUP BY
			EAETR.EV_ID
		LIMIT 100
	</select>
	
</mapper>