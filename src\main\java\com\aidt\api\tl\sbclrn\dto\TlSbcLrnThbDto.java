package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-12 16:20:19
 * @modify date 2024-04-12 16:20:19
 * @desc TlSbcLrnThbDto 교과학습 교과서썸네일정보
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnThbDto {
    /** 썸네일CDN(PC용) */
    @Parameter(name="썸네일CDN(PC용)")
    private String pcCdnPthNm;
    /** 썸네일대체텍스트(PC용) */
    @Parameter(name="썸네일대체텍스트(PC용)")
    private String pcAltnTxtCn;
    /** 썸네일CDN(모바일용) */
    @Parameter(name="썸네일CDN(모바일용)")
    private String moCdnPthNm;
    /** 썸네일대체텍스트(모바일용) */
    @Parameter(name="썸네일대체텍스트(모바일용)")
    private String moAltnTxtCn;
    /** 썸네일CDN(태블릿용) */
    @Parameter(name="썸네일CDN(태블릿용)")
    private String taCdnPthNm;
    /** 썸네일대체텍스트(태블릿용) */
    @Parameter(name="썸네일대체텍스트(태블릿용)")
    private String taAltnTxtCn;
    /** 학습목표 */
    @Parameter(name="학습목표")
    private String lrnGoalCn;
}
