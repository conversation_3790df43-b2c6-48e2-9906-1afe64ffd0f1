package com.aidt.api.at.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "CM_USR insert dto")
public class UsrDto {

	private String usrId;
	private String kerisUsrId;
	private String usrTpCd;
	private String usrStCd;
	private String claId;
	private String flnStCd;
	private String ntrYn;
	private String kerisTermAgrYn;
	private String kerisTermAgrDt;
	private String lrnrVelTpCd;
	private String crtrId;
	private String mdfrId;
	private String dbId;
}
