<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.mg">
	
	
		<!-- 학습맵 대단원 ID와 매핑된 지식맵 중단원 ID 조회 -->
	<select id="selectKmmpNodIdByLrmpNodId" parameterType="String" resultType="String">
		/** EaAsnCom-Mapper.xml - selectKmmpNodIdByLrmpNodId */
		SELECT
			DISTINCT L2.KMMP_NOD_ID AS kmmpNodId
		FROM LMS_CMS.BC_KMMP_NOD L2
		INNER JOIN LMS_CMS.BC_KMMP_NOD L3
		    ON L2.KMMP_NOD_ID = L3.URNK_KMMP_NOD_ID
		INNER JOIN LMS_CMS.BC_KMMP_NOD L4
		    ON L3.KMMP_NOD_ID = L4.URNK_KMMP_NOD_ID
		INNER JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN MAP
		    ON MAP.KMMP_NOD_ID = L4.KMMP_NOD_ID
		INNER JOIN LMS_CMS.BC_LRMP_NOD N4
		    ON N4.LRMP_NOD_ID = MAP.LRMP_NOD_ID
		INNER JOIN LMS_CMS.BC_LRMP_NOD N3
		    ON N3.LRMP_NOD_ID = N4.URNK_LRMP_NOD_ID
		INNER JOIN LMS_CMS.BC_LRMP_NOD N2
		    ON N2.LRMP_NOD_ID = N3.URNK_LRMP_NOD_ID
		INNER JOIN LMS_CMS.BC_LRMP_NOD N1
		    ON N1.LRMP_NOD_ID = N2.URNK_LRMP_NOD_ID
		WHERE N1.LRMP_NOD_ID = #{lrmpNodId}
	</select>
	
		<select id="selectKmmpNodList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		/** EaAsnCom-Mapper.xml - selectKmmpNodList */
		SELECT
			  DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		   , MAX(DPTH2.KMMP_NOD_NM) AS MLU_KMMP_NOD_NM
		   , MAX(DPTH1.LCKN_YN) AS LCKN_YN
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
			INNER JOIN LMS_CMS.BC_KMMP_NOD BKN ON DPTH1.KMMP_NOD_ID = BKN.KMMP_NOD_ID AND BKN.LU_EPS_YN = 'Y'
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
			   ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
			   AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
			   AND DPTH2.DPTH = 2
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
			   ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
			   AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
			   AND DPTH3.DPTH = 3
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
			   ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
			   AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
			   AND DPTH4.DPTH = 4
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
			   ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
			   AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
			   AND DPTH5.DPTH = 5
			LEFT OUTER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
			   ON BALAC.KMMP_NOD_ID = DPTH5.KMMP_NOD_ID
			   AND BALAC.CTN_TP_CD = 'QU'
			   AND BALAC.DEL_YN = 'N'
		WHERE DPTH1.OPT_TXB_ID = #{optTxbId}
		AND DPTH1.DPTH = 1
		AND DPTH1.USE_YN = 'Y'
		AND BALAC.KMMP_NOD_ID IS NOT NULL
		GROUP BY DPTH2.KMMP_NOD_ID
		ORDER BY MAX(DPTH1.RCSTN_ORDN), MAX(DPTH1.ORGL_ORDN), MAX(DPTH4.ORGL_ORDN)
	</select>

</mapper>