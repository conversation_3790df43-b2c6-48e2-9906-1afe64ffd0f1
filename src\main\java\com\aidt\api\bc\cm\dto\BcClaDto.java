package com.aidt.api.bc.cm.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "CM_학급")
public class BcClaDto {

	@Schema(description = "학급ID")
	private String claId;

	@Schema(description = "학교코드")
	private String schlCd;

	@Schema(description = "학년")
	private Long sgy;

	@Schema(description = "반번호")
	private Long claNo;

	@Schema(description = "학교명")
	private String schlNm;

	@Schema(description = "학교급코드")
	private String schlGrdCd;

	@Schema(description = "반명")
	private String claNm;
	
	@Schema(description = "KERIS 반명")
	private String classroomNm;

	@Schema(description = "관할교육청코드")
	private String jrdFedCd;

	@Schema(description = "관할교육청명")
	private String jrdFedNm;

	@Schema(description = "담임교사사용자ID")
	private String chgTcrUsrId;

	@Schema(description = "KERIS반코드")
	private String kerisClaCd;

	@Schema(description = "생성자ID")
	private String crtrId;

	@Schema(description = "생성일시")
	private LocalDateTime crtDtm;
	
	@Schema(description = "수정자ID")
	private String mdfrId;
	
	@Schema(description = "수정일시")
	private LocalDateTime mdfDtm;

	@Schema(description = "데이터베이스ID")
	private String dbId;
}
