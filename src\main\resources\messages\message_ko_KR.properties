############################################################################
# Messag \uC81C\uC57D\uC0AC\uD56D
#
#01. key = messag\uB85C \uAE30\uC220\uB41C\uB2E4.
#02. Comment \uB294 '#'\uC73C\uB85C \uC2DC\uC791\uB418\uBA74 \uB418\uB098, \uBB38\uC790\uC5F4 \uC911\uAC04\uC5D0 \uC788\uB294 '#'\uAE30\uD638\uB294 Comment\uB85C \uC778\uC2DD\uD558\uC9C0 \uC54A\uB294\uB2E4.
#03. '=' \uB300\uC2E0 ' '(\uACF5\uBC31)\uC744 \uC0AC\uC6A9\uD574\uB3C4 \uB41C\uB2E4. (\uC989 \uCCAB\uBC88\uC9F8 \uB098\uC624\uB294 \uACF5\uBC31\uC774 key\uACFC message\uB97C \uB098\uB204\uB294 \uAD6C\uC2E4\uB3C4 \uD55C\uB2E4.
#04. 3\uBC88\uC758 \uC774\uC720\uB85C \uC778\uD558\uC5EC key\uC5D0\uB294 \uC911\uAC04\uC5D0 \uACF5\uBC31\uC774 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uB294\uB2E4.
#05. \uBC18\uBA74, message\uC5D0\uB294 \uACF5\uBC31\uC774 \uD5C8\uC6A9\uB41C\uB2E4.
#06. \uB450 \uC904 \uC774\uC0C1\uC744 \uC0AC\uC6A9\uD558\uB824\uBA74 \uB77C\uC778\uC758 \uB05D\uC5D0 '\' \uB97C \uC0AC\uC6A9\uD558\uBA74 \uB41C\uB2E4.
#07. message\uB97C \uC0AC\uC6A9\uD560 \uB54C \uD55C\uAE00\uC774 \uC9C0\uC6D0\uB41C\uB2E4.  \uADF8\uB7EC\uB098 key\uB294 \uD55C\uAE00\uC9C0\uC6D0\uC774 \uC548\uB41C\uB2E4.
#08. '\' \uBB38\uC790\uAC00 \uD544\uC694\uD558\uBA74 '\'\uAC00 \uC544\uB2C8\uB77C '\\' \uC744 \uC0AC\uC6A9\uD574\uC57C \uD55C\uB2E4. \uB2E8, \uB2E4\uC74C \uC904 ==> & 
#09. \uC904\uC758 \uCCAB \uC2DC\uC791 \uBE48\uCE78\uC740 \uC544\uBB34\uB9AC \uB9CE\uB354\uB77C\uB3C4 \uBB34\uC2DC\uB41C\uB2E4.
#10. message\uC758 \uB9E8 \uB05D\uC740 \uBC18\uB4DC\uC2DC \uBB38\uC790\uB97C \uAE30\uC785\uD574\uC57C \uD55C\uB2E4.
#11. message\uB294 \uB300\uC18C\uBB38\uC790\uB97C \uAD6C\uBD84\uD55C\uB2E4. \uB300\uBB38\uC790\uC0AC\uC6A9\uC744 \uAD8C\uC7A5\uD55C\uB2E4.
#
###########################################################################
api.notfound.user=\uC0AC\uC6A9\uC790\uAC00 \uC874\uC7AC \uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
api.notfound.token=\uC778\uC99D \uD1A0\uD070\uC774 \uC874\uC7AC \uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
api.fail.save.token=Refresh \uD1A0\uD070\uC774 \uC800\uC7A5 \uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
api.Unauthenticated=\uC778\uC99D \uB418\uC9C0 \uC54A\uC740 \uC0AC\uC6A9\uC790 \uC785\uB2C8\uB2E4.
api.Unauthorized=\uC811\uADFC \uAD8C\uD55C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.

api.token.access.expired=\uC778\uC99D \uD1A0\uD070\uC774 \uB9CC\uB8CC \uB418\uC5C8\uC2B5\uB2C8\uB2E4.
api.token.access.signature=\uC778\uC99D \uD1A0\uD070\uC774 \uC720\uD6A8 \uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
api.token.access.invalid=\uC778\uC99D \uD1A0\uD070\uC774 \uC720\uD6A8 \uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. 
api.token.access.compact=\uC778\uC99D \uD1A0\uD070\uC774 \uC720\uD6A8 \uD558\uC9C0 \uB418\uC5C8\uC2B5\uB2C8\uB2E4.
api.token.access.unsupported=\uC9C0\uC6D0 \uD558\uC9C0 \uC54A\uB294 \uD1A0\uD070 \uD615\uC2DD\uC785\uB2C8\uB2E4.

api.token.refresh.expired=\uAC31\uC2E0 \uD1A0\uD070\uC774 \uB9CC\uB8CC \uB418\uC5C8\uC2B5\uB2C8\uB2E4.
api.token.refresh.signature=\uAC31\uC2E0 \uD1A0\uD070\uC774 \uC720\uD6A8 \uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
api.token.refresh.invalid=\uAC31\uC2E0 \uD1A0\uD070\uC774 \uC720\uD6A8 \uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. 
api.token.refresh.compact=\uAC31\uC2E0 \uD1A0\uD070\uC774 \uC720\uD6A8 \uD558\uC9C0 \uB418\uC5C8\uC2B5\uB2C8\uB2E4.
api.token.refresh.unsupported=\uC9C0\uC6D0 \uD558\uC9C0 \uC54A\uB294 \uD1A0\uD070 \uD615\uC2DD\uC785\uB2C8\uB2E4.

api.user.notfound=\uC0AC\uC6A9\uC790\uB97C \uCC3E\uC744\uC218 \uC5C6\uC2B5\uB2C8\uB2E4.

api.sl.sbclrn.0001=\uC774\uBBF8 \uC81C\uCD9C\uB41C \uACFC\uC81C\uC785\uB2C8\uB2E4.
api.sl.sbclrn.0002=\uC774\uBBF8 \uAE30\uD55C\uC774 \uC9C0\uB09C \uACFC\uC81C\uC785\uB2C8\uB2E4.
api.sl.sbclrn.0003=\uACFC\uC81C\uC81C\uCD9C \uC911 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD558\uC600\uC2B5\uB2C8\uB2E4.
api.sl.lsndat.0001=\uD574\uB2F9 \uB4F1\uB85D\uB41C \uD559\uC2B5\uC790\uB8CC\uC785\uB2C8\uB2E4.
api.sl.lsndat.0002=\uC870\uD68C\uD560 \uC218 \uC5C6\uB294 \uD559\uC2B5\uC790\uB8CC\uC785\uB2C8\uB2E4.

