package com.aidt.api.al.pl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc [학습목록조회 req dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlSbclrnStuSrhDto {
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    @Parameter(name="학습맵 노드 ID")
    private String lrmpNodId;
    
    @Parameter(name="사용자 ID")
    private String userId;
    
}
