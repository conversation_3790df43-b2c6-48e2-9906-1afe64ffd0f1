package com.aidt.api.ea.evcom.ev.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 평가 관리 - 메인리스트 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvEvIdReqDto {

	@Parameter(name="평가ID", required = true)
	private long evId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	
	
	@Parameter(name="학교반ID")
	private String claId;	

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="탭인덱스")
	private int tabIndex;
	
	@Parameter(name="버킷url")
	private String bucketUrl;
	
    @Parameter(name="학기구분코드")
    private String trmDvCd;	
}
