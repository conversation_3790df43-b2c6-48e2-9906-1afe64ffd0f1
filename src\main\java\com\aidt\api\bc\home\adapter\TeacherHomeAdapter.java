package com.aidt.api.bc.home.adapter;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.api.bc.home.dto.HomeSummaryAiCoaching;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class TeacherHomeAdapter {

	private final String MAPPER_NAMESPACE = "api.bc.home.tcr.";
	private final CommonDao commonDao;



	public List<HomeSummaryAiCoaching> getHomeSummaryAiCoaching(String optTxbId, Integer mluKmmpNodId) {
		return commonDao.selectList(MAPPER_NAMESPACE + "getHomeSummaryAiCoaching", Map.of("optTxbId",optTxbId,"mluKmmpNodId",mluKmmpNodId));
	}

}