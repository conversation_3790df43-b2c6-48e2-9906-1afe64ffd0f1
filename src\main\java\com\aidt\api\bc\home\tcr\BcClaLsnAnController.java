package com.aidt.api.bc.home.tcr;

import java.util.List;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto;
import com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-19 14:29:00
 * @modify 2024-06-19 14:29:00
 * @desc 교사 > 홈 > 우리반 수업 분석 Controller
 */

//@Slf4j
@Tag(name="[bc] 수업분석[BcClaLsnAn]", description="(교사)홈 > 우리반 수업 분석")
@RestController
@RequestMapping("/api/v1/bc/tcr/clalsnan")
public class BcClaLsnAnController {
	
	@Autowired
	private BcClaLsnAnService bcClaLsnAnService;
	
	/**
	 * [교과서 공부 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회
	 * 
	 * @param dto
	 * @return List<BcClaLsnAnResDto>
	 */
	@Tag(name="[bc] [교과서 공부 탭] 목록 조회", description="학생 목록 기준의 차시별 학습현황")
	@PostMapping(value = "/selectTxbStuList", consumes = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseDto<List<BcClaLsnAnResDto>> selectTxbStuList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
		
		if (dto == null 
				|| StringUtils.isBlank(dto.getOptTxbId())
				|| StringUtils.isBlank(dto.getLrmpNodId())
				|| StringUtils.isBlank(dto.getClaId())) {
			return Response.fail("Params does not exist");
		}
		
		return Response.ok(bcClaLsnAnService.selectTxbStuList(dto));
	}
	
	/**
	 * [우리반 수업 분석] - 차시목록
	 * 
	 * @param dto - 운영교과서
	 * @return 차시목록
	 */
	@Tag(name="[bc] [우리반 수업 분석] - 차시목록", description="차시목록")
	@PostMapping(value = "/selectTcList")
    public ResponseDto<List<BcClaLsnAnResDto>> selectTcList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
		
		if (dto == null 
				|| StringUtils.isBlank(dto.getOptTxbId())) {
			return Response.fail("Params does not exist");
		}
		
		return Response.ok(bcClaLsnAnService.selectTcList(dto));
	}
	
	/**
	 * [교과서 공부 탭] - 헤더 목록 조회
	 * 
	 * @param dto
	 * @return
	 */
	@Tag(name="[bc] [교과서 공부 탭] - 헤더 목록 조회", description="헤더 목록 조회")
	@PostMapping(value = "/selectTxbStuHdrList")
    public ResponseDto<List<BcClaLsnAnResDto>> selectTxbStuHdrList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
		
		if (dto == null 
				|| StringUtils.isBlank(dto.getOptTxbId())
				|| StringUtils.isBlank(dto.getLrmpNodId())) {
			return Response.fail("Params does not exist");
		}
		return Response.ok(bcClaLsnAnService.selectTxbStuHdrList(dto));
	}
	
	/**
	 * [형성평가 탭] - 헤더 목록 조회
	 * 
	 * @param dto
	 * @return
	 */
	@Tag(name="[bc] [형성평가 탭] - 헤더 목록 조회", description="헤더 목록 조회")
	@PostMapping(value = "/selectFrmEvHdrList")
    public ResponseDto<List<BcClaLsnAnResDto>> selectFrmEvHdrList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
		
		if (dto == null 
				|| StringUtils.isBlank(dto.getOptTxbId())
				|| StringUtils.isBlank(dto.getLrmpNodId())) {
			return Response.fail("Params does not exist");
		}
		return Response.ok(bcClaLsnAnService.selectFrmEvHdrList(dto));
	}
	
	/**
	 * [형성평가 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회
	 * 
	 * @param dto
	 * @return List<BcClaLsnAnResDto>
	 */
	@Tag(name="[bc] [형성평가 탭] 목록 조회", description="학생 목록 기준의 차시별 학습현황")
	@PostMapping(value = "/selectFrmEvList")
    public ResponseDto<List<BcClaLsnAnResDto>> selectFrmEvList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
		
		if (dto == null 
				|| StringUtils.isBlank(dto.getOptTxbId())
				|| StringUtils.isBlank(dto.getLrmpNodId())
				|| StringUtils.isBlank(dto.getClaId())) {
			return Response.fail("Params does not exist");
		}
		
		return Response.ok(bcClaLsnAnService.selectFrmEvList(dto));
	}
	
	/**
     * [수학익힙 탭] - 헤더 목록 조회
     * 
     * @param dto
     * @return
     */
    @Tag(name="[bc] [수학익힙 탭] - 헤더 목록 조회", description="헤더 목록 조회")
    @PostMapping(value = "/selectMthPrtHdrList")
    public ResponseDto<List<BcClaLsnAnResDto>> selectMthPrtHdrList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
        
        if (dto == null 
                || StringUtils.isBlank(dto.getOptTxbId())
                || StringUtils.isBlank(dto.getLrmpNodId())) {
            return Response.fail("Params does not exist");
        }
        return Response.ok(bcClaLsnAnService.selectMthPrtHdrList(dto));
    }
    
    /**
     * [수학익힙 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회
     * 
     * @param dto
     * @return List<BcClaLsnAnResDto>
     */
    @Tag(name="[bc] [수학익힙 탭] 목록 조회", description="학생 목록 기준의 차시별 학습현황")
    @PostMapping(value = "/selectMthPrtList")
    public ResponseDto<List<BcClaLsnAnResDto>> selectMthPrtList(@Valid @RequestBody BcClaLsnAnReqDto dto) {
        
        if (dto == null 
                || StringUtils.isBlank(dto.getOptTxbId())
                || StringUtils.isBlank(dto.getLrmpNodId())
                || StringUtils.isBlank(dto.getClaId())) {
            return Response.fail("Params does not exist");
        }
        
        return Response.ok(bcClaLsnAnService.selectMthPrtList(dto));
    }
}
