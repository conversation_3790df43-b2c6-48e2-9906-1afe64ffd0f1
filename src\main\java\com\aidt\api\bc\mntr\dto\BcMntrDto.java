package com.aidt.api.bc.mntr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-03-18 17:58:46
 * @modify 2024-03-18 17:58:46
 * @desc 학생학습모니터링 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcMntrDto{

	@Parameter(name="학습사용자ID")
	private String lrnUsrId;
	
	@Parameter(name="학습사용자명")
	private String lrnUsrNm;
	
	@Parameter(name="학습유형코드")
	private String lrnTpCd;
	
	@Parameter(name="현재학습메뉴ID")
	private Long curLrnMenuId;

	@Parameter(name="현재학습메뉴명")
	private String curLrnMenuNm;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="단원노드ID")
	private String luNodId;
	
	@Parameter(name="차시노드ID")
	private String tcNodId;
	
	@Parameter(name="학습활동ID")
	private String lrnAtvId;

	@Parameter(name="관심여부")
	private String ntrYn;
	
	@Parameter(name="학급ID")
	private String claId;
	
	@Parameter(name="감정상태코드")
	private String flnStCd;

	@Parameter(name="실시간이상행동유형코드")
	private String rtmAbnBhvTpCd;
	
	@Parameter(name="비정기이상행동유형코드")
	private String nfxpAbnBhvTpCd;
	
	@Parameter(name="현재접속여부")
	private String curConnYn;
	
	@Parameter(name="현재접속수")
	private String curConnCnt;
	
	@Parameter(name="전체학생수")
	private Integer stuCnt;
	
	@Parameter(name="지도필요수")
	private Integer gdeNeedCnt;
	
	@Parameter(name="지도필요코드명")
	private String gdeNeedCdNm;
	
	@Parameter(name="확인여부")
	private String cofmYn;
	
	@Parameter(name="지도필요과제/평가명")
	private String gdeNeedVlNm;
	
	private String sort;
	
	private String crtrId;
	private String crtDtm;
	private String mdfrId;
	private String mdfDtm;
	private String dbId;
	
	private int totalCnt;
	
	// 임시 : 최근 접속 시간     
	private String connDt;
	private String connTm;
	
	@Parameter(name="지도필요 Dto")
	private List<BcMntrJsonDto> mntrJsonDto;
	
	@Parameter(name="지도필요 JSON 데이터 컬럼")
	private String gdeNeedTp;
	
	@Parameter(name="풀이초수")
	private Integer xplTmScnt;
	
	@Parameter(name="정답개수")
	private Integer oCnt;
	
	@Parameter(name="오답개수")
	private Integer xCnt;
	
	@Parameter(name="푼문제수")
	private Integer totCnt;
	
	@Parameter(name="정답률")
	private Integer cansRt;
	
	@Parameter(name="학습시간")
	private String lrnTm;
	
	@Parameter(name="개념학습시간")
	private String ccptLrnTm;
	
	@Parameter(name="대화사용여부")
	private String dilgUseYn;
	
	@Parameter(name="지도필요코드")
	private String gdeNeedCd;
	
	@Parameter(name="지도필요값")
	private String gdeNeedVl;
	
	@Parameter(name="지도필요정보")
	private String gdeNeedInfo;
	
}
