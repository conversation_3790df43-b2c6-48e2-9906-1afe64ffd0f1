package com.aidt.api.ea.ev.stu;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.evcom.ev.dto.EaEvMainReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 교과 평가 - 학생", description="교과 평가 - 학생")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/ev/stu")
public class EaEvStuController {

	@Autowired
	EaEvStuService eaEvStuService;
	@Autowired
	private JwtProvider jwtProvider;

	/**
	 * 학생 - 평가 목록 조회 요청
	 *
	 * @param eaEvStuReqDto
	 * @return ResponseList<EaEvMainResDto>
	 */
	@Tag(name="[ea] 평가 목록 조회", description="평가 목록 조회")
	@PostMapping(value = "/selectEvList")
	public ResponseDto<List<Map<String, Object>>> selectEvList(@Valid @RequestBody EaEvMainReqDto eaEvStuReqDto) {
		log.debug("Entrance selectEvList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		eaEvStuReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		eaEvStuReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		return Response.ok(eaEvStuService.selectEvList(eaEvStuReqDto));
	}
	
	
	/**
	 * 학생 - 평가 리포트 조회 요청
	 *
	 * @param eaEvStuReqDto
	 * @return ResponseList<EaEvMainResDto>
	 */
	@Tag(name="[ea] 평가 리포트 조회", description="평가 리포트 조회")
	@PostMapping(value = "/selectEvRptList")
	public ResponseDto<EaEvMainResDto> selectEvRptList(@Valid @RequestBody EaEvMainReqDto eaEvStuReqDto) {
		log.debug("Entrance selectEvList");

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		
		if(eaEvStuReqDto.getUsrId().trim() == "")
		{
			eaEvStuReqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
		}
		eaEvStuReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		return Response.ok(eaEvStuService.selectEvRptList(eaEvStuReqDto));
	}
	
}
