package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrDtlTxbResDto {
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="대단원ID")
	private String luLrmpNodId;
	
	@Parameter(name="단원ID")
	private String lrmpNodId;
	
	@Parameter(name="단원명")
	private String lrmpNodNm;

	@Parameter(name="단원명")
	private String lrmpNodNmView;

	@Parameter(name="깊이")
	private String dpth;
	
	@Parameter(name="재구성 정렬순서")
	private int rcstnOrdn;

	@Parameter(name="대단원 개수")
	private int luCnt;

	@Parameter(name="학습활동 번호")
	private int lrnAtvRowNo;

	@Parameter(name="학습활동ID")
	private String lrnAtvId;
	
	@Parameter(name="학습활동명")
	private String lrnAtvNm;
	
	@Parameter(name="학습상태코드")
	private String lrnStCd;	
	
	@Parameter(name="학습상태명")
	private String lrnStNm;	
	
	@Parameter(name="학습활동 전체개수")
	private int atvCnt;
	
	@Parameter(name="학습활동 완료개수")
	private int cmplCnt;

	@Parameter(name="학습활동 학생ID")
	private String lrnUsrId;
	
	@Parameter(name="학습활동 진행률")
	private String atvPgrsRt;
	
	@Parameter(name="학습활동 진행시간초")
	private int lrnTmScnt;
	
	@Parameter(name="학습시작일")
	private String startDtm;
	
	@Parameter(name="학습종료일")
	private String endDtm;

	
}
