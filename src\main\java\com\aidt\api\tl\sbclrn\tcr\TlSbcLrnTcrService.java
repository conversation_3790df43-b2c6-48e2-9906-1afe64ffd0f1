
package com.aidt.api.tl.sbclrn.tcr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnEaAsnSrhDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvSrhDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvStuDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:28:18
 * @modify date 2024-01-04 14:28:18
 * @desc TlStuSbcLrn Service 교과학습 서비스
 */
@Service
public class TlSbcLrnTcrService {
    private final String MAPPER_NAMESPACE = "api.tl.sbclrn.tcr.";

    @Autowired
    private CommonDao commonDao;

    
    /** 
     * 교과학습 활동 리스트 조회
     * @param srhDto
     * @return List<TlSbcLrnAtvDto>
     */
    @Transactional(readOnly = true)
    public List<TlSbcLrnAtvDto> selectLrnAtvList(TlSbcLrnTocSrhDto srhDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvList", srhDto);
    }

    /**
     * 교과학습 평가현황 조회 
     * @param evSrhDto
     * @return List<TlSbcLrnEvStuDto>
     */
    @Transactional(readOnly = true)
    public List<TlSbcLrnEvStuDto> selectSbcLrnEvPst(TlSbcLrnEvSrhDto evSrhDto) {
        List<TlSbcLrnEvStuDto> evStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectSbcLrnEvStuList", evSrhDto);
        evStuList.forEach(list -> {
            evSrhDto.setUsrId(list.getUsrId());
            list.setCurQtmId(commonDao.select(MAPPER_NAMESPACE+"selectCurQtmId", evSrhDto));
            list.setEvPstList(commonDao.selectList(MAPPER_NAMESPACE + "selectSbcLrnEvPst", evSrhDto));
        });
        return evStuList;
    }

    /** 교과학습 과제출제건수 조회 */
    @Transactional(readOnly = true)
    public int countEaAsnData(TlSbcLrnEaAsnSrhDto eaAsnSrhDto) {
        int cnt = commonDao.select(MAPPER_NAMESPACE + "countEaAsnData", eaAsnSrhDto);
        return cnt;
    }
    
}
