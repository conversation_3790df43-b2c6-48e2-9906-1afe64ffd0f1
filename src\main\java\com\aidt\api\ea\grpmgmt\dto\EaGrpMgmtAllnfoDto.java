package com.aidt.api.ea.grpmgmt.dto;
import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email 
 * @create date 2024-04-26 10:53:20
 * @modify date 2024-04-26 10:53:20
 * @desc EaGrpMgGrpDto 모둠관리 모둠 Dto
 */

 @Data
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
// @JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtAllnfoDto {
    @Parameter(name = "모둠관리 모둠 리스트")
    private List<EaGrpMgmtGrpDto> grpList;

    @Parameter(name = "모둠관리 모둠 그룹 리스트")
    private List<EaGrpMgmtGrpTeamDto> grpTeamList;

    @Parameter(name = "모둠관리 모둠 그룹 학생 리스트")
    private List<EaGrpMgmtGrpTeamStuDto> grpTeamStuList;

    @Parameter(name = "모둠관리 모둠 그룹 전체 학생 리스트")
    private List<EaGrpMgmtGrpTeamStuDto> grpAllStuList;
    
}