package com.aidt.api.al.fdbk.dto.req;

import com.aidt.api.al.fdbk.dto.AiFdbkDto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:56:58
 * @modify date 2024-07-11 15:56:58
 * @desc
 */
@Data
@Builder
public class AiFdbkStuReqDto {

    private String optTxbId;

    private String usrId;

    private String lrmpNodId;

    public AiFdbkDto toDto() {
        return AiFdbkDto.builder()
            .optTxbId(this.optTxbId)
            .usrId(this.usrId)
            .lrmpNodId(this.lrmpNodId).build();
    }

}
