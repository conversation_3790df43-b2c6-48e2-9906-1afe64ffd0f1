package com.aidt.api.bc.wdlst.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.wdlst.dto.BcWdLstDto;
import com.aidt.api.bc.wdlst.dto.BcWdSrhHstDto;
import com.aidt.common.CommonDao;
import com.aidt.api.bc.cm.BcCmUtil;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:55:46
 * @modify 2024-01-05 17:55:46
 * @desc 단어장 Service
 */

@Service
public class BcWdLstStuService {

    private final String MAPPER_NAMESPACE = "api.bc.wdlst.stu.";

    @Autowired
    private CommonDao commonDao;

    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
    /**
     * 단어장 목록 조회 서비스
     *
     * @param BcWdLstDto
     * @return List<LwWdLstDto>
     */
    public List<BcWdLstDto> selectWdLstList(BcWdLstDto bcWdLstDto) {
    	List<BcWdLstDto> listData = commonDao.selectList(MAPPER_NAMESPACE + "selectWdLstList", bcWdLstDto);
    	
		for (BcWdLstDto dto : listData) {
			dto.setAuFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getAuFlePth()));
			dto.setImFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getImFlePth()));
			dto.setMeFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getMeFlePth()));
		}
    	
        return listData;
    }
    
    /**
     * 단어장 팝업 목록 조회 서비스
     *
     * @param BcWdLstDto
     * @return List<LwWdLstDto>
     */
    public List<BcWdLstDto> selectPopWdLstList(BcWdLstDto bcWdLstDto) {
    	
    	List<BcWdLstDto> listData = commonDao.selectList(MAPPER_NAMESPACE + "selectPopWdLstList", bcWdLstDto);
    	
		for (BcWdLstDto dto : listData) {
			dto.setAuFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getAuFlePth()));
			dto.setImFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getImFlePth()));
			dto.setMeFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getMeFlePth()));
		}
    	
		return listData;
    	
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectPopWdLstList", bcWdLstDto);
    }

    /**
     * 단어장 단어 등록 서비스
     *
     * @param BcWdLstDto
     * @return Integer
     */
    public int insertWdLstList(BcWdLstDto bcWdLstDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertWdLst", bcWdLstDto);
    }
    
    /**
     * 영어단어 관련검색어 조회 서비스
     *
     * @param BcWdLstDto
     * @return List<LwWdLstDto>
     */
    public List<BcWdLstDto> srchWdLst(BcWdLstDto bcWdLstDto) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "srchWdLst", bcWdLstDto);
    }
    
    /**
     * 영어단어 조회 서비스
     *
     * @param BcWdLstDto
     * @return List<LwWdLstDto>
     */
    public List<BcWdLstDto> selectCmEnWdLst(BcWdLstDto bcWdLstDto) {
    	
    	List<BcWdLstDto> listData = commonDao.selectList(MAPPER_NAMESPACE + "selectCmEnWdLst", bcWdLstDto);
    	
		for (BcWdLstDto dto : listData) {
			dto.setAuFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getAuFlePth()));
			dto.setImFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getImFlePth()));
			dto.setMeFlePth(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getMeFlePth()));
		}
		
		return listData;
    	
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectCmEnWdLst", bcWdLstDto);
    }
    
    /**
     * 단어장 삭제 서비스
     *
     * @param BcWdLstDto
     * @return Integer
     */
    public int deleteWdLstList(BcWdLstDto item) {
    	int result = 0;
    	for(int myWdId : item.getMyWdIds()) {
    		item.setMyWdId(myWdId);
    		result += commonDao.delete(MAPPER_NAMESPACE + "deleteWdLstList", item);
    	}
    	return result;
    }
    
    /**
     * 단어 검색이력 목록 조회 서비스
     *
     * @param BcWdSrhHstDto
     * @return List<LwWdSrhHstDto>
     */
    public List<BcWdSrhHstDto> selectWdSrhHstList(BcWdSrhHstDto bcWdSrhHstDto) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectWdSrhHstList", bcWdSrhHstDto);
    }
    
    /**
     * 단어 검색이력 등록 서비스
     *
     * @param BcWdSrhHstDto
     * @return Integer
     */
    public int insertWdSrhHstList(BcWdSrhHstDto bcWdSrhHstDto) {
    	
    	int getHstCnt = commonDao.select(MAPPER_NAMESPACE + "selectWdSrhHstCnt", bcWdSrhHstDto);
    	
    	if(getHstCnt > 0) {
    		return commonDao.update(MAPPER_NAMESPACE + "updateWdSrhHst", bcWdSrhHstDto);
    	}else {	
    		return commonDao.insert(MAPPER_NAMESPACE + "insertWdSrhHst", bcWdSrhHstDto);
    	} 
    }
}
