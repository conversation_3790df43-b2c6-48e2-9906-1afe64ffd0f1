<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.sl.lrnwif.stu">

	<!-- 특별학습 데이터 확인-->
    <select id="selectChkSlSpLrn" resultType="int">
        SELECT COUNT(*) 
        FROM LMS_LRM.sl_sp_lrn_rcstn 
        WHERE OPT_TXB_ID = #{optTxbId} 
        AND SP_LRN_ID =  #{spLrnId}  
        AND USE_YN = 'Y';
        
        /** 특별학습 김형준 SlLrnwifStu-Mapper.xml - selectChkSlSpLrn */
    </select> 

    <!-- 특별학습재구성 select-->
    <select id="selectSlSpLrnRcstnList" resultType="com.aidt.api.sl.lrnwif.dto.SlLrnwTocAtvDto">
        WITH RECURSIVE RCS AS (
            SELECT 
                   R1.SP_LRN_ID
                  ,R1.SP_LRN_NM
                  ,R2.SP_LRN_NOD_ID
                  ,R2.SP_LRN_NOD_NM
                  ,R2.URNK_SP_LRN_NOD_ID
                  ,CAST('' AS CHAR(100)) AS URNK_SP_LRN_NOD_NM
                  ,R2.DPTH
                  ,R2.LWS_YN
            FROM LMS_LRM.SL_SP_LRN_RCSTN R1 /* SL_특별학습재구성 */
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.DPTH = 1
            WHERE R1.OPT_TXB_ID = #{optTxbId}                         /* 운영교과서 ID */
              AND R1.USE_YN = 'Y'
              AND R1.SP_LRN_ID = #{spLrnId}                           /* 특별학습ID */
            UNION 
            SELECT 
                   R2.SP_LRN_ID
                  ,R1.SP_LRN_NM
                  ,R2.SP_LRN_NOD_ID
                  ,R2.SP_LRN_NOD_NM
                  ,IF(R2.DPTH = 2, R1.SP_LRN_NOD_ID, R1.URNK_SP_LRN_NOD_ID) AS URNK_SP_LRN_NOD_ID
                  ,IF(R2.DPTH = 2, R1.SP_LRN_NOD_NM, R1.URNK_SP_LRN_NOD_NM) AS URNK_SP_LRN_NOD_NM 
                  ,R2.DPTH
                  ,R2.LWS_YN
            FROM RCS R1
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R1.SP_LRN_NOD_ID = R2.URNK_SP_LRN_NOD_ID
                       AND R2.DEL_YN = 'N'
                )
        SELECT
               MAX(R.SP_LRN_ID)            AS LLU_NOD_ID                           /* 특별학습ID */
              ,MAX(R.SP_LRN_NM)            AS LLU_NOD_NM                           /* 특별학습영 */
              ,R.URNK_SP_LRN_NOD_ID   AS TC_NOD_ID                            /* 1depth 노드ID */
              ,MAX(R.URNK_SP_LRN_NOD_NM)   AS TC_NOD_NM                            /* 1depth 노드명 */
              ,COUNT(1)               AS ATV_TOT_CNT                          /* 컨텐츠 총건수 */
        FROM RCS R
            INNER JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                  ON C.SP_LRN_NOD_ID = R.SP_LRN_NOD_ID
                  AND C.DEL_YN = 'N'
        WHERE R.URNK_SP_LRN_NOD_ID = #{spLrnNodId}   /* 상위노드ID */
          AND R.LWS_YN = 'Y'
        GROUP BY URNK_SP_LRN_NOD_ID
        /** 특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectSlSpLrnRcstnList */

    </select>    
    <!-- 특별학습 상위 노드 select -->
    <select id="selectSpLrnNodId" resultType="com.aidt.api.sl.lrnwif.dto.SlLrnwPrgsDto" >
        SELECT
               A.URNK_SP_LRN_NOD_ID                                /* 상위노드ID */
              ,IFNULL(C.LRN_ST_CD, 'NL') AS LRN_ST_CD              /* 학습 진행상태 DL:진행중/ CL:완료 / NL:미학습 */
              ,IF(C.SP_LRN_CTN_ID IS NOT NULL AND C.SP_LRN_CTN_ID = (SELECT SE.SP_LRN_CTN_ID
                                   FROM LMS_LRM.SL_SP_LRN_PGRS_ST SE /* SL_특별학습진행상태 */
                                   WHERE SE.OPT_TXB_ID = #{optTxbId}
                                   AND SE.SP_LRN_ID = #{spLrnId}
                                   AND SE.LRN_USR_ID = #{userId}
                                   order by SE.MDF_DTM desc
                                   limit 1), 'Y', 'N') LAST_LRN_YN                        
        FROM LMS_CMS.BC_SP_LRN_NOD A  /* BC_특별학습노드 */
            INNER JOIN LMS_CMS.BC_SP_LRN_NOD D /* BC_특별학습노드 = 상위 */
                    ON A.SP_LRN_ID = D.SP_LRN_ID
                   AND A.URNK_SP_LRN_NOD_ID = D.SP_LRN_NOD_ID
                   AND D.DEL_YN = 'N'
            INNER JOIN LMS_CMS.BC_SP_LRN_CTN B /* BC_특별학습콘텐츠 */
                    ON B.SP_LRN_NOD_ID = A.SP_LRN_NOD_ID
                   AND B.DEL_YN = 'N'
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST C /* SL_특별학습진행상태 */
                   ON C.SP_LRN_CTN_ID = B.SP_LRN_CTN_ID
                  AND C.OPT_TXB_ID = #{optTxbId}                        /*운영교과서ID*/
                  AND C.SP_LRN_ID = #{spLrnId}                          /*특별학습ID*/
                  AND C.LRN_USR_ID = #{userId}                          /*유저ID*/
        WHERE A.SP_LRN_ID = #{spLrnId}                            /*특별학습ID*/
          AND A.DEL_YN = 'N'
          AND A.LWS_YN = 'Y'
        ORDER BY D.SRT_ORDN ASC, D.SP_LRN_NOD_ID ASC, A.SRT_ORDN ASC, A.SP_LRN_NOD_ID ASC, B.SRT_ORDN ASC

        /** 특별학습 김형준 SlLrnwifStu-Mapper.xml - selectSpLrnNodId */
    </select>
    <!--진행상황 select-->
    <select id="selectCtnId" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnLastPrgsDto">
        WITH RECURSIVE RCS AS (
            SELECT 
                   R1.SP_LRN_ID
                  ,R2.SP_LRN_NOD_ID
                  ,R2.URNK_SP_LRN_NOD_ID
                  ,C.SP_LRN_CTN_ID
                  ,R2.DPTH
                  ,R2.LWS_YN
                  ,CAST(LPAD(R2.SRT_ORDN, 10, '0') AS CHAR(50)) AS SORT
            FROM LMS_LRM.SL_SP_LRN_RCSTN R1 /* SL_특별학습재구성 */
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.DPTH = 1
                       AND R2.CSTN_CMPL_YN = 'Y'
                LEFT JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                       ON C.SP_LRN_NOD_ID = R2.SP_LRN_NOD_ID
                      AND C.DEL_YN = 'N'
            WHERE R1.OPT_TXB_ID = #{optTxbId} 
              AND R1.USE_YN = 'Y'
              AND R1.SP_LRN_ID = #{spLrnId}
            UNION 
            SELECT 
                   R2.SP_LRN_ID
                  ,R2.SP_LRN_NOD_ID
                  ,IF(R2.DPTH = 2, R1.SP_LRN_NOD_ID, R1.URNK_SP_LRN_NOD_ID) AS URNK_SP_LRN_NOD_ID
                  ,C.SP_LRN_CTN_ID
                  ,R2.DPTH
                  ,R2.LWS_YN
                  ,CONCAT(R1.SORT, LPAD(R2.SRT_ORDN, 10, '0')) AS SORT
            FROM RCS R1
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R1.SP_LRN_NOD_ID = R2.URNK_SP_LRN_NOD_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.CSTN_CMPL_YN = 'Y'
                LEFT JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                       ON C.SP_LRN_NOD_ID = R2.SP_LRN_NOD_ID
                      AND C.DEL_YN = 'N'
        )
        SELECT
               R.SP_LRN_ID                               /* 특별학습ID */
              ,R.SP_LRN_NOD_ID
              ,R.SP_LRN_CTN_ID
              ,IFNULL(B.LRN_ST_CD, 'NL') AS LRN_ST_CD    /* 학습 진행상태 DL:진행중/ CL:완료 / NL:미학습 */
              ,ROW_NUMBER() OVER (ORDER BY R.SORT) AS SORT 
              ,B.MDF_DTM 
              ,IF(B.MDF_DTM = S.MDF_DTM, 'Y', 'N') AS LAST_LRN_YN
        FROM RCS R
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST B /* SL_특별학습진행상태 */
                   ON B.OPT_TXB_ID = #{optTxbId} 
                  AND B.SP_LRN_ID = R.SP_LRN_ID
                  AND B.SP_LRN_CTN_ID = R.SP_LRN_CTN_ID
                  AND B.LRN_USR_ID = #{userId}
            LEFT JOIN (
                        SELECT MAX(SD.MDF_DTM)  AS MDF_DTM
                        FROM RCS SR
                             LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST SD /* SL_특별학습진행상태 */
                                    ON SD.SP_LRN_ID = #{spLrnId}
                                   AND SD.SP_LRN_CTN_ID = SR.SP_LRN_CTN_ID
                                   AND SD.LRN_USR_ID = #{userId}
            ) S
            ON 1=1
        WHERE R.URNK_SP_LRN_NOD_ID = #{spLrnNodId}
          AND R.LWS_YN = 'Y'
          AND R.SP_LRN_CTN_ID IS NOT NULL
        ORDER BY R.SORT ASC
        /** 특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectCtnId */

    </select>
    <!-- 특별학습 활동 조회 -->
    <select id="selectSlSpLrnPgrsDto" resultType="com.aidt.api.sl.splrn.dto.SlSpLrnPgrsDto">
        SELECT
               OPT_TXB_ID                                 /*운영교과서ID*/                        
              ,SP_LRN_ID                                  /*특별학습ID*/
              ,SP_LRN_CTN_ID                              /*컨텐츠ID*/
              ,LRN_USR_ID                                 /*유저ID*/
              ,LRN_ST_CD                                  /* 학습 진행상태 DL:진행중/ CL:완료 / NL:미학습 */
              ,LRN_TM_SCNT                                /* 학습시간초수*/
              ,CRTR_ID                                    /*생성자ID*/
              ,CRT_DTM                                    /*생성일시*/
              ,MDFR_ID                                    /*수정자ID*/
              ,MDF_DTM                                    /*수정일시*/
              ,DB_ID                                      /*DBID*/
        FROM LMS_LRM.SL_SP_LRN_PGRS_ST /* SL_특별학습진행상태 */
        WHERE OPT_TXB_ID = #{optTxbId}                /*운영교과서ID*/    
          AND SP_LRN_ID = #{spLrnId}                  /*특별학습ID*/
          AND SP_LRN_CTN_ID = #{spLrnCtnId}           /*컨텐츠ID*/
          AND LRN_USR_ID = #{userId}                  /*유저ID*/
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectSlSpLrnPgrsDto */
    </select>
    <!--학습도구 조회 -->
    <select id="selectLrnTlList" resultType="com.aidt.api.sl.lrnwif.dto.SlLrnwLrnTlDto">
        SELECT
               A.TXB_ID                                               /* 교과서ID*/
              ,C.LRN_TL_ID                                            /* 학습도구ID */
              ,C.LRN_TL_CTM_ALS_NM AS LRN_TL_KN_NM                    /* 학습도구 별명 */
              ,D.LRN_TL_KN_CD                                         /* 학습도구종류코드 */
              ,C1.CM_CD_NM AS LRN_TL_TP_NM                            /* 학습도구유형 */
              ,D.LRN_TL_TP_CD                                         /* 학습도구유형코드 WT=필기도구, TT=교과도구, MT=소통도구, CT=수업도구 */
        FROM LMS_LRM.CM_OPT_TXB A /* CM_운영교과서 */
             LEFT JOIN LMS_CMS.BC_TXB_LRNW B  /* BC_교과서학습창 */
                    ON A.TXB_ID = B.TXB_ID
             LEFT JOIN LMS_CMS.BC_TXB_LRN_TL_MPN C /* BC_교과서학습도구매핑 */
                    ON B.LRNW_ID =C.LRNW_ID  
             LEFT JOIN LMS_CMS.BC_LRN_TL D /* BC_학습도구 */
                    ON C.LRN_TL_ID = D.LRN_TL_ID
                   AND D.USE_YN = 'Y'
                   AND D.DEL_YN = 'N'  
             LEFT JOIN LMS_LRM.CM_CM_CD C1 /* 공통 코드 */
                    ON C1.URNK_CM_CD = 'LRN_TL_TP_CD'                        /* 상위공통코드 = 학습도구코드 */
                   AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT /* 유효시작-만료일자 */
                   AND C1.LMS_USE_YN = 'Y'
                   AND C1.DEL_YN = 'N'
                   AND C1.CM_CD = D.LRN_TL_TP_CD
             LEFT JOIN LMS_LRM.CM_CM_CD C2 /* 공통 코드 */
                    ON C1.URNK_CM_CD = C2.CM_CD
                   AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                   AND C2.LMS_USE_YN = 'Y'
                   AND C2.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{optTxbId}
          AND B.LRN_TP_CD = 'SL'                                         /* SL=특별학습 */
          AND C.LRN_TL_ID IS NOT NULL
          <if test='userTpCd == "ST"'>
              AND B.LRNW_USR_TP_CD = 'ST'
          </if>
          <if test='userTpCd != "ST"'>
              AND B.LRNW_USR_TP_CD = 'TE'
          </if>
        ORDER BY C1.SRT_ORDN ASC
        /** 특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectLrnTlList */
    </select>
    <!-- 특별학습 콘텐츠 조회 -->
    <select id="selectAtvMetaList" resultType="HashMap">
        WITH RECURSIVE RCS AS (
            SELECT 
                   R1.SP_LRN_ID
                  ,R2.SP_LRN_NOD_ID
                  ,R2.SP_LRN_NOD_NM
                  ,R2.URNK_SP_LRN_NOD_ID
                  ,R2.DPTH
                  ,R2.LWS_YN
                  ,CAST(LPAD(R2.SRT_ORDN, 10, '0') AS CHAR(50)) AS SORT
            FROM LMS_LRM.SL_SP_LRN_RCSTN R1 /* SL_특별학습재구성 */
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.DPTH = 1
                       AND R2.CSTN_CMPL_YN = 'Y'
            WHERE R1.OPT_TXB_ID =  #{optTxbId}                /*운영교과서ID*/
              AND R1.USE_YN = 'Y'
              AND R1.SP_LRN_ID = #{spLrnId}
            UNION 
            SELECT 
                   R2.SP_LRN_ID
                  ,R2.SP_LRN_NOD_ID
                  ,R2.SP_LRN_NOD_NM
                  ,IF(R2.DPTH = 2, R1.SP_LRN_NOD_ID, R1.URNK_SP_LRN_NOD_ID) AS URNK_SP_LRN_NOD_ID
                  ,R2.DPTH
                  ,R2.LWS_YN
                  ,CONCAT(R1.SORT, LPAD(R2.SRT_ORDN, 10, '0')) AS SORT
            FROM RCS R1
                INNER JOIN LMS_CMS.BC_SP_LRN_NOD R2 /* BC_특별학습노드 */
                        ON R1.SP_LRN_ID = R2.SP_LRN_ID
                       AND R1.SP_LRN_NOD_ID = R2.URNK_SP_LRN_NOD_ID
                       AND R2.DEL_YN = 'N'
                       AND R2.CSTN_CMPL_YN = 'Y'
                )
        SELECT
               C.SP_LRN_CTN_ID                           AS LRN_ATV_ID            /* 컨텐츠 ID */
              ,C.SP_LRN_CTN_ID
              ,IFNULL(C.SP_LRN_CTN_NM, R.SP_LRN_NOD_NM)  AS LRN_ATV_NM            /* 컨텐츠명 */
              ,C.FLE_MG_TP_CD                            /* 파일관리유형코드 FI=파일, UR=URL */
              ,IFNULL(C.SP_LRN_CTN_TP_CD, '')            AS CTN_TP_CD             /* 컨텐츠유형코드 */
              ,C.CDN_PTH_NM                              AS CDN_PTH_NM               /* CDN유형코드 */
              ,C.SP_LRN_CTN_CD                           AS CTN_CD
              ,C.SRT_ORDN                                AS RCSTN_ORDN            /* 정렬순서 */
              ,IFNULL(B.LRN_ST_CD, 'NL')                 AS LRN_ST_CD             /* 학습 진행상태 DL=진행중/ CL=완료 / NL=미학습 */
              ,IFNULL(B.LRN_TM_SCNT, 0)                  AS LRN_TM_SCNT           /* 학습시간초수 */
              ,IFNULL(C.STR_FLE_NM, '')                  AS STR_FLE_NM            /* 시작파일명 */
              ,C.VDO_FLE_NM_1280P                        /* 동영상파일명1280P */
              ,C.VDO_FLE_NM_720P                         /* 동영상파일명720P */
              ,C.VDO_FLE_NM_480P                         /* 동영상파일명480P */
        FROM RCS R
            INNER JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
                    ON C.SP_LRN_NOD_ID = R.SP_LRN_NOD_ID
                   AND C.DEL_YN = 'N'
                   <if test='lrnAtvId != null and lrnAtvId != ""'>
                   AND C.SP_LRN_CTN_ID = #{lrnAtvId}
                   </if>
                    
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST B /* SL_특별학습진행상태 */
                    ON B.OPT_TXB_ID = #{optTxbId}                /*운영교과서ID*/
                  AND B.SP_LRN_ID = R.SP_LRN_ID
                  AND B.SP_LRN_CTN_ID = C.SP_LRN_CTN_ID
                  AND B.LRN_USR_ID = #{userId}                  /*유저ID*/
        WHERE R.URNK_SP_LRN_NOD_ID = #{spLrnNodId} 
          AND R.LWS_YN = 'Y'
        ORDER BY R.SORT ASC, C.SRT_ORDN ASC
        /** 특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectAtvMetaList */
    </select>

    <!-- 특별학습 활동 저장-->
    <insert id="insertLrnCtnInfo" parameterType="map">
        INSERT INTO LMS_LRM.SL_SP_LRN_PGRS_ST /* SL_특별학습진행상태 */
            (
              OPT_TXB_ID           /* 운영교과서ID */
             ,SP_LRN_ID            /* 특별학습ID */
             ,SP_LRN_CTN_ID        /* 콘텐츠ID */
             ,LRN_USR_ID           /* 유저ID */                   
             ,LRN_ST_CD            /* 학습 진행상태 DL:진행중/ CL:완료 / NL:미학습 */
             ,LRN_TM_SCNT          /* 학습시간초수 */                     
             ,CRTR_ID              /* 생성자ID */ 
             ,CRT_DTM              /* 생성일시 */ 
             ,MDFR_ID              /* 수정자ID */   
             ,MDF_DTM              /* 수정일시 */ 
             ,DB_ID                /* DBID */                       
             )
        VALUES 
            (
             #{optTxbId}
            ,#{param.spLrnId}
            ,#{param.spLrnCtnId}
            ,#{userId}
            ,#{param.lrnStCd}
            ,#{param.lrnTmScnt}
            ,#{userId}
            ,NOW()
            ,#{userId}
            ,NOW()
            ,#{dbId}
            )
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - saveLrnCtnInfo  */
    </insert>
    <!-- 특별학습 2depth노드 조회 MyHome 포인트 지급관련 쿼리-->
    <!-- 
    <select id="selectSplrnNodDto" resultType="com.aidt.api.sl.lrnwif.dto.SlLrnwNodDto">
        SELECT 
             A.SP_LRN_ID          AS LLU_NOD_ID                      /* 특별학습ID*/
            ,A.SP_LRN_NOD_ID      AS TC_NOD_ID                       /* 특별학습 노드아이디*/ 
            ,A.SP_LRN_NOD_NM      AS TC_NOD_NM                       /* 특별학습 노드명*/ 
        FROM LMS_CMS.BC_SP_LRN_CTN B /* BC_특별학습콘텐츠 */
        LEFT JOIN LMS_CMS.BC_SP_LRN_NOD A /* BC_특별학습노드 */
            ON B.SP_LRN_NOD_ID = A.SP_LRN_NOD_ID
            AND A.SP_LRN_ID = #{param.lluNodId}                     /* 특별학습ID*/
            AND A.URNK_SP_LRN_NOD_ID = #{param.tcNodId}             /* 특별학습 상위노드ID*/ 
            AND A.DEL_YN = 'N'
        WHERE B.SP_LRN_CTN_ID = #{param.strAtvId}                   /* 특별학습 컨텐츠ID*/
        AND B.DEL_YN = 'N'
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectSplrnNodDto */
    </select>
    -->
    <!-- 특별학습 활동 업데이트-->
    <update id="updateLrnCtnInfo" >
        UPDATE LMS_LRM.SL_SP_LRN_PGRS_ST /* SL_특별학습진행상태 */
        SET LRN_ST_CD = IF(LRN_ST_CD = 'CL', 'CL', #{param.lrnStCd})    /* 학습 상태 코드 - 완료상태면 완료 상태 유지*/
           ,LRN_TM_SCNT = #{param.lrnTmScnt}                            /* 학습시간 초수 */
           ,MDFR_ID = #{userId}                                         /* 수정자 = 유저아이디*/ 
           ,MDF_DTM = NOW()                                             /* 수정일시 = 현재시간*/ 
        WHERE OPT_TXB_ID = #{optTxbId}                                  /* 운영교과서ID*/ 
          AND SP_LRN_ID = #{param.spLrnId}                                /* 특별학습ID*/ 
          AND SP_LRN_CTN_ID = #{param.spLrnCtnId}                         /* 특별학습컨텐츠 ID*/ 
          AND LRN_USR_ID = #{userId}                                      /* 사용자 ID*/ 
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - updateLrnCtnInfo */
    </update>

    <!-- 특별학습 완료 건수 select -->
    <!-- 기획안 v3.1대응= 불필요 코드 삭제
    <select id="selectLrnPgrsInfo" resultType="com.aidt.api.sl.lrnwif.dto.SlLrnwPrgsDto">    
      SELECT A.SP_LRN_ID                                                               /* 특별학습ID */
            ,A.URNK_SP_LRN_NOD_ID                                                      /* 상위노드 */
            ,COUNT(1)                                               AS ENTIRE          /* 전체건수 */
            ,SUM(IF(C.LRN_ST_CD = 'CL', 1, 0))                      AS DONE            /* 완료건수 */
            ,IF(COUNT(1) = SUM(IF(C.LRN_ST_CD = 'CL', 1, 0)), 1, 0) AS TOTAL_DONE      /* 완료 건수 == 전체건수 일때 1반환 */
        FROM LMS_CMS.BC_SP_LRN_NOD A /* BC_특별학습노드 */
            LEFT JOIN LMS_CMS.BC_SP_LRN_CTN B /* BC_특별학습콘텐츠 */
                   ON A.SP_LRN_NOD_ID = B.SP_LRN_NOD_ID
                  AND B.DEL_YN = 'N'
            LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST C /* SL_특별학습진행상태 */
                   ON C.SP_LRN_ID = A.SP_LRN_ID
                  AND C.SP_LRN_CTN_ID = B.SP_LRN_CTN_ID
                  AND C.OPT_TXB_ID = #{optTxbId}
                  AND C.LRN_USR_ID = #{userId}
        WHERE A.SP_LRN_ID = #{param.spLrnId}                                            /* 특별학습ID */
          AND A.URNK_SP_LRN_NOD_ID = #{param.spLrnNodId}                                /* 상위노드ID */
          AND A.LWS_YN = 'Y'                                                            /* 최하위여부 Y:여 N:부 */
          AND A.DEL_YN = 'N'
        GROUP BY A.URNK_SP_LRN_NOD_ID 
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectLrnPgrsInfo */    
    </select>
    -->
    
    <!-- 과제 select -->
    <!-- 기획안 v3.1대응= 불필요 코드 삭제
    <select id="selectEaAsnInfo" resultType="com.aidt.api.sl.lrnwif.dto.SlLrnwAsnDto">
        SELECT A.ASN_ID                                                /* 과제ID */
              ,IFNULL(A.TTL_LRN_CNT, 0)  AS TTL_LRN_CNT                /* 총학습수 */
              ,IFNULL(C.CMPL_LRN_CNT, 0) AS CMPL_LRN_CNT               /* 완료학습수 */
              ,C.SMT_CMPL_YN                                           /* 제출완료여부 */
        FROM LMS_LRM.EA_ASN A /* EA_과제 */
            INNER JOIN LMS_LRM.EA_ASN_RNGE B  /* EA_과제범위 */
                    ON A.ASN_ID = B.ASN_ID
                   AND A.OPT_TXB_ID = B.OPT_TXB_ID
                   AND A.LRN_TP_CD = B.LRN_TP_CD
                   AND B.SP_LRN_ID =  #{param.spLrnId} 
                   AND B.LU_NOD_ID = #{param.spLrnNodId} 
                   AND B.LRN_TP_CD = 'SL'                                   /* 특별 학습 */
                   AND B.DEL_YN = 'N'
            INNER JOIN LMS_LRM.EA_ASN_SMT C /* EA_과제제출 */
                    ON A.ASN_ID = C.ASN_ID
                   AND C.STU_USR_ID = #{userId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
          AND A.LRN_TP_CD = 'SL'
          AND A.DEL_YN='N'
        LIMIT 1
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - selectEaAsnInfo */
    </select>
    -->
    <!-- 과제 update -->
    <!-- 기획안 v3.1대응= 불필요 코드 삭제
    <update id="updateEaAsnInfo">
        UPDATE LMS_LRM.EA_ASN_SMT /* EA_과제제출 */
        SET MDFR_ID = #{userId}
           ,MDF_DTM = NOW()
           ,CMPL_LRN_CNT = #{cmplLrnCnt}                        /*  완료학습수*/
           <if test = 'smtCmplYn == "Y"'>
               ,SMT_DTM = NOW()
               ,SMT_CMPL_YN = 'Y'                               /*  제출완료여부 Y:여 N:부 */
           </if>
        WHERE ASN_ID = #{asnId}
          AND STU_USR_ID = #{userId}
        
        /**  특별학습 정은혜 SlLrnwifStu-Mapper.xml - updateEaAsnInfo */
    </update>
    -->
    
    <!-- 외부활동설정 -->
    <select id="selectExtAtvSetm" parameterType="Map" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm">
        SELECT
        	CLS_PPNG_USE_YN
        	,CLS_LINK_USE_YN
        	,CLS_ARCHV_USE_YN
        	,CLS_TSHRP_USE_YN
    	FROM LMS_LRM.cm_ext_atv_setm
    	WHERE OPT_TXB_ID = #{optTxbId}
        /** 특별학습 김형준 SlLrnwifStu-Mapper.xml - selectExtAtvSetm */
    </select>
    
    <!-- 외부활동추가링크 -->
    <select id="selectExtLink" parameterType="Map" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink">
        SELECT
        	EXTR_LINK_NM
        	,EXTR_LINK_URL
    	FROM LMS_LRM.cm_ext_atv_extr_link
    	WHERE OPT_TXB_ID = #{optTxbId}
    	AND DEL_YN = "N"
    	ORDER BY EXTR_LINK_NO ASC
        /** 특별학습 김형준 SlLrnwifStu-Mapper.xml - selectExtAtvSetm */
    </select>
</mapper>