package com.aidt.api.bc.slpp.stu;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.slpp.dto.BcSlppComandDto;
import com.aidt.api.bc.slpp.dto.BcSlppDto;
import com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto;
import com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:47
 * @modify 2024-01-05 17:52:47
 * @desc 학생_쪽지 Controller
 */

@Slf4j
@Tag(name="[bc] 쪽지[BcSlppStu]", description="쪽지(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/slpp")
public class BcSlppStuController {

    @Autowired
    private BcSlppStuService bcSlppStuService;

    /**
     * 쪽지 목록 조회 요청
     *
     * @param dto: BcSlppDto
     * @return ResponseList<BcSlppDto>
     */
    @Operation(summary="쪽지 목록 조회", description="쪽지 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectSlppList")
    public ResponseDto<List<BcSlppDto>> selectSlppList(BcSlppPagingRequestDto dto) {
        log.debug("Entrance selectSlppList");
        return Response.ok(bcSlppStuService.selectSlppList(dto));
    }

    /**
     * 쪽지 내용 등록 요청
     *
     * @param dto: BcSlppDto
     * @return ResponseDto<int>
     */
    @Operation(summary="쪽지 등록", description="필기를 등록한다.(학생)")
    @PostMapping(value = "/insertSlpp")
    public ResponseDto<Integer> insertSlpp(@RequestBody BcSlppDto dto){
    	log.info("bcSlppDto : " + dto);
    	log.info("insertSlpp : bcSlppDto2 : " + dto.getSlppCn());
    	log.debug("Entrance insertSlpp");
    	return Response.ok(bcSlppStuService.insertSlpp(dto));
    }

    /**
     * 쪽지 삭제 요청
     *
     * @param list: List<BcSlppDto>
     * @return ResponseDto<int>
     */
    @Operation(summary="쪽지 삭제", description="쪽지를 삭제한다.(학생)")
    @PostMapping(value = "/deleteSlppList")
    public ResponseDto<Integer> deleteSlpp(@RequestBody List<BcSlppDto> list) {
    	log.debug("Entrance deleteSlpp");
        log.info("deleteSlpp : bcSlppDto : " + list.stream().map(dto->dto.getSlppId()+"").collect(Collectors.joining(", ")));
    	// 데이터 검증부분 확인
        return Response.ok(bcSlppStuService.deleteSlpp(list));
    }

    /**
     * 쪽지 삭제 요청
     *
     * @param dto: BcSlppDto
     * @return ResponseDto<int>
     */
    @Operation(summary="쪽지 삭제", description="쪽지를 삭제한다.(학생)")
    @PostMapping(value = "/deleteSlpp")
    public ResponseDto<Integer> deleteSlpp(@RequestBody BcSlppDto dto) {
        log.debug("Entrance deleteSlpp");
        log.info("deleteSlpp : bcSlppDto : " + dto);
        // 데이터 검증부분 확인
        return Response.ok(bcSlppStuService.deleteSlpp(dto));
    }

    /**
     * 쪽지 전체 삭제 요청
     *
     * @param dto :BcSlppDto
     * @return Integer
     */
    @Operation(summary="쪽지 전체 삭제", description="쪽지를 삭제한다.")
    @PostMapping(value = "/deleteAllSlpp")
    public ResponseDto<Integer> deleteAllSlpp(@RequestBody BcSlppComandDto dto) {
        log.debug("Entrance deleteSlpp");
        log.info("deleteSlpp : bcSlppDto : " + dto);
        // 데이터 검증부분 확인
        return Response.ok(bcSlppStuService.deleteAllSlpp(dto));
    }

    /**
     * 학생 대화 시간 조회
     *
     * @param optTxbId : String
     * @return BcSlppTmSetmDto
     */
    @Operation(summary="교사 대화 시간 조회", description="교사 대화 시간 조회한다")
    @GetMapping(value = "/selectSlppTmSetm")
    public ResponseDto<BcSlppTmSetmDto> selectSlppTmSetm(String optTxbId) {
        log.info("selectSlppTmSetm: " + optTxbId );
        return Response.ok(bcSlppStuService.selectSlppTmSetm(optTxbId));
    }
    
    /**
	 * 읽지 않은 대화 존재여부 조회
	 * @param dto : BcSlppPagingRequestDto
	 * @return int
	 */
    @Operation(summary="읽지 않은 대화 여부 조회", description="읽지 않은 대화 존재 여부를 조회한다.")
    @GetMapping(value = "/selectSlppCofmCnt")
    public ResponseDto<Integer> selectSlppCofmCnt(BcSlppPagingRequestDto dto) {
        log.info("selectSlppList" + dto);
        return Response.ok(bcSlppStuService.selectSlppCofmCnt(dto));
    }
}
