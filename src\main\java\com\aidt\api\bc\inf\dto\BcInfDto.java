package com.aidt.api.bc.inf.dto;

import java.util.List;

import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.api.bc.tnte.dto.BcTnteDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 알림 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcInfDto{

	@Parameter(name="알림ID")
	private int infmId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교사사용자ID")
	private String tcrUsrId;

	@Parameter(name="알림메시지ID")
	private int infmMsgId;
	
	@Parameter(name="알림대상사용자ID")
	private String infmObjUsrId;

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="확인여부")
	private String cofmYn;
	
	@Parameter(name="알림유형코드")
	private String infmTpCd;
	
	@Parameter(name="알림분류코드")
	private String infmClCd;
	
	@Parameter(name="알림상세분류코드")
	private String infmDtlClCd;
	
	@Parameter(name="학생리스트")
	List<BcStuListDto> stuList;
	
	@Parameter(name="알림명")
	private String infmNm;
	
	@Parameter(name="알림내용")
	private String infmCn;
	
	@Parameter(name="알림이동구분코드")
	private String infmMvDvCd;
	
	@Parameter(name="알림이동내용")
	private String infmMvCn;
	
	@Parameter(name="요청 db 오프셋")
    public int getPageOffset() {
        return pageSize * pageNo;
    }
	
	@Parameter(name="알림태그리스트")
	private String[] infTagList;
	
	private String crtrId;
	private String crtDtm;
	private String mdfrId;
	private String mdfDtm;
	private String dbId;
	
	private int pageNo;
	private int pageSize;
	private int totalCnt;
	private int startPageNo;
	private int dataSize;

}
