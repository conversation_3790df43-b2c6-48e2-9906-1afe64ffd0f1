package com.aidt.api.at.token;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.appevent.service.AdminStatKafkaService;
import com.aidt.api.at.token.dto.KerisDto;
import com.aidt.api.at.token.dto.KerisLrnDataDto;
import com.aidt.api.at.token.dto.KerisLrnDataUpsertDto;
import com.aidt.api.at.token.dto.KerisLrnOutDataDto;
import com.aidt.api.at.util.AES128Chiper;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.KerisFrontLogDto;
import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-11-30 10:40:01
 * @modify date 2023-11-30 10:40:01
 * @desc Token Controller
 */
@Tag(name = "[at] Keris", description = "Keris 인증")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token")
public class KerisController {

	@Value("${server.meta.textbook.systemCode}")
	private String systemCode;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;

	@Autowired
	private KerisService kerisService;

	@Autowired
	private BcCmService bcCmService;

	@Autowired
	private AdminStatKafkaService adminStatKafkaService;

	@Operation(summary = "Keris 인증", description = "Keris 인증")
	@PostMapping(value = "/keris")
	public Map<String, Object> kerisCheck(@RequestBody KerisDto kerisDto) {

		// 2024-07-01 학생 상태별 로그인 처리 및 선행데이터 구성, 전입 / 전출 추가
		String userId = kerisDto.getUser_id();
		String userType = kerisDto.getUser_type();
		String userStatus = kerisDto.getUser_status();

		Map<String, Object> map = new HashMap<>();
		map.put("code", "00000");
		map.put("message", "성공");
		map.put("user_id", userId);
		map.put("api_version", kerisDto.getApi_version()); // 2025.01.13 규격문서 2.3 요청사항 response api_version 추가 (호출시 주가된
															// 버전값 그대로 전송)

		/*
		 * 1. 학생 UUID로 학급 구성이 되어있는지 판단(교사가 선 진입으로 최초 데이터 구성이 필요함) 2. 학생 상태별 전입 / 전출 분기
		 */
		if ("S".equals(userType)) {

			// 1번 진행

			/*
			 * int checkCnt = bcCmService.getClaAndOptTxbCheck(kerisDto.getUser_id());
			 * if(checkCnt == 0) { map.put("code", "40401"); map.put("message",
			 * "학급데이터 구성 필요(교사 로그인 선행)"); return map; }
			 */

			// 2번 진행
			if ("I".equals(userStatus)) {
				// 전입

				List<KerisLrnDataDto> dataList = kerisDto.getData();
				if (dataList != null && dataList.size() > 0) {

					// 필드 미참조 방어 로직(객체 분리)
					for (KerisLrnDataDto data : dataList) {
						KerisLrnDataUpsertDto upsertDto = new KerisLrnDataUpsertDto();
						upsertDto.setUsrId(userId);
						upsertDto.setDbId(systemCode);
						upsertDto.setStnSstEduCrsId(data.getCurriculum());
						upsertDto.setEduCrsAchBsCd(data.getAchievement_level());
						upsertDto.setPgrsRt(data.getPercent());
						upsertDto.setDevrLrmpId(data.getPartner_curriculum());

						// 전입 데이터 upsert 진행
						// bcCmService.upsertKerisStuLrnInfo(upsertDto);

						kerisService.upsertAtNtlvEduCrsStnData(upsertDto);
					}
				}
			} else if ("O".equals(userStatus)) {
				// 전출

				// List<KerisLrnDataDto> lrnDataList =
				// bcCmService.selectCmNtlvEduCrsStnSstList(userId);

				List<KerisLrnOutDataDto> lrnDataList = kerisService.selectAtCmNtlvEduCrsStnSstList(userId);
				if (lrnDataList != null && lrnDataList.size() > 0) {
					map.put("data", lrnDataList);
					map.put("count", lrnDataList.size());
				} else {
					map.put("data", new ArrayList<KerisLrnOutDataDto>());
					map.put("count", 0);
				}

				// 전출 간 학생은 어떻게 처리할 예정?
				// 페이지 진입 못하게 막아야 하는건가?
			}
		}

		return map;
	}

	@Operation(summary = "Keris 인증", description = "Keris 인증")
	@GetMapping(value = "/keris")
	public String kerisUrlCheck(HttpServletRequest request, HttpServletResponse response) {
		String domain = "";
		switch (SERVER_ACTIVE) {
		case "local":
			domain = "http://localhost:9080";
			break;
		case "dev":
			domain = "https://www-n-ele.aitextbook.co.kr";
			break;
		case "prod":
			domain = "https://" + systemCode + ".aitextbook.co.kr";
			break;
		case "stg":
			domain = "https://" + systemCode + "-k.aitextbook.co.kr";
			break;
		case "stag":
			domain = "https://" + systemCode + "-k.aitextbook.co.kr";
			break;
		default:
			break;
		}

		// String access_token = request.getParameter("access_token");
		String token = request.getParameter("access_token.token");
		if (StringUtils.isNotBlank(token)) {
			token = token.replaceAll("\r", "").replaceAll("\n", "");
		}

		String access_id = request.getParameter("access_token.access_id");
		if (StringUtils.isNotBlank(access_id)) {
			access_id = access_id.replaceAll("\r", "").replaceAll("\n", "");
		}

		String user_id = request.getParameter("user_id");
		if (StringUtils.isNotBlank(user_id)) {
			user_id = user_id.replaceAll("\r", "").replaceAll("\n", "");
		}

		String user_status = request.getParameter("user_status");
		if (StringUtils.isNotBlank(user_status)) {
			user_status = user_status.replaceAll("\r", "").replaceAll("\n", "");
		}

		String user_type = request.getParameter("user_type");
		if (StringUtils.isNotBlank(user_type)) {
			user_type = user_type.replaceAll("\r", "").replaceAll("\n", "");
		}

		String api_domain = request.getParameter("api_domain");
		if (StringUtils.isNotBlank(api_domain)) {
			api_domain = api_domain.replaceAll("\r", "").replaceAll("\n", "");
		}

		String class_code = request.getParameter("class_code");
		if (StringUtils.isNotBlank(class_code)) {
			class_code = class_code.replaceAll("\r", "").replaceAll("\n", "");
		}

		String lecture_code = request.getParameter("lecture_code");
		if (StringUtils.isNotBlank(lecture_code)) {
			lecture_code = lecture_code.replaceAll("\r", "").replaceAll("\n", "");
		}

		if (StringUtils.isBlank(lecture_code)) {
			return null;
		}

		// 2024-07-01 sso 체크(로컬 테스트용 파라미터)
		String ssoCheck = request.getParameter("ssoCheck");
		if (StringUtils.isNotBlank(ssoCheck)) {
			ssoCheck = ssoCheck.replaceAll("\r", "").replaceAll("\n", "");
		}

		// 2025.02.17 위탁정보 연동 추가 (이용약관동의여부, 이용약관동의일시)
		String use_terms_agree_yn = request.getParameter("entrusted_info.use_terms_agree_yn");
		if (StringUtils.isNotBlank(use_terms_agree_yn)) {
			use_terms_agree_yn = use_terms_agree_yn.replaceAll("\r", "").replaceAll("\n", "");
		}

		String use_terms_agree_dt = request.getParameter("entrusted_info.use_terms_agree_dt");
		if (StringUtils.isNotBlank(use_terms_agree_dt)) {
			use_terms_agree_dt = use_terms_agree_dt.replaceAll("\r", "").replaceAll("\n", "");
		}

		// 2025.03.21 교시 정보 추가
		String class_period = request.getParameter("class_period");
		if (StringUtils.isNotBlank(class_period)) {
			class_period = class_period.replaceAll("\r", "").replaceAll("\n", "");
		}

		// 2024-07-01 파트너ID 조회
		String partnerId = bcCmService.getPartnerId(systemCode);

		KerisDto kerisDto = new KerisDto();
		kerisDto.setUser_id(user_id);
		kerisDto.setLecture_code(lecture_code);
		kerisDto.setKerisUsrId(user_id);
		kerisDto.setUse_terms_agree_yn(use_terms_agree_yn);
		kerisDto.setUse_terms_agree_dt(use_terms_agree_dt);
		kerisDto.setClass_period(class_period);

		kerisDto.setUser_status(user_status);
		kerisDto.setUser_type(user_type);

		try {
			kerisService.loginAndUsrCheck(kerisDto);
		} catch (Exception e) {
			String msg = e.getMessage();

			// keris 중복 call 방어
			if (StringUtils.isBlank(msg) || !(StringUtils.isNotBlank(msg) && "TOKEN_DUP".equals(msg))) {
				throw new BizException(msg);
			}

		}

		/**
		 * 관리자 회원 등록 event 통계 set
		 */
		if (kerisDto != null) {
			/**
			 * 관리자 회원 등록 event 통계 set
			 */
			if (StringUtils.isNotBlank(kerisDto.getKafkaUsrId())) {
				adminStatKafkaService.usrInsertEvent(kerisDto.getKafkaUsrId());
			}

			if (StringUtils.isNotBlank(kerisDto.getKafkaOptTxbId())
					&& StringUtils.isNotBlank(kerisDto.getKafkaOptTxbPrid())) {
				adminStatKafkaService.optTxbPridInsertEvent(kerisDto.getKafkaOptTxbId(), kerisDto.getKafkaOptTxbPrid());
			}
		}

		try {
			// 2025.02.17 이용약관동의여부, 이용약관 동의일시 추가
			response.sendRedirect(domain + "/sso?access_token.token=" + token + "&access_token.access_id=" + access_id
					+ "&user_id=" + user_id + "&user_status=" + user_status + "&user_type=" + user_type + "&api_domain="
					+ api_domain + "&class_code=" + class_code + "&lecture_code=" + lecture_code + "&ssoCheck="
					+ ssoCheck + "&partnerId=" + partnerId + "&entrusted_info.use_terms_agree_yn="
					+ (use_terms_agree_yn == null ? "" : use_terms_agree_yn) + "&entrusted_info.use_terms_agree_dt="
					+ (use_terms_agree_dt == null ? "" : use_terms_agree_dt));

		} catch (IOException e) {
			log.error("AIDT-LMS URL 호출 실패");
		}
		return null;
	}

	@Operation(summary = "Keris 전입/전출", description = "Keris 전입/전출 체크")
	@PostMapping(value = "/in-out-check")
	public Map<String, Object> kerisStuInOutCheck(@RequestBody KerisDto kerisDto) {

		// API_AUTH_001 중 전입/전출 API를 분리해야 할 케이스가 생길 수 있으므로 임시 셋팅
		String userId = kerisDto.getUser_id();
		String userType = kerisDto.getUser_type();
		String userStatus = kerisDto.getUser_status();

		Map<String, Object> map = new HashMap<>();
		map.put("code", "00000");
		map.put("message", "성공");
		map.put("user_id", userId);
		map.put("api_version", kerisDto.getApi_version()); // 2025.01.13 규격문서 2.3 요청사항 response api_version 추가 (호출시 주가된
															// 버전값 그대로 전송)

		if ("S".equals(userType)) {

			if ("I".equals(userStatus)) {
				// 전입
				List<KerisLrnDataDto> dataList = kerisDto.getData();
				if (dataList != null && dataList.size() > 0) {

					// 필드 미참조 방어 로직(객체 분리)
					for (KerisLrnDataDto data : dataList) {
						KerisLrnDataUpsertDto upsertDto = new KerisLrnDataUpsertDto();
						upsertDto.setUsrId(userId);
						upsertDto.setDbId(systemCode);
						upsertDto.setStnSstEduCrsId(data.getCurriculum());
						upsertDto.setEduCrsAchBsCd(data.getAchievement_level());
						upsertDto.setPgrsRt(data.getPercent());
						upsertDto.setDevrLrmpId(data.getPartner_curriculum());

						// 전입 데이터 upsert 진행
						bcCmService.upsertKerisStuLrnInfo(upsertDto);
					}
				}
			} else if ("O".equals(userStatus)) {
				// 전출

				List<KerisLrnDataDto> lrnDataList = bcCmService.selectCmNtlvEduCrsStnSstList(userId);
				if (lrnDataList != null && lrnDataList.size() > 0) {
					map.put("data", lrnDataList);
					map.put("count", lrnDataList.size());
				}

				// 전출 간 학생은 어떻게 처리할 예정?
				// 페이지 진입 못하게 막아야 하는건가?
				// >> cm_usr, cm_token의 cla_id와 opt_txb_id 앞의 'del_' prefix를 더해 삭제 처리
				bcCmService.deleteKerisStuLrnInfo(userId);
			}
		}

		return map;
	}

	/**
	 * 프론트에서 사용하는 곳이 확인되지 않아 주석 처리 진행
	 * 
	 * @param kerisMemberDto
	 * @return
	 */
//	@Operation(summary = "Keris  학생정보 업데이트", description = "Keris  학생정보 업데이트")
//	@PostMapping(value = "/updateKerisStuInfo")
//	public ResponseDto<Long> updateKerisStuInfo(@RequestBody KerisMemberDto kerisMemberDto) {
//		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//
//		if (kerisMemberDto == null) {
//			throw new RuntimeException("필수값이 없습니다.");
//		}
//		// 로그인 사용자 정보 체크
//		if (kerisMemberDto.getUser_id().equals(userDetails.getUsrId())) {
//			throw new RuntimeException("접근 권한이 없는 정보 입니다.");
//		}
//		if (!"TE".equals(userDetails.getUsrTpCd())) {
//			throw new RuntimeException("접근 권한이 없는 정보 입니다.(선셍님 전용 기능입니다.)");
//		}
//		if (kerisMemberDto.getMemberIds() == null || kerisMemberDto.getMemberIds().isEmpty()) {
//			throw new RuntimeException("필수값이 없습니다.");
//		}
//
//		if (StringUtils.isEmpty(kerisMemberDto.getClass_code())
//				|| StringUtils.isEmpty(kerisMemberDto.getLecture_code())) {
//			throw new RuntimeException("필수값이 없습니다.");
//		}
//
//		return Response.ok(kerisService.updateKerisStuInfo(kerisMemberDto) * 1L);
//	}

	@Operation(summary = "Keris  ", description = "Keris  학생정보 업데이트")
	@GetMapping(value = "/selectEncLoginId")
	public Response.ResponseDto<Map<String, Object>> selectEncLoginId(String uuid) throws Exception {

		AES128Chiper chiper = new AES128Chiper();

		Map<String, Object> map = new HashMap<>();

		String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
		String encLogindId = chiper.encAES(uuid + "|" + currentTime);
		map.put("encLogindId", encLogindId);

		return Response.ok(map);
	}
	
	@Operation(summary = "Keris Front 연동 결과등록", description = "Keris Front 연동 결과등록")
	@PostMapping(value = "/frontLogInsert")
	public ResponseDto<Object> frontLogInsert(@RequestBody KerisFrontLogDto dto) {
		dto.setSystemCd(systemCode);
		return Response.ok(bcCmService.insertKerisFrontLog(dto));
	}

}
