package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlDto {

	@Parameter(name="순번")
	private int idx;
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="과목")
    private String sbjCd;
	
    /** 학습맵 노드 ID */
    @Parameter(name="학습맵 노드 ID")
    private String lrmpNodId;

    /** 상위 노드 ID */
    @Parameter(name="상위 노드 ID")
    private String urnkLrmpNodId;
    
    /** 학습맵 노드명 */
    @Parameter(name="학습맵 노드명")
    private String lrmpNodNm;
    
    /** 깊이 */
    @Parameter(name="깊이")
    private int dpth;
    
    /** 재구성 순서 */
    @Parameter(name="재구성 순서")
    private int rcstnOrdn;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;

    /** 중단원, 차시*/
    @Parameter(name="중단원, 차시")
    private List<EaLrnRptTlCmTxbTcDto> child;
    
    @Parameter(name="단원별 차시 정보")
    private List<String> lluAllTcInfo;
    
    @Parameter(name = "단원번호사용여부")
    private String luNoUseYn;

}
