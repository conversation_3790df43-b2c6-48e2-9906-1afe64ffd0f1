/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 04:06:02
 * @modify 2024. 6. 22.
 * @desc 
 */
package com.aidt.api.ea.lrnrpt.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EaAllrAnRpotDto {
	
	private String lrmpNodId;
	private String rcstnOrdn;
	private String rcstnNo;
	private String lrmpNodNm;
	private int fnlQstCnt;
	private int totAnsCnt;
	private int iansCnt;
	private int cansCnt;
//	private int cansRt;
	private double cansRt;
	
	private String evId;
	private String evDvCd;
	private String evDvNm;
	private String evDtlDvCd; //
	private String evDtlDvNm;
	private String evNm;
	
	private String eduCrsAchBsId;
	private String eduCrsAchBsCd;
	private String eduCrsAchBsCnNm;
	
	private String tpcId;
	private String kmmpNodId;
	private String kmmpNodNm;
	private String oCnt;
	private String xCnt;
	

	private String usrId;
	private String optTxbId;
	
	
}
