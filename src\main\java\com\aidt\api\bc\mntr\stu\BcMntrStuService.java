package com.aidt.api.bc.mntr.stu;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.bc.mntr.dto.BcMntrJsonDto;
import com.aidt.common.CommonDao;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-03-18 17:43:34
 * @modify 2024-03-18 17:43:34
 * @desc 교사_실시간모니터링 Service
 */

@Service
public class BcMntrStuService {

	private final String MAPPER_NAMESPACE = "api.bc.mntr.stu.";

	@Autowired
	private CommonDao commonDao;

    /**
     * 실시간 모니터링 현재학습콘텐츠 저장(학생)
     *
     * @param BcMntrDto
     * @return int
     */
	public int updateStuLrnCtt(BcMntrDto bcMntrDto) {
		return commonDao.insert(MAPPER_NAMESPACE + "updateStuLrnCtt", bcMntrDto);
	}
	
	/**
     * 실시간 모니터링 현재학습콘텐츠 저장(학생)
     *
     * @param BcMntrDto
     * @return int
     */
	public int updateStuCurConn(BcMntrDto bcMntrDto) {
		ObjectMapper objectMapper = new ObjectMapper();
		List<BcMntrJsonDto> existingJsonData = new ArrayList<>();

		Map<String, Object> params = new HashMap<>();
		params.put("optTxbId", bcMntrDto.getOptTxbId());
		params.put("lrnUsrId", bcMntrDto.getLrnUsrId());

		// 기존 JSON 데이터 조회
		String existingJson = commonDao.select(MAPPER_NAMESPACE + "selectGdeNeedTp", params);

		// 기존 데이터 파싱
		if (existingJson != null && !existingJson.isEmpty()) {
			try {
				existingJsonData = objectMapper.readValue(existingJson, new TypeReference<List<BcMntrJsonDto>>() {});
			} catch (JsonProcessingException e) {
				return -1;
			}
		} else {
			existingJsonData = new ArrayList<>();
		}

		// 오늘 날짜
		LocalDateTime now = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM.dd");
		String lrnDate = now.format(formatter);
//	
//		// 새로운 데이터 객체 생성
//		BcMntrJsonDto newJsonData = new BcMntrJsonDto();
//		newJsonData.setCd("A");
//		newJsonData.setType("접속 종료");
//		newJsonData.setDate(lrnDate);
//		newJsonData.setMessage("학생이 AIDT에서 로그아웃하거나 인터넷 연결이 끊겼습니다. 해당 학생의 상태를 확인해주세요.");
//
//		// 기존 데이터 리스트에 새 데이터 추가 (역순 : 제일 앞에 추가함)
//		existingJsonData.add(0, newJsonData);
		
		
		Optional<BcMntrJsonDto> concentrationIssue = existingJsonData.stream()
		        .filter(jsonDto -> "A".equals(jsonDto.getCd()))
		        .findFirst();

		if (concentrationIssue.isPresent()) {
			// 기존 데이터 업데이트
			concentrationIssue.get().setDate(lrnDate);
		} else {
			// 새 데이터 추가
			BcMntrJsonDto newJsonData = new BcMntrJsonDto("A", "접속 종료", lrnDate, "학생이 AIDT에서 로그아웃하거나 인터넷 연결이 끊겼습니다. 해당 학생의 상태를 확인해주세요.");
			existingJsonData.add(0, newJsonData); // 맨 앞에 추가
		}


		// JSON 리스트를 다시 문자열로 변환
		try {
			String updatedJson = objectMapper.writeValueAsString(existingJsonData);
			bcMntrDto.setGdeNeedTp(updatedJson);
			bcMntrDto.setCurConnYn("N");
		} catch (JsonProcessingException e) {
			return -1;
		}

		return commonDao.insert(MAPPER_NAMESPACE + "updateStuCurConn", bcMntrDto);
	}
	
	/**
     * 지도필요 insert
     *
     * @param BcMntrDto
     * @return int
     */
	public int insertGdeNeed(BcMntrDto bcMntrDto) {
		return commonDao.insert(MAPPER_NAMESPACE + "insertGdeNeed", bcMntrDto);
	}
}
