<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.at.keris">

	<!-- CM_TOKEN - 토큰 테이블에 정보 조회 -->
	<select id="selectCheckTokenInfo" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto" resultType="int">
		/** Keris-Mapper.xml - selectCheckTokenInfo */
		SELECT COUNT(*) FROM LMS_LRM.CM_TOKEN WHERE USR_ID = #{usrId}
	</select>
	
	<select id="selectCheckTokenInfoUsrId" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto" resultType="String">
		/** Keris-Mapper.xml - selectCheckTokenInfoUsrId */
		SELECT 
			USR_ID
		FROM 
			LMS_LRM.CM_TOKEN 
		WHERE
		<if test="usrTpCd != null and usrTpCd.equals('TE')">
			keris_usr_id = #{kerisUsrId}
		AND opt_txb_id = #{optTxbId}
		</if>
		<if test="usrTpCd == null or !usrTpCd.equals('TE')">
			USR_ID = #{usrId}
		</if>
	</select>

	<!-- TEMP_LOGIN - 임시 로그인 테이블에 정보 등록 (학생) -->
	<insert id="insertTokenInfo" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto">
		/** Keris-Mapper.xml - insertTokenInfo */
		INSERT INTO LMS_LRM.CM_TOKEN(
			USR_ID, KERIS_USR_ID, LOGIN_ID, PWD, OPT_TXB_ID, CLA_ID, TXB_ID
		) VALUES (
			#{usrId}
			, #{kerisUsrId}
			, #{loginId}
			<![CDATA[
				, concat(#{loginId}, '_!@12')
			]]>
			, #{optTxbId}
			, #{claId}
			, #{txbId}
		)
	</insert>
	
	<insert id="insertTokenInfoTE" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto">
		<selectKey keyProperty="usrId" order="BEFORE" resultType="String">
			SELECT CONCAT(DATE_FORMAT(now(6), '%Y%m%d%H%i%s%f'), '-', SUBSTR(replace(UUID(), '-', ''), 1, 15)) AS usrId
		</selectKey>
		/** Keris-Mapper.xml - insertTokenInfoTE */
		INSERT INTO LMS_LRM.CM_TOKEN(
			USR_ID, KERIS_USR_ID, LOGIN_ID, PWD, OPT_TXB_ID, CLA_ID, TXB_ID
		) VALUES (
			#{usrId}
			, #{kerisUsrId}
			, #{loginId}
			<![CDATA[
				, concat(#{loginId}, '_!@12')
			]]>
			, #{optTxbId}
			, #{claId}
			, #{txbId}
		)
	</insert>
	
    <resultMap id="KerisLrnDataResultMap" type="com.aidt.api.at.token.dto.KerisLrnOutDataDto">
        <result property="curriculum" column="STN_SST_EDU_CRS_ID"/>
        <result property="achievement_level" column="EDU_CRS_ACH_BS_CD"/>
        <result property="percent" column="PGRS_RT"/>
    </resultMap>
	
    
	
	<!-- 학생 로그인 후 전출데이터 추출 -->
	<select id="selectAtCmNtlvEduCrsStnSstList" parameterType="string" resultMap="KerisLrnDataResultMap">
		/** Keris-Mapper.xm - selectAtCmNtlvEduCrsStnSstList */
		SELECT 
			CRCL_CTN_ELM2_CD AS STN_SST_EDU_CRS_ID
			, IFNULL(EDU_CRS_ACH_BS_CD, '') AS EDU_CRS_ACH_BS_CD
			, PGRS_RT
		FROM 
			LMS_LRM.CM_NTLV_EDU_CRS_STN_SST
		WHERE USR_ID = #{usrId}
		AND PGRS_RT IS NOT NULL
		ORDER BY CRT_DTM
	</select>
	
	
	
	<resultMap id="KerisMemberInfoResultMap" type="com.aidt.api.at.token.dto.KerisMemberInfoDto">
        <result property="usrId" column="USR_ID"/>
        <result property="usrTpCd" column="USR_TP_CD"/>
        <result property="claId" column="CLA_ID"/>
        <result property="dbId" column="DB_ID"/>
        <result property="txbId" column="TXB_ID"/>
        <result property="optTxbId" column="OPT_TXB_ID"/>
        <result property="loginId" column="LOGIN_ID"/>
    </resultMap>
    
	<!-- 같은 클래스 정보 사용자  조회  -->
	<select id="selectUserList" parameterType="string" resultMap="KerisMemberInfoResultMap">
		/** Keris-Mapper.xml - selectAtCmNtlvEduCrsStnSstList */
		SELECT 
			a.USR_ID,a.USR_TP_CD,a.DB_ID,b.TXB_ID ,b.OPT_TXB_ID,b.CLA_ID,b.LOGIN_ID
		FROM 
			LMS_LRM.CM_USR a
		INNER JOIN LMS_LRM.CM_TOKEN b ON a.USR_ID=b.USR_ID 
		WHERE a.CLA_ID= #{claId} AND a.USR_TP_CD='ST'
	</select>
	
	<select id="selectProfUserInfo" parameterType="string" resultMap="KerisMemberInfoResultMap">
		/** Keris-Mapper.xml - selectProfUserInfo */
		SELECT 
			a.USR_ID,a.USR_TP_CD,a.DB_ID,b.TXB_ID ,b.OPT_TXB_ID,b.CLA_ID,b.LOGIN_ID
		FROM 
			LMS_LRM.CM_USR a
		INNER JOIN 
			LMS_LRM.CM_TOKEN b ON a.USR_ID=b.USR_ID 
		WHERE a.USR_ID= #{usrId} AND a.USR_TP_CD='TE'
	</select>
	
	
	<!-- 표준체계ID별 데이터 저장(전입 / 전출에도 사용) -->
	<insert id="upsertAtNtlvEduCrsStnData" parameterType="com.aidt.api.at.token.dto.KerisLrnDataUpsertDto">
		/** BcCm-Mapper.xml - upsertAtNtlvEduCrsStnData */
		INSERT INTO LMS_LRM.CM_NTLV_EDU_CRS_STN_SST 
		(USR_ID, CRCL_CTN_ELM2_CD, EDU_CRS_ACH_BS_CD, PGRS_RT, DEVR_LRMP_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES (
			#{usrId}
			, #{stnSstEduCrsId}
			, #{eduCrsAchBsCd}
			, #{pgrsRt}
			, #{devrLrmpId}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
		ON DUPLICATE KEY UPDATE
			EDU_CRS_ACH_BS_CD = VALUES(EDU_CRS_ACH_BS_CD),
			PGRS_RT = CASE WHEN PGRS_RT IS NULL THEN VALUES(PGRS_RT) ELSE PGRS_RT END,
			DEVR_LRMP_ID = VALUES(DEVR_LRMP_ID),
			MDF_DTM = NOW()
	</insert>
	

	<!-- USR_ID - 사용자 정보 조회 -->
	<select id="selectCheckUserInfo" parameterType="string" resultType="int">
		/** Keris-Mapper.xml - selectCheckUserInfo */
		SELECT 
			COUNT(*) 
		FROM 
			LMS_LRM.CM_USR 
		WHERE
			USR_ID = #{usrId}
	</select>

	<!-- USR_ID - 사용자 정보 저장 -->
	<insert id="insertUserInfo" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto">
		/** Keris-Mapper.xml - insertUserInfo */
		INSERT INTO LMS_LRM.CM_USR(
			USR_ID, KERIS_USR_ID, LRNR_VEL_TP_CD, USR_TP_CD, FST_REG_DTM, USR_ST_CD
			, CLA_ID, FLN_ST_CD, NTR_YN, KERIS_TERM_AGR_YN, KERIS_TERM_AGR_DT
			, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{usrId}
			, #{kerisUsrId}
			, 'NM'
			, #{usrTpCd}
			, NOW()
			, '1'
			, #{claId}
			, 'NM'
			, 'N'
			, #{kerisTermAgrYn}
			, #{kerisTermAgrDt}
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{dbId}
		)
		
	</insert>
	
	<select id="selectUsrKerisTermAgrYnChg" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto" resultType="string">
		/** Keris-Mapper.xml - selectUsrKerisTermAgrYnChg */
		SELECT
		<if test="kerisTermAgrYn == null"> 
			CASE WHEN u.keris_term_agr_yn IS NULL THEN 'N' ELSE 'Y' END AS chg_yn
		</if>
		<if test="kerisTermAgrYn != null">
			CASE WHEN u.keris_term_agr_yn = #{kerisTermAgrYn} THEN 'N' ELSE 'Y' END AS chg_yn
		</if>
		FROM
			lms_lrm.cm_usr u
		WHERE
			u.usr_id = #{usrId}
	</select>
	
	<update id="updateUsrKerisTermAgr" parameterType="com.aidt.api.at.token.dto.AtUsrInfoDto">
		/** Keris-Mapper.xml - updateUsrKerisTermAgr */
		UPDATE
			lms_lrm.cm_usr
		SET
			 keris_term_agr_yn = #{kerisTermAgrYn}
		    ,keris_term_agr_dt = #{kerisTermAgrDt}
		WHERE
			usr_id = #{usrId}
	</update>
	
	<select id="selectOptTxbPridCnt" parameterType="com.aidt.api.at.token.dto.OptTxbPridDto" resultType="int">
		/** Keris-Mapper.xml - selectOptTxbPridCnt */
		SELECT
			COUNT(otp.opt_txb_id) AS cnt
		FROM
			lms_lrm.cm_opt_txb_prid otp
		WHERE
			otp.opt_txb_id = #{optTxbId}
		AND otp.opt_txb_prid = #{optTxbPrid}
	</select>
	
	<insert id="insertOptTxbPrid" parameterType="com.aidt.api.at.token.dto.OptTxbPridDto">
		/** Keris-Mapper.xml - insertOptTxbPrid */
		INSERT INTO lms_lrm.cm_opt_txb_prid
		(
			 opt_txb_id
			,opt_txb_prid
			,keris_lect_cd
			,crtr_id
			,reg_dtm
		)
		VALUES
		(
			 #{optTxbId}
			,#{optTxbPrid}
			,#{kerisLectCd}
			,#{crtrId}
			,NOW()
		)
		ON DUPLICATE KEY UPDATE 
			 reg_dtm = NOW()
	</insert>

</mapper>