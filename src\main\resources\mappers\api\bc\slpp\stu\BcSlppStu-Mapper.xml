<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.slpp.stu">
	<!-- 쪽지 조회 -->
	<select id="selectSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto" resultType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppStu-Mapper.xml - selectSlppList */
		SELECT
			SLPP_ID,
			OPT_TXB_ID,
			TCR_USR_ID,
			STU_USR_ID,
			TRNM_USR_TP_CD,
			GRUP_SND_DV_CD,
			SLPP_CN,
			COFM_YN,
			TCR_DEL_YN,
			STU_DEL_YN,
			CRTR_ID,
			CRT_DTM,
			DATE_FORMAT(CRT_DTM, '%y-%m-%d') AS CRT_DTM_FORMAT1,
			(CASE
	         WHEN INSTR(DATE_FORMAT(CRT_DTM, '%Y-%m-%d %p %h:%i'), 'PM') > 0
	         THEN CONCAT('오후 ',DATE_FORMAT(CRT_DTM, '%h:%i'))
	         ELSE CONCAT('오전 ',DATE_FORMAT(CRT_DTM, '%h:%i'))
	         END) AS CRT_DTM_FORMAT2,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		FROM LMS_LRM.CM_SLPP
		WHERE TCR_USR_ID = #{tcrUsrId}
		AND STU_USR_ID = #{stuUsrId}
		AND TCR_DEL_YN != 'Y'
		AND STU_DEL_YN != 'Y'
		<if test = 'slppCn != null and !"".equals(slppCn)'>
			AND SLPP_CN LIKE CONCAT('%', #{slppCn}, '%')
		</if>
		ORDER BY CRT_DTM DESC, SLPP_ID DESC
		<if test = 'pageSize != null and !"".equals(pageSize) and pageNo != null and !"".equals(pageNo)'>
			LIMIT #{pageSize}
			OFFSET #{pageOffset}
		</if>
		;/** BcSlppStu-Mapper.xml - selectSlppList */
	</select>

	<!-- 쪽지 확인 처리 -->
	<update id="updateCofmYnSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto">
		/** BcSlppStu-Mapper.xml - updateCofmYnSlppList */
		UPDATE LMS_LRM.CM_SLPP CS
		SET
			CS.COFM_YN = 'Y',
			CS.MDF_DTM = NOW()
		WHERE CS.TCR_USR_ID = #{tcrUsrId}
		AND CS.STU_USR_ID = #{stuUsrId}
		AND (CS.TCR_USR_ID = #{crtrId} OR CS.STU_USR_ID = #{crtrId})
		AND CS.CRTR_ID != #{crtrId}
		;/** BcSlppStu-Mapper.xml - updateCofmYnSlppList */
	</update>
	
	<!-- 학생 쪽지 등록 -->
	<insert id="insertSlpp" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppStu-Mapper.xml - insertSlpp */
		INSERT INTO LMS_LRM.CM_SLPP(
			OPT_TXB_ID, TCR_USR_ID, STU_USR_ID, TRNM_USR_TP_CD, GRUP_SND_DV_CD, SLPP_CN
			, COFM_YN, TCR_DEL_YN, STU_DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{optTxbId},
			#{tcrUsrId},
			#{stuUsrId},
			#{trnmUsrTpCd},
			#{grupSndDvCd},
			#{slppCn},
			'N',
			'N',
			'N',
			#{crtrId},
			NOW(),
			#{crtrId},
			NOW(),
			'1'
		)
		;/** BcSlppStu-Mapper.xml - insertSlpp */
	</insert>
	
	<!-- 학생 쪽지 삭제 (단건) -->
	<update id="deleteSlpp" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppStu-Mapper.xml - deleteSlpp */
		UPDATE LMS_LRM.CM_SLPP
		SET
			STU_DEL_YN = 'Y',
			MDF_DTM = NOW()
		WHERE SLPP_ID = #{slppId}
		;/** BcSlppStu-Mapper.xml - deleteSlpp */
	</update>

	<!-- 학생 쪽지 삭제 (리스트) -->
	<update id="deleteSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppStu-Mapper.xml - deleteSlppList */
		UPDATE LMS_LRM.CM_SLPP CS
		INNER JOIN (
		<foreach collection="list" item="dto" separator=" UNION ALL ">
			SELECT
			#{dto.slppId} AS SLPP_ID
		</foreach>
		) TMP
			ON CS.SLPP_ID = TMP.SLPP_ID
		SET
			CS.STU_DEL_YN = 'Y',
			CS.MDF_DTM = NOW()
		;/** BcSlppStu-Mapper.xml - deleteSlppList */
	</update>
	<!-- 쪽지 전체 삭제 -->
	<update id="deleteAllSlpp" parameterType="com.aidt.api.bc.slpp.dto.BcSlppComandDto">
		/** BcSlppStu-Mapper.xml - deleteSlppList */
		UPDATE LMS_LRM.CM_SLPP CS
		SET
			CS.STU_DEL_YN = 'Y',
			CS.MDF_DTM = NOW()
		WHERE CS.TCR_USR_ID = #{tcrUsrId}
		AND CS.STU_USR_ID = #{stuUsrId}
		;/** BcSlppStu-Mapper.xml - deleteSlppList */
	</update>
	<!-- 학생 대화 시간 조회-->
	<select id="selectSlppTmSetm" parameterType="java.lang.String" resultType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		/** BcSlppStu-Mapper.xml - selectSlppTmSetm */
		SELECT
			CSTS.OPT_TXB_ID,
			CSTS.MON_YN,
			CSTS.TUE_YN,
			CSTS.WED_YN,
			CSTS.THU_YN,
			CSTS.FRI_YN,
			CSTS.SAT_YN,
			CSTS.SUN_YN,
			CSTS.SLPP_ABLE_STR_HM,
			CSTS.SLPP_ABLE_END_HM,
			CSTS.LGLH_YN,
			CSTS.DB_ID,
			CFUS.DILG_USE_YN as USE_YN
		FROM LMS_LRM.CM_SLPP_TM_SETM CSTS
		inner join CM_FNC_USE_SETM CFUS on
			CSTS.OPT_TXB_ID = CFUS.OPT_TXB_ID 
		WHERE CSTS.OPT_TXB_ID = #{optTxbId}
		LIMIT 1
		;/** BcSlppStu-Mapper.xml - selectSlppTmSetm */
	</select>
	
	
	<select id="selectSlppTmSetmDummy" parameterType="java.lang.String" resultType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		SELECT
		    #{optTxbId} AS OPT_TXB_ID,
		    'Y' AS MON_YN,
		    'Y' AS TUE_YN,
		    'Y' AS WED_YN,
		    'Y' AS THU_YN,
		    'Y' AS FRI_YN,
		    'N' AS SAT_YN,
		    'N' AS SUN_YN,
		    '0900' AS SLPP_ABLE_STR_HM,  -- 타임 형식
		    '1800' AS SLPP_ABLE_END_HM,  -- 타임 형식
		    'N' AS LGLH_YN,
		    'Y' AS DB_ID,
		    DILG_USE_YN AS USE_YN
		from
			CM_FNC_USE_SETM
		where
			OPT_TXB_ID = #{optTxbId}
	</select>
	
	<select id="selectSlppCofmCnt" parameterType="com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto" resultType="int">
		SELECT
			count(*)
		FROM
			CM_SLPP CS
		WHERE
			cs.TCR_USR_ID = #{tcrUsrId}
			AND cs.STU_USR_ID = #{stuUsrId} 
			AND cs.CRTR_ID != #{crtrId} 
			AND TRNM_USR_TP_CD = 'TE' 
			AND COFM_YN = 'N';
	</select>
</mapper>