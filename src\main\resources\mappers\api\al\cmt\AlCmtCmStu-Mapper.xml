<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.cmt.cm">

	<sql id="selectN01">
		SELECT
			AI_CMT_NO,
			REPLACE(CMT_CN, '#input#', CONCAT('#1#', #{n01.inputText}, '#2#')) as CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '1' AND ACH_LVL_CD = #{n01.achLvlCd} AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN02">
		SELECT
			AI_CMT_NO,
			CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '2' AND ACH_LVL_CD = #{n02.achLvlCd} AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN03">
		SELECT
			AI_CMT_NO,
			CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '3' AND ACH_LVL_CD = #{araLvlCd} AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN04">
		SELECT
			AI_CMT_NO,
			CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '4' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN05">
		SELECT
			AI_CMT_NO,
			CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '5' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND()
        <choose>
            <when test="n05 != null and usrTpCd.equals('ST')">
                LIMIT ${n05.selectCnt}
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>
	</sql>

	<sql id="selectN06">
		SELECT
		AI_CMT_NO,
		CMT_CN
		FROM
		LMS_LRM.AI_AI_CMT
		WHERE
		AI_CMT_NO = '6' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN07">
		SELECT
		AI_CMT_NO,
		CMT_CN
		FROM
		LMS_LRM.AI_AI_CMT
		WHERE
		AI_CMT_NO = '7' AND ACH_LVL_CD = #{lrnrVelTpCd} AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN08">
		SELECT
		AI_CMT_NO,
		CMT_CN
		FROM
		LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '8' AND ACH_LVL_CD = #{lrnrVelTpCd} AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN09">
		SELECT
		AI_CMT_NO,
		CMT_CN
		FROM
		LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '9' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN10">
		SELECT
		AI_CMT_NO,
		REPLACE(CMT_CN, '#input#', CONCAT('#1#', #{strthTpc}, '#2#')) as CMT_CN
		FROM
		LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '10' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN11">
		SELECT
		AI_CMT_NO,
		REPLACE(CMT_CN, '#input#', CONCAT('#1#', #{wknsTpc}, '#2#')) as CMT_CN
		FROM
		LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '11' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN12">
		<trim prefixOverrides="UNION ALL">
		<if test="lstngLvlCd != null and lstngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'LSTNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND LSTNG_LVL_CD = #{lstngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="spkngLvlCd != null and spkngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'SPKNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND SPKNG_LVL_CD = #{spkngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="rdngLvlCd != null and rdngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'RDNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND RDNG_LVL_CD = #{rdngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="wrtngLvlCd != null and wrtngLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'WRTNG' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND WRTNG_LVL_CD = #{wrtngLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>

		<if test="phncsLvlCd != null and phncsLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'PHNCS' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND PHNCS_LVL_CD = #{phncsLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		<if test="wrdLvlCd != null and wrdLvlCd != ''">
			UNION ALL
			(SELECT AI_CMT_NO, CMT_CN, 'WRD' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND WRD_LVL_CD = #{wrdLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		<if test="grmrLvlCd != null and grmrLvlCd != ''">
			UNION ALL
			(
			SELECT AI_CMT_NO, CMT_CN, 'GRMR' as CMT_TYPE
			FROM LMS_LRM.AI_AI_CMT
			WHERE AI_CMT_NO = '12' AND GRMR_LVL_CD = #{grmrLvlCd} AND USR_TP_CD = #{usrTpCd}
			ORDER BY RAND() LIMIT 1)
		</if>
		</trim>
	</sql>

	<sql id="selectN13">
		SELECT
			AI_CMT_NO,
			REPLACE(CMT_CN, '#input#', CONCAT('#1#', #{n13.inputText}, '#2#')) as CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '13' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN14">
		SELECT
			AI_CMT_NO,
			REPLACE(CMT_CN, '#input#', CONCAT('#1#', #{n14.inputText}, '#2#')) as CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '14' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

	<sql id="selectN15">
		SELECT
			AI_CMT_NO,
			CMT_CN
		FROM
			LMS_LRM.AI_AI_CMT
		WHERE
			AI_CMT_NO = '15' AND USR_TP_CD = #{usrTpCd}
		ORDER BY RAND() LIMIT 1
	</sql>

</mapper>