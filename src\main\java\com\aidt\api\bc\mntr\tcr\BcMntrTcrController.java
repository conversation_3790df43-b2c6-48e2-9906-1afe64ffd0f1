package com.aidt.api.bc.mntr.tcr;

import java.util.List;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.clablbd.dto.BcClaBlbdDto;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-03-18 17:43:29
 * @modify 2024-03-18 17:43:29
 * @desc 교사_실시간 모니터링 Controller
 */

@Slf4j
@Tag(name="[bc] 실시간 모니터링[BcMntrTcr]", description="실시간 모니터링(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/mntr")
public class BcMntrTcrController {

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private BcMntrTcrService bcMntrTcrService;
	
	/**
     * 실시간 모니터링 지도 필요 목록 조회
     *
     * @param BcMntrDto
     * @return ResponseList<BcMntrDto>
     */
	@Tag(name="[bc] 실시간 모니터링 지도 필요 목록 조회", description="실시간 모니터링 지도 필요 목록을 조회한다.(교사)")
	@GetMapping(value = "/selectGdeNeedList")
	public ResponseDto<List<BcMntrDto>> selectGdeNeedList(BcMntrDto bcMntrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		if(StringUtils.isEmpty(bcMntrDto.getOptTxbId())) {
			bcMntrDto.setOptTxbId(userDetails.getOptTxbId());
			bcMntrDto.setLrnUsrId(bcMntrDto.getLrnUsrId());
		}
		
		log.debug("Entrance selectStuLrnMntrList");
		return Response.ok(bcMntrTcrService.selectGdeNeedList(bcMntrDto));
	}
	
	
	/**
	 * 실시간 모니터링 지도 필요 목록 확인 여부 수정
	 *
	 * @param BcMntrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 실시간 모니터링 지도 필요 목록 확인 여부 수정", description="실시간 모니터링 지도 필요 목록 확인 여부 수정")
	@PostMapping(value = "/updateGdeNeedCofmYn")
	public ResponseDto<Integer> updateGdeNeedCofmYn(@Valid @RequestBody BcMntrDto bcMntrDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcMntrDto.setOptTxbId(userDetails.getOptTxbId());
		bcMntrDto.setDbId(userDetails.getTxbId());
		bcMntrDto.setMdfrId(userDetails.getUsrId());
		bcMntrDto.setLrnUsrId(bcMntrDto.getLrnUsrId());
	
		log.debug("Entrance saveClaBlbdInfo");
		return Response.ok(bcMntrTcrService.updateGdeNeedCofmYn(bcMntrDto));
	}
}
