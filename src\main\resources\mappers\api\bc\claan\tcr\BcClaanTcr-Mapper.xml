<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.claan.tcr">
	
	<!-- 학습요약 조회 -->
	<select id="selectLrnSmy" parameterType="com.aidt.api.bc.claan.dto.BcClaanReqDto" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectLrnSumm */
		select 
			   round(sum(t.std_dt_cnt)/count(1), 1) as avg_lrn_dt_cnt
		     , min(t.std_dt_cnt) as min_lrn_dt_cnt
		     , max(t.std_dt_cnt) as max_lrn_dt_cnt
		     , ifnull(time_format(sec_to_time(sum(t.ev_tm_scnt)/count(1)),'%H'),'00') as avg_lrn_hour
     		 , ifnull(time_format(sec_to_time(sum(t.ev_tm_scnt)/count(1)),'%i'),'00') as avg_lrn_min     		 
		     , case when time_format(sec_to_time(min(t.ev_tm_scnt)),'%H시간') = '00시간'
		                 then time_format(sec_to_time(min(t.ev_tm_scnt)),'%i분')
		            else time_format(sec_to_time(min(t.ev_tm_scnt)),'%H시간')
		        end as min_lrn_time
		     , case when time_format(sec_to_time(max(t.ev_tm_scnt)),'%H시간') = '00시간'
		                 then time_format(sec_to_time(max(t.ev_tm_scnt)),'%i분')
		            else time_format(sec_to_time(max(t.ev_tm_scnt)),'%H시간')
		        end as max_lrn_time
		     , round(sum(t.qst_cnt)/count(1)) as avg_qst_cnt
		     , min(t.qst_cnt) as min_qst_cnt
		     , max(t.qst_cnt) as max_qst_cnt
		     , ifnull(round(sum(cans_rt)/count(1), 1), 0) as avg_cans_rt    -- 정답률
		     , min(cans_rt) as min_cans_rt
		     , max(cans_rt) as max_cans_rt
		     , count(1) as lrn_usr_cnt
		from (
			select g.lrn_usr_id
		         , count(distinct g.std_date) std_dt_cnt
		         , sum(g.ev_tm_scnt) as ev_tm_scnt
		         , sum(g.qst_cnt)   as qst_cnt
		         , ifnull(round(sum(g.cans_cnt)/sum(g.qst_cnt)*100, 1), 0) as cans_rt
			from (
				/*교과평가,ai평가,교사평가*/
				select
					  date_format(eer.smt_dtm,'%Y%m%d') as std_date
		            , eer.usr_id as lrn_usr_id
		            , sum(eer.EV_TM_SCNT) as ev_tm_scnt
					, sum(ee.fnl_qst_cnt) as qst_cnt
					, sum(eer.cans_cnt)   as cans_cnt
				from lms_lrm.ea_ev ee -- ea 평가
				inner join lms_lrm.ea_ev_rs eer
						on ee.ev_id = eer.ev_id
				inner join lms_lrm.cm_usr cu
		             	on eer.usr_id = cu.usr_id 
		               and cu.usr_tp_cd = 'ST'
		               AND cu.CLA_ID = #{claId}
		        where ee.opt_txb_id = #{optTxbId}
		          and ee.use_yn      = 'Y'
		          and ee.del_yn      = 'N'
		          and eer.smt_dtm is not null
               <choose>
		            <when test='srhKn == "month"'>
		            	AND EER.smt_dtm BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		            	AND EER.smt_dtm <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND EER.smt_dtm <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(EER.smt_dtm) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
             	group by date_format(eer.smt_dtm,'%Y%m%d'), eer.usr_id             	
             	
             	UNION ALL
             	/*교과평가,ai평가,교사평가*/
				select
					  date_format(eer.mdf_dtm,'%Y%m%d') as std_date
		            , eer.usr_id as lrn_usr_id
		            , 0					 as ev_tm_scnt
					, 0					 as qst_cnt
					, 0					   as cans_cnt
				from lms_lrm.ea_ev ee -- ea 평가
				inner join lms_lrm.ea_ev_rs eer
						on ee.ev_id = eer.ev_id
				inner join lms_lrm.cm_usr cu
		             	on eer.usr_id = cu.usr_id 
		               and cu.usr_tp_cd = 'ST'
		               AND cu.CLA_ID = #{claId}
		        where ee.opt_txb_id = #{optTxbId}
		          and ee.use_yn      = 'Y'
		          and ee.del_yn      = 'N'
		          and eer.smt_dtm is not null
               <choose>
		            <when test='srhKn == "month"'>
		            	AND EER.mdf_dtm BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		            	AND EER.mdf_dtm <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND EER.mdf_dtm <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(EER.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
             	group by date_format(eer.mdf_dtm,'%Y%m%d'), eer.usr_id      
             	  	
		        union all
	        	/* 교과학습 */
			    SELECT
			        DATE_FORMAT(st.MDF_DTM,'%Y%m%d') AS stdDate,
			        st.LRN_USR_ID					 AS lrn_usr_id,				
			        SUM(st.LRN_TM_SCNT)              AS evTmScnt,
			        0                            	 AS qstCnt,
			        0                           	 AS cansCnt
			    from LMS_LRM.tl_sbc_lrn_atv_rcstn LAR
			    inner JOIN LMS_LRM.TL_SBC_LRN_ATV_ST st
			    	on LAR.OPT_TXB_ID = ST.OPT_TXB_ID
			    		and LAR.LRMP_NOD_ID = ST.LRMP_NOD_ID
			    		and LAR.LRN_ATV_ID = ST.LRN_ATV_ID
			    inner join lms_lrm.cm_usr cu 
			    		on st.LRN_USR_ID = cu.usr_id
					   and cu.usr_tp_cd = 'ST'
			    WHERE LAR.OPT_TXB_ID = #{optTxbId}
			    and LAR.CTN_TP_CD <![CDATA[<>]]> 'EX'		    
--			    AND LRN_ST_CD  = 'CL' 			-- 학습 완료
			   <choose>
		            <when test='srhKn == "month"'>
		                AND st.MDF_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		                AND st.MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND st.MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(st.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
			    group by DATE_FORMAT(st.MDF_DTM,'%Y%m%d'), LRN_USR_ID
			    
			    union all
	        	/* 교과학습 */
			    SELECT
			        DATE_FORMAT(st.CRT_DTM,'%Y%m%d') AS stdDate,
			        st.LRN_USR_ID					 AS lrn_usr_id,				
			        0				             AS evTmScnt,
			        0                            	 AS qstCnt,
			        0                           	 AS cansCnt
			    from LMS_LRM.tl_sbc_lrn_atv_rcstn LAR
			    inner JOIN LMS_LRM.TL_SBC_LRN_ATV_ST st
			    	on LAR.OPT_TXB_ID = ST.OPT_TXB_ID
			    		and LAR.LRMP_NOD_ID = ST.LRMP_NOD_ID
			    		and LAR.LRN_ATV_ID = ST.LRN_ATV_ID
			    inner join lms_lrm.cm_usr cu 
			    		on st.LRN_USR_ID = cu.usr_id
					   and cu.usr_tp_cd = 'ST'
			    WHERE LAR.OPT_TXB_ID = #{optTxbId}
			    and LAR.CTN_TP_CD <![CDATA[<>]]> 'EX'		    
--			    AND LRN_ST_CD  = 'CL' 			-- 학습 완료
			   <choose>
		            <when test='srhKn == "month"'>
		                AND st.CRT_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		                AND st.CRT_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND st.CRT_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(st.CRT_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
			    group by DATE_FORMAT(st.CRT_DTM,'%Y%m%d'), LRN_USR_ID
			    
				union all				    
			    /* 교과학습 - 선생님 추가 컨텐츠 */
			    select
					DATE_FORMAT(TTRCS.MDF_DTM,'%Y%m%d') AS stdDate,
					TTRCS.LRN_USR_ID					AS lrn_usr_id,
					SUM(TTRCS.LRN_TM_SCNT)            	AS evTmScnt,
			        0                             	AS qstCnt,
			        0                             	AS cansCnt
				FROM TL_TCR_REG_CTN_ST TTRCS
			    inner join lms_lrm.cm_usr cu 
			    		on TTRCS.LRN_USR_ID = cu.usr_id
			    		and cu.usr_tp_cd = 'ST'
			    WHERE OPT_TXB_ID = #{optTxbId}				    
--			    AND   TTRCS.LRN_ST_CD  = 'CL' -- 학습 완료
			    <choose>
		            <when test='srhKn == "month"'>
		                AND TTRCS.MDF_DTM BETWEEN 
						    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
						    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		                AND TTRCS.MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND TTRCS.MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(TTRCS.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
			     GROUP BY DATE_FORMAT(TTRCS.MDF_DTM,'%Y%m%d'), TTRCS.LRN_USR_ID
			     
			     union all				    
			    /* 교과학습 - 선생님 추가 컨텐츠 */
			    select
					DATE_FORMAT(TTRCS.CRT_DTM,'%Y%m%d') AS stdDate,
					TTRCS.LRN_USR_ID					AS lrn_usr_id,
					0            	AS evTmScnt,
			        0                             	AS qstCnt,
			        0                             	AS cansCnt
				FROM TL_TCR_REG_CTN_ST TTRCS
			    inner join lms_lrm.cm_usr cu 
			    		on TTRCS.LRN_USR_ID = cu.usr_id
			    		and cu.usr_tp_cd = 'ST'
			    WHERE OPT_TXB_ID = #{optTxbId}				    
--			    AND   TTRCS.LRN_ST_CD  = 'CL' -- 학습 완료
			    <choose>
		            <when test='srhKn == "month"'>
		                AND TTRCS.CRT_DTM BETWEEN 
						    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
						    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		                AND TTRCS.CRT_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND TTRCS.CRT_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(TTRCS.CRT_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>
			     GROUP BY DATE_FORMAT(TTRCS.CRT_DTM,'%Y%m%d'), TTRCS.LRN_USR_ID
			     
				 union all				    				    
			    /* 특별학습 */
			    SELECT
			        DATE_FORMAT(st.MDF_DTM,'%Y%m%d') AS stdDate,
			        st.LRN_USR_ID					 AS lrn_usr_id,
			        SUM(st.LRN_TM_SCNT)              AS evTmScnt,
			        0                             	 AS qstCnt,
			        0                             	 AS cansCnt
			    FROM LMS_LRM.SL_SP_LRN_PGRS_ST st
			    inner join lms_lrm.cm_usr cu 
			    		on st.LRN_USR_ID = cu.usr_id
			    	   and cu.usr_tp_cd = 'ST'
			    WHERE st.OPT_TXB_ID = #{optTxbId}
			    <choose>
		            <when test='srhKn == "month"'>
		                AND st.MDF_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		            	AND st.MDF_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND st.MDF_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(st.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>				   				    
			    AND   st.LRN_ST_CD  = 'CL'			-- 학습 완료
			    GROUP BY DATE_FORMAT(st.MDF_DTM,'%Y%m%d'), LRN_USR_ID
			    
			    union all				    				    
			    /* 특별학습 */
			    SELECT
			        DATE_FORMAT(st.CRT_DTM,'%Y%m%d') AS stdDate,
			        st.LRN_USR_ID					 AS lrn_usr_id,
			        0				              AS evTmScnt,
			        0                             	 AS qstCnt,
			        0                             	 AS cansCnt
			    FROM LMS_LRM.SL_SP_LRN_PGRS_ST st
			    inner join lms_lrm.cm_usr cu 
			    		on st.LRN_USR_ID = cu.usr_id
			    	   and cu.usr_tp_cd = 'ST'
			    WHERE st.OPT_TXB_ID = #{optTxbId}
			    <choose>
		            <when test='srhKn == "month"'>
		                AND st.CRT_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		            	AND st.CRT_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND st.CRT_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(st.CRT_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>				   				    
			    AND   st.LRN_ST_CD  = 'CL'			-- 학습 완료
			    GROUP BY DATE_FORMAT(st.CRT_DTM,'%Y%m%d'), LRN_USR_ID
			<if test = '!"E".equals(schlGrdCd)'>
			    UNION ALL
				select 	
					DATE_FORMAT(STU_SAV_DTM,'%Y%m%d') AS stdDate, -- 학생저장일시
					STU_USR_ID AS lrn_usr_id,	-- 학생사용자ID
					sum(LRN_TM_SCNT) AS evTmScnt,	-- 학습시간초수
					0                             	 AS qstCnt,
					0                             	 AS cansCnt
				from lms_lrm.cm_wrt_mg 
				WHERE OPT_TXB_ID = #{optTxbId}
				AND LRN_TM_SCNT IS NOT NULL 
				<choose>
		            <when test='srhKn == "month"'>
		                AND STU_SAV_DTM BETWEEN 
					    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
		            </when>
		            <when test='srhKn == "week"'>
		            	AND STU_SAV_DTM <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		                AND STU_SAV_DTM <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
		                AND DAYOFWEEK(#{smtDtm}) = 1
		            </when>
		            <when test='srhKn == "day"'>
		                AND DATE(STU_SAV_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
		            </when>
	            </choose>	
				group by DATE_FORMAT(STU_SAV_DTM,'%Y%m%d'), STU_USR_ID
			</if>		    	
		    ) g 
		    group by g.lrn_usr_id
		) t
    </select>
    
	<!-- 학습요약 조회 (삭제 예정) -->
	<select id="selectLrnSmy2" parameterType="com.aidt.api.bc.claan.dto.BcClaanReqDto" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectLrnSumm */
		select round(sum(t.std_dt_cnt)/count(1), 1) as avg_lrn_dt_cnt
		     , min(t.std_dt_cnt) as min_lrn_dt_cnt
		     , max(t.std_dt_cnt) as max_lrn_dt_cnt
		     , ifnull(time_format(sec_to_time(sum(t.ev_tm_scnt)/count(1)),'%H'),'00') as avg_lrn_hour
     		 , ifnull(time_format(sec_to_time(sum(t.ev_tm_scnt)/count(1)),'%i'),'00') as avg_lrn_min
		     , case when time_format(sec_to_time(min(t.ev_tm_scnt)),'%H시간') = '00시간'
		                 then time_format(sec_to_time(min(t.ev_tm_scnt)),'%i분')
		            else time_format(sec_to_time(min(t.ev_tm_scnt)),'%H시간')
		        end as min_lrn_time
		     , case when time_format(sec_to_time(max(t.ev_tm_scnt)),'%H시간') = '00시간'
		                 then time_format(sec_to_time(max(t.ev_tm_scnt)),'%i분')
		            else time_format(sec_to_time(max(t.ev_tm_scnt)),'%H시간')
		        end as max_lrn_time
		     , round(sum(t.qst_cnt)/count(1)) as avg_qst_cnt
		     , min(t.qst_cnt) as min_qst_cnt
		     , max(t.qst_cnt) as max_qst_cnt
		     , ifnull(round(sum(cans_rt)/count(1)), 0) as avg_cans_rt    -- 정답률
		     , min(cans_rt) as min_cans_rt
		     , max(cans_rt) as max_cans_rt
		     , count(1) as lrn_usr_cnt
		  from (
		    select g.lrn_usr_id
		         , count(distinct g.std_date) std_dt_cnt
		         , sum(g.ev_tm_scnt) as ev_tm_scnt
		         , sum(g.qst_cnt)   as qst_cnt
		         , ifnull(round(sum(g.cans_cnt)/sum(g.qst_cnt)*100), 0) as cans_rt
		      from (
		            /*교과평가,ai평가,교사평가*/
		            select date_format(eer.smt_dtm,'%Y%m%d') as std_date
		                 , eer.usr_id as lrn_usr_id
		                 , 0 as ev_tm_scnt
		                 , sum(ee.fnl_qst_cnt) as qst_cnt
		                 , sum(eer.cans_cnt)   as cans_cnt
		              from lms_lrm.ea_ev ee -- ea 평가
		             inner join lms_lrm.ea_ev_rs eer
		                on ee.ev_id = eer.ev_id
		             inner join lms_lrm.cm_usr cu
		             	on eer.usr_id = cu.usr_id 
		               and cu.usr_tp_cd = 'ST'
		             where ee.opt_txb_id = #{optTxbId}
		               and ee.use_yn      = 'Y'
		               and ee.del_yn      = 'N'
		               and eer.ev_cmpl_yn = 'Y'
		               <choose>
				            <when test='srhKn == "month"'>
					            AND EER.smt_dtm BETWEEN 
								    STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
								    AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
				            </when>
				            <when test='srhKn == "week"'>
				            AND EER.smt_dtm <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
				                AND EER.smt_dtm <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
				                AND DAYOFWEEK(#{smtDtm}) = 1
				            </when>
				            <when test='srhKn == "day"'>
				                AND DATE(EER.MDF_DTM) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
				            </when>
			            </choose>
		             group by date_format(eer.smt_dtm,'%Y%m%d'), eer.usr_id
		        union all
		        	/* 학습 시간 */
		            select substr(clt.lrn_yr_dtm, 1, 8) as std_date
		                 , clt.usr_id as lrn_usr_id
		                 , (clt.txb_lrn_tm_scnt + clt.ai_lrn_tm_scnt + clt.sp_lrn_tm_scnt + clt.ev_lrn_tm_scnt + clt.ai_ev_lrn_tm_scnt + clt.ai_wrtng_lrn_tm + clt.ai_rdng_lrn_tm) as ev_tm_scnt
		                 , 0 as qst_cnt
		                 , 0 as cans_cnt 
		              from lms_lrm.cm_opt_txb cot2
		             inner join lms_lrm.cm_usr cu 
		                on cu.cla_id = cot2.cla_id
		               and cu.usr_tp_cd = 'ST'
		             inner join lms_lrm.cm_lrn_tm clt 
		                on clt.opt_txb_id = cot2.opt_txb_id
		               and clt.usr_id = cu.usr_id
		             where clt.opt_txb_id = #{optTxbId}
		              <choose>
					    <when test='srhKn == "month"'>
					        AND substr(clt.lrn_yr_dtm, 1, 8) BETWEEN 
					            STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d')
					            AND LAST_DAY(STR_TO_DATE(CONCAT(#{smtDtm}, '01'), '%Y%m%d'))
					    </when>
					    <when test='srhKn == "week"'>
					    AND substr(clt.lrn_yr_dtm, 1, 8) <![CDATA[>=]]>  STR_TO_DATE(#{smtDtm}, '%Y%m%d')
					        AND substr(clt.lrn_yr_dtm, 1, 8) <![CDATA[<]]> STR_TO_DATE(#{smtDtm}, '%Y%m%d') + INTERVAL 7 DAY
					        AND DAYOFWEEK(#{smtDtm}) = 1
					    </when>
					    <when test='srhKn == "day"'>
					        AND substr(clt.lrn_yr_dtm, 1, 8) = STR_TO_DATE(#{smtDtm}, '%Y%m%d')
					    </when>
					  </choose>
		             group by substr(clt.lrn_yr_dtm, 1, 8), clt.usr_id
		    	
		    ) g 
		    group by g.lrn_usr_id
		) t
    </select>
    
    <!-- 단원별 성취 현황 -->
	<select id="selectLuAchdPst" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectLuAchdPst */
		select t1.lrmp_nod_id
		     , t1.rcstn_ordn
		     , case when t1.lu_no_use_yn = 'Y' then concat(t1.lrmp_num,'. ', t1.lrmp_nod_nm)
		            else t1.lrmp_nod_nm
		        end as lrmp_nod_nm
		     , t1.lrmp_num
		     , t1.lu_no_use_yn
		     , ifnull(t2.fnl_qst_cnt, 0) as schl_fnl_qst_cnt
		     , ifnull(t2.cans_cnt, 0) as schl_cans_cnt
		     , ifnull(t2.cans_rt, 0) as schl_cans_rt
		     , ifnull(t3.fnl_qst_cnt, 0) as fnl_qst_cnt
		     , ifnull(t3.cans_cnt, 0) as cans_cnt
		     , ifnull(t3.cans_rt, 0) as cans_rt
		     , ifnull(t3.tlrk_cans_rt, 0) as tlrk_cans_rt
		     , ifnull(t3.lrk_cans_rt, 0) as lrk_cans_rt
		     , ifnull(t3.mddl_cans_rt, 0) as mddl_cans_rt
		     , ifnull(t3.urnk_cans_rt, 0) as urnk_cans_rt
		     , ifnull(t3.thrk_cans_rt, 0) as thrk_cans_rt
		  from (
		    select a.lrmp_nod_id
		         , a.lrmp_nod_nm
		         , a.rcstn_ordn
		         , a.lu_no_use_yn
		         , row_number() over(order by a.lu_no_use_yn desc, a.rcstn_ordn) as lrmp_num
		      from lms_lrm.tl_sbc_lrn_nod_rcstn a
		     where a.opt_txb_id = #{optTxbId}
		       and a.dpth = 1
		       and a.use_yn = 'Y'
		       and a.lu_eps_yn = 'Y'
		       and exists (select 1 from  lms_lrm.ea_ev_ts_rnge z where z.lu_lrmp_nod_id=a.lrmp_nod_id and z.LU_OPT_TXB_ID = #{optTxbId})
		       and a.lu_no_use_yn= 'Y'
		  ) t1
		  left outer join (
		    select b.lu_lrmp_nod_id
		         , count(c.qtm_id) as fnl_qst_cnt
		         , sum(case when d.cans_yn = 'Y' then 1 else 0 end) as cans_cnt
		         , round(sum(case when d.cans_yn = 'Y' then 1 else 0 end)/count(c.qtm_id) * 100, 1) as cans_rt
		      from lms_lrm.ea_ev a		     
		     inner join lms_lrm.ea_ev_ts_rnge b
		        on a.ev_id = b.ev_id
		       and b.TS_RNGE_SEQ_NO = 1
		     inner join lms_lrm.ea_ev_qtm c
		        on a.ev_id = c.ev_id
		     inner join lms_lrm.ea_ev_qtm_anw d
		        on a.ev_id = d.ev_id 
		       and c.qtm_id = d.qtm_id
		     inner join lms_lrm.ea_ev_rs e
		        on a.ev_id = e.ev_id
		       and d.usr_id = e.usr_id
		      inner join  lms_lrm.cm_usr cu
		     	ON cu.usr_tp_cd = 'ST'
		     	AND cu.USR_ID=e.USR_ID
		     where (   (a.ev_dv_cd = 'SE' and a.ev_dtl_dv_cd in ('FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
		            or (a.ev_dv_cd = 'AE' and a.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
		            )
		       and a.opt_txb_id in (
		        select a.opt_txb_id 
		          from lms_lrm.cm_opt_txb a
		         where a.cla_id in (
		            select cla_id
		              from lms_lrm.cm_cla
		             where schl_cd = #{schlCd}
		               and sgy = #{sgyCd}
		               and schl_grd_cd = #{schlGrdCd}
		         )
		           and a.txb_id = (
		            select txb_id
		              from lms_lrm.cm_opt_txb
		             where opt_txb_id = #{optTxbId}
		           )
		       )
		       and e.ev_cmpl_yn = 'y'
		     group by b.lu_lrmp_nod_id
		  ) t2
		    on t1.lrmp_nod_id = t2.lu_lrmp_nod_id
		  left outer join (
		    select b.lu_lrmp_nod_id
		         , count(c.qtm_id) as fnl_qst_cnt
		         , sum(case when d.cans_yn = 'Y' then 1 else 0 end) as cans_cnt
		         , round(sum(case when d.cans_yn = 'Y' then 1 else 0 end) / count(c.qtm_id) * 100,1) as cans_rt
		         , round(sum(case when c.qtm_dffd_dv_cd = '01' and d.cans_yn = 'Y' then 1 else 0 end)/count((case when c.qtm_dffd_dv_cd = '01' then c.qtm_id else null end))*100,1) as tlrk_cans_rt
		         , round(sum(case when c.qtm_dffd_dv_cd = '02' and d.cans_yn = 'Y' then 1 else 0 end)/count((case when c.qtm_dffd_dv_cd = '02' then c.qtm_id else null end))*100,1) as lrk_cans_rt
		         , round(sum(case when c.qtm_dffd_dv_cd = '03' and d.cans_yn = 'Y' then 1 else 0 end)/count((case when c.qtm_dffd_dv_cd = '03' then c.qtm_id else null end))*100,1) as mddl_cans_rt
		         , round(sum(case when c.qtm_dffd_dv_cd = '04' and d.cans_yn = 'Y' then 1 else 0 end)/count((case when c.qtm_dffd_dv_cd = '04' then c.qtm_id else null end))*100,1) as urnk_cans_rt
		         , round(sum(case when c.qtm_dffd_dv_cd = '05' and d.cans_yn = 'Y' then 1 else 0 end)/count((case when c.qtm_dffd_dv_cd = '05' then c.qtm_id else null end))*100,1) as thrk_cans_rt
		      from lms_lrm.ea_ev a
		      inner join (select distinct b.lu_lrmp_nod_id,  b.ev_id from lms_lrm.ea_ev_ts_rnge b where b.LU_OPT_TXB_ID = #{optTxbId}) b
		        on a.ev_id = b.ev_id		       
		     inner join lms_lrm.ea_ev_qtm c
		        on a.ev_id = c.ev_id
		     inner join lms_lrm.ea_ev_qtm_anw d
		        on a.ev_id = d.ev_id 
		       and c.qtm_id = d.qtm_id
		     inner join lms_lrm.ea_ev_rs e
		       on a.ev_id = e.ev_id
		      and d.usr_id = e.usr_id
		      inner join  lms_lrm.cm_usr cu
		     	ON cu.usr_tp_cd = 'ST'
		     	AND cu.USR_ID=e.USR_ID
		     where (   (a.ev_dv_cd = 'SE' and a.ev_dtl_dv_cd in ('FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
		            or (a.ev_dv_cd = 'AE' and a.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
		            )
		       and a.opt_txb_id = #{optTxbId}
		       and e.ev_cmpl_yn = 'y'
		     group by b.lu_lrmp_nod_id
		  ) t3
		    on t1.lrmp_nod_id = t3.lu_lrmp_nod_id
		 order by t1.rcstn_ordn
	</select>
	
	<!-- 단원별 상세 현황 : 평가별 분석 탭 목록 -->
	<select id="selectEvAnTabList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectEvAnTabList */
	   select rslnr.lrmp_nod_id 
		     , max(rslnr.lrmp_nod_nm) as lrmp_nod_nm
		     , a.ev_dtl_dv_cd 
		     /*
		     , case when a.ev_dtl_dv_cd = 'OV' then 'AI 맞춤 진단'
		     	else (select cm_cd_nm from lms_lrm.cm_cm_cd where urnk_cm_cd = 'EV_DTL_DV_CD' and cm_cd = a.ev_dtl_dv_cd)
		       end as ev_dtl_dv_nm  -- 평가구분명
		     */
		     , (select cm_cd_nm from lms_lrm.cm_cm_cd where urnk_cm_cd = 'EV_DTL_DV_CD' and cm_cd = a.ev_dtl_dv_cd) as ev_dtl_dv_nm -- 평가구분명
		     , max(a.ev_id) as ev_id
		     , max(a.ev_nm) as ev_nm
		     , count(distinct c.qtm_id) as fnl_qst_cnt /*최종문제수*/
		     , round(sum(case when d.cans_yn = 'Y' then 1 else 0 end)/count(c.qtm_id) * 100,1) as cans_rt
		     , sum(case when d.cans_yn = 'Y' then 1 else 0 end) as aa
		  from lms_lrm.tl_sbc_lrn_nod_rcstn rslnr
		 inner join (select distinct lu_lrmp_nod_id,ev_id from lms_lrm.ea_ev_ts_rnge where LU_OPT_TXB_ID = #{optTxbId}) b
		    on rslnr.lrmp_nod_id = b.lu_lrmp_nod_id
		 inner join lms_lrm.ea_ev a
		    on a.ev_id = b.ev_id AND a.OPT_TXB_ID=rslnr.OPT_TXB_ID
		 inner join lms_lrm.ea_ev_qtm c
		    on a.ev_id = c.ev_id
		 inner join lms_lrm.ea_ev_qtm_anw d
		    on a.ev_id = d.ev_id 
		   and c.qtm_id = d.qtm_id
		 inner join lms_lrm.ea_ev_rs e
		    on a.ev_id = e.ev_id
		   and d.usr_id = e.usr_id
		 inner join  lms_lrm.cm_usr cu
		     	ON cu.usr_tp_cd = 'ST'
		     	AND cu.USR_ID=e.USR_ID
		 where rslnr.opt_txb_id = #{optTxbId}
		   and rslnr.llu_nod_id = #{lluNodId}
		   and e.ev_cmpl_yn = 'Y'
		   and rslnr.dpth = 1
		   and (   (a.ev_dv_cd = 'SE' and a.ev_dtl_dv_cd in ('FO', 'UG', 'TO'))    -- 교과학습(SE)의 형성평가(FO), 단원평가(UG), 차시평가(TO)
		        or (a.ev_dv_cd = 'AE' and a.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
		        )
		 group by rslnr.lrmp_nod_id, a.ev_dtl_dv_cd, a.evsh_id, ev_dtl_dv_nm
		 order by rslnr.rcstn_ordn
	</select>
	
	<!-- 단원별 상세 현황 : 평가별 분석 탭 - 학습필요학생 목록 -->
	<select id="selectLrnNeceStuList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		select b.usr_id as stu_id
		     , usr.usr_nm as stu_nm
		     , b.cans_rt  
		     , round(count(b.usr_id)*0.2, 0) as show_stu_cnt
		  from lms_lrm.ea_ev a
		 inner join lms_lrm.ea_ev_rs b
		    on a.ev_id = b.ev_id
		 inner join lms_lrm.cm_usr usr
		    on b.usr_id = usr.usr_id 
		 where a.ev_id = #{evId}
		   and a.opt_txb_id = #{optTxbId}
		   and b.cans_rt is not null
		   and usr.usr_tp_cd = 'ST'
		   and b.cans_rt <![CDATA[<=]]> #{avgCansRt}
		 order by b.cans_rt
	</select>
	
	<!-- 단원별 상세 현황 : 차시별 분석 탭 resultMap -->
	<resultMap id="TcAnTabMap" type="com.aidt.api.bc.claan.dto.BcClaanResDto">
        <result property="lrmpNodId" column="lrmp_nod_id"/>
        <result property="lrmpNodNm" column="lrmp_nod_nm"/>
        <result property="fnlQstCnt" column="fnl_qst_cnt"/>
        <result property="evId" column="ev_id"/>
        <result property="evNm" column="ev_nm"/>
        <result property="cansRt" column="cans_rt"/>
        <collection 
            property="tcAnStuList" 
            javaType="List"
            ofType="com.aidt.api.bc.claan.dto.BcClaanResDto"
            column="optTxbId=opt_txb_id, evId=ev_id, cansRt=cans_rt"
            select="selectTcAnTabStuList"
        >
        	<result property="stuId" column="stu_id"/>
	        <result property="cansRt" column="cans_rt"/>
        </collection>
    </resultMap>
	<!-- 단원별 상세 현황 : 차시별 분석 탭 목록 -->
	<select id="selectTcAnTabList" resultMap="TcAnTabMap">
		/* BcClaanTcr-Mapper - selectTcAnTabList */		
		select
			  nod.LRMP_NOD_ID AS lrmp_nod_id
			, nod.LRMP_NOD_NM lrmp_nod_nm
			, a.opt_txb_id
	  		, a.ev_id
	  		, a.ev_nm
	  		, a.fnl_qst_cnt
	  		, round(avg(e.CANS_RT),1) AS cans_rt
	  		, #{lluNodId} as llu_nod_id
		  from  lms_lrm.ea_ev a
		 inner join lms_lrm.ea_ev_rs e
		    on a.ev_id = e.ev_id 
		 inner join  lms_lrm.cm_usr cu
		     	ON cu.usr_tp_cd = 'ST'
		     	AND cu.USR_ID=e.USR_ID
		 inner join tl_sbc_lrn_atv_rcstn atv on a.ev_id=atv.ev_id and atv.OPT_TXB_ID=a.OPT_TXB_ID AND CTN_TP_CD='EX'
		 inner join lms_cms.bc_lrmp_nod nod on atv.LRMP_NOD_ID=nod.LRMP_NOD_ID
		 where 
		    a.opt_txb_id = #{optTxbId}
		   and e.ev_cmpl_yn = 'Y' 
		   and (a.ev_dv_cd = 'SE' and a.ev_dtl_dv_cd in ('FO', 'TO'))   -- 교과학습(SE)의 형성평가(FO), 차시평가(TO)
		 and exists(select 1 from lms_lrm.ea_ev_ts_rnge z  where z.LU_OPT_TXB_ID = #{optTxbId} and LU_LRMP_NOD_ID=#{lluNodId} and z.EV_ID=a.ev_id )
		 group by a.ev_id
		 		 , LRMP_NOD_ID
				 , nod.LRMP_NOD_NM 
				 , a.opt_txb_id
		  		 , a.ev_id
		  		 , a.ev_nm
		  		 , a.fnl_qst_cnt
			  	 , llu_nod_id
	</select>
	
	<!-- 단원별 상세 현황 : 차시별 분석 탭 - 학습 필요 학생 목록 -->
	<select id="selectTcAnTabStuList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectTcAnTabStuList */
		select usr.usr_id as stu_id
		     , usr.usr_nm as stu_nm
		     , t1.cans_rt
		  from (
		    select d.usr_id 
		         , count(1) as fnl_qst_cnt
		         , sum(case when d.cans_yn = 'Y' then 1 else 0 end) cans_cnt
		         , round(sum(case when d.cans_yn = 'Y' then 1 else 0 end)/count(1), 1) as cans_rt
		      from lms_lrm.tl_sbc_lrn_nod_rcstn rslnr
		     inner join lms_lrm.ea_ev_ts_rnge b
		        on rslnr.lrmp_nod_id = b.tc_lrmp_nod_id -- 차시
		     inner join lms_lrm.ea_ev a
		        on a.ev_id = b.ev_id AND a.OPT_TXB_ID=rslnr.OPT_TXB_ID
		     inner join lms_lrm.ea_ev_qtm c
		        on a.ev_id = c.ev_id
		     inner join lms_lrm.ea_ev_qtm_anw d
		        on a.ev_id = d.ev_id 
		       and c.qtm_id = d.qtm_id
		     inner join lms_lrm.ea_ev_rs e
		        on a.ev_id = e.ev_id
		       and d.usr_id = e.usr_id
		     where rslnr.dpth = 4
		       and rslnr.opt_txb_id = #{optTxbId}
		        and a.ev_id = #{evId}
		       and e.ev_cmpl_yn = 'Y' 
		       and (a.ev_dv_cd = 'SE' and a.ev_dtl_dv_cd in ('FO'))   -- 교과학습(SE)의 형성평가(FO)
		     group by d.usr_id
		  ) t1
		 inner join lms_lrm.cm_usr usr
		    on t1.usr_id = usr.usr_id 
		 where usr.usr_tp_cd = 'ST' 
		   and t1.cans_rt <![CDATA[<=]]> #{cansRt}
		 order by t1.cans_rt 
	</select>

	
	
	
	<!-- 단원별 상세 현황 : 영역별 분석 탭 resultMap -->
	<resultMap id="PartAnTabMap" type="com.aidt.api.bc.claan.dto.BcClaanResDto">
        <result property="lrmpNodId" column="lrmp_nod_id"/>
        <result property="lrmpNodNm" column="lrmp_nod_nm"/>
        <result property="fnlQstCnt" column="fnl_qst_cnt"/>
        <result property="cansRt" column="cans_rt"/>
        
        <collection 
            property="tcAnStuList" 
            javaType="List"
            ofType="com.aidt.api.bc.claan.dto.BcClaanResDto"
            column="optTxbId=opt_txb_id, lluNodId=llu_nod_id,lrmpNodId=lrmp_nod_id, cansRt=cans_rt"
            select="selectPartAnTabStuList"
        >
        	<result property="stuId" column="stu_id"/>
	        <result property="cansRt" column="cans_rt"/>
        </collection>
    </resultMap>
	
	<!-- 단원별 상세 현황 : 영역별 분석 탭 목록 -->
	<select id="selectPartAnTabList" resultMap="PartAnTabMap">
		/* BcClaanTcr-Mapper - selectPartAnTabList */
		SELECT
			KMMP_NOD_NM as lrmp_nod_nm, -- 영역명
			KMMP_NOD_ID  as lrmp_nod_id, -- 영역코드
			#{lluNodId} as llu_nod_id,
			#{optTxbId} as opt_txb_id,
			count(cans_yn) as fnl_qst_cnt, -- 푼 문제
			ROUND((SUM(CASE WHEN CANS_YN='Y' THEN 1 ELSE 0 END )/count(cans_yn)) * 100) as cans_rt
		FROM(
			select
	  		eer.USR_ID,
				eer.EV_CMPL_YN	,
				QP_CN_ARA_NM KMMP_NOD_NM, -- 영역명
				QP_CN_ARA_ID KMMP_NOD_ID, -- 영역코드
				eeqa.CANS_YN -- 정답 여부
			
			FROM   lms_lrm.ea_ev ee
			       INNER JOIN lms_lrm.ea_ev_rs eer on ee.ev_id = eer.ev_id
		      inner join  lms_lrm.cm_usr cu
		     	ON cu.usr_tp_cd = 'ST'
		     	AND cu.USR_ID=eer.USR_ID
			       INNER JOIN lms_lrm.ea_ev_qtm_anw eeqa
			       ON     ee.EV_ID=eeqa.EV_ID  AND  eer.USR_ID=eeqa.USR_ID
			       inner join lms_cms.qp_qtm_an qta on eeqa.QTM_ID=qta.QP_QTM_ID
			WHERE eer.EV_CMPL_YN='Y'
			AND ee.OPT_TXB_ID= #{optTxbId}
			AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
			AND exists (select 1 from ea_ev_ts_rnge z where z.LU_OPT_TXB_ID = #{optTxbId} and  z.LU_LRMP_NOD_ID= #{lluNodId}  AND ee.ev_id=z.EV_ID)
		) AA GROUP by KMMP_NOD_NM,KMMP_NOD_ID, llu_nod_id, opt_txb_id
		order by (select SRT_ORDN from cm_cm_cd cd WHERE  cd.URNK_CM_CD='CRCL_CN_ARA' and cd.CM_CD=AA.KMMP_NOD_ID) 
		
	</select>
	
	<!-- 단원별 상세 현황 : 차시별 분석 탭 - 학습 필요 학생 목록 -->
	<select id="selectPartAnTabStuList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectPartAnTabStuList */
		select usr.usr_id as stu_id
		     , usr.usr_nm as stu_nm
		     , t1.cans_rt
		  from (
		  
			select
	  		     eer.USR_ID
				 , max(ee.FNL_QST_CNT) as fnl_qst_cnt
		         , sum(case when eeqa.cans_yn = 'Y' then 1 else 0 end) cans_cnt
		         , round(sum(case when eeqa.cans_yn = 'Y' then 1 else 0 end)/count(1), 1) as cans_rt
			FROM   lms_lrm.ea_ev ee
			       INNER JOIN lms_lrm.ea_ev_rs eer on ee.ev_id = eer.ev_id
			       INNER JOIN lms_lrm.ea_ev_qtm_anw eeqa
			       ON     ee.EV_ID=eeqa.EV_ID  AND  eer.USR_ID=eeqa.USR_ID
			       inner join lms_cms.qp_qtm_an qta on eeqa.QTM_ID=qta.QP_QTM_ID
			WHERE eer.EV_CMPL_YN='Y'
			AND ee.OPT_TXB_ID= #{optTxbId}
			AND ((EE.EV_DV_CD ='AE' AND  EE.EV_DTL_DV_CD ='OV') OR (EE.EV_DV_CD ='SE' AND EE.EV_DTL_DV_CD IN('TO','FO','UG')))
			AND exists (select 1 from ea_ev_ts_rnge z where z.LU_LRMP_NOD_ID=  #{lluNodId}   AND ee.ev_id=z.EV_ID)
			AND qta.QP_CN_ARA_ID=#{lrmpNodId}
			group by eeqa.USR_ID
			  ) t1
		 inner join lms_lrm.cm_usr usr
		    on t1.usr_id = usr.usr_id 
		 where usr.usr_tp_cd = 'ST' 
		   and t1.cans_rt <![CDATA[<=]]> #{cansRt}
		 order by t1.cans_rt  
	</select>
	
	<!-- 단원별 상세 현황 : 성취기준별 분석 -->
	<select id="selectAchBsAnTabList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectAchBsAnTabList */
		select 
			  max(bncm.CRCL_ACH_BS_CD) as edu_crs_cn_cd
			, max(bncm.CRCL_ACH_BS_NM) as edu_crs_cn_nm 
			, count(1) as total
			, ROUND((SUM(CASE WHEN eqa.CANS_YN='Y' THEN 1 ELSE 0 END )/count(eqa.cans_yn)) * 100) as cansRt
		  from lms_lrm.ea_ev e  
		 inner join lms_lrm.ea_ev_qtm eq
		    on e.ev_id = eq.ev_id
		 inner join lms_lrm.ea_ev_rs er
		    on e.ev_id = er.ev_id		   
		 inner join  lms_lrm.cm_usr cu
		     	ON cu.usr_tp_cd = 'ST'
		     	AND cu.USR_ID=er.USR_ID		    
		 inner join lms_lrm.ea_ev_qtm_anw eqa
		    on e.ev_id = eqa.ev_id 
		   and eq.qtm_id = eqa.qtm_id
		   and er.usr_id = eqa.usr_id
		 inner join lms_cms.bc_ntnl_crcl_ctn_mpn_v2 bncm 
		    on bncm.ctn_id = eqa.qtm_id  -- 국가수준 교육과정 컨텐츠 매핑
		   and bncm.crcl_ctn_tp_cd = 'ex' -- 문항만
		 where e.opt_txb_id = #{optTxbId}
		   and exists(SELECT 1 FROM lms_lrm.ea_ev_ts_rnge where ev_id = e.ev_id and lu_lrmp_nod_id = #{lluNodId})
		   and (   (e.ev_dv_cd = 'SE' and e.ev_dtl_dv_cd in ('FO','FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
		        or (e.ev_dv_cd = 'AE' and e.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
		        )
		   and er.ev_cmpl_yn = 'Y'  -- 평가 완료한 학생만
		 group by bncm.CRCL_ACH_BS_CD			
	 	 order by edu_crs_cn_cd		 
	</select>
	
	<!-- 내용체계영역별 성취 현황 -->
	<select id="selectCrsCnAchPstList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - selectCrsCnAchPstList */
		<choose>
			<when test="'EN'.equals(sbjCd) || 'CE1'.equals(sbjCd) || 'CE2'.equals(sbjCd)">
				/* 영어일 경우 */
				select A.edu_crs_cn_cd
				     , A.QP_CN_ARA_NM as edu_crs_cn_nm
				     , ifnull(A.cans_rt, 0) as schl_cans_rt
				     , ifnull(B.cans_rt, 0) as cans_rt
				     , ifnull(A.qtm_cnt, 0) as schl_qtm_cnt
				     , ifnull(B.qtm_cnt, 0) as qtm_cnt
				     , ifnull(A.sum_lrn_scnt, 0) as schl_sum_lrn_scnt
				     , ifnull(B.sum_lrn_scnt, 0) as sum_lrn_scnt
				  from (
				    select qan.QP_CN_ARA_ID  AS  edu_crs_cn_cd  -- 내용체계영역별 코드,
				         , qan.QP_CN_ARA_NM
				         , round(sum(case when eqa.cans_yn = 'y' then 1 else 0 end)/count(eq.qtm_id)*100, 1) as cans_rt -- 내용체계 영역별 정답률
				         , count(eqa.qtm_id) as qtm_cnt
				         , sum(eqa.xpl_tm_scnt) as sum_lrn_scnt
				      from lms_lrm.ea_ev e  				   
				     inner join lms_lrm.ea_ev_qtm eq
				        on e.ev_id = eq.ev_id
				     inner join lms_lrm.ea_ev_qtm_anw eqa
				        on e.ev_id = eqa.ev_id 
				       and eq.qtm_id = eqa.qtm_id
				     inner join lms_lrm.ea_ev_rs er
				        on e.ev_id = er.ev_id
				       and eqa.usr_id = er.usr_id				       
				      inner join  lms_lrm.cm_usr cu
				     	ON cu.usr_tp_cd = 'ST'
				     	AND cu.USR_ID=er.USR_ID
				     inner join lms_cms.qp_qtm_an qan ON eqa.QTM_ID=qan.QP_QTM_ID
				     <!-- where e.opt_txb_id in (
				            select a.opt_txb_id 
				              from lms_lrm.cm_opt_txb a
				             where a.cla_id in (
				                select cla_id
				                  from lms_lrm.cm_cla
				                 where schl_cd = #{schlcd}
				                   and sgy = #{sgycd}
				                   and schl_grd_cd = #{schlgrdcd}
				             )
				               and a.txb_id = (
				                select txb_id
				                  from lms_lrm.cm_opt_txb
				                 where opt_txb_id = #{opttxbid}
				               )
				        ) -->
				        where e.opt_txb_id = #{optTxbId}
				       and er.ev_cmpl_yn = 'y'  -- 평가 완료한 학생만
				       and (   (e.ev_dv_cd = 'SE' and e.ev_dtl_dv_cd in ('FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
				            or (e.ev_dv_cd = 'AE' and e.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
				            )
				     group by qan.QP_CN_ARA_ID, qan.QP_CN_ARA_NM
				 ) A 
				  LEFT OUTER JOIN (
				    select qan.QP_CN_ARA_ID  AS  edu_crs_cn_cd  -- 내용체계영역별 코드,
				         , qan.QP_CN_ARA_NM  -- 내용체계영역별 코드
				         , round(sum(case when eqa.cans_yn = 'y' then 1 else 0 end)/count(eq.qtm_id)*100, 1) as cans_rt -- 내용체계 영역별 정답률
				         , count(distinct eq.qtm_id) as qtm_cnt
				         , sum(eqa.xpl_tm_scnt) as sum_lrn_scnt
				      from lms_lrm.ea_ev e  				     
				     inner join lms_lrm.ea_ev_qtm eq
				        on e.ev_id = eq.ev_id
				     inner join lms_lrm.ea_ev_qtm_anw eqa
				        on e.ev_id = eqa.ev_id 
				       and eq.qtm_id = eqa.qtm_id
				     inner join lms_lrm.ea_ev_rs er
				        on e.ev_id = er.ev_id
				       and eqa.usr_id = er.usr_id				       
				      inner join  lms_lrm.cm_usr cu
				     	ON cu.usr_tp_cd = 'ST'
				     	AND cu.USR_ID=er.USR_ID
				       inner join lms_cms.qp_qtm_an qan ON eqa.QTM_ID=qan.QP_QTM_ID
				     where e .opt_txb_id = #{optTxbId}
				       and er.ev_cmpl_yn = 'y'  -- 평가 완료한 학생만
				       and (   (e.ev_dv_cd = 'SE' and e.ev_dtl_dv_cd in ('FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
				            or (e.ev_dv_cd = 'AE' and e.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
				            )
				     group by qan.QP_CN_ARA_ID, qan.QP_CN_ARA_NM
				) B ON A.edu_crs_cn_cd=B.edu_crs_cn_cd
			</when>
			<otherwise>
				/* 영어가 아닐 경우 */
				select d.edu_crs_cn_cd
				     , d.edu_crs_cn_ara_cd as edu_crs_cn_nm
				     , ifnull(schl.cans_rt, 0) as schl_cans_rt
				     , ifnull(cla.cans_rt, 0) as cans_rt
				     , ifnull(schl.qtm_cnt, 0) as schl_qtm_cnt
				     , ifnull(cla.qtm_cnt, 0) as qtm_cnt
				     , ifnull(schl.sum_lrn_scnt, 0) as schl_sum_lrn_scnt
				     , ifnull(cla.sum_lrn_scnt, 0) as sum_lrn_scnt
				  from lms_cms.bc_ntnl_crcl_std a
				 inner join (
				     select c.stn_sst_edu_crs_id
				      from lms_lrm.cm_opt_txb b
				     inner join lms_cms.bc_txb c
				        on b.txb_id = c.txb_id 
				       and b.opt_txb_id = #{optTxbId}
				 ) t1
				    on t1.stn_sst_edu_crs_id = a.stn_sst_edu_crs_id
				 inner join (
				     select edu_crs_cn_ara_cd
				          , max(edu_crs_cn_cd) as edu_crs_cn_cd
				          , stn_sst_edu_crs_id 
				       from lms_cms.bc_ntnl_crcl_std_cn_v2  
				      group by edu_crs_cn_ara_cd,stn_sst_edu_crs_id
				 ) d
				    on a.stn_sst_edu_crs_id = d.stn_sst_edu_crs_id
				  left outer join (
				    select bncm.crcl_ctn_std_cd AS edu_crs_cn_cd  -- 내용체계영역별 코드
				         , round(sum(case when eqa.cans_yn = 'y' then 1 else 0 end)/count(eq.qtm_id)*100, 1) as cans_rt -- 내용체계 영역별 정답률
				         , count(eqa.qtm_id) as qtm_cnt
				         , sum(eqa.xpl_tm_scnt) as sum_lrn_scnt
				      from lms_lrm.ea_ev e  				     
				     inner join lms_lrm.ea_ev_qtm eq
				        on e.ev_id = eq.ev_id
				     inner join lms_lrm.ea_ev_qtm_anw eqa
				        on e.ev_id = eqa.ev_id 
				       and eq.qtm_id = eqa.qtm_id
				     inner join lms_lrm.ea_ev_rs er
				        on e.ev_id = er.ev_id
				       and eqa.usr_id = er.usr_id				       
				      inner join  lms_lrm.cm_usr cu
				     	ON cu.usr_tp_cd = 'ST'
				     	AND cu.USR_ID=er.USR_ID
				     inner join lms_cms.bc_ntnl_crcl_ctn_mpn_v2 bncm 
		        		on bncm.ctn_id = eqa.qtm_id  -- 국가수준 교육과정 컨텐츠 매핑
		        			<!-- 
				     where e.opt_txb_id in (
				        select a.opt_txb_id 
				          from lms_lrm.cm_opt_txb a
				         where a.cla_id in (
				            select cla_id
				              from lms_lrm.cm_cla
				             where schl_cd = #{schlCd}
				               and sgy = #{sgyCd}
				               and schl_grd_cd = #{schlGrdCd}
				         )
				           and a.txb_id = (
				            select txb_id
				              from lms_lrm.cm_opt_txb
				             where opt_txb_id = #{optTxbId}
				           )
				    )
				    -->
				    where e.opt_txb_id = #{optTxbId}
				       and er.ev_cmpl_yn = 'y'  -- 평가 완료한 학생만
				       and bncm.crcl_ctn_tp_cd = 'ex' -- 문항만
				       and (   (e.ev_dv_cd = 'SE' and e.ev_dtl_dv_cd in ('FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
				            or (e.ev_dv_cd = 'AE' and e.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
				            )
				     group by bncm.crcl_ctn_std_cd     
				  ) schl
				    on d.edu_crs_cn_cd = schl.edu_crs_cn_cd
				  left outer join (
				    select bncm.crcl_ctn_std_cd AS edu_crs_cn_cd  -- 내용체계영역별 코드
				         , round(sum(case when eqa.cans_yn = 'y' then 1 else 0 end)/count(eq.qtm_id)*100, 1) as cans_rt -- 내용체계 영역별 정답률
				         , count(distinct eq.qtm_id) as qtm_cnt
				         , sum(eqa.xpl_tm_scnt) as sum_lrn_scnt
				      from lms_lrm.ea_ev e  				    
				     inner join lms_lrm.ea_ev_qtm eq
				        on e.ev_id = eq.ev_id
				     inner join lms_lrm.ea_ev_qtm_anw eqa
				        on e.ev_id = eqa.ev_id 
				       and eq.qtm_id = eqa.qtm_id
				     inner join lms_lrm.ea_ev_rs er
				        on e.ev_id = er.ev_id
				       and eqa.usr_id = er.usr_id				       
				      inner join  lms_lrm.cm_usr cu
				     	ON cu.usr_tp_cd = 'ST'
				     	AND cu.USR_ID=er.USR_ID
				     inner join lms_cms.bc_ntnl_crcl_ctn_mpn_v2 bncm 
		        		on bncm.ctn_id = eqa.qtm_id  -- 국가수준 교육과정 컨텐츠 매핑
				     where e .opt_txb_id = #{optTxbId}
				       and er.ev_cmpl_yn = 'y'  -- 평가 완료한 학생만
				       and bncm.crcl_ctn_tp_cd = 'ex' -- 문항만
				       and (   (e.ev_dv_cd = 'SE' and e.ev_dtl_dv_cd in ('FO', 'UG'))   -- 교과학습(SE)의 형성평가(FO), 단원평가(UG)
				            or (e.ev_dv_cd = 'AE' and e.ev_dtl_dv_cd = 'OV')            -- AI추천학습(AE)의 AI 진단평가(OV)
				            )
				     group by bncm.crcl_ctn_std_cd
				  ) cla
				    on substr(d.edu_crs_cn_cd,1,8) = cla.edu_crs_cn_cd
		 		 order by d.edu_crs_cn_cd
			</otherwise>
		</choose>
	</select>
	
	<!-- 내용체계영역별 성취 학생&선생님 별 현황 (학급전체 아님)-->
	<select id="selectCrsCnAchPerStuOrTcrList" resultType="com.aidt.api.bc.claan.dto.BcClaanResDto">
		/* BcClaanTcr-Mapper - "selectCrsCnAchPerStuOrTcrList" */
		select d.edu_crs_cn_cd
		     , d.edu_crs_cn_nm
		     , d.srt_ordn
		     , ifnull(cla.cans_rt, 0) as cans_rt
		     , ifnull(cla.qtm_cnt, 0) as qtm_cnt
		     , ifnull(cla.sum_lrn_scnt, 0) as sum_lrn_scnt
		  from lms_cms.bc_ntnl_crcl_std a
		 inner join (
		     select c.stn_sst_edu_crs_id
		      from lms_lrm.cm_opt_txb b
		     inner join lms_cms.bc_txb c
		        on b.txb_id = c.txb_id 
		       and b.opt_txb_id = #{optTxbId}
		 ) t1
		    on t1.stn_sst_edu_crs_id = a.stn_sst_edu_crs_id
		 inner join lms_cms.bc_ntnl_crcl_std_cn d
		    on a.stn_sst_edu_crs_id = d.stn_sst_edu_crs_id
		   and d.edu_crs_cn_dv_cd = 'cs'
		  left outer join (
		    select bncm.crcl_ctn_std_cd  -- 내용체계영역별 코드
		         , round(sum(case when eqa.cans_yn = 'y' then 1 else 0 end)/count(eqa.qtm_id)*100, 1) as cans_rt -- 내용체계 영역별 정답률
		         , count(eqa.qtm_id) as qtm_cnt
		         , sum(eqa.xpl_tm_scnt) as sum_lrn_scnt
		      from lms_lrm.cm_opt_txb ot
		      join lms_lrm.cm_usr usr on usr.cla_id = ot.cla_id
		      join lms_lrm.ea_ev e on ot.opt_txb_id = e.opt_txb_id  
		      join lms_lrm.ea_ev_rs er on er.ev_id = e.ev_id and er.usr_id = usr.usr_id
		      join lms_lrm.ea_ev_qtm_anw eqa on eqa.ev_id = e.ev_id and eqa.usr_id = usr.usr_id
		      join lms_cms.bc_ntnl_crcl_ctn_mpn_v2 bncm on bncm.ctn_id = eqa.qtm_id  -- 국가수준 교육과정 컨텐츠 매핑
		     where ot.opt_txb_id = #{optTxbId}
		       and er.ev_cmpl_yn = 'y'  -- 평가 완료한 학생만
		       and bncm.crcl_ctn_tp_cd = 'ex' -- 문항만
		       and usr.usr_id = #{usrId}
		       and BNCM.CRCL_CTN_STD_CD is not null
		     group by bncm.crcl_ctn_std_cd
		  ) cla
		    on d.edu_crs_cn_cd = cla.crcl_ctn_std_cd
		 order by d.srt_ordn
	</select>
	
	
	<select id="selectLrnPref" parameterType="com.aidt.api.bc.claan.dto.BcClaanReqDto" resultType="hashMap">
		select 
			ifnull((sum(clt.TXB_LRN_TM_SCNT + clt.SP_LRN_TM_SCNT + clt.AI_WRTNG_LRN_TM) + ifnull((select sum(LRN_TM_SCNT)from lms_lrm.cm_wrt_mg where OPT_TXB_ID = cot2.OPT_TXB_ID),0)) / count(distinct cu.USR_ID) , 0) as txbLrnTmScnt , -- 개념학습시간
			ifnull(sum(clt.EV_LRN_TM_SCNT + clt.AI_LRN_TM_SCNT + clt.AI_EV_LRN_TM_SCNT) / count(distinct cu.USR_ID) , 0) as evLrnTmScnt -- 평가풀이시간
			from lms_lrm.cm_opt_txb cot2
			inner join lms_lrm.cm_usr cu 
				on cu.CLA_ID = cot2.CLA_ID 
				and cu.USR_TP_CD = 'ST'
			inner join lms_lrm.cm_lrn_tm clt 
				on clt.OPT_TXB_ID = cot2.OPT_TXB_ID 
				and clt.USR_ID = cu.USR_ID 
			where cot2.OPT_TXB_ID = #{optTxbId}
			/* 학급분석 - 정은혜 BcClaanTcr-Mapper.xml  selectLrnPref- 선호도분석 */
	</select>
	
	<!--학급분석 챌린지 달성률  -->		
	<select id="selectLrnChal" parameterType="com.aidt.api.bc.claan.dto.BcClaanReqDto" resultType="hashMap">
		/* BcClaanTcr-Mapper - selectLrnChal */
		SELECT 
			  ROUND(SUM(tb.stuRate) / COUNT(1), 1) AS RATE
			, COUNT(1) AS TOTAL	
		FROM (		
				SELECT 
					  tlc.LRN_USR_ID	    		    						
					, ROUND(COUNT(CASE WHEN tlc.LRN_GOAL_ST_CD = 'CL' THEN 1 END) / COUNT(CASE WHEN tlc.LRN_GOAL_ST_CD != 'QT' THEN 1 END) * 100, 1) AS stuRate
				FROM lms_lrm.cm_opt_txb cot2
				INNER JOIN lms_lrm.cm_usr cu 
						ON cu.CLA_ID = cot2.CLA_ID 
					   AND cu.USR_TP_CD = 'ST' 
				INNER JOIN LMS_LRM.TL_LRN_CHLG tlc                /* 학습 챌린지 */
						ON tlc.opt_txb_id = cot2.opt_txb_id
					   AND tlc.lrn_usr_id = cu.usr_id
					   AND tlc.del_yn = 'N'
				WHERE cot2.OPT_TXB_ID = #{optTxbId}
				AND YEARWEEK(tlc.LRN_STR_DT, 1) <![CDATA[ <= ]]> YEARWEEK(CURDATE(), 1) - 1
				GROUP BY tlc.LRN_USR_ID 
		) AS tb
	</select>
	
	<!-- 학급분석 평균 챌린지 도전율 -->
	<select id="selectLrnChalRate" parameterType="Map" resultType="Double">
		/* BcClaanTcr-Mapper - selectLrnChalRate */
		SELECT 
			IFNULL(ROUND(COUNT(distinct LC.LRN_USR_ID) / (SELECT COUNT(1) FROM LMS_LRM.CM_USR WHERE CLA_ID = (select CLA_ID from lms_lrm.cm_opt_txb where OPT_TXB_ID = #{optTxbId}) AND USR_TP_CD = 'ST') * 100 , 1), 0)
		FROM LMS_LRM.CM_USR USR 
		INNER JOIN LMS_LRM.TL_LRN_CHLG LC 
				ON USR.USR_ID = LC.LRN_USR_ID
			   AND LC.OPT_TXB_ID = #{optTxbId}
			   AND LC.lrn_end_dt BETWEEN #{startDate} AND #{endDate}
		WHERE 1=1
		AND USR.CLA_ID = #{optTxbId}
		AND USR.USR_TP_CD = 'ST'
	</select>
	
	
	<select id="selectEaLrnAsnInfo" parameterType="com.aidt.api.bc.claan.dto.BcClaanReqDto" resultType="hashMap">		
		SELECT
			IFNULL(ROUND(SUM(t.stuRatio) / (SELECT COUNT(1) FROM LMS_LRM.CM_USR WHERE CLA_ID = (select CLA_ID from lms_lrm.cm_opt_txb where OPT_TXB_ID = #{optTxbId}) AND USR_TP_CD = 'ST'), 1), 0) AS ratio
		FROM (
				SELECT  
					  eas.STU_USR_ID 
					, ROUND(SUM(CASE WHEN eas.SMT_CMPL_YN = 'Y' THEN 1 ELSE 0 END) / COUNT(eas.ASN_ID) * 100 ,1) AS stuRatio
					, COUNT(eas.ASN_ID) 
				FROM lms_lrm.ea_asn ea 
				INNER JOIN lms_lrm.cm_opt_txb cot2
						ON cot2.OPT_TXB_ID = ea.OPT_TXB_ID 
				INNER JOIN lms_lrm.cm_usr cu 
						ON cu.CLA_ID = cot2.CLA_ID 
					   AND cu.USR_TP_CD = 'ST' 
				INNER JOIN lms_lrm.ea_asn_smt eas 
						ON eas.ASN_ID = ea.ASN_ID 
					   AND eas.STU_USR_ID = cu.USR_ID 
				WHERE ea.OPT_TXB_ID = #{optTxbId}
				AND ea.DEL_YN = 'N'
				GROUP BY eas.STU_USR_ID 
			) t  
		/* 정은혜 BcClaanTcr-Mapper.xml -selectEaLrnAsnInfo  학급 과제제출률  */
	</select>
	
	<select id="selectAnPerWeek" resultType="com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto">
		SELECT
		    w.week,
		    IFNULL(SUM(G.evTmScnt), 0) AS evTmScnt
		from 
		    (SELECT 1 AS day_of_week, '일' AS week
		     UNION ALL
		     SELECT 2 AS day_of_week, '월'
		     UNION ALL
		     SELECT 3 AS day_of_week, '화'
		     UNION ALL
		     SELECT 4 AS day_of_week, '수'
		     UNION ALL
		     SELECT 5 AS day_of_week, '목'
		     UNION ALL
		     SELECT 6 AS day_of_week, '금'
		     UNION ALL
		     SELECT 7 AS day_of_week, '토') w 
	     left join (
				select
					clt.TXB_LRN_TM_SCNT + clt.AI_LRN_TM_SCNT + clt.SP_LRN_TM_SCNT + clt.EV_LRN_TM_SCNT + clt.AI_EV_LRN_TM_SCNT + clt.AI_WRTNG_LRN_TM as evTmScnt,
					clt.CRT_DTM as stdDate,
					DAYOFWEEK(clt.CRT_DTM) as day_of_week 
				from lms_lrm.cm_opt_txb cot2
				inner join lms_lrm.cm_usr cu 
					on cu.CLA_ID = cot2.CLA_ID 
					and cu.USR_TP_CD = 'ST' 
				inner join lms_lrm.cm_lrn_tm clt
					on clt.opt_txb_id = cot2.opt_txb_id
					and clt.usr_id = cu.usr_id
				where
					1=1
					and cot2.OPT_TXB_ID = #{optTxbId}
				union all 				
				select 
					LRN_TM_SCNT as evTmScnt
					,STU_SAV_DTM as stdDate
					,DAYOFWEEK(STU_SAV_DTM) as day_of_week 
				from lms_lrm.cm_wrt_mg cwm 				
				where OPT_TXB_ID = #{optTxbId}
				and LRN_TM_SCNT is not null 
				) G ON w.day_of_week = G.day_of_week
			GROUP BY
    			w.day_of_week, w.week
			ORDER BY
    			w.day_of_week
	</select>
	
	<select id="selectAnPerTm" resultType="com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto">
		SELECT 
		    ts.tm,
    		IFNULL(MAX(clt.evTmScnt),0) AS evTmScnt,
    		IFNULL(MAX(clt.userCount), 0) AS userCount
		FROM 
		    (SELECT '오전' AS tm
		     UNION ALL
		     SELECT '오후'
		     UNION ALL
		     SELECT '저녁'
		     UNION ALL
		     SELECT '새벽') AS ts
		LEFT JOIN (
			SELECT
				tb.tm
				,sum(tb.evTmScnt) as evTmScnt
				,sum(tb.userCount) as userCount
			from (		
			    	SELECT 
						CASE
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[>=]]> 8 AND HOUR(clt.MDF_DTM) <![CDATA[<]]> 12 THEN '오전'
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[=]]> 12 AND MINUTE(clt.MDF_DTM) <![CDATA[=]]> 0 THEN '오전'
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[>=]]> 12 AND HOUR(clt.MDF_DTM) <![CDATA[<]]> 18 THEN '오후'
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[=]]> 18 AND MINUTE(clt.MDF_DTM) <![CDATA[=]]> 0 THEN '오후'
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[>=]]> 18 AND HOUR(clt.MDF_DTM) <![CDATA[<]]> 24 THEN '저녁'
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[=]]> 0 and MINUTE(clt.MDF_DTM) <![CDATA[=]]> 0  THEN '저녁'
					        when HOUR(clt.MDF_DTM) <![CDATA[=]]> 0 and MINUTE(clt.MDF_DTM) <![CDATA[>]]> 0 then '새벽'
					        WHEN HOUR(clt.MDF_DTM) <![CDATA[>=]]> 1 AND HOUR(clt.MDF_DTM) <![CDATA[<]]> 8 THEN '새벽'
					    END AS tm,
					    SUM(TXB_LRN_TM_SCNT + AI_LRN_TM_SCNT + SP_LRN_TM_SCNT + EV_LRN_TM_SCNT + AI_EV_LRN_TM_SCNT +AI_WRTNG_LRN_TM) as evTmScnt,
					    COUNT(clt.USR_ID) AS userCount
					from lms_lrm.cm_lrn_tm clt 
					inner join lms_lrm.cm_opt_txb cot2 
						on cot2.OPT_TXB_ID = clt.OPT_TXB_ID 
					inner join lms_lrm.cm_usr cu 
						on cu.CLA_ID = cot2.CLA_ID 
						and cu.USR_TP_CD = 'ST'
					where clt.OPT_TXB_ID = #{optTxbId}
					and clt.USR_ID = cu.USR_ID
					GROUP BY tm, clt.usr_id
					union all 
					SELECT 
						CASE
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[>=]]> 8 AND HOUR(STU_SAV_DTM) <![CDATA[<]]> 12 THEN '오전'
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[=]]> 12 AND MINUTE(STU_SAV_DTM) <![CDATA[=]]> 0 THEN '오전'
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[>=]]> 12 AND HOUR(STU_SAV_DTM) <![CDATA[<]]> 18 THEN '오후'
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[=]]> 18 AND MINUTE(STU_SAV_DTM) <![CDATA[=]]> 0 THEN '오후'
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[>=]]> 18 AND HOUR(STU_SAV_DTM) <![CDATA[<]]> 0 THEN '저녁'
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[=]]> 0 and MINUTE(STU_SAV_DTM) <![CDATA[=]]> 0  THEN '저녁'
					        when HOUR(STU_SAV_DTM) <![CDATA[=]]> 0 and MINUTE(STU_SAV_DTM) <![CDATA[>]]> 0 then '새벽'
					        WHEN HOUR(STU_SAV_DTM) <![CDATA[>=]]> 1 AND HOUR(STU_SAV_DTM) <![CDATA[<]]> 8 THEN '새벽'
					    END AS tm,
					    SUM(LRN_TM_SCNT) as evTmScnt,
					    COUNT(STU_USR_ID) AS userCount			
					from lms_lrm.cm_wrt_mg
					where OPT_TXB_ID = #{optTxbId}
					and LRN_TM_SCNT is not null
					group by tm, STU_USR_ID						
			) as tb			
			group by tb.tm	
		) clt on ts.tm = clt.tm
		GROUP BY
			ts.tm
		ORDER BY
		    FIELD(ts.tm, '오전', '오후', '저녁', '새벽') 
		/*학급분석- 정은혜 BcClaanTcr-Mapper.xml -selectAnPerTm  시간대별 분석  */ 
	</select>
	
</mapper>