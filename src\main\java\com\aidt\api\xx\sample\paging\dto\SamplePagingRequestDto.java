package com.aidt.api.xx.sample.paging.dto;


import com.aidt.common.Paging.PagingRequestDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample RequestDto DTO
 */
@Getter
@Setter
public class SamplePagingRequestDto extends PagingRequestDto{
    @Parameter(name="검색 조건 usrId")
    private String usrId;

    // toString이 필요한 경우 Extends한 객체의 toString을 받아서 함께 사용
    @Override
    public String toString() {
        String str = super.toString();
        return "SamplePagingRequestDto(usrId=" + usrId + ") "+ str;
    }

}
