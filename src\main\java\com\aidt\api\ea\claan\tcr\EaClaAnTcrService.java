package com.aidt.api.ea.claan.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto;
import com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학급 분석 Service
 */
@Slf4j
@Service
public class EaClaAnTcrService {

	private final String MAPPER_NAMESPACE = "api.ea.claan.tcr.";
	
	@Autowired
	private CommonDao commonDao;

	/**
	 * 학급 분석 조회 요청
	 *
	 * @param param
	 * @return Map<String,Object>
	 */
	public Map<String,Object> selectEaClaAnInfo(EaClaAnTcrDto param) {
		Map<String,Object> map = new HashMap<>();
		//학급 인원 조회
		param.setClaCnt((Integer) commonDao.select(MAPPER_NAMESPACE+"selectClaCnt",param));
		//학년 운영 교과서 ID 조회
		param.setOptTxbIdList(commonDao.<EaClaAnTcrDto>selectList(MAPPER_NAMESPACE+"selectOptTxbIdList",param));

		//학습 시간 + 평가 결과 조회
		map.put("stuTimeEvResInfo",commonDao.select(MAPPER_NAMESPACE + "selectStuTimeEvResInfo", param));
//		//강점 차시 top3
//		param.setSorting("DESC");
//		map.put("goodTcTop3",commonDao.selectList(MAPPER_NAMESPACE+"selectGoodAndBadTcTop3",param));
//		//취약 차시 top3
//		param.setSorting("ASC");
//		map.put("badTcTop3",commonDao.selectList(MAPPER_NAMESPACE+"selectGoodAndBadTcTop3",param));
		
		Map<String,Object> fstLrnDt = commonDao.select(MAPPER_NAMESPACE + "selectFstLrnDt", param);
		double weekdayCnt = (long)fstLrnDt.get("minCrtDtm");
		
		//우리반 학습 분포도 > 학생별 성취도, 학습시간
		List<Map<String, Object>> claAnUsrList = commonDao.selectList(MAPPER_NAMESPACE+"selectClaAnUsrsAchd2",param);
//		map.put("usrsAchdChart",commonDao.selectList(MAPPER_NAMESPACE+"selectClaAnUsrsAchd",param));
		for(Map<String, Object> claAnUsr : claAnUsrList) {
	//		log.debug("################# acnt {}", claAnUsr.get("mdfdtm"));
			double x = (double)claAnUsr.get("ACNT");
			
			if(x > 0.0) {
				double scntRatio = (x/weekdayCnt) * 100;
				
				claAnUsr.put("x", Math.round(scntRatio * 100.0) / 100.0);
			} else {
				claAnUsr.put("x", x);
			}
		}
		
		map.put("usrsAchdChart",claAnUsrList);
		
		param.setUsrsAchdList(claAnUsrList);
		
		// 난이도별 상중하
		if(claAnUsrList.size() > 0) {
			map.put("claDfclRatio", commonDao.selectList(MAPPER_NAMESPACE+"selectClaDfclRatioList",param));
		}
		
		
		
		/*
		//우리반 학습 분포도 > 학생별 성취도, 학습시간
		List<Map<String,Object>> learDistChart = commonDao.selectList(MAPPER_NAMESPACE+"selectLearDistChart",param);
		List<String> usrNmLabels = new ArrayList<>();
		List<Map<String,Integer>> usrChartXY = new ArrayList<>();
		List<Integer> listY = new ArrayList<>();
		for (Map<String,Object> e : learDistChart){
			Map<String,Integer> xy = new HashMap<>();
			usrNmLabels.add(e.get("usrNm").toString());
			xy.put("x",Integer.parseInt(e.get("x").toString()));
			xy.put("y",Integer.parseInt(e.get("y").toString()));
			listY.add(Integer.parseInt(e.get("y").toString()));
			usrChartXY.add(xy);
		}
		map.put("usrNmLabels",usrNmLabels);//우리반 학습 분포도 > 학생명 라벨
		map.put("usrChartXY",usrChartXY); // 우리반 학습 분포도 > 학생 성취도(x),학습 시간(y)
		map.put("maxY",Collections.max(listY));//y 최대 값
		*/
		
//		//단원별 성취 현황 > 우리반 평균
//		map.put("myClassRt",commonDao.selectList(MAPPER_NAMESPACE+"selectAchStatByUnitMyClass",param));
//		//단원별 성취 현황 > 학년 전체 평균
//		map.put("schTotRt",commonDao.selectList(MAPPER_NAMESPACE+"selectAchStatByUnitSchTot",param));
//		//단원별 상세 현황 조회
//		//단원 목록 조회
//		List<Map<String,Object>> lluNodIdList = commonDao.selectList(MAPPER_NAMESPACE+"selectLluNodIdList",param);
//		for(Map<String,Object> e : lluNodIdList){
//			param.setLluNodId(Integer.parseInt(e.get("lrmpNodId").toString()));
//			Map<String,Object> lluInfo = commonDao.select(MAPPER_NAMESPACE+"selectLluMyClassAvg",param);//정답 수, 문제 수, 우리반 평균
//			lluInfo.put("totRat",commonDao.select(MAPPER_NAMESPACE+"selectLluTotAvg",param));// 전체 평균
//
//			e.put("lluInfo",lluInfo);
//			e.put("levelAvg",commonDao.selectList(MAPPER_NAMESPACE+"selectLevelUnitAvgList",param));//난이도 별 평균
//		}
//		map.put("lluNodList",lluNodIdList);

		//오답 BEST
		map.put("wrongAnwBest",commonDao.selectList(MAPPER_NAMESPACE+"selectWrongAnwBestList",param));
		return map;
	}


	/**
	 * 오답 BEST 조회 요청
	 *
	 * @param param
	 * @return List<Map<String,Object>>
	 */
	public List<Map<String,Object>> selectWrongAnwBestList(EaClaAnTcrDto param) {
		return commonDao.selectList(MAPPER_NAMESPACE+"selectWrongAnwBestList",param);
	}

	/**
	 * 오답 BEST 오답, 오답유사 시험지 출제 등록 요청
	 *
	 * @param param
	 * @return Map<String,Object>
	 */
	@Transactional
	public Map<String,Object> saveEaClaAnTcrTestPaper(EaClaAnTcrSaveDto param) {
		Map<String,Object> map = new HashMap<>();
		map.put("SUCCESS","Y");
		//EA_EV 평가 테이블 등록
		commonDao.insert(MAPPER_NAMESPACE + "insertEaEv", param);

		int seqNo   =0;//평가 시험 범위 순번
		int qtmOrdn =0;//문항 순서
		if("wrong".equals(param.getType())){// 오답 문제 출제
			//EA_EV_TS_RNGE 평가 시험 범위 등록
			for (String qpLluId : param.getQpLluIdList()){
				seqNo = seqNo+1;
				param.setTsRngeSeqNo(seqNo);
				param.setLuLrmpNodId(qpLluId);
				commonDao.insert(MAPPER_NAMESPACE+"insertEaEvTsRnge",param);
			}
			//EA_EV_DFFD_CSTN 평가 난이도 등록
			for (String evDffdDvCd : param.getEvDffdDvCdList()){
				int count = (int) param.getQtmList().stream().filter(e -> evDffdDvCd.equals(e.getQtmDffdDvCd())).count();
				param.setEvDffdDvCd(evDffdDvCd);//평가 난이도 구분 코드
				param.setEvDffdDsbCnt(count);   //평가 난이도 분포 수
				commonDao.insert(MAPPER_NAMESPACE+"insertEaEvDffdCstn",param);
			}
			//EA_EV_QTM 평가 문항 등록
			for (EaClaAnTcrSaveDto dto : param.getQtmList()){
				qtmOrdn = qtmOrdn+1;
				dto.setEvId(param.getEvId());
				dto.setQtmOrdn(qtmOrdn);
				dto.setDelYn("N");
				dto.setUsrId(param.getUsrId());
				dto.setDbId(param.getDbId());
				commonDao.insert(MAPPER_NAMESPACE+"insertEaEvQtm",dto);
			}

		}else{// 오답 유사 문제 출제
			List<String> qpLluIdList    = new ArrayList<>();
			List<String> evDffdDvCdList = new ArrayList<>();

			for (EaClaAnTcrSaveDto dto : param.getQtmList()){//오답 문항 리스트
				qtmOrdn = qtmOrdn+1;
				//유사 문항 조회
				EaClaAnTcrSaveDto similarQtm = commonDao.select(MAPPER_NAMESPACE+"selectClaAnSimilarQpQtm",dto);
				if (similarQtm!=null && !similarQtm.getQtmId().isEmpty()){//유사 문항 존재
					qpLluIdList.add(similarQtm.getQpLluId());
					evDffdDvCdList.add(similarQtm.getQtmDffdDvCd());

					similarQtm.setEvId(param.getEvId());
					similarQtm.setQtmOrdn(qtmOrdn);
					similarQtm.setDelYn("N");
					similarQtm.setUsrId(param.getUsrId());
					similarQtm.setDbId(param.getDbId());
					commonDao.insert(MAPPER_NAMESPACE+"insertEaEvQtm",similarQtm);
				}else{// 유사 문항 미존재 기존 오답 문항으로 등록
					qpLluIdList.add(dto.getQpLluId());
					evDffdDvCdList.add(dto.getQtmDffdDvCd());

					dto.setEvId(param.getEvId());
					dto.setQtmOrdn(qtmOrdn);
					dto.setDelYn("N");
					dto.setUsrId(param.getUsrId());
					dto.setDbId(param.getDbId());
					commonDao.insert(MAPPER_NAMESPACE+"insertEaEvQtm",dto);
				}
			}
			List<String> qpLluId    = qpLluIdList.stream().distinct().collect(Collectors.toList());   // 단원 중복 값 제거
			List<String> evDffdDvCd = evDffdDvCdList.stream().distinct().collect(Collectors.toList());// 난이도 중복 값 제거

			//EA_EV_TS_RNGE 평가 시험 범위 등록
			for (String id : qpLluId){
				seqNo = seqNo+1;
				param.setTsRngeSeqNo(seqNo);
				param.setLuLrmpNodId(id);
				commonDao.insert(MAPPER_NAMESPACE+"insertEaEvTsRnge",param);
			}

			//EA_EV_DFFD_CSTN 평가 난이도 등록
			for (String cd : evDffdDvCd){
				int count = (int) evDffdDvCdList.stream().filter(cd::equals).count();//문항 난이도 분포 수
				param.setEvDffdDvCd(cd); 		//평가 난이도 구분 코드
				param.setEvDffdDsbCnt(count);   //평가 난이도 분포 수
				commonDao.insert(MAPPER_NAMESPACE+"insertEaEvDffdCstn",param);
			}
		}

		return map;
	}

	/**
	 * 학급분석 > 전체학급관리
	 *
	 * @param param
	 * @return Map<String,Object>
	 */
	@Transactional
	public Map<String,Object> selectClaAllInfo(EaClaAnTcrDto param) {

		Map<String,Object> map = new HashMap<>();
		
		// 반 별 학생 수
		
		// 교과(개념)학습 진도율
		map.put("selectLrnSumList",commonDao.selectList(MAPPER_NAMESPACE+"selectLrnSumList",param));
		
		// 선생님 추천학습 진도율
		map.put("selectSlSumList",commonDao.selectList(MAPPER_NAMESPACE+"selectSlSumList",param));
		
		// AI 추천 학습 진도율
		map.put("selectAlSumList",commonDao.selectList(MAPPER_NAMESPACE+"selectAlSumList",param));
		
		//성취도, 학습 수준 
		map.put("selectEaCansSumList",commonDao.selectList(MAPPER_NAMESPACE+"selectEaCansSumList",param));
	
	  return map;

	}
	
}
