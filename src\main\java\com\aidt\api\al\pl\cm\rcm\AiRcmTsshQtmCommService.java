package com.aidt.api.al.pl.cm.rcm;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.aidt.api.al.pl.common.AlCmUtil;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.al.pl.dto.AlRcmLuEvCmplDto;
import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto;
import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @desc AI맞춤 문항추천 공통
 */
@Slf4j
@Service
public class AiRcmTsshQtmCommService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.back.aiRcmTsshQtm.";
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
	@Autowired private CommonDao commonDao;
	@Autowired private EaAsnComService eaAsnComService;
	
	
	
	/**
	 * 수학 진단평가시점의 토픽예측률로 학습자별 토픽 학습순서를 저장한다.
	 * 해당 순서는 최초 등록 이후 변경되지 않는다.
	 * */
	@Transactional
	public void updateAiUsrlyTpcLrnOrdn(List<AiRcmTsshQtmDto> evList, String usrId, String optTxbId, String sbjCd, String evDtlDvCd, String mluKmmpNodId) {
		//수학 진단평가 외 return
		if (!AlConstUtil.EV_DTL_DV_CD_OV.equals(evDtlDvCd) || !AlConstUtil.SBJ_MA.contains(sbjCd)) {
			return;
		}
		
		//수학 - 진단평가 다맞거나 다틀린경우 진단개념맵 방어로직
		int qtmCnt = 0;
		int qtmCansYCnt = 0;
		for (AiRcmTsshQtmDto ev : evList) {
			qtmCnt++;
			if(ev.getCansYn().equals("Y")) {
				qtmCansYCnt++;
			}
		}
		
		AiRcmTsshQtmDto req = new AiRcmTsshQtmDto();
		req.setOptTxbId(optTxbId);
		req.setMluKmmpNodId(mluKmmpNodId);
		req.setUsrId(usrId);
		req.setSbjCd(sbjCd);
		int lrnTpcCnt = commonDao.select(MAPPER_NAMESPACE + "selectLrnTpcCnt", req);

		// 학습해야 할 토픽 개수가 2개 이하일 경우 토픽숙련도 순으로 정렬된 리스트 가져오는것으로 수정(2024.11.20)
		if (lrnTpcCnt <= 2) {
			req.setTpcAvnStr("03");
		}
		List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPredAvgCansRtList", req);
		
		int lrnOrdn = 0;
		for (AiRcmTsshQtmDto dto : list) {
			lrnOrdn++;
			dto.setUsrId(usrId);
			dto.setOptTxbId(optTxbId);
			dto.setLrnOrdn(lrnOrdn);
			dto.setDbId("selectUsrlyQtmProf");
			dto.setAiDgnEvPredAvgCansRt(dto.getAiPredAvgCansRt());
			// 학습해야 할 토픽 개수가 없을 경우는 최취약3개, 1개 이상 2개 이하일 경우에는 학습해야할토픽+최취약2개만큼 학습하도록 처리(2024.10.11)
			// 최취약토픽 여부 체크는 학습해야 할 토픽 개수 중 마지막 2개 토픽에 최취약 토픽 여부 Y로 설정
			if (lrnTpcCnt == 0) {
				dto.setLrnYn(lrnOrdn <= 3 + lrnTpcCnt ? "Y" :  "N");
				dto.setNtnTpcYn(lrnOrdn <= 3 + lrnTpcCnt ? "Y" :  "N");
			} else if (lrnTpcCnt > 0 && lrnTpcCnt <= 2) {
				//틀린 토픽 + 강점토픽기준 이하일 경우에만 학습여부 'Y'
				//초등수학 && 토픽숙련도가 NULL인 토픽이 있는 경우에는 추가문항을 제공하지 않는다(2024.11.26)
				boolean hasNullTpcAvn = list.stream()
											.anyMatch(rtDto -> (req.getSbjCd().equals("MA") && rtDto.getTpcAvn() == null));

				if (hasNullTpcAvn) {
					dto.setLrnYn(dto.getTpcAvn() == null || dto.getTpcAvn() < AlConstUtil.TPC_AVN_03 || lrnOrdn <= lrnTpcCnt ? "Y" : "N");
					// null인 토픽이 있는 경우에는 심화를 붙이지 않는다(2024.11.27)
					dto.setNtnTpcYn("N");
				} else {
					dto.setLrnYn(dto.getTpcAvn() == null || dto.getTpcAvn() < AlConstUtil.TPC_AVN_03 || lrnOrdn <= 2 + lrnTpcCnt ? "Y" : "N");
					// null인 토픽이 없는 경우에는 풀어야 할 토픽이 1개일 경우 2,3번째 / 2개일 경우 3,4번째 토픽이 추가 토픽이므로 심화를 붙인다(2024.11.27)
					if (lrnOrdn == lrnTpcCnt + 1 || lrnOrdn == lrnTpcCnt + 2) {
						dto.setNtnTpcYn("Y");
					} else {
						dto.setNtnTpcYn("N");
					}
				}
			} else {
				dto.setLrnYn(dto.getTpcAvn() == null || dto.getTpcAvn() < AlConstUtil.TPC_AVN_03 ? "Y" :  "N" ); //강점토픽기준 이하일 경우에만 학습여부 'Y'
				dto.setNtnTpcYn("N"); // 최취약 토픽 심화 여부 N으로 처리
			}

			//수학 방어로직 - 진단평가에 출제되지않아 토픽숙련도가 없는 토픽은 토픽예측값을 set한다.
			dto.setAiDgnEvTpcAvn(dto.getTpcAvn() == null ? dto.getAiPredAvgCansRt() : dto.getTpcAvn());

			//진단평가 다틀린경우
			if (qtmCansYCnt == 0) {
				if(dto.getAiDgnEvTpcAvn() >= AlConstUtil.TPC_AVN_03) {
					//단원 내 강점토픽이 없도록 토픽숙련도 조정
					dto.setAiDgnEvTpcAvn(AlConstUtil.TPC_AVN_03 - 0.1);
				}
			}
			//진단평가 다맞은경우
			if (qtmCnt == qtmCansYCnt && qtmCnt > 0) {
				if(dto.getAiDgnEvTpcAvn() < AlConstUtil.TPC_AVN_03) {
					//단원 내 모든 토픽이 강점으로 세팅되도록 토픽숙련도 조정
					dto.setAiDgnEvTpcAvn(AlConstUtil.TPC_AVN_03 + 0.1);
				}
			}
			commonDao.insert(MAPPER_NAMESPACE + "insertAiUsrlyTpcLrnOrdn", dto);
		}
	}
	
	
	/**
	 * 
	 */
	
	/**
	 * 단원 내 같은평가 중복요청 확인
	 * */
	public int selectAeEvCheck(AiRcmTsshQtmReqDto dto) {
		return commonDao.select(MAPPER_NAMESPACE + "selectAeEvCheck", dto);
	}
	
	
	//단원완료여부 UPDATE 및 수학 토픽진행률 저장
	@Transactional
	public Map<String, Object> updateLuEvCmplYn(AlRcmLuEvCmplDto req) {
		Map<String, Object> resultMap = new HashMap<>();

		List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvInfoByEvId", req.getEvId());
		if(ObjectUtils.isEmpty(list)){
			resultMap.put("errorMessage", "평가정보가 없습니다.");
			return resultMap;
			//throw new IllegalArgumentException("평가정보가 없습니다.");
		}
		if(list.get(0).getEvCmplYn().equals("N")) {
			resultMap.put("errorMessage", "완료되지 않은 평가입니다.");
			return resultMap;
			//throw new IllegalArgumentException("완료되지 않은 평가입니다.");
		}
		
		String evDvCd = list.get(0).getEvDvCd();
		String evDtlDvCd = list.get(0).getEvDtlDvCd();
		String optTxbId = list.get(0).getOptTxbId();
		String mluKmmpNodId = list.get(0).getMluKmmpNodId();
		String tcKmmpNodId = list.get(0).getTcKmmpNodId();
		String tpcKmmpNodId = list.get(0).getTpcKmmpNodId();
		String lrnrVelTpCd = list.get(0).getLrnrVelTpCd();
		String evCmplYn = list.get(0).getEvCmplYn();
		
		//수학
		if(AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
			
			//수학 진단평가시 - 예측정답률이 0.8이상인경우 단원완료여부 예외('X')처리
			if(AlConstUtil.EV_DTL_DV_CD_OV.equals(evDtlDvCd)) {
				
				AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
				dto.setEvId(req.getEvId());
				dto.setUsrId(req.getUsrId());
				
				List<AiRcmTsshQtmDto> tpcAvn0102List = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPredAvgCansRt03List", dto);
				for (AiRcmTsshQtmDto tpc : tpcAvn0102List) {
					tpc.setLuevCmplYn("X");
					this.updateLuevCmplYnQuery(tpc);
				}
			}
			//진단평가 외 한 평가당 같은토픽으로만 이루어짐
			else {
				//토픽완료처리
				if(evDvCd.equals("AE")) {
					if(req.getTpcCmplYn().equals("Y")) {
						AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
						dto.setUsrId(req.getUsrId());
						dto.setEvId(req.getEvId());
						dto.setLuevCmplYn(req.getTpcCmplYn());
						dto.setOptTxbId(optTxbId);
						dto.setMluKmmpNodId(mluKmmpNodId);
						dto.setTcKmmpNodId(tcKmmpNodId);
						dto.setTpcKmmpNodId(tpcKmmpNodId);
						this.updateLuevCmplYnQuery(dto);
					}
				}else {
					//수학 오답풀기
					if(evCmplYn.equals("Y")) {
						AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
						dto.setUsrId(req.getUsrId());
						dto.setEvId(req.getEvId());
						dto.setLuevCmplYn("Y");
						dto.setOptTxbId(optTxbId);
						dto.setMluKmmpNodId(mluKmmpNodId);
						this.updateLuevCmplYnQuery(dto);
					}
				}
			}
		}//수학end
		
		//영어
		else if(AlConstUtil.SBJ_EN.contains(req.getSbjCd()) && (evDvCd.equals("AE") && evDtlDvCd.equals("C2"))) {
			
			AiRcmTsshQtmReqDto lrnrVelTpDto = new AiRcmTsshQtmReqDto();
			lrnrVelTpDto.setUsrId(req.getUsrId());
			lrnrVelTpDto.setOptTxbId(req.getOptTxbId());
			lrnrVelTpDto.setMluKmmpNodId(mluKmmpNodId);
			lrnrVelTpDto.setTcKmmpNodId(tcKmmpNodId);
			lrnrVelTpDto.setTpcKmmpNodId(tpcKmmpNodId);
			lrnrVelTpDto.setSbjCd(req.getSbjCd());
			
				
			lrnrVelTpCd = commonDao.select(MAPPER_NAMESPACE + "selectLrnrVelTpCd", lrnrVelTpDto);
			
			
			//영어 단원완료기준 조회
			//String luevCmplBs = enLuevCmplBsChk(req.getUsrId(), lrnrVelTpCd, optTxbId, mluKmmpNodId, tcKmmpNodId, req.getSchlGrdCd(), req.getSbjCd());
			
			//단원완료기준
			String luevCmplBs = "";
			
			//초등의 경우 C2까지만 진행하는 것으로 수정(2025.01.09)
			if(req.getSchlGrdCd().equals(AlConstUtil.SCHL_GRD_CD_ESCH)) {
				luevCmplBs = "C2";
			}
			//중고등영어 
			else if(req.getSchlGrdCd().equals(AlConstUtil.SCHL_GRD_CD_MCLS) || req.getSchlGrdCd().equals(AlConstUtil.SCHL_GRD_CD_HGH)) {
				luevCmplBs = "C2";
			}
			
			//단원완료처리
			AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
			dto.setUsrId(req.getUsrId());
			dto.setEvId(req.getEvId());
			dto.setLuevCmplYn(evDtlDvCd.equals(luevCmplBs) ? "Y" : "N");
			dto.setOptTxbId(optTxbId);
			dto.setMluKmmpNodId(mluKmmpNodId);
			dto.setTcKmmpNodId(tcKmmpNodId);
			this.updateLuevCmplYnQuery(dto);
			
		}//영어end

		return resultMap;
	}

	//todo: 비동기 작업 임시용
	public void updateAsnCmpl(AlPlQtmTpcProfDto req) {
		updateAsnCmpl(req, null);
	}

	//todo: 비동기 작업 임시용
	//과제
	@Transactional
	public void updateAsnCmpl(AlPlQtmTpcProfDto req, CommonUserDetail userDetail) {
		//단원 내 모든평가완료가 'Y'라면 해당단원에 과제가 있는경우 같이 완료처리한다. ('X'는 제외)
		boolean luevCmplYn = true;
		Integer cmplLrnCnt = 0;
		
		AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
		dto.setUsrId(req.getUsrId());
		dto.setMluKmmpNodId(req.getMluKmmpNodId());
		dto.setOptTxbId(req.getOptTxbId());
		List<AiRcmTsshQtmDto> luevCmplYnList = new ArrayList<>();
		if(AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
			luevCmplYnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEnLuevCmplYn", dto);
		}
		else if(AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
			luevCmplYnList = commonDao.selectList(MAPPER_NAMESPACE + "selectMaLuevCmplYn", dto);
		}
		
		if(luevCmplYnList != null && !luevCmplYnList.isEmpty()) {
			for (AiRcmTsshQtmDto info : luevCmplYnList) {
				if(info.getLuevCmplYn().equals("Y")) {
					cmplLrnCnt++;
				}else {
					luevCmplYn = false;
				}
			}
			
			dto.setLuevCmplYn(luevCmplYn ? "Y" : "N");
			dto.setCmplLrnCnt(cmplLrnCnt);
			commonDao.update(MAPPER_NAMESPACE + "updateSmtCmplYn", dto);
			
			//완료처리된 과제ID조회 - 알림서비스 호출
			String cmplAsnId = commonDao.select(MAPPER_NAMESPACE + "selectSmtCmplAsnId", dto);
			if(null != cmplAsnId) {
				EaAsnStuDto EaAsnStuDto = new EaAsnStuDto();
				EaAsnStuDto.setAsnId(Integer.parseInt(cmplAsnId));	//과제ID
				EaAsnStuDto.setAsnLrnTp("AL");
				EaAsnStuDto.setAsnLrnTpNm("AI 맞춤 학습");
				eaAsnComService.selectAllSmt(EaAsnStuDto, userDetail);
			}
				
		}
	}
	
	//토픽완료여부 update
	public void updateLuevCmplYnQuery(AiRcmTsshQtmDto dto) {
		commonDao.update(MAPPER_NAMESPACE + "updateLuevCmplYn", dto);
	}
	
	
	/**
	 * 영어 단원완료 기준 체크
	 * 
	 * */
	public String enLuevCmplBsChk(String usrId, String lrnrVelTpCd, String optTxbId, String mluKmmpNodId, String tcKmmpNodId, String schlGrdCd, String sbjCd) {
		//단원완료기준
		String luevCmplBs = "";
		
		AiRcmTsshQtmDto req = new AiRcmTsshQtmDto();
		req.setUsrId(usrId);
		req.setOptTxbId(optTxbId);
		req.setMluKmmpNodId(mluKmmpNodId);
		req.setTcKmmpNodId(tcKmmpNodId);
		//초등은 맞춤학습2만
		if(schlGrdCd.equals("E")) {
			req.setEvDtlDvCd("C2");
		}
		//중고등은 맞춤학습1,2 모두
		else {
			List<String> EvDtlDvCdList = new ArrayList<String>();
			EvDtlDvCdList.add("C1");
			EvDtlDvCdList.add("C2");
			req.setEvDtlDvCdList(EvDtlDvCdList);
		}
		
		//정답수 조회
		AiRcmTsshQtmDto cansInfo = commonDao.select(MAPPER_NAMESPACE + "selectEnTcCansYCntList", req);
			
		//초등의 경우 C2까지만 하는 것으로 변경(2025.01.09)
		if("E".equals(schlGrdCd)) {
			luevCmplBs = "C2";
		}
		//중고등 C1,C2에서 오답이 없는경우
		else if(schlGrdCd.equals(AlConstUtil.SCHL_GRD_CD_MCLS) || schlGrdCd.equals(AlConstUtil.SCHL_GRD_CD_HGH)) {
//			if(cansInfo.getCansCnt() <= 0) {
//				luevCmplBs = "C2";
//			}
			//중고등영어는 맞춤1,2 학습창분리로 맞춤3은 없어짐
			luevCmplBs = "C2";
		}
//		else {
//			luevCmplBs = "C3";
//		}
//		log.debug("단원완료기준:{}, 중단원:{}, 차시:{}, 정답수:{} ", luevCmplBs, mluKmmpNodId, tcKmmpNodId, cansInfo.getCansCnt());
		return luevCmplBs;
	}
	
	
	
	
	/**
     * AI 개념영상 리스트 조회
     * 
     * @param AiRcmTsshQtmDto - 중단원지식맵노드ID
     * @return List<Integer> :: AI_LRN_ATV_ID
     */
    @Transactional
    public List<AiRcmTsshQtmDto> selectCcptVdList(AiRcmTsshQtmDto dto) {
    	List<AiRcmTsshQtmDto> ccptVdList = commonDao.selectList(MAPPER_NAMESPACE + "selectCcptVdList", dto);
    	for (AiRcmTsshQtmDto aiRcmTsshQtmDto : ccptVdList) {
    		String url = AlCmUtil.makeFleCdnUrl(BUCKET_NAME, aiRcmTsshQtmDto.getCdnPthNm()+ "images/poster.png");
    		aiRcmTsshQtmDto.setCdnPthNmThumnail(url);
		}
    	return ccptVdList;
    }
    
    
    /**
     * AI 개념영상 리스트 차시별 매핑
     * 
     * @param AiRcmTsshQtmDto - 중단원학습맵노드ID
     * @return List<Integer> :: AI_LRN_ATV_ID
     */
    @Transactional
    public Map<String, List<AiRcmTsshQtmDto>> selectCcptVdListByTc(AiRcmTsshQtmDto dto) {
    	Map<String, List<AiRcmTsshQtmDto>> resMap = new HashMap<>();
    	dto.setSbjCd("EN");

		AiRcmTsshQtmDto txbInfo = commonDao.select(MAPPER_NAMESPACE + "selectTxbInfo", dto);
		dto.setSchlGrdCd(txbInfo.getSchlGrdCd());

    	List<AiRcmTsshQtmDto> ccptVdList = selectCcptVdList(dto);
    	resMap.put("allList", ccptVdList);
    	
    	for (AiRcmTsshQtmDto ccpt : ccptVdList) {
    		List<AiRcmTsshQtmDto> list = resMap.get(ccpt.getTcKmmpNodNm());
			if(list == null) {
				list = new ArrayList<>();
			}
			list.add(ccpt);
			resMap.put(ccpt.getTcKmmpNodNm(), list);
		}
    	
    	return resMap; 
    }
	
	
	/**
	 * 학습맵ID -> 지식맵ID로 치환
	 * 
	 *  @param String mluLrmpNodId 중단원학습맵노드ID
	 *  @return String mluKmmpNodId 중단원지식맵노드ID
	 * */
	public String convertLrmpIdToKmmpId(String optTxbId, String mluLrmpNodId) {
		log.debug("Convert LrmpNodId to KmmpNodId....");
		AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
		dto.setOptTxbId(optTxbId);
		dto.setMluLrmpNodId(mluLrmpNodId);
		return commonDao.select(MAPPER_NAMESPACE + "selectMluLrmpNodId", dto);
	}
	
	//AI_QTMLY_XPL_STAS 문항통계
	@Transactional
	public void insertAiQtmlyXplStas(List<AiRcmTsshQtmDto> list) {
		for (AiRcmTsshQtmDto dto : list) {
			dto.setDelYn("N");
			dto.setAvgCansRt(dto.getCansYn().equals("Y") ? 1.0 : 0.0);
			dto.setDbId("DB_ID_AI");
			commonDao.insert(MAPPER_NAMESPACE + "insertAiQtmlyXplStas", dto);
		}
	}
	
	
	/**
	 * 학습자수준 판단
	 * 형성평가, 총괄평가 완료시 난이도별 가중치를 포함하여 중단원 기준 학습자 수준을 판단하여 저장한다.
	 * 
	 *  @param String evDtlDvCd 평가구분상세코드
	 *  @param String mluLrmpNodId 중단원 학습맵 노드ID
	 *  @param String mluKmmpNodId 중단원 지식맵 노드ID
	 *  @param String tcKmmpNodId 차시 지식맵 노드ID
	 *  @param String sbjCd 과목코드
	 *  @param String usrId 사용자ID
	 * */
	@Transactional
	public void updateLrnrVelTpCd(AiRcmTsshQtmDto req) {
		if(req.getEvDtlDvCd() != null) {
			//형성평가, 총괄평가 평가완료시에만 학습자수준 update
			if(req.getEvDtlDvCd().equals("FO") || req.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
				AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
				String lrnrVelTpCd = "NM";
				
				//수학 - 중단원기준 저장
				if(AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
					dto = commonDao.select(MAPPER_NAMESPACE + "selectMluAvgScr", req);
					
					//형성평가 하
					int frLlCnt = dto.getFrLlCnt() *2;
					int frLlYCnt = dto.getFrLlYCnt() *2;
					//형성평가 중
					int frMmCnt = dto.getFrMmCnt() *3;
					int frMmYCnt = dto.getFrMmYCnt() *3;
					//총괄평가 중
					int ugMmCnt = dto.getUgMmCnt() *5;
					int ugMmYCnt = dto.getUgMmYCnt() *5;
					
					int qtmCnt = frLlCnt + frMmCnt + ugMmCnt;
					int qtmCansYCnt = frLlYCnt + frMmYCnt + ugMmYCnt;
					double mluAvgScr = 0;
					
					//학습자 수준 판단
					if(qtmCnt == 0 && qtmCansYCnt == 0) {
						lrnrVelTpCd = "NM";
					}else {
						mluAvgScr = ((double)qtmCansYCnt/qtmCnt) * 100;
						log.debug("mluAvgScr: " + mluAvgScr);
						
						if(mluAvgScr >= 80) {
							lrnrVelTpCd = "FS";
						}
						else if(60 <= mluAvgScr && mluAvgScr < 80) {
							lrnrVelTpCd = "NM";
						}
						else if(mluAvgScr < 60) {
							lrnrVelTpCd = "SL";
						}
					}
					dto.setOptTxbId(req.getOptTxbId());
					dto.setMluKmmpNodId(req.getMluKmmpNodId());
					dto.setLrnrVelTpCd(lrnrVelTpCd);
					dto.setUsrId(req.getUsrId());
					dto.setDbId("DB_ID_AI");
					this.updateStuLrnrVelTpCd(dto);
					
				}//수학 END
				
				//영어 - 영역(차시)별 저장
				else if(AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
					List<AiRcmTsshQtmDto> tcList = commonDao.selectList(MAPPER_NAMESPACE + "selectEnTcCansYCntList", req);
					for (AiRcmTsshQtmDto tc : tcList) {
						int tcCansY = tc.getCansCnt();

						//진단평가 예외 단원에 대한 학습자 수준 처리 추가(2024.09.09)
						if(AlConstUtil.OV_EXCEPTION_KMMP_NOD_ID_STR.contains(tc.getTcKmmpNodId())) {
							if(tcCansY >= AlConstUtil.EN_EXCEPTION_LRNR_VEL_FS) {
								lrnrVelTpCd = "FS";
							}
							else if(tcCansY >= AlConstUtil.EN_EXCEPTION_LRNR_VEL_NM && tcCansY < AlConstUtil.EN_EXCEPTION_LRNR_VEL_FS) {
								lrnrVelTpCd = "NM";
							}
							else if(tcCansY >= AlConstUtil.EN_EXCEPTION_LRNR_VEL_SL && tcCansY < AlConstUtil.EN_EXCEPTION_LRNR_VEL_NM) {
								lrnrVelTpCd = "SL";
							}
						} else if(AlConstUtil.OV_EXCEPTION2_KMMP_NOD_ID_STR.contains(tc.getTcKmmpNodId())) {
							if(tcCansY == AlConstUtil.EN_EXCEPTION2_LRNR_VEL_FS) {
								lrnrVelTpCd = "FS";
							}
							else if(tcCansY >= AlConstUtil.EN_EXCEPTION2_LRNR_VEL_NM && tcCansY < AlConstUtil.EN_EXCEPTION2_LRNR_VEL_FS) {
								lrnrVelTpCd = "NM";
							}
							else if(tcCansY >= AlConstUtil.EN_EXCEPTION2_LRNR_VEL_SL && tcCansY < AlConstUtil.EN_EXCEPTION2_LRNR_VEL_NM) {
								lrnrVelTpCd = "SL";
							}
						} else if(AlConstUtil.OV_EXCEPTION3_KMMP_NOD_ID_STR.contains(tc.getTcKmmpNodId())) {
							if(tcCansY == AlConstUtil.EN_EXCEPTION3_LRNR_VEL_FS) {
								lrnrVelTpCd = "FS";
							}
							else if(tcCansY >= AlConstUtil.EN_EXCEPTION3_LRNR_VEL_NM && tcCansY < AlConstUtil.EN_EXCEPTION3_LRNR_VEL_FS) {
								lrnrVelTpCd = "NM";
							}
							else if(tcCansY >= AlConstUtil.EN_EXCEPTION3_LRNR_VEL_SL && tcCansY < AlConstUtil.EN_EXCEPTION3_LRNR_VEL_NM) {
								lrnrVelTpCd = "SL";
							}
						} else {
							if(tcCansY == AlConstUtil.EN_LRNR_VEL_FS) {
								lrnrVelTpCd = "FS";
							}
							else if(tcCansY == AlConstUtil.EN_LRNR_VEL_NM) {
								lrnrVelTpCd = "NM";
							}
							else if(tcCansY == AlConstUtil.EN_LRNR_VEL_SL) {
								lrnrVelTpCd = "SL";
							}
						}


						dto.setOptTxbId(req.getOptTxbId());
						dto.setMluKmmpNodId(tc.getTcKmmpNodId());
						dto.setLrnrVelTpCd(lrnrVelTpCd);
						dto.setUsrId(req.getUsrId());
						dto.setDbId("DB_ID_AI");
						this.updateStuLrnrVelTpCd(dto);
					}
				}//영어 END
				
			}
		}
	}
	
	public int updateStuLrnrVelTpCd(AiRcmTsshQtmDto dto) {
		return commonDao.insert(MAPPER_NAMESPACE + "updateLrnrVelTpCd", dto);
	}
	
	
	/**
	 * 토픽숙련도 판단
	 * 
	 *  @param String sbjCd 과목ID
	 *  @param String usrId 사용자ID
	 * */
	@Transactional
	public void setTpcAvn(String sbjCd, String usrId, String evId, String mluKmmpNodId, String optTxbId, String evDtlDvCd) {
		//토픽숙련도 계산
		AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
		dto.setUsrId(usrId);
		dto.setEvDvCd("AE");
		dto.setEvDtlDvCd(evDtlDvCd);
		dto.setEvId(Integer.parseInt(evId));
		dto.setOptTxbId(optTxbId);
		List<AiRcmTsshQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectQtmCansYnByTpc", dto);
		
		//토픽별 리스트 그룹
		Map<String, List<AiRcmTsshQtmDto>> tpcByListMap = list.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId));
		for (String key : tpcByListMap.keySet()) {
			List<AiRcmTsshQtmDto> tpcByQtmList = tpcByListMap.get(key);
			
			Double tpcWgtNmvl = 0.0;
			int listCnt = 0;
			for (AiRcmTsshQtmDto tpcQtm : tpcByQtmList) {
				listCnt++;
				if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("01")) { tpcWgtNmvl += 0.7; }//최하
				if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("02")) { tpcWgtNmvl += 0.85; }//하
				if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("03")) { tpcWgtNmvl += 1.0; }//중
				if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("04")) { tpcWgtNmvl += 1.15; }//상
				if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("05")) { tpcWgtNmvl += 1.3; }//최상
				else { tpcWgtNmvl += 0.0; }
			}
			//토픽-문항 난이도별 실제점수 가중치
			tpcWgtNmvl = tpcWgtNmvl/listCnt;
			//토픽별 실제점수 :: 문항풀이이력이 없어도 초기등록시 0.5로 등록되어 있다.
			Double aiPredAvgScr = tpcByQtmList.get(0).getAiPredAvgScr();
			
			//토픽별 AI 예측점수
			Double aiPredAvgCansRt = tpcByQtmList.get(0).getAiPredAvgCansRt();
			//토픽별 문항풀이 횟수
			Integer txmPn = tpcByQtmList.get(0).getTxmPn();
			//토픽점수
			Double tpcAvn = 0.5;
			
			//토픽별 문항풀이가 없는경우 AI예측점수로 판단
			if(txmPn <= 1 && aiPredAvgScr == 0.5) {
				tpcAvn = aiPredAvgCansRt;
			}
			//토픽별 문항풀이 이력이 1건인 경우 실제점수7 : 예측점수3
			else if(txmPn == 1) {
				tpcAvn = ((tpcWgtNmvl * 7) + (aiPredAvgCansRt * 3)) / 10;
			}
			//실제점수(가중치)로 판단
			else {
				tpcAvn = tpcWgtNmvl;
			}
			dto.setTpcAvn(tpcAvn);
			
			dto.setTpcKmmpNodId(key);
			commonDao.update(MAPPER_NAMESPACE + "updateTpcAvn", dto);
		}
		
		
//		if(AlConstUtil.SBJ_MA.contains(sbjCd)) {
//			int qtmCnt = 0;
//			int qtmCansYCnt = 0;
//			for (AiRcmTsshQtmDto ev : evList) {
//				qtmCnt++;
//				if(ev.getCansYn().equals("Y")) {
//					qtmCansYCnt++;
//				}
//			}
//			
//			//단원 내 모든토픽리스트 순서대로 조회
//			dto.setOptTxbId(optTxbId);
//			dto.setMluKmmpNodId(mluKmmpNodId);
//			dto.setSbjCd(sbjCd);
//			List<AiRcmTsshQtmDto> selectTpcAvnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcAvnList", dto);
//			if(!selectTpcAvnList.isEmpty()) {
//				
//			}
//			for (AiRcmTsshQtmDto tpc : selectTpcAvnList) {
//				dto.setTpcKmmpNodId(tpc.getTpcKmmpNodId());
//				
//				//진단평가 다틀린경우
//				if(qtmCansYCnt == 0) {
//					if(tpc.getTpcAvn() >= AlConstUtil.TPC_AVN_03) {
//						//단원 내 강점토픽이 없도록 토픽숙련도 조정
//						dto.setTpcAvn(0.79);
//						commonDao.update(MAPPER_NAMESPACE + "updateTpcAvn", dto);
//					}
//				}
//				//진단평가 다틀린경우
//				if(qtmCnt == qtmCansYCnt && qtmCnt > 0) {
//					if(tpc.getTpcAvn() < AlConstUtil.TPC_AVN_03) {
//						//단원 내 모든 토픽이 강점으로 세팅되도록 토픽숙련도 조정
//						dto.setTpcAvn(AlConstUtil.TPC_AVN_03);
//						commonDao.update(MAPPER_NAMESPACE + "updateTpcAvn", dto);
//					}					
//				}
//				
//			}
//			
//		}
		
//		if(!selectTpcAvnList.isEmpty() && selectTpcAvnList.get(0).getLrnrVelTpCd().equals("FS")) {
//			Collections.reverse(selectTpcAvnList);
//			
//			Double tpcAvn = null;
//			for (AiRcmTsshQtmDto avn : selectTpcAvnList) {
//				if(avn.getTpcAvn() != null) {
//					tpcAvn = avn.getTpcAvn();
//					break;
//				}
//			}
//			if(tpcAvn != null) {
//				for (AiRcmTsshQtmDto avn : selectTpcAvnList) {
//					if(avn.getTpcAvn() == null) {
//						dto.setTpcAvn(tpcAvn);
//						commonDao.update(MAPPER_NAMESPACE + "updateTpcAvnIsNull", dto);
//					}else {
//						tpcAvn = dto.getTpcAvn();
//					}
//				}
//			}
//		}
		
	}
	
	
	/**
	 * 평가생성
	 * */
	@Transactional
	public Integer insertEaEvHst(AiRcmTsshQtmReqDto dto, List<AiRcmTsshQtmDto> qtmList) {
		Integer evID = null;
		
		if(!qtmList.isEmpty()) {
			AiRcmTsshQtmHistDto evHst = new AiRcmTsshQtmHistDto();
			evHst.setOptTxbId(dto.getOptTxbId());
			evHst.setUsrId(dto.getUsrId());
			evHst.setEvDvCd(dto.getEvDvCd());//평가구분코드
			evHst.setEvDtlDvCd(dto.getEvDtlDvCd());//평가상세구분코드
			evHst.setEvNm(dto.getEvNm());
			evHst.setTxmStrDtm(dto.getTxmStrDtm());
			evHst.setTxmEndDtm(dto.getTxmEndDtm());
			evHst.setQstCnt(dto.getQstCnt() == null ? qtmList.size() : dto.getQstCnt());//문제수
			evHst.setLcknYn("N");//잠금여부
			evHst.setRtxmPmsnYn("N");//재응시허용여부
			evHst.setTxmPtmeSetmYn("N");//응시기간설정여부
			evHst.setXplTmSetmYn(dto.getXplTmSetmYn() == null ? "N" : dto.getXplTmSetmYn());//풀이시간설정여부
			evHst.setXplTmScnt(dto.getXplTmScnt() == null? 600 : dto.getXplTmScnt());//풀이시간초수
			evHst.setUseYn("Y");
			evHst.setDelYn("N");
			evHst.setDbId("DB_ID_AI");//DB_ID
			
			//평가지 고정문항일 경우 평가지ID, 평가지코드 저장
			evHst.setEvshId(qtmList.get(0).getEvshId() == null ? null : qtmList.get(0).getEvshId());
			evHst.setEvshCd(qtmList.get(0).getEvshCd() == null ? null : qtmList.get(0).getEvshCd());
			evHst.setEvshDvCd(qtmList.get(0).getEvshDvCd() == null ? null : qtmList.get(0).getEvshDvCd());
			
			//EA_EV 평가 등록
			commonDao.insert(MAPPER_NAMESPACE + "insertEaEv", evHst);
			evID = evHst.getEvId();
			
			//평가결과테이블 기본값 등록
			evHst.setEvId(evID);
			commonDao.insert(MAPPER_NAMESPACE + "insertEaEvRs", evHst);
			
			//AI_RCM_TSSH_HST AI_추천시험지이력 등록
			evHst.setAiDgnRcmMthdCd("AI");
			evHst.setAiDgnRcmBsCd("AI");
			commonDao.insert(MAPPER_NAMESPACE + "insertAiRcmTsshHst", evHst);
			
			//문항List
			int listCnt = 0;
			List<String> rngeTpcStr = new ArrayList<>();
			for (AiRcmTsshQtmDto qtm : qtmList) {
				listCnt++;
				
				evHst.setLluKmmpNodId(qtm.getLluKmmpNodId());
				evHst.setMluKmmpNodId(qtm.getMluKmmpNodId() == null ? dto.getMluKmmpNodId() : qtm.getMluKmmpNodId());
				evHst.setTpcKmmpNodId(qtm.getTpcKmmpNodId() == null ? dto.getTpcKmmpNodId() : qtm.getTpcKmmpNodId());
				
				//tcKmmpNodId가 null 이거나 ""일 때 처리
				//evHst(qtm.getTcKmmpNodId() == null ? dto.getTcKmmpNodId() : qtm.getTcKmmpNodId());
				if(qtm.getTcKmmpNodId() == null || "".equals(qtm.getTcKmmpNodId())) {
					if(dto.getTcKmmpNodId() == null || "".equals(dto.getTcKmmpNodId())) {
						String tcKmmpNodId = commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodId", evHst);
						tcKmmpNodId = tcKmmpNodId == null ? "" : tcKmmpNodId;
						evHst.setTcKmmpNodId(tcKmmpNodId);
					} else {
						evHst.setTcKmmpNodId(dto.getTcKmmpNodId());
					}
				} else {
					evHst.setTcKmmpNodId(qtm.getTcKmmpNodId());
				};
				
				//EA_EV_TS_RNGE 평가시험범위 등록
				if(!rngeTpcStr.contains(qtm.getTpcKmmpNodId())) {
					//지식맵 범위 등록
					if(dto.getEvDvCd().equals("DE")) {
						if(listCnt == 1) {
							commonDao.insert(MAPPER_NAMESPACE + "insertEaAiEvTsRnge", evHst);
						}
					}else {
						commonDao.insert(MAPPER_NAMESPACE + "insertEaAiEvTsRnge", evHst);
					}
					rngeTpcStr.add(qtm.getTpcKmmpNodId());
				}
				
				//EA_EV_QTM 평가문항 등록
				evHst.setQtmId(qtm.getQtmId());
				evHst.setQtmOrdn(listCnt);
				evHst.setQtmDffdDvCd(qtm.getCtnDffdDvCd());
				evHst.setQpDffdNm(qtm.getCtnDffdDvNm());
				log.debug("ㅠㅠㅠ + " + evHst.toString());
				commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtm", evHst);
				
				//AI_RCM_TSSH_QTM_HST AI_추천시험지문항이력 등록
				evHst.setLrnrVelTpCd(dto.getLrnrVelTpCd());
				evHst.setCtnDffdDvCd(qtm.getCtnDffdDvCd());
				commonDao.insert(MAPPER_NAMESPACE + "insertAiRcmTsshQtmHst", evHst);
				
			}
			//학습맵 범위 등록
			commonDao.insert(MAPPER_NAMESPACE + "insertEaAiToEvTsRnge", evHst);
		}
		log.debug("log info■■■■■■■■■■■■■■■■■■■■■■■■■■■");
		log.debug("evID : " + evID);
		for (AiRcmTsshQtmDto qtm : qtmList) {
			log.debug(qtm.getRcmRnk() + " : " + qtm);
		}
		log.debug("log info■■■■■■■■■■■■■■■■■■■■■■■■■■■");
		
		return evID;
	}
	
	/**
	 * 평가에 문항추가
	 * */
	@Transactional
	public void insertEaEvQtm(AiRcmTsshQtmHistDto dto) {
		dto.setDbId("insertEaEvQtm");
		commonDao.insert(MAPPER_NAMESPACE + "insertEaEvQtm", dto);
	}
	
	
	/**
     * @param list 중복이 있는 list
     * @param key  중복 여부를 판단하는 키값
     * @param <T>  generic type
     * @return list
     * @desc list<Object> 특정 값 기준 중복 제거
     */
    public static <T> List<T> deduplication(final ArrayList<T> list, Function<? super T, ?> key) {
    	return list.stream()
        	.filter(deduplication(key))
            .collect(Collectors.toList());
    }
    private static <T> Predicate<T> deduplication(Function<? super T, ?> key) {
    	final Set<Object> set = ConcurrentHashMap.newKeySet();
        return predicate -> set.add(key.apply(predicate));
    }
    
    /**
     * List에서 특정 컬럼 max값의 dto를 반환
     * @param List<AiRcmTsshQtmDto>
     * @param Double 
     * @return AiRcmTsshQtmDto
     * */
    public AiRcmTsshQtmDto findMaxValueDto(List<AiRcmTsshQtmDto> list, Function<AiRcmTsshQtmDto, Double> mapper) {
        Double max = null;
        AiRcmTsshQtmDto maxDto = null;
        for (AiRcmTsshQtmDto dto : list) {
            Double value = mapper.apply(dto);
            if (value != null) {
                if (max == null || value > max) {
                    max = value;
                    maxDto = dto;
                }
            }
        }
        return maxDto;
    }
    
    /**
     * List에서 특정 컬럼 min값의 dto를 반환
     * @param List<AiRcmTsshQtmDto>
     * @param Double 
     * @return AiRcmTsshQtmDto
     * */
    public AiRcmTsshQtmDto findMinValueDto(List<AiRcmTsshQtmDto> list, Function<AiRcmTsshQtmDto, Double> mapper) {
        Double min = null;
        AiRcmTsshQtmDto minDto = null;
        for (AiRcmTsshQtmDto dto : list) {
            Double value = mapper.apply(dto);
            if (value != null) {
                if (min == null || value < min) {
                    min = value;
                    minDto = dto;
                }
            }
        }
        return minDto;
    }
    
    /**
     * 지식맵 노드명 조회
     * */
    public String selectKmmpNodNm(String kmmpNodId) {
    	return commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodNm", kmmpNodId);
    }

	/**
	 * 학습진도율 update
	 * */
	@Transactional
	public void updateLrnPrgsProf(AlPlQtmTpcProfDto req) {
		double aiLrnPgrsRt = 0.0;
		AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
		dto.setUsrId(req.getUsrId());
		dto.setMluKmmpNodId(req.getMluKmmpNodId());
		dto.setOptTxbId(req.getOptTxbId());
		dto.setSchlGrdCd(req.getSchlGrdCd());
		dto.setSbjCd(req.getSbjCd());

		// 학습해야 할 차시 또는 토픽의 개수를 위한 리스트 선언
		List<AiRcmTsshQtmDto> lrnList = new ArrayList<>();

		// 진단평가 데이터가 있을 경우 학습진도율 10 부여
		if (AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
			lrnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEnLrnrVelTpCd", dto);
		} else if (AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
			lrnList = commonDao.selectList(MAPPER_NAMESPACE + "selectMaLuevCmplYn", dto);
		}

		if (lrnList.size() > 0) {
			aiLrnPgrsRt += 10.0;
			// 학습완료된 차시 또는 토픽 정보를 담기 위한 리스트 선언
			List<AiRcmTsshQtmDto> evCmplList;
			long evCmplYCnt = 0L;
			BigDecimal aiLrnPgrsRtNotOvDec = null;
			List<String> tpcKmmpNodIdList = new ArrayList<>();
			// 맞춤학습 학습진도율 부여
			if (AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
				long totalEvCmplYCnt = 0L;
				// 영어의 경우 전체 차시 개수를 돌면서 학습완료 단계(C2,C3)에 맞게 학습완료 여부를 조회해서 완료된 차시의 개수를 가져온다
				for (AiRcmTsshQtmDto tcDto : lrnList) {
					dto.setTcKmmpNodId(tcDto.getTcKmmpNodId());
					evCmplList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvCmplList", dto);

					//단원완료기준(초등영어도 맞춤학습2까지만 진행하는 것으로 변경되어 수정)
					String finalLuevCmplBs = "C2";
					evCmplYCnt = evCmplList.stream()
							.filter(evCmplDto -> "Y".equals(evCmplDto.getLuevCmplYn()) && finalLuevCmplBs.equals(evCmplDto.getEvDtlDvCd()))
							.count();
					totalEvCmplYCnt += evCmplYCnt;
				}
				aiLrnPgrsRtNotOvDec = BigDecimal.valueOf(totalEvCmplYCnt)
						.divide(BigDecimal.valueOf(lrnList.size()), 10, RoundingMode.HALF_UP)
						.multiply(BigDecimal.valueOf(0.9))
						.multiply(BigDecimal.valueOf(100))
						.setScale(1, RoundingMode.HALF_UP);
			} else if (AlConstUtil.SBJ_MA.contains(req.getSbjCd())) {
				for (AiRcmTsshQtmDto tpcDto : lrnList) {
					tpcKmmpNodIdList.add(tpcDto.getTpcKmmpNodId());
				}
				dto.setKmmpNodIdList(tpcKmmpNodIdList);
				evCmplList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvCmplList", dto);
				// 수학의 경우 전체 토픽 개수 대비 학습완료된 토픽의 개수를 가져온다
				evCmplYCnt = evCmplList.stream()
						.filter(evCmplDto -> "Y".equals(evCmplDto.getLuevCmplYn()))
						.count();

				aiLrnPgrsRtNotOvDec = BigDecimal.valueOf(evCmplYCnt)
						.divide(BigDecimal.valueOf(lrnList.size()), 10, RoundingMode.HALF_UP)
						.multiply(BigDecimal.valueOf(0.9))
						.multiply(BigDecimal.valueOf(100))
						.setScale(1, RoundingMode.HALF_UP);
			}

			double aiLrnPgrsRtNotOv = aiLrnPgrsRtNotOvDec.doubleValue();
			aiLrnPgrsRt += aiLrnPgrsRtNotOv;
		}
		dto.setAiLrnPgrsRt(aiLrnPgrsRt);
		dto.setDbId("DB_ID_AI");
		commonDao.update(MAPPER_NAMESPACE + "updateLrnPgrsProf", dto);
	}

	public List<AlPlQtmTpcProfDto> selectEvTarget() {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEvTarget");
	}
    
	
}
