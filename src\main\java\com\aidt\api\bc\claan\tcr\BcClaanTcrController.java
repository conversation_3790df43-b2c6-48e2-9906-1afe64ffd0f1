package com.aidt.api.bc.claan.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.claan.dto.BcClaanReqDto;
import com.aidt.api.bc.claan.dto.BcClaanResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-07-08 10:53:00
 * @modify 2024-07-08 10:53:00
 * @desc 학급분석 컨트롤러
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/claan/tcr")
@Tag(name="[bc] 학급분석", description="학급분석-선생님")
public class BcClaanTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private BcClaanTcrService bcClaanTcrService;
	
	/**
     * 학급분석 : 학습요약
     * @param AlWrtReqDto - 운영교과서ID, 날짜검색타입, 검색날짜
     * @return ResponseDto<List<AlWrtResDto>> - 해당 학급의 학습요약
     */
    @Operation(summary="학급분석 : 학습요약")
    @PostMapping(value = "/selectLrnSmy")
    public ResponseDto<BcClaanResDto> selectLrnSmy(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	dto.setClaId(userDetails.getClaId());
    	if (dto == null || StringUtils.isBlank(dto.getOptTxbId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(bcClaanTcrService.selectLrnSmy(dto));
    }
    
    /**
     * 학급분석 : 단원별 성취 현황
     * @param AlWrtReqDto - 운영교과서ID
     * @return ResponseDto<List<AlWrtResDto>> - 해당 학급의 단원별 성취 현황
     */
    @Operation(summary="학급분석 : 단원별 성취 현황")
    @PostMapping(value = "/selectLuAchdPst")
    public ResponseDto<List<BcClaanResDto>> selectLuAchdPst(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	if (dto == null 
    			|| StringUtils.isBlank(dto.getOptTxbId())
    			|| StringUtils.isBlank(dto.getSchlCd())
    			|| StringUtils.isBlank(dto.getSgyCd())
    			|| StringUtils.isBlank(dto.getSchlGrdCd())
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(bcClaanTcrService.selectLuAchdPst(dto));
    }
    
    /**
     * 단원별 상세 현황 : 평가별 분석 탭 목록
     * @param AlWrtReqDto - 운영교과서ID, 대단원노드ID
     * @return ResponseDto<List<AlWrtResDto>> - 해당 학급의 평가별 분석 탭 목록
     */
    @Operation(summary="단원별 상세 현황 : 평가별 분석 탭 목록")
    @PostMapping(value = "/selectEvAnTabList")
    public ResponseDto<List<BcClaanResDto>> selectEvAnTabList(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	if (dto == null || StringUtils.isBlank(dto.getOptTxbId()) || StringUtils.isBlank(dto.getLluNodId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(bcClaanTcrService.selectEvAnTabList(dto));
    }
    
    /**
     * 단원별 상세 현황 : 차시별 분석 탭 목록
     * @param AlWrtReqDto - 운영교과서ID, 대단원노드ID
     * @return ResponseDto<List<AlWrtResDto>> - 해당 학급의 차시별 분석 탭 목록
     */
    @Operation(summary="단원별 상세 현황 : 차시별 분석 탭 목록")
    @PostMapping(value = "/selectTcAnTabList")
    public ResponseDto<List<BcClaanResDto>> selectTcAnTabList(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	if (dto == null || StringUtils.isBlank(dto.getOptTxbId()) || StringUtils.isBlank(dto.getLluNodId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(bcClaanTcrService.selectTcAnTabList(dto));
    }
    
    /**
     * 단원별 상세 현황 : 성취기준별 분석
     * @param AlWrtReqDto - 운영교과서ID, 대단원노드ID
     * @return ResponseDto<List<AlWrtResDto>> - 해당 학급의 성취기준별 분석 탭 목록
     */
    @Operation(summary="단원별 상세 현황 : 성취기준별 분석")
    @PostMapping(value = "/selectAchBsAnTabList")
    public ResponseDto<List<BcClaanResDto>> selectAchBsAnTabList(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	if (dto == null || StringUtils.isBlank(dto.getOptTxbId()) || StringUtils.isBlank(dto.getLluNodId())) {
    		return Response.fail("Params does not exist");
    	}
    	
        return Response.ok(bcClaanTcrService.selectAchBsAnTabList(dto));
    }
    /**
     * 단원별 상세 현황 : 영역별 분석
     * @param AlWrtReqDto - 운영교과서ID, 대단원노드ID
     * @return ResponseDto<List<AlWrtResDto>> - 해당 학급의 성취기준별 분석 탭 목록
     */
    @Operation(summary="단원별 상세 현황 : 영역별 분석")
    @PostMapping(value = "/selectPartAnTabList")
    public ResponseDto<List<BcClaanResDto>> selectPartAnTabList(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	if (dto == null || StringUtils.isBlank(dto.getOptTxbId()) || StringUtils.isBlank(dto.getLluNodId())) {
    		return Response.fail("Params does not exist");
    	}
    	
    	return Response.ok(bcClaanTcrService.selectPartAnTabList(dto));
    }
    
    /**
     * 내용체계영역별 성취 현황
     * @param AlWrtReqDto - 운영교과서ID, 학교코드, 학년코드, 학교급코드
     * @return ResponseDto<List<AlWrtResDto>> - 내용체계영역별 성취 현황
     */
    @Operation(summary="내용체계영역별 성취 현황")
    @PostMapping(value = "/selectCrsCnAchPstList")
    public ResponseDto<List<BcClaanResDto>> selectCrsCnAchPstList(@Valid @RequestBody BcClaanReqDto dto) {
    	
//    	if (dto == null 
//    			|| StringUtils.isBlank(dto.getOptTxbId())
//    			|| StringUtils.isBlank(dto.getSchlCd())
//    			|| StringUtils.isBlank(dto.getSgyCd())
//    			|| StringUtils.isBlank(dto.getSchlGrdCd())
//    			|| StringUtils.isBlank(dto.getSbjCd())
//    	) {
//    		return Response.fail("Params does not exist");
//    	}
    	
        return Response.ok(bcClaanTcrService.selectCrsCnAchPstList(dto));
    }
    
    
    
    /**
     * 학급분석 : 나의 학습 패턴 - 선호도, 학습코칭, 요일별분석, 시간대별분석 조회
     * @param param
     * @return
     */
    @Operation(summary="학습 리포트 - 선호도 조회", description="선생 학습 리포트 조회")
    @PostMapping(value="/selectLrnPtrnInfo")
    public ResponseDto<Map<String,Object>> selectEaLrnPrefInfo(@RequestBody BcClaanReqDto dto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	dto.setOptTxbId(userDetails.getOptTxbId());
    	dto.setUsrId(userDetails.getUsrId());
    	return Response.ok(bcClaanTcrService.selectEaLrnPrefInfo(dto));
    }

}
