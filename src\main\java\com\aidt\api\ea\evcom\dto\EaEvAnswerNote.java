package com.aidt.api.ea.evcom.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EaEvAnswerNote {

	private Integer evId;
	private Long qtmId;        //문항 아아디
	private Long annxFleId;    //노트 아이디
	private Integer txmPn;
	private String usrId;
	private String dbId;

	public static EaEvAnswerNote of(EaEvAnswerNoteReqDto reqDto, EaEvResult eaEvResult) {
		if(eaEvResult.isCompleted()) {
			eaEvResult.incrementReexaminationRound();
		}
		return EaEvAnswerNote.builder()
			.evId(eaEvResult.getEvId())
			.qtmId(reqDto.getQtmId())
			.annxFleId(reqDto.getAnnxFleId())
			.txmPn(eaEvResult.getTxmPn())
			.usrId(eaEvResult.getUsrId())
			.dbId(eaEvResult.getDbId())
			.build();
	}
}
