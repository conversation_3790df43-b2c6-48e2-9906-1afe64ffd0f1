package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSbcLrnAtvStaDto {
	/** 학습맵노드ID */
    @Parameter(name="학습맵노드ID")
    private String lrmpNodId;

    /** 학습활동ID */
    @Parameter(name="학습활동ID")
    private String lrnAtvId;

    /** 콘텐츠코드 */
    @Parameter(name="콘텐츠코드")
    private String ctnCd;

    /** 학습활동명 */
    @Parameter(name="학습활동명")
    private String lrnAtvNm;

    /** 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
    @Parameter(name="콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지)")
    private String ctnTpCd;

    /** 학습상태코드 */
    @Parameter(name="학습상태코드")
    private String lrnStCd;

    /** 학습상태명 */
    @Parameter(name="학습상태명")
    private String lrnStNm;

    /** 학습시간(초단위) */
    @Parameter(name="학습시간(초단위)")
    private int lrnTmScnt;

    /** 학습일자 */
    @Parameter(name="학습일자")
    private String lrnDt;
}
