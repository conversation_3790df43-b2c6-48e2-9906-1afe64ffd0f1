package com.aidt.api.sl.splrn.dto;


import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-01-10 10:45:26
 * @modify : date 2024-01-10 10:45:26
 * @desc : 특별학습 재구성
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor


public class SlSpLrnRestructViewDto {
	
	/* 재구성 */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    @Parameter(name="특별학습ID")
    private String spLrnId;

    @Parameter(name="특별학습이름")
    private String spLrnNm;

    @Parameter(name="학습목표")
    private String lrnGoalCn;

    @Parameter(name="사용여부")
    private String useYn;

    @Parameter(name="재구성순서")
    private int rcstnOrdn;

    @Parameter(name="cdn경로")
    private int cdnPthNm;
}