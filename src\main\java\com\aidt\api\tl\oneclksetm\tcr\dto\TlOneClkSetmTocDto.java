package com.aidt.api.tl.oneclksetm.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 16:18:45
 * @modify date 2024-02-15 16:18:45
 * @desc [TlOneClkSetmToc Dto 원클릭학습설정 학습목차 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlOneClkSetmTocDto {
        /** 운영교과서ID */
        @Parameter(name="운영교과서ID")
        private String optTxbId;
        /** LCMS교과서_지식맵_키 */
        @Parameter(name="LCMS교과서_지식맵_키")
        private String lrmpNodId;
        /** LCMS교과서_지식맵_부모_노드_키 */
        @Parameter(name="LCMS교과서_지식맵_부모_노드_키")
        private String urnkLrmpNodId;
        /** 지식맵노드명 */
        @Parameter(name="지식맵노드명")
        private String lrmpNodNm;
        /** 대단원ID */
        @Parameter(name="대단원ID")
        private String lluNodId;
        /** 원본순서 */
        @Parameter(name="원본순서")
        private int orglOrdn;
        /** 상위노드명(대단원 > 중단원 > 소단원) */
        @Parameter(name="상위노드명")
        private String upLuNm;
        /** 사용여부 */
        @Parameter(name="사용여부")
        private String useYn;
        /** 잠금여부 */
        @Parameter(name="잠금여부")
        private String lcknYn;
        /** 차시잠금여부(ai과제체크) */
        @Parameter(name="차시잠금여부(ai과제체크)")
        private String tcLcknYn;
        /** 단원번호사용여부 */
        @Parameter(name="단원번호사용여부")
        private String luNoUseYn;
        /** 단원노출여부 */
        @Parameter(name="단원노출여부")
        private String luEpsYn;
        /** 깊이 */
        @Parameter(name="깊이")
        private int dpth;
        
        /** 재구성순서 */
        @Parameter(name="재구성순서")
        private int rcstnOrdn;
        
        /**재구성노출번호*/
        @Parameter(name="재구성노출번호")
        private int rcstnNo;
        
        /**학습활동수*/
        @Parameter(name="학습활동수")
        private int atvCnt;
        
        /**변경학습활동수*/
        @Parameter(name="변경학습활동수")
        private int atvChnCnt;
        
        /** 원본학습시작일자 */
        @Parameter(name="원본학습시작일자")
        private String orglLrnStrDt;
        /** 학습시작일자 */
        @Parameter(name="학습시작일자")
        private String lrnStrDt;
        /** 과제유무 */
        @Parameter(name="과제유무")
        private String asnYn;
        /** 수정자ID */
        @Parameter(name="수정자ID")
        private String mdfrId;
}
