package com.aidt.api.bc.focLrn.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "집중학습정보")
public class BcFlDto {
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="집중학습정보")
	private String focLrnInfo;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="생성자ID")
	private String crtrId;
	
	@Parameter(name="생성일시")
	private String crtDtm;
	
	@Parameter(name="수정자ID")
	private String mdfrId;
	
	@Parameter(name="수정일시")
	private String mdfDtm;
	
	@Parameter(name="데이터베이스ID")
	private String dbId;
}
