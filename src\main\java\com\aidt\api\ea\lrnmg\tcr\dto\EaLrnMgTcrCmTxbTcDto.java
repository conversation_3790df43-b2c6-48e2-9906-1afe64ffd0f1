package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrCmTxbTcDto {
	
	/** ============ TL ================= **/
	/** 중단원ID*/
    @Parameter(name="중단원ID")
    private String tlLrmpNodId2;
    
    /** 중단원명*/
    @Parameter(name="중단원명")
    private String tlLrmpNodNm2;
    
    /** 소단원ID*/
    @Parameter(name="소단원ID")
    private String tlLrmpNodId3;
    
    /** 소단원명*/
    @Parameter(name="소단원명")
    private String tlLrmpNodNm3;
    
    /** 차시ID*/
    @Parameter(name="차시ID")
    private String tlLrmpNodId4;
    
    /** 차시명*/
    @Parameter(name="차시명")
    private String tlLrmpNodNm4;

    /** 재구성 순서 */
    @Parameter(name="재구성 순서")
    private int rcstnOrdn;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;
    
    /** ============ AI ================= **/
    @Parameter(name="중단원ID")
    private String aiKmmpNodId2;
    
    /** 중단원명*/
    @Parameter(name="중단원명")
    private String aiKmmpNodNm2;
    
    /** 소단원ID*/
    @Parameter(name="소단원ID")
    private String aiKmmpNodId3;
    
    /** 소단원명*/
    @Parameter(name="소단원명")
    private String aiKmmpNodNm3;
    
    /** 차시ID*/
    @Parameter(name="차시ID")
    private String aiKmmpNodId4;
     
    /** 차시명*/
    @Parameter(name="차시명")
    private String aiKmmpNodNm4;
}
