package com.aidt.api.tl.lsnmtrl.tcr;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlInfoDto;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:22:17
 * @modify date 2024-01-05 10:22:17
 * @desc TlLsnMtrlTcr Service 수업자료서비스(교사용)
 */
@Slf4j
@Tag(name="[tl] 교과학습 수업자료[TlLsnMtrlTcr]", description="교과학습 수업자료(교사용)")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/tcr/lsnmtrl")
public class TlLsnMtrlTcrController {
    
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlLsnMtrlTcrService tlLsnMtrlTcrService;
    
    /** DB-ID */
    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;

    /**
     * 교과학습 수업자료목록조회
     *
     * @param srhDto TlLsnMtrlTocSrhDto
     * @return ResponseList<TlStuLsnMtrlDto>
     */
    @Operation(summary="교과학습 수업자료목록 조회", description="우리반수업에서 수업자료목록(교사용)을 조회한다.")
    @PostMapping(value = "/selectLsnMtrlList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLsnMtrlInfoDto> selectLsnMtrlList(@Valid @RequestBody TlLsnMtrlTocSrhDto srhDto) {
        log.debug("Entrance selectLsnMtrlList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_TCR);
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setAllDatYn("Y");

        return Response.ok(tlLsnMtrlTcrService.selectLsnMtrlInfo(srhDto));
    }

    /**
     * 교과학습 수업자료 설정정보저장
     * 
     * @param lsnMtrlDto TlLsnMtrlDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교과학습 수업자료 설정정보저장", description="교과학습 LCMS기본자료의 설정정보를 저장한다.")
    @PostMapping(value = "/saveLsnMtrl", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> saveLsnMtrl(@Valid @RequestBody TlLsnMtrlDto lsnMtrlDto) {
        log.debug("Entrance saveLsnMtrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        lsnMtrlDto.setOptTxbId(userDetails.getOptTxbId());
        lsnMtrlDto.setMdfrId(userDetails.getUsrId());
        lsnMtrlDto.setDbId(DB_ID);

        return Response.ok(tlLsnMtrlTcrService.saveLsnMtrl(lsnMtrlDto));
    }

}
