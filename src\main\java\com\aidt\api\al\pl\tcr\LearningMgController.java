package com.aidt.api.al.pl.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.LearningMgDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 학습관리 - AI추천학습(선생님)
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/tcr/learningMg")
@Tag(name="[al] 학습현황관리(영어)", description="학습관리-AI추천학습 학습현황관리")
public class LearningMgController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private LearningMgService learningMgService;
	
	
	@Tag(name="[al] 학습현황관리 학습자수준 변경", description="선생님이 학습자 수준을 강제로 변경한다.")
    @PostMapping(value = "/updateStuAiLrnrLv")
    public ResponseDto<String> updateStuAiLrnrLv(@RequestBody @Valid LearningMgDto dto) {
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        if(dto.getClaId() == null) {
        	dto.setClaId(securityUserDetailDto.getClaId());
        }
    	return Response.ok(learningMgService.updateStuAiLrnrLv(dto));
    }
	
	@Tag(name="[al] 학습현황관리 AI학습 단원진도율", description="학습현황관리 AI학습 단원진도율")
    @PostMapping(value = "/selectStuAlProgRt")
    public ResponseDto<Map<String, Object>> selectStuAlProgRt(@RequestBody @Valid LearningMgDto dto) {
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        if(dto.getClaId() == null) {
        	dto.setClaId(securityUserDetailDto.getClaId());
        }
    	return Response.ok(learningMgService.selectStuAlProgRt(dto));
    }
	
	
	@Tag(name="[al] 학습현황관리 AI추천학습(영어)", description="선생님 학습현황관리 AI추천학습 페이지")
    @PostMapping(value = "/selectStuAeEvInfoList")
    public ResponseDto<Map<String, Object>> selectStuAeEvInfoList(@RequestBody @Valid LearningMgDto dto) {
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        if(dto.getClaId() == null) {
        	dto.setClaId(securityUserDetailDto.getClaId());
        }
    	return Response.ok(learningMgService.selectStuAeEvInfoList(dto));
    }
	
	
	@Tag(name="[al] 학습현황관리 AI추천학습(수학)", description="(선생님) 학습관리 - 학습현황관리 - AI 맞춤학습 (수학) :: 단원별 학생현황")
    @PostMapping(value = "/selectStuMaAeEvInfoList")
    public ResponseDto<Map<String, Object>> selectStuMaAeEvInfoList(@RequestBody @Valid LearningMgDto dto) {
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getClaId() == null) {
        	dto.setClaId(securityUserDetailDto.getClaId());
        }
    	return Response.ok(learningMgService.selectStuMaAeEvInfoList(dto));
    }
	
}
