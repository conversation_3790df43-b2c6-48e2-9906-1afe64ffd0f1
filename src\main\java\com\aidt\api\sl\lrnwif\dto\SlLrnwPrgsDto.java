package com.aidt.api.sl.lrnwif.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-19 14:59:26
 * @modify : date 2024-03-19 14:59:26
 * @desc : 특별학습 콘텐츠 활동조회- 완료 된 건수 조회
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwPrgsDto {
	@Parameter(name="특별학습ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnId;
	
	@Parameter(name="특별학습상위노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String urnkSpLrnNodId;
	
	@Parameter(name="특별학습상태", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnStCd;

	@Parameter(name="총 건수", required = true)
	@NotBlank(message = "{field.required}")
	private int entire;
	
	@Parameter(name="학습활동이 완료된 총 건수", required = true)
	@NotBlank(message = "{field.required}")
	private int done;
	
	@Parameter(name="상위 노드별 전체 완료 된 건수 - 완료 1 미완료 0표시", required = true)
	@NotBlank(message = "{field.required}")
	private int totalDone;
	
	@Parameter(name="마지막학습여부")
	private String lastLrnYn;
}
