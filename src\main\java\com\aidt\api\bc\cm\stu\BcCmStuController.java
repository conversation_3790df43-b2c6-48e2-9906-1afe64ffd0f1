package com.aidt.api.bc.cm.stu;

import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.cm.dto.BcUserInfoDto;
import com.aidt.api.bc.cm.dto.FlnDto;
import com.aidt.api.bc.cm.tcr.BcCmTcrService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:47
 * @modify 2024-01-05 17:52:47
 * @desc 공통 Controller
 */

//@Slf4j
@Tag(name="[bc] 공통[BcCmStu]", description="공통(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/common")
public class BcCmStuController {

    @Autowired
    private BcCmStuService bcCmStuService;
    
    @Autowired
    private BcCmTcrService bcCmTcrService;
    
    @Autowired
    private JwtProvider jwtProvider;

    /**
     * 로그인 성공 후 마이홈 포인트 API CALL
     *
     * @param
     * @return ResponseDto<UserDto>
     */
    @Operation(summary="마이홈 API CALL(로그인)", description="마이홈 API CALL(로그인)")
    @PostMapping(value = "/myhmApiCall")
    public ResponseDto<?> myhmApiCall(HttpServletRequest request) {
    	String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);
        //LocalDate currentDate = LocalDate.now();
    	LocalDate currentDate = LocalDate.now(ZoneId.of("Asia/Seoul"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = currentDate.format(formatter);
        Map<String, Object> apiResult = bcCmStuService.callMyhmApi(accessToken, Map.of(
                "pntCd", "LG_01",
                "pntChkBsVl", formattedDate));

        return Response.ok(Map.of("savedPoint", apiResult));
    }


    /**
     * 감정 상태 변경
     *
     * @param
     * @return ResponseDto<UserDto>
     */
    @Operation(summary="감정상태 변경", description="감정상태 변경")
    @PutMapping(value = "/updateFlnStCd")
    public int updateFlnStCd(@RequestBody FlnDto flnDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	flnDto.setMdfrId(userDetails.getUsrId());
    	flnDto.setUsrId(userDetails.getUsrId());
    	return bcCmStuService.updateFlnStCd(flnDto);
    }
    
    /**
     * 같은반 학생 목록
     *
     * @param BcUserInfoDto
     * @return ResponseList<List<BcUserInfoDto>>
     */
    @Operation(summary="같은반 학생 목록", description="같은반 학생 목록")
    @PostMapping("/sameClaStuList")
    public ResponseDto<List<BcUserInfoDto>> sameClaStuList(BcUserInfoDto bcUserInfoDto) {
        if (bcUserInfoDto == null || StringUtils.isBlank(bcUserInfoDto.getUsrId())) {
    		return Response.fail("Params does not exist.");
    	}
        return Response.ok(bcCmStuService.sameClaStuList(bcUserInfoDto));
    }
    
    /**
     * Third Party 사용 여부 조회
     *
     * @param String
     * @return ResponseList<Map<String, Object>>
     * @throws IOException 
     * @throws DatabindException 
     * @throws StreamReadException 
     */
    @Operation(summary="Third Party 사용 여부 조회", description="Third Party 사용 여부 조회.")
    @PostMapping(value = "/selectThirdPartyUseInfo")
    public ResponseDto<Map<String, Object>> selectThirdPartyUseInfo(@RequestBody Map<String, Object> params) throws StreamReadException, DatabindException, IOException {
        return Response.ok(bcCmTcrService.selectThirdPartyUseInfo(params.get("fileName").toString()));
    }


}
