package com.aidt.api.tl.sbclrn.tcr;

import java.util.List;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStChkDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumEvWkbDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnEaAsnSrhDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvSrhDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvStuDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto;
import com.aidt.api.tl.sbclrn.stu.TlSbcLrnStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 09:07:40
 * @modify date 2024-01-05 09:07:40
 * @desc TlSbcLrnTcrController  교과학습 교사
 */
@Slf4j
@Tag(name="[tl] 교과학습[TlSbcLrnTcr]", description="교과학습(교사)화면 구성을 위한 처리를 실행한다.")
@RestController
@RequestMapping("/api/v1/tl/tcr/sbclrn")
public class TlSbcLrnTcrController {
    
    @Autowired
    private JwtProvider jwtProvider;
	
    @Autowired
    private TlSbcLrnTcrService tlSbcLrnTcrService; //교사용 서비스
    
    @Autowired
    private TlSbcLrnStuService tlSbcLrnStuService; //학생용 서비스

    /**
     * 교과학습 학습활동 목록 조회서비스
     * 
     * @param srhDto
     * @return ResponseDto<List<TlSbcLrnAtvDto>>
     */
    @Operation(summary="교과학습 학습활동리스트 조회", description="해당 차시의 학습활동목록을 조회한다.")
    @PostMapping(value = "/selectLrnAtvList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlSbcLrnAtvDto>> selectLrnAtvList(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        
        if (StringUtils.isEmpty(srhDto.getLrmpNodId())) {
            throw new IllegalArgumentException("Invalid lrmpNodId in TlSbcLrnTocSrhDto ");
        }
        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlSbcLrnTcrService.selectLrnAtvList(srhDto));
    }

    /**
     * 교과학습 학습활동상세 Summery정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<TlSbcLrnAtvDto>
     */
    @Operation(summary="교과학습 학습활동상세 Summary조회", description="교과학습활동의 교과학습 Summary정보를 조회한다.")
    @PostMapping(value = "/selectLrnAtvSum", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlSbcLrnAtvSumDto> selectLrnAtvSum(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvSum");
        
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlSbcLrnStuService.selectLrnAtvSum(srhDto));
    }

    /**
     * 교과학습 학습활동 평가/익힘책 Summery정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<TlSbcLrnAtvDto>
     */
    @Operation(summary="교과학습 학습활동 평가/익힘책 Summary조회", description="교과학습활동의 평가 익힘책 Summary정보를 조회한다.")
    @PostMapping(value = "/selectLrnEvWkbSum", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlSbcLrnAtvSumEvWkbDto> selectLrnEvWkbSum(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnEvWkbSum");
        
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        if (StringUtils.isEmpty(srhDto.getLrmpNodId())) {
            throw new IllegalArgumentException("Invalid lrmpNodId in TlSbcLrnTocSrhDto ");
        }

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlSbcLrnStuService.selectLrnEvWkbSum(srhDto));
    }

    /** 
     * 교과학습 학습활동상태 체크
     * @return ResponseDto<TlSbcLrnAtvStChkDto>
     */
    @Operation(summary="교과학습 학습활동상태 체크", description="교과학습 학습활동상태 체크")
    @PostMapping(value = "/selectLrnAtvStChk")
    public ResponseDto<List<TlSbcLrnAtvStChkDto>> selectLrnAtvStChk() {
        log.debug("Entrance selectLrnAtvDtl");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        TlSbcLrnTocSrhDto srhDto = new TlSbcLrnTocSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());
        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_TCR);

        return Response.ok(tlSbcLrnStuService.selectLrnAtvStChk(srhDto));
    }
    
     /**
     * 교과학습 학생별 평가현황 조회 
     * @param evSrhDto
     * @return ResponseDto<List<TlSbcLrnEvStuDto>>
     */
    @Operation(summary="교과학습 학생별 평가현황 조회", description="교과학습 학생별 평가현황 조회")
    @PostMapping(value = "/selectSbcLrnEvPst", consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlSbcLrnEvStuDto>> selectSbcLrnEvPst(@Valid @RequestBody TlSbcLrnEvSrhDto evSrhDto) {
        log.debug("Entrance selectSbcLrnEvPst");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        if (StringUtils.isEmpty(String.valueOf(evSrhDto.getEvId()))) {
            throw new IllegalArgumentException("Invalid evId in TlSbcLrnEvSrhDto ");
        }

        evSrhDto.setOptTxbId(userDetails.getOptTxbId());
        evSrhDto.setTcrUsrId(userDetails.getUsrId());

        return Response.ok(tlSbcLrnTcrService.selectSbcLrnEvPst(evSrhDto));
    }


         /**
     * 교과학습 학생별 평가현황 조회 
     * @param evSrhDto
     * @return ResponseDto<List<TlSbcLrnEvStuDto>>
     */
    @Operation(summary="교과학습 과제출제건수 조회", description="해당 차시의 학습단계가 과제로 출제된 건수를 조회한다.")
    @PostMapping(value = "/countEaAsnData", consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> countEaAsnData(@Valid @RequestBody TlSbcLrnEaAsnSrhDto eaAsnSrhDto) {
        log.debug("Entrance countEaAsnData");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        eaAsnSrhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlSbcLrnTcrService.countEaAsnData(eaAsnSrhDto));
    }

    /**
     * 교과학습 차시 학습현황목록 조회(v3.3)
     * 
     * @return ResponseDto<TlSbcLrnAtvStatDto>
     */
    @Operation(summary = "교과학습 차시 학습현황목록 조회(v3.3)", description = "해당 차시의 학습단계별(개념/익힘/평가) 학습활동상태의 상세정보 목록을 조회한다.")
    @PostMapping(value = "/selectLrnTocStatusList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectLrnTocStatusList(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnTocStatusList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        if(StringUtils.isEmpty(srhDto.getLrnUsrId())) {
       	 srhDto.setLrnUsrId(userDetails.getUsrId());
       }
        return Response.ok(tlSbcLrnStuService.selectLrnTocStatusList(srhDto));
    }

}