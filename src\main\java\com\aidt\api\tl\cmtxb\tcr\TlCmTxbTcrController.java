package com.aidt.api.tl.cmtxb.tcr;

import java.util.*;


import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdReqDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbClsBrdUdtDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLastTcDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLastTcSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLluDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLrnDtlPstSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbPrevNextSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbTcTmDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbTotLrnTmDto;
import com.aidt.api.tl.cmtxb.stu.TlCmTxbStuService;
import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-08 14:42:44
 * @modify date 2024-02-08 14:42:44
 * @desc TlCmTxbTcr 교과학습 공통처리API(교사)
 */

@Slf4j
@Tag(name="[tl] 교과학습 공통처리API(교사)[TlCmtxbTcr]", description="(교사용)교과학습 공통처리를 처리한다.")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/tcr/cmtxb")
public class TlCmTxbTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private TlCmTxbStuService tlCmTxbStuService;
    
    @Autowired
    private TlCmTxbTcrService tlCmTxbTcrService;

    /**
     * 대단원 정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="대단원 정보 조회", description="대단원 정보 조회")
    @PostMapping(value = "/selectTxbLluList")
    public ResponseDto<List<TlCmTxbLluDto>> selectTxbLluList() {
        log.debug("Entrance selectTxbLluList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        TlCmTxbSrhDto srhDto = new TlCmTxbSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlCmTxbStuService.selectTxbLluList(srhDto));
    }

    /**
     * 차시 정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="차시 정보 조회", description="차시 정보 조회")
    @PostMapping(value = "/selectTxbTcList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlCmTxbLluDto>> selectTxbTcList(@RequestBody TlCmTxbSrhDto srhDto) {
        log.debug("Entrance selectTxbTcList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlCmTxbStuService.selectTxbTcList(srhDto));
    }

    /**
     * 수업예정 단원정보조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="수업예정 차시 정보 조회", description="수업예정 차시정보와 이전수업 차시정보를 취득한다.")
    @PostMapping(value = "/selectLastTxbTcList")
    public ResponseDto<TlCmTxbLastTcDto> selectLastTxbTcList() {
        log.debug("Entrance selectLastTxbTcList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        TlCmTxbLastTcSrhDto srhDto = new TlCmTxbLastTcSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());
        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_TCR);
        return Response.ok(tlCmTxbStuService.selectLastTxbTcList(srhDto));
    }

    /**
     * 이전차시 및 다음차시 정보조회
     * 
     * @param lrmpLodId 차시ID
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="이전차시 및 다음차시 정보조회", description="지정한 차시ID의 이전/다음 차시정보를 조회한다.")
    @PostMapping(value = "/selectTxbTcPrevNext")
    public ResponseDto<Object> selectTxbTcPrevNext(@Valid @RequestBody TlCmTxbPrevNextSrhDto srhDto) {
        log.debug("Entrance selectTxbTcPrevNext");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmTxbStuService.selectTxbTcPrevNext(srhDto));
    }
    /**
     * 학습활동클래스보드URL 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="학습활동클래스보드URL 조회", description="학습활동에 등록된 클래스보드URL을 조회한다.")
    @PostMapping(value = "/selectLrnAtvClsBrdUrl", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectLrnAtvClsBrdUrl(@Valid @RequestBody TlCmTxbClsBrdSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvClsBrdUrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmTxbStuService.selectTxbClsBrdUrl(srhDto));
    }

    /**
     * 상세학습현황 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="상세학습현황 조회", description="상세학습현황 조회")
    @PostMapping(value = "/selectTxbLrnDtlPst", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlCmTxbLrnDtlPstDto> selectTxbLrnDtlPst(@Valid @RequestBody TlCmTxbLrnDtlPstSrhDto srhDto) {
        log.debug("Entrance selectTxbLrnDtlPst");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlCmTxbTcrService.selectTxbLrnDtlPst(srhDto));
    }

    /**
     * 클래스보드url update
     * 
     * @param udtDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="클래스보드url update", description="지정차시의 클래스보드url정보를 저장한다.")
    @PostMapping(value = "/updateCmTxbClsBrdUrl", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateCmTxbClsBrdUrl(@Valid @RequestBody TlCmTxbClsBrdUdtDto udtDto) {
        log.debug("Entrance updateCmTxbClsBrdUrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        udtDto.setOptTxbId(userDetails.getOptTxbId());

        udtDto.setUsrId(userDetails.getUsrId());

        return Response.ok(tlCmTxbTcrService.updateCmTxbClsBrdUrl(udtDto));
    }

    /**
     * 클래스보드url update
     * 
     * @param udtDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="클래스보드url update(v3.3)", description="지정차시(클래스보드대제목ID, 클래스보드소제목ID)의 클래스보드url정보를 저장한다.")
    @PostMapping(value = "/updateCmTxbClabdUrl", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateCmTxbClabdUrl(@Valid @RequestBody TlCmTxbClsBrdUdtDto udtDto) {
        log.debug("Entrance updateCmTxbClabdUrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        udtDto.setOptTxbId(userDetails.getOptTxbId());

        udtDto.setUsrId(userDetails.getUsrId());

        return Response.ok(tlCmTxbTcrService.updateCmTxbClabdUrl(udtDto));
    }


    /**
     * 학생별 총 학습시간 조회
     * 
     * @return ResponseDto<List<TlCmTxbLluDto>>
     */
    @Operation(summary="학생별 총 학습시간 조회", description="학생별 총 학습시간 조회")
    @PostMapping(value = "/selectCmTxbTotLrnTm")
    public ResponseDto<List<TlCmTxbTotLrnTmDto>> selectCmTxbTotLrnTm() {
        log.debug("Entrance selectCmTxbTotLrnTm");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        TlCmTxbSrhDto srhDto = new TlCmTxbSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlCmTxbTcrService.selectCmTxbTotLrnTm(srhDto));
    }

    /**
     * 학습활동클래스보드URL 조회
     * 
     * @param srhDto
     * @return ResponseDto<Map<String, String>>
     */
    @Operation(summary="학습활동클래스보드URL 조회 v3.3대응", description="학습활동에 등록된 클래스보드URL을 조회한다.")
    @PostMapping(value = "/selectLrnAtvClabdUrl", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectLrnAtvClabdUrl(@Valid @RequestBody TlCmTxbClsBrdSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvClabdUrl");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 운영교과서ID 미설정시 세션값설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlCmTxbStuService.selectTxbClabdUrl(srhDto));
    }
    
    /**
     * 이번수업선택
     * 
     * @param tmDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="이번수업선택", description="이번수업으로 선택한 차시 정보 업데이트")
    @PostMapping(value = "/updateCmTxbTcTm", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateCmTxbTcTm(@RequestBody TlCmTxbTcTmDto tmDto) {
        log.debug("Entrance updateCmTxbTcTm");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        tmDto.setOptTxbId(userDetails.getOptTxbId());
        tmDto.setCrtrId(userDetails.getUsrId());
        tmDto.setMdfrId(userDetails.getUsrId());
        tmDto.setDbId(userDetails.getTxbId());
        
        return Response.ok(tlCmTxbTcrService.updateCmTxbTcTm(tmDto));
    }
    
    /**
     * 클래스보드 URL 생성
     * @param reqDto
     * @return
     */
    @Operation(summary="클래스보드 URL 생성", description="클래스보드 URL정보를 생성한다")
    @PostMapping(value = "/createClaBoardUrl", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> createClaBoardUrl(@RequestBody TlCmTxbClsBrdReqDto reqDto) {    	
    	log.debug("createClsBrdUrl 입력 파라미터 : {}" , reqDto);                      
        
    	return tlCmTxbTcrService.callCsbApi("/project/by-lms", reqDto);
    }
    
    /**
     * 시작 활동ID 조회
     * 
     * @param tmDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="시작 활동ID 조회", description="시작 활동ID 조회")
    @PostMapping(value = "/selectStrAtvId", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<String> selectStrAtvId(@RequestBody TlCmTxbTcTmDto tmDto) {
        log.debug("Entrance selectStrAtvId");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        tmDto.setOptTxbId(userDetails.getOptTxbId());
        
        return Response.ok(tlCmTxbTcrService.selectStrAtvId(tmDto));
    }

}
