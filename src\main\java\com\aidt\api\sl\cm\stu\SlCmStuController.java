package com.aidt.api.sl.cm.stu;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.cm.dto.SlCmSpLrnDto;
import com.aidt.api.sl.cm.dto.SlCmSrhDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-08 09:37:48
 * @modify date 2024-05-08 09:37:48
 * @desc [공통 특별학습 조회 controller]
 */
@Slf4j
@Tag(name="[sl] 공통 특별학습 학생 조회 [SlCmStu]", description="공통 특별학습 학생 조회 [SlCmStu]")
@RestController
@RequestMapping("/api/v1/sl/stu/cm")
public class SlCmStuController {

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
    private SlCmStuService slCmStuService;

    @Operation(summary="홈 화면 특별학습 조회", description = "홈 화면 특별학습을 조회한다.")
    @PostMapping(value = "/selectSlCmSpLrnList")
    public ResponseDto<List<SlCmSpLrnDto>> selectSlCmSpLrnList(@Valid @RequestBody SlCmSrhDto slCmSrhDto) {
        log.info("Entrance selectSlCmSpLrnList");
        // 사용자정보, 운영교과서 아이디등 정보 가져오기1
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
        slCmSrhDto.setLrnUsrId(securityUserDetailDto.getUsrId());
        slCmSrhDto.setOptTxbId(securityUserDetailDto.getOptTxbId());

        return Response.ok(slCmStuService.selectSlCmSpLrnList(slCmSrhDto));
    }
}
