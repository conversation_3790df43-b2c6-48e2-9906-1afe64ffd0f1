package com.aidt.api.tl.lrnwif;

import com.aidt.api.tl.lrnwif.dto.TlLrnwTocAtvDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto;
import com.aidt.common.CommonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class TlLrnwIfCacheService {
	private final String MAPPER_NAMESPACE = "api.tl.lrnwif.stu.";

    @Autowired
    private CommonDao commonDao;
    
    @Transactional(readOnly = true)
    @Cacheable(
    		cacheNames = "longCache",
    		key = "'tl:' + #srhDto.optTxbId + ':selectLrnTocAtvInfo'",
    		condition="!'Y'.equals(#srhDto.getOneClkYn())",
    		unless = "#result.size() == 0",
    		cacheManager = "aidtCacheManager"
    		)
    public List<TlLrnwTocAtvDto> selectLrnTocAtvInfo(TlLrnwTocSrhDto srhDto, String usrDvCd) {
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTocAtvInfo",  Map.of("param", srhDto, "usrDvCd", usrDvCd));
    }
}
