<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.webdisp.cm">

	<insert id="insertCpReq" parameterType="com.aidt.api.bc.cm.dto.BcCmWebDispDto">
		insert into lms_lrm.cm_cp_req
		    (     txb_id
		        , cp_no
		        , lrnr_vel_tp_cd
		        , req_usr_id
		        , cp_cmpl_yn
		        , err_cn
		        , crtr_id
		        , crt_dtm
		        , mdfr_id
		        , mdf_dtm
		    )
		select #{txbId}
		     , #{cpNo}
		     , null
		     , #{stuId}
		     , null
		     , null
		     , #{usrId}
		     , now()
		     , #{usrId}
		     , now()
		  from dual
		 where not exists (
		    select 1 
		      from lms_lrm.cm_cp_req
		     where txb_id = #{txbId}
		       and req_usr_id = #{stuId}
		 )
	</insert>
	
	<insert id="insertCpReqOpt" parameterType="com.aidt.api.bc.cm.dto.BcCmWebDispDto">
		insert into lms_lrm.cm_cp_req
		    (     txb_id
		        , cp_no
		        , lrnr_vel_tp_cd
		        , req_usr_id
		        , cp_cmpl_yn
		        , err_cn
		        , crtr_id
		        , crt_dtm
		        , mdfr_id
		        , mdf_dtm
		        , opt_txb_id
		    )
		select #{txbId}
		     , #{cpNo}
		     , null
		     , #{stuId}
		     , null
		     , null
		     , #{usrId}
		     , now()
		     , #{usrId}
		     , now()
		     , #{optTxbId}
		  from dual
		 where not exists (
		    select 1 
		      from lms_lrm.cm_cp_req
		     where txb_id = #{txbId}
		       and req_usr_id = #{stuId}
		 )
	</insert>
	
	<insert id="callStuDataCp" parameterType="com.aidt.api.bc.cm.dto.BcCmWebDispDto" statementType="CALLABLE">
		call lms_lrm.lms_usr_data_copy(
			  #{optTxbId, mode=IN}
			, #{rtnRowCnt, mode=OUT, jdbcType=INTEGER}
			, #{rtnMsg, mode=OUT, jdbcType=VARCHAR}
			, #{rtnErrNsg, mode=OUT, jdbcType=VARCHAR}
		)
	</insert>
	
	<insert id="callPersonaDataCp" parameterType="com.aidt.api.bc.cm.dto.BcCmWebDispDto" statementType="CALLABLE">
		call lms_lrm.lms_persona_data_copy(
			  #{txbId, mode=IN}
			, #{personaId, mode=IN}
			, #{usrId, mode=IN}
			, #{rtnRowCnt, mode=OUT, jdbcType=INTEGER}
		)
	</insert>

</mapper>
