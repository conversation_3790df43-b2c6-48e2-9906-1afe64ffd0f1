package com.aidt.api.at.mapper;

import com.aidt.api.at.token.dto.Jwt;

public interface TokenMapper {
   
    public Jwt.JwtUserDto selectUser(Jwt.JwtRequestDto jwtRequestDto);
    
    public Jwt.JwtUserDto selectRefreshToken(Jwt.JwtRefreshDto jwtRefreshDto);
    
    public Jwt.JwtUserDto selectUsrRefreshToken(Jwt.JwtRefreshDto jwtRefreshDto);

    public int updateRefreshToken(Jwt.JwtRefreshDto refreshDto);
    
    public int insertUsrRefreshToken(Jwt.JwtRefreshDto refreshDto);

    public int deleteRefreshToken(String refreshToken);
}