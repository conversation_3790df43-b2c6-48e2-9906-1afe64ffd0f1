/**
 * 
 */
package com.aidt.api.bc.cm.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcCmKerisAPiDto {

	@NotBlank(message = "{field.required}")
	private String access_id;
	@NotBlank(message = "{field.required}")
	private String token;
	@NotBlank(message = "{field.required}")
	private String apiUrl;
	@NotBlank(message = "{field.required}")
	private  String partnerId;
	
	private String user_id;
	private String class_code;
	private String lecture_code;
	private String user_type;
	private List<String> user_ids;
}
