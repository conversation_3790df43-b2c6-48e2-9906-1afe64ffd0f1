package com.aidt.api.bc.home.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "학생 현재 위치 상세 정보")
public class BcStuCurLrnStDtlDto {

    @Parameter(name="학습 유형 (tl, sl, al, ...")
    private String lrnTpCd;

    @Parameter(name="현재 학습 메뉴 ID")
    private Long curLrnMenuId;

    @Parameter(name="현재 학습 메뉴명")
    private String curLrnMenuNm;

    @Parameter(name="단원 노드 ID")
    private String luNodId;

    @Parameter(name="차시 노드 ID")
    private String tcNodId;

    @Parameter(name="학습 활동 ID")
    private String lrnAtvId;

    public boolean isRootLocation() {
        return (luNodId == null || luNodId.isBlank()) && (tcNodId == null || tcNodId.isBlank()) && (lrnAtvId == null || lrnAtvId.isBlank());
    }

}
