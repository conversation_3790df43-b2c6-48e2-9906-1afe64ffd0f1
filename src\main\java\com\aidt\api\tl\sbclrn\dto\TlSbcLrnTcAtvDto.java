package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 16:14:31
 * @modify date 2024-01-04 16:14:31
 * @desc TlSbcLrnTcAtvDto 차시학습활동상세Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnTcAtvDto {

       /** (차시)학습맵노드ID */
       @Parameter(name="(차시)학습맵노드ID")
       private String lrmpNodId;

       /** 학습활동ID */
       @Parameter(name="학습활동ID")
       private String lrnAtvId;

       /** 학습활동명 */
       @Parameter(name="학습활동명")
       private String lrnAtvNm;

       /** 학습단계ID */
       @Parameter(name="학습단계ID")
       private long lrnStpId;

       /** 학습단계 구분코드 */
       @Parameter(name="학습단계 구분코드")
       private String lrnStpCd;

       /** 학습단계명 */
       @Parameter(name="학습단계명")
       private String lrnStpNm;
       
       /** 콘텐츠코드 */
       @Parameter(name="콘텐츠코드")
       private String ctnCd;

       /** 콘텐츠유형코드(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
       @Parameter(name="콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지)")
       private String ctnTpCd;

       /** 학습상태코드 */
       @Parameter(name="학습상태코드")
       private String lrnStCd;

       /** 학습활동썸네일CDN패스 */
       @Parameter(name="학습활동썸네일CDN패스")
       private String lrnAtvThbCdnPath;

       /** 현재학습활동여부(마지막학습중이었던 학습활동) */
       @Parameter(name = "현재학습활동여부")
       private String currAtvYn;
       
       /** 교사콘텐츠여부 */
       @Parameter(name="교사콘텐츠여부")
       private String tcrCtnYn;
}
