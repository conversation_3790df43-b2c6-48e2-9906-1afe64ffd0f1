package com.aidt.api.tl.lrnwif.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwLrnStpDto 학습단계정보Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLrnwLrnStpDto {

    /** 학습단계ID */
    @Parameter(name="학습단계ID")
    private String lrnStpId;
    /** 학습단계구분코드 */
    @Parameter(name="학습단계구분코드")
    private String lrnStpDvCd;
    /** 학습단계코드 */
    @Parameter(name="학습단계코드")
    private String lrnStpCd;
    /** 학습단계명 */
    @Parameter(name="학습단계명")
    private String lrnStpNm;
    /** 학습단계정렬순서 */
    @Parameter(name="학습단계정렬순서")
    private String lrnStpOrdn;
    /** 학습활동목록 */
    @Parameter(name="학습단계정렬순서")
    private List<TlLrnwLrnAtvDto> lrnAtvList;
}
