package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-13
 * @modify date 2024-06-13
 * @desc 학습 리포트 - 선생님 추천학습 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTlSbcLrnAtvEvQtmDto {
	/** 평가ID */
    @Parameter(name="평가ID")
    private String evId;
    /** 문항ID */
    @Parameter(name="문항ID")
    private String qtmId;
    /** 문항번호 */
    @Parameter(name="문항번호")
    private String qtmOrdn;
    /** 사용자ID */
    @Parameter(name="사용자ID")
    private String usrId;
    /** 정답여부 */
    @Parameter(name="정답여부")
    private String cansYn;
    /** 문항별 풀이시간 */
    @Parameter(name="문항별 풀이시간")
    private String xplTmScnt;
}
