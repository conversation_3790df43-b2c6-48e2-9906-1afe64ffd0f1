package com.aidt.api.bc.chatbot.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCbDto{	
	@Parameter(name="질문내용")
	private String req;

	@Parameter(name="세부URL")
	private String subUrl;

	@Parameter(name="유저ID")
	private String usrId;

	@Parameter(name="초중고 구분 정보")
	private String schlGrdCd;

	@Parameter(name="학년 정보")
	private String userGrade;

	@Parameter(name="학습 유형 정보")
	private String learningType;

	@Parameter(name="교과서 아이디")
	private String txbId;
	
	// @Parameter(name="유저ID")
	// private String user;

	// @Parameter(name="피드백 저장 (Admin 유저 ID)")
	// private String userId;

	// @Parameter(name="피드백 저장 (질문)")
	// private String question;

	// @Parameter(name="피드백 저장 (현재답변)")
	// private String current;

	// @Parameter(name="피드백 저장 (변경답변)")
	// private String desired;
}
