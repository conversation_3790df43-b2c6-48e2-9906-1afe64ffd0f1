package com.aidt.api.bc.clablbd.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.clablbd.dto.BcClaBlbdDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-07 13:42:10
 * @modify 2024-06-07 13:42:10
 * @desc 학급게시판 Controller
 */

@Slf4j
@Tag(name="[bc] 학급게시판 [BcClaBlbdTcr]", description="학급게시판(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/clablbd")
public class BcClaBlbdTcrController {

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private BcClaBlbdTcrService bcClaBlbdTcrService;


	/**
	 * 학급게시판 조회
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<List<BcClaBlbdDto>>
	 */
	@Tag(name="[bc] 학급게시판 조회", description="학급게시판 조회")
	@GetMapping(value = "/selectClaBlbdList")
	public ResponseDto<List<BcClaBlbdDto>> selectClaBlbdList(BcClaBlbdDto bcClaBlbdDto) {
	
		// 2024-06-07 JWT토큰정보에서 운영교과서ID 추출 -> 추후 변경 필요
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setKerisUsrId(userDetails.getKerisUsrId());
		
		log.debug("Entrance selectClaBlbdList");
		return Response.ok(bcClaBlbdTcrService.selectClaBlbdList(bcClaBlbdDto));
	}
	
	
	/**
	 * 학급게시판 상세 조회
	 *
	 * @param claBlbdId
	 * @return ResponseDto<BcClaBlbdDto>
	 */
	@Tag(name="[bc] 학급게시판 상세 조회", description="학급게시판 상세 조회")
	@GetMapping(value = "/getClaBlbdInfo/{claBlbdId}")
	public ResponseDto<BcClaBlbdDto> getClaBlbdInfo(@PathVariable("claBlbdId") Long claBlbdId) {
		
		BcClaBlbdDto bcClaBlbdDto = new BcClaBlbdDto();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setClaBlbdId(claBlbdId);
		
		log.debug("Entrance getClaBlbdInfo");
		return Response.ok(bcClaBlbdTcrService.getClaBlbdInfo(bcClaBlbdDto));
	}
	
	
	/**
	 * 학급게시판 등록/수정/삭제
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 학급게시판 등록/수정/삭제", description="학급게시판 등록/수정/삭제(교사)")
	@PostMapping(value = "/saveClaBlbdInfo")
	public Map<String, Object> saveClaBlbdInfo(@Valid @RequestBody BcClaBlbdDto bcClaBlbdDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setDbId(userDetails.getTxbId());
		bcClaBlbdDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrTpCd(userDetails.getUsrTpCd());
	
		log.debug("Entrance saveClaBlbdInfo");
		Map<String, Object> result = bcClaBlbdTcrService.saveClaBlbdInfo(bcClaBlbdDto);
	    return result;
	}
	
	
	/**
	 * 학급게시판 댓글 등록/수정/삭제
	 *
	 * @param BcClaBlbdUcwrDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 학급게시판 댓글 등록/수정/삭제", description="학급게시판 댓글 등록/수정/삭제(교사)")
	@PostMapping(value = "/saveClaBlbdUcwrInfo")
	public ResponseDto<Integer> saveClaBlbdUcwrInfo(@Valid @RequestBody BcClaBlbdUcwrDto bcClaBlbdUcwrDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdUcwrDto.setDbId(userDetails.getTxbId());
		bcClaBlbdUcwrDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdUcwrDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdUcwrDto.setUsrId(userDetails.getUsrId());
	
		log.debug("Entrance saveClaBlbdUcwrInfo");
		return Response.ok(bcClaBlbdTcrService.saveClaBlbdUcwrInfo(bcClaBlbdUcwrDto));
	}
	
	
	/**
	 * 학급게시판 일괄 등록
	 *
	 * @param bcClaBlbdDto
	 * @return ResponseDto<List<BcClaBlbdDto>>
	 */
	@Tag(name="[bc] 학급게시판 일괄 등록", description="학급게시판 일괄 등록")
	@PostMapping(value = "/saveTcrPkgBlbd")
	public ResponseDto<Integer> saveTcrPkgBlbd(@RequestBody BcClaBlbdDto bcClaBlbdDto) {
	
		// 2024-06-07 JWT토큰정보에서 운영교과서ID 추출 -> 추후 변경 필요
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setDbId(userDetails.getTxbId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		bcClaBlbdDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdDto.setMdfrId(userDetails.getUsrId()); 
		
		log.debug("Entrance saveTcrPkgBlbd");
		return Response.ok(bcClaBlbdTcrService.saveTcrPkgBlbd(bcClaBlbdDto));
	}
	
	/**
	 * 학급게시판 일괄 삭제
	 *
	 * @param bcClaBlbdDto
	 * @return ResponseDto<List<BcClaBlbdDto>>
	 */
	@Tag(name="[bc] 학급게시판 일괄 삭제", description="학급게시판 일괄 삭제")
	@PostMapping(value = "/deleteTcrPkgBlbd")
	public ResponseDto<Integer> deleteTcrPkgBlbd(@RequestBody BcClaBlbdDto bcClaBlbdDto) {
	
		// 2024-06-07 JWT토큰정보에서 운영교과서ID 추출 -> 추후 변경 필요
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setDbId(userDetails.getTxbId());
		bcClaBlbdDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		
		log.debug("Entrance deleteTcrPkgBlbd");
		return Response.ok(bcClaBlbdTcrService.deleteTcrPkgBlbd(bcClaBlbdDto));
	}

	/**
	 * 학급게시판 일괄 등록 목록 조회
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<List<BcClaBlbdDto>>
	 */
	@Tag(name="[bc] 학급게시판 일괄 등록 목록 조회", description="학급게시판 일괄 등록 목록 조회")
	@PostMapping(value = "/selectTcrPkgClaList")
	public ResponseDto<List<BcClaBlbdDto>> selectTcrPkgClaList(@Valid @RequestBody BcClaBlbdDto bcClaBlbdDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setDbId(userDetails.getTxbId());
		bcClaBlbdDto.setCrtrId(userDetails.getUsrId());
		bcClaBlbdDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		bcClaBlbdDto.setUsrTpCd(userDetails.getUsrTpCd());
	
		log.debug("Entrance selectTcrPkgClaList");
		return Response.ok(bcClaBlbdTcrService.selectTcrPkgClaList(bcClaBlbdDto));
	}
	
	/**
     * 학급게시판 운영교과서 체크
     * @param BcCbDto
     * @return
     */
	@Operation(summary="학급게시판 운영교과서 체크", description="학급게시판 운영교과서 체크")
	@PostMapping(value = "/selectOptTxbIdChk", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String, Object> selectOptTxbIdChk(@Valid @RequestBody BcClaBlbdDto bcClaBlbdDto) {
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
		bcClaBlbdDto.setUsrId(userDetails.getUsrId());
		
		log.debug("Entrance selectOptTxbIdChk");
		return bcClaBlbdTcrService.selectOptTxbIdChk(bcClaBlbdDto);
	}
	
	/**
	 * 학급게시판 게시글 승인 여부 수정
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 학급게시판 게시글 승인 여부 수정", description="학급게시판 게시글 승인 여부 수정")
	@PostMapping(value = "/updateBlwrAprYn")
	public ResponseDto<Integer> updateBlwrAprYn(@Valid @RequestBody BcClaBlbdDto bcClaBlbdDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdDto.setDbId(userDetails.getTxbId());
		bcClaBlbdDto.setMdfrId(userDetails.getUsrId());
		bcClaBlbdDto.setOptTxbId(userDetails.getOptTxbId());
	
		log.debug("Entrance updateBlwrAprYn");
		return Response.ok(bcClaBlbdTcrService.updateBlwrAprYn(bcClaBlbdDto));
	}
	
	/**
	 * 학급게시판 댓글 승인 여부 수정
	 *
	 * @param BcClaBlbdDto
	 * @return ResponseDto<Integer>
	 */
	@Tag(name="[bc] 학급게시판 게시글 승인 여부 수정", description="학급게시판 게시글 승인 여부 수정")
	@PostMapping(value = "/updateUcwrAprYn")
	public ResponseDto<Integer> updateUcwrAprYn(@Valid @RequestBody BcClaBlbdUcwrDto bcClaBlbdUcwrDto) {
	
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		bcClaBlbdUcwrDto.setDbId(userDetails.getTxbId());
		bcClaBlbdUcwrDto.setMdfrId(userDetails.getUsrId());
	
		log.debug("Entrance updateUcwrAprYn");
		return Response.ok(bcClaBlbdTcrService.updateUcwrAprYn(bcClaBlbdUcwrDto));
	}
}
