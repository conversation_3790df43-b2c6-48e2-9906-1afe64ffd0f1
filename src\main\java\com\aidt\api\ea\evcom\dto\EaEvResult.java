package com.aidt.api.ea.evcom.dto;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.api.common.enums.SchoolGradeCode;
import com.aidt.api.common.enums.SubjectCode;
import com.aidt.common.CommonUserDetail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EaEvResult {

	private Integer evId;
	private String evDvCd;
	private String evDtlDvCd;
	private String rtxmPmsnYn;
	private String evCmplYn;
	private String evRcmplYn;
	private String txmStrYn;
	private Integer txmPn;
	private String sppNtnCmplYn;
	private String sppNtnStrYn;
	private String schlGrdCd;
	private String sbjCd;
	private String usrTpCd;
	private List<EaEvAnswer> answers;
	private long cansCnt;

	//fixme: 감사 정보는 분리해야하지 않을까? 공통적으로 만들고픔
	private String usrId;
	private String dbId;

	public void addAnswers(List<EaEvAnswer> answers) {
		if (CollectionUtils.isNotEmpty(answers)) {
			this.answers = answers;
			this.cansCnt = answers.stream()
				.filter(x -> x.getCansYn().equalsIgnoreCase("Y"))
				.count();
		}
	}

	public EvaluationDetailCode getEvaluationDetailCode() {
		return EvaluationDetailCode.getEvaluationDetailCode(this.evDtlDvCd);
	}

	public SubjectCode getSubjectCode() {
		return SubjectCode.getSubjectCode(this.sbjCd);
	}

	public SchoolGradeCode getSchoolGradeCode() {
		return SchoolGradeCode.getSchoolGradeCode(this.schlGrdCd);
	}

	public boolean isReexaminationPossible() {
		return "Y".equalsIgnoreCase(this.rtxmPmsnYn) && !isCompleted();
	}

	public boolean isReexaminationPossibleWithScore() {
		return isReexaminationPossible() && isPerfectScore();
	}

	public boolean isPerfectScore() {
		return CollectionUtils.isNotEmpty(this.answers) && this.answers.size() == this.cansCnt;
	}

	public void addAudit(CommonUserDetail userDetail) {
		if (userDetail != null) {
			this.usrId = userDetail.getUsrId();
			this.dbId = userDetail.getTxbId();
		}
	}

	public void startExam() {
		this.txmStrYn = "Y";
	}

	public void complete() {
		this.evCmplYn = "Y";
	}

	public void withdraw() {
		this.evCmplYn = "N";
	}

	public void incrementReexaminationRound() {
		if (isCompleted() && (isReexaminationCompleted() || this.txmPn == 0)) {
			this.txmPn++;
		}
	}

	public boolean isAiEvAnswer() {
		return "OV".equalsIgnoreCase(this.evDtlDvCd);
	}

	public boolean isCompleted() {
		return "Y".equalsIgnoreCase(this.evCmplYn);
	}

	public boolean isReexaminationCompleted() {
		return this.evRcmplYn.equalsIgnoreCase("Y");
	}

	public boolean hasNoCorrectAnswers() {
		return this.cansCnt < 1;
	}

}
