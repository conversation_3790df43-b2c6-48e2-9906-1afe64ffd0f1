package com.aidt.api.bc.tnte.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:59:47
 * @modify 2024-01-05 17:59:47
 * @desc 공지사항 dto
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcAnnxFleDto {

	@Parameter(name="첨부파일ID")
	private Long annxFleId;

	@Parameter(name="첨부ID")
	private Long annxId;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="첨부파일명")
	private String annxFleNm;

	@Parameter(name="첨부파일원본명")
	private String annxFleOrglNm;

	@Parameter(name="첨부파일확장자명")
	private String annxFleFextNm;

	@Parameter(name="첨부파일사이즈")
	private long annxFleSze;

	@Parameter(name="첨부파일경로명")
	private String annxFlePthNm;

	@Parameter(name="사용여부")
	private String useYn;


}
