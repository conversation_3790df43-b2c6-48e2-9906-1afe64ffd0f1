package com.aidt.api.al.sr.dto.sbc;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 16:40:10
 * @modify date 2024-05-24 16:40:10
 * @desc
 */
@Data
@Builder
public class AiSrTcrLrnResDto {

    private String usrId;

    private List<AiSrAchCmtDto> comments;

    @JsonIgnore
    private List<AiSrQtmDto> aiSrQtmDtoList;

    public void addComment(AiSrAchCmtDto comment) {
        if(comments == null)
            comments = new ArrayList<>();

        comments.add(comment);
    }

    public boolean isAll0() {
        List<AiSrQtmDto> collect = this.aiSrQtmDtoList.stream().filter(s -> Integer.parseInt(s.getCorrectCnt()) == 0).collect(Collectors.toList());
        return collect.size() == this.aiSrQtmDtoList.size();
    }

}
