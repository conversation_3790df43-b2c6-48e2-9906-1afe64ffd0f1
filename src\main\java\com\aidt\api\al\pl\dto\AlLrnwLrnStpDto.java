package com.aidt.api.al.pl.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 학습창 연계
 * 학습단계
 * */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlLrnwLrnStpDto {
	
	@Parameter(name="학습단계ID")
	private String lrnStpId;
	
	@Parameter(name="학습단계명")
	private String lrnStpNm;
	
	@Parameter(name="학습단계 정렬순서")
	private String lrnStpOrdn;
	
	@Parameter(name="학습단계 구분코드")
	private String lrnStpDvCd;
	
	@Parameter(name="학습활동 리스트")
	List<AlLrnwLrnAtvDto> lrnAtvList;
	
}
