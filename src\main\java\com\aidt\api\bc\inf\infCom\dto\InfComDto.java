package com.aidt.api.bc.inf.infCom.dto;


import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class InfComDto {
	
	@Parameter(name="알림메시지ID")
	private Long infmMsgId;			
	
	@Parameter(name="알림유형코드")
	private String infmTpCd;		
	
	@Parameter(name="알림분류코드")
	private String infmClCd;		
	
	@Parameter(name="알림상세분류코드")
	private String infmDtlClCd;		
	
	@Parameter(name="알림명")
	private String infmNm;			
	
	@Parameter(name="알림내용")
	private String infmCn;			
	
	@Parameter(name="알림이동내용")
	private String infmMvCn;		
	
	@Parameter(name="알림대상사용자ID")
	private String infmObjUsrId;	
	
	@Parameter(name="특별학습ID")
	private String spLrnId;

	@Parameter(name="특별학습명")
	private String rcmCtnNm;
	
	@Parameter(name="운영교과서ID")
	private String 	optTxbId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="평가ID")
	private Long evId;
	
	@Parameter(name="클래스ID")
	private String claId;
	
	@Parameter(name="교사사용자ID")
	private String tcrUsrId;
	
	@Parameter(name="다른학급저장여부")
	private String otherClaYn;
	
	@Parameter(name="학급게시판ID")
	private Long claBlbdId;
	
	@Parameter(name="학급게시판제목명")
	private String claBlbdTitlNm;
	
	@Parameter(name="과제ID")
	private Integer asnId;
	
	private String dbId;
	
}
