package com.aidt.api.al.cmt.dto;

import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.api.al.cmt.dto.res.AiCmtUsrDataResDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-31 13:43:07
 * @modify date 2024-07-31 13:43:07
 * @desc
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtUsrDataDto {

    private Integer aiCmtUsrDataId;

    private String evId;

    private String aiCmtNo;

    private String usrId;

    private String cmtCn;

    private String cmtType;

    private Timestamp mdfDtm;

    public AiCmtResDto toResDto() {
        return AiCmtResDto.builder()
                .aiCmtNo(this.aiCmtNo)
                .cmtCn(this.cmtCn)
                .cmtType(this.cmtType)
                .build();
    }

}
