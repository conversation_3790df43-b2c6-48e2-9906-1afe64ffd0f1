<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.clamg.tcr">

	<select id="selectOptTxbUnit" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		/* 전체 학급 관리 - 오윤영 - AllClaMgTcr-Mapper.xml - selectOptTxbUnit - 전체 학급 관리 > 단원 조회 */
		SELECT
			lrnNodRe.LLU_NOD_ID,
			lrnNodRe.LRMP_NOD_ID,
			lrnNodRe.LRMP_NOD_NM,
			lrnNodRe.RCSTN_NO
		FROM
			lms_lrm.tl_sbc_lrn_nod_rcstn lrnNodRe
		INNER JOIN lms_cms.bc_lrmp_nod lrmpNod ON lrnNodRe.LRMP_NOD_ID = lrmpNod.LRMP_NOD_ID
		WHERE 1=1
			AND lrnNodRe.OPT_TXB_ID = #{optTxbId}
			AND lrnNodRe.DPTH = 1
			AND lrnNodRe.LU_EPS_YN = 'Y'
			AND lrmpNod.DEL_YN = 'N'
		ORDER BY lrnNodRe.RCSTN_ORDN
		/* 전체 학급 관리 - 오윤영 - AllClaMgTcr-Mapper.xml - selectOptTxbUnit - 전체 학급 관리 > 단원 조회 */
	</select>

	<select id="selectAchievementRate" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		/* 전체 학급 관리 - 오윤영 - AllClaMgTcr-Mapper.xml - selectAchievementRate - 전체 학급 관리 > 통계 조회 > 성취율 */
		WITH AVG_UD_OV_UG AS (
			SELECT
				exam.OPT_TXB_ID AS opt_txb_id,
				class.CLA_NO AS cla_no,
--                 CASE
--                     WHEN exam.EV_DTL_DV_CD = 'UD'
--                     THEN ROUND((SUM(COALESCE(examRes.CANS_CNT, 0)) / (COUNT(examRes.USR_ID) * exam.FNL_QST_CNT)) * 100, 1)
--                     ELSE null
--                 END AS avg_rt_ud,
				CASE
					WHEN exam.EV_DTL_DV_CD = 'OV'
					THEN ROUND((SUM(COALESCE(examRes.CANS_CNT, 0)) / (COUNT(examRes.USR_ID) * exam.FNL_QST_CNT)) * 100, 1)
					ELSE null
					END AS avg_rt_ov,
				CASE
					WHEN exam.EV_DTL_DV_CD = 'UG'
					THEN ROUND((SUM(COALESCE(examRes.CANS_CNT, 0)) / (COUNT(examRes.USR_ID) * exam.FNL_QST_CNT)) * 100, 1)
					ELSE null
					END AS avg_rt_ug
			FROM
				lms_lrm.ea_ev exam
			INNER JOIN lms_lrm.ea_ev_ts_rnge eetr ON exam.EV_ID = eetr.EV_ID
			LEFT JOIN lms_lrm.cm_opt_txb txb ON txb.OPT_TXB_ID = exam.OPT_TXB_ID
			LEFT JOIN lms_lrm.cm_cla class ON class.CLA_ID = txb.CLA_ID
			LEFT JOIN lms_lrm.cm_usr `user` ON `user`.CLA_ID = class.CLA_ID AND `user`.USR_TP_CD = 'ST'
			LEFT JOIN lms_lrm.ea_ev_rs examRes ON examres.EV_ID = eetr.EV_ID AND examres.USR_ID = `user`.USR_ID
			WHERE 1=1
				AND exam.OPT_TXB_ID = #{optTxbId}
				AND eetr.LU_LRMP_NOD_ID = #{lluNodId}
				AND examres.EV_CMPL_YN = 'Y'
			GROUP BY exam.OPT_TXB_ID, exam.EV_ID
		)
		SELECT
			opt_txb_id,
--             CASE
--                 WHEN ROUND(SUM(COALESCE(avg_rt_ud, 0)) / COUNT(avg_rt_ud), 1) IS NULL
--                 THEN '-'
--                 ELSE CONCAT(ROUND(SUM(COALESCE(avg_rt_ud, 0)) / COUNT(avg_rt_ud), 1), '%')
--             END AS avg_rt_ud,
			CASE
				WHEN ROUND(SUM(COALESCE(avg_rt_ov, 0)) / COUNT(avg_rt_ov), 1) IS NULL
				THEN '-'
				ELSE CONCAT(ROUND(SUM(COALESCE(avg_rt_ov, 0)) / COUNT(avg_rt_ov), 1), '%')
				END AS avg_rt_ov,
			CASE
				WHEN ROUND(SUM(COALESCE(avg_rt_ug, 0)) / COUNT(avg_rt_ug), 1) IS NULL
				THEN '-'
				ELSE CONCAT(ROUND(SUM(COALESCE(avg_rt_ug, 0)) / COUNT(avg_rt_ug), 1), '%')
				END AS avg_rt_ug
		FROM AVG_UD_OV_UG
		GROUP BY opt_txb_id
		ORDER BY cla_no
		/* 전체 학급 관리 - 오윤영 - AllClaMgTcr-Mapper.xml - selectAchievementRate - 전체 학급 관리 > 통계 조회 > 성취율 */
	</select>

	<select id="selectLearnerLevel" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		/* 전체 학급 관리 - 오윤영 - AllClaMgTcr-Mapper.xml - selectLearnerLevel - 전체 학급 관리 > 통계 조회 > 학습자 수준 분포 */
		SELECT
			cot2.OPT_TXB_ID AS opt_txb_id,
			SUM(CASE WHEN tlll.LRNR_VEL_TP_CD = 'FS' THEN 1 ELSE 0 END) AS lrnr_lev_fs,
			SUM(CASE WHEN tlll.LRNR_VEL_TP_CD = 'NM' THEN 1 ELSE 0 END) AS lrnr_lev_nm,
			SUM(CASE WHEN tlll.LRNR_VEL_TP_CD = 'SL' THEN 1 ELSE 0 END) AS lrnr_lev_sl,
			SUM(CASE WHEN COALESCE(tlll.LRNR_VEL_TP_CD, 'BA') = 'BA' THEN 1 ELSE 0 END) AS lrnr_lev_ba,
			COUNT(cu.USR_ID) AS usr_tot_cnt
		FROM
		    lms_lrm.cm_opt_txb cot2
		INNER JOIN lms_lrm.cm_usr cu
			ON cot2.CLA_ID = cu.CLA_ID
				AND cu.USR_TP_CD = 'ST'
		INNER JOIN lms_lrm.tl_sbc_lrn_nod_rcstn tslnr
			ON tslnr.OPT_TXB_ID = cot2.OPT_TXB_ID
				AND tslnr.LRMP_NOD_ID = #{lluNodId}
		INNER JOIN lms_lrm.tl_lu_lrnr_lv tlll
			ON tlll.OPT_TXB_ID = tslnr.OPT_TXB_ID
				AND tlll.USR_ID = cu.USR_ID
				AND tlll.LLU_NOD_ID  = tslnr.LLU_NOD_ID
		WHERE cot2.OPT_TXB_ID = #{optTxbId}
		/* 전체 학급 관리 - 오윤영 - AllClaMgTcr-Mapper.xml - selectLearnerLevel - 전체 학급 관리 > 통계 조회 > 학습자 수준 분포 */
	</select>

	<select id="claInfoList" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		SELECT
			cot2.OPT_TXB_ID AS opt_txb_id,
			cc.CLA_ID AS cla_id,
			cc.CLA_NM AS cla_nm,
			cu.USR_ID AS tcr_usr_id
		FROM lms_lrm.cm_usr cu
		LEFT JOIN lms_lrm.cm_opt_tcr cot2
			ON cot2.TCR_USR_ID = cu.USR_ID
		LEFT JOIN lms_lrm.cm_opt_txb cot3
			ON cot3.OPT_TXB_ID = cot2.OPT_TXB_ID
		LEFT JOIN lms_lrm.cm_cla cc
			ON cc.CLA_ID = cot3.CLA_ID
		INNER JOIN lms_lrm.cm_usr cu2
			ON cu2.CLA_ID = cc.CLA_ID
			AND cu2.USR_TP_CD = 'ST'
		WHERE cu.KERIS_USR_ID = #{tcrUsrId}
		  AND cot3.TXB_ID = #{txbId}
		GROUP BY cot3.OPT_TXB_ID, cc.cla_id , cu.USR_ID
		ORDER BY cot3.OPT_TXB_ID, cc.cla_id , cu.USR_ID
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - claInfoList - 전체 학급 관리 선생님 별 담당 전체 학급 조회 */
	</select>

	<select id="selectTlRatioByCla" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		SELECT
			a.opt_txb_id,
			a.llu_nod_id,
			MAX(a.LRMP_NOD_NM) as lrmp_nod_nm,
			((IFNULL(a.tl_cl_cnt, 0) + IFNULL(b.tcr_cl_cnt, 0)) / (IFNULL(a.tl_tot_cnt, 0) + IFNULL(b.tcr_tot_cnt, 0))) * 100 as tl_ratio
		FROM
			(SELECT
				 R.OPT_TXB_ID
				,MAX(R.LRMP_NOD_ID) as llu_nod_id
				,MAX(B.lrmp_nod_id) as b_nod_id
				,MAX(R.LRMP_NOD_NM) as lrmp_nod_nm
				,SUM(IF(D.LRN_ST_CD = 'CL', 1, 0)) AS TL_CL_CNT									 /* 학습완료건수 */
				,COUNT(1)                          AS TL_TOT_CNT
			FROM
			    LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  												 /* TL_교과학습노드재구성(대단원) */
			INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN T  										 /* TL_교과학습노드재구성(차시) */
				ON R.OPT_TXB_ID = T.OPT_TXB_ID
					AND R.LRMP_NOD_ID = T.LLU_NOD_ID
					AND T.USE_YN = 'Y'
			INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN A 										 /* TL_교과학습활동재구성 */
				ON R.OPT_TXB_ID = A.OPT_TXB_ID
					AND T.LRMP_NOD_ID = A.LRMP_NOD_ID
			INNER JOIN LMS_CMS.BC_LRN_STP B 													 /* BC_학습단계 */
				ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
					AND A.LRN_STP_ID = B.LRN_STP_ID
					AND B.LRN_STP_DV_CD = 'CL'													 /* CL=개념, WB=익힘, EX=평가*/
					AND B.DEL_YN = 'N'
			LEFT JOIN LMS_LRM.CM_USR C														 /* 사용자정보 */
				ON C.USR_TP_CD = 'ST'
					AND C.CLA_ID = #{claId}
			LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D											 /* TL_교과학습활동상태 */
				ON A.OPT_TXB_ID = D.OPT_TXB_ID
					AND B.LRMP_NOD_ID = D.LRMP_NOD_ID
					AND A.LRN_ATV_ID = D.LRN_ATV_ID
					AND C.USR_ID = D.LRN_USR_ID
			WHERE 1=1
				AND R.OPT_TXB_ID = #{optTxbId}
			    AND R.LRMP_NOD_ID = #{lluNodId}
			GROUP BY R.opt_txb_id, R.LRMP_NOD_ID
			) a
		LEFT JOIN
			(SELECT
				ttrcm.opt_txb_id,
				ttrcm.lrmp_nod_id,
				SUM(if(ttrcs.lrn_st_cd = 'CL', 1,0)) as tcr_cl_cnt,
				COUNT(1) AS tcr_tot_cnt
			FROM
			    lms_lrm.tl_tcr_reg_ctn_mpn ttrcm
			INNER JOIN lms_lrm.tl_tcr_reg_ctn ttrc
				ON ttrc.TCR_REG_CTN_ID = ttrcm.TCR_REG_CTN_ID
					AND ttrc.TCR_USR_ID = #{tcrUsrId}
			LEFT JOIN LMS_LRM.CM_USR C
				ON C.USR_TP_CD = 'ST'
					AND C.CLA_ID = #{claId}
			LEFT JOIN lms_lrm.tl_tcr_reg_ctn_st ttrcs
				ON ttrcs.OPT_TXB_ID  = ttrcm.OPT_TXB_ID
					AND ttrcs.TCR_REG_CTN_ID = ttrc.TCR_REG_CTN_ID
					AND ttrcs.lrn_usr_id = C.usr_id
			WHERE 1=1
				AND ttrcm.opt_txb_id = #{optTxbId}
				AND ttrcm.use_yn = 'Y'
			    AND ttrcm.del_yn = 'N'
			GROUP BY ttrcm.opt_txb_id, ttrcm.lrmp_nod_id
			) b
			ON b.lrmp_nod_id = a.b_nod_id
		GROUP BY a.opt_txb_id, a.llu_nod_id
		ORDER BY a.opt_txb_id, a.llu_nod_id
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - selectTlRatioByCla - 전체 학급 관리 > 교과학습 진도율 */
	</select>

	<select id="selectAlMtRatioByCla" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		select
			a.urnk_kmmp_nod_id as llu_kmmp_nod_id
			 ,sum(CASE WHEN a.y_cnt = a.urnk_total_cnt THEN 1 ELSE 0 END) as y_total
			 ,sum(CASE WHEN a.y_cnt = a.urnk_total_cnt THEN 1 ELSE 0 END) / MAX(b.usr_total)  * 100 AS ai_ratio  						   /* 제출한 사람 기준으로 완료갯수 카운트 */
			 ,MAX(b.usr_total) as usr_tot_cnt
		FROM
			(SELECT
				 aknr2.urnk_kmmp_nod_id
				  ,MAX(aknr2.kmmp_nod_nm) as kmmp_nod_nm
				  ,MAX(aknr2.kmmp_nod_id) as aknr2_nod_id
				  , MAX(aknr2.opt_txb_id)
				  , MAX(eaetr.ev_id)
				  ,eer.usr_id
				  ,count(eer.usr_id) as usr_tot_cnt
				  ,MAX(cu.cla_id) as cla_id
				  ,(SELECT COUNT(*) FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR WHERE DPTH = 2 AND OPT_TXB_ID = #{optTxbId}
																		  and urnk_kmmp_nod_id = aknr2.urnk_kmmp_nod_id AND DEL_YN = 'N') AS urnk_total_cnt 										/* 중단원 총 갯수*/
				  ,CASE
					   WHEN SUM(CASE WHEN eaetr.luev_cmpl_yn = 'Y' THEN 1 ELSE 0 END) = COUNT(aknr2.kmmp_nod_id) 								/* 중단원 갯수와 완료 갯수가 같을 때만 진행 */
						   THEN count(distinct aknr2.kmmp_nod_id) 																					/* 중단원 id 카운트*/
					   ELSE 0
					END AS y_cnt
			 FROM LMS_LRM.AI_KMMP_NOD_RCSTN AKNR2
					  left JOIN lms_lrm.ea_ai_ev_ts_rnge eaetr
								ON eaetr.MLU_KMMP_NOD_ID = aknr2.kmmp_nod_id
									AND eaetr.opt_txb_id = aknr2.opt_txb_id
									and eaetr.AI_TS_RNGE_SEQ_NO = 1
					  left join lms_lrm.ea_ev ee
								ON ee.opt_txb_id = eaetr.opt_txb_id
									AND ee.ev_id = eaetr.ev_id
									AND ee.EV_DV_CD = 'AE'
									AND NOT ee.ev_dtl_dv_cd = 'OV'
					  LEFT JOIN lms_lrm.cm_opt_txb cot2
								ON cot2.opt_txb_id = ee.opt_txb_id
					  LEFT JOIN lms_lrm.cm_cla cc
								ON cc.cla_id = cot2.cla_id
					  INNER JOIN lms_lrm.cm_usr cu
								 ON cu.cla_id = cc.cla_id
									 AND cu.usr_tp_cd = 'ST'
					  inner JOIN lms_lrm.ea_ev_rs eer
								 ON eer.ev_id = ee.ev_id
									 AND eer.usr_id = cu.usr_id
			 WHERE AKNR2.opt_txb_id = #{optTxbId}
			   and AKNR2.dpth = 2
			   and aknr2.urnk_kmmp_nod_id = #{urnkKmmpNodId}
			 group by aknr2.urnk_kmmp_nod_id, eer.usr_id
			 order by aknr2.urnk_kmmp_nod_id, eer.usr_id
			) a
				left join (
				select
					count(distinct cu.usr_id) as usr_total,
					cu.cla_id
				from lms_lrm.cm_usr cu
				where cu.usr_tp_cd = 'ST'
				  and cu.cla_id = #{claId}
			)b
						  on b.cla_id = a.cla_id
		group by a.urnk_kmmp_nod_id;
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - selectAlMtRatioByCla - 전체 학급 관리 > AI학습 수학 진도율 */
	</select>

	<select id="selectAeCntBykmmpNodId" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		WITH RECURSIVE dpth_check AS (
			SELECT
				aknr.opt_txb_id,
				aknr.KMMP_NOD_ID,
				bkn.urnk_kmmp_nod_id ,
				bkn.kmmp_id,
				bkn.dpth,
				aknr.kmmp_nod_id AS group_id
			FROM
			    lms_lrm.ai_kmmp_nod_rcstn aknr
			INNER JOIN lms_lrm.cm_opt_txb cot2
				ON cot2.opt_txb_id = aknr.opt_txb_id
			INNER JOIN lms_cms.bc_txb bt
				ON bt.txb_id = cot2.txb_id
			INNER JOIN lms_cms.bc_kmmp bk
				ON bk.txb_id = bt.txb_id
			INNER JOIN lms_cms.bc_kmmp_nod bkn
				ON bkn.kmmp_id = bk.kmmp_id
					AND bkn.kmmp_nod_id = aknr.kmmp_nod_id
					AND bkn.LU_EPS_YN = 'Y'
			WHERE 1=1
				AND aknr.opt_txb_id =  #{optTxbId}
				AND bkn.dpth = 1
				AND aknr.kmmp_nod_id = #{urnkKmmpNodId}
			UNION ALL
			SELECT
				aknr.opt_txb_id,
				aknr.KMMP_NOD_ID,
				aknr.URNK_KMMP_NOD_ID ,
				aknr.kmmp_id,
				aknr.dpth,
				dc.group_id
			FROM
			    lms_lrm.ai_kmmp_nod_rcstn aknr
			INNER JOIN dpth_check dc
				ON aknr.urnk_kmmp_nod_id = dc.kmmp_nod_id
			WHERE 1=1
				AND aknr.dpth <![CDATA[<=]]> 4
				AND aknr.opt_txb_id = #{optTxbId}
		)
		SELECT
			MAX(dc.opt_txb_id),
			dc.group_id AS llu_kmmp_nod_id,
			COUNT(dc.group_id) AS ae_cnt
		FROM
		    dpth_check dc
		WHERE dc.dpth = 4
		GROUP BY group_id, dc.dpth
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - selectAeCntBykmmpNodId - 전체 학급 관리 > AI학습 4뎁스 연결 된 학습 갯수 조회 */
	</select>

	<select id="selectAlEnRatioByCla" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		SELECT
			a.MLU_KMMP_NOD_ID,
			a.llu_kmmp_nod_id,
			SUM(CASE WHEN a.y_cnt = #{aeCnt} THEN 1 ELSE 0 END) AS y_total,			 		 /* aeCnt 개를 다 끝내야만 한 단원 학습완료*/
			a.usr_tot_cnt AS usr_count,														 	 /* 전체 학급 유저 카운트*/
			CASE
			    WHEN COUNT(DISTINCT a.USR_ID) > 0  									     /* usr_id 갯수가 0보다 많을 때만 진행*/
				THEN COALESCE((SUM(CASE WHEN a.y_cnt = #{aeCnt} THEN 1 ELSE 0 END) * 100.0 / a.usr_tot_cnt), 0)
				ELSE 0
			END AS ai_ratio
		FROM
		    (SELECT
				eaetr.MLU_KMMP_NOD_ID,
				eaetr.llu_kmmp_nod_id,
				MAX(eaetr.LLU_KMMP_NOD_NM) as llu_kmmp_nod_nm,
				b.USR_ID,
				(
					SELECT
						 COUNT(DISTINCT cu.usr_id)
					FROM
						lms_lrm.cm_usr cu
					WHERE 1=1
						AND cu.usr_tp_cd = 'ST'
						AND cu.cla_id = b.cla_id
				) AS usr_tot_cnt,
				COUNT(DISTINCT CASE WHEN eaetr.LUEV_CMPL_YN = 'Y' THEN eaetr.tc_kmmp_nod_id END) AS y_cnt
			FROM
			    lms_lrm.ea_ev ee
			LEFT JOIN
				(
					SELECT
						cot2.opt_txb_id,
						cu.usr_id,
						cc.cla_id
					FROM
					    lms_lrm.cm_opt_txb cot2
					INNER JOIN lms_lrm.cm_cla cc
						ON cc.cla_id = cot2.cla_id
					INNER JOIN lms_lrm.cm_usr cu
						ON cu.cla_id = cc.cla_id
							AND cu.usr_tp_cd = 'ST'
					WHERE cot2.opt_txb_id = #{optTxbId}
				) b
				ON b.opt_txb_id = ee.opt_txb_id
				INNER JOIN lms_lrm.ea_ev_rs eer
					ON eer.EV_ID = ee.EV_ID
						AND eer.usr_id = b.usr_id
				INNER JOIN lms_lrm.ea_ai_ev_ts_rnge eaetr
						ON eaetr.OPT_TXB_ID = ee.opt_txb_id
							AND eaetr.EV_ID = ee.EV_ID
			    WHERE 1=1
					AND ee.opt_txb_id = #{optTxbId}
					AND ee.EV_DV_CD = 'AE'
					AND NOT ee.ev_dtl_dv_cd = 'OV'
					AND eaetr.mlu_kmmp_nod_id = #{kmmpNodId}
				GROUP BY eaetr.MLU_KMMP_NOD_ID, eer.USR_ID,  eaetr.llu_kmmp_nod_id
			 ) a
		GROUP BY a.MLU_KMMP_NOD_ID,  a.llu_kmmp_nod_id
		ORDER BY a.MLU_KMMP_NOD_ID,  a.llu_kmmp_nod_id;
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - selectAlEnRatioByCla - 전체 학급 관리 > AI학습 영어 진도율 */
	</select>

	<select id="selectSbjCd" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="String" >
		SELECT
		    bt.SBJ_CD
		FROM
			lms_lrm.cm_opt_txb cot2
		INNER JOIN lms_cms.bc_txb bt
			ON bt.TXB_ID = cot2.TXB_ID
		WHERE OPT_TXB_ID = #{optTxbId}
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - selectSbjCd - 전체 학급 관리 > 과목 코드확인 */
	</select>

	<select id="selectKmmpNodId" parameterType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto" resultType="com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto">
		SELECT
			MAX(aknr.KMMP_NOD_ID) AS kmmp_nod_id,
			aknr.urnk_kmmp_nod_Id AS urnk_kmmp_nod_Id
		FROM
		    lms_cms.BC_LRMP_KMMP_NOD_MPN blknm
		INNER JOIN lms_lrm.ai_kmmp_nod_rcstn aknr
			ON aknr.KMMP_NOD_ID  = blknm.KMMP_NOD_ID
		WHERE 1=1
			AND blknm.LRMP_NOD_ID = #{lluNodId}
			AND aknr.OPT_TXB_ID = #{optTxbId}
			AND aknr.DPTH =2
		GROUP BY aknr.urnk_kmmp_nod_Id
		/* 전체 학급 관리 - 정은혜 - AllClaMgTcr-Mapper.xml - selectKmmpNodId - 전체 학급 관리 >  학습맵 대단원id ---> 지식맵 노드id 변환 */
	</select>
</mapper>