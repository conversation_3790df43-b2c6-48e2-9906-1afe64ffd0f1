<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.back.lrnw">
	
	<!-- 학습도구 목록조회 -->
    <select id="selectLrnTlList" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto" resultType="com.aidt.api.al.pl.dto.AlLrnwLrnTlDto">
        SELECT
               A.TXB_ID                                               /* 교과서ID*/
              ,C.LRN_TL_ID                                            /* 학습도구ID */
              ,C.LRN_TL_CTM_ALS_NM                 AS LRN_TL_KN_NM    /* 학습도구 별명 */
              ,D.LRN_TL_KN_CD                                         /* 학습도구종류코드 */
              ,C1.CM_CD_NM                         AS LRN_TL_TP_NM    /* 학습도구유형 */
              ,D.LRN_TL_TP_CD                                         /* 학습도구유형코드 WT=필기도구, TT=교과도구, MT=소통도구, CT=수업도구 */
        FROM LMS_LRM.CM_OPT_TXB A /* CM_운영교과서 */
             LEFT JOIN LMS_CMS.BC_TXB_LRNW B /* BC_교과서학습창 */
                ON A.TXB_ID = B.TXB_ID
             LEFT JOIN LMS_CMS.BC_TXB_LRN_TL_MPN C /* BC_교과서학습도구매핑 */
                 ON B.LRNW_ID =C.LRNW_ID
             LEFT JOIN LMS_CMS.BC_LRN_TL D /* BC_학습도구 */
                ON C.LRN_TL_ID = D.LRN_TL_ID 
            LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
               ON C1.URNK_CM_CD = 'LRN_TL_TP_CD'
                AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
                AND C1.LMS_USE_YN = 'Y'
                AND C1.DEL_YN = 'N'
                AND C1.CM_CD = D.LRN_TL_TP_CD
             LEFT JOIN LMS_LRM.CM_CM_CD C2 /* CM_공통코드 */
               ON C1.URNK_CM_CD = C2.CM_CD
                AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                AND C2.LMS_USE_YN = 'Y'
                AND C2.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND B.LRN_TP_CD = 'AL'
        <if test='usrDvCd == "T"'>
            AND B.LRNW_USR_TP_CD = 'TE'
        </if>
        <if test='usrDvCd != "T"'>
            AND B.LRNW_USR_TP_CD = 'ST'
        </if>
        AND C.LRN_TL_ID IS NOT NULL
        ORDER BY C1.SRT_ORDN ASC
        /* AI맞춤 학습도구조회 - 이혜인 -  AlLrnw-Mapper.xml - selectLrnTlList */
    </select>
    
    <!-- 학습활동 조회 -->
    <select id="selectLrnStpMeta" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto" resultType="HashMap">
		SELECT
			BALAC.CTN_CD
		  , BALAC.AI_LRN_ATV_ID
		  , BALAC.LRN_ATV_NM
		  , BALAC.CTN_TP_CD
		  , BALAC.SRT_ORDN
		  , BACMD.AI_CTN_META_DATA_ID
		  , IFNULL(QQ.QP_DFFD_CD, '') AS CTN_DFFD_DV_CD
		  , BACMD.CDN_PTH_NM, BACMD.LRN_RCNT_TM_SCNT
		  , IFNULL(BACMD.LRN_CMPL_CHK_BS_CD, '') AS LRN_CMPL_CHK_BS_CD
		  , BACMD.LRN_ATV_TTL_QTM_CNT
		  , IFNULL(BACMD.OCR_USE_YN, '') AS OCR_USE_YN
		  , IFNULL(BACMD.MFRM_USE_YN, '') AS MFRM_USE_YN
		  , DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID
		  , DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID
		  , DPTH3.KMMP_NOD_ID AS SLU_KMMP_NOD_ID
		  , DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID
		  , DPTH5.KMMP_NOD_ID AS TPC_KMMP_NOD_ID
		  , IFNULL(DPTH5.RCSTN_ORDN, '') AS RCSTN_ORDN
		  , DPTH1.KMMP_NOD_NM AS LLU_KMMP_NOD_NM
		  , DPTH2.KMMP_NOD_NM AS MLU_KMMP_NOD_NM
		  , DPTH3.KMMP_NOD_NM AS SLU_KMMP_NOD_NM
		  , DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM
		  , DPTH5.KMMP_NOD_NM AS TPC_KMMP_NOD_NM
		  , BT.TXB_ID
		  , BT.AUTR_CD
		  , BT.AUTR_NM
		  , BT.SBJ_CD
		  , BT.SCHL_GRD_CD
		  , BT.SGY_CD
		FROM
		    LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
					ON DPTH1.KMMP_NOD_ID = DPTH2.URNK_KMMP_NOD_ID
					AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
					AND DPTH2.DPTH = 2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH2.KMMP_NOD_ID = DPTH3.URNK_KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
					AND DPTH3.DPTH = 3
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON DPTH3.KMMP_NOD_ID = DPTH4.URNK_KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
					AND DPTH4.DPTH = 4
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH5
					ON DPTH4.KMMP_NOD_ID = DPTH5.URNK_KMMP_NOD_ID
					AND DPTH4.OPT_TXB_ID = DPTH5.OPT_TXB_ID
					AND DPTH5.DPTH = 5
				INNER JOIN LMS_LRM.CM_OPT_TXB COT2
					ON DPTH1.OPT_TXB_ID = COT2.OPT_TXB_ID
				INNER JOIN LMS_CMS.BC_TXB BT
					ON COT2.TXB_ID = BT.TXB_ID
				INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
					ON DPTH5.KMMP_NOD_ID = BALAC.KMMP_NOD_ID
				LEFT OUTER JOIN LMS_CMS.QP_QTM QQ
					ON BALAC.CTN_CD = QQ.QP_QTM_ID
				LEFT OUTER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD
					ON BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID
		<!-- AND BALAC.CTN_CD = BACMD.CTN_CD -->
		WHERE
		    DPTH1.OPT_TXB_ID = #{optTxbId}
			AND DPTH1.DPTH = 1
			AND BALAC.AI_LRN_ATV_ID = #{lrnAtvId}
			AND (BALAC.CTN_TP_CD = 'PL' or BALAC.CTN_TP_CD = 'HT')
		/* AI맞춤 컨텐츠 메타데이터 조회 - 이혜인 -  AlLrnw-Mapper.xml - selectLrnStpMeta */
    </select>
    
    <insert id="updateAiLrnAtvSt" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto">
    	INSERT INTO LMS_LRM.AI_LRN_ATV_ST
			 ( OPT_TXB_ID, KMMP_NOD_ID, LRN_ATV_ID, LRN_USR_ID, LRN_ST_CD
			 , LRN_TM_SCNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{optTxbId}
			 , #{kmmpNodId}
			 , #{lrnAtvId}
			 , #{usrId}
			 , #{lrnStCd}
			 , #{lrnTmScnt}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		 ON DUPLICATE KEY UPDATE
		   LRN_ST_CD = #{lrnStCd}
		 , LRN_TM_SCNT = #{lrnTmScnt}
		 /* AI 학습활동 저장 - 이혜인 -  AlLrnw-Mapper.xml - updateAiLrnAtvSt */
    </insert>
    
    
    <select id="selectQtmInfo" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			EER.USR_ID
		  , EEQ.TPC_ID AS TPC_KMMP_NOD_ID
		  , NOD5.KMMP_NOD_NM AS TPC_KMMP_NOD_NM
		  , EEQ.QTM_DFFD_DV_CD AS CTN_DFFD_DV_CD
		  , AUTP.AI_PRED_AVG_CANS_RT
		  , AUTP.AI_PRED_AVG_SCR
		  , AUTP.TPC_AVN
		  , EEQ.QTM_ID
		  , AUQP.AI_PRED_CANS_RT
		FROM
		    LMS_LRM.EA_EV EE
				INNER JOIN LMS_LRM.EA_EV_RS EER
				    ON EE.EV_ID = EER.EV_ID
				INNER JOIN LMS_LRM.EA_EV_QTM EEQ
				    ON EE.EV_ID = EEQ.EV_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN NOD5
				    ON EEQ.TPC_ID = NOD5.KMMP_NOD_ID
				    AND EE.OPT_TXB_ID = NOD5.OPT_TXB_ID
				LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP
				    ON EEQ.TPC_ID = AUTP.TPC_ID
				    AND EER.USR_ID = AUTP.USR_ID
				    AND EE.OPT_TXB_ID = AUTP.OPT_TXB_ID
				LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
				    ON EEQ.QTM_ID = AUQP.QTM_ID
				    AND EER.USR_ID = AUQP.USR_ID
				    AND EE.OPT_TXB_ID = AUQP.OPT_TXB_ID
		WHERE
			EE.EV_ID = #{evId}
		    AND EEQ.QTM_ID = #{qtmId}
		    AND EER.USR_ID = #{usrId}
		    AND EE.OPT_TXB_ID = #{optTxbId}
		LIMIT 1
		/* AI 학습창 내 문항별 정보조회(임시 테스트용) - 이혜인 -  AlLrnw-Mapper.xml - selectQtmInfo */
    </select>
    
    
    <select id="selectTxbInfo" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
    	SELECT SBJ_CD, SCHL_GRD_CD FROM LMS_CMS.BC_TXB BT WHERE TXB_ID = #{txbId}
    	/* 과목조회 - 이혜인 -  AlLrnw-Mapper.xml - selectTxbInfo */
    </select>
    
    <select id="selectMluKmmpNodId" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
    	SELECT EAETR.MLU_KMMP_NOD_ID, EAETR.TC_KMMP_NOD_ID, EAETR.TPC_KMMP_NOD_ID, EE.EV_DTL_DV_CD
    	FROM LMS_LRM.EA_EV EE
    	INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID 
    	WHERE EE.EV_ID = #{evId}
    	LIMIT 1
    	/* 단원정보 조회 - 이혜인 -  AlLrnw-Mapper.xml - selectMluKmmpNodId */
    </select>
    
    <select id="selectAllEvListForLu" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			EE.EV_ID
		  , MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD
		  , MAX(EE.EV_NM) AS EV_NM
		  , IFNULL(MAX(EAETR.LUEV_CMPL_YN), 'N') AS LUEV_CMPL_YN
		  , MAX(EAETR.MLU_KMMP_NOD_NM) AS MLU_KMMP_NOD_NM
		  , MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM
		  , MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID
		  , MAX(EAETR.TPC_KMMP_NOD_NM) AS TPC_KMMP_NOD_NM
		  , EEQ.QTM_ID
		  , EEQ.QTM_ORDN
		  , MAX(EEQ.QP_DFFD_NM) AS QP_DFFD_NM
		  , MAX(EEQA.SMT_ANW_VL) AS SMT_ANW_VL
		  , MAX(EEQA.CANS_YN) AS CANS_YN
		  , MAX(EEQA.XPL_TM_SCNT) AS XPL_TM_SCNT
		  , IFNULL(MAX(AUQP.AI_PRED_CANS_RT), 0.5) AS AI_PRED_CANS_RT
		FROM
		    LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER
			    ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			    ON EE.EV_ID = EAETR.EV_ID
			    AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
			INNER JOIN LMS_LRM.EA_EV_QTM EEQ
			    ON EE.EV_ID = EEQ.EV_ID
			    AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA
			    ON EE.EV_ID = EEQA.EV_ID
			    AND EE.USR_ID = EEQA.USR_ID
			LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP
			    ON EEQ.QTM_ID = AUQP.QTM_ID
			    AND EER.USR_ID = AUQP.USR_ID
			    AND EE.OPT_TXB_ID = auqp.OPT_TXB_ID
		WHERE
		    EER.USR_ID = #{usrId}
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EE.EV_DV_CD = 'AE'
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			<if test = 'tpcKmmpNodId != null and !tpcKmmpNodId.equals("")
				and sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
				AND EAETR.TPC_KMMP_NOD_ID = #{tpcKmmpNodId}
			</if>
		GROUP BY EE.EV_ID, EEQ.QTM_ID, EEQ.QTM_ORDN
		ORDER BY EE.EV_ID, EEQ.QTM_ORDN
		/* 단원 내 모든 평가리스트 조회 - 이혜인 -  AlLrnw-Mapper.xml - selectAllEvListForLu */
    </select>
    
    <select id="selectLastEvInfo" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
    	SELECT EE.EV_ID, EE.EV_DTL_DV_CD, EE.EV_NM
    		 , IFNULL(EAETR.LUEV_CMPL_YN, 'N') AS LUEV_CMPL_YN
			 , IFNULL(EER.EV_CMPL_YN, 'N') AS EV_CMPL_YN
			 , EAETR.MLU_KMMP_NOD_NM, EAETR.TC_KMMP_NOD_NM, EAETR.TPC_KMMP_NOD_ID, EAETR.TPC_KMMP_NOD_NM
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE EER.USR_ID = #{usrId}
		AND EE.OPT_TXB_ID = #{optTxbId}
		AND EE.EV_DV_CD = 'AE'
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		ORDER BY EE.EV_ID desc
 		LIMIT 1
 		/* 단원 내 마지막 평가 확인 - 이혜인 -  AlLrnw-Mapper.xml - selectLastEvInfo */
    </select>
    
    <select id="selectMluKmmpNodNm" parameterType="String"  resultType="String">
    	SELECT BKN.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD BKN WHERE BKN.KMMP_NOD_ID = #{mluKmmpNodId}
    </select>
    
    <insert id="insertEaEvQtmAnw" parameterType="com.aidt.api.al.pl.dto.AlLrnwHtmlTypeReqDto">
   		INSERT INTO LMS_LRM.EA_EV_QTM_ANW
			 ( EV_ID, QTM_ID, USR_ID, SMT_ANW_VL, CANS_YN, XPL_TM_SCNT
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		VALUES
			 ( #{evId}
			 , #{qtmId}
			 , #{usrId}
			 , #{smtAnwVl}
			 , #{cansYn}
			 , #{xplTmScnt}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId} )
		 ON DUPLICATE KEY UPDATE
		 	   CANS_YN = #{cansYn}
		 	 , XPL_TM_SCNT = #{xplTmScnt}
		 	 , SMT_ANW_VL = #{smtAnwVl}
    </insert>
    
    
    <select id="selectLuCansCnt" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto"  resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(EE.EV_ID) AS EV_ID,
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
			MAX(EE.EV_NM) AS EV_NM,
			IFNULL(COUNT(EEQ.QTM_ID), 0) AS QTM_CNT,
			IFNULL(SUM(EEQA.CANS_YN = 'Y'), 0) AS CANS_Y_CNT
		FROM LMS_LRM.EA_EV EE
		INNER JOIN LMS_LRM.EA_EV_RS EER ON EE.EV_ID = EER.EV_ID
		INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR ON EE.EV_ID = EAETR.EV_ID AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON EE.EV_ID = EEQ.EV_ID AND EAETR.TPC_KMMP_NOD_ID = EEQ.TPC_ID 
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EE.EV_ID = EEQA.EV_ID AND EEQ.QTM_ID = EEQA.QTM_ID AND EER.USR_ID = EEQA.USR_ID
		WHERE EE.OPT_TXB_ID = #{optTxbId}
		AND EER.USR_ID = #{usrId}
		AND EE.EV_DV_CD = 'AE'
		AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
		AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
		/* 단원 내 정답수 조회 - 이혜인 -  AlLrnw-Mapper.xml - selectLuCansCnt */
    </select>
    
    <select id="selectVdAtvInfo" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto" resultType="HashMap">
    	SELECT BALAC.AI_LRN_ATV_ID AS LRN_ATV_ID
			 , BACMD.CTN_TP_CD
			 , BACMD.CDN_PTH_NM
		FROM LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
		left OUTER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD 
		ON BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID
		<!-- AND BALAC.CTN_CD = BACMD.CTN_CD -->
		WHERE BALAC.AI_LRN_ATV_ID = #{lrnAtvId}
		AND (BACMD.CTN_TP_CD = 'PL' or BACMD.CTN_TP_CD = 'HT')
    </select>
    <select id="selectVdAtvInfoTL" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto" resultType="HashMap">
			SELECT  ATV.LRMP_NOD_ID
				  , ATV.LRN_ATV_ID
				  , ATV.CTN_TP_CD
				  , CTN.CDN_PTH_NM
				  , CTN.CTN_NM
		    FROM LMS_CMS.BC_LRN_ATV_CTN ATV
		    JOIN LMS_CMS.BC_CTN_MTD CTN ON CTN.LRN_ATV_ID = ATV.LRN_ATV_ID 
		    WHERE ATV.LRN_ATV_ID = #{lrnAtvId}
		    AND ATV.CTN_TP_CD  IN ('PL','HT')
    </select>
    
     <!-- 학습활동 조회 -->
    <select id="selectLrnStpMetaPl" parameterType="com.aidt.api.al.pl.dto.AlLrnwReqDto" resultType="HashMap">
    	select BT.TXB_ID, BT.AUTR_CD, BT.AUTR_NM, BT.SBJ_CD, BT.SCHL_GRD_CD, BT.SGY_CD
		from LMS_LRM.CM_OPT_TXB COT2 
		INNER JOIN LMS_CMS.BC_TXB BT ON COT2.TXB_ID = BT.TXB_ID
		where cot2.OPT_TXB_ID = #{optTxbId}
		/* AI맞춤 이전학교급 컨텐츠 메타데이터 조회 - 김현혜 -  AlLrnw-Mapper.xml - selectLrnStpMeta */
    </select>
    
    
</mapper>