package com.aidt.api.ea.grpmgmt.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtAllnfoDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtSaveDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtStuListDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 모둠관리[EaGrpmgmtTcr]", description="모둠관리")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/ea/tcr/grpmgmt")
public class EaGrpMgmtTcrController {

    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private EaGrpMgmtTcrService eaGrpMgmtTcrService;

    @Operation(summary="모둠 관리 학급 학생 리스트", description="학습의 학생 리스트를 가져온다.")
    @PostMapping(value = "/selectStuList")
    public ResponseDto<List<EaGrpMgmtStuListDto>> selectStuList(@Valid @RequestBody EaGrpMgmtGrpSrhDto srhDto) {
        log.debug("Entrance selectStuList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());

        return Response.ok(eaGrpMgmtTcrService.selectStuList((srhDto)));
    }

    @Operation(summary="모둠 관리 학급 모둠 리스트", description="모둠 관리 학급 모둠 리스트.")
    @PostMapping(value = "/selectGroupList")
    public ResponseDto<List<EaGrpMgmtGrpDto>> selectGroupList(@Valid @RequestBody EaGrpMgmtGrpSrhDto srhDto) {
        log.debug("Entrance selectGroupList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        srhDto.setClaId(userDetails.getClaId());
        
        return Response.ok(eaGrpMgmtTcrService.selectGroupList((srhDto)));
    }
    
	@Tag(name="[ea] 모둠 관리 데이터 저장", description="모둠 관리 데이터 저장")
	@PostMapping(value = "/saveMainData",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> saveMainData(@Valid @RequestBody EaGrpMgmtSaveDto saveDto) {
		log.debug("Entrance saveMainData");
		
		
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		saveDto.setUsrId(userDetails.getUsrId());
		saveDto.setOptTxbId(userDetails.getOptTxbId());
		saveDto.setDbId(userDetails.getTxbId());
		return Response.ok(eaGrpMgmtTcrService.saveMainData(saveDto));
	}

	@Tag(name="[ea] 모둠 데이터 삭제", description="단건 삭제")
	@DeleteMapping(value = "/deleteGrpData",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> deleteGrpData(@Valid @RequestBody EaGrpMgmtGrpSrhDto deleteDto) {
		log.debug("Entrance deleteGrpData");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		deleteDto.setUsrId(userDetails.getUsrId());	 //사용자 ID
		return Response.ok(eaGrpMgmtTcrService.deleteGrpData(deleteDto));
	}

    @Operation(summary="모둠 관리 팀, 학생정보 리스트", description="모둠 관리 학급 모둠 리스트.")
    @PostMapping(value = "/selectTeamStudentList")
    public ResponseDto<EaGrpMgmtAllnfoDto> selectTeamStudentList(@Valid @RequestBody EaGrpMgmtGrpSrhDto srhDto) {
        log.debug("Entrance selectTeamStudentList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());

        return Response.ok(eaGrpMgmtTcrService.selectTeamStudentList((srhDto)));
    }
    
    @Operation(summary="모둠 리스트 전체 목록", description="모둠 리스트 전체 목록.")
    @PostMapping(value = "/selecrGrpNmAllList")
    public ResponseDto<List<Map<String,Object>>> selectGrpNmAllList(@Valid @RequestBody EaGrpMgmtGrpSrhDto srhDto) {
        log.debug("Entrance selecrGrpNmAllList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());

        return Response.ok(eaGrpMgmtTcrService.selecrGrpNmAllList((srhDto)));
    }
}
