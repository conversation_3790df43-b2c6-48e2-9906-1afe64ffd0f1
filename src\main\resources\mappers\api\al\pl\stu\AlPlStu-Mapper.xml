<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.stu.AlMluTcLstInqStu">
	
	
	<!-- 학생 중단원 목록 조회-->
	<select id="selectMluLstInqStu" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectMluLstInq */
		SELECT
			A.KMMP_NOD_ID AS KMMP_NOD_ID,
			MAX(A.KMMP_NOD_NM) AS KMMP_NOD_NM,
			MAX(A.TC_USE_YN) AS TC_USE_YN,
			MAX(A.<PERSON>_Y<PERSON>) AS USE_YN,
			MAX(A.LCKN_YN) AS LCKN_YN,
			MAX(AA.KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(AA.KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
			COALESCE(C.EV_DV_CD, '') AS EV_DV_CD,
			COALESCE(C.EV_DTL_DV_CD, '') AS EV_DTL_DV_CD,
			COALESCE(C.EV_CMPL_YN, 'N') AS EV_CMPL_YN,
			C.CANS_CNT AS CANS_CNT,
			C.EV_ID AS EV_ID,
			C.TPC_AVN AS TPC_AVN_STR,
			MAX(ALLV.LRNR_VEL_TP_CD) AS LRNR_VEL_TP_CD,
			C.MDF_DTM AS MDF_DTM
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN A
		LEFT JOIN (
			SELECT KMMP_ID, KMMP_NOD_ID, KMMP_NOD_NM
			FROM LMS_LRM.AI_KMMP_NOD_RCSTN
			WHERE dpth = 1
			AND OPT_TXB_ID = #{optTxbId}
		) AA
		ON A.KMMP_ID = AA.KMMP_ID
		AND A.URNK_KMMP_NOD_ID = AA.KMMP_NOD_ID
		LEFT JOIN (
			SELECT
				MAX(EV.EV_ID) AS EV_ID,
				EAETR.MLU_KMMP_NOD_ID,
				GROUP_CONCAT(
					CONCAT(
						IFNULL(AUTP.TPC_AVN, 0.5), ':',
						(SELECT MAX(KMMP_NOD_NM)
						FROM LMS_LRM.AI_KMMP_NOD_RCSTN
						WHERE KMMP_NOD_ID = AUTP.TPC_ID LIMIT 1)
					)
				) AS TPC_AVN,
				MAX(EV.EV_DV_CD) AS EV_DV_CD,
				EV.EV_DTL_DV_CD,
				IFNULL(MAX(RS.EV_CMPL_YN), 'N') AS EV_CMPL_YN,
				MAX(AI_TS_RNGE_SEQ_NO) AS AI_TS_RNGE_SEQ_NO,
				MAX(RS.CANS_CNT) AS CANS_CNT,
				MAX(RS.USR_ID) AS USR_ID,
				MAX(RS.MDF_DTM) AS MDF_DTM
			FROM LMS_LRM.EA_EV EV
			LEFT OUTER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			ON EV.EV_ID = EAETR.EV_ID
			LEFT OUTER JOIN (
				SELECT AUTP.TPC_ID, AUTP.TPC_AVN
				FROM LMS_LRM.AI_USRLY_TPC_PROF AUTP
				JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
				ON EAETR.TPC_KMMP_NOD_ID = AUTP.TPC_ID
				WHERE AUTP.USR_ID = #{usrId}
			)AUTP
			ON EAETR.TPC_KMMP_NOD_ID = AUTP.TPC_ID
			LEFT JOIN LMS_LRM.EA_EV_RS RS
			ON EV.EV_ID = RS.EV_ID
			WHERE EV.EV_DV_CD = 'AE' -- AI맞춤학습 고정
			AND RS.USR_ID = #{usrId}
			AND EV.EV_DTL_DV_CD IS NOT NULL
			GROUP BY EAETR.MLU_KMMP_NOD_ID, EV.EV_DTL_DV_CD
		) C ON A.KMMP_NOD_ID = C.MLU_KMMP_NOD_ID
		LEFT JOIN LMS_LRM.AI_LRNR_LV ALLV
		ON ALLV.USR_ID = C.USR_ID
		AND ALLV.LU_KMMP_NOD_ID = A.KMMP_NOD_ID
		WHERE A.DPTH = 2
		AND A.OPT_TXB_ID = #{optTxbId}
		AND A.DEL_YN = 'N'
		<if test = 'mkLuLrmNodId != null and !mkLuLrmNodId.equals("")'>
			AND A.KMMP_NOD_ID = #{mkLuLrmNodId}
		</if>
		GROUP BY A.KMMP_NOD_ID, C.EV_DV_CD, C.EV_DTL_DV_CD
		ORDER BY MAX(A.RCSTN_ORDN)
		
		/* AI맞춤 학생 중단원 목록 조회 - 김현혜 - ApPlStu-Mapper.xml - selectMluLstInqStu */
	</select>
	
	<!-- 학생 맞춤학습별 평균정답률 조회-->
	<select id="selectStpAvgCansRt" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectStpAvgCansRt */
		select 
			IFNULL(count(eeqa.qtm_id), 0) as qtmCnt,
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C1'), 0) as qtmC1Cnt,
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C1' and eeqa.CANS_YN = 'Y'), 0) as c1CansCnt, -- c1 맞춘갯수
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C2'), 0) as qtmC2Cnt,
			IFNULL(sum(ee.EV_DTL_DV_CD = 'C2' and eeqa.CANS_YN = 'Y'), 0) as c2CansCnt -- c2 맞춘갯수
		FROM lms_lrm.ai_kmmp_nod_rcstn aknr
		inner join lms_lrm.EA_AI_EV_TS_RNGE EAETR 
		on EAETR.MLU_KMMP_NOD_ID = aknr.KMMP_NOD_ID 
		inner join LMS_LRM.ea_ev EE
		on ee.ev_id = EAETR.ev_id
		inner join LMS_LRM.ea_ev_rs eer
		on eer.ev_id = ee.ev_id
		inner join LMS_LRM.ea_ev_qtm_anw eeqa
		on eeqa.ev_id = ee.ev_id
		JOIN LMS_CMS.BC_AI_CTN_META_DATA MD
		ON eeqa.QTM_ID = MD.CTN_CD
		where eer.usr_id = #{usrId}
		and aknr.kmmp_nod_id = #{mkLuLrmNodId}

		/* AI맞춤 학생 맞춤학습별 평균정답률 조회 - 김현혜 - ApPlStu-Mapper.xml - selectStpAvgCansRt */
	</select>
	
	<!-- 학생 맞춤학습별 토픽숙련도 조회-->
	<select id="selectQtmCansYnByTpcStu" parameterType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto" resultType="com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto">
		/** AlPlStu-Mapper.xml - selectQtmCansYnByTpcStu */
		SELECT
			MAX(ANW.USR_ID) AS USR_ID,
			MAX(QTM.TPC_ID) AS TPC_KMMP_NOD_ID,
			BALAC.CTN_CD AS QTM_ID,
			MAX(ANW.CANS_YN) AS CANS_YN,
			IFNULL(MAX(AUQP.TXM_PN), 0) AS TXM_PN,
			MAX(QQ.QP_DFFD_CD) AS CTN_DFFD_DV_CD,
			IFNULL(MAX(AUTP.AI_PRED_AVG_CANS_RT), 0) AS AI_PRED_AVG_CANS_RT,
			IFNULL(MAX(AUTP.AI_PRED_AVG_SCR), 0.5) AS AI_PRED_AVG_SCR
		FROM LMS_LRM.EA_EV_QTM QTM
		INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC <!-- 토픽에대한 모든문항 -->
		ON QTM.TPC_ID = BALAC.KMMP_NOD_ID
		AND BALAC.DEL_YN = 'N'
		INNER JOIN LMS_CMS.QP_QTM QQ 
		ON BALAC.CTN_CD = QQ.QP_QTM_ID
		INNER JOIN LMS_LRM.EA_EV_QTM_ANW ANW <!-- 문항에대한 정오 -->
		ON QTM.QTM_ID = ANW.QTM_ID 
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_TPC_PROF AUTP <!-- 토픽정답률, 예측정답률 -->
		ON QTM.TPC_ID = AUTP.TPC_ID
		AND ANW.USR_ID = AUTP.USR_ID
		LEFT OUTER JOIN LMS_LRM.AI_USRLY_QTM_PROF AUQP 
		ON BALAC.CTN_CD = AUQP.QTM_ID
		AND ANW.USR_ID = AUQP.USR_ID
		WHERE QTM.EV_ID = #{evId} <!-- 해당평가에 포함된 토픽 추출 -->
		AND ANW.USR_ID = #{usrId}
		AND BALAC.CTN_TP_CD = 'QU'
		GROUP BY BALAC.CTN_CD  
		ORDER BY MAX(QTM.TPC_ID), BALAC.CTN_CD, MAX(ANW.CANS_YN)

		/* AI맞춤 학생 맞춤학습별 토픽숙련도 조회 - 김현혜 - ApPlStu-Mapper.xml - selectQtmCansYnByTpcStu */
	</select>
	

</mapper>