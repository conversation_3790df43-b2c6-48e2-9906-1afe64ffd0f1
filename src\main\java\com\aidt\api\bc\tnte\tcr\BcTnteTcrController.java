package com.aidt.api.bc.tnte.tcr;

import java.io.IOException;
import java.util.List;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.tnte.dto.BcNewTnteDto;
import com.aidt.api.bc.tnte.dto.BcTnteDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.CoreUtil.Json;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import com.aidt.api.bc.cm.BcCmUtil;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:47
 * @modify 2024-01-05 17:52:47
 * @desc 필기 Controller
 */

@Slf4j
@Tag(name="[bc] 필기[BcTnteTcr]", description="필기(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/tnte")
public class BcTnteTcrController {

    @Autowired
    private BcTnteTcrService bcTnteTcrService;

	@Autowired
	private JwtProvider jwtProvider;

    @Autowired
    private WebFluxUtil webFluxUtil ;
    
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
    
    
    /**
     * 2024-07-11 액티비티 단위 학생별 필기 목록 조회(IN 학습창)
     *
     * @param BcTnteDto
     * @return ResponseList<BcTnteDto>
     */
    @Operation(summary="액티비티 단위 학생별 필기 목록 조회", description="액티비티 단위 학생별 필기 목록 조회한다.(교사)")
    @GetMapping(value = "/selectTcrTnteInfoInLwList")
    public List<BcTnteDto> selectTnteInfoInLwList(BcTnteDto bcTnteDto){
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
        bcTnteDto.setClaId( userDetails.getClaId());
       
    	return bcTnteTcrService.selectTcrTnteInfoInLwList(bcTnteDto);
    }

    /**
     * 필기 목록 조회 요청
     *
     * @param BcTnteDto
     * @return ResponseList<BcTnteDto>
     */
    @Operation(summary="필기 목록 조회", description="필기 목록을 조회한다.(교사)")
    @GetMapping(value = "/selectTnteList")
    public ResponseDto<List<BcTnteDto>> selectTnteList(BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
        bcTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteTcrService.selectTnteList(bcTnteDto));
    }

    /**
     * 필기 상세 조회 요청
     *
     * @param BcTnteDto
     * @return BcTnteDto
     */
//    @Operation(summary="필기 상세 조회", description="필기 상세를 조회한다.(교사)")
//    @GetMapping(value = "/selectTnteDtl")
//    public ResponseDto<BcTnteDto> selectTnteDtl(BcTnteDto bcTnteDto) throws JsonMappingException, JsonProcessingException {
//    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//    	bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
//    	
//        BcTnteDto bcTnteDto_ = bcTnteTcrService.selectTnteDtl(bcTnteDto);
//        
//        if (bcTnteDto_ != null){
//            String cdnPath = bcTnteDto_.getCdnPthNm();
//            String jsonData = "";
//            if (!cdnPath.isEmpty()){
//            	//파일이 있는 경우만 json 파일정보를 읽어 온다.
//        		if (BcCmUtil.isFileExists(BUCKET_NAME, cdnPath)) {
//        			jsonData = webFluxUtil.get(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, cdnPath), String.class);
//                    bcTnteDto_.setCanvasJsonData(Json.jsonString2ListMap(jsonData));	
//        		}else {
//        			bcTnteDto_ = null;
//        		}
//            }
//        }
//
//        return Response.ok(bcTnteDto_);
//    }
    
    @Operation(summary="필기 상세 조회", description="필기 상세를 조회한다.(교사)")
    @GetMapping(value = "/selectTnteDtl")
    public ResponseDto<BcTnteDto> selectTnteDtl(BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	
        BcTnteDto bcTnteDto_ = bcTnteTcrService.selectTnteDtl(bcTnteDto);
//        if (bcTnteDto_ != null){
//            String cdnPath = bcTnteDto_.getCdnPthNm();
//            if (!cdnPath.isEmpty()){
//            	//파일이 있는 경우만 json 파일정보를 읽어 온다.
//        		if (BcCmUtil.isFileExists(BUCKET_NAME, cdnPath)) {
//        			try {
//        				bcTnteDto_.setCanvasJsonData(BcCmUtil.s3JsonFileReader(BUCKET_NAME, cdnPath));	
//        			}
//        			catch(IOException e) {
//        				bcTnteDto_ = null;	
//        			}	
//        		}else {
//        			bcTnteDto_ = null;
//        		}
//            }
//        }

        return Response.ok(bcTnteDto_);
    }

    /**
     * 필기 상세 조회 요청 (학습창)
     *
     * @param LwTnteDto
     * @return LwTnteDto
     */
    @Operation(summary="필기 상세 조회(학습창)", description="필기 상세를 조회한다.(교사)")
    @GetMapping(value = "/selectLayoutTnteDtl")
    public ResponseDto<BcTnteDto> selectLayoutTnteDtl(BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcTnteDto.setLrnUsrId(userDetails.getUsrId());
    	return Response.ok(bcTnteTcrService.selectLayoutTnteDtl(bcTnteDto));
    }

    /**
     * 필기 등록 요청
     *
     * @param BcTnteDto
     * @return ResponseDto<int>
     */
    @Operation(summary="필기 등록", description="필기를 등록한다.(교사)")
    // @PostMapping(value = "/insertTnte", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping(value = "/insertTnte", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseDto<Integer> insertTnte(@Valid @RequestBody BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
        if (bcTnteDto.getLrnUsrId() == null || bcTnteDto.getLrnUsrId().equals("")) {
        	bcTnteDto.setLrnUsrId(userDetails.getUsrId());
        }
        bcTnteDto.setCrtrId(userDetails.getUsrId());
        bcTnteDto.setMdfrId(userDetails.getUsrId());
        bcTnteDto.setDbId(userDetails.getTxbId());
        return Response.ok(bcTnteTcrService.insertTnte(bcTnteDto));
    }


    /**
     * 필기 삭제 요청
     *
     * @param List<BcTnteDto>
     * @return ResponseDto<int>
     */
    @Operation(summary="필기 삭제", description="필기를 삭제한다.(교사)")
    @DeleteMapping(value = "/deleteTnte")
    public ResponseDto<Integer> deleteTnte(@RequestBody BcTnteDto tnteDto) {
    	
        return Response.ok(bcTnteTcrService.deleteTnteList(tnteDto));
    }
    
    
    
    
    
    
    
    
    
    
    

    // 2024-07-16 노트 구분 selectBox 조회(저장되어 있는 케이스만 보여줌)
    @Operation(summary="노트 구분 조회", description="노트 구분을 조회한다.(학생)")
    @GetMapping(value = "/selectNewTcrTnteDvList")
    public ResponseDto<List<BcNewTnteDto>> selectNewTcrTnteDvList(BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteTcrService.selectNewTcrTnteDvList(bcNewTnteDto));
    }
    
    // 2024-07-16 노트 구분 상세 selectBox 조회(저장되어 있는 케이스만 보여줌)
    @Operation(summary="노트 구분 상세 조회", description="노트 구분 상세를 조회한다.(학생)")
    @GetMapping(value = "/selectNewTcrTnteDvDtlList")
    public ResponseDto<List<BcNewTnteDto>> selectNewTcrTnteDvDtlList(BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteTcrService.selectNewTcrTnteDvDtlList(bcNewTnteDto));
    }
    
    
    
    // 2024-07-16 노트 목록 조회(개선안 버전)
    @Operation(summary="노트 목록 조회", description="노트 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectNewTcrTnteList")
    public ResponseDto<List<BcNewTnteDto>> selectNewTcrTnteList(BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteTcrService.selectNewTcrTnteList(bcNewTnteDto));
    }
    
    
    
    
    // 2024-07-19 노트 개선안 삭제
    @Operation(summary="노트 삭제", description="노트를 삭제한다.(학생)")
    @DeleteMapping(value = "/deleteNewTcrTnte")
    public ResponseDto<Integer> deleteNewTcrTnte(@RequestBody BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteTcrService.deleteNewTcrTnte(bcNewTnteDto));
    }

}
