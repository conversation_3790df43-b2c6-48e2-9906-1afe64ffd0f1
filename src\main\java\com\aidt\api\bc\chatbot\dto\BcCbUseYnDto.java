package com.aidt.api.bc.chatbot.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-01-06 14:41:54
 * @modify date 2025-01-06 14:41:54
 * @desc [BcCbUseYnDto 챗봇 사용 가능 여부]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCbUseYnDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    @Parameter(name="챗봇 사용 여부")
	private String cbUseYn;
}
