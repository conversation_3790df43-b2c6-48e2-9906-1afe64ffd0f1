package com.aidt.api.ea.lrnrpt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학습 리포트 - talk의 토픽 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnRptTalkTpcDto {

	@Parameter(name="순번")
	private int idx;
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="userID")
    private String userId;
	
	@Parameter(name="대단원 노드 ID")
	private String kmmpNodId;
	
	@Parameter(name="대단원 노드명")
	private String kmmpNodNm;
	
	@Parameter(name="토픽노드ID")
	private String tpcKmmpNodId;
	
	@Parameter(name="토픽노드명")
	private String tpcKmmpNodNm;
	
	@Parameter(name="학습상태")	//(NL:학습하기 DL:이어하기 CL:복습하기)
	private String lrnStCd;
	
	@Parameter(name="차시사용여부")
	private String tcUseYn;
	
	@Parameter(name="순서")
	private String rcstnOrdn;
	
	@Parameter(name="토픽순서")
	private String srtOrdn;
	
	@Parameter(name="생성일자")
	private String crtDtm;
	
	@Parameter(name="수정일자") //마지막 일자
	private String mdfDtm;
	
	@Parameter(name="학습시간")	//수정일자 - 생성일자
	private String lrnTime;

	@Parameter(name="노드랭크")
	private String nodRank;
	
	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
}
