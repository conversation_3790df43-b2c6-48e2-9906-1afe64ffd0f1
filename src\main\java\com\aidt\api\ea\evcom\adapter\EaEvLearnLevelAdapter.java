package com.aidt.api.ea.evcom.adapter;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.aidt.api.ea.evcom.dto.EaEvLearnLevelDto;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class EaEvLearnLevelAdapter {

	private final String MAPPER_NAMESPACE = "api.ea.evcom.";
	private final CommonDao commonDao;

	public void saveLearnerLevel(String usrId) {
		if (StringUtils.isBlank(usrId)) {
			throw new IllegalArgumentException("학습자 아이디가 존재하지 않아, 학습자 수준 업데이트를 할 수 없습니다.");
		}
		commonDao.update(MAPPER_NAMESPACE + "updateUsrLrnrVelTpCd", usrId);
	}

	public List<EaEvLearnLevelDto.LearnUnit> getLearnUnitLevels(EaEvLearnLevelDto learnLevel) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectLluLrnrLvs", learnLevel);
	}

	public void upsertLearnUnitLevels(EaEvLearnLevelDto learnLevels) {
		commonDao.insert(MAPPER_NAMESPACE + "updateTlLuLrnrLvs", learnLevels);
	}

}