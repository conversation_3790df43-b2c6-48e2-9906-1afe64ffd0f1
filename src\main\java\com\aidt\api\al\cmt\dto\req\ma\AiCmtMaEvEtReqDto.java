package com.aidt.api.al.cmt.dto.req.ma;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.cm.N05Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N13Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N14Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N15Dto;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvEtDto;
import com.aidt.api.al.cmt.dto.req.cm.LrnAnReqDto;
import com.aidt.api.al.cmt.dto.req.cm.N01ReqDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc 수학 > 학기말 총괄 평가
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtMaEvEtReqDto {

    @Parameter(name="학교급코드(초등:E,중등:M,고등:H)", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^(E|M|H)$", message="학교급코드(초등:E,중등:M,고등:H)")
    private String schlGrdCd;

    @Valid
    @Parameter(name="성취 수준 총평(가장 많이 맞힌 내용영역)", required=true)
    private N01ReqDto total;

    @Valid
    @Parameter(name="학습성장 목록", required=true)
    private List<LrnAnReqDto> lrnAnList;

    private String evId;

    private String usrId;

    public AiCmtMaEvEtDto toDto() {
        AiCmtLvlCalculator calculator = AiCmtLvlCalculator.MA_TYPE_80_100;

        AiCmtMaEvEtDto.AiCmtMaEvEtDtoBuilder aiCmtMaEvEtDtoBuilder = AiCmtMaEvEtDto.builder()
                .n01(this.total.toDto(calculator));

        LrnAnReqDto max = Collections.max(lrnAnList, Comparator.comparing(LrnAnReqDto::getGrowthRate));
        List<LrnAnReqDto> maxList = lrnAnList.stream().filter(s -> Objects.equals(s.getGrowthRate(), max.getGrowthRate())).collect(Collectors.toList());

        LrnAnReqDto min = Collections.min(lrnAnList, Comparator.comparing(LrnAnReqDto::getGrowthRate));
        List<LrnAnReqDto> minList = lrnAnList.stream().filter(s -> Objects.equals(s.getGrowthRate(), min.getGrowthRate())).collect(Collectors.toList());

        if(max.getGrowthRate() >= 5f) {
            String maxMergeStr = maxList.stream().map(LrnAnReqDto::getInputText).collect(Collectors.joining(", "));
            aiCmtMaEvEtDtoBuilder.n13(N13Dto.builder().inputText(maxMergeStr).build());
        }

        if(min.getGrowthRate() <= -5f) {
            String minMergeStr = minList.stream().map(LrnAnReqDto::getInputText).collect(Collectors.joining(", "));
            aiCmtMaEvEtDtoBuilder.n14(N14Dto.builder().inputText(minMergeStr).build());
        }

        if(!(max.getGrowthRate() >= 5f || min.getGrowthRate() <= -5f)) {
            aiCmtMaEvEtDtoBuilder.n15(N15Dto.builder().build());
        }

        // 맞힌 문항이 없을 때, 5번 스크립트 2개 조회.
        // + 학생만 (쿼리에서 처리) <sql id="selectN05">
        if(total.getQtmCnt().getCorrectQtmCnt() < 1) {
        	aiCmtMaEvEtDtoBuilder.n05(N05Dto.builder().selectCnt(2).build());
        }

        return aiCmtMaEvEtDtoBuilder.build();
    }
}
