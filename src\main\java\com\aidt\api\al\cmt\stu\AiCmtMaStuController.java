package com.aidt.api.al.cmt.stu;

import com.aidt.api.al.cmt.dto.req.cm.AiCmtCmRptLuReqDto;
import com.aidt.api.al.cmt.dto.req.ma.*;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 10:58:14
 * @modify date 2024-05-21 10:58:14
 * @desc 수학 코멘트 노출 : 학년/학기 초 진단,  학기/학년 말 총괄 평가
 */
//@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/cmt/ma/cm")
@Tag(name="[al] AI 코멘트(수학)", description="AI 코멘트(수학)")
public class AiCmtMaStuController {

    private final AiCmtStuService aiCommentStuService;

    private final AiCmtUsrDataStuService aiCmtUsrDataStuService;

    @Operation(summary="평가/학기초")
    @PostMapping(value = "/ev/st", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getMaEvStComment(@Valid @RequestBody AiCmtMaEvStReqDto reqDto) {
        return Response.ok(aiCmtUsrDataStuService.getOrInsertList(reqDto.getEvId(), reqDto.getUsrId() ,reqDto, aiCommentStuService::selectMaEv ));
    }

    @Operation(summary="평가/학기말 총괄평가")
    @PostMapping(value = "/ev/et", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getMaEvEtComment(@Valid @RequestBody AiCmtMaEvEtReqDto reqDto) {
        return Response.ok(aiCmtUsrDataStuService.getOrInsertList(reqDto.getEvId(), reqDto.getUsrId() ,reqDto, aiCommentStuService::selectMaEv ));
    }

    @Operation(summary="AI추천학습/진단리포트")
    @PostMapping(value = "/ai/dgn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getMaAiDgnRptComment(@Valid @RequestBody AiCmtMaAiDgnRptReqDto reqDto) {
        return Response.ok(aiCmtUsrDataStuService.getOrInsertList(reqDto.getEvId(), reqDto.getUsrId() ,reqDto, aiCommentStuService::selectMaAi ));
    }

    @Operation(summary="AI추천학습/학습리포트")
    @PostMapping(value = "/ai/lrn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getMaAiLrnRptComment(@Valid @RequestBody AiCmtMaAiLrnRptReqDto reqDto) {
        return Response.ok(aiCommentStuService.selectMaAi(reqDto));
    }

    @Operation(summary="학습리포트/단원별분석")
    @PostMapping(value = "/rpt/lu", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<AiCmtResDto>> getAiMaRptLuComment(@Valid @RequestBody AiCmtCmRptLuReqDto reqDto) {
        return Response.ok(aiCommentStuService.selectRpt(reqDto));
    }

//    @Operation(summary="평가/단원진단")
//    @PostMapping(value = "/ev/ud", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<List<AiCmtResDto>> getMaEvUdComment(@Valid @RequestBody AiCmtMaEvUdReqDto reqDto) {
//        return Response.ok(aiCommentStuService.selectMaEv(reqDto));
//    }

}
