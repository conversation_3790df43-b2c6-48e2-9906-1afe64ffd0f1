package com.aidt.api.bc.cm.file.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.cm.file.dto.BcFileReqDto;
import com.aidt.api.bc.cm.file.dto.BcFileResDto;
import com.aidt.api.bc.cm.file.service.BcCmFileService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "[bc] 공통[BcCm] New", description = "공통")
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
@RestController
@RequestMapping("/api/v1/bc/cm/common/file")
public class BcCmFileCommandController {

	private final BcCmFileService bcCmFileService;

	@Operation(summary = "[bc] 파일 업로드 API", description = "##Ncloud S3 파일 업로드 \n ##문서 뷰어 파일 업로드")
	@PostMapping(value = "/multi-upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<List<BcFileResDto>> uploadFiles(@Valid @ModelAttribute BcFileReqDto bcFileReqDto) {
		return Response.ok(bcCmFileService.uploadFlies(bcFileReqDto));

	}

	@Operation(summary = "[bc] 파일 삭제 API", description = "##Ncloud S3 파일 삭제")
	@DeleteMapping(value = "/{file-id}")
	public ResponseDto<Void> removeFile(@PathVariable(value = "file-id") Long annxFleId) {
		bcCmFileService.removeFile(annxFleId);
		return Response.ok();
	}

	@Operation(summary = "[bc] 파일 다운로드 API", description = "##Ncloud S3 파일 다운로드")
	@PostMapping(value = "/{file-id}/download", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public ResponseEntity<Resource> downloadFile(@PathVariable(value = "file-id") Long annxFleId) {
		return bcCmFileService.downloadFile(annxFleId);
	}
}
