<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.mntr.stu">

	<!-- 실시간 모니터링 현재학습콘텐츠 저장(학생) -->
	<update id="updateStuLrnCtt" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/** BcMntrStu-Mapper.xml - updateStuLrnCtt */
		UPDATE LMS_LRM.cm_stu_cur_lrn_st
		   SET LU_NOD_ID = #{luNodId}
			 , TC_NOD_ID = #{tcNodId}
			 , LRN_ATV_ID = #{lrnAtvId}
			 , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE LRN_USR_ID = #{lrnUsrId}
		   AND OPT_TXB_ID = #{optTxbId}
	</update>
	
	<select id="selectGdeNeedTp" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto" resultType="string">
		/** BcMntrStu-Mapper.xml - selectGdeNeedTp */
		SELECT CSCLS.GDE_NEED_TP
		  FROM LMS_LRM.CM_STU_CUR_LRN_ST CSCLS
		 WHERE CSCLS.OPT_TXB_ID = #{optTxbId}
		   AND CSCLS.LRN_USR_ID = #{lrnUsrId}
	</select>
	
	<!-- 실시간 모니터링 로그아웃 저장(학생) -->
	<update id="updateStuCurConn" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/** BcMntrStu-Mapper.xml - updateStuLrnCtt */
		UPDATE LMS_LRM.cm_stu_cur_lrn_st
		   SET CUR_CONN_YN = #{curConnYn}
		     , GDE_NEED_TP = #{gdeNeedTp}
			 , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE LRN_USR_ID = #{lrnUsrId}
		   AND OPT_TXB_ID = #{optTxbId}
	</update>
	
	<!-- 지도필요 insert -->
	<insert id="insertGdeNeed" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		insert into lms_lrm.cm_gde_need (
		      opt_txb_id
		    , stu_usr_id
		    , gde_need_cd
		    , gde_need_vl
		    , cofm_yn
		    , del_yn
		    , gde_need_info
		    , crtr_id
		    , crt_dtm
		    , mdfr_id
		    , mdf_dtm
		) values (
		      #{optTxbId}
		    , #{lrnUsrId}
		    , #{gdeNeedCd}
		    , #{gdeNeedVl}
		    , 'N'
		    , 'N'
		    , #{gdeNeedInfo}
		    , #{lrnUsrId}
		    , now()
		    , #{lrnUsrId}
		    , now()
		)
	</insert>
	
</mapper>