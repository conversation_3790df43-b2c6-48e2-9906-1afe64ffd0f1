package com.aidt.api.al.wrt.dto;

import org.springframework.beans.factory.annotation.Value;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlWrtReqDto {
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="학생ID")
	private String stuId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="교과서ID")
	private String txbId;
	
	@Parameter(name="대단원노드ID(레슨ID)")
	private String lluKmmpNodId;
	
	@Parameter(name="토픽노드ID")
	private String tpcKmmpNodId;
	
	@Parameter(name="진행상태코드")
	private String pgrsStCd;
	
	@Parameter(name="학생정답내용")
	private String stuCansCn;
	
	@Parameter(name="학습시간초수")
	private long lrnTmScnt;
	
	@Parameter(name="학급코드")
	private String classCode;
	
	@Parameter(name="AI 첨삭 내용")
	private String aiAnnxCn;
	
	@Parameter(name="AI 첨삭 내용")
	private String aiAnnxDtl;
	
	@Parameter(name="서브Url")
	private String subUrl;
	
	@Parameter(name="토픽ID")
	private String topicId;
	
	@Parameter(name="첨삭내용")
	private String userText;
	
	@Parameter(name="구성점수")
	private int cstnScr;
	
	@Parameter(name="표현점수")
	private int exprScr;
	
	@Parameter(name="어휘점수")
	private int vocScr;
	
	@Parameter(name="문법점수")
	private int grmrScr;
	
	@Parameter(name="선생님 첨삭내용")
	private String tcrAnnxCn;
	
	@Parameter(name="학생다시쓰기")
	private String stuMrkCn;
	
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
	
	@Parameter(name="아키핀에서 피드백 받을 서브도메인 값")
	private String systemCode;
}
