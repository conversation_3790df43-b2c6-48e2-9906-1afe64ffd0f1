package com.aidt.api.tl.sbclrn.dto;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 14:22:49
 * @modify date 2024-01-05 14:22:49
 * @desc TlTcrSbcLrnAtvStDto 교과학습 활동상태 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvStDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID")
    private String lrmpNodId;
    /** 학습활동ID */
    @Parameter(name="학습활동ID")
    private String lrnAtvId;
    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    /** (미진행, 학 */
    @Parameter(name="(미진행, 학")
    private String lrnStCd;
    /** 학습시간초수 */
    @Parameter(name="학습시간초수")
    private long lrnTmScnt;
    /** 생성자ID */
    @Parameter(name="생성자ID")
    private String crtrId;
    /** 생성일시 */
    @Parameter(name="생성일시")
    private Timestamp crtDtm;
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;
    /** 수정일시 */
    @Parameter(name="수정일시")
    private Timestamp mdfDtm;
}
