<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.oneclksetm.tcr">
	<!-- 원클릭학습설정 학습목차 조회 -->
	<select id="selectLrnTocList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
		SELECT
  		      A.OPT_TXB_ID  				                     /* 운영교과서ID */
  		     ,A.LRMP_NOD_ID  			                         /* 학습맵노드ID */
			 ,IFNULL(A.URNK_LRMP_NOD_ID, '') AS URNK_LRMP_NOD_ID /* 상위노드ID*/
 		     ,A.LRMP_NOD_NM  			                         /* 학습맵노드명 */
			 ,A.LLU_NOD_ID 				                         /* 대단원ID*/
			 ,A.DPTH  					                         /* 깊이 */
			 ,A.ORGL_ORDN 				                         /* 원본순서*/
 		     ,A.RCSTN_ORDN  			                         /* 재구성순서 */
 		     ,A.RCSTN_NO
			 ,A.LRN_STR_DT										 /* 학습시작일자 */
			 ,A.ORGL_LRN_STR_DT									 /* 원본학습시작일자 */
 		     ,A.LCKN_YN  				                         /* 잠금여부 */
 		     ,A.USE_YN  				                         /* 사용여부 */
 		     ,A.LU_EPS_YN										 /* 단원노출여부 */
 		     ,A.LU_NO_USE_YN									 /* 단원번호사용여부 */

 		 FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */
		 WHERE A.OPT_TXB_ID = #{optTxbId}
		 AND A.DPTH IN (1, 4)
		 AND A.LU_EPS_YN = 'Y'
		 
		 ORDER BY RCSTN_ORDN ASC, LRMP_NOD_ID ASC

		 /** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectLrnTocList */
	</select>

	<!-- 원클릭학습설정 학습활동 조회 -->
	<select id="selectLrnAtvList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		SELECT *
		FROM
		(SELECT
		       A.OPT_TXB_ID  		/* 운영교과서ID */
		      ,T.LLU_NOD_ID			/* 대단원ID */
		      ,A.LRMP_NOD_ID  		/* 학습맵노드ID */
		      ,A.LRN_ATV_ID 		/* 학습활동ID */
		      ,A.LRN_STP_ID  		/* 학습단계ID */
			  ,A.ORGL_LRN_STP_ID	/* 원본학습단계ID */
		      ,A.CTN_CD  			/* LCMS 학습활동코드 */
		      ,A.LRN_ATV_NM  		/* 문항아이디 or 파일명 */
		      ,A.CTN_TP_CD  		/* 콘텐츠 타입(QU=문항, HT=HTML5, PL=동영상, EX=평가지) */
		      ,A.USE_YN  			/* 사용여부 */
		      ,A.RCSTN_ORDN  		/* 재구성순서 */
			  ,A.ORGL_ORDN 			/* 원본순서*/
			  ,B.LRN_STP_DV_CD 		/* 학습단계구분코드*/
			  ,IFNULL(B.LRN_STP_CD, B.LRN_STP_ID) AS LRN_STP_CD /* 학습단계코드*/
			  ,IFNULL(O.LRN_STP_CD, O.LRN_STP_ID) AS ORGL_LRN_STP_CD	/* 원본학습단계코드 */
			  ,B.LRN_STP_NM 		/* 학습단계명*/
			  ,O.LRN_STP_NM AS ORGL_LRN_STP_NM 	/* 원본학습단계명 */
			  ,B.SRT_ORDN 			/* 학습단계순서*/
			  ,O.SRT_ORDN AS ORGL_SRT_ORDN
			  ,IFNULL(NM.CRCL_CTN_ELM2_CD, '')	AS EDU_CRS_CN_CD	/* 교육과정내용코드*/
			  ,IFNULL(U.CDN_PTH_NM, '') AS LRN_ATV_THB_PTH /* 학습활동 썸네일 패스 */
			  ,'N' AS TCR_CTN_YN
			  ,'N' AS DEL_YN
			  ,A.TCR_REG_CTN_ID
			  ,A.CP_YN
			  ,IFNULL(A.ORGL_LRMP_NOD_ID,'') as ORGL_LRMP_NOD_ID
			  ,T.LU_EPS_YN
		FROM (SELECT 
			    	R.OPT_TXB_ID,
			        R.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        R.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        R.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'N' AS TCR_CTN_YN,
			        '' AS TCR_REG_CTN_ID,
			        'N' AS CP_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
			    WHERE R.OPT_TXB_ID = #{optTxbId}
			    <if test='lrmpNodId != null and lrmpNodId neq ""'>
           			 AND R.LRMP_NOD_ID = #{lrmpNodId}
         		</if>
			      
				UNION ALL
				
			    SELECT 
			        R.OPT_TXB_ID,
			        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        TTRCM.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        TTRCM.USE_YN,
			        R.CLS_BRD_URL,
			        TTRCM.LRN_STP_ID AS ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        TTRCM.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'Y' AS TCR_CTN_YN,
			        TTRCM.TCR_REG_CTN_ID,
			        IFNULL(TTRCM.CP_YN, 'N') AS CP_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
			        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
			        and ttrcm.del_yn = 'N'
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
			        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
			    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
			    <if test='lrmpNodId != null and lrmpNodId neq ""'>
           			AND TTRCM.LRMP_NOD_ID = #{lrmpNodId}
         		</if>
			      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
			      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
			LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN T
				ON T.OPT_TXB_ID = #{optTxbId}
				AND A.LRMP_NOD_ID = T.LRMP_NOD_ID
		     LEFT JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
		          ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
		          AND A.LRN_STP_ID = B.LRN_STP_ID
				  AND B.DEL_YN = 'N'
			 LEFT JOIN LMS_CMS.BC_LRN_STP O /* BC_학습단계(원본) */
		          ON A.LRMP_NOD_ID = O.LRMP_NOD_ID
		          AND A.ORGL_LRN_STP_ID = O.LRN_STP_ID
				  AND B.DEL_YN = 'N'
			LEFT JOIN LMS_CMS.BC_CTN_MTD M /* BC_콘텐츠메타데이터 */
            	ON A.LRN_ATV_ID = M.LRN_ATV_ID
            	AND A.CTN_CD = M.CTN_CD
            	AND M.USE_YN = 'Y'
            	AND M.DEL_YN = 'N'
            LEFT JOIN LMS_CMS.BC_CTN_MTD_UPL_FLE U /* BC_콘텐츠메타데이터업로드파일 */
            	ON M.CTN_META_DATA_ID = U.CTN_META_DATA_ID
            	AND U.FLE_TP_CD = 'IM'
            	AND U.DEL_YN = 'N'
            LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 NM /* BC_국가수준교육과정컨텐츠매핑 */
           		ON M.CTN_META_DATA_ID = NM.CTN_ID
            		AND NM.CRCL_CTN_TP_CD = 'TL'
            		AND NM.DEL_YN = 'N'
		WHERE A.OPT_TXB_ID = #{optTxbId}
		<if test='lrmpNodId != null and lrmpNodId neq ""'>
            AND A.LRMP_NOD_ID = #{lrmpNodId}
         </if>
		AND B.LRN_STP_DV_CD = "CL"
		
		UNION ALL
		
		SELECT
				M.OPT_TXB_ID  		/* 운영교과서ID */
		      ,R.LLU_NOD_ID			/* 대단원ID */
		      ,M.LRMP_NOD_ID  		/* 학습맵노드ID */
		      ,M.TCR_REG_CTN_ID AS LRN_ATV_ID		/* 학습활동ID */
		      ,M.LRN_STP_ID AS LRN_STP_ID  		/* 학습단계ID */
			  ,M.LRN_STP_ID AS ORGL_LRN_STP_ID	/* 원본학습단계ID */
		      ,'' AS CTN_CD  			/* LCMS 학습활동코드 */
		      ,C.TCR_REG_CTN_NM AS LRN_ATV_NM  		/* 문항아이디 or 파일명 */
		      ,C.TP_CD AS CTN_TP_CD  		/* 콘텐츠 타입(QU=문항, HT=HTML5, PL=동영상, EX=평가지) */
		      ,M.USE_YN  			/* 사용여부 */
		      ,M.RCSTN_ORDN  		/* 재구성순서 */
			  ,M.RCSTN_ORDN AS ORGL_ORDN 			/* 원본순서*/
			  ,S.LRN_STP_DV_CD 		/* 학습단계구분코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS LRN_STP_CD /* 학습단계코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS ORGL_LRN_STP_CD	/* 원본학습단계코드 */
			  ,S.LRN_STP_NM 		/* 학습단계명*/
			  ,S.LRN_STP_NM AS ORGL_LRN_STP_NM 	/* 원본학습단계명 */
			  ,S.SRT_ORDN 			/* 학습단계순서*/
			  ,S.SRT_ORDN AS ORGL_SRT_ORDN
			  ,'' AS EDU_CRS_CN_CD	/* 교육과정내용코드*/
			  ,IFNULL(U.CDN_PTH_NM, '') AS LRN_ATV_THB_PTH /* 학습활동 썸네일 패스 */
			  ,'Y' AS TCR_CTN_YN
			  ,M.DEL_YN
			  ,M.TCR_REG_CTN_ID
			  ,IFNULL(M.CP_YN, 'N') AS CP_YN
			  ,'' as ORGL_LRMP_NOD_ID
			  ,R.LU_EPS_YN
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
			AND C.TP_CD <![CDATA[<>]]> 'AT'
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		LEFT JOIN LMS_CMS.BC_CTN_MTD MT /* BC_콘텐츠메타데이터 */
            	ON C.LRN_ATV_ID = MT.LRN_ATV_ID
            	AND MT.USE_YN = 'Y'
            	AND MT.DEL_YN = 'N'
        LEFT JOIN LMS_CMS.BC_CTN_MTD_UPL_FLE U /* BC_콘텐츠메타데이터업로드파일 */
            	ON MT.CTN_META_DATA_ID = U.CTN_META_DATA_ID
            	AND U.FLE_TP_CD = 'IM'
            	AND U.DEL_YN = 'N'
		WHERE M.OPT_TXB_ID = #{optTxbId}
		AND M.DEL_YN = 'N'
		<if test='lrmpNodId != null and lrmpNodId neq ""'>
            AND M.LRMP_NOD_ID = #{lrmpNodId}
         </if>) ATV
		
		ORDER BY ATV.SRT_ORDN IS NULL ASC, ATV.SRT_ORDN ASC, ATV.RCSTN_ORDN IS NULL ASC, ATV.RCSTN_ORDN ASC, LRMP_NOD_ID ASC, LRN_ATV_ID ASC

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectLrnAtvList */
	</select>

	<!-- 원클릭학습설정 Ai맞춤학습 목차 조회-->
	<select id="selectAlTocList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlTocDto">
		SELECT 
			OPT_TXB_ID,			/* 운영교과서ID */
			KMMP_NOD_ID,		/* 지식맵노드ID */
			KMMP_NOD_NM,		/* 지식맵노드명 */
			KMMP_ID,			/* 지식맵ID */
			DPTH,				/* 깊이 */
			ORGL_ORDN,			/* 원본순서 */
			RCSTN_ORDN,			/* 재구성순서 */
			TC_EPS_YN,			/* 차시노출여부 */
			TC_USE_YN			/* 차시사용여부 */
		FROM LMS_LRM.AI_KMMP_NOD_RCSTN 	/* AI_지식맵노드재구성 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND DPTH = 1
		AND DEL_YN = 'N'
		ORDER BY RCSTN_ORDN ASC, KMMP_NOD_ID ASC

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectAlTocList */
	</select>

	<!-- 원클릭학습설정 학급 리스트 조회 -->
	<select id="selectClaList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
		select 
			R.OPT_TXB_ID, 
			c.CLA_ID, 		/* 반 ID*/
			c.SGY, 			/* 학년*/
			c.CLA_NO,
			CASE
        		WHEN c.CLA_NM NOT LIKE '%반' THEN CONCAT(c.CLA_NM,'반')
        		ELSE c.CLA_NM
    		END AS CLA_NM,
    		c.CLASSROOM_NM as classroom_name
		from LMS_LRM.CM_USR A
		inner join LMS_LRM.cm_cla c
			on A.CLA_ID = C.CLA_ID
		inner join LMS_LRM.cm_opt_tcr r
			on A.USR_ID = R.TCR_USR_ID
		inner join LMS_LRM.cm_opt_txb t
			on R.OPT_TXB_ID = T.OPT_TXB_ID 
		where A.KERIS_USR_ID = #{tcrUsrId}
		AND T.TXB_ID=#{txbId}
		<if test='includeYn == "N"'>
		   AND B.OPT_TXB_ID <![CDATA[<>]]> #{optTxbId}
		</if>
		ORDER BY C.CLA_ID ASC, C.SGY ASC, C.CLA_NM ASC

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectClaList */
	</select>

	<!-- 원클릭학습설정 학습목차 저장 -->
	<update id="updateLrnTocList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
		UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN /* TL_교과학습노드재구성 */
		SET MDFR_ID = #{mdfrId}				/* 수정자ID */
		   ,MDF_DTM = NOW()	                /* 수정일시 */
		   ,USE_YN = #{useYn}  				/* 사용여부 */
		   ,LCKN_YN = #{lcknYn}  			/* 잠금여부 */
		   ,RCSTN_ORDN = #{rcstnOrdn}  		/* 재구성순서 */
		   ,RCSTN_NO = NULL
		   <if test='lrnStrDt eq ""'>
		   ,LRN_STR_DT = null				/* 학습시작일자 */
		   </if>
		   <if test='lrnStrDt neq ""'>
		   ,LRN_STR_DT = #{lrnStrDt}		/* 학습시작일자 */
		   </if>
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateLrnTocList */
	</update>

	<!-- 원클릭학습설정 학습활동 저장 -->
	<update id="updateLrnAtvList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto"> 
   		UPDATE LMS_LRM.TL_SBC_LRN_ATV_RCSTN /* TL_교과학습활동재구성 */
		SET MDFR_ID = #{mdfrId}				/* 수정자ID */
		   ,MDF_DTM = NOW()	                /* 수정일시 */
		   ,LRN_STP_ID = #{lrnStpId}		/* 학습단계ID */
		   ,USE_YN = #{useYn}  				/* 사용여부 */
		   ,RCSTN_ORDN = #{rcstnOrdn}  		/* 재구성 순서 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		AND LRN_ATV_ID = #{lrnAtvId}

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateLrnAtvList */
    </update>
    
    <!-- 원클릭학습설정 교사추가콘텐츠 저장 -->
	<update id="updateLrnCtnList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto"> 
   		UPDATE LMS_LRM.tl_tcr_reg_ctn_mpn /* TL_선생님등록콘텐츠매핑 */
		SET MDFR_ID = #{mdfrId}				/* 수정자ID */
		   ,MDF_DTM = NOW()	                /* 수정일시 */
		   ,LRN_STP_ID = #{lrnStpId}		/* 학습단계ID */
		   ,USE_YN = #{useYn}  				/* 사용여부 */
		   ,RCSTN_ORDN = #{rcstnOrdn}  		/* 재구성 순서 */
		   <if test='delYn != "" and delYn != null'>
		   ,DEL_YN = #{delYn}
		   </if>
		   ,SAV_YN = "Y"
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		AND TCR_REG_CTN_ID = #{lrnAtvId}

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateLrnCtnList */
    </update>
    
    <!-- 원클릭학습설정 교사추가콘텐츠 삭제 -->
	<delete id="deleteLrnCtnList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto"> 
   		DELETE FROM LMS_LRM.tl_tcr_reg_ctn_mpn /* TL_선생님등록콘텐츠매핑 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		AND TCR_REG_CTN_ID = #{lrnAtvId}

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - deleteLrnCtnList */
    </delete>

	<!-- 원클릭학습설정 다른 학급 학습목차 저장 -->
	<update id="updateAntClaLrnTocList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
		UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN A, 				/* TL_교과학습노드재구성 */
				(SELECT LCKN_YN,
						USE_YN,
						RCSTN_ORDN,
						RCSTN_NO,
						LRMP_NOD_ID
				 FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN 			/* TL_교과학습노드재구성 */
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}) B
		SET A.USE_YN = B.USE_YN,
			A.RCSTN_NO = B.RCSTN_NO,
			A.RCSTN_ORDN = B.RCSTN_ORDN,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()	
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAntClaLrnTocList */
	</update>

	<!-- 원클릭학습설정 다른 학급 학습활동 저장 -->
	<update id="updateAntClaLrnAtvList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto"> 
		UPDATE LMS_LRM.TL_SBC_LRN_ATV_RCSTN A,				/* TL_교과학습활동재구성 */
				(SELECT R.LRN_STP_ID,
						R.RCSTN_ORDN,
						R.USE_YN,
						R.LRN_ATV_ID,
						S.LRN_STP_DV_CD
				 FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN R		/* TL_교과학습활동재구성 */
				 	LEFT JOIN LMS_CMS.BC_LRN_STP S			/* BC_학습단계 */
				 		ON R.LRN_STP_ID = S.LRN_STP_ID
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}
				 AND S.LRN_STP_DV_CD = 'CL') B
		SET A.USE_YN = B.USE_YN,
			A.LRN_STP_ID = B.LRN_STP_ID,
			A.RCSTN_ORDN = B.RCSTN_ORDN,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND A.LRN_ATV_ID = B.LRN_ATV_ID

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAntClaLrnAtvList */
    </update>
    
    <!-- 다른 학급 교사 콘텐츠 비교 
    <select id="selectDiffCtnList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto" resultType="Map">
       SELECT DISTINCT CTN.ORGL
			,CTN.ANT_CLA
			,CASE
				WHEN CTN.ORGL IS NULL
				THEN 'DELETE'
				WHEN CTN.ANT_CLA IS NULL
				THEN 'INSERT'
			ELSE ''
			END AS BHV_DV
		FROM
			(SELECT M.TCR_REG_CTN_ID	AS ORGL
					, M2.TCR_REG_CTN_ID	AS ANT_CLA
			FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_mpn M2
				ON M2.OPT_TXB_ID = #{optTxbId}
				AND M.TCR_REG_CTN_ID = M2.TCR_REG_CTN_ID
			WHERE M.OPT_TXB_ID = #{orgnOptTxbId}

			UNION ALL

			SELECT M2.TCR_REG_CTN_ID	AS ORGL
					, M.TCR_REG_CTN_ID	AS ANT_CLA
			FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_mpn M2
				ON M2.OPT_TXB_ID = #{orgnOptTxbId}
				AND M.TCR_REG_CTN_ID = M2.TCR_REG_CTN_ID
			WHERE M.OPT_TXB_ID = #{optTxbId}) CTN
	
        /* 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectDiffCtnList */
    </select>
    -->
    
    <!-- 원클릭학습설정 다른 학급 교사콘텐츠 추가 -->
	<insert id="insertTcrCtn" parameterType="Map">
		INSERT INTO LMS_LRM.tl_tcr_reg_ctn_mpn
            (OPT_TXB_ID                  /* 운영교과서ID */
			,TCR_REG_CTN_ID
			,LRMP_NOD_ID
			,LRN_STP_ID
			,RCSTN_ORDN
			,USE_YN
			,SAV_YN
			,DEL_YN
			,CP_YN
			,CRTR_ID
			,CRT_DTM
			,MDFR_ID
			,MDF_DTM
            )
        SELECT
        	#{optTxbId}                 /* 운영교과서ID */
			,TCR_REG_CTN_ID
			,LRMP_NOD_ID
			,LRN_STP_ID
			,RCSTN_ORDN
			,USE_YN
			,SAV_YN
			,DEL_YN
			,CP_YN
            ,#{usrId}                     /* 생성자ID */
            ,CRT_DTM                    /* 생성일시 */
            ,#{usrId}                     /* 수정자ID */
            ,NOW()                    /* 수정일시 */
        FROM LMS_LRM.tl_tcr_reg_ctn_mpn M2
        WHERE M2.OPT_TXB_ID = #{orgnOptTxbId}
        
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertTcrCtn */
	</insert>

	<!-- 원클릭학습설정 다른 학급 교사콘텐츠 삭제 -->
	<delete id="deleteTcrCtn" parameterType="Map"> 
   		DELETE FROM  LMS_LRM.tl_tcr_reg_ctn_mpn M where M.OPT_TXB_ID = #{optTxbId}

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - deleteTcrCtn */
    </delete>
    
    <!-- 원클릭학습설정 다른 학급 교사콘텐츠 수정 
	<update id="updateTcrCtn" parameterType="Map"> 
		UPDATE LMS_LRM.tl_tcr_reg_ctn_mpn A,
				(SELECT 
					LRN_STP_ID
					,RCSTN_ORDN
					,USE_YN
					,DEL_YN
				FROM LMS_LRM.tl_tcr_reg_ctn_mpn
				WHERE OPT_TXB_ID = #{orgnOptTxbId}
				AND TCR_REG_CTN_ID = #{ctnId}) B
		SET A.LRN_STP_ID = B.LRN_STP_ID,
			A.RCSTN_ORDN = B.RCSTN_ORDN,
			A.USE_YN = B.USE_YN,
			A.DEL_YN = B.DEL_YN,
			A.MDFR_ID = #{usrId},
			A.MDF_DTM = NOW()
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND TCR_REG_CTN_ID = #{ctnId}

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateTcrCtn */
    </update>
    -->

	<!-- 원클릭학습설정 Ai맞춤학습 재구성 순서 저장 -->
	<update id="updateAlPlRcstnOrdn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlTocDto"> 
   		UPDATE LMS_LRM.AI_KMMP_NOD_RCSTN	/* AI_지식맵노드재구성 */
		SET MDFR_ID = #{mdfrId}				/* 수정자ID */
			,MDF_DTM = NOW()				/* 수정일시 */
			,RCSTN_ORDN = #{rcstnOrdn}		/* 재구성 순서 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND (KMMP_NOD_ID = #{kmmpNodId} OR URNK_KMMP_NOD_ID = #{kmmpNodId})

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAlPlRcstnOrdn */
    </update>

	<!-- 원클릭학습설정 Ai맞춤학습 잠금여부, 사용여부 저장 -->
	<update id="updateAlPlRcstn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlTocDto"> 
   		UPDATE LMS_LRM.AI_KMMP_NOD_RCSTN	/* AI_지식맵노드재구성 */
		SET MDFR_ID = #{mdfrId}				/* 수정자ID */
			,MDF_DTM = NOW()				/* 수정일시 */
			,TC_EPS_YN = #{tcEpsYn}			/* 차시노출여부 */
			,TC_USE_YN = #{tcUseYn}			/* 차시사용여부 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND KMMP_ID = #{kmmpNodId}

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAlPlRcstn */
    </update>

	<!-- 원클릭학습설정 다른 학급 Ai맞춤 재구성 저장 -->
	<update id="updateAntClaAlPlRcstn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto"> 
		UPDATE LMS_LRM.AI_KMMP_NOD_RCSTN A,					/* AI_지식맵노드재구성 */
				(SELECT KMMP_NOD_ID,
						RCSTN_ORDN,
						TC_EPS_YN,
						TC_USE_YN
				 FROM LMS_LRM.AI_KMMP_NOD_RCSTN				/* AI_지식맵노드재구성 */
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}) B
		SET A.TC_USE_YN = B.TC_USE_YN,
			A.TC_EPS_YN = B.TC_EPS_YN,
			A.RCSTN_ORDN = B.RCSTN_ORDN,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND A.KMMP_NOD_ID = B.KMMP_NOD_ID

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAntClaAlPlRcstn */
    </update>

	<!-- 원클릭학습설정 학습일정관리 저장 -->
	<update id="updateLrnScdlMg" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
		UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN /* TL_교과학습노드재구성 */
		SET MDFR_ID = #{mdfrId}				/* 수정자ID */
		   ,MDF_DTM = NOW()	                /* 수정일시 */
		   ,LCKN_YN = #{lcknYn}  			/* 잠금여부 */
		   <if test='lrnStrDt eq ""'>
		   ,LRN_STR_DT = null				/* 학습시작일자 */
		   </if>
		   <if test='lrnStrDt neq ""'>
		   ,LRN_STR_DT = #{lrnStrDt}		/* 학습시작일자 */
		   </if>
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateLrnScdlMg */
	</update>
	
	<!-- 원클릭학습설정 다른 학급 학습일정관리 저장 -->
	<update id="updateAntClaLrnScdlMg" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
		UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN A, 				/* TL_교과학습노드재구성 */
				(SELECT LCKN_YN,
						LRN_STR_DT,
						LRMP_NOD_ID
				 FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN 			/* TL_교과학습노드재구성 */
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}) B
		SET A.LCKN_YN = B.LCKN_YN,
			A.LRN_STR_DT = B.LRN_STR_DT,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()	
		WHERE A.OPT_TXB_ID = #{optTxbId}
		AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAntClaLrnTocList */
	</update>

	<!-- 원클릭학습설정 추천콘텐츠 재구성 목차 조회-->
	<select id="selectRcmCtnList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDto">
		SELECT 
			R.OPT_TXB_ID		/* 운영교과서ID */
			,R.SP_LRN_ID		/* 특별학습ID */
			,R.SP_LRN_NM		/* 특별학습명 */
			,MAX(L.LRN_GOAL_CN)	AS LRN_GOAL_CN	/* 학습목표 */
			,MAX(L.CRS_SETM_CN)	CSTN_CN			/* 구성내용 */
			,COUNT(C.LRN_USR_ID) AS RCM_STU_CNT /* 추천학생수 */
			,MAX(IFNULL(L.SP_LRN_DFFD,'')) AS SP_LRN_DFFD /* 난이도 */
			,MAX(IFNULL(P.PC_PATH,'')) AS PC_PATH
			,MAX(IFNULL(P.TA_PATH,'')) AS TA_PATH
		FROM LMS_LRM.SL_SP_LRN_RCSTN R 		/* SL_특별학습재구성 */
		LEFT JOIN LMS_CMS.BC_SP_LRN L		/* BC_특별학습 */
			ON R.SP_LRN_ID = L.SP_LRN_ID
			AND L.USE_YN = 'Y'
			AND L.DEL_YN = 'N'
			
		LEFT JOIN 
		 (SELECT C.OPT_TXB_ID,C.SP_LRN_ID,C.RCM_YN,C.LRN_USR_ID from LMS_LRM.SL_STU_RCM_LRN C 
		    INNER join
		      lms_lrm.cm_usr cm ON c.lrn_usr_id=cm.usr_id AND usr_tp_cd='ST' WHERE C.OPT_TXB_ID = #{optTxbId}
		 )C
			
			ON R.OPT_TXB_ID = C.OPT_TXB_ID
			AND R.SP_LRN_ID = C.SP_LRN_ID
			AND C.RCM_YN = 'Y'
		LEFT JOIN (
				SELECT    
                             M.SP_LRN_ID                                               /*특별학습 ID */
                            ,UF1.FLE_PTH_NM                     AS PC_PATH            /*태블릿 썸네일 PATH */
                            ,UF2.FLE_PTH_NM                     AS TA_PATH            /*피씨 썸네일 PATH */
                        FROM LMS_LRM.SL_SP_LRN_RCSTN M /* SL_특별학습재구성 */
                            LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM1 /* BC_특별학습썸네일파일매핑 */
                                   ON M.SP_LRN_ID = FM1.SP_LRN_ID
                                   AND FM1.THB_TML_TP_CD = 'PC'
                            LEFT JOIN LMS_CMS.BC_UPL_FLE UF1 /* BC_교과서업로드파일 */
                                    ON FM1.UPL_FLE_ID = UF1.UPL_FLE_ID 
                                   AND UF1.DEL_YN = 'N'
                            LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM2 /* BC_특별학습썸네일파일매핑 */
                                   ON M.SP_LRN_ID = FM2.SP_LRN_ID
                                  AND FM2.THB_TML_TP_CD = 'TA'
                            LEFT JOIN LMS_CMS.BC_UPL_FLE UF2 /* BC_교과서업로드파일 */
                                   ON FM2.UPL_FLE_ID = UF2.UPL_FLE_ID 
                                  AND UF2.DEL_YN = 'N'          
                        WHERE M.OPT_TXB_ID = #{optTxbId}
                        AND M.USE_YN ='Y'
				) P
			ON R.SP_LRN_ID = P.SP_LRN_ID
		WHERE R.OPT_TXB_ID = #{optTxbId}
		AND R.USE_YN = 'Y'
		GROUP BY R.SP_LRN_ID
				,R.OPT_TXB_ID
				,R.SP_LRN_NM
		ORDER BY R.RCSTN_ORDN ASC, R.SP_LRN_ID ASC

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectRcmCtnList */
	</select>

	<!-- 원클릭학습설정 추천학생 조회-->
	<!-- <select id="selectRcmStuLsit" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto">
		 SELECT 
			U.USR_ID								/* 학생ID */
			,U.USR_NM								/* 학생이름 */
			,U.STU_NO								/* 학생번호 */
			,U.LRNR_VEL_TP_CD						/* 학습자속도 */
			,IFNULL(R.RCM_YN,'') AS DAT_YN			/* 추천데이터존재여부 */
			,IFNULL(R.RCM_YN,'') AS RCM_YN			/* 추천여부 */
			,L.SP_LRN_ID
		FROM LMS_LRM.CM_USR U							/* CM_유저 */
		LEFT JOIN LMS_LRM.CM_OPT_TXB O					/* CM_운영교과서 */
			ON U.CLA_ID = O.CLA_ID
		LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN L				/* SL_특별학습재구성 */
			ON O.OPT_TXB_ID = L.OPT_TXB_ID
		LEFT JOIN LMS_LRM.SL_STU_RCM_LRN R				/* SL_학생별추천학습 */
			ON L.SP_LRN_ID = R.SP_LRN_ID
			AND U.USR_ID = R.LRN_USR_ID
		WHERE U.CLA_ID = #{claId}
		AND U.USR_TP_CD = 'ST'
		ORDER BY U.STU_NO ASC

		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectRcmStuLsit */
	</select> -->

	<!-- 원클릭학습설정 추천콘텐츠 최하위 조회-->
	<select id="selectSlSpLrnDtlList" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSpDtlDto">
        SELECT
			N.SP_LRN_ID
			,N.LWS_YN
			,N.URNK_SP_LRN_NOD_ID
			,N.SP_LRN_NOD_ID
		FROM LMS_CMS.BC_SP_LRN_NOD N 		/* BC_특별학습노드 */
		LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN R	/* SL_특별학습재구성 */
			ON N.SP_LRN_ID = R.SP_LRN_ID
		WHERE R.OPT_TXB_ID = #{optTxbId}
		AND N.DEL_YN = 'N'
		AND N.LWS_YN = 'Y'
		ORDER BY N.SRT_ORDN ASC
        /** 교과학습 김형준 SlSpLrnStu-Mapper.xml - selectSlSpLrnDtlList */       
    </select>
	
	<!-- 원클릭학습설정 추천콘텐츠 학생별 조회-->
	<select id="selectRcmCtnDtlList" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto">
        SELECT 
			U.USR_ID								/* 학생ID */
			,MAX(IFNULL(CD.CM_CD_NM, '-')) AS lrnr_vel_tp_cd					/* 학습자속도 */
			,MAX(IFNULL(R.RCM_YN,'')) AS DAT_YN			/* 추천데이터존재여부 */
			,MAX(IFNULL(R.RCM_YN,'N')) AS RCM_YN			/* 추천여부 */
			,MAX(#{spLrnId}) AS SP_LRN_ID							/* 특별학습ID */
			,L.SP_LRN_NM							/* 특별학습명 */
		FROM LMS_LRM.CM_USR U							/* CM_유저 */
		INNER JOIN LMS_LRM.cm_cm_cd CD
          	ON CD.URNK_CM_CD = 'LRNR_VEL_TP_CD'
          	AND CD.CM_CD = U.LRNR_VEL_TP_CD
          	AND CD.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.SL_STU_RCM_LRN R				/* SL_학생별추천학습 */
			ON U.USR_ID = R.LRN_USR_ID
			AND R.SP_LRN_ID = #{spLrnId}
			AND R.OPT_TXB_ID = #{optTxbId}
		LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN L				/* SL_특별학습재구성 */
			ON R.SP_LRN_ID = L.SP_LRN_ID
			AND L.OPT_TXB_ID = #{optTxbId}
		LEFT JOIN LMS_CMS.BC_SP_LRN_NOD N				/* BC_특별학습노드 */
			ON R.SP_LRN_ID = N.SP_LRN_ID
			AND N.LWS_YN = 'Y'
			AND N.DEL_YN = 'N'
		LEFT JOIN LMS_CMS.BC_SP_LRN_CTN C				/* BC_특별학습콘텐츠 */
			ON N.SP_LRN_NOD_ID = C.SP_LRN_NOD_ID
			AND C.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST S			/* SL_특별학습진행상태 */
			ON S.OPT_TXB_ID = #{optTxbId}
			AND S.SP_LRN_ID = #{spLrnId}
			AND C.SP_LRN_CTN_ID = S.SP_LRN_CTN_ID
			AND U.USR_ID = S.LRN_USR_ID
			AND S.LRN_ST_CD = 'CL'
		WHERE U.CLA_ID = #{claId}
		AND U.USR_TP_CD = 'ST'
		GROUP BY U.USR_ID
				,L.SP_LRN_NM	
		ORDER BY U.USR_ID ASC
        /** 교과학습 김형준 SlSpLrnStu-Mapper.xml - selectRcmCtnDtlList */       
    </select>
    
    <!-- 원클릭학습설정 추천콘텐츠 학생별 학습진행상태 조회-->
	<select id="selectRcmCtnLrnSt" parameterType="Map" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto">
        SELECT SUM(S.DONE)    AS DONE                                     /* 완료건수 */
		      ,COUNT(S.SP_LRN_ID)  AS ENTIRE                                   /* 전체 건수 */
		      ,(SELECT IF(COUNT(*)=0,'N','Y') AS EV_YN FROM LMS_LRM.ea_ev_rs E
				WHERE E.USR_ID = #{usrId}
				AND E.EV_CMPL_YN = 'Y') AS EV_YN
			  ,SUM(S.CTN_DONE) AS CTN_DONE
		FROM        
		(SELECT N.SP_LRN_ID
				,IF(SUM(IF(B.LRN_ST_CD = 'CL', 1, 0)) = COUNT(1), 1, 0) AS DONE
				, SUM(IF(B.LRN_ST_CD = 'CL', 1, 0)) AS CTN_DONE
		FROM LMS_CMS.bc_sp_lrn_nod N
		INNER JOIN LMS_CMS.BC_SP_LRN_CTN C /* BC_특별학습콘텐츠 */
		   ON C.SP_LRN_NOD_ID = N.SP_LRN_NOD_ID
		   AND C.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.SL_SP_LRN_PGRS_ST B /* SL_특별학습진행상태 */
                  ON B.OPT_TXB_ID = #{optTxbId}
                  AND B.SP_LRN_ID = N.SP_LRN_ID
                  AND B.SP_LRN_CTN_ID = C.SP_LRN_CTN_ID
                  AND B.LRN_USR_ID = #{usrId}
		WHERE N.SP_LRN_ID = #{spLrnId}
		AND N.LWS_YN = 'Y'
		AND N.DEL_YN = 'N'
		GROUP BY N.URNK_SP_LRN_NOD_ID, N.SP_LRN_ID) S
		GROUP BY S.SP_LRN_ID, EV_YN
        /** 교과학습 김형준 SlSpLrnStu-Mapper.xml - selectRcmCtnLrnSt */       
    </select>

	<!-- 원클릭학습설정 추천콘텐츠 학생별 저장 -->
	<insert id="insertRcmCtnInfo" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto">
		INSERT INTO LMS_LRM.SL_STU_RCM_LRN 		/* SL_학생별추천학습 */
			(OPT_TXB_ID							/* 운영교과서ID */
			,SP_LRN_ID							/* 특별학습ID */
			,LRN_USR_ID							/* 학생ID */
			,RCM_YN								/* 추천여부 */
			,CRTR_ID							
			,CRT_DTM
			,MDFR_ID
			,MDF_DTM
			,DB_ID) 
			VALUES
			(#{optTxbId}
			,#{spLrnId}
			,#{usrId}
			,#{rcmYn}
			,#{mdfrId}
			,NOW()
			,#{mdfrId}
			,NOW()
			,#{dbId})
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertRcmCtnInfo */
	</insert>

	<!-- 원클릭학습설정 추천콘텐츠 학생별 수정 -->
	<update id="updateRcmCtnInfo" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto">
		UPDATE LMS_LRM.SL_STU_RCM_LRN		/* SL_학생별추천학습 */
		SET RCM_YN = #{rcmYn}				/* 추천여부 */
			,MDFR_ID = #{mdfrId}
			,MDF_DTM = NOW()
		WHERE OPT_TXB_ID = #{optTxbId}
		AND SP_LRN_ID = #{spLrnId}
		AND LRN_USR_ID = #{usrId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateRcmCtnInfo */
	</update>

	<!-- 원클릭학습설정 기능사용설정 조회-->
	<select id="selectFncUseSetm" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmFncUseDto">
		SELECT
			OPT_TXB_ID
			,MYHM_PNT_USE_YN 				/* 마이홈포인트사용여부 */
			,PRAS_STMP_USE_YN 				/* 칭찬도장사용여부 */
			,DILG_USE_YN					/* 대화사용여부 */
			,CLA_BLBD_WRT_PMSN_YN			/* 학급게시판첨삭허용여부 */
			,CLA_BLBD_UCWR_PMSN_YN			/* 학급게시판댓글허용여부 */
			,IFNULL(DIY_EV_USE_YN, 'Y') as DIY_EV_USE_YN
			,IFNULL(LRN_RPT_USE_YN, 'Y')as LRN_RPT_USE_YN
		FROM LMS_LRM.CM_FNC_USE_SETM		/* CM_기능사용설정 */
		WHERE OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectFncUseSetm */
	</select>

	<!-- 원클릭학습설정 기능사용설정 수정 -->
	<update id="updateFncUseSetm" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmFncUseDto">
		UPDATE LMS_LRM.CM_FNC_USE_SETM						/* CM_기능사용설정 */
		SET MYHM_PNT_USE_YN = #{myhmPntUseYn}				/* 마이홈포인트사용여부 */
			,PRAS_STMP_USE_YN = #{prasStmpUseYn}			/* 칭찬도장사용여부 */
			,DILG_USE_YN = #{dilgUseYn}						/* 대화사용여부 */
			,CLA_BLBD_WRT_PMSN_YN = #{claBlbdWrtPmsnYn}		/* 학급게시판첨삭허용여부 */
			,CLA_BLBD_UCWR_PMSN_YN = #{claBlbdUcwrPmsnYn}	/* 학급게시판댓글허용여부 */
			,DIY_EV_USE_YN = #{diyEvUseYn}
			,LRN_RPT_USE_YN = #{lrnRptUseYn}
			,MDFR_ID = #{mdfrId}
			,MDF_DTM = NOW()
			,DB_ID = #{dbId}
		WHERE OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateFncUseSetm */
	</update>
	
	<!-- 원클릭학습설정 다른 학급 기능사용설정 저장 -->
	<update id="updateAntClaFncUseSetm" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
		UPDATE LMS_LRM.CM_FNC_USE_SETM A, 				/* TL_교과학습노드재구성 */
				(SELECT MYHM_PNT_USE_YN,
						PRAS_STMP_USE_YN,
						DILG_USE_YN,
						CLA_BLBD_WRT_PMSN_YN,
						CLA_BLBD_UCWR_PMSN_YN,
						DIY_EV_USE_YN,
						LRN_RPT_USE_YN
				 FROM LMS_LRM.CM_FNC_USE_SETM			/* TL_교과학습노드재구성 */
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}) B
		SET A.MYHM_PNT_USE_YN = B.MYHM_PNT_USE_YN,
			A.PRAS_STMP_USE_YN = B.PRAS_STMP_USE_YN,
			A.DILG_USE_YN = B.DILG_USE_YN,
			A.CLA_BLBD_WRT_PMSN_YN = B.CLA_BLBD_WRT_PMSN_YN,
			A.CLA_BLBD_UCWR_PMSN_YN = B.CLA_BLBD_UCWR_PMSN_YN,
			A.DIY_EV_USE_YN = B.DIY_EV_USE_YN,
			A.LRN_RPT_USE_YN = B.LRN_RPT_USE_YN,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()
		WHERE A.OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAntClaLrnTocList */
	</update>
	
	<!-- 원클릭학습설정 자료 아이디, 번호 조회-->
	<select id="selectMtrlInfo" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		SELECT 
			IFNULL(MAX(LSN_MTRL_NO), 0) AS LSN_MTRL_NO
			,IFNULL(MAX(RCSTN_ORDN),0) AS RCSTN_ORDN
		FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL /* TL_교과학습수업자료 */
        WHERE OPT_TXB_ID = #{optTxbId}
        AND LRMP_NOD_ID = #{lrmpNodId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectMtrlInfo */
	</select>
	
	<!-- 원클릭학습설정 내 자료 등록 -->
	<insert id="insertRegMtrl" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
        INSERT INTO LMS_LRM.TL_SBC_LRN_LSN_MTRL /* TL_교과학습수업자료 */
            (OPT_TXB_ID                  /* 운영교과서ID */
            ,LRMP_NOD_ID                 /* 학습맵노드ID */
            ,LSN_MTRL_NO                 /* 수업자료번호 */
            ,LRN_MTRL_ID                 /* 학습자료ID */
            ,LSN_MTRL_TP_CD              /* 자료구분코드*/
            ,ANNX_ID
            ,LSN_SPP_MTRL_NM
            ,OPNP_YN                     /* 학생에게 공개여부 */
            ,RCSTN_ORDN                  /* 재구성순서 */
            ,BS_MTRL_YN                  /* 기본자료여부 */
            ,DEL_YN
            ,URL
            ,LRN_BLWR_CN
            ,CRTR_ID                     /* 생성자ID */
            ,CRT_DTM                     /* 생성일시 */
            ,MDFR_ID                     /* 수정자ID */
            ,MDF_DTM                     /* 수정일시 */
            ,DB_ID                       /* 접속DB인스턴스ID */
            )
        VALUES
            (#{optTxbId}                                /* 운영교과서ID */
            ,#{lrmpNodId}                               /* 학습맵노드ID */
            ,#{lsnMtrlNo}            					/* 수업자료번호 */
            ,#{lrnMtrlId}         						/* 학습자료ID */
            ,#{lsnMtrlTpCd}				                /* 자료구분코드 */
            ,#{annxId}
            ,#{lsnSppMtrlNm}
            ,#{opnpYn}                                 	/* 학생에게 공개여부 */
            ,#{rcstnOrdn}             					/* 재구성순서 */   
            ,#{bsMtrlYn}                                /* 기본자료여부 */
            ,#{delYn}                                   /* 삭제여부 */
            ,#{url}
            ,#{lrnBlwrCn}
            ,#{mdfrId}                                  /* 생성자ID */
            ,NOW()                                      /* 생성일시 */
            ,#{mdfrId}                                  /* 수정자ID */
            ,NOW()                                      /* 수정일시 */
            ,#{dbId})                                   /* 접속DB인스턴스ID */

        /** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertRegMtrl */
    </insert>
    
    <delete id="deleteAntClaRegMtrl" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
    	DELETE FROM lms_lrm.tl_sbc_lrn_lsn_mtrl M where M.OPT_TXB_ID = #{optTxbId}
    </delete>
    
    <insert id="insertAntClaRegMtrl" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
        INSERT INTO LMS_LRM.TL_SBC_LRN_LSN_MTRL /* TL_교과학습수업자료 */
            (OPT_TXB_ID                  /* 운영교과서ID */
            ,LRMP_NOD_ID                 /* 학습맵노드ID */
            ,LSN_MTRL_NO                 /* 수업자료번호 */
            ,LRN_MTRL_ID                 /* 학습자료ID */
            ,LSN_MTRL_TP_CD              /* 자료구분코드*/
            ,ANNX_ID
            ,LSN_SPP_MTRL_NM
            ,OPNP_YN                     /* 학생에게 공개여부 */
            ,RCSTN_ORDN                  /* 재구성순서 */
            ,BS_MTRL_YN                  /* 기본자료여부 */
            ,DEL_YN
            ,URL
            ,LRN_BLWR_CN
            ,CRTR_ID                     /* 생성자ID */
            ,CRT_DTM                     /* 생성일시 */
            ,MDFR_ID                     /* 수정자ID */
            ,MDF_DTM                     /* 수정일시 */
            ,DB_ID                       /* 접속DB인스턴스ID */
            )
        SELECT
        	#{optTxbId}                 /* 운영교과서ID */
            ,LRMP_NOD_ID                 /* 학습맵노드ID */
            ,LSN_MTRL_NO                 /* 수업자료번호 */
            ,LRN_MTRL_ID                 /* 학습자료ID */
            ,LSN_MTRL_TP_CD              /* 자료구분코드*/
            ,ANNX_ID
            ,LSN_SPP_MTRL_NM
            ,OPNP_YN                     /* 학생에게 공개여부 */
            ,RCSTN_ORDN                  /* 재구성순서 */
            ,BS_MTRL_YN                  /* 기본자료여부 */
            ,DEL_YN
            ,URL
            ,LRN_BLWR_CN
            ,#{mdfrId}                     /* 생성자ID */
            ,CRT_DTM                    /* 생성일시 */
            ,#{mdfrId}                     /* 수정자ID */
            ,NOW()                    /* 수정일시 */
            ,DB_ID                       /* 접속DB인스턴스ID */
        FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL M2
        WHERE M2.OPT_TXB_ID = #{orgnOptTxbId}

        /** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertRegMtrl */
    </insert>
    
    <!-- 원클릭학습설정 자료 삭제 
	<update id="updateRegMtrl" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		UPDATE LMS_LRM.TL_SBC_LRN_LSN_MTRL
		SET OPNP_YN = #{opnpYn}
			,DEL_YN = #{delYn}
			,MDFR_ID = #{mdfrId}
			,MDF_DTM = NOW()
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		AND LSN_MTRL_NO = #{lsnMtrlNo}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateRegMtrl */
	</update>
	-->
	
	<!-- 원클릭학습설정 자료 수정 -->
	<update id="updateRegMtrl" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - updateRegMtrl */
		UPDATE LMS_LRM.TL_SBC_LRN_LSN_MTRL
		   SET LSN_SPP_MTRL_NM = #{mtrlNm}
			 , LRMP_NOD_ID = #{chgLrmpNodId}
			 , LRN_BLWR_CN = #{lrnBlwrCn}
			 , DEL_YN = #{delYn}
			 , OPNP_YN = #{opnpYn}
			 , ANNX_ID = #{annxId}
			 , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE OPT_TXB_ID = #{optTxbId}
		   AND LRMP_NOD_ID = #{lrmpNodId}
		   AND LSN_MTRL_NO = #{lsnMtrlNo}
	</update>
	
	<!-- 내 자료 조회-->
    <select id="selectRegMtrl" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto" resultType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        SELECT 
        	 M.LSN_MTRL_NO                            /* 수업자료번호 */
        	,M.LSN_MTRL_TP_CD                         /* 수업자료유형코드 */
        	,M.ANNX_ID                                /* 첨부ID */
        	,M.LSN_SPP_MTRL_NM   AS MTRL_NM           /* 수업보충자료ID */
        	,MAX(IFNULL(M.URL,''))               AS FLE_LINK_URL_ADR  /* 파일링크URL주소 */
        	,MAX(IFNULL(M.LRN_BLWR_CN,'')) 		AS LRN_BLWR_CN /* 학습게시글내용 */
            ,MAX(M.OPNP_YN) AS OPNP_YN 
        	,MAX(F.ANNX_FLE_ORGL_NM) AS FLE_NM            /* 파일명 */
        	,MAX(IFNULL(F.ANNX_FLE_PTH_NM, ''))   AS FLE_PTH_NM        /* 파일경로명 */
        	,MAX(IFNULL(F.DOC_VI_ID, '')) AS DOC_VI_ID
        FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL M      /* TL_교과학습수업자료 */
            LEFT JOIN LMS_LRM.CM_ANNX A             /* CM_첨부 */
                   ON M.ANNX_ID = A.ANNX_ID
                  AND A.USE_YN = 'Y'
            LEFT JOIN LMS_LRM.CM_ANNX_FLE F         /* CM_첨부파일 */
                   ON A.ANNX_ID = F.ANNX_ID
                  AND F.USE_YN = 'Y'
        WHERE M.OPT_TXB_ID = #{optTxbId}
          AND M.LRMP_NOD_ID = #{lrmpNodId}
          AND M.LSN_MTRL_NO = #{lsnMtrlNo}
          AND M.BS_MTRL_YN = 'N'  -- 원클릭학습설정에서 교사가 올린것만
          GROUP BY M.LSN_MTRL_NO
          			,M.LSN_MTRL_TP_CD
        			,M.ANNX_ID
        			,M.LSN_SPP_MTRL_NM
        /** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectRegMtrl */
    </select>
	
	<!-- 원클릭학습설정 자료 게시물 상세 조회-->
	<select id="selectBlwrInfo" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectBlwrInfo */
		SELECT OPT_TXB_ID
			 , LRMP_NOD_ID
			 , LSN_MTRL_NO
			 , LRN_MTRL_ID
			 , LSN_MTRL_TP_CD
			 , ANNX_ID
			 , LSN_SPP_MTRL_NM
			 , URL
			 , LRN_BLWR_CN
			 , OPNP_YN
			 , RCSTN_ORDN
			 , BS_MTRL_YN
			 , DEL_YN
			 , CRTR_ID
			 , CRT_DTM
			 , MDFR_ID
			 , MDF_DTM
			 , DB_ID
		  FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL
		 WHERE OPT_TXB_ID  = #{optTxbId}
		   AND LRMP_NOD_ID = #{lrmpNodId}
		   AND LSN_MTRL_NO = #{lsnMtrlNo}
	</select>
	
	<!-- 학급게시판 첨부파일 조회 -->
	<select id="selectAnnxFleList" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlAnnxFleDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectAnnxFleList */
		SELECT
			ANNX_FLE_ID,
			ANNX_ID,
			SRT_ORDN,
			DOC_VI_ID,
			ANNX_FLE_NM,
			ANNX_FLE_ORGL_NM,
			ANNX_FLE_FEXT_NM,
			ANNX_FLE_SZE,
			ANNX_FLE_PTH_NM,
			USE_YN
		FROM
			LMS_LRM.CM_ANNX_FLE
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>
	
	<!-- 원클릭학습설정 번호 노출 목차  조회 -->
	<select id="selectLrnSortList" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
	select AA.* from (
		SELECT   a.OPT_TXB_ID,
         a.LU_NO_USE_YN,
         a.LU_EPS_YN,
         a.RCSTN_ORDN,
         a.DPTH,
         a.URNK_LRMP_NOD_ID,
         a.LRMP_NOD_ID,
         a.RCSTN_NO
		FROM     lms_lrm.tl_sbc_lrn_nod_rcstn a
		         INNER JOIN lms_cms.bc_lrmp_nod b
		         ON       a.LRMP_NOD_ID=b.LRMP_NOD_ID
		WHERE    a.OPT_TXB_ID = #{optTxbId}
		  AND a.DPTH =1
                  AND      b.LU_EPS_YN    ='Y'
                  AND      a.USE_YN       ='Y'
                  AND      a.LU_NO_USE_YN ='Y'
		UNION ALL
		SELECT   a.OPT_TXB_ID,
         a.LU_NO_USE_YN,
         a.LU_EPS_YN,
         a.RCSTN_ORDN,
         a.DPTH,
         node3.LRMP_NOD_ID URNK_LRMP_NOD_ID,
         a.LRMP_NOD_ID,
         a.RCSTN_NO
		FROM     lms_lrm.tl_sbc_lrn_nod_rcstn a
         INNER JOIN lms_cms.bc_lrmp_nod b
         ON       a.LRMP_NOD_ID=b.LRMP_NOD_ID
         INNER JOIN lms_cms.bc_lrmp_nod node1
         ON       node1.LRMP_NOD_ID=b.URNK_LRMP_NOD_ID
         INNER JOIN lms_cms.bc_lrmp_nod node2
         ON       node2.LRMP_NOD_ID=node1.URNK_LRMP_NOD_ID
         INNER JOIN lms_cms.bc_lrmp_nod node3
         ON       node3.LRMP_NOD_ID=node2.URNK_LRMP_NOD_ID
		WHERE    a.OPT_TXB_ID          = #{optTxbId}
		AND
      
                           a.DPTH =4
                  AND      b.LU_EPS_YN    ='Y'
                  AND      a.USE_YN       ='Y'
                  AND      a.LU_NO_USE_YN ='Y'
		) AA                  
	ORDER BY 
         DPTH,
         RCSTN_ORDN
         
		 /** 교과학습 노성용 TlLrnOneClkSetmTcr-Mapper.xml - selectLrnSortList */
	</select>
	
	<!-- 원클릭학습설정 노출 번호  저장 -->
	<update id="updateLrnSort" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto">
		UPDATE LMS_LRM.TL_SBC_LRN_NOD_RCSTN /* TL_교과학습노드재구성 */
		SET MDF_DTM = NOW()	                /* 수정일시 */
		   ,RCSTN_NO = #{rcstnNo}  		/* 재구성 노출 번호 */
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRMP_NOD_ID = #{lrmpNodId}
		/** 교과학습 노성용 TlLrnOneClkSetmTcr-Mapper.xml - updateLrnSort */
	</update>
	
	 
	<!-- 원클릭학습설정 활동 수 조회 -->
	<select id="selectLrnAtvCnt" parameterType="Map" resultType="int">
		SELECT COUNT(*) AS ATV_CNT
         FROM	(SELECT
         				XA.LRN_ATV_ID
                FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN XA /* TL_교과학습활동재구성 */
               INNER JOIN LMS_CMS.BC_LRN_STP XB /* BC_학습단계 */
                     ON XA.LRMP_NOD_ID = XB.LRMP_NOD_ID
                     AND XA.LRN_STP_ID = XB.LRN_STP_ID
                WHERE XA.OPT_TXB_ID = #{optTxbId}
                AND XA.LRMP_NOD_ID = #{lrmpNodId}
                AND XA.USE_YN = 'Y'
                AND XB.LRN_STP_DV_CD = 'CL' /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EX: 평가) */
                AND XB.DEL_YN = 'N'
                
                UNION ALL
      			 
      			 SELECT 
					 		M.TCR_REG_CTN_ID AS LRN_ATV_ID
					FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
					INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
						ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
					INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
						ON M.OPT_TXB_ID = R.OPT_TXB_ID
						AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
					INNER JOIN LMS_CMS.bc_lrn_stp S
						ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
						AND M.LRN_STP_ID = S.LRN_STP_ID
						AND S.DEL_YN = 'N'
					WHERE M.OPT_TXB_ID = #{optTxbId}
					AND M.LRMP_NOD_ID = #{lrmpNodId}
					AND M.DEL_YN = 'N'
					AND M.USE_YN = 'Y')ATV_ST
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectLrnAtvCnt */
	</select>
	
	<!-- 원클릭학습설정 선생님 콘텐츠 수 조회 -->
	<select id="selectTcrCtnCnt" resultType="int">
		SELECT COUNT(*) FROM LMS_LRM.tl_tcr_reg_ctn
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectTcrCtnCnt */
	</select>
	
	<!-- 원클릭학습설정 선생님 활동 복사 조회 -->
	<select id="chkAtvCpInfo" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto" resultType="String">
		SELECT TCR_REG_CTN_ID FROM LMS_LRM.tl_tcr_reg_ctn where ORGL_OPT_TXB_ID = #{optTxbId} and lrn_atv_id = #{lrnAtvId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - chkAtvCpInfo */
	</select>
	
	<!-- 원클릭학습설정 선생님 콘텐츠 수 조회 -->
	<select id="selectAtvMinRcstnOrdn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto" resultType="int">
		SELECT MIN(ATV.RCSTN_ORDN)-1 FROM
				(SELECT R.RCSTN_ORDN 
				FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
				WHERE R.OPT_TXB_ID = #{optTxbId}
				AND R.LRMP_NOD_ID =#{cpLrmpNodId}	
				
				UNION ALL
				SELECT M.RCSTN_ORDN 
				FROM LMS_LRM.tl_tcr_reg_ctn_mpn M 
				WHERE M.OPT_TXB_ID = #{optTxbId}
				AND M.LRMP_NOD_ID =#{cpLrmpNodId}) ATV
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectAtvMinRcstnOrdn */
	</select>
	
	<!-- 원클릭학습설정 선생님 콘텐츠 추가 -->
	<insert id="insertTcrCtnReg" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		INSERT INTO LMS_LRM.tl_tcr_reg_ctn
				(TCR_REG_CTN_ID
				,TCR_USR_ID
				,TP_CD
				,ANNX_ID
				,TCR_REG_CTN_NM
				,LRN_BLWR_CN
				,LRN_CMPL_BS
				,ORGL_OPT_TXB_ID
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM
				,DB_ID)
		VALUES
				(#{tcrRegCtnId}
				,#{crtrId}
				,#{lsnMtrlTpCd}
				,#{annxId}
				,#{lsnSppMtrlNm}
				,#{lrnBlwrCn}
				,3
				,#{optTxbId}
				,#{crtrId}
				,NOW()
				,#{mdfrId}
				,NOW()
				,#{dbId});
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertTcrCtnReg */
	</insert>
	
	<!-- 원클릭학습설정 선생님 콘텐츠 매핑 추가 -->
	<insert id="insertTcrCtnRegMpn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		INSERT INTO LMS_LRM.tl_tcr_reg_ctn_mpn
				(OPT_TXB_ID
				,TCR_REG_CTN_ID
				,LRMP_NOD_ID
				,LRN_STP_ID
				,RCSTN_ORDN
				,USE_YN
				,DEL_YN
				,SAV_YN
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM)
		VALUES
				(#{optTxbId}
				,#{tcrRegCtnId}
				,#{lrmpNodId}
				,#{lrnStpId}
				,(SELECT MIN(ATV.RCSTN_ORDN)-1 FROM
				(SELECT R.RCSTN_ORDN 
				FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
				WHERE R.OPT_TXB_ID = #{optTxbId}
				AND R.LRMP_NOD_ID =#{lrmpNodId}	
				
				UNION ALL
				SELECT M.RCSTN_ORDN 
				FROM LMS_LRM.tl_tcr_reg_ctn_mpn M 
				WHERE M.OPT_TXB_ID = #{optTxbId}
				AND M.LRMP_NOD_ID =#{lrmpNodId}) ATV)
				,'Y'
				,'Y'
				,'N'
				,#{crtrId}
				,NOW()
				,#{mdfrId}
				,NOW())
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertTcrCtnRegMpn */
	</insert>
	
	<!-- 원클릭학습설정 선생님 콘텐츠 추가 
	<insert id="insertTcrCtnCp" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		INSERT INTO LMS_LRM.tl_tcr_reg_ctn (
		    TCR_REG_CTN_ID,
		    TCR_USR_ID,
		    TP_CD,
		    ANNX_ID,
		    TCR_REG_CTN_NM,
		    LRN_BLWR_CN,
		    LRN_CMPL_BS,
		    ORGL_OPT_TXB_ID,
		    CRTR_ID,
		    CRT_DTM,
		    MDFR_ID,
		    MDF_DTM,
		    DB_ID
		)
		SELECT
		    #{tcrRegCtnId} AS TCR_REG_CTN_ID,
		    TCR_USR_ID,
		    TP_CD,
		    ANNX_ID,
		    TCR_REG_CTN_NM,
		    LRN_BLWR_CN,
		    LRN_CMPL_BS,
		    #{optTxbId} AS ORGL_OPT_TXB_ID,
		    #{mdfrId} AS CRTR_ID,                 
		    NOW() AS CRT_DTM,              
		    #{mdfrId} AS MDFR_ID,              
		    NOW() AS MDF_DTM,               
		    #{dbId} AS DB_ID       
		FROM LMS_LRM.tl_tcr_reg_ctn 
		WHERE ORGL_OPT_TXB_ID = #{optTxbId}
		and TCR_REG_CTN_ID = #{lrnAtvId};
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertTcrCtnCp */
	</insert> -->
	
	<!-- 원클릭학습설정 활동 복사 -->
	<insert id="insertTcrAtvReg" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		INSERT INTO LMS_LRM.tl_tcr_reg_ctn
				(TCR_REG_CTN_ID
				,TCR_USR_ID
				,LRN_ATV_ID
				,TP_CD
				,ANNX_ID
				,TCR_REG_CTN_NM
				,LRN_BLWR_CN
				,LRN_CMPL_BS
				,ORGL_OPT_TXB_ID
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM
				,DB_ID)
		VALUES
				(#{tcrRegCtnId}
				,#{mdfrId}
				,#{lrnAtvId}
				,'AT'
				,0
				,#{lrnAtvNm}
				,''
				,3
				,#{optTxbId}
				,#{mdfrId}
				,NOW()
				,#{mdfrId}
				,NOW()
				,#{dbId});
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertTcrAtvReg */
	</insert>
	
	<!-- 원클릭학습설정 홣동 복사 매핑 추가 -->
	<insert id="insertTcrAtvRegMpn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		INSERT INTO LMS_LRM.tl_tcr_reg_ctn_mpn
				(OPT_TXB_ID
				,TCR_REG_CTN_ID
				,LRMP_NOD_ID
				,LRN_STP_ID
				,RCSTN_ORDN
				,USE_YN
				,DEL_YN
				,SAV_YN
				,CP_YN
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM)
		VALUES
				(#{optTxbId}
				,#{tcrRegCtnId}
				,#{lrmpNodId}
				,#{lrnStpId}
				,#{rcstnOrdn}
				,'Y'
				,'Y'
				,'N'
				,#{cpYn}
				,#{mdfrId}
				,NOW()
				,#{mdfrId}
				,NOW())
		ON DUPLICATE KEY UPDATE
             LRN_STP_ID = #{lrnStpId}
             ,RCSTN_ORDN = #{rcstnOrdn}
             ,MDFR_ID = #{mdfrId}  /* 수정자ID 업데이트 */
             ,MDF_DTM = NOW()  /* 수정일시 업데이트 */
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertTcrAtvRegMpn */
	</insert>
	
	<!-- 원클릭학습설정 추가콘텐츠 조회-->
	<select id="selectRegCtn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectRegCtn */
		
		  SELECT
				M.OPT_TXB_ID  		/* 운영교과서ID */
		      ,R.LLU_NOD_ID			/* 대단원ID */
		      ,M.LRMP_NOD_ID  		/* 학습맵노드ID */
		      ,IFNULL(C.LRN_ATV_ID, M.TCR_REG_CTN_ID) AS LRN_ATV_ID		/* 학습활동ID */
		      ,M.LRN_STP_ID AS LRN_STP_ID  		/* 학습단계ID */
			  ,M.LRN_STP_ID AS ORGL_LRN_STP_ID	/* 원본학습단계ID */
		      ,'' AS CTN_CD  			/* LCMS 학습활동코드 */
		      ,C.TCR_REG_CTN_NM AS LRN_ATV_NM  		/* 문항아이디 or 파일명 */
		      ,C.TP_CD AS CTN_TP_CD  		/* 콘텐츠 타입(QU=문항, HT=HTML5, PL=동영상, EX=평가지) */
		      ,M.USE_YN  			/* 사용여부 */
		      ,M.RCSTN_ORDN  		/* 재구성순서 */
			  ,M.RCSTN_ORDN AS ORGL_ORDN 			/* 원본순서*/
			  ,S.LRN_STP_DV_CD 		/* 학습단계구분코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS LRN_STP_CD /* 학습단계코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS ORGL_LRN_STP_CD	/* 원본학습단계코드 */
			  ,S.LRN_STP_NM 		/* 학습단계명*/
			  ,S.LRN_STP_NM AS ORGL_LRN_STP_NM 	/* 원본학습단계명 */
			  ,S.SRT_ORDN 			/* 학습단계순서*/
			  ,'' AS EDU_CRS_CN_CD	/* 교육과정내용코드*/
			  ,'' AS LRN_ATV_THB_PTH /* 학습활동 썸네일 패스 */
			  ,'Y' AS TCR_CTN_YN
			  ,M.DEL_YN
			  ,IFNULL(F.DOC_VI_ID,"") AS DOC_VI_ID
			  ,IFNULL(F.ANNX_FLE_PTH_NM,"") AS ANNX_FLE_PTH_NM
			  ,M.TCR_REG_CTN_ID
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.cm_annx X
			ON C.ANNX_ID = X.ANNX_ID
			AND X.USE_YN = 'Y'
		LEFT JOIN LMS_LRM.cm_annx_fle F
			ON X.ANNX_ID = F.ANNX_ID
			AND F.USE_YN = 'Y'
		WHERE M.OPT_TXB_ID = #{optTxbId}
		AND M.LRMP_NOD_ID = #{lrmpNodId}
		AND M.TCR_REG_CTN_ID = #{tcrRegCtnId}
	</select>
	
	<!-- 원클릭학습설정 복사콘텐츠 조회-->
	<select id="selectCpCtn" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectCpCtn */
		
		  SELECT
				M.OPT_TXB_ID  		/* 운영교과서ID */
		      ,R.LLU_NOD_ID			/* 대단원ID */
		      ,M.LRMP_NOD_ID  		/* 학습맵노드ID */
		      ,IFNULL(C.LRN_ATV_ID, M.TCR_REG_CTN_ID) AS LRN_ATV_ID		/* 학습활동ID */
		      ,#{lrnStpId} AS LRN_STP_ID  		/* 학습단계ID */
			  ,#{lrnStpId} AS ORGL_LRN_STP_ID	/* 원본학습단계ID */
		      ,'' AS CTN_CD  			/* LCMS 학습활동코드 */
		      ,C.TCR_REG_CTN_NM AS LRN_ATV_NM  		/* 문항아이디 or 파일명 */
		      ,C.TP_CD AS CTN_TP_CD  		/* 콘텐츠 타입(QU=문항, HT=HTML5, PL=동영상, EX=평가지) */
		      ,M.USE_YN  			/* 사용여부 */
		      ,M.RCSTN_ORDN  		/* 재구성순서 */
			  ,M.RCSTN_ORDN AS ORGL_ORDN 			/* 원본순서*/
			  ,S.LRN_STP_DV_CD 		/* 학습단계구분코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS LRN_STP_CD /* 학습단계코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS ORGL_LRN_STP_CD	/* 원본학습단계코드 */
			  ,S.LRN_STP_NM 		/* 학습단계명*/
			  ,S.LRN_STP_NM AS ORGL_LRN_STP_NM 	/* 원본학습단계명 */
			  ,S.SRT_ORDN 			/* 학습단계순서*/
			  ,'' AS EDU_CRS_CN_CD	/* 교육과정내용코드*/
			  ,'' AS LRN_ATV_THB_PTH /* 학습활동 썸네일 패스 */
			  ,'Y' AS TCR_CTN_YN
			  ,M.DEL_YN
			  ,IFNULL(F.DOC_VI_ID,"") AS DOC_VI_ID
			  ,IFNULL(F.ANNX_FLE_PTH_NM,"") AS ANNX_FLE_PTH_NM
			  ,'Y' AS CP_YN
			  ,M.TCR_REG_CTN_ID
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.cm_annx X
			ON C.ANNX_ID = X.ANNX_ID
			AND X.USE_YN = 'Y'
		LEFT JOIN LMS_LRM.cm_annx_fle F
			ON X.ANNX_ID = F.ANNX_ID
			AND F.USE_YN = 'Y'
		WHERE M.OPT_TXB_ID = #{optTxbId}
		AND M.LRMP_NOD_ID = #{lrmpNodId}
		AND M.TCR_REG_CTN_ID = #{tcrRegCtnId}
	</select>
	
		
	<!-- 원클릭학습설정 복사활동 조회-->
	<select id="selectCpAtv" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectCpAtv */
		
		 SELECT
				M.OPT_TXB_ID  		/* 운영교과서ID */
		      ,R.LLU_NOD_ID			/* 대단원ID */
		      ,M.LRMP_NOD_ID  		/* 학습맵노드ID */
		      ,IFNULL(C.LRN_ATV_ID, M.TCR_REG_CTN_ID) AS LRN_ATV_ID		/* 학습활동ID */
		      ,#{lrnStpId} AS LRN_STP_ID  		/* 학습단계ID */
			  ,#{lrnStpId} AS ORGL_LRN_STP_ID	/* 원본학습단계ID */
		      ,'' AS CTN_CD  			/* LCMS 학습활동코드 */
		      ,C.TCR_REG_CTN_NM AS LRN_ATV_NM  		/* 문항아이디 or 파일명 */
		      ,C.TP_CD AS CTN_TP_CD  		/* 콘텐츠 타입(QU=문항, HT=HTML5, PL=동영상, EX=평가지) */
		      ,M.USE_YN  			/* 사용여부 */
		      ,M.RCSTN_ORDN  		/* 재구성순서 */
			  ,M.RCSTN_ORDN AS ORGL_ORDN 			/* 원본순서*/
			  ,S.LRN_STP_DV_CD 		/* 학습단계구분코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS LRN_STP_CD /* 학습단계코드*/
			  ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS ORGL_LRN_STP_CD	/* 원본학습단계코드 */
			  ,S.LRN_STP_NM 		/* 학습단계명*/
			  ,S.LRN_STP_NM AS ORGL_LRN_STP_NM 	/* 원본학습단계명 */
			  ,S.SRT_ORDN 			/* 학습단계순서*/
			  ,IFNULL(NM.CRCL_CTN_ELM2_CD, '') AS EDU_CRS_CN_CD	/* 교육과정내용코드*/
			  ,IFNULL(U.CDN_PTH_NM, '') AS LRN_ATV_THB_PTH /* 학습활동 썸네일 패스 */
			  ,'N' AS TCR_CTN_YN
			  ,M.DEL_YN
			  ,"" AS DOC_VI_ID
			  ,"" AS ANNX_FLE_PTH_NM
			  ,'Y' AS CP_YN
			  ,M.TCR_REG_CTN_ID
			  ,AR.LRMP_NOD_ID as ORGL_LRMP_NOD_ID
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		INNER JOIN LMS_LRM.tl_sbc_lrn_atv_rcstn AR
			ON AR.OPT_TXB_ID = #{optTxbId}
			AND AR.LRN_ATV_ID = C.LRN_ATV_ID
		LEFT JOIN LMS_CMS.BC_CTN_MTD MT /* BC_콘텐츠메타데이터 */
            	ON C.LRN_ATV_ID = MT.LRN_ATV_ID
            	AND MT.USE_YN = 'Y'
            	AND MT.DEL_YN = 'N'
        LEFT JOIN LMS_CMS.BC_CTN_MTD_UPL_FLE U /* BC_콘텐츠메타데이터업로드파일 */
            	ON MT.CTN_META_DATA_ID = U.CTN_META_DATA_ID
            	AND U.FLE_TP_CD = 'IM'
            	AND U.DEL_YN = 'N'
        LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 NM /* BC_국가수준교육과정컨텐츠매핑 */
           		ON MT.CTN_META_DATA_ID = NM.CTN_ID
          		AND NM.CRCL_CTN_TP_CD = 'TL'
           		AND NM.DEL_YN = 'N'
		WHERE M.OPT_TXB_ID = #{optTxbId}
		AND M.LRMP_NOD_ID = #{lrmpNodId}
		AND M.TCR_REG_CTN_ID = #{tcrRegCtnId}
	</select>
	
	<!-- 원클릭학습설정 자료 게시물 상세 조회-->
	<select id="selectTcrBlwrInfo" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectTcrBlwrInfo */
		
		   SELECT 	M.OPT_TXB_ID
			 , M.LRMP_NOD_ID
			 , M.TCR_REG_CTN_ID
			 , C.TP_CD AS LSN_MTRL_TP_CD
			 , IF(C.ANNX_ID = 0, NULL, C.ANNX_ID) AS ANNX_ID
			 , C.TCR_REG_CTN_NM AS LSN_SPP_MTRL_NM
			 , C.LRN_BLWR_CN
			FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
				ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
				<if test='previewYn != "Y"'>
					AND M.USE_YN = 'Y'
				</if>
			LEFT JOIN LMS_LRM.cm_annx A
				ON C.ANNX_ID = A.ANNX_ID
				AND A.USE_YN = 'Y'
			LEFT JOIN LMS_LRM.cm_annx_fle F
				ON A.ANNX_ID = F.ANNX_ID
			WHERE OPT_TXB_ID  = #{optTxbId}
			AND M.LRMP_NOD_ID = #{lrmpNodId}
			AND M.TCR_REG_CTN_ID = #{tcrRegCtnId}
	</select>
	
	<!-- 원클릭학습설정 타교과서 목록 조회-->
	<select id="selectExtcmpTxbList" parameterType="Map" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmExtcmpTxbDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectExtcmpTxbList */
		SELECT T.TXB_ID
				,T.PBLS_NM
				,T.PBLS_CD
				,IFNULL(C.CM_CD_NM, T.PBLS_NM) AS CM_CD_NM
				,'Y' as orgn_yn 
		FROM LMS_CMS.bc_txb T
		LEFT JOIN LMS_LRM.CM_CM_CD C
			ON T.PBLS_CD = C.CM_CD
			AND C.URNK_CM_CD = 'PBLS_CD'
			AND C.DEL_YN = 'N'
		WHERE T.TXB_ID = (SELECT O.TXB_ID FROM LMS_LRM.cm_opt_txb O WHERE O.OPT_TXB_ID = #{optTxbId})
		
		UNION ALL
		
		SELECT E.EXTCMP_TXB_ID as TXB_ID
			,MAX(T.PBLS_NM) AS PBLS_NM
			,MAX(T.PBLS_CD) AS PBLS_CD
			,MAX(IFNULL(C.CM_CD_NM, T.PBLS_NM)) AS CM_CD_NM
			,MAX('N') as orgn_yn
		FROM LMS_CMS.bc_extcmp_txb_lrmp_nod_mpn E
		INNER JOIN LMS_CMS.bc_txb T
			ON E.EXTCMP_TXB_ID = T.TXB_ID
			AND T.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.CM_CM_CD C
			ON T.PBLS_CD = C.CM_CD
			AND C.URNK_CM_CD = 'PBLS_CD'
			AND C.DEL_YN = 'N'
		WHERE E.SRC_TXB_ID = (SELECT O.TXB_ID FROM LMS_LRM.cm_opt_txb O WHERE O.OPT_TXB_ID = #{optTxbId})
		GROUP BY E.EXTCMP_TXB_ID
	</select>
	
	<!-- 원클릭학습설정 타교과서 목차 조회-->
	<select id="selectExtcmpTxbInfoList" parameterType="Map" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmExtcmpTxbInfoDto">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectExtcmpTxbInfoList */
		SELECT 
         	<if test='orgnYn == "Y"'>
          	 	N.LRMP_NOD_ID AS LLU_NOD_ID
         	</if>
         	<if test='orgnYn != "Y"'>
         		E.SRC_LRMP_NOD_ID AS LLU_NOD_ID
         	</if>
				,N.LRMP_NOD_NM AS LLU_NOD_NM
				,N.SRT_ORDN
		FROM LMS_CMS.bc_lrmp_nod N
		INNER JOIN LMS_CMS.bc_lrmp L
			ON N.LRMP_ID = L.LRMP_ID
		<if test='orgnYn == "N"'>
          	INNER JOIN LMS_CMS.bc_extcmp_txb_lrmp_nod_mpn E
			ON E.SRC_TXB_ID = (SELECT O.TXB_ID FROM LMS_LRM.cm_opt_txb O WHERE O.OPT_TXB_ID = #{optTxbId})
			AND N.LRMP_NOD_ID = E.EXTCMP_LRMP_NOD_ID
         </if>
		WHERE L.TXB_ID = #{txbId}
		AND L.DEL_YN = 'N'
		AND N.DPTH = 1
		<if test='orgnYn == "Y"'>
          AND N.DEL_YN = 'N' 
          AND N.LU_EPS_YN = 'Y'
        </if>
	</select>
	
	<!-- 원클릭학습설정 타교과서 맵핑 상태 조회-->
	<select id="selectExtcmpTxbMpn" parameterType="Map" resultType="Map">
		/** TlLrnOneClkSetmTcr-Mapper.xml - selectExtcmpTxbMpn */
		
		SELECT O.TXB_ID
				,IFNULL(M.EXTCMP_TXB_ID, "") AS EXTCMP_TXB_ID
		FROM LMS_LRM.cm_opt_txb O
		LEFT JOIN LMS_LRM.tl_extcmp_txb_mpn M
			ON O.OPT_TXB_ID = M.OPT_TXB_ID
		WHERE O.OPT_TXB_ID = #{optTxbId}
	</select>
	
	<!-- 원클릭학습설정 타교과서 맵핑 추가 -->
	<insert id="insertExtcmpCnt" parameterType="Map">
		INSERT INTO LMS_LRM.tl_extcmp_txb_mpn
				(OPT_TXB_ID
				,EXTCMP_TXB_MAP_USE_YN
				,EXTCMP_TXB_ID
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM)
		VALUES
				(#{optTxbId}
				,"Y"
				,#{txbId}
				,#{crtrId}
				,NOW()
				,#{crtrId}
				,NOW())
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertExtcmpCnt */
	</insert>
	
	<!-- 원클릭학습설정 타교과서 맵핑 수정 -->
	<update id="updateExtcmpCnt" parameterType="Map">
		UPDATE LMS_LRM.tl_extcmp_txb_mpn
		SET EXTCMP_TXB_ID = #{txbId}
		WHERE OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateExtcmpCnt */
	</update>
	
	<!-- 외부활동설정 -->
    <select id="selectExtAtvSetm" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm">
        SELECT
        	CLS_PPNG_USE_YN
        	,CLS_ARCHV_USE_YN
        	,PADL_USE_YN
        	,"N" as CANV_USE_YN
        	,MIR_CANV_USE_YN
        	,"N" as GGL_DOC_USE_YN
    	FROM LMS_LRM.cm_ext_atv_setm
    	WHERE OPT_TXB_ID = #{param.optTxbId}
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectExtAtvSetm */
    </select>
    
    <!-- 외부활동추가링크 -->
    <select id="selectExtLink" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink">
        SELECT
        	EXTR_LINK_NO
        	,EXTR_LINK_NM
        	,EXTR_LINK_URL
    	FROM LMS_LRM.cm_ext_atv_extr_link
    	WHERE OPT_TXB_ID = #{param.optTxbId}
    	AND DEL_YN = "N"
    	ORDER BY EXTR_LINK_NO ASC
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectExtAtvSetm */
    </select>
    
    <!-- 원클릭학습설정 외부활동설정 수정 -->
	<update id="updateExtAtvSetm" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm">
		UPDATE LMS_LRM.CM_EXT_ATV_SETM						
		SET CLS_PPNG_USE_YN = #{clsPpngUseYn}
			,CLS_ARCHV_USE_YN = #{clsArchvUseYn}
        	,PADL_USE_YN = #{padlUseYn}
        	,MIR_CANV_USE_YN = #{mirCanvUseYn}
			,MDFR_ID = #{mdfrId}
			,MDF_DTM = NOW()
		WHERE OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateExtAtvSetm */
	</update>
	
	<!-- 원클릭학습설정 외부활동링크 수정 -->
	<update id="updateExtAtvLink" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink">
		UPDATE LMS_LRM.CM_EXT_ATV_EXTR_LINK						
		SET EXTR_LINK_NM = #{extrLinkNm}
			,EXTR_LINK_URL = #{extrLinkUrl}
			,DEL_YN = #{delYn}
			,MDFR_ID = #{mdfrId}
			,MDF_DTM = NOW()
		WHERE OPT_TXB_ID = #{optTxbId}
		AND EXTR_LINK_NO = #{extrLinkNo}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateExtAtvLink */
	</update>
	
	<!-- 원클릭학습설정 외부활동링크 삭제 -->
	<update id="deleteExtAtvLink" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink">
		DELETE FROM LMS_LRM.CM_EXT_ATV_EXTR_LINK						
		WHERE OPT_TXB_ID = #{optTxbId}
		AND EXTR_LINK_NO = #{extrLinkNo}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - deleteExtAtvLink */
	</update>
	
	<insert id="insertExtAtvLink" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink">
		INSERT INTO LMS_LRM.CM_EXT_ATV_EXTR_LINK
				(OPT_TXB_ID
				,EXTR_LINK_NO
				,EXTR_LINK_NM
				,EXTR_LINK_URL
				,DEL_YN
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM)
		VALUES (#{optTxbId}
				,#{extrLinkNo}
				,#{extrLinkNm}
				,#{extrLinkUrl}
				,"N"
				,#{mdfrId}
				,NOW()
				,#{mdfrId}
				,NOW()
		)
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertExtAtvLink */
	</insert>
	
	<select id="selectExtrLinkNo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink" resultType="int">
		SELECT IFNULL(MAX(EXTR_LINK_NO),0) 
		FROM LMS_LRM.CM_EXT_ATV_EXTR_LINK 
		WHERE OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectExtrLinkNo */
	</select>
	
	<!-- 원클릭학습설정 다른 학급 외부활동설정 저장 -->
	<update id="updateAntClaExtAtvLink" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
		UPDATE LMS_LRM.cm_ext_atv_setm A,
				(SELECT CLS_PPNG_USE_YN,
						CLS_LINK_USE_YN,
						CLS_ARCHV_USE_YN,
						CLS_TSHRP_USE_YN,
						PADL_USE_YN,
						CANV_USE_YN,
						MIR_CANV_USE_YN,
						GGL_DOC_USE_YN
				 FROM LMS_LRM.cm_ext_atv_setm
				 WHERE OPT_TXB_ID = #{orgnOptTxbId}) B
		SET A.CLS_PPNG_USE_YN = B.CLS_PPNG_USE_YN,
			A.CLS_LINK_USE_YN = B.CLS_LINK_USE_YN,
			A.CLS_ARCHV_USE_YN = B.CLS_ARCHV_USE_YN,
			A.CLS_TSHRP_USE_YN = B.CLS_TSHRP_USE_YN,
			A.PADL_USE_YN = B.PADL_USE_YN,
			A.CANV_USE_YN = B.CANV_USE_YN,
			A.MIR_CANV_USE_YN = B.MIR_CANV_USE_YN,
			A.GGL_DOC_USE_YN = B.GGL_DOC_USE_YN,
			A.MDFR_ID = #{mdfrId},
			A.MDF_DTM = NOW()
		WHERE A.OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - updateAntClaExtAtvLink */
	</update>
	
	<!-- 원클릭학습설정 다른 학급 외부활동링크 삭제 -->
	<delete id="deleteAntClaExtAtvLink" parameterType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto">
		DELETE FROM LMS_LRM.cm_ext_atv_extr_link
		WHERE OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - deleteAntClaExtAtvLink */
	</delete>
	
	<insert id="insertAntClaExtAtvLink" parameterType="Map">
		INSERT INTO LMS_LRM.CM_EXT_ATV_EXTR_LINK
				(OPT_TXB_ID
				,EXTR_LINK_NO
				,EXTR_LINK_NM
				,EXTR_LINK_URL
				,DEL_YN
				,CRTR_ID
				,CRT_DTM
				,MDFR_ID
				,MDF_DTM)
		 SELECT
        	#{optTxbId}                 /* 운영교과서ID */
			,EXTR_LINK_NO
			,EXTR_LINK_NM
			,EXTR_LINK_URL
			,DEL_YN
            ,#{usrId}                     /* 생성자ID */
            ,CRT_DTM                    /* 생성일시 */
            ,#{usrId}                     /* 수정자ID */
            ,NOW()                    /* 수정일시 */
        FROM LMS_LRM.CM_EXT_ATV_EXTR_LINK M2
        WHERE M2.OPT_TXB_ID = #{orgnOptTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - insertAntClaExtAtvLink */
	</insert>
	
	<!-- 원클릭학습설정 교과학습,활동 정보 조회 -->
	<select id="selectTxbAtvInfo" parameterType="Map" resultType="Map">
		select max(n1.DPTH) as dpth
			, max(n1.llu_NOD_ID) as llu_nod_id
			, max(n1.LRMP_NOD_ID) as lrmp_nod_id
			, max(n1.LCKN_YN) as lckn_yn
			, max(n1.USE_YN) as use_yn 
			, count(case when a1.use_yn = 'Y' and a1.del_yn = 'N' then 1 end) as atv_cnt 
		from lms_lrm.tl_sbc_lrn_nod_rcstn n1
		inner join (select r.lrn_atv_id
							,r.lrmp_nod_id
							,r.lrn_stp_id
							,r.opt_txb_id
							,'N' as del_yn
							,r.use_yn
					from lms_lrm.tl_sbc_lrn_atv_rcstn r
					where r.OPT_TXB_ID = #{optTxbId}

					union all

					select m.TCR_REG_CTN_ID as lrn_atv_id
							,m.LRMP_NOD_ID
							,m.LRN_STP_ID
							,m.OPT_TXB_ID
							,m.DEL_YN 
							,m.USE_YN 
					from lms_lrm.tl_tcr_reg_ctn_mpn m
					where m.OPT_TXB_ID = #{optTxbId}
					and m.USE_YN = 'Y'
					and m.DEL_YN = 'N') a1
					
			on a1.OPT_TXB_ID = #{optTxbId}
			and a1.LRMP_NOD_ID = n1.LRMP_NOD_ID
		inner join lms_cms.bc_lrn_stp s1
			on s1.LRMP_NOD_ID = a1.LRMP_NOD_ID
			and a1.LRN_STP_ID = s1.LRN_STP_ID
		where n1.OPT_TXB_ID = #{optTxbId}
		and n1.LU_EPS_YN = 'Y'
		and n1.dpth = 4
		and s1.LRN_STP_DV_CD = 'CL'
		group by n1.LRMP_NOD_ID;
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectTxbAtvInfo */
	</select>
	
	<!-- 원클릭학습설정 교과학습,활동 정보 조회 -->
	<select id="selectTxbLluInfo" parameterType="Map" resultType="Map">
		select n2.opt_txb_id,
		 n2.lrmp_nod_id,
		 <if test='use == "Y"'>
          n1.use_yn
        </if>
        <if test='use != "Y"'>
          n1.lckn_yn
        </if>
        from lms_lrm.tl_sbc_lrn_nod_rcstn n1
		inner join lms_lrm.tl_sbc_lrn_nod_rcstn n2
			on n2.OPT_TXB_ID = #{optTxbId}
			and n1.lrmp_nod_id = n2.lrmp_nod_id
			and n2.dpth = 1
			and n2.LU_EPS_YN = 'Y'
		where n1.OPT_TXB_ID = #{orgnOptTxbId}
		and n1.LU_EPS_YN = 'Y'
		<if test='use == "Y"'>
          and n1.use_yn != n2.use_yn
        </if>
        <if test='use != "Y"'>
          and n1.lckn_yn != n2.lckn_yn
        </if>
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectTxbLluInfo */
	</select>
	
	<!-- 원클릭학습설정 교과 재구성 데이터 확인 -->
	<select id="selectRcstnCnt" parameterType="Map" resultType="int">
		select count(*) from lms_lrm.tl_sbc_lrn_nod_rcstn where OPT_TXB_ID = #{optTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectRcstnCnt */
	</select>
	
	<!-- 원클릭학습설정 추천 콘텐츠 학생 목록 조회 -->
	<select id="selectRcmCtnStuList" parameterType="Map" resultType="com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmStuDto">
		select
			r.sp_lrn_id,
			u.usr_nm,
			u.usr_id,
			u.stu_no as usr_no
		from lms_lrm.sl_stu_rcm_lrn r
		inner join lms_lrm.cm_usr u
			on u.USR_ID = r.lrn_usr_id
		where r.OPT_TXB_ID = #{optTxbId}
		and r.rcm_yn = 'Y'
		order by u.stu_no asc
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectRcmCtnStuList */
	</select>
	
	<!-- 원클릭학습설정 추천 콘텐츠 학생 목록 조회 -->
	<select id="selectTotStuCnt" parameterType="String" resultType="int">
		select COUNT(*) 
		from LMS_LRM.cm_opt_txb O
		inner JOIN LMS_LRM.cm_usr U
			on U.CLA_ID = O.CLA_ID
			and U.USR_TP_CD = 'ST'
		where O.OPT_TXB_ID = #{orgnOptTxbId}
		/** 교과학습 김형준 TlLrnOneClkSetmTcr-Mapper.xml - selectTotStuCnt */
	</select>
</mapper>