package com.aidt.api.xx.sample.mybatis.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample Mybatis Save DTO
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SampleMybatisSaveDto {

    @Parameter(name="등록 리스트")
    private List<SampleMybatisDto> insert;

    @Parameter(name="수정 리스트")
    private List<SampleMybatisDto> update;

    @Parameter(name="삭제 리스트")
    private List<SampleMybatisDto> delete;
}
