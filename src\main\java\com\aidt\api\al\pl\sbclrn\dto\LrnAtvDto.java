package com.aidt.api.al.pl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-23 10:00:32
 * @modify date 2024-02-23 10:00:32
 * @desc [학습목록조회 학습단계 > 활동 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class LrnAtvDto {
	@Parameter(name="활동ID")
	private String lrnAtvId;
	
	@Parameter(name="활동명")
	private String lrnAtvNm;
	
	@Parameter(name="콘텐츠유형코드")
	private String ctnTpCd;
	
	@Parameter(name="컨텐츠URL")
	private String ctnUrl;
	
	@Parameter(name="활동정렬순서")
	private int rcstnOrdn;
	
	@Parameter(name="학습상태")
	private String lrnStCd;
	
	@Parameter(name="학습시간초수")
	private String lrnTmScnt;
	
//	@Parameter(name="이미지")
//	private String imgCdn;
//	
//	@Parameter(name="문항ID")
//	private int qtmId;
//	
//	@Parameter(name="동영상")
//	private LrnVdsDto vds;
}
