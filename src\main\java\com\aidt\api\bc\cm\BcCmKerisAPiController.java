package com.aidt.api.bc.cm;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.appevent.service.AdminStatKafkaService;
import com.aidt.api.bc.cm.constants.BcCmKerisApiEnum;
import com.aidt.api.bc.cm.dto.BcCmKerisAPiDto;
import com.aidt.api.bc.cm.dto.BcCmKerisBodyDto;
import com.aidt.api.bc.cm.dto.BcCmKerisConnErr;
import com.aidt.api.bc.cm.dto.BcCmKerisConnLog;
import com.aidt.api.bc.cm.dto.BcCmKerisDashboardDto;
import com.aidt.api.bc.cm.dto.BcCmKerisFrontReqLogDto;
import com.aidt.api.bc.cm.dto.BcCmNtlvEduCrsStnSstDto;
import com.aidt.api.bc.cm.dto.BcLoginDto;
import com.aidt.api.bc.cm.dto.BcUsrInfoDto;
import com.aidt.api.bc.cm.dto.KerisDashboardCheckDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "[bc] 공통[BcCmKeris]", description = "공통")
@RestController
@RequestMapping("/api/v1/bc/cm/keris")
public class BcCmKerisAPiController {

	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	private final KerisWebClientUtil KerisWebClientUtil = new KerisWebClientUtil();

	private final JwtProvider jwtProvider;
	private final BcCmService bcCmService;
	private final AdminStatKafkaService adminStatKafkaService;

	private final String SUCCESS_CODE = "00000";

	public BcCmKerisAPiController(BcCmService bcCmService, JwtProvider jwtProvider,
			AdminStatKafkaService adminStatKafkaService) {
		this.bcCmService = bcCmService;
		this.jwtProvider = jwtProvider;
		this.adminStatKafkaService = adminStatKafkaService;
	}

	@Operation(summary = "BcCmKeris 조회", description = "BcCmKeris 조회")
	@PostMapping(value = "/selectKerisAidt")
	public ResponseDto<BcCmKerisBodyDto> selectKerisAidt(@RequestBody BcCmKerisAPiDto cmKerisAPiDto) {
		try {
			Map<String, Object> paramMap = this.makeParamMap(cmKerisAPiDto);
			String apiUrl = cmKerisAPiDto.getApiUrl();
			String partnerId = cmKerisAPiDto.getPartnerId();

			BcCmKerisBodyDto result = KerisWebClientUtil.executePostJsonRequest(paramMap, apiUrl, partnerId);

			return Response.ok(result);
		} catch (Exception e) {
			e.printStackTrace();
			return Response.fail();
		}

	}

	@Operation(summary = "BcCmKeris 유저 정보 조회", description = "BcCmKeris 유저 정보 조회")
	@PostMapping(value = "/selectKerisUserInfo")
	public ResponseDto<Map<String, Object>> selectKerisUserInfo(@RequestBody BcCmKerisAPiDto cmKerisAPiDto) {

		// @TODO 테스트에 필요한 데이터를 받으면 return ok 처리
		if ("test".equals(cmKerisAPiDto.getUser_type())) {
			return Response.ok(null);
		}

		String userType = cmKerisAPiDto.getUser_type();
		Map<String, Object> responseMap = new HashMap<>();
		try {
			if ("T".equals(userType)) {
				responseMap = this.selectTeacher(cmKerisAPiDto);
				return Response.ok(responseMap);
			} else if ("S".equals(userType)) {
				responseMap = this.selectStudent(cmKerisAPiDto);
				return Response.ok(responseMap);
			}

		} catch (Exception e) {
			e.printStackTrace();
			responseMap.put("controller error", e.getMessage());
		}

		return Response.ok(responseMap);
	}

	private Map<String, Object> selectStudent(BcCmKerisAPiDto cmKerisAPiDto) {

		Map<String, Object> responseMap = new HashMap<>();

		// CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();

		try {
			Map<String, Object> paramMap = this.makeParamMap(cmKerisAPiDto);
			String partnerId = cmKerisAPiDto.getPartnerId();
			String apiUrl = cmKerisAPiDto.getApiUrl();

			/*
			 * 2025.02.12 AA 가이드 수정 - 동기 -> 비동기 방식으로 병렬 call 진행 후 결과부분에서 동기 처리
			 * BcCmKerisBodyDto stuAll = KerisWebClientUtil.executePostJsonRequest(paramMap,
			 * apiUrl + BcCmKerisApiEnum.STU_ALL.getUrl(), partnerId); BcCmKerisBodyDto
			 * classMembers = KerisWebClientUtil.executePostJsonRequest(paramMap, apiUrl +
			 * BcCmKerisApiEnum.STU_CLASS_MEMBER.getUrl(), partnerId);
			 */

			Mono<BcCmKerisBodyDto> response1 = KerisWebClientUtil.executePostJsonRequestAsync(paramMap,
					apiUrl + BcCmKerisApiEnum.STU_ALL.getUrl(), partnerId);

			Mono<BcCmKerisBodyDto> response2 = null;

			// 책장 서비스(lecture_code=none) 시 장애 발생으로 발송하지 않는다.
			if (StringUtils.isNotBlank(cmKerisAPiDto.getLecture_code())
					&& !"none".equals(cmKerisAPiDto.getLecture_code().toLowerCase())) {
				response2 = KerisWebClientUtil.executePostJsonRequestAsync(paramMap,
						apiUrl + BcCmKerisApiEnum.STU_CLASS_MEMBER.getUrl(), partnerId);
			} else {
				response2 = Mono.just(new BcCmKerisBodyDto());
			}

			Map<BcCmKerisApiEnum, BcCmKerisBodyDto> responseResultMap = Mono.zip(response1, response2)
					.map(t -> Map.of(BcCmKerisApiEnum.STU_ALL, t.getT1(), BcCmKerisApiEnum.STU_CLASS_MEMBER, t.getT2()))
					.block();

			BcCmKerisBodyDto stuAll = responseResultMap.get(BcCmKerisApiEnum.STU_ALL);
			BcCmKerisBodyDto classMembers = responseResultMap.get(BcCmKerisApiEnum.STU_CLASS_MEMBER);

			BcUsrInfoDto bcUsrInfoDto = this.makeLectureTcrUserInfo(cmKerisAPiDto, stuAll.getSchool_id());
			if (bcUsrInfoDto != null) {
				bcCmService.checkLectureTcrUserInfo(bcUsrInfoDto);
			}

			/*
			 * 2025.02.12 AA 가이드 선생님 이름 홈에서 사용하는 부분에서 api 통신 해당 부분 사용 시
			 * bcCmService.selectStuUser(loginInfo) 쿼리 대체 필요 BcLoginDto loginInfo =
			 * BcLoginDto.builder().usrId(userDetail.getUsrId()).optTxbId(userDetail.
			 * getOptTxbId()) .claId(userDetail.getClaId()).subDomain(DB_ID).build();
			 * 
			 * BcUsrInfoDto studentUser = bcCmService.selectStuUser(loginInfo); Map<String,
			 * Object> newParam = new HashMap<>(paramMap); newParam.put("user_id",
			 * studentUser.getChgTcrUsrId());
			 * 
			 * BcCmKerisBodyDto tcrName =
			 * KerisWebClientUtil.executePostJsonRequest(newParam, apiUrl +
			 * BcCmKerisApiEnum.TCR_NAME.getUrl(), partnerId);
			 */

			/**
			 * 관리자 학급, 운영교과서 event 통계 set - 책장 제외
			 */
			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
					&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaClaId())) {
				adminStatKafkaService.claInsertEvent(bcUsrInfoDto.getKafkaClaId());
			}

			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
					&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaOptTxbId())) {
				adminStatKafkaService.optTxbInsertEvent(bcUsrInfoDto.getKafkaOptTxbId());
			}

			/**
			 * 회원 등록 event 통계 set - 책장 제외
			 */
			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
					&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaUsrId())) {
				adminStatKafkaService.usrInsertEvent(bcUsrInfoDto.getKafkaUsrId());
			}

			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone() && bcUsrInfoDto.getKafkaUsrIds() != null) {
				adminStatKafkaService.usrInsertEvent(bcUsrInfoDto.getKafkaUsrIds());
			}

			responseMap.put("stuAll", stuAll);
			responseMap.put("classMember", classMembers);
			// responseMap.put("tcrName", tcrName);

			return responseMap;
		} catch (Exception e) {
			e.printStackTrace();
			responseMap.put("error", e.getMessage());
		}

		return responseMap;
	}

	private Map<String, Object> selectTeacher(BcCmKerisAPiDto cmKerisAPiDto) {
		Map<String, Object> responseMap = new HashMap<>();
		try {
			Map<String, Object> paramMap = this.makeParamMap(cmKerisAPiDto);
			String partnerId = cmKerisAPiDto.getPartnerId();
			String apiUrl = cmKerisAPiDto.getApiUrl();

			/*
			 * 2025.02.12 AA 가이드 수정 - 동기 -> 비동기 방식으로 병렬 call 진행 후 결과부분에서 동기 처리
			 * BcCmKerisBodyDto teacherAll =
			 * KerisWebClientUtil.executePostJsonRequest(paramMap, apiUrl +
			 * BcCmKerisApiEnum.TCR_ALL.getUrl(), partnerId); BcCmKerisBodyDto classMembers
			 * = KerisWebClientUtil.executePostJsonRequest(paramMap, apiUrl +
			 * BcCmKerisApiEnum.TCR_CLASS_MEMBER.getUrl(), partnerId);
			 */

			Mono<BcCmKerisBodyDto> response1 = KerisWebClientUtil.executePostJsonRequestAsync(paramMap,
					apiUrl + BcCmKerisApiEnum.TCR_ALL.getUrl(), partnerId);

			Mono<BcCmKerisBodyDto> response2 = null;

			// 책장 서비스(lecture_code=none) 시 장애 발생으로 발송하지 않는다.
			if (StringUtils.isNotBlank(cmKerisAPiDto.getLecture_code())
					&& !"none".equals(cmKerisAPiDto.getLecture_code().toLowerCase())) {
				response2 = KerisWebClientUtil.executePostJsonRequestAsync(paramMap,
						apiUrl + BcCmKerisApiEnum.TCR_CLASS_MEMBER.getUrl(), partnerId);
			} else {
				response2 = Mono.just(new BcCmKerisBodyDto());
			}

			Map<BcCmKerisApiEnum, BcCmKerisBodyDto> responseResultMap = Mono.zip(response1, response2)
					.map(t -> Map.of(BcCmKerisApiEnum.TCR_ALL, t.getT1(), BcCmKerisApiEnum.TCR_CLASS_MEMBER, t.getT2()))
					.block();

			BcCmKerisBodyDto teacherAll = responseResultMap.get(BcCmKerisApiEnum.TCR_ALL);
			BcCmKerisBodyDto classMembers = responseResultMap.get(BcCmKerisApiEnum.TCR_CLASS_MEMBER);

			BcCmKerisBodyDto stuAll = new BcCmKerisBodyDto();

			BcUsrInfoDto bcUsrInfoDto = this.makeLectureTcrUserInfo(cmKerisAPiDto, teacherAll.getSchool_id());

			if (classMembers.getMember_info() != null && !classMembers.getMember_info().isEmpty()) {
				List<BcUsrInfoDto> stuList = classMembers.getMember_info().stream()
						.filter(item -> item.getUser_id() != null)
						.map(item -> BcUsrInfoDto.builder().user_id(item.getUser_id()).build())
						.collect(Collectors.toList());

				if (!stuList.isEmpty()) {
					bcUsrInfoDto.setStuList(stuList);
				}
				Map<String, Object> newParam = new HashMap<>(paramMap);
				newParam.put("user_id", classMembers.getMember_info().get(0).getUser_id());
				stuAll = KerisWebClientUtil.executePostJsonRequest(newParam, apiUrl + BcCmKerisApiEnum.STU_ALL.getUrl(),
						partnerId);
			}

			bcCmService.checkLectureTcrUserInfo(bcUsrInfoDto);

			/**
			 * 관리자 학급, 운영교과서 event 통계 set - 책장, 미리보기 제외
			 */
			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
					&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaClaId())) {
				adminStatKafkaService.claInsertEvent(bcUsrInfoDto.getKafkaClaId());
			}

			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
					&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaOptTxbId())) {
				adminStatKafkaService.optTxbInsertEvent(bcUsrInfoDto.getKafkaOptTxbId());
			}

			/**
			 * 회원 등록 event 통계 set - 책장, 미리보기 제외
			 */
			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
					&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaUsrId())) {
				adminStatKafkaService.usrInsertEvent(bcUsrInfoDto.getKafkaUsrId());
			}

			if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone() && bcUsrInfoDto.getKafkaUsrIds() != null) {
				adminStatKafkaService.usrInsertEvent(bcUsrInfoDto.getKafkaUsrIds());
			}

			responseMap.put("teacherAll", teacherAll);
			responseMap.put("classMember", classMembers);
			responseMap.put("stuAll", stuAll);

			return responseMap;

		} catch (Exception e) {
			e.printStackTrace();
			responseMap.put("error", e.getMessage());
		}

		return responseMap;
	}

	private Map<String, Object> makeParamMap(BcCmKerisAPiDto cmKerisAPiDto) {

		Map<String, String> token = new HashMap<>();
		token.put("token", cmKerisAPiDto.getToken());
		token.put("access_id", cmKerisAPiDto.getAccess_id());

		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("access_token", token);

		if (cmKerisAPiDto.getUser_ids() != null && !cmKerisAPiDto.getUser_ids().isEmpty()) {
			paramMap.put("user_ids", cmKerisAPiDto.getUser_ids());
		} else {
			paramMap.put("user_id", cmKerisAPiDto.getUser_id());
		}

		if (cmKerisAPiDto.getClass_code() != null && !cmKerisAPiDto.getClass_code().isEmpty()) {
			paramMap.put("class_code", cmKerisAPiDto.getClass_code());
		}
		if (cmKerisAPiDto.getLecture_code() != null && !cmKerisAPiDto.getLecture_code().isEmpty()) {
			paramMap.put("lecture_code", cmKerisAPiDto.getLecture_code());
		}

		return paramMap;
	}

	private BcUsrInfoDto makeLectureTcrUserInfo(BcCmKerisAPiDto cmKerisAPiDto, String schoolId) {
		return BcUsrInfoDto.builder().lectureCd(cmKerisAPiDto.getLecture_code()).usrId(cmKerisAPiDto.getUser_id())
				.userType(cmKerisAPiDto.getUser_type()).schlCd(schoolId).classCode(cmKerisAPiDto.getClass_code())
				.build();
	}

	@SuppressWarnings("unchecked")
	@Operation(summary = "Keris 데이터 수집 연동", description = "Keris 데이터 수집 연동")
	@PostMapping(value = "/aidtDashboard")
	public ResponseDto<Map<String, Object>> aidtDashboard(@RequestBody BcCmKerisDashboardDto dto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		BcCmKerisConnLog connLog = new BcCmKerisConnLog();
		boolean result = false;
		String errMsg = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("act_type", dto.getAct_type());

		// api result code
		String arc = SUCCESS_CODE;

		/** keris conn log 기초 set start **/
		connLog.setUsrId(dto.getUser_id());
		connLog.setCrclCtnElm2Cd(dto.getCurriculum());
		connLog.setActCd(dto.getAct_type());
		connLog.setPrnrId(dto.getPartner_id());
		connLog.setApiVer(KerisWebClientUtil.getKerisApiVersion());

		// 2025.03.12 cm_usr.usr_id 난수생성 id로 변경됨에 따라 학습자 구분용 lrnUsrId 추가
		connLog.setLrnUsrId(userDetails.getUsrId());
		connLog.setOptTxbId(userDetails.getOptTxbId());
		/** keris conn log 기초 set end **/

		if (StringUtils.isNotBlank(dto.getAct_type())) {
			try {
				/**
				 * 2025.03.12 1. parameter user_id(kerisUsrId) 와 token에 담긴 kerisUsrId와 비교하여 틀린
				 * 경우 진행불가 처리 2. 학습자만 진행되도록 추가
				 */
				boolean next = true;

				// parameter user_id(kerisUsrId) 와 token kerisUsrId 비교
				if (!dto.getUser_id().equals(userDetails.getKerisUsrId())) {
					next = false;

					result = false;
					arc = "ERR";
					errMsg = "요청 user_id와 token의 kerisUsrId 값이 틀립니다. 요청 user_id = ";
					errMsg += dto.getUser_id();
					errMsg += " / token kerisUsrId = ";
					errMsg += userDetails.getKerisUsrId();
				} else if (!"ST".equals(userDetails.getUsrTpCd())) {
					next = false;

					result = false;
					arc = "PASS";
					errMsg = "학생의 요청이 아닙니다. 요청 권한 코드 = " + userDetails.getUsrTpCd();
				}

				if (next) {
					Map<String, Object> apiResult = null;

					BcCmNtlvEduCrsStnSstDto paramDto = new BcCmNtlvEduCrsStnSstDto();
					paramDto.setUsrId(dto.getUser_id()); // kerisUsrId
					paramDto.setCrclCtnElm2Cd(dto.getCurriculum());
					paramDto.setCrtrId(userDetails.getUsrId());
					paramDto.setMdfrId(userDetails.getUsrId());
					paramDto.setDbId(DB_ID);

					// 2025.03.12 cm_usr.usr_id keris_usr_id -> 난수 발생 ID 변경에 따른 추가
					paramDto.setLrnUsrId(userDetails.getUsrId());

					BcCmNtlvEduCrsStnSstDto sstDto = null;

					switch (dto.getAct_type()) {
					// initialized (초기화) : 커리큘럼 별 한번만 keris api 통신
					case "ST":
						sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);

						if (sstDto == null || StringUtil.isBlank(sstDto.getLrnStrYn())
								|| !"Y".equals(sstDto.getLrnStrYn())) {
							Map<String, Object> param = new HashMap<>();
							dashboardCommonParam(dto, param);

							log.debug("parameter >>> " + param);

							/** keris conn log set **/
							connLog.setConnUrl(dto.getApi_domain() + BcCmKerisApiEnum.INITIALIZED.getUrl());
							connLog.setBodyCn(new ObjectMapper().writeValueAsString(param));

							apiResult = KerisWebClientUtil.executePostJsonRequestMapFromString(param,
									dto.getApi_domain() + BcCmKerisApiEnum.INITIALIZED.getUrl(), dto.getPartner_id());
							log.debug("Keris API /aidt_dashboard/initialized result >>> " + apiResult);

							if (apiResult != null) {
								// 성공
								if (SUCCESS_CODE.equals(MapUtils.getString(apiResult, "code", ""))) {
									if (!"-1".equals(paramDto.getCrclCtnElm2Cd())) {
										paramDto.setOptTxbId(userDetails.getOptTxbId());
										paramDto.setTxbId(userDetails.getTxbId());

										paramDto.setLrnStrYn("Y");

										// initialized 등록 처리
										bcCmService.saveNtlvEduCrsStnSstLrnStr(paramDto);
									}

									result = true;
								}
								// 실패
								else {
									arc = MapUtils.getString(apiResult, "code");
									errMsg = MapUtils.getString(apiResult, "message");

									log.error("Keris API /aidt_dashboard/initialized fail >>>" + apiResult.toString());

									dashboardErr(dto.getApi_domain() + BcCmKerisApiEnum.INITIALIZED.getUrl(),
											dto.getPartner_id(), param, apiResult, userDetails.getUsrId(),
											userDetails.getOptTxbId());
								}
							} else {
								arc = "FAIL";
								errMsg = "Keris API /aidt_dashboard/initialized Result value is null.";
							}

						}
						// 이미 initialized 등록
						else {
							result = true;
							arc = "PASS";
							errMsg = "이미 initialized가 진행되었습니다.";
						}

						break;
					// curriculum_progresseds (진도율) : 호출 시 마다 keris api 통신
					case "IN":
						if (!"-1".equals(paramDto.getCrclCtnElm2Cd())) {
							sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);

							// initialized 등록된 경우에만 처리
							if (sstDto != null && StringUtil.isNotBlank(sstDto.getLrnStrYn())
									&& "Y".equals(sstDto.getLrnStrYn())) {
								// 완료처리가 되지 않은 경우 처리
								if (sstDto.getLrnCmplYn() == null || !"Y".equals(sstDto.getLrnCmplYn())) {
									/*
									 * 테스트용 if (StringUtil.isNotBlank(dto.getTestYn()) &&
									 * "Y".equals(dto.getTestYn())) { paramDto.setTestYn(true);
									 * paramDto.setPgrsRt(dto.getPercent()); } else { paramDto.setTestYn(false); }
									 */

									// 이미 진도율 100인 경우 update 안함
									if (!(StringUtil.isNotBlank(sstDto.getPgrsRt())
											&& "100".equals(sstDto.getPgrsRt()))) {
										paramDto.setOptTxbId(sstDto.getOptTxbId());
										paramDto.setTxbId(sstDto.getTxbId());

										// 진도율 등록 처리
										bcCmService.saveNtlvEduCrsStnSstPgrsRt(paramDto, sstDto.getPgrsRt());
									} else {
										paramDto.setPgrsRt(sstDto.getPgrsRt());
									}

									Map<String, Object> param = new HashMap<>();
									dashboardCommonParam(dto, param);

									// parameter 진도율(학습 경과도) 추가
									param.put("percent", paramDto.getPgrsRt());

									log.debug("parameter >>> " + param);

									/** keris conn log set **/
									connLog.setConnUrl(dto.getApi_domain() + BcCmKerisApiEnum.PROGRESSED.getUrl());
									connLog.setBodyCn(new ObjectMapper().writeValueAsString(param));

									apiResult = KerisWebClientUtil.executePostJsonRequestMapFromString(param,
											dto.getApi_domain() + BcCmKerisApiEnum.PROGRESSED.getUrl(),
											dto.getPartner_id());
									log.debug(
											"Keris API /aidt_dashboard/curriculum_progressed result >>> " + apiResult);

									if (apiResult != null) {
										// 성공
										if (SUCCESS_CODE.equals(MapUtils.getString(apiResult, "code", ""))) {
											// 진도율, 평가 완료 시 curriculum_completed 실행 및 terminated
											dashboardCompleteCheck(paramDto, dto, resultMap, userDetails);

											if (resultMap.containsKey("complete_result")) {
												String cmpCode = MapUtils.getString(
														(HashMap<String, Object>) resultMap.get("complete_result"),
														"code");

												if (StringUtil.isNotBlank(cmpCode) && SUCCESS_CODE.equals(cmpCode)) {
													if (resultMap.containsKey("terminated_result")) {
														String terCode = MapUtils
																.getString((HashMap<String, Object>) resultMap
																		.get("terminated_result"), "code");

														if (StringUtil.isNotBlank(terCode)
																&& SUCCESS_CODE.equals(terCode)) {
															result = true;
														}
													} else {
														result = true;
													}
												}
											} else {
												result = true;
											}
										}
										// 실패
										else {
											log.error("Keris API /aidt_dashboard/curriculum_progressed fail >>>"
													+ apiResult.toString());

											arc = MapUtils.getString(apiResult, "code");
											errMsg = MapUtils.getString(apiResult, "message");

											dashboardErr(dto.getApi_domain() + BcCmKerisApiEnum.PROGRESSED.getUrl(),
													dto.getPartner_id(), param, apiResult, userDetails.getUsrId(),
													userDetails.getOptTxbId());
										}
									} else {
										arc = "FAIL";
										errMsg = "Keris API /aidt_dashboard/curriculum_progressed Result value is null.";
									}
								} else {
									result = true;
									arc = "PASS";
									errMsg = "이미 completed된 상태입니다.";
								}
							} else {
								arc = "FAIL";
								errMsg = "initialized가 진행되지 않은 상태 입니다.";
							}
						} else {
							result = true;
							arc = "PASS";
							errMsg = "curriculum 값이 -1 입니다.";
						}

						break;
					// curriculum_score (스코어) : 호출 시 마다 keris api 통신
					case "PT":
						if (!"-1".equals(paramDto.getCrclCtnElm2Cd())) {
							sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);

							// initialized 등록된 경우에만 처리
							if (sstDto != null && StringUtil.isNotBlank(sstDto.getLrnStrYn())
									&& "Y".equals(sstDto.getLrnStrYn())) {
								// 완료처리가 되지 않은 경우 처리
								if (sstDto.getLrnCmplYn() == null || !"Y".equals(sstDto.getLrnCmplYn())) {
									try {
										if (StringUtil.isNotBlank(dto.getScore())) {
											int idx = dto.getScore().indexOf(".");

											log.debug("SCORE 소수점 >>> " + String.valueOf(idx));

											if (idx > -1) {
												dto.setScore(dto.getScore().substring(0, idx));
											}

											log.debug("SCORE >>> " + dto.getScore());

											paramDto.setEvScr(Long.parseLong(dto.getScore()));
											paramDto.setEduCrsAchBsCd(dto.getAchievement_level());
											paramDto.setOptTxbId(sstDto.getOptTxbId());
											paramDto.setTxbId(sstDto.getTxbId());

											// 스코어 등록 처리
											bcCmService.updateNtlvEduCrsStnSstEvScr(paramDto);

											Map<String, Object> param = new HashMap<>();
											dashboardCommonParam(dto, param);

											// parameter 점수 추가
											param.put("score", dto.getScore());

											log.debug("parameter >>> " + param);

											/** keris conn log set **/
											connLog.setConnUrl(dto.getApi_domain() + BcCmKerisApiEnum.SCORE.getUrl());
											connLog.setBodyCn(new ObjectMapper().writeValueAsString(param));

											apiResult = KerisWebClientUtil.executePostJsonRequestMapFromString(param,
													dto.getApi_domain() + BcCmKerisApiEnum.SCORE.getUrl(),
													dto.getPartner_id());
											log.debug("Keris API /aidt_dashboard/curriculum_score result >>> "
													+ apiResult);

											if (apiResult != null) {
												// 성공
												if (SUCCESS_CODE.equals(MapUtils.getString(apiResult, "code", ""))) {
													// 진도율, 평가 완료 시 curriculum_completed 실행 및 terminated
													dashboardCompleteCheck(paramDto, dto, resultMap, userDetails);

													if (resultMap.containsKey("complete_result")) {
														String cmpCode = MapUtils
																.getString((HashMap<String, Object>) resultMap
																		.get("complete_result"), "code");

														if (StringUtil.isNotBlank(cmpCode)
																&& SUCCESS_CODE.equals(cmpCode)) {
															if (resultMap.containsKey("terminated_result")) {
																String terCode = MapUtils
																		.getString(
																				(HashMap<String, Object>) resultMap
																						.get("terminated_result"),
																				"code");

																if (StringUtil.isNotBlank(terCode)
																		&& SUCCESS_CODE.equals(terCode)) {
																	result = true;
																}
															} else {
																result = true;
															}
														}
													} else {
														result = true;
													}
												}
												// 실패
												else {
													log.error("Keris API /aidt_dashboard/curriculum_score fail >>>"
															+ apiResult.toString());

													arc = MapUtils.getString(apiResult, "code");
													errMsg = MapUtils.getString(apiResult, "message");

													dashboardErr(dto.getApi_domain() + BcCmKerisApiEnum.SCORE.getUrl(),
															dto.getPartner_id(), param, apiResult,
															userDetails.getUsrId(), userDetails.getOptTxbId());
												}
											} else {
												arc = "FAIL";
												errMsg = "Keris API /aidt_dashboard/curriculum_score Result value is null.";
											}
										} else {
											arc = "FAIL";
											errMsg = "score 값이 null 이거나 빈값입니다.";
										}
									} catch (NumberFormatException e) {
										log.error(e.getMessage());

										arc = "FAIL";
										errMsg = "score 값이 숫자 타입이 아닙니다.";
									}
								} else {
									result = true;
									arc = "PASS";
									errMsg = "이미 completed된 상태입니다.";
								}
							} else {
								arc = "FAIL";
								errMsg = "initialized가 진행되지 않은 상태 입니다.";
							}
						} else {
							result = true;
							arc = "PASS";
							errMsg = "curriculum 값이 -1 입니다.";
						}

						break;
					// terminated (종료) : 연계 하지 않음
					case "ED":
						arc = "FAIL";
						errMsg = "terminated code(ED)는 지원하지 않습니다.";

						break;
					// curriculum_completed (완료) : 진도율이나 스코어에서 진행함. 진도율 100% 이고 스코어 등록이 되어 있으면 처리
					case "CP":
						arc = "FAIL";
						errMsg = "curriculum_completed code(CP)는 지원하지 않습니다.";

						break;
					default:
						arc = "FAIL";
						errMsg = "지원하지 않은 code입니다. >>> " + dto.getAct_type();

						break;
					}
				} // next boolean true
			} catch (Exception e) {
				arc = "FAIL";
				errMsg = "aidtDashboard method try catch Exception >>> " + e.getMessage();

				log.error(e.getMessage());
			}
		} else {
			arc = "FAIL";
			errMsg = "act_type parameter 값이 null 이거나 빈값입니다.";
		}

		resultMap.put("result", result);
		resultMap.put("err_msg", errMsg);
		resultMap.put("code", arc);

		/** keris conn log set **/
		connLog.setRsYn(result ? "Y" : "N");
		connLog.setRsCd(arc);
		connLog.setRsMsg(errMsg);

		try {
			connLog.setRsBodyCn(new ObjectMapper().writeValueAsString(resultMap));
		} catch (JsonProcessingException e) {
			connLog.setRsBodyCn("resultMap ObjectMapper convert error >>> " + e.getMessage());
			log.error(e.getMessage());
		}

		bcCmService.insertKerisConnLog(connLog);

		return Response.ok(resultMap);
	}

	private void dashboardCommonParam(BcCmKerisDashboardDto dto, Map<String, Object> map) {
		Map<String, Object> accessToken = new HashMap<String, Object>();
		accessToken.put("token", dto.getToken());
		accessToken.put("access_id", dto.getAccess_id());

		map.put("access_token", accessToken);
		map.put("user_id", dto.getUser_id());
		map.put("curriculum", dto.getCurriculum());
		map.put("timestamp", dto.getTimestamp());
		map.put("resend_process", "N");
	}

	private void dashboardCompleteCheck(BcCmNtlvEduCrsStnSstDto paramDto, BcCmKerisDashboardDto dto,
			Map<String, Object> resultMap, CommonUserDetail userDetails) {

		if (bcCmService.saveNtlvEduCrsStnSstLrnCompCheck(paramDto)) {
			Map<String, Object> param = new HashMap<>();
			dashboardCommonParam(dto, param);

			log.debug("parameter >>> " + param);
			Map<String, Object> completeResult = KerisWebClientUtil.executePostJsonRequestMapFromString(param,
					dto.getApi_domain() + BcCmKerisApiEnum.COMPLETED.getUrl(), dto.getPartner_id());
			log.debug("Keris API /aidt_dashboard/curriculum_completed result >>> " + completeResult);

			if (completeResult != null) {
				// 성공
				if (SUCCESS_CODE.equals(MapUtils.getString(completeResult, "code", ""))) {
					log.debug("parameter >>> " + param);
					Map<String, Object> termiResult = KerisWebClientUtil.executePostJsonRequestMapFromString(param,
							dto.getApi_domain() + BcCmKerisApiEnum.TERMINATED.getUrl(), dto.getPartner_id());
					log.debug("Keris API /aidt_dashboard/terminated result >>> " + termiResult);

					if (termiResult != null) {
						// 성공
						if (SUCCESS_CODE.equals(MapUtils.getString(termiResult, "code", ""))) {
							paramDto.setLrnCmplYn("Y");
							bcCmService.updateNtlvEduCrsStnSstLrnCmpl(paramDto);
						}
						// 실패
						else {
							dashboardErr(dto.getApi_domain() + BcCmKerisApiEnum.TERMINATED.getUrl(),
									dto.getPartner_id(), param, termiResult, userDetails.getUsrId(),
									userDetails.getOptTxbId());
						}

						resultMap.put("terminated_result", termiResult);
					} else {
						termiResult = new HashMap<String, Object>();
						termiResult.put("err_msg", "/aidt_dashboard/terminated API return message is null.");

						resultMap.put("terminated_result", termiResult);
					}
				} else {
					dashboardErr(BcCmKerisApiEnum.COMPLETED.getUrl(), dto.getPartner_id(), param, completeResult,
							userDetails.getUsrId(), userDetails.getOptTxbId());
				}

				resultMap.put("complete_result", completeResult);
			} else {
				completeResult = new HashMap<String, Object>();
				completeResult.put("err_msg", "/aidt_dashboard/curriculum_completed API return message is null.");

				resultMap.put("complete_result", completeResult);
			}
		}
	}

	@SuppressWarnings("unchecked")
	public void dashboardErr(String connUrl, String prnrId, Map<String, Object> paramMap, Map<String, Object> resultMap,
			String lrnUsrId, String optTxbId) {
		String bodyCn = null;
		String rsCn = null;
		String rsMsg = null;
		String errCd = null;
		String errTrnsId = null;

		Map<String, Object> errMap = null;

		if (paramMap != null) {
			try {
				bodyCn = new ObjectMapper().writeValueAsString(paramMap);
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}

		if (resultMap != null) {
			rsCn = MapUtils.getString(resultMap, "code");
			rsMsg = MapUtils.getString(resultMap, "message");

			errMap = (HashMap<String, Object>) resultMap.get("error");

			if (errMap != null) {
				errCd = MapUtils.getString(errMap, "code");
				errTrnsId = MapUtils.getString(errMap, "transfer_id");
			}
		}

		// errTrmsStCd = REQ : 재발송 대상 / LOG : log성 데이터
		BcCmKerisConnErr dto = BcCmKerisConnErr.builder().connUrl(connUrl).prnrId(prnrId)
				.apiVer(KerisWebClientUtil.getKerisApiVersion()).bodyCn(bodyCn).rsCd(rsCn).rsMsg(rsMsg).errCd(errCd)
				.errTrmsId(errTrnsId).errTrmsStCd(StringUtils.isNotBlank(errTrnsId) ? "REQ" : "LOG").lrnUsrId(lrnUsrId)
				.optTxbId(optTxbId).build();

		log.debug(" >>> " + String.valueOf(dto));

		bcCmService.insertKerisConnErr(dto);
	}

	/**
	 * 강제 에러 발생 테스트용
	 * 
	 * @param dto
	 * @return
	 */
	// @PostMapping(value = "/aidtDashboardErrTest")
	public ResponseDto<Map<String, Object>> aidtDashboardErrTest(@RequestBody BcCmKerisDashboardDto dto) {
		Map<String, Object> apiResult = null;

		Map<String, Object> param = new HashMap<>();
		dashboardCommonParam(dto, param);

		log.debug("parameter >>> " + param);

		apiResult = KerisWebClientUtil.executePostJsonRequestMapFromString(param,
				dto.getApi_domain() + BcCmKerisApiEnum.INITIALIZED.getUrl(), dto.getPartner_id());
		log.debug("Keris API /aidt_dashboard/initialized result >>> " + apiResult);

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		if (apiResult != null) {
			// 성공
			if (SUCCESS_CODE.equals(MapUtils.getString(apiResult, "code", ""))) {
				log.debug("################  성공 ###############");
				log.debug("################  성공 ###############");
				log.debug("################  성공 ###############");
			}
			// 실패
			else {
				dashboardErr(dto.getApi_domain() + BcCmKerisApiEnum.INITIALIZED.getUrl(), dto.getPartner_id(), param,
						apiResult, userDetails.getUsrId(), userDetails.getOptTxbId());
			}

		} else {
			log.error("########## API RESULT NULL ###############");
			log.error("########## API RESULT NULL ###############");
			log.error("########## API RESULT NULL ###############");
		}

		return Response.ok(new HashMap<String, Object>());
	}

	/**
	 * bcCmService.checkLectureTcrUserInfo 호출
	 * 
	 * @param bcUsrInfoDto
	 * @return
	 */
	@PostMapping(value = "/checkLectureTcrUserInfo")
	public ResponseEntity<?> checkLectureTcrUserInfo(@RequestBody BcUsrInfoDto bcUsrInfoDto) {

		if (!checkValidationForLectureTcrUserInfo(bcUsrInfoDto)) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("parameter is null or empty");
		}
		bcCmService.checkLectureTcrUserInfo(bcUsrInfoDto);

		/**
		 * 관리자 학급, 운영교과서 event 통계 set - 책장, 미리보기 제외
		 */
		if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
				&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaClaId())) {
			adminStatKafkaService.claInsertEvent(bcUsrInfoDto.getKafkaClaId());
		}

		if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
				&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaOptTxbId())) {
			adminStatKafkaService.optTxbInsertEvent(bcUsrInfoDto.getKafkaOptTxbId());
		}

		/**
		 * 회원 등록 event 통계 set - 책장, 미리보기 제외
		 */
		if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone()
				&& StringUtils.isNotBlank(bcUsrInfoDto.getKafkaUsrId())) {
			adminStatKafkaService.usrInsertEvent(bcUsrInfoDto.getKafkaUsrId());
		}

		if (bcUsrInfoDto != null && !bcUsrInfoDto.isKafkaLectNone() && bcUsrInfoDto.getKafkaUsrIds() != null) {
			adminStatKafkaService.usrInsertEvent(bcUsrInfoDto.getKafkaUsrIds());
		}

		return ResponseEntity.status(HttpStatus.OK).body("success");
	}

	/**
	 * 교사 KERIS ID 가져오기 API
	 * 
	 * @return ResponseEntity<String>
	 */
	@Operation(summary = "학생의 담당교사 ID 가져오기", description = "유저 데이터")
	@GetMapping("/getTcrUserId")
	public ResponseEntity<String> getTcrUserId() {
		try {
			CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();

			BcLoginDto loginInfo = BcLoginDto.builder().usrId(userDetail.getUsrId()).optTxbId(userDetail.getOptTxbId())
					.claId(userDetail.getClaId()).subDomain(DB_ID).build();

			BcUsrInfoDto studentUser = bcCmService.selectStuUser(loginInfo);

			if (studentUser == null || studentUser.getTcrKerisUsrId() == null
					|| studentUser.getTcrKerisUsrId().isEmpty()) {
				//return ResponseEntity.status(HttpStatus.NO_CONTENT).body("교사 ID가 존재하지 않습니다.");
				return ResponseEntity.ok(null);
			}

			return ResponseEntity.ok(studentUser.getTcrKerisUsrId());

		} catch (Exception e) {
			log.error("getTcrUserId API 호출 중 오류 발생", e);
			//return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("서버 오류가 발생했습니다.");
			return ResponseEntity.ok(null);
		}
	}

	private Boolean checkValidationForLectureTcrUserInfo(BcUsrInfoDto bcUsrInfoDto) {
		if (bcUsrInfoDto == null) {
			return false;
		}
		if (StringUtil.isBlank(bcUsrInfoDto.getLectureCd())) {
			return false;
		}
		if (StringUtil.isBlank(bcUsrInfoDto.getUsrId())) {
			return false;
		}
		if (StringUtil.isBlank(bcUsrInfoDto.getUserType())) {
			return false;
		}

		/*
		 * 2025.03.01 keris api class_code 더이상 보내지 않음 if
		 * (StringUtil.isBlank(bcUsrInfoDto.getClassCode())) { return false; }
		 */
		return true;
	}

	/**
	 * keris 데이터 수집 연동 check
	 * 
	 * 2025.03.18 keris api 통신 front로 변경 back-end는 check와 결과 등록 기능만
	 * 
	 * @param dto
	 * @return
	 */
	@Operation(summary = "Keris 데이터 수집 연동 check", description = "Keris 데이터 수집 연동 check")
	@PostMapping(value = "/kerisDashboardCheck")
	public ResponseDto<Map<String, Object>> kerisDashboardCheck(@RequestBody KerisDashboardCheckDto dto) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		boolean validCheck = true;
		StringBuilder rstMsg = new StringBuilder();

		BcCmKerisFrontReqLogDto logDto = BcCmKerisFrontReqLogDto.builder().actCd(dto.getAct_type())
				.usrId(dto.getUser_id()).crclCtnElm2Cd(dto.getCurriculum()).build();

		boolean apiCall = false;
		boolean complete = false;
		boolean initCall = false;

		if (!StringUtils.isNotBlank(dto.getAct_type())) {
			validCheck = false;
			setRstMsg(rstMsg, "parameter act_type 값이 null 이거나 빈값입니다.");
		}

		if (!StringUtils.isNotBlank(dto.getUser_id())) {
			validCheck = false;
			setRstMsg(rstMsg, "parameter user_id 값이 null 이거나 빈값입니다.");
		}

		if (!StringUtils.isNotBlank(dto.getCurriculum())) {
			validCheck = false;
			setRstMsg(rstMsg, "parameter curriculum 값이 null 이거나 빈값입니다.");
		}

		try {
			CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

			logDto.setLrnUsrId(userDetails.getUsrId());
			logDto.setOptTxbId(userDetails.getOptTxbId());

			if (dto.getUser_id().equals(userDetails.getKerisUsrId())) {
				if (!"ST".equals(userDetails.getUsrTpCd())) {
					validCheck = false;
					setRstMsg(rstMsg, "token usr_tp_cd 값이 ST가 아닙니다.");
				}
			} else {
				validCheck = false;
				setRstMsg(rstMsg, "parameter user_id 값과 token keris_usr_id 값이 다릅니다.");
			}

			if (validCheck) {
				BcCmNtlvEduCrsStnSstDto paramDto = new BcCmNtlvEduCrsStnSstDto();
				paramDto.setUsrId(dto.getUser_id()); // kerisUsrId
				paramDto.setCrclCtnElm2Cd(dto.getCurriculum());

				BcCmNtlvEduCrsStnSstDto sstDto = null;

				switch (dto.getAct_type()) {
				// initialized (초기화) : 커리큘럼 별 한번만 keris api 통신
				case "ST":
					// 표지 등등 keris 전송
					if ("-1".equals(paramDto.getCrclCtnElm2Cd())) {
						apiCall = true;
					} else {
						sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);

						if (sstDto != null && StringUtils.isNotBlank(sstDto.getLrnStrYn())
								&& "Y".equals(sstDto.getLrnStrYn())) {
							setRstMsg(rstMsg, "이미 initialized가 진행되었습니다.");
						} else {
							apiCall = true;
						}
					}

					break;
				// curriculum_progres
				case "IN":
					// 표지 등등 keris 전송
					if ("-1".equals(paramDto.getCrclCtnElm2Cd())) {
						setRstMsg(rstMsg, "curriculum 값이 -1 입니다.");
					} else {
						sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);
						
						/*
						 * initialized 안된 상태에서 진입하는 경우 발생으로 추가
						 */
						if(sstDto == null) {
							BcCmNtlvEduCrsStnSstDto saveDto = new BcCmNtlvEduCrsStnSstDto();
							saveDto.setUsrId(dto.getUser_id()); // kerisUsrId
							saveDto.setCrclCtnElm2Cd(dto.getCurriculum());
			
							try {
								saveDto.setOptTxbId(userDetails.getOptTxbId());
								saveDto.setTxbId(userDetails.getTxbId());
								saveDto.setCrtrId(userDetails.getUsrId());
								saveDto.setMdfrId(userDetails.getUsrId());
							} catch (Exception e) {
								log.error("/kerisDashboardResult token get user error");
							}
			
							saveDto.setLrnStrYn("Y");
							saveDto.setDbId(DB_ID);
							
							bcCmService.saveNtlvEduCrsStnSstLrnStr(saveDto);
							sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);
							
							if(sstDto != null) {
								initCall = true;
							}
						}
						
						// initialized 등록된 경우에만 처리
						if (sstDto != null && StringUtil.isNotBlank(sstDto.getLrnStrYn())
								&& "Y".equals(sstDto.getLrnStrYn())) {
							// 완료처리가 되지 않은 경우 처리
							if (sstDto.getLrnCmplYn() == null || !"Y".equals(sstDto.getLrnCmplYn())) {
								// 이미 진도율 100인 경우 db 조회 안함
								if (!(StringUtil.isNotBlank(sstDto.getPgrsRt()) && "100".equals(sstDto.getPgrsRt()))) {
									paramDto.setOptTxbId(sstDto.getOptTxbId());
									paramDto.setTxbId(sstDto.getTxbId());
									paramDto.setLrnUsrId(userDetails.getUsrId());
									paramDto.setMdfrId(userDetails.getUsrId());

									bcCmService.saveNtlvEduCrsStnSstPgrsRt(paramDto, sstDto.getPgrsRt());
								} else {
									paramDto.setPgrsRt(sstDto.getPgrsRt());
								}

								resultMap.put("percent", paramDto.getPgrsRt());

								apiCall = true;

								setRstMsg(rstMsg, "요청 percent = " + paramDto.getPgrsRt());

								// keris complete 및 terminated 전송여부
								complete = bcCmService.saveNtlvEduCrsStnSstLrnCompCheck(paramDto);
							} else {
								setRstMsg(rstMsg, "이미 completed된 상태입니다.");
							}
						} else {
							setRstMsg(rstMsg, "initialized가 진행되지 않은 상태 입니다.");
						}
					}

					break;
				// curriculum_score (스코어) : 호출 시 마다 keris api 통신
				case "PT":
					// 표지 등등 keris 전송
					if ("-1".equals(paramDto.getCrclCtnElm2Cd())) {
						setRstMsg(rstMsg, "curriculum 값이 -1 입니다.");
					} else {
						sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);
						
						/*
						 * initialized 안된 상태에서 진입하는 경우 발생으로 추가
						 */
						if(sstDto == null) {
							BcCmNtlvEduCrsStnSstDto saveDto = new BcCmNtlvEduCrsStnSstDto();
							saveDto.setUsrId(dto.getUser_id()); // kerisUsrId
							saveDto.setCrclCtnElm2Cd(dto.getCurriculum());
			
							try {
								saveDto.setOptTxbId(userDetails.getOptTxbId());
								saveDto.setTxbId(userDetails.getTxbId());
								saveDto.setCrtrId(userDetails.getUsrId());
								saveDto.setMdfrId(userDetails.getUsrId());
							} catch (Exception e) {
								log.error("/kerisDashboardResult token get user error");
							}
			
							saveDto.setLrnStrYn("Y");
							saveDto.setDbId(DB_ID);
							
							bcCmService.saveNtlvEduCrsStnSstLrnStr(saveDto);
							sstDto = bcCmService.selectNtlvEduCrsStnSst(paramDto);
							
							if(sstDto != null) {
								initCall = true;
							}
						}

						// initialized 등록된 경우에만 처리
						if (sstDto != null && StringUtil.isNotBlank(sstDto.getLrnStrYn())
								&& "Y".equals(sstDto.getLrnStrYn())) {
							// 완료처리가 되지 않은 경우 처리
							if (sstDto.getLrnCmplYn() == null || !"Y".equals(sstDto.getLrnCmplYn())) {
								if (StringUtil.isNotBlank(dto.getScore())) {
									int idx = dto.getScore().indexOf(".");

									log.debug("SCORE 소수점 >>> " + String.valueOf(idx));

									if (idx > -1) {
										dto.setScore(dto.getScore().substring(0, idx));
									}

									log.debug("SCORE >>> " + dto.getScore());

									try {
										paramDto.setEvScr(Long.parseLong(dto.getScore()));
										paramDto.setEduCrsAchBsCd(dto.getAchievement_level());
										paramDto.setOptTxbId(sstDto.getOptTxbId());
										paramDto.setTxbId(sstDto.getTxbId());
										paramDto.setMdfrId(userDetails.getUsrId());

										// 스코어 등록 처리
										bcCmService.updateNtlvEduCrsStnSstEvScr(paramDto);

										// parameter 점수 추가
										resultMap.put("score", dto.getScore());

										apiCall = true;

										setRstMsg(rstMsg, "요청 score = " + dto.getScore());

										// keris complete 및 terminated 전송여부
										complete = bcCmService.saveNtlvEduCrsStnSstLrnCompCheck(paramDto);
									} catch (NumberFormatException nfe) {
										setRstMsg(rstMsg, "score 값이 숫자 타입이 아닙니다.");
									}

								} else {
									setRstMsg(rstMsg, "score 값이 null 이거나 빈값입니다.");
								}
							} else {
								setRstMsg(rstMsg, "이미 completed된 상태입니다.");
							}
						} else {
							setRstMsg(rstMsg, "initialized가 진행되지 않은 상태 입니다.");
						}
					}

					break;
				// terminated (종료) : 연계 하지 않음
				case "ED":
					setRstMsg(rstMsg, "terminated code(ED)는 지원하지 않습니다.");
					break;
				// curriculum_completed (완료) : 진도율이나 스코어에서 진행함. 진도율 100% 이고 스코어 등록이 되어 있으면 처리
				case "CP":
					setRstMsg(rstMsg, "curriculum_completed code(CP)는 지원하지 않습니다.");
					break;
				default:
					break;
				}
			}
		} catch (Exception e) {
			apiCall = false;
			setRstMsg(rstMsg, "오류 발생. >>> " + e.getMessage());
		}

		setRstMsg(rstMsg, "apiCall = " + apiCall);
		setRstMsg(rstMsg, "initCall = " + initCall);
		setRstMsg(rstMsg, "complete = " + complete);
		
		resultMap.put("apiCall", apiCall);
		resultMap.put("initCall", initCall);
		resultMap.put("complete", apiCall ? complete : false);
		resultMap.put("rstMsg", rstMsg == null ? null : rstMsg.toString());
		resultMap.put("timestamp", this.getKerisTimeStamp());

		logDto.setRsYn(apiCall ? "Y" : "N");
		logDto.setRsMsg(rstMsg.toString());

		resultMap.put("reqLogId", bcCmService.insertKerisFrontReqLog(logDto));

		return Response.ok(resultMap);
	}

	/**
	 * error 메세지 조합
	 * 
	 * @param errMsgBuilder
	 * @param msg
	 */
	private void setRstMsg(StringBuilder rstMsgBuilder, String msg) {
		if (rstMsgBuilder != null) {
			if (rstMsgBuilder.length() > 0) {
				rstMsgBuilder.append(" / ");
			}

			rstMsgBuilder.append(msg);
		}
	}

	/**
	 * keris 데이터 수집 연동 결과 등록
	 * 
	 * 2025.03.18 keris api 통신 front로 변경 back-end는 check와 결과 등록 기능만
	 * 
	 * @param dto
	 * @return
	 */
	@Operation(summary = "Keris 데이터 수집 연동 결과등록", description = "Keris 데이터 수집 연동 결과등록")
	@PostMapping(value = "/kerisDashboardResult")
	public ResponseDto<Map<String, Object>> kerisDashboardResult(@RequestBody BcCmKerisDashboardDto dto) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		BcCmKerisConnLog connLog = new BcCmKerisConnLog();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		/** keris conn log 기초 set start **/
		connLog.setReqLogId(dto.getReqLogId());
		connLog.setUsrId(dto.getUser_id());
		connLog.setCrclCtnElm2Cd(dto.getCurriculum());

		try {
			connLog.setLrnUsrId(userDetails.getUsrId());
			connLog.setOptTxbId(userDetails.getOptTxbId());
		} catch (Exception e) {
			log.error("/kerisDashboardResult token get user error");
		}

		connLog.setActCd(dto.getAct_type());
		connLog.setRsCd(dto.getCode());
		connLog.setRsMsg(dto.getMessage());
		connLog.setRsBodyCn(dto.getRsBodyCn());
		connLog.setConnUrl(dto.getConnUrl());
		connLog.setPrnrId(dto.getPartner_id());
		connLog.setApiVer(dto.getApi_version());
		connLog.setBodyCn(dto.getBodyCn());

		if (StringUtils.isNotBlank(connLog.getRsCd()) && SUCCESS_CODE.equals(connLog.getRsCd())) {
			connLog.setRsYn("Y");
			
			if(!"-1".equals(dto.getCurriculum())) {
				// initialized
				if ("ST".equals(dto.getAct_type())) {
					BcCmNtlvEduCrsStnSstDto paramDto = new BcCmNtlvEduCrsStnSstDto();
					paramDto.setUsrId(dto.getUser_id()); // kerisUsrId
					paramDto.setCrclCtnElm2Cd(dto.getCurriculum());
	
					try {
						paramDto.setOptTxbId(userDetails.getOptTxbId());
						paramDto.setTxbId(userDetails.getTxbId());
						paramDto.setCrtrId(userDetails.getUsrId());
						paramDto.setMdfrId(userDetails.getUsrId());
					} catch (Exception e) {
						log.error("/kerisDashboardResult token get user error");
					}
	
					paramDto.setLrnStrYn("Y");
					paramDto.setDbId(DB_ID);
	
					// initialized 등록 처리
					bcCmService.saveNtlvEduCrsStnSstLrnStr(paramDto);
				}
				// curriculum_completed (완료)
				else if ("CP".equals(dto.getAct_type())) {
					BcCmNtlvEduCrsStnSstDto paramDto = new BcCmNtlvEduCrsStnSstDto();
					paramDto.setUsrId(dto.getUser_id()); // kerisUsrId
					paramDto.setCrclCtnElm2Cd(dto.getCurriculum());
					paramDto.setMdfrId(connLog.getLrnUsrId());
					paramDto.setLrnCmplYn("Y");
	
					bcCmService.updateNtlvEduCrsStnSstLrnCmpl(paramDto);
				}
			}
		} else {
			connLog.setRsYn("N");
			
			BcCmKerisConnErr errdto = BcCmKerisConnErr.builder().connUrl(dto.getConnUrl()).prnrId(dto.getPartner_id())
					.apiVer(dto.getApi_version()).bodyCn(dto.getBodyCn()).rsCd(dto.getCode()).rsMsg(dto.getMessage())
					.errCd(dto.getError_code()).errTrmsId(dto.getError_transfer_id())
					.errTrmsStCd(StringUtils.isNotBlank(dto.getError_transfer_id()) ? "REQ" : "LOG")
					.lrnUsrId(connLog.getLrnUsrId()).optTxbId(connLog.getOptTxbId()).reqLogId(dto.getReqLogId())
					.build();

			bcCmService.insertKerisConnErr(errdto);
		}

		bcCmService.insertKerisConnLog(connLog);

		resultMap.put("result", true);

		return Response.ok(resultMap);
	}

	/**
	 * 케리스 요구사항에 맞춘 타임스탬프
	 * ex) 2025-05-21T16:50:02.387+00:00
	 * @return
	 */
	public String getKerisTimeStamp() {
		
        OffsetDateTime kstDateTime = OffsetDateTime.now(ZoneOffset.of("+00:00"));
        
        // ISO 8601 형식으로 변환 (밀리초까지 포함)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        String isoStringWithMillis = kstDateTime.format(formatter);
        
        // +00:00을 추가
        return isoStringWithMillis + "+00:00";
	}
	
}
