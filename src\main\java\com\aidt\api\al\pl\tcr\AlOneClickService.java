package com.aidt.api.al.pl.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlOneClickDto;
import com.aidt.api.al.pl.dto.AlOneClkSetmAlPlDto;
import com.aidt.api.al.pl.dto.AlOneClkSetmClaDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto;
import com.aidt.common.CommonDao;

//@Slf4j
@Service
public class AlOneClickService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.back.AlOneClick.";
	
	@Autowired
	private CommonDao commonDao;
	
	@Transactional
	public Map<String, Object> updateRcstn(List<AlOneClickDto> alOneClickDto) {
		//평가파라미터
		if(alOneClickDto.isEmpty()) {
			throw new IllegalArgumentException("Invalid luList in Request Parameter ");
		}

		//학습맵 지식맵 대단원,중단원
		List<AiRcmTsshQtmDto> lrmpKmmpMapList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrmpKmmpMapListByOptTxb", alOneClickDto.get(0));

		if (lrmpKmmpMapList == null || lrmpKmmpMapList.isEmpty()) {
			throw new NullPointerException("학습맵 대단원 - 지식맵 중단원 매핑 정보가 없습니다.");
		}
		
		//AI지식맵노드재구성 대단원,중단원 update
		for (AiRcmTsshQtmDto lrmpKmmpMap : lrmpKmmpMapList) {
			//학습맵 차시 리스트
			List<AiRcmTsshQtmDto> lrmpTcList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrmpTcListByLluNodId", lrmpKmmpMap);

			// 차시 리스트 중에 하나라도 잠금처리되어 있으면 중단원도 잠금처리하는 로직 추가(2024.10.21)
			boolean hasLcknYnY = lrmpTcList.stream().anyMatch(tc -> "Y".equals(tc.getLcknYn()));

			if (hasLcknYnY) {
				lrmpKmmpMap.setLcknYn("Y");
			}
			commonDao.update(MAPPER_NAMESPACE + "updateAiKmmpNodRcstn", lrmpKmmpMap);
		}
		
		List<Map<String, Object>> evIdSendList = new ArrayList<>();
		Map<String, Object> returnMap = new HashMap<>();

		// 학습맵 -> 지식맵 치환을 위한 맵 생성
		Map<String, AiRcmTsshQtmDto> lrmpKmmpMapLookup = lrmpKmmpMapList.stream()
				.collect(Collectors.toMap(AiRcmTsshQtmDto::getLluLrmpNodId, Function.identity()));

		List<String> llukmmpNodIds = new ArrayList<>();
		Map<String, AlOneClickDto> nodIdToReqMap = new HashMap<>(); // nodId별 req 매핑

		// 평가파라미터 학습맵 -> 지식맵 치환
		for (AlOneClickDto req : alOneClickDto) {
			AiRcmTsshQtmDto lrmpKmmpMap = lrmpKmmpMapLookup.get(req.getLluNodId());

			if (lrmpKmmpMap != null) {
				String kmmpNodId = lrmpKmmpMap.getLluKmmpNodId();

				// 이미 추가된 nodId인지 체크
				if (!llukmmpNodIds.contains(kmmpNodId)) {
					llukmmpNodIds.add(kmmpNodId);
					nodIdToReqMap.put(kmmpNodId, req); // nodId 기준으로 req 저장
				} else {
					continue; // 이미 추가된 nodId는 건너뜀
				}
			}
		}

		AlOneClickDto dto = new AlOneClickDto();
		dto.setOptTxbId(alOneClickDto.get(0).getOptTxbId());
		dto.setLluKmmpNodIdList(llukmmpNodIds);

		List<AlOneClickDto> evIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvId", dto);
		for (AlOneClickDto ev : evIdList) {
			String kmmpNodId = ev.getLluKmmpNodId(); // ev에서 kmmpNodId 가져옴
			AlOneClickDto reqInfo = nodIdToReqMap.get(kmmpNodId); // 저장된 req 정보 가져오기

			if (reqInfo != null) {
				Map<String, Object> map = new HashMap<>();
				map.put("evId", ev.getEvId());
				map.put("lcknYn", reqInfo.getLcknYn());
				map.put("useYn", reqInfo.getUseYn());
				evIdSendList.add(map);
			}
		}

		returnMap.put("evList", evIdSendList);
		return returnMap;
	}
	
	
	
	
	@Transactional
	public Map<String, Object> updateAlPlRcstn(AlOneClkSetmAlPlDto alOneClickDto, String mdfrId, String orgnOptTxbId) {
		if(alOneClickDto == null) {
			throw new IllegalArgumentException("Invalid luList in Request Parameter ");
		}
		
		List<AlOneClkSetmClaDto> claDtoList = alOneClickDto.getClaList(); // 다른 학급 리스트
		
		int cnt = 0;
		for (AlOneClkSetmClaDto claDto : claDtoList) {
            // 다른 학급 리스트 데이터 검증
            if (StringUtils.isEmpty(claDto.getOptTxbId())) {
                throw new IllegalArgumentException("Invalid optTxbId in AlOneClkSetmClaDto ");
            }
            claDto.setMdfrId(mdfrId);
            claDto.setOrgnOptTxbId(orgnOptTxbId);
            cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaAiRcstn", claDto); // 재구성 저장
           
        }
		
		List<Map<String, Object>> evIdSendList = new ArrayList<>();
		Map<String, Object> returnMap = new HashMap<>();

		// 원본이 되는 운영교과서 ID의 지식맵 조회 후 해당 지식맵 하위 평가 조회
		List<AlOneClickDto> evIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectAntClaEvId", orgnOptTxbId); 
		
		for (AlOneClickDto ev : evIdList) {
				Map<String, Object> map = new HashMap<>();
				map.put("evId", ev.getEvId());
				map.put("lcknYn", ev.getLcknYn());
				map.put("useYn", ev.getUseYn());
				evIdSendList.add(map);
			}

		returnMap.put("evList", evIdSendList);
		return returnMap;
	}
}
