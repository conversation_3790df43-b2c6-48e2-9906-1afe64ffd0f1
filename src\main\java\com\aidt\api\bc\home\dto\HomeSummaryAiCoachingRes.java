package com.aidt.api.bc.home.dto;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HomeSummaryAiCoachingRes {

	@Schema(description = "빠른 학습자 수준 수")
	private Long lrnrVelTpFsCnt;

	@Schema(description = "보통 학습자 수준 수")
	private Long lrnrVelTpNmCnt;

	@Schema(description = "느린 학습자 수준 수")
	private Long lrnrVelTpSlCnt;

	@Schema(description = "미응시 학습자 수")
	private Integer ovNTxmStuCnt;

	public static HomeSummaryAiCoachingRes of(List<HomeSummaryAiCoaching> homeSummaryAiCoaching) {

		if(CollectionUtils.isEmpty(homeSummaryAiCoaching)) {
			return new HomeSummaryAiCoachingRes();
		}

		var examAiCoaching = homeSummaryAiCoaching.stream()
			.filter(HomeSummaryAiCoaching::isComplete)
			.collect(Collectors.toList());

		var LearnerVelocityTypeCountMap = examAiCoaching.stream()
			.map(HomeSummaryAiCoaching::getLearnerVelocityTypeCode)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(type -> type, Collectors.counting()));

		return HomeSummaryAiCoachingRes.builder()
			.lrnrVelTpFsCnt(LearnerVelocityTypeCountMap.getOrDefault("FS",0L))
			.lrnrVelTpNmCnt(LearnerVelocityTypeCountMap.getOrDefault("NM",0L))
			.lrnrVelTpSlCnt(LearnerVelocityTypeCountMap.getOrDefault("SL",0L))
			.ovNTxmStuCnt(homeSummaryAiCoaching.size() - examAiCoaching.size())
			.build();
	}

}
