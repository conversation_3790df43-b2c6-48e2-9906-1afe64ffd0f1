package com.aidt.api.tl.sbclrn.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:53:20
 * @modify date 2024-01-05 10:53:20
 * @desc TlTcrSbcLrnTocSrhDto 교과학습목차 조회 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlSbcLrnTocSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID", required = true)
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;

    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;

    /** 사용자구분코드 */
    @Parameter(name="사용자구분코드")
    private String usrDvCd;

}
