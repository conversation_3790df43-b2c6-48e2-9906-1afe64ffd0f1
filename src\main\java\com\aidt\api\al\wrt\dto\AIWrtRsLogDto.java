package com.aidt.api.al.wrt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "CM_AIWRITE_결과_로그")
public class AIWrtRsLogDto {

	@Schema(description = "운영교과서ID")
	private String optTxbId;

	@Schema(description = "대단원지식맵노드ID")
	private String lluKmmpNodId;

	@Schema(description = "토픽지식맵노드ID")
	private String tpcKmmpNodId;

	@Schema(description = "학생ID")
	private String stuId;

	@Schema(description = "결과여부")
	private String rsYn;

	@Schema(description = "결과메시지")
	private String rsMsg;

	@Schema(description = "BODY내용")
	private String bodyCn;
}
