package com.aidt.api.ea.evcom.dto;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.common.CommonUserDetail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EaEvSppNtnResult {

	private Integer evId;
	private Integer txmPn;
	private String ntnEvCrtYn;
	private Integer iansCnt;
	private String sppNtnTpCd;
	private String evCmplYn;
	private List<EaEvSppNtnAnswer> answers;
	private int qstCnt;

	private String optTxbId;
	private String usrId;
	private String dbId;

	public static EaEvSppNtnResult of(EaEvResult eaEvResult, CommonUserDetail userDetail) {

		var sppNtnTpCd = EvaluationDetailCode.SI;
		String ntnEvCrtYn = null;
		Integer iansCnt = null;

		if (eaEvResult.isPerfectScore()) {
			ntnEvCrtYn = "Y";
			sppNtnTpCd = EvaluationDetailCode.DE;
			iansCnt = 0;
		}

		return EaEvSppNtnResult.builder()
			.evId(eaEvResult.getEvId())
			.txmPn(eaEvResult.getTxmPn())
			.ntnEvCrtYn(ntnEvCrtYn)
			.sppNtnTpCd(sppNtnTpCd.getCode())
			.evCmplYn("N")
			.iansCnt(iansCnt)
			.optTxbId(userDetail.getOptTxbId())
			.usrId(eaEvResult.getUsrId())
			.dbId(eaEvResult.getDbId())
			.build();

	}

	public void addAnswer(List<EaEvSppNtnAnswer> answers) {
		if (CollectionUtils.isNotEmpty(answers)) {
			this.answers = answers;
			this.qstCnt = answers.size();
		}
	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class EaEvSppNtnQuestion {

		private Long qtmId;
		private String tpcId;
	}

	@Getter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class EaEvSppNtnAnswer {

		private Long qtmId;
		private String tpcId;
		private Long annxFleId;
		private String smtAnwVl;
		private String qstXplCn;
		private String xplStCd;
		private String hntCofmYn;
		private Integer xplTmScnt;
		private String cansYn;
	}
}
