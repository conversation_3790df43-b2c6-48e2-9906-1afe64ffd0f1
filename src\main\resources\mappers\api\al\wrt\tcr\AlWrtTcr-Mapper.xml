<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.wrt.tcr">
	
	<!-- AI 첨삭 : 토픽 목록 -->
	<select id="selectAlWrtTpcList" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto" resultType="com.aidt.api.al.wrt.dto.AlWrtResDto">
		/** AlWrtTcr-Mapper.xml - selectAlWrtTpcList */
		select tpc.llu_kmmp_nod_id 					-- 대단원노드ID(레슨ID)
		     , llu.kmmp_nod_nm as llu_kmmp_nod_nm	-- 대단원노드명(레슨명)
		     , tpc.tpc_kmmp_nod_id 					-- 토픽노드ID 
		     , tpc.tpc_kmmp_nod_nm 					-- 토픽노드명
		     , llu.tc_use_yn 						-- 차시사용여부
			 , llu.use_yn 							-- 사용여부
			 , llu.lckn_yn 							-- 잠금여부
		     , (select count(usr.USR_ID)
				  from lms_lrm.cm_usr usr
				 where usr.usr_tp_cd = 'ST'
				   and usr.cla_id = llu.opt_txb_id) as cla_cnt		-- 학생 수
			 , (select count(STU_USR_ID)
				  from lms_lrm.cm_wrt_mg
				 where opt_txb_id = llu.opt_txb_id
				   and llu_kmmp_nod_id = tpc.llu_kmmp_nod_id
				   and tpc_kmmp_nod_id = tpc.tpc_kmmp_nod_id
				   and del_yn = 'N'
				   and ifnull(pgrs_st_cd, 'LN') not in ('LN')) as sm_cnt		-- 제출 수
		  from lms_lrm.ai_kmmp_nod_rcstn llu
		 inner join lms_cms.bc_tk_wrt_tpc tpc
		    on llu.kmmp_nod_id = tpc.llu_kmmp_nod_id 
		   and llu.dpth = 1
		   and llu.del_yn = 'N'
		   and llu.use_yn = 'Y'
		   and tpc.del_yn = 'N'
		   and llu.use_yn = 'Y'
		 where tpc.tw_wrt_dv_cd = 'W'							-- 회화첨삭구분코드(T:talk/W:write)
		   and llu.opt_txb_id = #{optTxbId} 					-- 파라미터: 운영교과서ID
		 order by ifnull(llu.rcstn_ordn, llu.orgl_ordn), tpc.srt_ordn
	</select>
	
	<!-- AI 첨삭 : 상세 목록 -->
	<select id="selectAlWrtDtlList" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto" resultType="com.aidt.api.al.wrt.dto.AlWrtResDto">
		/** AlWrtTcr-Mapper.xml - selectAlWrtDtlList */
		select usr.stu_no
		     , usr.usr_id as stu_id
		     , usr.usr_nm as stu_nm
		     , wrt.pgrs_st_cd
		     , case
		         when ifnull(wrt.pgrs_st_cd, 'LN') = 'LN' then '미제출'
		         else '제출'
		        end as pgrs_st_nm
		     , DATE_FORMAT(wrt.stu_sav_dtm , '%Y. %m. %d. %p %h:%i') as stu_sav_dtm
		     , wrt.tcr_sav_dtm
		     , case when wrt.pgrs_st_cd = 'AP' 
		            	then (case when wrt.mdf_dtm <![CDATA[<]]> now() - interval 1 minute then 'Y' else 'N' end)
		         	else 'N'
		        end as ap_fail_yn
		     , wrt.llu_kmmp_nod_id
		     , wrt.tpc_kmmp_nod_id
		  from lms_lrm.cm_usr usr
		  left outer join lms_lrm.cm_wrt_mg wrt
		    on usr.usr_id = wrt.stu_usr_id 
		   and wrt.llu_kmmp_nod_id = #{lluKmmpNodId}
		   and wrt.tpc_kmmp_nod_id = #{tpcKmmpNodId}
		 where usr.usr_tp_cd = 'ST'
		   and usr.cla_id = #{classCode}
		 order by usr.stu_no 
	</select>
	
	<!-- AI 첨삭관리 : 별점 수정 (임시저장, 제출) -->
	<update id="updateWrtMgScore" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtTcr-Mapper.xml - updateWrtMgScore */
		update lms_lrm.cm_wrt_mg
		   set cstn_scr = #{cstnScr}			-- 구성점수
		     , expr_scr = #{exprScr}			-- 표현점수
		     , voc_scr = #{vocScr}				-- 어휘점수
		     , grmr_scr = #{grmrScr}			-- 문법점수
		     , mdfr_id = #{usrId}
		     , mdf_dtm = current_timestamp
		 where opt_txb_id = #{optTxbId}
		   and llu_kmmp_nod_id = #{lluKmmpNodId}
		   and tpc_kmmp_nod_id = #{tpcKmmpNodId}
		   and stu_usr_id = #{stuId}  
	</update>
	
	<!-- AI 첨삭관리 : 수정 (선생님 첨삭) -->
	<update id="updateWrtMgTcrEdit" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtTcr-Mapper.xml - updateWrtMgEdit */
		update lms_lrm.cm_wrt_mg
		   set tcr_usr_id = #{usrId}
		     , tcr_sav_dtm = current_timestamp
		     , mdfr_id = #{usrId}
		     , mdf_dtm = current_timestamp
		     <if test='tcrAnnxCn != null and !tcrAnnxCn.equals("")'>
		     , tcr_annx_cn = #{tcrAnnxCn}
		     </if>
		     <if test='pgrsStCd != null and !pgrsStCd.equals("")'>
		     , pgrs_st_cd = #{pgrsStCd}
		     </if>
		 where opt_txb_id = #{optTxbId}
		   and llu_kmmp_nod_id = #{lluKmmpNodId}
		   and tpc_kmmp_nod_id = #{tpcKmmpNodId}
		   and stu_usr_id = #{stuId}  
	</update>
	
	<!-- AI 첨삭관리 : 수정 (선생님 AI 재요청 시 mdf 정보 update) -->
	<update id="updateWrtMgAiReqa" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtTcr-Mapper.xml - updateWrtMgEdit */
		update lms_lrm.cm_wrt_mg
		   set mdfr_id = #{usrId}
		     , mdf_dtm = current_timestamp
		 where opt_txb_id = #{optTxbId}
		   and llu_kmmp_nod_id = #{lluKmmpNodId}
		   and tpc_kmmp_nod_id = #{tpcKmmpNodId}
		   and stu_usr_id = #{stuId}  
	</update>
	
	
	<!-- 교사 홈 : 선생님 첨삭 수 -->
	<select id="selectTcrWrtCnt" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto" resultType="int">
		/** AlWrtTcr-Mapper.xml - selectTcrWrtCnt */ 
		select count(a.tpc_kmmp_nod_id) as wrt_cnt
		  from lms_lrm.cm_wrt_mg a
		 where a.pgrs_st_cd in ('SM', 'EC')					-- SM : 학생작성완료 / EC : 선생님 첨삭완료(피드백전송 전)
		   and a.opt_txb_id = #{optTxbId} 					-- 파라미터: 운영교과서ID
	</select>
</mapper>