package com.aidt.api.al.sr.dto.sbc;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-28 09:17:06
 * @modify date 2024-06-28 09:17:06
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiSrUserDataDto {

    private String optTxbId;

    private String usrId;

    private String annxId;

    private String annxFleId;

    private String annxFlePthNm;

    private LocalDateTime mdfDtm;

    public AiSrUserDataResDto toResDto(String jsonStr) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy. MM. dd. a hh:mm").withLocale(Locale.forLanguageTag("ko"));
        return AiSrUserDataResDto.builder()
                .annxFlePthNm(this.annxFlePthNm)
                .annxId(this.annxId)
                .optTxbId(this.optTxbId)
                .usrId(this.usrId)
                .mdfDtm(this.mdfDtm.format(formatter))
                .jsonStr(jsonStr).build();
    }

}
