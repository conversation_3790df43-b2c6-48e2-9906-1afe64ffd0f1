package com.aidt.api.ea.grpmgmt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email 
 * @create date 2024-04-26 10:53:20
 * @modify date 2024-04-26 10:53:20
 * @desc EaGrpMgGrpSelDto 모둠관리 모둠 정보 조회 Dto
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtGrpSrhDto {	
    /** 운영교과서ID */
	@Parameter(name = "운영교과서ID")
    private String optTxbId;
	
    /** 클래스 ID */
	@Parameter(name = "클래스 ID")
    private String claId;

    /** 유저ID */
	@Parameter(name = "유저ID")
    private String usrId;

    /** 모둠ID */
	@Parameter(name = "모둠ID")
    private int grpId;
	
    /** 모둠ID */
	@Parameter(name = "모둠팀ID")
    private int grpTemId;
	
    /** 과제ID */
	@Parameter(name = "과제ID")
    private int asnId;
	
    /** DB ID */
	@Parameter(name = "DB ID")
    private String dbId;

    /** page 시작 행 번호 */
	@Parameter(name = "행 번호")
	private int startRow;
	
    /** page size */
	@Parameter(name = "page 사이즈")
	private int pageSize;
}