package com.aidt.api.al.pl.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.cm.ma.AlMaService;
import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlTpcMpnDto;
import com.aidt.api.al.pl.dto.LearningMgDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LearningMgService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.tcr.learningMg.";
    @Autowired private CommonDao commonDao;
    
    @Autowired AiRcmTsshQtmCommService aiRcmTsshQtmCommService;
    @Autowired AlMaService alMaService;
    
    
    /**
     * 학습자수준 강제변경(선생님)
     * 
     * */
    @Transactional
    public String updateStuAiLrnrLv(LearningMgDto req) {
    	String result = "";
    	if(req.getLrnrVelTpCd() == null || req.getLrnrVelTpCd().equals("")) {
    		return "[lrnrVelTpCd](은)는 필수 입력 값 입니다.";
    	}
    	if(AlConstUtil.SBJ_EN.contains(req.getSbjCd()) && (req.getTcKmmpNodId() == null || req.getTcKmmpNodId().equals(""))) {
    		return "[tcKmmpNodId](은)는 필수 입력 값 입니다.";
    	}
    	
    	//(과목공통) 진단평가 이후 사용자수준 변경가능
    	List<LearningMgDto> stuEvList = commonDao.selectList(MAPPER_NAMESPACE+ "selectStuAiRcmEvOvInfoList", req);
    	log.debug("stuEvInfo........................");
    	for (LearningMgDto learningMgDto : stuEvList) {
    		log.debug("stuEvInfo: ", learningMgDto);
		}
    	//진단평가 완료여부 체크
    	if(stuEvList == null || stuEvList.isEmpty()) {
    		return "진단평가 완료 후 변경할 수 있습니다.";
    	}
    	//(영어) 맞춤학습 시작후 변경불가
    	if(AlConstUtil.SBJ_EN.contains(req.getSbjCd())) {
    		List<LearningMgDto> stuRcmList = commonDao.selectList(MAPPER_NAMESPACE+ "selectStuAiRcmTcInfoList", req);
    		if(stuRcmList.size() > 0) {
    			return "맞춤학습을 시작한 학생은 학습자수준을 변경할 수 없습니다.";
    		}
    	}
    	
    	//학습자수준 update - 영어는 차시기준, 수학은 중단원기준
    	AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
		dto.setOptTxbId(req.getOptTxbId());
		dto.setMluKmmpNodId(AlConstUtil.SBJ_EN.contains(req.getSbjCd()) ? req.getTcKmmpNodId() : req.getMluKmmpNodId());
		dto.setLrnrVelTpCd(req.getLrnrVelTpCd());
		dto.setUsrId(req.getUsrId());
		dto.setDbId("DB_ID_AI");
		aiRcmTsshQtmCommService.updateStuLrnrVelTpCd(dto);
		result = "success";
		return result;
    }
    
    
    /**
     * 
     * 종합현황 - AI학습 단원진도율
     * */
    public Map<String, Object> selectStuAlProgRt(LearningMgDto dto){
    	Map<String, Object> resultMap = new HashMap<String, Object>();
    	List<Map<String, Object>> stuEvInfoMapList = new ArrayList<>();
    	
    	//학생리스트
    	List<LearningMgDto> selectClaStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectClaStuList", dto);
		//과목명조회
		LearningMgDto learningMgDto = commonDao.select(MAPPER_NAMESPACE + "selectSbjCd", dto);
		List<LearningMgDto> selectStuAlProgRt = new ArrayList<>();
		//단원진도율리스트
		/*if (learningMgDto.getSbjCd().equals("MA") || learningMgDto.getSbjCd().equals("CM1") || learningMgDto.getSbjCd().equals("CM2")) {
			selectStuAlProgRt = commonDao.selectList(MAPPER_NAMESPACE + "selectStuAlProgRtMa", dto);
		} else if (learningMgDto.getSbjCd().equals("EN") || learningMgDto.getSbjCd().equals("CE1") || learningMgDto.getSbjCd().equals("CE2")) {
			selectStuAlProgRt = commonDao.selectList(MAPPER_NAMESPACE + "selectStuAlProgRtEn", dto);
		}*/

		selectStuAlProgRt = commonDao.selectList(MAPPER_NAMESPACE + "selectStuAlProgRt", dto);
    	
    	for (LearningMgDto stu : selectClaStuList) {
    		Map<String, Object> stuMap = new HashMap<String, Object>();
    		stuMap.put("stuNo", stu.getStuNo());
    		stuMap.put("usrId", stu.getUsrId());
    		stuMap.put("usrNm", stu.getUsrNm());
    		stuMap.put("lrnrVelTpCd", stu.getLrnrVelTpCd());
    		
    		for (LearningMgDto prog : selectStuAlProgRt) {
				if(prog.getUsrId().equals(stu.getUsrId())) {
					/*stuMap.put("progRt", (prog.getLuCmplCnt() / prog.getLuCnt()) * 100);*/
					stuMap.put("progRt", prog.getAiLrnPgrsRt());
				}
			}
    		stuEvInfoMapList.add(stuMap);
		}
    	resultMap.put("stuEvInfoMapList", stuEvInfoMapList);
    	return resultMap;
    }   

    
    /**
     * (선생님) 학습관리 - 학습현황관리 - AI 맞춤학습 (영어)
     * 
     * */
    public Map<String, Object> selectStuAeEvInfoList(LearningMgDto dto){
    	Map<String, Object> resultMap = new HashMap<String, Object>();
    	List<Map<String, Object>> stuEvInfoMapList = new ArrayList<>();
    	
    	//진단평가 응시 학생
    	int ovTxmStuCnt = 0;
    	//우리반평균_빠른
    	int lrnrVelTpFsCnt = 0;
    	//우리반평균_보통
    	int lrnrVelTpNmCnt = 0;
    	//우리반평균_느린
    	int lrnrVelTpSlCnt = 0;
    	
    	//학생리스트
    	List<LearningMgDto> selectClaStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectClaStuList", dto);
    	//중단원리스트, 진단평가
    	List<LearningMgDto> selectOvInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectOvInfo", dto);
    	//차시별 맞춤학습
    	List<LearningMgDto> stuAeEvInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuAeEvInfoList", dto);
    	//차시별 사용자학습수준
    	List<LearningMgDto> selectOptTxbIdToAiLrnrLv = commonDao.selectList(MAPPER_NAMESPACE + "selectOptTxbIdToAiLrnrLv", dto);
    	
    	for (LearningMgDto stu : selectClaStuList) {
    		Map<String, Object> stuMap = new HashMap<String, Object>();
    		stuMap.put("stuNo", stu.getStuNo());
    		stuMap.put("usrId", stu.getUsrId());
    		stuMap.put("usrNm", stu.getUsrNm());
    		
    		if(stu.getLrnrVelTpCd().equals("FS")) { lrnrVelTpFsCnt++; }
    		if(stu.getLrnrVelTpCd().equals("NM")) { lrnrVelTpNmCnt++; }
    		if(stu.getLrnrVelTpCd().equals("SL")) { lrnrVelTpSlCnt++; }
    		
    		
    		for (LearningMgDto mlu : selectOvInfo) {
    			//진단평가
    			if(stu.getUsrId().equals(mlu.getUsrId())) {
    				stuMap.put("ovEvCmplYn", mlu.getOvEvCmplYn());
    				stuMap.put("ovQtmCnt", mlu.getQtmCnt());
    				stuMap.put("ovCansYCny", mlu.getCansYCny());
    				stuMap.put("ovEvTmScnt", mlu.getEvTmScnt());
    				stuMap.put("ovEvId", mlu.getEvId());
    				stuMap.put("ovLastLearnDay", mlu.getCrtDtm());
    				if(mlu.getOvEvCmplYn().equals("Y")) { ovTxmStuCnt++; }
    				
    				
    				//단원별 학습자수준(진단평가 맞힌 문항 개수 / 총개수 기준 - 2024.11.29)
    				String lrnrVelTpCd = "";
					double calc = (double) mlu.getCansYCny() / mlu.getQtmCnt();
    				if(calc >= 0.9) {
    					lrnrVelTpCd = "FS";
    				} else if (calc >= 0.5 && calc < 0.9) {
    					lrnrVelTpCd = "NM";
    				} else {
    					lrnrVelTpCd = "SL";
    				}

					log.debug("===usrId:{}, mlu.getCansYCny:{}, mlu.getQtmCnt:{}, calc:{}", mlu.getUsrId(), mlu.getCansYCny(), mlu.getQtmCnt(), calc);
    				stuMap.put("lrnrVelTpCd", lrnrVelTpCd);
    			}
    			
    			for (LearningMgDto lrnr : selectOptTxbIdToAiLrnrLv) {
    				if(stu.getUsrId().equals(lrnr.getUsrId()) && mlu.getMluKmmpNodId().equals(lrnr.getMluKmmpNodId())) {
    					Map<String, Object> tcMap = new HashMap<String, Object>();
    					tcMap.put("tcKmmpNodId", lrnr.getTcKmmpNodId());
    					tcMap.put("lrnrVelTpCd", lrnr.getLrnrVelTpCd());
    					stuMap.put(lrnr.getTcKmmpNodNm(), tcMap);
    					
    	    			for (LearningMgDto tc : stuAeEvInfoList) {
    	    				if(stu.getUsrId().equals(tc.getUsrId()) && lrnr.getTcKmmpNodNm().equals(tc.getTcKmmpNodNm())) {
    	    					tcMap = (Map<String, Object>) stuMap.get(tc.getTcKmmpNodNm());
    	    					tcMap.put("cvEvCmplYn", tc.getCvEvCmplYn());
    	    					tcMap.put("qtmCnt", tc.getQtmCnt());
    	    					tcMap.put("cansYCny", tc.getCansYCny());
    	    					tcMap.put("evTmScnt", tc.getEvTmScnt());
    	    					tcMap.put("crtDtm", tc.getCrtDtm());
    	    					tcMap.put("evId", tc.getEvId());  
    	    					tcMap.put("lrnDtm", tc.getLrnDtm());
    	    					stuMap.put(tc.getTcKmmpNodNm(), tcMap);
    	    				}
    					}
    				}
				}
    			
			}
    		stuEvInfoMapList.add(stuMap);
		}
    	resultMap.put("stuEvInfoMapList", stuEvInfoMapList);
    	
    	int claStuListCnt = selectClaStuList.isEmpty() ? 0 : selectClaStuList.size(); 
    	resultMap.put("ovNTxmStuCnt", claStuListCnt - ovTxmStuCnt);
    	resultMap.put("lrnrVelTpFsCnt", lrnrVelTpFsCnt);
    	resultMap.put("lrnrVelTpNmCnt", lrnrVelTpNmCnt);
    	resultMap.put("lrnrVelTpSlCnt", lrnrVelTpSlCnt);
    	
    	return resultMap;
    }
    
    
    /**
     * (선생님) 학습관리 - 학습현황관리 - AI 맞춤학습 (수학) :: 단원별 학생현황
     * 
     * */
    public Map<String, Object> selectStuMaAeEvInfoList(LearningMgDto dto){
    	Map<String, Object> resultMap = new LinkedHashMap<String, Object>();
    	List<Map<String, Object>> stuList = new ArrayList<Map<String,Object>>();
    	
    	//학생별 진단평가 토픽숙련도 - 운영교과서,중단원ID만 set(유저X)
    	AlTpcMpnDto alTpcMpnDto = new AlTpcMpnDto();
    	alTpcMpnDto.setOptTxbId(dto.getOptTxbId());
    	alTpcMpnDto.setMluKmmpNodId(dto.getMluKmmpNodId());
    	
    	Map<String, Map<String, Double>> stuOvTpcAvnMap = new HashMap<>();
		try {
			stuOvTpcAvnMap = alMaService.getOvTpcAvnUsrMap(alTpcMpnDto);
		} catch (Exception e) {
			log.debug("getOvTpcAvnMap :: 진단평가 데이터 없음");
		}
		
		//학생별 최근학습정보
		List<LearningMgDto> selectLastLearnInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectLastLearnInfo", dto);
		
		//학생별 진단평가정보
		List<LearningMgDto> selectStuOvEvId = commonDao.selectList(MAPPER_NAMESPACE + "selectStuOvEvId", dto);
		
		//학생별 총 학습시간
		List<LearningMgDto> selectStuXplTmScnt = commonDao.selectList(MAPPER_NAMESPACE + "selectStuXplTmScnt", dto);
		
		//학생별 토픽리스트
    	List<LearningMgDto> selectClaStuTpcList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuTpcListMa", dto);
		Map<String, List<LearningMgDto>> stuTpcListMap = selectClaStuTpcList.stream()
			    .collect(Collectors.groupingBy(
			    		LearningMgDto::getUsrId,
				        LinkedHashMap::new, // 순서를 보장하는 LinkedHashMap 사용
				        Collectors.toList()
				    ));
		//사용자리스트
    	for (String usrKey : stuTpcListMap.keySet()) {
    		
    		Map<String, Object> stuMap = new LinkedHashMap<String, Object>();
    		
    		//학습할 토픽
            int learnTpcCnt = 0;
            
            //AI진단평가_이미 알고있는 토픽
            int ovTpcCnt03 = 0;
            List<String> ovTpcCnt03List = new ArrayList<String>();
            //AI진단평가_학습이 더 필요한 토픽
            int ovTpcCnt01 = 0;
            List<String> ovTpcCnt01List = new ArrayList<String>();
    		
    		//AI추천학습_더 알게된 토픽
            int tpcCnt03 = 0;
            List<String> tpcCnt03List = new ArrayList<String>();
            //AI추천학습_아직 부족한 토픽
            int tpcCnt01 = 0;
            List<String> tpcCnt01List = new ArrayList<String>();
            
            //진단평가 토픽Map
            Map<String, Double> ovTpcAvnMap = stuOvTpcAvnMap.get(usrKey);
            
    		//토픽리스트
    		List<LearningMgDto> tpcList = stuTpcListMap.get(usrKey);
    		for (LearningMgDto tpc : tpcList) {
    			learnTpcCnt++;
    			
    			//진단평가 토픽숙련도
    			if(ovTpcAvnMap != null) {
    				Double ovTpcAvn =  ovTpcAvnMap.get(tpc.getTpcKmmpNodId()) == null ? 0.5 : ovTpcAvnMap.get(tpc.getTpcKmmpNodId());
            		if(ovTpcAvn > AlConstUtil.TPC_AVN_03) {
            			ovTpcCnt03++;
            			ovTpcCnt03List.add(tpc.getTpcKmmpNodNm());
    				}else {
    					ovTpcCnt01++;
    					ovTpcCnt01List.add(tpc.getTpcKmmpNodNm());
    				}
    			}
    			
    			//토픽숙련도
            	Double tpcAvn = tpc.getTpcAvn() == null ? 0.5 : tpc.getTpcAvn();
            	if(tpcAvn > AlConstUtil.TPC_AVN_03) {
            		tpcCnt03++;
            		tpcCnt03List.add(tpc.getTpcKmmpNodNm());
				}else {
					tpcCnt01++;
					tpcCnt01List.add(tpc.getTpcKmmpNodNm());
				}
            	
            	stuMap.put("stuNo", tpc.getStuNo());				//학생No
    			stuMap.put("usrId", tpc.getUsrId());				//학생ID
    			stuMap.put("usrNm", tpc.getUsrNm());				//학생명
    			stuMap.put("lrnrVelTpCd", tpc.getLrnrVelTpCd());	//학생학습수준
			}
    		stuMap.put("learnTpcCnt", learnTpcCnt);					//총 토픽수
    		stuMap.put("ovTpcCnt03", ovTpcCnt03);					//진단_이미알고있는토픽수
    		stuMap.put("ovTpcCnt03List", ovTpcCnt03List);			//진단_이미알고있는토픽리스트
    		stuMap.put("ovTpcCnt01", ovTpcCnt01);					//진단_학습이 더 필요한 토픽수
    		stuMap.put("ovTpcCnt01List", ovTpcCnt01List);			//진단_학습이 더 필요한 토픽리스트
    		stuMap.put("tpcCnt03", tpcCnt03);						//추천학습_더 알게된 토픽수
    		stuMap.put("tpcCnt03List", tpcCnt03List);				//추천학습_더 알게된 토픽리스트
    		stuMap.put("tpcCnt01", tpcCnt01);						//추천학습_아직 부족한 토픽수
    		stuMap.put("tpcCnt01List", tpcCnt01List);				//추천학습_아직 부족한 토픽리스트
    		
    		//학생별 최근학습
            for (LearningMgDto lastEv : selectLastLearnInfo) {
				if(lastEv.getUsrId().equals(usrKey.toString())){
					stuMap.put("lastLearnDay", lastEv.getCrtDtm());		//마지막학습일
					stuMap.put("lastEvDtlDvCd", lastEv.getEvDtlDvCd());	//마지막 완료평가 구분코드
				}
			}
            //학생별 진단평가ID - 레포트 이동시 필요한 파라미터라고 함 
            for (LearningMgDto ov : selectStuOvEvId) {
				if(ov.getUsrId().equals(usrKey.toString())){
					stuMap.put("ovEvId", ov.getEvId());					//진단평가ID
				}
			}
            
            //학생별 총 학습시간
            for (LearningMgDto tm : selectStuXplTmScnt) {
				if(tm.getUsrId().equals(usrKey.toString())){
					stuMap.put("learningTm", tm.getLearningTm());			//총학습시간
				}
			}
            stuList.add(stuMap);
    	}
    	resultMap.put("stuList", stuList);
    	
    	//우리반 평균 :: 평가내역이 있는 학생들 중 추천학습이 있으면 추천학습, 없으면 진단평가 기준으로 평균을 낸다.
        int claAvgTpc03 = 0; //이미 알고있는 토픽
        int claAvgTpc01 = 0; //학습이 더 필요한 토픽
        int claTxmCnt = 0;	//학습이력이 있는 학생
        for (Map<String, Object> stu : stuList) {
        	if(stu.get("lastEvDtlDvCd") != null) {
        		claTxmCnt++;
        		if(stu.get("lastEvDtlDvCd").equals("OV")) {
        			claAvgTpc03 += Integer.parseInt(stu.get("ovTpcCnt03").toString());
        			claAvgTpc01 += Integer.parseInt(stu.get("ovTpcCnt01").toString());
        		}else {
        			claAvgTpc03 += Integer.parseInt(stu.get("tpcCnt03").toString());
        			claAvgTpc01 += Integer.parseInt(stu.get("tpcCnt01").toString());
        		}
        	}
		}
        resultMap.put("claAvgTpc03", claTxmCnt == 0 ? 0 : claAvgTpc03 / claTxmCnt);	//우리반평균_이미 알고있는 토픽수
        resultMap.put("claAvgTpc01", claTxmCnt == 0 ? 0 : claAvgTpc01 / claTxmCnt);	//우리반평균_학습이 더 필요한 토픽수
        resultMap.put("ovNtxmStuCnt", stuTpcListMap.size() - claTxmCnt);	//AI진단 미응시학생
    	return resultMap;
    }
    
}
