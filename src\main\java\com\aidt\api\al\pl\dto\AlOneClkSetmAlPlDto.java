package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlOneClkSetmAlPlDto {
	 /** 원클릭학습설정 Ai맞춤학습 목차 저장 리스트 */
    @Parameter(name="원클릭학습설정 Ai맞춤학습 목차 저장 리스트")
    private List<AlOneClkSetmAlTocDto> alTocList;

    /** 원클릭학습설정 다른 학급 리스트 */
    @Parameter(name="원클릭학습설정 다른 학급 리스트")
    private List<AlOneClkSetmClaDto> claList;
	

}
