package com.aidt.api.bc.home.tcr;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto;
import com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-19 14:29:00
 * @modify 2024-06-19 14:29:00
 * @desc 교사 > 홈 > 우리반 수업 분석 service
 */

@Service
public class BcClaLsnAnService {
	
	private final String MAPPER_NAMESPACE = "api.bc.clalsnan.tcr.";
	
	@Autowired
    private CommonDao commonDao;
	
	/**
	 * [교과서 공부 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회
	 * 
	 * @param dto
	 * @return List<BcClaLsnAnResDto>
	 */
	public List<BcClaLsnAnResDto> selectTxbStuList(BcClaLsnAnReqDto dto) {
		
		List<BcClaLsnAnResDto> stuList = commonDao.selectList(MAPPER_NAMESPACE + "selectFrmEvList", dto);
		List<BcClaLsnAnResDto> lrnRsList = commonDao.selectList(MAPPER_NAMESPACE + "selectTcLsnAnList", dto);
		
		stuList.forEach(list -> {
			List<BcClaLsnAnResDto> stuRs = lrnRsList.stream().filter(rs -> rs.getUsrId().equals(list.getUsrId())).collect(Collectors.toList());
			list.setTcLsnAnList(stuRs);
		});
		return stuList;
	}
	
	/**
	 * [우리반 수업 분석] - 차시목록
	 * 
	 * @param dto
	 * @return List<BcClaLsnAnResDto>
	 */
	public List<BcClaLsnAnResDto> selectTcList(BcClaLsnAnReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTcList", dto);
	}
	
	/**
	 * [교과서 공부 탭] - 헤더 목록 조회
	 * 
	 * @param dto
	 * @return
	 */
	public List<BcClaLsnAnResDto> selectTxbStuHdrList(BcClaLsnAnReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTxbStuHdrList", dto);
	}
	
	/**
	 * [형성평가 탭] - 헤더 목록 조회
	 * 
	 * @param dto
	 * @return
	 */
	public List<BcClaLsnAnResDto> selectFrmEvHdrList(BcClaLsnAnReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectFrmEvHdrList", dto);
	}
	
	/**
	 * [형성평가 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회
	 * 
	 * @param dto
	 * @return List<BcClaLsnAnResDto>
	 */
	public List<BcClaLsnAnResDto> selectFrmEvList(BcClaLsnAnReqDto dto) {
		List<BcClaLsnAnResDto> stuList = commonDao.selectList(MAPPER_NAMESPACE + "selectFrmEvList", dto);
		List<BcClaLsnAnResDto> evRsList = commonDao.selectList(MAPPER_NAMESPACE + "selectFrmEvAnList", dto);
		
		stuList.forEach(list -> {
			List<BcClaLsnAnResDto> stuRs = evRsList.stream().filter(evRs -> evRs.getUsrId().equals(list.getUsrId())).collect(Collectors.toList());
			list.setTcLsnAnList(stuRs);
		});
		return stuList;
	}
	
	/**
     * [수학익힙 탭] - 헤더 목록 조회
     * 
     * @param dto
     * @return
     */
    public List<BcClaLsnAnResDto> selectMthPrtHdrList(BcClaLsnAnReqDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMthPrtHdrList", dto);
    }
    
    /**
     * [수학익힙 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회
     * 
     * @param dto
     * @return List<BcClaLsnAnResDto>
     */
    public List<BcClaLsnAnResDto> selectMthPrtList(BcClaLsnAnReqDto dto) {    
        List<BcClaLsnAnResDto> stuList = commonDao.selectList(MAPPER_NAMESPACE + "selectMthPrtList", dto);
		List<BcClaLsnAnResDto> wbRsList = commonDao.selectList(MAPPER_NAMESPACE + "selectMthPrtAtvList", dto);
		
		stuList.forEach(list -> {
			List<BcClaLsnAnResDto> stuRs = wbRsList.stream().filter(evRs -> evRs.getUsrId().equals(list.getUsrId())).collect(Collectors.toList());
			list.setTcLsnAnList(stuRs);
		});
		return stuList;
    }

}
