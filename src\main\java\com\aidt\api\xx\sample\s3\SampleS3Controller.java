package com.aidt.api.xx.sample.s3;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.UUID;

import javax.validation.Valid;

import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.xx.sample.s3.dto.SampleS3Dto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.AbortMultipartUploadRequest;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.ListMultipartUploadsRequest;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.MultipartUpload;
import com.amazonaws.services.s3.model.MultipartUploadListing;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.services.s3.model.S3ObjectSummary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[xx] Sample.S3" , description="Ncloude S3 File 관련 샘플")
@RestController
@RequestMapping("/api/v1/bc/tcr/s3")
public class SampleS3Controller {
	/**
	 * Bucket Retrieve
	 *
	 * @return ResponseDto<List<Bucket>>
	 */
	@Operation(summary="Bucket 조회", description = "Ncloud Bucket 조회")
	@GetMapping(value = "/bucket")
	public ResponseDto<List<Bucket>> bucketList() {


		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		List<Bucket> buckets = null;
		try {
			// bucket List Request
			buckets = s3.listBuckets();

			// disconnect
			s3.shutdown();

			// print
			buckets.forEach(b -> log.info(b.toString()));
			return Response.ok(buckets);
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	@Operation(summary="Bucket 조회", description = "Ncloud Bucket 조회")
	@GetMapping(value = "/bucket-with-role")
	public ResponseDto<List<Bucket>> bucketListWithRole() {
		log.info("bucket-with-role");
		// S3 client connect hey
//		final AmazonS3 s3 = getAmazonS3Client();
		final AmazonS3 s3 = getAmazonS3ClientWithRole();

		List<Bucket> buckets = null;
		try {
			// bucket List Request
			buckets = s3.listBuckets();

			// disconnect
			s3.shutdown();

			// print
			buckets.forEach(b -> log.info(b.toString()));
			return Response.ok(buckets);
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}


	/**
	 * Create Bucket
	 *
	 * @param bucket
	 * @return ResponseDto<?>
	 */
	@Operation(summary="Bucket 생성", description = "Ncloud Bucket 생성")
	@PostMapping(value = "/bucket/{bucketName}",
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<?> createBucket(@PathVariable("bucketName") String bucketName) {
		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		try {
			// bucket valid request
			if (s3.doesBucketExistV2(bucketName)) {

				// dis connect
				s3.shutdown();
				return Response.fail("Bucket [ " + bucketName + " ] already exists.");
			} else {
				// create request
				s3.createBucket(bucketName);

				log.info("Bucket {} has been created.", bucketName);

				// disconnect
				s3.shutdown();
				return Response.ok();
			}
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}


	/**
	 * Delete Bucket
	 *
	 * @param bucket
	 * @return ResponseDto<?>
	 */
	@Operation(summary="Bucket 삭제", description = "Ncloud Bucket 삭제")
	@DeleteMapping(value = "/bucket/{bucketName}",
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<?> deleteBucket(@PathVariable("bucketName") String bucketName) {
		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		try {
			// delete bucket if the bucket exists
			if (s3.doesBucketExistV2(bucketName)) {
				// delete all objects
				ObjectListing objectListing = s3.listObjects(bucketName);
				while (true) {
					// delete file
					for (Iterator<?> iterator = objectListing.getObjectSummaries().iterator(); iterator.hasNext();) {
						S3ObjectSummary summary = (S3ObjectSummary) iterator.next();
						s3.deleteObject(bucketName, summary.getKey());
					}

					// truncated
					if (objectListing.isTruncated()) {
						objectListing = s3.listNextBatchOfObjects(objectListing);
					} else {
						break;
					}
				}

				// abort incomplete multipart uploads
				MultipartUploadListing multipartUploadListing =
						s3.listMultipartUploads(new ListMultipartUploadsRequest(bucketName));
				while (true) {
					// uploader stop
					for (Iterator<?> iterator = multipartUploadListing.getMultipartUploads().iterator(); iterator
							.hasNext();) {
						MultipartUpload multipartUpload = (MultipartUpload) iterator.next();
						s3.abortMultipartUpload(new AbortMultipartUploadRequest(
								bucketName, multipartUpload.getKey(),
								multipartUpload.getUploadId()));
					}

					// truncated check
					if (multipartUploadListing.isTruncated()) {
						ListMultipartUploadsRequest listMultipartUploadsRequest =
								new ListMultipartUploadsRequest(bucketName);
						listMultipartUploadsRequest.withUploadIdMarker(multipartUploadListing.getNextUploadIdMarker());
						multipartUploadListing = s3.listMultipartUploads(listMultipartUploadsRequest);
					} else {
						break;
					}
				}

				// delete request
				s3.deleteBucket(bucketName);

				// disconnect
				s3.shutdown();
				log.info("Bucket {} has been deleted.", bucketName);
				return Response.ok();
			} else {
				return Response.fail("Bucket [ " + bucketName + " ] does not exist.");
			}
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	/**
	 * File Upload
	 *
	 * @param bucketName
	 * @param path
	 * @param files
	 * @return ResponseDto<List<FileDto>>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Operation(summary="S3 File Upload", description = "Ncloud S3 File 등록")
	@PostMapping(value = "/file", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseDto<SampleS3Dto.FileUploadResponse> fileUpload(String bucketName, MultipartFile file)
			throws IllegalStateException, IOException {
		// validation
		Optional.ofNullable(bucketName).orElseThrow(() -> new NoSuchElementException("Bucket name is empty"));
		Optional.ofNullable(file).orElseThrow(() -> new FileNotFoundException("File Not Found!"));

		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		// upload local file
		try {
			// File Meta
			ObjectMetadata metadata = new ObjectMetadata();
			metadata.setContentLength(file.getSize());
			metadata.setContentType(file.getContentType());

			// file Object Name
			String newFileName = UUID.randomUUID().toString();

			//File Upload
			s3.putObject(bucketName, newFileName, file.getInputStream(), metadata);

			// disconnect
			s3.shutdown();
			log.info("Object {} has been created.", file.getOriginalFilename());
			return Response.ok(
					SampleS3Dto.FileUploadResponse.builder()
							.originFileName(file.getOriginalFilename())
							.newFileName(newFileName)
							.contentType(file.getContentType())
							.fileSize(file.getSize())
							.build());
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	/**
	 * File Upload
	 *
	 * @param bucketName
	 * @param path
	 * @param files
	 * @return ResponseDto<List<FileDto>>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Operation(summary="S3 File Upload Multi", description = "Ncloud S3 File 여러개 등록")
	@PostMapping(value = "/file/multi",
			consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseDto<List<SampleS3Dto.FileUploadResponse>> fileUploadMulti(String bucketName, List<MultipartFile> files)
			throws IllegalStateException, IOException {
		// validation
		Optional.ofNullable(bucketName).orElseThrow(() -> new NoSuchElementException("Bucket name is empty"));
		Optional.ofNullable(files).orElseThrow(() -> new FileNotFoundException("File Not Found!"));

		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		// upload local file
		try {
			List<SampleS3Dto.FileUploadResponse> fileUploadResponseList = new ArrayList<>();
			ObjectMetadata metadata = null;
			for (MultipartFile file : files) {
				// File Meta
				metadata = new ObjectMetadata();
				metadata.setContentLength(file.getSize());
				metadata.setContentType(file.getContentType());

				// file Object Name
				String newFileName = UUID.randomUUID().toString();

				//File Upload
				s3.putObject(bucketName, newFileName, file.getInputStream(), metadata);

				log.info("Object {} has been created.", file.getOriginalFilename());

				fileUploadResponseList.add(SampleS3Dto.FileUploadResponse.builder()
						.originFileName(file.getOriginalFilename())
						.newFileName(newFileName)
						.contentType(file.getContentType())
						.fileSize(file.getSize())
						.build());
			} ;

			// disconnect
			s3.shutdown();

			return Response.ok(fileUploadResponseList);
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	/**
	 * Retrieve File List In bucket
	 *
	 * @param bucketName
	 * @param folder
	 * @param files
	 * @return ResponseDto<List<FileUploadResponse>>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Operation(summary="S3 File Retrieve", description = "Ncloud S3 File 조회")
	@GetMapping(value = "/file/list/{bucketName}")
	public ResponseDto<ObjectListing> fileList(@PathVariable("bucketName") String bucketName)
			throws IllegalStateException, IOException {
		//retrieve size
		int maxKeys = 1000;

		// S3 client connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		// top level folders and files in the bucket
		try {
			ListObjectsRequest listObjectsRequest = new ListObjectsRequest()
					.withBucketName(bucketName)
					.withDelimiter("/")
					.withMaxKeys(maxKeys);

			// file list request
			ObjectListing objectListing = s3.listObjects(listObjectsRequest);

			// disconnect
			s3.shutdown();
			log.info("File List:");
			for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
				log.info("    name=" + objectSummary.getKey() + ", size=" + objectSummary.getSize()
						+ ", owner=" + objectSummary.getOwner().getId());
			}
			return Response.ok(objectListing);
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}


	@Operation(summary="S3 File Retrieve", description = "Ncloud S3 File 조회 with role")
	@GetMapping(value = "/file/list-wirh-role/{bucketName}")
	public ResponseDto<ObjectListing> fileListWirhRole(@PathVariable("bucketName") String bucketName)
			throws IllegalStateException, IOException {
		//retrieve size
		int maxKeys = 1000;

		// S3 client connect
//		final AmazonS3 s3 = getAmazonS3Client();
		final AmazonS3 s3 = getAmazonS3ClientWithRole();

		// top level folders and files in the bucket
		try {
			ListObjectsRequest listObjectsRequest = new ListObjectsRequest()
					.withBucketName(bucketName)
					.withDelimiter("/")
					.withMaxKeys(maxKeys);

			// file list request
			ObjectListing objectListing = s3.listObjects(listObjectsRequest);

			// disconnect
			s3.shutdown();
			log.info("File List:");
			for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
				log.info("    name=" + objectSummary.getKey() + ", size=" + objectSummary.getSize()
						+ ", owner=" + objectSummary.getOwner().getId());
			}
			return Response.ok(objectListing);
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	/**
	 * Download File
	 *
	 * @param bucketName
	 * @param newFileName
	 * @return ResponseDto<ObjectListing>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Operation(summary="S3 File Download", description = "Ncloud S3 File 다운로드")
	@GetMapping(value = "/file/download")
	public ResponseEntity<?> downloadFile(@Valid SampleS3Dto.FileDownlaodRequest fileDownlaodRequest)
			throws IllegalStateException, IOException {

		// S3 client Connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		// top level folders and files in the bucket
		try {

			// get File
			S3Object s3Object = s3.getObject(fileDownlaodRequest.getBucketName(), fileDownlaodRequest.getNewFileName());

			// get inputStream
			S3ObjectInputStream s3ObjectInputStream = s3Object.getObjectContent();

			// convert to byte array
			byte[] bytes = IOUtils.toByteArray(s3ObjectInputStream);

			// file name urlencoding
			String fileName =
					URLEncoder.encode(fileDownlaodRequest.getOriginFileName(), "UTF-8").replaceAll("\\+", "%20");

			// Set Header
			HttpHeaders httpHeaders = new HttpHeaders();
			httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			httpHeaders.setContentLength(bytes.length);
			httpHeaders.setContentDispositionFormData("attachment", fileName);

			// inputStream.close();
			s3ObjectInputStream.close();

			// disconnect
			s3.shutdown();

			return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
		} catch (AmazonS3Exception e) {
			return ResponseEntity.internalServerError().build();
		} catch (SdkClientException e) {
			return ResponseEntity.internalServerError().build();
		}
	}

	/**
	 * Delete File
	 *
	 * @param bucketName
	 * @param newFileName
	 * @return ResponseDto<ObjectListing>
	 * @throws IllegalStateException
	 * @throws IOException
	 */
	@Operation(summary="S3 File Delete", description = "Ncloud S3 File 삭제")
	@DeleteMapping(value = "/file/{bucketName}/{newFileName}", consumes = { MediaType.APPLICATION_JSON_VALUE,
			MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseDto<ObjectListing> deleteFile(
			@PathVariable("bucketName") String bucketName,
			@PathVariable("newFileName") String newFileName)
			throws IllegalStateException, IOException {
		Optional.ofNullable(bucketName).orElseThrow(() -> new NoSuchElementException("Bucket name is empty"));
		Optional.ofNullable(newFileName).orElseThrow(() -> new NoSuchElementException("New file name is empty"));

		// S3 client Connect
		final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

		// top level folders and files in the bucket
		try {

			// request delete file
			s3.deleteObject(bucketName, newFileName);

			// disconnect
			s3.shutdown();

			log.info("Object {} has been deleted.\n", newFileName);
			return Response.ok();
		} catch (AmazonS3Exception e) {
			return Response.fail("S3 File Upload Multi AmazonS3Exception error");
		} catch (SdkClientException e) {
			return Response.fail("S3 File Upload Multi SdkClientException error");
		}
	}

	private AmazonS3 getAmazonS3ClientWithRole() {
        String endPoint = BcCmUtil.END_POINT;
        String regionName = BcCmUtil.REGION_NAME;

        return AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endPoint, regionName))
                .withCredentials(InstanceProfileCredentialsProvider.getInstance())
                .build();
    }
}
