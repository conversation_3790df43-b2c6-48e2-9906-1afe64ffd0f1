package com.aidt.api.al.fdbk.stu;

import com.aidt.api.al.fdbk.dto.AiFdbkGrowthRankDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.fdbk.dto.AiFdbkDto;
import com.aidt.api.al.fdbk.dto.req.AiFdbkStuReqDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:52:33
 * @modify date 2024-07-11 15:52:33
 * @desc
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/fdbk/stu")
@Tag(name="[al] AI 학습 피드백", description="학습 피드백")
public class AiFdbkStuController {

    private final JwtProvider jwtProvider;

    private final AiFdbkStuService aiFdbkStuService;

    @GetMapping("/feedback/{lrmpNodId}")
    public ResponseDto<AiFdbkDto> getFdbk(@PathVariable String lrmpNodId) {
        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        AiFdbkDto dto = aiFdbkStuService.getFdbk(
                AiFdbkStuReqDto.builder()
                        .lrmpNodId(lrmpNodId)
                        .optTxbId(user.getOptTxbId())
                        .usrId(user.getUsrId()).build());
        return Response.ok(dto);
    }

    @GetMapping("/growth-rank/{lrmpNodId}")
    public ResponseDto<List<AiFdbkGrowthRankDto>> getGrowthRankList(
            @PathVariable String lrmpNodId) {
        CommonUserDetail user = jwtProvider.getCommonUserDetail();

        List<AiFdbkGrowthRankDto> growthRankList = aiFdbkStuService.getGrowthRankList(user.getOptTxbId(), lrmpNodId, user.getUsrId());
        return Response.ok(growthRankList);
    }

}
