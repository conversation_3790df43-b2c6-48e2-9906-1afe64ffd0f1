package com.aidt.api.ea.evcom.ev.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 교과 평가 - 학생 요청 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvMainReqDto {

	/* 공통*/
	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	
	
	@Parameter(name="학교반ID")
	private String claId;	
	
	@Parameter(name="사용자ID")
	private String usrId;	
	
	@Parameter(name="사용자ID")
	private String tcrUsrId;		
	
	@Parameter(name="학급 평가ID")
	private long claEvId;

	/* 조회 조건 */
	@Parameter(name="탭구분코드")
	private String tbscDvCd;
	
	@Parameter(name="조회대단원학습맵노드ID")
	private String srhLuLrmpNodId;
	
	@Parameter(name="정렬구분코드")
	private String srtDvCd;
	
	@Parameter(name="학습유형구분코드")
	private String srhEvTp;
	
	private int pageNo;
	private int pageSize;
}
