package com.aidt.api.al.pl.cm.rcm;

import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * AI 맞춤 문항추천
 */

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/al/pl/cm/qtmTpcProf")
@Tag(name="[al] AI맞춤 사용자별 문항토픽별 프로파일", description="AI맞춤 사용자별 문항토픽별 프로파일")
public class AlQtmTpcProfController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private AlQtmTpcProfService alQtmTpcProfService;
	
	@Autowired
	private AiRcmTsshQtmCommService commService;
	
	/**
	 * 평가완료시 사용자별 문항,토픽 프로파일 데이터 적재
	 * 
	 * @return 
	 * */
	@Tag(name="[al] 사용자별 프로파일 적재", description="평가완료시 사용자별 문항,토픽 프로파일 데이터 적재")
    @PostMapping(value = "/selectUsrlyQtmProf")
    public ResponseDto<Map<String, Object>> selectAiRcmTsshQtmList(@Valid @RequestBody AlPlQtmTpcProfDto dto, HttpServletRequest request) {
		CommonUserDetail userDetail = jwtProvider.getCommonUserDetail();
		RequestContextHolder.currentRequestAttributes()
				.setAttribute("userDetail", userDetail, RequestAttributes.SCOPE_REQUEST);

		Map<String, Object> resultMap = alQtmTpcProfService.selectUsrlyQtmProf(dto, request);
		
		return Response.ok(resultMap);
    }
	
	
//	/**
//	 * AI맞춤 단원완료 UPDATE - API분리
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] 단원완료여부 체크 및 저장", description="단원완료여부 UPDATE 및 수학 토픽진행률 저장")
//    @PostMapping(value = "/updateLuEvCmplYn")
//    public void updateLuEvCmplYn(@Valid @RequestBody AlRcmLuEvCmplDto dto) {
//		log.debug(dto.toString());
//        
//    }
	
}
