package com.aidt.api.common.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum SubjectCode {

	EN("EN", "영어", "EN"),
	CE1("CE1", "공통영어1", "EN"),
	CE2("CE2", "공통영어2", "EN"),
	MA("MA", "수학", "MA"),
	CM1("CM1", "공통수학1", "MA"),
	CM2("CM2", "공통수학2", "MA");

	private final String code;
	private final String desc;
	private final String field;

	SubjectCode(String code, String desc, String field) {
		this.code = code;
		this.desc = desc;
		this.field = field;
	}

	public static SubjectCode getSubjectCode(String code) {
		return Arrays.stream(values())
			.filter(x -> x.getCode().equalsIgnoreCase(code))
			.findAny()
			.orElseThrow(() -> new IllegalArgumentException("과목 코드를 찾을 수 없습니다."));
	}

}
