package com.aidt.api.tl.chlg.tcr;

import com.aidt.api.tl.chlg.dto.TlLrnChlgDto;
import com.aidt.api.tl.chlg.stu.TlLrnChlgStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-01-21
 * @modify date 2025-01-21
 * @desc TlLrnChlgStu Service 교과학습 챌린지 조회 서비스
 */
@Slf4j
@Tag(name="[tl] 챌린지설정[TlLrnChlgTcr]", description="학생의 챌린지 현황을 조회함")
@RestController
@RequestMapping("/api/v1/tl/tcr/chlg")
public class TlLrnChlgTcrController {

    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlLrnChlgTcrService tlLrnChlgTcrService;

    @Autowired
    private TlLrnChlgStuService tlLrnChlgStuService;

    /**
     * 챌린지목록 조회 서비스
     *
     * @return ResponseDto<List<TlLrnChlgDto>>
     */
    @Operation(summary="학습챌린지 정보 리스트 조회", description="학습챌린지 정보목록을 조회한다.")
    @PostMapping(value = "/selectLrnChlgList")
    public Response.ResponseDto<List<TlLrnChlgDto>> selectLrnChlgList(@RequestBody Map<String, String> param) {
        log.debug("Entrance selectLrnChlgList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String dateString = param.get("targetDate");
        LocalDate targetDate = (dateString == null || dateString.isBlank()) ? LocalDate.now() : LocalDate.parse(dateString);
        // 챌린지목록정보 조회
        return Response.ok(tlLrnChlgTcrService.selectLrnChlgList(userDetails.getOptTxbId(), userDetails.getClaId(), targetDate));
    }

    /**
     * 챌린지목록 조회 서비스
     *
     * @return ResponseDto<List<TlLrnChlgDto>>
     */
    @Operation(summary="학습챌린지 정보 리스트 조회", description="학습챌린지 정보목록을 조회한다.")
    @PostMapping(value = "/selectLrnChlgListByUsrId")
    public Response.ResponseDto<List<TlLrnChlgDto>> selectLrnChlgListByUsrId(@RequestBody Map<String, String> param) {
        log.debug("Entrance selectLrnChlgListByUsrId");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        return Response.ok(tlLrnChlgStuService.selectLrnChlgList(userDetails.getOptTxbId(), param.get("usrId")));
    }

}
