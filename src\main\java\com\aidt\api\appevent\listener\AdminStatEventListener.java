package com.aidt.api.appevent.listener;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.aidt.api.appevent.event.adminstat.ClaInsertEvent;
import com.aidt.api.appevent.event.adminstat.LoginEvent;
import com.aidt.api.appevent.event.adminstat.OptTxbInsertEvent;
import com.aidt.api.appevent.event.adminstat.OptTxbPridInsertEvent;
import com.aidt.api.appevent.event.adminstat.UsrInsertEvent;
import com.aidt.api.config.MessageQueueConfig;
import com.aidt.base.message.application.AbstractAppEvent;
import com.aidt.base.message.messagequeue.MessageQueue;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AA 설계 의도 :
 * - com.aidt.api.config.MessageQueueConfig 선언된 Topic 단위 EventListener class 생성 
 */
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.enabled", matchIfMissing = true, havingValue = "true")
@Component
public class AdminStatEventListener {
	private final MessageQueue messageQueue;
	private final MessageQueueConfig.Topics topic = MessageQueueConfig.Topics.ADMIN_STAT;
	
	 
	@EventListener
	public void listen(LoginEvent event) {
		var payload = event.getPayload();
		log.debug("Event {} arrived: {}", event.getClass().getName(), payload);
		messageQueue.publish(topic, event);
	}

	@EventListener(classes = { 
			UsrInsertEvent.class, 
			OptTxbInsertEvent.class, 
			ClaInsertEvent.class,
			OptTxbPridInsertEvent.class
	})
	public void listen(AbstractAppEvent event) {
		var payload = event.getPayload();
		log.debug("Event {} arrived: {}", event.getClass().getName(), payload);
		messageQueue.publish(topic, event);
	}
}
