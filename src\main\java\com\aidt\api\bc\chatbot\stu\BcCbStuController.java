package com.aidt.api.bc.chatbot.stu;


import com.aidt.api.bc.chatbot.dto.*;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.co.shineware.nlp.komoran.model.Token;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


@Tag(name="[bc] 챗봇 개발사 API 호출", description="챗봇 개발사 API 호출")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/stu/chatbot")
public class BcCbStuController {

    @Autowired
    private JwtProvider jwtProvider;
	
    @Autowired
    private BcCbStuService bcChatBotStuService;

    /** DB-ID */
    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;

    /**
     * 챗봇 개발사 API 호출
     * @param BcCbDto
     * @return
     * @throws JsonProcessingException 
     * @throws JsonMappingException 
     */
    @Operation(summary="챗봇 개발사 API 호출", description="챗봇 개발사 API 호출하여 리턴값을 가져온다")
    @PostMapping(value = "/callChatBotApi", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> callChatBotApi(@Valid @RequestBody BcCbDto bcChatBotDto) throws JsonMappingException, JsonProcessingException {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcChatBotDto.setUsrId(userDetails.getUsrId());
        bcChatBotDto.setTxbId(userDetails.getTxbId());
        return bcChatBotStuService.callChatBotApi(bcChatBotDto);
    }

    @Operation(summary="챗봇 LMS 데이터 키워드 검색", description="챗봇 LMS 데이터 키워드 검색")
    @PostMapping(value = "/selectKeyWord", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BcCbKeyWordDto> selectKeyWord(@Valid @RequestBody BcCbSrhAllDto chatBotDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        chatBotDto.setOptTxbId(userDetails.getOptTxbId());
        chatBotDto.setUsrId(userDetails.getUsrId());
        return bcChatBotStuService.selectKeyWord(chatBotDto);
    }
    
    @Operation(summary="챗봇 LMS 데이터 키워드 검색", description="챗봇 LMS 데이터 키워드 검색")
    @PostMapping(value = "/selectAiKeyWord", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BcCbKeyWordDto> selectAiKeyWord(@Valid @RequestBody BcCbSrhAllDto chatBotDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        chatBotDto.setOptTxbId(userDetails.getOptTxbId());
        chatBotDto.setUsrId(userDetails.getUsrId());
        return bcChatBotStuService.selectAiKeyWord(chatBotDto);
    }

    @Operation(summary="LMS 단어사전 검색", description="LMS 단어사전 검색")
    @PostMapping(value = "/selectDicWd", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BcCbWdDto> selectDicWd(@Valid @RequestBody BcCbSrhAllDto chatBotDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        chatBotDto.setTxbId(userDetails.getTxbId());
        return bcChatBotStuService.selectDicWd(chatBotDto);
    }
    
    @Operation(summary="LMS 수학 단어사전 검색", description="LMS 수학 단어사전 검색")
    @PostMapping(value = "/selectMathWd", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BcCbWdDto> selectMathWd(@Valid @RequestBody BcCbSrhAllDto chatBotDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        chatBotDto.setTxbId(userDetails.getTxbId());
        return bcChatBotStuService.selectMathWd(chatBotDto);
    }

    @Operation(summary="챗봇 만족도 저장", description="챗봇 만족도를 저장한다(G : 참잘했어요, S : 보통이에요, N : 노력하세요)")
    @PostMapping(value = "/saveCsatInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> saveCsatInfo(@Valid @RequestBody BcCbCsatDto saveDto) {
        // 등록 데이터 검증
        if (saveDto == null) {
            throw new IllegalArgumentException("Invalid saveDto");
        }
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        saveDto.setLrnUsrId(userDetails.getUsrId());
        saveDto.setDbId(DB_ID);

         return Response.ok(bcChatBotStuService.saveCsatInfo(saveDto));
    }

    @Operation(summary="챗봇 사용자 이용 정보", description="사용자의 질문을 저장한다.")
    @PostMapping(value = "/saveUsrUseInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> saveUsrUseInfo(@Valid @RequestBody BcCbUsrUseDto saveDto) {
        // 등록 데이터 검증
        if (saveDto == null) {
            throw new IllegalArgumentException("Invalid saveDto");
        }
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        saveDto.setLrnUsrId(userDetails.getUsrId());
        saveDto.setDbId(DB_ID);

         return Response.ok(bcChatBotStuService.saveUsrUseInfo(saveDto));
    }

    @Operation(summary="LMS 단어사전 검색", description="LMS 단어사전 검색")
    @PostMapping(value = "/selectUsrUseInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BcCbUsrUseDto> selectUsrUseInfo(@Valid @RequestBody BcCbUsrUseDto chatBotDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        chatBotDto.setLrnUsrId(userDetails.getUsrId());
        chatBotDto.setDbId(DB_ID);
        return bcChatBotStuService.selectUsrUseInfo(chatBotDto);
    }
    
    @Operation(summary="학생 챗봇 사용 가능 여부", description="학생 챗봇 사용 가능 여부")
    @PostMapping(value = "/selectCbUseYn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BcCbUseYnDto selectCbUseYn() {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	BcCbUseYnDto chatBotDto = new BcCbUseYnDto();
    	chatBotDto.setOptTxbId(userDetails.getOptTxbId());
    	return bcChatBotStuService.selectCbUseYn(chatBotDto);
    }
    
    @Operation(summary="챗봇 전체 데이터 검색", description="챗봇 전체 데이터 검색")
    @PostMapping(value = "/selectCbDataAll", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BcCbDataAllDto selectCbDataAll(@Valid @RequestBody BcCbSrhAllDto chatBotDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        chatBotDto.setTxbId(userDetails.getTxbId());
        chatBotDto.setUsrId(userDetails.getUsrId());
        chatBotDto.setOptTxbId(userDetails.getOptTxbId());
        
        return bcChatBotStuService.selectCbDataAll(chatBotDto);
    }

    @Operation(summary="챗봇 형태소 분석", description="챗봇 형태소 분석")
    @PostMapping(value = "/callKomoran", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Token>  callKomoran(@Valid @RequestBody BcCbWdSrhDto chatBotDto) throws Exception {
        return bcChatBotStuService.analyzeWithDigitHandling(chatBotDto);
    }
    
    @GetMapping("/autr")
    @Operation(summary = "사용자 교과서 저자 정보 조회", description = "사용자 교과서 저자 정보 조회")
    public Map<String, String> selectTxbAutrInfo() {
        return bcChatBotStuService.selectTxbAutrInfo();
    }
    
    @GetMapping("/dailyCnvSnro")
    @Operation(summary = "일상 대화 시나리오 조회", description = "일상 대화 시나리오 조회")
    public String selectDailyCnvSnro(String srhCn) {
        return bcChatBotStuService.selectDailyCnvSnro(srhCn);
    }
    
}


