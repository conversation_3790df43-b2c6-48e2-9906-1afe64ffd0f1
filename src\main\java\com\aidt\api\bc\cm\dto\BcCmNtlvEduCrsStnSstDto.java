package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "국가수준교육과정표준체계")
public class BcCmNtlvEduCrsStnSstDto {

	@Schema(description = "사용자ID (cm_usr.keris_usr_id")
	private String usrId;
	@Schema(description = "학습사용자ID (cm_usr.usr_id")
	private String lrnUsrId;
	@Schema(description = "교육과정콘텐츠2단계코드")
	private String crclCtnElm2Cd;
	@Schema(description = "교과서ID")
	private String txbId;
	@Schema(description = "운영교과서ID")
	private String optTxbId;
	@Schema(description = "학습시작여부")
	private String lrnStrYn;
	@Schema(description = "학습시작일시")
	private String lrnStrDtm;
	@Schema(description = "평가점수")
	private Long evScr;
	@Schema(description = "학습완료여부")
	private String lrnCmplYn;
	@Schema(description = "학습완료일시")
	private String lrnCmplDtm;
	@Schema(description = "교육과정성취기본코드")
	private String eduCrsAchBsCd;
	@Schema(description = "진행율")
	private String pgrsRt;

	private String devrLrmpId;
	private String crtrId;
	private String mdfrId;
	private String dbId;
	
	private boolean testYn;
}
