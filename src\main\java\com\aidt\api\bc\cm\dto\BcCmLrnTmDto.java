/**
 * 
 */
package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-26 17:59:47
 * @modify 2024-06-26 17:59:47
 * @desc CM_학습시간 시간대별 호출 저장  dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcCmLrnTmDto {
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="학습년도일시")
	private String lrnYrDtm;

	@Parameter(name="사용자ID")
    private String usrId;
	
	@Parameter(name="교과서학습시간초수")
	private Long txbLrnTmScnt;
	
	@Parameter(name="AI학습시간초수")
	private Long aiLrnTmScnt;
	
	@Parameter(name="특별학습시간초수")
	private Long spLrnTmScnt;
	
	@Parameter(name="평가학습시간초수")
	private Long evLrnTmScnt;
	
	@Parameter(name="AI평가학습시간초수")
	private Long aiEvLrnTmScnt;
	
	@Parameter(name="AI쓰기학습시간")
	private Long aiWrtngLrnTm;
	
	@Parameter(name="AI읽기학습시간")
	private Long aiRdngLrnTm;
	

	private String crtrId;
	private String crtDtm;
	private String mdfrId;
	private String mdfDtm;
	private String dbId;
}
