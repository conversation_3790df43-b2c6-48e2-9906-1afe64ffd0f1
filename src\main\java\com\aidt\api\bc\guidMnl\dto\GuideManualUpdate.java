package com.aidt.api.bc.guidMnl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GuideManualUpdate {
    @Parameter(name="목차")
    private List<Long> ids;

    @Parameter(name="수정자")
    private String mdfrId;
}
