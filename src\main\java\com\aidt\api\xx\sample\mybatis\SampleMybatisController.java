package com.aidt.api.xx.sample.mybatis;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto;
import com.aidt.api.xx.sample.mybatis.dto.SampleMybatisSaveDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample Mybatis Service
 */
@Slf4j
@Tag(name="[xx] Sample.Mybatis" , description="Sample Mybatis")
@RestController
@RequestMapping("/api/v1/al/sample/mybatis")
public class SampleMybatisController {

    @Autowired
    private SampleMybatisService mybatisService;

    /**
     * 마이바티스 조회 요청
     *
     * @param crudDto
     * @return ResponseList<CrudDto>
     */
    // 염병
    @Operation(summary="Mybatis 다건 조회", description = "Path Variable 방식 조회")
    @GetMapping(value = "/{userId}")
    public ResponseDto<List<SampleMybatisDto>> selectList(@PathVariable("userId") String userId) {
        log.debug("Entrance selectList");
        return Response.ok(mybatisService.selectList(userId));
    }

    /**
     * 마이바티스 등록 요청
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="Mybatis 다건 등록", description = "다건 등록")
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> insertList(@RequestBody List<SampleMybatisDto> mybatisDtoList) {
        // 등록 데이터 검증
        if (mybatisDtoList.isEmpty())
            throw new IllegalArgumentException("Invalid mybatisDtoList");
        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
            if (StringUtils.isEmpty(mybatisDto.getUsrId())) {
                throw new IllegalArgumentException("Invalid UserId in mybatisDtoList ");
            }
        }
        return Response.ok(mybatisService.insertList(mybatisDtoList));
    }

    /**
     * 마이바티스 수정 요청
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="Mybatis 수정 샘플", description = "다건 수정")
    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> update(@RequestBody List<SampleMybatisDto> mybatisDtoList) {
        // 수정 데이터 검증
        if (mybatisDtoList.isEmpty())
            throw new IllegalArgumentException("Invalid mybatisDtoList");
        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
            if (StringUtils.isEmpty(mybatisDto.getUsrId())) {
                throw new IllegalArgumentException("Invalid UserId in mybatisDtoList ");
            }
        }
        return Response.ok(mybatisService.updateList(mybatisDtoList));
    }

    /**
     * 마이바티스 삭제 요청
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="Mybatis 삭제 샘플", description = "다건 삭제")
    @DeleteMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> put(@RequestBody List<SampleMybatisDto> mybatisDtoList) {
        // 삭제 데이터 검증
        if (mybatisDtoList.isEmpty())
            throw new IllegalArgumentException("Invalid mybatisDtoList");
        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
            if (StringUtils.isEmpty(mybatisDto.getUsrId())) {
                throw new IllegalArgumentException("Invalid UserId in mybatisDtoList");
            }
        }
        return Response.ok(mybatisService.deleteList(mybatisDtoList));
    }

    /**
     * 마이바티스 저장 요청
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    // 씨발
    @Operation(summary="Mybatis 저장 샘플", description = "CUD List 데이터 처리")
    @PostMapping(value = "/save", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> save(@RequestBody SampleMybatisSaveDto mybatisSaveDto) {
        return Response.ok(mybatisService.save(mybatisSaveDto));
    }

    /**
     * 마이바티스 대량 등록 방식
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="Mybatis 대량 등록", description = "Bulk Insert 방식 대량 등록")
    @PostMapping(value = "/bulkinsert", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> bulkInsert(@RequestBody List<SampleMybatisDto> mybatisDtoList) {
        // 대량 등록 데이터 검증
        if (mybatisDtoList.isEmpty())
            throw new IllegalArgumentException("Invalid mybatisDtoList");
        for (SampleMybatisDto mybatisDto : mybatisDtoList) {
            if (StringUtils.isEmpty(mybatisDto.getUsrId())) {
                throw new IllegalArgumentException("Invalid UserId in mybatisDtoList");
            }
        }
        return Response.ok(mybatisService.bulkInsert(mybatisDtoList));
    }
}
