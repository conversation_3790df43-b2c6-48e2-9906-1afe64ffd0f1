package com.aidt.api.ea.grpblbd.tcr;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.grpblbd.tcr.dto.EaGrpBlbdTcrDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

//@Slf4j
@Tag(name="[ea] 모둠 게시판 - 교사", description="모둠 게시판 - 교사")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/grpblbd")
public class EaGrpBlbdTcrController {

	@Autowired
	private EaGrpBlbdTcrService eaGrpBlbdTcrService;

    /**
     * 모둠 게시판 List 조회
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 List 조회 (교사)", description="모둠 게시판 List 조회 (교사)")
    @PostMapping(value="/selectGrpBlbdList")
    public ResponseDto<Map<String, Object>> selectGrpBlbdList(@RequestBody EaGrpBlbdTcrDto eaGrpBlbdTcrDto) {
    	// 모둠 게시판 List 조회
    	Map<String, Object> result = eaGrpBlbdTcrService.selectGrpBlbdList(eaGrpBlbdTcrDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 상세, 댓글 조회
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 상세, 댓글 조회 (교사)", description="모둠 게시판 상세, 댓글 조회 (교사)")
    @PostMapping(value="/selectBlbdDetail")
    public ResponseDto<Map<String, Object>> selectBlbdDetail(@RequestBody EaGrpBlbdTcrDto eaGrpBlbdTcrDto) {
    	// 모둠 게시판 상세 조회
    	Map<String, Object> result = eaGrpBlbdTcrService.selectBlbdDetail(eaGrpBlbdTcrDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 글 등록, 수정, 삭제
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 글 등록, 수정, 삭제 (교사)", description="모둠 게시판 글 등록, 수정, 삭제 (교사)")
    @PostMapping(value="/saveBlbd")
    public ResponseDto<Map<String, Object>> saveBlbd(@RequestBody EaGrpBlbdTcrDto eaGrpBlbdTcrDto) {
    	// 모둠 게시판 글쓰기, 글수정
    	Map<String, Object> result = eaGrpBlbdTcrService.saveBlbd(eaGrpBlbdTcrDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 댓글 등록, 수정, 삭제
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 댓글 등록, 수정, 삭제 (교사)", description="모둠 게시판 댓글 등록, 수정, 삭제 (교사)")
    @PostMapping(value="/saveComment")
    public ResponseDto<Map<String, Object>> saveComment(@RequestBody EaGrpBlbdTcrDto eaGrpBlbdTcrDto) {
    	// 모둠 게시판 댓글 등록, 수정, 삭제
    	Map<String, Object> result = eaGrpBlbdTcrService.saveComment(eaGrpBlbdTcrDto);

    	return Response.ok(result);
    }

    /**
     * 모둠 게시판 답글 등록, 수정, 삭제
     * @param eaGrpBlbdStuDto
     * @return ResponseDto<Map<String, Object>>
     */
    @Operation(summary="모둠 게시판 답글 등록, 수정, 삭제 (교사)", description="모둠 게시판 답글 등록, 수정, 삭제 (교사)")
    @PostMapping(value="/saveReply")
    public ResponseDto<Map<String, Object>> saveReply(@RequestBody EaGrpBlbdTcrDto eaGrpBlbdTcrDto) {
    	// 모둠 게시판 답글 등록, 수정, 삭제
    	Map<String, Object> result = eaGrpBlbdTcrService.saveReply(eaGrpBlbdTcrDto);

    	return Response.ok(result);
    }
}
