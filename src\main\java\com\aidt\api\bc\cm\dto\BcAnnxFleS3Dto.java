package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-04-01 17:59:47
 * @modify 2024-04-01 17:59:47
 * @desc 첨부파일 삭제 dto
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcAnnxFleS3Dto {

	@Parameter(name="업무명")
	private String taskName;

	@Parameter(name="파일명")
	private String fileName;

	@Parameter(name="첨부ID")
	private Long annxId;

	@Parameter(name="첨부파일ID")
	private Long annxFleId;

	@Parameter(name="원본파일명")
	private String originFileName;

	@Parameter(name="다운로드파일명")
	private String fnAnnxFleOrglNm;


}
