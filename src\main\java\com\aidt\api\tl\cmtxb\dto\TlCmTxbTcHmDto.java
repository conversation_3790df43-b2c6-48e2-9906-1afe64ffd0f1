package com.aidt.api.tl.cmtxb.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-10-08 10:13:33
 * @modify date 2024-10-08 10:13:33
 * @desc [TlCmTxbTcHmDto 교과학습 대단원, 차시 조회 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbTcHmDto {

    /** 차시ID*/
    @Parameter(name="차시ID")
    private String lrmpNodId;

    /** 차시명*/
    @Parameter(name="차시명")
    private String lrmpNodNm;

    /** task명 (Activity Step)*/
    @Parameter(name="task명 (Activity Step)")
    private String stpNm;

    /** task명 (Activity Step)*/
    @Parameter(name="taskId (Activity Step)")
    private String stpId;

    /** 액티비티Id*/
    @Parameter(name="액티비티Id")
    private String atvId;

    /** 대단원명 */
    @Parameter(name="대단원명")
    private String lluNodNm;

    /** 대단원 번호*/
    @Parameter(name="대단원 번호")
    private String lluNo;

    /** 대단원 번호 사용 여부*/
    @Parameter(name="대단원 번호 사용 여부")
    private String lluNoUseYn;

    /** 차시 번호*/
    @Parameter(name="차시 번호")
    private String lrmpNodNo;

    /** 차시 번호 사용 여부*/
    @Parameter(name="차시 번호 사용 여부")
    private String lrmpNodNoUseYn;

    /** 썸네일 PC PATH*/
    @Parameter(name="썸네일 PC PATH")
    private String pcThbPthNm;

    /** 썸네일 TA PATH*/
    @Parameter(name="썸네일 TA PATH")
    private String tbThbPthNm;

    @Parameter(name="지난차시여부")
    private String lastTcYn;

}
