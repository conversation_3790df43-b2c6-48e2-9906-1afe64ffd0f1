package com.aidt.api.tl.cmstnsst.stu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.tl.cmstnsst.dto.TlCmEduCrsCnDto;
import com.aidt.api.tl.cmstnsst.dto.TlCmLluStnSttSrhDto;
import com.aidt.api.tl.cmstnsst.dto.TlCmStnSttSrhDto;
import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-19 13:05:28
 * @modify date 2024-06-19 13:05:28
 * @desc TlCmStnStt 교과학습 교육과정표준관련처리API(학생)
 */
@Service
public class TlCmStnSttStuService {
    private final String MAPPER_NAMESPACE = "api.tl.cmstnsst.stu.";

    @Autowired
    private CommonDao commonDao;
    /** 
     * 교과학습 교육과정표준체계ID목록 조회
     * @return List<TlCmEduCrsCnDto> 교육과정표준체계ID목록
     */
    public List<TlCmEduCrsCnDto> selectTxbEduCrsCnCdList(TlCmStnSttSrhDto srhDto) {
        List<String> lrnStpDvList = new ArrayList<String>();
        lrnStpDvList = this.getLrnStpDvList(srhDto.getLrnStpDvClYn(), srhDto.getLrnStpDvWbYn(), srhDto.getLrnStpDvExYn());
        List<TlCmEduCrsCnDto> rtnlist = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbEduCrsCnCdList", Map.of("param", srhDto, "stpDvList", lrnStpDvList));
        return rtnlist;
    }
    /** 
     * 교과학습 대단원-교육과정표준체계ID목록 조회
     * @return List<String> 교육과정표준체계ID목록
     */
    public List<String> selectTxbLluEduCrsCnCdList(TlCmLluStnSttSrhDto srhDto) {
        List<String> lrnStpDvList = new ArrayList<String>();
        lrnStpDvList = this.getLrnStpDvList(srhDto.getLrnStpDvClYn(), srhDto.getLrnStpDvWbYn(), srhDto.getLrnStpDvExYn());
        List<String> listMap = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbLluEduCrsCnCdList", Map.of("param", srhDto, "stpDvList", lrnStpDvList));
        // List<String> rtnList = new ArrayList<String>();
        // for(int ii=0; ii < listMap.size(); ii++) {
        //     String cd = listMap.get(ii).get("EDU_CRS_CN_CD");
        //     rtnList.add(cd);
        // }
        return listMap;
    }
    /**
     * 학습단계코드 구분
     * @param lrnStpDvClYn
     * @param lrnStpDvWbYn
     * @param lrnStpDvExYn
     * @return
     */
    private List<String> getLrnStpDvList(String lrnStpDvClYn, String lrnStpDvWbYn, String lrnStpDvExYn) {
        List<String> list = new ArrayList<String>();
        if (lrnStpDvClYn == null || !"N".equals(lrnStpDvClYn)) {
            list.add(TlConstUtil.LRN_STP_DV_CL);
        }
        if (lrnStpDvWbYn== null || !"N".equals(lrnStpDvWbYn)) {
            list.add(TlConstUtil.LRN_STP_DV_WB);
        }
        if (lrnStpDvExYn == null || !"N".equals(lrnStpDvExYn)) {
            list.add(TlConstUtil.LRN_STP_DV_EX);
        }
        return list;
    }
}
