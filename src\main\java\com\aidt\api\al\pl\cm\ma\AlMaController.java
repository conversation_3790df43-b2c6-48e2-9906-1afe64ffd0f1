package com.aidt.api.al.pl.cm.ma;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto;
import com.aidt.api.al.pl.dto.AlTpcMpnDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-20 
 * @modify date 2024-02-20 
 * @desc AI맞춤학습 단원차시조회
 */
//@Slf4j
@Tag(name="[al] AI맞춤학습 단원차시조회", description="AI맞춤학습 단원차시조회 조회")
@RestController
@RequestMapping("/api/v1/al/cm/ma")
public class AlMaController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private AlMaService maService;
	
	@Autowired 
	private AiRcmTsshQtmCommService commService;
	
    /**
     * 학생 중단원목록 조회 controller
     *
     * @param 
     * @return ResponseDto<List<AlMluTcStuDto>>
     */
	@Tag(name="[al]중단원목록 조회", description="중단원 목록 조회 컨트롤러")
    @PostMapping(value = "/selectMluLstInq")
    public ResponseDto<Map<String, Object>> selectMluLstInq(@Valid @RequestBody AlMluTcLstInqStuReqDto reqDto, @RequestParam(required = false) String saveYN) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(reqDto.getUsrId() == null) {
    		reqDto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(reqDto.getOptTxbId() == null) {
    		reqDto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	return Response.ok(maService.selectMluLstStuInq(reqDto, saveYN));
    }
    
	/**
	 * AI 맞춤 개념영상 리스트 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 수학 개념영상 리스트 조회", description="AI 맞춤 개념영상 리스트 조회")
    @PostMapping(value = "/selectCcptVdList")
    public ResponseDto<List<AiRcmTsshQtmDto>> selectCcptVdList(@Valid @RequestBody AiRcmTsshQtmDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        dto.setSbjCd("MA");
        return Response.ok(commService.selectCcptVdList(dto));
    }
    
    /**
	 * AI 개념맵 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 개념맵 조회", description="AI 개념맵 조회")
    @PostMapping(value = "/selectTpcMpnList")
    public ResponseDto<Map<String, Object>> selectTpcMpnList(@Valid @RequestBody AlTpcMpnDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectTpcMpnList(dto));
    }
	
	 /**
	 * AI 개념맵 조회 - 진단평가
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 진단평가 개념맵 조회", description = "AI 진단평가에 한해 다시 계산하여 개념맵을 조회")
    @PostMapping(value = "/selectTpcMpnListOnlyOv")
    public ResponseDto<Map<String, Object>> selectTpcMpnListOnlyOv(@Valid @RequestBody AlTpcMpnDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectTpcMpnListOnlyOv(dto));
    }
	
	/**
	 * AI 수학 오답갯수조회
	 * 
	 * @return 
	 * */
	//selectIansQtmCnt
	@Tag(name="[al]AI 수학 오답갯수& 단원평가여부 조회", description = "AI 수학 오답갯수 & 단원평가 여부조회")
    @PostMapping(value = "/selectIansQtmCnt")
    public ResponseDto<Map<String, Object>> selectIansQtmCnt(@Valid @RequestBody AlMluTcLstInqStuReqDto dto, @RequestParam(required = false) String saveYN) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectIansQtmSeUgCnt(dto, saveYN));
    }
	
	/**
     * 수학 답변리스트 조회
     *
     * @param 
     * @return ResponseDto<Map<String,Object>>
     */
	@Tag(name = "[al]수학 답변리스트 조회", description = "수학 답변리스트 조회")
    @PostMapping(value = "/selectAlMaEvQtmAnwList")
    public ResponseDto<Map<String,Object>> selectAlPlEvQtmAnwList(@Valid @RequestBody AlMluTcLstInqStuReqDto dto) {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	if(dto.getUsrId() == null) {
    		dto.setUsrId(securityUserDetailDto.getUsrId());
        }
    	if(dto.getOptTxbId() == null) {
    		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}
    	
    	if(dto.getEvIds() == null || dto.getEvIds().length == 0) {
			return Response.fail("평가 정보가 없습니다.");
		}
    	return Response.ok(maService.selectAlMaEvQtmAnwList(dto));
    }
	
	/**
	 * AI 학생별 토픽 숙련도 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 학습현황관리 학생별 토픽 숙련도 조회", description="AI 학생별 토픽 숙련도 조회")
    @PostMapping(value = "/selectTpcMpnRptList")
    public ResponseDto<List<Map<String, Object>>> selectTpcMpnRptList(@Valid @RequestBody AlTpcMpnDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectTpcMpnRptList(dto));
    }
	
	/**
	 * AI 학생별 토픽 숙련도 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al] AI 학습현황관리 중단원 목록만 조회", description="AI 학습현황관리 중단원 목록만 조회")
    @PostMapping(value = "/selectOnlyMluList")
    public ResponseDto<List<Map<String, Object>>> selectOnlyMluList(@Valid @RequestBody AlMluTcLstInqStuReqDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectOnlyMluList(dto));
    }
	
	
	/**
	 * 단원 평가 여부 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al]단원평가여부 조회", description = "단원평가 여부조회")
    @PostMapping(value = "/selectEvSeUg")
    public ResponseDto<AlMluTcLstInqStuResponseDto> selectEvSeUg(@Valid @RequestBody AlMluTcLstInqStuReqDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectEvSeUg(dto));
    }
	
	/**
	 * 맞춤학습 평가아이디 조회
	 * 
	 * @return 
	 * */
	@Tag(name="[al]맞춤학습 평가아이디 조회", description = "맞춤학습 평가아이디 조회")
    @PostMapping(value = "/selectAlMaEvIdList")
    public ResponseDto<String[]> selectAlMaEvIdList(@Valid @RequestBody AlMluTcLstInqStuReqDto dto) {
		//세션정보
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		if(dto.getOptTxbId() == null) {
			dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
		}
        if(dto.getUsrId() == null) {
        	dto.setUsrId(securityUserDetailDto.getUsrId());
        }
		return Response.ok(maService.selectAlMaEvIdList(dto));
    }
}
