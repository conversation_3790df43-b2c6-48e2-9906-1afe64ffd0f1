package com.aidt.api.at.err.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "CM_FRONT_오류_로그")
public class FrontErrLogDto {

	@Schema(description = "접속구분코드 (초등[EL] / 중고등[MH]")
	private String connDvCd;

	@Schema(description = "FRONT접속URL")
	private String frontConnUrl;

	@Schema(description = "BODY내용 (parameter)")
	private String bodyCn;

	@Schema(description = "회원ID")
	private String usrId;

	@Schema(description = "운영교과서ID")
	private String optTxbId;

	@Schema(description = "오류메시지")
	private String errMsg;

	@Schema(description = "SYSTEM코드")
	private String systemCd;
}
