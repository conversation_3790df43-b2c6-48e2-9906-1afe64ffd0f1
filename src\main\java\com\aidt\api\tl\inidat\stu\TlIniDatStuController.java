package com.aidt.api.tl.inidat.stu;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.inidat.dto.TlIniDatCondDto;
import com.aidt.api.tl.inidat.tcr.TlIniDatTcrService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 15:53:04
 * @modify date 2024-03-05 15:53:04
 * @desc TlIniDatStu  교과학습 초기데이터작성 Service
 */
@Slf4j
@Tag(name="[tl] 교과학습초기데이터작성[TlSbcLrnStu]", description="로그인한 학생 초기데이터를 작성한다.")
@RestController
@RequestMapping("/api/v1/tl/stu/inidat")
public class TlIniDatStuController {
	@Autowired
    private JwtProvider jwtProvider;
	
    @Autowired
    private TlIniDatTcrService tlIniDatTcrService; //교사용 서비스
	
	/**
     * 교과학습 초기데이터생성 처리
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교과학습 재구성", description="해당 교과서의 재구성초기데이터를 생성한다.")
    @PostMapping(value = "/registIniDat")
    public ResponseDto<Integer> registIniDat() {
        log.debug("Entrance registIniDat");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        TlIniDatCondDto srhDto = new TlIniDatCondDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setTcrUsrId(userDetails.getUsrId());

        return Response.ok(tlIniDatTcrService.registIniDat(srhDto));
    }

}
