package com.aidt.api.tl.chlg.tcr;

import com.aidt.api.tl.chlg.dto.TlLrnChlgDto;
import com.aidt.common.CommonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-01-21
 * @modify date 2025-01-21
 * @desc TlLrnChlgStu Service 교과학습 챌린지 조회 서비스
 */
@Service
public class TlLrnChlgTcrService {
    private final String MAPPER_NAMESPACE = "api.tl.chlg.tcr.";
    @Autowired
    private CommonDao commonDao;

    /**
     * 챌린지목록 조회 서비스
     *
     * @param optTxbId 운영교과서ID
     * @param lrnUsrId 교사ID
     * @return List<TlLrnChlgDto>
     */
    public List<TlLrnChlgDto> selectLrnChlgList(String optTxbId, String claId, LocalDate targetDate) {
        LocalDate startDate = targetDate.with(DayOfWeek.MONDAY);
        LocalDate endDate = targetDate.with(DayOfWeek.SUNDAY);
        List<TlLrnChlgDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectChlgListTcr",
                Map.of("optTxbId", optTxbId, "claId", claId, "startDate", startDate, "endDate", endDate));
        return list;
    }

}
