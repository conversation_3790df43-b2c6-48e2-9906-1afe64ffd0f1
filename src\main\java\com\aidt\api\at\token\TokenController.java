package com.aidt.api.at.token;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.aidt.base.exception.CustomException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.appevent.service.AdminStatKafkaService;
import com.aidt.api.at.dto.User;
import com.aidt.api.at.token.dto.Jwt;
import com.aidt.api.at.token.dto.Jwt.JwtRefreshDto;
import com.aidt.api.at.user.UserDetailService;
import com.aidt.base.BaseUtil;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;
import com.aidt.common.util.MessageUtils;
import com.fasterxml.jackson.core.type.TypeReference;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-11-30 10:40:01
 * @modify date 2023-11-30 10:40:01
 * @desc Token Controller
 */
@Tag(name="[at] Token", description="인증 토큰")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token")
public class TokenController {
	
    private final JwtProvider jwtProvider;
    
    private final MessageUtils msgUtil;
    
    private final TokenService tokenService;

    private final UserDetailService userDetailService;

    private final AdminStatKafkaService adminStatKafkaService;
    
    //토큰 발급
    @Operation(summary="토큰 발급", description="사용자 인증 토큰 발급")
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Jwt.JwtResponseDto> createToken(HttpServletRequest req, @Valid @RequestBody Jwt.JwtRequestDto requestDto) {
    	
    	// 사용자정보
    	Jwt.JwtUserDto userDto = tokenService.selectUser(requestDto);
    	log.debug(">> userDto:{}",userDto);
    	if(userDto==null) {
    		//throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,msgUtil.get("api.notfound.user"));
    		throw new RuntimeException(msgUtil.get("api.notfound.user"));
    	}

        // refreshToken
    	String refreshToken = jwtProvider.createRefreshToken(UUID.randomUUID().toString().replace("-", ""));
    	JwtRefreshDto refreshDto = Jwt.JwtRefreshDto.builder().usrId(userDto.getUsrId()).refreshToken(refreshToken).kerisUsrId(userDto.getKerisUsrId()).build();    	
    	
    	/*
    	 * 2025.03.18 중복로그인 시 refresh token 덮어쓰는 이슈로 인해 table list로 관리
    	 */
    	//int cnt = tokenService.updateRefreshToken(refreshDto);
    	int cnt = tokenService.insertUsrRefreshToken(refreshDto);
    	
    	if (cnt == 0) {
    		//throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,msgUtil.get("api.fail.save.token")); // 토큰저장실패
    		throw new RuntimeException(msgUtil.get("api.fail.save.token"));
    	}

    	// 사용자정보 DB조회
    	User.UserResponseDto userDetail = userDetailService.selectUserDetail(userDto.getUsrId());
    	Map<String,Object> userData = BaseUtil.getObjectMapperUtil().convert(userDetail, new TypeReference<Map<String,Object>>() {});
    	
        // accessToken
    	String accessToken = jwtProvider.createAccessToken(userDto.getUsrId(), userData, userDto.getRoles()); 
        Jwt.JwtResponseDto jwtResponseDto = Jwt.JwtResponseDto.builder().accessToken(accessToken).refreshToken(refreshToken).build();

        /**
         * 관리자 login event 통계 set
         */
        adminStatKafkaService.loginEvent(userDetail);
        
        return Response.ok(jwtResponseDto);
    }

    //Interface 토큰발급
    //@Hidden
    @PostMapping(value = "/if", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Jwt.JwtAccessTokenDto> createInterfaceToken(HttpServletRequest req, @Valid @RequestBody Jwt.JwtRequestDto jwtRequestDto) {

    	//Jwt.JwtUserDto userDto = tokenService.selectUser(jwtRequestDto);    	
    	//if(userDto==null) {
    	//	throw new CustomException(ExceptionMessage.INTERNAL_SERVER_ERROR,msgUtil.get("api.notfound.user"));
    	//}

        String accessToken = jwtProvider.createInterfaceAccessToken();
        Jwt.JwtAccessTokenDto jwtAccessTokenDto = Jwt.JwtAccessTokenDto.builder().accessToken(accessToken).build();
        log.debug(">> Interface AccessToken: {}", accessToken);

        return Response.ok(jwtAccessTokenDto);
    }

    //RefreshToken 회수
    //@Hidden
    @DeleteMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> RemoveToken(HttpServletRequest req) {
        String refreshToken = jwtProvider.resolveToken(req, ConstantsExt.refreshTokenHeader);

        if (Optional.ofNullable(refreshToken).isEmpty()) {
            return Response.fail("Invalid Refresh Token");
        }
        
        // Token 회수
        int cnt = tokenService.deleteRefreshToken(refreshToken);
        /*
        if (cnt == 0) {
            // Cnt가 0인 상태는 Refresh_Token이 없거나 다른 경우이다.
            // 1. 2중으로 Token을 발행한 경우( 후자의 Device에 할당된 RefreshToken은 유효, 이전 Token은 덮어 쓰기된 상태)
            // 2. 2중으로 로그인한 상테에서 누군가 먼저 로그아웃한 상태
            // Exception 처리 필요.
            log.debug(">> 중복 발행 Or 화수 완료");
        } else {
            // 정상 회수
            log.debug(">> 회수 완료");
        }
        */

        return Response.ok();
    }

    //만료 토큰 갱신(RefreshToken이 유효한 상테만)
    //@Hidden
    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Jwt.JwtAccessTokenDto> refreshToken(HttpServletRequest req) {
    	
        String refreshToken = jwtProvider.resolveToken(req, ConstantsExt.refreshTokenHeader);
//        jwtProvider.tokenValidate(refreshToken, ConstantsExt.refreshTokenHeader);
        
        // 토큰 갱신
        Jwt.JwtRefreshDto jwtRefreshDto = Jwt.JwtRefreshDto.builder().refreshToken(refreshToken).build();
        
        /*
         * 2025.03.18 중복로그인 대응 신규 테이블에서 get
         */
        //Jwt.JwtUserDto userDto = tokenService.selectRefreshToken(jwtRefreshDto);
        Jwt.JwtUserDto userDto = tokenService.selectUsrRefreshToken(jwtRefreshDto);
    	if(userDto==null || StringUtils.isEmpty(userDto.getUsrId())) {
    		throw new RuntimeException("Invalid refresh token");
    	}

        try {

            //jwtProvider.tokenValidate(refreshToken, "RefreshToken");

            // 사용자정보 DB조회
            User.UserResponseDto userDetail = userDetailService.selectUserDetail(userDto.getUsrId());
            Map<String,Object> userData = BaseUtil.getObjectMapperUtil().convert(userDetail, new TypeReference<Map<String,Object>>() {});

            // accessToken
            String accessToken = jwtProvider.createAccessToken(userDto.getUsrId(), userData, userDto.getRoles());
            Jwt.JwtAccessTokenDto jwtAccessTokenDto = Jwt.JwtAccessTokenDto.builder().accessToken(accessToken).build();

            return Response.ok(jwtAccessTokenDto);

        } catch (CustomException e) {
            log.debug("Invalid Refresh Token: {}", e.getError().getMessage());
            throw new AccessDeniedException(e.getMessage());
        }

    }
}
