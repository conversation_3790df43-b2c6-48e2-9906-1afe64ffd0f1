package com.aidt.api.tl.cmtxb.dto;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 10:43:39
 * @modify date 2024-04-02 10:43:39
 * @desc [학습상세현황 학습활동 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlCmTxbLrnDtlAtvDto {
       /** 학습활동ID */
       @Parameter(name="학습활동ID")
       private String lrnAtvId;

       /** 학습활동명 */
       @Parameter(name="학습활동명")
       private String lrnAtvNm;

       /** 사용여부 */
       @Parameter(name="사용여부")
       private String useYn;

       /** 학습상태 */
       @Parameter(name="학습상태")
       private String lrnStCd;
       
       /** 콘텐츠타입 */
       @Parameter(name="콘텐츠타입")
       private String ctnTpCd;

       /** 재구성순서 */
       @Parameter(name="재구성순서")
       private int rcstnOrdn;

       /** 생성일시 */
       @Parameter(name="생성일시")
       private Timestamp crtDtm;

       /** 수정일시 */
       @Parameter(name="수정일시")
       private Timestamp mdfDtm;

       /** 학습이력*/
       @Parameter(name="학습이력")
       private String lrnHs;

       /** 학습결과물*/
       @Parameter(name="학습결과물")
       private String lrnRs;
}