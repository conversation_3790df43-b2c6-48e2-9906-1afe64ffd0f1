package com.aidt.api.ea.lrnmg.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrEaDetailResDto {
	
	@Parameter(name = "운영교과서ID")
	private String optTxbId;
	
	@Parameter(name = "사용자ID")
	private String usrId;
	
	@Parameter(name = "과제 id")
	private String asnId;
	
	@Parameter(name = "총 조회 개수")
	private String totalCnt;
	
	@Parameter(name = "교사 사용자id")
	private String tcrUsrId;
	
	@Parameter(name = "과제명")
	private String asnNm;
	
	@Parameter(name = "과제설명")
	private String asnCn;
	
	@Parameter(name = "과제유형코드")
	private String asnTpCd;
	
	@Parameter(name = "학습유형코드")
	private String lrnTpCd;
	
	@Parameter(name = "유형명")
	private String asnLrnTpNm;
	
	@Parameter(name = "과제기간구분코드")
	private String asnPtmeDvCd;
	
	@Parameter(name = "과제기간")
	private String asnPtmeNm;
	
	@Parameter(name = "완료기간")
	private String smtDtm;
}
