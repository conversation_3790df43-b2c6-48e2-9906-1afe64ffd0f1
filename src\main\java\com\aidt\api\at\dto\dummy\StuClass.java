package com.aidt.api.at.dto.dummy;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StuClass {
	private  List<StuDto> students;
	private String authCode;
	private String txbId;
	private String lectureCode;
	private String classCode ;
	private String schoolId ;
	private StuDto prof;
	
	public StuClass(String authCode, String txbId) {
			if(authCode.length()>32) {
				  throw new IllegalArgumentException("authCode는 32자 이하여야 합니다. 입력된 길이: " + authCode.length());
	        }
		    this.students = new ArrayList<StuDto>();
		    for(int i =0;i<10;i++) {
		    	students.add(new StuDto(authCode, txbId, generateRandomName(), i*1L));
		    }
	        this.lectureCode=authCode + txbId + "_lecture";
	        this.classCode=authCode + txbId + "_classcode";
	        this.schoolId=authCode.substring(0,10);
	        this.prof= new StuDto(authCode,txbId,generateRandomName());
	        
	        this.authCode=authCode;
	        this.txbId=txbId;
	}
	
	 private  String generateRandomName() {
	        // 성씨 목록
	        String[] surnames = {"이", "김", "노", "장", "조", "박"};
	        
	        // 이름 목록
	        String[] givenNames = {
	            "성수", "진희", "성진", "수희", "강희", 
	            "장석", "기석", "호석", "상기", 
	            "원희", "현희", "승범", "승현","종현",
	            "지연","광희","재석"
	        };

	        Random random = new Random();
	        
	        // 랜덤 성씨와 이름 선택
	        String surname = surnames[random.nextInt(surnames.length)];
	        String givenName = givenNames[random.nextInt(givenNames.length)];
	        
	        return surname + givenName;
	    }

}
