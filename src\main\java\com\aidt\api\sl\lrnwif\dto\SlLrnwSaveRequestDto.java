package com.aidt.api.sl.lrnwif.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-07 14:13:26
 * @modify : date 2024-03-07 14:13:26
 * @desc : SlLrnwTocAtvDto 특별학습 콘텐츠 활동 조회 
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwSaveRequestDto {

	@Parameter(name="특별학습ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnId;

	@Parameter(name="특별학습 노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnNodId;
	
	@Parameter(name="특별학습 콘텐츠ID", required = true)
	@NotBlank(message = "{field.required}")
	private String spLrnCtnId;

	@Parameter(name="학습시간", required = true)
	@NotBlank(message = "{field.required}")
	private int lrnTmScnt;
	
	@Parameter(name="학습상태코드", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnStCd;
}
