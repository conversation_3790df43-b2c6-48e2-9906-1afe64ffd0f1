package com.aidt.api.al.pl.cm.rcm.event;

import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationCode;
import com.aidt.api.al.pl.cm.rcm.factory.ProfileFactory;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.at.err.BackErrLogService;
import com.aidt.api.at.err.dto.BackErrLogDto;
import com.aidt.api.common.helper.PointHelper;
import com.aidt.api.error.ApiErrorLogException;
import com.aidt.api.error.ErrorCode;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@AllArgsConstructor
public class AiQuestionTopicProfileBuildEventHandler {

	private final PointHelper pointHelper;
	private final AiRcmTsshQtmCommService aiRcmTsshQtmCommService;
	private final ProfileFactory profileFactory;
	private final BackErrLogService backErrLogService;

	@Async
	@TransactionalEventListener
	public void saveAiQuestionAnalysisStatistics(AiQuestionTopicProfileBuildEvent event) {
		var eaAiEv = event.getEaAiEvLoader().getEaAiEv();
		//DIY평가는 통계에서 제외
		if (EvaluationCode.DE.equals(eaAiEv.getEvaluationCode())) {
			return;
		}
		//todo:
		/**
		 * todo: AI 문항별 풀이 통계 리팩토링 계획
		 * 1. LMS_LRM.AI_QTMLY_XPL_STAS 쌓고는 있지만 통계 데이터를 사용하는 곳이 없다... 필요성 논의 필요
		 * 2. AI 통계 용 서비스 및 어댑터, 쿼리 분리
		 * 3. 이벤트에서 받은 AI 예측 프로파일 목록으로 데이터 저장
		 */
		try {
			var temp = event.getEaAiEvQuestionAnswers().stream()
				.map(x -> AiRcmTsshQtmDto.builder()
					.evId(eaAiEv.getEvId())
					.evDvCd(eaAiEv.getEvDvCd())
					.usrId(eaAiEv.getUsrId())
					.lrnrVelTpCd(x.getLrnrVelTpCd())
					.optTxbId(eaAiEv.getOptTxbId())
					.lluKmmpNodId(x.getMluKmmpNodId().toString())
					.mluKmmpNodId(x.getMluKmmpNodId().toString())
					.tcKmmpNodId(x.getTcKmmpNodId().toString())
					.tpcKmmpNodId(x.getTpcKmmpNodId().toString())
					.qtmId(x.getQtmId().intValue())
					.cansYn(x.getCansYn())
					.ctnDffdDvCd(x.getCtnDffdDvCd())
					.build())
				.collect(Collectors.toList());

			aiRcmTsshQtmCommService.insertAiQtmlyXplStas(temp);
		} catch (Exception e) {
			log.error("AI 문항별 풀이 통계 저장에 실패하였습니다.");
			errorLogging(event, new ApiErrorLogException(ErrorCode.AI_QTMLY_XPL_STAS, e, ErrorCode.TYPE_INTERNAL));
		}
	}

	@Async
	@TransactionalEventListener
	public void savePoint(AiQuestionTopicProfileBuildEvent event) {
		var eaAiEvLoader = event.getEaAiEvLoader();
		var evId = eaAiEvLoader.getEvId();
		var user = event.getUserDetail();
		try {
			var profileStrategy = profileFactory.getProfileStrategy(eaAiEvLoader.getSubjectCode());
			var pointCode = profileStrategy.getPointCode(eaAiEvLoader);
			if (StringUtils.isBlank(pointCode)) {
				log.warn("AI 포인트 적립할 코드를 찾을 수 없습니다.");
				return;
			}
			pointHelper.saveAsync(pointCode, evId, event.getAccessToken());
		} catch (Exception e) {
			log.error("AI 포인트 적립 실패:: evId={}, usrId={}", evId, user.getUsrId(), e);
			errorLogging(event, new ApiErrorLogException(ErrorCode.MY_HOME, e, ErrorCode.TYPE_EXTERNAL));
		}
	}

	@Async
	@TransactionalEventListener
	public void notifyAssignmentCompletion(AiQuestionTopicProfileBuildEvent event) {
		var eaAiEv = event.getEaAiEvLoader().getEaAiEv();
		var user = event.getUserDetail();

		/**
		 * todo: 추후 과제 제출 리팩토링 계획 
		 * 1. 단원 전체 완료 조회를 과제별로 전략화 
		 * 2. 과제 완료 처리 로직 분리
		 * 3. 과제 아이디 조회 리팩토링 (= 단원 전체 완료 조건 통일)
		 * 4. 과제 완료 노티피케이션 구현
		 */
		try {
			aiRcmTsshQtmCommService.updateAsnCmpl(AlPlQtmTpcProfDto.builder()
				.usrId(user.getUsrId())
				.mluKmmpNodId(eaAiEv.getMluKmmpNodId().toString())
				.optTxbId(eaAiEv.getOptTxbId())
				.sbjCd(event.getEaAiEvLoader().getTextbook().getSbjCd())
				.build(), user);
		} catch (Exception e) {
			log.error("과제 완료 처리 및 알림 전송에 실패하였습니다.");
			errorLogging(event, new ApiErrorLogException(ErrorCode.ASN_CMPL, e, ErrorCode.TYPE_INTERNAL));
		}

	}

	private void errorLogging(AiQuestionTopicProfileBuildEvent event, ApiErrorLogException exception) {
		var user = event.getUserDetail();
		var errClsNm = Optional.ofNullable(exception.getCause())
			.map(Throwable::getStackTrace)
			.filter(stack -> stack.length > 0)
			.map(stack -> stack[0].getMethodName())
			.orElseGet(() -> {
				StackTraceElement[] stack = exception.getStackTrace();
				return (stack.length > 0) ? stack[0].getMethodName() : "UNKNOWN";
			});

		var causeMsg = Optional.ofNullable(exception.getCause())
			.map(Throwable::getMessage)
			.orElse(exception.getMessage());

		if (causeMsg.length() > 1000) {
			causeMsg = causeMsg.substring(0, 1000);
		}

		var errorCode = exception.getErrorCode();

		backErrLogService.insertBackErrLog(BackErrLogDto.of(
			null,
			errClsNm,
			causeMsg,
			errorCode.getCode(),
			exception.getErrSrcCd(),
			null,
			user.getUsrId(),
			user.getOptTxbId(),
			exception.getStartTime()
		));

	}
}
