package com.aidt.api.bc.tnte.dto;

import com.aidt.common.Paging.PagingRequestDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 노트
 */

@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcNewTnteLrnAtvDto extends PagingRequestDto{
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="단원노드ID")
	private String luNodId;

	@Parameter(name="차시노드ID")
	private String tcNodId;

	@Parameter(name="학습사용자ID")
	private String lrnUsrId;

	@Parameter(name="노트ID")
	private String tnteId;
	
	@Parameter(name="학습활동ID")
	private String lrnAtvId;

	@Parameter(name="학습활동명")
	private String lrnAtvNm;

	@Parameter(name="CDN경로명")
	private String cdnPthNm;
	
	@Parameter(name="노트CDN경로명")
	private String tnteCdnPthNm;
	
	@Parameter(name="학습단계구분코드")
	private String lrnStpDvCd;
	
	@Parameter(name="순서")
	private String rcstnOrdn;
	
	@Parameter(name="평가ID")
	private String evId;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
    @Parameter(name="교사등록콘텐츠")
    private String tcrCtn;
    
    @Parameter(name="콘텐츠유형")
    private String tpCd;
    
	@Parameter(name="응시회차")
	private Integer txmPn;

}
