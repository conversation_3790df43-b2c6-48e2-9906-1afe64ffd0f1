package com.aidt.api.bc.cm.textbook.adapter;

import org.springframework.stereotype.Component;

import com.aidt.api.bc.cm.textbook.dto.Textbook;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class BcTextbookAdapter {

	private final String MAPPER_NAMESPACE = "api.bc.common.cm.";
	private final CommonDao commonDao;

	public Textbook getTextbook(String textbookId) {
		return commonDao.select(MAPPER_NAMESPACE + "getTextbook", textbookId);
	}
}