package com.aidt.api.ea.grpmgmt.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email
 * @create date 2024-03-13 10:53:20
 * @modify date 2024-03-13 10:53:20
 * @desc TlLrnwTxbWebDto 교과서/익힘책 canvas object 저장 Dto
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtSaveDto {

	/** 운영교과서ID */
	@Parameter(name = "운영교과서ID")
	private String optTxbId;

	/** 사용자ID */
	@Parameter(name = "사용자ID")
	private String usrId;

	/** 데이터베이스ID */
	@Parameter(name = "데이터베이스ID")
	private String dbId;

	/** 모둠ID */
	@Parameter(name = "모둠ID")
	// @NotBlank(message = "{field.required}")
	private int grpId;

	/** 모둠명 */
	@Parameter(name = "모둠명")
	// @NotBlank(message = "{field.required}")
	private String grpNm;

	/** 팀수 */
	@Parameter(name = "팀수")
	private int temCnt;

	/** 모둠명 */
	@Parameter(name = "모둠그룹자동생성여부")
	private String grpGruAutoCrtYn;

	/** 모둠명 */
	@Parameter(name = "모둠팀장사용여부")
	private String grpTmgrUseYn;

	/** 삭제여부 */
	@Parameter(name = "삭제여부")
	private String delYn;

	/* 모둠 팀 정보 */
	@Parameter(name = "모둠 팀 정보")
	List<EaGrpMgmtGrpTeamDto> teamList;

	/* 모둠 팀 학생 정보 */
	@Parameter(name = "모둠 팀 학생 정보")
	List<EaGrpMgmtGrpTeamStuDto> stuList;
}
