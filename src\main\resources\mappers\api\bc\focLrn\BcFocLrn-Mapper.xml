<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.focLrn">
    <!-- 선생님이 공유한 화면 정보를 저장한다 -->
    <insert id="saveFocusLrnInfo" parameterType="com.aidt.api.bc.focLrn.dto.BcFlDto">
        INSERT INTO LMS_LRM.CM_CLA_FOC_LRN /* CM_학급집중학습 */
              (
                OPT_TXB_ID      /* 운영교과서ID */
                ,FOC_LRN_INFO   /* 집중학습정보 */
                ,CRTR_ID        /* 생성자ID */
                ,CRT_DTM        /* 생성일시 */
                ,MDFR_ID        /* 수정자ID */
                ,MDF_DTM        /* 수정일시 */
                ,DB_ID          /* 접속DB인스턴스ID */
              )
        VALUES (
                 #{optTxbId}    /* 운영교과서ID */
                ,#{focLrnInfo}   /* 집중학습정보 */
                ,#{usrId}       /* 생성자ID */
                ,NOW()          /* 생성일시 */
                ,#{usrId}       /* 수정자ID */
                ,NOW()          /* 수정일시 */
                ,#{dbId}        /* 접속DB인스턴스ID */
        )
        ON DUPLICATE KEY UPDATE
                FOC_LRN_INFO  = #{focLrnInfo}
            ,   MDFR_ID     = #{usrId}
            ,   MDF_DTM     = NOW()

        /* 추연도 BcFocLrn-Mapper.xml - saveFocusLrnInfo */
    </insert>
    
    				<!--   SELECT DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'); -->
    <select id="selectFocLrnInfo" parameterType="com.aidt.api.bc.focLrn.dto.BcFlDto" resultType="com.aidt.api.bc.focLrn.dto.BcFlDto">
    	SELECT  OPT_TXB_ID,
				FOC_LRN_INFO,
				CRTR_ID,
				CRT_DTM,
				MDFR_ID,
				MDF_DTM,
				DB_ID
    	FROM 	LMS_LRM.CM_CLA_FOC_LRN
    	WHERE 	OPT_TXB_ID = #{optTxbId}
    	AND		DATE_FORMAT(MDF_DTM, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d')
    </select>
</mapper>