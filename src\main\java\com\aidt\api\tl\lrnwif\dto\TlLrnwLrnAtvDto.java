package com.aidt.api.tl.lrnwif.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwLrnAtvDto 학습활동정보Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLrnwLrnAtvDto {
    /** 학습단계ID */
    @Parameter(name="학습단계ID")
    private String lrnStpId;
    /** 학습활동ID */
    @Parameter(name="학습활동ID")
    private String lrnAtvId;
    /** 학습활동명 */
    @Parameter(name="학습활동명")
    private String lrnAtvNm;
    /** 클레스보드URL */
    @Parameter(name="클레스보드URL")
    private String clsBrdUrl; //TODO 삭제예정
    /** 클레스보드목록 */
    @Parameter(name="클레스보드목록")
    private List<TlLrnwAtvClabdDto> clsbdList;
    /** 콘텐츠유형코드 */
    @Parameter(name="콘텐츠유형코드")
    private String ctnTpCd;
    /** 콘텐츠CDN패스 */
    @Parameter(name="콘텐츠CDN패스")
    private String ctnUrl;
    /** 활동정렬순서 */
    @Parameter(name="활동정렬순서")
    private String rcstnOrdn;
    /** 문항ID */
    @Parameter(name="문항ID")
    private String qtmId;
    /** 평가ID */
    @Parameter(name="평가ID")
    private String evId;
    /** 평가ID */
    @Parameter(name="평가사용여부")
    private String evUseYn;
    /** 평가ID */
    @Parameter(name="평가삭제여부")
    private String evDelYn;
    /** 평가ID */
    @Parameter(name="평가잠금여부")
    private String evLcknYn;
    /** 평가ID */
    @Parameter(name="평가응시기간설정여부")
    private String evTxmPtmeSetmYn;
    /** 평가ID */
    @Parameter(name="평가응시시작일시")
    private String evTxmStrDtm;
    /** 평가ID */
    @Parameter(name="평가응시종료일시")
    private String evTxmEndDtm;
    /** 평가ID */
    @Parameter(name="평가재응시허용여부")
    private String evRtxmPmsnYn;
    /** 평가ID */
    @Parameter(name="평가지구분명")
    private String evDtlDvNm;
    /** 학습상태(미학습:NL, 학습중:DL, 학습완료: CL) */
    @Parameter(name="학습상태(미학습:NL, 학습중:DL, 학습완료: CL) ")
    private String lrnStCd;
    /** 학습시간초수 */
    @Parameter(name="학습시간초수")
    private String lrnTmScnt;
    /** 학습활동-교육과정콘텐츠표준ID목록 */
    @Parameter(name="교육과정콘텐츠표준ID")
    private List<String> crclCtnStdIdList;
    /** 콘텐츠메타데이터목록 */
    @Parameter(name="콘텐츠메타데이터목록")
    private TlLrnwLrnCtnDto ctnMtd;
    /** 교사콘텐츠여부 */
    @Parameter(name="교사콘텐츠여부")
    private String tcrCtnYn;
}
