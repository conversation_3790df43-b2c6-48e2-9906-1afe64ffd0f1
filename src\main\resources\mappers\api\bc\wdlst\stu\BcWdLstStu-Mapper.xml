<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.wdlst.stu">

	<!-- 단어장 단어목록 조회 -->
	<select id="selectWdLstList" resultType="com.aidt.api.bc.wdlst.dto.BcWdLstDto">
		/** BcWdLstStu-Mapper.xml - selectWdLstList */
		<include refid="api.bc.common.pagingHeader"/>
		SELECT
			Q.*,
			CASE
				WHEN Q.ME_FLE_PTH IS NULL THEN 'disabled'
				ELSE 'default'
			END AS is_listening
		FROM
		(
		SELECT WDLST.MY_WD_ID
			 , ENWD.DIC_WD_ID
			 , ENWD.WD_NM
			 , EXPL.DIC_WD_EXPL_ID
			 , EXPL.WDCL_KN_CD
			 , EXPL.WD_MEAN_CN
			 , EXPL.EXSN_CN
			 , EXPL.EXSN_INTP_CN
			 , EXPL.RPRS_WDCL_YN
			 , WVFLE.FLE_PTH_NM AS AU_FLE_PTH
			 , IMFLE.FLE_PTH_NM AS IM_FLE_PTH
			 , EVFLE.FLE_PTH_NM AS ME_FLE_PTH
			 , WDLST.CRTR_ID
			 , ENWD.SCHL_GRD_CD
			 , DATE_FORMAT(WDLST.CRT_DTM, '%m. %d.') AS FN_CRT_DTM
		  FROM LMS_LRM.MY_MY_WDLST WDLST
		 INNER JOIN LMS_CMS.BC_DIC_WD ENWD
				 ON WDLST.EN_WD_ID = ENWD.DIC_WD_ID
				AND ENWD.USE_YN = 'Y'
				AND ENWD.DEL_YN = 'N'
				AND ENWD.TXB_ID = #{txbId}
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXPL EXPL
				 ON ENWD.DIC_WD_ID = EXPL.DIC_WD_ID
				AND EXPL.USE_YN ='Y'
				AND EXPL.DEL_YN  = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE WVFLE
				 ON EXPL.DIC_WD_EXPL_ID = WVFLE.DIC_WD_EXPL_ID
				AND WVFLE.EXSN_FLE_TP_CD = 'WV'
				AND WVFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE EVFLE
				 ON EXPL.DIC_WD_EXPL_ID = EVFLE.DIC_WD_EXPL_ID
				AND EVFLE.EXSN_FLE_TP_CD = 'EV'
				AND EVFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE IMFLE
				 ON EXPL.DIC_WD_EXPL_ID = IMFLE.DIC_WD_EXPL_ID
				AND IMFLE.EXSN_FLE_TP_CD = 'IM'
				AND IMFLE.DEL_YN = 'N'
		 WHERE WDLST.USR_ID = #{usrId}
		   AND WDLST.OPT_TXB_ID = #{optTxbId}
		   <if test='schlGrdCd != null and !schlGrdCd.equals("")'>
		   	AND ENWD.SCHL_GRD_CD = #{schlGrdCd}
		   </if>
		   <if test='scWord != null and !scWord.equals("")'>
			AND FIND_IN_SET(REPLACE(#{scWord}, ' ', ''), REPLACE(ENWD.SRH_WD, ' ', '')) > 0
		   </if>
		<choose>
			<when test = 'scOption.equals("crtDtm")'>
				ORDER BY WDLST.CRT_DTM
			</when>
			<when test = 'scOption.equals("engWdNm")'>
				ORDER BY ENWD.WD_NM
			</when>
			<otherwise>
				ORDER BY ENWD.DIC_WD_ID, EXPL.RPRS_WDCL_YN 
			</otherwise>
		</choose>
		<choose>
			<when test = 'scOrder.equals("desc")'>
				DESC
			</when>
			<when test = 'scOrder.equals("asc")'>
				ASC
			</when>
			<otherwise>
				DESC
			</otherwise>
		</choose>
		) Q
		<include refid="api.bc.common.pagingFooter"/>
	</select>

	<!-- 영어단어사전 단어장 팝업 단어목록 조회 -->
	<select id="selectPopWdLstList" resultType="com.aidt.api.bc.wdlst.dto.BcWdLstDto">
		/** BcWdLstStu-Mapper.xml - selectPopWdLstList */
		SELECT WDLST.MY_WD_ID
			 , ENWD.DIC_WD_ID
			 , ENWD.WD_NM
			 , EXPL.DIC_WD_EXPL_ID
			 , EXPL.WDCL_KN_CD
			 , EXPL.WD_MEAN_CN
			 , EXPL.EXSN_CN
			 , EXPL.EXSN_INTP_CN
			 , EXPL.RPRS_WDCL_YN
			 , WVFLE.FLE_PTH_NM AS AU_FLE_PTH
			 , IMFLE.FLE_PTH_NM AS IM_FLE_PTH
			 , EVFLE.FLE_PTH_NM AS ME_FLE_PTH
			 , WDLST.CRTR_ID
			 , ENWD.SCHL_GRD_CD
			 , WDLST.CRT_DTM
		  FROM LMS_LRM.MY_MY_WDLST WDLST
		 INNER JOIN LMS_CMS.BC_DIC_WD ENWD
				 ON WDLST.EN_WD_ID = ENWD.DIC_WD_ID
				AND ENWD.USE_YN = 'Y'
				AND ENWD.DEL_YN = 'N'
				AND ENWD.TXB_ID = #{txbId}
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXPL EXPL
				 ON EXPL.DIC_WD_ID = ENWD.DIC_WD_ID
				AND EXPL.USE_YN ='Y'
				AND EXPL.DEL_YN  = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE WVFLE
				 ON EXPL.DIC_WD_EXPL_ID = WVFLE.DIC_WD_EXPL_ID
				AND WVFLE.EXSN_FLE_TP_CD = 'WV'
				AND WVFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE EVFLE
				 ON EXPL.DIC_WD_EXPL_ID = EVFLE.DIC_WD_EXPL_ID
				AND EVFLE.EXSN_FLE_TP_CD = 'EV'
				AND EVFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE IMFLE
				 ON EXPL.DIC_WD_EXPL_ID = IMFLE.DIC_WD_EXPL_ID
				AND IMFLE.EXSN_FLE_TP_CD = 'IM'
				AND IMFLE.DEL_YN = 'N'
		 WHERE WDLST.USR_ID = #{usrId}
		   AND WDLST.OPT_TXB_ID = #{optTxbId}
		   <if test='schlGrdCd != null and !schlGrdCd.equals("")'>
		   	AND ENWD.SCHL_GRD_CD = #{schlGrdCd}
		   </if>
		   <if test='scWord != null and !scWord.equals("")'>
		   	AND EXISTS ( SELECT 1
						   FROM (
								SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(ENWD.SRH_WD, ',', numbers.n), ',', -1)) AS word
								  FROM (SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5) numbers
								 WHERE LENGTH(ENWD.SRH_WD) - LENGTH(REPLACE(ENWD.SRH_WD, ',', '')) + 1 >= numbers.n
							    ) AS split_words
						  WHERE TRIM(word) LIKE CONCAT(#{scWord},'%')
						)
		   </if>
		 ORDER BY WDLST.CRT_DTM DESC, ENWD.DIC_WD_ID, EXPL.RPRS_WDCL_YN DESC
	</select>

	<!-- 단어장 단어등록 -->
	<insert id="insertWdLst" parameterType="com.aidt.api.bc.wdlst.dto.BcWdLstDto" useGeneratedKeys="true" keyProperty="myWdId">
		/** BcWdLstStu-Mapper.xml - insertWdLst */
		INSERT INTO LMS_LRM.MY_MY_WDLST(
			   USR_ID
			 , EN_WD_ID
			 , OPT_TXB_ID
			 , SCHL_GRD_CD
			 , CRTR_ID
			 , CRT_DTM
			 , MDFR_ID
			 , MDF_DTM
			 , DB_ID
		) VALUES (
			   #{usrId}
			 , #{enWdId}
			 , #{optTxbId}
			 , #{schlGrdCd}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId}
		)
	</insert>

	<!-- 단어장 단어삭제 -->
	<delete id="deleteWdLstList">
		/** BcWdLstStu-Mapper.xml - deleteWdLstList */
		DELETE FROM LMS_LRM.MY_MY_WDLST
		 WHERE MY_WD_ID = #{myWdId}
		   AND DB_ID = #{dbId}
	</delete>

	<!-- 단어검색이력 조회 -->
	<select id="selectWdSrhHstList" resultType="com.aidt.api.bc.wdlst.dto.BcWdSrhHstDto">
		/** BcWdLstStu-Mapper.xml - selectWdSrhHstList */
		SELECT HST.WD_SRH_HST_ID
			 , HST.OPT_TXB_ID
			 , HST.USR_ID
			 , HST.EN_WD_ID
			 , HST.CRTR_ID
			 , HST.CRT_DTM
			 , HST.MDFR_ID
			 , HST.MDF_DTM
			 , HST.DB_ID
			 , ENWD.WD_NM
			 , EXPL.WD_MEAN_CN
		  FROM LMS_LRM.MY_WD_SRH_HST HST
		 INNER JOIN LMS_CMS.BC_DIC_WD ENWD
				 ON HST.EN_WD_ID = ENWD.DIC_WD_ID
				AND ENWD.USE_YN = 'Y'
				AND ENWD.DEL_YN = 'N'
				AND ENWD.TXB_ID = #{txbId}
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXPL EXPL
				 ON EXPL.DIC_WD_ID = ENWD.DIC_WD_ID
				AND EXPL.USE_YN ='Y'
				AND EXPL.DEL_YN  = 'N'
		<where>
			AND HST.OPT_TXB_ID = #{optTxbId}
			AND HST.USR_ID = #{usrId}
			<if test='scWord != null and !scWord.equals("")'>
			AND EXISTS ( SELECT 1
						   FROM (
								SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(ENWD.SRH_WD, ',', numbers.n), ',', -1)) AS word
								  FROM (SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5) numbers
								 WHERE LENGTH(ENWD.SRH_WD) - LENGTH(REPLACE(ENWD.SRH_WD, ',', '')) + 1 >= numbers.n
							    ) AS split_words
						  WHERE TRIM(word) LIKE CONCAT(#{scWord},'%')
						)
			</if>
		</where>
		ORDER BY HST.MDF_DTM DESC
	</select>

	<!-- 검색이력 기존 데이터 확인 -->
	<select id="selectWdSrhHstCnt" resultType="integer">
		/** BcWdLstStu-Mapper.xml - selectWdSrhHstCnt */
		SELECT COUNT(*)
		  FROM LMS_LRM.MY_WD_SRH_HST
		<where>
			AND OPT_TXB_ID = #{optTxbId}
			AND USR_ID = #{usrId}
			AND EN_WD_ID = #{enWdId}
		</where>
	</select>

	<!-- 단어검색이력 등록 -->
	<insert id= "insertWdSrhHst" parameterType="com.aidt.api.bc.wdlst.dto.BcWdSrhHstDto">
		/** BcWdLstStu-Mapper.xml - insertWdSrhHstList */
		INSERT INTO LMS_LRM.MY_WD_SRH_HST(
			OPT_TXB_ID
			 , USR_ID
			 , EN_WD_ID
			 , CRTR_ID
			 , CRT_DTM
			 , MDFR_ID
			 , MDF_DTM
			 , DB_ID
		)VALUES(
			#{optTxbId}
			 , #{usrId}
			 , #{enWdId}
			 , #{usrId}
			 , NOW()
			 , #{usrId}
			 , NOW()
			 , #{dbId}
		)
	</insert>

	<!-- 단어검색이력 업데이트 -->
	<update id= "updateWdSrhHst" parameterType="com.aidt.api.bc.wdlst.dto.BcWdSrhHstDto">
		/** BcWdLstStu-Mapper.xml - updateWdSrhHstList */
		UPDATE LMS_LRM.MY_WD_SRH_HST
		   SET MDFR_ID = #{usrId}
			 , MDF_DTM = NOW()
		<where>
		   AND OPT_TXB_ID = #{optTxbId}
		   AND USR_ID = #{usrId}
		   AND EN_WD_ID = #{enWdId}
		   AND DB_ID = #{dbId}
		</where>
	</update>

	<!-- 영단어 관련검색어 조회 -->
	<select id="srchWdLst" resultType="com.aidt.api.bc.wdlst.dto.BcWdLstDto">
		/** BcWdLstStu-Mapper.xml - srchWdLst */
		SELECT ENWD.DIC_WD_ID
			 , ENWD.WD_NM
			 , EXPL.WD_MEAN_CN
		  FROM LMS_CMS.BC_DIC_WD ENWD
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXPL EXPL
				 ON ENWD.DIC_WD_ID = EXPL.DIC_WD_ID
				AND EXPL.USE_YN = 'Y'
				AND EXPL.DEL_YN = 'N'
		<where>
			AND ENWD.USE_YN = 'Y'
			AND ENWD.DEL_YN = 'N'
			AND ENWD.TXB_ID = #{txbId}
			<if test='scWord != null and !scWord.equals("")'>
			AND EXISTS ( SELECT 1
						   FROM (
								SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(ENWD.SRH_WD, ',', numbers.n), ',', -1)) AS word
								  FROM (SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5) numbers
								 WHERE LENGTH(ENWD.SRH_WD) - LENGTH(REPLACE(ENWD.SRH_WD, ',', '')) + 1 >= numbers.n
							    ) AS split_words
						  WHERE TRIM(word) LIKE CONCAT(#{scWord},'%')
						)
			</if>
			<if test='schlGrdCd != null and !schlGrdCd.equals("")'>
			AND ENWD.SCHL_GRD_CD = #{schlGrdCd}
			 </if>
		</where>
		ORDER BY ENWD.WD_NM
	</select>

	<!-- 영어단어 조회 -->
	<select id="selectCmEnWdLst" resultType="com.aidt.api.bc.wdlst.dto.BcWdLstDto">
		/** BcWdLstStu-Mapper.xml - selectCmEnWdLst */
		SELECT ENWD.DIC_WD_ID
			 , ENWD.WD_NM
			 , EXPL.DIC_WD_EXPL_ID
			 , EXPL.WDCL_KN_CD
			 , EXPL.WD_MEAN_CN
			 , EXPL.EXSN_CN
			 , EXPL.EXSN_INTP_CN
			 , EXPL.RPRS_WDCL_YN
			 , WVFLE.FLE_PTH_NM AS AU_FLE_PTH
			 , IMFLE.FLE_PTH_NM AS IM_FLE_PTH
			 , EVFLE.FLE_PTH_NM AS ME_FLE_PTH
			 , WDLST.MY_WD_ID
			 , WDLST.USR_ID
			 , ENWD.SCHL_GRD_CD
			 , WDLST.OPT_TXB_ID
		  FROM LMS_CMS.BC_DIC_WD ENWD
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXPL EXPL
				 ON ENWD.DIC_WD_ID = EXPL.DIC_WD_ID
				AND EXPL.USE_YN ='Y'
				AND EXPL.DEL_YN  = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE WVFLE
				 ON EXPL.DIC_WD_EXPL_ID = WVFLE.DIC_WD_EXPL_ID
				AND WVFLE.EXSN_FLE_TP_CD = 'WV'
				AND WVFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE EVFLE
				 ON EXPL.DIC_WD_EXPL_ID = EVFLE.DIC_WD_EXPL_ID
				AND EVFLE.EXSN_FLE_TP_CD = 'EV'
				AND EVFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_CMS.BC_DIC_WD_EXSN_FLE IMFLE
				 ON EXPL.DIC_WD_EXPL_ID = IMFLE.DIC_WD_EXPL_ID
				AND IMFLE.EXSN_FLE_TP_CD = 'IM'
				AND IMFLE.DEL_YN = 'N'
		  LEFT JOIN LMS_LRM.MY_MY_WDLST WDLST
				 ON WDLST.EN_WD_ID = ENWD.DIC_WD_ID
				AND WDLST.USR_ID = #{usrId}
				AND WDLST.OPT_TXB_ID = #{optTxbId}
		 WHERE ENWD.USE_YN = 'Y'
		   AND ENWD.DEL_YN = 'N'
		   AND ENWD.TXB_ID = #{txbId}
		   <if test='schlGrdCd != null and !schlGrdCd.equals("")'>
		   	AND ENWD.SCHL_GRD_CD = #{schlGrdCd}
		   </if>
		   <if test='scWord != null and !scWord.equals("")'>
			    <choose>
			      <when test='alphabetFlag'>
			       AND EXISTS (
			          SELECT 1
			          FROM (
			            SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(ENWD.SRH_WD, ',', numbers.n), ',', -1)) AS word
			            FROM (SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5) numbers
			            WHERE LENGTH(ENWD.SRH_WD) - LENGTH(REPLACE(ENWD.SRH_WD, ',', '')) + 1 >= numbers.n
			          ) AS split_words
			          WHERE TRIM(word) LIKE CONCAT(#{scWord}, '%')
			        )
			        
			      </when>
			      <otherwise>
			        AND FIND_IN_SET(REPLACE(#{scWord}, ' ', ''), REPLACE(ENWD.SRH_WD, ' ', '')) > 0
			      </otherwise>
			    </choose>
			  </if>
		  ORDER BY ENWD.DIC_WD_ID, EXPL.RPRS_WDCL_YN DESC
	</select>
</mapper>