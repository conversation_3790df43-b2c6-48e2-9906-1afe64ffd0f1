<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aidt.api.at.mapper.RestoreMapper">

	<select id="selectUsrLrnrVelRestoreTargetList" resultType="String">
		SELECT
			u.usr_id
		FROM
			lms_lrm.cm_usr u
		WHERE
			u.usr_tp_cd = 'ST'
	</select>
	
	<insert id="insertRestoreLog" parameterType="com.aidt.api.at.err.dto.FrontErrLogDto">
		INSERT INTO lms_lrm.cm_front_err_log
		(
			 conn_dv_cd
			,front_conn_url
			,body_cn
			,usr_id
			,opt_txb_id
			,err_msg
			,system_cd
			,reg_dtm
		)
		VALUES
		(
			 #{connDvCd}			
			,#{frontConnUrl}
			,#{bodyCn}
			,#{usrId}
			,#{optTxbId}
			,#{errMsg}
			,#{systemCd}
			, NOW()
		)
	</insert>
	
	<select id="selectEvQtmAnwRestoreTarget" resultType="com.aidt.api.at.err.dto.EvQtmAnwRestoreDto">
		SELECT 
			 eeqa.ev_id
			,eeqa.qtm_id
			,eeqa.usr_id
			,eeqa.xpl_st_cd
			,(
				SELECT 
					CASE 
						WHEN sdpxt.ppe_xpl_tm_scnt * 0.3 >= eqa.xpl_tm_scnt THEN '02'
						ELSE '00'
					END
				FROM 
					lms_lrm.ea_ev_qtm_anw eqa
					INNER JOIN lms_lrm.cm_usr u
						ON u.usr_id = eqa.usr_id
					INNER JOIN lms_lrm.cm_opt_txb ot
						ON ot.cla_id = u.cla_id
					INNER JOIN lms_cms.bc_txb t
						ON t.txb_id = ot.txb_id
					INNER JOIN lms_cms.qp_qtm_an qa
						ON qa.qp_qtm_id = eqa.qtm_id
					INNER JOIN lms_lrm.ea_ev_qtm eq
						ON eq.ev_id = eqa.ev_id
						AND eq.qtm_id = eqa.qtm_id
					LEFT OUTER JOIN lms_lrm.ea_sbj_dffd_ppe_xpl_tm sdpxt
						ON sdpxt.sbj_cd = (
											CASE 
												WHEN t.sbj_cd in ('MA', 'CM1', 'CM2') THEN 'MA'
												WHEN t.sbj_cd in ('EN', 'CE1', 'CE2') THEN 'EN'
												ELSE NULL
										 	 END
										  )	
						AND sdpxt.schl_grd_cd = t.schl_grd_cd
						AND sdpxt.cn_ara_cd = (
												CASE 
													WHEN t.sbj_cd in ('MA', 'CM1', 'CM2') THEN 'CM'
													WHEN t.sbj_cd in ('EN', 'CE1', 'CE2') THEN qa.qp_cn_ara_id
													ELSE NULL
												END
											  )
						AND sdpxt.dffd_cd = eq.qtm_dffd_dv_cd
				WHERE 
					eqa.ev_id = eeqa.ev_id
				AND eqa.qtm_id = eeqa.qtm_id
				AND eqa.usr_id = eeqa.usr_id
			 ) AS chg_xpl_st_cd
		FROM
			lms_lrm.ea_ev_qtm_anw eeqa
			INNER JOIN lms_lrm.ea_ev_rs er
				ON er.ev_id = eeqa.ev_id
				AND er.usr_id = eeqa.usr_id
				AND er.ev_cmpl_yn = 'Y'
		WHERE 
			eeqa.cans_yn = 'Y'
		AND eeqa.xpl_st_cd = '00'
	</select>
	
	<update id="updateEvQtmAnwXplStCd" parameterType="com.aidt.api.at.err.dto.EvQtmAnwRestoreDto">
		UPDATE
			lms_lrm.ea_ev_qtm_anw
		SET
			xpl_st_cd = #{chgXplStCd}
		WHERE
			ev_id = #{evId}
		AND qtm_id = #{qtmId}
		AND usr_id = #{usrId}
	</update>
	
</mapper>