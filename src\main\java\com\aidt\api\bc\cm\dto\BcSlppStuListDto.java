package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description="대화학생목록")
public class BcSlppStuListDto {

	@Parameter(name="토픽ID")
	private String tpcId;

	@Parameter(name="교과서ID")
	private String txbId;

	@Parameter(name="교사사용자ID")
	private String tcrUsrId;

	@Parameter(name="학생사용자ID")
	private String stuUsrId;

	@Parameter(name="학급ID")
	private String claId;


	private String crtrId;
	private String crtDtm;
	private String mdfrId;
	private String mdfDtm;
	private String dbId;


}
