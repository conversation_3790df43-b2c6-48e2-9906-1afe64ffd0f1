
package com.aidt.api.tl.sbclrn.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvEvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvEvQtmDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStChkDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStatDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumEvWkbDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvWbDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTcAtvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnThbDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:28:18
 * @modify date 2024-01-04 14:28:18
 * @desc TlSbcLrnStu Service 교과학습 서비스
 */
@Service
public class TlSbcLrnStuService {
    private final String MAPPER_NAMESPACE = "api.tl.sbclrn.stu.";

    @Autowired
    private CommonDao commonDao;

    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    /**
     * 교과학습 학습활동 목록 조회 서비스
     * 
     * @param srhDto
     * @return List<TlSbcLrnAtvDto>
     */
//    @Transactional(readOnly = true)
//    public List<TlSbcLrnAtvDto> selectLrnAtvList(TlSbcLrnTocSrhDto srhDto) {
//        return commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvList", srhDto);
//    }

    /**
     * 교과학습활동 상세조회(Summary)
     * 
     * @param srhDto
     * @return
     */
    @Transactional(readOnly = true)
    public TlSbcLrnAtvSumDto selectLrnAtvSum(TlSbcLrnTocSrhDto srhDto) {
        TlSbcLrnAtvSumDto rtnDto = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvSum", srhDto);
        
        if(rtnDto == null) {
        	return new TlSbcLrnAtvSumDto();
        };
        
        int totCnt = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvTotCnt", srhDto);
        int clCnt = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvClCnt", srhDto);
        rtnDto.setAllLrnAtvCnt(Integer.toString(totCnt));
        rtnDto.setLrnAtvFinCnt(Integer.toString(clCnt));
        
        String objUrl = TlCmUtil.DOMAIN_PATH + BUCKET_NAME;
        List<TlSbcLrnTcAtvDto> atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTcAtvList", Map.of("param", srhDto, "objUrl", objUrl, "srchCurrAtvYn", "N")); // v3.1대응
        List<TlSbcLrnTcAtvDto> currAtvList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTcAtvList", Map.of("param", srhDto, "objUrl", objUrl, "srchCurrAtvYn", "Y")); // v3.1대응
        
        TlSbcLrnThbDto tcThbDto = commonDao.select(MAPPER_NAMESPACE + "selectTxbThbInfo2",
                Map.of("lrmpNodId", srhDto.getLrmpNodId())); // v3.1대응용 mapper id변경할것
        
        if(tcThbDto != null) {
            tcThbDto.setPcCdnPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, tcThbDto.getPcCdnPthNm()));
            tcThbDto.setTaCdnPthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, tcThbDto.getTaCdnPthNm()));
        } else {
        	tcThbDto = new TlSbcLrnThbDto();
        	tcThbDto.setLrnGoalCn("");
        }
        
        // 학습활동 데이터가 없는 경우
        if (atvList != null && atvList.size() == 0) {
        	// 학습활동 데이터는 데이터 오류나, TEST데이터가 오류나므로 대응함.
        	rtnDto.setAtvList(new ArrayList<TlSbcLrnTcAtvDto>());
        	rtnDto.setLrnGoal(tcThbDto.getLrnGoalCn());
        	rtnDto.setTcThbInfo(tcThbDto);
        	return rtnDto;
        } else if (currAtvList != null && 0 < currAtvList.size() && "Y".equals(currAtvList.get(0).getCurrAtvYn())) {
            String currAtvId = currAtvList.get(0).getLrnAtvId();
            atvList.forEach(dto -> {
                if (currAtvId.equals(dto.getLrnAtvId())) {
                    dto.setCurrAtvYn("Y");
                }
            });
        } else {
            // 첫번째 행을 현재학습활동으로 설정
            if (atvList != null && 0 < atvList.size()) {
                atvList.get(0).setCurrAtvYn("Y");
            }
        }
        rtnDto.setAtvList(atvList); // v3.1대응
        rtnDto.getAtvList().forEach(list -> {
        	if(list.getLrnAtvThbCdnPath() != null && !list.getLrnAtvThbCdnPath().isEmpty()) {
        		list.setLrnAtvThbCdnPath(TlCmUtil.mapURL(list.getLrnAtvThbCdnPath()));
        	}
        });
        //        TlSbcLrnThbDto thbDto = commonDao.select(MAPPER_NAMESPACE + "selectTxbThbInfo",
//                Map.of("optTxbId", srhDto.getOptTxbId()));
        
        
        
        rtnDto.setLrnGoal(tcThbDto.getLrnGoalCn());
//        rtnDto.setThbInfo(thbDto);
        rtnDto.setTcThbInfo(tcThbDto);
        return rtnDto;
    }

    /**
     * 교과학습활동 평가/익힘책조회 (Summery)
     * 
     * @param srhDto
     * @return
     */
    @Transactional(readOnly = true)
    public TlSbcLrnAtvSumEvWkbDto selectLrnEvWkbSum(TlSbcLrnTocSrhDto srhDto) {
        TlSbcLrnAtvSumEvWkbDto rtnDto = new TlSbcLrnAtvSumEvWkbDto();
        // 평가정보취득
        Map<String, String> evMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvEvSum", srhDto);
        // 익힘문제정보취득
        Map<String, String> wkMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvWkbSum", srhDto);
        String showYn = "N";
        if (evMap != null) {
            rtnDto.setNodNo(String.valueOf(evMap.get("NOD_NO")));
            rtnDto.setLluNodId(evMap.get("LLU_NOD_ID"));
            rtnDto.setLrmpNodNm1(evMap.get("LRMP_NOD_NM1"));
            rtnDto.setEvLrnAtvId(String.valueOf(evMap.get("LRN_ATV_ID")));
            rtnDto.setEvLrnAtvNm(evMap.get("LRN_ATV_NM"));
            rtnDto.setEvLrnStpNm(evMap.get("LRN_STP_NM"));
            rtnDto.setEvTotCnt(String.valueOf(evMap.get("EV_TOT_CNT")));
            rtnDto.setEvFinCnt(String.valueOf(evMap.get("EV_FIN_CNT")));
            rtnDto.setEvLrnStCd(evMap.get("LRN_ST_CD"));
            rtnDto.setEvId(String.valueOf(evMap.get("EV_ID")));
            rtnDto.setEvDtlDvCd(evMap.get("EV_DTL_DV_CD"));
            rtnDto.setEvXplTmMin(String.valueOf(evMap.get("XPL_TM_MIN")));
            rtnDto.setEvXplTmSetmYn(evMap.get("XPL_TM_SETM_YN"));
            rtnDto.setExtrEvId(String.valueOf(evMap.get("EXTR_EV_ID")));
            showYn = "Y";
        }
        if (wkMap != null) {
            // rtnDto.setWkbLrnAtvId(wkMap.get("LRN_ATV_ID"));
            // rtnDto.setWkbLrnAtvNm(wkMap.get("LRN_ATV_NM"));
            // rtnDto.setWkbLrnStpNm(wkMap.get("LRN_STP_NM"));
            rtnDto.setWkbStrPgeNo(String.valueOf(wkMap.get("WKB_STR_PGE_NO")));
            rtnDto.setWkbEndPgeNo(String.valueOf(wkMap.get("WKB_END_PGE_NO")));
            rtnDto.setWkbFinCnt(String.valueOf(wkMap.get("WKB_FIN_CNT")));
            rtnDto.setWkbTotCnt(String.valueOf(wkMap.get("WKB_TOT_CNT")));
            rtnDto.setWkbLrnAtvId(String.valueOf(wkMap.get("LRN_ATV_ID")));
            // rtnDto.setWkbLrnStCd(wkMap.get("LRN_ST_CD"));
            // rtnDto.setWkbEvId(String.valueOf(wkMap.get("EV_ID")));
            String wkbStCd = TlConstUtil.LRN_ST_NL;
            if (!"0".equals(rtnDto.getWkbFinCnt()) && !rtnDto.getWkbFinCnt().equals(rtnDto.getWkbTotCnt())) {
                wkbStCd = TlConstUtil.LRN_ST_DL;
            } else if (!"0".equals(rtnDto.getWkbFinCnt()) && rtnDto.getWkbFinCnt().equals(rtnDto.getWkbTotCnt())) {
                wkbStCd = TlConstUtil.LRN_ST_CL;
            }
            rtnDto.setWkbLrnStCd(wkbStCd);
            showYn = "0".equals(rtnDto.getWkbTotCnt()) ? "N" : "Y";
            if (evMap == null) {
                rtnDto.setNodNo(String.valueOf(wkMap.get("NOD_NO")));
                rtnDto.setLluNodId(wkMap.get("LLU_NOD_ID"));
                rtnDto.setLrmpNodNm1(wkMap.get("LRMP_NOD_NM1"));
            }
        }
        rtnDto.setShowYn(showYn);
        return rtnDto;
    }

    /**
     * 교과학습활동 평가정보 및 문항정보 취득(v2.3대응)
     * 
     * @param srhDto
     * @return
     */
    @Transactional(readOnly = true)
    public TlSbcLrnAtvEvDto selectLrnEvInfo(TlSbcLrnTocSrhDto srhDto) {
        TlSbcLrnAtvEvDto rtnDto = commonDao.select(MAPPER_NAMESPACE + "selectLrnEvInfo", srhDto); // 평가정보취득
        if (rtnDto != null) {
            // 평가문항리스트조회
            List<TlSbcLrnAtvEvQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnEvQtmList", Map
                    .of("optTxbId", srhDto.getOptTxbId(), "lrnUsrId", srhDto.getLrnUsrId(), "evId", rtnDto.getEvId()));
            rtnDto.setEvQtmList(list);

            if (rtnDto.getExtrEvId() != null && !rtnDto.getExtrEvId().equals("")) {
                List<TlSbcLrnAtvEvQtmDto> exrtList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnEvQtmList", Map
                        .of("optTxbId", srhDto.getOptTxbId(), "lrnUsrId", srhDto.getLrnUsrId(), "evId",
                                rtnDto.getExtrEvId()));
                rtnDto.setExtrEvQtmList(exrtList);
            }
        }
        return rtnDto;
    }

    /**
     * 교과학습활동 익힘책정보 취득(v2.3대응)
     * 
     * @param srhDto
     * @return
     */
    @Transactional(readOnly = true)
    public TlSbcLrnAtvWbDto selectLrnWbInfo(TlSbcLrnTocSrhDto srhDto) {
        TlSbcLrnAtvWbDto rtnDto = new TlSbcLrnAtvWbDto();
        // 익힘문제정보취득
        Map<String, String> wkMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvWkbSum", srhDto);
        String showYn = "N";
        if (wkMap != null) {
            rtnDto.setLrnAtvId(String.valueOf(wkMap.get("LRN_ATV_ID")));
            rtnDto.setWkbFinCnt(String.valueOf(wkMap.get("WKB_FIN_CNT")));
            rtnDto.setWkbTotCnt(String.valueOf(wkMap.get("WKB_TOT_CNT")));
            String wkbStCd = TlConstUtil.LRN_ST_NL;
            if (!"0".equals(rtnDto.getWkbFinCnt()) && !rtnDto.getWkbFinCnt().equals(rtnDto.getWkbTotCnt())) {
                wkbStCd = TlConstUtil.LRN_ST_DL;
            } else if (!"0".equals(rtnDto.getWkbFinCnt()) && rtnDto.getWkbFinCnt().equals(rtnDto.getWkbTotCnt())) {
                wkbStCd = TlConstUtil.LRN_ST_CL;
            }
            rtnDto.setWkbLrnStCd(wkbStCd);
            showYn = "0".equals(rtnDto.getWkbTotCnt()) ? "N" : "Y";
        }
        rtnDto.setShowYn(showYn);

        return rtnDto;
    }

    /**
     * 최근 학습한 학습활동ID 취득
     * 
     * @param srhDto
     * @return
     */
    public List<TlSbcLrnAtvStChkDto> selectLrnAtvStChk(TlSbcLrnTocSrhDto srhDto) {
        List<TlSbcLrnAtvStChkDto> rtnList = new ArrayList<TlSbcLrnAtvStChkDto>();
        List<TlSbcLrnAtvStChkDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvStChk", srhDto);
        String lastLrmpNodId = commonDao.select(MAPPER_NAMESPACE + "selectLastLrmpNodId", srhDto);
        boolean searchNext = false;
        boolean isNotFound = true;

        for (TlSbcLrnAtvStChkDto item : list) {
            if (lastLrmpNodId == null || "".equals(lastLrmpNodId)) {
                break;
            }
            if ("Y".equals(item.getLcknYn())) {
                continue;
            }
            if (!searchNext) {
                if (lastLrmpNodId.equals(item.getLrmpNodId())) { // 마직막 학습한 노드이면
                    if (item.getCntTot().equals(item.getCntCl())) { // 총건수와 완료건수가 일치하는 경우는 다음 학습노드를 찾는다.
                        searchNext = true;
                    } else {
                        rtnList.add(item);
                        isNotFound = false;
                        break;
                    }
                }
            } else {
                if (!item.getCntTot().equals(item.getCntCl())) { // 완료되지 않은 학습노드를 찾는다.
                    rtnList.add(item);
                    isNotFound = false;
                    break;
                }
            }
        }
        // 최근학습데이터를 못찾은 경우는 처음것을 보여준다.
        if (isNotFound) {
            if (0 < list.size()) {
                rtnList.add(list.get(0));
            }
        }
        return rtnList;
    }

    /**
     * 해당 차시의 학습활동상세정보 목록조회
     * 
     * @param srhDto
     * @return
     */
//    public List<TlSbcLrnAtvStatDto> selectLrnTocStatList(TlSbcLrnTocSrhDto srhDto) {
//
//        List<TlSbcLrnAtvStatDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTocStatList", Map.of("param", srhDto, "lrnStpDvCd", "CL"));
//        return list;
//    }

    /**
     * 해당 차시의 학습활동상세정보 목록조회(v3.3대응)
     * 
     * @param srhDto
     * @return
     */
    public Map<String, Object> selectLrnTocStatusList(TlSbcLrnTocSrhDto srhDto) {

        List<TlSbcLrnAtvStatDto> clList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTocStatList", Map.of("param", srhDto, "lrnStpDvCd", "CL"));
        List<TlSbcLrnAtvStatDto> wbList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTocStatList", Map.of("param", srhDto, "lrnStpDvCd", "WB"));
        List<TlSbcLrnAtvStatDto> exList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTocStatList", Map.of("param", srhDto, "lrnStpDvCd", "EX"));
        if (exList != null && 0 < exList.size()) {
            String evId = exList.get(0).getEvId();
            String fnlQstCnt = "0"; /* 총문제수 */
            String cansCnt = "0";   /* 정답수 */
            List<String> cansList = new ArrayList<String>(); /* 정답여부 */
            String evCmplYn = "N";
            if (evId != null && !"".equals(evId)) {
                List<Map<String,Object>> qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectExQtmList", Map.of( "param", srhDto,  "evId", evId));
                if (qtmList != null && 0 < qtmList.size()) {
                    fnlQstCnt = qtmList.get(0).get("FNL_QST_CNT").toString();
                    cansCnt = qtmList.get(0).get("CANS_CNT").toString();
                    evCmplYn = qtmList.get(0).get("EV_CMPL_YN").toString();
                }
                // 문항별 정답여부 리스트 추가
                qtmList.stream().forEach(itmMap -> {
                    cansList.add(itmMap.get("CANS_YN").toString());
                });
            }
            exList.get(0).setFnlQstCnt(fnlQstCnt);
            exList.get(0).setCansCnt(cansCnt);
            exList.get(0).setEvCmplYn(evCmplYn);
            exList.get(0).setCansList(cansList);
            if(evCmplYn.equals("Y")) {
            	exList.get(0).setLrnStNm("응시 완료");
            } else if (evCmplYn.equals("D")) {
            	exList.get(0).setLrnStNm("응시 중");
            } else {
            	exList.get(0).setLrnStNm("미응시");
            }
        }
        Map<String, Object> rtnMap = new HashMap<String, Object>();
        rtnMap.put("cl", clList);
        rtnMap.put("wb", wbList);
        rtnMap.put("ex", exList);
        return rtnMap;
    }

}
