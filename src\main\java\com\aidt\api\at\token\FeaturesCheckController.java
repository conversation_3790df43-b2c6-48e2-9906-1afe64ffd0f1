package com.aidt.api.at.token;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.aidt.api.at.token.dto.FeaturesCheckReqDto;
import com.aidt.api.at.token.dto.FeaturesCheckResDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name="[at] CheckFeature", description="장애 대응")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/at/token")
public class FeaturesCheckController {

    @Autowired
    private FeaturesCheckService featuresCheckService;

    @Operation(summary="장애 대응 조회", description="장애 대응 조회.")
    @PostMapping ("/selectCheckFeatures")
    public FeaturesCheckResDto selectCheckFeature(@RequestBody FeaturesCheckReqDto dto ) {
        return featuresCheckService.selectCheckFeature(dto);
    }

    @Operation(summary="서버 배포 버전 정보", description="서버 배포 버전 정보.")
    @GetMapping ("/checkVersion")
    public ResponseEntity<Object> checkVersion() {
        return ResponseEntity.ok(featuresCheckService.getSystemLoadVerNm());
    }
}
