package com.aidt.api.tl.common;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-11 14:35:49
 * @modify date 2024-01-11 14:35:49
 * @desc TlCmUtil 교과학습공통기능클래스
 */
public class TlCmUtil {
    // TODO 도메인URL 프로퍼티파일로 빼서 넣을것
    /** 파일도메인패스 */
    public final static String DOMAIN_PATH = "/api/v1/content/";

    /** 파일CDN URL 가공처리 
     * @param bucketName 
     * @param cdnPath
     * @return String url
     */
    public static String makeFleCdnUrl(String bucketName, String cdnPath) {
        String rtnUrl = "";
        if (cdnPath == null || cdnPath.trim().length() == 0) {
            rtnUrl = "";
        } else if (cdnPath.indexOf("http") == 0) {
            rtnUrl = cdnPath;
        } else {
            String pathSplit = 0 < cdnPath.indexOf("/") ? "/" : "";
            rtnUrl = DOMAIN_PATH + bucketName + pathSplit + cdnPath;
        }
        
        if(rtnUrl != null && !rtnUrl.isEmpty()) {
        	rtnUrl = mapURL(rtnUrl);
        }
        
        return rtnUrl;
    }

    /**
     *  콘텐츠CDN패스URL가공처리
     * @param bucketName
     * @param cdnPath
     * @param startFleNm
     * @return
     */
    public static String makeCtnCdnUrl(String bucketName, String cdnPath, String startFleNm) {
        String rtnUrl = "";

        if (cdnPath.indexOf("http") == 0) { // Test용 데이터용
            rtnUrl = cdnPath;
        }  else {
            if (0 < cdnPath.length()) {
                rtnUrl = DOMAIN_PATH + bucketName + cdnPath;
                if (0 < startFleNm.length()) {
                    // ctnUrl 마지막이 "/" 으로 끝나지 않으면 "/"를 추가해서 파일명을 설정해준다.
                    rtnUrl += rtnUrl.lastIndexOf("/") == (rtnUrl.length() - 1) ? startFleNm : "/" + startFleNm;
                }
            }
        }
        
        if(rtnUrl != null && !rtnUrl.isEmpty()) {
        	rtnUrl = mapURL(rtnUrl);
        }
        
        return rtnUrl;
    }
    
    public static String mapURL(String inputURL) {
        if (inputURL == null || inputURL.isEmpty()) {
            throw new IllegalArgumentException("Input URL cannot be null or empty");
        }

        String lowerCaseURL = inputURL.toLowerCase();
//        if (lowerCaseURL.endsWith(".html") || lowerCaseURL.endsWith(".htm")) {
//            return inputURL;
//        }

        String devPrefix = "/api/v1/content/lms-op-dev-sto-02/";
        String prdPrefix = "/api/v1/content/lms-prd-sto-02/";
        String devBaseURL = "https://aidtcdn-op-dev.aitextbook.co.kr/";
        String prdBaseURL = "https://aidtcdn.aitextbook.co.kr/";

        if (inputURL.startsWith(devPrefix)) {
            return devBaseURL + inputURL.substring(devPrefix.length());
        } else if (inputURL.startsWith(prdPrefix)) {
            return prdBaseURL + inputURL.substring(prdPrefix.length());
        } else {
            return inputURL;
        }
    }
}
