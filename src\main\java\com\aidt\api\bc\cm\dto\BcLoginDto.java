package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "로그인")
public class BcLoginDto {

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="학급ID")
	private String claId;
	
	@Parameter(name = "교과서ID")
	private String txbId;

	@Parameter(name = "교과서학기코드")
	private String txbTermCd;
	
	@Parameter(name = "서브도메인")
	private String subDomain;

	@Parameter(name = "강의코드")
	private String lectureCd;

	@Parameter(name = "학급코드")
	private String classCd;

}
