package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlOneClkSetmClaDto {
	/** 다른 학급 운영교과서 ID */
    @Parameter(name="다른 학급 운영교과서ID")
    private String optTxbId;

    /** 현재 운영교과서 ID */
    @Parameter(name="현재 운영교과서ID")
    private String orgnOptTxbId;

    /** 수정자 ID */
    @Parameter(name="수정자 ID")
    private String mdfrId;

    /** 학급ID */
    @Parameter(name="학급ID")
    private String claId;

    /** 학년 */
    @Parameter(name="학년")
    private int sgy;

    /** 반명 */
    @Parameter(name="반명")
    private String claNm;
}
