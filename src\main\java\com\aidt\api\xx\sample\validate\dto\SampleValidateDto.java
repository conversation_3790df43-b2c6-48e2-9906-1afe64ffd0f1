package com.aidt.api.xx.sample.validate.dto;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

// @NotEmpty는 속성이 null 또는 비어 있지 않은지 확인합니다. 문자열, 콜렉션, 맵 또는 배열 값에 적용할 수 있습니다.
// @NotBlank는 텍스트 값에만 적용할 수 있으며 속성이 null 또는 공백이 아닌지 확인합니다.
// @NotNull은 주석이 달린 속성 값이 null이 아님을 확인합니다.
// @AssertTrue는 주석이 달린 속성 값이 참인지 확인합니다.
// @Size는 주석이 달린 속성 값의 크기가 최소와 최대 속성 사이인지 확인합니다. 문자열, 콜렉션, 맵 및 배열 속성에 적용할 수 있습니다.
// @Min은 주석이 달린 속성의 값이 값 속성보다 작지 않은지 확인합니다.
// @Max는 주석이 달린 속성의 값이 값 속성보다 크지 않은지 확인합니다.
// @전자 메일은 주석이 달린 속성이 올바른 전자 메일 주소인지 확인합니다.
// @Positive 및 @PositiveOrZero는 숫자 값에 적용되며 0을 포함하여 엄격히 양수인지 또는 양수인지 확인합니다.
// @Negative(음) 및 @NegativeOrZero(음)는 숫자 값에 적용되며, 숫자 값이 엄밀하게 음수인지, 0을 포함하여 음수인지 확인합니다.
// @Past 및 @PastOrPresent는 날짜 값이 현재를 포함한 과거 또는 과거인지 확인합니다. Java 8에 추가된 날짜 유형을 포함한 날짜 유형에 적용할 수 있습니다.
// @Future 및 @FutureOrPresent는 날짜 값이 미래 또는 현재를 포함한 미래에 있는지 확인합니다.
// https://javaee.github.io/javaee-spec/javadocs/javax/validation/constraints/package-summary.html

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample Validate DTO
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SampleValidateDto {
    @Parameter(name="사용자 ID", required=true)
    @NotBlank(message = "{field.required}")
    @Size(min = 8, message = "8자 {field.min}")
    private String userId;

    @Parameter(name="이름", required=true)
    @NotBlank(message = "{field.required}")
    private String userNm;

    @Parameter(name="나이", required=true)
    @Min(value = 10, message = "10 {field.min}")
    @Max(value = 100, message = "100 {field.max}")
    private int age;

    @Parameter(name="이메일", required=true)
    @NotBlank(message = "{field.required}")
    // @Pattern(regexp =
    // "^(?=.{1,64}@)[A-Za-z0-9_-]+(\\\\.[A-Za-z0-9_-]+)*@[^-][A-Za-z0-9-]+(\\\\.[A-Za-z0-9-]+)*(\\\\.[A-Za-z]{2,})$",
    // message = "{field.exp.email}(ex. {filed.email.type01})")
    @Pattern(regexp = "^[0-9a-zA-Z]([-_.]?[0-9a-zA-Z])*@[0-9a-zA-Z]([-_.]?[0-9a-zA-Z])*.[a-zA-Z]{2,3}$", message = "{field.exp.email}(ex. {filed.email.type01})")
    private String email;

    @Parameter(name="IP주소", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}$", message = "{field.exp.ip}(ex. {filed.ip.type01})")
    private String ip;

    @Parameter(name="폰번호", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^01([0|1|6|7|8|9])-?([0-9]{3,4})-?([0-9]{4})$", message = "{field.exp.phone}(ex. {filed.phone.type01})")
    private String phone;

    @Parameter(name="날짜", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$", message = "{field.exp.date}(ex. {filed.date.type01})")
    private String date;
}
