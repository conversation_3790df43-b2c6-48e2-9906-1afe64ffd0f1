package com.aidt.api.bc.chatbot.tcr;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.chatbot.dto.BcCbSnroDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-01 14:28:18
 * @modify date 2024-06-01 14:28:18
 * @desc BcChatBotStu Service 챗봇 서비스
 */

@Service
public class BcCbTcrService {
    
	private final String MAPPER_NAMESPACE = "api.bc.chatbot.";

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
    private CommonDao commonDao;
	
    /**
     * 챗봇 질문 내용 저장
     * 
     * @param saveDto 저장데이터 목록
     * @return
     */
    @Transactional
    public int saveSnroMasterData(List<BcCbSnroDto> saveDto) {
    	int cnt = 0;
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

    	for (BcCbSnroDto bcCbSnroDto : saveDto) {
    		bcCbSnroDto.setUsrId(userDetails.getUsrId());
    		bcCbSnroDto.setDbId(userDetails.getTxbId());
    		cnt += commonDao.insert(MAPPER_NAMESPACE + "insertSnroData", bcCbSnroDto);
    		
            for (String srhWd : bcCbSnroDto.getSrhWdList()){
            	if (!srhWd.trim().equals("")) {
                	Map<String, Object> test = Map.of("snroId", bcCbSnroDto.getSnroId(), 
        					"srhWd", srhWd.trim(), 
        					"usrId", bcCbSnroDto.getUsrId(), 
        					"dbId", bcCbSnroDto.getDbId()
        					);
                	cnt += commonDao.insert(MAPPER_NAMESPACE + "insertSnroDetailData", test);	
            	}
            }
		}
    	
    	return cnt;
    }
    
}
