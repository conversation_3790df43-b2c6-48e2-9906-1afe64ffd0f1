# 🔍 AIDT API-LM 프로젝트 분석 보고서

## 📋 프로젝트 개요

- **프로젝트명**: 천재교육 AIDT(AI디지털교과서) 학습관리 API
- **서비스명**: aidt-api-lm
- **버전**: 1.0.0-SNAPSHOT
- **설명**: 천재교육 AIDT(AI디지털교과서) 학습관리 API
- **개발사**: 천재교육
- **아키텍처**: RESTful API 서버 (마이크로서비스 아키텍처)

## 🏗️ 기술 스택

### Backend Framework
- **Java**: 11
- **Spring Boot**: 2.x
- **Spring Security**: JWT 기반 인증
- **Spring Cache**: 캐싱 지원
- **Maven**: 빌드 도구

### Database & Storage
- **Primary DB**: MySQL (HikariCP 커넥션 풀)
- **ORM**: MyBatis
- **Cache**: Redis (Lettuce 클라이언트)
- **Cloud Storage**: NCloud S3

### Message Queue & Communication
- **Message Queue**: Apache Kafka
- **HTTP Client**: WebFlux (비동기 통신)
- **API Documentation**: Swagger/OpenAPI 3

### Monitoring & DevOps
- **APM**: WhaTap
- **Containerization**: Docker (멀티 스테이지 빌드)
- **Logging**: Logback + SLF4J

## 🎯 주요 기능 도메인

### 1. AI 맞춤학습 (AL - AI Learning)

#### 개인화 학습 추천
- 학습자 수준별 콘텐츠 추천
- AI 기반 학습 경로 생성
- 학습 진도 관리 및 추적

#### 주요 컨트롤러
- `AlLrnwController`: 학습창 연계 처리
- `AlPlStuCtnController`: 학생용 콘텐츠 관리
- `AlPlTcrCtnController`: 교사용 콘텐츠 관리

#### 핵심 서비스
- `AiQuestionTopicProfileLoadService`: AI 문제 토픽 프로파일 로드
- `AlMgService`: 학습맵 관리
- `AiRcmTsshQtmCommService`: AI 추천 문제 공통 서비스

### 2. AI 회화 (TK - Talk)

#### 실시간 AI 대화
- 아키핀(Archipin) API 연동
- 자연어 처리 기반 대화 시스템
- 회화 토픽별 시나리오 관리

#### 주요 기능
- `startDalogueApi`: 대화 시작
- `nextDalogueApi`: 대화 진행
- 실시간 대화 상태 관리

#### 외부 API 연동
```yaml
aidt.endpoint:
  archipinTalk: https://cht-dialog.aitextbook.co.kr:7182
```

### 3. AI 피드백 (FDBK - Feedback)

#### 개인화 피드백 생성
- AI 기반 학습 피드백
- 성취도 분석 및 랭킹
- 학습 성장률 추적

#### 주요 컴포넌트
- `AiFdbkStuService`: 학생 피드백 서비스
- `AiFdbkTcrService`: 교사 피드백 서비스
- `AiFdbkFixCmtService`: 고정 코멘트 서비스

### 4. AI 센터 연동

#### KT AI Center API 연동
- 정답 예측률 분석
- 학습자 프로파일 생성
- AI 기반 학습 수준 분석

#### 핵심 기능
- `AiCenterHelper`: AI 센터 API 헬퍼
- 문항별 정답 예측률 조회
- 학습자 AI 프로파일 업데이트

## 🗄️ 데이터베이스 구조

### 주요 테이블

#### 사용자 관리
- `CM_USR`: 사용자 기본 정보
- `CM_TOKEN`: 인증 토큰 정보
- `TEMP_LOGIN`: 임시 로그인 정보

#### 교과서 및 학습 관리
- `CM_OPT_TXB`: 교과서 옵션 정보
- `AI_KMMP_NOD_RCSTN`: AI 지식맵 노드 재구성
- `CM_NTLV_EDU_CRS_STN_SST`: 국가수준 교육과정 학생 통계

#### AI 학습 데이터
- AI 피드백 관련 테이블
- 학습 프로파일 테이블
- 문제 및 토픽 관리 테이블

### 데이터베이스 설정
```yaml
spring:
  datasource.hikari:
    driver-Class-Name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
    jdbc-url: **********************************************************************
    username: LRM_WAS
    maximum-pool-size: 200
```

## 🔧 시스템 설정

### 환경별 프로파일

#### Local 환경
- 로컬 개발 환경
- Kafka 비활성화
- 로컬 데이터베이스 연결

#### Dev 환경
- 개발 서버 환경
- 모든 외부 서비스 연동
- Redis 캐시 활성화

#### Staging/Production 환경
- 운영 환경 설정
- 성능 최적화 설정
- 모니터링 활성화

### 외부 서비스 연동

#### AI 서비스 엔드포인트
```yaml
aidt.endpoint:
  aicenter:
    url: https://knt-dev.aitextbook.co.kr/api/v1/predict/
    token: devkey1
  archipin: https://cht-tutor.aitextbook.co.kr:7261
  archipinTalk: https://cht-dialog.aitextbook.co.kr:7182
  archipinWrite: https://cht-correction.aitextbook.co.kr:7271
```

#### 클라우드 서비스
```yaml
ncloud:
  s3.bucket-name: lms-op-dev-sto-02
  s3.content-bucket-name: lms-op-dev-sto-02
```

## 🚀 배포 및 인프라

### Docker 컨테이너화

#### 멀티 스테이지 빌드
- Base Stage: 공통 설정
- Dev Stage: 개발 환경
- Staging Stage: 스테이징 환경
- Production Stage: 운영 환경

#### JVM 설정
```dockerfile
ENV JVM_OPTS="-Xms2048m -Xmx4096m"
ENV JAVA_OPTS="$JVM_OPTS"
```

#### 모니터링 에이전트
- WhaTap APM 에이전트 포함
- 환경별 설정 파일 분리

### 쿠버네티스 배포
- 마이크로서비스 아키텍처
- 서비스 간 통신 설정
- 로드 밸런싱 및 스케일링

## 📊 주요 특징

### 1. AI 기반 개인화 학습
- 학습자별 맞춤형 콘텐츠 추천
- 실시간 학습 진도 추적
- 적응형 학습 경로 제공

### 2. 실시간 AI 대화 시스템
- 자연어 처리 기반 대화
- 교육 맥락에 특화된 AI 응답
- 다양한 회화 시나리오 지원

### 3. 마이크로서비스 아키텍처
- 독립적인 서비스 단위 구성
- 확장성 및 유지보수성 향상
- 서비스 간 느슨한 결합

### 4. 멀티 테넌트 지원
- 교과서별 데이터 분리
- 클래스별 권한 관리
- 사용자 유형별 기능 제공

### 5. 외부 API 연동
- KT AI Center 연동
- 아키핀 AI 서비스 연동
- KERIS 교육정보 연동

### 6. 실시간 데이터 처리
- Kafka 기반 이벤트 스트리밍
- 비동기 메시지 처리
- 실시간 알림 및 업데이트

## 🔐 보안 및 인증

### JWT 기반 인증
- 토큰 기반 무상태 인증
- 사용자 권한별 접근 제어
- 토큰 갱신 및 만료 관리

### 사용자 권한 관리
- **ST (Student)**: 학생 권한
- **TE (Teacher)**: 교사 권한
- **PA (Parent)**: 학부모 권한
- **AD (Admin)**: 관리자 권한

### KERIS 연동
- 한국교육학술정보원 연동
- 교육 데이터 표준 준수
- 개인정보 보호 정책 적용

## 📈 성능 및 모니터링

### 캐싱 전략
- Redis 기반 분산 캐시
- 자주 조회되는 데이터 캐싱
- 캐시 무효화 전략

### 모니터링
- WhaTap APM을 통한 성능 모니터링
- 로그 기반 오류 추적
- 메트릭 수집 및 분석

### 성능 최적화
- 커넥션 풀 최적화 (HikariCP)
- 비동기 처리 (WebFlux)
- 데이터베이스 쿼리 최적화

## 🔄 개발 및 운영 프로세스

### 개발 환경
- Maven 기반 빌드
- 프로파일별 설정 관리
- 자동화된 테스트 환경

### CI/CD
- Docker 기반 컨테이너화
- 환경별 자동 배포
- 무중단 배포 지원

### 코드 품질 관리
- 표준화된 코딩 컨벤션
- API 문서 자동 생성 (Swagger)
- 로깅 및 오류 처리 표준화

---

## 📝 결론

AIDT API-LM 프로젝트는 **AI 기반 디지털 교과서 플랫폼의 핵심 학습관리 API**로, 다음과 같은 특징을 가집니다:

- **현대적인 기술 스택**: Spring Boot, 마이크로서비스, 컨테이너화
- **AI 기반 개인화**: 학습자별 맞춤형 교육 서비스
- **확장 가능한 아키텍처**: 마이크로서비스 기반 설계
- **교육 특화 기능**: 교육 도메인에 특화된 AI 서비스
- **안정적인 운영**: 모니터링, 로깅, 보안 체계 구축

이 시스템은 전통적인 교육 방식을 디지털 전환하여, AI 기술을 활용한 개인화된 학습 경험을 제공하는 혁신적인 교육 기술 솔루션입니다.
