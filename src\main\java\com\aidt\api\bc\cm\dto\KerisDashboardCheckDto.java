package com.aidt.api.bc.cm.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

@Data
public class KerisDashboardCheckDto {

	@Parameter(name = "keris api 데이터 수집 구분", required = true)
	private String act_type;

	@Parameter(name = "keris user_id", required = true)
	private String user_id;

	@Parameter(name = "keris curriculum", required = true)
	private String curriculum;
	
	@Parameter(name = "keris score", required = false)
	private String score;
	
	@Parameter(name = "keris achievement_level", required = false)
	private String achievement_level;
}
