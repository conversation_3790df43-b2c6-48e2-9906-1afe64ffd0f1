package com.aidt.api.sl.lrnwif.dto;

import java.util.List;

import com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-07 14:13:26
 * @modify : date 2024-03-07 14:13:26
 * @desc : SlLrnwTocAtvDto 특별학습 콘텐츠 활동 조회 
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwTocAtvDto {
	
	@Parameter(name="특별학습ID")
	private String lluNodId;

	@Parameter(name="특별학습 상위노드ID")
	private String tcNodId;

	/* 학습한 이력이 없는 경우 첫번째 CtnId 마지막으로 학습한 콘텐츠ID가 학습완료인 경우는 다음 콘텐츠 ID를 설정*/
	@Parameter(name="시작특별학습콘텐츠ID")
	private String strAtvId;
	
	@Parameter(name="특별학습명")
	private String lluNodNm;

	@Parameter(name="특별학습노드명")
	private String tcNodNm;

	@Parameter(name="콘텐츠 총건수")
	private int atvTotCnt;
	
    @Parameter(name="학습도구")
    private List<SlLrnwLrnTlDto> lrnTlList;
    
    @Parameter(name="콘텐츠목록")
    private List<SlLrnwAtvDto> lrnStpList;

    /** 외부활동설정 */
    @Parameter(name="외부활동설정")
    private TlLrnwExtAtvSetm extAtvSetm;
	
}
