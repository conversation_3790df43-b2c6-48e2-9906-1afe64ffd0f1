package com.aidt.api.ea.evcom.ev.dto;

import com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-13
 * @modify date 2024-03-13
 * @desc 평가 관리 - 평가리포트 - 학생 현황 리스트 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvRptUsrsOxDto {

	@Parameter(name="우리반 문항별 평균 데이터 리스트")
	private List<EaEvRptUsrsQtmResDto> clsQtmAvgList;
	
	@Parameter(name="사용자별 문항 답변 리스트")
	private List<EaEvComQtmAnwDto> usrQtmAnwList;
}
