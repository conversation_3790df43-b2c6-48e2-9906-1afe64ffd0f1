package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21
 * @modify date 2024-05-21
 * @desc 평가 관리 - 채점완료 - 문항 답변리스트 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlPlEaEvMainReportDto {
	
	/* 평가 정보 */
	@Parameter(name="평가ID")
	private long evId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="평가명")
	private String evNm;
	
	@Parameter(name="편집 그룹 순번")
	private int editGrpOrdn;
	
	@Parameter(name="편집 그룹 순번별 번호")
	private int editGrpOrdnRowNo;
	
	@Parameter(name="평가구분코드")
	private String evDvCd;
	
	@Parameter(name="평가구분명")
	private String evDvNm;
	
	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;
	
	@Parameter(name="평가상세구분명")
	private String evDtlDvNm;
	
	@Parameter(name="평가상세구분Class명")
	private String evDtlDvCssNm;

	@Parameter(name="평가활성여부")
	private String evActvYn;

	@Parameter(name="응시상태명")
	private String txmStNm;	

	@Parameter(name="응시기간설정여부")
	private String txmPtmeSetmYn;
	
	@Parameter(name="응시시작일시")
	private String txmStrDtm;
	
	@Parameter(name="응시종료일시")
	private String txmEndDtm;
	
	@Parameter(name="응시시작일시 캘린더용")
	private String txmStrDtmCalender;
	
	@Parameter(name="응시종료일시 캘린더용")
	private String txmEndDtmCalender;
	
	@Parameter(name="풀이시간설정여부")
	private String xplTmSetmYn;
	
	@Parameter(name="풀이시간초수(분 제외한 초수)")
	private int xplTmScnt;
	
	@Parameter(name="풀이시간분")
	private int xplTmMi;		
	
	@Parameter(name="평균풀이시간초수")
	private int avgXplTmScnt;
	
	@Parameter(name="평균풀이시간분")
	private int avgXplTmMi;	
	
	@Parameter(name="문제수")
	private int qstCnt;
	
	@Parameter(name="최종 문제수")
	private int fnlQstCnt;
	
	@Parameter(name="잠금여부")
	private String lcknYn;
	
	@Parameter(name="재응시허용여부")
	private String rtxmPmsnYn;
	
	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="생성자ID")
	private String crtrId;
	
	@Parameter(name="생성일시")
	private String crtDtm;
	
	@Parameter(name="수정자ID")
	private String mdfrId;
	
	@Parameter(name="수정일시")
	private String mdfDtm;
	
	@Parameter(name="데이터베이스ID")
	private String dbId;
	
	@Parameter(name="학습활동 엑티비티ID")
	private String lrnAtvId;	
	
	/* 시험범위(단원) 정보 */	
	@Parameter(name="대단원학습맵노드ID")
	private String luLrmpNodId;
	
	@Parameter(name="대단원학습맵노드명")
	private String luLrmpNodNm;	
	
	@Parameter(name="차시학습맵노드ID")
	private String tcLrmpNodId;
	
	@Parameter(name="차시학습맵노드명")
	private String tcLrmpNodNm;	

	/* 응시 정보 */	
	@Parameter(name="응시회차")
	private int txmPn;
	
	@Parameter(name="응시시작여부")
	private String txmStrYn;
	
	@Parameter(name="평가완료여부")
	private String evCmplYn;

	@Parameter(name="제출일시")
	private String smtDtm;

	@Parameter(name="전체학습생수")
	private int allStuCnt;
	
	@Parameter(name="응시완료자수")
	private int txmCmplCnt;
	
	@Parameter(name="정답개수")
	private int cansCnt;
	
	@Parameter(name="정답률") 
	private String cansRt;		
	
	@Parameter(name="평균정답률") 
	private String avgCansRt;			

	@Parameter(name="평균정답개수")
	private String avgCansCnt;

//	/* 문항 정보 */
//	@Parameter(name="문항 리스트")
//	private List<EaEvComQtmInfoDto> qtmIdList;	
	
	/* 문항별 답변 정보 정보 */	
	@Parameter(name="문항별 답변 정보 리스트")
	private List<AlPlEaEvComQtmAnwDto> qtmAnwList;		
	
	/* "강점/약점 정보 - 학생 */	
	@Parameter(name="강점/약점 리스트 - 학생")
	private List<AlPlEaEvComQtmAnwDto> stpnWkpnStuList;	
	
//	/* "오답 BEST 정보 */	
//	@Parameter(name="오답 BEST 리스트")
//	private List<EaEvRptUsrsQtmResDto> qtmIansBestList;	
//	
//	/* "강점/약점 정보 */	
//	@Parameter(name="강점/약점 리스트")
//	private List<EaEvRptUsrsQtmResDto> stpnWkpnList;
//	

//	
//	/* "정오답 현황 정보 */	
//	@Parameter(name="정오답 현황 리스트")
//	private List<EaEvRptUsrsQtmResDto> qtmOxList;	
//	
//	/* 학생별 현황 정보 */	
//	@Parameter(name="학생별 현황 리스트")
//	private List<EaEvRptUsrsQtmResDto> qtmUsrRptList;	
//	
//	/* 평가난이도 정보 */
//	@Parameter(name="평가난이도 리스트")
//	private List<EaEvDffdCstnDto> dffdList;
//
//	/* 시험범위 정보 */
//	@Parameter(name="시험번위 리스트")
//	private List<EaEvTsRngeDto> tsRngeList;
	
	private int totalCnt;
	
	/* 차시별 정답률 정보 */
	@Parameter(name="강점/약점 리스트 - 학생")
	private List<AlPlEaEvComQtmAnwDto> tcStuRptList;	
	
	@Parameter(name="과목 코드")
	private String sbjCd;
	
	@Parameter(name="평가ID 배열")
	private String[] evIds;
}
