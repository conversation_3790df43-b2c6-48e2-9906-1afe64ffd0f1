<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.sl.cm.stu">
    <!-- 공통 특별학습 리스트-->
	<select id="selectSlCmSpLrnList" parameterType="com.aidt.api.sl.cm.dto.SlCmSrhDto" resultType="com.aidt.api.sl.cm.dto.SlCmSpLrnDto">
		SELECT
        	M.OPT_TXB_ID,                 /* 운영교과서ID */
        	M.SP_LRN_ID,                  /* 특별학습ID */
        	M.SP_LRN_NM,                  /* 특별학습명 */
        	M.RCSTN_ORDN,                 /* 재구성순서 */
            S.MO_PATH,                    /* 모바일 썸네일 PATH */ 
            S.TA_PATH,                    /* 태블릿 썸네일 PATH */
            S.PC_PATH                     /* PC 썸네일 PATH */
        FROM LMS_LRM.SL_SP_LRN_RCSTN M /* SL_특별학습재구성 */
            LEFT JOIN (
					SELECT    
						 M.SP_LRN_ID                                               		  /*특별학습 ID */
						,IFNULL(UF1.FLE_PTH_NM,'')					AS MO_PATH            /*모바일 썸네일 PATH */
						,IFNULL(UF2.FLE_PTH_NM,'') 					AS TA_PATH            /*태블릿 썸네일 PATH */
						,IFNULL(UF3.FLE_PTH_NM,'') 					AS PC_PATH            /*피씨 썸네일 PATH */
					FROM LMS_LRM.SL_SP_LRN_RCSTN M /* SL_특별학습재구성 */
						LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM1 /* BC_특별학습썸네일파일매핑 */
							ON M.SP_LRN_ID = FM1.SP_LRN_ID
							AND FM1.THB_TML_TP_CD = 'MO'
						LEFT JOIN LMS_CMS.BC_UPL_FLE UF1 /* BC_업로드파일 */
							ON FM1.UPL_FLE_ID = UF1.UPL_FLE_ID 
							AND UF1.DEL_YN = 'N'
						LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM2 /* BC_특별학습썸네일파일매핑 */
							ON M.SP_LRN_ID = FM2.SP_LRN_ID
							AND FM2.THB_TML_TP_CD = 'TA'
						LEFT JOIN LMS_CMS.BC_UPL_FLE UF2 /* BC_업로드파일 */
							ON FM2.UPL_FLE_ID = UF2.UPL_FLE_ID 
							AND UF2.DEL_YN = 'N'
						LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN FM3 /* BC_특별학습썸네일파일매핑 */
							ON M.SP_LRN_ID = FM3.SP_LRN_ID
							AND FM3.THB_TML_TP_CD = 'PC'
						LEFT JOIN LMS_CMS.BC_UPL_FLE UF3 /* BC_업로드파일 */
							ON FM3.UPL_FLE_ID = UF3.UPL_FLE_ID 
							AND UF3.DEL_YN = 'N'			
					WHERE  M.OPT_TXB_ID = #{optTxbId}
					  AND M.USE_YN ='Y'
			) S ON S.SP_LRN_ID = M.SP_LRN_ID
			LEFT JOIN LMS_LRM.SL_STU_RCM_LRN R /* SL_학생별추천학습 */
				ON M.OPT_TXB_ID = R.OPT_TXB_ID
				AND M.SP_LRN_ID = R.SP_LRN_ID
				AND R.LRN_USR_ID = #{lrnUsrId}
        WHERE M.OPT_TXB_ID = #{optTxbId}
          AND M.USE_YN = 'Y'
          AND R.RCM_YN = 'Y'
        ORDER BY RCSTN_ORDN ASC, SP_LRN_ID ASC
        <if test='spLrnMax != null and spLrnMax neq "" and spLrnMax > 0'>  /* 최대개수 여부 */
            LIMIT #{spLrnMax}
         </if>
		/** 특별학습 김형준 SlCmStu-Mapper.xml - selectSlCmSpLrnList */
	</select>
</mapper>