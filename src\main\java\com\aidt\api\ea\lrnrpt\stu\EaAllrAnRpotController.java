/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 03:06:03
 * @modify 2024. 6. 22.
 * @desc 
 */
package com.aidt.api.ea.lrnrpt.stu;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.h2.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.claan.dto.BcClaanReqDto;
import com.aidt.api.bc.claan.dto.BcClaanResDto;
import com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto;
import com.aidt.api.ea.evcom.lrnRpt.dto.LrnPtrnDto;
import com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotDto;
import com.aidt.api.ea.lrnrpt.dto.EaAllrAnRpotSrhDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlTcpRcmCtnDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 학습리포트 > 종합분석
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 15:06:03
 * @modify 2024-06-22 15:06:03
 * @desc 
 */

@Slf4j
@Tag(name="[ea] 학습리포트", description="학습리포트 > 종합분석")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/stu/lrnrpt")
public class EaAllrAnRpotController {
	
	@Autowired 
    private JwtProvider jwtProvider;
	 
    @Autowired    
    EaAllrAnRpotService eaAllrAnRpotService;
    
    @Operation(summary="학습 리포트 조회", description="선생,학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectEaLrnRptMainInfo")
    public ResponseDto<Map<String,Object>> selectEaLrnRptMainInfo(@RequestBody EaLrnRptReqDto param) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
        // 세션정보에서 설정
    	param.setOptTxbId(userDetails.getOptTxbId());
    	param.setUsrId(userDetails.getUsrId());
        return Response.ok(eaAllrAnRpotService.selectEaLrnRptMainInfo(param));
    }
    
    @Operation(summary="학습 리포트 - 선호도 조회", description="학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectLrnPrefInfo")
    public ResponseDto<Map<String,Object>> selectEaLrnPrefInfo(@RequestBody EaLrnRptReqDto param) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	param.setOptTxbId(userDetails.getOptTxbId());
    	param.setUsrId(userDetails.getUsrId());
    	return Response.ok(eaAllrAnRpotService.selectLrnPref(param));
    }
    
    @Operation(summary="학습 리포트 - 학습 챌린지 조회", description="학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectEaLrnChalInfo")
    public ResponseDto<Map<String,Object>> selectEaLrnChalInfo(@RequestBody EaLrnRptReqDto param) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	param.setOptTxbId(userDetails.getOptTxbId());
    	param.setUsrId(userDetails.getUsrId());
    	return Response.ok(eaAllrAnRpotService.selectLrnChal(param));
    }
    
    @Operation(summary="학습 리포트 - 학습코칭 : 과제 제출내역 조회", description="학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectEaLrnAsnInfo")
    public ResponseDto<Map<String,Object>> selectEaLrnAsnInfo(@RequestBody EaLrnRptReqDto param) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	param.setOptTxbId(userDetails.getOptTxbId());
    	param.setUsrId(userDetails.getUsrId());
    	param.setClaId(userDetails.getClaId());
    	return Response.ok(eaAllrAnRpotService.selectEaLrnAsnInfo(param));
    }
    
    @Operation(summary="학습 리포트 - 학습 성장 분석 조회", description="학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectEvLrnGrowAnList")
    public ResponseDto<List<LrnPtrnDto>> selectEvLrnGrowAnList(@RequestBody EaLrnRptReqDto param) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	param.setUsrId(userDetails.getUsrId());
    	return Response.ok(eaAllrAnRpotService.selectEvLrnGrowAnList(param));
    }
    
    @Operation(summary="학습 리포트 - 시간대별 분석", description="학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectAnPerTm")
    public ResponseDto<List<LrnPtrnDto>> selectAnPerTm(@RequestBody EaLrnRptReqDto param) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	param.setOptTxbId(userDetails.getOptTxbId());
    	param.setUsrId(userDetails.getUsrId());
    	return Response.ok(eaAllrAnRpotService.selectAnPerTm(param));
    }
    
    @Operation(summary="학습 리포트 - 요일별 분석 조회", description="학생 공통 학습 리포트 조회")
    @PostMapping(value="/selectAnPerWeekInfo")
    public ResponseDto<List<LrnPtrnDto>> selectAnPerWeekInfo(@RequestBody EaLrnRptReqDto param) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
    	param.setOptTxbId(userDetails.getOptTxbId());
    	param.setUsrId(userDetails.getUsrId());
        
        return Response.ok(eaAllrAnRpotService.selectAnPerWeekInfo(param));
    }
    
    
    @Operation(summary="단원별 성취 현황 목록 조회", description="단원별 성취 현황 목록 조회")
    @PostMapping(value = "/selectAllAnLuList")
    public ResponseDto<List<EaAllrAnRpotDto>> selectAllAnLuList(@RequestBody EaAllrAnRpotSrhDto srhDto) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        
        return Response.ok(eaAllrAnRpotService.selectAllAnLuList(srhDto));
    }
    
    @Operation(summary = "평가별 분석 목록 조회", description = "평가별 분석 목록 조회")
    @PostMapping(value = "/selectAllAnEvList")
    public ResponseDto<List<EaAllrAnRpotDto>> selectAllAnEvList(@RequestBody EaAllrAnRpotSrhDto srhDto) {

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        
        return Response.ok(eaAllrAnRpotService.selectAllAnEvList(srhDto));
    }
    
    @Operation(summary = "영역별 분석 목록 조회", description = "평가별 분석 목록 조회")
    @PostMapping(value = "/selectAllAnAraList")
    public ResponseDto<List<EaAllrAnRpotDto>> selectAllAnAraList(@RequestBody EaAllrAnRpotSrhDto srhDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	srhDto.setUsrId(userDetails.getUsrId());
    	
    	return Response.ok(eaAllrAnRpotService.selectAllAnAraList(srhDto));
    }
    
    
    
    @Operation(summary = "차시별 분석 목록 조회", description = "차시별 분석 목록 조회")
    @PostMapping(value = "/selectAllAnTcList")
    public ResponseDto<List<EaAllrAnRpotDto>> selectAllAnTcList(@RequestBody EaAllrAnRpotSrhDto srhDto) {

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        
        return Response.ok(eaAllrAnRpotService.selectAllAnTcList(srhDto));
    }
    
    @Operation(summary = "성취별 분석 목록 조회", description = "성취별 분석 목록 조회")
    @PostMapping(value = "/selectAllAnAchList")
    public ResponseDto<List<EaAllrAnRpotDto>> selectAllAnAchList(@RequestBody EaAllrAnRpotSrhDto srhDto) {

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        
        return Response.ok(eaAllrAnRpotService.selectAllAnAchList(srhDto));
    }
    
    @Operation(summary = "AI 분석 목록 조회", description = "AI 분석 목록 조회")
    @PostMapping(value = "/selectAllAnAiList")
    public ResponseDto<List<EaAllrAnRpotDto>> selectAllAnAiList(@RequestBody EaAllrAnRpotSrhDto srhDto) {

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        
        return Response.ok(eaAllrAnRpotService.selectAllAnAiList(srhDto));
    }
    
    @Operation(summary = "추천학습 조회", description = "추천학습 조회")
    @PostMapping(value = "/selectTpcRcmCtnList")
    public ResponseDto<List<EaLrnRptAlTcpRcmCtnDto>> selectTpcRcmCtnList(@RequestBody EaAllrAnRpotSrhDto srhDto) {

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        if(StringUtils.isNullOrEmpty(srhDto.getUsrId())) {
    		srhDto.setUsrId(userDetails.getUsrId());
    	}
        
        return Response.ok(eaAllrAnRpotService.selectTpcRcmCtnList(srhDto));
    }
    
    @Operation(summary = "오답노트 갯수 조회", description = "오답노트 갯수 조회")
    @PostMapping(value = "/selectIansCnt")
    public ResponseDto<LrnPtrnDto> selectIansCnt(@RequestBody EaAllrAnRpotSrhDto srhDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	// 세션정보에서 설정
    	srhDto.setOptTxbId(userDetails.getOptTxbId());
    	if(StringUtils.isNullOrEmpty(srhDto.getUsrId())) {
    		srhDto.setUsrId(userDetails.getUsrId());
    	}
    	
    	return Response.ok(eaAllrAnRpotService.selectIansCnt(srhDto));
    }
    
    /**
     * 내용체계영역별 성취 현황
     * @param AlWrtReqDto - 운영교과서ID, 학교코드, 학년코드, 학교급코드
     * @return ResponseDto<List<AlWrtResDto>> - 내용체계영역별 성취 현황
     */
    @Operation(summary="내용체계영역별 성취 현황")
    @PostMapping(value = "/selectCrsCnAchPerStuOrTcrList")
    public ResponseDto<List<BcClaanResDto>> selectCrsCnAchPerStuOrTcrList(@Valid @RequestBody BcClaanReqDto dto) {
    	
    	if (dto == null 
    			|| StringUtils.isNullOrEmpty(dto.getOptTxbId())
    			|| StringUtils.isNullOrEmpty(dto.getSchlCd())
    			|| StringUtils.isNullOrEmpty(dto.getSgyCd())
    			|| StringUtils.isNullOrEmpty(dto.getSchlGrdCd())
    	) {
    		return Response.fail("Params does not exist");
    	}
    	
    	

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	dto.setUsrTpCd(userDetails.getUsrTpCd());
    	if(StringUtils.isNullOrEmpty(dto.getUsrId())) {
    		dto.setUsrId(userDetails.getUsrId());
    	}
    	
        return Response.ok(eaAllrAnRpotService.selectCrsCnAchPerStuOrTcrList(dto));
    }
    
    
}
