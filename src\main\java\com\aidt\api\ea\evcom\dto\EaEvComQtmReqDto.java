package com.aidt.api.ea.evcom.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 13:58:오후 1:58
 * @modify date 2024-03-05 13:58:오후 1:58
 * @desc   평가 공통 - 문항리스트 조회 요청 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvComQtmReqDto {

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="과목CD")
    private String sbjCd;

    @Parameter(name="DB ID")
    private String dbId;

    @Parameter(name="평가 ID")
    private long evId;

    @Parameter(name="탭인덱스")
    private int tabIndex;

    @Parameter(name="기존 평가 ID")
    private long oldEvId;

    @Parameter(name="응시 회차")
    private int txmPn;

    @Parameter(name="재응시 회차")
    private int rtxmPn;

    @Parameter(name="문항 ID")
    private long qpQtmId;

	@Parameter(name="문제수")
	private int qstCnt;

    @Parameter(name="학기구분코드")
    private String trmDvCd;
    
    @Parameter(name="정답,해설,해석,힌트 노출 여부")
    private String lrnwEvYn;

    @Parameter(name="교과학습 여부")
    private String sbcLrnYn;

    @Parameter(name="심화 평가 생성 여부")
    private String ntnEvCrtYn;

    @Parameter(name="오답개수")
    private int iansCnt;

	@Parameter(name="버킷url")
	private String bucketUrl;

	@Parameter(name="성취기준ID")
	private String eduAchBsId;

	@Parameter(name="내용영역CD")
	private String eduAreaCd;
	
    /* 문항ID 리스트 */
    @Parameter(name="문항ID 리스트")
    List<EaEvComQtmReqDto> qpQtmList;

    @Parameter(name = "메모첨부파일ID")
    private long annxFleId;    
    
    @Parameter(name="문항노트파일경로명")
    String annxFlePthNm;

    @Parameter(name = "풀이상태코드")
    private String xplStCd;

    @Parameter(name = "힌트확인여부")
    private String hntCofmYn;    
    
	@Parameter(name="토픽ID_지식맵")
	private String tpcId;   
	
	@Parameter(name="오답심화생성여부")
	private String sppNtnYn;

	@Parameter(name="사용자유형")
	private String usrTpCd;
}
