package com.aidt.api.at.err.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "CM_WEB_SOCKET_LOG")
public class WebSocketLogDto {

	@Parameter(name = "운영교과서ID", required = true)
	private String optTxbId;

	@Parameter(name = "사용자ID", required = true)
	private String usrId;

	@Parameter(name = "KERIS사용자ID", required = true)
	private String kerisUsrId;

	@Parameter(name = "메시지유형코드", required = true)
	private String msgTpCd;

	@Parameter(name = "세션ID", required = true)
	private String sessionId;

	@Parameter(name = "결과코드", required = true)
	private String rsCd;

	@Parameter(name = "결과메시지", required = true)
	private String rsMsg;

	@Parameter(name = "접속URL", required = true)
	private String connUrl;

	@Parameter(name = "BODY내용", required = false)
	private String bodyCn;

	@Parameter(name = "사용자브라우저정보", required = true)
	private String usrBrwsInfo;

	@Parameter(name = "사용자IP", required = false)
	private String usrIp;

}
