package com.aidt.api.bc.cm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "CM_KERIS_FRONT_요청_로그")
public class BcCmKerisFrontReqLogDto {

	@Schema(description = "로그ID")
	private Long logId;
	@Schema(description = "사용자ID")
	private String usrId;
	@Schema(description = "교육과정콘텐츠2단계코드")
	private String crclCtnElm2Cd;
	@Schema(description = "학습사용자ID")
	private String lrnUsrId;
	@Schema(description = "운영교과서ID")
	private String optTxbId;
	@Schema(description = "행동코드")
	private String actCd;
	@Schema(description = "결과여부")
	private String rsYn;
	@Schema(description = "결과메시지")
	private String rsMsg;
}
