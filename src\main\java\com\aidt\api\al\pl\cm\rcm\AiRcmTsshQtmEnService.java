package com.aidt.api.al.pl.cm.rcm;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmResponseDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-28
 * @desc AI맞춤 문항추천 - 영어
 */
@Slf4j
@Service
public class AiRcmTsshQtmEnService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.back.aiRcmTsshQtm.";
	
	@Autowired private CommonDao commonDao;
	@Autowired private AiRcmTsshQtmCommService commService;
	
	
	/**
     * AI 맞춤 문항추천 - 초등영어 (진단,맞춤1,2,3)
     * 
     * @param dto
     * @return AiRcmTsshQtmResponseDto
     */
	public AiRcmTsshQtmResponseDto getEschEnQtmList(AiRcmTsshQtmReqDto dto){
		/* 운영교과서에 맞는 지식맵 노드 id가 파라미터 값으로 넘어왔는지 검증(2024.10.30) */
		AiRcmTsshQtmResponseDto responseDto = new AiRcmTsshQtmResponseDto();
		dto.setKmmpNodId(dto.getMluKmmpNodId());
		int nodeCount = commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodCount", dto);
		if (nodeCount < 1) {
			responseDto.setErrorStatus("error");
			responseDto.setErrorMessage("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
			return responseDto;
//			throw new Error("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
		}

		
		//유사문항 추천여부
		Boolean SmlrYn = false;
		
		//진단평가는 건너뛰고 바로 문항조회
		if(dto.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
			dto.setEschEnSetDv("AI");
		}else {
			//정답수기준(기본)
			int cansBs = AlConstUtil.CANS_BS_ESCH_EN;
			//정답수기준(파닉스)
			if(AlConstUtil.PHONICS_KMMP_NOD_ID_STR.contains(dto.getTcKmmpNodId())) {
				cansBs =  AlConstUtil.CANS_BS_ESCH_EN_PHONICS;
			}
			//정답수기준(알파벳)
			if(AlConstUtil.ALPHABET_KMMP_NOD_ID_STR.contains(dto.getTcKmmpNodId())) {
				cansBs =  AlConstUtil.CANS_BS_ESCH_EN_ALPHABET;
			}
			
			//학습자 수준 조회
	        String lrnrVelTpCd = commonDao.select(MAPPER_NAMESPACE + "selectLrnrVelTpCd", dto);
	        if(lrnrVelTpCd == null) {lrnrVelTpCd = "NM";}
	        dto.setLrnrVelTpCd(lrnrVelTpCd);
	        
	        //맞춤학습1
	        if(dto.getEvDtlDvCd().equals("C1")) {
	        	if(lrnrVelTpCd.equals("FS")) {//빠른
	        		dto.setEschEnSetDv("AT");
	        	}
	        	else if(lrnrVelTpCd.equals("NM")) {//보통
	        		dto.setEschEnSetDv("BT");
	        	}
	        	else if(lrnrVelTpCd.equals("SL")) {//느린
	        		dto.setEschEnSetDv("CT");
	        	}
			}
	        //맞춤학습2
			else if(dto.getEvDtlDvCd().equals("C2")) {
				AiRcmTsshQtmDto req = new AiRcmTsshQtmDto();
				req.setEvDtlDvCd("C1");
				req.setMluKmmpNodId(dto.getMluKmmpNodId());
				req.setTcKmmpNodId(dto.getTcKmmpNodId());
				req.setUsrId(dto.getUsrId());
				req.setOptTxbId(dto.getOptTxbId());
				AiRcmTsshQtmDto c1 = commonDao.select(MAPPER_NAMESPACE + "selectEnTcCansYCntList", req);
				
				if(lrnrVelTpCd.equals("FS")) {//빠른
	        		dto.setEschEnSetDv("AT");
	        		SmlrYn = true;
	        	}
				else if(lrnrVelTpCd.equals("NM")) {//보통
					if(c1 != null && c1.getCansCnt() >= cansBs) {
						dto.setEschEnSetDv("AT");
					}else {
						dto.setEschEnSetDv("BT");
						SmlrYn = true;
					}
	        	}
				else if(lrnrVelTpCd.equals("SL")) {//느린
					if(c1 != null && c1.getCansCnt() >= cansBs) {
						dto.setEschEnSetDv("BT");
					}else {
						dto.setEschEnSetDv("CT");
						SmlrYn = true;
					}
	        	}
			}
//	        //도전학습
//			else if(dto.getEvDtlDvCd().equals("C3")) {
//				AiRcmTsshQtmDto req = new AiRcmTsshQtmDto();
//				req.setEvDtlDvCd("C1");
//				req.setMluKmmpNodId(dto.getMluKmmpNodId());
//				req.setTcKmmpNodId(dto.getTcKmmpNodId());
//				req.setUsrId(dto.getUsrId());
//				req.setOptTxbId(dto.getOptTxbId());
//				AiRcmTsshQtmDto c1 = commonDao.select(MAPPER_NAMESPACE + "selectEnTcCansYCntList", req);
//
//				req.setEvDtlDvCd("C2");
//				AiRcmTsshQtmDto c2 = commonDao.select(MAPPER_NAMESPACE + "selectEnTcCansYCntList", req);
//
//				//빠른 학습자에대한 처리 확인 필요
//				//dto.setEschEnSetDv("CT");
//
//				if(lrnrVelTpCd.equals("FS")) {//빠른
//
//
//					//단원완료처리
//					AiRcmTsshQtmDto comdto = new AiRcmTsshQtmDto();
//					comdto.setUsrId(dto.getUsrId());
//					comdto.setLuevCmplYn("Y");
//					comdto.setOptTxbId(dto.getOptTxbId());
//					comdto.setMluKmmpNodId(dto.getMluKmmpNodId());
//					comdto.setTcKmmpNodId(dto.getTcKmmpNodId());
//					updateLuevCmplYnQuery(comdto);
//					responseDto.setErrorStatus("error");
//					responseDto.setErrorMessage("빠른 학습자는 도전학습이 없습니다.");
//					return responseDto;
////	                throw new IllegalArgumentException("빠른 학습자는 도전학습이 없습니다.");
//				}
//
//				if(lrnrVelTpCd.equals("NM")) {//보통
//					if(c1 != null && c1.getCansCnt() >= cansBs) {
//						dto.setEschEnSetDv("AT");
//						SmlrYn = true;
//					}
//					else if(c2 != null && c2.getCansCnt() >= cansBs) {
//						dto.setEschEnSetDv("AT");
//					}else {
//						dto.setEschEnSetDv("AT");
//					}
//	        	}
//				else if(lrnrVelTpCd.equals("SL")) {//느린
//					if(c1 != null && c1.getCansCnt() >= cansBs) {
//						if(c2 != null && c2.getCansCnt() >= cansBs) {
//							dto.setEschEnSetDv("AT");
//						}else {
//							dto.setEschEnSetDv("BT");
//							SmlrYn = true;
//						}
//					}
//					else {
//						dto.setEschEnSetDv("BT");
//					}
//	        	}
//			}
	        log.debug("====================초등영어 문항세트: "+ dto.getEschEnSetDv());
		}
		//문항리스트
		 List<AiRcmTsshQtmDto> qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectEevshQtmList", dto);
		
		 //유사문항
		if(SmlrYn) {
			log.debug("초등영어 유사문항");
			Map<String, Object> params = new HashMap<>();
		    params.put("qtmList", qtmList);
		    params.put("rltQtmTpCd", "SI");
		    params.put("sbjGrd", "eschEn");
		    params.put("optTxbId", dto.getOptTxbId());
			List<AiRcmTsshQtmDto> smlrList = commonDao.selectList(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);
			qtmList = smlrList;
		}
		
		//EA_EV 평가 생성
		Integer evID = commService.insertEaEvHst(dto, qtmList);
		responseDto.setEvId(evID);
		
		return responseDto;
	}
	

	// 토픽완료여부 update
	public void updateLuevCmplYnQuery(AiRcmTsshQtmDto dto) {
		commonDao.update(MAPPER_NAMESPACE + "updateLuevCmplYn", dto);
	}
	
	/**
	 * 중고등영어 맞춤학습1 
	 * 
	 *  @param List<AiRcmTsshQtmDto>
	 *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
	 *  @return List<AiRcmTsshQtmDto>
	 * */
	public List<AiRcmTsshQtmDto> getMsHschEnC1QtmList(List<AiRcmTsshQtmDto> list, String lrnrVelTpCd, Integer rcmQtmCnt, AiRcmTsshQtmReqDto dto){
		List<AiRcmTsshQtmDto> resultList = new ArrayList<>();
		List<Integer> qtmCheck = new ArrayList<>();
		Set<String> tpcSet = new HashSet<String>();
		
		//문항리스트를 토픽별로 매핑
		Map<String, List<AiRcmTsshQtmDto>> tpcByListMap = list.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId));
		
		//총괄평가의 정오정보
		AiRcmTsshQtmReqDto temp = new AiRcmTsshQtmReqDto();
		temp.setOptTxbId(dto.getOptTxbId());
		temp.setUsrId(dto.getUsrId());
		temp.setMluKmmpNodId(dto.getMluKmmpNodId());
		temp.setTcKmmpNodId(dto.getTcKmmpNodId());
		temp.setEvDtlDvCd(AlConstUtil.EV_DTL_DV_CD_OV);
		List<AiRcmTsshQtmDto> ugCansList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmCansInfo", temp);
		
		//기존문항 중복방지
		for (AiRcmTsshQtmDto ug : ugCansList) {
			qtmCheck.add(ug.getQtmId());
			tpcSet.add(ug.getTpcKmmpNodId());
		}
		//토픽순서 랜덤정렬
		List<String> tpcSetList = new ArrayList<>(tpcSet);
        Collections.shuffle(tpcSetList);
		
		//문항추천
		for (int i = 0; i <= rcmQtmCnt; i++) {
	        //토픽 번갈아 추천 :: 같은토픽 내에서만 출제되는것 방지
			for (String tpc : tpcSetList) {
				//추천문항수 만족시 종료
				if(rcmQtmCnt <= resultList.size()) {
					break;
				}
				
				if(tpcByListMap.get(tpc) == null) {
					continue;
				}
				List<AiRcmTsshQtmDto> velList = new ArrayList<>(tpcByListMap.get(tpc));
				
				//빠른학습자 - '상'난이도 예측정답률 낮은순
				if(lrnrVelTpCd.equals("FS")) {
					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04"));
//					velList.sort(
//							Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt)
//							.thenComparing(Comparator.comparingDouble(AiRcmTsshQtmDto::getQtmId))
//							);
				}
				//보통학습자 - '중'난이도 예측정답률 낮은순
				else if(lrnrVelTpCd.equals("NM")) {
					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//					velList.sort(
//							Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt)
//							.thenComparing(Comparator.comparingDouble(AiRcmTsshQtmDto::getQtmId))
//							);
				}
				//느린학습자 - '하'난이도 예측정답률 높은순
				else if(lrnrVelTpCd.equals("SL")) {
					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02"));
//					velList.sort(
//							Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed()
//							.thenComparing(Comparator.comparingDouble(AiRcmTsshQtmDto::getQtmId))
//							);
				}
				//2024-08-10 중고등영어 맞춤학습1에 대해 난이도 내에서 문항을 1개 초과의 토픽에서 랜덤출제하도록 함(박지영팀장님확인)
				Collections.shuffle(velList);
				
				//문항추천
				for (AiRcmTsshQtmDto vel : velList) {
					if(!qtmCheck.contains(vel.getQtmId())) {
						resultList.add(vel);
						qtmCheck.add(vel.getQtmId());
						break;
					}
				}
			}
		}
		
		return resultList;
	}
	
	/**
	 * 중고등영어 맞춤학습2
	 * 
	 *  @param List<AiRcmTsshQtmDto>
	 *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
	 *  @return List<AiRcmTsshQtmDto>
	 * */
	public List<AiRcmTsshQtmDto> getMsHschEnC2QtmList(List<AiRcmTsshQtmDto> list, String lrnrVelTpCd, Integer rcmQtmCnt, AiRcmTsshQtmReqDto dto){
		List<AiRcmTsshQtmDto> resultList = new ArrayList<>();
		List<Integer> qtmCheck = new ArrayList<>();
		
		//진단평가 정오정보
		AiRcmTsshQtmReqDto temp = new AiRcmTsshQtmReqDto();
		temp.setOptTxbId(dto.getOptTxbId());
		temp.setUsrId(dto.getUsrId());
		temp.setMluKmmpNodId(dto.getMluKmmpNodId());
		temp.setTcKmmpNodId(dto.getTcKmmpNodId());
		temp.setEvDtlDvCd(AlConstUtil.EV_DTL_DV_CD_OV);
		List<AiRcmTsshQtmDto> ugCansList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmCansInfo", temp);
		//맞춤학습1 정오정보
		temp.setEvDtlDvCd("C1");
		List<AiRcmTsshQtmDto> c1CansList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvQtmCansInfo", temp);
		
		//기존문항 중복방지
		for (AiRcmTsshQtmDto ug : ugCansList) {
			qtmCheck.add(ug.getQtmId());
		}
		for (AiRcmTsshQtmDto c1 : c1CansList) {
			qtmCheck.add(c1.getQtmId());
		}
		
		for (AiRcmTsshQtmDto c1 : c1CansList) {
			//유사문항 조회
			Map<String, Object> params = new HashMap<>();
		    params.put("qtmId", c1.getQtmId());
		    params.put("rltQtmTpCd", c1.getCansYn().equals("Y") ? "SI" : "TW");
		    params.put("optTxbId", dto.getOptTxbId());
			AiRcmTsshQtmDto rltDto = commonDao.select(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);
			
			if(rltDto != null && !qtmCheck.contains(rltDto.getQtmId())) {
				resultList.add(rltDto);
				qtmCheck.add(rltDto.getQtmId());
			}
			//유사문항이 없는경우 해당 난이도에서 출제
			else {
				List<AiRcmTsshQtmDto> velList = new ArrayList<>(list);
				//빠른학습자 - 각 난이도에서 예측정답률 낮은순
				if(lrnrVelTpCd.equals("FS")) {
					if(c1.getCansYn().equals("Y")) {
						velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("05"));
					}else {
						velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
					}
					velList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
				}
				//보통학습자 - 각 난이도에서 예측정답률 낮은순
				else if(lrnrVelTpCd.equals("NM")) {
					if(c1.getCansYn().equals("Y")) {
						velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04"));
					}else {
						velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02"));
					}
					velList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
				}
				//느린학습자 - 각 난이도에서 예측정답률 높은순
				else if(lrnrVelTpCd.equals("SL")) {
					if(c1.getCansYn().equals("Y")) {
						velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
					}else {
						velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("01"));
					}
					velList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
				}
				//유사쌍둥이 없는경우 중복출제 방어
				for (AiRcmTsshQtmDto vel : velList) {
					if(!qtmCheck.contains(vel.getQtmId())) {
						resultList.add(vel);
						qtmCheck.add(vel.getQtmId());
						break;
					}
				}
				
			}//else end
		}//c1 end
		
		
		
//		for (AiRcmTsshQtmDto c1 : c1CansList) {
//			List<AiRcmTsshQtmDto> velList = new ArrayList<>(list);
//			
//			//빠른학습자 - 각 난이도에서 예측정답률 낮은순
//			if(lrnrVelTpCd.equals("FS")) {
//				if(c1.getCansYn().equals("Y")) {
//					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("05"));
//				}else {
//					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//				}
//				velList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//			}
//			//보통학습자 - 각 난이도에서 예측정답률 낮은순
//			else if(lrnrVelTpCd.equals("NM")) {
//				if(c1.getCansYn().equals("Y")) {
//					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("04"));
//				}else {
//					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("02"));
//				}
//				velList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
//			}
//			//느린학습자 - 각 난이도에서 예측정답률 높은순
//			else if(lrnrVelTpCd.equals("SL")) {
//				if(c1.getCansYn().equals("Y")) {
//					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("03"));
//				}else {
//					velList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getCtnDffdDvCd().equals("01"));
//				}
//				velList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//			}
//			
//			//문항 추천
//			if(!velList.isEmpty()) {
//				AiRcmTsshQtmDto qtmDto = new AiRcmTsshQtmDto();
//				for (int j = 0; j < velList.size(); j++) {
//					//추천문항수 만족시 종료
//					if(rcmQtmCnt <= resultList.size()) {
//						break;
//					}
//					
//					//유사문항 조회
//					Map<String, Object> params = new HashMap<>();
//				    params.put("qtmId", velList.get(j).getQtmId());
//				    params.put("rltQtmTpCd", c1.getCansYn().equals("Y") ? "SI" : "TW");
//				    params.put("optTxbId", dto.getOptTxbId());
//					AiRcmTsshQtmDto rltDto = commonDao.select(MAPPER_NAMESPACE + "selectBcRltQtmMpnList", params);
//					
//					//유사문항이 없는경우 원천문항 출제
//					if(rltDto == null) {
//						if(!qtmCheck.contains(velList.get(j).getQtmId())) {
//							resultList.add(velList.get(j));
//							qtmCheck.add(velList.get(j).getQtmId());
//							break;
//						}
//					}else {
//						if(!qtmCheck.contains(rltDto.getQtmId())) {
//							resultList.add(rltDto);
//							qtmCheck.add(rltDto.getQtmId());
//							break;
//						}
//					}
//					
//				}
//				
//			}
//			
//		}//for end
		
		return resultList;
	}
	
//	/**
//	 * 중고등영어 집중학습(오답재출제)
//	 * 
//	 *  @param List<AiRcmTsshQtmDto>
//	 *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
//	 *  @return List<AiRcmTsshQtmDto>
//	 * */
//	public List<AiRcmTsshQtmDto> getMsHschEnC3QtmList(AiRcmTsshQtmReqDto dto){
//		//TODO 맞춤1,맞춤2의 오답만 나오도로고 수정하기
//		List<AiRcmTsshQtmDto> qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectIansQtmList", dto);
//		qtmList.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
//		return qtmList;
//	}
	
}
