package com.aidt.api.al.fdbk.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:56:58
 * @modify date 2024-07-11 15:56:58
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiFdbkBsDataDto {

    private String usrId;

    private int aiFdbkBsDataId;

    private List<String> aiFdbkNoList;

    private String aiFdbkDvCd;

    private String fdbkCn;

}
