package com.aidt.api.at.token.dto;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "CM_운영교과서_교시")
public class OptTxbPridDto {

	@Schema(description = "운영교과서ID")
	private String optTxbId;
	@Schema(description = "운영교과서_교시")
	private String optTxbPrid;
	@Schema(description = "KERIS강의코드")
	private String kerisLectCd;
	@Schema(description = "생성자ID")
	private String crtrId;
	@Schema(description = "생성자ID")
	private LocalDateTime regDtm;
}
