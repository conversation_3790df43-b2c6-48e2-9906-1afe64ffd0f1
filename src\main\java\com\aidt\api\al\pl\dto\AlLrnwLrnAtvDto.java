package com.aidt.api.al.pl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 학습창 연계
 * 학습활동
 * */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlLrnwLrnAtvDto {
	
	@Parameter(name="활동ID")
	private long lrnAtvId;
	
	@Parameter(name="활동명")
	private String lrnAtvNm;
	
	@Parameter(name="콘텐츠유형코드")
	private String ctnTpCd;
	
	@Parameter(name="문항ID")
	private String qtmId;
	
	@Parameter(name="컨텐츠URL")
	private String ctnUrl;
	
	@Parameter(name="활동정렬순서")
	private String rcstnOrdn;
	
	@Parameter(name="학습상태")
	private String lrnStCd;
	
	@Parameter(name="학습시간초수")
	private String lrnTmScnt;
	
	@Parameter(name="메타데이터리스트")
	private AlLrnwCtnMtdDto ctnMtd;
	
}
