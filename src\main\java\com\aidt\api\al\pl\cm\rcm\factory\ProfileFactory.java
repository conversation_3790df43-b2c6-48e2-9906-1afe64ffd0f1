package com.aidt.api.al.pl.cm.rcm.factory;

import java.util.List;

import org.springframework.stereotype.Component;

import com.aidt.api.al.pl.cm.rcm.strategy.ProfileStrategy;
import com.aidt.api.common.enums.SubjectCode;

@Component
public class ProfileFactory {

	private final List<ProfileStrategy> profileStrategies;

	public ProfileFactory(List<ProfileStrategy> profileStrategies) {
		this.profileStrategies = profileStrategies;
	}

	public ProfileStrategy getProfileStrategy(SubjectCode subjectCode) {
		return profileStrategies.stream()
				.filter(strategy -> strategy.isSupported(subjectCode))
				.findFirst()
				.orElseThrow(() -> new UnsupportedOperationException("AI 프로파일 적재 전략을 찾을 수 없습니다."));
	}
}