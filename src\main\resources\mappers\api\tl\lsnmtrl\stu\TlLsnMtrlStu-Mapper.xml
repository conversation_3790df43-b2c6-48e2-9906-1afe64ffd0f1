<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.lsnmtrl.stu">
    <!-- 수업자료목록조회 -->
    <!-- 
    <select id="selectLsnMtrlList" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto" resultType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        SELECT
             A.OPT_TXB_ID            /* 운영교과서ID */
            ,A.LRMP_NOD_ID           /* 학습맵노드ID */
            ,A.LSN_MTRL_NO           /* 수업자료번호 */
            ,A.LRN_MTRL_ID           /* 학습자료ID */
            ,A.LSN_MTRL_TP_CD        /* 게시물,링크,라이브퀴즈,토의토론,워크시트,보드 */
            ,A.OPNP_YN               /* 학생에게 공개여부 */
            ,A.RCSTN_ORDN            /* 재구성순서 */
            ,B.ANNC_MTRL_TITL_NM AS LRN_MTRL_TITL_NM /* 학습자료제목명 */
            ,B.ANNX_ID               /* 첨부ID */
        FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL A /* TL_교과학습수업자료 */ 
             INNER JOIN LMS_LRM.CM_ANNLST_LRN_MTRL B /* CM_공지사항학습자료 */
                ON A.OPT_TXB_ID = B.OPT_TXB_ID
                AND A.LRN_MTRL_ID = B.ANNLST_LRN_MTRL_ID
                AND B.ANNC_MTRL_DV_CD = 'LM' /* LM=학습자료 */
                AND B.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND A.LRMP_NOD_ID = #{lrmpNodId}  /* 학습맵노드ID */
        AND A.BS_MTRL_YN = 'Y'
        AND A.DEL_YN = 'N'
        <if test='usrDvCd == "S"'>
            AND A.OPNP_YN = 'Y'  /* 학생에게 공개여부 */
        </if>
        ORDER BY RCSTN_ORDN ASC, LSN_MTRL_NO ASC
        /** 교과학습 강성희 TlLsnMtrlStu-Mapper.xml - selectLsnMtrlList */
      </select>
 -->
</mapper>