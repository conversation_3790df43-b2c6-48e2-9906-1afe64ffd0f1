package com.aidt.api.tl.lsnmtrl.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:41:27
 * @modify date 2024-01-05 10:41:27
 * @desc TlLsnMtrlDto 수업자료Dto
 */
@Data
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLsnMtrlDto{

    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    /** 학습맵노드ID */
    @Parameter(name="학습맵노드ID")
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;
    /** 수업자료번호 */
    @Parameter(name="수업자료번호")
    private String lsnMtrlNo;
    /** 학습자료ID */
    @Parameter(name="학습자료ID")
    @NotBlank(message = "{field.required}")
    private String lrnMtrlId;
    /** 게시물,링크,라이브퀴즈,토의토론,워크시트,보드 */
    @Parameter(name="게시물,링크,라이브퀴즈,토의토론,워크시트,보드")
    private String lsnMtrlTpCd;
    /** 학생에게 공개여부 */
    @Parameter(name="학생에게 공개여부")
    @NotNull(message = "{field.required}")
    private String opnpYn;
    /** 접속DB인스턴스ID */
    @Parameter(name="접속DB인스턴스ID")
    private String dbId;
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;
    /** 상태코드 */
    @Parameter(name="상태코드(I:신규, U:수정, D:삭제)")
    private String statCd;
    /** 학습자료제목명 */
    @Parameter(name="학습자료제목명")
    private String lrnMtrlTitlNm;
    /** 첨부ID */
    @Parameter(name="첨부ID")
    private String annxId;
    // 이하 v2.3대응
    /** 수업보충자료ID */
    @Parameter(name="수업보충자료ID")
    private String lsnSppMtrlId;
    /** 자료구분(BS, LM) */
    @Parameter(name="자료구분(BS=LCMS, LM=학습자료)")
    private String mtrlDvCd;
    /** 수업보충자료명 */
    @Parameter(name="수업보충자료명")
    private String mtrlNm;
    /** 파일관리유형코드 */
    @Parameter(name="파일관리유형코드")
    private String fleMgTpCd;
    /** 파일링크URL주소 */
    @Parameter(name="파일링크URL주소")
    private String fleLinkUrlAdr;
    /** 학습게시글내용 */
    @Parameter(name="학습게시글내용")
    private String lrnBlwrCn;
    /** 파일경로명 */
    @Parameter(name="파일경로명")
    private String flePthNm;
    /** 파일유형코드 */
    @Parameter(name="파일유형코드")
    private String fleTpCd;
    /** 대체텍스트내용 */
    @Parameter(name="대체텍스트내용")
    private String altnTxtCn;
    /** 파일명 */
    @Parameter(name="파일명")
    private String fleNm;
    /** 뷰어ID */
    @Parameter(name="뷰어ID")
    private String docViId;
}
