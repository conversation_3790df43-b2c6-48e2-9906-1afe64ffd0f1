package com.aidt.api.bc.clablbd.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.clablbd.dto.BcClaBlbdCopyFleDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdDto;
import com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.bc.inf.infCom.InfComService;
import com.aidt.api.bc.inf.infCom.dto.InfComDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-07 13:42:10
 * @modify 2024-06-07 13:42:10
 * @desc 학급게시판 Service
 */

@Service
public class BcClaBlbdTcrService {

	private final String MAPPER_NAMESPACE = "api.bc.clablbd.tcr.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private InfComService infComService;

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private BcCmService bcCmService;

	/**
	 * 학급게시판 조회
	 *
	 * @param BcClaBlbdDto
	 * @return List<BcClaBlbdDto>
	 */
	public List<BcClaBlbdDto> selectClaBlbdList(BcClaBlbdDto bcClaBlbdDto) {

		List<BcClaBlbdDto> usrIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectBlbdUsrList", bcClaBlbdDto);
		if (bcClaBlbdDto.getSrchField() != null && !bcClaBlbdDto.getSrchField().isEmpty()) {

			Map<String, String> kerisUsrIdToUsrIdMap = new HashMap<>();
			for (BcClaBlbdDto dto : usrIdList) {
				kerisUsrIdToUsrIdMap.put(dto.getUsrId(), dto.getKerisUsrId());
			}

			String[] usrIds = bcClaBlbdDto.getUsrIds();

			for (int i = 0; i < usrIds.length; i++) {
				String newUsrId = kerisUsrIdToUsrIdMap.get(usrIds[i]);
				if (newUsrId != null) {
					usrIds[i] = newUsrId;

				}
			}
		}

		List<BcClaBlbdDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectClaBlbdList", bcClaBlbdDto);
		Integer claBlbdCnt = commonDao.select(MAPPER_NAMESPACE + "selectClaBlbdCnt", bcClaBlbdDto);
		if (!list.isEmpty()) {
			list.get(0).setBlbdCnt(claBlbdCnt);
		}

		// 학급게시판에 등록된 첨부파일 조회
		for (BcClaBlbdDto item : list) {
			Long annxId = item.getAnnxId();
			if (annxId != null && annxId > 0) {
				List<BcAnnxFleDto> fleList = commonDao.selectList(MAPPER_NAMESPACE + "selectAnnxFleList", annxId);
				item.setFleList(fleList);
			}
		}

		return list;
	}

	/**
	 * 학급게시판 상세 조회
	 *
	 * @param BcClaBlbdDto
	 * @return BcClaBlbdDto
	 */
	public BcClaBlbdDto getClaBlbdInfo(BcClaBlbdDto bcClaBlbdDto) {
		BcClaBlbdDto claBlbdInfo = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdInfo", bcClaBlbdDto);
		// 학급게시판 댓글 목록
		List<BcClaBlbdUcwrDto> ucwrList = commonDao.selectList(MAPPER_NAMESPACE + "selectClaBlbdUcwrList",
			bcClaBlbdDto);
		if (ucwrList != null && ucwrList.size() > 0) {
			claBlbdInfo.setUcwrList(ucwrList);
		}

		Long annxId = claBlbdInfo.getAnnxId();
		if (annxId != null && annxId > 0) {
			List<BcAnnxFleDto> fleList = commonDao.selectList(MAPPER_NAMESPACE + "selectAnnxFleList", annxId);
			claBlbdInfo.setFleList(fleList);
		}
		return claBlbdInfo;
	}

	/**
	 * 학급게시판 등록/수정/삭제
	 *
	 * @param BcClaBlbdDto
	 * @return Integer
	 */
	@Transactional
	public Map<String, Object> saveClaBlbdInfo(BcClaBlbdDto bcClaBlbdDto) {
		Map<String, Object> result = new HashMap<>();

		int check = 0;
		String flag = "";

		if (bcClaBlbdDto.getFlag().equals("insert")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdInfo", bcClaBlbdDto);
			flag = "insert";
			if (check > 0) {
				result.put("claBlbdId", bcClaBlbdDto.getClaBlbdId());
			}

		} else if (bcClaBlbdDto.getFlag().equals("update")) {
			// 본인이 작성한 게시글만 수정 가능
			// String claBlbdUsrCheck = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdUsrCheck", bcClaBlbdDto);

			// if("Y".equals(claBlbdUsrCheck)) {
			if (bcClaBlbdDto.getFlag().equals("update")) {
				check = commonDao.update(MAPPER_NAMESPACE + "updateClaBlbdInfo", bcClaBlbdDto);
				flag = "update";
				// 필독 -> 일반 게시글 수정 시 알림 삭제
				if ("Y".equals(bcClaBlbdDto.getBfRqrdYn()) && "N".equals(bcClaBlbdDto.getRqrdYn())) {
					List<Long> infParam = new ArrayList<>();
					infParam.add(bcClaBlbdDto.getClaBlbdId());
					Map<String, Object> map = new HashMap<>();
					map.put("param", infParam);
					map.put("infmClCd", "CB");
					infComService.deleteInfCom(map);
				}
			}
			// }
		} else {
			check = commonDao.update(MAPPER_NAMESPACE + "deleteClaBlbdInfo", bcClaBlbdDto);
			flag = "delete";
			List<Long> infParam = new ArrayList<>();
			infParam.add(bcClaBlbdDto.getClaBlbdId());
			Map<String, Object> map = new HashMap<>();
			map.put("param", infParam);
			map.put("infmClCd", "CB");
			infComService.deleteInfCom(map);
		}
		result.put("count", check);
		result.put("flag", flag);
		result.put("message", check > 0 ? "Success" : "Failure");

		return result;
	}

	/**
	 * 학급게시판 등록/수정/삭제
	 *
	 * @param BcClaBlbdDto
	 * @return Integer
	 */
	public int saveClaBlbdUcwrInfo(BcClaBlbdUcwrDto bcClaBlbdUcwrDto) {
		int check = 0;
		if (bcClaBlbdUcwrDto.getFlag().equals("insert")) {
			check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdUcwrInfo", bcClaBlbdUcwrDto);
		} else if (bcClaBlbdUcwrDto.getFlag().equals("update")) {
			// 본인이 작성한 게시글만 수정 가능
			// String claBlbdUsrCheck = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdUcwrUsrCheck", bcClaBlbdUcwrDto);
			// if("Y".equals(claBlbdUsrCheck)) {
			check = commonDao.insert(MAPPER_NAMESPACE + "updateClaBlbdUcwrInfo", bcClaBlbdUcwrDto);
			// }
		} else {
			check = commonDao.insert(MAPPER_NAMESPACE + "deleteClaBlbdUcwrInfo", bcClaBlbdUcwrDto);
		}
		return check;
	}

	/**
	 * 학급게시판 일괄 등록
	 *
	 * @param BcClaBlbdDto
	 * @return BcClaBlbdDto
	 */
	@Transactional
	public int saveTcrPkgBlbd(BcClaBlbdDto bcClaBlbdDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		// 현재 게시글 상세 조회
		BcClaBlbdDto claBlbdInfo = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdInfo", bcClaBlbdDto); // 현재 게시글

		int rqrdcount = 0;
		int check = 0;

		String[] chkClaList = bcClaBlbdDto.getChkClaList(); // 체크한 학급 운영교과서ID
		String currentOptTxbId = claBlbdInfo.getOptTxbId(); // 현재 글의 운영교과서아이디
		String currentPkgBlbdId = claBlbdInfo.getPkgBlbdId(); // 현재 글의 묶음게시판아이디
		Long orgBlbdId = claBlbdInfo.getClaBlbdId();

		String className = this.getClass().getSimpleName();
		String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();

		if (claBlbdInfo.getPkgBlbdId() == null || claBlbdInfo.getPkgBlbdId().isEmpty()) {
			// 일괄 등록된 게시글이 아닌 경우
			for (String optTxbId : chkClaList) {
				// 운영교과서ID로 필독갯수 조회 5개 이상이면 일반 게시물로 insert하기 위해 필독 갯수 조회
				rqrdcount = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdRqrdCnt", optTxbId);

				bcClaBlbdDto.setOptTxbId(optTxbId); // 운영교과서ID
				bcClaBlbdDto.setUsrTpCd(claBlbdInfo.getUsrTpCd()); // 사용자유형코드
				bcClaBlbdDto.setUsrId(claBlbdInfo.getUsrId()); // 사용자ID
				bcClaBlbdDto.setClaBlbdTitlNm(claBlbdInfo.getClaBlbdTitlNm()); // 학급게시판제목명
				bcClaBlbdDto.setClaBlbdCn(claBlbdInfo.getClaBlbdCn()); // 학급게시판내용

				if (claBlbdInfo.getAnnxId() == null) {
					bcClaBlbdDto.setAnnxId(null); // 첨부ID
				} else {
					bcClaBlbdDto.setAnnxId(claBlbdInfo.getAnnxId()); // 첨부ID
				}

				bcClaBlbdDto.setPkgBlbdId(String.valueOf(claBlbdInfo.getClaBlbdId())); // 묶음게시판ID에는 원글의 학급게시판ID

				if (rqrdcount >= 5) {
					bcClaBlbdDto.setRqrdYn("N"); // 필독여부
				} else {
					bcClaBlbdDto.setRqrdYn(claBlbdInfo.getRqrdYn()); // 필독여부
				}

				bcClaBlbdDto.setUcwrUseYn(claBlbdInfo.getUcwrUseYn()); // 댓글사용여부

				if (currentOptTxbId.equals(optTxbId)) {
					// 현재 글에 일괄게시판아이디 update
					bcClaBlbdDto.setClaBlbdId(orgBlbdId);
					check = commonDao.insert(MAPPER_NAMESPACE + "updateClaBlbdInfo", bcClaBlbdDto);
				} else {
					// 현재 글 복사
					check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdPkgInfo", bcClaBlbdDto);

					CmClaCpLogDto dto = CmClaCpLogDto.builder()
						.optTxbId(currentOptTxbId)
						.cpOptTxbId(optTxbId)
						.cpDvCd("BLBD")
						.cpPrcsYn(check > 0 ? "Y" : "N")
						.backendFlePth(className + "." + methodName)
						.kerisUsrId(userDetails.getKerisUsrId())
						.crtrId(userDetails.getUsrId())
						.build();

					bcCmService.insertClaCpLog(dto);

					// 필독
					if (rqrdcount < 5 && "Y".equals(bcClaBlbdDto.getRqrdYn())) {
						insertPkgBlbdInfm(bcClaBlbdDto);
					}
				}
			}

		} else {
			for (String optTxbId : chkClaList) {
				// 운영교과서ID로 필독갯수 조회 5개 이상이면 일반 게시물로 insert
				rqrdcount = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdRqrdCnt", optTxbId); // 필독 게시글 갯수

				bcClaBlbdDto.setPkgBlbdId(String.valueOf(claBlbdInfo.getPkgBlbdId())); // 묶음게시판ID에는 원글의 학급게시판ID
				bcClaBlbdDto.setOptTxbId(optTxbId); // 운영교과서ID
				BcClaBlbdDto pkgBlbdInfo = commonDao.select(MAPPER_NAMESPACE + "getPkgBlbdInfo", bcClaBlbdDto);

				bcClaBlbdDto.setUsrTpCd(claBlbdInfo.getUsrTpCd()); // 사용자유형코드
				bcClaBlbdDto.setUsrId(claBlbdInfo.getUsrId()); // 사용자ID
				bcClaBlbdDto.setClaBlbdTitlNm(claBlbdInfo.getClaBlbdTitlNm()); // 학급게시판제목명
				bcClaBlbdDto.setClaBlbdCn(claBlbdInfo.getClaBlbdCn()); // 학급게시판내용

				if (claBlbdInfo.getAnnxId() == null) {
					bcClaBlbdDto.setAnnxId(null); // 첨부ID
				} else {
					bcClaBlbdDto.setAnnxId(claBlbdInfo.getAnnxId()); // 첨부ID
				}

				if (rqrdcount >= 5) {
					bcClaBlbdDto.setRqrdYn("N"); // 필독여부
				} else {
					bcClaBlbdDto.setRqrdYn(claBlbdInfo.getRqrdYn()); // 필독여부
				}

				bcClaBlbdDto.setUcwrUseYn(claBlbdInfo.getUcwrUseYn()); // 댓글사용여부

				// 본인이 작성한 게시글만 수정 가능
				// String claBlbdUsrCheck = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdUsrCheck", pkgBlbdInfo);
				// if("Y".equals(claBlbdUsrCheck)) {
				if (!currentOptTxbId.equals(optTxbId)) {
					// pkgList에서 pkgBlbdId가 존재하는지 확인
					if (pkgBlbdInfo != null) {
						if (pkgBlbdInfo.getPkgBlbdId() != null) {
							// pkgBlbdId가 존재하면, 기존 묶음 게시글에 대해 update
							bcClaBlbdDto.setClaBlbdId(pkgBlbdInfo.getClaBlbdId());
							check = commonDao.insert(MAPPER_NAMESPACE + "updateClaBlbdInfo", bcClaBlbdDto);

							// 필독게시글 5개 이하, 필독여부 N -> Y 일때 알림발송
							if (rqrdcount < 5 && "N".equals(pkgBlbdInfo.getRqrdYn())
								&& "Y".equals(bcClaBlbdDto.getRqrdYn())) {
								insertPkgBlbdInfm(bcClaBlbdDto);
							}
							// 필독여부 Y -> N 일때 알림삭제
							if ("Y".equals(pkgBlbdInfo.getRqrdYn()) && "N".equals(bcClaBlbdDto.getRqrdYn())) {
								List<Long> infParam = new ArrayList<>();
								infParam.add(pkgBlbdInfo.getClaBlbdId());
								Map<String, Object> map = new HashMap<>();
								map.put("param", infParam);
								map.put("infmClCd", "CB");
								infComService.deleteInfCom(map);
							}
							CmClaCpLogDto dto = CmClaCpLogDto.builder()
								.optTxbId(currentOptTxbId)
								.cpOptTxbId(optTxbId)
								.cpDvCd("BLBD")
								.cpPrcsYn(check > 0 ? "Y" : "N")
								.backendFlePth(className + "." + methodName)
								.kerisUsrId(userDetails.getKerisUsrId())
								.crtrId(userDetails.getUsrId())
								.build();

							bcCmService.insertClaCpLog(dto);
						} else {
							// pkgBlbdId가 없으면 새로운 게시글로 insert
							check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdPkgInfo", bcClaBlbdDto);
						}
					} else {
						// pkgBlbdId가 없으면 새로운 게시글로 insert
						check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdPkgInfo", bcClaBlbdDto);
						CmClaCpLogDto dto = CmClaCpLogDto.builder()
							.optTxbId(currentOptTxbId)
							.cpOptTxbId(optTxbId)
							.cpDvCd("BLBD")
							.cpPrcsYn(check > 0 ? "Y" : "N")
							.backendFlePth(className + "." + methodName)
							.kerisUsrId(userDetails.getKerisUsrId())
							.crtrId(userDetails.getUsrId())
							.build();

						bcCmService.insertClaCpLog(dto);
					}
				}
				// } else {
				// pkgBlbdId가 없으면 새로운 게시글로 insert
				// check = commonDao.insert(MAPPER_NAMESPACE + "insertClaBlbdPkgInfo", bcClaBlbdDto);
				// }
			}
		}
		return check;
	}

	/**
	 * 학급게시판 일괄 삭제
	 *
	 * @param BcClaBlbdDto
	 * @return BcClaBlbdDto
	 */
	@Transactional
	public int deleteTcrPkgBlbd(BcClaBlbdDto bcClaBlbdDto) {
		int check = 0;

		String[] chkClaList = bcClaBlbdDto.getChkClaList(); // 체크한 학급게시판ID
		for (String claBlbdId : chkClaList) {
			bcClaBlbdDto.setClaBlbdId(Long.parseLong(claBlbdId)); // 학급게시판ID
			// 일괄 삭제 운영교과서 확인
			String optTxbId = commonDao.select(MAPPER_NAMESPACE + "getClaBlbdPkgUsrCheck", bcClaBlbdDto);
			if (optTxbId != null && !optTxbId.isEmpty()) {
				bcClaBlbdDto.setOptTxbId(optTxbId);
				check = commonDao.update(MAPPER_NAMESPACE + "deleteClaBlbdInfo", bcClaBlbdDto);
				List<Long> infParam = new ArrayList<>();
				infParam.add(bcClaBlbdDto.getClaBlbdId());
				Map<String, Object> map = new HashMap<>();
				map.put("param", infParam);
				map.put("infmClCd", "CB");
				infComService.deleteInfCom(map);
			}
		}

		return check;
	}

	/**
	 * 학급게시판 일괄등록 목록
	 *
	 * @param List<BcClaBlbdDto>
	 * @return List<BcClaBlbdDto>
	 */
	public List<BcClaBlbdDto> selectTcrPkgClaList(BcClaBlbdDto bcClaBlbdDto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTcrPkgClaList", bcClaBlbdDto);
	}

	@Transactional(readOnly = true)
	public Map<String, Object> selectOptTxbIdChk(BcClaBlbdDto bcClaBlbdDto) {
		Map<String, Object> returnMap = new HashMap<>();

		String optTxbId = bcClaBlbdDto.getOptTxbId();
		String chkOptTxbId = commonDao.select(MAPPER_NAMESPACE + "selectOptTxbIdChk", bcClaBlbdDto);

		if (chkOptTxbId == null) {
			returnMap.put("status", "error");
			returnMap.put("errorMessage", "학급게시판 정보를 찾을 수 없습니다.");
		} else if (chkOptTxbId.equalsIgnoreCase(optTxbId)) {
			returnMap.put("status", "success");
		}

		return returnMap;
	}

	/**
	 * 학급게시판 게시글 승인 여부 수정
	 *
	 * @param BcClaBlbdDto
	 * @return Integer
	 */
	public int updateBlwrAprYn(BcClaBlbdDto bcClaBlbdDto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateBlwrAprYn", bcClaBlbdDto);
	}

	/**
	 * 학급게시판 댓글 승인 여부 수정
	 *
	 * @param BcClaBlbdDto
	 * @return Integer
	 */
	public int updateUcwrAprYn(BcClaBlbdUcwrDto bcClaBlbdUcwrDto) {
		return commonDao.update(MAPPER_NAMESPACE + "updateUcwrAprYn", bcClaBlbdUcwrDto);
	}

	/**
	 * 학급게시판 다른학급 저장시 알림등록
	 * @param bcClaBlbdDto
	 */
	public void insertPkgBlbdInfm(BcClaBlbdDto bcClaBlbdDto) {
		InfComDto param = new InfComDto();
		param.setClaBlbdId(bcClaBlbdDto.getClaBlbdId());
		param.setClaBlbdTitlNm(bcClaBlbdDto.getClaBlbdTitlNm());
		param.setInfmClCd("CB");
		param.setInfmDtlClCd("BR");
		param.setUsrId(bcClaBlbdDto.getUsrId());
		param.setDbId(bcClaBlbdDto.getDbId());
		param.setOptTxbId(bcClaBlbdDto.getOptTxbId());
		infComService.insertInfmOtherCla(param);
	}

	/**
	 * 동일 첨부ID를 가지고 있는지 확인
	 * 
	 * @param dto
	 * @return
	 */
	public boolean selectOtherClaBlbdCopyAnnxFleYn(BcClaBlbdCopyFleDto dto) {
		return ((Integer)commonDao.select(MAPPER_NAMESPACE + "selectOtherClaBlbdCopyAnnxFleYn", dto)) > 0 ? true
			: false;
	}
}
