package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-07 16:52:28
 * @modify date 2024-02-07 16:52:28
 * @desc [교과학습 활동 상태 체크 dto]
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외

public class TlSbcLrnAtvStChkDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
     /** 차시ID */
     @Parameter(name="학습맵노드ID")
     private String lrmpNodId;
     /** 차시명 */
     @Parameter(name="차시명")
     private String lrmpNodNm;
     /** 대단원노드ID*/
     @Parameter(name="대단원노드ID")
     private String urnkLrmpNodId;
     /** 대단원노드명*/
     @Parameter(name="대단원노드명")
     private String urnkLrmpNodNm;
     /** 깊이*/
     @Parameter(name="깊이")
     private String depth;
     /** 해당 차시 학습 총 개수*/
     @Parameter(name="해당 차시 학습 총 개수")
     private String cntTot;
//     /** 학습 상태(학습중) 갯수*/
//     @Parameter(name="학습 상태 코드 (학습중) 갯수")
//     private String cntDL;
     /** 학습 상태(학습완료) 갯수*/
     @Parameter(name="학습 상태 코드 (학습완료) 갯수")
     private String cntCl;
     /** 미학습 및 학습중 갯수*/
     @Parameter(name="학습 상태 코드 (미학습 및 학습중) 갯수")
     private String cntNl;
     /** 마지막 학습 여부*/
     @Parameter(name="마지막 학습 여부")
     private String lastYn;
     @Parameter(name="단원 잠금 여부")
     private String lcknYn;
}
