package com.aidt.api.tl.sbclrn.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-22 16:20:19
 * @modify date 2024-01-22 16:20:19
 * @desc TlTcrSbcLrnAtvSumDto 교과학습 활동상태 요약정보(교과서)
 */
@Data
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvSumDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    /** 학습맵노드ID(차시) */
    @Parameter(name="학습맵노드ID")
    private String lrmpNodId;
    /** 학습맵노드명(대단원) */
    @Parameter(name="학습맵노드명(대단원)")
    private String lrmpNodNm1;
    /** 학습맵노드명(중단원) */
    @Parameter(name="학습맵노드명(중단원)")
    private String lrmpNodNm2;
    /** 학습맵노드명(소단원) */
    @Parameter(name="학습맵노드명(소단원)")
    private String lrmpNodNm3;
    /** 학습맵노드명(차시) */
    @Parameter(name="학습맵노드명(차시)")
    private String lrmpNodNm4;
    /** 학습활동ID */
    @Parameter(name="학습활동ID")
    private String lrnAtvId;
    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;
    /** (미학습, 학습중, 학습완료) */
    @Parameter(name="학습상태코드")
    private String lrnStCd;
    /** 교과서시작페이지번호 */
    @Parameter(name="교과서시작페이지번호")
    private String txbStrPgeNo;
    /** 교과서종료페이지번호 */
    @Parameter(name="교과서종료페이지번호")
    private String txbEndPgeNo;
    /** 익힘책시작페이지번호 */
    @Parameter(name="익힘책시작페이지번호")
    private String wkbStrPgeNo;
    /** 익힘책종료페이지번호 */
    @Parameter(name="익힘책종료페이지번호")
    private String wkbEndPgeNo;
    /** 총학습활동건수 */
    @Parameter(name="총학습활동건수")
    private String allLrnAtvCnt;
    /** 학습활동완료건수 */
    @Parameter(name="학습활동완료건수")
    private String lrnAtvFinCnt;
    /** 대단원번호 */
    @Parameter(name="대단원번호")
    private String nodNo;
//    /** 썸네일정보 */
//    @Parameter(name="썸네일정보")
//    private TlSbcLrnThbDto thbInfo;
    /** 차시썸네일정보 */
    @Parameter(name="차시썸네일정보")
    private TlSbcLrnThbDto tcThbInfo;
    /** 학습목표 */
    @Parameter(name="학습목표")
    private String lrnGoal;
    /** 학습활동목록 */
    @Parameter(name="학습활동목록")
    private List<TlSbcLrnTcAtvDto> atvList;
}
