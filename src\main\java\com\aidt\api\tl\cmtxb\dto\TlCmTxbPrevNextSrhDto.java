package com.aidt.api.tl.cmtxb.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-15 10:48:05
 * @modify date 2024-03-15 10:48:05
 * @desc [TlCmTxbPrevNextSrhDto 이전차시 다음차시 조회조건 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbPrevNextSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 차시노드ID */
    @Parameter(name="차시노드ID")
    @NotBlank(message = "{field.required}")
    private String lrmpNodId;
    
}
