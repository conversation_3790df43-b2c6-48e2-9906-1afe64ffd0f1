package com.aidt.api.bc.cm.tcr;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.dto.BcClaListDto;
import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.api.bc.cm.dto.BcUsrInfoDto;
import com.aidt.common.CommonDao;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.DatabindException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-16 12:19:59
 * @modify 2024-01-16 12:19:59
 * @desc 첨부파일 테스트 service (DB)
 */

@Service
public class BcCmTcrService {

	// 임시경로
    private final String MAPPER_NAMESPACE = "api.bc.cm.tcr.";
    
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
    @Autowired
    private CommonDao commonDao;
    

    // 교사 로그인 후 :: 담당 학생 실시간 모니터링 테이블 데이터 check
    public void stuCurLrnStCheck(List<BcStuListDto> stuList, String dbId) {
    	if(stuList != null && stuList.size() > 0) {
    		for(BcStuListDto stuInfo : stuList) {
    			int stuCurLrnStCnt = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStCnt", stuInfo);
    			if(stuCurLrnStCnt == 0) {
    				stuInfo.setTxbId(dbId);
    				commonDao.insert(MAPPER_NAMESPACE + "insertStuCurLrnSt", stuInfo);
    			}
    		}
    	}

    }

    // 교사가 담당학생에 대한 관심여부 변경
    public int updateStuNtrYn(BcUsrInfoDto bcUsrInfoDto) {
    	return commonDao.update(MAPPER_NAMESPACE + "updateStuNtrYn", bcUsrInfoDto);
    }


    /**
	 * @param String
	 * @return List<BcStuListDto>
	 */
	public List<BcStuListDto> selectStuList(String usrId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectStuList", usrId);
    }

    /**
	 * @param String
	 * @return List<BcStuListDto>
	 */
	public List<BcClaListDto> selectClaList(String kerisUsrId, String txbId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectClaList", Map.of("kerisUsrId",kerisUsrId, "txbId", txbId));
    }
	
    /**
	 * @return Map<String, Object>
     * @throws IOException 
     * @throws DatabindException 
     * @throws StreamReadException 
	 */
	
	@SuppressWarnings("unchecked")
	public Map<String, Object> selectThirdPartyUseInfo(String fileName) throws StreamReadException, DatabindException, IOException{		
		Map<String, Object> rtnData = new HashMap<String, Object>();
		
        AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
        
        S3Object s3Object = s3.getObject(BUCKET_NAME, fileName);
        S3ObjectInputStream imputStream = s3Object.getObjectContent();
        
        ObjectMapper objectMapper = new ObjectMapper();
        rtnData = objectMapper.readValue(imputStream, Map.class);
		return rtnData;
	}
	
	
}
