//package com.aidt.api.al.pl.stu;
//
//import java.util.List;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.aidt.api.al.pl.dto.AlPlStuDto;
//import com.aidt.base.response.Response;
//import com.aidt.base.response.ResponseDto;
//
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * <AUTHOR>
// * @email <EMAIL>
// * @create date 2023-12-12 22:42:32
// * @modify date 2023-12-12 22:42:32
// * @desc AI맞춤학습 단원목차 테스트
// */
//@Slf4j
//@Tag(name="[al] AI맞춤학습[AlPlStu]", description="AI맞춤학습(학생용)")
//@RestController
//@RequestMapping("/api/v1/al/pl/stu")
//public class AlPlStuController {
//	
//    @Autowired
//    private AlPlStuService alPlStuService;
//
//	//@Autowired
//    //private JwtProvider jwtProvider;
//
//    /**
//     * AI맞춤학습 목차조회 서비스(대단원)
//     * 
//     * @return ResponseDto<List<AlPlStuDto>>
//     */
//    @Operation(summary="AI맞춤학습 목차리스트 조회", description="AI맞춤학습 목차를 가져온다.")
//    @PostMapping(value = "/selectAiSjList")
//    public ResponseDto<List<AlPlStuDto>> selectSbcTocList() {
//        log.debug("Entrance selectAiSjList");
//
//        AlPlStuDto alPlStuDto = new AlPlStuDto();
//        alPlStuDto.setOptTxbId("tcr001001001001001"); // "OPTTXB01"
//
//        return Response.ok(alPlStuService.selectAiSjList(alPlStuDto));
//    }
//    
//    /**
//     * AI맞춤학습 목차조회 서비스(중단원)
//     * 
//     * @return ResponseDto<List<AlPlStuDto>>
//     */
//    @Operation(summary="AI맞춤학습 목차리스트 조회", description="AI맞춤학습 목차를 가져온다.")
//    @PostMapping(value = "/selectAiSjList2")
//    public ResponseDto<List<AlPlStuDto>> selectSbcTocList2() {
//        log.debug("Entrance selectAiSjList");
//
//        
//        AlPlStuDto alPlStuDto = new AlPlStuDto();
//        alPlStuDto.setOptTxbId("tcr001001001001001"); // "OPTTXB01"
//
//        return Response.ok(alPlStuService.selectAiSjList2(alPlStuDto));
//    }
//    
//
//}
