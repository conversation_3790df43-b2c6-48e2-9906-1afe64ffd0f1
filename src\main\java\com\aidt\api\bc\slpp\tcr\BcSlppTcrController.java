package com.aidt.api.bc.slpp.tcr;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.slpp.dto.BcSlppComandDto;
import com.aidt.api.bc.slpp.dto.BcSlppDto;
import com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto;
import com.aidt.api.bc.slpp.dto.BcSlppRoomDto;
import com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:53:08
 * @modify 2024-01-05 17:53:08
 * @desc 교사_쪽지 Controller
 */

@Slf4j
@Tag(name="[bc] 쪽지[BcSlppTcr]", description="쪽지(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/slpp")
public class BcSlppTcrController {

    @Autowired
    private BcSlppTcrService bcSlppTcrService;

    /**
     * 쪽지 목록 조회 요청
     *
     * @param dto: BcSlppDtoPagingRequest
     * @return List<BcSlppDto>
     */
    @Operation(summary="쪽지 목록 조회", description="쪽지 목록을 조회한다.")
    @GetMapping(value = "/selectSlppList")
    public ResponseDto<List<BcSlppDto>> selectSlppList(BcSlppPagingRequestDto dto) {
        log.info("selectSlppList" + dto);
        return Response.ok(bcSlppTcrService.selectSlppList(dto));
    }

    /**
     * 쪽지 내용 등록 요청
     *
     * @param dto: BcSlppDto
     * @return Integer
     */
    @Operation(summary="쪽지 등록", description="쪽지를 등록한다.")
    @PostMapping(value = "/insertSlpp")
    public ResponseDto<Integer> insertSlpp(@RequestBody BcSlppDto dto){
    	log.info("insertSlpp : " + dto);
    	return Response.ok(bcSlppTcrService.insertSlpp(dto));
    }

    /**
     * 쪽지 삭제 요청
     *
     * @param list :List<BcSlppDto>
     * @return Integer
     */
    @Operation(summary="쪽지 삭제", description="쪽지를 삭제한다.")
    @PostMapping(value = "/deleteSlppList")
    public ResponseDto<Integer> deleteSlpp(@RequestBody List<BcSlppDto> list) {
    	log.info("deleteSlpp : " + list.stream().map(dto->dto.getSlppId()+"").collect(Collectors.joining(", ")));
    	// 데이터 검증부분 확인
        return Response.ok(bcSlppTcrService.deleteSlpp(list));
    }

    /**
     * 쪽지 삭제 요청
     *
     * @param dto :BcSlppDto
     * @return Integer
     */
    @Operation(summary="쪽지 삭제", description="쪽지를 삭제한다.")
    @PostMapping(value = "/deleteSlpp")
    public ResponseDto<Integer> deleteSlpp(@RequestBody BcSlppDto dto) {
        log.debug("Entrance deleteSlpp");
        log.info("deleteSlpp : bcSlppDto : " + dto);
        // 데이터 검증부분 확인
        return Response.ok(bcSlppTcrService.deleteSlpp(dto));
    }

    /**
     * 쪽지 전체 삭제 요청
     *
     * @param dto :BcSlppDto
     * @return Integer
     */
    @Operation(summary="쪽지 전체 삭제", description="쪽지를 삭제한다.")
    @PostMapping(value = "/deleteAllSlpp")
    public ResponseDto<Integer> deleteAllSlpp(@RequestBody BcSlppComandDto dto) {
        log.debug("Entrance deleteAllSlpp");
        log.info("deleteSlpp : " + dto);
        // 데이터 검증부분 확인
        return Response.ok(bcSlppTcrService.deleteAllSlpp(dto));
    }
    /////////////////////
    /**
     * 교사 대화 목록
     * @param tcrUsrId : String
     * @return List<BcSlppRoomDto>
     */
    @Operation(summary="교사 대화 목록 조회", description="교사 대화 목록 조회한다")
    @GetMapping(value = "/selectSlppRoomList")
    public ResponseDto<List<BcSlppRoomDto>> selectSlppRoomList(@RequestParam String tcrUsrId){
        log.info("selectSlppRoomList: " + tcrUsrId );
        return Response.ok(bcSlppTcrService.selectSlppRoomList(tcrUsrId));
    }

    /**
     * 쪽지 내용 등록 요청(교사 - 단체 보내기)
     *
     * @param list: List<BcSlppDto>
     * @return Integer
     */
    @Operation(summary="쪽지 등록", description="쪽지를 등록한다.")
    @PostMapping(value = "/insertSlppList")
    public ResponseDto<Integer> insertSlpp(@RequestBody List<BcSlppDto> list){
        log.info("insertSlpp : " + list.stream().map(dto->dto.getSlppId()+"").collect(Collectors.joining(", ")));
        return Response.ok(bcSlppTcrService.insertSlpp(list));
    }
    ////////////////////////

    /**
     * 대화 가능 시간 설정 - save (필요 없을듯한데..)
     * @param request : BcSlppTmSetm
     * @return Integer
     */
    @Operation(summary="대화 가능 시간 설정", description="대화 가능 시간을 설정한다")
    @PostMapping(value = "/saveSlppTmSetm")
    public ResponseDto<Integer> saveSlppTmSetm(@RequestBody BcSlppTmSetmDto request){
        log.info("saveBcSlppTmSetm: " + request);
        return Response.ok(bcSlppTcrService.saveSlppTmSetm(request));
    }

//    /**
//     * 교사 대화 시간 설정 신규
//     * @param request : BcSlppTmSetm
//     * @return Integer
//     */
//    @Operation(summary="신규 대화 가능 시간 설정", description="신규 대화 가능 시간을 설정한다")
//    @PostMapping(value = "/insertBcSlppTmSetm4Save")
//    public ResponseDto<Integer> insertBcSlppTmSetm4Save(BcSlppTmSetmDto request){
//        log.info("insertBcSlppTmSetm: " + request);
//        return Response.ok(bcSlppTcrService.insertBcSlppTmSetm(request));
//    }

    /**
     * 교사 대화 시간 설정 신규
     * @param optTxbId : BcSlppTmSetm
     * @return Integer
     */
    @Operation(summary="신규 대화 가능 시간 설정", description="신규 대화 가능 시간을 설정한다")
    @PostMapping(value = "/insertSlppTmSetm")
    public ResponseDto<Integer> insertSlppTmSetm(String optTxbId){
        log.info("insertSlppTmSetm: " + optTxbId);
        return Response.ok(bcSlppTcrService.insertSlppTmSetm(optTxbId));
    }


    /**
     * 교사 대화 시간 설정 갱신
     * @param dto : BcSlppTmSetm
     * @return Integer
     */
    @Operation(summary="신규 대화 가능 시간 설정", description="신규 대화 가능 시간을 설정한다")
    @PostMapping(value = "/updateSlppTmSetm")
    public ResponseDto<Integer> updateSlppTmSetm(BcSlppTmSetmDto dto){
        log.info("updateSlppTmSetm: " + dto );
        return Response.ok(bcSlppTcrService.updateSlppTmSetm(dto));
    }
    
    
    /**
     * 교사 대화 사용/미사용 설정
     *
     * @param optTxbId : String
     * @return BcSlppTmSetmDto
     */
    @Operation(summary="교사 대화 사용/미사용 설정", description="교사 대화 사용여부 설정")
    @PostMapping(value = "/updateSlppUseYn")
    public ResponseDto<Integer> updateSlppUseYn(@RequestBody BcSlppTmSetmDto dto) {
//    	log.info("selectSlppTmSetm: " + optTxbId );
    	log.debug("dto : " + String.valueOf(dto));
    	return Response.ok(bcSlppTcrService.updateSlppUseYn(dto));
    }

    /**
     * 교사 대화 시간 조회
     *
     * @param optTxbId : String
     * @return BcSlppTmSetmDto
     */
    @Operation(summary="교사 대화 시간 조회", description="교사 대화 시간 조회한다")
    @GetMapping(value = "/selectSlppTmSetm")
    public ResponseDto<BcSlppTmSetmDto> selectSlppTmSetm(String optTxbId) {
        log.info("selectSlppTmSetm: " + optTxbId );
        return Response.ok(bcSlppTcrService.selectSlppTmSetm(optTxbId));
    }
    
    
    /**
	 * 읽지 않은 대화 존재여부 조회
	 * @param dto : BcSlppPagingRequestDto
	 * @return int
	 */
    @Operation(summary="읽지 않은 대화 여부 조회", description="읽지 않은 대화 존재 여부를 조회한다.")
    @GetMapping(value = "/selectSlppCofmCnt")
    public ResponseDto<Integer> selectSlppCofmCnt(BcSlppPagingRequestDto dto) {
        log.info("selectSlppList" + dto);
        return Response.ok(bcSlppTcrService.selectSlppCofmCnt(dto));
    }
    
    

}
