package com.aidt.api.sl.inidat.stu;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.api.sl.inidat.tcr.SlIniDatTcrService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-01 11:53:04
 * @modify date 2024-07-01 11:53:04
 * @desc SlIniDatStu  특별학습 초기데이터작성 Controller
 */

@Slf4j
@Tag(name="[sl] 특별학습초기데이터작성[SlIniDatStu]", description = "로그인한 학생의 특별학습 초기데이터를 작성한다.")
@RestController
@RequestMapping("/api/v1/sl/stu/inidat")
public class SlIniDatStuController {

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
    private SlIniDatStuService slIniDatStuService;
	
	@Autowired
	private SlIniDatTcrService slIniDatTcrService; //교사용 서비스
	
	 /**
     * 특별학습 학생 추천학습 데이터 생성
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="특별학습 학생 추천학습 데이터 생성", description="특별학습의 난이도와 로그인사용자(학생)의 학습수준에 맞춰 추천 특별학습을 생성한다.")
    @PostMapping(value = "/insertRcmLrnDat")
    public ResponseDto<Integer> insertRcmLrnDat() {
        log.debug("Entrance insertRcmLrnDat");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        SlIniDatCondDto srhDto = new SlIniDatCondDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setStuUsrId(userDetails.getUsrId());
        srhDto.setClaId(userDetails.getClaId());

        return Response.ok(slIniDatStuService.insertRcmLrnDat(srhDto));
    }
    
    /**
     * 특별학습초기데이터작성 서비스
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="특별학습 초기데이터작성", description="해당 교과서에 매핑된 특별학습의 재구성데이터를 생성한다.")
    @PostMapping(value = "/registIniDat")
    public ResponseDto<Integer> registIniDat() {
        log.debug("Entrance registIniDat");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        SlIniDatCondDto srhDto = new SlIniDatCondDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setTcrUsrId(userDetails.getUsrId());

        return Response.ok(slIniDatTcrService.registIniDat(srhDto));
    }
}
