package com.aidt.api.al.pl.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.dto.AlPlTcrDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/*
 * 로그인한 교사가 담당하는 담당클래스를 조회하여, AI맞춤학습 재구성 초기데이터를 작성한다.
 * */
@Slf4j
@Service
public class AlPlIniDatTcrService {
	private final String MAPPER_NAMESPACE = "api.al.pl.tcr.AlPlIniDat.";
	
	@Autowired
	private CommonDao commonDao;

	
	@Transactional
    public int registIniDat(AlPlTcrDto cndDto) {
		log.debug("AlPlIniDatTcrService - usrId: "+cndDto.getUsrId()+ " , optTxbId: "+cndDto.getOptTxbId());
		int rtnVal = 0;
		
		//AI_지식맵노드재구성 및 교과서ID 조회
		AlPlTcrDto tcr = commonDao.select(MAPPER_NAMESPACE + "selectTxbId", cndDto);
		if(tcr.getTxbId() == null || tcr.getRowCnt() > 0) {
			log.debug("##### 이미 등록된 AI_지식맵노드재구성 데이터 = " + String.valueOf(tcr.getRowCnt()));
			return rtnVal;
		}
		//AI_지식맵노드재구성 초기정보 등록
		tcr.setUsrId(cndDto.getUsrId());
		int insCnt = commonDao.insert(MAPPER_NAMESPACE + "insertAiKmmpNodRcstn", tcr);
		log.debug("##### AI_지식맵노드재구성 등록건수 = " + String.valueOf(insCnt));
		
		return rtnVal;
	 }
}
