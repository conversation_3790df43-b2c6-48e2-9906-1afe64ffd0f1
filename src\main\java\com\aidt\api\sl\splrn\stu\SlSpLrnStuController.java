package com.aidt.api.sl.splrn.stu;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.splrn.dto.SlSpLrnDtlBanrViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnDtlViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnSrhDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-01-08 10:24:20
 * @modify : date 2024-01-08 10:24:20
 * @desc : special student controller
 */
@Tag(name="[sl] 특별학습 학생[SlSpLrnStu]", description="특별학습목록 및 상세 정보를 취한다")
@Slf4j
@RequestMapping("/api/v1/sl/stu/splrn")
@RestController
public class SlSpLrnStuController {

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private SlSpLrnStuService slSpLrnStuService;

	/**
	 * 특별학습 목록리스트
	 * 
	 * @return result
	 */
	@Operation(summary="특별학습목록 - 목록리스트조회(학생)", description="특별학습 목록을 다건 조회한다.")
	@PostMapping("/selectSpLrnList")
	public ResponseDto<List<SlSpLrnMainViewDto>> selectSpLrnList(@RequestBody SlSpLrnSrhDto srhDto) {
		log.debug("Entrance selectSpLrnList");
		
		String usrTpCd = "";
		
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		
		if (StringUtils.isEmpty(srhDto.getAllSrhYn())) {
            usrTpCd = securityUserDetailDto.getUsrTpCd();
        } else {
        	usrTpCd = "TE";
        }

		List<SlSpLrnMainViewDto> result = slSpLrnStuService.selectSpLrnList(securityUserDetailDto.getOptTxbId(),
				securityUserDetailDto.getUsrId(), usrTpCd);

		return Response.ok(result);
	}

//	/**
//	 * 추천 목록 리스트
//	 * 
//	 * @return 추천목록 리스트
//	 */
//	@Operation(summary="특별학습목록 - 추천목록리스트조회(학생)", description="특벽할습 추천목록리스트 조회(limit 4)한다.")
//	@PostMapping("/selectRcmList")
//	public ResponseDto<List<SlSpLrnRcmdDto>> selectRcmList() {
//		log.debug("Entrance selectRcmList");
//
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//
//		List<SlSpLrnRcmdDto> result = slSpLrnStuService.selectSpLrnStuRcmList(securityUserDetailDto.getUsrId(),
//				securityUserDetailDto.getOptTxbId(), securityUserDetailDto.getUsrTpCd());
//
//		return Response.ok(result);
//
//	}

	/**
	 * 특별학습 상세 - 상세 배너
	 * 
	 * @param spLrnId
	 * @return
	 */
	@Operation(summary="특별학습상세 - 상단 배너(학생)", description="특벽할습 상세 배너 목록 타이틀을 조회한다")
	@PostMapping("/selectSpLrnDtlBanner")
	public ResponseDto<SlSpLrnDtlBanrViewDto> selectSpLrnDtlBanner(
			@RequestBody SlSpLrnDtlBanrViewDto slSpLrnDtlBanrViewDto) {
		log.debug("Entrance selectSpLrnDtlBanner");
		String spLrnId = slSpLrnDtlBanrViewDto.getSpLrnId();
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		SlSpLrnDtlBanrViewDto result = slSpLrnStuService.selectSpLrnDtlBanrViewDto(spLrnId,
				securityUserDetailDto.getUsrId(), securityUserDetailDto.getOptTxbId());

		return Response.ok(result);

	}

	// /**
	// * 특별학습 상세 - 목록리스트조회
	// *
	// * @param spCtnId //특별콘텐츠 id
	// * @return selectSpLrnDtlList 상세목록리스트
	// */
	// @Tag(name="[sl] 특별학습상세목록조회(학생)", description="특벽할습 상세목록을 다건 조회한다")
	// @PostMapping("/selectSpLrnDtlList")
	// public ResponseDto<List<SlSpLrnDtlViewDto>> selectSpLrnDtlList(@RequestBody
	// SlSpLrnDtlViewDto slSpLrnDtlViewDto) {
	// log.debug("Entrance selectSpLrnDtlList");
	// String spLrnId = slSpLrnDtlViewDto.getSpLrnId();

	// CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
	// List<SlSpLrnDtlViewDto> result =
	// slSpLrnStuService.selectSpLrnStuDtlList(spLrnId,
	// securityUserDetailDto.getUsrId(), securityUserDetailDto.getOptTxbId());
	// return Response.ok(result);
	// }

	/**
	 * 특별학습 상세 - 목록리스트조회(V3.1)
	 * 
	 * @param spCtnId //특별콘텐츠 id
	 * @return selectSpLrnDtlList 상세목록리스트
	 */
	@Operation(summary="(V3.1)특별학습상세목록조회(학생)", description="특벽할습 상세목록을 다건 조회한다")
	@PostMapping("/selectSpLrnDtlList")
	public ResponseDto<List<SlSpLrnDtlViewDto>> selectSpDtlList(@RequestBody SlSpLrnDtlViewDto slSpLrnDtlViewDto) {
		log.debug("Entrance selectSpDtlList");
		String spLrnId = slSpLrnDtlViewDto.getSpLrnId();

		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		List<SlSpLrnDtlViewDto> result = slSpLrnStuService.selectSpDtlList(spLrnId,
				securityUserDetailDto.getUsrId(), securityUserDetailDto.getOptTxbId());
		return Response.ok(result);
	}

}
