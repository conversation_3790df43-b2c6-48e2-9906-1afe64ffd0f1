<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aidt.api.bc.guidMnl.mapper.ManualMapper">

    <sql id="selectManualNodeColumnSql">
        ${column}GUID_MNL_ID AS '${jdbc}GUID_MNL_ID',
        ${column}GUID_MNL_NOD_ID AS '${jdbc}GUID_MNL_NOD_ID',
        ${column}GUID_MNL_NOD_CD AS '${jdbc}GUID_MNL_NOD_CD',
        ${column}GUID_MNL_NOD_NM AS '${jdbc}GUID_MNL_NOD_NM',
        ${column}GUID_MNL_NOD_CN AS '${jdbc}GUID_MNL_NOD_CN',
        ${column}DPTH AS '${jdbc}DPTH',
        ${column}DOC_VI_ID AS '${jdbc}DOC_VI_ID',
        ${column}LWS_YN AS '${jdbc}LWS_YN',
        ${column}URNK_GUID_MNL_NOD_ID AS '${jdbc}URNK_GUID_MNL_NOD_ID',
        ${column}SRT_ORDN AS '${jdbc}SRT_ORDN',
        ${column}DEL_YN AS '${jdbc}DEL_YN',
        DATE_FORMAT(${column}CRT_DTM,'%Y.%m.%d') AS '${jdbc}CRT_DTM',
        DATE_FORMAT(${column}MDF_DTM,'%Y.%m.%d') AS '${jdbc}MDF_DTM',
        ${column}CRTR_ID AS '${jdbc}CRTR_ID',
        ${column}MDFR_ID AS '${jdbc}MDFR_ID'
    </sql>

    <sql id="selectManualNodeUploadColumnSql">
        ${column}GUID_MNL_NOD_FLE_ID AS '${jdbc}GUID_MNL_NOD_FLE_ID',
        ${column}GUID_MNL_NOD_ID AS '${jdbc}GUID_MNL_NOD_ID',
        ${column}FLE_PTH_NM AS '${jdbc}FLE_PTH_NM',
        ${column}ORGL_FLE_PTH_NM AS '${jdbc}ORGL_FLE_PTH_NM',
        ${column}GUID_MNL_NOD_FLE_NM AS '${jdbc}GUID_MNL_NOD_FLE_NM',
        ${column}FLE_TP_CD AS '${jdbc}FLE_TP_CD',
        ${column}DEL_YN AS '${jdbc}DEL_YN',
        ${column}VD_SMY_CN AS '${jdbc}VD_SMY_CN',
        DATE_FORMAT(${column}CRT_DTM,'%Y.%m.%d') AS '${jdbc}CRT_DTM',
        DATE_FORMAT(${column}MDF_DTM,'%Y.%m.%d') AS '${jdbc}MDF_DTM',
        ${column}CRTR_ID AS '${jdbc}CRTR_ID',
        ${column}MDFR_ID AS '${jdbc}MDFR_ID'
    </sql>


    <!-- ###################### SELECT ###################### -->

    <resultMap id="NodeResultMap" type="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
        <id property="guidMnlNodId" column="GUID_MNL_NOD_ID"/>
        <result property="key" column="nodeKey"/>
        <result property="guidMnlId" column="GUID_MNL_ID"/>
        <result property="guidMnlNodCd" column="GUID_MNL_NOD_CD"/>
        <result property="guidMnlNodNm" column="GUID_MNL_NOD_NM"/>
        <result property="guidMnlNodCn" column="GUID_MNL_NOD_CN"/>
        <result property="dpth" column="DPTH"/>
        <result property="lwsYn" column="LWS_YN"/>
        <result property="urnkGuidMnlNodId" column="URNK_GUID_MNL_NOD_ID"/>
        <result property="srtOrdn" column="SRT_ORDN"/>
        <result property="crtrId" column="CRTR_ID"/>
        <result property="mdfrId" column="MDFR_ID"/>
        <result property="crtDtm" column="CRT_DTM"/>
        <result property="mdfDtm" column="MDF_DTM"/>
        <result property="delYn" column="DEL_YN"/>
        <collection property="children" ofType="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
            <id property="guidMnlNodId" column="depth2.GUID_MNL_NOD_ID"/>
            <result property="key" column="depth2.nodeKey"/>
            <result property="guidMnlId" column="depth2.GUID_MNL_ID"/>
            <result property="guidMnlNodCd" column="depth2.GUID_MNL_NOD_CD"/>
            <result property="guidMnlNodNm" column="depth2.GUID_MNL_NOD_NM"/>
            <result property="guidMnlNodCn" column="depth2.GUID_MNL_NOD_CN"/>
            <result property="dpth" column="depth2.DPTH"/>
            <result property="docViId" column="depth2.DOC_VI_ID"/>
            <result property="lwsYn" column="depth2.LWS_YN"/>
            <result property="urnkGuidMnlNodId" column="depth2.URNK_GUID_MNL_NOD_ID"/>
            <result property="srtOrdn" column="depth2.SRT_ORDN"/>
            <result property="crtrId" column="depth2.CRTR_ID"/>
            <result property="mdfrId" column="depth2.MDFR_ID"/>
            <result property="crtDtm" column="depth2.CRT_DTM"/>
            <result property="mdfDtm" column="depth2.MDF_DTM"/>
            <result property="delYn" column="depth2.DEL_YN"/>
            <!--<collection property="children" ofType="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
                <id property="guidMnlNodId" column="depth3.GUID_MNL_NOD_ID"/>
                <result property="key" column="depth3.nodeKey"/>
                <result property="guidMnlId" column="depth3.GUID_MNL_ID"/>
                <result property="guidMnlNodCd" column="depth3.GUID_MNL_NOD_CD"/>
                <result property="guidMnlNodNm" column="depth3.GUID_MNL_NOD_NM"/>
                <result property="guidMnlNodCn" column="depth3.GUID_MNL_NOD_CN"/>
                <result property="dpth" column="depth3.DPTH"/>
                <result property="lwsYn" column="depth3.LWS_YN"/>
                <result property="urnkGuidMnlNodId" column="depth3.URNK_GUID_MNL_NOD_ID"/>
                <result property="srtOrdn" column="depth3.SRT_ORDN"/>
                <result property="crtrId" column="depth3.CRTR_ID"/>
                <result property="mdfrId" column="depth3.MDFR_ID"/>
                <result property="crtDtm" column="depth3.CRT_DTM"/>
                <result property="mdfDtm" column="depth3.MDF_DTM"/>
                <result property="delYn" column="depth3.DEL_YN"/>-->
                <collection property="upload" ofType="com.aidt.api.bc.guidMnl.dto.GuideManualUpload">
                    <id property="guidMnlNodFleId" column="upload.GUID_MNL_NOD_FLE_ID"/>
                    <result property="guidMnlNodId" column="upload.GUID_MNL_NOD_ID"/>
                    <result property="flePthNm" column="upload.FLE_PTH_NM"/>
                    <result property="orglFlePthNm" column="upload.ORGL_FLE_PTH_NM"/>
                    <result property="guidMnlNodFleNm" column="upload.GUID_MNL_NOD_FLE_NM"/>
                    <result property="fleTpCd" column="upload.FLE_TP_CD"/>
                    <result property="vdSmyCn" column="upload.VD_SMY_CN"/>
                    <result property="crtDtm" column="upload.CRT_DTM"/>
                    <result property="mdfDtm" column="upload.MDF_DTM"/>
                    <result property="crtrId" column="upload.CRTR_ID"/>
                    <result property="mdfrId" column="upload.MDFR_ID"/>
                    <result property="delYn" column="upload.DEL_YN"/>
                </collection>
        </collection>
    </resultMap>

    <select id="selectGuideManualNodeHierarchyByManualId" resultMap="NodeResultMap" parameterType="Long">
        SELECT
            <include refid="selectManualNodeColumnSql">
                <property name="column" value="depth1."/>
                <property name="jdbc" value=""/>
            </include>,
            CONCAT(depth1.SRT_ORDN, "") AS `nodeKey`,
            <include refid="selectManualNodeColumnSql">
                <property name="column" value="depth2."/>
                <property name="jdbc" value="depth2."/>
            </include>,
            CONCAT(depth1.SRT_ORDN, "-", depth2.SRT_ORDN) AS `depth2.nodeKey`,
            <!--<include refid="selectManualNodeColumnSql">
                <property name="column" value="depth3."/>
                <property name="jdbc" value="depth3."/>
            </include>,-->
            CONCAT(depth1.SRT_ORDN, "-", depth2.SRT_ORDN, "-") AS `depth2.nodeKey`,
            <include refid="selectManualNodeUploadColumnSql">
                <property name="column" value="upload."/>
                <property name="jdbc" value="upload."/>
            </include>
        FROM
            LMS_LRM.CM_GUID_MNL_NOD depth1
            LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL manual ON depth1.GUID_MNL_ID = manual.GUID_MNL_ID AND depth1.URNK_GUID_MNL_NOD_ID IS NULL AND depth1.DEL_YN = 'N'
            LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL_NOD depth2 ON depth2.URNK_GUID_MNL_NOD_ID = depth1.GUID_MNL_NOD_ID AND depth2.DEL_YN = 'N'
            LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL_NOD_FLE upload ON upload.GUID_MNL_NOD_ID = depth2.GUID_MNL_NOD_ID  AND upload.DEL_YN = 'N'
        WHERE manual.GUID_MNL_ID = #{manualId} AND manual.DEL_YN = 'N' AND depth1.TAB_DV_CD = 'T'
        ORDER BY depth1.SRT_ORDN, depth2.SRT_ORDN
    </select>

    <resultMap id="Node3DepthResultMap" type="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
                <id property="guidMnlNodId" column="depth3.GUID_MNL_NOD_ID"/>
                <result property="key" column="depth3.nodeKey"/>
                <result property="guidMnlId" column="depth3.GUID_MNL_ID"/>
                <result property="guidMnlNodCd" column="depth3.GUID_MNL_NOD_CD"/>
                <result property="guidMnlNodNm" column="depth3.GUID_MNL_NOD_NM"/>
                <result property="guidMnlNodCn" column="depth3.GUID_MNL_NOD_CN"/>
                <result property="dpth" column="depth3.DPTH"/>
                <result property="lwsYn" column="depth3.LWS_YN"/>
                <result property="urnkGuidMnlNodId" column="depth3.URNK_GUID_MNL_NOD_ID"/>
                <result property="srtOrdn" column="depth3.SRT_ORDN"/>
                <result property="crtrId" column="depth3.CRTR_ID"/>
                <result property="mdfrId" column="depth3.MDFR_ID"/>
                <result property="crtDtm" column="depth3.CRT_DTM"/>
                <result property="mdfDtm" column="depth3.MDF_DTM"/>
                <result property="delYn" column="depth3.DEL_YN"/>
                <collection property="upload" ofType="com.aidt.api.bc.guidMnl.dto.GuideManualUpload">
                    <id property="guidMnlNodFleId" column="upload.GUID_MNL_NOD_FLE_ID"/>
                    <result property="guidMnlNodId" column="upload.GUID_MNL_NOD_ID"/>
                    <result property="flePthNm" column="upload.FLE_PTH_NM"/>
                    <result property="orglFlePthNm" column="upload.ORGL_FLE_PTH_NM"/>
                    <result property="guidMnlNodFleNm" column="upload.GUID_MNL_NOD_FLE_NM"/>
                    <result property="fleTpCd" column="upload.FLE_TP_CD"/>
                    <result property="crtDtm" column="upload.CRT_DTM"/>
                    <result property="mdfDtm" column="upload.MDF_DTM"/>
                    <result property="crtrId" column="upload.CRTR_ID"/>
                    <result property="mdfrId" column="upload.MDFR_ID"/>
                    <result property="delYn" column="upload.DEL_YN"/>
                </collection>
    </resultMap>

    <select id="selectGuideManualNode3DepthAndUploadByManualId" resultMap="Node3DepthResultMap" parameterType="Long">
        SELECT
        <include refid="selectManualNodeColumnSql">
            <property name="column" value="depth3."/>
            <property name="jdbc" value="depth3."/>
        </include>,
        CONCAT(depth1.SRT_ORDN, "-", depth2.SRT_ORDN, "-", depth3.SRT_ORDN) AS `depth3.nodeKey`,
        <include refid="selectManualNodeUploadColumnSql">
            <property name="column" value="upload."/>
            <property name="jdbc" value="upload."/>
        </include>
        FROM LMS_LRM.CM_GUID_MNL_NOD depth1
        LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL manual ON depth1.GUID_MNL_ID = manual.GUID_MNL_ID AND depth1.URNK_GUID_MNL_NOD_ID IS NULL AND depth1.DEL_YN = 'N'
        LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL_NOD depth2 ON depth2.URNK_GUID_MNL_NOD_ID = depth1.GUID_MNL_NOD_ID AND depth2.DEL_YN = 'N'
        LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL_NOD_FLE upload ON upload.GUID_MNL_NOD_ID = depth2.GUID_MNL_NOD_ID AND upload.DEL_YN = 'N'
        WHERE manual.GUID_MNL_ID = #{manualId} AND manual.DEL_YN = 'N'
        ORDER BY depth1.SRT_ORDN, depth2.SRT_ORDN
    </select>

    <select id="selectGuideManualNodeByNodeId" parameterType="Long" resultType="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
        SELECT * FROM LMS_LRM.CM_GUID_MNL_NOD WHERE GUID_MNL_NOD_ID = #{nodeId} AND DEL_YN = 'N' ORDER BY SRT_ORDN
    </select>

    <select id="selectGuideManualByDbId" parameterType="Long" resultType="com.aidt.api.bc.guidMnl.dto.GuideManual">
        SELECT * FROM LMS_LRM.CM_GUID_MNL WHERE GUID_MNL_ID = #{guidMnlId} AND DEL_YN = 'N'
    </select>

    <select id="selectGuidManualPdfFileByVerInfo" parameterType="Long" resultType="Integer">
        SELECT
            VER_INFO
        FROM LMS_LRM.CM_GUID_MNL_FLE
        WHERE GUID_MNL_ID = #{guidMnlId} AND TXB_ID = #{txbId}
        ORDER BY ver_info DESC
        LIMIT 1
    </select>

    <select id="selectGuideManualByNodeId" parameterType="Long" resultType="com.aidt.api.bc.guidMnl.dto.GuideManual">
        SELECT * FROM LMS_LRM.CM_GUID_MNL A INNER JOIN LMS_LRM.CM_GUID_MNL_NOD B ON A.GUID_MNL_ID = B.GUID_MNL_ID WHERE GUID_MNL_NOD_ID = #{nodeId}
    </select>

    <select id="selectGuideManualById" parameterType="Long" resultType="com.aidt.api.bc.guidMnl.dto.GuideManual">
        SELECT * FROM LMS_LRM.CM_GUID_MNL A WHERE GUID_MNL_ID = #{id}
    </select>

    <select id="selectGuideManuals" parameterType="String" resultType="hashMap">
        SELECT
            textbook.TXB_NM,
            textbook.AUTR_NM,
            textbook.PBLS_NM,
            textbook.SCHL_GRD_CD,
            manual.TXB_ID
        FROM LMS_LRM.CM_GUID_MNL manual
            INNER JOIN LMS_CMS.BC_TXB textbook ON manual.TXB_ID = textbook.TXB_ID
        WHERE manual.USR_TP_CD = #{role} AND manual.DEL_YN = 'N'
        ORDER BY textbook.TXB_ID
    </select>

    <select id="selectTextbookCode" parameterType="Long" resultType="String">
        SELECT
           TXB_CD
        FROM LMS_CMS.BC_TXB
        WHERE TXB_ID = #{textbookId}
            LIMIT 1
    </select>


    <select id="selectGuideManualByTextbookId" parameterType="Map" resultMap="ManualMap">
        SELECT
            GUID_MNL_ID,
            TXB_ID,
            GUID_MNL_CD,
            USR_TP_CD,
            DATE_FORMAT(CRT_DTM,'%Y.%m.%d %r') AS 'CRT_DTM',
                DATE_FORMAT(MDF_DTM,'%Y.%m.%d %r') AS 'MDF_DTM',
                DEL_YN
        FROM LMS_LRM.CM_GUID_MNL
        WHERE TXB_ID = #{textbookId}
          AND USR_TP_CD = #{role}
          AND DEL_YN = 'N'
        ORDER BY CRT_DTM ASC
            LIMIT 1
    </select>

    <resultMap id="ManualMap" type="com.aidt.api.bc.guidMnl.dto.GuideManual">
        <id property="guidMnlId" column="GUID_MNL_ID"/>
        <result property="txbId" column="TXB_ID"/>
        <result property="guidMnlCd" column="GUID_MNL_CD"/>
        <result property="tabDvCd" column="TAB_DV_CD"/>
        <result property="docViId" column="DOC_VI_ID"/>
        <result property="usrTpCd" column="USR_TP_CD"/>
        <result property="crtDtm" column="CRT_DTM"/>
        <result property="mdfDtm" column="MDF_DTM"/>
        <result property="delYn" column="DEL_YN"/>
        <result property="dbId" column="DB_ID"/>
        <collection property="nodes" ofType="NodeResultMap" column="GUID_MNL_ID" select="selectGuideManualNodeHierarchyByManualId"/>
    </resultMap>

    <select id="selectGuideManualUploadByNodeId" parameterType="Long" resultType="com.aidt.api.bc.guidMnl.dto.GuideManualUpload">
        SELECT * FROM LMS_LRM.CM_GUID_MNL_NOD_FLE WHERE GUID_MNL_NOD_ID = #{nodeId} AND DEL_YN = 'N'
    </select>

    <select id="selectAllNodeIdsByManualId" parameterType="Long" resultType="Long">
        SELECT GUID_MNL_NOD_ID FROM LMS_LRM.CM_GUID_MNL_NOD WHERE GUID_MNL_ID = #{id} AND DEL_YN = 'N'
    </select>

    <select id="selectSiblingNodes" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
        SELECT * FROM LMS_LRM.CM_GUID_MNL_NOD
        WHERE
            <choose>
                <when test="urnkGuidMnlNodId != null and urnkGuidMnlNodId != ''">
                    URNK_GUID_MNL_NOD_ID = #{urnkGuidMnlNodId}
                </when>
                <otherwise>
                    URNK_GUID_MNL_NOD_ID IS NULL
                </otherwise>
            </choose>
            AND DPTH = #{dpth}
            AND GUID_MNL_ID = #{guidMnlId}
            AND DEL_YN = 'N'
        ORDER BY SRT_ORDN, GUID_MNL_NOD_ID DESC
    </select>

    <select id="selectNodeUploadByManualId" parameterType="Long" resultType="Long">
        select depth3.GUID_MNL_NOD_ID
        from LMS_LRM.CM_GUID_MNL_NOD depth3
            join LMS_LRM.CM_GUID_MNL_NOD_FLE upload on depth3.GUID_MNL_NOD_ID = upload.GUID_MNL_NOD_ID
        where depth3.GUID_MNL_ID = #{guidMnlId};
    </select>

    <!-- ###################### INSERT ###################### -->

    <insert id="insertGuideManual" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManual" keyProperty="guidMnlId">
        INSERT INTO LMS_LRM.CM_GUID_MNL (
            TXB_ID,
            USR_TP_CD,
            GUID_MNL_CD,
            DEL_YN,
            CRTR_ID,
            CRT_DTM,
            MDFR_ID,
            MDF_DTM,
            DB_ID
        ) VALUES (
            #{txbId},
            #{usrTpCd},
            #{guidMnlCd},
            "N",
            #{crtrId},
            NOW(),
            #{mdfrId},
            NOW(),
            #{dbId}
        )
    </insert>

    <insert id="insertGuideManualNode" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualNode" useGeneratedKeys="true" keyProperty="guidMnlNodId">
        INSERT INTO LMS_LRM.CM_GUID_MNL_NOD (
            GUID_MNL_ID,
            URNK_GUID_MNL_NOD_ID,
            GUID_MNL_NOD_NM,
            GUID_MNL_NOD_CD,
            SRT_ORDN,
            LWS_YN,
            GUID_MNL_NOD_CN,
            DPTH,
            DEL_YN,
            CRTR_ID,
            CRT_DTM,
            MDFR_ID,
            MDF_DTM,
            DB_ID
        ) VALUES (
            #{guidMnlId},
            #{urnkGuidMnlNodId},
            #{guidMnlNodNm},
            #{guidMnlNodCd},
            #{srtOrdn},
            #{lwsYn},
            #{guidMnlNodCn},
            #{dpth},
            "N",
            #{crtrId},
            NOW(),
            #{mdfrId},
            NOW(),
            #{dbId}
        )
    </insert>

    <insert id="multiInsertGuideManualNodes" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualNode" useGeneratedKeys="true" keyProperty="guidMnlNodId">
        INSERT INTO LMS_LRM.CM_GUID_MNL_NOD (
            GUID_MNL_ID,
            URNK_GUID_MNL_NOD_ID,
            GUID_MNL_NOD_NM,
            GUID_MNL_NOD_CD,
            SRT_ORDN,
            LWS_YN,
            GUID_MNL_NOD_CN,
            DPTH,
            DEL_YN,
            CRTR_ID,
            CRT_DTM,
            MDFR_ID,
            MDF_DTM,
            DB_ID
        ) VALUES
        <foreach item="node" collection="nodes" separator=",">
        (
            #{node.guidMnlId},
            #{node.urnkGuidMnlNodId},
            #{node.guidMnlNodNm},
            #{node.guidMnlNodCd},
            #{node.srtOrdn},
            #{node.lwsYn},
            #{node.guidMnlNodCn},
            #{node.dpth},
            "N",
            #{node.crtrId},
            NOW(),
            #{node.mdfrId},
            NOW(),
            #{node.dbId}
        )
        </foreach>
    </insert>

    <insert id="insertGuideManualContentUpload" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualUpload" useGeneratedKeys="true" keyProperty="guidMnlNodFleId">
        INSERT INTO LMS_LRM.CM_GUID_MNL_NOD_FLE (
            GUID_MNL_NOD_ID,
            FLE_PTH_NM,
            ORGL_FLE_PTH_NM,
            GUID_MNL_NOD_FLE_NM,
            FLE_TP_CD,
            VD_SMY_CN,
            DEL_YN,
            CRT_DTM,
            MDF_DTM,
            CRTR_ID,
            MDFR_ID,
            DB_ID
        ) VALUES (
            #{guidMnlNodId},
            #{flePthNm},
            #{orglFlePthNm},
            #{guidMnlNodFleNm},
            #{fleTpCd},
            #{vdSmyCn},
            "N",
            NOW(),
            NOW(),
            #{crtrId},
            #{mdfrId},
            #{dbId}
        )
    </insert>

    <insert id="insertGuidManualPdfFile" parameterType="com.aidt.api.bc.guidMnl.dto.GuidManualPdfFile" useGeneratedKeys="true" keyProperty="guidMnlFleId">
        INSERT INTO LMS_LRM.CM_GUID_MNL_FLE (
            TXB_ID,
            GUID_MNL_ID,
            DOC_VI_ID,
            FLE_NM,
            FLE_ORGL_NM,
            FLE_PTH_NM,
            USE_YN,
            VER_INFO,
            CRTR_ID,
            CRT_DTM,
            MDFR_ID,
            MDF_DTM,
            DB_ID
        ) VALUES (
                     #{txbId},
                     #{guidMnlId},
                     #{docViId},
                     #{fleNm},
                     #{fleOrglNm},
                     #{flePthNm},
                     'Y',
                     #{verInfo},
                     #{crtrId},
                     NOW(),
                     #{mdfrId},
                     NOW(),
                     #{dbId}
                 )
    </insert>

    <!-- ###################### UPDATE ###################### -->

    <update id="updateGuideManualNode" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
        UPDATE LMS_LRM.CM_GUID_MNL_NOD
        <trim prefix="SET" suffixOverrides=",">
            GUID_MNL_NOD_NM = #{guidMnlNodNm},
            GUID_MNL_NOD_CN = #{guidMnlNodCn},
            <if test="srtOrdn != null and srtOrdn != ''">
                SRT_ORDN = #{srtOrdn},
            </if>
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        </trim>
        WHERE 1 = 1
            AND GUID_MNL_NOD_ID = #{guidMnlNodId}
    </update>

    <update id="deleteGuideManualNodeUploads" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualUpdate">
        UPDATE LMS_LRM.CM_GUID_MNL_NOD_FLE
        SET
            DEL_YN = 'Y',
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        WHERE 1=1
            AND GUID_MNL_NOD_FLE_ID IN (
                <foreach collection="ids" item="id" separator=",">
                    #{id}
                </foreach>
            )
    </update>

    <update id="deleteGuideManualNodes" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualUpdate">
        UPDATE LMS_LRM.CM_GUID_MNL_NOD
        SET
            DEL_YN = 'Y',
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        WHERE 1=1
            AND GUID_MNL_NOD_ID IN (
                <foreach collection="ids" item="id" separator=",">
                    #{id}
                </foreach>
            )
    </update>

    <update id="deleteGuideManual" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualUpdate">
        UPDATE LMS_LRM.CM_GUID_MNL
        SET
            DEL_YN = 'Y',
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        WHERE 1 = 1
            AND GUID_MNL_ID IN (
                <foreach collection="ids" item="id" separator=",">
                    #{id}
                </foreach>
            )
    </update>

    <update id="updateVersionGuidManualPdfFile" parameterType="Long">
        UPDATE LMS_LRM.CM_GUID_MNL_FLE
        SET
            USE_YN = 'N'
        WHERE GUID_MNL_ID = #{guidMnlId}
          AND TXB_ID = #{txbId}
          AND USE_YN = 'Y';
    </update>

    <!-- ###################### DELETE ###################### -->
    <delete id="forceDeleteGuideManual" parameterType="Long">
        DELETE FROM LMS_LRM.CM_GUID_MNL
        WHERE GUID_MNL_ID = #{id}
    </delete>

    <delete id="forceDeleteGuideManualNodes" parameterType="List">
        DELETE FROM LMS_LRM.CM_GUID_MNL_NOD
        WHERE GUID_MNL_NOD_ID IN (
        <foreach collection="list" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <delete id="forceDeleteGuideManualNodesByManualId" parameterType="Long">
        DELETE FROM LMS_LRM.CM_GUID_MNL_NOD
        WHERE GUID_MNL_ID = #{id}
    </delete>

    <delete id="forceDeleteGuideManualContentUploads" parameterType="List">
        DELETE FROM LMS_LRM.CM_GUID_MNL_NOD_FLE
        WHERE GUID_MNL_NOD_FLE_ID IN (
        <foreach collection="list" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <delete id="forceDeleteGuideManualContentUploadsByManualId" parameterType="Long">
        DELETE FROM LMS_LRM.CM_GUID_MNL_NOD_FLE
        WHERE GUID_MNL_NOD_ID IN (
            SELECT node.GUID_MNL_NOD_ID FROM LMS_LRM.CM_GUID_MNL_NOD node WHERE node.GUID_MNL_ID = #{id}
        )
    </delete>


    <select id="selectGuideManualPdfFiles" parameterType="List">
    /* selectGuideManualPdfFiles */
        SELECT * FROM CM_GUID_MNL_FLE
        WHERE 1=1
            AND TXB_ID = #{txbId}
            AND GUID_MNL_ID = #{guidMnlId}
        ORDER BY VER_INFO DESC
        LIMIT 100
    </select>

    <update id="updatePdfFileUseYn" parameterType="com.aidt.api.bc.guidMnl.dto.GuidManualPdfFile">
        /* updatePdfFileUseYn */
        UPDATE CM_GUID_MNL_FLE
        SET
            USE_YN = CASE
                         WHEN GUID_MNL_FLE_ID = #{guidMnlFleId} THEN 'Y'
                         ELSE 'N'
                     END,
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        WHERE 1=1
          AND TXB_ID = #{txbId}
          AND GUID_MNL_ID = #{guidMnlId}
    </update>

    <select id="selectGuideManualFileData" parameterType="com.aidt.api.bc.guidMnl.dto.GuidManualPdfFile" resultType="com.aidt.api.bc.guidMnl.dto.GuidManualPdfFileDownload">
        SELECT FLE_NM,
               FLE_ORGL_NM,
               FLE_PTH_NM
        FROM CM_GUID_MNL_FLE
        WHERE 1=1
        <choose>
            <when test="guidMnlFleId != null">
                AND GUID_MNL_FLE_ID = #{guidMnlFleId}
            </when>
            <otherwise>
                AND TXB_ID = #{txbId}
                AND GUID_MNL_ID = #{guidMnlId}
                AND USE_YN = 'Y'
            </otherwise>
        </choose>
        LIMIT 1
    </select>



    <!-- 1depth만 불러오기 시작 -->

    <sql id="selectManual1depthColumnSql">
        ${column}GUID_MNL_ID AS '${jdbc}GUID_MNL_ID',
        ${column}GUID_MNL_NOD_ID AS '${jdbc}GUID_MNL_NOD_ID',
        ${column}GUID_MNL_NOD_CD AS '${jdbc}GUID_MNL_NOD_CD',
        ${column}GUID_MNL_NOD_NM AS '${jdbc}GUID_MNL_NOD_NM',
        ${column}GUID_MNL_NOD_CN AS '${jdbc}GUID_MNL_NOD_CN',
        ${column}DPTH AS '${jdbc}DPTH',
        ${column}DOC_VI_ID AS '${jdbc}DOC_VI_ID',
        ${column}LWS_YN AS '${jdbc}LWS_YN',
        ${column}URNK_GUID_MNL_NOD_ID AS '${jdbc}URNK_GUID_MNL_NOD_ID',
        ${column}SRT_ORDN AS '${jdbc}SRT_ORDN',
        ${column}DEL_YN AS '${jdbc}DEL_YN',
        DATE_FORMAT(${column}CRT_DTM,'%Y.%m.%d') AS '${jdbc}CRT_DTM',
        DATE_FORMAT(${column}MDF_DTM,'%Y.%m.%d') AS '${jdbc}MDF_DTM',
        ${column}CRTR_ID AS '${jdbc}CRTR_ID',
        ${column}MDFR_ID AS '${jdbc}MDFR_ID'
    </sql>

    <select id="selectGuideManual1depthByTextbookId" parameterType="Map" resultMap="Manual1depthMap">
        /* selectGuideManual1depthByTextbookId */
        SELECT
            GUID_MNL_ID,
            TXB_ID,
            GUID_MNL_CD,
            USR_TP_CD,
            DATE_FORMAT(CRT_DTM,'%Y.%m.%d %r') AS 'CRT_DTM',
                DATE_FORMAT(MDF_DTM,'%Y.%m.%d %r') AS 'MDF_DTM',
                DEL_YN
        FROM LMS_LRM.CM_GUID_MNL
        WHERE TXB_ID = #{textbookId}
          AND USR_TP_CD = #{role}
          AND DEL_YN = 'N'
        ORDER BY CRT_DTM ASC
            LIMIT 1
    </select>

    <resultMap id="Manual1depthMap" type="com.aidt.api.bc.guidMnl.dto.GuideManual">
        <id property="guidMnlId" column="GUID_MNL_ID"/>
        <result property="txbId" column="TXB_ID"/>
        <result property="guidMnlCd" column="GUID_MNL_CD"/>
        <result property="usrTpCd" column="USR_TP_CD"/>
        <result property="crtDtm" column="CRT_DTM"/>
        <result property="mdfDtm" column="MDF_DTM"/>
        <result property="delYn" column="DEL_YN"/>
        <result property="dbId" column="DB_ID"/>
        <collection property="nodes" ofType="1depthResultMap" column="GUID_MNL_ID" select="selectGuideManual1depthByManualId"/>
    </resultMap>

    <!-- ###################### SELECT ###################### -->

    <resultMap id="1depthResultMap" type="com.aidt.api.bc.guidMnl.dto.GuideManualNode">
        <id property="guidMnlNodId" column="GUID_MNL_NOD_ID"/>
        <result property="key" column="nodeKey"/>
        <result property="guidMnlId" column="GUID_MNL_ID"/>
        <result property="guidMnlNodCd" column="GUID_MNL_NOD_CD"/>
        <result property="guidMnlNodNm" column="GUID_MNL_NOD_NM"/>
        <result property="guidMnlNodCn" column="GUID_MNL_NOD_CN"/>
        <result property="dpth" column="DPTH"/>
        <result property="docViId" column="DOC_VI_ID"/>
        <result property="lwsYn" column="LWS_YN"/>
        <result property="urnkGuidMnlNodId" column="URNK_GUID_MNL_NOD_ID"/>
        <result property="srtOrdn" column="SRT_ORDN"/>
        <result property="crtrId" column="CRTR_ID"/>
        <result property="mdfrId" column="MDFR_ID"/>
        <result property="crtDtm" column="CRT_DTM"/>
        <result property="mdfDtm" column="MDF_DTM"/>
        <result property="delYn" column="DEL_YN"/>
    </resultMap>

    <select id="selectGuideManual1depthByManualId" resultMap="1depthResultMap" parameterType="Long">
    /* selectGuideManual1depthByManualId */
        SELECT
        <include refid="selectManual1depthColumnSql">
            <property name="column" value="depth1."/>
            <property name="jdbc" value=""/>
        </include>,
        CONCAT(depth1.SRT_ORDN, "") AS `nodeKey`
        FROM
        LMS_LRM.CM_GUID_MNL_NOD depth1
        LEFT OUTER JOIN LMS_LRM.CM_GUID_MNL manual ON depth1.GUID_MNL_ID = manual.GUID_MNL_ID AND depth1.URNK_GUID_MNL_NOD_ID IS NULL AND depth1.DEL_YN = 'N'
        WHERE manual.GUID_MNL_ID = #{manualId} AND manual.DEL_YN = 'N' AND depth1.TAB_DV_CD = 'M'
        GROUP BY depth1.GUID_MNL_NOD_ID
        ORDER BY depth1.SRT_ORDN
    </select>

    <!-- 1depth만 불러오기 끝-->


    <!-- DbId 조회 -->
    <select id="selectDbIdByTextbookId" resultType="String" parameterType="Long">
        /* selectDbIdByTextbookId */
        SELECT AUTR_TXB_CD FROM LMS_CMS.BC_TXB WHERE TXB_ID = #{textbookId}
    </select>


    <!-- 있는지 확인 -->
    <select id="selectFileNode" resultType="int" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualFileUploadReqDto">
        /* selectFileNode */
        SELECT COUNT(*) FROM LMS_LRM.CM_GUID_MNL_NOD_FLE
        WHERE 1=1
            AND GUID_MNL_NOD_ID = #{guidMnlNodId}
    </select>

    <!-- Content: pdf -->
    <update id="updateFileNodeDelYn" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualFileUploadReqDto">
        /* updateFileNodeDelYn */
        UPDATE LMS_LRM.CM_GUID_MNL_NOD_FLE
        SET DEL_YN = 'Y'
        WHERE GUID_MNL_NOD_FLE_ID = #{guidMnlNodFleId}
    </update>

    <!-- Content: pdf (DocViId 수정) -->
    <update id="updateNodeDocViId" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualFileUploadReqDto">
        /* updateNodeDocViId */
        UPDATE LMS_LRM.CM_GUID_MNL_NOD
        SET DOC_VI_ID = #{docViId},
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        WHERE GUID_MNL_NOD_ID = #{guidMnlNodId}
    </update>

    <update id="updateFileNode" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualUpload">
        /* updateGuideManualNodeFile */
        update LMS_LRM.CM_GUID_MNL_NOD_FLE
        SET DEL_YN = 'N',
            GUID_MNL_NOD_FLE_NM = #{guidMnlNodFleNm},
            ORGL_FLE_PTH_NM = #{orglFlePthNm},
            FLE_PTH_NM = #{flePthNm},
            FLE_TP_CD = #{fleTpCd},
            VD_SMY_CN = #{vdSmyCn},
            MDFR_ID = #{mdfrId},
            MDF_DTM = NOW()
        WHERE GUID_MNL_NOD_FLE_ID = #{guidMnlNodFleId}
    </update>

    <insert id="insertFileNode" parameterType="com.aidt.api.bc.guidMnl.dto.GuideManualUpload">
        INSERT INTO LMS_LRM.CM_GUID_MNL_NOD_FLE
            VALUES ()
    </insert>
</mapper>