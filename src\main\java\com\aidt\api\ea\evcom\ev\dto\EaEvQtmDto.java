package com.aidt.api.ea.evcom.ev.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 13:58:오후 1:58
 * @modify date 2024-02-15 13:58:오후 1:58
 * @desc   평가 공통 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvQtmDto {
    //문항 테이블
    @Parameter(name="문항 ID", required = true)
    @NotBlank(message = "{field.required}")
    private String qtmId;    
    @Parameter(name="문항 순번")
    private int qtmOrdn;
    
    @Parameter(name="난이도코드")
    private String difficultyCode;
    @Parameter(name="난이도명")
    private String difficultyName;

    @Parameter(name="삭제여부")
	private String delYn;	
	@Parameter(name="삭제일시")
	private String delDtm;   
    
    @Parameter(name="대단원ID")
    private String lluId;
    @Parameter(name="대단원명")
    private String lluNm;
    @Parameter(name="토픽ID")
    private String topicId;
    @Parameter(name="토픽명")
    private String topicNm;
    
    @Parameter(name="지식맵토픽ID")
    private String topicIdKmmp;    
}
