package com.aidt.api.sl.lrnwif.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-07 14:13:26
 * @modify : date 2024-03-07 14:13:26
 * @desc : SlLrnwCtnDto 특별학습 콘텐츠 목록
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwAtvDto {
	
	@Parameter(name="학습활동")
	private List<SlLrnwAtvMetaDto> lrnAtvList;
	
	

}
