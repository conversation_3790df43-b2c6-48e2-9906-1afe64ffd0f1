<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.sbclrn.tcr">
	<!-- 교과학습 활동 리스트 조회 -->
	<select id="selectLrnAtvList" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvDto">
		SELECT * FROM
        (SELECT
		       A.OPT_TXB_ID  				/* 운영교과서ID */
		      ,A.LRMP_NOD_ID  				/* 학습맵노드ID */
		      ,A.LRN_ATV_ID  				/* 학습활동ID */
		      ,A.LRN_STP_ID  				/* 학습단계ID */
			  ,B.LRN_STP_DV_CD				/* 학습단계 구분코드 */
			  ,B.LRN_STP_NM					/* 학습단계명 */
		      ,A.CTN_CD  					/* LCMS 학습활동코드(varchar(10)) */
		      ,A.LRN_ATV_NM  				/* 문항아이디 or 파일명 */
		      ,A.CTN_TP_CD  				/* 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
		      ,A.USE_YN  					/* 사용여부 */
		      ,A.RCSTN_ORDN  				/* 재구성순서 */
		      ,A.EV_ID  					/* 평가ID */
		      ,B.SRT_ORDN
		FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
		LEFT JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
			ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
			AND A.LRN_STP_ID = B.LRN_STP_ID 
		WHERE A.OPT_TXB_ID = #{optTxbId} 	
		AND A.LRMP_NOD_ID = #{lrmpNodId}  	
		
		UNION ALL
		
		SELECT M.OPT_TXB_ID  				/* 운영교과서ID */
		      ,M.LRMP_NOD_ID  				/* 학습맵노드ID */
		      ,IFNULL(C.LRN_ATV_ID,M.TCR_REG_CTN_ID) AS LRN_ATV_ID  				/* 학습활동ID */
		      ,M.LRN_STP_ID  				/* 학습단계ID */
			  ,S.LRN_STP_DV_CD				/* 학습단계 구분코드 */
			  ,S.LRN_STP_NM					/* 학습단계명 */
		      ,'' AS CTN_CD  					/* LCMS 학습활동코드(varchar(10)) */
		      ,C.TCR_REG_CTN_NM AS LRN_ATV_NM  				/* 문항아이디 or 파일명 */
		      ,C.TP_CD AS CTN_TP_CD  				/* 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
		      ,M.USE_YN  					/* 사용여부 */
		      ,M.RCSTN_ORDN  				/* 재구성순서 */
		      ,'' AS EV_ID  					/* 평가ID */
		      ,S.SRT_ORDN
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		WHERE M.OPT_TXB_ID = #{optTxbId} 
		AND M.LRMP_NOD_ID = #{lrmpNodId} 
		AND M.DEL_YN = 'N'
		AND M.USE_YN = 'Y') ATV
		ORDER BY ATV.SRT_ORDN ASC, ATV.RCSTN_ORDN ASC 

		/** 교과학습 김형준 TlSbcLrnTcr-Mapper.xml - selectLrnAtvList */
	</select>

	<!-- 교과학습 평가 학생 리스트 조회 -->
	<select id="selectSbcLrnEvStuList" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvStuDto">
		SELECT
			U.USR_ID,							/* 학생ID */
			U.USR_NM,							/* 학생이름 */
			U.STU_NO,							/* 학생번호 */
			U.CLA_ID,							/* 학급ID */
			O.OPT_TXB_ID,						/* 운영교과서ID */
			T.TCR_USR_ID,						/* 교사ID */
			E.EV_ID,							/* 평가ID */
			E.FNL_QST_CNT,						/* 문항수 */
			R.EV_CMPL_YN						/* 평가완료여부 */
		FROM LMS_LRM.CM_USR U  /* CM_사용자 */
		LEFT JOIN LMS_LRM.CM_OPT_TXB O  /* CM_운영교과서 */
			ON U.CLA_ID = O.CLA_ID
		LEFT JOIN LMS_LRM.CM_OPT_TCR T  /* CM_운영교사 */
			ON O.OPT_TXB_ID = T.OPT_TXB_ID
		LEFT JOIN LMS_LRM.EA_EV E  /* EA_평가 */
			ON O.OPT_TXB_ID = E.OPT_TXB_ID
			AND EV_ID = #{evId}
			AND E.USE_YN = 'Y'
			AND E.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.EA_EV_RS R /* EA_평가결과 */
			ON E.EV_ID = R.EV_ID
			AND U.USR_ID = R.USR_ID
		WHERE O.OPT_TXB_ID = #{optTxbId}
		AND T.TCR_USR_ID = #{tcrUsrId}
		AND U.USR_TP_CD = 'ST'
		ORDER BY U.STU_NO ASC

		/** 교과학습 김형준 TlSbcLrnTcr-Mapper.xml - selectSbcLrnEvStuList */
	</select>

	<!-- 교과학습 평가결과 조회 -->
	<select id="selectSbcLrnEvPst" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvPstDto">
		SELECT
			Q.QTM_ID,							/* 문항ID */
			IFNULL(A.CANS_YN, '') AS CANS_YN	/* 정답여부 */
		FROM LMS_LRM.EA_EV_QTM Q  /* EA_평가문항 */
		LEFT JOIN LMS_LRM.EA_EV_QTM_ANW A  /* EA_평가문항답변 */
			ON Q.EV_ID = A.EV_ID
			AND Q.QTM_ID = A.QTM_ID
			AND A.USR_ID = #{usrId}
		WHERE Q.EV_ID = #{evId}
		AND Q.DEL_YN = 'N'
		ORDER BY Q.QTM_ORDN ASC

		/** 교과학습 김형준 TlSbcLrnTcr-Mapper.xml - selectSbcLrnEvPst */
	</select>

	<!-- 교과학습 현재 풀이중인 문항 조회 -->
	<select id="selectCurQtmId" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnEvSrhDto" resultType="string">
		SELECT
			IFNULL(Q.QTM_ID, ' ') AS QTM_ID
		FROM LMS_LRM.EA_EV_QTM Q  /* EA_평가문항 */
		WHERE Q.EV_ID = #{evId}
		AND Q.QTM_ID NOT IN (SELECT QTM_ID 
								FROM LMS_LRM.EA_EV_QTM_ANW /* EA_평가문항답변 */
								WHERE EV_ID = #{evId}
								AND USR_ID = #{usrId})
		AND Q.DEL_YN = 'N'
		ORDER BY Q.QTM_ORDN ASC
		LIMIT 1

		/** 교과학습 김형준 TlSbcLrnTcr-Mapper.xml - selectCurQtmId */
	</select>

	<!-- 교과학습 과제출제건수조회 -->
	<select id="countEaAsnData" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnEaAsnSrhDto" resultType="int">
        SELECT
              COUNT(1) AS CNT
        FROM LMS_LRM.EA_ASN EA /* EA_과제 */
            INNER JOIN LMS_LRM.EA_ASN_RNGE EB /* EA_과제범위 */
                  ON EA.ASN_ID = EB.ASN_ID
                  AND EA.OPT_TXB_ID = EB.OPT_TXB_ID
                  AND EA.LRN_TP_CD = EB.LRN_TP_CD
        WHERE EA.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND EA.LRN_TP_CD = 'TL' /* 교과 학습 */
        AND EA.DEL_YN='N'
        AND EB.LU_NOD_ID = #{lluNodId}  /* 대단원ID */
        AND EB.TC_NOD_ID = #{tcNodId} /* 차시노드ID */
        AND EB.LRN_STP_DV_CD = #{lrnStpDvCd} /* 학습단계구분코드 CL=개념학습,WB=익힘책,EX=평가 */
        AND EB.DEL_YN = 'N'

		/** 교과학습 강성희 TlSbcLrnTcr-Mapper.xml - countEaAsnData */
	</select>

</mapper>