package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI center 통신 request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlPlCenterResponseDto {

		@Parameter(name="유저ID")
		private String usr_id;

		@Parameter(name="문항Id")
		private String qtm_id;
		
		@Parameter(name="KT모델 예측 정답률")
		private float infer;
}
