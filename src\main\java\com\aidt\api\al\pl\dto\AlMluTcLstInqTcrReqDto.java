package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc [AI맞춤학습 단원차시조회 request dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlMluTcLstInqTcrReqDto {

    @Parameter(name="운영교과서ID")
    private String optTxbId;

    @JsonProperty("mluLrmpNodId")
    @Parameter(name="중단원노드ID")
    private String mluLrmpNodId;

    @Parameter(name="사용자ID")
    private String usrId;
    
    @Parameter(name="중단원지식맵노드ID")
    private String mkLuLrmNodId;
}
