package com.aidt.api.al.pl.dto;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiRcmTsshQtmResponseDto {
	
	@Parameter(name="평가ID", required=true)
	@NotNull(message = "{field.required}")
    private Integer evId;
	
	@Parameter(name="문항ID")
	private String qtmId;
	
	@Parameter(name="사용자ID")
    private String usrId;
	
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    @Parameter(name="errorStatus")
    private String errorStatus;
    
    @Parameter(name="errorMessage")
    private String errorMessage;
	
}
