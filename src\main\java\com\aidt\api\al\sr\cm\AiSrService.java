package com.aidt.api.al.sr.cm;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.aidt.api.al.sr.dto.pf.AiSrPfAtvNmResDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultDto;
import com.aidt.api.al.sr.dto.pf.AiSrPfResultReqDto;
import com.aidt.api.al.sr.dto.sbc.AiSrAchCmtDto;
import com.aidt.api.al.sr.dto.sbc.AiSrQtmDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataDto;
import com.aidt.api.al.sr.dto.sbc.AiSrUserDataReqDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-25 17:48:50
 * @modify date 2024-05-25 17:48:50
 * @desc
 */
@Service
public class AiSrService {

    private final String MAPPER_NAMESPACE = "api.al.sr.tcr.";

    private final CommonDao commonDao;

    public AiSrService(CommonDao commonDao) {
        this.commonDao = commonDao;
    }

    public List<AiSrPfAtvNmResDto> getPfmcAtvList(String txbId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectSrPfBsList", txbId);
    }

    public List<AiSrPfResultDto> getPfmcAtvResultList(AiSrPfResultReqDto reqDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectSrPfResultList", reqDto.getAiPfBsDataId());
    }

    public List<AiSrQtmDto> selectQtmListByNodeId(List<String> usrIds, List<String> nodeIds, String optTxbId) {

        Map<String, Object> param = Map.of(
                "userIds", usrIds,
                "nodeIds", nodeIds,
                "optTxbId", optTxbId);
        return commonDao.selectList(MAPPER_NAMESPACE + "selectQtmListByNodeId", param);
    }

    public List<AiSrAchCmtDto> getAchCmt(String achBsCd) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAchCmtList", achBsCd);
    }

    public AiSrUserDataDto getSrUserDataFilePath(AiSrUserDataReqDto reqDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectSrUserDataFilePath", reqDto);
    }

    public AiSrUserDataDto getSrUserFile(AiSrUserDataReqDto reqDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectSrUserFile", reqDto);
    }

    public int insertSrUserFile(AiSrUserDataReqDto reqDto) {
        return commonDao.insert(MAPPER_NAMESPACE + "insertSrUserFile", reqDto);
    }

    public int updateSrUserFile(AiSrUserDataReqDto reqDto) {
        return commonDao.update(MAPPER_NAMESPACE + "updateSrUserFile", reqDto);

    }
}
