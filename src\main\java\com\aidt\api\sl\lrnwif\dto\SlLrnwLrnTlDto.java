package com.aidt.api.sl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-07 14:13:26
 * @modify : date 2024-03-07 14:13:26
 * @desc : SlLrnwLrnTlDto 학습도구dto
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwLrnTlDto {

	/** 학습도구유형코드 */
	@Parameter(name="학습도구유형코드")
	private String lrnTlTpCd;
	/** 학습도구유형명 */
	@Parameter(name="학습도구유형명")
	private String lrnTlTpNm;
	/** 학습도구기능코드 */
	@Parameter(name="학습도구기능코드")
	private String lrnTlKnCd;
	/** 학습도구명 */
	@Parameter(name="학습도구명")
	private String lrnTlKnNm;

}
