package com.aidt.api.ea.evcom.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.ea.evcom.adapter.EaEvQuestionAdapter;
import com.aidt.api.ea.evcom.dto.EaEvSppNtnResult;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true)
@Service
@RequiredArgsConstructor
public class EaEvSppNtnQueryService {

	private final EaEvQuestionAdapter eaEvQuestionAdapter;

	public List<EaEvSppNtnResult.EaEvSppNtnQuestion> getSppNtnQuestion(EaEvSppNtnResult eaEvSppNtnResult) {
		return eaEvQuestionAdapter.getSppNtnQuestions(eaEvSppNtnResult);
	}

}