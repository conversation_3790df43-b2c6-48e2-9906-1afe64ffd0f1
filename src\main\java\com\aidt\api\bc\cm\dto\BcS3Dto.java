package com.aidt.api.bc.cm.dto;

import javax.validation.constraints.NotBlank;

import lombok.Builder;
import lombok.Data;

public class BcS3Dto {

    @Data
    @Builder
    public static class FileUploadResponse {
        String originFileName;
        String newFileName;
        long fileSize;
        String contentType;
    }

    @Data
    @Builder
    public static class FileListResponse {
        String fileName;
        long fileSize;
        String contentType;
    }

    @Data
    public static class FileDownlaodRequest {
        @NotBlank(message = "{field.required}")
        String bucketName;

        @NotBlank(message = "{field.required}")
        String newFileName;

        @NotBlank(message = "{field.required}")
        String originFileName;
    }
}
