package com.aidt.api.al.fdbk.dto.req;

import com.aidt.api.al.fdbk.dto.AiFdbkDto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:56:58
 * @modify date 2024-07-11 15:56:58
 * @desc
 */
@Data
@Builder
public class AiFdbkReqDto {

    private String usrId;

    private String lrmpNodId;

    private String fdbkCn;

    public AiFdbkDto toDto(String optTxbId, String tcrId) {
        return AiFdbkDto.builder()
                .optTxbId(optTxbId)
                .usrId(this.usrId)
                .lrmpNodId(this.lrmpNodId)
                .fdbkCn(this.fdbkCn)
                .tcrId(tcrId)
                .build();
    }

}
