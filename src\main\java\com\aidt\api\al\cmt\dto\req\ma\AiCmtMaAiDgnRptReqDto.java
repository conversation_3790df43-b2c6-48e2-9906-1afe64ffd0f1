package com.aidt.api.al.cmt.dto.req.ma;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.cm.N02Dto;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaAiDgnRptDto;
import com.aidt.api.al.cmt.dto.req.cm.N02ReqDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtMaAiDgnRptReqDto {

    @Parameter(name="학교급코드(초등:E,중등:M,고등:H)", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^(E|M|H)$", message="학교급코드(초등:E,중등:M,고등:H)")
    private String schlGrdCd;

    @Parameter(name="성취수준 총평", required=true)
    private N02ReqDto total;

    @Parameter(name="강점 토픽", required=true)
    private String strthTpc;

    @Parameter(name="약점 토픽", required=true)
    private String wknsTpc;

    @Parameter(name="학습자속도유형코드(빠른:FS,보통:NM,느린:SL)", required=true)
    @Pattern(regexp = "^(FS|NM|SL)$", message="학습자속도유형코드(빠른:FS,보통:NM,느린:SL)")
    private String lrnrVelTpCd;

    private String evId;

    private String usrId;

    public AiCmtMaAiDgnRptDto toDto() {
        AiCmtLvlCalculator calculator = "H".equalsIgnoreCase(this.schlGrdCd)
                ? AiCmtLvlCalculator.MA_TYPE_50_80 : AiCmtLvlCalculator.MA_TYPE_60_80;

        N02Dto n02Dto = total.toDto(calculator);

        boolean isAll100 = Objects.equals(this.total.getQtmCnt().getCorrectQtmCnt(), this.total.getQtmCnt().getTotalQtmCnt());

        return AiCmtMaAiDgnRptDto.builder()
                .n02(n02Dto)
                .strthTpc(this.total.getQtmCnt().getCorrectQtmCnt().equals(0) ? null : this.strthTpc)
                .wknsTpc(isAll100 ? null : this.wknsTpc)
                .lrnrVelTpCd(this.lrnrVelTpCd)
                .build();
    }
}
