package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-10 17:48:03
 * @modify date 2025-02-17 23:23:03
 * @desc 사용자정보
 *
 * kerisUsrId 추가
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "사용자정보")
public class BcUserInfoDto {

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교과서ID")
    private String txbId;

	@Parameter(name="학급ID")
    private String claId;

	@Parameter(name="사용자ID")
    private String usrId;

	@Parameter(name="사용자명")
    private String usrNm;
	
	@Parameter(name="사용자유형코드")
    private String usrTpCd;
	
	@Parameter(name="학생번호")
    private String stuNo;
	
	@Parameter(name="학습자속도유형코드")
	private String lrnrVelTpCd;

	@Parameter(name="감정상태코드")
	private String flnStCd;
	
	@Parameter(name="관심여부")
	private String ntrYn;

	@Parameter(name="케리스ID")
	private String kerisUsrId;

}
