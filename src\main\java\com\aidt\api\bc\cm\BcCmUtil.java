package com.aidt.api.bc.cm;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.AccessControlList;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.GroupGrantee;
import com.amazonaws.services.s3.model.Permission;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-11 14:35:49
 * @modify date 2024-01-11 14:35:49
 * @desc TlCmUtil 교과학습공통기능클래스
 */
@Slf4j
public class BcCmUtil {

	// TODO 도메인URL 프로퍼티파일로 빼서 넣을것
	/** 파일도메인패스 */
	private final static String DOMAIN_PATH = "/api/v1/content/";

	public static final String END_POINT = "https://kr.object.gov-ncloudstorage.com";
	public static final String REGION_NAME = "";
	public static final String ACCESS_KEY = "74DA15D0E8943102E713";
	public static final String SECRET_KEY = "1EA4D1E1CB273AA207AE7735608988F94B6C0740";

	/**
	 * 파일CDN URL 가공처리
	 * 
	 * @param bucketName
	 * @param cdnPath
	 * @return String url
	 */
	public static String makeFleCdnUrl(String bucketName, String cdnPath) {
		String rtnUrl = "";
		if (cdnPath == null || cdnPath.trim().length() == 0) {
			rtnUrl = "";
		} else if (cdnPath.indexOf("http") == 0) {
			rtnUrl = cdnPath;
		} else {
			String pathSplit = 0 < cdnPath.indexOf("/") ? "/" : "";
			rtnUrl = DOMAIN_PATH + bucketName + pathSplit + cdnPath;
		}

		if (rtnUrl != null && !rtnUrl.isEmpty()) {
			rtnUrl = mapURL(rtnUrl);
		}

		return rtnUrl;
	}

	/**
	 * 콘텐츠CDN패스URL가공처리
	 * 
	 * @param bucketName
	 * @param cdnPath
	 * @param startFleNm
	 * @return
	 */
	public static String makeCtnCdnUrl(String bucketName, String cdnPath, String startFleNm) {
		String rtnUrl = "";

		if (cdnPath.indexOf("http") == 0) { // Test용 데이터용
			rtnUrl = cdnPath;
		} else {
			if (0 < cdnPath.length()) {
				rtnUrl = DOMAIN_PATH + bucketName + cdnPath;
				if (0 < startFleNm.length()) {
					// ctnUrl 마지막이 "/" 으로 끝나지 않으면 "/"를 추가해서 파일명을 설정해준다.
					rtnUrl += rtnUrl.lastIndexOf("/") == (rtnUrl.length() - 1) ? startFleNm : "/" + startFleNm;
				}
			}
		}

		if (rtnUrl != null && !rtnUrl.isEmpty()) {
			rtnUrl = mapURL(rtnUrl);
		}

		return rtnUrl;
	}

	public static String mapURL(String inputURL) {
		if (inputURL == null || inputURL.isEmpty()) {
			throw new IllegalArgumentException("Input URL cannot be null or empty");
		}

		String lowerCaseURL = inputURL.toLowerCase();
		if (lowerCaseURL.endsWith(".html") || lowerCaseURL.endsWith(".htm")) {
			return inputURL;
		}

		String devPrefix = "/api/v1/content/lms-op-dev-sto-02/";
		String prdPrefix = "/api/v1/content/lms-prd-sto-02/";
		String devBaseURL = "https://aidtcdn-op-dev.aitextbook.co.kr/";
		String prdBaseURL = "https://aidtcdn.aitextbook.co.kr/";

		if (inputURL.startsWith(devPrefix)) {
			return devBaseURL + inputURL.substring(devPrefix.length());
		} else if (inputURL.startsWith(prdPrefix)) {
			return prdBaseURL + inputURL.substring(prdPrefix.length());
		} else {
			return inputURL;
		}
	}

	/**
	 * 콘텐츠CDN패스URL가공처리
	 * 
	 * @param bucketName
	 * @param cdnPath
	 * @param startFleNm
	 * @return
	 */
	public static String getTxbID(String systemCode) {
		systemCode = systemCode.trim();
		HashMap<String, String> txbMap = new HashMap<String, String>();
		txbMap.put("m1math0sk2", "237");
		txbMap.put("m1math0sk5", "238");
		txbMap.put("m1engl0sl2", "241");
		txbMap.put("h0cen10sj2", "242");
		txbMap.put("h0cen20sj2", "244");
		txbMap.put("h0cen10sk1", "245");
		txbMap.put("h0cen20sk1", "246");
		txbMap.put("e3math1sp1", "247");
		txbMap.put("e3math2sp1", "248");
		txbMap.put("e4math1sp1", "249");
		txbMap.put("e4math2sp1", "250");
		txbMap.put("e3engl0sl1", "251");
		txbMap.put("h0cma10sj1", "252");
		txbMap.put("e3math1sh1", "254");
		txbMap.put("e3math2sh1", "255");
		txbMap.put("e4engl0sl1", "256");
		txbMap.put("e3engl0sk3", "257");
		txbMap.put("e4math1sh1", "258");
		txbMap.put("e3engl0sh2", "259");
		txbMap.put("e4math2sh1", "260");
		txbMap.put("e4engl0sk3", "261");
		txbMap.put("e4engl0sh2", "262");
		txbMap.put("h0cma10sh3", "263");
		txbMap.put("h0cma20sh3", "264");
		txbMap.put("h0cma20sj1", "265");
		txbMap.put("m1engl0ss1", "266");

		return txbMap.get(systemCode);
	}

	/**
	 * 파일이 S3에 존재하는지 확인하는 메서드
	 * 
	 * @param path 파일 경로 (예: "media/01_caption.vtt")
	 * @return 파일이 존재하면 true, 없으면 false
	 */
	public static boolean isFileExists(String BUCKET_NAME, String path) {
		try {

			AmazonS3 s3 = getAmazonS3Client();

			// 불필요한 슬래시 제거
			if (path.startsWith("/")) {
				path = path.substring(1); // 앞의 슬래시 제거
			}

			// 파일 메타데이터 조회 (파일이 없으면 예외 발생)
			s3.getObjectMetadata(BUCKET_NAME, path);
			return true; // 파일이 존재하면 true 반환
		} catch (SdkClientException e) {
			// 파일이 없거나 접근 불가 시 false 반환
			return false;
		}
	}

	/**
	 * S3 Connect
	 *
	 * @return AmazonS3
	 */
	public static AmazonS3 getAmazonS3Client() {
		return AmazonS3ClientBuilder.standard()
			.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(END_POINT, REGION_NAME))
			.withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(ACCESS_KEY, SECRET_KEY)))
			.build();
	}

	@SuppressWarnings("unchecked")
	public static List<Map<String, Object>> s3JsonFileReader(String bucketName, String fileName) throws IOException {
		List<Map<String, Object>> rtnDataList = new ArrayList<>();

		try {
			AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

			S3Object s3Object = s3.getObject(bucketName, fileName);
			S3ObjectInputStream imputStream = s3Object.getObjectContent();
			ObjectMapper objectMapper = new ObjectMapper();

			JsonNode rootNode = objectMapper.readTree(imputStream);

			if (rootNode.isArray()) {
				rtnDataList = objectMapper.convertValue(rootNode, List.class);
			} else {
				rtnDataList.add(objectMapper.convertValue(rootNode, Map.class));
			}
		} catch (AmazonServiceException ase) {
			log.error("### AmazonServiceException ### error.. {}", ase.getMessage());
		} catch (SdkClientException se) {
			log.error("### SdkClientException ### error.. {}", se.getMessage());
		} catch (IllegalArgumentException iae) {
			log.error("### IllegalArgumentException ### error.. {}", iae.getMessage());
		}

		return rtnDataList;
	}

	public static String s3JsonFileReaderString(String bucketName, String fileName) {
		String result = null;

		try {
			AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
			S3Object s3Object = s3.getObject(bucketName, fileName);

			var bytes = s3Object.getObjectContent().readAllBytes();
			result = new String(bytes, StandardCharsets.UTF_8);

			log.debug("<<< result >>> {}", result);
		} catch (AmazonServiceException ase) {
			log.error("### AmazonServiceException ### error.. {}", ase.getMessage());
		} catch (SdkClientException se) {
			log.error("### SdkClientException ### error.. {}", se.getMessage());
		} catch (IOException ie) {
			log.error("### SdkClientException ### error.. {}", ie.getMessage());
		}

		return result;
	}

	/**
	 * S3 파일 복사
	 * 
	 * @param s3
	 * @param BUCKET_NAME
	 * @param ori : 원본 path
	 * @param des : 복사 대상 path
	 * @return
	 */
	public static boolean copyS3File(AmazonS3 s3, String bucketName, String ori, String des) {
		boolean result = false;

		if (s3 != null) {
			try {
				s3.copyObject(
					new CopyObjectRequest(bucketName, ori, bucketName, des));

				AccessControlList acl = s3.getObjectAcl(bucketName, des);
				acl.grantPermission(GroupGrantee.AllUsers, Permission.Read);

				s3.setObjectAcl(bucketName, des, acl);

				result = true;
			} catch (AmazonServiceException ase) {
				log.error("S3 file copy AmazonServiceException Exception >>> {}", ase.getMessage());
			} catch (SdkClientException sce) {
				log.error("S3 file copy SdkClientException Exception >>> {}", sce.getMessage());
			}
		}

		return result;
	}

	/**
	 * S3 파일 삭제
	 * 
	 * @param s3
	 * @param BUCKET_NAME
	 * @param path
	 * @return
	 */
	public static boolean deleteS3File(AmazonS3 s3, String bucketName, String path) {
		boolean result = false;

		if (s3 != null) {
			try {
				s3.deleteObject(bucketName, path);
				result = true;
			} catch (AmazonServiceException ase) {
				log.error("S3 file delete AmazonServiceException Exception >>> {}", ase.getMessage());
			} catch (SdkClientException sce) {
				log.error("S3 file delete SdkClientException Exception >>> {}", sce.getMessage());
			}
		}

		return result;
	}
}
