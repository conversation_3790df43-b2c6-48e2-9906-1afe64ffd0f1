package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 11:23:05
 * @modify date 2024-03-05 11:23:05
 * @desc [TlSbcLrnEvPstDto 교과학습 학생 별 평가결과 조회 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlSbcLrnEvPstDto {
    /** 문항ID */
    @Parameter(name="문항Id")
    private String qtmId;
    /** 정답여부 */
    @Parameter(name="정답여부")
    private String cansYn;
}
