package com.aidt.api.common.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum SchoolGradeCode {

	ELEMENTARY("E", "초등"),
	MIDDLE("M", "중등"),
	HIGH("H", "고등");

	private final String code;
	private final String desc;

	SchoolGradeCode(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static SchoolGradeCode getSchoolGradeCode(String code) {
		return Arrays.stream(values())
			.filter(x -> x.getCode().equalsIgnoreCase(code))
			.findAny()
			.orElseThrow(() -> new IllegalArgumentException("학교급 코드를 찾을 수 없습니다."));
	}

}
