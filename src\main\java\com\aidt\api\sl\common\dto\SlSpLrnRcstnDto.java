package com.aidt.api.sl.common.dto;

import java.util.Date;

import javax.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @ email : <EMAIL>
 * @ create : date 2024-02-13 8:41:26
 * @ modify : date 2024-02-13 8:41:26
 * @ desc : 특별학습 재구성 
 */


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnRcstnDto {
	
	@Parameter(name="운영교과서아이디", required = true)
    @NotBlank(message = "{field.required}")
	private String optTxbId;
	
	@Parameter(name="특별학습아이디", required = true)
    @NotBlank(message = "{field.required}")
	private String spLrnId;

	@Parameter(name="사용여부", required = true)
    @NotBlank(message = "{field.required}")
	private String useYn;	
	
	@Parameter(name="이전사용여부")
	private String orgUseYn;	

	@Parameter(name="특별학습명")
	private String spLrnNm;	

	@Parameter(name="특별학습목표")
	private String lrnGoalCn;	

	@Parameter(name="원본순서")
	private int orglOrdn;	

	@Parameter(name="재구성순서", required = true)
    @NotBlank(message = "{field.required}")
	private int rcstnOrdn;	

	@Parameter(name="생성자아이디")
	private String crtrId;	

	@Parameter(name="생성일시")
	private Date crtDtm;	

	@Parameter(name="수정자아이디")
	private String mdfrId;	

	@Parameter(name="수정일시")
	private Date mdfDtm;	

	@Parameter(name="데이터베이스아이디")
	private String dbId;	

}
