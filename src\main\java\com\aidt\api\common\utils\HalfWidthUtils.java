package com.aidt.api.common.utils;
import java.text.Normalizer;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

public class HalfWidthUtils {

	private HalfWidthUtils() {
		throw new UnsupportedOperationException("halfWidthUtils 클래스를 인스턴스화할 수 없습니다.");
	}

	// NFKC로 변환되지 않는 특수문자 전각 유니코드 및 반각 치환 정의
	// 문항이나 답안이나 학생의 답을 찾아보긴 해야 할듯 ㅎㅎ
	// 현재 다 적용해 보고, 판단해야할듯ㅠ
	private static final Map<Character, String> SPECIAL_CHAR_MAP = Map.ofEntries(
		Map.entry('\u00b8', ","),   // 세디유(¸) -> 쉼표(,)
		Map.entry('\u2032', "'"),   // 프라임 기호(′) -> 작은따옴표(')
		Map.entry('\u00b4', "'"),   // 억음 부호(´) -> 작은따옴표(')
		Map.entry('\u02c8', "'"),   // 수직 스트로크(ˈ) -> 작은따옴표(')
		Map.entry('\u2013', "-"),   // en dash(–) -> 하이픈(-)
		Map.entry('\u2014', "-"),   // em dash(—) -> 하이픈(-)
		Map.entry('\u2015', "-"),   // 수평 바(―) -> 하이픈(-)
		Map.entry('\u2017', "_"),   // 밑줄 바(‗) -> 밑줄(_)
		Map.entry('\u2018', "'"),   // 왼쪽 작은따옴표(') -> 작은따옴표(')
		Map.entry('\u2019', "'"),   // 왼쪽 작은따옴표(’) -> 작은따옴표(')
		Map.entry('\u201a', "'"),   // 아래 작은따옴표(‚) -> 작은따옴표(')
		Map.entry('\u201b', "'"),   // 왼쪽 작은따옴표 반대(‛) -> 작은따옴표(')
		Map.entry('\u201e', "\""),  // 아래 큰따옴표(„) -> 큰따옴표(")
		Map.entry('\u201f', "\""),  // 왼쪽 큰따옴표 반대(‟) -> 큰따옴표(")
		Map.entry('\u2033', "\""),  // 이중 프라임(″) -> 큰따옴표(")
		Map.entry('\u2034', "\"\"'"), // 삼중 프라임(‴) -> 큰따옴표+작은따옴표
		Map.entry('\u2036', "\"\""), // 이중 역 프라임(‶) -> 큰따옴표
		Map.entry('\u2039', "<"),   // 왼쪽 외화살괄호(‹) -> 작은따옴표(<)
		Map.entry('\u203a', ">"),   // 오른쪽 외화살괄호(›) -> 작은따옴표(>)
		Map.entry('\u203c', "!!"),  // 이중 느낌표(‼) -> !!
		Map.entry('\u203d', "?!"),  // 물음표와 느낌표(‽) -> ?!
		Map.entry('\u2044', "/"),   // 분수 슬래시(⁄) -> 슬래시(/)
		Map.entry('\u204e', "*"),   // 별표(⁎) -> 별표(*)

		//한컴 입력기 자판 특수문자 추가
		Map.entry('\u2053', "~"),    // 물결표(⁓) → 물결표(~)
		Map.entry('\u3001', ","),   // 일본어 쉼표(、) → 콤마(,)
		Map.entry('\u3002', "."),   // 일본어 마침표(。) → 마침표(.)
		Map.entry('\u3003', "\""),  // 반복 부호(〃) → 큰따옴표(")
		Map.entry('\u2225', "||"),  // 평행 기호(∥) → 두 줄(||)
		Map.entry('\u223C', "~"),   // 물결 연산자(∼) → 물결표(~)
		Map.entry('\u00B7', "."),   // 가운데점(·) → 마침표(.)
		Map.entry('\u02C7', "^"),   // 캐런(ˇ) → 캐럿(^)
		Map.entry('\u00A1', "!"),   // 거꾸로 된 느낌표(¡) → 느낌표(!)
		Map.entry('\u00BF', "?"),   // 거꾸로 된 물음표(¿) → 물음표(?)
		Map.entry('\u02D0', ":")   // 삼각 콜론(ː) → 콜론(:)

	);

	//u3200–u32FF	동그라미 한글, 동그라미 한자, 월/요일, 숫자 등
	//uFF00–uFFEF	전각/반각 영문·숫자·기호, 반각 가타카나/한글 등
	//u2460–u24FF	동그라미 숫자/알파벳, 괄호·마침표 숫자/알파벳 등
	//u2018-u2019  	‘ (왼쪽 작은따옴표)’ (오른쪽 작은따옴표)
	//u201C-u201D   “ (왼쪽 큰따옴표) ” (오른쪽 큰따옴표)
	private static final Pattern FULL_WIDTH_PATTERN = Pattern.compile(
		"[\\u3200-\\u32FF\\uFF00-\\uFFEF\\u2460-\\u24FF\\u2018\\u2019\\u201C\\u201D]"
	);

	//u2000-u206F	문장 부호, 공백, 따옴표, 대시, 특수 기호 등
	//u2E00-u2E7F	고대 문자, 특수 괄호, 보충 문장 부호 등
	//u3000-u303F	동아시아권(한중일) 문장 부호, 특수 기호 등
	//u00B8~u02D0	그 외, SPECIAL_CHAR_MAP에 속한 것
	private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile(
		"[\\u2000-\\u206F\\u2E00-\\u2E7F\\u3000-\\u303F\\u00B8\\u00B4\\u02C8\\u2225\\u223C\\u00B7\\u02C7\\u00A1\\u00BF\\u02D0]"

	);

	public static boolean containsFullWidth(String str) {
		if (StringUtils.isBlank(str)) {
			return false;
		}
		return FULL_WIDTH_PATTERN.matcher(str).find();
	}

	public static boolean containsSpecialChar(String str) {
		if (StringUtils.isBlank(str)) {
			return false;
		}
		return SPECIAL_CHAR_PATTERN.matcher(str).find();
	}

	public static String normalize2HalfWidth(String str) {

		if (StringUtils.isBlank(str)) {
			return str;
		}

		var isSpecialChar = containsSpecialChar(str);
		if (!containsFullWidth(str) && !isSpecialChar) {
			return str;
		}
		// 기본 NFKC 적용
		String normalized = Normalizer.normalize(str.trim(), Normalizer.Form.NFKC);

		if (!isSpecialChar) {
			return normalized;
		}

		//추가 매핑 적용
		StringBuilder result = new StringBuilder(normalized.length());
		for (int i = 0; i < normalized.length(); i++) {
			char c = normalized.charAt(i);
			var replacement = SPECIAL_CHAR_MAP.get(c);

			if (replacement != null) {
				result.append(replacement);
			} else {
				result.append(c);
			}
		}

		return result.toString();
	}

}