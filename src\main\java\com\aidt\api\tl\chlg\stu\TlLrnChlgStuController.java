package com.aidt.api.tl.chlg.stu;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.chlg.dto.TlLrnChlgDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-09 15:57:52
 * @modify date 2024-04-09 15:57:52
 * @desc TlLrnChlgStu Service 교과학습 챌리지설정처리 서비스
 */
@Slf4j
@Tag(name="[tl] 챌린지설정[TlLrnChlgStu]", description="교과학습 및 AI의 챌리지정보를 설정한다.")
@RestController
@RequestMapping("/api/v1/tl/stu/chlg")
public class TlLrnChlgStuController {

    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlLrnChlgStuService tlLrnChlgStuService;

    /** DB-ID */
    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;

    /**
     * 학습챌린지 정보목록 조회 서비스
     * 
     * @return ResponseDto<List<TlLrnChlgDto>>
     */
    @Operation(summary="학습챌린지 정보 리스트 조회", description="학습챌린지 정보목록을 조회한다.")
    @PostMapping(value = "/selectLrnChlgList")
    public ResponseDto<List<TlLrnChlgDto>> selectLrnChlgList() {
        log.debug("Entrance selectLrnChlgList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 챌린지목록정보 조회
        return Response.ok(tlLrnChlgStuService.selectLrnChlgList(userDetails.getOptTxbId(), userDetails.getUsrId()));
    }

    /**
     * 학습챌린지 정보 등록
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="학습챌린지 정보 등록", description="챌린지정보를 저장한다.")
    @PostMapping(value = "/saveLrnChlgList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> saveList(@Valid @RequestBody TlLrnChlgDto saveDto) {
        // 등록 데이터 검증
        if (saveDto == null) {
            throw new IllegalArgumentException("Invalid saveDto");
        }
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setLrnUsrId(userDetails.getUsrId());
        saveDto.setDbId(DB_ID);

        return Response.ok(tlLrnChlgStuService.saveList(saveDto));
    }
    
    /**
     * 학습챌린지 정보 등록
     *
     * @param mybatisSaveDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="학습챌린지 중도 포기", description="챌린지를 중도포기 처리한다.")
    @PostMapping(value = "/updateLrnChlgQt", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateLrnChlgQt(@Valid @RequestBody TlLrnChlgDto saveDto) {
        // 등록 데이터 검증
        if (saveDto == null) {
            throw new IllegalArgumentException("Invalid saveDto");
        }
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setLrnUsrId(userDetails.getUsrId());
        saveDto.setDbId(DB_ID);

        return Response.ok(tlLrnChlgStuService.updateLrnChlgQt(saveDto));
    }
    
    
//    /**
//     * 학습챌린지 정보 업데이트
//     *
//     * @return ResponseDto<Integer>
//     */
//    @Operation(summary="학습챌린지 정보 업데이트", description="학습챌린지 정보 업데이트")
//    @PostMapping(value = "/updateLrnChlgList", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<Integer> saveList() {
//    	
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//
//        return Response.ok(tlLrnChlgStuService.updateList(userDetails.getOptTxbId(), userDetails.getUsrId()));
//    }
}
