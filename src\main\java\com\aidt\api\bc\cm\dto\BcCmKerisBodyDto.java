package com.aidt.api.bc.cm.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCmKerisBodyDto {
    private String code;                    // 응답 코드
    private String message;                 // 응답 메시지
    private String user_name;               // 사용자 이름
    private String school_name;             // 학교 이름
    private String school_id;               // 학교 ID
    private String user_division;           // 학교 구분 코드
    private String user_grade;              // 학년
    private String user_class;              // 반
    private String user_number;             // 번호
    private String user_gender;             // 성별 코드

    private List<BcCmKerisUserInfoDto> user_list = new ArrayList<>();           // 사용자 리스트 (다수 사용자일 때)
    private List<BcCmKerisScheduleInfoDto> schedule_info = new ArrayList<>();   // 시간표 정보 리스트
    private List<BcCmKerisClassInfoDto> class_info = new ArrayList<>();         // 학급 구성원 리스트
    private List<BcCmKerisMemberInfoDto> member_info = new ArrayList<>();

    private String errorMessage;
    private Map<String, Object> error;
    private String api_version;
}

