package com.aidt.api.common.helper;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.aidt.api.bc.cm.exception.BizException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class S3Helper {

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String bucketName;

	@Value("${server.meta.textbook.systemCode}")
	private String dbName;

	private final String folderName = "lms";

	private final AmazonS3 s3;

	public String upload(final String taskName, final MultipartFile multipartFile) {
		final var objectMetadata = new ObjectMetadata();
		objectMetadata.setContentType(multipartFile.getContentType());
		objectMetadata.setContentLength(multipartFile.getSize());
		final var s3Path = getS3Path(taskName, multipartFile.getOriginalFilename());
		try (InputStream inputStream = multipartFile.getInputStream()) {
			upload(s3Path, inputStream, objectMetadata);
		} catch (IOException e) {
			throw new BizException("S3 파일 변환에 실패하여 등록할 수 없습니다.");
		}
		return s3Path;
	}

	private void upload(final String s3Path, final InputStream inputStream, final ObjectMetadata objectMetadata) {
		try {
			s3.putObject(new PutObjectRequest(bucketName, s3Path, inputStream, objectMetadata)
				.withCannedAcl(CannedAccessControlList.PublicRead));
		} catch (AmazonS3Exception e) {
			log.error("S3 file upload failed: {}", e.getMessage());
			throw new BizException("S3 파일 업로드 중 서비스 오류가 발생했습니다.");
		} catch (SdkClientException e) {
			log.error("S3 client error: {}", e.getMessage());
			throw new BizException("S3 서비스 연결에 실패하였습니다.");
		}
	}

	public void upload(final String taskName, final File file, String contentType) {
		try {
			final var objectMetadata = new ObjectMetadata();
			objectMetadata.setContentType(contentType);

			s3.putObject(new PutObjectRequest(bucketName, getS3Path(taskName, file.getName()), file)
				.withMetadata(objectMetadata)
				.withCannedAcl(CannedAccessControlList.PublicRead));
		} catch (AmazonS3Exception e) {
			log.warn("S3 file upload failed: {}", e.getMessage());
			throw new BizException("S3 파일 업로드 중 서비스 오류가 발생했습니다.");
		} catch (SdkClientException e) {
			log.error("S3 client error: {}", e.getMessage());
			throw new BizException("S3 서비스 연결에 실패하였습니다.");
		}
	}

	public void remove(String s3Path) {
		try {
			s3.deleteObject(new DeleteObjectRequest(bucketName, s3Path));
		} catch (AmazonS3Exception e) {
			log.error("S3 file deletion failed: {}", e.getMessage());
			throw new BizException("S3 파일 삭제 중 서비스 오류가 발생했습니다.");
		} catch (SdkClientException e) {
			log.error("S3 client error: {}", e.getMessage());
			throw new BizException("S3 서비스 연결에 실패하였습니다.");
		}
	}

	private String getS3Path(final String taskName, final String fileName) {
		return String.format("%s/%s/%s/%s/%s.%s", dbName, folderName, taskName, taskName, UUID.randomUUID(),
			FilenameUtils.getExtension(fileName));
	}

	public InputStreamResource download(String s3Path) {
		try {
			var s3Object = s3.getObject(new GetObjectRequest(bucketName, s3Path));
			return new InputStreamResource(s3Object.getObjectContent());
		} catch (AmazonS3Exception e) {
			log.error("S3 file not found: {}, Error: {}", s3Path, e.getMessage());
			throw new BizException("요청한 파일을 찾을 수 없습니다.");
		} catch (AmazonServiceException e) {
			log.error("S3 service error: {}", e.getMessage());
			throw new BizException("S3 서비스 오류로 인해 다운로드에 실패하였습니다.");
		} catch (SdkClientException e) {
			log.error("S3 client error: {}", e.getMessage());
			throw new BizException("S3 서비스 연결에 실패하였습니다.");
		}
	}

}
