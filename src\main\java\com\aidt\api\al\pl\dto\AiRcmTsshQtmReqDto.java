package com.aidt.api.al.pl.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiRcmTsshQtmReqDto {
	
    private String usrId;
	
	@Parameter(name="학생ID", required = false)
    private String[] stuIdList;
	
	@Parameter(name="과목코드", required=true)
	private String sbjCd;
	
	@Parameter(name="학습맵 지식맵 구분코드 (학습맵:LRMP, 지식맵:KMMP)")
	private String lrmpKmmpDvCd;
	
	@Parameter(name="학교급코드", required=true)
	@Pattern(regexp="^(E|M|H)$", message="Only 'E' or 'M' or 'H' are allowed")
	private String schlGrdCd;
	
	@Parameter(name="중단원 지식맵 노드ID", required=true)
	private String mluKmmpNodId;
    
    @Parameter(name="차시 지식맵 노드ID", required = false)
	private String tcKmmpNodId;

    @Parameter(name="차시 지식맵 노드ID 리스트", required = false)
    private String[] tcKmmpNodIdList;

    @Parameter(name="토픽 ID 리스트", required = false)
    private String[] tpcKmmpNodIdList;
    
    @Parameter(name="토픽 ID", required = false)
    private String tpcKmmpNodId;
	
    @Parameter(name="대단원 학습맵 노드ID")
    private String luLrmpNodId;

    @Parameter(name="중단원 학습맵 노드ID", required = false)
    private String mluLrmpNodId;
    
    @Parameter(name="소단원 학습맵 노드ID")
    private String sluLrmpNodId;
    
    @Parameter(name="차시 학습맵 노드ID", required = false)
    private String tcLrmpNodId;
    
    @Parameter(name="추천문항수", required = false)
    private Integer rcmQtmCnt;

    @Parameter(name="문항추천구분 (수준별: LV)", required = false)
    private String qtmRcmDvCd;

    @Parameter(name="토픽추천구분 (취약개선학습: VLN", required = false)
    private String tpcRcmDvCd;

    @Parameter(name="토픽별 추천문항수", required = false)
    private Integer tpcRcmQtmCnt;

    @Parameter(name="중복추천허용여부", required=true)
    @NotBlank(message = "{field.required}")
    private String rpetRcmYn;

//    @Parameter(name="출제방식코드 S1:일괄출제, S2:동적일괄출제, S3:동적순차출제")
//    private String stxqMthdCd;
    
    @Parameter(name="사용자 학습수준 (빠른:FS, 보통:NM, 느린:SL)")
    private String lrnrVelTpCd;

    @Parameter(name="평가ID")
    private Integer evId;

    @Parameter(name="평가명", required=false)
    @NotBlank(message = "{field.required}")
    private String evNm;

    @Parameter(name="평가구분코드 DE:DIY평가, AE:AI평가", required=true)
    @Pattern(regexp = "^(AE|DE)$", message = "Only 'AE', 'DE' are allowed")
    @NotBlank(message = "{field.required}")
    private String evDvCd;

    @Parameter(name="평가상세구분코드 OV:단원총괄, C1:선택학습1, C2:선택학습2", required = false)
    private String evDtlDvCd;
    
    @Parameter(name="응시시작일시", required = false)
    private String txmStrDtm;

    @Parameter(name="응시종료일시", required = false)
    private String txmEndDtm;

    @Parameter(name="운영교과서ID")
    private String optTxbId;

    @Parameter(name="풀이시간설정여부", required=true)
    @NotBlank(message = "{field.required}")
    private String xplTmSetmYn;

    @Parameter(name="풀이시간초수 (EA_EV에 등록 - 평가Rimit)", required = false)
    private Integer xplTmScnt;
    
    @Parameter(name="오답풀이이력여부", required = false)
    private String iansEvHstYn;
    
    @Parameter(name="초등영어세트구분", required = false)
    private String eschEnSetDv;
    
    @Parameter(name="문항수")
    private Integer qstCnt;
    
    @Parameter(name="교과서ID")
    private String txbId;
    
    @Parameter(name="지식맵 노드ID")
	private String kmmpNodId;
    private String kmmpNodNm;

    @Parameter(name="이전학교급문항", required = false)
    private String lnkgTpcKmmpNodId;

}
