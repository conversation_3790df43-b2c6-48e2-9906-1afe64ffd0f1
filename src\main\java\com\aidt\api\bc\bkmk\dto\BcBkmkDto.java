package com.aidt.api.bc.bkmk.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.aidt.common.Paging.PagingRequestDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 북마크
 */

@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcBkmkDto extends PagingRequestDto{

	@Parameter(name="북마크ID")
	private int bkmkId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="단원노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String luNodId;

	@Parameter(name="차시노드ID", required = true)
	@NotBlank(message = "{field.required}")
	private String tcNodId;

	@Parameter(name="학습활동ID", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnAtvId;

	@Parameter(name="학습사용자ID")
	private String lrnUsrId;

	@Parameter(name="학습유형코드", required = true)
	@NotBlank(message = "{field.required}")
	private String lrnTpCd;

	@Parameter(name="학습유형코드명")
	private String lrnTpCdNm;

	@Parameter(name="대단원명")
	private String lluNm;

	@Parameter(name="중단원명")
	private String mluNm;

	@Parameter(name="소단원명")
	private String sluNm;

	@Parameter(name="차시명")
	private String tcNm;

	@Parameter(name="학습활동명")
	private String lrnAtvNm;

	@Parameter(name="조회정렬조건")
	private String srhSrt;

	@Parameter(name="정렬정보")
	private String srtInfo;

	@Parameter(name="등록시간")
	private String crtTm;

	@Parameter(name="북마크삭제용배열필드")
	private List<Integer> bkmkIds;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="정렬(단원순)")
	private String rcstnOrdn;

	// 생성자ID
	private String crtrId;

	// 생성일시
	private String crtDtm;

	// 수정자ID
	private String mdfrId;

	// 수정일시
	private String mdfDtm;

	// 데이터베이스ID
	private String dbId;

	private int totalCnt;

}
