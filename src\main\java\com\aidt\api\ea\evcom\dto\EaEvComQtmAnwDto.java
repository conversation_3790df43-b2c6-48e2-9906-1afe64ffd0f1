package com.aidt.api.ea.evcom.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-13
 * @modify date 2024-02-13
 * @desc 평가 관리 - 채점완료 - 문항 답변리스트 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvComQtmAnwDto {

	@Parameter(name="평가ID")
	private long evId;

	@Parameter(name="평가명")
	private String evNm;

	@Parameter(name="평가완료여부")
	private String evCmplYn;

	@Parameter(name="평가보충심화유형코드")
	private String sppNtnTpCd;

	@Parameter(name="평가보충심화완료여부")
	private String sppNtnCmplYn;

	@Parameter(name="문항ID")
	private String qtmId;

	@Parameter(name="문항번호")
	private int qtmNo;

	@Parameter(name="문항순서")
	private String qtmOrdn;

	@Parameter(name="문항난이도")
	private String qtmDffdDvCd;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="삭제일시")
	private String delDtm;

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="제출답변값")
	private String smtAnwVl;

    @Parameter(name = "문제 풀이 내용")
    private String qstXplCn;

	@Parameter(name="정답여부")
	private String cansYn;

	@Parameter(name="풀이시간초수")
	private long xplTmScnt;

	@Parameter(name="풀이시간초수명")
	private String xplTmScntNm;

	@Parameter(name="찜여부")
	private String dibsYn;

	@Parameter(name="찜생성일시")
	private String dibsCrtDtm;

	@Parameter(name="오답노트제출답변값")
	private String iansNteSmtAnwVl;

	@Parameter(name="오답노트정답여부")
	private String iansNteCansYn;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성일시")
	private String crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private String mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;

	@Parameter(name="문항플랫폼 토픽명")
	private String qpTcNm;

    @Parameter(name = "메모첨부파일ID")
    private long annxFleId;

    @Parameter(name="문항노트파일경로명")
    String annxFlePthNm;

    @Parameter(name = "풀이상태코드")
    private String xplStCd;

    @Parameter(name = "힌트확인여부")
    private String hntCofmYn;

    @Parameter(name = "문항유형코드")
    private String questionFormCode;

    @Parameter(name = "교육과정콘텐츠표준ID")
    private String eduCrsCnCd;

}
