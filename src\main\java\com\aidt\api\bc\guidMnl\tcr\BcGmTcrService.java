package com.aidt.api.bc.guidMnl.tcr;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.dto.BcS3Dto;
import com.aidt.api.bc.guidMnl.dto.*;
import com.aidt.api.bc.guidMnl.mapper.ManualMapper;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BcGmTcrService {
    @Autowired
    private ManualMapper manualMapper;

    @Autowired
    private CommonDao commonDao;

    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private BcCmService bcCmService;

//    @Value("${server.meta.textbook.systemCode}")
//    private String DB_ID;

    @Value("${spring.profiles.active}")
    private String SERVER_ACTIVE;

    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    /**
     * 교과서 매뉴얼 계층구조 조회
     * @param param
     * @return
     */
    public GuideManual getGuideManualHierarchyByTextbookId(Map<String, Object> param) {
        Long textbookId = longValue(param.get("textbookId"));
        String role = (String)param.get("role");
        if (textbookId <= 0) {
            throw new Error("교과서 ID가 없습니다");
        }

        String textbookCode = manualMapper.selectTextbookCode(textbookId);
        GuideManual manual = manualMapper.selectGuideManualByTextbookId(textbookId, role);
        if (manual == null) {
            return createDefaultGuideManual(textbookId, role, textbookCode != null ? textbookCode : "test");
        } else if (manual.getNodes().isEmpty()) {
            manual.setNodes(List.of(createDefaultNode(manual.getGuidMnlId(), 1, null, 0)));
        }
        return manual;
    }


    /**
     * 1depth 조회
     */
    public GuideManual getGuideManual1depthByTextbookId(Map<String, Object> param) {
        return manualMapper.selectGuideManual1depthByTextbookId(param);
    }

    /**
     * copy 할 매뉴얼 데이터 조회
     * @return
     */
    public List<Map<String, Object>> getGuideManuals(String role){
        return manualMapper.selectGuideManuals(role);
    }


    /**
     * 뎁스 추가
     * 또는 내용, 이름 변경
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveGuideManualNode(GuideManualNode param) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        log.debug("PARAM => {}" , param);
        if (param.getGuidMnlNodId() == null || param.getGuidMnlNodId() == 0) {
            // ID 없음 -> 신규생성 (하위까지)
            createDefaultNode(param.getGuidMnlId(), param.getDpth(), longValue(param.getUrnkGuidMnlNodId()) > 0 ? param.getUrnkGuidMnlNodId() : null, param.getSrtOrdn());
            // 형제노드 재정렬
            List<GuideManualNode> siblings = manualMapper.selectSiblingNodes(param);
            siblings.forEach(node -> {
                node.setSrtOrdn(siblings.indexOf(node) + 1);
                manualMapper.updateGuideManualNode(node);
            });

        } else {
            param.setMdfrId(userDetails.getUsrId());
            manualMapper.updateGuideManualNode(param);
        }
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveGuideManualNodeReorder(List<GuideManualNode> nodes) {
        nodes.forEach(this::saveGuideManualNode);
        return 1;
    }

    /**
     * 타교과서 복사
     * 또는 노드 초기화(toCopy = 0 일 경우)
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer copyManual(Map<String, Object> param) {
        Long textbookId = longValue(param.get("textbookId"));
        Long toCopy = longValue(param.get("toCopyId"));
        String role = (String)param.get("role");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        String originalTextbookCode = manualMapper.selectTextbookCode(textbookId);
        if(originalTextbookCode == null){
            originalTextbookCode = "test";
        }

        GuideManual original = manualMapper.selectGuideManualByTextbookId(textbookId, role); // 빈 값
        GuideManual toCopyManual = manualMapper.selectGuideManualByTextbookId(toCopy, role); // 작성 다 된 불러오기 값

        if (original != null) {
            List<Long> uploadIds = manualMapper.selectNodeUploadByManualId(original.getGuidMnlId());
            List<Long> nodeIds = manualMapper.selectAllNodeIdsByManualId(original.getGuidMnlId());

//            deleteGuideManualNode(nodeIds);
            if(!uploadIds.isEmpty()){
//                uploadIds.forEach(id -> {
//                    GuideManualUpload upload = manualMapper.selectGuideManualUploadByNodeId(id);
//                    if (upload != null) {
//                        // 첨부파일 있는 경우 삭제
//                        deleteGuideManualUpload(upload);
//                    }
//                });

                manualMapper.deleteGuideManualNodeUploads(
                        new GuideManualUpdate(uploadIds, userDetails.getUsrId())
                ); // 업로드 del_yn 만 변경

            }
//            manualMapper.forceDeleteGuideManualNodes(nodeIds);
//            manualMapper.forceDeleteGuideManual(original.getGuidMnlId());
            manualMapper.deleteGuideManualNodes(
                    new GuideManualUpdate(nodeIds, userDetails.getUsrId())
            ); // 노드 del_yn 변경
            manualMapper.deleteGuideManual(
                    new GuideManualUpdate(List.of(original.getGuidMnlId()), userDetails.getUsrId())
            ); // 매뉴얼 del_yn 변경
        }

        if (toCopyManual == null || toCopyManual.getNodes().isEmpty() || toCopy == 0) {
            this.createDefaultGuideManual(textbookId, role, originalTextbookCode);
        } else {
            // 새 매뉴얼 생성
            GuideManual newManual = new GuideManual();
            newManual.setTxbId(textbookId);
            newManual.setUsrTpCd((String)param.get("role"));
            newManual.setGuidMnlCd("mnl_" + textbookId);
            newManual.setDbId(originalTextbookCode);
            newManual.setCrtrId(userDetails.getUsrId());
            newManual.setMdfrId(userDetails.getUsrId());
            manualMapper.insertGuideManual(newManual);

            // 노드 및 업로드 복사
            this.copyNodes(toCopyManual.getNodes(), newManual.getGuidMnlId(), originalTextbookCode, userDetails.getUsrId());
        }

        return 1;
    }

    /**
     * 노드 삭제 (첨부파일 삭제 포함)
     * @param ids
     * @return
     */
    public int deleteGuideManualNode(List<Long> ids) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        ids.forEach(id -> {
            GuideManualNode node = manualMapper.selectGuideManualNodeByNodeId(id);
            if (node != null) {
                List<GuideManualNode> siblings = manualMapper.selectSiblingNodes(node).stream().filter(n -> !Objects.equals(n.getGuidMnlNodId(), id)).collect(Collectors.toList());
                if(!siblings.isEmpty()) {
                    siblings.forEach(s -> {
                        s.setSrtOrdn(siblings.indexOf(s) + 1);
                        manualMapper.updateGuideManualNode(s);
                    });
                }
            }
            GuideManualUpload upload = manualMapper.selectGuideManualUploadByNodeId(id);
            if (upload != null) {
                // 첨부파일 있는 경우 삭제
                deleteGuideManualUpload(upload);
            }
        });

        return manualMapper.deleteGuideManualNodes(
                new GuideManualUpdate(ids, userDetails.getUsrId())
        );
    }

    /**
     * 3뎁스 첨부파일 DB데이터 저장
     * @param file
     * @param nodeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GuideManualUpload saveGuideManualUpload(MultipartFile file, Long nodeId) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        GuideManualUpload org = manualMapper.selectGuideManualUploadByNodeId(nodeId);
        GuideManual manual = manualMapper.selectGuideManualByNodeId(nodeId);
        if (org != null) {
//            deleteS3File(org);
//            manualMapper.forceDeleteGuideManualContentUploads(List.of(org.getGuidMnlNodFleId()));
            deleteGuideManualUpload(org);
        }

        List<BcS3Dto.FileUploadResponse> response = saveS3File(file, manual.getDbId());
        
        String baseUrl = "/" + manual.getDbId() + "/lms/guidMnl/";

        GuideManualUpload result = new GuideManualUpload();
        result.setGuidMnlNodId(nodeId);
        result.setGuidMnlNodFleNm(response.get(0).getNewFileName());
        result.setFlePthNm(baseUrl + response.get(0).getNewFileName());
        result.setOrglFlePthNm(baseUrl + response.get(0).getOriginFileName());
        result.setFleTpCd(response.get(0).getContentType());
        result.setCrtrId(userDetails.getUsrId());
        result.setMdfrId(userDetails.getUsrId());
        result.setDbId(manual.getDbId());

        manualMapper.insertGuideManualContentUpload(result);
        return result;
    }

    /**
     * 3뎁스 첨부파일 DB데이터 삭제
     * @param upload
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteGuideManualUpload(GuideManualUpload upload) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

//        deleteS3File(upload);
//        // s3삭제 후 첨부테이블 삭제
//        int result = manualMapper.forceDeleteGuideManualContentUploads(List.of(upload.getGuidMnlNodFleId()));
//        if(result > 0) {
//            log.info("Object {} has been deleted.\n", upload.getGuidMnlNodFleNm());
//        }else {
//            log.info("Object {} has been deleted.\n", upload.getGuidMnlNodFleNm());
//        }

        return manualMapper.deleteGuideManualNodeUploads(
                new GuideManualUpdate(List.of(upload.getGuidMnlNodFleId()), userDetails.getUsrId())
        );
    }


    /**
     * 메뉴얼 가이드 첨부파일 (PDF, ZIP) DB데이터 저장
     *
     * @param file
     * @param guidMnlId
     * @param txbId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GuidManualPdfFile savePdfUpload(MultipartFile file, Long guidMnlId, Long txbId) throws JsonProcessingException {
        try {
            CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
            Integer verInfo = manualMapper.selectGuidManualPdfFileByVerInfo(guidMnlId, txbId);
            GuideManual manual = manualMapper.selectGuideManualByDbId(guidMnlId);

            int currentVersion = (verInfo != null) ? verInfo : 0;
            int newVersion = (currentVersion == 0) ? 1 : currentVersion + 1;

            List<BcS3Dto.FileUploadResponse> response = guidmnlSaveS3File(file, manual.getDbId());
            String filePath = "/" + manual.getDbId() + "/lms/guidMnl";
            GuidManualPdfFile result = new GuidManualPdfFile();
            result.setTxbId(txbId);
            result.setGuidMnlId(guidMnlId);
            result.setFleNm(response.get(0).getNewFileName());
            result.setFleOrglNm(response.get(0).getOriginFileName());
            result.setFlePthNm(filePath);
            result.setCrtrId(userDetails.getUsrId());
            result.setVerInfo(newVersion);
            result.setMdfrId(userDetails.getUsrId());
            result.setDbId(manual.getDbId());

            manualMapper.updateVersionGuidManualPdfFile(guidMnlId, txbId);

            String bucketUrl = BcCmUtil.END_POINT + "/"+BUCKET_NAME+"/"+manual.getDbId()+"/lms/guidMnl/" + response.get(0).getNewFileName();

            String docViId = bcCmService.callStreamDocsIdApi(bucketUrl);
            result.setDocViId(docViId);

            manualMapper.insertGuidManualPdfFile(result);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private List<BcS3Dto.FileUploadResponse> guidmnlSaveS3File(MultipartFile file, String dbId) {

        // S3 client connect
        final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

        // upload local file
        try {
            List<BcS3Dto.FileUploadResponse> fileUploadResponseList = new ArrayList<>();
            ObjectMetadata metadata = null;
            // File Meta
            metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            // 확장자 추가
            int lastIndex = Objects.requireNonNull(file.getOriginalFilename()).lastIndexOf('.');
            String extensionWithDot = Objects.requireNonNull(file.getOriginalFilename()).substring(lastIndex);

            // file Object Name
            String newFileName = UUID.randomUUID().toString() + extensionWithDot;

            // bucketName 조합
            String fnBucketName = BUCKET_NAME+"/"+dbId+"/lms/guidMnl";
            s3.putObject(fnBucketName, newFileName, file.getInputStream(), metadata);

            log.info("Object {} has been created.", file.getOriginalFilename());
            fileUploadResponseList.add(BcS3Dto.FileUploadResponse.builder()
                    .originFileName(file.getOriginalFilename())
                    .newFileName(newFileName)
                    .contentType(file.getContentType())
                    .fileSize(file.getSize())
                    .build());


            // 업로드된 파일 read 권한 부여
            AccessControlList accessControlList = s3.getObjectAcl(fnBucketName, newFileName);
            accessControlList.grantPermission(GroupGrantee.AllUsers, Permission.Read);
            s3.setObjectAcl(fnBucketName, newFileName, accessControlList);


            // disconnect
            s3.shutdown();

            return fileUploadResponseList;

        } catch (AmazonS3Exception e) {
            throw new Error("S3 File Upload Multi AmazonS3Exception error");
        } catch (SdkClientException | IOException e) {
            throw new Error("S3 File Upload Multi SdkClientException error");
        }
    }

    public List<GuidManualPdfFile> getGuideManualPdfFiles(GuidManualPdfFile manualPdfFile) {
        return manualMapper.selectGuideManualPdfFiles(manualPdfFile);
    }

    public GuidManualPdfFileDownload getGuidFileName(GuidManualPdfFile manualPdfFile) {
        return manualMapper.selectGuideManualFileData(manualPdfFile);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updatePdfFileUseYn(GuidManualPdfFile manualPdfFile) {
        manualPdfFile.setMdfrId(jwtProvider.getCommonUserDetail().getUsrId());
        return manualMapper.updatePdfFileUseYn(manualPdfFile);
    }

    public ResponseEntity<?> downloadPdfFile(GuidManualPdfFile manualPdfFile) {
        GuidManualPdfFileDownload manualPdfFileDownload = manualMapper.selectGuideManualFileData(manualPdfFile);
        String filePath = manualPdfFileDownload.getFlePthNm();
        String fileName = manualPdfFileDownload.getFleNm();
        String fileOrglName = manualPdfFileDownload.getFleOrglNm();

        final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
        try {
            String fnBucketName = BUCKET_NAME + filePath;

            S3Object s3Object = s3.getObject(fnBucketName, fileName);

            S3ObjectInputStream s3ObjectInputStream = s3Object.getObjectContent();

            byte[] bytes = IOUtils.toByteArray(s3ObjectInputStream);

            String encodeFileName = URLEncoder.encode(fileOrglName, "UTF-8").replaceAll("\\+", "%20");

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            httpHeaders.setContentLength(bytes.length);
            httpHeaders.setContentDispositionFormData("attachment", encodeFileName);

            s3ObjectInputStream.close();

            s3.shutdown();

            return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
        } catch (AmazonS3Exception e) {
            if (e.getStatusCode() == 404) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("해당 파일을 찾을 수 없습니다.");
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("파일 다운로드 오류: " + e.getMessage());
        } catch (SdkClientException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("SDK 클라이언트 오류: " + e.getMessage());
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("파일 처리 오류: " + e.getMessage());
        }
    }

    ///////////////// PRIVATE

    /**
     * 교과서 매뉴얼 없는 경우 기본값(1~3뎁스) 매뉴얼 생성 후 리턴
     * @param textbookId
     * @param role
     * @return
     */
    private GuideManual createDefaultGuideManual(Long textbookId, String role, String dbId) {
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        GuideManual manual = new GuideManual();
        manual.setTxbId(textbookId);
        manual.setGuidMnlCd("mnl_" + textbookId);
        manual.setUsrTpCd(role);
        manual.setDbId(dbId);
        manual.setCrtrId(userDetails.getUsrId());
        manual.setMdfrId(userDetails.getUsrId());
        manualMapper.insertGuideManual(manual);

        manual.setNodes(List.of(createDefaultNode(manual.getGuidMnlId(), 1, null, 1)));

        return manual;
    }

    /**
     * 기본 노드 인서트(하위뎁스 1개씩 포함)
     * @param manualId
     * @param depth
     * @param parentId
     * @param order
     * @return
     */
    private GuideManualNode createDefaultNode(Long manualId, int depth, Long parentId, int order) {
        if (depth > 1 && parentId == null) {
            throw new Error("상위노드 지정 필요");
        }
        GuideManual manual = manualMapper.selectGuideManualById(manualId);
        String dbId = manual.getDbId();
        // TODO 코드 작명법..
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        GuideManualNode newNode = new GuideManualNode();
        newNode.setGuidMnlId(manualId);
        newNode.setDpth(depth);
        newNode.setGuidMnlNodNm(depth + " Depth");
        newNode.setGuidMnlNodCd("nod"+depth+"_"+manualId);
        newNode.setUrnkGuidMnlNodId(parentId);
        newNode.setSrtOrdn(order);
        newNode.setLwsYn(depth == 3 ? "Y" : "N");
        newNode.setCrtrId(userDetails.getUsrId());
        newNode.setMdfrId(userDetails.getUsrId());
        newNode.setDbId(dbId);
        manualMapper.insertGuideManualNode(newNode);
        if (depth < 3) {
            newNode.setChildren(List.of(createDefaultNode(manualId, ++depth, newNode.getGuidMnlNodId(), 1)));
        }
        return newNode;
    }

    private void copyNodes(List<GuideManualNode> originalNodes, Long newManualId, String textbookCode, String userId) {
        for (GuideManualNode orgNode : originalNodes) {
            // 1 Depth 노드 생성
            GuideManualNode newNode = createNode(orgNode, newManualId, null, textbookCode, userId);
            manualMapper.insertGuideManualNode(newNode);

            if (orgNode.getChildren() != null && !orgNode.getChildren().isEmpty()) {
                for (GuideManualNode orgNode2 : orgNode.getChildren()) {
                    // 2 Depth 노드 생성
                    GuideManualNode newNode2 = createNode(orgNode2, newManualId, newNode.getGuidMnlNodId(), textbookCode, userId);
                    manualMapper.insertGuideManualNode(newNode2);

                    if (orgNode2.getChildren() != null && !orgNode2.getChildren().isEmpty()) {
                        for (GuideManualNode orgNode3 : orgNode2.getChildren()) {
                            // 3 Depth 노드 생성
                            GuideManualNode newNode3 = createNode(orgNode3, newManualId, newNode2.getGuidMnlNodId(), textbookCode, userId);
                            manualMapper.insertGuideManualNode(newNode3);

                            if (orgNode3.getUpload() != null) {
                                // s3 이미지 복사, 업로드 생성
                                String newFilePath = copyS3ImageFile(textbookCode, orgNode3.getUpload().getFlePthNm());

                                GuideManualUpload newUpload = createUpload(newFilePath, orgNode3.getUpload(), newNode3.getGuidMnlNodId(), textbookCode, userId);
                                manualMapper.insertGuideManualContentUpload(newUpload);
                            }
                        }
                    }
                }
            }
        }
    }

    private GuideManualNode createNode(GuideManualNode orgNode, Long newManualId, Long parentId, String textbookCode, String userId) {
        GuideManualNode newNode = new GuideManualNode();
        newNode.setGuidMnlId(newManualId);
        newNode.setDpth(orgNode.getDpth());
        newNode.setGuidMnlNodNm(orgNode.getGuidMnlNodNm());
        newNode.setGuidMnlNodCd("nod" + orgNode.getDpth() + "_" + newManualId);
        newNode.setUrnkGuidMnlNodId(parentId);
        newNode.setSrtOrdn(orgNode.getSrtOrdn());
        newNode.setLwsYn("N");
        newNode.setCrtrId(userId);
        newNode.setMdfrId(userId);
        newNode.setDbId(textbookCode);
        return newNode;
    }

    /**
     * 유틸 훔쳐옴..
     * @param obj
     * @return
     */
    private static long longValue(Object obj) {
        long value = 0L;
        try {
            value = Long.parseLong(Objects.requireNonNull(obj).toString());
            return value;
        } catch (NumberFormatException | NullPointerException e) {
            // 아무것도 안해야 실패 시 정상적으로 0이 리턴
            return value;
        }
    }

    private GuideManualUpload createUpload(String newFilePath, GuideManualUpload orgUpload, Long newNodeId, String textbookCode, String userId) {
        GuideManualUpload newUpload = new GuideManualUpload();
        newUpload.setGuidMnlNodId(newNodeId);
        newUpload.setFlePthNm("/" + newFilePath);
        newUpload.setOrglFlePthNm("/" + newFilePath);
        newUpload.setGuidMnlNodFleNm(orgUpload.getGuidMnlNodFleNm());
        newUpload.setFleTpCd(orgUpload.getFleTpCd());
        newUpload.setCrtrId(userId);
        newUpload.setMdfrId(userId);
        newUpload.setDbId(textbookCode);
        return newUpload;
    }

    private String copyS3File(String newdbId, String originKey){
        final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
        String[] oldKey = originKey.split("/");
        log.debug("oldKey = " + Arrays.toString(oldKey));
        String copyName = originKey.split("/")[oldKey.length - 1] + "_copy";
        oldKey[oldKey.length -1] = copyName;
        String[] originalKeyExceptBucketName = originKey.split(BUCKET_NAME);

        if (originalKeyExceptBucketName[1].startsWith("/")) {
            originalKeyExceptBucketName[1] = originalKeyExceptBucketName[1].substring(1);
        }
        String copyObjectKey = newdbId+"/lms/guidMnl/" + copyName;
        String newBucketUrl = BcCmUtil.END_POINT + "/"+BUCKET_NAME+"/"+copyObjectKey;
        CopyObjectRequest copyObjectRequest = new CopyObjectRequest(BUCKET_NAME, originalKeyExceptBucketName[1], BUCKET_NAME, copyObjectKey);
        s3.copyObject(copyObjectRequest);
        return newBucketUrl;
    }

    private String copyS3ImageFile(String textbookCode, String originKey) {
        final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

        String originFilePath = Arrays.stream(originKey.split("/"))
                .skip(4)
                .collect(Collectors.joining("/"));
        String copyFilePath = textbookCode + originFilePath.substring(originFilePath.indexOf("/"));

        // 원본 객체의 ACL 가져오기
        AccessControlList acl = s3.getObjectAcl(BUCKET_NAME, originFilePath);

        // CopyObjectRequest에 원본 객체의 ACL 설정
        CopyObjectRequest copyObjectRequest = new CopyObjectRequest(BUCKET_NAME, originFilePath, BUCKET_NAME, copyFilePath);
        copyObjectRequest.setAccessControlList(acl);

        // 객체 복사
        s3.copyObject(copyObjectRequest);

        return copyFilePath;
    }

    private void deleteS3File(GuideManualUpload upload) {
        final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();
        GuideManual manual = manualMapper.selectGuideManualByNodeId(upload.getGuidMnlNodId());
        try {

            // bucketName 조합
            String fnBucketName = BUCKET_NAME+"/"+manual.getDbId()+"/lms/guidMnl";

            // request delete file
            s3.deleteObject(fnBucketName, upload.getGuidMnlNodFleNm());

            // disconnect
            s3.shutdown();

        } catch (AmazonS3Exception e) {
            throw new Error("S3 File Delete Multi AmazonS3Exception error");
        } catch (SdkClientException e) {
            throw new Error("S3 File Delete Multi SdkClientException error");
        }
    }

    private List<BcS3Dto.FileUploadResponse> saveS3File(MultipartFile file, String dbId) {

        // S3 client connect
        final AmazonS3 s3 = BcCmUtil.getAmazonS3Client();

        // upload local file
        try {
            List<BcS3Dto.FileUploadResponse> fileUploadResponseList = new ArrayList<>();
            ObjectMetadata metadata = null;
            // File Meta
            metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            // file Object Name
            String newFileName = UUID.randomUUID().toString();

            // bucketName 조합
            String fnBucketName = BUCKET_NAME+"/"+dbId+"/lms/guidMnl";
            s3.putObject(fnBucketName, newFileName, file.getInputStream(), metadata);

            log.info("Object {} has been created.", file.getOriginalFilename());
            fileUploadResponseList.add(BcS3Dto.FileUploadResponse.builder()
                    .originFileName(file.getOriginalFilename())
                    .newFileName(newFileName)
                    .contentType(file.getContentType())
                    .fileSize(file.getSize())
                    .build());


            // 업로드된 파일 read 권한 부여
            AccessControlList accessControlList = s3.getObjectAcl(fnBucketName, newFileName);
            accessControlList.grantPermission(GroupGrantee.AllUsers, Permission.Read);
            s3.setObjectAcl(fnBucketName, newFileName, accessControlList);


            // disconnect
            s3.shutdown();

            return fileUploadResponseList;

        } catch (AmazonS3Exception e) {
            throw new Error("S3 File Upload Multi AmazonS3Exception error");
        } catch (SdkClientException | IOException e) {
            throw new Error("S3 File Upload Multi SdkClientException error");
        }
    }

    public void saveContentFile(GuideManualFileUploadReqDto guideManualFileUploadReqDto) throws JsonProcessingException {
        List<BcS3Dto.FileUploadResponse> response = null;
        String bucketUrl = "";
        String docViId = "";

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String contentType = guideManualFileUploadReqDto.getContentType();
        String dbId = manualMapper.selectDbIdByTextbookId(Long.valueOf(userDetails.getTxbId()));

        boolean isExistedFileNode = manualMapper.selectFileNode(guideManualFileUploadReqDto) > 0;

        // s3 파일 업로드
        if (guideManualFileUploadReqDto.getFile() != null) {
            response = saveS3File(guideManualFileUploadReqDto.getFile(), dbId);
            // pdf 업로드 및 docViId 리턴
            bucketUrl = BcCmUtil.END_POINT + "/"+BUCKET_NAME+ "/" + dbId + "/lms/guidMnl/" + response.get(0).getNewFileName();
            if("pdf".equals(contentType)) {
                docViId = bcCmService.callStreamDocsIdApi(bucketUrl);
                guideManualFileUploadReqDto.setDocViId(docViId);
            }
        }

        if("pdf".equals(contentType)) {
            if (isExistedFileNode) manualMapper.updateFileNodeDelYn(guideManualFileUploadReqDto);
            if (guideManualFileUploadReqDto.getFile() != null) {
                guideManualFileUploadReqDto.setMdfrId(userDetails.getUsrId());
                manualMapper.updateNodeDocViId(guideManualFileUploadReqDto);
            }
        }
        else if ("video".equals(contentType)) {
            String baseUrl = "/" + dbId + "/lms/guidMnl/";

            if (response == null) {
                log.info("video 파일 없음");
            } else {
                var guideManualUpload = GuideManualUpload.builder()
                        .guidMnlNodFleId(guideManualFileUploadReqDto.getGuidMnlNodFleId())
                        .guidMnlNodId(guideManualFileUploadReqDto.getGuidMnlNodId())
                        .guidMnlNodFleNm(response.get(0).getNewFileName())
                        .orglFlePthNm(baseUrl + response.get(0).getOriginFileName())
                        .flePthNm(bucketUrl)
                        .fleTpCd(response.get(0).getContentType())
                        .vdSmyCn(guideManualFileUploadReqDto.getGuidMnlVdSmyCn())
                        .crtrId(userDetails.getUsrId())
                        .mdfrId(userDetails.getUsrId())
                        .dbId(dbId).build();

                if (isExistedFileNode) manualMapper.updateFileNode(guideManualUpload);
                else manualMapper.insertGuideManualContentUpload(guideManualUpload);
            }
        }
    }
}
