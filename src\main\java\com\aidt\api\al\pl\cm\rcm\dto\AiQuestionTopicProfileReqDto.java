package com.aidt.api.al.pl.cm.rcm.dto;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class AiQuestionTopicProfileReqDto {

	@Schema(description = "평가 ID")
	@NotNull(message = "평가 번호가 존재하지 않습니다.")
	private Integer evId;

	@Schema(description = "토픽 완료 정보")
	private String tpcCmplYn;


	public boolean isTopicCompleted() {
		return "Y".equals(tpcCmplYn);
	}

}
