package com.aidt.api.al.fdbk.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:56:58
 * @modify date 2024-07-11 15:56:58
 * @desc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiFdbkDto {

    private int aiFdbkId;

    private String optTxbId;

    private String usrId;

    private String lrmpNodId;

    private String fdbkCn;

    private LocalDateTime mdfDtm;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tcrId;

}
