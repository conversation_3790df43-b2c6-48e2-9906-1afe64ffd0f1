package com.aidt.api.al.cmt.dto.req.cm;

import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-29 10:49:23
 * @modify date 2024-05-29 10:49:23
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QtmCntReqDto {

    @Positive
    @Parameter(name="영역 전체 문항수", required=true)
    private Integer totalQtmCnt;

    @PositiveOrZero
    @Parameter(name="영역 정답 문항수", required=true)
    private Integer correctQtmCnt;

}
