<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.clablbd.tcr">

	<!-- 사용자의 가장 최근 게시글 작성/수정 시간과 현재 시각의 차이 (단위 초) -->
	<select id="selectUsrLstClaBldbTmDff" parameterType="String" resultType="java.lang.Long">
		SELECT TIMESTAMPDIFF(SECOND, CLA_BLBD.MDF_DTM, NOW()) as TIME_DIFF
		FROM LMS_LRM.CM_CLA_BLBD CLA_BLBD
		WHERE CLA_BLBD.USR_ID = #{usrId}
		ORDER BY CLA_BLBD.MDF_DTM DESC
		LIMIT 1
	</select>

	<select id="selectBlbdUsrList" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - selectBlbdUsrList */
		SELECT A.USR_ID
			 , A.KERIS_USR_ID
		  FROM LMS_LRM.CM_USR A
		 WHERE A.KERIS_USR_ID = #{kerisUsrId}
	</select>
	
	<!-- 학급게시판 조회 -->
	<select id="selectClaBlbdList" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - selectClaBlbdList */
		<include refid="api.bc.common.pagingHeader"/>
		SELECT *
			 , ROW_NUMBER() OVER (PARTITION BY T.RQRD_YN ORDER BY T.CRT_DTM DESC) AS RNUM
		  FROM (
			SELECT A.CLA_BLBD_ID
				 , A.OPT_TXB_ID
				 , A.USR_TP_CD
				 , A.USR_ID
				 , C.KERIS_USR_ID
				 , A.CLA_BLBD_TITL_NM
				 , A.CLA_BLBD_CN
				 , A.ANNX_ID
				 , A.RQRD_YN
				 , A.UCWR_USE_YN
				 , A.DEL_YN
				 , A.BLWR_APR_YN
				 , A.CRTR_ID
				 , DATE_FORMAT(A.CRT_DTM,'%m. %d.') AS CRT_DTM
				 , A.MDFR_ID
				 , A.MDF_DTM
				 , A.DB_ID
				 , (SELECT
				 		COALESCE((SELECT COUNT(*)
				 					FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB
				 				   INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
				 				   WHERE SUB.CLA_BLBD_ID = A.CLA_BLBD_ID
				 					 AND SUB.DEL_YN = 'N'
				 					 AND SUB.UCWR_DPTH = 1), 0)
				 		+
				 		COALESCE((SELECT COUNT(*)
				 			FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB
				 		   INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
				 		   WHERE SUB.URNK_UCWR_ID IN (SELECT SUB_INNER.CLA_BLBD_UCWR_ID
				 										FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB_INNER
				 									   INNER JOIN LMS_LRM.CM_USR B ON SUB_INNER.USR_ID = B.USR_ID
				 									   WHERE SUB_INNER.CLA_BLBD_ID = A.CLA_BLBD_ID
				 										 AND SUB_INNER.DEL_YN = 'N'
				 										 AND SUB_INNER.UCWR_DPTH = 1)
				 			 AND SUB.DEL_YN = 'N'
				 			 AND SUB.UCWR_DPTH = 2), 0)
				 	) AS UCWR_CNT
				 , ( SELECT COUNT(*) FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB 
					  INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
					  WHERE A.CLA_BLBD_ID = SUB.CLA_BLBD_ID AND SUB.DEL_YN = 'N' AND SUB.UCWR_APR_YN = 'N') AS UCWR_APR_CNT
				 , 0 AS BLBD_NUM
				 , B.CLA_BLBD_WRT_PMSN_YN
				 , B.CLA_BLBD_UCWR_PMSN_YN
			  FROM LMS_LRM.CM_CLA_BLBD A
			  LEFT OUTER JOIN LMS_LRM.CM_FNC_USE_SETM B ON A.OPT_TXB_ID = B.OPT_TXB_ID
			 INNER JOIN LMS_LRM.CM_USR C ON A.USR_ID = C.USR_ID
			 <where>
					AND A.DEL_YN = 'N'
					AND A.OPT_TXB_ID = #{optTxbId}
					AND A.RQRD_YN='Y'
				<if test='usrTpCd != null and !usrTpCd.equals("")'>
					AND A.USR_TP_CD = #{usrTpCd}
				</if>
				<if test='startDate != null and !startDate.equals("")'>
					AND DATE_FORMAT(A.CRT_DTM, '%Y%m%d') <![CDATA[>=]]> #{startDate}
				</if>
				<if test='endDate != null and !endDate.equals("")'>
					AND DATE_FORMAT(A.CRT_DTM, '%Y%m%d') <![CDATA[<=]]> #{endDate}
				</if>
				<if test='myWriter != null and !myWriter.equals("")'>
					AND A.USR_ID = #{myWriter}
				</if>
				<if test='srchField != null and !srchField.equals("")'>
					AND (
					<trim prefixOverrides="OR">
						A.CLA_BLBD_TITL_NM LIKE CONCAT('%', #{srchField},'%')
						OR
						A.CLA_BLBD_CN LIKE CONCAT('%', #{srchField},'%')
						<if test="usrIds != null and usrIds.length > 0">
							OR
							A.USR_ID IN (
								<foreach collection="usrIds" item="usrId" separator=",">
									#{usrId}
								</foreach>
							)
							OR
							C.KERIS_USR_ID IN (
								<foreach collection="usrIds" item="usrId" separator=",">
									#{usrId}
								</foreach>
							)
						</if>
					</trim>
					)
				</if>
			 </where>
			UNION ALL
			SELECT A.CLA_BLBD_ID
				 , A.OPT_TXB_ID
				 , A.USR_TP_CD
				 , A.USR_ID
				 , C.KERIS_USR_ID
				 , A.CLA_BLBD_TITL_NM
				 , A.CLA_BLBD_CN
				 , A.ANNX_ID
				 , 'N' AS RQRD_YN
				 , A.UCWR_USE_YN
				 , A.DEL_YN
				 , A.BLWR_APR_YN
				 , A.CRTR_ID
				 , DATE_FORMAT(A.CRT_DTM,'%m. %d.') AS CRT_DTM
				 , A.MDFR_ID
				 , A.MDF_DTM
				 , A.DB_ID
				 , (SELECT
				 		COALESCE((SELECT COUNT(*)
				 					FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB
				 				   INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
				 				   WHERE SUB.CLA_BLBD_ID = A.CLA_BLBD_ID
				 					 AND SUB.DEL_YN = 'N'
				 					 AND SUB.UCWR_DPTH = 1), 0)
				 		+
				 		COALESCE((SELECT COUNT(*)
				 			FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB
				 		   INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
				 		   WHERE SUB.URNK_UCWR_ID IN (SELECT SUB_INNER.CLA_BLBD_UCWR_ID
				 										FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB_INNER
				 									   INNER JOIN LMS_LRM.CM_USR B ON SUB_INNER.USR_ID = B.USR_ID
				 									   WHERE SUB_INNER.CLA_BLBD_ID = A.CLA_BLBD_ID
				 										 AND SUB_INNER.DEL_YN = 'N'
				 										 AND SUB_INNER.UCWR_DPTH = 1)
				 			 AND SUB.DEL_YN = 'N'
				 			 AND SUB.UCWR_DPTH = 2), 0)
				 	) AS UCWR_CNT
				 , ( SELECT COUNT(*) FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB 
				 	  INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
				 	  WHERE A.CLA_BLBD_ID = SUB.CLA_BLBD_ID AND SUB.DEL_YN = 'N' AND SUB.UCWR_APR_YN = 'N') AS UCWR_APR_CNT
				 , ROW_NUMBER() OVER () AS BLBD_NUM
				 , B.CLA_BLBD_WRT_PMSN_YN
				 , B.CLA_BLBD_UCWR_PMSN_YN
			  FROM LMS_LRM.CM_CLA_BLBD A
			  LEFT OUTER JOIN LMS_LRM.CM_FNC_USE_SETM B ON A.OPT_TXB_ID = B.OPT_TXB_ID
			 INNER JOIN LMS_LRM.CM_USR C ON A.USR_ID = C.USR_ID
			 <where>
					AND A.DEL_YN = 'N'
					AND A.OPT_TXB_ID = #{optTxbId}
				<if test='usrTpCd != null and !usrTpCd.equals("")'>
					AND A.USR_TP_CD = #{usrTpCd}
				</if>
				<if test='startDate != null and !startDate.equals("")'>
					AND DATE_FORMAT(A.CRT_DTM, '%Y%m%d') <![CDATA[>=]]> #{startDate}
				</if>
				<if test='endDate != null and !endDate.equals("")'>
					AND DATE_FORMAT(A.CRT_DTM, '%Y%m%d') <![CDATA[<=]]> #{endDate}
				</if>
				<if test='myWriter != null and !myWriter.equals("")'>
					AND A.USR_ID = #{myWriter}
				</if>
				<if test='srchField != null and !srchField.equals("")'>
					AND (
					<trim prefixOverrides="OR">
						A.CLA_BLBD_TITL_NM LIKE CONCAT('%', #{srchField},'%')
						OR
						A.CLA_BLBD_CN LIKE CONCAT('%', #{srchField},'%')
						<if test="usrIds != null and usrIds.length > 0">
							OR
							A.USR_ID IN (
								<foreach collection="usrIds" item="usrId" separator=",">
									#{usrId}
								</foreach>
							)
							OR
							C.KERIS_USR_ID IN (
								<foreach collection="usrIds" item="usrId" separator=",">
									#{usrId}
								</foreach>
							)
						</if>
					</trim>
					)
				</if>
			 </where>
			) T
		) R
		WHERE (R.RQRD_YN = 'Y' AND R.RNUM <![CDATA[<=]]> 5) OR R.RQRD_YN != 'Y'
		ORDER BY
			CASE
        		WHEN R.RQRD_YN = 'Y' AND R.RNUM <![CDATA[<=]]> 5 THEN 0
        		ELSE 1
   			 END,
   			R.BLBD_NUM DESC,
			R.CRT_DTM DESC
		LIMIT #{pageSize, jdbcType=INTEGER} OFFSET #{pageNo, jdbcType=INTEGER}
	</select>

	<select id="selectClaBlbdCnt" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="Integer">
		SELECT COUNT(1)
		  FROM LMS_LRM.CM_CLA_BLBD A
		 INNER JOIN LMS_LRM.CM_USR B ON A.USR_ID = B.USR_ID
			<where>
					AND A.DEL_YN = 'N'
					AND A.OPT_TXB_ID = #{optTxbId}
				<if test='usrTpCd != null and !usrTpCd.equals("")'>
					AND A.USR_TP_CD = #{usrTpCd}
				</if>
				<if test='startDate != null and !startDate.equals("")'>
					AND DATE_FORMAT(A.CRT_DTM, '%Y%m%d') <![CDATA[>=]]> #{startDate}
				</if>
				<if test='endDate != null and !endDate.equals("")'>
					AND DATE_FORMAT(A.CRT_DTM, '%Y%m%d') <![CDATA[<=]]> #{endDate}
				</if>
				<if test='myWriter != null and !myWriter.equals("")'>
					AND A.USR_ID = #{myWriter}
				</if>
				<if test='srchField != null and !srchField.equals("")'>
					AND (
						A.CLA_BLBD_TITL_NM LIKE CONCAT('%', #{srchField},'%')
						OR
						A.CLA_BLBD_CN LIKE CONCAT('%', #{srchField},'%')
						<if test="usrIds != null and usrIds.length > 0">
							OR
							A.USR_ID IN (
								<foreach collection="usrIds" item="usrId" separator=",">
									#{usrId}
								</foreach>
							)
							OR
							B.KERIS_USR_ID IN (
								<foreach collection="usrIds" item="usrId" separator=",">
									#{usrId}
								</foreach>
							)
						</if>
					)
				</if>
			</where>
	</select>

	<!-- 학급게시판 첨부파일 조회 -->
	<select id="selectAnnxFleList" resultType="com.aidt.api.bc.cm.dto.BcAnnxFleDto">
		/** BcClaBlbdTcr-Mapper.xml - selectAnnxFleList */
		SELECT
			ANNX_FLE_ID,
			ANNX_ID,
			SRT_ORDN,
			DOC_VI_ID,
			ANNX_FLE_NM,
			ANNX_FLE_ORGL_NM,
			ANNX_FLE_FEXT_NM,
			ANNX_FLE_SZE,
			ANNX_FLE_PTH_NM,
			USE_YN
		FROM
			LMS_LRM.CM_ANNX_FLE
		WHERE ANNX_ID = #{annxId}
		AND USE_YN = 'Y'
		ORDER BY SRT_ORDN
	</select>


	<!-- 학급게시판 상세 조회 -->
	<select id="getClaBlbdInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - getClaBlbdInfo */
		SELECT A.CLA_BLBD_ID
			 , A.OPT_TXB_ID
			 , A.USR_TP_CD
			 , A.USR_ID
			 , A.CLA_BLBD_TITL_NM
			 , A.CLA_BLBD_CN
			 , A.ANNX_ID
			 , A.PKG_BLBD_ID
			 , A.RQRD_YN
			 , A.UCWR_USE_YN
			 , A.DEL_YN
			 , A.BLWR_APR_YN
			 , A.CRTR_ID
			 , DATE_FORMAT(A.CRT_DTM,'%m. %d.') AS CRT_DTM
			 , A.MDFR_ID
			 , A.MDF_DTM
			 , A.DB_ID
			 , (SELECT
						COALESCE((SELECT COUNT(*)
									FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB
								   INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
								   WHERE SUB.CLA_BLBD_ID = A.CLA_BLBD_ID
									 AND SUB.DEL_YN = 'N'
									 AND SUB.UCWR_DPTH = 1), 0)
						+
						COALESCE((SELECT COUNT(*)
							FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB
						   INNER JOIN LMS_LRM.CM_USR B ON SUB.USR_ID = B.USR_ID
						   WHERE SUB.URNK_UCWR_ID IN (SELECT SUB_INNER.CLA_BLBD_UCWR_ID
														FROM LMS_LRM.CM_CLA_BLBD_UCWR SUB_INNER
													   INNER JOIN LMS_LRM.CM_USR B ON SUB_INNER.USR_ID = B.USR_ID
													   WHERE SUB_INNER.CLA_BLBD_ID = A.CLA_BLBD_ID
														 AND SUB_INNER.DEL_YN = 'N'
														 AND SUB_INNER.UCWR_DPTH = 1)
							 AND SUB.DEL_YN = 'N'
							 AND SUB.UCWR_DPTH = 2), 0)
					) AS UCWR_CNT
			 , (SELECT COUNT(*) AS RQRD_CNT FROM LMS_LRM.CM_CLA_BLBD A WHERE A.OPT_TXB_ID = #{optTxbId} AND A.RQRD_YN = 'Y' AND A.DEL_YN = 'N')
			 , B.CLA_BLBD_WRT_PMSN_YN
			 , B.CLA_BLBD_UCWR_PMSN_YN
			 , C.KERIS_USR_ID
		  FROM LMS_LRM.CM_CLA_BLBD A
		  LEFT OUTER JOIN LMS_LRM.CM_FNC_USE_SETM B ON A.OPT_TXB_ID = B.OPT_TXB_ID
		  INNER JOIN LMS_LRM.CM_USR C ON A.USR_ID = C.USR_ID
		 WHERE A.CLA_BLBD_ID = #{claBlbdId}
		   AND A.OPT_TXB_ID = #{optTxbId}
	</select>


	<!-- 학급게시판 댓글 조회 -->
	<select id="selectClaBlbdUcwrList" parameterType="long" resultType="com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto">
		/** BcClaBlbdTcr-Mapper.xml - selectClaBlbdUcwrList */
		WITH RECURSIVE COMMENT_TREE AS (
		    SELECT A.CLA_BLBD_UCWR_ID
		         , A.CLA_BLBD_ID
		         , A.USR_ID
		         , A.URNK_UCWR_ID
		         , A.SRT_ORDN
		         , A.UCWR_DPTH
		         , A.CLA_BLBD_UCWR_CN
		         , CAST(A.CLA_BLBD_UCWR_ID AS CHAR(200)) AS PATH
		         , A.DEL_YN
		         , A.UCWR_APR_YN
		         , DATE_FORMAT(A.CRT_DTM,'%m. %d. %p %h:%i') AS CRT_DTM
		         , B.USR_TP_CD
		         , B.KERIS_USR_ID
		      FROM LMS_LRM.CM_CLA_BLBD_UCWR A
		     INNER JOIN LMS_LRM.CM_USR B ON A.USR_ID = B.USR_ID
		     WHERE A.URNK_UCWR_ID IS NULL
		      AND A.CLA_BLBD_ID = #{claBlbdId}
		    UNION ALL
		    SELECT C.CLA_BLBD_UCWR_ID
		         , C.CLA_BLBD_ID
		         , C.USR_ID
		         , C.URNK_UCWR_ID
		         , C.SRT_ORDN
		         , C.UCWR_DPTH
		         , C.CLA_BLBD_UCWR_CN
		         , <![CDATA[
		          	CONCAT(CT.PATH, '>', C.CLA_BLBD_UCWR_ID)
		           ]]>
		         , C.DEL_YN
		         , C.UCWR_APR_YN
		         , DATE_FORMAT(C.CRT_DTM,'%m. %d. %p %h:%i') AS CRT_DTM
		         , D.USR_TP_CD
		         , D.KERIS_USR_ID
		      FROM LMS_LRM.CM_CLA_BLBD_UCWR C
		      JOIN COMMENT_TREE CT ON C.URNK_UCWR_ID = CT.CLA_BLBD_UCWR_ID
		     INNER JOIN LMS_LRM.CM_USR D ON C.USR_ID = D.USR_ID
		     WHERE C.CLA_BLBD_ID = #{claBlbdId}
		)
		SELECT
		    CLA_BLBD_UCWR_ID,
		    CLA_BLBD_ID,
		    URNK_UCWR_ID,
		    SRT_ORDN,
		    UCWR_DPTH,
		    CLA_BLBD_UCWR_CN,
		    PATH,
		    DEL_YN,
		    UCWR_APR_YN,
		    CRT_DTM,
		    USR_ID,
		    USR_TP_CD,
		    KERIS_USR_ID
		FROM COMMENT_TREE
		ORDER BY PATH
	</select>

	<!-- 학급게시판 등록 -->
	<insert id="insertClaBlbdInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto"
	useGeneratedKeys="true" keyProperty="claBlbdId" keyColumn="CLA_BLBD_ID">
		/** BcClaBlbdTcr-Mapper.xml - insertClaBlbdInfo */
		INSERT INTO LMS_LRM.CM_CLA_BLBD (
			   OPT_TXB_ID
			 , USR_TP_CD
			 , USR_ID
			 , CLA_BLBD_TITL_NM
			 , CLA_BLBD_CN
			 , ANNX_ID
			 , RQRD_YN
			 , UCWR_USE_YN
			 , BLWR_APR_YN
			 , DEL_YN
			 , CRTR_ID
			 , CRT_DTM
			 , MDFR_ID
			 , MDF_DTM
			 , DB_ID
		) VALUES (
			   #{optTxbId}
			 , #{usrTpCd}
			 , #{usrId}
			 , #{claBlbdTitlNm}
			 , #{claBlbdCn}
			 , #{annxId}
			 , #{rqrdYn}
			 , #{ucwrUseYn}
			 , 'Y'
			 , 'N'
			 , #{crtrId}
			 , NOW()
			 , #{mdfrId}
			 , NOW()
			 , #{dbId}
		)
	</insert>

	<!-- 학급게시판 작성자 확인 -->
	<select id="getClaBlbdUsrCheck" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="String">
		/** BcClaBlbdTcr-Mapper.xml - getClaBlbdUsrCheck */
		SELECT CASE WHEN COUNT(*) > 0 THEN 'Y' ELSE 'N' END AS BLBD_YN
		  FROM LMS_LRM.CM_CLA_BLBD A
		 WHERE A.CLA_BLBD_ID = #{claBlbdId}
		   AND A.USR_ID = #{usrId}
		   AND A.OPT_TXB_ID = #{optTxbId}
	</select>
	
	<!-- 학급게시판 수정 -->
	<update id="updateClaBlbdInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - updateClaBlbdInfo */
		UPDATE LMS_LRM.CM_CLA_BLBD
		   SET CLA_BLBD_TITL_NM = #{claBlbdTitlNm}
			 , CLA_BLBD_CN = #{claBlbdCn}
			 , RQRD_YN = #{rqrdYn}
			 , UCWR_USE_YN = #{ucwrUseYn}
			 , ANNX_ID = #{annxId}
			 <if test='pkgBlbdId != null and !pkgBlbdId.equals("")'>
			 , PKG_BLBD_ID = #{pkgBlbdId}
			 </if>
			 , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE CLA_BLBD_ID = #{claBlbdId}
		   AND OPT_TXB_ID = #{optTxbId}
	</update>
	
	<!-- 학급게시판 삭제 -->
	<delete id="deleteClaBlbdInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - deleteClaBlbdInfo */
		DELETE FROM LMS_LRM.CM_CLA_BLBD
		 WHERE CLA_BLBD_ID = #{claBlbdId}
		   AND OPT_TXB_ID = #{optTxbId}
	</delete>
	
	<!-- 
	<update id="deleteClaBlbdInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - deleteClaBlbdInfo */
		UPDATE LMS_LRM.CM_CLA_BLBD
		   SET DEL_YN = 'Y'
			 , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE CLA_BLBD_ID = #{claBlbdId}
		   AND OPT_TXB_ID = #{optTxbId}
	</update>
	-->
	
	<!-- 학급게시판 댓글 등록 -->
	<insert id="insertClaBlbdUcwrInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto">
		/** BcClaBlbdTcr-Mapper.xml - insertClaBlbdUcwrInfo */
		INSERT INTO LMS_LRM.CM_CLA_BLBD_UCWR (
			   CLA_BLBD_ID
			 , USR_ID
			 , URNK_UCWR_ID
			 , UCWR_DPTH
			 , SRT_ORDN
			 , CLA_BLBD_UCWR_CN
			 , DEL_YN
			 , UCWR_APR_YN
			 , CRTR_ID
			 , CRT_DTM
			 , MDFR_ID
			 , MDF_DTM
			 , DB_ID
		) VALUES (
			   #{claBlbdId}
			 , #{usrId}
			 , #{urnkUcwrId}
			 , #{ucwrDpth}
			 , (SELECT IFNULL(MAX(A.SRT_ORDN), 0) + 1 FROM LMS_LRM.CM_CLA_BLBD_UCWR A)
			 , #{claBlbdUcwrCn}
			 , 'N'
			 , 'Y'
			 , #{crtrId}
			 , NOW()
			 , #{mdfrId}
			 , NOW()
			 , #{dbId}
		)
	</insert>
	
	<!-- 학급게시판 댓글작성자 확인 -->
	<select id="getClaBlbdUcwrUsrCheck" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="String">
		/** BcClaBlbdTcr-Mapper.xml - getClaBlbdUcwrUsrCheck */
		SELECT CASE WHEN COUNT(*) > 0 THEN 'Y' ELSE 'N' END AS BLBD_YN
		  FROM LMS_LRM.CM_CLA_BLBD_UCWR A
		 WHERE A.CLA_BLBD_UCWR_ID = #{claBlbdUcwrId}
		   AND A.USR_ID = #{usrId}
	</select>
	
	<!-- 학급게시판 댓글 수정 -->
	<update id="updateClaBlbdUcwrInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto">
		/** BcClaBlbdTcr-Mapper.xml - updateClaBlbdUcwrInfo */
		UPDATE LMS_LRM.CM_CLA_BLBD_UCWR
		   SET CLA_BLBD_UCWR_CN = #{claBlbdUcwrCn}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE CLA_BLBD_UCWR_ID = #{claBlbdUcwrId}
		  AND CLA_BLBD_ID = #{claBlbdId}
	</update>
	
	<!-- 학급게시판 댓글 삭제 -->
	<delete id="deleteClaBlbdUcwrInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto">
		/** BcClaBlbdTcr-Mapper.xml - deleteClaBlbdUcwrInfo */
		DELETE FROM LMS_LRM.CM_CLA_BLBD_UCWR
		WHERE CLA_BLBD_UCWR_ID = #{claBlbdUcwrId}
		  AND CLA_BLBD_ID = #{claBlbdId}
	</delete>
	<!-- 
	<update id="deleteClaBlbdUcwrInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto">
		/** BcClaBlbdTcr-Mapper.xml - deleteClaBlbdUcwrInfo */
		UPDATE LMS_LRM.CM_CLA_BLBD_UCWR
		   SET DEL_YN = 'Y'
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE CLA_BLBD_UCWR_ID = #{claBlbdUcwrId}
		  AND CLA_BLBD_ID = #{claBlbdId}
	</update>
	 -->
	
	<!-- 학급게시판 필독 갯수 조회 -->
	<select id="getClaBlbdRqrdCnt" parameterType="String" resultType="int">
		/** BcClaBlbdTcr-Mapper.xml - getClaBlbdRqrdCnt */
		SELECT COUNT(*) AS RQRD_CNT
		  FROM LMS_LRM.CM_CLA_BLBD A
		 WHERE A.OPT_TXB_ID = #{optTxbId}
		   AND A.RQRD_YN = 'Y'
		   AND A.DEL_YN = 'N'
	</select>
	
	<!-- 학급게시판 상세 조회 -->
	<select id="getPkgBlbdInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - getPkgBlbdInfo */
		SELECT *
		  FROM LMS_LRM.CM_CLA_BLBD A
		 WHERE A.OPT_TXB_ID = #{optTxbId}
		   AND A.PKG_BLBD_ID = #{pkgBlbdId}
		   AND A.DEL_YN = 'N'
	</select>
	
	<!-- 학급게시판 일괄등록 목록 -->
	<select id="selectTcrPkgClaList" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - selectTcrPkgClaList */
		SELECT A.OPT_TXB_ID
			 , A.CLA_BLBD_ID
		  FROM LMS_LRM.CM_CLA_BLBD A
		 WHERE A.PKG_BLBD_ID = #{pkgBlbdId}
	</select>
	
	<!-- 학급게시판 운영교과서 확인 -->
	<select id="selectOptTxbIdChk" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="String">
		/** BcClaBlbdTcr-Mapper.xml - selectTcrPkgClaList */
		SELECT A.OPT_TXB_ID
		  FROM LMS_LRM.CM_CLA_BLBD A
		 WHERE A.CLA_BLBD_ID = #{claBlbdId}
		   AND A.DEL_YN = 'N'
	</select>
	
	<!-- 학급게시판 게시글 게시 승인 -->
	<update id="updateBlwrAprYn" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto">
		/** BcClaBlbdTcr-Mapper.xml - updateBlwrAprYn */
		UPDATE LMS_LRM.CM_CLA_BLBD
		   SET BLWR_APR_YN = 'Y'
		     , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE CLA_BLBD_ID = #{claBlbdId}
		   AND OPT_TXB_ID = #{optTxbId}
	</update>
	
	<!-- 학급게시판 댓글 게시 승인 -->
	<update id="updateUcwrAprYn" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdUcwrDto">
		/** BcClaBlbdTcr-Mapper.xml - updateUcwrAprYn */
		UPDATE LMS_LRM.CM_CLA_BLBD_UCWR
		   SET UCWR_APR_YN = 'Y'
			 , MDFR_ID = #{mdfrId}
			 , MDF_DTM = NOW()
		 WHERE CLA_BLBD_UCWR_ID = #{claBlbdUcwrId}
		   AND CLA_BLBD_ID = #{claBlbdId}
	</update>
	
	<!-- 학급게시판 일괄 등록 -->
	<insert id="insertClaBlbdPkgInfo" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" useGeneratedKeys="true" keyProperty="claBlbdId">
		/** BcClaBlbdTcr-Mapper.xml - insertClaBlbdPkgInfo */
		INSERT INTO LMS_LRM.CM_CLA_BLBD (
			   OPT_TXB_ID
			 , USR_TP_CD
			 , USR_ID
			 , CLA_BLBD_TITL_NM
			 , CLA_BLBD_CN
			 , ANNX_ID
			 , PKG_BLBD_ID
			 , RQRD_YN
			 , UCWR_USE_YN
			 , BLWR_APR_YN
			 , DEL_YN
			 , CRTR_ID
			 , CRT_DTM
			 , MDFR_ID
			 , MDF_DTM
			 , DB_ID
		) VALUES (
			   #{optTxbId}
			 , #{usrTpCd}
			 , #{usrId}
			 , #{claBlbdTitlNm}
			 , #{claBlbdCn}
			 , #{annxId}
			 , #{pkgBlbdId}
			 , #{rqrdYn}
			 , #{ucwrUseYn}
			 , 'Y'
			 , 'N'
			 , #{crtrId}
			 , NOW()
			 , #{mdfrId}
			 , NOW()
			 , #{dbId}
		)
	</insert>
	
	<!-- 학급게시판 일괄 삭제 운영교과서 확인 -->
	<select id="getClaBlbdPkgUsrCheck" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdDto" resultType="String">
		/** BcClaBlbdTcr-Mapper.xml - getClaBlbdPkgUsrCheck */
		SELECT A.OPT_TXB_ID
		  FROM LMS_LRM.CM_CLA_BLBD A
		 WHERE A.CLA_BLBD_ID = #{claBlbdId}
	</select>
	
	<select id="selectOtherClaBlbdCopyAnnxFleYn" parameterType="com.aidt.api.bc.clablbd.dto.BcClaBlbdCopyFleDto" resultType="int">
		/** BcClaBlbdTcr-Mapper.xml - selectOtherClaBlbdCopyAnnxFleYn */
		SELECT
			COUNT(cb.cla_blbd_id) AS cnt
		FROM
			lms_lrm.cm_cla_blbd cb
		WHERE
			cb.cla_blbd_id != #{claBlbdId}
		AND cb.annx_id = #{annxId}
		AND cb.del_yn = 'N'
	</select>
	
</mapper>
