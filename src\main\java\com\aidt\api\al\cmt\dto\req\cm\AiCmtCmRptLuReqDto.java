package com.aidt.api.al.cmt.dto.req.cm;

import com.aidt.api.al.cmt.dto.ett.cm.AiCmtCmRptLuDto;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtCmRptLuReqDto {

    @Parameter(name="강점 토픽", required=true)
    private String strthTpc;

    @Parameter(name="약점 토픽", required=true)
    private String wknsTpc;

    public AiCmtCmRptLuDto toDto() {
        return AiCmtCmRptLuDto.builder()
                .strthTpc(this.strthTpc)
                .wknsTpc(this.wknsTpc)
                .build();
    }
}
