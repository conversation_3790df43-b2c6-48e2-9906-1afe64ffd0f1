<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.cm.stu">

	<!-- 첨부 등록 -->
	<insert id="insertAnnx" parameterType="com.aidt.api.bc.cm.dto.BcAnnxFleDto" >
		/** BcCmStu-Mapper.xml - insertAnnx */
		INSERT INTO LMS_LRM.CM_ANNX(
			ANNX_ID, USE_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{annxId}
			, #{useYn}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 첨부파일 등록 -->
	<insert id="insertAnnxFle" parameterType="com.aidt.api.bc.cm.dto.BcAnnxFleDto" useGeneratedKeys="true" keyProperty="annxFleId">
		/** BcCmStu-Mapper.xml - insertAnnxFle */
		INSERT INTO LMS_LRM.CM_ANNX_FLE(
			ANNX_ID, SRT_ORDN, DOC_VI_ID, ANNX_FLE_NM, ANNX_FLE_ORGL_NM, ANNX_FLE_FEXT_NM, ANNX_FLE_SZE, ANNX_FLE_PTH_NM, USE_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{annxId}
			, (SELECT COALESCE(A.SRT_ORDN, 0) + 1 FROM LMS_LRM.CM_ANNX_FLE A WHERE ANNX_ID = #{annxId})
			, #{docViId}
			, #{annxFleNm}
			, #{annxFleOrglNm}
			, #{annxFleFextNm}
			, #{annxFleSze}
			, #{annxFlePthNm}
			, #{useYn}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>


	<!-- 감정 상태 변경 -->
	<insert id="updateCrud" parameterType="com.aidt.api.bc.cm.dto.FlnDto" >
		/** BcCmStu-Mapper.xml - updateCrud */
		UPDATE LMS_LRM.CM_USR
		SET
			FLN_ST_CD = #{flnStCd}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE
			USR_ID = #{usrId}
	</insert>

	<!-- 같은반 학생 목록 -->
	<select id="sameClaStuList" parameterType="com.aidt.api.bc.cm.dto.BcUserInfoDto" resultType="com.aidt.api.bc.cm.dto.BcUserInfoDto" >
		/** BcCmStu-Mapper.xml - sameClaStuList */
		SELECT CU.USR_ID , CU.USR_NM, CU.STU_NO , CU.KERIS_USR_ID
		  FROM CM_USR CU 
		 WHERE CU.CLA_ID IN (SELECT CLA_ID 
		                       FROM CM_USR 
		                      WHERE USR_ID = #{usrId})
	</select>

</mapper>
