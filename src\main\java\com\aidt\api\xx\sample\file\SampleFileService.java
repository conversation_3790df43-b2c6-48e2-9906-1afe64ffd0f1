package com.aidt.api.xx.sample.file;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.aidt.api.xx.sample.file.dto.SampleFileDto;
import com.aidt.common.util.ConstantsExt;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SampleFileService {
    private final DataFormatter formatter = new DataFormatter();

    /**
     * 단일 파일 저장
     * 
     * @param multipartFile
     * @param addPath
     * @return FileDto
     * @throws IOException
     */
    public SampleFileDto storeFile(final MultipartFile multipartFile, String addPath) throws IOException {
        if (multipartFile.isEmpty()) {
            return null;
        }
        String saveLocation = ConstantsExt.multipartLocation;
        if (!addPath.isBlank()) {
            saveLocation += addPath;
        }
        String originalFileName = multipartFile.getOriginalFilename();

        // UUID 4 사용 중복 안되게 파일명 변경
        String saveFileName = UUID.randomUUID() + "." + extractExt(originalFileName);
        File dirCheck = new File(saveLocation);
        if (!dirCheck.exists() && (dirCheck.mkdirs())) {
            log.debug("# Make Directory[ " + dirCheck.getPath() + " ]");
        }
        // 파일저장
        // log.debug("## Save Location : " + saveLocation + File.separator +
        // saveFileName);
        String saveFullPath = saveLocation + "/" + saveFileName;
        multipartFile.transferTo(Paths.get(saveFullPath));
        return SampleFileDto.builder()
                .originalFileName(originalFileName)
                .storeFileName(saveFileName)
                .fileSize(multipartFile.getSize())
                .contentType(multipartFile.getContentType())
                .downPath(saveFullPath)
                .build();
    }

    /**
     * 다중 파일 저장
     * 
     * @param multipartFiles
     * @param addPath
     * @return List<FileDto>
     * @throws IOException
     */
    public List<SampleFileDto> storeFiles(final List<MultipartFile> multipartFiles, String addPath) throws IOException {
        List<SampleFileDto> storeFileResult = new ArrayList<>();
        for (MultipartFile multipartFile : multipartFiles) {
            if (!multipartFile.isEmpty()) {
                storeFileResult.add(storeFile(multipartFile, addPath));
            }
        }
        return storeFileResult;
    }

    /**
     * 파일 다운로드
     * 
     * @param res
     * @param fileDto
     * @throws IOException
     */
    public ResponseEntity<Resource> download(SampleFileDto fileDto) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition",
                "attachment; filename=\"" + URLEncoder.encode(fileDto.getOriginalFileName(), "utf-8") + "\"");
        File file = new File(fileDto.getDownPath());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        String mimeType = URLConnection.guessContentTypeFromName(fileDto.getOriginalFileName());
        if (mimeType == null) {
            mimeType = "application/octet-stream";
        }
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .contentType(MediaType.parseMediaType(mimeType))
                .body(resource);
    }

    /**
     * 엑셀을 읽어 List<Map<String, String>>으로 반환
     *
     * @param excelFile  엑셀파일
     * @param sheetIndex 시트번호, 첫 시트번호는 0
     * @return List<Map<String, String>>
     * @throws IOException
     */
    public List<Map<String, String>> excelParser(final MultipartFile excelFile, final int sheetIndex,
            final boolean isHeader) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(excelFile.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int rowCount = sheet.getPhysicalNumberOfRows();
            log.debug("## RowCount : " + rowCount);
            if (rowCount <= 1) {
                return null;
            }

            List<String> header = new ArrayList<>();
            Row headerRow = sheet.getRow(0);

            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                Cell cursor = headerRow.getCell(i);
                if (isHeader) {
                    header.add(formatter.formatCellValue(cursor));
                } else {
                    header.add(String.valueOf(headerRow.getCell(i).getColumnIndex()));
                }
            }
            log.debug("## header.size() : " + header.size());
            List<Map<String, String>> result = new ArrayList<>();
            for (int i = 1; i < rowCount; i++) {
                Row row = sheet.getRow(i);
                if (null == row) {
                    log.debug("## ROW IS NULL !!!!!!!!!!!!!");
                    continue;
                }
                result.add(parseRow(header, row));
            }
            return result;
        }
    }

    /**
     * 1개의 행을 읽어 Map으로 반환
     *
     * @param header 헤더 리스트 (첫 행)
     * @param row    데이터 (2번쨰 행부터)
     * @return Map
     */
    private Map<String, String> parseRow(List<String> header, Row row) {
        // log.debug("header.size() : " + header.size());
        // log.debug("row.toString() : " + row.toString());
        Map<String, String> data = new HashMap<>();
        for (int i = 0; i < row.getPhysicalNumberOfCells(); i++) {
            Cell cursor = row.getCell(i);
            data.put(header.get(i), formatter.formatCellValue(cursor));
        }
        return data;
    }

    /**
     * 확장자 추출
     * 
     * @param originalFilename
     * @return String
     */
    private String extractExt(String originalFilename) {
        int pos = originalFilename.lastIndexOf(".");
        return originalFilename.substring(pos + 1);
    }

    // /**
    // * @return String
    // */
    // private String getToDay() {
    // return
    // LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd")).toString();
    // }
}