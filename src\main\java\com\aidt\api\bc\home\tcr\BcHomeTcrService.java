package com.aidt.api.bc.home.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aidt.api.bc.home.tcr.dto.BcRcnClaWrtSmtReqDto;
import com.aidt.api.bc.home.tcr.dto.BcRcnClaEvSmtDto;
import com.aidt.api.bc.home.tcr.dto.BcRcnClaEvSmtReqDto;
import com.aidt.api.bc.home.tcr.dto.BcStuCurLrnStDtlDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.home.tcr.dto.BcHomeTcrDto;
import com.aidt.common.CommonDao;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:51:19
 * @modify 2024-01-05 17:51:19
 * @desc 교사_홈 Service
 */

@Service
public class BcHomeTcrService {

    private final String MAPPER_NAMESPACE = "api.bc.home.tcr.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 홈 조회 서비스
     *
     * @param BcHomeTcrDto
     * @return List<BcHomeTcrDto>
     */
    public List<BcHomeTcrDto> selectHomeList(String userId) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectHomeList", userId);
    }

    /**
     * @param usrId
     * @return
     */

    @Transactional(readOnly = true)
    @Cacheable(
            cacheNames = "shortCache",
            key = "'bc:' + #params['optTxbId'] + ':selectStuCurLrnStDtl:' + #curLrnSt.getLrnTpCd() + ':' + (#curLrnSt.getLuNodId() ?: '-') + ':' + (#curLrnSt.getTcNodId() ?: '-') + ':' + (#curLrnSt.getLrnAtvId() ?: '-') + ':' + (#curLrnSt.getCurLrnMenuId() ?: '-')",
            condition = "!(#curLrnSt.getLrnTpCd() == 'titleOnly' || #curLrnSt.isRootLocation())",
            cacheManager = "aidtCacheManager"
    )
    public String selectStuCurLrnStDtl(Map<String, String> params, BcStuCurLrnStDtlDto curLrnSt) {
        if (curLrnSt == null) {
            return null;
        }
        List<String> dtlList = new ArrayList<>();
        String titleNm = curLrnSt.getCurLrnMenuNm();

        if ("titleOnly".equalsIgnoreCase(curLrnSt.getLrnTpCd())) {
            return titleNm;
        }

        if ("TL".equalsIgnoreCase(curLrnSt.getLrnTpCd())) {
            params.put("tcNodId", curLrnSt.getTcNodId());
            params.put("atvId", curLrnSt.getLrnAtvId());
            Map<String, String> qResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthTl", params);
            if (qResult == null) return titleNm;
            if (qResult.get("fmtLu") != null) {
                dtlList.add(qResult.get("fmtLu"));
            }
            if (qResult.get("fmtTc") != null) {
                dtlList.add(qResult.get("fmtTc"));
            }
            if (params.get("isEng") == "true" && qResult.get("stpNm") != null) {
                dtlList.add(qResult.get("stpNm"));
            }
            if (qResult.get("atvNm") != null) {
                dtlList.add(qResult.get("atvNm"));
            }
            if (Long.valueOf(5).equals(curLrnSt.getCurLrnMenuId())) {
                params.put("evId", curLrnSt.getLrnAtvId());
                Map<String, String> evResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthEv", params);
                if (evResult != null && evResult.get("fmtEvNm") != null) {
                    dtlList.add(evResult.get("fmtEvNm"));  // 평가명
                }
            }
        } else if ("AL".equalsIgnoreCase(curLrnSt.getLrnTpCd())) {
            if (Long.valueOf(0).equals(curLrnSt.getCurLrnMenuId())) {
                dtlList.add("Lesson Study");
            } else if (Long.valueOf(1).equals(curLrnSt.getCurLrnMenuId())) {
                dtlList.add("Let's Talk");
            } else if (Long.valueOf(2).equals(curLrnSt.getCurLrnMenuId())) {
                dtlList.add("Let's Write");
                params.put("kmmpNodId", curLrnSt.getTcNodId());
                Map<String, String> alResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthAl", params);
                if (alResult != null && alResult.get("kmmpNodNm") != null) {
                    dtlList.add(alResult.get("kmmpNodNm"));
                }
                if (alResult != null && alResult.get("tpcKmmpNodNm") != null) {
                    dtlList.add(alResult.get("tpcKmmpNodNm"));
                }
            } else if (Long.valueOf(4).equals(curLrnSt.getCurLrnMenuId())) { // AI 맞춤 학습 (학습창)
                titleNm = "AI 맞춤 학습";
                if (params.get("isEng").equals("true")) {
                    dtlList.add("Lesson Study");
                }
                params.put("kmmpNodId", curLrnSt.getTcNodId());
                params.put("evId", curLrnSt.getLrnAtvId());
                Map<String, String> alResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthAlLw", params);
                if (alResult != null && alResult.get("fmtTcNm") != null) {
                    dtlList.add(alResult.get("fmtTcNm"));
                }
                if (alResult != null && alResult.get("evNm") != null) {
                    dtlList.add(alResult.get("evNm"));
                }
            }
        } else if ("AS".equalsIgnoreCase(curLrnSt.getLrnTpCd())) {
            params.put("asnId", curLrnSt.getLrnAtvId());
            Map<String, String> asnResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthAsn", params);
            if (asnResult == null) return titleNm;
            if (asnResult.get("asnTpNm") != null) {
                String asnTpNm = asnResult.get("asnTpNm"); // 과제유형
                dtlList.add(asnTpNm);
            }
            if (asnResult.get("asnNm") != null) {
                dtlList.add(asnResult.get("asnNm"));  // 과제명
            }
        } else if ("EV".equalsIgnoreCase(curLrnSt.getLrnTpCd())) {
            params.put("evId", curLrnSt.getLrnAtvId());
            Map<String, String> evResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthEv", params);
            if (evResult != null && evResult.get("fmtLuNm") != null) {
                String evTpNm = evResult.get("fmtLuNm"); // 단원명
                dtlList.add(evTpNm);
            }
            if (evResult != null && evResult.get("fmtEvNm") != null) {
                dtlList.add(evResult.get("fmtEvNm"));  // 평가명
            }
        } else if ("SL".equalsIgnoreCase(curLrnSt.getLrnTpCd())) {
            params.put("spLrnId", curLrnSt.getTcNodId());
            if (curLrnSt.getLrnAtvId() != null) {
                params.put("lrnAtvId", curLrnSt.getLrnAtvId());
                Map<String, String> lrnResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthSlLw", params);
                if (lrnResult != null && lrnResult.get("depth1") != null) {
                    String lrnTpNm = lrnResult.get("depth1");
                    dtlList.add(lrnTpNm);
                }
                if (lrnResult != null && lrnResult.get("depth2") != null) {
                    dtlList.add(lrnResult.get("depth2"));
                }
                if (lrnResult != null && lrnResult.get("depth3") != null) {
                    dtlList.add(lrnResult.get("depth3"));
                }
            } else {
                Map<String, String> lrnResult = commonDao.select(MAPPER_NAMESPACE + "selectStuCurLrnStPthSl", params);
                if (lrnResult != null && lrnResult.get("spLrnNm") != null) {
                    dtlList.add(lrnResult.get("spLrnNm"));
                }
            }
        }
        if (dtlList.size() > 0) {
            return titleNm + " · " + String.join(" > ", dtlList);
        }
        return titleNm;
    }

    public List<BcRcnClaEvSmtDto> selectRcnClaEvSmt(BcRcnClaEvSmtReqDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectRcnClaEvSmt", dto);
    }

    public Map<String, Object> selectRcnClaWrtSmt(BcRcnClaWrtSmtReqDto bcRcnClaWrtSmtReqDto) {
        Map<String, Object> result = new HashMap<>();
        List<BcRcnClaEvSmtDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectRcnClaWrtSmt", bcRcnClaWrtSmtReqDto);
        Map<String, Integer> count = commonDao.select(MAPPER_NAMESPACE + "selectRcnClaWrtSmtCnt", bcRcnClaWrtSmtReqDto);
        result.put("list", list);
        result.put("count", count);
        return result;
    }

    public List<Map<String, String>> selectGDEStuList(Map<String, String> params) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectGDEStuList", params);
    }

    public List<Map<String, Object>> selectLluAch(String optTxbId) {
        List<Map<String, Object>> achList = commonDao.selectList(MAPPER_NAMESPACE + "selectLluAch", optTxbId);
        List<Map<String, Object>> result = new ArrayList<>();
        if (achList == null) {
            return result;
        }

        return achList;
    }
}
