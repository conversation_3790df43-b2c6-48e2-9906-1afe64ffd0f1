package com.aidt.api.bc.home.service;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.home.adapter.TeacherHomeAdapter;
import com.aidt.api.bc.home.dto.HomeSummaryAiCoaching;
import com.aidt.api.common.helper.JwtHelper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true)
@Service
@RequiredArgsConstructor
public class TeacherHomeQueryService {

	private final TeacherHomeAdapter teacherHomeAdapter;
	private final JwtHelper jwtHelper;

	public List<HomeSummaryAiCoaching> getHomeSummaryAiCoaching(Integer mluKmmpNodId) {
		var user = jwtHelper.getCommonUserDetail();

		if (ObjectUtils.anyNull(user, mluKmmpNodId)) {
			log.error("입력 값이 잘못되어 AI 맞춤학습 코칭 요약을 불러올 수 없습니다. :: user={}, mluKmmpNodId={}", user, mluKmmpNodId);
			throw new IllegalArgumentException("AI 맞춤학습 코칭 요약을 불러올 수 없습니다. ");
		}

		var homeSummaryAiCoaching = teacherHomeAdapter.getHomeSummaryAiCoaching(user.getOptTxbId(), mluKmmpNodId);
		if (CollectionUtils.isEmpty(homeSummaryAiCoaching)) {
			log.warn("AI 맞춤학습 코칭 요약을 불러올 수 없습니다. :: user={}, mluKmmpNodId={}", user, mluKmmpNodId);
			return Collections.emptyList();
		}
		return homeSummaryAiCoaching;
	}
}