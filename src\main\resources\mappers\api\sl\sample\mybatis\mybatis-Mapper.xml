<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.sl.sample.mybatis">
	<select id="selectList" resultType="com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto">
	    SELECT 	  IDX
				, USR_ID
				, USR_NM
				, CNTRY
				, PHONE
				, COLOR
				, PRICE
				, QUAN
				, DATE_FORMAT(DT, '%Y-%m-%d') AS DT
		FROM TEMP_USR_DAT
		WHERE 1=1
		<if test = 'userId != null and !"".equals(userId)'>
			AND USR_ID LIKE CONCAT('%',#{userId},'%')
		</if>
		ORDER BY IDX DESC
	</select>

	<insert id="insertList" parameterType="com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto">
		INSERT INTO TEMP_USR_DAT(
			IDX, USR_ID, USR_NM, CNTRY, PHONE, COLOR, PRICE, QUAN, DT
		) VALUES (
			( SELECT IFNULL( MAX(IDX)+1,1) FROM TEMP_USR_DAT TUD) ,
			#{usrId},
			#{usrNm},
			#{cntry},
			#{phone},
			#{color},
			#{price},
			#{quan},
			CURDATE()
		)
	</insert>
	<update id="updateList" parameterType="com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto"> 
   		UPDATE TEMP_USR_DAT SET
			USR_NM = #{usrNm}, 
			CNTRY = #{cntry}, 
			PHONE = #{phone}, 
			COLOR = #{color}, 
			PRICE = #{price}, 
			QUAN = #{quan}, 
			DT = STR_TO_DATE(#{dt},'%Y-%m-%d')
		WHERE USR_ID = #{usrId}
    </update>

	<delete id="deleteList" parameterType="com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto">
		DELETE FROM TEMP_USR_DAT WHERE USR_ID = #{usrId}
	</delete>

	<insert id="bulkInsert" parameterType="com.aidt.api.xx.sample.mybatis.dto.SampleMybatisDto">
		INSERT INTO TEMP_USR_DAT(
			IDX, USR_ID, USR_NM, CNTRY, PHONE, COLOR, PRICE, QUAN, DT
		) VALUES
		<foreach collection="list" index="index" item="item" separator=",">
		(
			( SELECT IFNULL( MAX(IDX)+1,1) FROM TEMP_USR_DAT TUD),
			#{item.usrId},
			#{item.usrNm},
			#{item.cntry},
			#{item.phone},		
			#{item.color},		
			#{item.price},
			#{item.quan},	
			CURDATE()	
		)		
		</foreach>
	</insert>

</mapper>