<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.mntr.tcr">

	<!-- 실시간 모니터링 지도 필요 목록 조회 -->
	<select id="selectGdeNeedList" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto" resultType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/** BcMntrTcr-Mapper.xml - selectGdeNeedList */
		SELECT A.OPT_TXB_ID 
			 , A.STU_USR_ID AS LRN_USR_ID
			 , A.GDE_NEED_CD 
			 , A.COFM_YN
			 , B.SETM_VL_1 AS GDE_NEED_CD_NM
			 , CASE 
					WHEN A.GDE_NEED_CD IN ('ASA')
					THEN (SELECT ASN_NM
							FROM LMS_LRM.EA_ASN
						   WHERE ASN_ID = A.GDE_NEED_VL)
					WHEN A.GDE_NEED_CD IN ('ETA', 'ERA')
					THEN (SELECT EV_NM
							FROM LMS_LRM.EA_EV
						   WHERE EV_ID = A.GDE_NEED_VL)
			   END AS GDE_NEED_VL_NM
			 , DATE_FORMAT(A.CRT_DTM, '%m. %d.' ) AS CRT_DTM
		  FROM LMS_LRM.CM_GDE_NEED A
		 INNER JOIN LMS_LRM.CM_CM_CD B
		    ON B.URNK_CM_CD = 'GDE_NEED_CD'
		   AND A.GDE_NEED_CD = B.CM_CD
		 WHERE A.OPT_TXB_ID  = #{optTxbId}
		   AND A.STU_USR_ID = #{lrnUsrId}
		 ORDER BY A.CRT_DTM DESC
	</select>
	
	<!-- 실시간 모니터링 지도 필요 목록 확인 여부 수정 -->
	<update id="updateGdeNeedCofmYn" parameterType="com.aidt.api.bc.mntr.dto.BcMntrDto">
		/** BcMntrTcr-Mapper.xml - updateGdeNeedCofmYn */
		UPDATE LMS_LRM.cm_gde_need
		   SET COFM_YN = 'Y'
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE OPT_TXB_ID = #{optTxbId}
		  AND STU_USR_ID = #{lrnUsrId}
		  AND COFM_YN = 'N'
	</update>
</mapper>