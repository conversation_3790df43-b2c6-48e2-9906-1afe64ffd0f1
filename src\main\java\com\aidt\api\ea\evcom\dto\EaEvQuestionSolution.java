package com.aidt.api.ea.evcom.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EaEvQuestionSolution {

	private Long qpQtmId;              // 문항 아이디
	private String qpQstTypCd;         // 문항 유형 코드  
	private String qpJsonDataCn;       // json data
	private Integer partCansCnt;       // 부분 정답 개수
	private Integer plurCansCnt;       // 복수 정답 개수

	@Builder
	@Getter
	@NoArgsConstructor
	@AllArgsConstructor
	public static class EaEvQuestionKeyword {
		private Long qpQtmId;                // 문항 아이디
		private String qpKwd;                // 키워드
		private Integer kwdCnt;              // 키워드 수
		private String kwdCaseYn;            // 대소문자여부
		private String kwdSpaceSkipYn;       // 공백무시여부
	}

}
