package com.aidt.api.tl.cmtxb.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 08:46:16
 * @modify date 2024-02-15 08:46:16
 * @desc [TlCmTxbSrhDto 교과학습 대단원, 차시 조회 조건 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    /** 학기코드 */
    @Parameter(name="학기코드")
    private String trmDvCd;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;

    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;

    /** 학습상태포함여부 */
    @Parameter(name="학습상태포함여부")
    private String withLrnStYn;
    
    /** 비노출단원표시여부 */
    @Parameter(name="비노출단원표시여부")
    private String withLrnLuYn;
}
