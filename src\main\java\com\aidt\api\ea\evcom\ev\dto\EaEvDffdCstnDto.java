package com.aidt.api.ea.evcom.ev.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-25
 * @modify date 2024-01-25
 * @desc 평가 관리 - 교사 평가난이도구성 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvDffdCstnDto {

	@Parameter(name="평가ID")
	private long evId;
	
	@Parameter(name="평가난이도구분코드", required = true)
    @NotBlank(message = "{field.required}")
	private String evDffdDvCd;
	
	@Parameter(name="평가난이도구분명")
	private String evDffdDvCdNm;
	
	@Parameter(name="평가난이도분포수", required = true)
    @NotBlank(message = "{field.required}")
	private int evDffdDsbCnt;
}
