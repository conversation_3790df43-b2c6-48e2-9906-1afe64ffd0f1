package com.aidt.api.sl.cm.dto;

import javax.validation.constraints.Min;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-08 09:37:48
 * @modify date 2024-05-08 09:37:48
 * @desc [공통 특별학습 조회 dto]
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlCmSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습사용자ID */
    @Parameter(name="학습사용자ID")
    private String lrnUsrId;

    /** 특별학습 최대 개수 */
    @Parameter(name="특별학습 최대 개수")
    @Min(value = 0, message = "0 이상의 숫자만 가능합니다.")
    private int spLrnMax;
}
