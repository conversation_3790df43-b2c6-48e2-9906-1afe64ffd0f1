package com.aidt.api.bc.inf.common;

public enum InfPathEnum {
    
	// 평가 등록(학생)
	EA_EV_STU_MAIN("/ea/ev/stu/EaEvStuMain.vue"),
	
	// 평가 마감(교사)
	EA_EV_TCR_MAIN("/ea/ev/tcr/EaEvTcrMain.vue"),
	
	// 새로운 과제 등록(학생)
	EA_ASN_STU_MAIN("/ea/asn/stu/EaAsnStuMain.vue"),
	
	// 일반 과제 변경,마감(학생)
	EA_ASN_GENERAL_STU_DETAIL("/ea/asn/stu/EaAsnGeneralStuDetail.vue"),
	
	// 일반 과제 마감(교사)
	EA_ASN_GENERAL_TCR_DETAIL("/ea/asn/tcr/EaAsnGeneralTcrDetail.vue"),
	
	// 우리반 수업 과제 마감(학생)
	EA_ASN_SBJ_STU_DETAIL("/ea/asn/stu/EaAsnSbjStuDetail.vue"),

	// 우리반 수업 과제 마감(교사)
	EA_ASN_SBJ_TCR_DETAIL("/ea/asn/tcr/EaAsnSbjTcrDetail.vue"),
		
	// 모둠 과제 마감(교사)
	EA_ASN_GRP_TCR("/ea/asn/tcr/EaAsnGrpTcr.vue"),
	
	EA_ASN_GRP_STU_DETAIL("/ea/asn/stu/EaAsnGrpStuDetail.vue"),	

	// AI 첨삭 결과 등록(학생)
	AL_PL_STU_MAIN("/al/pl/stu/AlPlStuMain.vue"),
	
	// AI 첨삭 제출 완료(교사)
	AL_PL_TCR_MAIN("/al/pl/tcr/AlPlTcrMain.vue"),
	
	// 공지사항 등록(학생)
	BC_CLA_BLBD_STU_DTL("/bc/claBlbd/stu/BcClaBlbdStuDtl.vue"),
	
	// 공지사항 등록(교사)
	BC_CLA_BLBD_TCR_DTL("/bc/claBlbd/tcr/BcClaBlbdTcrDtl.vue"),

	// 칭찬도장 지급
	LW_MYHM_PNT_HIS_STU_MAIN("/lw/myhmpnt/stu/LwMyhmPntHisStuMain.vue"),
	
	// 단원별피드백
	EA_LRN_RPT_STU_MAIN("/ea/lrnrpt/stu/EaLrnRptStuMain.vue"),
	
	// 선생님 추천학습
	SLSP_LRN_STU_MAIN("/sl/splrn/stu/SlSpLrnStuMain.vue");
	
    private final String path;

    InfPathEnum(String path) {
        this.path = path;
    }

    public String getPath() {
        return path;
    }

    public static String fromPath(String path) {
        for (InfPathEnum component : values()) {
            if (component.getPath().equals(path)) {
                return component.name();
            }
        }
        throw new IllegalArgumentException("Unknown path: " + path);
    }
}
