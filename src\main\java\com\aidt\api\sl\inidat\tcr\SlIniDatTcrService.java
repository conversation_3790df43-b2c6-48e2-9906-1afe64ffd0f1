package com.aidt.api.sl.inidat.tcr;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-10 11:28:18
 * @modify date 2024-05-10 14:28:18
 * @desc SlIniDatTcr Service 특별학습학습 초기데이터작성 서비스
 */
@Slf4j
@Service
public class SlIniDatTcrService {
    private final String MAPPER_NAMESPACE = "api.sl.inidat.tcr.";

    @Autowired
    private CommonDao commonDao;
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

    /**
     * 특별학습학습재구성데이터 등록처리
     * @param cndDto
     * @return
     */
    @Transactional
    public int registIniDat(SlIniDatCondDto cndDto) {
        int rtnVal = 0;
        // 1.운영교과서ID로 교과서ID및담당클래스를 조회한다.
        List<Map<String, Object>> optTxbIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectOptTxbId", cndDto);
        List<SlIniDatCondDto> tgtList = new ArrayList<SlIniDatCondDto>();
        String txbId = "";

        // 2.담당클래스에 재구성데이터가 생성되어있는지 체크한다.
        for(Map<String, Object> item: optTxbIdList) {
            if ("0".equals(item.get("CNT").toString())) {
                SlIniDatCondDto dto = new SlIniDatCondDto();
                dto.setOptTxbId(item.get("OPT_TXB_ID").toString());
                dto.setTcrUsrId(cndDto.getTcrUsrId());
                tgtList.add(dto);
                if ("".equals(txbId)) {
                    txbId = item.get("TXB_ID").toString();
                }
            }
        }

        if (tgtList.size() == 0) {
            return rtnVal;
        }

        log.debug("##### 생성대상 TXB_ID = " + txbId);

        // 3. 처리2에서 생성되지 않은 것이 판명되면 재구성데이터를 생성한다.
        
        for(SlIniDatCondDto tgtDto: tgtList) {
            log.debug("##### 생성대상 운영교과서ID = " + tgtDto.getOptTxbId() + ",  교사ID = " + tgtDto.getTcrUsrId());
            
            int insCnt1 = 0;
            
            try {
                // SL_특별학습재구성 데이터 작성
                insCnt1 = commonDao.insert(MAPPER_NAMESPACE + "insertSlSpLrnRcstn",
                            Map.of("tgtOptTxbId", tgtDto.getOptTxbId(),"tgtTxtId", txbId, "tgtTcrUsrId", tgtDto.getTcrUsrId(), "dbId", DB_ID));
			} catch(DuplicateKeyException dke) {
				log.error("Duplicate error (SL_특별학습재구성 데이터 작성)");
				log.error(dke.getMessage());
			}

            log.debug("##### SL_특별학습재구성 건수 = " + String.valueOf(insCnt1));
        }
        return rtnVal;
    }
}
