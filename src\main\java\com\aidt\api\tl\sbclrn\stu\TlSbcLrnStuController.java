package com.aidt.api.tl.sbclrn.stu;

import java.util.List;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvEvDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStChkDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStatDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumEvWkbDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvWbDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocDto;
import com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:29:09
 * @modify date 2024-01-04 14:29:09
 * @desc TlSbcLrnStu Service 교과학습서비스
 */
@Slf4j
@Tag(name="[tl] 교과학습[TlSbcLrnStu]", description="교과학습(학생용)화면 구성을 위한 처리를 실행한다.")
@RestController
@RequestMapping("/api/v1/tl/stu/sbclrn")
public class TlSbcLrnStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlSbcLrnStuService tlSbcLrnStuService;

    /**
     * 교과학습 학습활동상세 Summery정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<TlSbcLrnAtvDto>
     */
    @Operation(summary="교과학습 학습활동상세 Summary조회", description="교과학습활동의 교과학습 Summary정보를 조회한다.")
    @PostMapping(value = "/selectLrnAtvSum", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlSbcLrnAtvSumDto> selectLrnAtvSum(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvSum");
        
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(tlSbcLrnStuService.selectLrnAtvSum(srhDto));
    }

    /**
     * 교과학습 학습활동 평가/익힘책 Summery정보 조회
     * 
     * @param srhDto
     * @return ResponseDto<TlSbcLrnAtvDto>
     */
    @Operation(summary="교과학습 학습활동 평가/익힘책 Summary조회", description="교과학습활동의 평가 익힘책 Summary정보를 조회한다.")
    @PostMapping(value = "/selectLrnEvWkbSum", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlSbcLrnAtvSumEvWkbDto> selectLrnEvWkbSum(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnEvWkbSum");
        
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlSbcLrnStuService.selectLrnEvWkbSum(srhDto));
    }

    /**
     * 교과학습 학습활동 평가정보 조회(v2.3대응)
     * 
     * @param srhDto
     * @return ResponseDto<selectLrnEvInfo>
     */
    @Operation(summary="교과학습 학습활동 평가정보조회", description="교과학습활동의 평가정보를 조회한다.")
    @PostMapping(value = "/selectLrnEvInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlSbcLrnAtvEvDto> selectLrnEvInfo(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnEvInfo");
        
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlSbcLrnStuService.selectLrnEvInfo(srhDto));
    }

    /**
     * 교과학습 학습활동 익힘책정보 조회(v2.3대응)
     * 
     * @param srhDto
     * @return ResponseDto<TlSbcLrnAtvDto>
     */
    @Operation(summary="교과학습 학습활동 익힘책정보조회", description="교과학습활동의 평가정보를 조회한다.")
    @PostMapping(value = "/selectLrnWbInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlSbcLrnAtvWbDto> selectLrnWbInfo(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnEvInfo");
        
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());

        return Response.ok(tlSbcLrnStuService.selectLrnWbInfo(srhDto));
    }

    /**
     * 교과학습 학습활동상태 체크
     * 
     * @return ResponseDto<TlSbcLrnAtvStChkDto>
     */
    @Operation(summary="교과학습 학습활동상태 체크", description="교과학습 학습활동상태 체크하여 최근에 학습한 차시ID를 취득한다.")
    @PostMapping(value = "/selectLrnAtvStChk")
    public ResponseDto<List<TlSbcLrnAtvStChkDto>> selectLrnAtvStChk() {
        log.debug("Entrance selectLrnAtvStChk");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        TlSbcLrnTocSrhDto srhDto = new TlSbcLrnTocSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setLrnUsrId(userDetails.getUsrId());
        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_STU);

        return Response.ok(tlSbcLrnStuService.selectLrnAtvStChk(srhDto));
    }

    /**
     * 교과학습 차시 학습현황목록 조회
     * 
     * @return ResponseDto<TlSbcLrnAtvStatDto>
     */
//    @Operation(summary="교과학습 차시 학습현황목록 조회", description="해당 차시의 학습활동상태의 상세정보 목록을 조회한다.")
//    @PostMapping(value = "/selectLrnTocStatList", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<List<TlSbcLrnAtvStatDto>> selectLrnTocStatList(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
//        log.debug("Entrance selectLrnTocStatList");
//
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//
//        // 세션정보에서 설정
//        srhDto.setOptTxbId(userDetails.getOptTxbId());
//        srhDto.setLrnUsrId(userDetails.getUsrId());
//
//        return Response.ok(tlSbcLrnStuService.selectLrnTocStatList(srhDto));
//    }

    /**
     * 교과학습 차시 학습현황목록 조회(v3.3)
     * 
     * @return ResponseDto<TlSbcLrnAtvStatDto>
     */
    @Operation(summary = "교과학습 차시 학습현황목록 조회(v3.3)", description = "해당 차시의 학습단계별(개념/익힘/평가) 학습활동상태의 상세정보 목록을 조회한다.")
    @PostMapping(value = "/selectLrnTocStatusList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> selectLrnTocStatusList(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
        log.debug("Entrance selectLrnTocStatusList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        if(StringUtils.isEmpty(srhDto.getLrnUsrId())) {
        	 srhDto.setLrnUsrId(userDetails.getUsrId());
        }
        return Response.ok(tlSbcLrnStuService.selectLrnTocStatusList(srhDto));
    }


    /**
     * 교과학습 학습활동 목록 조회서비스
     * 
     * @param srhDto
     * @return ResponseDto<List<TlSbcLrnAtvDto>>
     */
//    @Operation(summary="교과학습 학습활동리스트 조회", description="해당 차시의 학습활동목록을 조회한다.")
//    @PostMapping(value = "/selectLrnAtvList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<List<TlSbcLrnAtvDto>> selectLrnAtvList(@Valid @RequestBody TlSbcLrnTocSrhDto srhDto) {
//        log.debug("Entrance selectLrnAtvList");
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//
//        if (StringUtils.isEmpty(srhDto.getLrmpNodId())) {
//            throw new IllegalArgumentException("Invalid lrmpNodId in TlSbcLrnTocSrhDto ");
//        }
//        // 세션정보에서 설정
//        srhDto.setOptTxbId(userDetails.getOptTxbId());
//
//        return Response.ok(tlSbcLrnStuService.selectLrnAtvList(srhDto));
//    }

}
