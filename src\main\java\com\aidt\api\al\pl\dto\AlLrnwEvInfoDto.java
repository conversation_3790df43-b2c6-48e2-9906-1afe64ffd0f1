package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI 맞춤 문항추천 서비스
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlLrnwEvInfoDto {

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="중단원 지식맵 노드ID")
	private String mluKmmpNodId;
	private String mluKmmpNodNm;

	@Parameter(name="차시 지식맵 노드ID")
	private String tcKmmpNodId;
	private String tcKmmpNodNm;
	private String tcKmmpNodNm2;

	@Parameter(name="사용자 학습수준 (빠른:FS, 보통:NM, 느린:SL)")
	private String lrnrVelTpCd;

	@Parameter(name="평가ID")
	private Integer evId;
	
	@Parameter(name="평가완료여부")
	private String evCmplYn;

	@Parameter(name="평가상세구분코드 UD:단원진단, C1:선택학습1, C2:선택학습2, ST:학기초진단, TO:차시평가, UG:단원총괄, ET:학기말평가")
	private String evDtlDvCd;
	
	@Parameter(name="단원평가완료여부")
	private String luevCmplYn;
	
	@Parameter(name="차시평균정답률")
	private Double tcAvgCansRt;
	private String tcAvgCansRtStr;


}
