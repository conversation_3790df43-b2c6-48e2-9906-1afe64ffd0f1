package com.aidt.api.bc.clablbd.dto;

import java.util.List;

import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-07 13:45:20
 * @modify 2024-06-07 13:45:20
 * @desc 학급게시판 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcClaBlbdDto{

	@Parameter(name="학급게시판ID")
	private Long claBlbdId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="사용자유형코드")
	private String usrTpCd;

	@Parameter(name="사용자유형코드명")
	private String usrTpCdNm;

	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="케리스ID")
	private String kerisUsrId;

	@Parameter(name="학급게시판제목명")
	@Size(max = 100, message = "제목은 2자 이상 100자 이하로 입력해주세요.")
	private String claBlbdTitlNm;

	@Parameter(name="학급게시판내용")
	private String claBlbdCn;

	@Parameter(name="첨부ID")
	private Long annxId;

	@Parameter(name="묶음게시판ID")
	private String pkgBlbdId;
	
	@Parameter(name="필독여부")
	private String rqrdYn;
	
	@Parameter(name="변경전필독여부")
	private String bfRqrdYn;

	@Parameter(name="댓글사용여부")
	private String ucwrUseYn;

	@Parameter(name="기능사용설정_첨삭허용여부")
	private String claBlbdWrtPmsnYn;

	@Parameter(name="기능사용설정_댓글사용여부")
	private String claBlbdUcwrPmsnYn;
	
	@Parameter(name="게시글승인여부")
	private String blwrAprYn;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성일시")
	private String crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private String mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;

	@Parameter(name="댓글수")
	private int ucwrCnt;
	
	@Parameter(name="댓글승인요청수")
	private int ucwrAprCnt;

	@Parameter(name="순번")
	private String blbdNum;
	
	@Parameter(name="첨부파일 리스트")
	List<BcAnnxFleDto> fleList;

	@Parameter(name="댓글 리스트")
	List<BcClaBlbdUcwrDto> ucwrList;
	
	@Parameter(name="사용자명")
	private String usrNm;
	
	@Parameter(name="내가쓴글")
	private String myWriter;
	
	@Parameter(name="등록수정삭제flag")
	private String flag;
	
	@Parameter(name="개시글 수")
	private int blbdCnt;

	@Parameter(name="필독 수")
	private int rqrdCnt;
	
	@Parameter(name="검색input")
	private String srchField;

	@Parameter(name="등록일(시작)")
	private String startDate;

	@Parameter(name="등록일(종료)")
	private String endDate;

	@Parameter(name="사용자ID목록")
	private String[] usrIds;
	
	@Parameter(name="일괄저장반목록")
	private String[] chkClaList;
	
	@Parameter(name="삭제할 아이디")
	private String[] delPkgList;

	@Parameter(name="페이지번호")
	private int pageNo;

	@Parameter(name="페이지표시수")
	private int pageSize;

	@Parameter(name="전체수")
	private int totalCnt;

	@Parameter(name="시작페이지번호")
	private int startPageNo;

}
