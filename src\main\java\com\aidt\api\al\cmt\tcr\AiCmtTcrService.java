package com.aidt.api.al.cmt.tcr;

import com.aidt.api.al.cmt.cm.AiCmtService;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;
import com.aidt.api.al.cmt.dto.ett.en.AiCmtEnEvEtDto;
import com.aidt.api.al.cmt.dto.ett.en.AiCmtEnEvStDto;
import com.aidt.api.al.cmt.dto.ett.en.AiCmtEnEvUgDto;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvEtDto;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvStDto;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvUdDto;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvUgDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvEtReqDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvStReqDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvToReqDto;
import com.aidt.api.al.cmt.dto.req.en.AiCmtEnEvUgReqDto;
import com.aidt.api.al.cmt.dto.req.ma.AiCmtMaEvEtReqDto;
import com.aidt.api.al.cmt.dto.req.ma.AiCmtMaEvStReqDto;
import com.aidt.api.al.cmt.dto.req.ma.AiCmtMaEvUdReqDto;
import com.aidt.api.al.cmt.dto.res.AiCmtEnResWrapperDto;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:01:58
 * @modify date 2024-05-21 11:01:58
 * @desc
 */
@Service
public class AiCmtTcrService {

    private final AiCmtService aiCmtService;

    public AiCmtTcrService(AiCmtService aiCmtService) {
        this.aiCmtService = aiCmtService;
    }

    public List<AiCmtResDto> selectMaEv(AiCmtMaEvStReqDto reqDto) {
        AiCmtMaEvStDto dto = reqDto.toDto();
        dto.setUsrTpCd("TE");
        return aiCmtService.selectMaEvSt(dto);
    }

    public List<AiCmtResDto> selectMaEv(AiCmtMaEvUdReqDto reqDto) {
        AiCmtMaEvUdDto dto = reqDto.toDto();
        dto.setUsrTpCd("TE");
        return aiCmtService.selectMaEvUd(dto);
    }

    public List<AiCmtResDto> selectMaEv(AiCmtMaEvEtReqDto reqDto) {
        AiCmtMaEvEtDto dto = reqDto.toDto();
        dto.setUsrTpCd("TE");
        return aiCmtService.selectMaEvEt(dto);
    }

    public AiCmtEnResWrapperDto selectEnEv(AiCmtEnEvStReqDto reqDto) {
        AiCmtEnEvStDto dto = reqDto.toDto();
        N12Dto n12 = reqDto.toDto().getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        dto.setUsrTpCd("TE");
        dto.getN12().setUsrTpCd("TE");

        List<AiCmtResDto> total = aiCmtService.selectEnEvSt(dto);

        List<AiCmtResDto> resultTotal = this.tpcNameConvert(total, dto.getAraRankList());

        return AiCmtEnResWrapperDto.builder().total(resultTotal).ara(ara).build();
    }

    public AiCmtEnResWrapperDto selectEnEv(AiCmtEnEvEtReqDto reqDto) {
        AiCmtEnEvEtDto dto = reqDto.toDto();
        N12Dto n12 = reqDto.toDto().getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        dto.setUsrTpCd("TE");
        dto.getN12().setUsrTpCd("TE");
        List<AiCmtResDto> total = aiCmtService.selectEnEvEt(dto);

        return AiCmtEnResWrapperDto.builder().total(total).ara(ara).build();
    }

    public AiCmtEnResWrapperDto selectEnEv(AiCmtEnEvUgReqDto reqDto) {
        AiCmtEnEvUgDto dto = reqDto.toDto();
        N12Dto n12 = reqDto.toDto().getN12();

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }

        dto.setUsrTpCd("TE");
        dto.getN12().setUsrTpCd("TE");
        List<AiCmtResDto> total = aiCmtService.selectEnEvUg(dto);

        List<AiCmtResDto> resultTotal = this.tpcNameConvert(total, dto.getAraRankList());

        return AiCmtEnResWrapperDto.builder().total(resultTotal).ara(ara).build();
    }

    public List<AiCmtResDto> selectEnEv(AiCmtEnEvToReqDto reqDto) {
        N12Dto n12 = reqDto.toDto().getN12();
        n12.setUsrTpCd("TE");

        List<AiCmtResDto> ara = null;
        if(!StringUtils.isEmpty(n12.toMergeString())) {
            ara = aiCmtService.selectEnAr(n12);
        }
        return ara;
    }

    private List<AiCmtResDto> tpcNameConvert(List<AiCmtResDto> total, List<String> araRankList) {
         if(araRankList.size() < 3) {
            return total.stream().filter(t -> !t.getAiCmtNo().equalsIgnoreCase("3")).collect(Collectors.toList());
        }

        return total.stream().map(t -> {
            if (t.getAiCmtNo().equalsIgnoreCase("3")) {
                String cmt = t.getCmtCn();
                cmt = cmt.replace("#강점1#와(과)", this.checkBatchimEnding(araRankList.get(0)) ? "#강점1#과" : "#강점1#와");
                cmt = cmt.replace("#약점1#와(과)", this.checkBatchimEnding(araRankList.get(araRankList.size() - 1)) ? "#약점1#과" : "#약점1#와");
                cmt = cmt.replace("#강점1#", String.format("#1#%s#2#", araRankList.get(0)));
                cmt = cmt.replace("#강점2#", String.format("#1#%s#2#", araRankList.get(1)));
                cmt = cmt.replace("#약점1#", String.format("#1#%s#2#", araRankList.get(araRankList.size() - 1)));
                cmt = cmt.replace("#약점2#", String.format("#1#%s#2#", araRankList.get(araRankList.size() - 2)));
                t.setCmtCn(cmt);
                return t;
            }
            return t;
        }).collect(Collectors.toList());
    }

    private boolean checkBatchimEnding(String name) {
        if(StringUtils.isEmpty(name)) return false;
        char lastName = name.charAt(name.length() - 1);
        return (lastName - 0xAC00) % 28 > 0;
    }

}
