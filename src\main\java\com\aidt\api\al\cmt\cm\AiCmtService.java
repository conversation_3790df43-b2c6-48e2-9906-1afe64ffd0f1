package com.aidt.api.al.cmt.cm;

import com.aidt.api.al.cmt.dto.ett.cm.AiCmtCmRptLuDto;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;
import com.aidt.api.al.cmt.dto.ett.en.*;
import com.aidt.api.al.cmt.dto.ett.ma.*;
import com.aidt.api.al.cmt.dto.res.AiCmtResDto;
import com.aidt.common.CommonDao;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-27 14:46:33
 * @modify date 2024-05-27 14:46:33
 * @desc
 */
@Service
public class AiCmtService {

    private final String MAPPER_NAMESPACE = "api.al.cmt.stu.";

    private final CommonDao commonDao;

    public AiCmtService(CommonDao commonDao) {
        this.commonDao = commonDao;
    }

    public List<AiCmtResDto> selectMaEvSt(AiCmtMaEvStDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMaEvStList", dto);
    }

    public List<AiCmtResDto> selectMaEvUd(AiCmtMaEvUdDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMaEvUdList", dto);
    }

    public List<AiCmtResDto> selectMaEvEt(AiCmtMaEvEtDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMaEvEtList", dto);
    }

    public List<AiCmtResDto> selectMaAiDgnRpt(AiCmtMaAiDgnRptDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMaAiDgnRptList", dto);
    }

    public List<AiCmtResDto> selectMaAiLrnRpt(AiCmtMaAiLrnRptDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMaAiLrnRptList", dto);
    }

    public List<AiCmtResDto> selectMaRptLu(AiCmtCmRptLuDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectMaRptLuList", dto);
    }

    public List<AiCmtResDto> selectEnEvTo(N12Dto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnAraList", dto);
    }

    public List<AiCmtResDto> selectEnEvSt(AiCmtEnEvStDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnEvStList", dto);
    }

    public List<AiCmtResDto> selectEnAr(N12Dto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnAraList", dto);
    }

    public List<AiCmtResDto> selectEnEvUg(AiCmtEnEvUgDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnEvUgList", dto);
    }

    public List<AiCmtResDto> selectEnEvEt(AiCmtEnEvEtDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnEvEtList", dto);
    }

    public List<AiCmtResDto> selectEnAiDgnRpt(AiCmtEnAiDgnRptDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnAiDgnRptList", dto);
    }

    public List<AiCmtResDto> selectEnAiLrnRpt(AiCmtEnAiLrnRptDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnAiLrnRptList", dto);
    }

    public List<AiCmtResDto> selectEnRptLu(AiCmtCmRptLuDto dto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectEnRptLuList", dto);
    }

}
