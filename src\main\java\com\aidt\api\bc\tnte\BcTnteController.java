package com.aidt.api.bc.tnte;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.at.dto.User;
import com.aidt.api.at.user.UserDetailService;
import com.aidt.api.bc.tnte.dto.BcNewTnteDto;
import com.aidt.api.bc.tnte.dto.BcTnteDto;
import com.aidt.api.bc.tnte.stu.BcTnteStuService;
import com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto;
import com.aidt.api.sl.splrn.stu.SlSpLrnStuService;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbLluDto;
import com.aidt.api.tl.cmtxb.dto.TlCmTxbSrhDto;
import com.aidt.api.tl.cmtxb.stu.TlCmTxbStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-10 17:48:03
 * @modify date 2024-05-10 17:48:03
 * @desc 필기 공통 Controller
 */


@Slf4j
@Tag(name="[bc] 필기[BcTnte]", description="필기(공통)")
@RestController
@RequestMapping("/api/v1/bc/cm/tnte")
public class BcTnteController {
	
	@Autowired
	private BcTnteService bcTnteService;
	

    @Autowired
    private TlCmTxbStuService tlCmTxbStuService;
    

    @Autowired
    private UserDetailService userDetailService;
    

	@Autowired
	private SlSpLrnStuService slSpLrnStuService;
	
	

    @Autowired
    private BcTnteStuService bcTnteStuService;
    

    @Autowired
    private JwtProvider jwtProvider;

	/**
     * 필기 목록 조회 요청
     *
     * @param BcTnteDto
     * @return ResponseList<BcTnteDto>
     */
    @Operation(summary="필기 목록 조회", description="필기 목록을 조회한다.(공통)")
    @GetMapping(value = "/selectTnteList")
    public ResponseDto<List<BcTnteDto>> selectTnteList(BcTnteDto bcTnteDto) {
    	if (bcTnteDto == null || StringUtils.isBlank(bcTnteDto.getLrnUsrId())) {
    		return Response.fail("Params does not exist.");
    	}
        return Response.ok(bcTnteService.selectTnteList(bcTnteDto));
    }
    
    
    /**
     * 필기 목록 조회 요청
     *
     * @param BcTnteDto
     * @return ResponseList<BcTnteDto>
     */
    @Operation(summary="필기[BcTnte] ClassBoard 필기 목록 조회", description="필기 목록을 조회한다.(공통)")
    @GetMapping(value = "/selectTnteListByClassBoard")
    public ResponseDto<List<BcTnteDto>> selectTnteList(String lrnUsrId) {
    	if (lrnUsrId == null || StringUtils.isBlank(lrnUsrId)) {
    		return Response.fail("Params does not exist.");
    	}
    	

    	User.UserResponseDto userResponseDto = userDetailService.selectUserDetail(lrnUsrId);
    	if(userResponseDto==null) {
    		throw new RuntimeException("사용자가 존재 하지 않습니다.");
    	}
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(!userDetails.getOptTxbId().equals(userResponseDto.getOptTxbId())) {
    		throw new RuntimeException("접근 권한이 없는 학습자 입니다.");
    	}

    	BcTnteDto bcTnteDto = new BcTnteDto();
    	bcTnteDto.setLrnUsrId(lrnUsrId);
    	return Response.ok(bcTnteService.selectTnteList(bcTnteDto));
    }
    
 
   /**
    * 단원 정보 
    * @param lrnUsrId
    * @return
    */
    @Operation(summary="필기[BcTnte] ClassBoard 단원 정보 조회", description="차시 정보 조회")
    @GetMapping(value = "/selectTxbTcListByClassBoard")
    public ResponseDto<List<TlCmTxbLluDto>> selectTxbTcList(String lrnUsrId) {
        log.debug("ClassBoard Entrance selectTxbTcList");
        if (lrnUsrId == null || StringUtils.isBlank(lrnUsrId)) {
    		return Response.fail("Params does not exist.");
    	}
    	

    	User.UserResponseDto userResponseDto = userDetailService.selectUserDetail(lrnUsrId);
    	if(userResponseDto==null) {
    		throw new RuntimeException("사용자가 존재 하지 않습니다.");
    	}
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(!userDetails.getOptTxbId().equals(userResponseDto.getOptTxbId())) {
    		throw new RuntimeException("접근 권한이 없는 학습자 입니다.");
    	}
    	
    	TlCmTxbSrhDto srhDto = new TlCmTxbSrhDto();
        srhDto.setOptTxbId(userResponseDto.getOptTxbId());
        srhDto.setLrnUsrId(userResponseDto.getUsrId());
        srhDto.setUseYn("Y");
        
        List<TlCmTxbLluDto> list = tlCmTxbStuService.selectTxbLluList(srhDto);
        
        if(list==null) {
    		throw new RuntimeException("목차 정보가 없습니다.");
        	
        }
        List<TlCmTxbLluDto> selectList = new ArrayList<TlCmTxbLluDto>();
        for(TlCmTxbLluDto dto : list ) {
        	if("Y".equals(dto.getUseYn())
        			){
        		if("Y".equals(dto.getLuNoUseYn())) {
        			 dto.setLrmpNodNm(dto.getRcstnNo()+"."+ dto.getLrmpNodNm());
        		}
        		selectList.add(dto);
        		
        	}
        }
        
        
       
        return Response.ok(selectList);
    }

    
    
    /**
	 * 특별학습 목록리스트
	 * 
	 * @return result
	 */
	@Operation(summary="특별학습목록 - 목록리스트조회(학생)", description="특별학습 목록을 다건 조회한다.")
	@GetMapping("/selectSpLrnListByClassBoard")
	public ResponseDto<List<SlSpLrnMainViewDto>> selectSpLrnList(String lrnUsrId) {
		log.debug("Entrance selectSpLrnList");

		if (lrnUsrId == null || StringUtils.isBlank(lrnUsrId)) {
			return Response.fail("Params does not exist.");
		}

		User.UserResponseDto userResponseDto = userDetailService.selectUserDetail(lrnUsrId);
		if (userResponseDto == null) {
			throw new RuntimeException("사용자가 존재 하지 않습니다.");
		}

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		if (!userDetails.getOptTxbId().equals(userResponseDto.getOptTxbId())) {
			throw new RuntimeException("접근 권한이 없는 학습자 입니다.");
		}

		String usrTpCd = "TE";
		List<SlSpLrnMainViewDto> result = slSpLrnStuService.selectSpLrnList(userResponseDto.getOptTxbId(),
				userResponseDto.getUsrId(), usrTpCd);

		return Response.ok(result);
	}
	
	
	  
    // 2024-07-16 노트 목록 조회(개선안 버전)
    @Operation(summary="노트 목록 조회", description="노트 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectNewStuTnteListByClassroom")
    public ResponseDto<List<BcNewTnteDto>> selectNewStuTnteList(BcNewTnteDto bcNewTnteDto) {
    	/**
    	 *  pageNo: 0
			pageSize: 10
    	 */
    	if (bcNewTnteDto.getLrnUsrId() == null || StringUtils.isBlank(bcNewTnteDto.getLrnUsrId())) {
			return Response.fail("Params does not exist.");
		}
    	if (bcNewTnteDto.getLrnTpCd() == null || StringUtils.isBlank(bcNewTnteDto.getLrnTpCd())) {
    		return Response.fail("Params does not exist.");
    	}
    	
    	if (bcNewTnteDto.getSrhSrt() == null || StringUtils.isBlank(bcNewTnteDto.getSrhSrt())) {
    		return Response.fail("Params does not exist.");
    	}
    	
    	if(!"TL".equals(bcNewTnteDto.getLrnTpCd())
    			&& !"AI".equals(bcNewTnteDto.getLrnTpCd())
    			&& !"SL".equals(bcNewTnteDto.getLrnTpCd())
    			&& !"SE".equals(bcNewTnteDto.getLrnTpCd())
    			&& !"DE".equals(bcNewTnteDto.getLrnTpCd())
    			){
    		return Response.fail("Params does not exist.");
    	}

    	if(bcNewTnteDto.getPageSize()==0) {
    		bcNewTnteDto.setPageSize(10);
    	}
    	
		User.UserResponseDto userResponseDto = userDetailService.selectUserDetail(bcNewTnteDto.getLrnUsrId());
		if (userResponseDto == null) {
			throw new RuntimeException("사용자가 존재 하지 않습니다.");
		}

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		if (!userDetails.getOptTxbId().equals(userResponseDto.getOptTxbId())) {
			throw new RuntimeException("접근 권한이 없는 학습자 입니다.");
		}
		
    	bcNewTnteDto.setOptTxbId(userResponseDto.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userResponseDto.getUsrId());
//    	bcNewTnteDto.setSrhSrt("ins");
    	if(!"ins".equals(bcNewTnteDto.getSrhSrt()) && !"nod".equals(bcNewTnteDto.getSrhSrt())) {
        	bcNewTnteDto.setSrhSrt("ins");
    	}
        return Response.ok(bcTnteStuService.selectNewStuTnteList(bcNewTnteDto));
    }
    
    
    /**
     * 분류정보 
     * @param lrnUsrId
     * @return
     */
     @Operation(summary="필기[BcTnte] ClassBoard 분류정보", description="차시 정보 조회")
     @GetMapping(value = "/selectCodeListByClassBoard")
     public ResponseDto<List<KeyValuePair<String, String>>> selectCodeListByClassBoard(String lrnUsrId) {
         log.debug("ClassBoard Entrance selectTxbTcList");
         if (lrnUsrId == null || StringUtils.isBlank(lrnUsrId)) {
     		return Response.fail("Params does not exist.");
     	}
     	

     	User.UserResponseDto userResponseDto = userDetailService.selectUserDetail(lrnUsrId);
     	if(userResponseDto==null) {
     		throw new RuntimeException("사용자가 존재 하지 않습니다.");
     	}
     	
     	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
     	
     	if(!userDetails.getOptTxbId().equals(userResponseDto.getOptTxbId())) {
     		throw new RuntimeException("접근 권한이 없는 학습자 입니다.");
     	}
     	
     	// 과목 코드 조회
     	String sbjCd = bcTnteStuService.selectSbj(userResponseDto.getOptTxbId());
     	
        List<KeyValuePair<String, String>> keyValueList = new ArrayList<>();

        keyValueList.add(new KeyValuePair<>("TL", "우리 반 수업"));
        keyValueList.add(new KeyValuePair<>("AI", "AI 맞춤 학습"));
        keyValueList.add(new KeyValuePair<>("SL", "선생님 추천 학습"));
        keyValueList.add(new KeyValuePair<>("SE", "우리 반 평가"));
        if (sbjCd.equals("MA") || sbjCd.equals("CM1") || sbjCd.equals("CM2")) {
        	 keyValueList.add(new KeyValuePair<>("DE", "내가 만든 평가"));
     	}
       
         
         return Response.ok(keyValueList);
     }
    
}


class KeyValuePair<K, V> {
    private K key;
    private V value;

    public KeyValuePair(K key, V value) {
        this.key = key;
        this.value = value;
    }

    public K getKey() {
        return key;
    }

    public V getValue() {
        return value;
    }
}
