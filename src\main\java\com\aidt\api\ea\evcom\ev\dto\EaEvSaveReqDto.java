package com.aidt.api.ea.evcom.ev.dto;


import java.util.List;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 평가 관리 - 메인리스트 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvSaveReqDto {

	/* 평가 정보 */
	@Parameter(name="평가ID")
	private long evId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;
	@Parameter(name="교과서ID")
	private String txbId;
	@Parameter(name="학급ID")
	private String claId;
	
	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="다른학급적용 적용할 평가ID")
	private long otherEvId;	
	@Parameter(name="학급 평가ID")
	private long claEvId;	
	@Parameter(name="평가지ID")
	private long evshId;
	@Parameter(name="다른학급구분, test:응시설정/qtm:문제수정")
	private String otherClaType;
	@Parameter(name="다른학급운영교과서ID")
	private String otherOptTxbId;
	@Parameter(name="다른학급교과서ID")
	private String otherTxbId;
	@Parameter(name="다른학급학급ID")
	private String otherClaId;

	@Parameter(name="학생평가 가능여부")
	private String stuEvAbleYn;

	@Parameter(name="평가명", required = true)
	private String evNm;

	@Parameter(name="평가구분코드")
	private String evDvCd;

	@Parameter(name="평가상세구분코드")
	private String evDtlDvCd;

	@Parameter(name="응시기간설정여부")
	private String txmPtmeSetmYn;

	@Parameter(name="응시시작일시")
	private String txmStrDtm;

	@Parameter(name="응시종료일시")
	private String txmEndDtm;

	@Parameter(name="풀이시간설정여부")
	private String xplTmSetmYn;

	@Parameter(name="풀이시간초수(분 제외한 초수)")
	private int xplTmScnt;

	@Parameter(name="전체 출제 여부")
	private String allStxqYn;

	@Parameter(name="문제수", required = true)
	private int qstCnt;

	@Parameter(name="최종 문제수")
	private int fnlQstCnt;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="재응시허용여부")
	private String rtxmPmsnYn;

	@Parameter(name="사용여부")
	private String useYn;

	@Parameter(name="데이터베이스ID")
	private String dbId;

	/* 평가난이도 정보 */
	@Parameter(name="평가난이도 리스트")
	List<EaEvDffdCstnDto> dffdList;

	/* 시험범위 정보 */
	@Parameter(name="시험번위 리스트")
	List<EaEvTsRngeDto> tsRngeList;

	/* 문항 정보 */
	@Parameter(name="문항 리스트")
	List<EaEvQtmDto> qtmIdList;
	
	/* 문항 정보 */
	@Parameter(name="학생 리스트")
	List<EaEvSaveReqDto> stuList;
	
	/* 조회요청 문항리스트 */
	@Parameter(name="조회요청 문항리스트")
	List<EaEvDffdsQpQtmResDto> qtmSearchList;	
	
	/* 다른학급 리스트 */
	@Parameter(name="다른학급 리스트")
	List<EaEvSaveReqDto> otherClaList;		
}
