package com.aidt.api.sl.lrnwif.stu;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.lrnwif.dto.SlLrnwGetRequestDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwSaveRequestDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwTocAtvDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-05 08:24:20
 * @modify : date 2024-03-05 08:24:20
 * @desc : special student controller
 */
@Slf4j
@Tag(name="[sl] 특별학습 학습창연계(학생) [SlLrnwifStu]", description="학습창관련 연계데이터 처리(학생용)")
@RequestMapping("/api/v1/sl/lrnwif/stu")
@RestController
public class SlLrnwifStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private SlLrnwifStuService slSpLrnWifService;

	/**
	 * 특별학습 활동저장
	 * 
	 * @param slSpLrnAtvDto
	 * @return ResponseDto<Integer>
	 */
	@Operation(summary="특별학습 콘텐츠 활동저장(학생)", description="학습창에서 사용자의 학습활동상태및 학습시간을 저장한다.")
	@PostMapping(value = "/saveLrnCtnInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Integer> saveSpLrnAtv(
			@Parameter(name="특별학습 활동 저장 DTO", required = true) @RequestBody SlLrnwSaveRequestDto slLrnwSaveRequestDto) {
		log.debug("Entrance saveSpLrnAtv");
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		
		if(slLrnwSaveRequestDto.getSpLrnId() == null || slLrnwSaveRequestDto.getSpLrnCtnId() == null) {
			throw new IllegalArgumentException("[spLrnId, spLrnCtnId]는 필수 입력 값 입니다.");
		}

		return Response.ok(slSpLrnWifService.saveSpLrnAtv(slLrnwSaveRequestDto,
				securityUserDetailDto.getOptTxbId(), securityUserDetailDto.getUsrId(),
				securityUserDetailDto.getTxbId()));
	}

	/**
	 * 특별학습 활동 조회
	 * 
	 * @param spLrnId
	 * @param spLrnNodId
	 * @param spLrnCtnId
	 * @return SlSpLrnLastPrgsDto
	 */
	@Operation(summary="특별학습 콘텐츠 활동조회(학생)", description="학습창에서 특별학습 콘텐츠활동정보를 조회한다.")
	@PostMapping(value = "/selectLrnCtnInfo", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<SlLrnwTocAtvDto> selectLrnCtnInfo(@RequestBody SlLrnwGetRequestDto reqeuestDto,
			HttpServletRequest request) {
		log.debug("Entrance selectLrnCtnInfo");
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		SlLrnwTocAtvDto slLrnwTocAtvDto = slSpLrnWifService.selectLastSlSpLrnPgrs(
				reqeuestDto,
				securityUserDetailDto.getOptTxbId(), securityUserDetailDto.getUsrId(),
				securityUserDetailDto.getUsrTpCd());

		// SlLrnwNodDto slLrnwNodDto =
		// slSpLrnWifService.selectSplrnNodDto(slLrnwTocAtvDto);

		// // 마이홈 point지급처리
		// 
		// Map<String, Object> apiResult = this.regMyhmPnt(slLrnwNodDto, request);
		return Response.ok(slLrnwTocAtvDto);
	}

	// /**
	// * MyHome point 등록처리
	// * @param saveDto
	// * @param request
	// * @return
	// */
	// private Map<String, Object> regMyhmPnt(SlLrnwNodDto slLrnwNodDto,
	// HttpServletRequest request) {
	// Map<String, Object> apiResult = null;
	// String accessToken = jwtProvider.resolveToken(request,
	// ConstantsExt.accessTokenHeader);
	// apiResult = slSpLrnWifService.callMyhmApi(accessToken, Map.of(
	// "pntCd", SlConstUtil.MYHOME_PNT_CD_CL,
	// "pntChkBsVl", slLrnwNodDto.getTcNodId()));

	// return apiResult;
	// }
}
