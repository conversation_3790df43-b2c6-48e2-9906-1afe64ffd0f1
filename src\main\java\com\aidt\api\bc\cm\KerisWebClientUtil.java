package com.aidt.api.bc.cm;

import java.util.HashMap;
import java.util.Map;

import org.apache.poi.util.StringUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.DefaultUriBuilderFactory;

import com.aidt.api.bc.cm.dto.BcCmKerisBodyDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.netty.channel.ChannelOption;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
public class KerisWebClientUtil {

	private DefaultUriBuilderFactory defaultUriBuilderFactory = new DefaultUriBuilderFactory();

	private final int RETRY_COUNT = 2;

	private HttpClient httpClient = HttpClient.create().option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000); // 30초

	private final String KERIS_API_VERSION = "2.3";

	public String getKerisApiVersion() {
		return this.KERIS_API_VERSION;
	}

	private WebClient webClient() {
		defaultUriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.VALUES_ONLY); // get Uri 인코딩 설정
		return WebClient.builder().uriBuilderFactory(defaultUriBuilderFactory)
				.codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(2 * 1024 * 1024))
				.clientConnector(new ReactorClientHttpConnector(httpClient)).build();
	}

	/**
	 * 동기 
	 * 
	 * @param paramMap
	 * @param apiUrl
	 * @param partnerId
	 * @return
	 */
	protected BcCmKerisBodyDto executePostJsonRequest(Map<String, Object> paramMap, String apiUrl, String partnerId) {
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Partner-ID", partnerId);
		httpHeaders.add("API-version", KERIS_API_VERSION);
		httpHeaders.add("Content-Type", "application/json");

		log.debug("Try to call Keris API : " + apiUrl);

		try {
			String jsonString = new ObjectMapper().writeValueAsString(paramMap);

			return this.webClient().method(HttpMethod.POST).uri(apiUrl)
					.headers(headers -> headers.addAll(httpHeaders == null ? new HttpHeaders() : httpHeaders))
					.bodyValue(jsonString).retrieve()
					.onStatus(httpStatus -> httpStatus != HttpStatus.OK, clientResponse -> {
						log.warn("HTTP error occurred: Status code - " + clientResponse.statusCode());
						return Mono.error(new RuntimeException("HTTP Error: " + clientResponse.statusCode()));
					}).bodyToMono(BcCmKerisBodyDto.class).retry(RETRY_COUNT).onErrorResume(throwable -> {
						log.error("Error occurred while calling keris API: ", throwable);
						BcCmKerisBodyDto response = new BcCmKerisBodyDto();
						// 객체분리 후 메시지 설정
						response.setErrorMessage("apiUrl : " + apiUrl + " ### Error : " + throwable.getMessage());
						return Mono.just(response);
					}).block();

		} catch (JsonProcessingException e) {
			log.error("JSON Processing Exception while calling keris API: ", e);
			log.debug("Fail to call Keris API : " + apiUrl);
			return new BcCmKerisBodyDto();
		}
	}
	
	/**
	 * 비동기
	 * 
	 * @param paramMap
	 * @param apiUrl
	 * @param partnerId
	 * @return
	 */
	protected Mono<BcCmKerisBodyDto> executePostJsonRequestAsync(Map<String, Object> paramMap, String apiUrl, String partnerId) {
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Partner-ID", partnerId);
		httpHeaders.add("API-version", KERIS_API_VERSION);
		httpHeaders.add("Content-Type", "application/json");

		log.debug("Try to call Keris API : " + apiUrl);

		try {
			String jsonString = new ObjectMapper().writeValueAsString(paramMap);

			return this.webClient().method(HttpMethod.POST).uri(apiUrl)
					.headers(headers -> headers.addAll(httpHeaders == null ? new HttpHeaders() : httpHeaders))
					.bodyValue(jsonString).retrieve()
					.onStatus(httpStatus -> httpStatus != HttpStatus.OK, clientResponse -> {
						log.warn("HTTP error occurred: Status code - " + clientResponse.statusCode());
						return Mono.error(new RuntimeException("HTTP Error: " + clientResponse.statusCode()));
					}).bodyToMono(BcCmKerisBodyDto.class).retry(RETRY_COUNT).onErrorResume(throwable -> {
						log.error("Error occurred while calling keris API: ", throwable);
						BcCmKerisBodyDto response = new BcCmKerisBodyDto();
						// 객체분리 후 메시지 설정
						response.setErrorMessage("apiUrl : " + apiUrl + " ### Error : " + throwable.getMessage());
						return Mono.just(response);
					});

		} catch (JsonProcessingException e) {
			log.error("JSON Processing Exception while calling keris API: ", e);
			log.debug("Fail to call Keris API : " + apiUrl);

			return Mono.just(new BcCmKerisBodyDto());
		}
	}


	/**
	 * Keris Api result String 일때 Map 으로 변환 하여 return
	 *
	 * @param paramMap
	 * @param apiUrl
	 * @param partnerId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	protected Map<String, Object> executePostJsonRequestMapFromString(Map<String, Object> paramMap, String apiUrl,
																	  String partnerId) {
		Map<String, Object> result = null;
		String apiResult = executePostJsonRequestStringErrAllow(paramMap, apiUrl, partnerId);

		if (StringUtil.isNotBlank(apiResult)) {
			ObjectMapper objectMapper = new ObjectMapper();
			result = new HashMap<String, Object>();

			try {
				result = objectMapper.readValue(apiResult, Map.class);

				log.debug(result.toString());
			} catch (JsonProcessingException je) {
				log.error(je.getMessage());
			}
		}

		return result;
	}

	protected String executePostJsonRequestString(Map<String, Object> paramMap, String apiUrl, String partnerId) {
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Partner-ID", partnerId);
		httpHeaders.add("API-version", KERIS_API_VERSION);
		httpHeaders.add("Content-Type", "application/json");

		log.debug("Try to call Keris API : " + apiUrl);

		try {
			String jsonString = new ObjectMapper().writeValueAsString(paramMap);

			return this.webClient().method(HttpMethod.POST).uri(apiUrl)
					.headers(headers -> headers.addAll(httpHeaders == null ? new HttpHeaders() : httpHeaders))
					.bodyValue(jsonString).retrieve()
					.onStatus(httpStatus -> httpStatus != HttpStatus.OK, clientResponse -> {
						log.warn("HTTP error occurred: Status code - " + clientResponse.statusCode());
						return Mono.error(new RuntimeException("HTTP Error: " + clientResponse.statusCode()));
					}).bodyToMono(String.class).retry(RETRY_COUNT).onErrorResume(throwable -> {
						log.error("Error occurred while calling keris API: ", throwable);
						return Mono.just(null);
					}).block();

		} catch (JsonProcessingException e) {
			log.error("JSON Processing Exception while calling keris API: ", e);
			log.debug("Fail to call Keris API : " + apiUrl);
			return null;
		}
	}

	/**
	 * Keris 장애 발생 시에도 return 받을 수 있게 추가 생성
	 *
	 * @param paramMap
	 * @param apiUrl
	 * @param partnerId
	 * @return
	 */
	protected String executePostJsonRequestStringErrAllow(Map<String, Object> paramMap, String apiUrl,
														  String partnerId) {
		String result = null;

		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add("Partner-ID", partnerId);
		httpHeaders.add("API-version", KERIS_API_VERSION);
		httpHeaders.add("Content-Type", "application/json");

		log.debug("Try to call Keris API : " + apiUrl);

		try {
			String jsonString = new ObjectMapper().writeValueAsString(paramMap);

			Mono<String> value = this.webClient().method(HttpMethod.POST).uri(apiUrl)
					.headers(headers -> headers.addAll(httpHeaders == null ? new HttpHeaders() : httpHeaders))
					.bodyValue(jsonString).exchangeToMono(response -> {
						// 200, 400 시 keris에서 return 해주는 code가 있음.
						if (response.statusCode().equals(HttpStatus.OK)
								|| response.statusCode().equals(HttpStatus.BAD_REQUEST)) {
							return response.bodyToMono(String.class);
						}
						// 400 외 4xx ~ 5xx 장애 시 기본 형식에 맞춰 return
						else if (!response.statusCode().equals(HttpStatus.BAD_REQUEST)
								&& (response.statusCode().is4xxClientError()
								|| response.statusCode().is5xxServerError())) {
							return Mono.just("{\"code\":\"" + response.statusCode().value() + "\",\"message\":\""
									+ response.statusCode().name() + "\"}");
						} else {
							// return Mono.error(new RuntimeException("HTTP Error: " +
							// response.statusCode()));
							return Mono.just("{\"code\":\"" + response.statusCode().value() + "\",\"message\":\""
									+ response.statusCode().name() + "\"}");
						}
					});

			result = value.block();
		/* 모든 장애건에 대해 return 위해 최상위 Exception 으로 지정..
		} catch (JsonProcessingException e) {
			log.error("JSON Processing Exception while calling keris API: ", e);
			log.debug("Fail to call Keris API : " + apiUrl);
			return "{\"code\":\"ERR\",\"message\":\"try catch JsonProcessingException Error >>> \"" + e.getMessage() + "}";
		*/
		} catch (Exception e) {
			log.error("JSON Processing Exception while calling keris API: ", e);
			log.debug("Fail to call Keris API : " + apiUrl);
			return "{\"code\":\"ERR\",\"message\":\"webClient try catch Exception Error >>> \"" + e.getMessage() + "}";
		}

		return result;
	}
}
