package com.aidt.api.al.wrt.dto;

import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlWrtAcpReqDto {
	
	@Parameter(name="토픽ID")
	private String topic_id;
	
	@Parameter(name="첨삭내용")
	private String user_text;
	
	@Parameter(name="상세추가여부")
	private String error_details = "true";
	
	private Map<String, String> wrtInfo = new HashMap<String, String>();
	
}
