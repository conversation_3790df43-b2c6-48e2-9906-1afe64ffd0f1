package com.aidt.api.al.pl.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto;
import com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto;
import com.aidt.api.al.pl.dto.AlPlEaEvMainReportDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 16:42:32
 * @modify date 2024-05-21 16:42:32
 * @desc AI맞춤학습 진단 평가 리포트
 */
@Slf4j
@Service
public class AlPlStuReportService {

    private final String MAPPER_NAMESPACE = "api.al.pl.stu.report.";

    @Autowired
    private CommonDao commonDao;
    
    /**
	 * 특별학습 목록 조회
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return slSpLrnMainViewDtoList
	 */
	public AlPlEaEvMainReportDto selectAlEvCmStuRptList(String optTxbId, String usrId, AlPlEaEvMainReportDto alPlEaEvMainReportDto) {
		alPlEaEvMainReportDto.setUsrId(usrId);
		alPlEaEvMainReportDto.setOptTxbId(optTxbId);
		
		log.info("usrId : " + alPlEaEvMainReportDto.getUsrId());
		log.info("optTxbId : " + alPlEaEvMainReportDto.getOptTxbId());
		log.info("evId : " + alPlEaEvMainReportDto.getEvId());
		AlPlEaEvMainReportDto returnDto = commonDao.select(MAPPER_NAMESPACE + "selectAlEvCmStuRptList", alPlEaEvMainReportDto);
		log.info(returnDto.toString());
		
		if(returnDto.getCansRt().equals("100.00")) {	//100%일 경우 소스점 제외
			int intValue = (int) Double.parseDouble(returnDto.getCansRt()); // 소수점 이하를 버립니다
	        String result = String.valueOf(intValue);
	        log.debug(result); // 123
	        returnDto.setCansRt(result);
		}
		if(returnDto.getAvgCansRt() != null) {
			if(returnDto.getAvgCansRt().equals("100.00")) {
				int intValue = (int) Double.parseDouble(returnDto.getAvgCansRt()); // 소수점 이하를 버립니다
				String result = String.valueOf(intValue);
				log.debug(result); // 123
				returnDto.setAvgCansRt(result);
			}			
		}else {
			returnDto.setAvgCansRt("0");
		}
		
		log.debug(alPlEaEvMainReportDto.toString());
		List<AlPlEaEvComQtmAnwDto> qtmAnwDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAlPlEvQtmAnwList", alPlEaEvMainReportDto);
		returnDto.setQtmAnwList(qtmAnwDto);

        List<AlPlEaEvComQtmAnwDto> stpnWkpnDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAlPlEvRptStuStpnWkpnList", alPlEaEvMainReportDto);
        returnDto.setStpnWkpnStuList(stpnWkpnDto);
        
        //과목이 영어일때 차시별 정답률 필요.
    	if(AlConstUtil.SBJ_EN.contains(alPlEaEvMainReportDto.getSbjCd())) { 
        	List<AlPlEaEvComQtmAnwDto> tcStuRptListDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAlEvTcStuRptList", alPlEaEvMainReportDto);
            returnDto.setTcStuRptList(tcStuRptListDto);
        }
        
		return returnDto;
	}
	
	
	/**
	 * AI 학습단계별 문항,토픽 프로파일정보 조회(임시)
	 * */
	public List<AiRcmTsshQtmHistDto> selectEaEvQtmTpcProfInfo(AiRcmTsshQtmHistDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEaEvQtmTpcProfInfo", dto);
	}

}
