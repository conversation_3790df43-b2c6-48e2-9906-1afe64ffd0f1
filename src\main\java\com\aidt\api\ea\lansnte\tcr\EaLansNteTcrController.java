package com.aidt.api.ea.lansnte.tcr;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.evcom.dto.EaEvComQtmReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;
import com.aidt.api.ea.evcom.lansnte.dto.CreateQuesSimilarDiyDto;
import com.aidt.api.ea.evcom.lansnte.dto.EaLansNteReqDto;
import com.aidt.api.ea.evcom.lansnte.dto.EaLansNteResDto;
import com.aidt.api.ea.lansnte.stu.EaLansNteStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 오답 노트 - 교사", description="오답 노트 - 교사")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/lansnte")
public class EaLansNteTcrController {

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	EaLansNteStuService eaLansNteStuService;


	/**
	 * 선생	오답노트 단원 별 목록 조회 요청 NEW
	 *
	 * @param	dto
	 * @return ResponseList<Map<String, Object>>
	 */
	@Tag(name="[ea] 선생 오답 노트 단원 별 조회 New", description="단원 별 조회 New")
	@PostMapping(value = "/selectLansNteLuListNew")
	public ResponseDto<Map<String, Object>> selectLansNteLuListNew(@RequestBody EaLansNteReqDto dto) {
		log.debug("Entrance selectLansNteLuListNew");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());	 //운영 교과서 I
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		
		return Response.ok(eaLansNteStuService.selectLansNteLuListNew(dto));
	}

	/**
	 * 학생 오답노트 단원별 체크된 단원 문항리스트 조회 요청
	 *
	 * @param	dto
	 * @return ResponseList<Map<String, Object>>
	 */
	@Tag(name="[ea] 학생 오답노트 단원별 체크된 단원 문항리스트 조회 요청", description="학생 오답노트 단원별 체크된 단원 문항리스트 조회 요청")
	@PostMapping(value = "/selectIansQtmInfoList")
	public ResponseDto<List<Map<String, Object>>> selectIansQtmInfoList(@RequestBody EaEvQtmIdReqDto dto) {
		log.debug("Entrance selectIansQtmInfoList");
		
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());	 //운영 교과서 I
		
		return Response.ok(eaLansNteStuService.selectIansQtmInfoList(dto));
	}

	
	/**
	 * 선생	오답노트 시험지 별 목록 조회 요청
	 *
	 * @param	dto
	 * @return ResponseList<List<EaLansNteResDto>>
	 */
	@Tag(name="[ea] 선생 오답 노트 시험지 별 조회", description="시험지 별 조회")
	@PostMapping(value = "/selectLansNteTsPaperList")
	public ResponseDto<List<EaLansNteResDto>> selectLansNteTsPaperList(@RequestBody EaLansNteReqDto dto) {
		log.debug("Entrance selectLansNteTsPaperList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());	 //운영 교과서 ID
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		return Response.ok(eaLansNteStuService.selectLansNteTsPaperList(dto));
	}



	/**
	 * 선생	오답노트 - 시험지 별 오답 문제 풀기 - 오답 문항 조회
	 *
	 * @param	dto
	 * @return ResponseList<List<Map<String, Object>>>
	 */
	@Tag(name="[ea] 선생 오답 노트 시험지 별 오답 문항 조회", description="선생 오답 노트 시험지 별 오답 문항 조회")
	@PostMapping(value = "/selectLansNteTsPaperQpList")
	public ResponseDto<List<Map<String, Object>>> selectLansNteTsPaperQpList(@RequestBody CreateQuesSimilarDiyDto dto) {
		log.debug("Entrance selectLansNteTsPaperQpList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());	 //운영 교과서 ID
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		return Response.ok(eaLansNteStuService.selectLansNteTsPaperQpList(dto));
	}

	/**
	 * 선생	오답노트 - 오답 유사 문제 DIY 평가 생성
	 *
	 * @param	dto
	 * @return ResponseList<Map<String,String>>
	 */
	@Tag(name="[ea] 선생 오답 노트 시험지 별 오답 문항 조회", description="선생 오답 노트 시험지 별 오답 문항 조회")
	@PostMapping(value = "/createQuesSimilarDiy")
	public ResponseDto<EaEvQtmIdReqDto> createQuesSimilarDiy(@RequestBody EaEvQtmIdReqDto dto) {
		log.debug("Entrance createQuesSimilarDiy");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(userDetails.getOptTxbId());	 //운영 교과서 ID
		dto.setUsrId(userDetails.getUsrId());		 //사용자 ID
		dto.setDbId(userDetails.getTxbId());		//DB_ID
		dto.setTxbId(userDetails.getTxbId());
		
		return Response.ok(eaLansNteStuService.createQuesSimilarDiy(dto));
	}

    /**
     * 오답노트 단원별 오답유사 DIY평가
     *
     * @param eaEvComDto
     * @return ResponseDto<EaEvComQtmReqDto>
     */
    @Operation(summary="오답노트 단원별 오답유사 DIY평가 생성", description="오답노트 단원별 오답유사 DIY평가 생성")
    @PostMapping(value="/createEvSmlrLuQtmList")
    public ResponseDto<EaEvQtmIdReqDto> createEvSmlrLuQtmList(@RequestBody EaEvQtmIdReqDto reqDto) {
        log.debug("Entrance createEvSmlrLuQtmList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        
        if(reqDto.getUsrId() == null || reqDto.getUsrId().trim() == "")
        {
        	reqDto.setUsrId(userDetails.getUsrId()); // TODO 사용자ID
        }
        reqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
        reqDto.setDbId(userDetails.getTxbId()); // TODO DBID == 교과서ID
        reqDto.setTxbId(userDetails.getTxbId());
        
        return Response.ok(eaLansNteStuService.createEvSmlrLuQtmList(reqDto));
    }
    
	/**
	 * 오답노트 > 시험지별 > 대단원 리스트 요청
	 *
	 * @param param
	 * @return ResponseList<List<Map<String,Object>>>
	 */
	@Tag(name="[ea] 오답노트 시험지별 대단원 리스트 조회", description="[ea] 오답노트 시험지별 대단원 리스트 조회")
	@PostMapping(value = "/selectWrongAnwBestLluList")
	public ResponseDto<List<Map<String,Object>>> selectWrongAnwBestLluList(@RequestBody EaLansNteReqDto param) {
		log.debug("Entrance selectWrongAnwBestLluList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		return Response.ok(eaLansNteStuService.selectWrongAnwBestLluList(param));
	}    
    
	/**
	 * 오답노트 > 시험지별 > 오답 BEST 조회 요청
	 *
	 * @param param
	 * @return ResponseList<List<Map<String,Object>>>
	 */
	@Tag(name="[ea] 오답노트 시험지별 오답 BEST 학생리스트 조회", description="[ea] 오답노트 시험지별 오답 BEST 학생리스트 조회")
	@PostMapping(value = "/selectWrongAnwBestStuList")
	public ResponseDto<List<Map<String,Object>>> selectWrongAnwBestStuList(@RequestBody EaLansNteReqDto param) {
		log.debug("Entrance selectWrongAnwBestStuList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		return Response.ok(eaLansNteStuService.selectWrongAnwBestStuList(param));
	}
}
