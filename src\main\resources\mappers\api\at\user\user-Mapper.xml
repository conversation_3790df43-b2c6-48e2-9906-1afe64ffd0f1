<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aidt.api.at.mapper.UserMapper">

	<select id="selectUserDetail" resultType="com.aidt.api.at.dto.User$UserResponseDto">
		SELECT	  A.USR_ID
				, A.KERIS_USR_ID
				, A.OPT_TXB_ID
				, A.TXB_ID
				, A.CLA_ID
				, B.USR_NM
				, B.USR_TP_CD
				, CASE B.USR_TP_CD
					WHEN 'ST' THEN 'stu'
					WHEN 'TE' THEN 'tcr'
					WHEN 'PA' THEN 'prt'
					WHEN 'AD' THEN 'mgr'
	        		ELSE ''
        		END AS "ROLE"
		 FROM LMS_LRM.CM_TOKEN A, LMS_LRM.CM_USR B
		WHERE A.USR_ID = B.USR_ID
		  AND A.USR_ID = #{usrId}
	</select>
	
	<select id="selectTokenUsrId" parameterType="com.aidt.api.at.user.dto.UserCreateDto" resultType="String">
		SELECT
			t.usr_id
		FROM
			lms_lrm.cm_token t
		WHERE
			t.keris_usr_id = #{kerisUsrId}
		AND t.opt_txb_id = #{optTxbId}
	</select>
	
	<select id="selectUsrCheckCnt" parameterType="String" resultType="int">
		SELECT
			COUNT(u.usr_id) AS cnt
		FROM
			lms_lrm.cm_usr u
		WHERE
			u.usr_id = #{usrId}
	</select>
	
	<insert id="insertUsr" parameterType="com.aidt.api.at.user.dto.UsrDto">
		INSERT INTO LMS_LRM.CM_USR
		(
			 usr_id
			,keris_usr_id
			,usr_tp_cd
			,fst_reg_dtm
			,usr_st_cd
			,cla_id
			,fln_st_cd
			,ntr_yn
			,keris_term_agr_yn
			,keris_term_agr_dt
			,lrnr_vel_tp_cd
			,crtr_id
			,crt_dtm
			,mdfr_id
			,mdf_dtm, db_id
		) 
		VALUES 
		(
			 #{usrId}
			,#{kerisUsrId}
			,#{usrTpCd}
			,NOW()			
			,#{usrStCd}
			,#{claId}
			,#{flnStCd}
			,#{ntrYn}
			,#{kerisTermAgrYn}
			,#{kerisTermAgrDt}
			,#{lrnrVelTpCd}
			,#{crtrId}
			,NOW()
			,#{mdfrId}
			,NOW()
			,#{dbId}
		)
	</insert>
	
	<select id="selectUsrUsrId" parameterType="com.aidt.api.at.user.dto.UserCreateDto" resultType="String">
		SELECT
			u.usr_id
		FROM
			lms_lrm.cm_usr u
		WHERE
			u.keris_usr_id = #{kerisUsrId}
		AND u.cla_id = #{claId}
	</select>
	
	<insert id="insertToken" parameterType="com.aidt.api.at.user.dto.TokenDto">
		INSERT INTO LMS_LRM.CM_TOKEN
		(
			 usr_id
			,keris_usr_id
			,login_id
			,pwd
			,opt_txb_id
			,txb_id
			,cla_id
		) 
		VALUES 
		(
			 #{usrId}
			,#{kerisUsrId}
			,#{loginId}
			,#{pwd}
			,#{optTxbId}
			,#{txbId}
			,#{claId}
		)
	</insert>
	
	<insert id="insertTokenMakeUsrId" parameterType="com.aidt.api.at.user.dto.TokenDto">
		<selectKey keyProperty="usrId" order="BEFORE" resultType="String">
			SELECT CONCAT(DATE_FORMAT(now(6), '%Y%m%d%H%i%s%f'), '-', SUBSTR(replace(UUID(), '-', ''), 1, 15));
		</selectKey>
		INSERT INTO LMS_LRM.CM_TOKEN
		(
			 usr_id
			,keris_usr_id
			,login_id
			,pwd
			,opt_txb_id
			,txb_id
			,cla_id
		) 
		VALUES 
		(
			 #{usrId}
			,#{kerisUsrId}
			,#{loginId}
			,#{pwd}
			,#{optTxbId}
			,#{txbId}
			,#{claId}
		)
	</insert>
	
	<update id="updateUsrKerisTermAgrYn" parameterType="com.aidt.api.at.user.dto.UsrDto">
		UPDATE
			lms_lrm.cm_usr
		SET
			 keris_term_agr_yn = #{kerisTermAgrYn}
		    ,keris_term_agr_dt = #{kerisTermAgrDt}
		WHERE
			usr_id = #{usrId}
	</update>
	
	<select id="selectTokenLoginIdConfirm" parameterType="String" resultType="String">
		SELECT
			t.usr_id
		FROM
			lms_lrm.cm_token t
		WHERE
			t.login_id = #{loginId}
	</select>

</mapper>