package com.aidt.api.tl.oneclksetm.tcr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlPlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlTocDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmExtcmpTxbDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmFncUseDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSbcLrnDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 16:07:44
 * @modify date 2024-02-15 16:07:44
 * @desc [TlOneClkSetmTcr Controller 원클릭학습설정]
 */
@Slf4j
@Tag(name="[tl] 원클릭학습설정[TlOneClkSetmTcr]", description="원클릭학습설정")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/tcr/oneclksetm")
public class TlOneClkSetmTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlOneClkSetmTcrService tlOneClkSetmTcrService;

    /**
     * 원클릭학습설정 학습목차 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmTocDto>>
     */
    @Operation(summary="원클릭학습설정 학습목차 조회", description="원클릭학습설정 학습목차 조회")
    @PostMapping(value = "/selectLrnTocList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmTocDto>> selectLrnTocList() {
        log.debug("Entrance selectLrnTocList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        return Response.ok(tlOneClkSetmTcrService.selectLrnTocList(srhDto));
    }

    /**
     * 원클릭학습설정 학습활동 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmAtvDto>>
     */
    @Operation(summary="원클릭학습설정 학습활동 조회", description="원클릭학습설정 학습활동 조회")
    @PostMapping(value = "/selectLrnAtvList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmAtvDto>> selectLrnAtvList(@RequestBody TlOneClkSetmSrhDto srhDto) {
        log.debug("Entrance selectLrnAtvList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectLrnAtvList(srhDto));
    }

    /**
     * 원클릭학습설정 Ai맞춤학습 목차 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmAtvDto>>
     */
    @Operation(summary="원클릭학습설정 Ai맞춤학습 목차 조회", description="원클릭학습설정 Ai맞춤학습 목차 조회")
    @PostMapping(value = "/selectAlTocList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmAlTocDto>> selectAlTocList() {
        log.debug("Entrance selectAlTocList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();

        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectAlTocList(srhDto));
    }

    /**
     * 원클릭학습설정 학급 리스트 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmClaDto>>
     */
    @Operation(summary="원클릭학습설정 학급 리스트 조회", description="원클릭학습설정 학급 리스트 조회")
    @PostMapping(value = "/selectClaList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmClaDto>> selectClaList(@RequestBody TlOneClkSetmSrhDto srhDto) {
        log.debug("Entrance selectClaList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        srhDto.setTcrUsrId(userDetails.getKerisUsrId());
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setTxbId(userDetails.getTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectClaList(srhDto));
    }

    /**
     * 원클릭학습설정 교과학습 재구성 저장
     * 
     * @param sbcLrnDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 교과학습 재구성 저장, 다른 학급 저장", description="원클릭학습설정 교과학습 재구성 저장, 다른 학급 저장")
    @PostMapping(value = "/updateSbclrnRcstn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateSbclrnRcstn(@RequestBody TlOneClkSetmSbcLrnDto sbcLrnDto) {
        log.debug("Entrance updateSbclrnRcstn");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String mdfrId = userDetails.getUsrId();
        String kerisUsrId= userDetails.getKerisUsrId();
        String orgnOptTxbId = userDetails.getOptTxbId();
        String dbId = (userDetails.getTxbId());
        return Response.ok(tlOneClkSetmTcrService.updateSbclrnRcstn(sbcLrnDto, mdfrId, kerisUsrId, orgnOptTxbId, dbId));
    }

    /**
     * 원클릭학습설정 Ai맞춤학습 재구성 저장
     * 
     * @param sbcLrnDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 Ai맞춤학습 재구성 저장, 다른 학급 저장", description="원클릭학습설정 Ai맞춤학습 재구성 저장, 다른 학급 저장")
    @PostMapping(value = "/updateAlPlRcstn", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateAlPlRcstn(@RequestBody TlOneClkSetmAlPlDto alPlDto) {
        log.debug("Entrance updateAlPlRcstn");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String mdfrId = userDetails.getUsrId();
        String orgnOptTxbId = userDetails.getOptTxbId();
        return Response.ok(tlOneClkSetmTcrService.updateAlPlRcstn(alPlDto, mdfrId, orgnOptTxbId));
    }

    /**
     * 원클릭학습설정 학습일정관리 저장
     * 
     * @param sbcLrnDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 학습일정관리 저장", description="원클릭학습설정 학습일정관리 저장")
    @PostMapping(value = "/updateLrnScdlMg", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateLrnScdlMg(@RequestBody TlOneClkSetmSbcLrnDto sbcLrnDto) {
        log.debug("Entrance updateLrnScdlMg");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String mdfrId = userDetails.getUsrId();
        String orgnOptTxbId = userDetails.getOptTxbId();
        String kerisUsrId= userDetails.getKerisUsrId();
        return Response.ok(tlOneClkSetmTcrService.updateLrnScdlMg(sbcLrnDto, orgnOptTxbId, mdfrId, kerisUsrId));
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 목차 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmRcmCtnDto>>
     */
    @Operation(summary="원클릭학습설정 추천콘텐츠 재구성 목차 조회", description="원클릭학습설정 추천콘텐츠 재구성 목차 조회")
    @PostMapping(value = "/selectRcmCtnList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmRcmCtnDto>> selectRcmCtnList() {
        log.debug("Entrance selectRcmCtnList");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();

        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setClaId(userDetails.getClaId());
        return Response.ok(tlOneClkSetmTcrService.selectRcmCtnList(srhDto));
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 학생추천리스트 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmRcmCtnDtlDto>>
     */
    @Operation(summary="원클릭학습설정 추천콘텐츠 재구성 학생추천리스트 조회", description="원클릭학습설정 추천콘텐츠 재구성 학생추천리스트 조회")
    @PostMapping(value = "/selectRcmCtnDtlList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmRcmCtnDtlDto>> selectRcmCtnDtlList(@RequestBody TlOneClkSetmSrhDto srhDto) {
        log.debug("Entrance selectRcmCtnDtlList");
        log.debug(srhDto.getSpLrnId());
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setClaId(userDetails.getClaId());
        return Response.ok(tlOneClkSetmTcrService.selectRcmCtnDtlList(srhDto));
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 우리반 일괄 저장
     * 
     * @param srhDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 추천콘텐츠 재구성 우리반 일괄 저장", description="원클릭학습설정 추천콘텐츠 재구성 우리반 일괄 저장")
    @PostMapping(value = "/updateRcmCtnCla", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateRcmCtnCla(@RequestBody TlOneClkSetmSrhDto srhDto) {
        log.debug("Entrance updateRcmCtnCla");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        srhDto.setClaId(userDetails.getClaId());

        return Response.ok(tlOneClkSetmTcrService.updateRcmCtnCla(srhDto, userDetails.getOptTxbId(),
                userDetails.getTxbId(), userDetails.getUsrId()));
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 학생추천정보 저장
     * 
     * @param srhDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 추천콘텐츠 재구성 학생추천정보 저장", description="원클릭학습설정 추천콘텐츠 재구성 학생추천정보 저장")
    @PostMapping(value = "/updateRcmCtnInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateRcmCtnInfo(@RequestBody TlOneClkSetmSrhDto srhDto) {
        log.debug("Entrance updateRcmCtnInfo");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        List<TlOneClkSetmRcmCtnDtlDto> dtlList = srhDto.getDtlList();

        return Response.ok(tlOneClkSetmTcrService.updateRcmCtnInfo(dtlList, userDetails.getOptTxbId(),
                userDetails.getTxbId(), userDetails.getUsrId()));
    }

    /**
     * 원클릭학습설정 기능사용설정 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmFncUseDto>>
     */
    @Operation(summary="원클릭학습설정 기능사용설정 조회", description="원클릭학습설정 기능사용설정 조회")
    @PostMapping(value = "/selectFncUseSetm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlOneClkSetmFncUseDto> selectFncUseSetm() {
        log.debug("Entrance selectFncUseSetm");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectFncUseSetm(srhDto));
    }

    /**
     * 원클릭학습설정 기능사용설정 수정
     * 
     * @param srhDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 기능사용설정 수정", description="원클릭학습설정 기능사용설정 수정")
    @PostMapping(value = "/updateFncUseSetm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateFncUseSetm(@RequestBody TlOneClkSetmFncUseDto fncDto) {
        log.debug("Entrance updateFncUseSetm");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        String orgnOptTxbId = userDetails.getOptTxbId();
        fncDto.setOptTxbId(userDetails.getOptTxbId());
        fncDto.setDbId(userDetails.getTxbId());
        fncDto.setMdfrId(userDetails.getUsrId());
        return Response.ok(tlOneClkSetmTcrService.updateFncUseSetm(fncDto, orgnOptTxbId ,userDetails.getKerisUsrId()));
    }
    
    /**
     * 원클릭학습설정 내 자료 등록
     * 
     * @param mtrlDto
     * @return int
     */
    @Operation(summary="원클릭학습설정 내 자료 등록", description="원클릭학습설정 내 자료 등록")
    @PostMapping(value = "/insertRegMtrl",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLsnMtrlDto> insertRegMtrl(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
        log.debug("Entrance insertRegMtrl");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        mtrlDto.setOptTxbId(userDetails.getOptTxbId());
        mtrlDto.setCrtrId(userDetails.getUsrId());
        mtrlDto.setMdfrId(userDetails.getUsrId());
        mtrlDto.setDbId(userDetails.getTxbId());

        return Response.ok(tlOneClkSetmTcrService.insertRegMtrl(mtrlDto));
    }
    
    /**
     * 원클릭학습설정 자료 삭제
     * 
     * @param srhDto
     * @return int
     */
    @Operation(summary="원클릭학습설정 자료 삭제", description="원클릭학습설정 자료 삭제")
    @PostMapping(value = "/deleteLsnMtrl",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> deleteLsnMtrl(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
        log.debug("Entrance deleteLsnMtrl");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        mtrlDto.setOptTxbId(userDetails.getOptTxbId());
        mtrlDto.setCrtrId(userDetails.getUsrId());
        mtrlDto.setMdfrId(userDetails.getUsrId());
        mtrlDto.setDbId(userDetails.getTxbId());

        return Response.ok(tlOneClkSetmTcrService.deleteLsnMtrl(mtrlDto));
    }
    
    
    /**
     * 원클릭학습설정 내 자료 게시물 등록
     * 
     * @param mtrlDto
     * @return int
     */
    @Operation(summary="원클릭학습설정 내 자료 게시물 등록", description="원클릭학습설정 내 자료 게시물 등록")
    @PostMapping(value = "/insertBlwr",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLsnMtrlDto> insertBlwr(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
        log.debug("Entrance insertBlwr");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        mtrlDto.setOptTxbId(userDetails.getOptTxbId());
        mtrlDto.setCrtrId(userDetails.getUsrId());
        mtrlDto.setMdfrId(userDetails.getUsrId());
        mtrlDto.setDbId(userDetails.getTxbId());
        
        return Response.ok(tlOneClkSetmTcrService.insertBlwr(mtrlDto));
    }
    
//    /**
//     * 원클릭학습설정 내 자료 게시물 수정
//     * 
//     * @param mtrlDto
//     * @return int
//     */
//    @Operation(summary="원클릭학습설정 내 자료 게시물 수정", description="원클릭학습설정 내 자료 게시물 수정")
//    @PostMapping(value = "/updateBlwr",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<Integer> updateBlwr(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
//        log.debug("Entrance updateBlwr");
//
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//        mtrlDto.setOptTxbId(userDetails.getOptTxbId());
//        mtrlDto.setMdfrId(userDetails.getUsrId());
//        mtrlDto.setDbId(userDetails.getTxbId());
//        
//        return Response.ok(tlOneClkSetmTcrService.updateBlwr(mtrlDto));
//    }
    
    /**
     * 원클릭학습설정 자료 게시물 상세 조회
     * 
     * @param mtrlDto
     * @return ResponseDto<List<TlOneClkSetmRegMtrlDto>>
     */
    @Operation(summary="원클릭학습설정 자료 게시물 상세 조회", description="원클릭학습설정 자료 게시물 상세 조회")
    @PostMapping(value = "/selectBlwrInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlOneClkSetmRegMtrlDto> selectBlwrInfo(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
        log.debug("Entrance selectBlwrInfo");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        mtrlDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectBlwrInfo(mtrlDto));
    }
    
    /**
     * 원클릭학습설정 선생님 콘텐츠 등록
     * 
     * @param mtrlDto
     * @return int
     */
    @Operation(summary="원클릭학습설정 선생님 콘텐츠 등록", description="원클릭학습설정 선생님 콘텐츠 등록")
    @PostMapping(value = "/insertTcrCtnReg",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlOneClkSetmAtvDto> insertTcrCtnReg(@RequestBody TlOneClkSetmRegMtrlDto mtrlDto) {
        log.debug("Entrance insertTcrCtnReg");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        mtrlDto.setOptTxbId(userDetails.getOptTxbId());
        mtrlDto.setCrtrId(userDetails.getUsrId());
        mtrlDto.setMdfrId(userDetails.getUsrId());
        mtrlDto.setDbId(userDetails.getTxbId());

        return Response.ok(tlOneClkSetmTcrService.insertTcrCtnReg(mtrlDto));
    }
    
    /**
     * 원클릭학습설정 콘텐츠 복사
     * 
     * @param mtrlDto
     * @return int
     */
    @Operation(summary="원클릭학습설정 콘텐츠 복사", description="원클릭학습설정 콘텐츠 복사")
    @PostMapping(value = "/insertTcrAtvReg",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmAtvDto>> insertTcrAtvReg(@RequestBody List<TlOneClkSetmAtvDto> atvList) {
        log.debug("Entrance insertTcrAtvReg");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        return Response.ok(tlOneClkSetmTcrService.insertTcrAtvReg(atvList, userDetails.getOptTxbId(), userDetails.getUsrId(), userDetails.getTxbId()));
    }
    
    /**
     * 원클릭학습설정 타사교과서목록 조회
     * 
     * @param mtrlDto
     * @return ResponseDto<List<TlOneClkSetmRegMtrlDto>>
     */
    @Operation(summary="원클릭학습설정 타사교과서목록 조회", description="원클릭학습설정 타사교과서목록 조회")
    @PostMapping(value = "/selectExtcmpTxbList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlOneClkSetmExtcmpTxbDto>> selectExtcmpTxbList() {
        log.debug("Entrance selectExtcmpTxbList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        return Response.ok(tlOneClkSetmTcrService.selectExtcmpTxbList(userDetails.getOptTxbId()));
    }
    
    /**
     * 원클릭학습설정 타사교과서 사용 체크
     * 
     * @return ResponseDto<String>
     */
    @Operation(summary="원클릭학습설정 타사교과서 사용 체크", description="원클릭학습설정 타사교과서 사용 체크")
    @PostMapping(value = "/selectExtcmpTxbChk", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<String> selectExtcmpTxbChk() {
        log.debug("Entrance selectExtcmpTxbChk");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        return Response.ok(tlOneClkSetmTcrService.selectExtcmpTxbChk(userDetails.getOptTxbId()));
    }
    
    /**
     * 원클릭학습설정 외부활동설정 조회
     * 
     * @param srhDto
     * @return ResponseDto<List<TlOneClkSetmFncUseDto>>
     */
    @Operation(summary="원클릭학습설정 외부활동설정 조회", description="원클릭학습설정 외부활동설정 조회")
    @PostMapping(value = "/selectExtAtvSetm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLrnwExtAtvSetm> selectExtAtvSetm() {
        log.debug("Entrance selectExtAtvSetm");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(tlOneClkSetmTcrService.selectExtAtvSetm(srhDto));
    }
    
    /**
     * 원클릭학습설정 외부활동설정 수정
     * 
     * @param srhDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 기능사용설정 수정", description="원클릭학습설정 기능사용설정 수정")
    @PostMapping(value = "/updateExtAtvSetm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> updateExtAtvSetm(@RequestBody TlLrnwExtAtvSetm extDto) {
        log.debug("Entrance updateExtAtvSetm");

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        String orgnOptTxbId = userDetails.getOptTxbId();
        extDto.setOptTxbId(userDetails.getOptTxbId());
        extDto.setDbId(userDetails.getTxbId());
        extDto.setMdfrId(userDetails.getUsrId());
        return Response.ok(tlOneClkSetmTcrService.updateExtAtvSetm(extDto, orgnOptTxbId, userDetails.getKerisUsrId()));
    }
    
    /**
     * 원클릭학습설정 전체 학생수 조회
     * 
     * @param srhDto
     * @return ResponseDto<Integer>
     */
    @Operation(summary="원클릭학습설정 전체 학생수 조회", description="원클릭학습설정 전체 학생수 조회")
    @PostMapping(value = "/selectTotStuCnt", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> selectTotStuCnt() {
    	log.debug("Entrance selectTotStuCnt");
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	String orgnOptTxbId = userDetails.getOptTxbId();

    	return Response.ok(tlOneClkSetmTcrService.selectTotStuCnt(orgnOptTxbId));
    }
}
