<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.grpmgmt.tcr">

<select id="selectStuList" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto" resultType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtStuListDto">
    SELECT      USR_NM, USR_ID, STU_NO, USR_GND_CD, C.CM_CD_NM USR_GND_NM,
                LRNR_VEL_TP_CD, D.CM_CD_NM LRNR_VEL_TP_NM
    FROM        LMS_LRM.CM_USR A
    INNER JOIN  LMS_LRM.CM_OPT_TXB B
        ON A.CLA_ID = B.CLA_ID
    LEFT JOIN LMS_LRM.CM_CM_CD C
        ON C.URNK_CM_CD = 'USR_GND_CD'
        AND C.CM_CD     = A.USR_GND_CD
    LEFT JOIN LMS_LRM.CM_CM_CD D
        ON D.URNK_CM_CD = 'LRNR_VEL_TP_CD'
        AND D.CM_CD     = A.LRNR_VEL_TP_CD
    WHERE   B.OPT_TXB_ID    = #{optTxbId}   /* 운영교과서ID */
        AND A.USR_TP_CD     = 'ST'          /* 유저 타입 */
    ORDER BY STU_NO
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - selectStuList */
</select>

<select id="selectGrpNmAllList" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto" resultType="hashMap">
	SELECT 	GRP_ID, GRP_NM
	FROM 	LMS_LRM.EA_GRP A
	WHERE   A.OPT_TXB_ID  = #{optTxbId}
		AND A.DEL_YN      = 'N'
</select>

<select id="selectGroupList" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto" resultType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpDto">

	SELECT  A.GRP_ID, A.GRP_NM, A.TEM_CNT, A.CRT_DTM, A.MDF_DTM, 
			A.ASN_NM_ING, A.ASN_NM_END, A.ASN_NM_OT_Y, A.ASN_NM_OT_N,
            A.ASN_NM_ING_CNT, A.ASN_NM_END_CNT, A.ASN_NM_OT_Y_CNT, A.ASN_NM_OT_N_CNT,
            A.GRP_GRU_AUTO_CRT_YN, A.GRP_TMGR_USE_YN, A.TOTAL_CNT, A.SMT_CMPL_CNT,
            A.GRP_TEM_ID, A.GRP_TEM_NM, C.STU_USR_ID USR_ID, C.USR_NM, C.GRP_TMGR_YN, 
            C.LRNR_VEL_TP_CD, D.CM_CD_NM AS LRNR_VEL_TP_NM
	FROM
	(
	    SELECT      A.GRP_ID, A.GRP_NM, A.TEM_CNT, A.CRT_DTM, A.MDF_DTM, A.GRP_GRU_AUTO_CRT_YN, A.GRP_TMGR_USE_YN, 
	    			AA.GRP_TEM_ID, AA.GRP_TEM_NM, CNT.TOTAL_CNT,
                    GROUP_CONCAT(CONCAT(IFNULL(CONCAT('[',CC.CM_CD_NM, ']'), ''), '', C.ASN_NM)) ASN_NM_ING,
                    GROUP_CONCAT(CONCAT(IFNULL(CONCAT('[',DD.CM_CD_NM, ']'), ''), '', D.ASN_NM)) ASN_NM_END,
                    GROUP_CONCAT(CONCAT(IFNULL(CONCAT('[',FF.CM_CD_NM, ']'), ''), '', F.ASN_NM)) ASN_NM_OT_Y,
                    GROUP_CONCAT(CONCAT(IFNULL(CONCAT('[',GG.CM_CD_NM, ']'), ''), '', G.ASN_NM)) ASN_NM_OT_N,
                    COUNT(C.ASN_NM) AS ASN_NM_ING_CNT, 
	                COUNT(D.ASN_NM) AS ASN_NM_END_CNT,
	                COUNT(F.ASN_NM) AS ASN_NM_OT_Y_CNT,
	                COUNT(G.ASN_NM) AS ASN_NM_OT_N_CNT,
	                COUNT(SMT.CNT) AS SMT_CMPL_CNT 
		FROM       
		(
			<!-- 모둠을 page count 만큼만 가져온다 -->
			SELECT      A.GRP_ID, A.GRP_NM, A.TEM_CNT, A.CRT_DTM, A.MDF_DTM, A.GRP_GRU_AUTO_CRT_YN,
						A.GRP_TMGR_USE_YN, A.OPT_TXB_ID, A.DEL_YN
			From LMS_LRM.EA_GRP A
			WHERE   A.OPT_TXB_ID  = #{optTxbId}
				AND A.DEL_YN      = 'N'
			ORDER BY A.MDF_DTM DESC
			LIMIT	#{pageSize}
			OFFSET 	#{startRow}
		) A
		INNER JOIN 
		(
			<!-- 모둠 전체 수 -->
			SELECT COUNT(GRP_ID) TOTAL_CNT, OPT_TXB_ID
			From LMS_LRM.EA_GRP A
			WHERE   A.OPT_TXB_ID  = #{optTxbId}
				AND A.DEL_YN      = 'N'
			GROUP BY OPT_TXB_ID
		) CNT
			ON A.OPT_TXB_ID = CNT.OPT_TXB_ID
		LEFT JOIN 	LMS_LRM.EA_GRP_TEM AA
			ON A.GRP_ID = AA.GRP_ID
		<!-- 모둠에 할당된 과제 -->
        LEFT JOIN   (   SELECT 	DISTINCT B.GRP_ID, B.ASN_ID 
                        FROM 	LMS_LRM.EA_GRP A 
                        INNER JOIN LMS_LRM.EA_GRP_ASN_SMT B
                        	ON A.GRP_ID = B.GRP_ID
                        WHERE   A.OPT_TXB_ID  = #{optTxbId}
							AND A.DEL_YN      = 'N'
                        GROUP BY GRP_ID, ASN_ID
                    ) B
	        ON A.GRP_ID = B.GRP_ID
	    <!-- 모둠에 할당된 과제중에 학생이 제출한 과제가 있는지 체크 -->
	    LEFT JOIN (		SELECT 	B.ASN_ID, COUNT(B.STU_USR_ID) CNT 
	    				FROM 	LMS_LRM.EA_ASN A
	    				INNER JOIN LMS_LRM.EA_ASN_SMT B
	    					ON A.ASN_ID = B.ASN_ID
	    				WHERE	A.OPT_TXB_ID 	= #{optTxbId}
		    				AND B.SMT_CMPL_YN 	= 'Y' 
		    				AND A.DEL_YN 		= 'N'
	    				GROUP BY ASN_ID
	    		  ) SMT 
	    	ON B.ASN_ID = SMT.ASN_ID
	    <!-- 모둠에 할당된 과제중에 종료된 기간 과제 -->
	    LEFT JOIN   LMS_LRM.EA_ASN C
	        ON B.ASN_ID = C.ASN_ID
	        AND C.END_DTM <![CDATA[>]]> NOW()
	        AND C.ASN_PTME_DV_CD = 'PT'
        LEFT JOIN LMS_LRM.CM_CM_CD CC
            ON CC.URNK_CM_CD  	= 'LRN_TP_CD' 
            AND C.LRN_TP_CD		=  CC.CM_CD
        <!-- 모둠에 할당된 과제중에 진행중인 기간 과제 -->
		LEFT JOIN   LMS_LRM.EA_ASN D
	        ON B.ASN_ID = D.ASN_ID
	        AND D.END_DTM <![CDATA[<]]> NOW()
	        AND D.ASN_PTME_DV_CD = 'PT'
        LEFT JOIN LMS_LRM.CM_CM_CD DD
            ON DD.URNK_CM_CD  	= 'LRN_TP_CD' 
            AND D.LRN_TP_CD		=  DD.CM_CD
  		LEFT JOIN 
		(	
			SELECT	A.ASN_ID, A.ASN_NM, A.LRN_TP_CD, IFNULL(COUNT(B.STU_USR_ID), 0) SMT_CMPL_ALL_CNT
			FROM 	LMS_LRM.EA_ASN A
			INNER JOIN LMS_LRM.EA_ASN_SMT B	
				ON A.ASN_ID  		= B.ASN_ID 
				AND B.STU_USR_ID  IS NOT NULL
			INNER JOIN LMS_LRM.CM_USR C 
				ON B.STU_USR_ID 	= C.USR_ID 
				AND C.CLA_ID  		= #{claId}
			WHERE A.ASN_PTME_DV_CD 	= 'OT'
				AND A.OPT_TXB_ID  	= #{optTxbId}
				AND A.ASN_ID 		= B.ASN_ID
			GROUP BY A.ASN_ID, A.ASN_NM, A.LRN_TP_CD
		) E
			ON B.ASN_ID = E.ASN_ID
        <!-- 모둠에 할당된 과제중에 제출완료 상시 과제 -->
  		LEFT JOIN 
		(	
			SELECT	A.ASN_ID, A.ASN_NM, A.LRN_TP_CD, IFNULL(COUNT(B.STU_USR_ID), 0) SMT_CMPL_Y_CNT
			FROM 	LMS_LRM.EA_ASN A
			INNER JOIN LMS_LRM.EA_ASN_SMT B	
				ON A.ASN_ID  		= B.ASN_ID 
				AND B.SMT_CMPL_YN  	= 'Y'
				AND B.STU_USR_ID  IS NOT NULL
			INNER JOIN LMS_LRM.CM_USR C 
				ON B.STU_USR_ID 	= C.USR_ID 
				AND C.CLA_ID  		= #{claId}
			WHERE A.ASN_PTME_DV_CD 	= 'OT'
				AND A.OPT_TXB_ID  	= #{optTxbId}
				AND A.ASN_ID 		= B.ASN_ID
			GROUP BY A.ASN_ID, A.ASN_NM, A.LRN_TP_CD
		) F
			ON B.ASN_ID = F.ASN_ID
			AND F.SMT_CMPL_Y_CNT = E.SMT_CMPL_ALL_CNT
        LEFT JOIN LMS_LRM.CM_CM_CD FF
            ON FF.URNK_CM_CD  	= 'LRN_TP_CD' 
            AND F.LRN_TP_CD		=  FF.CM_CD
        <!-- 모둠에 할당된 과제중에 제출 되지 않은 상시 과제 -->
        LEFT JOIN 
		(	
			SELECT	A.ASN_ID, A.ASN_NM, A.LRN_TP_CD, IFNULL(COUNT(B.STU_USR_ID), 0) SMT_CMPL_N_CNT
			FROM 	LMS_LRM.EA_ASN A
			INNER JOIN LMS_LRM.EA_ASN_SMT B
				ON A.ASN_ID  = B.ASN_ID 
				AND B.SMT_CMPL_YN  = 'N'
				AND B.STU_USR_ID  IS NOT NULL
			INNER JOIN LMS_LRM.CM_USR C 
				ON B.STU_USR_ID = C.USR_ID 
				AND C.CLA_ID  	= #{claId}
			WHERE A.ASN_PTME_DV_CD = 'OT'
				AND A.OPT_TXB_ID  = #{optTxbId}
				AND A.ASN_ID = B.ASN_ID
			GROUP BY A.ASN_ID, A.ASN_NM, A.LRN_TP_CD
		) G
			ON B.ASN_ID = G.ASN_ID
        LEFT JOIN LMS_LRM.CM_CM_CD GG
            ON GG.URNK_CM_CD  	= 'LRN_TP_CD' 
            AND G.LRN_TP_CD		=  GG.CM_CD
		WHERE   A.OPT_TXB_ID  = #{optTxbId}
	        AND A.DEL_YN      = 'N'
	    GROUP BY A.GRP_ID, A.GRP_NM, A.TEM_CNT, A.CRT_DTM, A.MDF_DTM, A.GRP_GRU_AUTO_CRT_YN, A.GRP_TMGR_USE_YN, 
	    		 AA.GRP_TEM_ID, AA.GRP_TEM_NM, CNT.TOTAL_CNT
	    ORDER BY A.MDF_DTM DESC
    ) A
	LEFT JOIN LMS_LRM.EA_GRP_TMBR C
	    ON A.GRP_ID = C.GRP_ID
	    AND A.GRP_TEM_ID = C.GRP_TEM_ID
	    AND C.DEL_YN = 'N'
	INNER JOIN LMS_LRM.CM_USR CU 
		ON C.STU_USR_ID = CU.USR_ID 
		AND CU.CLA_ID  = #{claId}
    LEFT JOIN   LMS_LRM.CM_CM_CD D
       	ON  D.URNK_CM_CD = 'LRNR_VEL_TP_CD'
        AND C.LRNR_VEL_TP_CD = D.CM_CD
	ORDER BY A.MDF_DTM DESC, A.GRP_ID, A.GRP_TEM_ID
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - selectGroupList */
</select>

<!-- 모둠 등록처리 -->
<insert id="insertGrpData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpDto" useGeneratedKeys="true" keyProperty="grpId" keyColumn="GRP_ID">
    INSERT INTO LMS_LRM.EA_GRP
    (
        OPT_TXB_ID,
        GRP_NM,
        TEM_CNT,
        GRP_GRU_AUTO_CRT_YN, 
        GRP_TMGR_USE_YN,
        DEL_YN,
        CRTR_ID,
        CRT_DTM,
        MDFR_ID,
        MDF_DTM,
        DB_ID
    )
    VALUES
    (
        #{optTxbId}
        ,#{grpNm}
        ,#{temCnt}
        ,#{grpGruAutoCrtYn}
        ,#{grpTmgrUseYn}
        ,'N'
        ,#{usrId}
        ,NOW()
        ,#{usrId}
        ,NOW()
        ,#{dbId}
    )
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - insertGrpData */
</insert>

<!-- 모둠 Update-->
<update id="updateGrpData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpDto">
    UPDATE LMS_LRM.EA_GRP       /* EA_모둠*/
    SET 
        GRP_NM                  = #{grpNm}              /* 모둠명 */
        ,TEM_CNT                = #{temCnt}             /* 팀수 */
        ,GRP_GRU_AUTO_CRT_YN    = #{grpGruAutoCrtYn}    /* 모둠그룹자동생성여부 */
        ,GRP_TMGR_USE_YN        = #{grpTmgrUseYn}       /* 모둠팀장사용여부 */
        ,DEL_YN                 = #{delYn}   /* 삭제여부 */
        ,MDFR_ID                = #{usrId}   /* 수정자ID */
        ,MDF_DTM                = NOW()      /* 수정일시 */
    WHERE GRP_ID   = #{grpId}   /* 모둠ID */
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - updateGrpData */
</update>

<!-- 모둠 팀 정보 저장 -->
<insert id="updateGrpTeamData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpTeamDto">

    INSERT INTO LMS_LRM.EA_GRP_TEM /* EA_모둠팀 */
    (
        GRP_ID          /* 모둠ID */
        ,GRP_TEM_ID     /* 모둠팀ID */
        ,GRP_TEM_NM     /* 모둠팀명 */
        ,TMBR_CNT       /* 팀원수 */
        ,CRTR_ID        /* 생성자ID */
        ,CRT_DTM        /* 생성일시 */
        ,MDFR_ID        /* 수정자ID */
        ,MDF_DTM        /* 수정일시 */
        ,DB_ID          /* 데이터베이스ID */
    )
    VALUES (
        #{grpId}        /* 모둠ID */
        ,#{grpTemId}    /* 모둠팀ID */
        ,#{grpTemNm}    /* 모둠팀명 */
        ,#{tmbrCnt}     /* 팀원수*/
        ,#{crtrId}       /* 생성자ID */
        ,NOW()          /* 생성일시 */
        ,#{crtrId}       /* 수정자ID */
        ,NOW()          /* 수정일시 */
        ,#{dbId}        /* 접속DB인스턴스ID */
    )
    ON DUPLICATE KEY UPDATE 
            GRP_TEM_NM  = #{grpTemNm}
        ,   TMBR_CNT    = #{tmbrCnt}
        ,   MDFR_ID     = #{crtrId}
        ,   MDF_DTM     = NOW()

    /* 추연도 EaGrpMgmtTcr-Mapper.xml - updateGrpTeamData */
</insert>

<!-- 모둠 팀 학생 정보 저장 -->
<insert id="updateGrpTeamStuData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpTeamStuDto">

    INSERT INTO LMS_LRM.EA_GRP_TMBR /* EA_모둠팀원 */
    (

        GRP_ID          /* 모둠ID */
        ,GRP_TEM_ID     /* 모둠팀ID */
        ,STU_USR_ID     /* 학생사용자ID */
        ,USR_NM         /* 사용자명 */
        ,GRP_TMGR_YN    /* 모둠팀장여부 */
        ,LRNR_VEL_TP_CD /* 학습자속도유형코드 */
        ,DEL_YN         /* 삭제여부 */
        ,CRTR_ID        /* 생성자ID */
        ,CRT_DTM        /* 생성일시 */
        ,MDFR_ID        /* 수정자ID */
        ,MDF_DTM        /* 수정일시 */
        ,DB_ID          /* 데이터베이스ID */
    )
    VALUES (
        #{grpId}        /* 모둠ID */
        ,#{grpTemId}    /* 모둠팀ID */
        ,#{usrId}       /* 학생사용자ID */
        ,#{usrNm}       /* 사용자명 */
        ,#{grpTmgrYn}   /* 모둠팀장여부 */
        ,#{lrnrVelTpCd} /* 모둠팀장여부 */
        ,#{delYn}       /* 삭제여부 */
        ,#{crtrId}      /* 생성자ID */
        ,NOW()          /* 생성일시 */
        ,#{crtrId}      /* 수정자ID */
        ,NOW()          /* 수정일시 */
        ,#{dbId}        /* 접속DB인스턴스ID */
    )
    ON DUPLICATE KEY UPDATE 
            GRP_TMGR_YN     = #{grpTmgrYn}
        ,   LRNR_VEL_TP_CD  = #{lrnrVelTpCd}
        ,   MDFR_ID         = #{crtrId}
        ,   MDF_DTM         = NOW()
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - updateGrpTeamStuData */
</insert>

<!-- 모둠 정보 삭제 -->
<delete id="deleteGrpData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto">    
    DELETE FROM LMS_LRM.EA_GRP
    WHERE  GRP_ID = #{grpId}

    /* 추연도 EaGrpMgmtTcr-Mapper.xml - deleteGrpData */
</delete>

<!-- 모둠 과제 delete -->
<delete id="deleteAsnData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto">
    DELETE FROM LMS_LRM.EA_ASN      
    WHERE  ASN_ID IN (	SELECT 	ASN_ID 
    					FROM 	LMS_LRM.EA_GRP_ASN_SMT 
    					WHERE 	GRP_ID = #{grpId}
						<if test = 'grpTemId != null and grpTemId != 0'>
		   					AND GRP_TEM_ID = #{grpTemId}
		   				</if>
    				)
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - updateGrpAsnData */
</delete>

<!-- 모둠 과제 학생정보 delete-->
<delete id="deleteAsnSmtData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto">
    DELETE 	FROM LMS_LRM.EA_ASN_SMT
    WHERE   ASN_ID IN 	(	SELECT 	ASN_ID 
    						FROM 	LMS_LRM.EA_GRP_ASN_SMT 
    						WHERE 	GRP_ID = #{grpId}
							<if test = 'grpTemId != null and grpTemId != 0'>
			   					AND GRP_TEM_ID = #{grpTemId}
			   				</if>
    					)
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - deleteAsnSmtData */
</delete>

<!-- 모둠 과제 제출정보 delete-->
<delete id="deleteGrpAsnSmtData" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto">
    DELETE 	FROM LMS_LRM.EA_GRP_ASN_SMT
    WHERE  	GRP_ID = #{grpId}
	<if test = 'grpTemId != null and grpTemId != 0'>
		AND GRP_TEM_ID = #{grpTemId}
	</if>
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - deleteGrpAsnSmtData */
</delete>


<!-- 모둠 팀 정보 삭제 -->
<delete id="deleteGrpTeamData" parameterType="Map">
    DELETE FROM LMS_LRM.EA_GRP_TEM
    WHERE GRP_ID = #{grpId}

    /* 추연도 EaGrpMgmtTcr-Mapper.xml - deleteGrpTeamData */
</delete>

<!-- 모둠 팀 학생 정보 삭제 -->
<delete id="deleteGrpTeamStuData" parameterType="Map">
    DELETE FROM LMS_LRM.EA_GRP_TMBR
    WHERE GRP_ID = #{grpId}

    /* 추연도 EaGrpMgmtTcr-Mapper.xml - deleteGrpTeamStuData */
</delete>

<select id="selectGrpAsnList" parameterType="int" resultType="hashMap">
	SELECT 	DISTINCT ASN_ID
	FROM 	LMS_LRM.EA_GRP_ASN_SMT A
	WHERE   A.GRP_ID  = #{grpId}
</select>

<insert id="insertGrpAsnSmtAll" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto">
	INSERT INTO LMS_LRM.EA_GRP_ASN_SMT (
		GRP_ID
		, GRP_TEM_ID
		, ASN_ID
		, SMT_CMPL_YN
		, CRTR_ID
		, CRT_DTM
		, MDFR_ID
 		, MDF_DTM
 		, DB_ID
	) 
	SELECT 
		GRP_ID
		, GRP_TEM_ID
		, #{asnId}
		, 'N'
		, #{usrId}
		, NOW()
		, #{usrId}
		, NOW()
		, #{dbId}
	FROM 	LMS_LRM.EA_GRP_TEM 
	WHERE	GRP_ID = #{grpId}
</insert>

<insert id="insertAsnSmtAll" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto">
	INSERT INTO LMS_LRM.EA_ASN_SMT (
		ASN_ID
		, STU_USR_ID
		, SMT_CMPL_YN
		, CRTR_ID
		, CRT_DTM
		, MDFR_ID
		, MDF_DTM
		, DB_ID
	) 
	SELECT 
		#{asnId}
		, STU_USR_ID
		, 'N'
		, #{usrId}
		, NOW()
		, #{usrId}
		, NOW()
		, #{dbId}
	FROM 	LMS_LRM.EA_GRP_TMBR 
	WHERE	GRP_ID = #{grpId}
</insert>


<select id="selectGroupInfo" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto" resultType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpDto">
    SELECT  GRP_ID, OPT_TXB_ID, GRP_NM, TEM_CNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM
	FROM    LMS_LRM.EA_GRP
	WHERE   GRP_ID  = #{grpId}
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - selectGroupInfo */
</select>

<select id="selectGroupTeamInfo" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto" resultType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpTeamDto">
    SELECT  GRP_ID, GRP_TEM_ID, GRP_TEM_NM, TMBR_CNT,
            CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
	FROM    LMS_LRM.EA_GRP_TEM
	WHERE   GRP_ID  = #{grpId}
    ORDER BY GRP_TEM_ID
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - selectGroupTeamInfo */
</select>

<select id="selectGroupTeamStuInfo" parameterType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto" resultType="com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpTeamStuDto">
    SELECT  A.GRP_ID, A.GRP_TEM_ID, A.STU_USR_ID AS USR_ID, B.STU_NO, 
            A.USR_NM, A.GRP_TMGR_YN, A.DEL_YN, B.USR_GND_CD, A.LRNR_VEL_TP_CD
	FROM    LMS_LRM.EA_GRP_TMBR A
    INNER JOIN LMS_LRM.CM_USR B
        ON A.STU_USR_ID = B.USR_ID
	WHERE   GRP_ID  = #{grpId}
    ORDER BY GRP_TEM_ID
    /* 추연도 EaGrpMgmtTcr-Mapper.xml - selectGroupTeamStuInfo */
</select>


</mapper>