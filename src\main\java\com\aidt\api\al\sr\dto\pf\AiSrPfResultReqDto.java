package com.aidt.api.al.sr.dto.pf;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-03 11:25:30
 * @modify date 2024-06-03 11:25:30
 * @desc
 */
@Data
public class AiSrPfResultReqDto {

    private String aiPfBsDataId;

    private List<StudentReqDto> students;

    @Data
    public static class StudentReqDto {

        private String usrId;

        private String stuNo;

        private String usrNm;

        private String lvlCd;

        public AiSrPfResultResDto.StudentResDto toDto(AiSrPfResultDto aiSrPfResultDto) {
            return AiSrPfResultResDto.StudentResDto.builder()
                    .cmt(aiSrPfResultDto.getPfCmt())
                    .lvlCd(this.lvlCd)
                    .usrId(this.usrId)
                    .stuNo(this.stuNo)
                    .usrNm(this.usrNm).build();
        }
    }
}
