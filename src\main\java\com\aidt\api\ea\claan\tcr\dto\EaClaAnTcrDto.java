package com.aidt.api.ea.claan.tcr.dto;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학급 분석 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaClaAnTcrDto {

	@Parameter(name="운영 교과서 ID")
	private String optTxbId;
	
	@Parameter(name="학급ID")
	private String claId;	

	@Parameter(name="학급 인원")
	private int claCnt;

	@Parameter(name="정렬")
	private String sorting;

	@Parameter(name="정렬")
	private List<EaClaAnTcrDto> optTxbIdList;

	@Parameter(name="대단원 ID")
	private long lluNodId;

	@Parameter(name="검색용 대단원 ID")
	private long qpLluId;
	
	@Parameter(name="검색용 분포도 학생 LIST")
	private List<Map<String, Object>> usrsAchdList;


}
