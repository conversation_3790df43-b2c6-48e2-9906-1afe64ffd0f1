package com.aidt.api.bc.home.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "우리 반 최근 평가 제출 목록")
public class BcRcnClaEvSmtDto {

    @Schema(description = "평가 이름")
    private String evNm;

    @Schema(description = "평가 ID")
    private String evId;

    @Schema(description = "평가 구분 코드")
    private String evDvCd;

    @Schema(description = "평가 구분 코드")
    private String evDtlDvCd;

    @Schema(description = "평가 상세 구분 명")
    private String evDtlDvNm;

    @Schema(description = "평가 제출 일자")
    private String smtDtm;

    @Schema(description = "학생 이름")
    private String stuNm;

    @Schema(description = "학생 아이디")
    private String usrId;

    @Schema(description = "차시 이름, 단원 번호 포함")
    private String fmtTcNm;

    @Schema(description = "단원 이름, 단원 번호 포함")
    private String fmtLuNm;

}
