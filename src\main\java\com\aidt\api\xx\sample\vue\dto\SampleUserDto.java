package com.aidt.api.xx.sample.vue.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class SampleUserDto {

	// 사용자 ID(개인 식별코드)
	private String userId;

	// 사용자 이름
	private String userName;

	// 학교 이름
	private String schoolName;

	// 학교구분 코드(2:초등, 3:중등, 4:고등)
	private String userDivision;

	// 학교급코드(E:초등, M:중등, H:고등)
	private String schlGrdCd;

	// 학교급코드명
	private String schlGrdCdNm;

	// 학년
	private String userGrade;

	// 반
	private String userClass;

	// 학기구분코드(00:공통, 01:1학기, 02:2학기)
	private String trmDvCd;

	// 요일(1:월, 2:화, 3:수, 4:목, 5:금)
	private String dayWeek;

	// 과목코드(KO:국어, EN:영어, MA:수학, SO:사회, SC:과학, IN:정보)
	private String sbjCd;

	// 과목명
	private String subjectName;

	// 교시
	private String classPeriod;

	// 교실
	private String classroomName;

	// 학생 성별(1:남학생, 2:여학생)
	private String userGender;

	// 학급코드
	private String classCode;

	// 운영교과서ID
	private String optTxbId;

	// 교과서ID
	private String txbId;

	// 대화사용여부
	private String slppUseYn;

	/*
		임시 필드 영역
	*/

	// 임시토큰ID
	private String tempUsrId;

	// 사용자유형코드
	private String usrTpCd;

	// 담임교사ID
	private String chgTcrUsrId;

	// 담임교사명
	private String chgTcrUsrNm;

	// 로그인 API 체크
	private String pointCheck;

	// 감정상태코드
	private String flnStCd;

	// uuid
	private String uuid;
	private String usrId;
    private String regId;
    private String regDt;
    private String usrNm;
    private String role;

    private String serverCheck;

}

