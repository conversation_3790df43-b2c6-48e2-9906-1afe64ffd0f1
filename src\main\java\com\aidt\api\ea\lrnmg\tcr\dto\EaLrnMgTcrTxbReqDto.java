package com.aidt.api.ea.lrnmg.tcr.dto;


import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석 - 우리반수업검색조건
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrTxbReqDto {
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="학급ID")
	private String claId;
	
	@Parameter(name = "대단원 ID", required = true)
    @NotBlank(message = "{field.required}")
	private String lluNodId;	

	@Parameter(name = "차시 ID", required = true)
    @NotBlank(message = "{field.required}")
	private String tcNodId;
}
