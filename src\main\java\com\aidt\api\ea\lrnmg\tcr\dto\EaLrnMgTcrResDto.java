package com.aidt.api.ea.lrnmg.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 교사 - 학생분석
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrResDto {
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="전체학생수")
	private int allStuCnt;
	
	@Parameter(name="관심학생수")
	private int interestStuCnt;
	
	@Parameter(name="빠른학생수")
	private int fastStuCnt;
	
	@Parameter(name="보통학생수")
	private int normalStuCnt;
	
	@Parameter(name="느린학생수")
	private int slowStuCnt;
	
	/* 학생별 학습정보 */
	@Parameter(name="학생별 학습정보")
	private List<EaLrnMgTcrMainListDto> usrLrnStList;	
	
	@Parameter(name = "단원 ID")
	private String kmmpNodId;	
	
	@Parameter(name = "단원 Nm")
	private String kmmpNodNm;	
	
	@Parameter(name = "차시사용여부")
	private String tcUseYn;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="토픽명")
	private String tpcKmmpNodNm;
	
	@Parameter(name="토픽ID")
	private String tpcKmmpNodId;
}
