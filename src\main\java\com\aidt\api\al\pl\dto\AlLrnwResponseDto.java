package com.aidt.api.al.pl.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 학습창 연계
 * 학습창 Responce
 * */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlLrnwResponseDto {
	
	private String lluNodNo;
	
	@Parameter(name="대단원ID")
	private String lluNodId;
	
	@Parameter(name="대단원명")
	private String lluNodNm;
	
	@Parameter(name="대단원노출여부")
	private String lluEspYn;
	
	@Parameter(name="중단원ID")
	private String mluNodId;
	
	@Parameter(name="중단원명")
	private String mluNodNm;
	
	@Parameter(name="중단원노출여부")
	private String mluEspYn;
	
	@Parameter(name="소단원ID")
	private String sluNodId;
	
	@Parameter(name="소단원명")
	private String sluNodNm;
	
	@Parameter(name="소단원노출여부")
	private String sluEspYn;
	
	@Parameter(name="차시ID")
	private String tcNodId;
	
	@Parameter(name="차시명")
	private String tcNodNm;
	
	@Parameter(name="차시노출여부")
	private String tcEspYn;
	
	@Parameter(name="차시잠금여부")
	private String lcknYn;
	
	@Parameter(name="총페이지")
	private Integer atvTotCnt;
	
	@Parameter(name="시작활동ID")
	private long strAtvId;
	
	private String atvEspYn;
	
	
	@Parameter(name="학습도구")
    private List<AlLrnwLrnTlDto> lrnTlList;
	
	@Parameter(name="학습단계")
	private List<AlLrnwLrnStpDto> lrnStpList;

	@Parameter(name="errorStatus")
	private String errorStatus;

	@Parameter(name="errorMessage")
	private String errorMessage;
	
	
}
