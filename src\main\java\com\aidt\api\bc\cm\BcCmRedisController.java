package com.aidt.api.bc.cm;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.common.Response;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-11-04 17:06:00
 * @modify 2024-11-04 17:06:00
 * @desc Redis 공통 Controller
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/bc/cm/redis")
@Tag(name="[bc] Redis 공통", description="[bc] Redis 공통")
@ConditionalOnExpression("'${spring.redis.enabled:false}'.equals('true')")
public class BcCmRedisController {
//	private final RedisTemplate redisTemplate;
//    private ObjectMapper objectMapper = new ObjectMapper();
    
//    @Operation(summary="Redis set object value", description = "Redis set object value")
//    @PostMapping("/setObj")
//    public Response.ResponseDto<Object> setString(@RequestBody Map<String, Object> params) {
//    	// key 와 value가 없을 경우 fail 처리 추가
//        String rdsKey = (String) params.get("rdsKey");
//        Object value = params.get("value");
//        try {
//        	String jsonValue = objectMapper.writeValueAsString(value);
//            redisTemplate.opsForValue().set(rdsKey, jsonValue);
//		} catch (JsonProcessingException e) {
//			e.printStackTrace();
//			return Response.fail();
//		}
//        return Response.ok();
//    }
//
//    @Operation(summary="Redis get object value", description = "Redis get object value")
//    @PostMapping("/getObj")
//    public Response.ResponseDto<Object> getString(String rdsKey) {
//    	// key 와 fail 처리 추가
//    	// 키가 존재 하지 않을 경우 어떻게 처리할지
//        ValueOperations<String, String> vop = redisTemplate.opsForValue();
//        Object rtnObj = vop.get(rdsKey);
//        return Response.ok(rtnObj);
//    }
//
//    @Operation(summary="Redis set hash value", description = "Redis set hash value")
//    @PostMapping("/setHash")
//    public Response.ResponseDto<Object> setHash(@RequestBody Map<String, Object> params) {
//        String rdsKey = (String) params.get("rdsKey");
//        String hashKey = (String) params.get("hashKey");
//        Object value = params.get("value");
//
//        HashOperations<String, Object, Object> hop = redisTemplate.opsForHash();
//        Map<String, Object> inputMap = new HashMap<>();
//        hop.put(rdsKey, hashKey, value);
//        return Response.ok();
//    }
//
//    @Operation(summary="Redis get Hash value", description = "Redis get Hash value")
//    @PostMapping("/getHash")
//    public Response.ResponseDto<Object> getHash(String rdsKey, String hashKey) {
//    	HashOperations<String, Object, Object> hop = redisTemplate.opsForHash();
//    	return Response.ok(hop.get(rdsKey, hashKey));
//    }
//
//    @Operation(summary="Redis get Hash value", description = "Redis get Hash value")
//    @PostMapping("/getHashAll")
//    public Response.ResponseDto<Object> getHash(String rdsKey) {
//    	HashOperations<String, Object, Object> hop = redisTemplate.opsForHash();
//    	return Response.ok(hop.entries(rdsKey));
//    }
    

}
