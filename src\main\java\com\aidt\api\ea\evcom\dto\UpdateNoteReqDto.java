package com.aidt.api.ea.evcom.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-05-02
 * @modify date 2025-05-02
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class UpdateNoteReqDto {
    @Parameter(name = "사용자 ID")
    private String usrId;

    @Parameter(name = "평가 ID")
    @NotNull
    private Integer evId;

    @Parameter(name = "문항 ID")
    private String qtmId;

    @Parameter(name = "첨부 ID")
    private long annxId;

    @Parameter(name = "첨부 파일 ID")
    private long annxFleId;

    @Parameter(name = "첨부 파일 경로")
    private String annxFlePthNm;

    @Parameter(name = "응시 회차")
    private int txmPn;

    @Parameter(name = "재응시 회차")
    private int rtxmPn;

    @Parameter(name = "유사/심화 평가 여부")
    private Boolean isExtra;
}


