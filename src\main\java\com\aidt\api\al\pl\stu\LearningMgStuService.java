package com.aidt.api.al.pl.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.cm.ma.AlMaService;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AlTpcMpnDto;
import com.aidt.api.al.pl.dto.LearningMgDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LearningMgStuService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.stu.learningMgStu.";
    @Autowired private CommonDao commonDao;
    
    @Autowired AlMaService alMaService;
    
    
    /**
     * 학습리포트 수학 AI학습현황
     * 
     * */
    public List<Map<String, Object>> selectLearningMgMaLuList(LearningMgDto dto) {
    	List<Map<String, Object>> resultMapList = new ArrayList<Map<String,Object>>();
    	
    	//진단평가 토픽숙련도 - 운영교과서,유저ID만 set(중단원X)
    	AlTpcMpnDto alTpcMpnDto = new AlTpcMpnDto();
    	alTpcMpnDto.setUsrId(dto.getUsrId());
    	alTpcMpnDto.setOptTxbId(dto.getOptTxbId());
		Map<String, Double> tpcAvnMap =  new HashMap<String, Double>();
		try {
			tpcAvnMap = alMaService.getOvTpcAvnMap(alTpcMpnDto);
		} catch (Exception e) {
			log.debug("getOvTpcAvnMap :: 진단평가 데이터 없음");
		}
		
		//단원 내 토픽숙련도 및 최근평가 조회
		List<LearningMgDto> mluTpcAvnUnsEvInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectStuTpcAvnUnsEvInfo", dto);
		Map<String, List<LearningMgDto>> mluListMap = mluTpcAvnUnsEvInfo.stream()
			    .collect(Collectors.groupingBy(
			        LearningMgDto::getMluKmmpNodId,
			        LinkedHashMap::new, // 순서를 보장하는 LinkedHashMap 사용
			        Collectors.toList()
			    ));
    	
		for (Map.Entry<String, List<LearningMgDto>> mluMap : mluListMap.entrySet()) {
			log.debug(mluMap.getKey() + ": " + String.valueOf(mluMap.getValue()));
            
            Map<String, Object> mluInfoMap = new HashMap<String, Object>();
            
            //학습할 토픽
            int learnTpcCnt = 0;
            
            //AI진단평가_이미 알고있는 토픽
            int ovTpcCnt03 = 0;
            List<String> ovTpcCnt03List = new ArrayList<String>();
            //AI진단평가_학습이 더 필요한 토픽
            int ovTpcCnt01 = 0;
            List<String> ovTpcCnt01List = new ArrayList<String>();
            
            //AI추천학습_더 알게된 토픽
            int tpcCnt03 = 0;
            List<String> tpcCnt03List = new ArrayList<String>();
            //AI추천학습_아직 부족한 토픽
            int tpcCnt01 = 0;
            List<String> tpcCnt01List = new ArrayList<String>();
            
            List<LearningMgDto> mluList = mluMap.getValue();
            for (LearningMgDto mlu : mluList) {
            	learnTpcCnt++;
            	
            	//진단평가 토픽숙련도
            	Double ovTpcAvn =  tpcAvnMap.get(mlu.getTpcKmmpNodId()) == null ? 0.5 : tpcAvnMap.get(mlu.getTpcKmmpNodId());
        		if(ovTpcAvn > AlConstUtil.TPC_AVN_03) {
        			ovTpcCnt03++;
        			ovTpcCnt03List.add(mlu.getTpcKmmpNodNm());
				}else {
					ovTpcCnt01++;
					ovTpcCnt01List.add(mlu.getTpcKmmpNodNm());
				}
            	
            	//토픽숙련도
            	Double tpcAvn = mlu.getTpcAvn() == null ? 0.5 : mlu.getTpcAvn();
            	if(tpcAvn > AlConstUtil.TPC_AVN_03) {
            		tpcCnt03++;
            		tpcCnt03List.add(mlu.getTpcKmmpNodNm());
				}else {
					tpcCnt01++;
					tpcCnt01List.add(mlu.getTpcKmmpNodNm());
				}
            	mluInfoMap.put("mluKmmpNodId", mlu.getMluKmmpNodId());	//중단원ID
            	mluInfoMap.put("mluKmmpNodNm", mlu.getMluKmmpNodNm());	//중단원명
            	mluInfoMap.put("lastEvDtlDvCd", mlu.getEvDtlDvCd());	//마지막 완료평가 구분코드
            	mluInfoMap.put("lastLearnDay", mlu.getCrtDtm());		//마지막학습일
            	mluInfoMap.put("learnTmAll", mlu.getLearningTm());		//총 학습시간
            	mluInfoMap.put("tcUseYn", mlu.getTcUseYn());			//차시사용여부
				mluInfoMap.put("useYn", mlu.getUseYn());				//사용여부
				mluInfoMap.put("lcknYn", mlu.getLcknYn());				//잠금여부
            	mluInfoMap.put("ovEvId", mlu.getOvEvId());				//진단평가ID (평가완료기준, 파라미터용이라함)
			}
    		mluInfoMap.put("learnTpcCnt", learnTpcCnt);					//학습할 토픽
    		mluInfoMap.put("ovTpcCnt03", ovTpcCnt03);					//진단_이미알고있는토픽수
    		mluInfoMap.put("ovTpcCnt03List", ovTpcCnt03List);			//진단_이미알고있는토픽리스트
    		mluInfoMap.put("ovTpcCnt01", ovTpcCnt01);					//진단_학습이 더 필요한 토픽수
    		mluInfoMap.put("ovTpcCnt01List", ovTpcCnt01List);			//진단_학습이 더 필요한 토픽리스트
    		mluInfoMap.put("tpcCnt03", tpcCnt03);						//추천학습_이미알고있는토픽수
    		mluInfoMap.put("tpcCnt03List", tpcCnt03List);				//추천학습_이미알고있는토픽리스트
    		mluInfoMap.put("tpcCnt01", tpcCnt01);						//추천학습_학습이 더 필요한 토픽수
    		mluInfoMap.put("tpcCnt01List", tpcCnt01List);				//추천학습_학습이 더 필요한 토픽리스트
    		
    		resultMapList.add(mluInfoMap);
        }
    	return resultMapList;
    }
    
    
}
