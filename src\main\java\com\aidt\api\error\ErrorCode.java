package com.aidt.api.error;

import lombok.Getter;
import org.springframework.http.HttpStatus;


@Getter
public enum ErrorCode {
    LU_EV_CMP_YN("LU_EV_CMP_YN", "단원 완료 여부 업데이트에 실패하였습니다."),
    LRNR_LEVEL_TP_CD("LRNR_LEVEL_TP_CD", "학습자 수준 업데이트에 실패하였습니다."),
    AI_CENTER("AI_CENTER", "AI 센터 통신에 실패하였습니다."),
    TPC_AVN("TPC_AVN", "토픽 숙련도 업데이트에 실패하였습니다."),
    AI_USRLY_TPC_LRN_ORDN("AI_USRLY_TPC_LRN_ORDN", "사용자별 토픽 학습 순서 저장에 실패하였습니다."),
    AI_QTMLY_XPL_STAS("AI_QTMLY_XPL_STAS", "AI 문항별 풀이 통계 저장에 실패하였습니다."),
    MY_HOME("MY_HOME", "마이홈 포인트 적립에 실패하였습니다."),
    ASN_CMPL("ASN_CMPL", "과제 완료 처리 및 알림 서비스 전송에 실패하였습니다."),
    LRN_PRGS_PROF("LRN_PRGS_PROF", "학습 진도율 업데이트에 실패하였습니다."),
    BAD_REQUEST("400", "요청이 잘못되었습니다."),
    INTERNAL_SERVER_ERROR("500", "서버 내부 오류가 발생하였습니다."),
    DEFAULT("DEFAULT", "알 수 없는 오류가 발생했습니다.");

    public static final String TYPE_INTERNAL = "INT";
    public static final String TYPE_EXTERNAL = "EXT";

    private final String code;
    private final String message;

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
