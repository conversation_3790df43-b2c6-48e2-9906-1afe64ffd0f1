package com.aidt.api.ea.grpmgmt.tcr;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtAllnfoDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpSrhDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpTeamDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtGrpTeamStuDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtSaveDto;
import com.aidt.api.ea.grpmgmt.dto.EaGrpMgmtStuListDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

@Service
public class EaGrpMgmtTcrService {
    private final String MAPPER_NAMESPACE = "api.ea.grpmgmt.tcr.";
    
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private CommonDao commonDao;
    
    private int currentGrpId = 0;
    private int currentGrpTemId = 0;

    /**
     * 학급의 학생리스트 조회
     * 
     * @param srhDto
     * @return List<EaGrpMgmtUserListDto>
     */
    public List<EaGrpMgmtStuListDto> selectStuList(EaGrpMgmtGrpSrhDto srhDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectStuList", srhDto);
    }

    /**
     * 학급의 모둠리스트 조회
     * 
     * @param srhDto
     * @return List<EaGrpMgmtUserListDto>
     */
    public List<EaGrpMgmtGrpDto> selectGroupList(EaGrpMgmtGrpSrhDto srhDto) {
    	List<EaGrpMgmtGrpDto> dataList = commonDao.selectList(MAPPER_NAMESPACE + "selectGroupList", srhDto);
		List<EaGrpMgmtGrpDto> returnList = new ArrayList<EaGrpMgmtGrpDto>();
		
		currentGrpId = 0;
		for (EaGrpMgmtGrpDto grp : dataList) {
			if (grp.getGrpId() != currentGrpId) {
				EaGrpMgmtGrpDto grpDto = new EaGrpMgmtGrpDto();
				currentGrpId = grp.getGrpId();
				grpDto.setAsnNmEnd(grp.getAsnNmEnd());
				grpDto.setAsnNmEndCnt(grp.getAsnNmEndCnt());
				grpDto.setAsnNmIng(grp.getAsnNmIng());
				grpDto.setAsnNmIngCnt(grp.getAsnNmIngCnt());
				grpDto.setAsnNmOtN(grp.getAsnNmOtN());
				grpDto.setAsnNmOtNCnt(grp.getAsnNmOtNCnt());
				grpDto.setAsnNmOtY(grp.getAsnNmOtY());
				grpDto.setAsnNmOtYCnt(grp.getAsnNmOtYCnt());
				grpDto.setGrpGruAutoCrtYn(grp.getGrpGruAutoCrtYn());
				grpDto.setGrpId(grp.getGrpId());
				grpDto.setGrpNm(grp.getGrpNm());
				grpDto.setTemCnt(grp.getTemCnt());
				grpDto.setGrpTmgrUseYn(grp.getGrpTmgrUseYn());
				grpDto.setSmtCmplCnt(grp.getSmtCmplCnt());
				grpDto.setTotalCnt(grp.getTotalCnt());
				
				List<EaGrpMgmtGrpTeamDto> grpTeamList = new ArrayList<EaGrpMgmtGrpTeamDto>();
				List<EaGrpMgmtGrpDto> teamList = dataList.stream().filter(rs -> rs.getGrpId() == currentGrpId).collect(Collectors.toList());
				
				currentGrpTemId = 0;
				
				for (EaGrpMgmtGrpDto tem : teamList) {
					if (!tem.getGrpTemId().equals(currentGrpTemId)) {
						EaGrpMgmtGrpTeamDto grpTeam = new EaGrpMgmtGrpTeamDto();
						List<EaGrpMgmtGrpTeamStuDto> grpTeamStuList = new ArrayList<EaGrpMgmtGrpTeamStuDto>();
						
						currentGrpTemId = tem.getGrpTemId();
						grpTeam.setGrpId(currentGrpId);	
						grpTeam.setGrpTemId(tem.getGrpTemId());
						grpTeam.setGrpTemNm(tem.getGrpTemNm());	
						
						dataList.stream().filter(rs -> rs.getGrpId() == currentGrpId && rs.getGrpTemId().equals(currentGrpTemId)).forEach(memList -> {
							EaGrpMgmtGrpTeamStuDto grpTeamStu = new EaGrpMgmtGrpTeamStuDto();
							grpTeamStu.setGrpId(currentGrpId);
							grpTeamStu.setGrpTemId(currentGrpTemId);
							grpTeamStu.setUsrId(memList.getUsrId());
							grpTeamStu.setUsrNm(memList.getUsrNm());
							grpTeamStu.setGrpTmgrYn(memList.getGrpTmgrYn());
							grpTeamStu.setLrnrVelTpCd(memList.getLrnrVelTpCd());
							grpTeamStu.setLrnrVelTpNm(memList.getLrnrVelTpNm());
							grpTeamStuList.add(grpTeamStu);
							grpTeamStu = null;
						});
						
						grpTeam.setStuList(grpTeamStuList);
						grpTeamList.add(grpTeam);
						grpTeam = null;
						grpDto.setGrpTemList(grpTeamList);
					}
				}

				returnList.add(grpDto);
			}
		}
		
//		return dataList;
		return returnList;
    }
    
     /**
     * 모둠 생성
     * 
     * @param saveDto
     * @return int
     */
    @Transactional
    public int saveMainData(EaGrpMgmtSaveDto saveDto) {
        int cnt = 0;
        boolean asnFlag = false;
        List<EaGrpMgmtGrpTeamDto> teamList = saveDto.getTeamList();   // 모둠 팀 리스트
        List<EaGrpMgmtGrpTeamStuDto> stuList = saveDto.getStuList();  // 모둠 팀 학생 리스트

        //Insert
        if (Integer.toString(saveDto.getGrpId()).equals("0")){
            commonDao.insert(MAPPER_NAMESPACE + "insertGrpData", saveDto);
        }
        //Update
        else{
            //1. 모둠 테이블 업데이트
            commonDao.update(MAPPER_NAMESPACE + "updateGrpData", saveDto);
            //2. 모둠 팀원 테이블 삭제
            commonDao.delete(MAPPER_NAMESPACE + "deleteGrpTeamData", Map.of("grpId", saveDto.getGrpId()));
            //3. 모둠 팀원 학생 테이블 삭제
            commonDao.delete(MAPPER_NAMESPACE + "deleteGrpTeamStuData", Map.of("grpId", saveDto.getGrpId()));
            
            asnFlag = true;
            
        }

        for (EaGrpMgmtGrpTeamDto teamDto : teamList){
            teamDto.setGrpId(saveDto.getGrpId());
            teamDto.setCrtrId(saveDto.getUsrId());
            teamDto.setMdfrId(saveDto.getUsrId());
            teamDto.setDbId(saveDto.getDbId());
            cnt += commonDao.update(MAPPER_NAMESPACE + "updateGrpTeamData", teamDto);
        }

        for (EaGrpMgmtGrpTeamStuDto stuDto : stuList){
            stuDto.setGrpId(saveDto.getGrpId());
            stuDto.setCrtrId(saveDto.getUsrId());
            stuDto.setMdfrId(saveDto.getUsrId());
            stuDto.setDbId(saveDto.getDbId());
            stuDto.setDelYn("N");
            cnt += commonDao.update(MAPPER_NAMESPACE + "updateGrpTeamStuData", stuDto);
        }
        
        if (asnFlag) {
        	int grpId = saveDto.getGrpId();
        	
        	
        	List<Map<String, Object>> mapList = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpAsnList", grpId);
        	
        	if (!mapList.isEmpty()) {
        		EaGrpMgmtGrpSrhDto grpDto = new EaGrpMgmtGrpSrhDto();	
        		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        		
        		grpDto.setGrpId(grpId);
        		grpDto.setUsrId(userDetails.getUsrId());
        		grpDto.setDbId(DB_ID);
        		
            	//과제 제출 테이블 삭제
            	commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmtData", grpDto);
            	//모둠 과제 제출 테이블 삭제
            	commonDao.delete(MAPPER_NAMESPACE + "deleteGrpAsnSmtData", grpDto);
            	
            	for(Map<String, Object> data : mapList) {
            		if (!data.isEmpty()) {
            			grpDto.setAsnId(Integer.parseInt(data.get("ASN_ID").toString()));
            			// 모둠 과제 제출 테이블 INSERT
            			commonDao.insert(MAPPER_NAMESPACE + "insertGrpAsnSmtAll", grpDto);
            			// 과제 제출 테이블 INSERT
            			commonDao.insert(MAPPER_NAMESPACE + "insertAsnSmtAll", grpDto);
            		}
            	}
        	}
        	
        	mapList = null;
        }

        return cnt;
    }

    @Transactional
    public int deleteGrpData(EaGrpMgmtGrpSrhDto deleteDto) {
    	int cnt = 0;
    	//모둠 팀 멤버 삭제
    	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpTeamStuData", Map.of("grpId", deleteDto.getGrpId()));
        //모둠 팀 삭제
    	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpTeamData", Map.of("grpId", deleteDto.getGrpId()));
    	//모둠 삭제
    	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpData", deleteDto);
    	
//    	//할당 된 과제가 없을 경우만 삭제 할수 있기 때문에 과제 테이블 삭제 로직 주석 처리
//    	//과제 삭제
//    	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnData", deleteDto);
//    	//과제 제출 테이블 삭제
//    	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmtData", deleteDto);
//    	//모둠 과제 제출 테이블 삭제
//    	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpAsnSmtData", deleteDto);
        return cnt;
    }

        /**
     * 학급의 모둠리스트 조회
     * 
     * @param srhDto
     * @return List<EaGrpMgmtUserListDto>
     */
    public EaGrpMgmtAllnfoDto selectTeamStudentList(EaGrpMgmtGrpSrhDto srhDto) {
        EaGrpMgmtAllnfoDto allDto = new EaGrpMgmtAllnfoDto();
        List<EaGrpMgmtGrpDto> grpDto = commonDao.selectList(MAPPER_NAMESPACE + "selectGroupInfo", srhDto);
        List<EaGrpMgmtGrpTeamDto> teamDto = commonDao.selectList(MAPPER_NAMESPACE + "selectGroupTeamInfo", srhDto);
        List<EaGrpMgmtGrpTeamStuDto> studentDto = commonDao.selectList(MAPPER_NAMESPACE + "selectGroupTeamStuInfo", srhDto);

        allDto.setGrpList(grpDto);
        allDto.setGrpTeamList(teamDto);
        allDto.setGrpTeamStuList(studentDto);
        // allDto.setGrpAllStuList(studentAllDto);

        return allDto;
    }
    
	public List<Map<String, Object>> selecrGrpNmAllList(EaGrpMgmtGrpSrhDto srhDto){
		List<Map<String, Object>> map = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpNmAllList", srhDto);
		return map;
	}
}
