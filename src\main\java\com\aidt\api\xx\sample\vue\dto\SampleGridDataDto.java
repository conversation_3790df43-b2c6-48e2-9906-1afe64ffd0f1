package com.aidt.api.xx.sample.vue.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "Sample Crud 입출력 DTO")
public class SampleGridDataDto {
    @Parameter(name="번호")
    private String no;

    @Parameter(name="사용자ID")
    private String userId;

    @Parameter(name="사용자명")
    private String userName;

    @Parameter(name="전화번호")
    private String tel;

    @Parameter(name="이메일")
    private String email;

    @Parameter(name="내용")
    private String textDesc;

    @Parameter(name="등록자ID")
    private String regId;

    @Parameter(name="등록일시")
    private String regDt;

    // 페이징
    private Integer rowsPerPage;
    private Integer page;
    private Integer totalCount;

    // 정렬
    private String orderBy;
    private Boolean descending;

}