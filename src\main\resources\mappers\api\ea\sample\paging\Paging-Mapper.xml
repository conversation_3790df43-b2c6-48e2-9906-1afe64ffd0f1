<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.sample.paging">
	<select id="selectList" resultType="com.aidt.api.xx.sample.paging.dto.SamplePagingDataDto">
		<include refid="api.ea.common.pagingHeader"/>
			SELECT * 
			FROM TEMP_USR_DAT
			WHERE 1=1
			<if test = 'usrId != null and !"".equals(usrId)'>
				AND USR_ID LIKE CONCAT('%',#{usrId},'%')
			</if>
			ORDER BY IDX DESC
		<include refid="api.ea.common.pagingFooter"/>
	</select>

</mapper>