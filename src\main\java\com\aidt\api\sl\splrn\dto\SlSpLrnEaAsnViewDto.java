package com.aidt.api.sl.splrn.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-01 12:55:26
 * @modify date 2024-06-01 12:55:26
 * @desc 특별학습과제출제확인용Dto
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnEaAsnViewDto {
	/** 운영교과서ID */
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	/** 특별학습ID */
	@Parameter(name="특별학습ID")
	private String spLrnId;
	
	/** 특별학습명 */
	@Parameter(name="특별학습명")
	private String spLrnNm;

	/** 특별학습노드ID */
	@Parameter(name="특별학습노드ID")
	private String spLrnNodId;
	
	/** 특별학습노드명 */
	@Parameter(name="특별학습노드명")
	private String spLrnNodNm;

	/** 사용여부 */
	@Parameter(name="사용여부")
	private String useYn;
	
	/** 과제 등록된 특별학습수 */
	@Parameter(name="과제 등록된 특별학습수")
	private int eaRegCount;
	
	/** 과제노드Id */
	@Parameter(name="과제노드Id")
	private String luNodId;
	
	/** 특별학습노드목록 */
	@Parameter(name="특별학습노드목록")
	private List<SlSpLrnNodDto> spLrnNodList;
	
}
