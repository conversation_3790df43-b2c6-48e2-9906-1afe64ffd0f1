package com.aidt.api.ea.qtmCom;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcCmLrnTmDto;
import com.aidt.api.ea.qtmCom.dto.EaQtmAiHintDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmReqDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmResDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;

import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 14:00:오후 2:00
 * @modify date 2024-02-15 14:00:오후 2:00
 * @desc 평가(DIY 평가) 공통 -  Controller
 */

@Slf4j
@Tag(name="[ea] 평가 문항 공통", description="평가 문항 공통")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/cm/qtmCom")
public class EaQtmComController {
    @Autowired
    private JwtProvider jwtProvider;

	@Autowired
	private BcCmService bcCmService;

	@Autowired
    EaQtmComService comService;


    @Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

    /**
     * 문항플랫폼 AI힌트 조회 요청
     * @param reqDto
     * @return ResponseList<EaQtmAiHintDto>
     */
    @Operation(summary="문항플랫폼 AI힌트 조회", description="문항플랫폼 AI힌트 조회")
    @PostMapping(value="/selectQpQtmAiHntList")
    public ResponseDto<List<EaQtmAiHintDto>> selectQpQtmAiHntList(@RequestBody EaEvQtmIdReqDto reqDto) {
        log.debug("Entrance selectQpQtmAiHntList");
        return Response.ok(comService.selectQpQtmAiHntList(reqDto));
    }

	/**
     * 교사평가/DIY평가 - 평가 추가 - 난이도 분포수에 따른 문항 리스트 조회 요청
     * @param evReqDto
     * @return ResponseList<EaEvDffdsQpQtmResDto>
     */
    @Operation(summary="난이도분포도별 문항리스트 조회", description="난이도분포도별 문항리스트 조회")
    @PostMapping(value = "/selectDffdDsbQtmIdList")
    public ResponseDto<List<EaEvDffdsQpQtmResDto>> selectDffdDsbQtmIdList(@Valid @RequestBody EaEvDffdsQpQtmReqDto evReqDto) {
        log.debug("Entrance selectDffdDsbQtmIdList");
        
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		evReqDto.setOptTxbId(userDetails.getOptTxbId()); // TODO 교과서ID
		evReqDto.setTxbId(userDetails.getTxbId()); // TODO 교과서ID
       
        return Response.ok(comService.selectDffdDsbQtmIdList(evReqDto));
    }
   
    
}
