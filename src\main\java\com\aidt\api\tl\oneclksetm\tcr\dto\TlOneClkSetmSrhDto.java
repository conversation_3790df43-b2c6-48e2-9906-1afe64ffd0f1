package com.aidt.api.tl.oneclksetm.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 16:06:10
 * @modify date 2024-02-15 16:06:10
 * @desc [TlOneClkSetmSrhDto 원클릭학습설정 목록 조회 조건 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlOneClkSetmSrhDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 운영교사ID */
    @Parameter(name="운영교사ID")
    private String tcrUsrId;
    
    /** 차시 ID */
    @Parameter(name="차시ID")
    private String lrmpNodId;

    /** 학급ID */
    @Parameter(name="학급ID")
    private String claId;
    
    /** 교과서ID */
    @Parameter(name="교과서ID")
    private String txbId;
    
    /** 현 학급 포함 여부 */
    @Parameter(name="현 학급 포함 여부")
    private String includeYn;

    /** 특별학습 ID */
    @Parameter(name="특별학습ID")
    private String spLrnId;

    /** 특별학습 ID 리스트 */
    @Parameter(name="특별학습ID 리스트")
    private List<String> spLrnIdList;

    /** 추천데이터 */
    @Parameter(name="추천데이터")
    private List<TlOneClkSetmRcmCtnDtlDto> dtlList;
}
