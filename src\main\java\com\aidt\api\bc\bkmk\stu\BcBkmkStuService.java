package com.aidt.api.bc.bkmk.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.bc.bkmk.dto.BcBkmkDto;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:55:16
 * @modify 2024-01-05 17:55:16
 * @desc 북마크 Service
 */

@Service
public class BcBkmkStuService {

    private final String MAPPER_NAMESPACE = "api.bc.bkmk.stu.";

    @Autowired
    private CommonDao commonDao;

    /**
     * 북마크 목록 조회 서비스
     *
     * @param bcBkmkDto : LwBkmkDto
     * @return List<LwBkmkDto>
     */
    public List<BcBkmkDto> selectBkmkList(BcBkmkDto bcBkmkDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectBkmkList", bcBkmkDto);
    }

    /**
     * 북마크 상세 조회 서비스
     *
     * @param lwBkmkDto : LwBkmkDto
     * @return LwBkmkDto
     */
    public BcBkmkDto selectBkmkDtl(BcBkmkDto bcBkmkDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectBkmkDtl", bcBkmkDto);
    }

    /**
     * 북마크 저장
     *
     * @param bcBkmkDto : LwBkmkDto
     * @return int
     */
	public int insertBkmk(BcBkmkDto bcBkmkDto) {

		int checkId = commonDao.select(MAPPER_NAMESPACE + "selectBkmkCheck", bcBkmkDto);
		int rsltCnt = 0;
		if(checkId == 0) {
			rsltCnt = commonDao.insert(MAPPER_NAMESPACE + "insertBkmk", bcBkmkDto);
		}

		if(rsltCnt > 0) {
			return bcBkmkDto.getBkmkId();
		}else {
			return checkId;
		}
    }

    /**
     * 북마크 삭제
     *
     * @param bkmkIdList: List<Integer>
     * @return int
     */
	public int deleteBkmk(BcBkmkDto bcBkmkDto) {
		return commonDao.delete(MAPPER_NAMESPACE + "deleteBkmk", bcBkmkDto);
    }

}
