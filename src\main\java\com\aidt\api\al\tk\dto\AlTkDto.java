package com.aidt.api.al.tk.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-02 09:06:00
 * @modify 2024-06-02 09:06:00
 * @desc AI회화 학생 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlTkDto{

	@Parameter(name="대단원노드ID(레슨ID)")
	private String kmmpNodId;
	
	@Parameter(name="대단원노드명(레슨명)")
	private String kmmpNodNm;
	
	@Parameter(name="토픽노드ID")
	private String tpcKmmpNodId;
	
	@Parameter(name="토픽노드명")
	private String tpcKmmpNodNm;
	
	@Parameter(name="대단원지식맵노드ID(레슨ID)")
	private String llukmmpNodId;
	
	@Parameter(name="아키핀ID")
	private String acpId;
	
	@Parameter(name="학습상태코드")
	private String lrnStCd;
	
	@Parameter(name="차시사용여부")
	private String tcUseYn;
	
	@Parameter(name="토픽순서")
	private String srtOrdn;
	
	@Parameter(name="노드랭크")
	private String nodRank;
		
	@Parameter(name="학습사용자ID")
	private String lrnUsrId;
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="완료학습조회")
	private String isCmplLrn;

	@Parameter(name="이전단원 사용여부")
	private String prevUseYn;
	
	@Parameter(name="이전단원 대단원노드ID")
	private String prevLluKmmpNodId;
	
	@Parameter(name="이전단원 토픽노드ID")
	private String prevTpcKmmpNodId;
	
	@Parameter(name="다음단원 사용여부")
	private String nextUseYn;
	
	@Parameter(name="다음단원 대단원노드ID")
	private String nextLluKmmpNodId;
	
	@Parameter(name="다음단원 토픽노드ID")
	private String nextTpcKmmpNodId;
	
	@Parameter(name="학교급")
	private String schlGrdCd;
	
	@Parameter(name="토픽인덱스")
	private String tpcIndex;
	
	
	@Parameter(name="토픽목록")
	public List<AlTkDto> tpcList;
	
	
	
	@Parameter(name="생성자ID")
	private String crtrId;
	
	@Parameter(name="등록자ID")
	private String mdfrId;
	
	@Parameter(name="데이터베이스ID")
	private String dbId;
	
	
	
	@Parameter(name="유저ID-아키핀")
	private String userID;
	
	@Parameter(name="dialogueID-아키핀")
	private Integer dialogueID;
	
	@Parameter(name="이어하기여부-아키핀")
	private Boolean dialogueContinue;
	
	@Parameter(name="유저발화-아키핀")
	private String userSpeech;
	
	@Parameter(name="학습년도일시")
	private String lrnYrDtm;
	
	@Parameter(name="AI읽기학습시간")
	private Long aiRdngLrnTm;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
}
