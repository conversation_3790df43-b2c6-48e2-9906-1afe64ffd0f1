package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "CM_KERIS_접속_로그")
public class BcCmKerisConnLog {

	@Schema(description = "로그ID")
	private Long logId;
	
	@Schema(description = "로그ID")
	private Long reqLogId;

	@Schema(description = "사용자ID")
	private String usrId;

	@Schema(description = "교육과정콘텐츠2단계코드")
	private String crclCtnElm2Cd;
	
	@Schema(description = "학습사용자ID")
	private String lrnUsrId;
	
	@Schema(description = "운영교과서ID")
	private String optTxbId;

	@Schema(description = "행동코드")
	private String actCd;

	@Schema(description = "결과여부")
	private String rsYn;

	@Schema(description = "결과코드")
	private String rsCd;

	@Schema(description = "결과메시지")
	private String rsMsg;

	@Schema(description = "결과BODY내용")
	private String rsBodyCn;

	@Schema(description = "접속URL")
	private String connUrl;

	@Schema(description = "파트너ID")
	private String prnrId;

	@Schema(description = "API버전")
	private String apiVer;

	@Schema(description = "BODY내용")
	private String bodyCn;

	@Schema(description = "등록일시")
	private String regDtm;
	
}
