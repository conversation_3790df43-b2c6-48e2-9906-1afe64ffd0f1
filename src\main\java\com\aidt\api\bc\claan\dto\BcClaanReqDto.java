package com.aidt.api.bc.claan.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcClaanReqDto {
	
	@Parameter(name="운영 교과서 ID")
    private String optTxbId;
	
	@Parameter(name="학급 ID")
	private String claId;
	
	@Parameter(name="학습 리포트 조회 기간")
    private String srhKn;

    @Parameter(name="학습 리포트 조회 조건")
    private int smtDtm;
    
    @Parameter(name="대단원노드ID")
    private String lluNodId;
    
    @Parameter(name="학교코드")
    private String schlCd;
    
    @Parameter(name="학년코드")
    private String sgyCd;
    
    @Parameter(name="학교급코드")
    private String schlGrdCd;
    
    @Parameter(name="평가ID")
    private String evId;
    
    @Parameter(name="평균정답률")
    private Double avgCansRt;
    
    @Parameter(name="유저ID")
    private String usrId;
    
    @Parameter(name="과목코드")
    private String sbjCd;
    
    @Parameter(name="사용자유형코드")
    private String usrTpCd;
    
    

}
