package com.aidt.api.bc.cm.file.service;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.file.adapter.BcCmFileAdapter;
import com.aidt.api.bc.cm.file.dto.BcFileResDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true)
@Service
@RequiredArgsConstructor
public class BcCmFileQueryService {

	private final BcCmFileAdapter bcCmFileAdapter;

	public BcFileResDto getFile(Long annxFleId) {
		var file = bcCmFileAdapter.getAnnxFile(annxFleId);

		if(ObjectUtils.isEmpty(file)) {
			log.warn("파일이 존재하지 않습니다.");
			//fixme: 흐음.exception을 던져야 하지 않을까?
			return null;
		}
		return BcFileResDto.of(file);
	}
}