package com.aidt.api.ea.evcom.dto;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class EaEvAnswerReqDto {

	@Schema(description = "평가 ID")
	@NotNull(message = "평가 번호가 존재하지 않습니다.")
	private Integer evId;

	@Schema(description = "평가 시간 초수")
	private Integer evTmScnt;

	@Schema(description = "응시 시작 여부")
	private String txmStrYn;

	@Schema(description = "평가 완료 여부")
	private String evCmplYn;

	@Schema(description = "재응시 회차")
	private Integer txmPn;

	@Schema(description = "평가 상세 구분 코드")
	private String evDtlDvCd;

	@Valid
	@Schema(description = "문항 목록")
	@NotEmpty(message = "평가 답안의 문항이 존재하지 않습니다.")
	private List<EaEvAnswerQtmDto> qtmList;

	@Valid
	@Schema(description = "학습활동 저장")
	private EaEvLearnActivityDto atvSaveDto;

	//todo: 노트만 저장 API 분리 적용 시 삭제
	@Schema(description = "노트만 저장 여부")
	private String noteOnlyYn;
	@Schema(description = "문항 ID")
	private Long qtmId;
	@Schema(description = "첨부 파일 ID")
	private Long annxFleId;
	public boolean isNoteOnly() {
		return "Y".equals(this.noteOnlyYn);
	}

	public boolean isOneMinuteExceeded() {
		return this.evTmScnt >= 60;
	}

	@Getter
	@AllArgsConstructor
	public static class EaEvAnswerQtmDto {
		@Schema(description = "문항 ID")
		@NotNull(message = "평가 답안의 문항 번호가 존재하지 않습니다.")
		private Long qtmId;

		@Schema(description = "첨부 파일 ID")
		private Long annxFleId;

		@Schema(description = "제출 답안")
		private String smtAnwVl;

		@Schema(description = "풀이상태 코드")
		private String xplStCd;

		@Schema(description = "힌트 확인 여부")
		private String hntCofmYn;

		@Schema(description = "설명 소요시간")
		private Integer xplTmScnt;

		@Schema(description = "외부 여부")
		private String isExternal;

		@Schema(description = "외주문항정답여부")
		private String isCorrect;

		public boolean isExternalQuestion() {
			return StringUtils.isNotBlank(this.isExternal) && this.isExternal.equalsIgnoreCase("Y");
		}

		public String getExternalQuestionCorrect() {
			return StringUtils.isNotBlank(this.isCorrect) ? this.isCorrect : "N";
		}
	}

	@Getter
	@AllArgsConstructor
	public static class EaEvLearnActivityDto {

		@Schema(description = "차시ID")
		//@NotBlank(message = "차시 아이디가 존재하지 않습니다.")
		private String lrmpNodId;

		@Schema(description = "학습활동ID")
		//@NotBlank(message = "혹습활동 아이디가 존재하지 않습니다.")
		private String lrnAtvId;

		@Schema(description = "학습상태코드", allowableValues = {"NL", "DL", "CL"})
		@NotBlank(message = "학습 상태 코드가 존재하지 않습니다.")
		private String lrnStCd;

		@Schema(description = "학습시간초수")
		//@Min(value = 0, message = "학습 시간은 0초이상이여야 합니다.")
		private int lrnTmScnt;

	}

}
