package com.aidt.api.tl.sbclrn.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 11:23:05
 * @modify date 2024-03-05 11:23:05
 * @desc [TlSbcLrnEvStuDto 교과학습 평가학생 조회 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlSbcLrnEvStuDto {
        /** 학생 ID */
        @Parameter(name="학생 ID")
        private String usrId;
        /** 학생 이름 */
        @Parameter(name="학생 이름")
        private String usrNm;
        /** 학생 번호 */
        @Parameter(name="학생 번호")
        private String stuNo;
        /** 학급 ID */
        @Parameter(name="학급 ID")
        private String claId;
        /** 평가 ID */
        @Parameter(name="평가 ID")
        private int evId;
        /** 평가 문항 수*/
        @Parameter(name="평가 문항수")
        private int fnlQstCnt;
        /** 평가 완료 여부*/
        @Parameter(name="평가 완료 여부")
        private String evCmplYn;
        /** 현재 풀이중인 문항*/
        @Parameter(name="현재 풀이중인 문항")
        private String curQtmId;
        /** 평가 결과 리스트 */
        @Parameter(name="평가 결과 리스트")
        private List<TlSbcLrnEvPstDto> evPstList ;
}
