package com.aidt.api.bc.chatbot.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
public class BcCbWdDto{	
	@Parameter(name="교과서ID")
	private String txbId;
	@Parameter(name="단어 ID")
	private String dicWdId;
	@Parameter(name="단어명")
	private String wdNm;
	@Parameter(name="학급코드")
	private String schlGrdCd;
	@Parameter(name="품사코드")
	private String wdclKnCd;
	@Parameter(name="품사명")
	private String wdclKnNm;
	@Parameter(name="단어뜻")
	private String wdMeanCn;
	@Parameter(name="예문")
	private String exsnCn;
	@Parameter(name="예문 해설")
	private String exsnIntpCn;
	@Parameter(name="단어파일 경로")
	private String wvFlePthNm;
	@Parameter(name="단어파일 타입 코드")
	private String wvExsnFleTpCd;
	@Parameter(name="단어파일 명")
	private String wvFleNm;
	@Parameter(name="단어파일 alt 설명")
	private String wvAltnTxtCn;
	@Parameter(name="예문파일 경로")
	private String evFlePthNm;
	@Parameter(name="예문파일 타입 코드")
	private String evExsnFleTpCd;
	@Parameter(name="예문파일 명")
	private String evFleNm;
	@Parameter(name="예문파일 alt 설명")
	private String evAltnTxtCn;
	@Parameter(name="이미지파일 경로")
	private String imFlePthNm;
	@Parameter(name="이미지파일 타입 코드")
	private String imExsnFleTpCd;
	@Parameter(name="이미지파일 명")
	private String imFleNm;
	@Parameter(name="이미지파일 alt 설명")
	private String imAltnTxtCn;
}
