package com.aidt.api.bc.slpp.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import com.aidt.api.bc.cm.dto.BcBaDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 18:01:28
 * @modify 2024-01-05 18:01:28
 * @desc 쪽지 dto
 */
	
@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcSlppDto extends BcBaDto{

	/*
		todo - dto validation
	 * 1. 학생과 학생 대화 불가
	 * 2. 메시지 전송 실패한 경우 => 실패 표시 아이콘, 재전송, 삭제버튼 노출
	 * 3. 텍스트만 전송 가능, 최대 200자까지 입력, 비속어 관리
	 * 4. 비속어 금지어 포함된 텍스트 작성 후 전송 버튼 눌렀을 경우 => 입력칸에 해당 단어 강조 표시, 토스트 메시지 노출
	 */

	@NotNull
	@Parameter(name="쪽지ID")
	private int slppId;

	@NotNull
	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@NotNull
	@Parameter(name="교사사용자ID")
	private String tcrUsrId;

	@NotNull
	@Parameter(name="학생사용자ID")
	private String stuUsrId;

	@NotNull
	@Pattern(regexp = "S|T")
	@Parameter(name="송신사용자유형코드(학생/교사)")
	private String trnmUsrTpCd;

	@Parameter(name="송신사용자유형코드명")
	private String trnmUsrTpCdNm;

	@NotNull
	@Pattern(regexp = "U|M")
	@Parameter(name="단체발송구분코드(일대일/전체)")
	private String grupSndDvCd;

	@Parameter(name="단체발송구분코드명")
	private String grupSndDvCdNm;

	@NotNull
	@Parameter(name="쪽지내용")
	private String slppCn;

	@Parameter(name="확인여부")
	private String cofmYn;

	@Parameter(name="교사삭제여부")
	private String tcrDelYn;

	@Parameter(name="학생삭제여부")
	private String stuDelYn;

	@Parameter(name="작성일")
	private String crtDtmFormat1;

	@Parameter(name="작성일")
	private String crtDtmFormat2;

	@Parameter(name="작성자")
	private String crtrId;

}
