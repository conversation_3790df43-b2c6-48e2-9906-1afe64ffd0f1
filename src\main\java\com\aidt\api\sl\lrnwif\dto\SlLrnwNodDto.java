package com.aidt.api.sl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-04-24 16:57:26
 * @modify : date 2024-04-24 16:57:26
 * @desc : SlLrnwNodDto 2depth 노드 dto
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwNodDto {
	
	@Parameter(name="특별학습ID")
	private String lluNodId;
	
	@Parameter(name="특별학습노드ID")
	private String tcNodId;
	
	@Parameter(name="특별학습노드명")
	private String tcNodNm;

}
