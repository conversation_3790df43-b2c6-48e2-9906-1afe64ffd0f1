<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.cm.tcr">



	<!-- 교사 로그인 후 :: 담당 학생 실시간 모니터링 테이블 데이터 조회 -->
	<select id="selectStuCurLrnStCnt" parameterType="com.aidt.api.bc.cm.dto.BcStuListDto" resultType="int">
		/** BcCmTcr-Mapper.xml - selectStuCurLrnStCnt */
		SELECT COUNT(*) FROM LMS_LRM.CM_STU_CUR_LRN_ST
		WHERE LRN_USR_ID = #{usrId}
	</select>

	<!-- 교사 로그인 후 :: 담당 학생 실시간 모니터링 테이블 데이터 등록 -->
	<insert id="insertStuCurLrnSt" parameterType="com.aidt.api.bc.cm.dto.BcStuListDto">
		/** BcCmTcr-Mapper.xml - insertStuCurLrnSt */
		INSERT INTO LMS_LRM.CM_STU_CUR_LRN_ST(
			LRN_USR_ID, LRN_TP_CD, CUR_LRN_MENU_ID, CUR_LRN_MENU_NM, OPT_TXB_ID
			, FLN_ST_CD, CUR_CONN_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{usrId}
			, 'TL'
			, '1'
			, '홈'
			, #{optTxbId}
			, 'SN'
			, 'N'
			, #{usrId}
			, NOW()
			, #{usrId}
			, NOW()
			, #{txbId}
		)
	</insert>

	<!-- 교사가 담당학생에 대한 관심여부 변경 -->
	<update id="updateStuNtrYn" parameterType="com.aidt.api.bc.cm.dto.BcUsrInfoDto">
		/** BcCmTcr-Mapper.xml - updateStuNtrYn */
		UPDATE LMS_LRM.CM_USR
		SET
			NTR_YN = #{ntrYn}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE USR_ID = #{usrId}
	</update>

	<!-- 학생 목록 조회 -->
	<select id="selectStuList" parameterType="String" resultType="com.aidt.api.bc.cm.dto.BcStuListDto">
		/** BcCmTcr-Mapper.xml - selectStuList */
		SELECT
			B.USR_ID
			, B.USR_NM
			, B.STU_NO
		    , B.KERIS_USR_ID
			, (
				SELECT COUNT(*)
				FROM
					LMS_LRM.CM_CLA A
				INNER JOIN LMS_LRM.CM_USR B ON A.CLA_ID = B.CLA_ID
											AND B.USR_TP_CD = 'ST'
				WHERE A.CHG_TCR_USR_ID = #{usrId}
			) AS ALL_STU_CNT
		FROM
			LMS_LRM.CM_CLA A
		INNER JOIN LMS_LRM.CM_USR B ON A.CLA_ID = B.CLA_ID
									AND B.USR_TP_CD = 'ST'
		WHERE A.CHG_TCR_USR_ID = #{usrId}
	</select>

	<!-- 학급 목록 조회 -->
	<select id="selectClaList" parameterType="Map" resultType="com.aidt.api.bc.cm.dto.BcClaListDto">
		/** BcCmTcr-Mapper.xml - selectClaList */
		select 
			R.OPT_TXB_ID, 
			c.CLA_ID, 		/* 반 ID*/
			c.SGY, 			/* 학년*/
			c.CLA_NO,
			CASE
        		WHEN c.CLA_NM NOT LIKE '%반' THEN CONCAT(c.CLA_NM,'반')
        		ELSE c.CLA_NM
    		END AS CLA_NM,
    		c.classroom_nm as classroom_name
		from LMS_LRM.CM_USR A
		inner join LMS_LRM.cm_cla c
			on A.CLA_ID = C.CLA_ID
		inner join LMS_LRM.cm_opt_tcr r
			on A.USR_ID = R.TCR_USR_ID
		inner join LMS_LRM.cm_opt_txb t
			on R.OPT_TXB_ID = T.OPT_TXB_ID 
		where A.KERIS_USR_ID = #{kerisUsrId}
		AND T.TXB_ID=#{txbId}
		
		ORDER BY C.CLA_ID ASC, C.SGY ASC, C.CLA_NM ASC
	</select>
</mapper>
