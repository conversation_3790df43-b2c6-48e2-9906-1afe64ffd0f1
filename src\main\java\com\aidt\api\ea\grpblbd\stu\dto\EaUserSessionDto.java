package com.aidt.api.ea.grpblbd.stu.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-23
 * @modify date 2024-01-23
 * @desc User Session Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaUserSessionDto {

	@Parameter(name="Session Class Id")
	private String sessionClaId;

	@Parameter(name="Session OptTxbId")
	private String sessionOptTxbId;

	@Parameter(name="Session Role")
	private String sessionRole;

	@Parameter(name="Session TxbId")
	private String sessionTxbId;

	@Parameter(name="Session UsrId")
	private String sessionUsrId;

	@Parameter(name="Session UsrNm")
	private String sessionUsrNm;

	@Parameter(name="Session UsrTpCd")
	private String sessionUsrTpCd;
}
