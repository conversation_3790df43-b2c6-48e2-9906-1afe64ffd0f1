/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 04:06:36
 * @modify 2024. 6. 22.
 * @desc 
 */
package com.aidt.api.ea.lrnrpt.dto;

import java.util.List;

import javax.validation.constraints.Size;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-22 16:06:36
 * @modify 2024-06-22 16:06:36
 * @desc 
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EaAllrAnRpotSrhDto {

	@Parameter(name="운영교과서ID", required=false)
	@Size(max=30, message="30자{field.max}")
	public String optTxbId;
	
	@Parameter(name="학습사용자ID", required=false)
	@Size(max=36, message="36자{field.max}")
	public String usrId;
	
	@Parameter(name="대단원ID", required=false)
	@Size(max=30, message="30자{field.max}")
	public String lluNodId;
	
	@Parameter(name="추천학습조회 param", required=false)
	List<EaAllrAnRpotDto> anRpotList;
}
