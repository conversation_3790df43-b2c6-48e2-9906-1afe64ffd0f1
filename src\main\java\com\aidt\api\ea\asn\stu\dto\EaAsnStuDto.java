package com.aidt.api.ea.asn.stu.dto;

import java.util.List;
import com.aidt.api.ea.asncom.dto.EaAsnFleDto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 과제 - 학생 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaAsnStuDto {

	@Parameter(name="순번")
	private int idx;

	@Parameter(name="아이디")
	private String userId;

	@Parameter(name="유저규칙")
	private String userRole;

	@Parameter(name="이름")
	private String userNm;

	@Parameter(name="이메일")
	private String email;

	@Parameter(name="메시지")
	private String msg;

	@Parameter(name="대상")
	private String target;



	//EA_과제
	@Parameter(name="과제ID")
	private Integer asnId;

	@Parameter(name="운영교과서ID")
	private String optTxbId;

	@Parameter(name="교사사용자ID")
	private String tcrUsrId;

	@Parameter(name="과제명")
	private String asnNm;

	@Parameter(name="과제설명")
	private String asnCn;

	@Parameter(name="과제유형코드")
	private String asnTpCd;

	@Parameter(name="학습유형코드")
	private String lrnTpCd;

	@Parameter(name="과제기간구분코드")
	private String asnPtmeDvCd;

	@Parameter(name="시작일시")
	private String strDtm;

	@Parameter(name="종료일시")
	private String endDtm;

	@Parameter(name="마감이후제출가능여부")
	private String flnAfSmtAbleYn;

	@Parameter(name="과제평가방식유형코드")
	private String evMthdTpCd;

	@Parameter(name="만점점수")
	private int pscScr;

	@Parameter(name="삭제여부")
	private String delYn;
	
	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성일시")
	private String crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private String mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;


	//EA_과제범위
	@Parameter(name="과제범위순번")
	private int asnRngeSeqNo;

	@Parameter(name="학습단계구분코드")
	private String lrnStpDvCd;

	@Parameter(name="단원노드ID")
	private String luNodId;

	@Parameter(name="차시노드ID")
	private String tcNodId;

	@Parameter(name="활통콘텐츠ID")
	private String atvCtnId;

	@Parameter(name="특별학습ID")
	private String spLrnId;



	//EA_과제제출
	@Parameter(name="학생사용자ID")
	private String stuUsrId;

	@Parameter(name="제출일시")
	private String smtDtm;

	@Parameter(name="제출내용")
	private String smtCn;

	@Parameter(name="첨부ID")
	private String annxId;

	@Parameter(name="제출완료여부")
	private String smtCmplYn;

	@Parameter(name="점수")
	private Integer scr;

	@Parameter(name="피드백내용")
	private String fdbkCn;


	//EA_모둠과제제출
	@Parameter(name="모둠ID")
	private Integer grpId;

	@Parameter(name="모둠팀ID")
	private Integer grpTemId;


	//EA_모둠
	@Parameter(name="모둠명")
	private String grpClNm;

	@Parameter(name="팀수")
	private int temCnt;


	//EA_모둠팀
	@Parameter(name="모둠팀명")
	private String GrpTemNm;

	@Parameter(name="팀원수")
	private int tmbrCnt;


	//EA_모둠팀원
	@Parameter(name="사용자명")
	private String usrNm;

	@Parameter(name="모듬팀장여부")
	private String grpTmgrYn;

	//EA_모둠게시판
	@Parameter(name="게시판ID")
	private String BlbdId;

	@Parameter(name="게시판명")
	private String blbdNm;


    //추가
	@Parameter(name="정렬구분코드")
	private String tebCd;

	@Parameter(name="학생사용자ID List")
	private List<String> stuUsrIdList;

	@Parameter(name="단원차시명")
	private String nodNm;

	@Parameter(name="기간")
	private String asnPtmeDv;

	@Parameter(name="콘텐츠ID 배열")
	private String[] atvCntArr;

	@Parameter(name="모둠팀원 이름 배열")
	private String[] grpUsrNmArr;

	@Parameter(name="유형코드이름")
	private String asnPtmeDvNm;

	@Parameter(name="유형코드")
	private String asnLrnTp;

	@Parameter(name="시작일시")
	private String strDtmNm;

	@Parameter(name="종료일시")
	private String endDtmNm;

	@Parameter(name="과제학습유형코드이름")
	private String asnLrnTpNm;

    @Parameter(name="d-day")
	private String dDay;

	@Parameter(name="날짜 계산 - D-Day Chcek")
	private String dateDiffStartEnd;

	@Parameter(name="날짜 계산 - 과제기간 종료 여부")
	private String dateDiffEndNow;

	@Parameter(name="정렬조건")
	private String sortOption;

	@Parameter(name="조회조건")
	private String searchOption;
	
	@Parameter(name="ai조회조건")
	private List<String> aiSearchOptionList;
	
	@Parameter(name="유형 조회조건")
	private String asnTpOption;

	@Parameter(name="교사첨부ID")
	private String tcrAnnxId;

	@Parameter(name="학생첨부ID")
	private String stuAnnxId;

	@Parameter(name="븍별학습노드 List")
	private List<String> luNodList;

	@Parameter(name="평가ID")
	private Integer evId;

	@Parameter(name="총 개수")
	private int totalCnt;

	private int pageNo;
	private int pageSize;


	@Parameter(name="제출일시")
	private String smtDtmNm;



	@Parameter(name="마이홈포인트사용여부")
	private String myhmPntUseYn;
	
	
	@Parameter(name="마이홈포인트사용여부")
	private String evCmplYn;
	
	@Parameter(name="AI학습창 파라미터")
	private String isRcm;
	
	

	/** 파일 관련 VO **/
	@Parameter(name="첨부파일ID")
	private int annxFileId;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="첨부파일명")
	private String annxFileNm;

	@Parameter(name="첨부파일원본명")
	private String annxFileOrglNm;

	@Parameter(name="첨부파일확장자명")
	private String annxFileFextNm;

	@Parameter(name="첨부파일사이즈")
	private int annxFileSize;

	@Parameter(name="첨부파일경로명")
	private String annxFilePathNm;

	@Parameter(name="최초제출일시")
	private String fstSmtDtm;
	
	@Parameter(name="문서뷰어ID")
    private String docViId;

	@JsonProperty("usrIdList")
	private List<usrIdList> usrIdList;

	@Parameter(name="과제첨부파일리스트")
	private List<EaAsnFleDto> fleList;

	@Parameter(name="과제제출첨부파일ID")
	private String annxIdForSmt;

	@Parameter(name="과제제출첨부파일리스트")
	private List<EaAsnFleDto> smtFleList;
	
	@Parameter(name="업데이트 여부")
	private String updateCheck;
	
	@Parameter(name="단원번호")
	private String nodNo;
	
	@Parameter(name="차시번호")
	private String tcNo;

	@Parameter(name="차시이름")
	private String tcNm;
	
	@Parameter(name="학습활동ID")
	private String lrnAtvId;
	
	@Parameter(name="과제전제완료여부")
	private String allSmtYn;
	
	@Parameter(name="차시잠금여부")
	private String tcLockYn;
	
	@Parameter(name="점수여부")
	private String scrYn;
	
	@Parameter(name="평가생성여부")
	private String evRcmYn;
	
	@Parameter(name="평가상세코드")
	private String evDtlDvCd;

	@Data
	public static class usrIdList {
		@JsonProperty("stuUsrId")
		private String stuUsrId;
	}
	
	public void setAiSearchOptionList(List<String> aiSearchOptionList) {
	    this.aiSearchOptionList = aiSearchOptionList;
	}
}
