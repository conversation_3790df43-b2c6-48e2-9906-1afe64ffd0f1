package com.aidt.api.sl.splrn.dto;

import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-01-10 10:45:26
 * @modify : date 2024-02-23 15:41:00
 * @desc : 특별학습 상세목록 리스트
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnDtlViewDto {
	@Parameter(name="조회 인덱스")
	private int idx;

	@Parameter(name="특별학습ID")
	private String spLrnId;

	@Parameter(name="특별학습하위노드ID")
	private String spLrnNodId;

	@Parameter(name="특별학습상위노드ID")
	private String urnkSpLrnNodId;

	@Parameter(name="특별학습콘텐츠아이디", required = true)
	private String splrnCtnId;

	@Parameter(name="콘텐츠순서")
	private String srtOrdn;

	@Parameter(name="노드명")
	private String spLrnNodNm;

	@Parameter(name="학습상태코드", required = true)
	private String lrnStCd;

	@Parameter(name="완료갯수")
	private int done;

	@Parameter(name="전체갯수")
	private int entire;

	@Parameter(name="최종수정시간")
	private Date maxMdfDtm;

	@Parameter(name="최하위여부")
	private String lwsYn;

	@Parameter(name="특별학습 콘텐츠별 진행도")
	private List<SlSpLrnPgrsDto> slSpLrnPgrsDtoList;

	@Parameter(name="특별학습 썸네일 리스트")
	private List<SlSpLrnNodThbDto> bcSplrnNodThbList;

	@Parameter(name="(v3.1)특별학습 콘텐츠별 진행도")
	private List<SlSpLrnPgrsDto> slSpLrnPgrsList;

}
