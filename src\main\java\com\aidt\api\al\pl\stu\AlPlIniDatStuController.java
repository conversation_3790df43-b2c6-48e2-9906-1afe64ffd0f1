package com.aidt.api.al.pl.stu;

import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.al.pl.dto.AlPlStuDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@Tag(name="[al] AI맞춤학습 초기데이터 작성(학생)", description="로그인한 학생이 담당하는 담당클래스를 조회하여, AI맞춤학습 재구성 초기데이터를 작성한다.")
@RestController
@RequestMapping("/api/v1/al/stu/alPlIniDat")
public class AlPlIniDatStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private AlPlIniDatStuService alPlIniDatStuService;
	
	/**
     * AI_지식맵노드재구성 초기생성
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="AI맞춤학습 지식맵노드재구성", description="AI_지식맵노드재구성 초기데이터를 생성한다.")
    @PostMapping(value = "/registIniDat")
    public ResponseDto<Integer> registIniDat() {
        log.debug("AlPlIniDatStuController registIniDat");

        AlPlStuDto dto = new AlPlStuDto();
        
        //세션정보에서 설정
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	dto.setUsrId("system");
        
        return Response.ok(alPlIniDatStuService.registIniDat(dto));
    }
}
