<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.tnte.cm">
	<!-- 
		2025.02.03 LRN_ATV_ID 컬럼표준화 BIGINT 타입
		LMS_LRM.MY_TNTE.LRN_ATV_ID는 문자열도 들어가는 타입으로 LRN_ATV_INFO로 컬럼명 변경됨에 따라 쿼리문에 사용된 LRN_ATV_ID -> LRN_ATV_INFO 수정처리
	-->
	<!-- 필기 조회 -->
	<select id="selectTnteList" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcTnteDto">
		/** BcTnte-Mapper.xml - selectTnteList */
		SELECT
			TNTE.TNTE_ID,
			B.RCSTN_ORDN,
			TNTE.TNTE_DV_CD,
			TNTE.LRN_USR_ID,
			TNTE.LRN_TP_CD,
			(
				SELECT B.CM_CD_NM FROM LMS_LRM.CM_CM_CD B WHERE B.URNK_CM_CD = 'LRN_TP_CD' AND B.CM_CD = TNTE.LRN_TP_CD
			) AS LRN_TP_CD_NM,
			TNTE.OPT_TXB_ID,
			TNTE.LU_NOD_ID,
			TNTE.TC_NOD_ID,
			TNTE.LRN_ATV_INFO AS LRN_ATV_ID,
			B.LRMP_NOD_NM AS LLU_NM,
			C.LRMP_NOD_NM AS TC_NM,
			(
				SELECT D.LRN_ATV_NM FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN D
				WHERE D.OPT_TXB_ID = TNTE.OPT_TXB_ID
				AND D.LRN_ATV_ID = TNTE.LRN_ATV_INFO
			) AS LRN_ATV_NM,
			TNTE.CDN_PTH_NM,
			DATE_FORMAT(TNTE.CRT_DTM, '%Y.%m.%d') AS CRT_DTM
		FROM
			LMS_LRM.MY_TNTE TNTE
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON TNTE.OPT_TXB_ID = B.OPT_TXB_ID
												  AND TNTE.LU_NOD_ID = B.LRMP_NOD_ID
												  AND B.DPTH = 1
												  AND B.LCKN_YN = 'N'
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C ON TNTE.TC_NOD_ID = C.LRMP_NOD_ID
												  AND TNTE.OPT_TXB_ID = C.OPT_TXB_ID
												  AND C.DPTH = 4
												  AND C.LCKN_YN = 'N'
        <where>
			<if test = 'optTxbId != null and !"".equals(optTxbId)'>
				AND TNTE.OPT_TXB_ID = #{optTxbId}
			</if>
			<if test = 'luNodId != null and !"".equals(luNodId)'>
				AND TNTE.LU_NOD_ID = #{luNodId}
			</if>
			<if test = 'tcNodId != null and !"".equals(tcNodId)'>
				AND TNTE.TC_NOD_ID = #{tcNodId}
			</if>
			<if test = 'lrnAtvId != null and !"".equals(lrnAtvId)'>
				AND TNTE.LRN_ATV_INFO = #{lrnAtvId}
			</if>
			<if test = 'lrnUsrId != null and !"".equals(lrnUsrId)'>
				AND TNTE.LRN_USR_ID = #{lrnUsrId}
			</if>
		</where>
		ORDER BY TNTE.CRT_DTM desc
	</select>

</mapper>