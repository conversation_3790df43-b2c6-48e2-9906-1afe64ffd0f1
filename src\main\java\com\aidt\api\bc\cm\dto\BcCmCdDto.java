package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "메뉴 공통")
public class BcCmCdDto {

	@Parameter(name="메뉴ID")
	private String cmCd;

	@Parameter(name="상위메뉴ID")
	private String cmCdNm;
	
	@Parameter(name="상위메뉴ID")
	private String urnkCmCd;


}