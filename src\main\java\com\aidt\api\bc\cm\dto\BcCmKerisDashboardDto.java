package com.aidt.api.bc.cm.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class BcCmKerisDashboardDto {
	/**********************
	 * keris api server url 운영인지 test 인지 프론트에서 판단
	 **********************/
	private String api_domain;

	/***********
	 * keris api 연동 header parameter
	 **********/
	@NotBlank(message = "{field.required}")
	private String partner_id;
	@NotBlank(message = "{field.required}")
	private String api_version;

	/***********
	 * keris api 연동 body parameter
	 ***********/
	@NotBlank(message = "{field.required}")
	private String token;
	@NotBlank(message = "{field.required}")
	private String access_id;
	@NotBlank(message = "{field.required}")
	private String user_id;
	@NotBlank(message = "{field.required}")
	private String act_type;
	@NotBlank(message = "{field.required}")
	private String curriculum;
	@NotBlank(message = "{field.required}")
	private String timestamp;
	
	/**********
	 * keris api 연동 결과 
	 */
	private String code;
	private String message;
	private String error_code;
	private String error_transfer_id;
	
	/***********
	 * conn log 필요 데이터
	 */
	private Long reqLogId;
	private String connUrl;
	private String bodyCn;
	private String rsBodyCn;

	// /aidt_dashboard/curriculum_progressed 에서만 사용
	private String percent;

	// /aidt_dashboard/curriculum_score 에서만 사용
	private String score;
	private String achievement_level;
}
