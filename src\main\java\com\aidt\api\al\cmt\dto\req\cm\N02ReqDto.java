package com.aidt.api.al.cmt.dto.req.cm;

import javax.validation.Valid;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.cm.N02Dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-24 15:10:58
 * @modify date 2024-05-24 15:10:58
 * @desc 내용영역평가
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class N02ReqDto {

    @Valid
    @Parameter(name="전체 문항", required=true)
    private QtmCntReqDto qtmCnt;

    public N02Dto toDto(AiCmtLvlCalculator calculator) {
        return N02Dto.builder()
                .achLvlCd(calculator.calculate(this.qtmCnt))
                .build();
    }

}
