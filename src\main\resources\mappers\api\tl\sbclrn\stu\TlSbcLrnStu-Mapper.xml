<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.sbclrn.stu">

    <!-- 단원목차내 학습활동목록조회
    <select id="selectLrnAtvList" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvDto">
        SELECT * FROM
        (SELECT
		       A.OPT_TXB_ID  				/* 운영교과서ID */
		      ,A.LRMP_NOD_ID  				/* 학습맵노드ID */
		      ,A.LRN_ATV_ID  				/* 학습활동ID */
		      ,A.LRN_STP_ID  				/* 학습단계ID */
			  ,B.LRN_STP_DV_CD				/* 학습단계 구분코드 */
			  ,B.LRN_STP_NM					/* 학습단계명 */
		      ,A.CTN_CD  					/* LCMS 학습활동코드(varchar(10)) */
		      ,A.LRN_ATV_NM  				/* 문항아이디 or 파일명 */
		      ,A.CTN_TP_CD  				/* 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
		      ,A.USE_YN  					/* 사용여부 */
		      ,A.RCSTN_ORDN  				/* 재구성순서 */
		      ,A.EV_ID  					/* 평가ID */
		      ,B.SRT_ORDN
		FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
		LEFT JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
			ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
			AND A.LRN_STP_ID = B.LRN_STP_ID 
		WHERE A.OPT_TXB_ID = #{optTxbId} 	
		AND A.LRMP_NOD_ID = #{lrmpNodId}  	
		
		UNION ALL
		
		SELECT M.OPT_TXB_ID  				/* 운영교과서ID */
		      ,M.LRMP_NOD_ID  				/* 학습맵노드ID */
		      ,M.TCR_REG_CTN_ID AS LRN_ATV_ID  				/* 학습활동ID */
		      ,M.LRN_STP_ID  				/* 학습단계ID */
			  ,S.LRN_STP_DV_CD				/* 학습단계 구분코드 */
			  ,S.LRN_STP_NM					/* 학습단계명 */
		      ,'' AS CTN_CD  					/* LCMS 학습활동코드(varchar(10)) */
		      ,C.TCR_REG_CTN_NM AS LRN_ATV_NM  				/* 문항아이디 or 파일명 */
		      ,C.TP_CD AS CTN_TP_CD  				/* 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
		      ,M.USE_YN  					/* 사용여부 */
		      ,M.RCSTN_ORDN  				/* 재구성순서 */
		      ,'' AS EV_ID  					/* 평가ID */
		      ,S.SRT_ORDN
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		WHERE M.OPT_TXB_ID = #{optTxbId} 
		AND M.LRMP_NOD_ID = #{lrmpNodId} 
		AND M.DEL_YN = 'N'
		AND M.USE_YN = 'Y') ATV
		ORDER BY ATV.SRT_ORDN ASC, ATV.RCSTN_ORDN ASC 

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnAtvList */
    </select>
	-->
    <!-- 학습활동 교과학습상세 조회(Summary) -->
    <!-- 
    <select id="selectTxbThbInfo" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnThbDto">
        SELECT E.TXB_ID
              ,IFNULL(F2.CDN_PTH_NM, '')       AS PC_CDN_PTH_NM
              ,IFNULL(F2.ALTN_TXT_CN, '')      AS PC_ALTN_TXT_CN
              ,IFNULL(G2.CDN_PTH_NM, '')       AS MO_CDN_PTH_NM
              ,IFNULL(G2.ALTN_TXT_CN, '')      AS MO_ALTN_TXT_CN
              ,IFNULL(H2.CDN_PTH_NM, '')       AS TA_CDN_PTH_NM
              ,IFNULL(H2.ALTN_TXT_CN, '')      AS TA_ALTN_TXT_CN
        FROM LMS_LRM.CM_OPT_TXB E /* CM_운영교과서 */
             LEFT JOIN LMS_CMS.BC_TXB_ETC_FLE_MPN F1 /* BC_교과서기타파일매핑 */
                    ON E.TXB_ID = F1.TXB_ID
                   AND F1.TML_TP_CD = 'PC'
             LEFT JOIN LMS_CMS.BC_TXB_UPL_FLE F2 /* BC_교과서업로드파일 */
                    ON F1.UPL_FLE_ID = F2.UPL_FLE_ID
                   AND F2.DEL_YN = 'N'
             LEFT JOIN LMS_CMS.BC_TXB_ETC_FLE_MPN G1 /* BC_교과서기타파일매핑 */
                    ON E.TXB_ID = G1.TXB_ID
                   AND G1.TML_TP_CD = 'MO'
             LEFT JOIN LMS_CMS.BC_TXB_UPL_FLE G2 /* BC_교과서업로드파일 */
                    ON G1.UPL_FLE_ID = G2.UPL_FLE_ID
                   AND G2.DEL_YN = 'N'
             LEFT JOIN LMS_CMS.BC_TXB_ETC_FLE_MPN H1 /* BC_교과서기타파일매핑 */
                    ON E.TXB_ID = H1.TXB_ID
                   AND H1.TML_TP_CD = 'TA'
             LEFT JOIN LMS_CMS.BC_TXB_UPL_FLE H2 /* BC_교과서업로드파일 */
                    ON H1.UPL_FLE_ID = H2.UPL_FLE_ID
                   AND H2.DEL_YN = 'N'
        WHERE E.OPT_TXB_ID = #{optTxbId}

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectTxbThbInfo */
    </select>
    -->
    <!-- 학습활동 교과학습상세 조회(Summary) v3.1대응용
         -차시 썸네일정보및 학습목표취득 
    -->
    <select id="selectTxbThbInfo2" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnThbDto">
        SELECT R.LRMP_NOD_ID
              ,IFNULL(B.FLE_PTH_NM, '')       AS PC_CDN_PTH_NM
              ,IFNULL(B.ALTN_TXT_CN, '')      AS PC_ALTN_TXT_CN
              ,IFNULL(C.FLE_PTH_NM, '')       AS TA_CDN_PTH_NM
              ,IFNULL(C.ALTN_TXT_CN, '')      AS TA_ALTN_TXT_CN
              ,IFNULL(A.LRN_GOAL_CN, '')      AS LRN_GOAL_CN
          FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R
            LEFT JOIN LMS_CMS.BC_LRMP_NOD_THB_MPN A /* BC_학습맵노드썸네일매핑 */
                   ON R.LRMP_NOD_ID = A.LRMP_NOD_ID
            LEFT JOIN LMS_CMS.BC_UPL_FLE B /* BC_업로드파일 - PC용*/
                   ON A.PC_THB_UPL_ID = B.UPL_FLE_ID
                  AND B.DEL_YN = 'N'
            LEFT JOIN LMS_CMS.BC_UPL_FLE C /* BC_업로드파일 - 태블릿용*/
                   ON A.TA_THB_UPL_ID = C.UPL_FLE_ID
                  AND C.DEL_YN = 'N'
        WHERE R.LRMP_NOD_ID = #{lrmpNodId}
        LIMIT 1
        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectTxbThbInfo2 */
    </select>

    <!-- 학습활동 교과학습상세 조회(Summary) -->
    <select id="selectLrnAtvSum" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvSumDto">
        SELECT
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LRMP_NOD_ID  /* LCMS교과서_지식맵_키 */
              ,A.URNK_LRMP_NOD_ID  /* LCMS교과서_지식맵_부모_노드_키 */
              ,IFNULL(D.RCSTN_NO,'')             AS NOD_NO /* 대단원NO */
              ,D.LRMP_NOD_NM                                       AS LRMP_NOD_NM1  /* 지식맵노드명(대단원) */
              ,C.LRMP_NOD_NM                                       AS LRMP_NOD_NM2  /* 지식맵노드명(중단원) */
              ,B.LRMP_NOD_NM                                       AS LRMP_NOD_NM3  /* 지식맵노드명(소단원) */
              ,A.LRMP_NOD_NM                                       AS LRMP_NOD_NM4  /* 지식맵노드명(차시) */
              ,A.USE_YN   /* 사용여부 */
              ,A.LCKN_YN  /* 잠금여부 */
              ,A.TXB_STR_PGE_NO                                    AS TXB_STR_PGE_NO /* 교과서PDF시작페이지No */
              ,A.TXB_END_PGE_NO                                    AS TXB_END_PGE_NO /* 교과서PDF종료페이지No */
              ,A.WKB_STR_PGE_NO                                    AS WKB_STR_PGE_NO /* 익힘책PDF시작페이지No */
              ,A.WKB_END_PGE_NO                                    AS WKB_END_PGE_NO /* 익힘책PDF종료페이지No */
              ,0 AS ALL_LRN_ATV_CNT /* 총학습활동건수*/
              ,0 AS LRN_ATV_FIN_CNT /* 학습활동완료건수 */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성-차시 */
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B /* TL_교과학습노드재구성- 소단원 */
                  ON A.OPT_TXB_ID = B.OPT_TXB_ID
                  AND A.URNK_LRMP_NOD_ID = B.LRMP_NOD_ID
                  AND B.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C /* TL_교과학습노드재구성- 중단원 */
                   ON B.OPT_TXB_ID = C.OPT_TXB_ID
                  AND B.URNK_LRMP_NOD_ID = C.LRMP_NOD_ID
                  AND C.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D /* TL_교과학습노드재구성- 대단원 */
                  ON C.OPT_TXB_ID = D.OPT_TXB_ID
                  AND C.URNK_LRMP_NOD_ID = D.LRMP_NOD_ID
                  AND D.USE_YN = 'Y'
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND A.LRMP_NOD_ID = #{lrmpNodId}  /* LCMS교과서_지식맵_키 */
        AND A.USE_YN = 'Y'  /* 사용여부 */
        LIMIT 1

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnAtvSum */
    </select>
    
    <!-- 학습활동 총 개수 -->
    <select id="selectLrnAtvTotCnt" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="int">
        SELECT COUNT(*)
              	FROM
              	(SELECT XA.LRN_ATV_ID
                FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN XA /* TL_교과학습활동재구성 */
                     INNER JOIN LMS_CMS.BC_LRN_STP XB /* BC_학습단계 */
                           ON XA.LRMP_NOD_ID = XB.LRMP_NOD_ID
                           AND XA.LRN_STP_ID = XB.LRN_STP_ID
                WHERE XA.OPT_TXB_ID = #{optTxbId}
                AND XA.LRMP_NOD_ID = #{lrmpNodId}
                AND XA.USE_YN = 'Y'
                AND XB.LRN_STP_DV_CD = 'CL' /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EV: 평가) */
                AND XB.DEL_YN = 'N'
                
                UNION ALL
                
               SELECT M.TCR_REG_CTN_ID AS LRN_ATV_ID
				FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
				INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
					ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
				INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
					ON M.OPT_TXB_ID = R.OPT_TXB_ID
					AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
				INNER JOIN LMS_CMS.bc_lrn_stp S
					ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
					AND M.LRN_STP_ID = S.LRN_STP_ID
					AND S.DEL_YN = 'N'
				WHERE M.OPT_TXB_ID = #{optTxbId}
				AND M.LRMP_NOD_ID = #{lrmpNodId}
				AND M.DEL_YN = 'N'
				AND M.USE_YN = 'Y')ATV

        /* 교과학습 김형준 TlSbcLrnStu-Mapper.xml - selectLrnAtvTotCnt */
    </select>
    
    <!-- 학습활동 완료 개수 -->
    <select id="selectLrnAtvClCnt" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="int">
        SELECT COUNT(*) /* 학습활동완료건수 */
              	FROM
              	(SELECT XA.LRN_ATV_ID
                FROM 
					(SELECT 
					    	R.OPT_TXB_ID,
					        R.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        R.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        R.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        R.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'N' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
					    WHERE R.OPT_TXB_ID = #{optTxbId}
					      AND R.LRMP_NOD_ID = #{lrmpNodId}
					      
						UNION ALL
						
					    SELECT 
					        R.OPT_TXB_ID,
					        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        TTRCM.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        TTRCM.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        TTRCM.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'Y' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
					        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
					        and ttrcm.del_yn = 'N'
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
					        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
					    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
					      AND TTRCM.LRMP_NOD_ID = #{lrmpNodId}
					      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
					      AND TTRC.LRN_ATV_ID IS NOT NULL) XA /* TL_교과학습활동재구성 */
                     INNER JOIN LMS_CMS.BC_LRN_STP XB /* BC_학습단계 */
                           ON XA.LRMP_NOD_ID = XB.LRMP_NOD_ID
                           AND XA.LRN_STP_ID = XB.LRN_STP_ID
                     INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_ST XC
                           ON XC.OPT_TXB_ID = XA.OPT_TXB_ID
                           AND XC.LRMP_NOD_ID = XA.ORGL_LRMP_NOD_ID
                           AND XC.LRN_ATV_ID = XA.LRN_ATV_ID
                           AND XC.LRN_ST_CD = 'CL'  /* 학습상태 학습완료(30) */
                WHERE XA.OPT_TXB_ID = #{optTxbId}
                AND XA.LRMP_NOD_ID = #{lrmpNodId}
                AND XA.USE_YN = 'Y'
                AND XB.LRN_STP_DV_CD = 'CL' /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EX: 평가) */
                AND XB.DEL_YN = 'N'
                AND XC.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/ 
                
                UNION ALL
      			 
      			 SELECT M.TCR_REG_CTN_ID AS LRN_ATV_ID
					FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
					INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
						ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
						AND C.TP_CD <![CDATA[<>]]> 'AT'
					INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
						ON M.OPT_TXB_ID = R.OPT_TXB_ID
						AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
					INNER JOIN LMS_CMS.bc_lrn_stp S
						ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
						AND M.LRN_STP_ID = S.LRN_STP_ID
						AND S.DEL_YN = 'N'
					INNER JOIN LMS_LRM.tl_tcr_reg_ctn_st T
						ON M.OPT_TXB_ID = T.OPT_TXB_ID
						AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
						AND T.LRN_USR_ID = #{lrnUsrId}
						AND T.LRN_ST_CD = 'CL'
					WHERE M.OPT_TXB_ID = #{optTxbId}
					AND M.LRMP_NOD_ID = #{lrmpNodId}
					AND M.DEL_YN = 'N'
					AND M.USE_YN = 'Y')ATV_ST

        /* 교과학습 김형준 TlSbcLrnStu-Mapper.xml - selectLrnAtvClCnt */
    </select>
    
    <!-- 학습활동 교과학습 차시별 학습활동목록 조회 v3.1대응 -->
    <select id="selectLrnTcAtvList" parameterType="Map" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTcAtvDto">
        SELECT *
        FROM
        (SELECT A.LRMP_NOD_ID
              ,A.LRN_STP_ID /* 학습단계ID */
              ,IFNULL(B.LRN_STP_CD, B.LRN_STP_ID)                            AS LRN_STP_CD  /* 학습단계코드 */
              ,B.LRN_STP_NM /* 학습단계명 */
              ,B.SRT_ORDN
              ,A.RCSTN_ORDN
              ,A.CTN_TP_CD
              ,A.CTN_CD /* 콘텐츠코드 */
              ,A.LRN_ATV_ID /* 학습활동ID */
              ,A.LRN_ATV_NM /* 학습활동명 */
              ,IFNULL(C.LRN_ST_CD, 'NL')                                       AS LRN_ST_CD /* 학습상태코드*/
              ,C.MDF_DTM
              ,'N' AS TCR_CTN_YN
              <if test='srchCurrAtvYn != "Y"'>
                ,IF(E.CDN_PTH_NM IS NULL, '', CONCAT(#{objUrl}, E.CDN_PTH_NM)) AS LRN_ATV_THB_CDN_PATH  /* 콘텐츠썸네일CDN */
              </if>
              <if test='srchCurrAtvYn == "Y"'>
                ,IF(C.LRN_USR_ID IS NULL, 'N', 'Y')                            AS CURR_ATV_YN
              </if>
        FROM (
	        	SELECT 
			    	R.OPT_TXB_ID,
			        R.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        R.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        R.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'N' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
			    WHERE R.OPT_TXB_ID = #{param.optTxbId}
			      AND R.LRMP_NOD_ID = #{param.lrmpNodId}
			      
				UNION ALL
				
			    SELECT 
			        R.OPT_TXB_ID,
			        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        TTRCM.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        TTRCM.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        TTRCM.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'Y' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
			        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
			        and ttrcm.del_yn = 'N'
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
			        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
			    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
			      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
			      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
			      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
             INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                     ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C /* TL_교과학습활동상태 */
                    ON A.OPT_TXB_ID = C.OPT_TXB_ID
                   AND A.ORGL_LRMP_NOD_ID = C.LRMP_NOD_ID
                   AND A.LRN_ATV_ID = C.LRN_ATV_ID
                   AND C.LRN_USR_ID = #{param.lrnUsrId}
             <if test='srchCurrAtvYn != "Y"'>
               LEFT JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                         ON A.LRN_ATV_ID = D.LRN_ATV_ID
                    AND D.USE_YN= 'Y'
                    AND D.DEL_YN= 'N'
               LEFT JOIN LMS_CMS.BC_CTN_MTD_UPL_FLE E /* BC_콘텐츠메타데이터업로드파일 */
                         ON E.CTN_META_DATA_ID = D.CTN_META_DATA_ID
                    AND E.DEL_YN = 'N'
                    AND E.FLE_TP_CD = 'IM'
             </if>
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        AND A.USE_YN  = 'Y'
        AND A.LRMP_NOD_ID = #{param.lrmpNodId}
        AND B.LRN_STP_DV_CD = 'CL' /* 학습단계구분코드=개념학습, 평가 */
        AND B.DEL_YN = 'N'
        
        UNION ALL
        
        SELECT
        	M.LRMP_NOD_ID
			,M.LRN_STP_ID /* 학습단계ID */
            ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID)                            AS LRN_STP_CD  /* 학습단계코드 */
            ,S.LRN_STP_NM /* 학습단계명 */
            ,S.SRT_ORDN
            ,M.RCSTN_ORDN
            ,C.TP_CD AS CTN_TP_CD
            ,'' AS CTN_CD /* 콘텐츠코드 */
            ,M.TCR_REG_CTN_ID AS LRN_ATV_ID /* 학습활동ID */
            ,C.TCR_REG_CTN_NM AS LRN_ATV_NM /* 학습활동명 */
            ,IFNULL(T.LRN_ST_CD, 'NL')                                       AS LRN_ST_CD /* 학습상태코드*/
            ,T.MDF_DTM
            ,'Y' AS TCR_CTN_YN
            <if test='srchCurrAtvYn != "Y"'>
            ,'' AS LRN_ATV_THB_CDN_PATH  /* 콘텐츠썸네일CDN */
            </if>
            <if test='srchCurrAtvYn == "Y"'>
            ,IF(T.LRN_USR_ID IS NULL, 'N', 'Y')                            AS CURR_ATV_YN
            </if>
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
			AND C.TP_CD <![CDATA[<>]]> 'AT'
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
			ON M.OPT_TXB_ID = T.OPT_TXB_ID
			AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
			AND T.LRN_USR_ID = #{param.lrnUsrId}
		WHERE M.OPT_TXB_ID = #{param.optTxbId}
		AND M.LRMP_NOD_ID = #{param.lrmpNodId}
		AND M.DEL_YN = 'N'
		AND M.USE_YN = 'Y') ATV
        <if test='srchCurrAtvYn != "Y"'>
          ORDER BY ATV.SRT_ORDN ASC, ATV.LRN_STP_ID ASC, ATV.RCSTN_ORDN ASC, ATV.LRN_ATV_ID ASC
        </if>
        <if test='srchCurrAtvYn == "Y"'>
          ORDER BY ATV.MDF_DTM DESC LIMIT 1
        </if>

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnTcAtvList */
    </select>


    <!-- 학습활동 상세 평가(Summary) -->
    <select id="selectLrnAtvEvSum" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="Map">
        SELECT 
               A.OPT_TXB_ID  /* 운영교과서ID */
              ,A.LLU_NOD_ID  /* 대단원노드ID */
              ,A.LRMP_NOD_ID  /* LCMS교과서_지식맵_키 */
               ,IFNULL(D.RCSTN_NO,'')              AS NOD_NO /* 대단원NO */
              ,D.LRMP_NOD_NM AS LRMP_NOD_NM1  /* 지식맵노드명(대단원) */
              ,A.USE_YN  /* 사용여부 */
              ,E.LRN_ATV_ID /* 학습활동ID */
              ,E.LRN_ATV_NM /* 학습활동명 */
              ,E.EV_ID
              ,H.EV_DTL_DV_CD
              ,E.RCSTN_ORDN
              ,IFNULL(F.LRN_STP_CD, F.LRN_STP_ID)                       AS LRN_STP_CD /* 학습단계코드 */
              ,F.LRN_STP_NM /* 학습단계명 */
              ,IFNULL(G.LRN_ST_CD, 'NL')                                AS LRN_ST_CD /* 학습상태코드 */
              ,IFNULL(H.FNL_QST_CNT, 0)                                 AS EV_TOT_CNT /* 총문제수 */
              ,IFNULL(H.XPL_TM_SETM_YN, 'N')                            AS XPL_TM_SETM_YN /* 풀이시간설정여부 */
              ,CAST(IFNULL(H.XPL_TM_SCNT, 0)/60 AS UNSIGNED)            AS XPL_TM_MIN /* 풀이시간(분) */
              ,IFNULL((SELECT XA.CANS_CNT
                       FROM LMS_LRM.EA_EV_RS XA /* EA_평가결과 */
                       WHERE XA.EV_ID = E.EV_ID
                           AND XA.EV_CMPL_YN = 'Y' /* 평가완료여부 */
                           AND XA.USR_ID = G.LRN_USR_ID),0)             AS EV_FIN_CNT /* 학습활동완료건수*/
               ,G.EXTR_EV_ID  /* 추가평가ID */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성-차시 */
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D /* TL_교과학습노드재구성- 대단원 */
                  ON A.OPT_TXB_ID = D.OPT_TXB_ID
                  AND A.LLU_NOD_ID = D.LRMP_NOD_ID
                  AND D.DPTH = 1
                  AND D.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN E /* TL_교과학습활동재구성 */
                  ON E.OPT_TXB_ID = A.OPT_TXB_ID
                  AND E.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND E.USE_YN = 'Y'
             INNER JOIN LMS_CMS.BC_LRN_STP F /* BC_학습단계 */
                  ON E.LRMP_NOD_ID = F.LRMP_NOD_ID
                  AND E.LRN_STP_ID = F.LRN_STP_ID
                  AND F.LRN_STP_DV_CD = 'EX' /* 학습단계 구분(CL : 개념학습, WB: 익힘책,  EX: 평가) */
                  AND F.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST G  /* TL_교과학습활동상태 */
                  ON G.OPT_TXB_ID = E.OPT_TXB_ID
                  AND G.LRMP_NOD_ID = E.LRMP_NOD_ID
                  AND G.LRN_ATV_ID = E.LRN_ATV_ID
                  AND G.LRN_USR_ID = #{lrnUsrId}
             LEFT JOIN LMS_LRM.EA_EV H /* EA_평가 */
                  ON H.EV_ID = E.EV_ID
                  AND H.OPT_TXB_ID = E.OPT_TXB_ID
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND A.LRMP_NOD_ID = #{lrmpNodId} /* LCMS교과서_지식맵_키 */
        AND A.USE_YN = 'Y'  /* 사용여부 */
        AND A.DPTH = 4
        ORDER BY E.RCSTN_ORDN ASC

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnAtvEvSum */

      </select>

    <!-- 학습활동 상세 익힘책(Summary) -->
    <select id="selectLrnAtvWkbSum" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="Map">
        SELECT
               MAX(A.OPT_TXB_ID) as OPT_TXB_ID  /* 운영교과서ID */
              ,MAX(A.LLU_NOD_ID) as LLU_NOD_ID  /* 대단원노드ID */
              ,A.LRMP_NOD_ID  /* LCMS교과서_지식맵_키 */
              ,MAX(IFNULL(D.RCSTN_NO,''))           AS NOD_NO /* 대단원NO */
              ,MAX(D.LRMP_NOD_NM) AS LRMP_NOD_NM1  /* 지식맵노드명(대단원) */
              -- ,A.USE_YN  /* 사용여부 */
              -- ,E.LRN_ATV_ID /* 학습활동ID */
              -- ,E.LRN_ATV_NM /* 학습활동명 */
              -- ,E.EV_ID
              -- ,E.RCSTN_ORDN
              -- ,F.LRN_STP_CD /* 학습단계코드 */
              -- ,F.LRN_STP_NM /* 학습단계명 */
              -- ,IFNULL(G.LRN_ST_CD, 'NL')                             AS LRN_ST_CD /* 학습상태코드 미학습(NL) */
              ,COUNT(1)                                                 AS WKB_TOT_CNT /* 총문제수 */
              ,SUM(IF(G.LRN_ST_CD = 'CL', 1, 0))                        AS WKB_FIN_CNT /* 학습활동완료건수*/
              ,MAX(A.WKB_STR_PGE_NO) as WKB_STR_PGE_NO /* 익힘책시작페이지번호 */
              ,MAX(A.WKB_END_PGE_NO) as WKB_END_PGE_NO /* 익힘책종료페이지번호 */
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성-차시 */
             LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D /* TL_교과학습노드재구성- 대단원 */
                  ON A.OPT_TXB_ID = D.OPT_TXB_ID
                  AND A.URNK_LRMP_NOD_ID = D.LRMP_NOD_ID
                  AND D.DPTH = 1
                  AND D.USE_YN = 'Y'
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN E /* TL_교과학습활동재구성 */
                  ON E.OPT_TXB_ID = A.OPT_TXB_ID
                  AND E.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND E.USE_YN = 'Y'
             INNER JOIN LMS_CMS.BC_LRN_STP F /* BC_학습단계 */
                   ON E.LRMP_NOD_ID = F.LRMP_NOD_ID
                   AND E.LRN_STP_ID = F.LRN_STP_ID
                   AND F.LRN_STP_DV_CD = 'WB' /* 학습단계 구분(WB: 익힘책) */
                   AND F.DEL_YN = 'N' 
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST G  /* TL_교과학습활동상태 */
                  ON G.OPT_TXB_ID = E.OPT_TXB_ID
                  AND G.LRMP_NOD_ID = E.LRMP_NOD_ID
                  AND G.LRN_ATV_ID = E.LRN_ATV_ID
                  AND G.LRN_USR_ID = #{lrnUsrId}
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND A.LRMP_NOD_ID = #{lrmpNodId} /* LCMS교과서_지식맵_키 */
        AND A.USE_YN = 'Y'  /* 사용여부 */
        AND A.DPTH = 4
        group by A.LRMP_NOD_ID
        ORDER BY A.RCSTN_ORDN ASC

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnAtvWkbSum */
      </select>

    <!-- 학습활동 평가정보조회(v2.3대응) -->
    <select id="selectLrnEvInfo" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvEvDto">
        SELECT A.LRMP_NOD_ID                              /* 차시노드ID */
              ,A.LRN_ATV_ID                               /* 활동ID */
              ,B.EV_ID                                    /* 평가ID */
              ,B.EV_DTL_DV_CD                             /* 평가상세구분코드 */
              ,C2.CM_CD_NM               AS EV_DTL_DV_NM  /* 평가상세구분코드명 */
              ,B.EV_NM                                    /* 평가명 */
              ,B.FNL_QST_CNT                              /* 최종문제수 */
              ,B.LCKN_YN                                  /* 잠금여부 */
              ,B.RTXM_PMSN_YN                             /* 재응시허용여부 */
              ,B.XPL_TM_SETM_YN                            /* 풀이시간설정여부 */
              ,CAST(IFNULL(B.XPL_TM_SCNT, 0)/60 AS UNSIGNED) AS XPL_TM_SCNT  /* 풀이시간초수 */
              ,IF(C.EV_CMPL_YN = 'Y', 'Y', IF(IFNULL(C.TXM_STR_YN,'N') = 'Y','D','N'))                     AS EV_CMPL_YN   /* 평가완료여부 */
              ,IFNULL(D.EXTR_EV_ID,'')                       AS EXTR_EV_ID   /*추가평가ID*/
              ,IFNULL(D.LRN_ST_CD, 'NL')					AS LRN_ST_CD /*학습상태코드*/
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A  /* TL_교과학습활동재구성 */
             INNER JOIN LMS_LRM.EA_EV B      /* EA_평가 */
                   ON A.OPT_TXB_ID = B.OPT_TXB_ID
                   AND A.EV_ID = B.EV_ID
             LEFT JOIN LMS_LRM.EA_EV_RS C    /* EA_평가결과 */
                  ON B.EV_ID = C.EV_ID
                  AND C.USR_ID = #{lrnUsrId} /* 학생ID */
             LEFT JOIN LMS_LRM.CM_CM_CD C1
                  ON C1.CM_CD ='EV_DTL_DV_CD' 
                  AND C1.LMS_USE_YN = 'Y' 
                  AND C1.DEL_YN='N'
                  AND NOW() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
             LEFT JOIN LMS_LRM.CM_CM_CD C2
                  ON C1.CM_CD = C2.URNK_CM_CD
                  AND C2.LMS_USE_YN = 'Y' 
                  AND C2.DEL_YN='N'
                  AND NOW() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                  AND C2.CM_CD = B.EV_DTL_DV_CD
             LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D
                  ON A.OPT_TXB_ID = D.OPT_TXB_ID
                  AND A.LRMP_NOD_ID = D.LRMP_NOD_ID
             	   AND A.LRN_ATV_ID = D.LRN_ATV_ID
             	   AND D.LRN_USR_ID = #{lrnUsrId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.LRMP_NOD_ID = #{lrmpNodId}
        AND A.CTN_TP_CD = 'EX'
        AND A.USE_YN = 'Y'
        AND B.DEL_YN = 'N'
        AND B.USE_YN = 'Y'
        ORDER BY A.RCSTN_ORDN ASC, A.LRMP_NOD_ID ASC
        LIMIT 1

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnEvInfo */
    </select>
    <!-- 학습활동 평가문항목록조회(v2.3대응) -->
    <select id="selectLrnEvQtmList" parameterType="Map" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvEvQtmDto">
        SELECT A.EV_ID           /* 평가ID */
              ,B.QTM_ID          /* 문항ID */
              ,B.QTM_ORDN        /* 문항순서 */
              ,C.USR_ID          /* 사용자ID */
              ,IFNULL(C.CANS_YN, '') AS CANS_YN  /* 정답여부 */
        FROM LMS_LRM.EA_EV A /* EA_평가 */
             INNER JOIN LMS_LRM.EA_EV_QTM B /* EA_평가문항 */
                   ON A.EV_ID = B.EV_ID
                   AND B.DEL_YN = 'N'
             LEFT JOIN LMS_LRM.EA_EV_QTM_ANW C /* EA_평가문항답변 */
                  ON B.EV_ID = C.EV_ID
                  AND B.QTM_ID = C.QTM_ID
                  AND C.USR_ID = #{lrnUsrId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
        AND A.DEL_YN = 'N'
        AND A.USE_YN = 'Y'
        AND A.EV_ID = #{evId}
        ORDER BY B.QTM_ORDN ASC, B.QTM_ID ASC

        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnEvQtmList */
    </select>

    <!-- 최근 학습활동ID 취득처리 -->
    <select id="selectLrnAtvStChk" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStChkDto">
		SELECT
                 ATV.OPT_TXB_ID
                ,ATV.LRMP_NOD_ID
                ,ATV.LRMP_NOD_NM
                ,ATV.URNK_LRMP_NOD_ID
                ,ATV.URNK_LRMP_NOD_NM
                ,ATV.DPTH
                ,SUM(IF(ATV.DPTH  <![CDATA[>]]>   1 AND ATV.OPT_TXB_ID IS NOT NULL, 1, 0) )         AS CNT_TOT
                ,SUM(IF(ATV.DPTH  <![CDATA[>]]>   1 AND ATV.LRN_ST_CD = 'CL', 1, 0))                AS CNT_CL /* 학습완료 */
                ,SUM(IF(ATV.DPTH  <![CDATA[>]]>   1 AND ATV.LRN_ST_CD  <![CDATA[<>]]>  'CL', 1, 0)) AS CNT_NL
                ,MAX(ATV.LLU_ORDN) as LLU_ORDN
                ,MAX(ATV.TC_ORDN) as TC_ORDN
                ,MAX(ATV.ATV_ORDN) as ATV_ORDN
                ,ATV.LCKN_YN AS LCKN_YN
 		FROM (SELECT R.OPT_TXB_ID
                ,R.LRMP_NOD_ID
                ,R.LRMP_NOD_NM
                ,IFNULL(S.LRMP_NOD_ID,'')                                                      AS URNK_LRMP_NOD_ID
                ,IFNULL(S.LRMP_NOD_NM,'')                                                      AS URNK_LRMP_NOD_NM
                ,R.DPTH
                ,B.LRN_ST_CD
                ,A.RCSTN_ORDN 	AS ATV_ORDN
                ,R.RCSTN_ORDN		AS TC_ORDN
                ,S.RCSTN_ORDN		AS LLU_ORDN
                ,R.LCKN_YN AS LCKN_YN
        		FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN R  /* TL_교과학습노드재구성 -차시노드 */
            INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN S   /* TL_교과학습노드재구성 -대단원노드 */
                    ON R.OPT_TXB_ID = S.OPT_TXB_ID
                   AND R.LLU_NOD_ID = S.LRMP_NOD_ID
                   AND S.USE_YN = 'Y'
                   AND S.DPTH = 1
                   AND S.LU_EPS_YN = 'Y'
            LEFT JOIN (
            			SELECT 
					    	R.OPT_TXB_ID,
					        R.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        R.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        R.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        R.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'N' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
					    WHERE R.OPT_TXB_ID = #{optTxbId}
					      
						UNION ALL
						
					    SELECT 
					        R.OPT_TXB_ID,
					        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        TTRCM.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        TTRCM.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        TTRCM.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'Y' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
					        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
					        and ttrcm.del_yn = 'N'
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
					        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
					    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
					      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
					      AND TTRC.LRN_ATV_ID IS NOT NULL) A  /* TL_교과학습활동재구성 */
                   ON  R.OPT_TXB_ID = A.OPT_TXB_ID
                  AND R.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND A.USE_YN = 'Y'
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST B /* TL_교과학습활동상태 */
                   ON A.OPT_TXB_ID = B.OPT_TXB_ID
                  AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
                  AND A.LRN_ATV_ID = B.LRN_ATV_ID
                  AND B.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/
        WHERE R.OPT_TXB_ID = #{optTxbId}
        AND R.DPTH = '4' /* 차시만 취득*/
        AND R.USE_YN = 'Y'
        AND R.LU_EPS_YN = 'Y'

		UNION ALL
			
		SELECT M.OPT_TXB_ID
            ,R.LRMP_NOD_ID
            ,R.LRMP_NOD_NM
            ,IFNULL(N.LRMP_NOD_ID,'')                                                      AS URNK_LRMP_NOD_ID
            ,IFNULL(N.LRMP_NOD_NM,'')                                                      AS URNK_LRMP_NOD_NM
            ,R.DPTH                                                                        AS DEPTH
            ,T.LRN_ST_CD
            ,M.RCSTN_ORDN	AS ATV_ORDN
            ,R.RCSTN_ORDN	AS TC_ORDN
            ,N.RCSTN_ORDN 	AS LLU_ORDN
		    ,R.LCKN_YN AS LCKN_YN
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
			AND C.TP_CD <![CDATA[<>]]> 'AT'
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN N   /* TL_교과학습노드재구성 -대단원노드 */
         ON R.OPT_TXB_ID = N.OPT_TXB_ID
         AND R.LLU_NOD_ID = N.LRMP_NOD_ID
         AND N.USE_YN = 'Y'
         AND N.DPTH = 1
         AND N.LU_EPS_YN = 'Y'
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
			ON M.OPT_TXB_ID = T.OPT_TXB_ID
			AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
			AND T.LRN_USR_ID = #{lrnUsrId}
		WHERE M.OPT_TXB_ID = #{optTxbId}
		AND M.DEL_YN = 'N'
		AND M.USE_YN = 'Y'
		AND R.USE_YN = 'Y'
      	AND R.LU_EPS_YN = 'Y'
		) ATV

        GROUP BY ATV.OPT_TXB_ID
        		,ATV.LRMP_NOD_ID
                ,ATV.LRMP_NOD_NM
                ,ATV.URNK_LRMP_NOD_ID
                ,ATV.URNK_LRMP_NOD_NM
                ,ATV.DPTH
                ,ATV.LCKN_YN
        ORDER BY LLU_ORDN ASC, ATV.URNK_LRMP_NOD_ID ASC, TC_ORDN ASC, ATV_ORDN ASC
        
        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnAtvStChk */
    </select>

    <!-- 마지막 학습노드ID 찾기 -->
    <select id="selectLastLrmpNodId" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="String">
       SELECT IFNULL(ATV.LRMP_NOD_ID,'') AS LRMP_NOD_ID
	 	FROM (SELECT B.MDF_DTM
		  		,IFNULL(B.LRN_TC_ID, A.LRMP_NOD_ID) AS LRMP_NOD_ID
			FROM (
            		SELECT 
					    	R.OPT_TXB_ID,
					        R.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        R.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        R.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        R.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'N' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
					    WHERE R.OPT_TXB_ID = #{optTxbId}
					      
						UNION ALL
						
					    SELECT 
					        R.OPT_TXB_ID,
					        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
							R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
					        R.LRN_ATV_ID,
					        TTRCM.LRN_STP_ID,
					        R.CTN_CD,
					        R.LRN_ATV_NM,
					        R.CTN_TP_CD,
					        TTRCM.USE_YN,
					        R.CLS_BRD_URL,
					        R.ORGL_LRN_STP_ID,
					        R.ORGL_ORDN,
					        TTRCM.RCSTN_ORDN,
					        R.EV_ID,
					        R.CRTR_ID,
					        R.CRT_DTM,
					        R.MDFR_ID,
					        R.MDF_DTM,
					        R.DB_ID,
					        'Y' AS TCR_CTN_YN
					    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
					        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
					        and ttrcm.del_yn = 'N'
					    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
					        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
					    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
					      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
					      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
      		INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_ST B /* TL_교과학습활동상태 */
                 ON B.OPT_TXB_ID = #{optTxbId}
                 AND A.ORGL_LRMP_NOD_ID = B.LRMP_NOD_ID
                 AND A.LRN_ATV_ID = B.LRN_ATV_ID
                 AND B.LRN_USR_ID = #{lrnUsrId} /* 학습사용자*/
      		WHERE A.OPT_TXB_ID = #{optTxbId}
      		AND A.USE_YN = 'Y'

      		UNION ALL

      		SELECT 	T.MDF_DTM
		  			,M.LRMP_NOD_ID
			FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
				ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
				AND C.TP_CD <![CDATA[<>]]> 'AT'
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn_st T
				ON T.OPT_TXB_ID = #{optTxbId}
				AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
				AND T.LRN_USR_ID = #{lrnUsrId}
			WHERE M.OPT_TXB_ID = #{optTxbId}
			AND M.DEL_YN = 'N'
			AND M.USE_YN = 'Y') ATV
	ORDER BY IFNULL( ATV.MDF_DTM, date_add(now(), interval -1 year)) DESC
	LIMIT 1
        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLastLrmpNodId */
    </select>

     <!-- 해당 차시의 학습활동상세정보 목록조회 -->
    <select id="selectLrnTocStatList" parameterType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnTocSrhDto" resultType="com.aidt.api.tl.sbclrn.dto.TlSbcLrnAtvStatDto">
    <if test='lrnStpDvCd == "CL"'>
		SELECT *
		FROM (SELECT 
                A.LRMP_NOD_ID	 /* 학습맵노드ID */
               ,A.LRN_ATV_ID	 /* 학습활동ID */
               ,A.CTN_CD	      /* 콘텐츠코드 */
               ,A.LRN_ATV_NM	 /* 학습활동명 */
               ,A.CTN_TP_CD	 /* 콘텐츠유형코드 */
               ,IFNULL(C.LRN_ST_CD, 'NL') AS LRN_ST_CD  /* 학습상태코드 */
               ,C1.CM_CD_NM               AS LRN_ST_NM  /* 학습상태명 */
               ,IFNULL(C.LRN_TM_SCNT, 0)  AS LRN_TM_SCNT  /* 학습시간(초) */
               ,DATE_FORMAT(C.MDF_DTM, '%m. %d.') AS LRN_DT /* 학습일자 */
               ,A.LRN_STP_ID  /* 학습단계ID */
               ,B.LRN_STP_NM  /* 학습단계명 */
               ,IFNULL(A.EV_ID, '')                AS EV_ID       /* 평가ID */
               ,B.SRT_ORDN
               ,A.RCSTN_ORDN
          FROM (
          		SELECT 
			    	R.OPT_TXB_ID,
			        R.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        R.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        R.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'N' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
			    WHERE R.OPT_TXB_ID = #{param.optTxbId}
			     AND R.LRMP_NOD_ID = #{param.lrmpNodId}
			      
				UNION ALL
				
			    SELECT 
			        R.OPT_TXB_ID,
			        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        TTRCM.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        TTRCM.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        TTRCM.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'Y' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
			        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
			        and ttrcm.del_yn = 'N'
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
			        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
			    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
			      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
			      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
			      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
               INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.LRN_STP_DV_CD = 'CL' /* CL=개념, WB=익힘, EX=평가*/
                    AND B.DEL_YN = 'N'
               LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C /* TL_교과학습활동상태 */
                    ON A.OPT_TXB_ID = C.OPT_TXB_ID
                    AND A.ORGL_LRMP_NOD_ID = C.LRMP_NOD_ID
                    AND A.LRN_ATV_ID = C.LRN_ATV_ID
                    AND C.LRN_USR_ID = #{param.lrnUsrId}
               LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
                    ON C1.URNK_CM_CD = 'LRN_ST_CD'
                    AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
                    AND C1.LMS_USE_YN = 'Y'
                    AND C1.DEL_YN = 'N'
                    AND C1.CM_CD = IFNULL(C.LRN_ST_CD, 'NL')
               LEFT JOIN LMS_LRM.CM_CM_CD C2 /* CM_공통코드 */
                    ON C1.URNK_CM_CD = C2.CM_CD
                    AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                    AND C2.LMS_USE_YN = 'Y'
                    AND C2.DEL_YN = 'N'
          WHERE A.OPT_TXB_ID = #{param.optTxbId}
          AND A.LRMP_NOD_ID = #{param.lrmpNodId}
          AND A.USE_YN = 'Y'
          
          UNION ALL
          
          SELECT
           		M.LRMP_NOD_ID	 /* 학습맵노드ID */
               ,M.TCR_REG_CTN_ID AS LRN_ATV_ID	 /* 학습활동ID */
               ,'' AS CTN_CD	      /* 콘텐츠코드 */
               ,C.TCR_REG_CTN_NM AS LRN_ATV_NM	 /* 학습활동명 */
               ,C.TP_CD AS CTN_TP_CD	 /* 콘텐츠유형코드 */
               ,IFNULL(T.LRN_ST_CD, 'NL') AS LRN_ST_CD  /* 학습상태코드 */
               ,C1.CM_CD_NM               AS LRN_ST_NM  /* 학습상태명 */
               ,IFNULL(T.LRN_TM_SCNT, 0)  AS LRN_TM_SCNT  /* 학습시간(초) */
               ,DATE_FORMAT(T.MDF_DTM, '%m. %d.') AS LRN_DT /* 학습일자 */
               ,M.LRN_STP_ID  /* 학습단계ID */
               ,S.LRN_STP_NM  /* 학습단계명 */
               ,''                AS EV_ID       /* 평가ID */
               ,S.SRT_ORDN
               ,M.RCSTN_ORDN
          FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
				INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
					ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
					AND C.TP_CD <![CDATA[<>]]> 'AT'
				INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
					ON M.OPT_TXB_ID = R.OPT_TXB_ID
					AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
				INNER JOIN LMS_CMS.bc_lrn_stp S
					ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
					AND M.LRN_STP_ID = S.LRN_STP_ID
					AND S.DEL_YN = 'N'
				LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
					ON M.OPT_TXB_ID = T.OPT_TXB_ID
					AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
					AND T.LRN_USR_ID = #{param.lrnUsrId}
				LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
                    ON C1.URNK_CM_CD = 'LRN_ST_CD'
                    AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
                    AND C1.LMS_USE_YN = 'Y'
                    AND C1.DEL_YN = 'N'
                    AND C1.CM_CD = IFNULL(T.LRN_ST_CD, 'NL')
               LEFT JOIN LMS_LRM.CM_CM_CD C2 /* CM_공통코드 */
                    ON C1.URNK_CM_CD = C2.CM_CD
                    AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                    AND C2.LMS_USE_YN = 'Y'
                    AND C2.DEL_YN = 'N'
				WHERE M.OPT_TXB_ID = #{param.optTxbId}
				AND M.LRMP_NOD_ID = #{param.lrmpNodId}
				AND M.DEL_YN = 'N'
				AND M.USE_YN = 'Y') ATV 
          ORDER BY ATV.SRT_ORDN ASC, ATV.RCSTN_ORDN ASC, ATV.LRN_ATV_ID ASC
    </if>
    <if test='lrnStpDvCd != "CL"'>
          SELECT 
                A.LRMP_NOD_ID	 /* 학습맵노드ID */
               ,A.LRN_ATV_ID	 /* 학습활동ID */
               ,A.CTN_CD	      /* 콘텐츠코드 */
               ,A.LRN_ATV_NM	 /* 학습활동명 */
               ,A.CTN_TP_CD	 /* 콘텐츠유형코드 */
               ,IFNULL(C.LRN_ST_CD, 'NL') AS LRN_ST_CD  /* 학습상태코드 */
               ,C1.CM_CD_NM               AS LRN_ST_NM  /* 학습상태명 */
               ,IFNULL(C.LRN_TM_SCNT, 0)  AS LRN_TM_SCNT  /* 학습시간(초) */
               ,DATE_FORMAT(C.MDF_DTM, '%m. %d.') AS LRN_DT /* 학습일자 */
               ,A.LRN_STP_ID  /* 학습단계ID */
               ,B.LRN_STP_NM  /* 학습단계명 */
               ,IFNULL(A.EV_ID, '')                AS EV_ID       /* 평가ID */
          FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
               INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.LRN_STP_DV_CD = #{lrnStpDvCd} /* CL=개념, WB=익힘, EX=평가*/
                    AND B.DEL_YN = 'N'
               LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C /* TL_교과학습활동상태 */
                    ON A.OPT_TXB_ID = C.OPT_TXB_ID
                    AND A.LRMP_NOD_ID = C.LRMP_NOD_ID
                    AND A.LRN_ATV_ID = C.LRN_ATV_ID
                    AND C.LRN_USR_ID = #{param.lrnUsrId}
               LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
                    ON C1.URNK_CM_CD = 'LRN_ST_CD'
                    AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
                    AND C1.LMS_USE_YN = 'Y'
                    AND C1.DEL_YN = 'N'
                    AND C1.CM_CD = IFNULL(C.LRN_ST_CD, 'NL')
               LEFT JOIN LMS_LRM.CM_CM_CD C2 /* CM_공통코드 */
                    ON C1.URNK_CM_CD = C2.CM_CD
                    AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                    AND C2.LMS_USE_YN = 'Y'
                    AND C2.DEL_YN = 'N'
          WHERE A.OPT_TXB_ID = #{param.optTxbId}
          AND A.LRMP_NOD_ID = #{param.lrmpNodId}
          AND A.USE_YN = 'Y'
          ORDER BY B.SRT_ORDN ASC, A.RCSTN_ORDN ASC, A.LRN_ATV_ID ASC
     </if>
        /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectLrnTocStatList */
    </select>

    <!-- 평가문항별정답여부 조회 v3.3대응 -->
    <select id="selectExQtmList" parameterType="Map" resultType="Map">
          SELECT A.EV_ID
                ,A.FNL_QST_CNT                            /* 총문제수 */
                ,B.QTM_ORDN
                ,IFNULL(C.CANS_CNT, 0)     AS CANS_CNT    /* 정답수 */
                ,IF(C.EV_CMPL_YN = 'Y', 'Y', IF(IFNULL(C.TXM_STR_YN,'N') = 'Y','D','N')) AS EV_CMPL_YN
                ,IFNULL(D.CANS_YN, 'N')    AS CANS_YN     /* 정답여부 */
          FROM LMS_LRM.EA_EV A /* EA_평가 */
               INNER JOIN LMS_LRM.EA_EV_QTM B /* EA_평가문항 */
                       ON A.EV_ID = B.EV_ID
                      AND B.DEL_YN = 'N'
               LEFT JOIN LMS_LRM.EA_EV_RS C /* EA_평가결과 */
                      ON A.EV_ID = C.EV_ID
                     AND C.USR_ID = #{param.lrnUsrId}
               LEFT JOIN LMS_LRM.EA_EV_QTM_ANW D  /* EA_평가문항답변 */
                      ON A.EV_ID = D.EV_ID
                     AND B.QTM_ID = D.QTM_ID
                     AND D.USR_ID = #{param.lrnUsrId}
          WHERE A.EV_ID = #{evId}
            AND A.OPT_TXB_ID = #{param.optTxbId}
            AND A.USE_YN = 'Y'
            AND A.DEL_YN = 'N'
          ORDER BY B.QTM_ORDN ASC, B.QTM_ID ASC          
          /* 교과학습 강성희 TlSbcLrnStu-Mapper.xml - selectExQtmList */
    </select>
</mapper>