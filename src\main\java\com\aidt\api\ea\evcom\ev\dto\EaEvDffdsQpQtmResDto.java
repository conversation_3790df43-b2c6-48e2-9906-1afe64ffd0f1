package com.aidt.api.ea.evcom.ev.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 13:58:오후 1:58
 * @modify date 2024-03-05 13:58:오후 1:58
 * @desc   평가 공통 - 문항리스트 조회 결과 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaEvDffdsQpQtmResDto {

    @Parameter(name="문항 ID")
    private String qtmId;

	@Parameter(name="토픽ID_지식맵")
	private String tpcId;    
	
	@Parameter(name="평가난이도구분코드")
	private String evDffdDvCd;
	
	@Parameter(name="평가난이도구분명")
	private String evDffdDvNm;
	
	@Parameter(name="평가난이도분포수")
	private int evDffdDsbCnt;

	@Parameter(name="난이도별 문항수")
	private long dffdQtmCnt;
	
	@Parameter(name="난이도별 문항 랜덤 순번")
	private long dffdRandRowNo;
}
