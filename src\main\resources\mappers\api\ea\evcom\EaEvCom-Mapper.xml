<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.evcom">

    <select id="selectComCodList" parameterType = "com.aidt.api.ea.evcom.dto.EaEvComDto" resultType="hashMap">
        SELECT
	        CM_CD                 AS cmCd,
	        CM_CD_NM              AS cmCdNm,
	        SRT_ORDN			  AS srtOrdn,
	        IFNULL(SETM_VL_1, '') AS setmVl1, -- 문제난이도 선택 기본값으로 사용 중
	        SETM_VL_2             AS setmVl2,
	        SETM_VL_3             AS setmVl3
        FROM LMS_LRM.CM_CM_CD
        WHERE URNK_CM_CD = #{urnkCmCd}
        AND LMS_USE_YN   = 'Y'
        AND DEL_YN       = 'N'
        ORDER BY SRT_ORDN


        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 공통 코드 조회 */
    </select>

    <select id="selectLuIdList" parameterType = "com.aidt.api.ea.evcom.dto.EaEvComDto" resultType="hashMap">

        SELECT
	          LRMP_NOD_ID                          AS value -- 학습맵 노드 ID
			, CASE WHEN IFNULL(RCSTN_NO, '') = '' THEN LRMP_NOD_NM
				   ELSE CONCAT(RCSTN_NO, '. ', LRMP_NOD_NM)
				   END 							   AS name 	-- 학습맵 노드 명
	        , RCSTN_ORDN                           AS rcstnOrdn -- 정렬순서
	        , LCKN_YN							   AS lcknYn -- 잠금여부
		,LU_NO_USE_YN AS luNoUseYn -- 단원 번호 사용 여부
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
        WHERE OPT_TXB_ID = #{optTxbId}
        AND IFNULL(URNK_LRMP_NOD_ID, '') = ''
        AND USE_YN = 'Y'
        AND LU_EPS_YN = 'Y'
        ORDER BY RCSTN_ORDN


        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 단원 목록 조회 */
    </select>

    <select id="selectMluSluLrmpNodList" parameterType = "com.aidt.api.ea.evcom.dto.EaEvComDto" resultType="hashMap">

		SELECT
	          LRMP_NOD_ID   	AS value -- 학습맵 노드 ID
            , URNK_LRMP_NOD_ID 	AS urnkLrmpNodId -- 상위 학습맵 노드 ID
	        , CASE
                  WHEN LU_NO_USE_YN='Y'
                  THEN concat(ROW_NUMBER() OVER (PARTITION BY DPTH, URNK_LRMP_NOD_ID, LU_NO_USE_YN ORDER BY RCSTN_ORDN),'. ',LRMP_NOD_NM)
                  ELSE LRMP_NOD_NM
              END 				AS name -- 학습맵 노드 명, 중단원은 RCSTN_NO 데이터 없는 듯
	        , RCSTN_ORDN    	AS rcstnOrdn -- 정렬순서
	        , LCKN_YN			AS lcknYn -- 잠금여부
	        , DPTH				AS dpth
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
        WHERE OPT_TXB_ID = #{optTxbId}
        AND LLU_NOD_ID = #{luLrmpNodId}
        AND USE_YN = 'Y'
        AND LU_EPS_YN = 'Y'
        AND DPTH IN (2, 3)
        ORDER BY DPTH, URNK_LRMP_NOD_ID, RCSTN_ORDN

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - selectMluSluLrmpNodList - 중단원/소단원 학습맵노드 목록 조회 */
    </select>


    <select id="selectMluLrmpNodId" parameterType = "com.aidt.api.ea.evcom.dto.EaEvComDto" resultType="hashMap">

        SELECT
	          LRMP_NOD_ID                          AS value -- 학습맵 노드 ID
            , CASE
                  WHEN LU_NO_USE_YN='Y'
                  THEN concat(ROW_NUMBER() OVER (PARTITION BY LU_NO_USE_YN ORDER BY RCSTN_ORDN),'. ',LRMP_NOD_NM)
                  ELSE LRMP_NOD_NM
              END AS name -- 학습맵 노드 명, 중단원은 RCSTN_NO 데이터 없는 듯
	        , RCSTN_ORDN                           AS rcstnOrdn -- 정렬순서
	        , LCKN_YN							   AS lcknYn -- 잠금여부
	        , LU_NO_USE_YN						   AS luNoUseYn -- 단원번호사용여부
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
        WHERE OPT_TXB_ID     = #{optTxbId}
        AND URNK_LRMP_NOD_ID = #{luLrmpNodId}
        AND USE_YN = 'Y'
        AND LU_EPS_YN = 'Y'
        ORDER BY RCSTN_ORDN


        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 중단원 목록 조회 */
    </select>

    <select id="selectTcIdList" parameterType="com.aidt.api.ea.evcom.dto.EaEvComDto"
            resultType="hashMap">

		SELECT
	          LRMP_NOD_ID AS value
            , CASE
                  WHEN LU_NO_USE_YN='Y'
                  THEN concat(ROW_NUMBER() OVER (PARTITION BY LU_NO_USE_YN ORDER BY RCSTN_ORDN),'. ',LRMP_NOD_NM)
                  ELSE LRMP_NOD_NM
              END AS name -- 학습맵 노드 명, 소단원은 RCSTN_NO 데이터 없는 듯
	         , RCSTN_ORDN AS rcstnOrdn -- 정렬순서
	         , LCKN_YN	  AS lcknYn -- 잠금여부
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
        WHERE OPT_TXB_ID = #{optTxbId}
        AND LLU_NOD_ID = #{luLrmpNodId}
        AND DPTH = 4
        AND USE_YN = 'Y'
        AND LU_EPS_YN = 'Y'
        ORDER BY RCSTN_ORDN

        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 차시 목록 조회 */
    </select>

    <select id="selectEvCmplYn" resultType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">


        SELECT
	           E.EV_ID
	         , E.EV_DV_CD
	         , E.EV_DTL_DV_CD
	         , IFNULL(E.RTXM_PMSN_YN, 'N') AS RTXM_PMSN_YN -- 재응시 허용여부
	         , IFNULL(ER.EV_CMPL_YN, 'N')  	AS EV_CMPL_YN   -- 평가 완료 여부
 			 , IFNULL(ERR.EV_CMPL_YN, 'N')	AS EV_RCMPL_YN  -- 재응시 완료 여부
 			 , IFNULL(ERR.RTXM_PN, 0) 	  	AS TXM_PN 		-- 응시회차(신규 : 0, 재응시 : 1 부터)
			 , IFNULL(ESNR.EV_CMPL_YN, 'X')					AS sppNtnCmplYn -- 보충심화 완료여부 (X는 보충심화 없다는 뜻)
			 , IFNULL(ESNR.TXM_STR_YN, 'X')					AS sppNtnStrYn  -- 보충심화 시작여부
		     , BT.SCHL_GRD_CD				AS schlGrdCd
		     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
					WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
					ELSE ''
					END AS sbjCd
			 , IFNULL((SELECT U.USR_TP_CD FROM LMS_LRM.CM_USR U WHERE U.USR_ID = #{usrId}), 'X') AS usrTpCd
        FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
        LEFT JOIN LMS_LRM.EA_EV_RS ER ON E.EV_ID = ER.EV_ID AND ER.USR_ID = #{usrId}
        LEFT JOIN (
        	SELECT
        	       ERR.EV_ID, ERR.USR_ID, ERR.EV_CMPL_YN, ERR.RTXM_PN
        	FROM LMS_LRM.EA_EV_RS_RTXM ERR
        	WHERE ERR.EV_ID = #{evId} AND ERR.USR_ID = #{usrId}
        	ORDER BY ERR.RTXM_PN DESC
        	LIMIT 1
        ) ERR ON ERR.EV_ID = ER.EV_ID AND ERR.USR_ID = ER.USR_ID
		LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS ESNR ON ESNR.EV_ID = ER.EV_ID AND ESNR.USR_ID = ER.USR_ID AND ESNR.TXM_PN = IFNULL(ERR.RTXM_PN, 0)
        WHERE E.EV_ID = #{evId}



        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가 결과 등록 전 재응시평가 확인  */

    </select>

    <!--  평가 결과 update -->
    <update id="updateEaEvRs" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
        INSERT INTO LMS_LRM.EA_EV_RS (
	          EV_ID, USR_ID, TXM_STR_YN, EV_CMPL_YN
	        <if test='"Y".equals(evCmplYn)'>
	        	, SMT_DTM
	        </if>
	        , EV_TM_SCNT, CANS_CNT, CANS_RT
	        , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        SELECT
			   E.EV_ID
	        , #{usrId}
	        , #{txmStrYn}
	        , #{evCmplYn}
	        <if test='"Y".equals(evCmplYn)'>
	            , now()
	        </if>
			, E.XPL_TM_SCNT_SUM
			, E.CANS_CNT_SUM
            , E.CANS_RT_SUM
	        , #{usrId}
	        , now()
	        , #{usrId}
	        , now()
	        , #{dbId}
		FROM (
				SELECT
					  E.EV_ID
					, SUM(EQA.XPL_TM_SCNT) AS XPL_TM_SCNT_SUM
					, SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT_SUM
		            , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 2) AS CANS_RT_SUM
				FROM LMS_LRM.EA_EV E
				LEFT JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = #{usrId}
				WHERE E.EV_ID = #{evId}
				GROUP BY E.EV_ID
		) E
        ON DUPLICATE KEY UPDATE
	        <if test='"Y".equals(evCmplYn)'>
	            SMT_DTM    = NOW(),
	        </if>
            EV_TM_SCNT = VALUES(EV_TM_SCNT),
            CANS_CNT   = VALUES(CANS_CNT),
            CANS_RT    = VALUES(CANS_RT),
	        TXM_STR_YN = #{txmStrYn},
	        EV_CMPL_YN = #{evCmplYn},
	        MDFR_ID    = #{usrId},
	        MDF_DTM    = NOW()

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가 결과 update */

    </update>

    <update id="updateEaEvRsRtxm" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">

        INSERT INTO LMS_LRM.EA_EV_RS_RTXM (
        	EV_ID, USR_ID, RTXM_PN, TXM_STR_YN, EV_CMPL_YN,
	        <if test='"Y".equals(evCmplYn)'>
	            SMT_DTM,
	        </if>
	        EV_TM_SCNT, CANS_CNT, CANS_RT,
	        CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
	    )
        SELECT
			   E.EV_ID
	        , #{usrId}
	        , #{txmPn}
	        , #{txmStrYn}
	        , #{evCmplYn}
	        <if test='"Y".equals(evCmplYn)'>
	            , now()
	        </if>
			, E.XPL_TM_SCNT_SUM
			, E.CANS_CNT_SUM
            , E.CANS_RT_SUM
	        , #{usrId}
	        , now()
	        , #{usrId}
	        , now()
	        , #{dbId}
		FROM (
				SELECT
					  E.EV_ID
					, SUM(EQA.XPL_TM_SCNT) AS XPL_TM_SCNT_SUM
					, SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT_SUM
		            , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 2) AS CANS_RT_SUM
				FROM LMS_LRM.EA_EV E
				LEFT JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = #{usrId} AND EQA.RTXM_PN = #{txmPn}
				WHERE E.EV_ID = #{evId}
				GROUP BY E.EV_ID
		) E
        ON DUPLICATE KEY UPDATE
	        <if test='"Y".equals(evCmplYn)'>
	            SMT_DTM    = NOW(),
	        </if>
            EV_TM_SCNT = VALUES(EV_TM_SCNT),
            CANS_CNT   = VALUES(CANS_CNT),
            CANS_RT    = VALUES(CANS_RT),
	        TXM_STR_YN = #{txmStrYn},
	        EV_CMPL_YN = #{evCmplYn},
	        MDFR_ID    = #{usrId},
	        MDF_DTM    = NOW()

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 재응시 평가 결과 update */

    </update>

    <select id="checkRecordEaEvQtmAnw" resultType="java.lang.Integer" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    SELECT COUNT(*) 
	      FROM LMS_LRM.EA_EV_QTM_ANW
	     WHERE EV_ID = #{evId}
		   AND QTM_ID = #{qtmId}
		   AND USR_ID = #{usrId}
		    /* 평가 공통 - EaEvCom-Mapper.xml - 평가 문항 답변 등록 select count */
	</select>
	
    <insert id="insertEaEvQtmAnw" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    INSERT INTO LMS_LRM.EA_EV_QTM_ANW (
	        EV_ID, QTM_ID, USR_ID
	        <if test='!"Y".equals(noteOnlyYn)'>
	            , SMT_ANW_VL, QST_XPL_CN
	            , CANS_YN, IANS_NTE_CANS_YN, XPL_TM_SCNT
	            , XPL_ST_CD, HNT_COFM_YN
	        </if>
	        , ANNX_FLE_ID
	        , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
	    )
	    VALUES (
	        #{evId},        -- 평가 ID
	        #{qtmId},       -- 문항 ID
	        #{usrId},       -- 사용자 ID
	        <if test='!"Y".equals(noteOnlyYn)'>
	            #{smtAnwVl},    -- 제출 답변 값
	            #{qstXplCn},    -- 문제 풀이 내용
	            IFNULL(#{cansYn}, 'N'), -- 정답 여부
	            IFNULL(#{cansYn}, 'N'), -- 오답 노트 정답 여부
	            IF(IFNULL(#{xplTmScnt},0) > 1800, 1800, #{xplTmScnt}),   -- 풀이 시간 초 수
	            IFNULL(#{xplStCd}, '00'),   -- 풀이상태코드
	            IFNULL(#{hntCofmYn}, 'N'),  -- 힌트확인여부
	        </if>
	        #{annxFleId},   -- 첨부파일ID(메모)
	        #{usrId},
	        NOW(),
	        #{usrId},
	        NOW(),
	        #{dbId}
	    )
	     /* 평가 공통 - EaEvCom-Mapper.xml - 평가 문항 답변 등록 insert */
	</insert>

	<update id="updateEaEvQtmAnw" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    UPDATE LMS_LRM.EA_EV_QTM_ANW
	    SET
	        MDFR_ID = #{usrId},
	        MDF_DTM = NOW(),
	        ANNX_FLE_ID = #{annxFleId}
	        <if test='!"Y".equals(noteOnlyYn)'>
	            , SMT_ANW_VL  = #{smtAnwVl}
	            , QST_XPL_CN  = #{qstXplCn}
	            , CANS_YN     = #{cansYn}
	            , IANS_NTE_CANS_YN = #{cansYn}
	            , XPL_TM_SCNT = IF(IFNULL(#{xplTmScnt},0) > 1800, 1800, #{xplTmScnt})
	            , XPL_ST_CD 	= #{xplStCd}
	            , HNT_COFM_YN = IFNULL(#{hntCofmYn}, 'N')
	        </if>
	    WHERE EV_ID = #{evId} 
		  AND QTM_ID = #{qtmId}
		  AND USR_ID = #{usrId}
		   /* 평가 공통 - EaEvCom-Mapper.xml - 평가 문항 답변 등록 update */
	</update>

	<select id="checkRecordEaEvQtmAnwRtxm" resultType="java.lang.Integer" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    SELECT COUNT(*) 
	      FROM LMS_LRM.EA_EV_QTM_ANW_RTXM
	     WHERE EV_ID = #{evId} 
	       AND QTM_ID = #{qtmId} 
	       AND USR_ID = #{usrId} 
	       AND RTXM_PN = #{txmPn}
	       /* 평가 공통 - EaEvCom-Mapper.xml - 재응시 평가 문항 답변 등록 select count */
	</select>
	
	<insert id="insertEaEvQtmAnwRtxm" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    INSERT INTO LMS_LRM.EA_EV_QTM_ANW_RTXM (
	        EV_ID, QTM_ID, USR_ID, RTXM_PN
	        <if test='!"Y".equals(noteOnlyYn)'>
	        , SMT_ANW_VL, QST_XPL_CN
	        , CANS_YN
	        , XPL_TM_SCNT
	        , XPL_ST_CD
	        , HNT_COFM_YN
	        </if>
	        , ANNX_FLE_ID
	        , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
	    )
	    VALUES (
	        #{evId},        -- 평가 ID
	        #{qtmId},       -- 문항 ID
	        #{usrId},       -- 사용자 ID
	        #{txmPn},       -- 재응시 회차
	        <if test='!"Y".equals(noteOnlyYn)'>
	        #{smtAnwVl},    -- 제출 답변 값
	        #{qstXplCn},    -- 문제 풀이 내용
	        IFNULL(#{cansYn}, 'N'),  -- 정답 여부
	        IF(IFNULL(#{xplTmScnt},0) > 1800, 1800, #{xplTmScnt}),   -- 풀이 시간 초 수
	        IFNULL(#{xplStCd}, '00'),   -- 풀이상태코드
	        IFNULL(#{hntCofmYn}, 'N'),  -- 힌트확인여부
	        </if>
	        #{annxFleId},   -- 첨부파일ID(메모)
	        #{usrId},
	        NOW(),
	        #{usrId},
	        NOW(),
	        #{dbId}
	    )
	    /* 평가 공통 - EaEvCom-Mapper.xml - 재응시 평가 문항 답변 등록 insert */
	</insert>
	
	<update id="updateEaEvQtmAnwRtxm" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    UPDATE LMS_LRM.EA_EV_QTM_ANW_RTXM
	    SET
	        MDFR_ID     = #{usrId},
	        MDF_DTM     = NOW(),
	        ANNX_FLE_ID = #{annxFleId}
	        <if test='!"Y".equals(noteOnlyYn)'>
	        ,SMT_ANW_VL  = #{smtAnwVl}
	        ,QST_XPL_CN  = #{qstXplCn}
	        ,CANS_YN     = #{cansYn}
	        ,XPL_TM_SCNT = IF(IFNULL(XPL_TM_SCNT, 0) + IFNULL(#{xplTmScnt}, 0) > 1800, 1800, IFNULL(XPL_TM_SCNT, 0) + IFNULL(#{xplTmScnt}, 0))
	        ,XPL_ST_CD   = #{xplStCd}
	        ,HNT_COFM_YN = IFNULL(#{hntCofmYn}, 'N')
	        </if>
	    WHERE EV_ID = #{evId} 
	      AND QTM_ID = #{qtmId} 
	      AND USR_ID = #{usrId}
	      AND RTXM_PN = #{txmPn}
	      /* 평가 공통 - EaEvCom-Mapper.xml - 재응시 평가 문항 답변 등록 update */
	</update>
    

    <!--  평가 결과 update -->
    <update id="updateEaEvSppNtnRs" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
        INSERT INTO LMS_LRM.EA_EV_SPP_NTN_RS (
	          EV_ID, USR_ID, TXM_PN, SPP_NTN_TP_CD, QST_CNT
	        , SMT_DTM, EV_TM_SCNT, CANS_CNT
	        , TXM_STR_YN, EV_CMPL_YN
	        , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        SELECT
			  E.EV_ID, #{usrId}, #{txmPn}, #{sppNtnTpCd}, #{qstCnt}
	        , CASE WHEN #{evCmplYn} = 'Y' THEN now() ELSE NULL END
			, E.XPL_TM_SCNT_SUM
	        , E.CANS_CNT_SUM
	        , CASE WHEN E.XPL_TM_SCNT_SUM > 0 THEN 'Y' ELSE 'N' END
	        , #{evCmplYn}
	        , #{usrId}, now(), #{usrId}, now(), #{dbId}
		FROM (
				SELECT
					  E.EV_ID
					, SUM(EQA.XPL_TM_SCNT) AS XPL_TM_SCNT_SUM
					, SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT_SUM
				FROM LMS_LRM.EA_EV E
				LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.USR_ID = #{usrId} AND EQA.TXM_PN = #{txmPn}
				WHERE E.EV_ID = #{evId}
				GROUP BY E.EV_ID
		) E
        ON DUPLICATE KEY UPDATE
	        SMT_DTM    = VALUES(SMT_DTM)
          , EV_TM_SCNT = VALUES(EV_TM_SCNT)
          , CANS_CNT   = VALUES(CANS_CNT)
	      , TXM_STR_YN = VALUES(TXM_STR_YN)
	      , EV_CMPL_YN = VALUES(EV_CMPL_YN)
	      , MDFR_ID    = #{usrId}
	      , MDF_DTM    = NOW()

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가보충심화결과 update */

    </update>

    <update id="updateEaEvSppNtnQtmAnw" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">

        INSERT INTO LMS_LRM.EA_EV_SPP_NTN_QTM_ANW (
	        EV_ID, USR_ID, TXM_PN, SPP_NTN_TP_CD, QTM_ID
	      , QTM_ORDN, TPC_ID
		  <if test='!"Y".equals(noteOnlyYn)'>
	      , SMT_ANW_VL, QST_XPL_CN, CANS_YN, XPL_TM_SCNT
          , XPL_ST_CD, HNT_COFM_YN
          </if>
          , ANNX_FLE_ID
	      , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
		VALUES
		<foreach collection="qtmList" index="index" item="item" separator=",">
		(
	        #{evId}, #{usrId}, #{txmPn}, #{sppNtnTpCd}, #{item.qtmId}
		  , #{index}+1, #{item.tpcId}
		  <if test='!"Y".equals(noteOnlyYn)'>
	      , #{item.smtAnwVl}
	      , #{item.qstXplCn}
	      , #{item.cansYn}
	      , IF(IFNULL(#{item.xplTmScnt},0) > 1800, 1800, #{item.xplTmScnt})
	      , #{item.xplStCd}
	      , IFNULL(#{item.hntCofmYn}, 'N')
	      </if>
	      , #{item.annxFleId}
	      , #{usrId}, now(), #{usrId}, now(), #{dbId}
		)
		</foreach>
        ON DUPLICATE KEY UPDATE
	        MDFR_ID     = #{usrId}
	      , MDF_DTM     = NOW()
	      , ANNX_FLE_ID = VALUES(ANNX_FLE_ID)
		  <if test='!"Y".equals(noteOnlyYn)'>
		  , SMT_ANW_VL  = VALUES(SMT_ANW_VL)
		  , QST_XPL_CN  = VALUES(QST_XPL_CN)
	      , CANS_YN     = VALUES(CANS_YN)
	      , XPL_TM_SCNT = IF(IFNULL(VALUES(XPL_TM_SCNT), 0) > 1800, 1800, VALUES(XPL_TM_SCNT))
	      , XPL_ST_CD 	= VALUES(XPL_ST_CD)
	      , HNT_COFM_YN = IFNULL(VALUES(HNT_COFM_YN), 'N')
	      </if>

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가 평가보충심화결과 답변 등록 */

    </update>

    <!--    보충심화 평가 완료여부 체크    -->
	<select id="selectEvSppNtnCheckInfo" resultType="hashMap">

		SELECT
	           E.EV_ID 				AS evId
	         , E.EV_DV_CD			AS evDvCd
	         , E.EV_DTL_DV_CD		AS evDtlDvCd
			 , ER.USR_ID			AS usrId
			 , IFNULL(ER.EV_CMPL_YN, 'N')	AS sppNtnCmplYn
			 , ER.SPP_NTN_TP_CD		AS sppNtnTpCd
		     , BT.SCHL_GRD_CD		AS schlGrdCd
		     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
					WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
					ELSE ''
					END sbjCd
        FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
        LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = #{usrId} AND ER.TXM_PN = #{txmPn}
		WHERE E.EV_ID = #{evId}



        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가 평가보충심화결과 가능 체크 */

	</select>



    <select id="selectEaEvList" parameterType="com.aidt.api.ea.evcom.dto.EaEvComQtmReqDto"
            resultType="hashMap">


	        SELECT
	              EE.EV_ID				AS evId
		        , EE.FNL_QST_CNT     	AS qstCnt   	-- 문제 수
		        , EE.EV_DV_CD           AS evDvCd  		-- 평가 구분 코드
	        	, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = EE.EV_DV_CD) AS evDvNm -- 평가 구분 명
		        , EE.EV_DTL_DV_CD       AS evDtlDvCd	-- 평가 상세 구분 코드
	        	, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = EE.EV_DTL_DV_CD) AS evDtlDvNm -- 평가 상세 구분 명
		        , EE.LCKN_YN            AS lcknYn
		        , EE.EV_NM              AS evNm
		        , EER.EV_TM_SCNT		AS evTmScnt 	-- 평가시간초수
		        , EER.TXM_STR_YN		AS txmStrYn 	-- 평가시작여부
		        , EER.EV_CMPL_YN		AS evCmplYn 	-- 평가완료여부
		        , IFNULL(EESNR.TXM_STR_YN, 'X') AS sppNtnTxmStrYn -- 보충/심화 시작 여부
		        , IFNULL(EESNR.EV_CMPL_YN, 'X') AS sppNtnCmplYn   -- 보충/심화 완료 여부
        	FROM LMS_LRM.EA_EV EE
	        <choose>
	            <when test="txmPn != null and txmPn > 0">
	                LEFT JOIN LMS_LRM.EA_EV_RS_RTXM EER ON EER.EV_ID = EE.EV_ID AND EER.USR_ID = #{usrId} AND EER.RTXM_PN = #{txmPn}
	            </when>
	            <otherwise>
	                LEFT JOIN LMS_LRM.EA_EV_RS EER ON EER.EV_ID = EE.EV_ID AND EER.USR_ID = #{usrId}
	            </otherwise>
	        </choose>
	        LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS EESNR ON EESNR.EV_ID = EE.EV_ID AND EESNR.USR_ID = EER.USR_ID AND TXM_PN = #{txmPn}
        	WHERE EE.EV_ID = #{evId}


        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 학습창 평가 정보 조회   */

    </select>

    <select id="selectEaEvSppNtnQtmList" parameterType="com.aidt.api.ea.evcom.dto.EaEvComQtmReqDto"
            resultType="hashMap">


	        SELECT
		        EEQA.EV_ID                     		AS evId,            	-- 평가ID
		        IFNULL(ER.EV_CMPL_YN, 'N') 			AS sppNtnCmplYn, 		-- 평가보충심화완료여부
			    EEQA.SPP_NTN_TP_CD 					AS sppNtnTpCd,          -- 보충/심화 구분
		        EEQA.QTM_ID                         AS qtmId,               -- 문항 ID
		        ROW_NUMBER() OVER(ORDER BY EEQA.QTM_ORDN) AS qtmOrdn,		-- 문항순번
		        EEQA.SMT_ANW_VL                     AS smtAnwVl,            -- 제출 답변 값
		    	IFNULL(EEQA.QST_XPL_CN, '') 		AS qstXplCn,			-- 문제 풀이 내용
		        IFNULL(EEQA.XPL_TM_SCNT,0)          AS xplTmScnt,           -- 풀이 시간 초수
		        QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
		        QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
		        QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
		        QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
		        IFNULL(QTL.QP_TPC_LU_ID, '')        AS topicChapterId,    	-- 토픽(차시) ID
		        IFNULL(QTL.QP_TPC_LU_NM, '')        AS topicChapterName,    -- 토픽(차시) 명
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		 -- 템플릿 xml 내용
		        <if test='"Y".equals(lrnwEvYn)'>
		            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
		            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
		            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
		            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
		            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
		            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
		            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
		            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
		            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
		            '' AS answerUrl,	-- 정답 url
		            '' AS explainUrl,	-- 해설 url
		        </if>
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl, -- HTML(ZIP) url
	            '' AS questionUrl, 		-- 질문 url
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QPI.QP_PSSG_HTML_TMPL_CN, '') AS passageHtmlTemplate, -- 지문HtmlTemplate
		        IFNULL(QPI.QP_PSSG_TMPL_XML_CN, '') AS passageTemplateXml, 	-- 지문Template xml 내용
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType	-- 문항 등록 형태(hpw, html)
		      , IFNULL(BNCM.CRCL_CTN_ELM2_CD, '') 	AS eduCrsCnCd    	-- 교육과정콘텐츠표준CD
		      , IFNULL(BEA.EDU_CRS_ACH_BS_CD, '') 	AS eduCrsAchBsCd  	-- 교육과정성취기준CD
		      , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    		-- 문항 키워드명
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	          , IFNULL(EEQA.ANNX_FLE_ID,'')			AS annxFleId 		-- 노트 첨부파일 ID
	          , IFNULL(CAF.ANNX_FLE_PTH_NM,'')		AS annxFlePthNm 	-- 노트 첨부파일 경로명
	          , IFNULL(EEQA.XPL_ST_CD,'00')			AS xplStCd 			-- 풀이상태코드
	          , IFNULL(EEQA.HNT_COFM_YN,'N')		AS hntCofmYn 		-- 힌트확인여부
	          , IFNULL(USR.LRNR_VEL_TP_CD, 'NM') 	AS lrnrVelTpCd 		-- 학습수준유형코드
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode 	-- 형판코드
       		  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName 	-- 형판명
	          , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
	          , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
		    FROM LMS_LRM.EA_EV_SPP_NTN_RS ER
		    JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EEQA ON EEQA.EV_ID = ER.EV_ID AND EEQA.USR_ID = ER.USR_ID AND EEQA.TXM_PN = ER.TXM_PN
	        JOIN LMS_CMS.QP_QTM QQ     ON QQ.QP_QTM_ID     = EEQA.QTM_ID
	        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
        	JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
	        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
	        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
	        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID = QPQ.QP_PSSG_ID
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
	        LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = QQ.QP_QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX'
	        LEFT JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID
			LEFT JOIN LMS_LRM.CM_ANNX_FLE CAF ON CAF.ANNX_FLE_ID = EEQA.ANNX_FLE_ID
			LEFT JOIN LMS_LRM.CM_USR USR ON USR.USR_ID = ER.USR_ID
	        WHERE ER.EV_ID = #{evId}
	        AND ER.USR_ID = #{usrId}
	        AND ER.TXM_PN = #{txmPn}


        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 학습창 보충/심화 평가 문항 목록 조회 */

    </select>

    <!--   평가 채점완료 - 보충/심화 문항답변목록 조회 요청   -->
    <select id="selectEvSppNtnQtmAnwList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto">

        SELECT
	          ER.EV_ID AS evId
	        , IFNULL(ER.EV_CMPL_YN, 'N') AS sppNtnCmplYn -- 평가보충심화완료여부
		    , EEQA.SPP_NTN_TP_CD AS sppNtnTpCd           -- 보충/심화 구분
	        , ROW_NUMBER() OVER(PARTITION BY EEQA.EV_ID, EEQA.USR_ID ORDER BY EEQA.QTM_ORDN) AS qtmNo
	        , (SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = EEQA.EV_ID) AS evNm
	        , EEQA.QTM_ID AS qtmId
	        , EEQA.QTM_ORDN AS qtmOrdn
	        , QQ.QP_DFFD_CD AS qtmDffdDvCd
	        , EEQA.USR_ID AS usrId
	        , EEQA.SMT_ANW_VL AS smtAnwVl
		    , IFNULL(EEQA.QST_XPL_CN, '') AS qstXplCn			-- 문제 풀이 내용
	        , EEQA.CANS_YN AS cansYn
	        , EEQA.XPL_TM_SCNT AS xplTmScnt
	        , DATE_FORMAT(SEC_TO_TIME(EEQA.XPL_TM_SCNT), '%i:%s') AS xplTmScntNm
	        , (SELECT KN.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD KN WHERE KN.KMMP_NOD_ID = EEQA.TPC_ID) as qpTcNm -- 토픽(지식맵 5뎁스) 명
	        , IFNULL(BNCM.CRCL_CTN_ELM2_CD, '') 	AS eduCrsCnCd    	-- 교육과정콘텐츠표준ID
	        , IFNULL(EEQA.ANNX_FLE_ID,'')			AS annxFleId 		-- 노트 첨부파일 ID
	        , IFNULL(CAF.ANNX_FLE_PTH_NM,'')		AS annxFlePthNm 	-- 노트 첨부파일 경로명
	        , IFNULL(EEQA.XPL_ST_CD,'00')			AS xplStCd 			-- 풀이상태코드
	        , IFNULL(EEQA.HNT_COFM_YN,'')			AS hntCofmYn 		-- 힌트확인여부
	        , IFNULL(USR.LRNR_VEL_TP_CD, 'NM') 		AS lrnrVelTpCd 		-- 학습수준유형코드
            , QQ.QP_QST_TYP_CD                   	AS questionFormCode -- 문항 유형 코드
        FROM LMS_LRM.EA_EV_SPP_NTN_RS ER
        JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EEQA ON EEQA.EV_ID = ER.EV_ID AND EEQA.USR_ID = ER.USR_ID AND EEQA.TXM_PN = ER.TXM_PN
	    JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = EEQA.QTM_ID
        LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = EEQA.QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX'
		LEFT JOIN LMS_LRM.CM_ANNX_FLE CAF ON CAF.ANNX_FLE_ID = EEQA.ANNX_FLE_ID
		LEFT JOIN LMS_LRM.CM_USR USR ON USR.USR_ID = ER.USR_ID
        WHERE ER.EV_ID = #{evId}
        AND ER.USR_ID = #{usrId}
        AND ER.TXM_PN = #{txmPn}

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가 채점완료 - 보충/심화 문항답변목록 조회 요청 - selectEvSppNtnQtmAnwList*/

    </select>


    <select id="selectEaEvQtmList" parameterType="com.aidt.api.ea.evcom.dto.EaEvComQtmReqDto"
            resultType="hashMap">


	        SELECT
		        EEQ.QTM_ID                          AS qtmId,               -- 문항 ID
		        ROW_NUMBER() OVER(ORDER BY EEQ.QTM_ORDN) AS qtmOrdn,		-- 문항순번
		        EEQA.SMT_ANW_VL                     AS smtAnwVl,            -- 제출 답변 값
		        IFNULL(EEQA.QST_XPL_CN, '')			AS qstXplCn,			-- 문제 풀이 내용
		        IFNULL(EEQA.XPL_TM_SCNT,0)          AS xplTmScnt,           -- 풀이 시간 초수
		        QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
		        QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
		        QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
		        QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
		        IFNULL(QTL.QP_TPC_LU_ID, '')        AS topicChapterId,    	-- 토픽(차시) ID
		        IFNULL(QTL.QP_TPC_LU_NM, '')        AS topicChapterName,    -- 토픽(차시) 명
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		 -- 템플릿 xml 내용
		        <if test='"Y".equals(lrnwEvYn)'>
		            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
		            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
		            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
		            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
		            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
		            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
		            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
		            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
		            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
		            '' AS answerUrl,	-- 정답 url
		            '' AS explainUrl,	-- 해설 url
		        </if>
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl, -- HTML(ZIP) url
	            '' AS questionUrl, 		-- 질문 url
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QPI.QP_PSSG_HTML_TMPL_CN, '') AS passageHtmlTemplate, -- 지문HtmlTemplate
		        IFNULL(QPI.QP_PSSG_TMPL_XML_CN, '') AS passageTemplateXml, 	-- 지문Template xml 내용
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType	-- 문항 등록 형태(hpw, html)
		      , IFNULL(BNCM.CRCL_CTN_ELM2_CD, '') 	AS eduCrsCnCd    	-- 교육과정콘텐츠표준CD
		      , IFNULL(BEA.EDU_CRS_ACH_BS_CD, '') 	AS eduCrsAchBsCd  	-- 교육과정성취기준CD
		      , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    		-- 문항 키워드명
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
	          , IFNULL(EEQA.ANNX_FLE_ID,'')			AS annxFleId 		-- 노트 첨부파일 ID
	          , IFNULL(CAF.ANNX_FLE_PTH_NM,'')		AS annxFlePthNm 	-- 노트 첨부파일 경로명
	          , IFNULL(EEQA.XPL_ST_CD,'00')			AS xplStCd 			-- 풀이상태코드
	          , IFNULL(EEQA.HNT_COFM_YN,'N')		AS hntCofmYn 		-- 힌트확인여부
	          , IFNULL(USR.LRNR_VEL_TP_CD, 'NM') 	AS lrnrVelTpCd 		-- 학습수준유형코드
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode 	-- 형판코드
       		  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName 	-- 형판명
	          , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
	          , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
	        FROM LMS_LRM.EA_EV_QTM EEQ
	        <choose>
	            <when test="txmPn > 0">
	                LEFT JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EEQA ON EEQA.EV_ID = EEQ.EV_ID AND EEQA.QTM_ID = EEQ.QTM_ID AND EEQA.USR_ID = #{usrId} AND EEQA.RTXM_PN = #{txmPn}
	            </when>
	            <otherwise>
	                LEFT JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON EEQA.EV_ID = EEQ.EV_ID AND EEQA.QTM_ID = EEQ.QTM_ID AND EEQA.USR_ID = #{usrId}
	            </otherwise>
	        </choose>
	        JOIN LMS_CMS.QP_QTM QQ     ON QQ.QP_QTM_ID     = EEQ.QTM_ID
	        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
        	JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
	        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
	        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
	        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID = QPQ.QP_PSSG_ID
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	        LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
    	    LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = QQ.QP_QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX'
	        LEFT JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID
			LEFT JOIN LMS_LRM.CM_ANNX_FLE CAF ON CAF.ANNX_FLE_ID = EEQA.ANNX_FLE_ID
			LEFT JOIN LMS_LRM.CM_USR USR ON USR.USR_ID = EEQA.USR_ID
	        WHERE EEQ.EV_ID = #{evId}
	        AND EEQ.DEL_YN = 'N'


        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 학습창 평가 문항 목록 조회 */

    </select>

    <!--    문항 단건 조회-->
    <select id="selectQtmInfo" parameterType="com.aidt.api.ea.evcom.dto.EaEvComQtmReqDto"
            resultType="hashMap">
        SELECT
		        QQ.QP_QTM_ID                        AS qtmId,
		        ROW_NUMBER() OVER() 				AS qtmOrdn, 		-- 문항순번
		        QQ.QP_QST_TYP_CD                    AS questionFormCode,    -- 문항 유형 코드
		        QWC01.QP_UNIF_CD_NM                 AS questionFormName,    -- 문항 유형 명
		        QQ.QP_DFFD_CD                       AS difficultyCode,      -- 난이도 코드
		        QWC02.QP_UNIF_CD_NM                 AS difficultyName,      -- 난이도 명
		        IFNULL(QTL.QP_TPC_LU_ID, '')        AS topicChapterId,    	-- 토픽(차시) ID
		        IFNULL(QTL.QP_TPC_LU_NM, '')        AS topicChapterName,    -- 토픽(차시) 명
		        IFNULL(QQC.QP_QST_CN, '')      		AS question,        	-- 질문
		        IFNULL(QQC.QP_QST_HTML_CN, '')      AS questionHtml,        -- 질문Html
		        IFNULL(QQC.QP_QST_HTML_TMPL_CN, '') AS questionHtmlTemplate, -- 질문HtmlTemplate
		        IFNULL(QQC.QP_CHOC_TM1_HTML_CN, '') AS choice1Html,         -- 선택1Html
		        IFNULL(QQC.QP_CHOC_TM2_HTML_CN, '') AS choice2Html,         -- 선택2Html
		        IFNULL(QQC.QP_CHOC_TM3_HTML_CN, '') AS choice3Html,         -- 선택3Html
		        IFNULL(QQC.QP_CHOC_TM4_HTML_CN, '') AS choice4Html,         -- 선택4Html
		        IFNULL(QQC.QP_CHOC_TM5_HTML_CN, '') AS choice5Html,         -- 선택5Html
		        IFNULL(QQC.QP_CHOC_TM1_TMPL_CN, '') AS choice1HtmlTemplate, -- 선택1HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM2_TMPL_CN, '') AS choice2HtmlTemplate, -- 선택2HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM3_TMPL_CN, '') AS choice3HtmlTemplate, -- 선택3HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM4_TMPL_CN, '') AS choice4HtmlTemplate, -- 선택4HtmllTemplate
		        IFNULL(QQC.QP_CHOC_TM5_TMPL_CN, '') AS choice5HtmlTemplate, -- 선택5HtmllTemplate
		        IFNULL(QQC.QP_TMPL_XML_CN, '') 		AS itemStyleXml, 		-- 템플릿 xml 내용
	            IFNULL(QQC.QP_CANS_CN, '')     		AS answer,         	 	-- 정답
	            IFNULL(QQC.QP_CANS_HTML_CN, '')     AS answerHtml,          -- 정답Html
	            IFNULL(QQC.QP_CANS_HTML_TMPL_CN, '') AS answerHtmlTemplate, -- 정답HtmlTemplate
	            IFNULL(QQC.QP_EXL_HTML_CN, '')      AS explainHtml,         -- 해설Html
	            IFNULL(QQC.QP_EXL_HTML_TMPL_CN, '') AS explainHtmlTemplate, -- 해설HtmlTemplate
	            IFNULL(QQC.QP_INTP_HTML_CN, '')     AS translateHtml,       -- 해석Html
	            IFNULL(QQC.QP_INTP_HTML_TMPL_CN, '') AS translateHtmlTemplate, -- 해석HtmlTemplate
	            IFNULL(QQC.QP_HNT_HTML_CN, '')      AS hintHtml,            -- 힌트Html
	            IFNULL(QQC.QP_HNT_HTML_TMPL_CN, '') AS hintHtmlTemplate, 	-- 힌트HtmlTemplate
		        IFNULL(QPI.QP_PSSG_ID, '')			AS passageID, 			-- 지문ID
		        IFNULL(QPI.QP_PSSG_HTML_CN, '')		AS passageHtml,			-- 지문Html
		        IFNULL(QPI.QP_PSSG_HTML_TMPL_CN, '') AS passageHtmlTemplate, -- 지문HtmlTemplate
		        IFNULL(QPI.QP_PSSG_TMPL_XML_CN, '') AS passageTemplateXml, 	-- 지문Template xml 내용
		        IFNULL(QQ.QP_QTM_REG_TYP_NM, '') 	AS ItemRegisterType,	-- 문항 등록 형태(hpw, html)
		        '' AS questionUrl, 	-- 질문 url
		        '' AS answerUrl,	-- 정답 url
		        '' AS explainUrl,	-- 해설 url
	            CASE WHEN QQ.QP_QTM_REG_TYP_NM = 'HTML(ZIP)' THEN
	            		CONCAT(#{bucketUrl}, '/question/upload/item/html-zip/', QQ.QP_QTM_ID, '/index.html')
	            	 ELSE ''
	            	 END AS htmlZipUrl -- HTML(ZIP) url
	          , IFNULL(QQ.QP_KWD_NM, '') 			AS kwdNm    -- 문항 키워드명
	          , IFNULL(QQ.QP_TMPL_CD, '')			AS choiceArrayCode -- 형판코드
       		  , IFNULL(QWC03.QP_UNIF_CD_NM, '') 	AS choiceArrayName -- 형판명
	          , IFNULL(QCT.PLUR_CANS_CNT, 0)		AS plurCansCnt 		-- 복수정답수
			  , IFNULL(QCT.LATEX_YN, '')			AS latexYn 			-- 레이텍여부
			  , IFNULL(QCT.QP_JSON_DATA_CN, '')		AS jsonData			--
			  , IFNULL(QQ.A11Y_VCE_SCRB, '')		AS a11yVceScrb 		-- A11Y 보이스 대본
			  , (
			 		SELECT
							TPC_NM
					FROM (
							SELECT K_NOD.KMMP_NOD_NM AS TPC_NM, 'A1' QTM_TP_CD
							FROM LMS_CMS.BC_EVSH BE
							JOIN LMS_CMS.BC_EVSH_QTM_MPN BEQM ON BEQM.EVSH_ID = BE.EVSH_ID
							JOIN LMS_CMS.BC_KMMP_NOD K_NOD ON K_NOD.KMMP_NOD_ID = BEQM.TPC_ID
							WHERE BE.TXB_ID = #{txbId}
							AND BE.DEL_YN = 'N'
							AND BEQM.QTM_ID = QQ.QP_QTM_ID
							AND BEQM.DEL_YN = 'N'
					UNION ALL
							SELECT K_NOD.KMMP_NOD_NM AS TPC_NM,  'A2' QTM_TP_CD
							FROM LMS_CMS.BC_AI_LRN_ATV_CTN K_ATV
							JOIN LMS_CMS.BC_KMMP_NOD K_NOD ON K_NOD.KMMP_NOD_ID = K_ATV.KMMP_NOD_ID
							JOIN LMS_CMS.BC_KMMP K_MP ON K_MP.KMMP_ID = K_NOD.KMMP_ID
							WHERE K_ATV.CTN_CD = QQ.QP_QTM_ID
							AND K_ATV.CTN_TP_CD = 'QU'
							AND K_NOD.DEL_YN = 'N'
							AND K_MP.DEL_YN = 'N'
							AND K_MP.TXB_ID =  #{txbId}
					) TPC
					GROUP BY TPC.TPC_NM
					ORDER BY MAX(TPC.QTM_TP_CD) LIMIT 1
			  ) AS topicNmKmmp
        FROM LMS_CMS.QP_QTM QQ
        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
    	JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_QTM QPQ ON QPQ.QP_QTM_ID   = QQ.QP_QTM_ID
        LEFT JOIN LMS_CMS.QP_PSSG_INFO QPI ON QPI.QP_PSSG_ID = QPQ.QP_PSSG_ID
        LEFT JOIN LMS_CMS.QP_TPC_LU QTL ON QTL.QP_TPC_LU_ID  = QQA.QP_TPC_LU_ID
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC01 ON QWC01.QP_LCL_CD = '01' AND QWC01.QP_UNIF_CD = QQ.QP_QST_TYP_CD
        LEFT JOIN LMS_CMS.QP_WRK_CD QWC02 ON QWC02.QP_LCL_CD = '02' AND QWC02.QP_UNIF_CD = QQ.QP_DFFD_CD
	    LEFT JOIN LMS_CMS.QP_WRK_CD QWC03 ON QWC03.QP_LCL_CD = '26' AND QWC03.QP_UNIF_CD = QQ.QP_TMPL_CD
        WHERE QQ.QP_QTM_ID = #{qpQtmId}


        /* 평가 공통 - 김상민 - EaEvCom-Mapper.xml - 학습창 단건 문항 조회 */
    </select>

    <!--    평가답변리스트 조회 - 학습창   -->
    <select id="selectEvQtmAnwList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto">
        /** EaEvStu-Mapper.xml - selectEvQtmAnwList */

        SELECT
	          Q.EV_ID AS evId
	        , ROW_NUMBER() OVER(PARTITION BY Q.EV_ID, QA.USR_ID ORDER BY Q.QTM_ORDN) AS qtmNo
	        , (SELECT EV_NM FROM LMS_LRM.EA_EV WHERE EV_ID = Q.EV_ID) AS evNm
	        , Q.QTM_ID AS qtmId
	        , Q.QTM_ORDN AS qtmOrdn
	        , Q.QTM_DFFD_DV_CD AS qtmDffdDvCd
	        , Q.DEL_YN AS delYn
	        , Q.DEL_DTM AS delDtm
	        , QA.USR_ID AS usrId
	        , QA.SMT_ANW_VL AS smtAnwVl
	        , IFNULL(QA.QST_XPL_CN, '') AS qstXplCn			-- 문제 풀이 내용
	        , QA.CANS_YN AS cansYn
	        , QA.XPL_TM_SCNT AS xplTmScnt
	        , DATE_FORMAT(SEC_TO_TIME(QA.XPL_TM_SCNT), '%i:%s') AS xplTmScntNm
	        , QA.CRTR_ID AS crtrId
	        , QA.CRT_DTM AS crtDtm
	        , QA.MDFR_ID AS mdfrId
	        , QA.MDF_DTM AS mdfDtm
	        , QA.DB_ID AS dbId
	        , (SELECT KN.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD KN WHERE KN.KMMP_NOD_ID = Q.TPC_ID) as qpTcNm -- 토픽(지식맵 5뎁스) 명
	        , IFNULL(BNCM.CRCL_CTN_ELM2_CD, '') 	AS eduCrsCnCd    	-- 교육과정콘텐츠표준ID
	        , IFNULL(QA.ANNX_FLE_ID,'')				AS annxFleId 		-- 노트 첨부파일 ID
	        , IFNULL(CAF.ANNX_FLE_PTH_NM,'')		AS annxFlePthNm 	-- 노트 첨부파일 경로명
	        , IFNULL(QA.XPL_ST_CD,'00')				AS xplStCd 			-- 풀이상태코드
	        , IFNULL(QA.HNT_COFM_YN,'')				AS hntCofmYn 		-- 힌트확인여부
	        , IFNULL(USR.LRNR_VEL_TP_CD, 'NM') 		AS lrnrVelTpCd 		-- 학습수준유형코드
		    , QQ.QP_QST_TYP_CD                    	AS questionFormCode -- 문항 유형 코드
        FROM LMS_LRM.EA_EV_QTM Q
        <choose>
            <when test="txmPn != null and txmPn > 0">
                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID AND QA.RTXM_PN = #{txmPn}
            </when>
            <otherwise>
                JOIN LMS_LRM.EA_EV_QTM_ANW QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID
            </otherwise>
        </choose>
		JOIN LMS_CMS.QP_QTM QQ     ON QQ.QP_QTM_ID     = Q.QTM_ID
        LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = Q.QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX'
		LEFT JOIN LMS_LRM.CM_ANNX_FLE CAF ON CAF.ANNX_FLE_ID = QA.ANNX_FLE_ID
		LEFT JOIN LMS_LRM.CM_USR USR ON USR.USR_ID = QA.USR_ID
        WHERE Q.EV_ID = #{evId}
        AND Q.DEL_YN = 'N'
        AND QA.USR_ID = #{usrId}

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 평가답변리스트 조회 - selectEvQtmAnwList*/
    </select>



    <!--    학생화면 평가리포트 - 문항별정오현황 리스트 조회    -->
    <select id="selectEvRptQtmAnwList" resultType="hashMap">

        SELECT
	          E.EV_ID AS evId
	        , ROW_NUMBER() OVER(PARTITION BY E.EV_ID, E.SPP_NTN_YN ORDER BY E.QTM_ORDN) AS qtmNo
	        , E.EV_NM AS evNm
			, E.EV_DV_CD AS evDvCd				-- 평가구분코드
			, (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) evDvNm -- 평가구분명
			, E.EV_DTL_DV_CD AS evDtlDvCd		-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
			, CASE WHEN E.SPP_NTN_YN = 'Y' THEN CASE WHEN E.EV_DTL_DV_CD = 'DE' THEN '심화문제' ELSE '오답문제' END
				   ELSE (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = E.EV_DTL_DV_CD)  -- 평가상세구분명
				   END AS evDtlDvNm
	        , E.SPP_NTN_YN AS sppNtnYn
	        , E.QTM_ID AS qtmId
	        , E.QTM_ORDN AS qtmOrdn
	        , IFNULL(QQ.QP_DFFD_CD, '') AS qtmDffdDvCd
	        , IFNULL(QQ.QP_QST_TYP_CD, '') AS qpQstTypCd
	        , E.USR_ID AS usrId
	        , E.CANS_YN AS cansYn
	        , E.XPL_TM_SCNT AS xplTmScnt
	        , DATE_FORMAT(SEC_TO_TIME(E.XPL_TM_SCNT), '%i:%s') AS xplTmScntNm
	        , (SELECT QP_CN_ARA_NM FROM LMS_CMS.QP_QTM_AN WHERE QP_QTM_ID = E.QTM_ID) AS qpCnAraNm -- 문항플랫폼 내용영역
	        , (SELECT KN.KMMP_NOD_NM FROM LMS_CMS.BC_KMMP_NOD KN WHERE KN.KMMP_NOD_ID = E.TPC_ID) as topicNm -- 토픽(지식맵 5뎁스) 명
			, IFNULL(E.CANS_RT_CLA, 0) AS cansRtCla -- 반평균 정답률
			, CASE WHEN E.CANS_RT_CLA >= 70 THEN '높음'
				   WHEN E.CANS_RT_CLA >= 40 THEN '중간'
				   ELSE '낮음'
				   END AS cansRtClaLvNm -- 반평균정답률 레벨명
	        , E.XPL_TM_SCNT_RT_CLA AS xplTmScntCla -- 반평균 풀이시간
	        , DATE_FORMAT(SEC_TO_TIME(E.XPL_TM_SCNT_RT_CLA), '%i:%s') AS xplTmScntNmCla -- 반평균 풀이시간 명
		  	, IFNULL(E.ANNX_FLE_ID, 0)		AS annxFleId
		  	, IFNULL(E.XPL_ST_CD, '00')		AS xplStCd
		FROM (
	        	SELECT
			          E.EV_ID
			        , E.EV_NM
					, E.EV_DV_CD
					, 'N' AS SPP_NTN_YN
					, E.EV_DTL_DV_CD
			        , EQ.QTM_ID
			        , EQ.TPC_ID
			        , EQ.QTM_ORDN
			        , EQA.USR_ID
			        , EQA.CANS_YN
			        , EQA.XPL_TM_SCNT
			        , EQA.ANNX_FLE_ID
				    , EQA.XPL_ST_CD
					, CLA.CANS_RT_CLA
			        , CLA.XPL_TM_SCNT_RT_CLA
		        FROM LMS_LRM.EA_EV E
		        JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		        <choose>
		            <when test="txmPn != null and txmPn > 0">
		                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.RTXM_PN = #{txmPn}
		            </when>
		            <otherwise>
		                JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
		            </otherwise>
		        </choose>
				LEFT JOIN (-- 반 평균정담률은 첫응시 데이터로
					SELECT CLA_EQA.EV_ID, CLA_EQA.QTM_ID
					     , ROUND(SUM(CLA_EQA.XPL_TM_SCNT)/COUNT(CLA_EQA.USR_ID)) AS XPL_TM_SCNT_RT_CLA
					     , ROUND(SUM(CASE WHEN CLA_EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(CLA_EQA.USR_ID)*100, 1) AS CANS_RT_CLA
				    FROM LMS_LRM.EA_EV_QTM_ANW CLA_EQA
				    WHERE CLA_EQA.EV_ID = #{evId}
				    AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR CLA_USR WHERE CLA_USR.USR_ID = CLA_EQA.USR_ID AND CLA_USR.USR_TP_CD = 'ST' )
				    GROUP BY CLA_EQA.EV_ID, CLA_EQA.QTM_ID
				) CLA ON CLA.EV_ID = EQ.EV_ID AND CLA.QTM_ID = EQ.QTM_ID
		        WHERE E.EV_ID = #{evId}
		        AND EQ.DEL_YN = 'N'
		        AND EQA.USR_ID = #{usrId}
		UNION ALL
		        SELECT
			          E.EV_ID
			        , E.EV_NM
					, E.EV_DV_CD
					, 'Y' AS SPP_NTN_YN
					, ER.SPP_NTN_TP_CD AS EV_DTL_DV_CD
			        , EQA.QTM_ID
			        , EQA.TPC_ID
			        , EQA.QTM_ORDN
			        , EQA.USR_ID
			        , EQA.CANS_YN
			        , EQA.XPL_TM_SCNT
			        , EQA.ANNX_FLE_ID
				    , EQA.XPL_ST_CD
					, CLA.CANS_RT_CLA
			        , CLA.XPL_TM_SCNT_RT_CLA
		        FROM LMS_LRM.EA_EV E
		        JOIN LMS_LRM.EA_EV_SPP_NTN_RS ER ON ER.EV_ID = E.EV_ID
		        JOIN LMS_LRM.EA_EV_SPP_NTN_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.TXM_PN = ER.TXM_PN
				LEFT JOIN (-- 반 평균정답률은 첫응시 데이터로
					SELECT CLA_EQA.EV_ID, CLA_EQA.QTM_ID
					     , ROUND(SUM(CLA_EQA.XPL_TM_SCNT)/COUNT(CLA_EQA.USR_ID)) AS XPL_TM_SCNT_RT_CLA
					     , ROUND(SUM(CASE WHEN CLA_EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(CLA_EQA.USR_ID)*100, 1) AS CANS_RT_CLA
				    FROM LMS_LRM.EA_EV_SPP_NTN_QTM_ANW CLA_EQA
				    WHERE CLA_EQA.EV_ID = #{evId}
				    AND CLA_EQA.TXM_PN = 0
				    AND EXISTS(SELECT 1 FROM LMS_LRM.CM_USR CLA_USR WHERE CLA_USR.USR_ID = CLA_EQA.USR_ID AND CLA_USR.USR_TP_CD = 'ST' )
				    GROUP BY CLA_EQA.EV_ID, CLA_EQA.QTM_ID
				) CLA ON CLA.EV_ID = E.EV_ID AND CLA.QTM_ID = EQA.QTM_ID
		        WHERE E.EV_ID = #{evId}
		        AND ER.USR_ID = #{usrId}
		        AND ER.TXM_PN = #{txmPn}
		        AND ER.EV_CMPL_YN = 'Y'
		) E
		LEFT JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = E.QTM_ID

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 학생화면 평가리포트 - 문항별정오현황 리스트 조회  - selectEvRptQtmAnwList*/

    </select>

    <!--    학생화면 평가리포트 - 응시회차 리스트 조회    -->
    <select id="selectEvTxmPnList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvMainResDto">

		SELECT EV_ID AS evId, 0 AS txmPn
		FROM LMS_LRM.EA_EV_RS
		WHERE EV_ID = #{evId}
		AND USR_ID = #{usrId}
	UNION ALL
		SELECT EV_ID AS evId, RTXM_PN AS txmPn
		FROM LMS_LRM.EA_EV_RS_RTXM
		WHERE EV_ID = #{evId}
		AND USR_ID = #{usrId}
		AND EV_CMPL_YN = 'Y'

        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 - 응시회차 리스트 조회 - selectEvRptTxmPnList */

    </select>


    <!--    학생화면 평가리포트 조회    -->
    <select id="selectEvRptStuList" resultType="hashMap">
        SELECT
	          E.EV_ID				AS evId					-- 평가ID
	        , E.OPT_TXB_ID			AS optTxbId				-- 운영교과서ID
	        , E.USR_ID				AS usrId				-- 사용자ID
	        , E.EV_DV_CD			AS evDvCd				-- 평가구분코드
	        , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DV_CD' AND CM_CD = E.EV_DV_CD) AS evDvNm -- 평가구분명
	        , IFNULL(E.EV_DTL_DV_CD, '')	AS evDtlDvCd			-- 평가상세구분코드 학기초평가, 단원평가, 차시평가, 학기말평가
	        , (SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND CM_CD = E.EV_DTL_DV_CD) AS evDtlDvNm -- 평가상세구분명
	        , E.EV_NM				AS evNm					-- 평가명
	        , IFNULL(RR.RTXM_PN, 0) 						AS txmPnLast 		-- 응시회차
			, IFNULL(RR.EV_CMPL_YN, 'N')					AS evCmplYnLast	-- 최종 재응시의 평가완료여부
			, IFNULL(ESNR.EV_CMPL_YN, 'X')					AS sppNtnCmplYnLast -- 보충심화 완료여부 (X는 보충심화 없다는 뜻)
			, IFNULL(ESNR.TXM_STR_YN, 'X')					AS sppNtnStrYnLast  -- 보충심화 시작여부
	        , IFNULL(R.EV_CMPL_YN, 'N')												AS evCmplYn		-- 평가완료여부
	        , DATE_FORMAT(R.SMT_DTM, '%m. %d.') 									AS smtDtm -- 제출일시 2024-10-16월일로 수정
	        , CAST(FLOOR(R.EV_TM_SCNT/60) AS Unsigned Integer) 					  	AS xplTmMi		-- 풀이시간 분
	        , R.EV_TM_SCNT%60		AS xplTmScnt			-- 풀이시간 초수
	        , CAST(FLOOR(R_AVG.EV_TM_SCNT_SUM/R_AVG.TOTAL_USR_CNT/60) AS Unsigned Integer)   AS avgXplTmMi -- 평균풀이시간 분
	        , CAST(FLOOR((R_AVG.EV_TM_SCNT_SUM/R_AVG.TOTAL_USR_CNT)%60) AS Unsigned Integer) AS avgXplTmScnt -- 평균풀이시간 초
	        , R.CANS_CNT			AS cansCnt				-- 정답수
			, E.FNL_QST_CNT			AS fnlQstCnt			-- 최종 문제 수
	        , ROUND(R.CANS_CNT/E.FNL_QST_CNT*100, 1) 							   	AS cansRt -- 정답률
	        , ROUND(R_AVG.CANS_CNT_SUM/(E.FNL_QST_CNT*R_AVG.TOTAL_USR_CNT)*100, 1) 	AS avgCansRt -- 평균정답률
			, E.RTXM_PMSN_YN		AS rtxmPmsnYn			-- 재응시 허용 여부
 			, T.LU_LRMP_NOD_ID		AS luLrmpNodId			-- 대단원 ID
 			, T.TC_LRMP_NOD_ID		AS tcLrmpNodId			-- 차시 ID
 			, (SELECT COUNT(1) FROM LMS_LRM.EA_EV_QTM_ANW WHERE EV_ID = E.EV_ID AND USR_ID = R.USR_ID AND IANS_NTE_CANS_YN = 'N') AS iansCnt
 			, IFNULL(NOD_LU.TRM_DV_CD, '')	AS trmDvCd		-- 단원의 학기코드
  			, IFNULL((SELECT CM_CD_NM FROM LMS_LRM.CM_CM_CD TRM WHERE TRM.URNK_CM_CD = 'TRM_DV_CD' AND CM_CD = NOD_LU.TRM_DV_CD), '공통') AS trmDvNm		-- 단원의 학기명
 			, (
 				   SELECT ATV.LRN_ATV_ID
	 			   FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV
	 			   WHERE ATV.OPT_TXB_ID = NOD_TC.OPT_TXB_ID
	 			   AND ATV.LRMP_NOD_ID = NOD_TC.LRMP_NOD_ID
	 			   AND ATV.EV_ID = E.EV_ID
	 			   AND ATV.USE_YN = 'Y'
	 			   ORDER BY ATV.RCSTN_ORDN DESC LIMIT 1
 			  ) AS lrnAtvId -- 학습활동 엑티비디 ID
 			, (
 				   SELECT NOD_TC.LRMP_NOD_ID
	 			   FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC
	 			   WHERE NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID
	 			   AND NOD_TC.LLU_NOD_ID = T.LU_LRMP_NOD_ID
	 			   AND NOD_TC.DPTH = 4
	 			   AND NOD_TC.USE_YN = 'Y'
	 			   ORDER BY NOD_TC.RCSTN_ORDN DESC LIMIT 1
 			  ) AS tcLrmpNodIdLast -- 마지막차시ID
 			 , IFNULL( (SELECT EV_CMPL_YN FROM LMS_LRM.EA_EV_SPP_NTN_RS WHERE EV_ID = E.EV_ID AND USR_ID = #{usrId} AND TXM_PN = #{txmPn} ), 'N') AS sppNtnCmplYn -- 보충심화 완료여부
        FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_TS_RNGE T ON T.EV_ID = E.EV_ID AND E.EV_DV_CD = 'SE' -- 교과평가 단원만 조회
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU ON NOD_LU.OPT_TXB_ID = T.LU_OPT_TXB_ID AND NOD_LU.LRMP_NOD_ID = T.LU_LRMP_NOD_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_TC ON NOD_TC.OPT_TXB_ID = T.TC_OPT_TXB_ID AND NOD_TC.LRMP_NOD_ID = T.TC_LRMP_NOD_ID
        <choose>
            <when test="txmPn != null and txmPn > 0">
				JOIN LMS_LRM.EA_EV_RS_RTXM R ON R.EV_ID = E.EV_ID AND R.USR_ID = #{usrId} AND R.RTXM_PN = #{txmPn}
            </when>
            <otherwise>
				JOIN LMS_LRM.EA_EV_RS R ON R.EV_ID = E.EV_ID AND R.USR_ID = #{usrId}
            </otherwise>
        </choose>
		LEFT JOIN ( -- 재응시 프로세스에 따른 최총 회자의 정보 관련
				SELECT
						RR.EV_ID, RR.USR_ID, MAX(RR.RTXM_PN) RTXM_PN
				FROM LMS_LRM.EA_EV_RS_RTXM RR
				WHERE RR.EV_ID = #{evId}
				AND RR.USR_ID = #{usrId}
				GROUP BY RR.EV_ID, RR.USR_ID
		) RR_MAX ON RR_MAX.EV_ID = R.EV_ID AND RR_MAX.USR_ID = R.USR_ID
		LEFT JOIN LMS_LRM.EA_EV_RS_RTXM RR ON RR.EV_ID = RR_MAX.EV_ID AND RR.USR_ID = RR_MAX.USR_ID AND RR.RTXM_PN = RR_MAX.RTXM_PN
		LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS ESNR ON ESNR.EV_ID = R.EV_ID AND ESNR.USR_ID = R.USR_ID AND ESNR.TXM_PN = IFNULL(RR_MAX.RTXM_PN, 0)
        LEFT JOIN( -- 반 평균정담률은 첫응시 데이터로
	 			SELECT R2.EV_ID
	 				 , SUM(R2.EV_TM_SCNT) EV_TM_SCNT_SUM
	 				 , SUM(R2.CANS_CNT) CANS_CNT_SUM
	 				 , COUNT(U.USR_ID) TOTAL_USR_CNT
			   	FROM LMS_LRM.EA_EV_RS R2
			   	JOIN LMS_LRM.CM_USR U ON U.USR_ID = R2.USR_ID
			   	WHERE R2.EV_ID = #{evId}
			   	AND R2.EV_CMPL_YN = 'Y'
			   	AND U.USR_TP_CD = 'ST'
			   	GROUP BY R2.EV_ID
		) R_AVG ON R_AVG.EV_ID = E.EV_ID
        WHERE E.EV_ID = #{evId}


        /* 평가 공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 조회 - selectEvRptStuList */

    </select>

    <!--    학생 평가리포트 내용영역별(교육표준체계) 조회 > 수학   -->
	<select id="selectEvRptStuEduAreaList" resultType="hashMap">

		SELECT
	          EQA.EV_ID AS evId
	        , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) srtOrdn
	        , QA.QP_CN_ARA_ID AS eduAreaCd
	        , MAX(QA.QP_CN_ARA_NM) AS eduAreaNm
	        , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
        FROM LMS_LRM.EA_EV E
        <choose>
            <when test="txmPn != null and txmPn > 0">
                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.RTXM_PN = #{txmPn}
            </when>
            <otherwise>
                JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID
            </otherwise>
        </choose>
        JOIN LMS_CMS.QP_QTM_AN QA ON QA.QP_QTM_ID = EQA.QTM_ID
        JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA' AND CC.CM_CD = QA.QP_CN_ARA_ID
        WHERE E.EV_ID = #{evId}
        AND EQA.USR_ID = #{usrId}
        GROUP BY EQA.EV_ID, QA.QP_CN_ARA_ID

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 내용영역별(교육표준체계) 조회  > 수학 > 문통 내용영역으로 변경 - selectEvRptStuEduAreaList*/

 	</select>

    <!--    학생 평가리포트 학기초진단 > 내용영역별 - 학기초진단 학습맵노드에 매핑된 콘텐츠 조회 > 학생 공통  -->
	<select id="selectEvRptStuEduAreaLrnAtvCtnList" resultType="hashMap">

			SELECT  E.EV_ID AS evId
			 	  , L_DPTH4_MTD.CTN_META_DATA_ID AS ctnMetaDataId
			      , ROW_NUMBER() OVER(ORDER BY MAX(L_DPTH4_ATV.RCSTN_ORDN), L_DPTH4_MTD.CTN_META_DATA_ID) AS rowNo
			      , IFNULL(MAX(L_DPTH4_ATV.LRMP_NOD_ID), '') 	AS lrmpNodId
				  , IFNULL(MAX(L_DPTH4_ATV.LRN_ATV_ID), '')  	AS lrnAtvId
				  , IFNULL(MAX(L_DPTH4_MTD.CDN_PTH_NM), '') 	AS cdnPthNm
				  , IFNULL(MAX(L_DPTH4_MTD.CTN_NM), '') 		AS ctnNm
				  , IFNULL(MAX(BNCM.CRCL_CN_ARA	), '') 			AS eduAreaCd
		    FROM LMS_LRM.EA_EV E
		    JOIN LMS_LRM.EA_EV_TS_RNGE ETR ON ETR.EV_ID = E.EV_ID
		    JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = ETR.LU_OPT_TXB_ID AND L_DPTH4.LLU_NOD_ID = ETR.LU_LRMP_NOD_ID AND L_DPTH4.DPTH = 4
		    JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN L_DPTH4_ATV ON L_DPTH4_ATV.OPT_TXB_ID = L_DPTH4.OPT_TXB_ID AND L_DPTH4_ATV.LRMP_NOD_ID = L_DPTH4.LRMP_NOD_ID
		    	 AND L_DPTH4_ATV.CTN_TP_CD  IN ('PL','HT')
		    JOIN LMS_CMS.BC_CTN_MTD L_DPTH4_MTD ON L_DPTH4_MTD.LRN_ATV_ID = L_DPTH4_ATV.LRN_ATV_ID
		    JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = L_DPTH4_MTD.LRN_ATV_ID AND BNCM.CRCL_CTN_TP_CD = 'TL'
		    WHERE E.EV_ID = #{evId}
		    AND E.EV_DV_CD = 'SE'  -- EV_DV_CD, EV_DTL_DV_CD 조건 필요 없는데, EA_EV_TS_RNGE에 조인시 혹시 다른 평가유형 조인되면 안되어서 방어차원으로 존재.
		    AND E.EV_DTL_DV_CD IN ('ST')
		    GROUP BY E.EV_ID, L_DPTH4_MTD.CTN_META_DATA_ID

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 내용영역별 - 학습맵노드에 매핑된 콘텐츠 조회  - selectEvRptStuEduAreaLrnAtvCtnList*/

 	</select>

    <!--    학생 평가리포트 학기초진단 > 내용영역별(교육표준체계) - 토픽에 매핑된 콘텐츠 조회   -->
	<select id="selectEvRptStuEduAreaTpcCtnList" resultType="hashMap">

			SELECT  EQ.EV_ID 		AS evId
			      , ROW_NUMBER() OVER(ORDER BY K_DPTH5_META.AI_CTN_META_DATA_ID) AS rowNo
			      , IFNULL(K_DPTH5_ATV.KMMP_NOD_ID, '') 	AS kmmpNodId
				  , IFNULL(K_DPTH5_META.AI_LRN_ATV_ID, 0)  AS aiLrnAtvId
				  , IFNULL(K_DPTH5_META.CDN_PTH_NM, '') 	AS cdnPthNm
				  , IFNULL(K_DPTH5_META.CTN_NM, '') 		AS ctnNm
		    FROM LMS_LRM.EA_EV_QTM EQ
		    JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
			JOIN LMS_CMS.BC_AI_LRN_ATV_CTN K_DPTH5_ATV ON K_DPTH5_ATV.KMMP_NOD_ID = EQ.TPC_ID
			JOIN LMS_CMS.BC_AI_CTN_META_DATA K_DPTH5_META ON K_DPTH5_META.AI_LRN_ATV_ID = K_DPTH5_ATV.AI_LRN_ATV_ID
		    WHERE EQ.EV_ID = #{evId}
		    AND EQA.USR_ID = #{usrId}
        	AND K_DPTH5_ATV.CTN_TP_CD IN ('PL', 'HT') -- 동영상, HTML


 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 내용영역별(교육표준체계) - 토픽에 매핑된 콘텐츠 조회  - selectEvRptStuEduCrsTpcRcmLrnList*/
 	</select>


    <!--    학생 평가리포트 단원진단 > 주제유형별 분석 조회    -->
	<select id="selectEvRptStuEduAreaThemeTpcList" resultType="hashMap">

		SELECT	EV_ID 			AS evId
		      , ROW_NUMBER() OVER(ORDER BY RCSTN_ORDN) srtOrdn
		      , KMMP_NOD_ID 	AS eduAreaCd
		      , KMMP_NOD_NM 	AS eduAreaNm
			  , cansRt
			  , CASE WHEN 80 > cansRt THEN 'Y' ELSE 'N' END AS rcmLrnYN -- 추천학습여부
		FROM (
				SELECT E.EV_ID
				     , K_DPTH5.KMMP_NOD_ID
				     , MAX(K_DPTH5.KMMP_NOD_NM) AS KMMP_NOD_NM
				     , MAX(K_DPTH5.RCSTN_ORDN) AS RCSTN_ORDN
					 , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
			    FROM LMS_LRM.EA_EV E
			    JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
			    JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
				JOIN LMS_LRM.AI_KMMP_NOD_RCSTN K_DPTH5 ON K_DPTH5.OPT_TXB_ID = E.OPT_TXB_ID AND K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID  -- 토픽단원(5Depth)
				WHERE E.EV_ID = #{evId}
				AND EQA.USR_ID = #{usrId}
	        	GROUP BY EQ.EV_ID, K_DPTH5.KMMP_NOD_ID
		) E


 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 > 단원진단 > 주제유형별 분석 조회  - selectEvRptStuThmTpList*/

 	</select>

    <!--    학생 평가리포트 단원진단 > 주제유형별 > 추천학습콘텐츠 조회 > 토픽에 매핑된 AI지식맵 콘텐츠  -->
	<select id="selectEvRptStuEduAreaThemeTpcCtnList" resultType="hashMap">

		SELECT	EV_ID 									AS evId
		      , ROW_NUMBER() OVER(ORDER BY K_DPTH5_META.AI_CTN_META_DATA_ID) AS rowNo
		      , IFNULL(K_DPTH5_ATV.KMMP_NOD_ID, '') 	AS kmmpNodId
			  , IFNULL(K_DPTH5_META.AI_LRN_ATV_ID, 0)  AS aiLrnAtvId
			  , IFNULL(K_DPTH5_META.CDN_PTH_NM, '') 	AS aiLrnAtvCdnPth
			  , IFNULL(K_DPTH5_META.CTN_NM, '') 		AS ctnNm
		FROM (
 				SELECT E.EV_ID
				     , K_DPTH5.KMMP_NOD_ID
				FROM LMS_LRM.EA_EV E
			    JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
			    JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = EQ.EV_ID AND EQA.QTM_ID = EQ.QTM_ID
				JOIN LMS_LRM.AI_KMMP_NOD_RCSTN K_DPTH5 ON K_DPTH5.OPT_TXB_ID = E.OPT_TXB_ID AND K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID  -- 토픽단원(5Depth)
				WHERE E.EV_ID = #{evId}
				AND EQA.USR_ID = #{usrId}
	        	GROUP BY E.EV_ID, K_DPTH5.KMMP_NOD_ID
	        	HAVING(80 > ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1))
		) E
		JOIN LMS_CMS.BC_AI_LRN_ATV_CTN K_DPTH5_ATV ON K_DPTH5_ATV.KMMP_NOD_ID = E.KMMP_NOD_ID
		JOIN LMS_CMS.BC_AI_CTN_META_DATA K_DPTH5_META ON K_DPTH5_META.AI_LRN_ATV_ID = K_DPTH5_ATV.AI_LRN_ATV_ID
 		WHERE K_DPTH5_ATV.CTN_TP_CD IN ('PL', 'HT') -- 동영상, HTML


 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 > 단원진단 > 주제유형별 > 추천학습콘텐츠 조회 > 토픽에 매핑된 AI지식맵 콘텐츠 - selectEvRptStuThmTpRcmLrnList*/

 	</select>

    <!--    학생 평가리포트 단원진단 > 주제유형별 > 추천학습콘텐츠 조회 > 학습맵노드에 매핑된 콘텐츠 조회   -->
	<select id="selectEvRptStuEduAreaLrnAtvCtnUDList" resultType="hashMap">

			SELECT  E.EV_ID AS evId
			 	  , L_DPTH4_MTD.CTN_META_DATA_ID AS ctnMetaDataId
			      , ROW_NUMBER() OVER(ORDER BY MAX(L_DPTH4_ATV.RCSTN_ORDN), L_DPTH4_MTD.CTN_META_DATA_ID) AS rowNo
			      , IFNULL(MAX(L_DPTH4_ATV.LRMP_NOD_ID), '') 	AS lrmpNodId
				  , IFNULL(MAX(L_DPTH4_ATV.LRN_ATV_ID), '')  	AS lrnAtvId
				  , IFNULL(MAX(L_DPTH4_MTD.CDN_PTH_NM), '') 	AS cdnPthNm
				  , IFNULL(MAX(L_DPTH4_MTD.CTN_NM), '') 		AS ctnNm
		    FROM LMS_LRM.EA_EV E
		    JOIN LMS_LRM.EA_EV_TS_RNGE ETR ON ETR.EV_ID = E.EV_ID
		    JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = ETR.LU_OPT_TXB_ID
		    	 AND L_DPTH4.LLU_NOD_ID = ETR.LU_LRMP_NOD_ID
		    	 AND L_DPTH4.LRMP_NOD_ID = ETR.TC_LRMP_NOD_ID
		    	 AND L_DPTH4.DPTH = 4
		    JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN L_DPTH4_ATV ON L_DPTH4_ATV.OPT_TXB_ID = L_DPTH4.OPT_TXB_ID AND L_DPTH4_ATV.LRMP_NOD_ID = L_DPTH4.LRMP_NOD_ID
		    	 AND L_DPTH4_ATV.CTN_TP_CD  IN ('PL','HT')
		    JOIN LMS_CMS.BC_CTN_MTD L_DPTH4_MTD ON L_DPTH4_MTD.LRN_ATV_ID = L_DPTH4_ATV.LRN_ATV_ID
		    WHERE E.EV_ID = #{evId}
		    AND E.EV_DV_CD = 'SE'
		    AND E.EV_DTL_DV_CD IN ('UD')
		    GROUP BY E.EV_ID, L_DPTH4_MTD.CTN_META_DATA_ID

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 > 단원진단 > 학습맵노드에 매핑된 콘텐츠 조회  - selectEvRptStuEduAreaLrnAtvCtnUDList */

 	</select>

    <!--    학생 평가리포트 단원평가 > 단원성취도분석 > 차시별 조회    -->
	<select id="selectEvRptStuUgAchdChList" resultType="hashMap">


		SELECT	E.EV_ID evId
		      , E.LRMP_NOD_ID lrmpNodId
		      , E.lrmpNodNm
		      , ROW_NUMBER() OVER(ORDER BY E.rcstnOrdn) rcstnOrdn
			  , E.qtmCnt, E.cansCnt, E.cansRt
		      , MAX(E.cansRt) OVER() cansRtMax
		      , MIN(E.cansRt) OVER() cansRtMin
		FROM (
				SELECT
					   E.EV_ID
					 , L_DPTH4.LRMP_NOD_ID
					 , MAX(L_DPTH4.LRMP_NOD_NM) lrmpNodNm
					 , MAX(L_DPTH4.RCSTN_ORDN) rcstnOrdn
				     , COUNT(EQA.QTM_ID) qtmCnt
				     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) cansCnt
				     , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
				FROM (
						SELECT E.EV_ID, E.OPT_TXB_ID
						     , EQ.QTM_ID
						     , MIN(NOD_MPN.LRMP_NOD_ID) AS LRMP_NOD_ID
						FROM LMS_LRM.EA_EV E
						JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
						JOIN LMS_CMS.BC_KMMP_NOD K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID
						JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_DPTH5.URNK_KMMP_NOD_ID
						WHERE E.EV_ID = #{evId}
						GROUP BY E.OPT_TXB_ID, E.EV_ID, EQ.QTM_ID
				) E
		        <choose>
		            <when test="txmPn != null and txmPn > 0">
		                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = E.QTM_ID AND EQA.RTXM_PN = #{txmPn}
		            </when>
		            <otherwise>
		                JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = E.QTM_ID
		            </otherwise>
		        </choose>
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = E.OPT_TXB_ID AND L_DPTH4.LRMP_NOD_ID = E.LRMP_NOD_ID
				WHERE EQA.USR_ID = #{usrId}
				GROUP BY E.EV_ID, L_DPTH4.LRMP_NOD_ID
		) E


 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 단원평가 > 단원성취도분석 > 차시별 조회 - selectEvRptStuUgAchdChList*/
 	</select>

     <!--    학생 평가리포트 단원평가 > 단원성취도분석 > 성쥐기준별 조회    -->
	<select id="selectEvRptStuUgAchdBsList" resultType="hashMap">
		SELECT	evId, eduAchBsId, achNm
		      , ROW_NUMBER() OVER(ORDER BY eduAchBsId) srtOrdn
			  , qtmCnt, cansCnt, cansRt
		      , MAX(cansRt) OVER() cansRtMax
		      , MIN(cansRt) OVER() cansRtMin
		FROM (
				SELECT E.EV_ID AS evId
				     , BEA.EDU_CRS_ACH_BS_ID AS eduAchBsId
					 , MAX(BEA.EDU_CRS_ACH_BS_CN_NM) AS achNm
				     , COUNT(EQA.QTM_ID) qtmCnt
				     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) cansCnt
				     , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) cansRt
				FROM LMS_LRM.EA_EV E
		        <choose>
		            <when test="txmPn != null and txmPn > 0">
		                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.RTXM_PN = #{txmPn}
		            </when>
		            <otherwise>
		                JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID
		            </otherwise>
		        </choose>
				JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = EQA.QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX' -- 문항만
				JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID
				WHERE E.EV_ID = #{evId}
				AND EQA.USR_ID = #{usrId}
				GROUP BY E.EV_ID, BEA.EDU_CRS_ACH_BS_ID
		) E

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 단원평가 > 단원성취도분석 > 성쥐기준별 조회  - selectEvRptStuUgAchdBsList*/

 	</select>


     <!--    학생 평가리포트 단원평가 > 단원성취도분석 > 성쥐기준별 조회 > 40%미만 상세에서 차시 복습하기 > 차시Id 조회 -->
	<select id="selectEvRptStuUgAchdBsChList" resultType="hashMap">
				SELECT E.EV_ID AS evId
					 , L_DPTH4.LRMP_NOD_ID AS lrmpNodId
				FROM (
						SELECT E.EV_ID, E.OPT_TXB_ID
						     , EQ.QTM_ID
						     , MIN(NOD_MPN.LRMP_NOD_ID) AS LRMP_NOD_ID
						FROM LMS_LRM.EA_EV E
						JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
						JOIN LMS_CMS.BC_KMMP_NOD K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID
						JOIN LMS_CMS.BC_KMMP_NOD K_DPTH4 ON K_DPTH4.KMMP_NOD_ID = K_DPTH5.URNK_KMMP_NOD_ID
						JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_DPTH4.KMMP_NOD_ID
						WHERE E.EV_ID = #{evId}
						GROUP BY E.OPT_TXB_ID, E.EV_ID, EQ.QTM_ID
				) E
				LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = E.OPT_TXB_ID AND L_DPTH4.LRMP_NOD_ID = E.LRMP_NOD_ID
				LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM ON BNCM.CTN_ID = E.QTM_ID AND BNCM.CRCL_CTN_TP_CD = 'EX' -- 문항만
				LEFT JOIN LMS_CMS.BC_EDU_CRS_ACH_BS_V2 BEA ON BEA.EDU_CRS_ACH_BS_ID = BNCM.CRCL_ACH_BS_ID
				WHERE BEA.EDU_CRS_ACH_BS_ID = #{eduAchBsId}
				GROUP BY E.EV_ID, L_DPTH4.LRMP_NOD_ID
				LIMIT 1

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 단원평가 > 단원성취도분석 > 성쥐기준별 조회 > 40%미만 상세에서 차시 복습하기 > 차시Id 조회  - selectEvRptStuUgAchdBsChList*/
 	</select>


     <!--    학생 평가리포트 나의학습성장분석 > 학기말(현재평가), 학기초 조회    -->
	<select id="selectEvRptStuMyLrnGrtMaList" resultType="hashMap">
		SELECT
 			   CC.CM_CD 		AS eduAreaCd
 		     , MAX(CC.CM_CD_NM) AS eduAreaNm
 		     , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) AS srtOrdn
	         , ROUND(MAX(cansRt_st), 1) AS cansRt_st -- 학기초 영역별 정답률
	         , ROUND(MAX(cansRt), 1) AS cansRt -- 학기말 영역별 정답률
		FROM (
				SELECT
				        QQA_ST.QP_CN_ARA_ID
	 				  , SUM(CASE WHEN EQA_ST.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA_ST.QTM_ID)*100 AS cansRt_st
	 				  , 0 AS cansRt
				FROM LMS_LRM.EA_EV E_ST
				JOIN LMS_LRM.EA_EV_TS_RNGE ET_ST ON ET_ST.EV_ID = E_ST.EV_ID
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU_ST ON NOD_LU_ST.OPT_TXB_ID = ET_ST.LU_OPT_TXB_ID AND NOD_LU_ST.LRMP_NOD_ID = ET_ST.LU_LRMP_NOD_ID
		    <choose>
		        <when test="txmPn != null and txmPn > 0">
		            JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA_ST ON EQA_ST.EV_ID = E_ST.EV_ID AND EQA_ST.RTXM_PN = #{txmPn}
		        </when>
		        <otherwise>
		            JOIN LMS_LRM.EA_EV_QTM_ANW EQA_ST ON EQA_ST.EV_ID = E_ST.EV_ID
		        </otherwise>
		    </choose>
		        JOIN LMS_CMS.QP_QTM_AN QQA_ST ON QQA_ST.QP_QTM_ID = EQA_ST.QTM_ID
				WHERE E_ST.OPT_TXB_ID = #{optTxbId}
				AND E_ST.EV_DV_CD = 'SE'
				AND E_ST.EV_DTL_DV_CD = 'ST'
				AND EQA_ST.USR_ID = #{usrId}
				GROUP BY QQA_ST.QP_CN_ARA_ID
		UNION ALL
				SELECT  QQA.QP_CN_ARA_ID
			          , 0 AS cansRt_st -- 학기초 영역별 정답률
			          , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS cansRt -- 학기말 영역별 정답률
		        FROM LMS_LRM.EA_EV E
		    <choose>
		        <when test="txmPn != null and txmPn > 0">
		            JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.RTXM_PN = #{txmPn}
		        </when>
		        <otherwise>
		            JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID
		        </otherwise>
		    </choose>
	    		JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
		        WHERE E.EV_ID = #{evId}
		        AND EQA.USR_ID = #{usrId}
		        GROUP BY QQA.QP_CN_ARA_ID
		) ARA
 		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA' AND CC.CM_CD = ARA.QP_CN_ARA_ID
		GROUP BY CC.CM_CD

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 나의학습성장분석 수학 조회 - selectEvRptStuMyLrnGrtMaList*/

 	</select>

     <!--    학생 평가리포트 나의학습성장분석 > 학기말(현재평가), 학기초 조회    -->
	<select id="selectEvRptStuMyLrnGrthEnList" resultType="hashMap">

		SELECT
 			   CC.CM_CD 		AS arelyCd
 		     , MAX(CC.CM_CD_NM) AS arelyNm
 		     , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) AS srtOrdn
	         , ROUND(MAX(cansRt_st), 1) AS cansRt_st -- 학기초 영역별 정답률
	         , ROUND(MAX(cansRt), 1) AS cansRt -- 학기말 영역별 정답률
		FROM (
				SELECT
				        QQA_ST.QP_CN_ARA_ID
	 				  , SUM(CASE WHEN EQA_ST.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA_ST.QTM_ID)*100 AS cansRt_st
	 				  , 0 AS cansRt
				FROM LMS_LRM.EA_EV E_ST
				JOIN LMS_LRM.EA_EV_TS_RNGE ET_ST ON ET_ST.EV_ID = E_ST.EV_ID
				JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN NOD_LU_ST ON NOD_LU_ST.OPT_TXB_ID = ET_ST.LU_OPT_TXB_ID AND NOD_LU_ST.LRMP_NOD_ID = ET_ST.LU_LRMP_NOD_ID
		    <choose>
		        <when test="txmPn != null and txmPn > 0">
		            JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA_ST ON EQA_ST.EV_ID = E_ST.EV_ID AND EQA_ST.RTXM_PN = #{txmPn}
		        </when>
		        <otherwise>
		            JOIN LMS_LRM.EA_EV_QTM_ANW EQA_ST ON EQA_ST.EV_ID = E_ST.EV_ID
		        </otherwise>
		    </choose>
		        JOIN LMS_CMS.QP_QTM_AN QQA_ST ON QQA_ST.QP_QTM_ID = EQA_ST.QTM_ID
				WHERE E_ST.OPT_TXB_ID = #{optTxbId}
				AND E_ST.EV_DV_CD = 'SE'
				AND E_ST.EV_DTL_DV_CD = 'ST'
				AND IFNULL(NOD_LU_ST.TRM_DV_CD, '') = #{trmDvCd}
				AND EQA_ST.USR_ID = #{usrId}
				GROUP BY QQA_ST.QP_CN_ARA_ID
		UNION ALL
				SELECT  QQA.QP_CN_ARA_ID
			          , 0 AS cansRt_st -- 학기초 영역별 정답률
			          , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS cansRt -- 학기말 영역별 정답률
		        FROM LMS_LRM.EA_EV E
		    <choose>
		        <when test="txmPn != null and txmPn > 0">
		            JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.RTXM_PN = #{txmPn}
		        </when>
		        <otherwise>
		            JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID
		        </otherwise>
		    </choose>
	    		JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
		        WHERE E.EV_ID = #{evId}
		        AND EQA.USR_ID = #{usrId}
		        GROUP BY QQA.QP_CN_ARA_ID
		) ARA
 		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA' AND CC.CM_CD = ARA.QP_CN_ARA_ID
		GROUP BY CC.CM_CD

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 나의학습성장분석 영어 조회 - selectEvRptStuMyLrnGrthEnList*/

 	</select>

     <!--    학생 평가리포트 영어 > 영역별 분석 조회    -->
	<select id="selectEvRptStuEnArelyAnList" resultType="hashMap">
		SELECT EQA.EV_ID 		AS evId
		     , CC.CM_CD 	AS arelyCd
		     , MAX(CC.CM_CD_NM) AS arelyNm
		     , ROW_NUMBER() OVER(ORDER BY MAX(CC.SRT_ORDN)) AS srtOrdn
		     , MAX(CC.SETM_VL_2) AS cmtType -- AI커멘트 영여영역 유형코드
		     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS cansCnt
		     , COUNT(EQA.QTM_ID) AS arelyCnt
 	         , ROUND(SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100, 1) AS cansRt
        FROM LMS_LRM.EA_EV E
    <choose>
        <when test="txmPn != null and txmPn > 0">
            JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQA ON EQA.EV_ID = E.EV_ID AND EQA.RTXM_PN = #{txmPn}
        </when>
        <otherwise>
            JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID
        </otherwise>
    </choose>
        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
 		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'CRCL_CN_ARA_CD' AND CC.CM_CD = QQA.QP_CN_ARA_ID
        WHERE E.EV_ID = #{evId}
        AND EQA.USR_ID = #{usrId}
		GROUP BY EQA.EV_ID, CC.CM_CD

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml -  학생 평가리포트 영어 > 영역별 분석 조회  - selectEvRptStuEnArelyAnList*/

 	</select>

    <!--    학생 평가리포트 강점/약점 조회    -->
	<select id="selectEvRptStuStpnWkpnList" resultType="com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto">
		SELECT
				E.EV_ID					as evId
			  , E.QTM_ID				as qtmId
			  , E.QTM_ORDN				as qtmOrdn
			  , E.QTM_DFFD_DV_CD		as qtmDffdDvCd
			  , IFNULL(E.QP_TC_NM,'-')	as qpTcNm
			  , E.XPL_TM_SCNT			as xplTmScnt
			  , D_NO.cansYn
			  , D_NO.rowNo				as qtmNo
		FROM (
			SELECT 1 as rowNo, 'N' as cansYn UNION ALL
			SELECT 2 as rowNo, 'N' as cansYn UNION ALL
			SELECT 3 as rowNo, 'N' as cansYn UNION ALL
			SELECT 1 as rowNo, 'Y' as cansYn UNION ALL
			SELECT 2 as rowNo, 'Y' as cansYn UNION ALL
			SELECT 3 as rowNo, 'Y' as cansYn
		) D_NO
		LEFT JOIN (
				SELECT
						E.EV_ID
					  , Q.QTM_ID
					  , Q.QTM_ORDN
					  , Q.QTM_DFFD_DV_CD
					  , Q.QP_TC_NM
					  , QA.CANS_YN
					  , QA.XPL_TM_SCNT
					  , ROW_NUMBER() OVER(PARTITION BY QA.CANS_YN ORDER BY QA.CANS_YN, QA.XPL_TM_SCNT, Q.QTM_ORDN) qtmNo
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.EA_EV_QTM Q ON Q.EV_ID = E.EV_ID
        <choose>
            <when test="txmPn != null and txmPn > 0">
                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID AND QA.RTXM_PN = #{txmPn}
            </when>
            <otherwise>
                JOIN LMS_LRM.EA_EV_QTM_ANW QA ON QA.EV_ID = Q.EV_ID AND QA.QTM_ID = Q.QTM_ID
            </otherwise>
        </choose>
				WHERE E.EV_ID = #{evId}
				AND Q.DEL_YN = 'N'
				AND QA.USR_ID = #{usrId}
		) E ON E.qtmNo = D_NO.rowNo AND E.CANS_YN = D_NO.cansYn
		ORDER BY D_NO.cansYn, D_NO.rowNo

 		/** 평가공통 - 박원희 - EaEvCom-Mapper.xml - 학생 평가리포트 강점/약점 조회 - selectEvRptStuStpnWkpnList*/
 	</select>

    <select id="selectLrnSumm" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">
        SELECT
            COUNT(1)                                                     AS stdDate,  -- 학습일
            IFNULL(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%H'),'00')  AS evTmScntH,-- 학습 시간 시
            IFNULL(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%i'),'00')  AS evTmScntM,-- 학습 시간 분
            SUM(T.qstCnt)                                                AS qstCnt,   -- 문제 풀이 수
            IFNULL(ROUND(((SUM(T.cansCnt)/SUM(T.qstCnt))*100)),0)        AS cansRt    -- 정답률
        FROM(
            SELECT
                G.stdDate,
                SUM(G.evTmScnt) AS evTmScnt,
                SUM(G.qstCnt)   AS qstCnt,
                SUM(G.cansCnt)  AS cansCnt
            FROM
            (
            /*교과평가,AI평가,교사평가*/
            SELECT
                DATE_FORMAT(EER.SMT_DTM,'%Y%m%d')    AS stdDate,
                SUM(EER.EV_TM_SCNT) AS evTmScnt,
                SUM(EE.FNL_QST_CNT) AS qstCnt,
                SUM(EER.CANS_CNT)   AS cansCnt
            FROM LMS_LRM.EA_EV EE
            INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID = #{usrId})
            WHERE EE.OPT_TXB_ID = #{optTxbId}
            AND EE.USE_YN      = 'Y'
            AND EE.DEL_YN      = 'N'
            AND EER.EV_CMPL_YN = 'Y'
            <if test='smtDtm != null and smtDtm neq ""'>
                AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> #{smtDtm}
            </if>
            GROUP BY DATE_FORMAT(EER.SMT_DTM,'%Y%m%d')

            UNION ALL

            /*교과학습*/
            SELECT
                DATE_FORMAT(ST.MDF_DTM,'%Y%m%d') AS stdDate,
                SUM(ST.LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            from LMS_LRM.tl_sbc_lrn_atv_rcstn tslar
            inner join LMS_LRM.TL_SBC_LRN_ATV_ST ST
            	on TSLAR.OPT_TXB_ID = ST.OPT_TXB_ID
            	and TSLAR.LRMP_NOD_ID = ST.LRMP_NOD_ID
            	and TSLAR.LRN_ATV_ID = ST.LRN_ATV_ID
            WHERE TSLAR.OPT_TXB_ID = #{optTxbId}
            AND   ST.LRN_USR_ID = #{usrId}
            and TSLAR.CTN_TP_CD <![CDATA[<>]]> 'EX'
            AND   ST.LRN_ST_CD  = 'CL' -- 학습 완료
            <if test='smtDtm != null and smtDtm neq ""'>
                AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(MDF_DTM),0) <![CDATA[<=]]> #{smtDtm}
            </if>
            GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d')

            UNION ALL

            /*특별학습*/
            SELECT
                DATE_FORMAT(MDF_DTM,'%Y%m%d') AS stdDate,
                SUM(LRN_TM_SCNT)              AS evTmScnt,
                0                             AS qstCnt,
                0                             AS cansCnt
            FROM LMS_LRM.SL_SP_LRN_PGRS_ST
            WHERE OPT_TXB_ID = #{optTxbId}
            AND   LRN_USR_ID = #{usrId}
            AND   LRN_ST_CD  = 'CL'-- 학습 완료
            <if test='smtDtm != null and smtDtm neq ""'>
                AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
                AND  IFNULL(MONTH(MDF_DTM),0) <![CDATA[<=]]> #{smtDtm}
            </if>
            GROUP BY DATE_FORMAT(MDF_DTM,'%Y%m%d')

            /*AI학습은 추후 테이블 생성 될 예정 최익재님이 쿼리 제공*/
            ) G GROUP BY G.stdDate
        ) T


        /* 학습 리포트 공통 - 김상민 - EaEvCom-Mapper.xml - 학습 리포트 학습 요약 조회 - selectLrnSumm */
    </select>

    <select id="selectAchStatByUnit" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">
        SELECT
            TSLNR.LRMP_NOD_ID             AS lrmpNodId,  -- 단원 ID
            CONCAT(TSLNR.ORGL_ORDN,'단원') AS lrmpNodOrdn,-- 단원 순서
            TSLNR.LRMP_NOD_NM             AS lrmpNodNm,  -- 단원 명
            COUNT(QTM.CANS_YN) 			  AS totCnt,     -- 학습 이력 카운트
            ROUND((COUNT(CASE WHEN QTM.CANS_YN='Y' THEN 1 END )/COUNT(TSLNR.LRMP_NOD_ID))*100) AS cansRat -- 단원 별 성취율(정답률)
        FROM ( SELECT ORGL_ORDN, LRMP_NOD_ID, LRMP_NOD_NM
               FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN WHERE OPT_TXB_ID = #{optTxbId}
               AND IFNULL(URNK_LRMP_NOD_ID, '') = ''
               AND USE_YN = 'Y'
               ORDER BY RCSTN_ORDN
        ) TSLNR
        LEFT JOIN
        (
            SELECT
            EEQ.QP_LLU_ID ,
            EEQA.CANS_YN
            FROM LMS_LRM.EA_EV EE
            INNER JOIN LMS_LRM.EA_EV_RS EER  ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID=#{usrId} AND EER.EV_CMPL_YN='Y')
            INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N')
            INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=#{usrId})
            WHERE EE.OPT_TXB_ID =#{optTxbId}
            AND   EE.DEL_YN ='N'
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> #{smtDtm}
        </if>
        )QTM ON TSLNR.LRMP_NOD_ID=QTM.QP_LLU_ID
        GROUP BY TSLNR.LRMP_NOD_ID, TSLNR.LRMP_NOD_NM


        /* 학습 리포트 공통 - 김상민 - EaEvCom-Mapper.xml - 단원별 성취 현황 조회 - selectAchStatByUnit */
    </select>

    <select id="selectTcList" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">

        SELECT
            T.QP_TC_ID        AS qpTcId, -- 차시 ID
            T.QP_TC_NM        AS qpTcNm, -- 차시 명
            COUNT(T.QP_TC_ID) AS qstCnt, -- 푼 문제
            COUNT(CASE WHEN T.CANS_YN = 'Y' THEN 1 END )AS cansCnt, -- 맞힌 문제
            ROUND((COUNT(CASE WHEN T.CANS_YN = 'Y' THEN 1 END )/COUNT(T.QP_TC_ID))*100) AS cansRt -- 정답률
        FROM(
            SELECT
                EEQ.QP_TC_ID ,
                EEQ.QP_TC_NM ,
                EEQA.CANS_YN
            FROM LMS_LRM.EA_EV EE
            INNER JOIN LMS_LRM.EA_EV_RS EER  ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID=#{usrId} AND EER.EV_CMPL_YN='Y')
            INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N' AND EEQ.QP_LLU_ID =#{luLrmpNodId})
            INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=#{usrId})
            WHERE EE.OPT_TXB_ID =#{optTxbId}
            AND   EE.DEL_YN ='N'
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> #{smtDtm}
        </if>
        )T
        GROUP BY T.QP_TC_ID,T.QP_TC_NM

        /* 평가 학습 채점 - 김상민 - EaEvCom-Mapper.xml - 단원별 성취 현황 > 차시별 푼 문제+맞힌 문제+정답률 조회 - selectTcList */
    </select>

    <select id="selectAnswer" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto"
            resultType="hashMap">
        SELECT
	        QCT.QP_QTM_ID                   AS qpQtmId,      -- 문항 아이디
	        QQ.QP_QST_TYP_CD                AS qpQstTypCd,   -- 문항 유형 코드
	        IFNULL(QCT.QP_JSON_DATA_CN,'')  AS qpJsonDataCn, -- json data
	        IFNULL(QCT.PART_CANS_CNT,0)    AS partCansCnt,  -- 부분 정답 개수
	        IFNULL(QCT.PLUR_CANS_CNT,0)    AS plurCansCnt   -- 복수 정답 개수
        FROM LMS_CMS.QP_QTM QQ
        INNER JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID = QQ.QP_QTM_ID
        WHERE QQ.QP_QTM_ID = #{qtmId}


        /* 평가 학습 채점 - 김상민 - EaEvCom-Mapper.xml - 평가 결과 문항별 정답 조회 - selectAnswer */
    </select>

    <select id="selectKwdList" resultType="hashMap">
        SELECT QAK.QP_QTM_ID		as qpQtmId			  -- 문항ID
			 , QAK.QP_KWD			as qpKwd		  -- 키워드
			 , QAK.KWD_CNT			as kwdCnt		  -- 키워드 수
			 , QAK.KWD_CASE_YN		as kwdCaseYn 	  -- 대소문자여부
			 , QAK.KWD_SPCE_SKIP_YN as kwdSpaceSkipYn -- 공백무시여부
		FROM LMS_CMS.QP_QTM QQ
    	INNER JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID = QQ.QP_QTM_ID
    	INNER JOIN LMS_CMS.QP_ANW_KWD QAK ON QCT.QP_QTM_ID = QAK.QP_QTM_ID
 		WHERE QQ.QP_QTM_ID = #{qtmId}

 		/* 평가 학습 채점 - 조용진 - EaEvCom-Mapper.xml - 평가 결과 문항별 키워드 조회 - selectKwdList */
    </select>

    <select id="selectTcGoodBadList" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">
        SELECT TT.* FROM (
	        SELECT
	            T.QP_TC_ID        AS qpTcId, -- 차시 ID
	            T.QP_TC_NM        AS qpTcNm, -- 차시 명
	            ROUND((COUNT(CASE WHEN T.CANS_YN = 'Y' THEN 1 END )/COUNT(T.QP_TC_ID))*100) AS cansRt -- 정답률
	        FROM(
	            SELECT
	                EEQ.QP_TC_ID ,
	                EEQ.QP_TC_NM ,
	                EEQA.CANS_YN
	            FROM LMS_LRM.EA_EV EE
	            INNER JOIN LMS_LRM.EA_EV_RS EER  ON (EE.EV_ID=EER.EV_ID AND EER.USR_ID=#{usrId} AND EER.EV_CMPL_YN='Y')
	            INNER JOIN LMS_LRM.EA_EV_QTM EEQ ON (EER.EV_ID=EEQ.EV_ID AND EEQ.DEL_YN='N')
	            INNER JOIN LMS_LRM.EA_EV_QTM_ANW EEQA ON (EEQA.EV_ID=EEQ.EV_ID AND EEQA.QTM_ID=EEQ.QTM_ID AND EEQA.USR_ID=#{usrId})
	            WHERE EE.OPT_TXB_ID =#{optTxbId}
	            AND   EE.DEL_YN ='N'
	        	<if test='smtDtm != null and smtDtm neq ""'>
	            AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
	            AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> #{smtDtm}
	        	</if>
	        )T
	        GROUP BY T.QP_TC_ID,T.QP_TC_NM
			<choose>
	    	<when test = 'order != null and order.equals("DESC")'><!-- 정렬구분 -->
	        ORDER BY 3 DESC
		    </when>
			<otherwise>
	        ORDER BY 3 ASC
			</otherwise>
			</choose>
        ) TT
        LIMIT 5

        /* 학습 리포트 공통 - 김상민 - EaEvCom-Mapper.xml - AI 학습 분석 > 잘 하고 있어요! + 조금 더 노력이 필요해요! 조회 - selectTcGoodBadList */
    </select>

    <select id="selectTmScntList" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">

        SELECT
        smtDtm,
        IF(TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%H')='00',0,TIME_FORMAT(SEC_TO_TIME(SUM(T.evTmScnt)),'%H')) as evTmScnt
        FROM(
        /*교과평가,AI평가,교사평가*/
        SELECT
        MONTH(EER.SMT_DTM)  as smtDtm,
        SUM(EER.EV_TM_SCNT) AS evTmScnt
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID = #{usrId})
        WHERE EE.OPT_TXB_ID = #{optTxbId}
        AND EE.USE_YN      = 'Y'
        AND EE.DEL_YN      = 'N'
        AND EER.EV_CMPL_YN = 'Y'
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(EER.SMT_DTM),0) = #{smtDtm}
        </if>
        GROUP BY MONTH(EER.SMT_DTM)


        UNION ALL

        /*교과학습*/
        SELECT
        MONTH(MDF_DTM)   as smtDtm,
        SUM(LRN_TM_SCNT) AS evTmScnt
        FROM LMS_LRM.TL_SBC_LRN_ATV_ST
        WHERE OPT_TXB_ID = #{optTxbId}
        AND   LRN_USR_ID = #{usrId}
        AND   LRN_ST_CD  = 'CL' -- 학습 완료
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(MDF_DTM),0) = #{smtDtm}
        </if>
        GROUP BY MONTH(MDF_DTM)


        UNION ALL

        /*특별학습*/
        SELECT
        MONTH(MDF_DTM)	 as smtDtm,
        SUM(LRN_TM_SCNT) AS evTmScnt
        FROM LMS_LRM.SL_SP_LRN_PGRS_ST
        WHERE OPT_TXB_ID = #{optTxbId}
        AND   LRN_USR_ID = #{usrId}
        AND   LRN_ST_CD  = 'CL'-- 학습 완료
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(MDF_DTM),0) = #{smtDtm}
        </if>
        GROUP BY MONTH(MDF_DTM)

        /*AI학습은 추후 테이블 생성 될 예정 최익재님이 쿼리 제공*/
        ) T
        GROUP BY T.smtDtm
        ORDER BY T.smtDtm

        /* 학습 리포트 공통 - 김상민 - EaEvCom-Mapper.xml - 학습 성장 분석 > 학습 시간 조회 - selectTmScntList */
    </select>


    <select id="cansRtList" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto"
            resultType="hashMap">

        SELECT
        MONTH(EER.SMT_DTM)                                       AS smtDtm,
        ROUND(((SUM(EER.CANS_CNT)/SUM(EE.FNL_QST_CNT))*100))     AS cansRt    -- 정답률
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID = #{usrId})
        WHERE EE.OPT_TXB_ID = #{optTxbId}
        AND EE.USE_YN      = 'Y'
        AND EE.DEL_YN      = 'N'
        AND EER.EV_CMPL_YN = 'Y'
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(EER.SMT_DTM),0) = #{smtDtm}
        </if>
        group by MONTH(EER.SMT_DTM)
        ORDER by MONTH(EER.SMT_DTM)

        /* 학습 리포트 공통 - 김상민 - EaEvCom-Mapper.xml - 학습 성장 분석 > 성취도(정답률) 조회 - cansRtList */
    </select>

    <update id="insertLansNteQtmAnw" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
        UPDATE LMS_LRM.EA_EV_QTM_ANW SET
			   IANS_NTE_SMT_ANW_VL = #{smtAnwVl},
			   IANS_NTE_CANS_YN = IFNULL(#{cansYn}, 'N'),
               MDFR_ID = #{usrId},
               MDF_DTM = NOW()
         WHERE EV_ID  = #{evId}
           AND QTM_ID = #{qtmId}
           AND USR_ID = #{usrId}

        /* 오답노트 학습 채점 - 김상민 - EaEvCom-Mapper.xml - 오답노트 > 오답 문제 정답 여부 update - insertLansNteQtmAnw */
    </update>


    <!-- 평가 오답 문항 유사/심화 체크 -->
    <select id="selectEvIansNtnCheck" parameterType="com.aidt.api.ea.evcom.dto.EaEvComQtmReqDto"
    resultType="hashMap">

			SELECT COUNT(QTM_ID) iansCnt  -- 오답 갯수
			FROM LMS_LRM.EA_EV_QTM_ANW
			WHERE EV_ID = #{evId}
			AND USR_ID = #{usrId}
			AND CANS_YN = 'N'

        /* EaEvCom-Mapper.xml - selectEvIansNtnCheck - 평가 오답 문항 유사/심화 체크 - 박원희 */

    </select>


    <!-- 평가 창 > 제출 > 보충/심화 문항ID 리스트 조회-->
    <select id="selectEvSmlrQtmIdList" resultType="hashMap">
	        SELECT
	        	   QQ.QP_QTM_ID AS qpQtmId
	        	 , QQ.QP_QTM_ID AS qtmId
	        	 , MAX(QM.tpcId) AS tpcId
	 		FROM (
					SELECT QM.RLT_QTM_TP_CD, QM.SRC_QTM_ID, QM.RLT_QTM_ID
						  , (
						  		SELECT TPC_ID
						  		FROM LMS_CMS.BC_EVSH_QTM_MPN
						  		WHERE QTM_ID = QM.SRC_QTM_ID -- 연관문항에 토픽이 없어서 원천문항의 토릭을 가져옴
						  		AND DEL_YN = 'N'
						  		LIMIT 1
						    ) AS tpcId
						  , EQ.QTM_ORDN
					FROM LMS_LRM.EA_EV E
					JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
				<choose>
		            <when test="txmPn != null and txmPn > 0">
		                JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EA ON EA.EV_ID = EQ.EV_ID AND EA.QTM_ID = EQ.QTM_ID AND EA.RTXM_PN = #{txmPn}
		            </when>
		            <otherwise>
		                JOIN LMS_LRM.EA_EV_QTM_ANW EA ON EA.EV_ID = EQ.EV_ID AND EA.QTM_ID = EQ.QTM_ID
		            </otherwise>
		        </choose>
					JOIN LMS_CMS.BC_RLT_QTM_MPN QM ON QM.SRC_QTM_ID = EQ.QTM_ID
					WHERE E.EV_ID = #{evId}
					AND EQ.DEL_YN = 'N'
					AND EA.USR_ID = #{usrId}
					<choose>
			    	<when test = 'ntnEvCrtYn != null and "Y".equals(ntnEvCrtYn) and iansCnt != null and iansCnt == 0'>
		    			AND QM.RLT_QTM_TP_CD = 'DE'
				    </when>
					<otherwise>
						AND EA.CANS_YN = 'N'
						AND QM.RLT_QTM_TP_CD IN ('SI', 'TW')
					</otherwise>
					</choose>
					AND QM.DEL_YN = 'N'
					AND QM.TXB_ID = (SELECT TXB_ID FROM LMS_LRM.CM_OPT_TXB WHERE OPT_TXB_ID = E.OPT_TXB_ID)
			) QM
	        JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID 		   = QM.RLT_QTM_ID
	        JOIN LMS_CMS.QP_QTM_CN QQC ON QQC.QP_QTM_ID    = QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID    = QQ.QP_QTM_ID
	        JOIN LMS_CMS.QP_CANS_TS QCT ON QCT.QP_QTM_ID   = QQ.QP_QTM_ID
	        WHERE QQ.DEL_YN ='N'
	        GROUP BY QQ.QP_QTM_ID
	        ORDER BY MAX(QM.QTM_ORDN), MAX(QM.RLT_QTM_TP_CD) DESC, MAX(QQ.QP_DFFD_CD) DESC, QQ.QP_QTM_ID

        /* EaEvCom-Mapper.xml - selectEvSmlrQtmIdList - 평가 창 > 제출 > 보충/심화 문항ID 리스트 조회 - 박원희 */

    </select>


	<!--    평가 잠금여부 설정     -->
	<update id="updateEvLockYn"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto">

		/** EaEvCom-Mapper.xml - updateEvLockYn - 평가 잠금여부 설정 - 박원희 */

        UPDATE LMS_LRM.EA_EV
        SET
            LCKN_YN  = #{lcknYn},
            MDFR_ID = #{usrId},
            MDF_DTM = NOW()
        WHERE EV_ID = #{evId}

	</update>
	<!--    평가 사용여부 설정     -->
	<update id="updateEvUseYn"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto">

		/** EaEvCom-Mapper.xml - updateEvUseYn - 평가 사용여부 설정 - 박원희*/

        UPDATE LMS_LRM.EA_EV
        SET
            USE_YN  = #{useYn},
            MDFR_ID = #{usrId},
            MDF_DTM = NOW()
        WHERE EV_ID = #{evId}

	</update>

    <!--    원클릭학습설정 다른학급 적용전 재구성 노드 조회     -->
	<select id="selectUpdateNeedEvList" resultType="hashMap" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto">
       /** EaEvCom-Mapper.xml - selectUpdateNeedEvList - 조용진 - 원클릭학습설정 다른학급 적용전 재구성 노드 조회 */

		SELECT E.EV_ID 			as evId			-- 평가ID
	   		 , ET.LU_LRMP_NOD_ID as luLrmpNodId -- 대단원노드ID
			 , IFNULL(ET.TC_LRMP_NOD_ID,'') as tcLrmpNodId	-- 차시노드ID
			 , TSLNR.USE_YN		as useYn		-- 교과학습 재구성 사용여부
			 , TSLNR.LCKN_YN	as lcknYn		-- 교과학습 재구성 잠금여부
		FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.EA_EV_TS_RNGE ET ON E.EV_ID = ET.EV_ID
		INNER JOIN (
			SELECT LRMP_NOD_ID	-- 차시 노드ID
				 , if(lu_eps_yn = 'N', (select use_yn from lms_lrm.tl_sbc_lrn_nod_rcstn r2 where r2.OPT_TXB_ID = #{optTxbId} and r2.lrmp_nod_id = r1.llu_nod_id), r1.USE_YN) as USE_YN
				 , if(lu_eps_yn = 'N', (select lckn_yn from lms_lrm.tl_sbc_lrn_nod_rcstn r2 where r2.OPT_TXB_ID = #{optTxbId} and r2.lrmp_nod_id = r1.llu_nod_id), r1.LCKN_YN) as LCKN_YN
			FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN r1
			WHERE OPT_TXB_ID = #{bcOptTxbId}
			AND DPTH in (1,4)
		) TSLNR 
			ON TSLNR.LRMP_NOD_ID = ET.TC_LRMP_NOD_ID
			or (TSLNR.LRMP_NOD_ID = ET.LU_LRMP_NOD_ID and ET.TC_LRMP_NOD_ID is NULL)
		WHERE E.OPT_TXB_ID =  #{optTxbId}
		AND ET.LU_OPT_TXB_ID =  #{optTxbId}
		AND E.EV_DV_CD = 'SE'
		AND E.EV_DTL_DV_CD != 'ET'
		<if test = 'useYn != null'>
    	AND E.USE_YN != TSLNR.USE_YN
	  	</if>
	  	<if test = 'lcknYn != null'>
    	AND E.LCKN_YN != TSLNR.LCKN_YN
	  	</if>
	</select>

	<!--    원클릭학습설정 대단원/차시 잠금/사용여부 평가 적용     -->
	<update id="updateEvOneClickLrnSetm"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto">
		/** EaEvCom-Mapper.xml - updateEvOneClickLrnSetm - 원클릭학습설정 대단원/차시 잠금/사용여부 평가 적용 - 박원희 */

			UPDATE LMS_LRM.EA_EV E
	        JOIN LMS_LRM.EA_EV_TS_RNGE ET ON E.EV_ID = ET.EV_ID
	        SET
	            <if test = 'lcknYn != null and !lcknYn.equals("")'>
	            	E.LCKN_YN = #{lcknYn},
				</if>
	            <if test = 'useYn != null and !useYn.equals("")'>
	            	E.USE_YN = #{useYn},
				</if>
	            E.MDFR_ID = #{usrId},
	            E.MDF_DTM = NOW()
	        WHERE E.OPT_TXB_ID = #{optTxbId}
	        AND E.EV_DV_CD = 'SE'
	        AND ET.LU_OPT_TXB_ID = #{optTxbId}
	        AND ET.LU_LRMP_NOD_ID = #{lluNodId}
	        <choose>
			<when test = 'tluNodId != null and !tluNodId.equals("")'><!-- 차시 설정일 경우 -->
	        	AND ET.TC_LRMP_NOD_ID = #{tluNodId}
  	   		</when>
    		<when test = 'rltLuAllUpdYn != null and !rltLuAllUpdYn.equals("Y")'><!-- 대단원만 설정일 경우 하위차시 업데이트 여부 -->
            	AND E.EV_DTL_DV_CD NOT IN ("FO", "TO")
	    	</when>
			</choose>

	</update>
	<!--    원클릭학습설정 대단원/차시 잠금/사용여부 평가 적용     -->
	<update id="updateEvOneClickLrnSetmAL"
		parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto">

		/** EaEvCom-Mapper.xml - updateEvOneClickLrnSetm - 원클릭학습설정 AI학습 잠금/사용여부 평가 적용 - 박원희 */

			UPDATE LMS_LRM.EA_EV E
	        SET
	            <if test = 'lcknYn != null and !lcknYn.equals("")'>
	            	E.LCKN_YN = #{lcknYn},
				</if>
	            <if test = 'useYn != null and !useYn.equals("")'>
	            	E.USE_YN = #{useYn},
				</if>
	            E.MDFR_ID = #{usrId},
	            E.MDF_DTM = NOW()
	        WHERE E.EV_ID = #{evId}
	        AND E.EV_DV_CD = 'AE'

	</update>

	<!--    원클릭학습설정 평가 update 후 수정된 평가 ID, 운영교과서ID 조회 (알림관련)-->
	<select id="selectUpdatedEvInfo" resultType="hashMap" parameterType="com.aidt.api.ea.evcom.ev.dto.EaEvUpdateLockUseYnDto">
	/** EaEvCom-Mapper.xml - selectUpdatedEvInfo - 원클릭학습설정 평가 update 후 수정된 평가 ID, 운영교과서ID 조회- 조용진 */

    SELECT E.EV_ID		as evId		-- 평가ID
    	  ,E.OPT_TXB_ID as optTxbId -- 운영교과서ID
	    FROM LMS_LRM.EA_EV E
	    JOIN LMS_LRM.EA_EV_TS_RNGE ET ON E.EV_ID = ET.EV_ID
	    WHERE E.OPT_TXB_ID = #{optTxbId}
	    AND E.EV_DV_CD = 'SE'
	    AND ET.LU_OPT_TXB_ID = #{optTxbId}
	    AND ET.LU_LRMP_NOD_ID = #{lluNodId}
	    <choose>
	        <when test='tluNodId != null and !tluNodId.equals("")'>
	            AND ET.TC_LRMP_NOD_ID = #{tluNodId}
	        </when>
	        <when test='rltLuAllUpdYn != null and !rltLuAllUpdYn.equals("Y")'>
	            AND E.EV_DTL_DV_CD NOT IN ("FO", "TO")
	        </when>
	    </choose>
	</select>

	<select id="selectSchlGrdCd" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto" resultType="String">
        SELECT
            CASE WHEN SCHL_GRD_CD = 'E' THEN 'N'
            ELSE 'Y' END AS schlGrdCdYn
        FROM LMS_LRM.CM_CLA
        WHERE CLA_ID = #{claId}
        /* EaEvCom-Mapper.xml - selectSchlGrdCd - 중,고등 학생 여부 확인 - 김상민 */
    </select>

    <select id="selectLrnSummAvg" parameterType="com.aidt.api.ea.evcom.lrnRpt.dto.EaLrnRptReqDto" resultType="hashMap">
        SELECT
            IFNULL(TIME_FORMAT(SEC_TO_TIME(ROUND(SUM(T.evTmScnt)/(
                SELECT COUNT(1)
                FROM LMS_LRM.CM_OPT_TXB OT
                JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
                WHERE OT.OPT_TXB_ID = #{optTxbId}
                AND U.USR_TP_CD = 'ST'
                GROUP BY OT.OPT_TXB_ID
            ))),'%H'),'00')  AS evTmScntAvg,-- 반평균 학습 시간
            ROUND(SUM(T.qstCnt)/(
            SELECT COUNT(1)
            FROM LMS_LRM.CM_OPT_TXB OT
            JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
            WHERE OT.OPT_TXB_ID = #{optTxbId}
            AND U.USR_TP_CD = 'ST'
            GROUP BY OT.OPT_TXB_ID
            )) AS qstCntAvg -- 반평균 문제 풀이 수
        FROM(
        /*교과평가,AI평가,교사평가*/
        SELECT
        SUM(EER.EV_TM_SCNT) AS evTmScnt,
        SUM(EE.FNL_QST_CNT) AS qstCnt
        FROM LMS_LRM.EA_EV EE
        INNER JOIN LMS_LRM.EA_EV_RS EER ON (EE.EV_ID = EER.EV_ID AND EER.USR_ID	IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'
        ))
        WHERE EE.OPT_TXB_ID = #{optTxbId}
        AND EE.USE_YN      = 'Y'
        AND EE.DEL_YN      = 'N'
        AND EER.EV_CMPL_YN = 'Y'
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(EER.SMT_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(EER.SMT_DTM),0) <![CDATA[<=]]> #{smtDtm}
        </if>

        UNION ALL

        /*교과학습*/
        SELECT
        SUM(LRN_TM_SCNT) AS evTmScnt,
        0                AS qstCnt
        FROM LMS_LRM.TL_SBC_LRN_ATV_ST
        WHERE OPT_TXB_ID = #{optTxbId}
        AND   LRN_USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'
        )
        AND   LRN_ST_CD  = 'CL' -- 학습 완료
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(MDF_DTM),0) <![CDATA[<=]]> #{smtDtm}
        </if>

        UNION ALL

        /*특별학습*/
        SELECT

        SUM(LRN_TM_SCNT) AS evTmScnt,
        0                AS qstCnt
        FROM LMS_LRM.SL_SP_LRN_PGRS_ST
        WHERE OPT_TXB_ID = #{optTxbId}
        AND   LRN_USR_ID IN (
        SELECT U.USR_ID
        FROM LMS_LRM.CM_OPT_TXB OT
        JOIN LMS_LRM.CM_USR U ON U.CLA_ID = OT.CLA_ID
        WHERE OT.OPT_TXB_ID = #{optTxbId}
        AND U.USR_TP_CD = 'ST'
        )
        AND   LRN_ST_CD  = 'CL'-- 학습 완료
        <if test='smtDtm != null and smtDtm neq ""'>
            AND  IFNULL(DATE_FORMAT(MDF_DTM,'%Y'),0)  = DATE_FORMAT(NOW(),'%Y')
            AND  IFNULL(MONTH(MDF_DTM),0) <![CDATA[<=]]> #{smtDtm}
        </if>

        /*AI학습은 추후 테이블 생성 될 예정 최익재님이 쿼리 제공*/
        ) T

    </select>



    <!--    학생 정보 조회    -->
	<select id="selectUserList" resultType="hashMap">
       /** EaEvCom-Mapper.xml - selectUserList - 박원희 - 학생 정보 조회*/

		SELECT USR.USR_ID AS usrId
		     , USR_NM AS usrNm
			 , STU_NO AS stuNo
			 , COUNT(USR.USR_ID) OVER() userCnt
			 , ROW_NUMBER() OVER(ORDER BY USR.STU_NO, USR.USR_NM, USR.USR_ID) AS rowNo
		FROM LMS_LRM.CM_USR USR
		WHERE USR.CLA_ID = #{claId}
		AND USR.USR_TP_CD = 'ST'

	</select>


	<!--   LCMS이관 평가지 교과 평가 평가지ID 조회하기     -->
	<select id="selectEvFromLcmsEv" parameterType="hashMap" resultType="hashMap">

		SELECT BE.EVSH_ID AS evshId
		FROM LMS_CMS.BC_EVSH BE
		WHERE BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND BE.EVSH_TP_CD IN (SELECT CM_CD FROM LMS_LRM.CM_CM_CD WHERE URNK_CM_CD = 'EV_DTL_DV_CD' AND SETM_VL_1 = 'SE') -- AI맞춤 평가지는 AI팀에서 처리함.
		AND EXISTS (SELECT 1
					FROM LMS_CMS.BC_EVSH_QTM_MPN BQ
					WHERE BQ.EVSH_ID = BE.EVSH_ID
					AND EXISTS (SELECT 1 FROM LMS_CMS.QP_QTM WHERE QP_QTM_ID = BQ.QTM_ID )    	-- 문항정보 체크
					AND EXISTS (SELECT 1 FROM LMS_CMS.QP_QTM_AN WHERE QP_QTM_ID = BQ.QTM_ID ) 	-- 문항분석 체크
					AND EXISTS (SELECT 1 FROM LMS_CMS.QP_QTM_CN WHERE QP_QTM_ID = BQ.QTM_ID ) 	-- 문항내용 체크
					AND EXISTS (SELECT 1 FROM LMS_CMS.QP_CANS_TS WHERE QP_QTM_ID = BQ.QTM_ID )	-- 문항정답 체크
		)
		AND NOT EXISTS (SELECT 1 FROM LMS_LRM.EA_EV EV WHERE EV.EVSH_ID = BE.EVSH_ID AND EV.OPT_TXB_ID = #{optTxbId})
		;

        /* EaEvCom-Mapper.xml - selectEvFromLcmsEv - LCMS이관 평가지 교과 평가 평가지ID 조회하기 - 박원희 */
	</select>

	<!--   LCMS이관 평가지 교과 평가 추가하기     -->
	<insert id="insertEvFromLcmsEv" parameterType="hashMap">
		INSERT INTO LMS_LRM.EA_EV (
			  OPT_TXB_ID, USR_ID
			, EV_DV_CD, EV_DTL_DV_CD, EV_NM, TXM_PTME_SETM_YN, TXM_STR_DTM, TXM_END_DTM
			, XPL_TM_SETM_YN, XPL_TM_SCNT, QST_CNT, FNL_QST_CNT, LCKN_YN, RTXM_PMSN_YN, USE_YN, DEL_YN
			, EVSH_DV_CD, EVSH_ID, EVSH_CD
			, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID, TCR_BA_YN -- 교사기본값
		)
		SELECT
			   #{optTxbId} AS OPT_TXB_ID
		     , #{usrId} as USR_ID
		     , 'SE' as EV_DV_CD
		     , BE.EVSH_TP_CD as EV_DTL_DV_CD
			 , BE.EVSH_NM as EV_NM, 'N' as TXM_PTME_SETM_YN, NULL as TXM_STR_DTM, NULL as TXM_END_DTM
			 , 'N' as XPL_TM_SETM_YN, NULL AS XPL_TM_SCNT
			 , (SELECT COUNT(1) FROM LMS_CMS.BC_EVSH_QTM_MPN WHERE EVSH_ID = BE.EVSH_ID AND DEL_YN = 'N') as QST_CNT
			 , (SELECT COUNT(1) FROM LMS_CMS.BC_EVSH_QTM_MPN WHERE EVSH_ID = BE.EVSH_ID AND DEL_YN = 'N') as FNL_QST_CNT
			 , 'N' as LCKN_YN, 'N' as RTXM_PMSN_YN
			 , CASE WHEN BE.TCR_BA_YN = 'N' THEN 'N' ELSE 'Y' END as USE_YN
			 , 'N' as DEL_YN
		     , 'TL' as EVSH_DV_CD
			 , BE.EVSH_ID, BE.EVSH_CD
			 , #{usrId} as CRTR_ID, NOW() as CRT_DTM, #{usrId} as MDFR_ID, NOW() as MDF_DTM, #{dbId} as DB_ID
			 , BE.TCR_BA_YN -- 교사 기본값(Y, N: 사용여부 ,NA : 사용여부 Y이면서 화면에서 변경불가)
		FROM LMS_CMS.BC_EVSH BE
		JOIN LMS_LRM.CM_CM_CD CC ON CC.URNK_CM_CD = 'EV_DTL_DV_CD' AND CC.SETM_VL_1 = 'SE' AND CC.CM_CD = BE.EVSH_TP_CD -- AI맞춤 평가지는 AI팀에서 처리함.
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'

        /* EaEvCom-Mapper.xml - insertEvFromLcmsEv - LCMS이관 평가지 교과 평가 추가하기 - 박원희 */
	</insert>

	<!--    LCMS이관 평가지 교과 평가 문항리스트 등록     -->
	<insert id="insertEvQtmFromLcmsEv" parameterType="hashMap">

		/** EA_문항리스트 등록 */
		INSERT INTO LMS_LRM.EA_EV_QTM
		(	   EV_ID, QTM_ID, QTM_ORDN, QTM_DFFD_DV_CD, QP_DFFD_NM
			 , QP_LLU_ID, QP_LLU_NM, QP_TC_ID, QP_TC_NM, TPC_ID
			 , DEL_YN, DEL_DTM
			 , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT STRAIGHT_JOIN
		       E.EV_ID, BQ.QTM_ID
		     , ROW_NUMBER() OVER(PARTITION BY E.EV_ID ORDER BY E.EV_ID, MAX(BQ.QTM_EPS_ORDN)) ROW_NO
		     , MAX(Q.QP_DFFD_CD) QP_DFFD_CD, MAX(QW.QP_UNIF_CD_NM) QP_UNIF_CD_NM
		     , MAX(QL.QP_LLU_ID) QP_LLU_ID, MAX(QL.LLU_NM) LLU_NM
		     , MAX(QT.QP_TPC_LU_ID) QP_TPC_LU_ID, MAX(QT.QP_TPC_LU_NM) QP_TPC_LU_NM, MAX(BQ.TPC_ID) TPC_ID
		     , 'N' AS DEL_YN, NULL as DEL_DTM
			 , #{usrId} as CRTR_ID, NOW() as CRT_DTM, #{usrId} as MDFR_ID, NOW() as MDF_DTM, #{dbId} as DB_ID
		FROM LMS_CMS.BC_EVSH BE
		JOIN LMS_CMS.BC_EVSH_QTM_MPN BQ ON BQ.EVSH_ID = BE.EVSH_ID
		JOIN LMS_LRM.EA_EV E ON E.EVSH_ID = BE.EVSH_ID
		JOIN LMS_CMS.QP_QTM Q ON Q.QP_QTM_ID = BQ.QTM_ID
		JOIN LMS_CMS.QP_QTM_AN QA ON QA.QP_QTM_ID = Q.QP_QTM_ID
		LEFT JOIN LMS_CMS.QP_LLU QL ON QL.QP_LLU_ID = QA.QP_LLU_ID
		LEFT JOIN LMS_CMS.QP_TPC_LU QT ON QT.QP_TPC_LU_ID = QA.QP_TPC_LU_ID
		LEFT JOIN LMS_CMS.QP_WRK_CD QW ON QW.QP_LCL_CD = '02' AND QW.QP_UNIF_CD = Q.QP_DFFD_CD -- 문항난이도 필수 체크 해야 되나?
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND BQ.DEL_YN = 'N'
		AND E.OPT_TXB_ID = #{optTxbId}
		AND EXISTS (SELECT 1 FROM LMS_CMS.QP_QTM_CN WHERE QP_QTM_ID = Q.QP_QTM_ID )  -- 문항내용 체크
		AND EXISTS (SELECT 1 FROM LMS_CMS.QP_CANS_TS WHERE QP_QTM_ID = Q.QP_QTM_ID ) -- 문항정답 체크
		GROUP BY E.EV_ID, BQ.QTM_ID
		;

        /* EaEvCom-Mapper.xml - insertEvQtmFromLcmsEv - LCMS이관 평가지 교과 평가 문항 생성 - 박원희 */
	</insert>



	<!--    평가 평가난이도구성 등록     -->
	<insert id="insertEvDffdFromLcmsEv" parameterType="hashMap">

		INSERT INTO LMS_LRM.EA_EV_DFFD_CSTN( EV_ID, EV_DFFD_DV_CD, EV_DFFD_DSB_CNT, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID )
		SELECT STRAIGHT_JOIN
			   E.EV_ID, IFNULL(Q.QTM_DFFD_DV_CD, ''), COUNT(Q.QTM_ID) CNT
		     , #{usrId} as CRTR_ID, NOW() as CRT_DTM, #{usrId} as MDFR_ID, NOW() as MDF_DTM, #{dbId} as DB_ID
		FROM LMS_CMS.BC_EVSH BE
		JOIN LMS_LRM.EA_EV E ON E.EVSH_ID = BE.EVSH_ID
		JOIN LMS_LRM.EA_EV_QTM Q ON Q.EV_ID = E.EV_ID
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND E.OPT_TXB_ID = #{optTxbId}
		GROUP BY E.EV_ID, Q.QTM_DFFD_DV_CD
		ORDER BY E.EV_ID, Q.QTM_DFFD_DV_CD DESC;


        /* EaEvCom-Mapper.xml - insertEvDffdFromLcmsEv - LCMS이관 평가지 교과 평가 평가난이도구성 생성 - 박원희 */
	</insert>

	<!--    평가 시험범위 등록     -->
	<insert id="insertEvTsRngeFromLcmsEv" parameterType="hashMap">

		INSERT INTO LMS_LRM.EA_EV_TS_RNGE(
				  EV_ID, TS_RNGE_SEQ_NO
				, LU_OPT_TXB_ID, LU_LRMP_NOD_ID, LU_LRMP_NOD_NM
				, MLU_OPT_TXB_ID, MLU_LRMP_NOD_ID, MLU_LRMP_NOD_NM
				, SLU_OPT_TXB_ID, SLU_LRMP_NOD_ID, SLU_LRMP_NOD_NM
				, TC_OPT_TXB_ID, TC_LRMP_NOD_ID, TC_LRMP_NOD_NM, TPC_ID
				, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		SELECT STRAIGHT_JOIN
		       E.EV_ID, 1 as TS_RNGE_SEQ_NO
		     , E.OPT_TXB_ID AS LU_OPT_TXB_ID, LLU.LRMP_NOD_ID AS LU_LRMP_NOD_ID, LLU.LRMP_NOD_NM AS LU_LRMP_NOD_NM
		     , E.OPT_TXB_ID AS MLU_OPT_TXB_ID, MLU.LRMP_NOD_ID AS MLU_LRMP_NOD_ID, MLU.LRMP_NOD_NM AS MLU_LRMP_NOD_NM
		     , E.OPT_TXB_ID AS SLU_OPT_TXB_ID, SLU.LRMP_NOD_ID AS SLU_LRMP_NOD_ID, SLU.LRMP_NOD_NM AS SLU_LRMP_NOD_NM
		     , E.OPT_TXB_ID AS TC_OPT_TXB_ID, TLU.LRMP_NOD_ID AS TC_LRMP_NOD_ID, TLU.LRMP_NOD_NM AS TC_LRMP_NOD_NM, NULL as TPC_ID
		     , #{usrId} as CRTR_ID, NOW() as CRT_DTM, #{usrId} as MDFR_ID, NOW() as MDF_DTM, #{dbId} as DB_ID
		FROM LMS_CMS.BC_EVSH BE
		JOIN LMS_LRM.EA_EV E ON E.EVSH_ID = BE.EVSH_ID
		LEFT JOIN LMS_CMS.BC_LRMP_NOD LLU ON LLU.LRMP_NOD_ID = BE.LLU_NOD_ID
		LEFT JOIN LMS_CMS.BC_LRMP_NOD MLU ON MLU.LRMP_NOD_ID = BE.MLU_NOD_ID
		LEFT JOIN LMS_CMS.BC_LRMP_NOD SLU ON SLU.LRMP_NOD_ID = BE.SLU_NOD_ID
		LEFT JOIN LMS_CMS.BC_LRMP_NOD TLU ON TLU.LRMP_NOD_ID = BE.TC_LU_ID
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND E.OPT_TXB_ID = #{optTxbId}
		ORDER BY E.EV_ID
		;

        /* EaEvCom-Mapper.xml - insertEvTsRngeFromLcmsEv - LCMS이관 평가지 교과 평가 평가시험범위 생성 - 박원희 */
	</insert>

	<!--    평가 결과 등록     -->
	<insert id="insertEvRsFromLcmsEv" parameterType="hashMap">

		INSERT INTO LMS_LRM.EA_EV_RS (
				EV_ID, USR_ID, SMT_DTM, EV_TM_SCNT, CANS_CNT, CANS_RT
			  , TXM_STR_YN, EV_CMPL_YN, STU_EV_ABLE_YN
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT STRAIGHT_JOIN
		       E.EV_ID, USR.USR_ID, NULL, NULL, NULL, NULL
			 , 'N', 'N', 'Y'
		     , #{usrId} as CRTR_ID, NOW() as CRT_DTM, #{usrId} as MDFR_ID, NOW() as MDF_DTM, #{dbId} as DB_ID
		FROM LMS_CMS.BC_EVSH BE
		JOIN LMS_LRM.EA_EV E ON E.EVSH_ID = BE.EVSH_ID
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_USR USR ON USR.USR_TP_CD = 'ST' AND USR.CLA_ID = OT.CLA_ID
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND E.OPT_TXB_ID = #{optTxbId}
		ORDER BY E.EV_ID, USR.USR_ID
		;

        /* EaEvCom-Mapper.xml - insertEvRsFromLcmsEv - LCMS이관 평가지 교과평가 평가결과 생성 - 박원희 */
	</insert>

	<!--    평가 결과 등록     -->
	<insert id="insertEvRsLoginAddStu" parameterType="hashMap">
		INSERT INTO LMS_LRM.EA_EV_RS (
				EV_ID, USR_ID, SMT_DTM, EV_TM_SCNT, CANS_CNT, CANS_RT
			  , TXM_STR_YN, EV_CMPL_YN, STU_EV_ABLE_YN
			  , CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		)
		SELECT
		       E.EV_ID, USR.USR_ID, NULL, NULL, NULL, NULL
			 , 'N', 'N', 'Y'
		     , #{usrId} as CRTR_ID, NOW() as CRT_DTM, #{usrId} as MDFR_ID, NOW() as MDF_DTM, E.DB_ID as DB_ID
		FROM LMS_LRM.EA_EV E
		JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
		JOIN LMS_LRM.CM_USR USR ON USR.USR_TP_CD = 'ST' AND USR.CLA_ID = OT.CLA_ID
		WHERE E.OPT_TXB_ID = #{optTxbId}
		AND E.EV_DV_CD='SE'
		AND NOT EXISTS (SELECT 1
							   FROM LMS_LRM.EA_EV_RS z
							   WHERE E.EV_ID=z.EV_ID AND USR.USR_ID=z.USR_ID
			);


        /* EaEvCom-Mapper.xml - insertEvRsLoginAddStu - 로그인시 교과평가 평가결과 결과테이블에 없는 학생정보 생성 - 박원희 */
	</insert>


	<!--    평가 최종문제수 업데이트     -->
	<insert id="updateEvQstCntFromLcmsEv" parameterType="hashMap">

		UPDATE LMS_CMS.BC_EVSH BE
		JOIN LMS_LRM.EA_EV E ON E.EVSH_ID = BE.EVSH_ID
		SET E.QST_CNT = (SELECT COUNT(1) FROM LMS_LRM.EA_EV_QTM WHERE EV_ID = E.EV_ID AND DEL_YN = 'N')
		  , E.FNL_QST_CNT = (SELECT COUNT(1) FROM LMS_LRM.EA_EV_QTM WHERE EV_ID = E.EV_ID AND DEL_YN = 'N')
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND E.OPT_TXB_ID = #{optTxbId}
		;

        /* EaEvCom-Mapper.xml - updateEvQstCntFromLcmsEv - 평가 최종문제수 업데이트 - 박원희 */
	</insert>


	<!--    교과학습활동재구성 테이블 평가ID 업데이트     -->
	<insert id="updateSbcLrnAtvEvidFromLcmsEv" parameterType="hashMap">

		UPDATE LMS_CMS.BC_EVSH BE
		JOIN LMS_LRM.EA_EV E ON E.EVSH_ID = BE.EVSH_ID
		JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN ATV ON ATV.OPT_TXB_ID = E.OPT_TXB_ID AND ATV.CTN_TP_CD = 'EX' AND ATV.LRMP_NOD_ID = BE.TC_LU_ID
		SET ATV.EV_ID = E.EV_ID
		WHERE BE.EVSH_ID IN
		<foreach item="item" index ="index" collection="evshList" open="(" separator="," close=")">
		#{item.evshId}
		</foreach>
		AND BE.TXB_ID = #{txbId}
		AND BE.USE_YN = 'Y'
		AND BE.DEL_YN = 'N'
		AND E.OPT_TXB_ID = #{optTxbId}
		AND E.EV_DV_CD = 'SE'
		AND E.EV_DTL_DV_CD IN ('FO', 'TO')
		;

        /* EaEvCom-Mapper.xml - updateSbcLrnAtvEvidFromLcmsEv - 교과학습활동재구성 테이블 평가ID 업데이트 - 박원희 */
	</insert>


	<!--   평가완료 후 학습자 수준 업데이트     -->
	<update id="updateUsrLrnrVelTpCd" >

	   UPDATE LMS_LRM.CM_USR USR
	   JOIN (
		       SELECT
						E.USR_ID
		     		  , LV.LRNR_VEL_TP_CD
				FROM (
						SELECT
							   ER.USR_ID
						     , BT.SCHL_GRD_CD
						     , BT.SBJ_CD
						     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
									WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
									ELSE ''
									END SBJ_CD_TMP
						     , COUNT(EQA.QTM_ID)	AS QTM_CNT
						     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT
						     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS CANS_RT
						FROM LMS_LRM.EA_EV_RS ER
						JOIN LMS_LRM.EA_EV E ON E.EV_ID = ER.EV_ID
						JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
						JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
						JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
						JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = ER.USR_ID
						WHERE ER.USR_ID = #{usrId}
						AND ER.EV_CMPL_YN = 'Y'
						AND ( 	(E.EV_DV_CD = 'SE' AND E.EV_DTL_DV_CD IN ('FO','TO','UG'))
							OR
							  	(E.EV_DV_CD = 'AE' AND E.EV_DTL_DV_CD = 'OV')
							)
						GROUP BY ER.USR_ID, BT.SCHL_GRD_CD, BT.SBJ_CD
				) E
				LEFT JOIN LMS_LRM.EA_LRN_LV_BS LV ON LV.SBJ_CD = E.SBJ_CD_TMP AND LV.SCHL_GRD_CD = E.SCHL_GRD_CD
				WHERE E.CANS_RT BETWEEN LV.LRN_LV_STR_VL AND LV.LRN_LV_END_VL
				LIMIT 1
		) LV ON LV.USR_ID = USR.USR_ID
		SET USR.LRNR_VEL_TP_CD = IFNULL(LV.LRNR_VEL_TP_CD, 'BA')
		WHERE USR.USR_ID = #{usrId}

        /* EaEvCom-Mapper.xml - updateUsrLrnrVelTpCd - 평가완료 답변등록 후 사용자 학습수준 업데이트 - 박원희 */

	</update>

	<!--   평가완료 후 단원별 학습자 수준 업데이트     -->
	<update id="updateTlLuLrnrLv" >

        INSERT INTO lms_lrm.tl_lu_lrnr_lv (
	          OPT_TXB_ID, USR_ID, LLU_NOD_ID, LRNR_VEL_TP_CD, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
        )
        SELECT
			   #{optTxbId}, #{usrId}, #{LRMP_NOD_ID}, #{LRNR_VEL_TP_CD}
	         , #{usrId}, now(), #{usrId}, now(), #{dbId}
        ON DUPLICATE KEY UPDATE
	        LRNR_VEL_TP_CD = VALUES(LRNR_VEL_TP_CD)
	      , MDFR_ID    	   = #{usrId}
	      , MDF_DTM        = NOW()


        /* EaEvCom-Mapper.xml - updateTlLuLrnrLv - 평가완료 답변등록 후 단원별 학습자 수준 업데이트 - 박원희 */

	</update>

	<!--   평가완료 답변 제출 시 단원별 학습수준 조회    -->
	<select id="selectLluLrnrLvList"  resultType="hashMap">

        	SELECT
        	       LLU.LRMP_NOD_ID, IFNULL(LV.LRNR_VEL_TP_CD, 'BA') LRNR_VEL_TP_CD
        	FROM (
					SELECT OPT_TXB_ID, LRMP_NOD_ID, RCSTN_ORDN
					FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN
 					WHERE OPT_TXB_ID = #{optTxbId}
			        AND DPTH = 1
			) LLU
			LEFT JOIN (
					SELECT
						   EV.USR_ID, EV.SCHL_GRD_CD, EV.SBJ_CD_TMP, EV.LLU_NOD_ID, EV.CANS_RT
						 , LB.LRNR_VEL_TP_CD, LB.LRN_LV_STR_VL, LB.LRN_LV_END_VL
					FROM (
							SELECT
									   ER.USR_ID
								     , BT.SCHL_GRD_CD
								     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
											WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
											ELSE ''
											END SBJ_CD_TMP
								     , L_DPTH4.LLU_NOD_ID
								     , COUNT(EQA.QTM_ID)	AS QTM_CNT
								     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END) AS CANS_CNT
								     , SUM(CASE WHEN EQA.CANS_YN = 'Y' THEN 1 ELSE 0 END)/COUNT(EQA.QTM_ID)*100 AS CANS_RT
								FROM LMS_LRM.CM_OPT_TXB OT
								JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
								JOIN LMS_LRM.EA_EV E  ON E.OPT_TXB_ID = OT.OPT_TXB_ID
								JOIN LMS_LRM.EA_EV_RS ER  ON ER.EV_ID = E.EV_ID
								JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
								JOIN LMS_CMS.BC_KMMP_NOD K_DPTH5 ON K_DPTH5.KMMP_NOD_ID = EQ.TPC_ID
								JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.KMMP_NOD_ID = K_DPTH5.URNK_KMMP_NOD_ID
								JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L_DPTH4 ON L_DPTH4.OPT_TXB_ID = OT.OPT_TXB_ID AND L_DPTH4.LRMP_NOD_ID = NOD_MPN.LRMP_NOD_ID
								JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = ER.EV_ID AND EQA.USR_ID = ER.USR_ID AND EQA.QTM_ID = EQ.QTM_ID
		 						WHERE OT.OPT_TXB_ID = #{optTxbId}
								AND ( 	(E.EV_DV_CD = 'SE' AND E.EV_DTL_DV_CD IN ('FO','TO','UG'))
									OR
									  	(E.EV_DV_CD = 'AE' AND E.EV_DTL_DV_CD = 'OV')
									)
		 						AND ER.USR_ID = #{usrId}
								AND ER.EV_CMPL_YN = 'Y'
								GROUP BY ER.USR_ID, BT.SCHL_GRD_CD
										, CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
											   WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
											   ELSE ''
											   END
										, L_DPTH4.LLU_NOD_ID
					) EV
					JOIN LMS_LRM.EA_LRN_LV_BS LB ON LB.SBJ_CD = EV.SBJ_CD_TMP AND LB.SCHL_GRD_CD = EV.SCHL_GRD_CD
					WHERE EV.CANS_RT BETWEEN LB.LRN_LV_STR_VL AND LB.LRN_LV_END_VL
			) LV ON LV.LLU_NOD_ID = LLU.LRMP_NOD_ID
			ORDER BY LLU.RCSTN_ORDN


        /* EaEvCom-Mapper.xml - selectLluLrnrLvList - 평가완료 답변 제출 시 단원별 학습수준 조회 - 박원희 */

	</select>

	<!--   최대 응시회차 조회     -->
	<select id="selectMaxEvRtxmPn" resultType="hashMap">
		SELECT
			E.EV_ID                               AS evId,
			IFNULL(MAX(ERR.RTXM_PN), 0)           AS txmPn,           -- 최대 응시회차
			IFNULL(EER.EV_CMPL_YN, 'N')           AS evCmplYn,        -- EA_EV_RS (기본 응시정보) 평가 완료 여부
			IFNULL(RR.EV_CMPL_YN, 'N')            AS evCmplYnRtxm     -- EA_EV_RS_RTXM (최종 회차) 평가 완료 여부
		FROM LMS_LRM.EA_EV E
				 LEFT JOIN LMS_LRM.EA_EV_RS_RTXM ERR
						   ON ERR.EV_ID = E.EV_ID
							   AND ERR.USR_ID = #{usrId}
				 LEFT JOIN LMS_LRM.EA_EV_RS EER
						   ON EER.EV_ID = E.EV_ID
							   AND EER.USR_ID = #{usrId}
				 LEFT JOIN (
			SELECT *
			FROM LMS_LRM.EA_EV_RS_RTXM
			WHERE EV_ID = #{evId}
			  AND USR_ID = #{usrId}
			ORDER BY RTXM_PN DESC
				LIMIT 1
		) RR ON RR.EV_ID = E.EV_ID
		WHERE E.EV_ID = #{evId}
		GROUP BY E.EV_ID, EER.EV_CMPL_YN, RR.EV_CMPL_YN
	</select>

	<!--   평가완료 후 응시회차 조회     -->
	<select id="selectEvRtxmPn" resultType="hashMap">

		SELECT  E.EV_ID AS evId
		      , MAX(E.EV_DV_CD) AS evDvCd
		      , MAX(E.EV_DTL_DV_CD) AS evDtlDvCd
			  , IFNULL(MAX(RTXM_PN), 0) AS txmPn
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_RS_RTXM ER ON ER.EV_ID = E.EV_ID AND ER.USR_ID = #{usrId}
		WHERE E.EV_ID =  #{evId}
		GROUP BY E.EV_ID

	</select>

	<!--   평가완료 후 풀이상태 업데이트     -->
	<update id="updateEvQtmAnwXplStCd" >

		UPDATE LMS_LRM.EA_EV_QTM_ANW EQA_UP
		JOIN (
				SELECT
						E.EV_ID
					  , E.USR_ID
				      , E.QTM_ID
		     		  , CASE WHEN E.CANS_YN = 'Y' AND DXT.PPE_XPL_TM_SCNT * 0.3 >= E.XPL_TM_SCNT THEN '02' -- 급하게 푼 문제
		     		  	     WHEN E.CANS_YN = 'N' THEN
		     		  	     	  CASE WHEN E.LRNR_VEL_TP_CD = 'FS' AND E.QTM_DFFD_DV_CD IN ('01', '02', '03') THEN '11' -- 실수로 예상되는 문제
		     		  	     	  	   WHEN E.LRNR_VEL_TP_CD = 'NM' AND E.QTM_DFFD_DV_CD IN ('01', '02') THEN '11' -- 실수로 예상되는 문제
		     		  	     	  	   ELSE '00'
		     		  	     	  	   END
		     		  	     ELSE '00' END AS XPL_ST_CD
				FROM (
						SELECT
							   BT.SCHL_GRD_CD
						     , BT.SBJ_CD
						     , USR.USR_ID
						     , USR.LRNR_VEL_TP_CD
						     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
									WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
									ELSE ''
									END SBJ_CD_TMP
						     , E.EV_ID
							 , EQ.QTM_ID
						     , EQ.QTM_DFFD_DV_CD
						     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'CM'
									WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN QQA.QP_CN_ARA_ID
									ELSE ''
									END CN_ARA_ID
						     , EQA.CANS_YN
						     , EQA.XPL_TM_SCNT
						FROM LMS_LRM.EA_EV E
						JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
						JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
						JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
						JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
						JOIN LMS_LRM.EA_EV_QTM_ANW EQA ON EQA.EV_ID = E.EV_ID AND EQA.QTM_ID = EQ.QTM_ID AND EQA.USR_ID = USR.USR_ID
						JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = EQA.QTM_ID
						WHERE E.EV_ID = #{evId}
						AND USR.USR_ID = #{usrId}
				) E
				LEFT JOIN LMS_LRM.EA_SBJ_DFFD_PPE_XPL_TM DXT
				          ON DXT.SBJ_CD = E.SBJ_CD_TMP
						  AND DXT.SCHL_GRD_CD = E.SCHL_GRD_CD
						  AND DXT.CN_ARA_CD = E.CN_ARA_ID
						  AND DXT.DFFD_CD = E.QTM_DFFD_DV_CD
		) E ON E.EV_ID = EQA_UP.EV_ID AND E.USR_ID = EQA_UP.USR_ID AND E.QTM_ID = EQA_UP.QTM_ID
		SET EQA_UP.XPL_ST_CD = IFNULL(E.XPL_ST_CD, '00')
		WHERE EQA_UP.EV_ID = #{evId}
		AND EQA_UP.USR_ID = #{usrId}

        /* EaEvCom-Mapper.xml - updateEvQtmAnwXplStCd - 평가완료 풀이상태 업데이트 - 박원희 */

	</update>

	<!--   평가완료 답변 제출 시 문항의 풀이상태 코드 조회    -->
	<select id="selectEvQtmAnwXplStCd"  resultType="hashMap">

		SELECT
				E.EV_ID			AS evId
			  , E.USR_ID		AS usrId
		      , E.QP_QTM_ID		AS qtmId
     		  , CASE WHEN E.CANS_YN = 'Y' AND DXT.PPE_XPL_TM_SCNT * 0.3 >= E.XPL_TM_SCNT THEN '02' -- 급하게 푼 문제
     		  	     WHEN E.CANS_YN = 'N' THEN
     		  	     	  CASE WHEN E.LRNR_VEL_TP_CD = 'FS' AND E.QP_DFFD_CD IN ('01', '02', '03') THEN '11' -- 실수로 예상되는 문제
     		  	     	  	   WHEN E.LRNR_VEL_TP_CD = 'NM' AND E.QP_DFFD_CD IN ('01', '02') THEN '11' -- 실수로 예상되는 문제
     		  	     	  	   ELSE '00'
     		  	     	  	   END
     		  	     ELSE '00' END AS xplStCd
		FROM (
				SELECT
					   BT.SCHL_GRD_CD
				     , BT.SBJ_CD
				     , USR.USR_ID
				     , USR.LRNR_VEL_TP_CD
				     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'MA'
							WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN 'EN'
							ELSE ''
							END SBJ_CD_TMP
				     , E.EV_ID, QQ.QP_QTM_ID, QQ.QP_DFFD_CD
				     , CASE WHEN BT.SBJ_CD IN ('MA', 'CM1', 'CM2') THEN 'CM'
							WHEN BT.SBJ_CD IN ('EN', 'CE1', 'CE2') THEN QQA.QP_CN_ARA_ID
							ELSE ''
							END CN_ARA_ID
				     , IFNULL(#{cansYn}, 'N') AS CANS_YN
				     , #{xplTmScnt} AS XPL_TM_SCNT
				FROM LMS_LRM.EA_EV E
				JOIN LMS_LRM.CM_OPT_TXB OT ON OT.OPT_TXB_ID = E.OPT_TXB_ID
				JOIN LMS_LRM.CM_USR USR ON USR.CLA_ID = OT.CLA_ID
				JOIN LMS_CMS.BC_TXB BT ON BT.TXB_ID = OT.TXB_ID
				JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = #{qtmId}-- 영어일경우 영역별 코드 구하기 위해
				JOIN LMS_CMS.QP_QTM_AN QQA ON QQA.QP_QTM_ID = QQ.QP_QTM_ID -- 영어일경우 영역별 코드 구하기 위해
				WHERE E.EV_ID = #{evId}
				AND USR.USR_ID = #{usrId}
		) E
		LEFT JOIN LMS_LRM.EA_SBJ_DFFD_PPE_XPL_TM DXT
		          ON DXT.SBJ_CD = E.SBJ_CD_TMP
				  AND DXT.SCHL_GRD_CD = E.SCHL_GRD_CD
				  AND DXT.CN_ARA_CD = E.CN_ARA_ID
				  AND DXT.DFFD_CD = E.QP_DFFD_CD

        /* EaEvCom-Mapper.xml - selectEvQtmAnwXplStCd - 평가완료 답변 제출 시 문항의 풀이상태 코드 조회 - 박원희 */

	</select>



	 <!--    교사 메인 평가 성쥐기준별 조회    -->
	<select id="selectEvRptTcrAchdBsList" resultType="hashMap">
	   SELECT
			max(bnccm.CRCL_ACH_BS_CD) as eduAchBsCd,
			bnccm.CRCL_ACH_BS_ID as eduAchBsId,
			max(bnccm.CRCL_ACH_BS_NM) as achNm,
			ee.EV_ID as evId,
            ROUND(sum(CASE WHEN eeqa.CANS_YN='Y' then 1 else 0 end)/count(*)*100,2) AS cansRt
		FROM  EA_EV ee
			inner join EA_EV_RS EER
				on eer.EV_ID = ee.EV_ID
			inner join cm_usr cm on EER.USR_ID=cm.USR_ID and cm.USR_TP_CD='ST'
       INNER JOIN lms_lrm.ea_ev_qtm_anw eeqa
       ON     ee.EV_ID=eeqa.EV_ID  AND  eer.USR_ID=eeqa.USR_ID
			inner join lms_cms.bc_ntnl_crcl_ctn_mpn_v2 bnccm
				on BNCCM.CTN_ID = eeqa.QTM_ID  AND bnccm.CRCL_CTN_TP_CD='EX'
				where
						 ee.EV_ID=#{evId} and
		eer.EV_CMPL_YN = 'Y'
		 AND ee.OPT_TXB_ID = #{optTxbId}
		GROUP by ee.EV_ID,BNCCM.CRCL_ACH_BS_ID
		ORDER BY MAX(bnccm.CRCL_ACH_BS_CD),MAX(bnccm.CRCL_ACH_BS_NM)
 		/** 평가공통 - 노성용 - EaEvCom-Mapper.xml - 교사 메인 평가 성쥐기준별 조회   - selectEvRptTcrAchdBsList*/
 	</select>


	 <!--    교사 메인 평가 AI별 조회    -->
	<select id="selectEvRptTcrAiList" resultType="hashMap">

	SELECT A.EV_ID  AS evId ,
	       A.TPC_ID AS tpcId ,
	       A.KMMP_NOD_NM ,
	       A.KMMP_NOD_ID,
	       cansRt ,
	       BALAC.AI_LRN_ATV_ID AS aiLrnAtvId ,
	       BALAC.LRN_ATV_NM    AS aiLrnAtvNm ,
	       BALAC.CTN_TP_CD     AS ctnTpCd ,
	       CASE
	              WHEN BACMD.CDN_PTH_NM !=''
	              THEN CONCAT(#{bucketUrl},BACMD.CDN_PTH_NM)
	              ELSE ''
	       END AS cdnPthNm
	FROM   ( SELECT  ee.EV_ID,                          -- 시험지
	                d.TPC_ID,                           -- 토픽
	                e.KMMP_NOD_NM,                      -- 토픽명
	                node1.KMMP_NOD_ID,
	                ROUND(sum(CASE WHEN eeqa.CANS_YN='Y' then 1 else 0 end)/count(*)*100,2) AS cansRt -- 비율
	       FROM     EA_EV ee
	                INNER JOIN EA_EV_RS EER
	                ON       eer.EV_ID = ee.EV_ID
					inner join cm_usr cm on EER.USR_ID=cm.USR_ID and cm.USR_TP_CD='ST'
	                INNER JOIN lms_lrm.ea_ev_qtm_anw eeqa
	                ON       ee.EV_ID  =eeqa.EV_ID
	                AND      eer.USR_ID=eeqa.USR_ID
	                INNER JOIN lms_cms.bc_evsh_qtm_mpn d
	                ON       ee.EVSH_ID =d.EVSH_ID
	                AND      eeqa.QTM_ID=d.QTM_ID
	                INNER JOIN lms_cms.bc_kmmp_nod e
	                ON       d.TPC_ID=e.KMMP_NOD_ID
	                INNER JOIN lms_cms.bc_kmmp_nod node1
	                ON       e.URNK_KMMP_NOD_ID =node1.KMMP_NOD_ID
	       WHERE    ee.EV_ID         =#{evId}
	       AND      eer.EV_CMPL_YN   = 'Y'
	       AND      ee.OPT_TXB_ID    =  #{optTxbId}
	       GROUP BY ee.EV_ID,d.TPC_ID
	       )
	       A
	       INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN BALAC
	       ON     A.TPC_ID     = BALAC.KMMP_NOD_ID
	       AND    BALAC.DEL_YN = 'N'
	       AND
	              (
	                     BALAC.CTN_TP_CD = 'PL' OR BALAC.CTN_TP_CD = 'HT'
	              )
	       INNER JOIN LMS_CMS.BC_AI_CTN_META_DATA BACMD
	       ON     BALAC.AI_LRN_ATV_ID = BACMD.AI_LRN_ATV_ID

 		/** 평가공통 - 노성용 - EaEvCom-Mapper.xml - 교사 메인 평가 AI 조회   - selectEvRptTcrAiList*/
 	</select>

	 <!--    교사 메인 평가 현황    -->
	<select id="selectEvRptTcrState" resultType="hashMap">

		SELECT
			ee.EV_ID as evId,
			count(distinct EER.USR_ID) userCnt,
			round(avg(
			  (eer.CANS_CNT + ifnull(eer2.CANS_CNT, 0))
			/ (ee.qst_cnt + ifnull(eer2.QST_CNT , 0))
			* 100), 2) as cansRt
		FROM  EA_EV ee
			inner join EA_EV_RS EER
				on eer.EV_ID = ee.EV_ID
			inner join cm_usr cm on EER.USR_ID=cm.USR_ID and cm.USR_TP_CD='ST'
			left join ea_ev_spp_ntn_rs eer2 on eer2.EV_ID = ee.EV_ID and eer2.USR_ID = eer.USR_ID and eer2.EV_CMPL_YN = 'Y'
		where ee.EV_ID=#{evId}
		and eer.EV_CMPL_YN = 'Y'
		AND ee.OPT_TXB_ID = #{optTxbId}

 		/** 평가공통 - 노성용 - EaEvCom-Mapper.xml -  교사 메인 평가 현황   - selectEvRptTcrState*/
 	</select>

	<select id="selectCurrentTxmPn" resultType="String">
		SELECT
			CASE
				WHEN RS.EV_CMPL_YN IS NULL THEN 0
				<if test='sppNtnYn == "Y"'>
				WHEN SP.EV_CMPL_YN IS NULL THEN 0
				</if>
		  		ELSE IFNULL(MAX(ER.RTXM_PN), 0) + 1
		  	END AS txmPn
		FROM LMS_LRM.EA_EV E
		LEFT JOIN LMS_LRM.EA_EV_RS RS
			ON RS.EV_ID = E.EV_ID AND RS.USR_ID = #{usrId} AND RS.EV_CMPL_YN = 'Y'
		<if test='sppNtnYn == "Y"'>
		LEFT JOIN LMS_LRM.EA_EV_SPP_NTN_RS SP
			ON SP.EV_ID = E.EV_ID AND SP.USR_ID = #{usrId} AND SP.EV_CMPL_YN = 'Y'
		</if>
		LEFT JOIN LMS_LRM.EA_EV_RS_RTXM ER
			ON ER.EV_ID = E.EV_ID AND ER.USR_ID = #{usrId} AND ER.EV_CMPL_YN = 'Y'
		WHERE E.EV_ID = #{evId}
 		/** 평가공통 - EaEvCom-Mapper.xml -  평가 현재 회차 조회   - selectCurrentTxmPn */
 	</select>

	<select id="countSppNtnAnw" resultType="int">
		SELECT COUNT(1) FROM LMS_LRM.EA_EV_SPP_NTN_QTM_ANW
		WHERE EV_ID = #{evId}
		AND USR_ID =  #{usrId}
		AND SMT_ANW_VL IS NOT NULL
		AND SMT_ANW_VL != ''
 		/** 평가공통 - EaEvCom-Mapper.xml -  오답/심화 제출 답변 수 카운트   - countSppNtnAnw */
 	</select>

	<!--   평가완료 후 국가표준쳬계 교육과정에 포함된 문항들의 누적 결과 조회 :     -->
    <select id="selectEvCmplCrclCtnElm2CdList"  resultType="hashMap">
		
 	  SELECT  
	    BNCM.CRCL_CTN_ELM2_CD AS crclCtnElm2Cd,
	    COUNT(BNCM.CTN_ID) AS totCnt,
	               SUM(case when EV.EV_CMPL_YN='Y' then 1 else 0 end) AS evCnt
	              , SUM(IF(EV.CANS_YN = 'Y', 1, 0)) AS evCansCnt
	              , ROUND(SUM(case when EV.EV_CMPL_YN='Y' then 1 else 0 end)/COUNT(BNCM.CTN_ID)*100, 2) AS pgrsRt
	              , IF(COUNT(EV.QTM_ID) = 0, 0, ROUND(SUM(IF(EV.CANS_YN = 'Y', 1, 0))/COUNT(EV.QTM_ID)*100, 2)) AS cansRt   
		FROM LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM
		INNER JOIN (
		    SELECT E.EV_ID, EQ.QTM_ID, ER.USR_ID, ER.EV_CMPL_YN,   IFNULL(EQAR.CANS_YN, EQA.CANS_YN)  AS CANS_YN 
		    FROM LMS_LRM.EA_EV E    
		    INNER JOIN  LMS_CMS.bc_evsh bce ON bce.EVSH_ID=E.EVSH_ID  AND bce.DEL_YN='N'
		    INNER JOIN LMS_LRM.EA_EV_QTM EQ ON EQ.EV_ID = E.EV_ID
		    LEFT JOIN LMS_LRM.EA_EV_RS ER ON ER.EV_ID = E.EV_ID
		    LEFT JOIN LMS_LRM.EA_EV_QTM_ANW EQA 
		        ON EQA.EV_ID = ER.EV_ID 
		        AND EQA.USR_ID = ER.USR_ID 
		        AND EQA.QTM_ID = EQ.QTM_ID
		     LEFT JOIN (
		                        SELECT ERR_MAX.EV_ID, ERR_MAX.USR_ID, MAX(ERR_MAX.RTXM_PN) RTXM_PN
		                        FROM LMS_LRM.EA_EV_RS_RTXM ERR_MAX
		                        WHERE EXISTS (SELECT 1 FROM LMS_LRM.EA_EV E2 WHERE E2.OPT_TXB_ID = #{optTxbId} AND E2.EV_DV_CD = 'SE' AND E2.DEL_YN = 'N' AND E2.USE_YN = 'Y' AND E2.EV_ID = ERR_MAX.EV_ID)
		                        AND ERR_MAX.USR_ID =  #{usrId}
		                        AND ERR_MAX.EV_CMPL_YN = 'Y'
		                        GROUP BY ERR_MAX.EV_ID, ERR_MAX.USR_ID
		                ) ERR_MAX ON ERR_MAX.EV_ID = ER.EV_ID AND ERR_MAX.USR_ID = ER.USR_ID
		                LEFT JOIN LMS_LRM.EA_EV_QTM_ANW_RTXM EQAR ON EQAR.EV_ID = EQA.EV_ID AND EQAR.USR_ID = EQA.USR_ID AND EQAR.QTM_ID = EQA.QTM_ID AND EQAR.RTXM_PN = ERR_MAX.RTXM_PN    
		    WHERE E.OPT_TXB_ID = #{optTxbId}
		      AND E.EV_DV_CD = 'SE'
		      AND E.USE_YN = 'Y'
		      AND E.EV_DTL_DV_CD in ('FO','TO')
		      AND EQ.DEL_YN = 'N'
		      AND ER.USR_ID =  #{usrId}
		) EV ON EV.QTM_ID = BNCM.CTN_ID
		INNER  JOIN (
		    SELECT DISTINCT BNCM2.CRCL_CTN_ELM2_CD
		    FROM LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 BNCM2
		    WHERE BNCM2.CRCL_CTN_TP_CD = 'EX'
		      AND EXISTS (
		          SELECT 1  
		          FROM LMS_LRM.EA_EV E3 
		          INNER  JOIN LMS_LRM.EA_EV_QTM EQ3 ON EQ3.EV_ID = E3.EV_ID
		          WHERE E3.EV_ID = #{evId}
		            AND EQ3.QTM_ID = BNCM2.CTN_ID
		            AND EQ3.DEL_YN = 'N'
		            AND E3.EV_DV_CD = 'SE'
		            AND E3.EV_DTL_DV_CD in ('FO','TO')
		      )
		) TE ON BNCM.CRCL_CTN_ELM2_CD = TE.CRCL_CTN_ELM2_CD
		WHERE BNCM.CRCL_CTN_TP_CD = 'EX'
		GROUP BY BNCM.CRCL_CTN_ELM2_CD    
        
		
        /* EaEvCom-Mapper.xml - selectEvCmplCrclCtnElm2CdList - 평가완료 후 국가표준쳬계 교육과정에 포함된 문항들의 진척율 조회 - 박원희 */

    </select>
    
    <select id="selectLrmp" resultType="Map" parameterType="com.aidt.api.ea.evcom.dto.InsertEaEvQtmAnwReqDto">
	    select LRMP_NOD_ID, LRN_ATV_ID from LMS_LRM.tl_sbc_lrn_atv_rcstn where OPT_TXB_ID = #{optTxbId} and CTN_TP_CD = 'EX' and EV_ID = #{evId};
		    /* 평가 공통 - EaEvCom-Mapper.xml - 평가 차시, 액티비티 조회 selectLrmp*/
	</select>

	<delete id="deleteAnnx" parameterType="Long">
		/** EaEvCom-Mapper.xml - 첨부 삭제 */
		DELETE
		FROM LMS_LRM.CM_ANNX
		WHERE 1 = 1
			AND ANNX_ID = #{annxId}
	</delete>

	<delete id="deleteAnnxFle" parameterType="Long">
		/** EaEvCom-Mapper.xml - 첨부 파일 삭제 */
		DELETE
		FROM LMS_LRM.CM_ANNX_FLE
		WHERE 1 = 1
		  AND ANNX_ID = #{annxId}
	</delete>

	<update id="updateEaEvQtmAnwAnnxId" parameterType="com.aidt.api.ea.evcom.dto.UpdateNoteReqDto">
		/** EaEvCom-Mapper.xml - 일반 평가 문항 답변에 연결된 노트 update */
		UPDATE
		    LMS_LRM.EA_EV_QTM_ANW
		SET
			MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
			, ANNX_FLE_ID = 0 -- 연결된 첨부파일ID 삭제
		WHERE 1 = 1
			AND EV_ID = #{evId}
			AND QTM_ID = #{qtmId}
			AND USR_ID = #{usrId}
	</update>

	<update id="updateEaEvQtmAnwRtxmAnnxId" parameterType="com.aidt.api.ea.evcom.dto.UpdateNoteReqDto">
		/** EaEvCom-Mapper.xml - 재응시 평가 문항 답변에 연결된 노트 update */
		UPDATE
		    LMS_LRM.EA_EV_QTM_ANW_RTXM
		SET
			MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
			, ANNX_FLE_ID = 0 -- 연결된 첨부파일ID 삭제
		WHERE 1 = 1
			AND EV_ID = #{evId}
			AND QTM_ID = #{qtmId}
			AND USR_ID = #{usrId}
			AND RTXM_PN = #{rtxmPn}
	</update>

	<update id="updateEaEvSppNtnQtmAnwAnnxId" parameterType="com.aidt.api.ea.evcom.dto.UpdateNoteReqDto">
		/** EaEvCom-Mapper.xml - 유사/심화 평가 문항 답변에 연결된 노트 update */
		UPDATE
			LMS_LRM.EA_EV_SPP_NTN_QTM_ANW
		SET
		    MDFR_ID = #{usrId}
		  	, MDF_DTM = NOW()
		  	, ANNX_FLE_ID = 0 -- 연결된 첨부파일ID 삭제
		WHERE 1 = 1
			AND EV_ID = #{evId}
			AND QTM_ID = #{qtmId}
			AND USR_ID = #{usrId}
		  	AND TXM_PN = #{txmPn}
	</update>
</mapper>