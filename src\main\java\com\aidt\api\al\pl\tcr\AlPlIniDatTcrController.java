package com.aidt.api.al.pl.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AlPlTcrDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Tag(name="[al] AI맞춤학습 초기데이터 작성", description="로그인한 교사가 담당하는 담당클래스를 조회하여, AI맞춤학습 재구성 초기데이터를 작성한다.")
@RestController
@RequestMapping("/api/v1/al/tcr/alPlIniDat")
public class AlPlIniDatTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
	private AlPlIniDatTcrService alPlIniDatTcrService;
	
	/**
     * AI_지식맵노드재구성 초기생성
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="AI맞춤학습 지식맵노드재구성", description="AI_지식맵노드재구성 초기데이터를 생성한다.")
    @PostMapping(value = "/registIniDat")
    public ResponseDto<Integer> registIniDat() {
        log.debug("AlPlIniDatTcrController registIniDat");
        
        //CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        AlPlTcrDto dto = new AlPlTcrDto();
        
        //세션정보에서 설정
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	dto.setUsrId(securityUserDetailDto.getUsrId());
        
        return Response.ok(alPlIniDatTcrService.registIniDat(dto));
    }
}
