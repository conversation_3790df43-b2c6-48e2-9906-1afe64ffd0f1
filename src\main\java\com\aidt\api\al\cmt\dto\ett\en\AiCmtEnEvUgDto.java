package com.aidt.api.al.cmt.dto.ett.en;

import java.util.List;

import com.aidt.api.al.cmt.dto.ett.cm.N02Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N05Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnEvUgDto {

    private N02Dto n02;

    private N05Dto n05;

    private String araLvlCd;

    private N12Dto n12;

    private Boolean existAiRcmCtn;

    private List<String> araRankList;

    @Builder.Default
    private String usrTpCd = "ST";

}
