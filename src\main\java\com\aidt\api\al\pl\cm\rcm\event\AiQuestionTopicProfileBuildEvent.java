package com.aidt.api.al.pl.cm.rcm.event;

import java.util.List;

import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvLoader;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvPredictProfile;
import com.aidt.common.CommonUserDetail;

import lombok.Getter;

@Getter
public class AiQuestionTopicProfileBuildEvent {

	//todo: 비동기 진행에 필요한 도메인 추가

	private final EaAiEvLoader eaAiEvLoader;
	private final List<EaAiEvPredictProfile> eaAiEvQuestionAnswers;
	private final CommonUserDetail userDetail;
	private final String accessToken;

	public AiQuestionTopicProfileBuildEvent(EaAiEvLoader eaAiEvLoader, List<EaAiEvPredictProfile> eaAiEvQuestionAnswers,
		CommonUserDetail userDetail, String accessToken) {
		this.eaAiEvLoader = eaAiEvLoader;
		this.eaAiEvQuestionAnswers = eaAiEvQuestionAnswers;
		this.userDetail = userDetail;
		this.accessToken = accessToken;
	}
}
