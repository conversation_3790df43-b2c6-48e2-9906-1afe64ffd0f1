package com.aidt.api.al.pl.cm.mg;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-20 
 * @modify date 2024-02-20 
 * @desc AI맞춤학습 단원차시조회
 */
//@Slf4j
@Tag(name="[al] AI맞춤학습 학습맵지식맵 관리", description="AI맞춤학습 학습맵지식맵 관리")
@RestController
@RequestMapping("/api/v1/al/cm/mg")
public class AlMgController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired 
	private AlMgService mgService;
	
	/**
     * 학습맵 대단원 ID와 매핑된 지식맵 중단원 ID 조회
     * @param lrmpNodId
     * @return KmmpNodId
     */
    @Operation(summary="학습맵 대단원 ID와 매핑된 지식맵 중단원 ID 조회", description="학습맵 대단원 ID와 매핑된 지식맵 중단원 ID 조회")
    @PostMapping(value="/selectKmmpNodIdByLrmpNodId")
    public ResponseDto<List<String>> selectKmmpNodIdByLrmpNodId(@RequestBody String lrmpNodId) {

        if (lrmpNodId == null) {
            return Response.fail("Params does not exist.");
        }

        List<String> KmmpNodIds = mgService.selectKmmpNodIdByLrmpNodId(lrmpNodId);

        return Response.ok(KmmpNodIds);
    }
    
    /**
     * 학습맵 대단원 ID와 매핑된 지식맵 중단원 ID 조회
     * @param lrmpNodId
     * @return KmmpNodId
     */
    @Operation(summary="지식맵 중단원 정보만 조회", description="지식맵 중단원 정보만 조회")
    @GetMapping(value="/selectKmmpNodList")
    public ResponseDto<List<AiRcmTsshQtmDto>> selectKmmpNodList() {
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	AiRcmTsshQtmDto dto = new AiRcmTsshQtmDto();
    	if(dto.getOptTxbId() == null) {
    		dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
    	}

        return Response.ok(mgService.selectKmmpNodList(dto));

    }
}
