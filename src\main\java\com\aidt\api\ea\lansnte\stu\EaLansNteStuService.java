package com.aidt.api.ea.lansnte.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.ea.evcom.dto.EaEvComQtmAnwDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmDto;
import com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto;
import com.aidt.api.ea.evcom.lansnte.dto.CreateQuesSimilarDiyDto;
import com.aidt.api.ea.evcom.lansnte.dto.EaLansNteReqDto;
import com.aidt.api.ea.evcom.lansnte.dto.EaLansNteResDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 오답 노트 - 학생 Service
 */
@Slf4j
@Service
public class EaLansNteStuService {

	private final String MAPPER_NAMESPACE = "api.ea.lansnte.stu.";
	private final String DOMAIN_PATH = "/api/v1/content/";

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

	@Value("${spring.profiles.active}")
	private String SERVER_ACTIVE;


	@Autowired
	private CommonDao commonDao;


    public String getLocalDomain() {
        String domain = "";
		try {
//				BUCKET_NAME = BUCKET_NAME.replace("dev", "prd");

				if("local".equals(SERVER_ACTIVE)) {
					domain = "https://www-n-ele.aitextbook.co.kr";
				}
		}
        catch (Exception e) {
        	log.warn(e.getMessage());
        }

		return domain;
    }

	/**
	 * 학생 오답노트 단원별 목록 조회 요청
	 *
	 * @param dto
	 * @return Map<String, Object>
	 */
	public Map<String, Object> selectLansNteLuListNew(EaLansNteReqDto dto) {
		Map<String, Object> iansInfo = new HashMap<>();
		iansInfo.put("iansList", commonDao.selectList(MAPPER_NAMESPACE + "selectLansNteLuListNew", dto));
		iansInfo.put("iansQtmList", commonDao.selectList(MAPPER_NAMESPACE + "selectLansNteLuQtmList", dto));

		return iansInfo;
	}

	/**
	 *  학생 오답노트 단원별 체크된 단원 학습창전달 문항리스트 조회 요청 - 변경 완
	 *
	 * @param evReqDto
	 * @return EaEvMainResDto
	 */
	public List<Map<String, Object>> selectIansQtmInfoList(EaEvQtmIdReqDto reqDto) {

		if(reqDto.getQtmIdList() == null || reqDto.getQtmIdList().isEmpty())
		{
			return null;
		}

		//문항리스트
    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	reqDto.setBucketUrl(objUrl);

    	// 평가ID, 문항ID로 조회
    	return commonDao.selectList(MAPPER_NAMESPACE + "selectIansQtmInfoFromEvQtmList", reqDto);
	}


	/**
	 * 학생 오답노트 시험지별 탭 > 목록 조회 리스트 - 수정 완
	 *
	 * @param dto
	 * @return List<EaLansNteResDto>
	 */
	public List<EaLansNteResDto> selectLansNteTsPaperList(EaLansNteReqDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectLansNteTsPaperList", dto);
	}


	/**
	 * 학생	오답노트 - 시험지 별 > 오답 문제 풀기 > 학습창전달 오답 문항 정보 조회 - 수정 완
	 *
	 * @param dto
	 * @return List<EaLansNteResDto>
	 */
	public List<Map<String, Object>> selectLansNteTsPaperQpList(CreateQuesSimilarDiyDto dto) {

		//문항리스트
    	String objUrl = getLocalDomain() + DOMAIN_PATH + BUCKET_NAME;
    	dto.setBucketUrl(objUrl);

		return commonDao.selectList(MAPPER_NAMESPACE + "selectLansNteTsPaperQpList", dto);
	}


	/**
	 * 학생,선생	오답노트 - 시험지 별 오답 문제 풀기 - 오답 유사 문항 조회
	 *
	 * @param dto
	 * @return List<EaLansNteResDto>
	 */
	public EaEvQtmIdReqDto createQuesSimilarDiy(EaEvQtmIdReqDto dto) {

		long evId = dto.getEvId();
		dto.setEvIdOld(evId);

		//평가 오답문제의 유사문제 리스트 조회
		//프로세스
		// 1. 평가의 오답 문항의 문항ID, 난이도 리스트 조회
		// 2. 한 문항씩 "연관문항테이블", "AI학습콘텐츠 테이블", "평가답변 (오답인 원 문항 정보 - 앞에 두개 테이블에 데이터 없을 경우)"
		//#{qtmId}#{txbId}, #{evId}, #{usrId}

		// 평가답변 오답문항에 대한 정보를 조회.
		List<EaEvQtmIdReqDto> iansQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectQtmListFromEvId", dto);

		if(iansQtmList == null || iansQtmList.isEmpty()) {
			return null;
		}

		// 오답문항 갯수만큼 유사문항 조회하기위해
		dto.setIansQtmCnt(iansQtmList.size());


		// 오답문항의 유사문항 조회
    	int qtmOrdn = 1;
		List<EaEvQtmIdReqDto> smlrList = new ArrayList<>();
		List<EaEvQtmIdReqDto> smlrTmp = new ArrayList<>();
		List<EaEvQtmIdReqDto> smlrTmp2 = new ArrayList<>();

		smlrTmp.addAll(iansQtmList);
		Map<String, Object> tmp =  new HashMap<>();

		for(EaEvQtmIdReqDto qtm : iansQtmList) {
			tmp.put("optTxbId", dto.getOptTxbId());
			tmp.put("txbId", dto.getTxbId());
			tmp.put("qtmId", qtm.getQtmId());
			tmp.put("tpcId", qtm.getTpcId());
			tmp.put("qtmDffdCd", qtm.getQtmDffdCd());
			tmp.put("qtmIdNotIn", smlrTmp);
			tmp.put("qtmOrdn", qtmOrdn++);

			// 한문항씩 유사문항 조회
			smlrTmp2 = commonDao.selectList(MAPPER_NAMESPACE + "selectIansSmlrQtmList", tmp);
			smlrTmp.addAll(smlrTmp2); // 오답문항 + 유사문항 중복 제거용 리스트
			smlrList.addAll(smlrTmp2); // 유사문항 전용 리스트
		}

		// 유사문항리스트 없음
    	if(smlrList == null || smlrList.isEmpty()) {
    		return null;
    	}

    	// 조회된 유사문항의 평가문항정보 조회
		dto.setSmrlQtmList(smlrList);
		List<EaEvQtmDto> crtEvQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectIansSmlrCrtEvQtmList", dto);

		if(crtEvQtmList == null || crtEvQtmList.isEmpty()) {
    		return null;
    	}

		dto.setCrtEaEvQtmList(crtEvQtmList);
		dto.setQstCnt(crtEvQtmList.size());

    	//평가 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvFromSmlrEv", dto);

		//평가 시험범위 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRngeTsPaperEvId", dto);

		//평가 문항 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvQtmFromSmlrEv", dto);
		//평가 난이도구성 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvDffdFromSmlrEv", dto);
		//평가 결과 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvRsFromSmlrEvStu", dto);

		dto.setQtmIdList(null);
		dto.setCrtLluList(null);
		dto.setCrtEaEvQtmList(null);

		return dto;
	}

    /**
     * 오답노트 > 단원별 > 오답 유사문제 풀기 ==> 평가 생성
     *
     * @param eaEvComDto
     * @return EaEvComQtmReqDto
     */
    @Transactional
	public EaEvQtmIdReqDto createEvSmlrLuQtmList(EaEvQtmIdReqDto reqDto) {

    	int qtmOrdn = 1;
    	List<EaEvQtmDto> crtEvQtmList = new ArrayList<>();

    	if(reqDto.getIansQtmCrtYn() != null && reqDto.getIansQtmCrtYn().equals("Y")) {
    		// 오답문제 리스트 조회
    		crtEvQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvIansQtmIdList", reqDto);
    	}
    	else {
    		// 오답문항 갯수만큼 유사문항 조회하기위해
    		reqDto.setIansQtmCnt(reqDto.getQtmIdList().size());

    		// 오답문항의 유사문항 조회
    		List<EaEvQtmIdReqDto> smlrList = new ArrayList<>();
    		List<EaEvQtmIdReqDto> smlrTmp = new ArrayList<>();
    		List<EaEvQtmIdReqDto> smlrTmp2 = new ArrayList<>();

    		smlrTmp.addAll(reqDto.getQtmIdList());
    		Map<String, Object> tmp =  new HashMap<>();

    		for(EaEvQtmIdReqDto qtm : reqDto.getQtmIdList()) {
    			tmp.put("optTxbId", reqDto.getOptTxbId());
    			tmp.put("txbId", reqDto.getTxbId());
    			tmp.put("qtmId", qtm.getQtmId());
    			tmp.put("tpcId", qtm.getTpcId());
    			tmp.put("qtmDffdCd", qtm.getQtmDffdCd());
    			tmp.put("qtmIdNotIn", smlrTmp);
    			tmp.put("qtmOrdn", qtmOrdn++);

    			// 한문항씩 유사문항 조회
    			smlrTmp2 = commonDao.selectList(MAPPER_NAMESPACE + "selectIansSmlrQtmList", tmp);
    			smlrTmp.addAll(smlrTmp2); // 오답문항 + 유사문항 중복 제거용 리스트
    			smlrList.addAll(smlrTmp2); // 유사문항 전용 리스트
    		}

    		// 유사문항리스트 없음
        	if(smlrList == null || smlrList.isEmpty()) {
        		return null;
        	}

    		// 조회된 유사문항의 평가문항정보 조회
    		reqDto.setSmrlQtmList(smlrList);
    		crtEvQtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectIansSmlrCrtEvQtmList", reqDto);
    	}

     	// 유사문항리스트 없음
    	if(crtEvQtmList == null || crtEvQtmList.isEmpty()) {
    		return null;
    	}

    	reqDto.setCrtEaEvQtmList(crtEvQtmList);
    	reqDto.setQstCnt(crtEvQtmList.size());

    	//평가 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvFromSmlrEv", reqDto);
		//평가 시험범위 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvTsRngeFromSmlrEv", reqDto);
		//평가 문항 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvQtmFromSmlrEv", reqDto);
		//평가 난이도구성 생성
		commonDao.insert(MAPPER_NAMESPACE + "insertEvDffdFromSmlrEv", reqDto);
		//평가 결과 생성
		if(reqDto.getEvDvCd().equals("TE")){
			commonDao.insert(MAPPER_NAMESPACE + "insertEvRsFromSmlrEvTcr", reqDto);
		}
		else {
			commonDao.insert(MAPPER_NAMESPACE + "insertEvRsFromSmlrEvStu", reqDto);
		}


		reqDto.setQtmIdList(null);
		reqDto.setCrtLluList(null);
		reqDto.setCrtEaEvQtmList(null);


        return reqDto;
    }

	/**
	 * 오답노트 > 시험지별 > 대단원 리스트
	 *
	 * @param param
	 * @return List<Map<String,Object>>
	 */
	public List<Map<String,Object>> selectWrongAnwBestLluList(EaLansNteReqDto param) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectWrongAnwBestLluList", param);
	}

	/**
	 * 오답노트 > 시험지별 > 오답 BEST > 학생리스트 조회 요청
	 *
	 * @param param
	 * @return List<Map<String,Object>>
	 */
	public List<Map<String,Object>> selectWrongAnwBestStuList(EaLansNteReqDto param) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectWrongAnwBestStuList", param);
	}

	/**
	 * 오답노트 > 시험지별 > 맞힌 문항 조회 요청
	 * <p>오답노트 학습을 통해 맞힌 문항 조회</p>
	 * @param param {optTxbId(운영교과서 ID), usrId(사용자ID), evId(평가지ID)}
	 * @return List<Integer>
	 */
	public List<EaEvComQtmAnwDto> selectIansNoteCansYQtmList(EaLansNteReqDto param){
		return commonDao.selectList(MAPPER_NAMESPACE + "selectIansNoteCansYQtmList", param);
	}
}



