package com.aidt.api.sl.splrn.dto;

import java.util.Date;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-13 8:41:26
 * @modify : date 2024-02-13 8:41:26
 * @desc : 특별학습 콘텐츠 DTO
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnCtnDto {

	@Parameter(name="특별학습ID")
	private String spLrnId;

	@Parameter(name="특별학습노드ID")
	private String spLrnNodId;

	@Parameter(name="특별학습콘텐츠ID")
	private String spLrnCtnId;

	@Parameter(name="특별학습콘텐츠코드")
	private String spLrnCtnCd;

	@Parameter(name="특별학습콘텐츠명")
	private String spLrnCtnNm;

	@Parameter(name="콘텐츠유형코드")
	private String fleMgTpCd;

	@Parameter(name="CDN경로명")
	private String cdnPthNm;

	@Parameter(name="압축파일명")
	private String cmprFleNm;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성일시")
	private Date crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private Date mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;
	

}
