package com.aidt.api.tl.cmtxb.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 10:43:39
 * @modify date 2024-04-02 10:43:39
 * @desc [학습상세현황 대단원 dto]
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbLrnDtlLluDto {
    /** 학습맵 노드 ID */
    @Parameter(name="학습맵 노드 ID")
    private String lrmpNodId;
    
    /** 학습맵 노드명 */
    @Parameter(name="학습맵 노드명")
    private String lrmpNodNm;
    
    /** 깊이 */
    @Parameter(name="깊이")
    private int dpth;
    
    /** 재구성 순서 */
    @Parameter(name="재구성 순서")
    private int rcstnOrdn;

    /** 잠금여부 */
    @Parameter(name="잠금여부")
    private String lcknYn;

    /** 사용여부 */
    @Parameter(name="사용여부")
    private String useYn;

    /** 단원 내 활동 수*/
    @Parameter(name="단원 내 활동 수")
    private int atvCnt;

    /** 단원 내 활동 완료 수*/
    @Parameter(name="단원 내 활동 완료 수")
    private int clCnt;

    /** 진행률*/
    @Parameter(name="진행률")
    private double pgrsRt;

    /** 차시 리스트*/
    @Parameter(name="차시 리스트")
    private List<TlCmTxbLrnDtlTcDto> tcList;
}
