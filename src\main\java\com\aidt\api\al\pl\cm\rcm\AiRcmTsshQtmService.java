package com.aidt.api.al.pl.cm.rcm;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmHistDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmReqDto;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmResponseDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-28
 * @modify date 2024-02-28
 * @desc AI맞춤 문항추천 - 초중고 수학
 */
@Slf4j
@Service
public class AiRcmTsshQtmService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.back.aiRcmTsshQtm.";
	
	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private AlQtmTpcProfService alQtmTpcProfService;
	
	@Autowired
	private AiRcmTsshQtmCommService commService;
	
	@Autowired
	private AiRcmTsshQtmMaService maService;
	
	@Autowired
	private AiRcmTsshQtmEnService enService;
	
	
	/**
     * AI 맞춤 문항추천 서비스
     * 
     * @param dto
     * @return AiRcmTsshQtmResponseDto
     */
    @Transactional
    public AiRcmTsshQtmResponseDto selectAiRcmTsshQtmList(AiRcmTsshQtmReqDto dto) {
		log.info("AI 맞춤 문항추천 서비스 -----------------------------------------------------");
		log.info(" -----------------------------------------------------");
		/* 운영교과서에 맞는 지식맵 노드 id가 파라미터 값으로 넘어왔는지 검증(2024.10.30) */
    	AiRcmTsshQtmResponseDto responseDto = new AiRcmTsshQtmResponseDto();

    	dto.setKmmpNodId(dto.getMluKmmpNodId());
		int nodeCount = commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodCount", dto);
		if (nodeCount < 1) {
			responseDto.setErrorStatus("error");	
			responseDto.setErrorMessage("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
			return responseDto;
//		throw new Error("운영 교과서 ID에 매칭되는 지식맵 노드 ID가 없습니다.");
		}

        List<AiRcmTsshQtmDto> qtmList = new ArrayList<>();
        
        //진단평가
        if(null != dto.getEvDtlDvCd() && dto.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
            
//            //수학만 진단평가시 모든문항의 예측값을 받는다.
//			if((AlConstUtil.SBJ_MA.contains(dto.getSbjCd()))){
//				qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiRcmTsshQtmList", dto);
//				
//				//AI center 예측점수 API 호출 - 진단평가일때만
//				AlPlQtmTpcProfDto alPlQtmTpcProfDto = new AlPlQtmTpcProfDto();
//				alPlQtmTpcProfDto.setUsrId(dto.getUsrId());
//				alPlQtmTpcProfDto.setLrnrVelTpCd("NM");
//				alPlQtmTpcProfDto.setSbjCd(dto.getSbjCd());
//				alPlQtmTpcProfDto.setSchlGrdCd(qtmList.get(0).getSchlGrdCd());
//				alPlQtmTpcProfDto.setAutrCd(qtmList.get(0).getAutrCd());
//				alPlQtmTpcProfDto.setSgyCd(qtmList.get(0).getSgyCd());
//				alQtmTpcProfService.postAiCenterPredict(qtmList, alPlQtmTpcProfDto);
//			}
			
			//진단평가 시험지문항 조회
			dto.setEschEnSetDv("AI");
			qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectEevshQtmList", dto);
			
			
        }
        //맞춤학습, DIY
        else {
            
            //문항리스트 생성 :: 중단원 내의 모든 문항리스트를 생성한다. 차시가 지정되어있는 경우 차시 내 모든 문항리스트를 생성
            qtmList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiRcmTsshQtmList", dto);
            if(qtmList.isEmpty() || qtmList.size() == 0) {
            	responseDto.setErrorStatus("error");	
    			responseDto.setErrorMessage("문항정보가 없습니다.");
    			return responseDto;
//                throw new IllegalArgumentException("문항정보가 없습니다.");
            }
            
	        //학습자 수준 조회
	        String lrnrVelTpCd = commonDao.select(MAPPER_NAMESPACE + "selectLrnrVelTpCd", dto);
	        if(lrnrVelTpCd == null) {lrnrVelTpCd = "NM";}
	        dto.setLrnrVelTpCd(lrnrVelTpCd);
            
	        
	    	//AI맞춤학습
	    	if(dto.getEvDvCd().equals("AE")) {
	    		
	    		if(dto.getEvDtlDvCd() == null) {
	    			responseDto.setErrorStatus("error");	
	    			responseDto.setErrorMessage("[evDtlDvCd](은)는 필수 입력 값 입니다.");
	    			return responseDto;
//	    			throw new IllegalArgumentException("[evDtlDvCd](은)는 필수 입력 값 입니다.");
	    		}
	    		if(!"OV,C1,C2".contains(dto.getEvDtlDvCd())) {
	    			responseDto.setErrorStatus("error");	
	    			responseDto.setErrorMessage("[evDtlDvCd]값이 올바르지 않습니다.");
	    			return responseDto;
//	    			throw new IllegalArgumentException("[evDtlDvCd]값이 올바르지 않습니다.");
	    		}
	    		
	    		//3.8v에서는 주석처리
	    		//수학
				if(AlConstUtil.SBJ_MA.contains(dto.getSbjCd())) {
//					if(dto.getEvDtlDvCd().equals("C1")) { 
//		    			qtmList = maService.getMaC1QtmList(qtmList, lrnrVelTpCd, dto);
//					}
//		    		else if(dto.getEvDtlDvCd().equals("C2")) {
//		    			qtmList = maService.getMaC2QtmList(qtmList, lrnrVelTpCd, dto);
//		    		}
//		    		else if(dto.getEvDtlDvCd().equals("C3")) { 
//		    			qtmList = maService.getMaC3QtmList(qtmList, lrnrVelTpCd); 
//					}
				}
				//3.8v에서는 주석처리
				
				//중고등영어
				if((AlConstUtil.SBJ_EN.contains(dto.getSbjCd()))
						&& (dto.getSchlGrdCd().equals("M") || dto.getSchlGrdCd().equals("H"))) {
					
					//중고등영어 공통 추천문항수
					Integer rcmQtmCnt = AlConstUtil.RCM_QTM_CNT_EN;
					
					//예외문항수
					if(dto.getTxbId().equals(AlConstUtil.K1_CE1_EXCE_RCM_QTM)
							|| dto.getTxbId().equals(AlConstUtil.K1_CE2_EXCE_RCM_QTM)
						|| dto.getTxbId().equals(AlConstUtil.S1_EN_EXCE_RCM_QTM)) {
						
						String fieldName = "";
						
						//강상구 공통영어1
						if(dto.getTxbId().equals(AlConstUtil.K1_CE1_EXCE_RCM_QTM)) {
							fieldName = "K1_CE1_EXCE_RCM_QTM_CNT_";
						}
						//강상구 공통영어2
						if(dto.getTxbId().equals(AlConstUtil.K1_CE2_EXCE_RCM_QTM)) {
							fieldName = "K1_CE2_EXCE_RCM_QTM_CNT_";
						}
						//소영순 중학영어
						if(dto.getTxbId().equals(AlConstUtil.S1_EN_EXCE_RCM_QTM)) {
							fieldName = "S1_EN_EXCE_RCM_QTM_CNT_";
						}
						
						//차시명 조회
						String kmmpNodNm = commService.selectKmmpNodNm(dto.getTcKmmpNodId());
						try {
				            fieldName += kmmpNodNm;

				            //예외 추천문항수 조회
				            Field field = AlConstUtil.class.getField(fieldName);
				            rcmQtmCnt = (Integer) field.get(null);
				            
				            log.debug("예외 추천문항수 조회성공! 교과서ID:{}, 문항수:{}, 필드명:{}", dto.getTxbId(), rcmQtmCnt, fieldName);
				        } catch (Exception e) {
				        	log.debug("예외 추천문항수 조회실패! 교과서ID:{}, 문항수:{}, 필드명:{}", dto.getTxbId(), rcmQtmCnt, fieldName);
				        }
					}
					
					if(dto.getEvDtlDvCd().equals("C1")) { 
						qtmList = enService.getMsHschEnC1QtmList(qtmList, lrnrVelTpCd, rcmQtmCnt, dto);
					}
		    		else if(dto.getEvDtlDvCd().equals("C2")) {
		    			qtmList = enService.getMsHschEnC2QtmList(qtmList, lrnrVelTpCd, rcmQtmCnt, dto);
		    		}
//		    		else if(dto.getEvDtlDvCd().equals("C3")) {
//		    			responseDto.setErrorStatus("error");
//		    			responseDto.setErrorMessage("평가가 없습니다.");
//		    			return responseDto;
////		    			throw new IllegalArgumentException("평가가 없습니다.");
//					}
				}
	    		
	    	}
	    	//DIY
	    	else if(dto.getEvDvCd().equals("DE")) {
	    		//영어는 미제공
	    		if(AlConstUtil.SBJ_MA.contains(dto.getSbjCd())) {
	    			//5단계 학습차시 결정 :: 결정된 차시 외의 문항은 제거한다.
	            	if(null == dto.getTcKmmpNodIdList() || dto.getTcKmmpNodIdList().length == 0) {
	            		qtmList = getTcChoc(qtmList, lrnrVelTpCd);
	            	}
	            	
	            	//6단계 학습토픽 추천 우선순위 :: 토픽추천 유형에 따라 토픽 우선순위를 결정한다.
	            	if(null == dto.getTpcKmmpNodIdList() || dto.getTpcKmmpNodIdList().length == 0) {
	            		qtmList = getTpcChoc(qtmList, dto.getTpcRcmDvCd(), lrnrVelTpCd, dto.getEvDtlDvCd());
	            	}
	            	
	            	//7단계 문항 추천 우선순위 :: 문항추천 유형에 따라 문항 우선순위를 결정하고 문항수에 따라 출제
	            	qtmList = getQtmChoc(qtmList, dto.getQtmRcmDvCd(), lrnrVelTpCd);
	    		}
	    		
	    		if(null == qtmList || qtmList.isEmpty()) {
	    			responseDto.setErrorStatus("error");	
	    			responseDto.setErrorMessage("문항정보가 없습니다.");
	    			return responseDto;
//	    			throw new IllegalArgumentException("문항정보가 없습니다.");
	    		}
	    		
	    		//중복제거
				if (dto.getRpetRcmYn().equals("N")) {
					Set<Integer> idSet = new HashSet<>();
					qtmList = qtmList.stream()
							.filter(qtm -> idSet.add(qtm.getQtmId()))
							.collect(Collectors.toList());
				}
				
				
				//토픽별 문항수
				if (dto.getTpcRcmQtmCnt() != null) {
					qtmList = qtmList.stream()
							.collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId))
							.values()
							.stream()
							.flatMap(tpcByQtmList -> tpcByQtmList.stream().limit(dto.getTpcRcmQtmCnt()))
							.collect(Collectors.toList());
				}
				
				//추천문항수
				if(dto.getRcmQtmCnt() != null && qtmList.size() >= dto.getRcmQtmCnt()) {
					qtmList = qtmList.subList(0, Math.min(dto.getRcmQtmCnt(), qtmList.size()));
				}
				
	    	}//diy end
			
		}
		
		//EA_EV 평가 생성 TODO 테스트주석
		Integer evID = commService.insertEaEvHst(dto, qtmList);
		responseDto.setEvId(evID);
		
		return responseDto;
    }
    
    
    
    
    
    /**
     * AI 추천시험지/문항 이력 저장
     * 
     * @param AiRcmTsshQtmDto
     */
    @Transactional
    public void updateAiRcmTsshQtmHist(AiRcmTsshQtmResponseDto aiRcmTsshQtmResponseDto) {
    	
    	List<AiRcmTsshQtmHistDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectXplTmScnt", aiRcmTsshQtmResponseDto);
    	if(list.isEmpty()) {
    		throw new IllegalArgumentException("평가정보가 존재하지 않습니다.");
    	}
		for (AiRcmTsshQtmHistDto dto : list) {
			if(null != dto.getCansYn() && !"".equals(dto.getCansYn()) && null != dto.getXplTmScnt()) {
				//AI_RCM_TSSH_HST AI_추천시험지이력 - 풀이시간초수 update
				commonDao.update(MAPPER_NAMESPACE + "updateAiRcmTsshHst", dto);
				
				//AI_RCM_TSSH_QTM_HST AI_추천시험지문항이력 정답여부, 풀이시간초수 update
				commonDao.update(MAPPER_NAMESPACE + "updateAiRcmTsshQtmHst", dto);
			}
			
		}
    }
    
    
	
	
	/**
	 * 학습 차시 결정
	 * 학습자 수준에 따른 차시 선택
	 * 
	 *  @param List<AiRcmTsshQtmDto>
	 *  @param String lrnrVelTpCd 학습자수준
	 *  @return List<AiRcmTsshQtmDto>
	 * */
	public List<AiRcmTsshQtmDto> getTcChoc(List<AiRcmTsshQtmDto> list, String lrnrVelTpCd) {
		List<AiRcmTsshQtmDto> resultList = list;
		
		if(lrnrVelTpCd.equals("FS")) {
			//점수가 가장 낮은 차시 선택
			String lwstTc = commService.findMinValueDto(resultList, AiRcmTsshQtmDto::getAiPredAvgScr).getTcKmmpNodId();
			resultList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getTcKmmpNodId().equals(lwstTc));
			
			//선택된 차시의 문항풀이수가 10건 이하일경우 AI예측 점수가 가장 낮은 차시로 재선택
			Integer txmPn = resultList.stream().mapToInt(AiRcmTsshQtmDto::getTxmPn).sum();
			if(txmPn < 10) {
				String predLwstDept4 = commService.findMinValueDto(list, AiRcmTsshQtmDto::getAiPredAvgCansRt).getTcKmmpNodId();
				list.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getTcKmmpNodId().equals(predLwstDept4));
				return list;
			}
		}
		else if(lrnrVelTpCd.equals("NM") || lrnrVelTpCd.equals("SL")) {
			//AI예측점수가 높으면서 예측/실제 GAP이 낮은순으로 정렬
			Collections.sort(resultList, Comparator
					.comparing(AiRcmTsshQtmDto::getAiPredAvgCansRt).reversed()
					.thenComparing(Comparator.comparing(AiRcmTsshQtmDto::getTpcPredAtlGap))
					);
			//첫번째 차시 외 제거
			resultList.removeIf(AiRcmTsshQtmDto -> !AiRcmTsshQtmDto.getTcKmmpNodId().equals(resultList.get(0).getTcKmmpNodId()));
		}
		return resultList;
	}
	
	
	/**
	 * 학습 토픽 결정
	 * 학습자 수준에 따른 토픽 추천 우선순위 세팅
	 * 최초학습시 문제: AI맞춤학습에서 진단을 필수로 하기 때문에 진단결과가 없는 경우는 존재할 수 없음
	 * 
	 *  @param List<AiRcmTsshQtmDto>
	 *  @param String tpcRcmDvCd 토픽추천구분
	 *  @param String lrnrVelTpCd 학습자 수준
	 *  @return List<AiRcmTsshQtmDto>
	 * */
	public List<AiRcmTsshQtmDto> getTpcChoc(List<AiRcmTsshQtmDto> list, String tpcRcmDvCd, String lrnrVelTpCd, String EvDtlDvCd) {
		Optional<AiRcmTsshQtmDto> vlnQtmList = list.stream()
				.filter(AiRcmTsshQtmDto -> AiRcmTsshQtmDto.getTpcAvn() < AlConstUtil.TPC_AVN_01)
				.findFirst();
		//취약토픽 존재여부
		Boolean vlnEstYn = vlnQtmList.isPresent() ? true : false; 
		//취약토픽이 없는 경우 실제 점수, AI예측점수가 낮은 순으로 정렬
		if(!vlnEstYn) {
			list.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredAvgScr)
					.thenComparingDouble(AiRcmTsshQtmDto::getAiPredAvgCansRt));
		}
		
		//Default = 취약개선학습
		if(tpcRcmDvCd == null || tpcRcmDvCd == "") {tpcRcmDvCd = "VLN";}
		
		int listCnt = list.size();
		for (AiRcmTsshQtmDto aiRcmTsshQtmDto : list) {
			
			//취약개선학습
			if(tpcRcmDvCd.equals("VLN")) {
				//토픽숙련도(취약:01, 보통:02, 완벽:03)
				Double tpcAvn = aiRcmTsshQtmDto.getTpcAvn();
				
				//취약토픽이 존재할 경우 - 학습자수준(상:취약, 중:취약, 하:취약+보통)에 따라 추천
				if (vlnQtmList.isPresent()) {
					if(tpcAvn < AlConstUtil.TPC_AVN_01) {
						aiRcmTsshQtmDto.setRcmRnk(aiRcmTsshQtmDto.getRcmRnk() + 1);
					}else if(tpcAvn >= AlConstUtil.TPC_AVN_01 && tpcAvn <= AlConstUtil.TPC_AVN_03 && lrnrVelTpCd.equals("SL")) {
						aiRcmTsshQtmDto.setRcmRnk(aiRcmTsshQtmDto.getRcmRnk() + 1);
					}else {
						aiRcmTsshQtmDto.setRcmRnk(aiRcmTsshQtmDto.getRcmRnk() -1);
					}
				}
				else {
					//취약토픽이 없는 경우 정렬순서에 따라 추천 우선순위 세팅한다.
					aiRcmTsshQtmDto.setRcmRnk(aiRcmTsshQtmDto.getRcmRnk() + listCnt);
					listCnt--;
				}
			}//취약개선학습 END
			else if(tpcRcmDvCd.equals("단기점수상승")) {}
			else if(tpcRcmDvCd.equals("완전학습")) {}
			
		}//for END
		
		//추천우선순위로 정렬
		list.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getRcmRnk).reversed());
		
		return list;
	}
	
	/**
	 * 추천문항 우선순위 세팅
	 * 
	 *  @param List<AiRcmTsshQtmDto>
	 *  @param String qtmRcmDvCd 문항추천구분 (수준별, 취약개선, 점수상승, 고난이도, 학습동기 등)
	 *  @param String lrnrVelTpCd 학습자수준(빠른,보통,느린)
	 *  @return List<AiRcmTsshQtmDto>
	 * */
	public List<AiRcmTsshQtmDto> getQtmChoc(List<AiRcmTsshQtmDto> list, String qtmRcmDvCd, String lrnrVelTpCd) {
		//Default = 수준별
		if(qtmRcmDvCd == null || qtmRcmDvCd == "") {qtmRcmDvCd = "LV";}
		
		//문항 추천구분 :: 수준별
		if(qtmRcmDvCd.equals("LV")) {
			if(lrnrVelTpCd.equals("FS") || lrnrVelTpCd.equals("NM")) {
				//오답 유사 or AI예측점수가 낮은 문항순으로 정렬
				list.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt));
			}else if(lrnrVelTpCd.equals("SL")) {
				//오답 유사or AI예측점수가 높은 문항순으로 정렬
				list.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getAiPredCansRt).reversed());
			}
		}
		else if(qtmRcmDvCd.equals("취약개선")) {}
		else if(qtmRcmDvCd.equals("단기점수상승")) {}
		else if(qtmRcmDvCd.equals("완전학습")) {}
		
		//출제방식에 따른 정렬순서에 따라 추천우선순위 세팅
		int listCnt = list.size();
		for (AiRcmTsshQtmDto aiRcmTsshQtmDto : list) {
			aiRcmTsshQtmDto.setRcmRnk(aiRcmTsshQtmDto.getRcmRnk() + listCnt);
			listCnt--;
		}
		
		//추천우선순위로 정렬
		list.sort(Comparator.comparingDouble(AiRcmTsshQtmDto::getRcmRnk).reversed());
		
		return list;
	}
    
    

}
