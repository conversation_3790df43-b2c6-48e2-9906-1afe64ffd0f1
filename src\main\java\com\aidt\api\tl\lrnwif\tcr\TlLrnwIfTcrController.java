package com.aidt.api.tl.lrnwif.tcr;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnAtvDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnPdfDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocAtvDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebSaveDto;
import com.aidt.api.tl.lrnwif.stu.TlLrnwIfStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 15:57:52
 * @modify date 2024-02-15 15:57:52
 * @desc TlLrnwIfTcr Service 교과학습 학습창연계처리 서비스
 */
@Slf4j
@Tag(name="[tl] 교과학습 학습창연계처리[TlLrnwIfTcr]", description="학습창관련 연계데이터 처리(교사용)")
@RestController
@RequestMapping("/api/v1/tl/tcr/lrnwif")
public class TlLrnwIfTcrController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlLrnwIfStuService tlLrnwIfStuService;

    /**
     * 교과학습 목차및학습활동 조회 서비스
     * 
     * @return ResponseDto<List<TlLrnwTocAtvDto>>
     */
    @Operation(summary="교과학습 목차및학습활동 리스트 조회", description="학습창에서 표시할 목차및학습활동정보를 가져온다.")
    @PostMapping(value = "/selectLrnTocAtvInfo")
    public ResponseDto<TlLrnwTocAtvDto> selectLrnTocAtvInfo(@Valid @RequestBody TlLrnwTocSrhDto srhDto) {
        log.debug("Entrance selectLrnTocAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        if(srhDto.getOneClkYn() == null || srhDto.getOneClkYn().equals("")) {
        	srhDto.setOneClkYn("N");
        }
        return Response.ok(tlLrnwIfStuService.selectLrnTocAtvInfo(srhDto, TlConstUtil.USR_DIV_TCR, userDetails.getTxbId()));
    }

    /**
     * 교과학습 학습활동 저장 서비스
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교과학습 학습활동 저장", description="학습창에서 사용자의 학습활동상태및 학습시간을 저장한다.")
    @PostMapping(value = "/saveLrnAtvInfo")
    public ResponseDto<Object> saveLrnAtvInfo(@Valid @RequestBody TlLrnwAtvSaveDto saveDto) {
        log.debug("Entrance saveLrnAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setLrnUsrId(userDetails.getUsrId());
        saveDto.setDbId(userDetails.getTxbId());
        return Response.ok(tlLrnwIfStuService.saveLrnAtvInfo(saveDto, TlConstUtil.USR_DIV_TCR));
    }

     /**
     * 교과서/익힘책 보기 리스트
     * 
     * @return ResponseDto<List<TlLrnwLrnPdfDto>>
     * @throws JsonProcessingException 
     * @throws JsonMappingException 
     */
    @Operation(summary="교과서/익힘책 보기 리스트", description="해당 차시의 교과서 정보 Page no, 이미지 path 를 가져온다")
    @PostMapping(value = "/selectLrntxtwkbList")
    public ResponseDto<List<TlLrnwLrnPdfDto>> selectLrntxtwkbList(@Valid @RequestBody TlLrnwTxbWebDto txbWebDto) throws JsonMappingException, JsonProcessingException {
        log.debug("Entrance selectLrntxtwkbList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        txbWebDto.setOptTxbId(userDetails.getOptTxbId());
        if (txbWebDto.getUsrId() == "") {
        	txbWebDto.setUsrId(userDetails.getUsrId());	
        }

        List<TlLrnwLrnPdfDto> pdfData = tlLrnwIfStuService.selectLrntxtwkbList(txbWebDto);
        
        return Response.ok(pdfData);
    }

    /**
    * 교과서/익힘책 필기 정보 저장 서비스
    * 
    * @return ResponseDto<Integer>
    */
    @Operation(summary="교과서/익힘책 필기 정보 저장", description="교과서/익힘책 필기 정보를 저장한다")
    @PostMapping(value = "/saveLrntxtwkbInfo")
    public ResponseDto<Integer> saveLrntxtwkbInfo(@Valid @RequestBody TlLrnwTxbWebSaveDto saveDto) {
        log.debug("Entrance saveLrntxtwkbInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setUsrId(userDetails.getUsrId());
        saveDto.setDbId(userDetails.getTxbId());
        return Response.ok(tlLrnwIfStuService.saveLrntxtwkbInfo(saveDto));
    }
    
    /**
     * 학생 학습활동 결과물 조회 서비스
     *
     * @return ResponseDto<List<TlLrnwSbcLrnTocDto>>
     */
    @Operation(summary="학생 학습활동 결과물 조회 서비스", description="교사가 학생 학습창을 조회한다.")
    @PostMapping(value = "/selectLrnTocAtvInfoResult", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLrnwLrnAtvDto> selectLrnTocAtvInfoResult(@Valid @RequestBody TlLrnwTocSrhDto srhDto) {
        log.debug("Entrance selectLrnTocAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
 
        return Response.ok(tlLrnwIfStuService.selectLrnTocAtvInfoResult(srhDto, TlConstUtil.USR_DIV_STU));
    }
}
