package com.aidt.api.al.pl.cm.ma;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmMaService;
import com.aidt.api.al.pl.common.AlCmUtil;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuReqDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.api.al.pl.dto.AlPlEaEvComQtmAnwDto;
import com.aidt.api.al.pl.dto.AlTpcMpnDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-28
 * @modify date 2024-02-28
 * @desc AI맞춤학습 학생 단원차시조회
 */

@Slf4j
@Service
public class AlMaService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.ma.";
	
	@Autowired private CommonDao commonDao;
	@Autowired AiRcmTsshQtmCommService commService;
	@Autowired AiRcmTsshQtmMaService maService;
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
	/**
     * 학생 중단원목록 조회 작업중
     * 
     * @param 
     * @return List<AlMluTcLstInqStuResponseDto>
     */
	@Cacheable(
			cacheNames = "shortCache",
			key = "'al:' + #reqDto.optTxbId + ':selectMluLstStuInq:' + #reqDto.usrId",
			condition = "#saveYN != null",
			cacheManager = "aidtCacheManager"
	)
	public Map<String, Object> selectMluLstStuInq(AlMluTcLstInqStuReqDto reqDto, String saveYN) {
		
		Map<String,Object> resultMap = new LinkedHashMap<>();
		
		List<AlMluTcLstInqStuResponseDto> mluList = commonDao.selectList(MAPPER_NAMESPACE + "selectMluLstInqStu", reqDto);
		List<AlMluTcLstInqStuResponseDto> aeEvInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectAeEvInfoList", reqDto);
	
		
		//개념영상
		AiRcmTsshQtmDto aiRcmTsshQtmDto = new AiRcmTsshQtmDto();
		aiRcmTsshQtmDto.setOptTxbId(reqDto.getOptTxbId());
		aiRcmTsshQtmDto.setMluKmmpNodId(reqDto.getMluKmmpNodId());
		aiRcmTsshQtmDto.setUsrId(reqDto.getUsrId());
		aiRcmTsshQtmDto.setSbjCd("MA");
		List<AiRcmTsshQtmDto> ccptVdList = commService.selectCcptVdList(aiRcmTsshQtmDto);
		
		
		for (AlMluTcLstInqStuResponseDto mlu : mluList) {//1dpth
			if(resultMap.containsKey(mlu.getMluKmmpNodId())) {
				continue;
			}
			AiRcmTsshQtmDto selectTpcPgrsRt = maService.selectTpcPgrsRt(reqDto.getOptTxbId(), mlu.getMluKmmpNodId(), reqDto.getUsrId()); // 진행중 토픽
			Map<String, Object> mluMap = new HashMap<>();
			mluMap.put("luImgPth", mlu.getLuImgPth());
			mluMap.put("lluKmmpNodId", mlu.getLluKmmpNodId());
			mluMap.put("lluKmmpNodNm", mlu.getLluKmmpNodNm());
			mluMap.put("kmmpNodId", mlu.getMluKmmpNodId());
			mluMap.put("kmmpNodNm", mlu.getMluKmmpNodNm());
//			mluMap.put("tcEpsYn", mlu.getTcEpsYn());
			mluMap.put("tcUseYn", mlu.getTcUseYn());
			mluMap.put("useYn", mlu.getUseYn());
			mluMap.put("lcknYn", mlu.getLcknYn());
			mluMap.put("rcstnOrdn", mlu.getRcstnOrdn());
//			mluMap.put("orglOrdn", mlu.getOrglOrdn());
			mluMap.put("tpcAvn", mlu.getTpcAvn());
			mluMap.put("ovQtmCnt", mlu.getOvQtmCnt());
			mluMap.put("lrnrVelTpCd", mlu.getLrnrVelTpCd());
			
			
			if(selectTpcPgrsRt == null) {
				mluMap.put("tpcPgrsRt", null);
				mluMap.put("tpcPgrsEvId", null);
			}else {
				mluMap.put("tpcPgrsRt", selectTpcPgrsRt.getTpcKmmpNodId() == null ? null : selectTpcPgrsRt.getTpcKmmpNodId());
				
				if(selectTpcPgrsRt.getQtmCnt() != null && selectTpcPgrsRt.getQtmAnwCnt() != null 
						&& selectTpcPgrsRt.getQtmCnt() > selectTpcPgrsRt.getQtmAnwCnt()) {
					mluMap.put("tpcPgrsEvId", selectTpcPgrsRt.getEvId());
				}
				else {
					mluMap.put("tpcPgrsEvId", null);
				}
				
				// 현재 학습중인 토픽명, 갯수
				if(selectTpcPgrsRt.getLrnwCmplTpcCnt() != null && selectTpcPgrsRt.getLrnwTpcCnt()!= null && selectTpcPgrsRt.getTpcKmmpNodNm() != null) {
					mluMap.put("lrnwCmplTpcCnt", selectTpcPgrsRt.getLrnwCmplTpcCnt());
					mluMap.put("lrnwTpcCnt", selectTpcPgrsRt.getLrnwTpcCnt());
					mluMap.put("tpcKmmpNodNm", selectTpcPgrsRt.getTpcKmmpNodNm());
				}
				
				if(selectTpcPgrsRt.getAllRpcPgrsRtList() != null) {
					mluMap.put("allRpcPgrsRtList", selectTpcPgrsRt.getAllRpcPgrsRtList());
				}
			}
			
			Map<String, Object> avnMap = new HashMap<>();
			if(Double.parseDouble (mlu.getTpcAvn()) < AlConstUtil.TPC_AVN_01) {
				mluMap.put("tcAvn", "01");
			}else if(Double.parseDouble (mlu.getTpcAvn()) >  AlConstUtil.TPC_AVN_03) {
				mluMap.put("tcAvn", "03");
			}else {
				mluMap.put("tcAvn", "02");	
			}
			
			List<String> tpc01 = new ArrayList<>();
			List<String> tpc02 = new ArrayList<>();
			List<String> tpc03 = new ArrayList<>();

			for (AlMluTcLstInqStuResponseDto tpc : mluList) {
				if(mlu.getMluKmmpNodId().equals(tpc.getMluKmmpNodId())) {
					if(Double.parseDouble (tpc.getTpcAvn()) < AlConstUtil.TPC_AVN_01) {
						tpc01.add(tpc.getTpcKmmpNodNm());
					}else if(Double.parseDouble (tpc.getTpcAvn()) > AlConstUtil.TPC_AVN_03) {
						tpc03.add(tpc.getTpcKmmpNodNm());
					}else {
						tpc02.add(tpc.getTpcKmmpNodNm());	
					}
				}
			}

			mluMap.put("tpc01", tpc01);
			mluMap.put("tpc02", tpc02);
			mluMap.put("tpc03", tpc03);
			
			//평가
			List<AlMluTcLstInqStuResponseDto> recentLearningList = aeEvInfoList;
			// mdfDtmOrder 기준으로 정렬
	        Collections.sort(recentLearningList, new Comparator<AlMluTcLstInqStuResponseDto>() {
	            public int compare(AlMluTcLstInqStuResponseDto o1, AlMluTcLstInqStuResponseDto o2) {
	                return Integer.compare(Integer.parseInt(o1.getMdfDtmOrder()), Integer.parseInt(o2.getMdfDtmOrder()));
	            }
	        });
			
			
	        
	        List<AlMluTcLstInqStuResponseDto> evInfoList = aeEvInfoList;
	        Collections.sort(recentLearningList, new Comparator<AlMluTcLstInqStuResponseDto>() {
	            public int compare(AlMluTcLstInqStuResponseDto o1, AlMluTcLstInqStuResponseDto o2) {
	                return Integer.compare(Integer.parseInt(o2.getEvId()), Integer.parseInt(o1.getEvId()));
	            }
	        });
			
			
			for (AlMluTcLstInqStuResponseDto evInfo : evInfoList) {
				//진단평가
				if(mlu.getMluKmmpNodId().equals(evInfo.getMluKmmpNodId()) 
						&& evInfo.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {//1dpth
					mluMap.put("evId", evInfo.getEvId());
					mluMap.put("evDtlDvCd", evInfo.getEvDtlDvCd());
					mluMap.put("evCmplYn", evInfo.getEvCmplYn());
					mluMap.put("MdfDtm", evInfo.getMdfDtm());
					mluMap.put("luevCmplYn", evInfo.getLuevCmplYn());
				}
				//차시별 맞춤학습
				if(mlu.getMluKmmpNodId().equals(evInfo.getMluKmmpNodId()) 
						&& !evInfo.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {//2dpth
					Map<String, String> evMap = new HashMap<>();
					evMap.put("evId", evInfo.getEvId().toString());
					evMap.put("evDtlDvCd", evInfo.getEvDtlDvCd());
					evMap.put("evCmplYn", evInfo.getEvCmplYn());
					evMap.put("MdfDtm", evInfo.getMdfDtm());
					
					mluMap.put(evInfo.getEvDtlDvCd(), evMap);
				}
				
			}//aeEvInfoList for end
			
			//개념영상
			List<Map<String, Object>> vdMapList = new ArrayList<>();
			for (int i = 0; i < ccptVdList.size(); i++) {
//				if(i >= 3) {
//					break;
//				}
				if(mlu.getMluKmmpNodId().equals(ccptVdList.get(i).getMluKmmpNodId())) {
					Map<String, Object> vdMap = new HashMap<>();
					vdMap.put("tpcStat", ccptVdList.get(i).getTpcStat());
					vdMap.put("aiLrnAtvId", ccptVdList.get(i).getAiLrnAtvId());
					vdMap.put("aiLrnAtvNm", ccptVdList.get(i).getAiLrnAtvNm());
					vdMap.put("cdnPthNm", AlCmUtil.makeFleCdnUrl(BUCKET_NAME, ccptVdList.get(i).getCdnPthNm())+ "images/poster.png");
					vdMap.put("lrnStCd", ccptVdList.get(i).getLrnStCd());
					vdMapList.add(vdMap);
				}
			}
			mluMap.put("vdList", vdMapList);
			resultMap.put(mlu.getMluKmmpNodId(), mluMap);
			resultMap.put("recentLearningList", recentLearningList);
		}//mluTcList for end

		return resultMap;
	}
	
	    
	/**
	 * [AI 학습리포트] 개념맵 토픽별 매핑후 return
	 * 
	 *  @param List<AiRcmTsshQtmDto> mluLrmpNodId
	 *  @return Map<String, List<AlTpcMpnDto>>
	 * */
	public Map<String, Object> selectTpcMpnList(AlTpcMpnDto dto){
		Map<String, Object> resMap = new HashMap<>();
		
		Integer tpcCnt03 = 0;
		Integer tpcCntAll = 0;

		// 이미 처리된 TpcKmmpNodId를 저장하기 위한 Set
		Set<String> processedNodIds = new HashSet<>();

		List<AlTpcMpnDto> tpcMpnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcMpnList", dto);
		String tpc03 = Collections.max(tpcMpnList, Comparator.comparing(AlTpcMpnDto::getTpcAvn)).getTpcKmmpNodNm();
    	String tpc01 = Collections.min(tpcMpnList, Comparator.comparing(AlTpcMpnDto::getTpcAvn)).getTpcKmmpNodNm();
    	resMap.put("tpc03", tpc03);
    	resMap.put("tpc01", tpc01);
    	
		for (AlTpcMpnDto mpn : tpcMpnList) {
			List<AlTpcMpnDto> list = (List<AlTpcMpnDto>) resMap.get(mpn.getTpcKmmpNodId());
			if(list == null) {
				list = new ArrayList<>();
				tpcCntAll++;
			}
			
			if(mpn.getTpcAvn() < AlConstUtil.TPC_AVN_01) {
				mpn.setTpcAvnCd("01");
			}else if(mpn.getTpcAvn() > AlConstUtil.TPC_AVN_03) {
				mpn.setTpcAvnCd("03");
				// 동일한 TpcKmmpNodId가 이전에 처리된 적이 없다면 tpcCnt03++ 증가
				if (!processedNodIds.contains(mpn.getTpcKmmpNodId())) {
					tpcCnt03++;
					processedNodIds.add(mpn.getTpcKmmpNodId()); // 현재 TpcKmmpNodId를 Set에 추가
				}
			}else {
				mpn.setTpcAvnCd("02");
			}
			
			//개념맵
			list.add(mpn);
			resMap.put(mpn.getTpcKmmpNodId(), list);
		}
		//이전토픽
		for (AlTpcMpnDto mpn : tpcMpnList) {
			if(null != mpn.getTpcLnkgDvCd() && mpn.getTpcLnkgDvCd().equals("PR")){
				List<AlTpcMpnDto> list = (List<AlTpcMpnDto>) resMap.get(mpn.getLnkgTpcKmmpNodId());
				if(list != null && list.size() > 0) {
					list.add(mpn);
					resMap.put(mpn.getLnkgTpcKmmpNodId(), list);
				}
			}
		}
		//알고있는토픽
		resMap.put("tpcCnt03", tpcCnt03);
		resMap.put("tpcCntAll", tpcCntAll);
		
		//학습 단계별 분석
		List<AiRcmTsshQtmDto> evCansRtList = commonDao.selectList(MAPPER_NAMESPACE + "selectEvCansRt", dto);
		Integer avgCansRt = 0; //단원전체 평균정답률
		Integer progRt = 0; //진도율
		Integer progVl = 100 / evCansRtList.size(); //진도율 틱값
		for (AiRcmTsshQtmDto ev : evCansRtList) {
			avgCansRt += ev.getCansRt();
			progRt += progVl;
		}
		resMap.put("evCansRtList", evCansRtList);
		resMap.put("avgCansRt", avgCansRt / evCansRtList.size());
		resMap.put("progRt", progRt);
		
		//진행중인토픽
		AiRcmTsshQtmDto selectTpcPgrsRt = maService.selectTpcPgrsRt(dto.getOptTxbId(), dto.getMluKmmpNodId(), dto.getUsrId());
		resMap.put("progTpcId", selectTpcPgrsRt.getTpcKmmpNodId());
		resMap.put("progTpcNm", selectTpcPgrsRt.getTpcKmmpNodNm());
		
		return resMap;
	}
	
	//진단평가 문항정보 조회
	public List<AiRcmTsshQtmDto> selectTpcAvnOnlyOvList(AlTpcMpnDto dto) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectTpcAvnOnlyOvList", dto);
	}
	
	/**
	 * 사용자에 대한 '진단평가'에 한한 토픽숙련도 계산하여 Map 반환 - ((학습현황 학생 에서 호출))
	 * @param usrId 사용자ID 필수
	 * @param mluKmmpNodId 중단원지식맵노드ID 선택
	 * */
	public Map<String, Double> getOvTpcAvnMap(AlTpcMpnDto dto){
		//토픽숙련도
		Map<String, Double> tpcAvnMap = new HashMap<>();
		
		//진단평가 문항정보 토픽별 Grouping
		List<AiRcmTsshQtmDto> ovTpcList = this.selectTpcAvnOnlyOvList(dto);
		if(ovTpcList.isEmpty()) {
			throw new IllegalArgumentException("진단평가 데이터가 없습니다.");
		}
			
		for (AiRcmTsshQtmDto tpc : ovTpcList) {
			tpcAvnMap.put(tpc.getTpcKmmpNodId(), tpc.getAiDgnEvTpcAvn());
		}
        return tpcAvnMap;
	}
	
	/**
	 * '진단평가'에 한한 토픽숙련도 계산하여 사용자별 토픽맵으로 반환 - ((학습현황 선생님 에서 호출))
	 * 
	 * */
	public Map<String, Map<String, Double>> getOvTpcAvnUsrMap(AlTpcMpnDto dto){
		
		//사용자맵
		Map<String, Map<String, Double>> tpcAvnUsrMap = new HashMap<String, Map<String,Double>>();
		
		//진단평가 문항정보 토픽별 Grouping
		List<AiRcmTsshQtmDto> ovList = this.selectTpcAvnOnlyOvList(dto);
		if(ovList.isEmpty()) {
			throw new IllegalArgumentException("진단평가 데이터가 없습니다.");
		}
		
		//유저별로 리스트 Mapping
		Map<String, List<AiRcmTsshQtmDto>> usrbyMap = ovList.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getUsrId));
		for (String usrKey : usrbyMap.keySet()) {
			List<AiRcmTsshQtmDto> usrTpcList = usrbyMap.get(usrKey);
			
			//토픽숙련도
			Map<String, Double> tpcAvnMap = new HashMap<>();
			for (AiRcmTsshQtmDto usrTpc : usrTpcList) {
				tpcAvnMap.put(usrTpc.getTpcKmmpNodId(), usrTpc.getAiDgnEvTpcAvn());
			}

			tpcAvnUsrMap.put(usrKey, tpcAvnMap);
		}
			
        return tpcAvnUsrMap;
	}
	
	
	/**
	 * [AI 진단리포트] 개념맵 '진단평가'에 한해서 토픽별 매핑후 return
	 * 
	 *  @param List<AiRcmTsshQtmDto> mluLrmpNodId
	 *  @return Map<String, List<AlTpcMpnDto>>
	 * */
	public Map<String, Object> selectTpcMpnListOnlyOv(AlTpcMpnDto dto){
		Map<String, Object> resMap = new HashMap<>();
		Integer tpcCnt03 = 0;
		Integer tpcCntAll = 0;

		// 이미 처리된 TpcKmmpNodId를 저장하기 위한 Set
		Set<String> processedNodIds = new HashSet<>();

		List<AlTpcMpnDto> tpcMpnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcMpnOvList", dto);
		
		String tpc03 = Collections.max(tpcMpnList, Comparator.comparing(AlTpcMpnDto::getAiDgnEvTpcAvn)).getTpcKmmpNodNm();
    	String tpc01 = Collections.min(tpcMpnList, Comparator.comparing(AlTpcMpnDto::getAiDgnEvTpcAvn)).getTpcKmmpNodNm();
    	resMap.put("tpc03", tpc03);
    	resMap.put("tpc01", tpc01);

		
    	Map<String, Map<String, Object>> groupedMap = new LinkedHashMap<>();
		for (AlTpcMpnDto mpn : tpcMpnList) {
		List<AlTpcMpnDto> list = (List<AlTpcMpnDto>) resMap.get(mpn.getTpcKmmpNodId());
		if(list == null) {
			list = new ArrayList<>();
			tpcCntAll++;
		}

		if(mpn.getAiDgnEvTpcAvn() < AlConstUtil.TPC_AVN_01) {
			mpn.setTpcAvnCd("01");
		}else if(mpn.getAiDgnEvTpcAvn() > AlConstUtil.TPC_AVN_03) {
			mpn.setTpcAvnCd("03");
			// 동일한 TpcKmmpNodId가 이전에 처리된 적이 없다면 tpcCnt03++ 증가
			if (!processedNodIds.contains(mpn.getTpcKmmpNodId())) {
				tpcCnt03++;
				processedNodIds.add(mpn.getTpcKmmpNodId()); // 현재 TpcKmmpNodId를 Set에 추가
			}
		}else {
			mpn.setTpcAvnCd("02");
		}
		
        // tcKmmpNodId를 기준으로 새로운 리스트 생성
        if (!groupedMap.containsKey(mpn.getTcKmmpNodId()) ||
        		(!groupedMap.containsKey(mpn.getTcKmmpNodId()) && dto.getOptTxbId().equals("237-mam01k210") && dto.getMluKmmpNodId().equals("24939"))) {
            // tcKmmpNodId에 대한 새로운 노드 생성
        	Map<String, Object> nodeMap = new HashMap<>();

        	nodeMap.put("tcKmmpNodId", mpn.getTcKmmpNodId());
        	nodeMap.put("tcKmmpNodNm", mpn.getTcKmmpNodNm());        		        	
            nodeMap.put("details", new ArrayList<Map<String, Object>>()); // details 배열 추가
            nodeMap.put("prTpcs", new ArrayList<Map<String, Object>>()); // details 배열 추가
            groupedMap.put(mpn.getTcKmmpNodId(), nodeMap);
        }
        
     // 각 mpn을 details 리스트에 추가
        Map<String, Object> detailMap = new HashMap<>();
        
        detailMap.put("tpcKmmpNodId", mpn.getTpcKmmpNodId());
        detailMap.put("tpcKmmpNodNm", mpn.getTpcKmmpNodNm());
        detailMap.put("lnkgTpcKmmpNodId", mpn.getLnkgTpcKmmpNodId());
        detailMap.put("lnkgTpcKmmpNodNm", mpn.getLnkgTpcKmmpNodNm());
        detailMap.put("tpcLnkgDvCd", mpn.getTpcLnkgDvCd());
        detailMap.put("tpcAvnCd", mpn.getTpcAvnCd());
        detailMap.put("lrnYn", mpn.getLrnYn());
        detailMap.put("aiDgnEvTpcAvn", mpn.getAiDgnEvTpcAvn());
        detailMap.put("aiDgnEvPredAvgCansRt", mpn.getAiDgnEvPredAvgCansRt());

        // tcKmmpNodId에 해당하는 노드의 details 배열에 추가
        Map<String, Object> nodeMap = groupedMap.get(mpn.getTcKmmpNodId());
        List<Map<String, Object>> detailsList = (List<Map<String, Object>>) nodeMap.get("details");
        List<Map<String, Object>> prTpcList = (List<Map<String, Object>>) nodeMap.get("prTpcs");
        detailsList.add(detailMap); 
        
        if(mpn.getTpcLnkgDvCd() != null && mpn.getTpcLnkgDvCd().equals("PR"))
        {
        	Map<String, Object> prTpcMap = new HashMap<>();
        	prTpcMap.put("lnkgTpcKmmpNodId", mpn.getLnkgTpcKmmpNodId());
        	prTpcMap.put("lnkgTpcKmmpNodNm", mpn.getLnkgTpcKmmpNodNm());
        	prTpcMap.put("tpcAvnCd", mpn.getTpcAvnCd());
        	prTpcMap.put("srtOrdn", mpn.getSrtOrdn());
        	prTpcList.add(prTpcMap);
        	nodeMap.put("prTpcs", prTpcList);
        }
        // 그룹화된 데이터에 tpcKmmpNodId와 details를 설정
        nodeMap.put("details", detailsList);

		//개념맵
		list.add(mpn);
		resMap.put(mpn.getTpcKmmpNodId(), list);
		
        // 차시만 있고 토픽이 없는 경우, 콘텐츠팀 요청으로 하드코딩 (중학수학 김동재)
		if (dto.getMluKmmpNodId().equals("24939") && mpn.getTcKmmpNodId().equals("24957")) {
		    // 먼저 24957의 데이터를 groupedMap에 추가
		    groupedMap.put(mpn.getTcKmmpNodId(), nodeMap);
		    
		    List<Map<String, Object>> filteredDetails = detailsList.stream()
		            .filter(detail -> !detail.get("tpcKmmpNodId").equals("24958"))
		            .collect(Collectors.toList());

		    // 27078의 새로운 nodeMap 생성
		    Map<String, Object> newNodeMap = new HashMap<>();
		    newNodeMap.put("tcKmmpNodId", "27078");
		    newNodeMap.put("tcKmmpNodNm", "도수의 총합이 다른 두 자료의 분포는 어떻게 비교할까?");
		    newNodeMap.put("details", new ArrayList<>(filteredDetails)); // 24957의 details를 복사
		    newNodeMap.put("prTpcs", new ArrayList<>()); // 빈 리스트 초기화

		    groupedMap.put("27078", newNodeMap);
		}
	}
	//이전토픽
	for (AlTpcMpnDto mpn : tpcMpnList) {
		if(null != mpn.getTpcLnkgDvCd() && mpn.getTpcLnkgDvCd().equals("PR")){
			List<AlTpcMpnDto> list = (List<AlTpcMpnDto>) resMap.get(mpn.getLnkgTpcKmmpNodId());
			if(list != null && list.size() > 0) {
				list.add(mpn);
				resMap.put(mpn.getLnkgTpcKmmpNodId(), list);
			}
		}
	}
	
	
		//알고있는토픽
		resMap.put("tpcCnt03", tpcCnt03);
		resMap.put("tpcCntAll", tpcCntAll);
		
		resMap.put("groupedTpcList", groupedMap);
		return resMap;
	}

	@Cacheable(
			cacheNames = "shortCache",
			key = "'al:' + #reqDto.optTxbId + ':selectIansQtmSeUgCntMa:' + #reqDto.usrId",
			condition = "#saveYN != null",
			cacheManager = "aidtCacheManager"
	)
	public Map<String, Object> selectIansQtmSeUgCnt(AlMluTcLstInqStuReqDto reqDto, String saveYN){
		Map<String,Object> resultMap = new HashMap<>();
		
		
		int iansQtmCnt = commonDao.select(MAPPER_NAMESPACE + "selectIansQtmCnt", reqDto);
		List<AlMluTcLstInqStuResponseDto> evSeUg = commonDao.selectList(MAPPER_NAMESPACE + "selectEvSeUg", reqDto);
		resultMap.put("iansQtmCnt", iansQtmCnt);
		if(evSeUg.size() > 0) {
			resultMap.put("evSeUg", evSeUg.get(0));
		} else {
			resultMap.put("evSeUg", null);		
		}
		
		return resultMap;
	}
	
	public Map<String,Object> selectAlMaEvQtmAnwList(@Valid AlMluTcLstInqStuReqDto dto) {
		Map<String,Object> resultMap = new HashMap<>();
		
		//평가 답변 정보
		if(!dto.getEvDtlDvCd().equals("OV")) {
			List<AlPlEaEvComQtmAnwDto> evIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlMaEvIdList", dto);
			String[] evIdArray = new String[evIdList.size()];

	        // for 루프를 통해 getEvId 호출 및 배열에 저장
	        for (int i = 0; i < evIdList.size(); i++) {
	            evIdArray[i] = evIdList.get(i).getEvId();
	        }
			dto.setEvIds(evIdArray);			
		}
		List<AlPlEaEvComQtmAnwDto> qtmAnwList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlMaEvQtmAnwList", dto);
//		List<AiRcmTsshQtmDto> tcRptList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlEvTcRptList", req);
		resultMap.put("qtmAnwList", qtmAnwList);
//		resultMap.put("tcRptList", tcRptList);
		
		return resultMap;
}
	
	
	
	/**
	 * 학습리포트 - 학생별 토픽 숙련도 조회
	 * 
	 *  @param 
	 *  @return 
	 * */
	public List<Map<String, Object>> selectTpcMpnRptList(AlTpcMpnDto dto){
		
		
		List<AlTpcMpnDto> tpcMpnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcMpnRptList", dto);
		List<AlTpcMpnDto> usrEvTmScntList = commonDao.selectList(MAPPER_NAMESPACE + "selectUsrEvTmScnt", dto); //사용자별 학습시간 조회
		
		List<String> keyList = tpcMpnList.stream()
	            .map(AlTpcMpnDto::getUsrId) // usrId 추출..
	            .distinct() // 중복된 키 제거 
	            .collect(Collectors.toList()); 
		
		List<Map<String, Object>> usrList = new ArrayList<>();
		for(String usr : keyList) {
			Integer tpcCnt03 = 0;
			Integer tpcCntAll = 0;
			String usrId = "";
			String usrNm = "";
			String stuNo = "";
			String hours = "";
			String minutes = "";
			String seconds = "";
			String mdfDtm = "";
			String OvEvId = "";
			String OVCmplYn = "";
			String C1EvId = "";
			String C1EvCmplYn = "";
//			String C3EvId = "";
//			String C3EvCmplYn = "";
			String lrnrVelTpCd = "";
			Map<String, Object> usrMap = new HashMap<>();
			for(AlTpcMpnDto mpn : tpcMpnList) {
				if(usr.equals(mpn.getUsrId())) {
					usrId = usr;
					usrNm = mpn.getUsrNm();
					stuNo = mpn.getStuNo();
					if(mpn.getOVEvId() != null && mpn.getOVCmplYn() != null) {
						OvEvId = mpn.getOVEvId();						
						OVCmplYn = mpn.getOVCmplYn();						
					}
					
					if(mpn.getC1EvId() != null && mpn.getC1EvCmplYn() != null) {
						C1EvId = mpn.getC1EvId();						
						C1EvCmplYn = mpn.getC1EvCmplYn();						
					}
					
//					if(mpn.getOVEvId() != null&& mpn.getC3EvCmplYn() != null) {
//						C3EvId = mpn.getC3EvId();
//						C3EvCmplYn = mpn.getC3EvCmplYn();
//					}
					lrnrVelTpCd = mpn.getLrnrVelTpCd();
					if(mpn.getTpcAvn() > AlConstUtil.TPC_AVN_03) {
						tpcCnt03++;
					}
					tpcCntAll++;
				}
			}		
			
			for(AlTpcMpnDto usrEvTmScnt: usrEvTmScntList) {
				if(usr.equals(usrEvTmScnt.getUsrId())) {
					hours = usrEvTmScnt.getHours();
					minutes = usrEvTmScnt.getMinutes();
					seconds = usrEvTmScnt.getSeconds();
					mdfDtm = usrEvTmScnt.getMdfDtm();
				}
			}
			usrMap.put("tpcCnt03", tpcCnt03);
			usrMap.put("tpcCntAll", tpcCntAll);
			usrMap.put("usrId", usrId);
			usrMap.put("usrNm", usrNm);
			usrMap.put("stuNo", stuNo);
			usrMap.put("hours", hours);
			usrMap.put("minutes", minutes);
			usrMap.put("seconds", seconds);
			usrMap.put("mdfDtm", mdfDtm);
			usrMap.put("OvEvId", OvEvId);
			usrMap.put("OVCmplYn", OVCmplYn);
			usrMap.put("C1EvId", C1EvId);
			usrMap.put("C1EvCmplYn", C1EvCmplYn);
//			usrMap.put("C3EvId", C3EvId);
//			usrMap.put("C3EvCmplYn", C3EvCmplYn);
			usrMap.put("lrnrVelTpCd", lrnrVelTpCd);
			usrList.add(usrMap);
		}
		
		
		List<AiRcmTsshQtmDto> ovList = commonDao.selectList(MAPPER_NAMESPACE + "selectTpcRptOV", dto);
		Map<String, List<AiRcmTsshQtmDto>> usrByListMap = ovList.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getUsrId));
		log.debug(usrByListMap.toString());
		
		for (String usr : usrByListMap.keySet()) {
			//토픽숙련도
			Map<String, Double> tpcAvnMap = new HashMap<>();
			List<AiRcmTsshQtmDto> tpcByQtmList = usrByListMap.get(usr);
			Map<String, List<AiRcmTsshQtmDto>> tpcByListMap = tpcByQtmList.stream().collect(Collectors.groupingBy(AiRcmTsshQtmDto::getTpcKmmpNodId));
			
			for(String key : tpcByListMap.keySet()) {
				
				Double tpcWgtNmvl = 0.0;
				int listCnt = 0;
				for (AiRcmTsshQtmDto tpcQtm : tpcByQtmList) {
					listCnt++;
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("01")) { tpcWgtNmvl += 0.7; }//최하
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("02")) { tpcWgtNmvl += 0.85; }//하
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("03")) { tpcWgtNmvl += 1.0; }//중
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("04")) { tpcWgtNmvl += 1.15; }//상
					if(tpcQtm.getCansYn().equals("Y") && tpcQtm.getCtnDffdDvCd().equals("05")) { tpcWgtNmvl += 1.3; }//최상
					else { tpcWgtNmvl += 0.0; }
				}
				//토픽-문항 난이도별 실제점수 가중치
				tpcWgtNmvl = tpcWgtNmvl/listCnt;
				//토픽별 실제점수 :: 문항풀이이력이 없어도 초기등록시 0.5로 등록되어 있다.
				Double aiPredAvgScr = tpcByQtmList.get(0).getAiPredAvgScr();
				
				//토픽별 AI 예측점수
				Double aiPredAvgCansRt = tpcByQtmList.get(0).getAiPredAvgCansRt();
				//토픽별 문항풀이 횟수
				Integer txmPn = tpcByQtmList.get(0).getTxmPn();
				//토픽점수
				Double tpcAvn = 0.5;
				
				//토픽별 문항풀이가 없는경우 AI예측점수로 판단
				if(txmPn <= 1 && aiPredAvgScr == 0.5) {
					tpcAvn = aiPredAvgCansRt;
				}
				//토픽별 문항풀이 이력이 1건인 경우 실제점수7 : 예측점수3
				else if(txmPn == 1) {
					tpcAvn = ((tpcWgtNmvl * 7) + (aiPredAvgCansRt * 3)) / 10;
				}
				//실제점수(가중치)로 판단
				else {
					tpcAvn = tpcWgtNmvl;
				}
				tpcAvnMap.put(key, tpcAvn);
			}
			
			for(String usrKey : keyList) {
				Integer tpcCnt03 = 0;
				Integer tpcCntAll = 0;
				String usrId = "";
				Map<String, Object> usrMap = new HashMap<>();
				for(AlTpcMpnDto mpn : tpcMpnList) {
					Double tpcAvn =  tpcAvnMap.get(mpn.getTpcKmmpNodId());
					if(usrKey.equals(mpn.getUsrId())) {
						usrId = usrKey;
						if(tpcAvn == null) {
							tpcCntAll+=1;
						}else if(tpcAvn < AlConstUtil.TPC_AVN_01) {
							tpcCntAll+=1;
						}else if(tpcAvn > AlConstUtil.TPC_AVN_03) {
							tpcCntAll+=1;
							tpcCnt03+=1;
						}else {
							tpcCntAll+=1;
						}
					}
				}		

				
				for(int i=0; i<usrList.size(); i++) {
					if(usrList.get(i).get("usrId") == usrId) {
						usrList.get(i).put("tpcCnt03Ov", tpcCnt03);
						usrList.get(i).put("tpcCntAllOv", tpcCntAll);
					}
				}
			}
			
		}
		return usrList;
	}

	
	/**
	 * 학습리포트 - 학습현황관리 중단워목록만 조회
	 * 
	 *  @param 
	 *  @return 
	 * */
	public List<Map<String, Object>> selectOnlyMluList(@Valid AlMluTcLstInqStuReqDto dto) {
		 List<AlMluTcLstInqStuResponseDto> kmmpList= commonDao.selectList(MAPPER_NAMESPACE + "selectOnlyMluList", dto);
		 List<Map<String, Object>> res = new ArrayList<>();
		 
		 Map<String, List<String>> kmmpMap = new HashMap<>();
		 Map<String, String> kmmpNodNmMap = new HashMap<>();
		 
		 for (AlMluTcLstInqStuResponseDto kmmp : kmmpList) {
	            String kmmpNodId = kmmp.getKmmpNodId();
	            String tcKmmpNodId = kmmp.getTcKmmpNodId();
	            String kmmpNodNm = kmmp.getKmmpNodNm();

	            if (kmmpMap.containsKey(kmmpNodId)) {
	                Set<String> tcKmmpNodIds = new HashSet<>(kmmpMap.get(kmmpNodId)); // 중복 제거를 위해 Set 사용
	                tcKmmpNodIds.add(tcKmmpNodId);
	                kmmpMap.put(kmmpNodId, new ArrayList<>(tcKmmpNodIds));
	            } else {
	                List<String> tcKmmpNodIds = new ArrayList<>();
	                tcKmmpNodIds.add(tcKmmpNodId);
	                kmmpMap.put(kmmpNodId, tcKmmpNodIds);
	                kmmpNodNmMap.put(kmmpNodId, kmmpNodNm);
	            }
	        }
		 
		 for (String kmmpNodId : kmmpMap.keySet()) {
	            List<String> tcKmmpNodIds = kmmpMap.get(kmmpNodId);
	            String kmmpNodNm = kmmpNodNmMap.get(kmmpNodId);
	            
	         // tcKmmpNodIds를 문자열로 변환
	            StringBuilder tcIdsStrBuilder = new StringBuilder();
	            for (String id : tcKmmpNodIds) {
	                if (tcIdsStrBuilder.length() > 0) {
	                    tcIdsStrBuilder.append(", ");
	                }
	                tcIdsStrBuilder.append(id);
	            }
	            String tcIdsStr = tcIdsStrBuilder.toString();

	            // Map 객체 생성
	            Map<String, Object> item = new HashMap<>();
	            item.put("kmmpNodId", kmmpNodId);
	            item.put("kmmpNodNm", kmmpNodNm); 
	            item.put("tcKmmpNodIds", tcKmmpNodIds);
	            item.put("tcKmmpNodIdsStr", tcIdsStr);

	            // res 리스트에 추가
	            res.add(item);
	        }
		 
		 res.sort(Comparator.comparing(o -> Integer.parseInt((String) o.get("kmmpNodId"))));
		 
		 return res;
	}


	public AlMluTcLstInqStuResponseDto selectEvSeUg(@Valid AlMluTcLstInqStuReqDto dto) {
		List<AlMluTcLstInqStuResponseDto> evSeUg = commonDao.selectList(MAPPER_NAMESPACE + "selectEvSeUg", dto);
		if (evSeUg.size() > 0) {
			return evSeUg.get(0);			
		} else {
			return null;
		}
	}
	
	
	public String[] selectAlMaEvIdList(@Valid AlMluTcLstInqStuReqDto dto) {
			List<AlPlEaEvComQtmAnwDto> evIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlMaEvIdList", dto);
			String[] evIdArray = new String[evIdList.size()];

	        // for 루프를 통해 getEvId 호출 및 배열에 저장
	        for (int i = 0; i < evIdList.size(); i++) {
	            evIdArray[i] = evIdList.get(i).getEvId();
	        }			
		return evIdArray;
}
	

	

}
