package com.aidt.api.al.pl.cm.mg;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-28
 * @modify date 2024-02-28
 * @desc AI맞춤학습 학생 단원차시조회
 */

@Slf4j
@Service
public class AlMgService {
	
	private final String MAPPER_NAMESPACE = "api.al.pl.mg.";
	
	@Autowired private CommonDao commonDao;
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
    /**
     * 학습맵 대단원 ID와 매핑된 지식맵 중단원 ID 조회(복수 조회 가능)
     * @param lrmpNodId
     * @return KmmpNodId
     */
    
    public List<String> selectKmmpNodIdByLrmpNodId(String lrmpNodId) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectKmmpNodIdByLrmpNodId", lrmpNodId);
	}
    
    public List<AiRcmTsshQtmDto> selectKmmpNodList(AiRcmTsshQtmDto req) {
		return commonDao.selectList(MAPPER_NAMESPACE + "selectKmmpNodList", req);
	}

	

	

}
