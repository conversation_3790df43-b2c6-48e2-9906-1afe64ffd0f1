package com.aidt.api.tl.oneclksetm.tcr.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-26 10:41:27
 * @modify date 2024-06-26 10:41:27
 * @desc TlOneClkSetmRegMtrlDto 원클릭학습설정 내 자료 Dto
 */
@Data
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외

public class TlOneClkSetmRegMtrlDto {
	
	/** 운영교과서ID */
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	/** 대단원노드ID */
	@Parameter(name="대단원노드ID")
	private String lluNodId;
	    
	/** 학습맵노드ID */
	@Parameter(name="학습맵노드ID")
	private String lrmpNodId;
	
	/** 학습자료ID */
    @Parameter(name="학습자료ID")
    private int lrnMtrlId;
    
    /** 학습자료번호*/
    @Parameter(name="학습자료번호")
    private int lsnMtrlNo;
    
    /** 추가콘텐츠ID*/
    @Parameter(name="추가콘텐츠ID")
    private String tcrRegCtnId;
    
    /** 학습자료코드(링크,동영상,문서) */
    @Parameter(name="학습자료코드(링크,동영상,문서)")
    private String lsnMtrlTpCd;
    
    /** 첨부ID */
    @Parameter(name="첨부ID")
    private int annxId;
    
    /** 학습자료제목명 */
    @Parameter(name="학습자료제목명")
    private String lsnSppMtrlNm;
    
    /** 자료제목명 */
    @Parameter(name="자료제목명")
    private String mtrlNm;
    
    /** 학습단계ID */
    @Parameter(name="학습단계ID")
    private String lrnStpId;
    
    /** URL */
    @Parameter(name="URL")
    private String url;
    
    /** 학습게시글내용 */
    @Parameter(name="학습게시글내용")
    private String lrnBlwrCn;
    
    /** 공개여부 */
    @Parameter(name="공개여부")
    private String opnpYn;

    /** 미리보기여부 */
    @Parameter(name="미리보기여부")
    private String previewYn;
    
    /** 재구성순서 */
    @Parameter(name="재구성순서")
    private int rcstnOrdn;
    
    /** 삭제여부 */
    @Parameter(name="삭제여부")
    private String delYn;
    
    /** 생성자ID */
    @Parameter(name="생성자ID")
    private String crtrId;
    
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;
    
    /** 기본자료여부 */
    @Parameter(name="기본자료여부")
    private String bsMtrlYn;
    
    /** dbID */
    @Parameter(name="dbID")
    private String dbId;
    
    /** 첨부파일 리스트 */
    @Parameter(name="첨부파일 리스트")
	List<TlAnnxFleDto> fleList;
    
    /** 변경할 학습맵노드ID - 게시물 수정*/
	@Parameter(name="학습맵노드ID")
	private String chgLrmpNodId;

}
