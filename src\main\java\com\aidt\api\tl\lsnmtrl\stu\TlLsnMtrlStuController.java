package com.aidt.api.tl.lsnmtrl.stu;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlInfoDto;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto;
import com.aidt.api.tl.lsnmtrl.tcr.TlLsnMtrlTcrService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-05 10:22:17
 * @modify date 2024-01-05 10:22:17
 * @desc TlLsnMtrlStu Service 수업자료서비스(학생용)
 */
@Slf4j
@Tag(name="[tl] 교과학습 수업자료[TlLsnMtrlStu]", description="교과학습 수업자료(학생용)")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/tl/stu/lsnmtrl")
public class TlLsnMtrlStuController {
	
    @Autowired
    private JwtProvider jwtProvider;
    
    @Autowired
    private TlLsnMtrlTcrService tlLsnMtrlTcrService;

    /**
     * 교과학습 수업자료목록조회
     *
     * @param srhDto TlLsnMtrlTocSrhDto
     * @return ResponseList<TlLsnMtrlDto>
     */
    @Operation(summary="교과학습 수업자료목록 조회", description="수업자료목록(학생용)을 조회한다.")
    @PostMapping(value = "/selectLsnMtrlList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLsnMtrlInfoDto> selectLsnMtrlList(@Valid @RequestBody TlLsnMtrlTocSrhDto srhDto) {
        log.debug("Entrance selectLsnMtrlList");
        if(StringUtils.isEmpty(srhDto.getLrmpNodId())) {
    		throw new IllegalArgumentException("차시정보가 없습니다.");
    	}
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_STU);
        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setAllDatYn("N");
        return Response.ok(tlLsnMtrlTcrService.selectLsnMtrlInfo(srhDto));
    }

//    /**
//     * v3.1 대응 교과학습 수업자료목록조회
//     *
//     * @param srhDto TlLsnMtrlTocSrhDto
//     * @return ResponseList<TlLsnMtrlDto>
//     */
//    @Operation(summary="교과학습 수업자료목록 조회", description="수업자료목록(학생용)을 조회한다.")
//    @PostMapping(value = "/selectLsnMtrlList2", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseDto<TlLsnMtrlInfoDto> selectLsnMtrlList2(@Valid @RequestBody TlLsnMtrlTocSrhDto srhDto) {
//        log.debug("Entrance selectLsnMtrlList");
//
//        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//
//        srhDto.setUsrDvCd(TlConstUtil.USR_DIV_STU);
//        // 세션정보에서 설정
//        srhDto.setOptTxbId(userDetails.getOptTxbId());
//        return Response.ok(tlLsnMtrlTcrService.selectLsnMtrlInfo2(srhDto));
//    }
}
