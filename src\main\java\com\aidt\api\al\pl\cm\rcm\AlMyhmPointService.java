package com.aidt.api.al.pl.cm.rcm;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.common.util.CoreUtil;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class AlMyhmPointService {
	
	@Autowired private WebFluxUtil webFluxUtil;
	
	@Value("${aidt.endpoint.lw_myhm_stu_point:}")
	private String endpoint_lw_myhm_stu_point;
	
	/**
	 * AI학습 - 마이홈 포인트 적립API 호출
	 * */
	public Map<String, Object> callMyhmApi(String accessToken, Map<String,String> paramMap) throws JsonProcessingException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Authorization", "Bearer " + accessToken);
        httpHeaders.add("Content-Type", "application/json");

        // point saving using lw-api sample
        String jsonString = new ObjectMapper().writeValueAsString(paramMap);
        String post = webFluxUtil.post(this.endpoint_lw_myhm_stu_point, httpHeaders, jsonString, String.class);
        return CoreUtil.Json.jsonString2Map(post);
    }
	
	
	/**
     * 마이홈 포인트코드 반환
     * 
     * @param sbjCd 과목코드
     * @param schlGrdCd 학교급코드
     * @param tcKmmpNodId 차시지식맵노드ID
     * @param evDtlDvCd 평가상세구분코드
     * @param ctnTpCd 컨텐츠유형코드
     * @return pointCode 포잍트코드
     */
	public String getPointCode(String sbjCd, String schlGrdCd, String tcKmmpNodNm, String evDtlDvCd, String ctnTpCd) {
		String pointCode = "";
		//수학
		if(AlConstUtil.SBJ_MA.contains(sbjCd)) {
			if(evDtlDvCd.equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
				pointCode = "AI_CM_01";
			}
			else if(evDtlDvCd.equals("C1") || evDtlDvCd.equals("C2")) {
				pointCode = "AI_CM_06";
			}
//			else if(evDtlDvCd.equals("C1")) {
//				pointCode = "AI_CM_02";
//			}else if(evDtlDvCd.equals("C2")) {
//				pointCode = "AI_CM_03";
//			}else if(evDtlDvCd.equals("C3")) {
//				pointCode = "AI_CM_04";
//			}
			else if(ctnTpCd.equals(AlConstUtil.AL_MYHM_CTN_VD)) {
				pointCode = "AI_CM_05";
			}
		}
		//영어
		if(AlConstUtil.SBJ_EN.contains(sbjCd)) {
			if(evDtlDvCd.equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
				pointCode = "AI_CE_01";
			}else {
				if(schlGrdCd.equals(AlConstUtil.SCHL_GRD_CD_ESCH)) {
					if(tcKmmpNodNm.equals("듣기")) {
						pointCode = "AI_EE_01";
					}else if(tcKmmpNodNm.equals("말하기")) {
						pointCode = "AI_EE_02";
					}else if(tcKmmpNodNm.equals("읽기")) {
						pointCode = "AI_EE_03";
					}else if(tcKmmpNodNm.equals("쓰기")) {
						pointCode = "AI_EE_04";
					}else if(tcKmmpNodNm.equals("파닉스")) {
						pointCode = "AI_EE_05";
					}else if(tcKmmpNodNm.equals("알파벳")) {
						pointCode = "AI_EE_06";
					}else if(ctnTpCd.equals(AlConstUtil.AL_MYHM_CTN_VD)) {
						pointCode = "AI_EE_07";
					}
				}else if(schlGrdCd.equals(AlConstUtil.SCHL_GRD_CD_MCLS)) {
					if(tcKmmpNodNm.toLowerCase().equals("listening")) {
						pointCode = "AI_ME_01";
					}else if(tcKmmpNodNm.toLowerCase().equals("speaking")) {
						pointCode = "AI_ME_02";
					}else if(tcKmmpNodNm.toLowerCase().equals("reading")) {
						pointCode = "AI_ME_03";
					}else if(tcKmmpNodNm.toLowerCase().equals("writing")) {
						pointCode = "AI_ME_04";
					}else if(tcKmmpNodNm.toLowerCase().equals("grammar")) {
						pointCode = "AI_ME_05";
					}else if(tcKmmpNodNm.toLowerCase().equals("vocabulary")) {
						pointCode = "AI_ME_06";
					}else if(ctnTpCd.equals(AlConstUtil.AL_MYHM_CTN_VD)) {
						pointCode = "AI_ME_07";
					}
				}else if(schlGrdCd.equals(AlConstUtil.SCHL_GRD_CD_HGH)) {
					if(tcKmmpNodNm.toLowerCase().equals("listening")) {
						pointCode = "AI_HE_01";
					}else if(tcKmmpNodNm.toLowerCase().equals("speaking")) {
						pointCode = "AI_HE_02";
					}else if(tcKmmpNodNm.toLowerCase().equals("reading")) {
						pointCode = "AI_HE_03";
					}else if(tcKmmpNodNm.toLowerCase().equals("writing")) {
						pointCode = "AI_HE_04";
					}else if(tcKmmpNodNm.toLowerCase().equals("grammar")) {
						pointCode = "AI_HE_05";
					}else if(tcKmmpNodNm.toLowerCase().equals("vocabulary")) {
						pointCode = "AI_HE_06";
					}else if(ctnTpCd.equals(AlConstUtil.AL_MYHM_CTN_VD)) {
						pointCode = "AI_HE_07";
					}
				}
			}
		}
		return pointCode;
	}

}
