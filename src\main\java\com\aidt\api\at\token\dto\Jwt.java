package com.aidt.api.at.token.dto;

import java.util.Arrays;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-11-30 10:40:01
 * @modify date 2023-11-30 10:40:01
 * @desc Java Web Token DTO
 */
public class Jwt {
    @Data
    public static class JwtRequestDto {
        @NotBlank(message = "{field.required}")
        @Parameter(name="로그인ID", required = true)
        private String loginId;

        @NotBlank(message = "{field.required}")
        @Size(min = 4)
        @Parameter(name="비밀번호", required = true)
        private String password;
    }

    @Data
    public static class JwtUserDto {
        @Parameter(name="로그인ID", required = true)
        private String usrId;

        @Parameter(name="권한")
        private List<String> roles;

        /**
         * @param roles
         */
        public void setRoles(String roles) {
            this.roles = Arrays.asList(roles.split(","));
        }
        
        private String kerisUsrId;
    }

    @Builder
    @Data
    public static class JwtRefreshDto {
        @Parameter(name="유저ID", required = true)
        private String usrId;

        @Parameter(name="갱신 토큰", required = true)
        private String refreshToken;
        
        @Parameter(name="keris사용자ID", required = false)
        private String kerisUsrId;
    }

    @Builder
    @Data
    public static class JwtResponseDto {
        @Parameter(name="인증토큰")
        private String accessToken;

        @Parameter(name="갱신토큰")
        private String refreshToken;
    }

    @Builder
    @Data
    public static class JwtAccessTokenDto {
        @Parameter(name="인증토큰")
        private String accessToken;
    }
}
