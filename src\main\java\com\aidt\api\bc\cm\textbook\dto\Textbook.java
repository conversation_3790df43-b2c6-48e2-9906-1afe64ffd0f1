package com.aidt.api.bc.cm.textbook.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Textbook {

	private Integer txbId;                            // 교과서ID
	private String txbCd;                             // 교과서코드
	private String txbNm;                             // 교과서명
	private String pblsCd;                            // 출판사코드
	private String pblsNm;                            // 출판사명
	private String autrCd;                            // 저자코드
	private String autrTxbCd;                         // 저자교과서코드
	private String autrNm;                            // 저자명
	private Integer stnSstEduCrsId;                   // 표준체계교육과정ID
	private String eduCrsCd;                          // 교육과정코드
	private String eduCrsNm;                          // 교육과정명
	private String schlGrdCd;                         // 학교급코드
	private Integer sgyCd;                            // 학년코드
	private String sbjCd;                             // 과목코드
	private String trmDvCd;                           // 학기구분코드

}
