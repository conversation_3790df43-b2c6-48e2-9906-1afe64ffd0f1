package com.aidt.api.ea.clamg.tcr;

import java.util.*;

import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrReqDto;
import com.aidt.api.ea.clamg.tcr.dto.AllClaMgTcrResDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-10-07
 * @modify date 2024-10-07
 * @desc 전체 학급 관리 Service
 */
@Slf4j
@Service
public class AllClaMgTcrService {

	private final String MAPPER_NAMESPACE = "api.ea.clamg.tcr.";

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private CommonDao commonDao;

	/**
	 * 단원 조회
	 *
	 * @param param
	 * @return List<AllClaMgTcrResDto>
	 */
	public List<AllClaMgTcrResDto> selectOptTxbUnit(AllClaMgTcrReqDto param) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());

		String lrmpNodId = String.valueOf(param.getLrmpNodId());
		param.setLrmpNodId(lrmpNodId);

		String kmmpNodId = String.valueOf(param.getKmmpNodId());
		param.setKmmpNodId(kmmpNodId);

		return commonDao.selectList(MAPPER_NAMESPACE + "selectOptTxbUnit", param);
	}

	/**
	 * 통계 전체 조회
	 *
	 * @param param
	 * @return List<AllClaMgTcrResDto>
	 */
	public List<AllClaMgTcrResDto> selectAllStats(AllClaMgTcrReqDto param) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//		param.setTcrUsrId(userDetails.getUsrId());
		param.setTcrUsrId(userDetails.getKerisUsrId());
		param.setTxbId(userDetails.getTxbId());
		String lrmpNodId = String.valueOf(param.getLrmpNodId());
		param.setLrmpNodId(lrmpNodId);

		String kmmpNodId = String.valueOf(param.getKmmpNodId());
		param.setKmmpNodId(kmmpNodId);

		List<AllClaMgTcrResDto> allClaMgTcrResDtoList = new ArrayList<>();

		// tcrUsrId로 학급(인원수) 조회
		List<AllClaMgTcrResDto> claInfoList = commonDao.selectList(MAPPER_NAMESPACE + "claInfoList", param);

		if (!claInfoList.isEmpty()) {
            for (AllClaMgTcrResDto claInfo : claInfoList) {
                AllClaMgTcrResDto allClaMgTcrResDto = new AllClaMgTcrResDto();

                param.setOptTxbId(claInfo.getOptTxbId());
                param.setClaId(claInfo.getClaId());
				param.setTcrUsrId(claInfo.getTcrUsrId());

				// 반, 학급 정보
                allClaMgTcrResDto.setClaNm(claInfo.getClaNm());
                allClaMgTcrResDto.setOptTxbId(claInfo.getOptTxbId());
//                allClaMgTcrResDto.setUsrTotCnt(claInfo.getUsrTotCnt());

                // 교과학습 진도율
                AllClaMgTcrResDto tlRatio = commonDao.select(MAPPER_NAMESPACE + "selectTlRatioByCla", param);
                if (tlRatio != null) {
                    allClaMgTcrResDto.setLluNodId(tlRatio.getLluNodId());
                    allClaMgTcrResDto.setLrmpNodNm(tlRatio.getLrmpNodNm());
                    allClaMgTcrResDto.setTlRatio(tlRatio.getTlRatio());
                }

                // AI맞춤학습 진도율
                AllClaMgTcrResDto kmmpNod = commonDao.select(MAPPER_NAMESPACE + "selectKmmpNodId", param);
                if (kmmpNod != null) {
                    param.setKmmpNodId(String.valueOf((kmmpNod.getKmmpNodId())));
                    param.setUrnkKmmpNodId(kmmpNod.getUrnkKmmpNodId());

					AllClaMgTcrResDto alRatioByCla = selectAlRatioByCla(param);
					if (alRatioByCla != null) {
						allClaMgTcrResDto.setAiRatio(alRatioByCla.getAiRatio());
					}
                }

                // 성취율
                AllClaMgTcrResDto achievementDto = commonDao.select(MAPPER_NAMESPACE + "selectAchievementRate", param);
                if (achievementDto != null) {
//                    allClaMgTcrResDto.setAvgRtUd(achievementDto.getAvgRtUd());
                    allClaMgTcrResDto.setAvgRtOv(achievementDto.getAvgRtOv());
                    allClaMgTcrResDto.setAvgRtUg(achievementDto.getAvgRtUg());
                } else {
                    // 특별 목차 -> 성취율 결과 존재하지 않아서 결과값 "-"처리
//                    allClaMgTcrResDto.setAvgRtUd("-");
                    allClaMgTcrResDto.setAvgRtOv("-");
                    allClaMgTcrResDto.setAvgRtUg("-");
                }

				// 학습자 수준 분포
				AllClaMgTcrResDto learnerLevelDto = commonDao.select(MAPPER_NAMESPACE + "selectLearnerLevel", param);
				if (learnerLevelDto != null || learnerLevelDto.getUsrTotCnt() > 0) {
					allClaMgTcrResDto.setLrnrLevSl(learnerLevelDto.getLrnrLevSl());
					allClaMgTcrResDto.setLrnrLevNm(learnerLevelDto.getLrnrLevNm());
					allClaMgTcrResDto.setLrnrLevFs(learnerLevelDto.getLrnrLevFs());
					allClaMgTcrResDto.setLrnrLevBa(learnerLevelDto.getLrnrLevBa());
					allClaMgTcrResDto.setUsrTotCnt(learnerLevelDto.getUsrTotCnt());
				} else {
					// 학생 존재하지 않을때 0처리
					allClaMgTcrResDto.setLrnrLevSl("0");
					allClaMgTcrResDto.setLrnrLevNm("0");
					allClaMgTcrResDto.setLrnrLevFs("0");
					allClaMgTcrResDto.setLrnrLevBa("0");
					allClaMgTcrResDto.setUsrTotCnt(0);
				}

                allClaMgTcrResDtoList.add(allClaMgTcrResDto);
            }
		}

		return allClaMgTcrResDtoList;
	}

	/**
	 * AI 학습 진도율
	 * 
	 * @param optTxbId, lluNodId
	 * @return Map<String, Object>
	 */
	private AllClaMgTcrResDto selectAlRatioByCla(AllClaMgTcrReqDto param) {
		AllClaMgTcrResDto alRatioByCla = new AllClaMgTcrResDto();

		// SBJ_CD(과목코드) : MA 수학 / EN 영어
		String sbjCd = commonDao.select(MAPPER_NAMESPACE + "selectSbjCd", param);

		if (!param.getKmmpNodId().isEmpty()) {
			// 수학일 때
			if (sbjCd.equals("MA") || sbjCd.equals("CM1") || sbjCd.equals("CM2")) {
				// 수학 진도율 계산
				alRatioByCla = commonDao.select(MAPPER_NAMESPACE + "selectAlMtRatioByCla", param);
			}
			// 영어일 때
			if (sbjCd.equals("EN") || sbjCd.equals("CE1") || sbjCd.equals("CE2")) {
				// 뎁스 매퍼 만들어서 1단원 학습 갯수 카운트 후 파람에 세팅
				AllClaMgTcrResDto AeCntBykmmpNodId = commonDao.select(MAPPER_NAMESPACE + "selectAeCntBykmmpNodId", param);
				param.setAeCnt(AeCntBykmmpNodId.getAeCnt());
				// 영어진도율 계산
				alRatioByCla = commonDao.select(MAPPER_NAMESPACE + "selectAlEnRatioByCla", param);

			}
		}

		return alRatioByCla;
	}

}
