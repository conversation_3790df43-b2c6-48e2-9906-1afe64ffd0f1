package com.aidt.api.bc.home.tcr;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aidt.api.al.pl.dto.LearningMgDto;
import com.aidt.api.bc.home.tcr.dto.*;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:51:03
 * @modify 2024-01-05 17:51:03
 * @desc 교사_홈 Controller
 */

@Slf4j
@Tag(name="[bc] 홈[BcHomeTcr]", description="홈(교사)")
@RestController
@RequestMapping("/api/v1/bc/tcr/home")
public class BcHomeTcrController {

    @Autowired
    private BcHomeTcrService bcHomeTcrService;
    @Autowired
    private BcHomeTcrAlService bcHomeTcrAlService;

    @Autowired
    private JwtProvider jwtProvider;

    /**
     * 홈 조회 요청
     *
     * @param String
     * @return ResponseList<BcHomeTcrDto>
     */
    @Operation(summary="홈 조회", description="홈을 조회한다.(교사)")
    @GetMapping(value = "/{userId}")
    public ResponseDto<List<BcHomeTcrDto>> selectHomeList(@PathVariable("userId") String userId) {

    	// 2024-01-11 : 조회 방식의 파라미터는 단일 타겟ID가 아닐것으로 판단. 변경 필요

        log.debug("Entrance selectList");
        return Response.ok(bcHomeTcrService.selectHomeList(userId));
    }

    /**
     * 학생 현재 위치 상세 조회
     *
     * @param String (usrId)
     * @return ResponseDto<List<String>> 학생 현재 위치 상세 ([단원번호+단원명, 차시번호+차시명, 액티비티명, ...])
     */
    @Operation(summary="학생 현재 위치 상세 조회", description="학생 현재 위치 상세를 조회한다.")
    @PostMapping(value = "/selectStuCurLrnStDtl/{usrId}")
    public ResponseDto<String> selectStuCurLrnStDtl(@PathVariable("usrId") String usrId, @RequestParam(value = "isEng", required = false) Boolean isEng, @RequestBody BcStuCurLrnStDtlDto curLrnSt) {

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        Map<String, String> params = new HashMap<>();
        params.put("usrId", usrId);
        params.put("optTxbId", userDetails.getOptTxbId());
        params.put("isEng", isEng.toString());

        return Response.ok(bcHomeTcrService.selectStuCurLrnStDtl(params, curLrnSt));
    }

    /**
     * 최근 우리 반 학생 평가 제출 현황 조회
     */
    @Operation(summary="최근 우리 반 학생 평가 제출 현황 조회", description="최근 우리 반 학생 평가 제출 현황 조회한다.")
    @PostMapping(value = "/selectRcnClaEvSmt")
    public ResponseDto<List<BcRcnClaEvSmtDto>> selectRcnClaEvSmt(@RequestBody BcRcnClaEvSmtReqDto bcRcnClaEvSmtReqDto) {

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcRcnClaEvSmtReqDto.setClaId(userDetails.getClaId());

        return Response.ok(bcHomeTcrService.selectRcnClaEvSmt(bcRcnClaEvSmtReqDto));
    }

    /**
     * 최근 우리 반 학생 첨삭 제출 현황 조회
     */
    @Operation(summary="최근 우리 반 학생 평가 첨삭 제출 현황 조회", description="최근 우리 반 학생 첨삭 제출 현황 조회한다.")
    @PostMapping(value = "/selectRcnClaWrtSmt")
    public ResponseDto<Map<String, Object>> selectRcnClaWrtSmt(@RequestBody BcRcnClaWrtSmtReqDto bcRcnClaWrtSmtReqDto) {

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcRcnClaWrtSmtReqDto.setOptTxbId(userDetails.getOptTxbId());

        return Response.ok(bcHomeTcrService.selectRcnClaWrtSmt(bcRcnClaWrtSmtReqDto));
    }

    /**
     * 우리 반 상태 학생 목록 조회
     */
    @Operation(summary="우리반의 상태 이상 학생 목록 조회", description="우리반의 상태 이상 학생 목록 조회")
    @GetMapping(value = "/selectGDEStuList")
    public ResponseDto<List<Map<String, String>>> selectGDEStuList(@RequestParam(defaultValue = "1") String day) {

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String optTxbId = userDetails.getOptTxbId();

        return Response.ok(bcHomeTcrService.selectGDEStuList(Map.of("optTxbId", optTxbId,"day", day)));
    }

    /**
     * 교사 홈 단원 성취 현황
     */
    @Operation(summary="교사 홈 단원 성취현황 데이터 조회", description="교사 홈 단원 성취현황 데이터 조회")
    @GetMapping(value = "/selectLluAch")
    public ResponseDto<List<Map<String, Object>>> selectLluAch() {

        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        String optTxbId = userDetails.getOptTxbId();

        return Response.ok(bcHomeTcrService.selectLluAch(optTxbId));
    }

    @Tag(name="교사홈 > AI 맞춤 학습 코칭", description="AI 맞춤 학습 코칭 단원별 데이터")
    @PostMapping(value = "/selectStuAeEvInfoList")
    public ResponseDto<Map<String, Object>> selectStuAeEvInfoList(@RequestBody @Valid LearningMgDto dto) {
        CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
        if(dto.getOptTxbId() == null) {
            dto.setOptTxbId(securityUserDetailDto.getOptTxbId());
        }
        if(dto.getUsrId() == null) {
            dto.setUsrId(securityUserDetailDto.getUsrId());
        }
        if(dto.getClaId() == null) {
            dto.setClaId(securityUserDetailDto.getClaId());
        }
        return Response.ok(bcHomeTcrAlService.selectStuAeEvInfoList(dto));
    }

}
