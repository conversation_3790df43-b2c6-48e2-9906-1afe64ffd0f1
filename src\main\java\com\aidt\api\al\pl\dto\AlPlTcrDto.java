package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc Sample Mybatis DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlPlTcrDto {
	
	@Parameter(name="운영교과서ID")
    private String optTxbId;
	
	@Parameter(name="교과서ID")
    private String txbId;
	
    @Parameter(name="순번")
    private int idx;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="이름")
    private String usrNm;

    @Parameter(name="지역")
    private String cntry;

    @Parameter(name="폰모델")
    private String phone;

    @Parameter(name="색상")
    private String color;

    @Parameter(name="가격")
    private String price;

    @Parameter(name="수량")
    private String quan;

    @Parameter(name="일자")
    private String dt;

    @Parameter(name="생성일자")
    private String cdt;

    @Parameter(name="수정일자")
    private String udt;
    
    @Parameter(name=" 대상")
    private String target;
    
    @Parameter(name=" 로우갯수")
    private int rowCnt;
}
