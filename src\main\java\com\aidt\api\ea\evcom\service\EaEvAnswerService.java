package com.aidt.api.ea.evcom.service;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.api.common.helper.JwtHelper;
import com.aidt.api.ea.evcom.adapter.EaEvAnswerAdapter;
import com.aidt.api.ea.evcom.dto.EaEvAnswerNote;
import com.aidt.api.ea.evcom.dto.EaEvAnswerNoteReqDto;
import com.aidt.api.ea.evcom.dto.EaEvAnswerReqDto;
import com.aidt.api.ea.evcom.dto.EaEvLearnActivityDto;
import com.aidt.api.ea.evcom.dto.EaEvResult;
import com.aidt.api.ea.evcom.event.EaEvAnswerSubmitEvent;
import com.aidt.api.ea.evcom.helper.EaEvScoringHelper;
import com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto;
import com.aidt.api.tl.lrnwif.stu.TlLrnwIfStuService;
import com.aidt.common.CommonUserDetail;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service
@RequiredArgsConstructor
public class EaEvAnswerService {

	private final EaEvAnswerAdapter eaEvAnswerAdapter;
	private final EaEvScoringHelper eaEvScoringHelper;
	private final JwtHelper jwtHelper;
	private final TlLrnwIfStuService tlLrnwIfstuService;
	private final EaEvSppNtnService eaEvSppNtnService;
	private final ApplicationEventPublisher publisher;

	public void saveNote(EaEvAnswerNoteReqDto dto) {
		var user = jwtHelper.getCommonUserDetail();
		var eaEvRs = getEaEvRs(dto.getEvId(), user);
		var eaEvAnswerNote = EaEvAnswerNote.of(dto, eaEvRs);
		if (eaEvRs.isCompleted()) {
			eaEvAnswerAdapter.upsertReEaEvAnswerNote(eaEvAnswerNote);
			return;
		}

		eaEvAnswerAdapter.upsertEaEvAnswerNote(eaEvAnswerNote);
	}

	public void saveAnswer(EaEvAnswerReqDto dto) {

		//todo: 노트만 저장 API 분리 적용 시 삭제
		if (dto.isNoteOnly()) {
			saveNote(EaEvAnswerNoteReqDto.of(dto));
			return;
		}

		var user = jwtHelper.getCommonUserDetail();
		var eaEvRs = getEaEvRs(dto.getEvId(), user);

		processEvaluationResults(eaEvRs, dto.getQtmList(), false);
	}

	public void submitAnswer(EaEvAnswerReqDto dto) {
		var user = jwtHelper.getCommonUserDetail();
		var eaEvRs = getEaEvRs(dto.getEvId(), user);

		processEvaluationResults(eaEvRs, dto.getQtmList(), true);
		//학습 활동 저장
		saveLearnActivity(dto, user);

		//풀이 상태 업데이트
		eaEvAnswerAdapter.updateAnswerExplanationStatus(dto.getEvId(), user.getUsrId());

		//fixme: 평가 보충/심화를 바로 사용하지 않는다면 비동기로 해도 되지 않을까?
		//평가 보충/심화 저장
		eaEvSppNtnService.saveEaEvSppNtnRs(eaEvRs, user);

		//평가 제출 후, 비동기 후속 처리 이벤트 실행
		submitEvent(dto, eaEvRs, user);
	}

	private void processEvaluationResults(EaEvResult eaEvRs, List<EaEvAnswerReqDto.EaEvAnswerQtmDto> questionAnswers,
		boolean isSubmit) {

		//문항 정오답 처리
		var answers = eaEvScoringHelper.evaluateAnswers(questionAnswers);
		eaEvRs.addAnswers(answers);
		//재응시 일 경우
		if (eaEvRs.isCompleted()) {
			//제출 완료된 본 평가의 재응시 평가도 완료했거나, 본 평가만 진행한 상태일 경우 재응시 회차 증가
			eaEvRs.incrementReexaminationRound();
			//재응시 답안 처리
			eaEvAnswerAdapter.upsertReEaEvAnswers(eaEvRs);
			//재응시 미완료
			if (!isSubmit) {
				eaEvRs.withdraw();
			}
			//재응시 결과 처리
			eaEvAnswerAdapter.upsertReEaEvRs(eaEvRs);
			return;
		}
		//본 평가 답안 처리
		eaEvAnswerAdapter.upsertEvaEvAnswers(eaEvRs);

		if (isSubmit) {
			eaEvRs.complete();
		}
		//본 평가 결과 업데이트
		eaEvAnswerAdapter.upsertEaEvRs(eaEvRs);
	}

	private EaEvResult getEaEvRs(Integer evId, CommonUserDetail userDetail) {

		var eaEvRs = eaEvAnswerAdapter.getEaEvRs(evId, userDetail.getUsrId());

		if (ObjectUtils.isEmpty(eaEvRs)) {
			log.warn("평가 정보를 찾을 수 없습니다.");
			throw new BizException("평가 정보를 찾을 수 없습니다.");
		}
		//감사 정보 추가
		eaEvRs.addAudit(userDetail);
		//응시 시작 처리
		eaEvRs.startExam();

		return eaEvRs;
	}

	//fixme: 이벤트 태워서 비동기 처리로 해도 되지 않을까? (정책이나 프로세스 확인 시)
	private void saveLearnActivity(EaEvAnswerReqDto reqDto, CommonUserDetail userDetail) {

		var learnActivity = EaEvLearnActivityDto.of(reqDto, userDetail);

		if (ObjectUtils.isEmpty(learnActivity)) {
			return;
		}

		//목차 및 활동 아이디가 없을 경우, 조회
		if (learnActivity.isAnyNodeOrActivityIdBlank()) {
			var getIds = eaEvAnswerAdapter.getNodeOrActivityId(learnActivity.getEvId(), userDetail.getOptTxbId());
			learnActivity.addMapNodeAndActivityIds(getIds.getLrmpNodId(), getIds.getLrnAtvId());
		}

		if (!learnActivity.isAnyNodeOrActivityIdBlank()) {

			//회원 구분 코드 변환
			var usrTpCd = Optional.ofNullable(userDetail.getUsrTpCd())
				.filter(StringUtils::isNotBlank)
				.map(s -> s.substring(0, 1))
				.orElseThrow(() -> new BizException("회원 구분 코드를 변환할 수 없습니다."));

			//학습활동 저장 처리 호출
			tlLrnwIfstuService.saveLrnAtvInfo(TlLrnwAtvSaveDto.builder()
					.lrmpNodId(learnActivity.getLrmpNodId())
					.lrnAtvId(learnActivity.getLrnAtvId())
					.lrnStCd(learnActivity.getLrnStCd())
					.lrnTmScnt(learnActivity.getLrnTmScnt())
					.lrnUsrId(learnActivity.getLrnUsrId())
					.optTxbId(learnActivity.getOptTxbId())
					.dbId(learnActivity.getDbId())
					.build(),
				usrTpCd
			);
		}
	}

	private void submitEvent(EaEvAnswerReqDto eaEvAnswerReqDto, EaEvResult eaEvRs, CommonUserDetail userDetail) {
		publisher.publishEvent(
			new EaEvAnswerSubmitEvent(eaEvAnswerReqDto, eaEvRs, userDetail, jwtHelper.getAccessToken()));
	}

}