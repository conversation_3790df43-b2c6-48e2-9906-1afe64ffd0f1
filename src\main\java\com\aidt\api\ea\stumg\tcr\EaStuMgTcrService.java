package com.aidt.api.ea.stumg.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto;
import com.aidt.api.ea.stumg.tcr.dto.EaStuMgTcrDto;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학생 관리 - 교사 Service
 */
@Service
public class EaStuMgTcrService {

	//private final String MAPPER_NAMESPACE = "api.ea.stumg.tcr.";
	
	//@Autowired
	//private CommonDao commonDao;
	
	// Return Object
	List<EaStuMgTcrDto> responseList = new ArrayList<>();
	

	
}
