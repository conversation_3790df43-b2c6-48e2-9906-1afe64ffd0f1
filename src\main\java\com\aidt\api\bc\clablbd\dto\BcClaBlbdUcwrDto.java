package com.aidt.api.bc.clablbd.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-06-07 17:20:10
 * @modify 2024-06-07 17:20:10
 * @desc 학급게시판댓글 dto
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcClaBlbdUcwrDto{

	@Parameter(name="학급게시판댓글ID")
	private Long claBlbdUcwrId;

	@Parameter(name="학급게시판ID")
	private Long claBlbdId;

	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="케리스ID")
	private String kerisUsrId;

	@Parameter(name="상위댓글ID")
	private Long urnkUcwrId;

	@Parameter(name="정렬순서")
	private Long srtOrdn;

	@Parameter(name="댓글깊이")
	private Long ucwrDpth;

	@Parameter(name="학급게시판댓글내용")
	@Size(max = 500, message = "댓글은 2자 이상 500자 이하로 입력해주세요.")
	private String claBlbdUcwrCn;
	
	@Parameter(name="댓글승인여부")
	private String ucwrAprYn;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성일시")
	private String crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private String mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;

	@Parameter(name="사용자명")
	private String usrNm;
	
	@Parameter(name="사용자유형코드")
	private String usrTpCd;
	
	@Parameter(name="등록수정삭제flag")
	private String flag;
}
