package com.aidt.api.at.user;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.at.dto.User;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/at/user")
@Tag(name="[at] UserDetail", description="UserDetail")
public class UserDetailController {

	@Autowired
	private JwtProvider jwtProvider;
	
    @Autowired
    private UserDetailService userDetailService;

    @Operation(summary="사용자 정보", description="사용자 정보 조회")
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<User.UserResponseDto> selectUserDetail(HttpServletRequest req, @Valid @RequestBody User.RequestDto requestDto) {
    	
    	//String usrId = requestDto.getUsrId();    	
    	String usrId = jwtProvider.getCommonUserDetail().getUsrId();
    	log.debug(">> usrId: {}",usrId);
    	
    	User.UserResponseDto userResponseDto = userDetailService.selectUserDetail(usrId);
    	if(userResponseDto==null) {
    		throw new RuntimeException("사용자가 존재 하지 않습니다.");
    	}
        return Response.ok(userResponseDto);
    }
    
}
