package com.aidt.api.at.err;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.aidt.api.at.err.dto.FrontErrLogDto;
import com.aidt.api.at.err.dto.WebSocketLogDto;
import com.aidt.common.CommonDao;

@Service
public class FrontErrLogService {

	@Autowired
	private CommonDao commonDao;

	@Transactional
	public boolean insertFrontErrLog(FrontErrLogDto dto) {
		return commonDao.insert("api.bc.common.cm.insertFrontErrLog", dto) > 0 ? true : false;
	}

	@Transactional
	public void insertFrontWebSocketLog(WebSocketLogDto dto) {
		String insYn = commonDao.select("api.bc.common.cm.selectFrontWebSocketInsYn");
		
		if(StringUtils.hasText(insYn) && "Y".equals(insYn)) {
			commonDao.insert("api.bc.common.cm.insertFrontWebSocketLog", dto);
		}
	}
}
