package com.aidt.api.tl.lrnwif.dto;

import java.util.List;

import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlLrnwTocAtvDto Dto 단원목차및학습활동
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlLrnwTocAtvDto {

    /** 대단원No */
    @Parameter(name="대단원No")
    private String lluNodNo;
    /** 대단원ID */
    @Parameter(name="대단원ID")
    private String lluNodId;
    /** 대단원명 */
    @Parameter(name="대단원명")
    private String lluNodNm;
    /** 대단원노출여부 */
    @Parameter(name="대단원노출여부")
    private String lluEspYn;
    /** 중단원ID */
    @Parameter(name="중단원ID")
    private String mluNodId;
        /** 중단원명 */
    @Parameter(name="중단원명")
    private String mluNodNm;
    /** 중단원노출여부 */
    @Parameter(name="중단원노출여부")
    private String mluEspYn;
    /** 소단원ID */
    @Parameter(name="소단원ID")
    private String sluNodId;
    /** 소단원명 */
    @Parameter(name="소단원명")
    private String sluNodNm;
    /** 소단원노출여부 */
    @Parameter(name="소단원노출여부")
    private String sluEspYn;
    /** 차시ID */
    @Parameter(name="차시ID")
    private String tcNodId;
    /** 차시명 */
    @Parameter(name="차시명")
    private String tcNodNm;
    /** 차시노출여부 */
    @Parameter(name="차시노출여부")
    private String tcEspYn;
    /** 개념활동건수 */
    @Parameter(name="개념활동건수")
    private String atvClCnt;
    /** 익힘활동건수 */
    @Parameter(name="익힘활동건수")
    private String atvWbCnt;
    /** 학습활동건수 */
    @Parameter(name="학습활동건수")
    private String atvTotCnt;
    /** 시작학습활동ID(개념학습) */
    @Parameter(name="시작학습활동ID(개념학습)")
    private String strAtvId;
    /** 시작학습활동ID(익힘) */
    @Parameter(name="시작학습활동ID(익힘)")
    private String wbStrAtvId;
    /** 학습단계노출여부 */
    @Parameter(name="학습단계노출여부")
    private String taskEpsYn;
    /** 학습활동노출여부 */
    @Parameter(name="학습활동노출여부")
    private String atvEspYn;
    /** 차시잠금여부 */
    @Parameter(name="차시잠금여부")
    private String lcknYn;
    /** 교과시작페이지 */
    @Parameter(name="교과시작페이지")
    private String txbStrPgeNo;
    /** 교과종료페이지 */
    @Parameter(name="교과종료페이지")
    private String txbEndPgeNo;
    /** 교과학습사용여부 */
    @Parameter(name="교과학습사용여부")
    private String txbUseYn;
    /** 익힘시작페이지 */
    @Parameter(name="익힘시작페이지")
    private String wkbStrPgeNo;
    /** 익힘종료페이지 */
    @Parameter(name="익힘종료페이지")
    private String wkbEndPgeNo;
    /** 익힘학습사용여부 */
    @Parameter(name="익힘학습사용여부")
    private String wkbUseYn;
    /** 학습도구 */
    @Parameter(name="학습도구")
    private List<TlLrnwLrnTlDto> lrnTlList;
    /** 학습단계 */
    @Parameter(name="학습단계")
    private List<TlLrnwLrnStpDto> lrnStpList;
    /** 차시-교육과정표준체계ID목록 */
    @Parameter(name="차시-교육과정표준체계ID목록")
    private List<String> crclStdIdList;
    /** LCMS기본자료목록 */
    @Parameter(name="기본자료목록")
    private List<TlLsnMtrlDto> bsMtrlList;
    /** LMS학습자료목록 */
    @Parameter(name="기본자료목록")
    private List<TlLsnMtrlDto> lmMtrlList;
    /** 외부활동설정 */
    @Parameter(name="외부활동설정")
    private TlLrnwExtAtvSetm extAtvSetm;
}
