package com.aidt.api.bc.cm.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "CM_케리스_FRONT_로그")
public class KerisFrontLogDto {

	@Parameter(name = "케리스 회원ID", required = true)
	private String kerisUsrId;

	@Parameter(name = "사용자ID", required = false)
	private String usrId;

	@Parameter(name = "운영교과서ID", required = false)
	private String optTxbId;

	@Parameter(name = "접속URL", required = true)
	private String connUrl;

	@Parameter(name = "Header정보", required = true)
	private String headerInfo;

	@Parameter(name = "BODY내용", required = true)
	private String bodyCn;

	@Parameter(name = "결과메시지", required = true)
	private String rsMsg;

	@Parameter(name = "FRONT요청시간")
	private String frontReqTm;

	@Parameter(name = "SYSTEM 코드")
	private String systemCd;

}
