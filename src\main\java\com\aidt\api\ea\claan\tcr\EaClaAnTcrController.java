package com.aidt.api.ea.claan.tcr;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrDto;
import com.aidt.api.ea.claan.tcr.dto.EaClaAnTcrSaveDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 학급 분석", description="학급 분석")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/tcr/claan")
public class EaClaAnTcrController {
	@Autowired
	private JwtProvider jwtProvider;
	@Autowired
	EaClaAnTcrService eaClaAnTcrService;

	/**
	 * 학급 분석 조회 요청
	 *
	 * @param param
	 * @return ResponseList<Map<String,Object>>
	 */
	@Tag(name="[ea] 학생 DIY 평가 목록 조회", description="학생 DIY 평가 목록 조회")
	@PostMapping(value = "/selectEaClaAnInfo")
	public ResponseDto<Map<String,Object>> selectEaClaAnInfo(@RequestBody EaClaAnTcrDto param) {
		log.debug("Entrance selectDiyEvStuList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		param.setClaId(userDetails.getClaId()); // TODO 학급ID
		return Response.ok(eaClaAnTcrService.selectEaClaAnInfo(param));
	}

	/**
	 * 오답 BEST 조회 요청
	 *
	 * @param param
	 * @return ResponseList<List<Map<String,Object>>>
	 */
	@Tag(name="[ea] 오답 BEST 조회", description="오답 BEST 조회")
	@PostMapping(value = "/selectWrongAnwBestList")
	public ResponseDto<List<Map<String,Object>>> selectWrongAnwBestList(@RequestBody EaClaAnTcrDto param) {
		log.debug("Entrance selectWrongAnwBestList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		return Response.ok(eaClaAnTcrService.selectWrongAnwBestList(param));
	}

	/**
	 * 오답 BEST 오답, 오답유사 시험지 출제 등록 요청
	 *
	 * @param param
	 * @return ResponseDto<Map<String,Object>>
	 */
	@Tag(name="[ea] 오답 BEST 오답, 오답유사 시험지 등록", description="오답 BEST 오답, 오답유사 시험지 등록")
	@PostMapping(value = "/saveEaClaAnTcrTestPaper", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseDto<Map<String,Object>> saveEaClaAnTcrTestPaper(@Valid @RequestBody EaClaAnTcrSaveDto param) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		param.setUsrId(userDetails.getUsrId());	   	 //사용자 ID
		param.setDbId(userDetails.getTxbId());  	 //DB_ID 추후 변경 될 수 있다고 함
		return Response.ok(eaClaAnTcrService.saveEaClaAnTcrTestPaper(param));
	}

	
	/**
	 * 전체 학급 관리 요청
	 *
	 * @param param
	 * @return ResponseList<Map<String,Object>>
	 */
	@Tag(name="[ ] 전체 학급 관리", description="전체 학급 진도율, 성취도, 학습자 수준 분포 조회")
	@PostMapping(value = "/selectClaAllInfo")
	public ResponseDto<Map<String,Object>> selectClaAllInfo(@RequestBody EaClaAnTcrDto param) {
		log.debug("Entrance selectDiyEvStuList");
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		param.setOptTxbId(userDetails.getOptTxbId());//운영 교과서 ID
		param.setClaId(userDetails.getClaId());
		return Response.ok(eaClaAnTcrService.selectClaAllInfo(param));
	}
	
}
