package com.aidt.api.bc.tnte.stu;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.tnte.dto.BcNewTnteDto;
import com.aidt.api.bc.tnte.dto.BcTnteDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.CoreUtil.Json;
import com.aidt.common.util.WebFluxUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import com.aidt.api.bc.cm.BcCmUtil;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:52:47
 * @modify 2024-01-05 17:52:47
 * @desc 필기 Controller
 */

//@Slf4j
@Tag(name="[bc] 필기[BcTnteStu]", description="필기(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/tnte")
public class BcTnteStuController {

    @Autowired
    private BcTnteStuService bcTnteStuService;

	@Autowired
	private JwtProvider jwtProvider;

    @Autowired
	private WebFluxUtil webFluxUtil;
    
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    /**
     * 필기 목록 조회 요청
     *
     * @param BcTnteDto
     * @return ResponseList<BcTnteDto>
     */
    @Operation(summary="필기 목록 조회", description="필기 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectTnteList")
    public ResponseDto<List<BcTnteDto>> selectTnteList(BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
        bcTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteStuService.selectTnteList(bcTnteDto));
    }

    /**
     * 필기 상세 조회 요청
     *
     * @param BcTnteDto
     * @return BcTnteDto
     * @throws JsonProcessingException 
     * @throws JsonMappingException 
     */
//    @Operation(summary="필기 상세 조회", description="필기 상세를 조회한다.(학생)")
//    @GetMapping(value = "/selectTnteDtl")
//    public ResponseDto<BcTnteDto> selectTnteDtl(BcTnteDto bcTnteDto) throws JsonMappingException, JsonProcessingException {
//    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
//    	bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
//    	
//    	if (bcTnteDto.getTnteDvCd() == null || bcTnteDto.getTnteDvCd().equals("")) {
//    		if (bcTnteDto.getLrnTpCd() != null && bcTnteDto.getLrnTpCd().equals("SL")) {
//    			bcTnteDto.setTnteDvCd("TC");
//    		}else {
//    			bcTnteDto.setTnteDvCd("MC");
//    		}
//    	}
//    	
//        BcTnteDto bcTnteDto_ = bcTnteStuService.selectTnteDtl(bcTnteDto);
//        
//        if (bcTnteDto_ != null){
//            String cdnPath = bcTnteDto_.getCdnPthNm();
//            String jsonData = "";
//            if (!cdnPath.isEmpty()){
//            	//파일이 있는 경우만 json 파일정보를 읽어 온다.
//        		if (BcCmUtil.isFileExists(BUCKET_NAME, cdnPath)) {
//        			jsonData = webFluxUtil.get(BcCmUtil.makeFleCdnUrl(BUCKET_NAME, cdnPath), String.class);
//                    bcTnteDto_.setCanvasJsonData(Json.jsonString2ListMap(jsonData));	
//        		}else {
//        			bcTnteDto_ = null;
//        		}
//            }
//        }
//        
//        return Response.ok(bcTnteDto_);
//    }
    @Operation(summary="필기 상세 조회", description="필기 상세를 조회한다.(학생)")
    @GetMapping(value = "/selectTnteDtl")
    public ResponseDto<BcTnteDto> selectTnteDtl(BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	
    	if (bcTnteDto.getTnteDvCd() == null || bcTnteDto.getTnteDvCd().equals("")) {
    		if (bcTnteDto.getLrnTpCd() != null && bcTnteDto.getLrnTpCd().equals("SL")) {
    			bcTnteDto.setTnteDvCd("TC");
    		}else {
    			bcTnteDto.setTnteDvCd("MC");
    		}
    	}
    	
        BcTnteDto bcTnteDto_ = bcTnteStuService.selectTnteDtl(bcTnteDto);
        
//        if (bcTnteDto_ != null){
//            String cdnPath = bcTnteDto_.getCdnPthNm();
//            if (!cdnPath.isEmpty()){
//            	//파일이 있는 경우만 json 파일정보를 읽어 온다.
//        		if (BcCmUtil.isFileExists(BUCKET_NAME, cdnPath)) {
//        			try {
//        				bcTnteDto_.setCanvasJsonData(BcCmUtil.s3JsonFileReader(BUCKET_NAME, cdnPath));	
//        			}
//        			catch(IOException e) {
//        				bcTnteDto_ = null;	
//        			}
//        		}else {
//        			bcTnteDto_ = null;
//        		}
//            }
//        }
        
        return Response.ok(bcTnteDto_);
    }
    

    /**
     * 필기 상세 조회 요청 (학습창)
     *
     * @param LwTnteDto
     * @return LwTnteDto
     */
    @Operation(summary="필기 상세 조회(학습창)", description="필기 상세를 조회한다.(교사)")
    @GetMapping(value = "/selectLayoutTnteDtl")
    public ResponseDto<BcTnteDto> selectLayoutTnteDtl(BcTnteDto bcTnteDto) {

    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcTnteDto.setLrnUsrId(userDetails.getUsrId());

    	return Response.ok(bcTnteStuService.selectLayoutTnteDtl(bcTnteDto));
    }

    /**
     * 필기 등록 요청
     *
     * @param BcTnteDto
     * @return ResponseDto<int>
     */
    @Operation(summary="필기 등록", description="필기를 등록한다.(교사)")
    // @PostMapping(value = "/insertTnte", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @PostMapping(value = "/insertTnte", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
    public ResponseDto<Integer> insertTnte(@Valid @RequestBody BcTnteDto bcTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        bcTnteDto.setOptTxbId(userDetails.getOptTxbId());
        if (bcTnteDto.getLrnUsrId() ==null || bcTnteDto.getLrnUsrId().equals("")) {
        	bcTnteDto.setLrnUsrId(userDetails.getUsrId());
        }
        bcTnteDto.setCrtrId(userDetails.getUsrId());
        bcTnteDto.setMdfrId(userDetails.getUsrId());
        bcTnteDto.setDbId(userDetails.getTxbId());
        return Response.ok(bcTnteStuService.insertTnte(bcTnteDto));
    }


    /**
     * 필기 삭제 요청
     *
     * @param List<BcTnteDto>
     * @return ResponseDto<int>
     */
    @Operation(summary="필기 삭제", description="필기를 삭제한다.(학생)")
    @DeleteMapping(value = "/deleteTnte")
    public ResponseDto<Integer> deleteTnte(@RequestBody BcTnteDto tnteDto) {
   
        return Response.ok(bcTnteStuService.deleteTnteList(tnteDto));
    }
    
    
    
    
    
    
    
    
    
    
    // 2024-07-16 노트 구분 selectBox 조회(저장되어 있는 케이스만 보여줌)
    @Operation(summary="노트 구분 조회", description="노트 구분을 조회한다.(학생)")
    @GetMapping(value = "/selectNewStuTnteDvList")
    public ResponseDto<List<BcNewTnteDto>> selectNewStuTnteDvList(BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteStuService.selectNewStuTnteDvList(bcNewTnteDto));
    }
    
    // 2024-07-16 노트 구분 상세 selectBox 조회(저장되어 있는 케이스만 보여줌)
    @Operation(summary="노트 구분 상세 조회", description="노트 구분 상세를 조회한다.(학생)")
    @GetMapping(value = "/selectNewStuTnteDvDtlList")
    public ResponseDto<List<BcNewTnteDto>> selectNewStuTnteDvDtlList(BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteStuService.selectNewStuTnteDvDtlList(bcNewTnteDto));
    }
    
    
    
    // 2024-07-16 노트 목록 조회(개선안 버전)
    @Operation(summary="노트 목록 조회", description="노트 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectNewStuTnteList")
    public ResponseDto<List<BcNewTnteDto>> selectNewStuTnteList(BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteStuService.selectNewStuTnteList(bcNewTnteDto));
    }
    
    
    
    
    // 2024-07-19 노트 개선안 삭제
    @Operation(summary="노트 삭제", description="노트를 삭제한다.(학생)")
    @DeleteMapping(value = "/deleteNewStuTnte")
    public ResponseDto<Integer> deleteNewStuTnte(@RequestBody BcNewTnteDto bcNewTnteDto) {
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	bcNewTnteDto.setOptTxbId(userDetails.getOptTxbId());
    	bcNewTnteDto.setLrnUsrId(userDetails.getUsrId());
        return Response.ok(bcTnteStuService.deleteNewStuTnte(bcNewTnteDto));
    }
    
    
    
    


}
