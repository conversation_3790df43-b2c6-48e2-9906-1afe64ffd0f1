package com.aidt.api.al.cmt.dto.res;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-27 17:27:10
 * @modify date 2024-05-27 17:27:10
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnResWrapperDto {

    private List<AiCmtResDto> total;

    private List<AiCmtResDto> ara;

}
