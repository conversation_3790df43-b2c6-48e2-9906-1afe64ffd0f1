package com.aidt.api.tl.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-06-05 16:00:01
 * @modify date 2024-06-05 16:00:01
 * @desc TlCrclCntStdInfoDto 교육과정콘텐츠표준 정보 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCrclCntStdInfoDto {

	@Parameter(name="학습활동ID")
	private String lrnAtvId;

    @Parameter(name="교육과정콘텐츠ID")
    private String crclCtnId;

    @Parameter(name="콘텐츠ID")
    private String ctnId;

    @Parameter(name="교육과정콘텐츠유형코드")
    private String crclCtnTpCd;

    @Parameter(name="교육과정콘텐츠표준ID")
    private String crclCtnStdId;

    @Parameter(name="교육과정콘텐츠1단계ID")
    private String crclCtnElm1Id;

    @Parameter(name="교육과정콘텐츠2단계ID")
    private String crclCtnElm2Id;

    @Parameter(name="교육과정성취기준ID")
    private String crclAchBsId;

    @Parameter(name="교육과정범주")
    private String crclCtgr;

    @Parameter(name="교육과정내용영역")
    private String crclCnAra;

    @Parameter(name="교육과정행동영역")
    private String crclActAra;

    @Parameter(name="교육과정교과역량")
    private String crclSbcCpbl;

    @Parameter(name="정렬순서")
    private String srtOrdn;

    @Parameter(name="삭제여부")
    private String delYn;


}
