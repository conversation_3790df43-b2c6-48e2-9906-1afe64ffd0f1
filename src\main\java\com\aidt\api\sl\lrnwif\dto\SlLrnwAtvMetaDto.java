package com.aidt.api.sl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-14 14:13:26
 * @modify : date 2024-03-14 14:13:26
 * @desc : 특별학습 활동 메타 목록
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwAtvMetaDto {
	/** 특별학습콘텐츠ID */
	@Parameter(name="특별학습콘텐츠ID")
	private String lrnAtvId;
	/** 특별학습콘텐츠명 */
	@Parameter(name="특별학습콘텐츠명")
	private String lrnAtvNm;
	/** 콘텐츠유형코드(파일유형코드 FI=파일, UR=URL) */
	@Parameter(name="콘텐츠유형코드(파일유형코드)")
	private String ctnTpCd;
	/** CDN경로명 */
	@Parameter(name="CDN경로명")
	private String ctnUrl;
	/** 정렬순서 */
	@Parameter(name="정렬순서")
	private String rcstnOrdn;
	/** 학습상태코드 */
	@Parameter(name="학습상태코드")
	private String lrnStCd;
	/** 학습시간초수 */
	@Parameter(name="학습시간초수")
	private String lrnTmScnt;
	/** 교육과정콘텐츠표준ID */
	@Parameter(name="교육과정콘텐츠표준ID")
	private String crclCtnStdId;
	// /** 특별학습콘텐츠유형코드(PL=동영상, HT=HTML) */
	// @Parameter(name="특별학습콘텐츠유형코드")
	// private String spLrnCtnTpCd;
	// /** 콘텐츠시작파일명 */
	// @Parameter(name="콘텐츠시작파일명")
	// private String strFleNm;
    /** 콘텐츠메타데이터목록 */
    @Parameter(name="콘텐츠메타데이터목록")
    private SlLrnwLrnCtnDto ctnMtd;

}
