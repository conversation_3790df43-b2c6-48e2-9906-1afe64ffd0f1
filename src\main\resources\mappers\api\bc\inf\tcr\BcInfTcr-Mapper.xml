<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.inf.tcr">


	<!-- 2024-06-17 조회시마다 알림 전체 읽음 처리 로직 추가 -->
	<update id="updateInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfTcr-Mapper.xml - updateInfList */
		UPDATE LMS_LRM.CM_INFM
		SET
			COFM_YN = 'Y'
			, MDFR_ID = #{usrId}
			, MDF_DTM = NOW()
		WHERE INFM_OBJ_USR_ID = #{usrId}
		AND (COFM_YN = 'N' OR COFM_YN IS NULL)
	</update>

	<!-- 알림 조회 -->
	<select id="selectInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto" resultType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfTcr-Mapper.xml - selectInfList */
		SELECT
			A.INFM_ID as INFM_ID,
			A.OPT_TXB_ID as OPT_TXB_ID,
			A.TCR_USR_ID as TCR_USR_ID,
			A.INFM_MSG_ID as INFM_MSG_ID,
			A.INFM_OBJ_USR_ID as INFM_OBJ_USR_ID,
			A.COFM_YN as COFM_YN,
			A.CRTR_ID as CRTR_ID,
			DATE_FORMAT(A.CRT_DTM, '%m. %d. %p %h:%i') as CRT_DTM,
			A.MDFR_ID as MDFR_ID,
			A.MDF_DTM as MDF_DTM,
			A.DB_ID as DB_ID,
			B.INFM_TP_CD as INFM_TP_CD,
			B.INFM_CL_CD as INFM_CL_CD,
			B.INFM_DTL_CL_CD as INFM_DTL_CL_CD,
			B.INFM_NM as INFM_NM,
			B.INFM_CN as INFM_CN,
			A.INFM_MV_CN
		FROM
			LMS_LRM.CM_INFM A
		INNER JOIN
			LMS_LRM.CM_INFM_MSG B
		ON A.INFM_MSG_ID = B.INFM_MSG_ID
		<where>
			A.INFM_OBJ_USR_ID = #{usrId}
			<if test='infmTpCd != null and !infmTpCd.equals("")'>
				AND B.INFM_TP_CD = #{infmTpCd}
			</if>
		</where>
		ORDER BY A.CRT_DTM DESC
		<if test = 'pageSize != null and !"".equals(pageSize) and pageNo != null and !"".equals(pageNo)'>
			LIMIT #{pageSize}
			OFFSET #{pageOffset}
		</if>
	</select>

	<insert id="insertInfMsgList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto" useGeneratedKeys="true" keyProperty="infmMsgId">
		/** BcInfTcr-Mapper.xml - insertInfMsgList */
		INSERT INTO CM_INFM_MSG (
			INFM_TP_CD,
			INFM_CL_CD,
			INFM_DTL_CL_CD,
			INFM_NM,
			INFM_CN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		VALUES(
			#{infmTpCd},
			#{infmClCd},
			#{infmDtlClCd},
			#{infmNm},
			#{infmCn},
			#{usrId},
			NOW(),
			#{usrId},
			NOW(),
			#{dbId}
		)
	</insert>

	<insert id="insertInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfTcr-Mapper.xml - insertInfList */
		INSERT INTO CM_INFM (
			OPT_TXB_ID,
			TCR_USR_ID,
			INFM_MSG_ID,
			INFM_OBJ_USR_ID,
			COFM_YN,
			INFM_MV_CN,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		)
		VALUES(
			#{optTxbId},
			#{tcrUsrId},
			#{infmMsgId},
			#{infmObjUsrId},
			'N',
			#{infmMvCn},
			#{usrId},
			NOW(),
			#{usrId},
			NOW(),
			#{dbId}
		)
	</insert>

	<delete id="deleteInfMsg" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfTcr-Mapper.xml - deleteInfMsg */
		DELETE FROM LMS_LRM.CM_INFM_MSG 
		WHERE INFM_MSG_ID IN (
								SELECT INFM_MSG_ID
								 FROM LMS_LRM.CM_INFM
								WHERE
									INFM_OBJ_USR_ID = #{usrId}
									AND DB_ID = #{dbId}
								<if test= 'infmId != 0'>
									AND INFM_ID = #{infmId}
								</if>
							)
	</delete>

	<delete id="deleteInfList" parameterType="com.aidt.api.bc.inf.dto.BcInfDto">
		/** BcInfTcr-Mapper.xml - deleteInfList */
		DELETE FROM LMS_LRM.CM_INFM
		WHERE
			INFM_OBJ_USR_ID = #{usrId}
			AND DB_ID = #{dbId}
		<if test= 'infmId != 0'>
			AND INFM_ID = #{infmId}
		</if>
	</delete>
	
</mapper>