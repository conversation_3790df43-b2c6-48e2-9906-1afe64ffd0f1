package com.aidt.api.al.wrt.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlWrtResDto {

	@Parameter(name="대단원노드ID(레슨ID)")
	private String lluKmmpNodId;
	
	@Parameter(name="대단원노드명(레슨명)")
	private String lluKmmpNodNm;
	
	@Parameter(name="토픽노드ID")
	private String tpcKmmpNodId;
	
	@Parameter(name="토픽노드명")
	private String tpcKmmpNodNm;
	
	@Parameter(name="진행상태")
	private String pgrsStCd;
	
	@Parameter(name="진행상태명")
	private String pgrsStNm;
	
	@Parameter(name="차시사용여부")
	private String tcUseYn;
	
	@Parameter(name="학생 수")
	private int claCnt;
	
	@Parameter(name="제출 수")
	private int smCnt;
	
	@Parameter(name="학생 Id")
	private String stuId;
	
	@Parameter(name="학생 번호")
	private String stuNo;
	
	@Parameter(name="학생 명")
	private String stuNm;
	
	@Parameter(name="학생 저장 일시")
	private String stuSavDtm;
	
	@Parameter(name="선생님 저장 일시")
	private String tcrSavDtm;
	
	@Parameter(name="이전노드 사용여부")
	private String prevUseYn;
	
	@Parameter(name="이전노드 대단원노드ID(레슨ID)")
	private String prevLluNodId;
	
	@Parameter(name="이전노드 토픽노드ID ")
	private String prevTpcNodId;
	
	@Parameter(name="다음노드 사용여부")
	private String nextUseYn;
	
	@Parameter(name="다음노드 대단원노드ID(레슨ID)")
	private String nextLluNodId;
	
	@Parameter(name="다음노드 토픽노드ID ")
	private String nextTpcNodId;
	
	@Parameter(name="학생정답내용")
	private String stuCansCn;
	
	@Parameter(name="교사첨부내용")
	private String tcrAnnxCn;
	
	@Parameter(name="교사첨부상세")
	private String tcrAnnxDtl;
	
	@Parameter(name="학생채점내용")
	private String stuMrkCn;
	
	@Parameter(name="구성점수")
	private String cstnScr;
	
	@Parameter(name="표현점수")
	private String exprScr;
	
	@Parameter(name="어휘점수")
	private String vocScr;
	
	@Parameter(name="문법점수")
	private String grmrScr;
	
	@Parameter(name="아키핀ID")
	private String acpId;
	
	@Parameter(name="AI첨삭내용")
	private String aiAnnxCn;
	
	@Parameter(name="학습시간초수")
	private String lrnTmScnt;
	
	@Parameter(name="AI통신실패여부")
	private String apFailYn;

	@Parameter(name="잠금여부")
	private String lcknYn;

	@Parameter(name="사용여부")
	private String useYn;
	
}
