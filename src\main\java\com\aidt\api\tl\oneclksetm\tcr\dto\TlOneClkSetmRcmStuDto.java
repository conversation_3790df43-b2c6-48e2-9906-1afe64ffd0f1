package com.aidt.api.tl.oneclksetm.tcr.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TlOneClkSetmRcmStuDto {
    /** 특별학습ID */
    @Parameter(name="특별학습ID")
    private String spLrnId;

    /** 학생 이름 */
    @Parameter(name="학생 이름")
    private String usrNm;
    
    /** 학생 번호 */
    @Parameter(name="학생 번호")
    private String usrNo;
    
    /** 학생 id */
    @Parameter(name="학생 id")
    private String usrId;

}
