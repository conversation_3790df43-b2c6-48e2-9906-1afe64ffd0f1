package com.aidt.api.tl.cmtxb.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 10:43:39
 * @modify date 2024-04-02 10:43:39
 * @desc [학습상세현황 dto]
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbLrnDtlPstDto {
    /** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학생ID */
    @Parameter(name="학생ID")
    private String usrId;

    /** 학생 이름*/
    @Parameter(name="학생 이름")
    private String usrNm;

    /** 학년 */
    @Parameter(name="학년")
    private int sgy;

    /** 반 */
    @Parameter(name="반")
    private int claNo;

    /** 학생 번호 */
    @Parameter(name="학생 번호")
    private int stuNo;

    /** 학습현황 리스트*/
    @Parameter(name="학습현황 리스트")
    private List<TlCmTxbLrnDtlLluDto> lrnPstList;
}
