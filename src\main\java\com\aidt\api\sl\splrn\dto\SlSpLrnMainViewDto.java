package com.aidt.api.sl.splrn.dto;


import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-01-10 10:45:26
 * @modify : date 2024-01-31 16:13:10
 * @desc : 특별학습 재구성
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnMainViewDto  {
	
	/* 재구성 */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    @Parameter(name="특별학습ID")
    private String spLrnId;
    
    @Parameter(name="특별학습노드ID")
    private String spLrnNodId;
    
    @Parameter(name="특별학습명")
    private String spLrnNm;

    @Parameter(name="특별학습목표")
    private String lrnGoalCn;

    // @Parameter(name="모바일 파일경로")
    // private String moPath;
    
    @Parameter(name="태블릿 파일경로")
    private String taPath;
    
    @Parameter(name="PC 파일경로")
    private String pcPath;
    
    @Parameter(name="전체 강의수")
    private int entire; // 전체 강의 수

    @Parameter(name="완료한 강의수")
    private int done;  // 완료한 강의 수
    
    @Parameter(name="전체 콘텐츠수")
    private int ctnEntire; // 전체 콘텐츠수
    
    @Parameter(name="완료한 콘텐츠수")
    private int ctnDone;  // 완료한 콘텐츠수
    
    @Parameter(name="노드별 학습 진행상태")
    private String lrnStCd;  // 완료한 강의 수
    
 
    
 
    
    
    
    

}
