package com.aidt.api.ea.evcom.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-02 08:35:오전 8:35
 * @modify date 2024-04-02 08:35:오전 8:35
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class InsertEaEvQtmAnwReqDto {
    @Parameter(name = "운영 교과서 ID")
    private String optTxbId;

	@Parameter(name="교과서ID")
	private String txbId;	
	
	@Parameter(name = "사용자 ID")
    private String usrId;
	
	@Parameter(name = "사용자 유형")
    private String usrTpCd;
	
    @Parameter(name = "DB ID")
    private String dbId;

    @Parameter(name = "평가 ID")
    @NotNull
    private Integer evId;

    @Parameter(name = "평가구분코드")
    private String evDvCd;
    
    @Parameter(name = "평가상세구분코드")
    private String evDtlDvCd;
    
    @Parameter(name="재응시 허용 여부")
    private String rtxmPmsnYn;
    
    @Parameter(name = "응시 시작 여부")
    @NotNull
    private String txmStrYn;

    @Parameter(name = "문항 목록")
    @NotNull
    private List<InsertEaEvQtmAnwReqDto> qtmList;

    @Parameter(name = "평가 완료 여부")
    @NotNull
    private String evCmplYn;

    @Parameter(name = "제출 답변 값")
    private String smtAnwVl;
    
    @Parameter(name = "문제 풀이 내용")
    private String qstXplCn;    

    @Parameter(name = "재응시 회차")
    private int rtxmPn;
    
    @Parameter(name = "응시 회차")
    private int txmPn;

    @Parameter(name = "정답 여부")
    private String cansYn;

    @Parameter(name = "정답 수")
    private int cansCnt;

    @Parameter(name = "문제 수")
    private int qstCnt;

    @Parameter(name = "풀이 시간 초 수")
    private Integer xplTmScnt;


    @Parameter(name = "평가 시간 초수")
    private Integer evTmScnt;

    @Parameter(name = "평가 결과 존재 여부")
    private String evRsYn;

    @Parameter(name = "재응시 평가 완료 여부")
    private String evRcmplYn;

    @Parameter(name = "문항 ID")
    private String qtmId;
    
    @Parameter(name = "외주문항여부")
    private String isExternal;
    
    @Parameter(name = "외주문항정답여부")
    private String isCorrect;
    
    @Parameter(name = "메모첨부파일ID")
    private long annxFleId;    
    
    @Parameter(name = "풀이상태코드")
    private String xplStCd;

    @Parameter(name = "힌트확인여부")
    private String hntCofmYn;
    
    @Parameter(name="심화 평가 생성 여부")
    private String ntnEvCrtYn;

    @Parameter(name="오답개수")
    private int iansCnt;    

    @Parameter(name="보충심화유형코드")
    private String sppNtnTpCd;  

    @Parameter(name="보충심화평가개수")
    private int sppNtnEvCnt; 
    
    @Parameter(name="보충심화 완료여부")
    private String sppNtnCmplYn;  
    
    @Parameter(name="보충심화 시작여부")
    private String sppNtnStrYn;      
    
	@Parameter(name="토픽ID_지식맵")
	private String tpcId; 
	
    
	@Parameter(name="학교급코드")
	private String schlGrdCd;   
    
	@Parameter(name="과목코드")
	private String sbjCd;   
	
	@Parameter(name="노트만 저장여부")
	private String noteOnlyYn="N";
	
	@Parameter(name="학습활동 저장 dto")
	private TlLrnwAtvSaveDto atvSaveDto;
	
	
}


