<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.ea.qtmcom">

    <select id="selectQpQtmAiHntList" parameterType = "com.aidt.api.ea.evcom.ev.dto.EaEvQtmIdReqDto"
            resultType="com.aidt.api.ea.qtmCom.dto.EaQtmAiHintDto">
            
		SELECT 
			   HNT.QP_QTM_ID
			 , HNT.QP_HNT_ID
			 , HNT.QP_HNT_TYP_CD
			 , (SELECT QP_UNIF_CD_NM FROM lms_cms.qp_wrk_cd WHERE QP_LCL_CD = 'HT' AND QP_UNIF_CD = HNT.QP_HNT_TYP_CD) AS QP_HNT_TYP_NM
			 , HNT.QP_HNT_CTN
			 , HNT.QP_HNT_ORDN
		FROM lms_cms.qp_qtm_ai_hnt HNT
		WHERE HNT.QP_QTM_ID = #{qtmId}


        /* 평가 문통 공통 - 박원희 - EaQtmCom-Mapper.xml - selectQpQtmAiHntList - 문항플랫폼 AI힌트 조회 */
        
    </select>
    
    
    <!--    교사/DIY 평가추가 > 난이도별 문항리스트 조회 > 단원 리스트로 지식맵 토픽ID 리스트 조회 -->	
    <select id="selectLuSearchTpcIdList" resultType="hashMap">

		SELECT
		       K_NOD5.KMMP_NOD_ID AS tpcId
		FROM (
			<foreach item="item" index ="index" collection="tsRngeList" separator="union">
				SELECT 
						L_NOD4.LRMP_NOD_ID AS NOD_ID_DPTH4
				FROM LMS_CMS.BC_LRMP_NOD L_NOD1
 				JOIN LMS_CMS.BC_LRMP_NOD L_NOD2 ON L_NOD2.URNK_LRMP_NOD_ID = L_NOD1.LRMP_NOD_ID
				JOIN LMS_CMS.BC_LRMP_NOD L_NOD3 ON L_NOD3.URNK_LRMP_NOD_ID = L_NOD2.LRMP_NOD_ID
				JOIN LMS_CMS.BC_LRMP_NOD L_NOD4 ON L_NOD4.URNK_LRMP_NOD_ID = L_NOD3.LRMP_NOD_ID
				WHERE 1 = 1
				<choose>
					<when test='item.luLrmpNodId != null and item.luLrmpNodId neq ""'>
				    AND L_NOD1.LRMP_NOD_ID = #{item.luLrmpNodId}
				    </when>
				    <otherwise>
				    AND L_NOD1.LRMP_NOD_ID = '0'
				    </otherwise>
				</choose> 
				AND EXISTS(SELECT 1 FROM LMS_CMS.BC_LRMP BL WHERE BL.LRMP_ID  = L_NOD1.LRMP_ID AND BL.TXB_ID = #{txbId} )
				AND L_NOD1.DPTH = 1
				AND IFNULL(L_NOD1.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
	            <if test='item.mluLrmpNodId != null and item.mluLrmpNodId neq ""'>
					AND L_NOD2.LRMP_NOD_ID = #{item.mluLrmpNodId}
	         	</if>				
				AND L_NOD2.DPTH = 2
				AND IFNULL(L_NOD2.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
	            <if test='item.sluLrmpNodId != null and item.sluLrmpNodId neq ""'>
					AND L_NOD3.LRMP_NOD_ID = #{item.sluLrmpNodId}
	         	</if>
				AND L_NOD3.DPTH = 3
				AND IFNULL(L_NOD3.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
				AND L_NOD4.DPTH = 4
				AND IFNULL(L_NOD4.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외			
		    </foreach>   
		) L_NOD4
		JOIN LMS_CMS.BC_LRMP_KMMP_NOD_MPN NOD_MPN ON NOD_MPN.LRMP_NOD_ID = L_NOD4.NOD_ID_DPTH4
		JOIN LMS_CMS.BC_KMMP_NOD K_NOD4 ON K_NOD4.KMMP_NOD_ID = NOD_MPN.KMMP_NOD_ID
		JOIN LMS_CMS.BC_KMMP_NOD K_NOD5 ON K_NOD5.URNK_KMMP_NOD_ID = K_NOD4.KMMP_NOD_ID
		WHERE IFNULL(K_NOD4.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외
		AND IFNULL(K_NOD5.TPC_DV_CD, 'IN') = 'IN'  -- 교과서외토픽 제외

        /* 평가 문통 공통 - 박원희 - EaQtmCom-Mapper.xml - selectLuSearchTpcIdList - 난이도별 문항리스트 조회 > 단원 리스트로 지식맵 토픽ID 리스트 조회  */
        
    </select>
    
    <!--교사/DIY교사 평가추가 > 난이도별 문항리스트 조회 > 토픽ID리스트에 포함된 난이도별 문항 리스트 조회 -->	
    <select id="selectDffdTpcQtmList" resultType="com.aidt.api.ea.evcom.ev.dto.EaEvDffdsQpQtmResDto">

		SELECT
			    TQ.TPC_ID 			AS tpcId
			  , TQ.QP_QTM_ID		AS qtmId
			  , TQ.QP_DFFD_CD		AS evDffdDvCd
			  , TQ.dffdRandRowNo
			  , COUNT(TQ.QP_QTM_ID) OVER(PARTITION BY TQ.QP_DFFD_CD) dffdQtmCnt -- 난이도별 문항 수
		FROM (
				SELECT
						MAX(TQ.TPC_ID) TPC_ID
				      , QQ.QP_QTM_ID
					  , QQ.QP_DFFD_CD QP_DFFD_CD
					  , ROW_NUMBER () OVER(PARTITION BY QQ.QP_DFFD_CD ORDER BY RAND()) AS dffdRandRowNo
				FROM (
						SELECT
								QTM_ID
							  , MAX(TPC_ID) TPC_ID
						FROM (
								SELECT	
										 K_ATV.KMMP_NOD_ID AS TPC_ID
								       , K_ATV.CTN_CD AS QTM_ID
								FROM LMS_CMS.BC_AI_LRN_ATV_CTN K_ATV
								WHERE K_ATV.CTN_TP_CD = 'QU' -- 문항
								AND K_ATV.DEL_YN = 'N'
								AND K_ATV.KMMP_NOD_ID IN 
					            <foreach item="item" index ="index" collection="tpcList" open="(" separator="," close=")">
					                #{item.tpcId}
					            </foreach> 
					        <if test='isDiy.equals("N")'>    
							UNION ALL
								SELECT
									     B_EQM.TPC_ID
								       , B_EQM.QTM_ID
								FROM LMS_CMS.BC_EVSH_QTM_MPN B_EQM
								WHERE EXISTS(SELECT 1 FROM LMS_CMS.BC_EVSH WHERE EVSH_ID = B_EQM.EVSH_ID AND EVSH_TP_CD = 'DI') -- 교사 전용 문항  
								AND B_EQM.TPC_ID IN
					            <foreach item="item" index ="index" collection="tpcList" open="(" separator="," close=")">
					                #{item.tpcId}
					            </foreach>
					         </if>
					 	) TQ
					 	GROUP BY QTM_ID
				) TQ
				JOIN LMS_CMS.QP_QTM QQ ON QQ.QP_QTM_ID = TQ.QTM_ID
				WHERE QQ.DEL_YN = 'N'
				AND EXISTS(SELECT 1 FROM LMS_CMS.QP_QTM_AN WHERE QP_QTM_ID = QQ.QP_QTM_ID)
				AND EXISTS(SELECT 1 FROM LMS_CMS.QP_QTM_CN WHERE QP_QTM_ID = QQ.QP_QTM_ID)
				AND EXISTS(SELECT 1 FROM LMS_CMS.QP_CANS_TS WHERE QP_QTM_ID = QQ.QP_QTM_ID)
				AND QQ.QP_DFFD_CD IN 
	            <foreach item="item" index ="index" collection="dffdDsbList" open="(" separator="," close=")">
	                #{item.evDffdDvCd}
	            </foreach>
	            GROUP BY QQ.QP_DFFD_CD, QQ.QP_QTM_ID
		) TQ 	
		WHERE 21 > TQ.dffdRandRowNo

        /* 평가 문통 공통 - 박원희 - EaQtmCom-Mapper.xml - selectDffdTpcQtmList - 난이도별 문항리스트 조회 > 토픽ID리스트에 포함된 난이도별 문항 리스트 조회  */
        
    </select>

		
		
</mapper>