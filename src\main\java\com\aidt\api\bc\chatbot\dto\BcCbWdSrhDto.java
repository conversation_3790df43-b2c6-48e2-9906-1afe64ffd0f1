package com.aidt.api.bc.chatbot.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCbWdSrhDto{	
	@Parameter(name="단어검색어")
	private List<String> wdNmList;

    @Parameter(name="단어검색아이디")
	private String dicWdId;

	@Parameter(name="교과서ID")
	private String txbId;
}
