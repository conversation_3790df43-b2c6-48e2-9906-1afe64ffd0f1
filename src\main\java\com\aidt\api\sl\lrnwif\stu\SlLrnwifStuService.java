package com.aidt.api.sl.lrnwif.stu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.BcS3FileCheckUtil;
import com.aidt.api.sl.common.SlCmUtil;
import com.aidt.api.sl.common.SlConstUtil;
import com.aidt.api.sl.lrnwif.dto.SlLrnwAtvDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwAtvMetaDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwGetRequestDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwLrnCtnDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwLrnTlDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwPrgsDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwSaveRequestDto;
import com.aidt.api.sl.lrnwif.dto.SlLrnwTocAtvDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnLastPrgsDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnPgrsDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm;
import com.aidt.common.CommonDao;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : 2024-03-05 11:45:12
 * @modify : 2024-03-05 11:45:12
 * @desc : 특별학습 - 학습창 연계
 * 
 */

@Slf4j
@Service
public class SlLrnwifStuService {
	private String MAPPER_NAMESPACE = "api.sl.lrnwif.stu.";

    String pointUrl = "/api/v1/lw/myhm/stu/point";

    @Value("${spring.profiles.active}")
	private String serverActive;
	@Value("${server.meta.textbook.systemCode}")
	private String DB_ID;

	@Autowired
	private CommonDao commonDao;

	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	
	@Autowired
	private BcS3FileCheckUtil bcS3FileCheckUtil;

	/**
	 * 특별학습 활동저장
	 * 
	 * @param SlSpLrnPgrsDto 진행상황DTO
	 * @param optTxbId       운용교과서아이디
	 * @param userId         유저아이디
	 * @return cnt 1반환시 성공
	 */
	@Transactional
	public int saveSpLrnAtv(SlLrnwSaveRequestDto slLrnwSaveRequestDto, String optTxbId, String userId, String txbId) {
		int cnt = 0;
		
		// 학습상태 조회
		SlSpLrnPgrsDto sta = selectSlSpLrnPgrsDto(slLrnwSaveRequestDto.getSpLrnId(),
				slLrnwSaveRequestDto.getSpLrnCtnId(), optTxbId, userId);

		if (sta == null) {
			cnt = commonDao.insert(MAPPER_NAMESPACE + "insertLrnCtnInfo",
					Map.of("param", slLrnwSaveRequestDto, "dbId", DB_ID, "optTxbId", optTxbId, "userId", userId));
		} else {
			cnt = commonDao.update(MAPPER_NAMESPACE + "updateLrnCtnInfo",
					Map.of("param", slLrnwSaveRequestDto, "optTxbId", optTxbId, "userId", userId));
		}

		/*
		// sta.stcd 가 CL일 때만 진행
		// insert 후 과제 select 후 행 확인 제출완료 여부 = 'N'/ asnID(과제ID) 가져와서 --> 과제 update
		if (sta != null && sta.getLrnStCd().equals("CL")) {

			// 1. 과제 select - 총 학습수, 완료된 학습수, 제출완료 여부 확인
			SlLrnwAsnDto eaAsn = commonDao.select(MAPPER_NAMESPACE + "selectEaAsnInfo",
					Map.of("param", slLrnwSaveRequestDto, "optTxbId", optTxbId, "userId", userId));


			// 완료 건수가 같을 때만 제출 여부 Y
			String smtCmplYn = "N";
			if (eaAsn != null) {
				// 2. 특별학습 진행상황 select - 상위노드별 하위콘텐츠 총 갯수 == 완료된 총 갯수 => 전체 완료건수 1
				SlLrnwPrgsDto spPrgs = commonDao.select(MAPPER_NAMESPACE + "selectLrnPgrsInfo",
						Map.of("param", slLrnwSaveRequestDto, "optTxbId", optTxbId, "userId", userId));
				if (eaAsn.getTtlLrnCnt() == spPrgs.getTotalDone()) {
					smtCmplYn = "Y";
					// 3. 과제 update
					commonDao.update(MAPPER_NAMESPACE + "updateEaAsnInfo", Map.of("cmplLrnCnt", spPrgs.getTotalDone(),
							"smtCmplYn", smtCmplYn, "asnId", eaAsn.getAsnId(), "optTxbId", optTxbId, "userId", userId));
				}
			}

		}
 		*/
		log.debug("#### cnt : {}", cnt);
		return cnt;
	}

	/**
	 * 특별학습 컨텐츠 조회- 학습창연계
	 * 
	 * @param spLrnId
	 * @param spLrnNodId
	 * @param spLrnCtnId
	 * @param optTxbId
	 * @param userId
	 * @return SlSpLrnLastPrgsList
	 */
	@Transactional(readOnly = true)
	public SlLrnwTocAtvDto selectLastSlSpLrnPgrs(SlLrnwGetRequestDto reqeuestDto, String optTxbId, String userId,
			String userTpCd) {
		String spLrnId = reqeuestDto.getSpLrnId();
		String urnkNodId = reqeuestDto.getSpLrnNodId(); // 상위노드ID
		
		if(spLrnId == null || spLrnId.isEmpty()) {
			return new SlLrnwTocAtvDto();
		}
		
		int datCnt = commonDao.select(MAPPER_NAMESPACE + "selectChkSlSpLrn",
				Map.of("spLrnId", spLrnId, "optTxbId", optTxbId));
		
		if(datCnt <= 0) {
			throw new IllegalArgumentException("학습정보가 없습니다.");
		}

		// 배너에서 시작하기 눌렀을 때 ( nodId가 null로 들어옴)
		if (urnkNodId.equals("")) {
			//상위노드 select
			urnkNodId = selectUrnkSpLrnNodId(spLrnId, optTxbId, userId);
		}

		// 특별학습 콘텐츠 활동 dto 생성
		SlLrnwTocAtvDto slLrnwTocAtvDto = new SlLrnwTocAtvDto();
		// 특별학습 id, 상위노드id setting
		slLrnwTocAtvDto.setLluNodId(spLrnId);
		slLrnwTocAtvDto.setTcNodId(urnkNodId);

		SlLrnwTocAtvDto atvdto = selectSlLrnwTocAtvDto(optTxbId, spLrnId, urnkNodId, userId);
		if (atvdto != null) {
			// 특별학습명 setting
			slLrnwTocAtvDto.setLluNodNm(atvdto.getLluNodNm());
			// 노드명 setting
			slLrnwTocAtvDto.setTcNodNm(atvdto.getTcNodNm());
			// 컨텐츠 총 카운트 setting
			slLrnwTocAtvDto.setAtvTotCnt(atvdto.getAtvTotCnt());
		}

		// 마지막 컨텐츠아이디 setting
		String lastCtnId = selectLastCtnId(optTxbId, spLrnId, urnkNodId, userId);
		slLrnwTocAtvDto.setStrAtvId(lastCtnId);
		
		//외부활동설정
        TlLrnwExtAtvSetm extAtv = commonDao.select(MAPPER_NAMESPACE + "selectExtAtvSetm", Map.of("optTxbId", optTxbId));
        extAtv.setExtrLink(commonDao.selectList(MAPPER_NAMESPACE + "selectExtLink", Map.of("optTxbId", optTxbId)));
        slLrnwTocAtvDto.setExtAtvSetm(extAtv);

		// 학습 도구 setting
		List<SlLrnwLrnTlDto> tlList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTlList",
				Map.of("optTxbId", optTxbId, "userTpCd", userTpCd));
		slLrnwTocAtvDto.setLrnTlList(tlList != null ? tlList: new ArrayList<SlLrnwLrnTlDto>());

		// 콘텐츠 메타 목록
		List<Map<String, Object>> mapAtvMetaList = commonDao.selectList(MAPPER_NAMESPACE + "selectAtvMetaList",
				Map.of("optTxbId", optTxbId, "spLrnId", spLrnId, "spLrnNodId", urnkNodId, "userId", userId, "userTpCd",
						userTpCd));
		List<SlLrnwAtvMetaDto> atvMetaList = new ArrayList<SlLrnwAtvMetaDto>();
		for(int ii = 0; ii < mapAtvMetaList.size(); ii++) {
			Map<String, Object> dtoMap = mapAtvMetaList.get(ii);
			SlLrnwAtvMetaDto dto = new SlLrnwAtvMetaDto();
			String lrnAtvId = dtoMap.get("LRN_ATV_ID") == null ? "" : String.valueOf(dtoMap.get("LRN_ATV_ID"));
			dto.setLrnAtvId(lrnAtvId);
			dto.setLrnAtvNm(dtoMap.get("LRN_ATV_NM") == null ? "" : dtoMap.get("LRN_ATV_NM").toString());
			dto.setCtnTpCd(dtoMap.get("CTN_TP_CD") == null ? "" : dtoMap.get("CTN_TP_CD").toString());
			dto.setRcstnOrdn(String.valueOf(dtoMap.get("RCSTN_ORDN")));
			dto.setLrnStCd(dtoMap.get("LRN_ST_CD") != null ? dtoMap.get("LRN_ST_CD").toString(): "");
			dto.setCrclCtnStdId(dtoMap.get("CRCL_CTN_STD_ID") != null ? dtoMap.get("CRCL_CTN_STD_ID").toString(): "");
			SlLrnwLrnCtnDto ctnMtd = new SlLrnwLrnCtnDto();
			ctnMtd.setCtnCd(dtoMap.get("CTN_CD").toString());
			ctnMtd.setMetaDataId(dtoMap.get("SP_LRN_CTN_ID").toString());
			if (dto.getCtnTpCd() != null && SlConstUtil.LRN_CTN_TP_CD_HT.equals(dto.getCtnTpCd())) {
				dto.setCtnUrl(SlCmUtil.makeCtnCdnUrl(BUCKET_NAME, dtoMap.get("CDN_PTH_NM").toString(), dtoMap.get("STR_FLE_NM").toString()));
			} else if (SlConstUtil.LRN_CTN_TP_CD_PL.equals(dto.getCtnTpCd())) {
				dto.setCtnUrl(SlCmUtil.makeCtnCdnUrl(BUCKET_NAME, dtoMap.get("CDN_PTH_NM").toString(), ""));
				// 2024.06.15 동영상이 고/중/저 화질등록이 필수였으나, 콘텐츠에 따라 일부 화질만 제공하는 것으로 변경됨
				// 동영상파일명이 공란이면 해당 화질미제공으로 설저한다.
				String v480p = dtoMap.get("VDO_FLE_NM_480P") == null ? "": dtoMap.get("VDO_FLE_NM_480P").toString(); /* 동영상파일명480P */
				String v720p = dtoMap.get("VDO_FLE_NM_720P") == null ? "": dtoMap.get("VDO_FLE_NM_720P").toString(); /* 동영상파일명720P */
				String v1280p = dtoMap.get("VDO_FLE_NM_1280P") == null ? "": dtoMap.get("VDO_FLE_NM_1280P").toString(); /* 동영상파일명1280P */
				ctnMtd.setVdsLRsln(0 == v480p.trim().length() ? "" : "media/01_video_480.mp4"); // 고정값설정
				ctnMtd.setVdsMRsln(0 == v720p.trim().length() ? "" : "media/01_video_720.mp4"); // 고정값설정
				ctnMtd.setVdsHRsln(0 == v1280p.trim().length() ? "" : "media/01_video_1080.mp4"); // 고정값설정
				ctnMtd.setSttlSmiFleNm("media/01_caption.smi"); // 고정값설정
				//ctnMtd.setSttlVttFleNm("media/01_caption.vtt"); // 고정값설정
				
				String path =dtoMap.get("CDN_PTH_NM").toString() + "media/01_caption.vtt";
				path = path.startsWith("/") ? path.substring(1) : path;
				ctnMtd.setSttlVttFleNm(bcS3FileCheckUtil.isFileExists(BUCKET_NAME, path)? "media/01_caption.vtt":""); // 고정값설정
				ctnMtd.setScrbFleNm("media/01_script.txt"); // 고정값설정
				ctnMtd.setVceFleNm("media/01_audio.mp3"); // 고정값설정
				ctnMtd.setThbFleNm("images/poster.png"); // 고정값설정
				dto.setCtnMtd(ctnMtd);
			}
			atvMetaList.add(dto);
		}

		List<SlLrnwAtvDto> atvList = new ArrayList<SlLrnwAtvDto>();
		// 학습활동 목록
		SlLrnwAtvDto atvDto = new SlLrnwAtvDto();
		atvDto.setLrnAtvList(atvMetaList);
		atvList.add(atvDto);
		slLrnwTocAtvDto.setLrnStpList(atvList);
		
		
		return slLrnwTocAtvDto;

	}
	/**
	 * 특별학습 상위노드 id
	 * 
	 * @param optTxbId 운영교과서Id
	 * @param spLrnId  특별학습Id
	 * @return urnkSpLrnNodId 상위노드ID
	 */
	@Transactional(readOnly = true)
	private String selectUrnkSpLrnNodId(String spLrnId, String optTxbId, String userId) {
		
		List<SlLrnwPrgsDto> dtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectSpLrnNodId",
				Map.of("spLrnId", spLrnId, "optTxbId", optTxbId, "userId", userId));
		
		String spLrnNodId = "";
		boolean foundSt = false; // 대상노드를 찾았는지 여부
		boolean foundLastYn = false; // 마지막학습여부 찾았는지 여부
		int rowno = 0;
		for (SlLrnwPrgsDto nod : dtoList) {
			rowno++;
			if ("Y".equals(nod.getLastLrnYn())) { // 학습시간이 마지막인 것중 
				foundLastYn = true;
				if ("NL".equals(nod.getLrnStCd()) || "DL".equals(nod.getLrnStCd())) { // 미학습, 학습중
					spLrnNodId = nod.getUrnkSpLrnNodId();
				    foundSt = true;
				    break;
				}
			}
			if (!foundSt && foundLastYn) { // 학습시간이 마지막 것이 완료된 경우.. 다음 학습데이터를 설정
				if (!"CL".equals(nod.getLrnStCd()) || rowno == dtoList.size()) {
					spLrnNodId = nod.getUrnkSpLrnNodId();
					foundSt = true;
					break;
				}
			} 
		}
		if (!foundSt && 0 < dtoList.size()) { // 학습예정 데이터를 찾지 못한경우는 첫번째꺼 설정한다.
			spLrnNodId = dtoList.get(0).getUrnkSpLrnNodId();
		}
		return spLrnNodId;
		
	}
	
	

	/**
	 * 특별학습 재구성 select
	 * 
	 * @param optTxbId 운영교과서Id
	 * @param spLrnId  특별학습Id
	 * @return slSpLrnRcstn 특별학습재구성- 특별학습Id, 특별학습명, 특별학습 목표
	 */
	@Transactional(readOnly = true)
	private SlLrnwTocAtvDto selectSlLrnwTocAtvDto(String optTxbId, String spLrnId, String urnkNodId, String userId) {
		SlLrnwTocAtvDto lrnw = commonDao.select(MAPPER_NAMESPACE + "selectSlSpLrnRcstnList",
				Map.of("optTxbId", optTxbId, "spLrnId", spLrnId, "spLrnNodId", urnkNodId, "userId", userId));
		return lrnw;
	}

	/**
	 * 특별학습 마지막시점 CtnId select
	 * 
	 * @param optTxbId
	 * @param spLrnId
	 * @param spLrnNodId
	 * @return lastCtnId 마지막시점 ctnId
	 */
	@Transactional(readOnly = true)
	private String selectLastCtnId(String optTxbId, String spLrnId, String urnkNodId, String userId) {
		String lastCtnId = null;
		// 진행상황리스트 조회 - optTxbId, spLrnId
		List<SlSpLrnLastPrgsDto> slSpLrnLastPrgsList = commonDao.selectList(MAPPER_NAMESPACE + "selectCtnId",
				Map.of("optTxbId", optTxbId, "spLrnId", spLrnId, "spLrnNodId", urnkNodId, "userId", userId));
		// // CL , DL/NL 상태인 경우를 찾았는지 여부를 나타내는 플래그
		// boolean foundCL = false;
		// boolean foundDL = false;
		// boolean foundNL = false;
		boolean founded = false;
		boolean lastLrnFounded = false;

		// !empty 일 때 LRN_ST_CD 확인 후 ctnId 설정
		if (!slSpLrnLastPrgsList.isEmpty()) {
			for (int ii = 0; ii < slSpLrnLastPrgsList.size(); ii++) {
				SlSpLrnLastPrgsDto dto = slSpLrnLastPrgsList.get(ii);
				// if (!foundDL && slSpLrnLastPrgsList.get(ii).getLrnStCd().equals("DL")) {
				// 	lastCtnId = slSpLrnLastPrgsList.get(ii).getSpLrnCtnId();
				// 	foundDL = true;
				// } else if (!foundCL && !foundDL && !foundNL && slSpLrnLastPrgsList.get(ii).getLrnStCd().equals("CL")) {
				// 	foundCL = true;
				// 	lastCtnId = slSpLrnLastPrgsList.get(ii).getSpLrnCtnId();
				// } else if (!foundDL && !foundNL && slSpLrnLastPrgsList.get(ii).getLrnStCd().equals("NL")) {
				// 	lastCtnId = slSpLrnLastPrgsList.get(ii).getSpLrnCtnId();
				// 	foundNL = true;
				// }
				// 마지막학습인경우
				if ("Y".equals(dto.getLastLrnYn())) {
					lastLrnFounded = true;
					if (!"CL".equals(dto.getLrnStCd())) { // 미학습, 학습중이면 설정
						lastCtnId = slSpLrnLastPrgsList.get(ii).getSpLrnCtnId();
						founded = true;
						break;
					}
				}
				// 마지막 학습데이터가 완료라 다음학습을 찾는다.
				if (!founded && lastLrnFounded) { 
					if (!"CL".equals(dto.getLrnStCd()) || slSpLrnLastPrgsList.size() == (ii + 1)) {
						lastCtnId = slSpLrnLastPrgsList.get(ii).getSpLrnCtnId();
						founded = true;
						break;
					}
				}
			}
			// 학습이력이 없는 경우 첫번째꺼 셋팅
			if (!lastLrnFounded) {
				lastCtnId = slSpLrnLastPrgsList.get(0).getSpLrnCtnId();
			}
		}
		return lastCtnId;

	}

	/**
	 * 특별학습 활동 조회 - 학습창 연계
	 * 
	 * @param spLrnId    특별학습 아이디
	 * @param spLrnCtnId 특별학습콘텐츠 아이디
	 * @param optTxbId   운용교과서 아이디
	 * @param userId     유저아이디
	 * @return SlSpLrnPgrsDto
	 */
	@Transactional(readOnly = true)
	private SlSpLrnPgrsDto selectSlSpLrnPgrsDto(String spLrnId, String spLrnCtnId, String optTxbId, String userId) {
		return commonDao.select(MAPPER_NAMESPACE + "selectSlSpLrnPgrsDto",
				Map.of("spLrnId", spLrnId, "spLrnCtnId", spLrnCtnId, "optTxbId", optTxbId, "userId", userId));
	}
	
	
	/**
	 * 특별학습 컨텐츠 조회- 학습창연계_결과물
	 * @param reqeuest
	 * @param optTxbId
	 * @return
	 */
	@Transactional(readOnly = true)
	public SlLrnwAtvMetaDto selectLastSlSpLrnPgrsResult(SlLrnwGetRequestDto reqeuest, String optTxbId) {

		String spLrnId = reqeuest.getSpLrnId();
		String spLrnNodId = reqeuest.getSpLrnNodId();
		String userId =  reqeuest.getUsrId();
		String lrnAtvId = reqeuest.getLrnAtvId();
		// 콘텐츠 메타 목록
		Map<String, Object> mapAtvMeta = commonDao.select(MAPPER_NAMESPACE + "selectAtvMetaList",
				Map.of("optTxbId", optTxbId, "spLrnId", spLrnId, "spLrnNodId", spLrnNodId, "userId", userId, "lrnAtvId",lrnAtvId
						));
		Map<String, Object> dtoMap = mapAtvMeta;
		SlLrnwAtvMetaDto dto = new SlLrnwAtvMetaDto();
		if(!mapAtvMeta.isEmpty()) {
			
			dto.setLrnAtvId(dtoMap.get("LRN_ATV_ID") == null ? "" : String.valueOf(dtoMap.get("LRN_ATV_ID")));
			dto.setLrnAtvNm(dtoMap.get("LRN_ATV_NM") == null ? "" : dtoMap.get("LRN_ATV_NM").toString());
			dto.setCtnTpCd(dtoMap.get("CTN_TP_CD") == null ? "" : dtoMap.get("CTN_TP_CD").toString());
			dto.setRcstnOrdn(String.valueOf(dtoMap.get("RCSTN_ORDN")));
			dto.setLrnStCd(dtoMap.get("LRN_ST_CD") != null ? dtoMap.get("LRN_ST_CD").toString(): "");
			dto.setCrclCtnStdId(dtoMap.get("CRCL_CTN_STD_ID") != null ? dtoMap.get("CRCL_CTN_STD_ID").toString(): "");
			SlLrnwLrnCtnDto ctnMtd = new SlLrnwLrnCtnDto();
			ctnMtd.setCtnCd(dtoMap.get("CTN_CD").toString());
			ctnMtd.setMetaDataId(dtoMap.get("SP_LRN_CTN_ID").toString());
			if (dto.getCtnTpCd() != null && SlConstUtil.LRN_CTN_TP_CD_HT.equals(dto.getCtnTpCd())) {
				dto.setCtnUrl(SlCmUtil.makeCtnCdnUrl(BUCKET_NAME, dtoMap.get("CDN_PTH_NM").toString(), dtoMap.get("STR_FLE_NM").toString()));
			} else if (SlConstUtil.LRN_CTN_TP_CD_PL.equals(dto.getCtnTpCd())) {
				dto.setCtnUrl(SlCmUtil.makeCtnCdnUrl(BUCKET_NAME, dtoMap.get("CDN_PTH_NM").toString(), ""));
				// 2024.06.15 동영상이 고/중/저 화질등록이 필수였으나, 콘텐츠에 따라 일부 화질만 제공하는 것으로 변경됨
				// 동영상파일명이 공란이면 해당 화질미제공으로 설저한다.
				String v480p = dtoMap.get("VDO_FLE_NM_480P") == null ? "": dtoMap.get("VDO_FLE_NM_480P").toString(); /* 동영상파일명480P */
				String v720p = dtoMap.get("VDO_FLE_NM_720P") == null ? "": dtoMap.get("VDO_FLE_NM_720P").toString(); /* 동영상파일명720P */
				String v1280p = dtoMap.get("VDO_FLE_NM_1280P") == null ? "": dtoMap.get("VDO_FLE_NM_1280P").toString(); /* 동영상파일명1280P */
				ctnMtd.setVdsLRsln(0 == v480p.trim().length() ? "" : "media/01_video_480.mp4"); // 고정값설정
				ctnMtd.setVdsMRsln(0 == v720p.trim().length() ? "" : "media/01_video_720.mp4"); // 고정값설정
				ctnMtd.setVdsHRsln(0 == v1280p.trim().length() ? "" : "media/01_video_1080.mp4"); // 고정값설정
				ctnMtd.setSttlSmiFleNm("media/01_caption.smi"); // 고정값설정
				String path =dtoMap.get("CDN_PTH_NM").toString() + "media/01_caption.vtt";
				path = path.startsWith("/") ? path.substring(1) : path;
				ctnMtd.setSttlVttFleNm(bcS3FileCheckUtil.isFileExists(BUCKET_NAME, path)? "media/01_caption.vtt":""); // 고정값설정
				
				ctnMtd.setScrbFleNm("media/01_script.txt"); // 고정값설정
				ctnMtd.setVceFleNm("media/01_audio.mp3"); // 고정값설정
				ctnMtd.setThbFleNm("images/poster.png"); // 고정값설정
				dto.setCtnMtd(ctnMtd);
			}
		}

		return dto;

	}
}
