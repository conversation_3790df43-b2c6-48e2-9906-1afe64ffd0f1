package com.aidt.api.at.token.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class FeaturesCheckResDto {

    @Parameter(name="상태 코드")
    private String statusCode;

    @Parameter(name="상태 메시지")
    private String message;

    @Parameter(name="리소스 버전")
    private String etag;

    @Parameter(name="파일 수정 시간")
    private String lastModified;

    @JsonIgnore
    @Parameter(name="파일 버전")
    private String version;

    private List<Map<String, Object>> features;
}
