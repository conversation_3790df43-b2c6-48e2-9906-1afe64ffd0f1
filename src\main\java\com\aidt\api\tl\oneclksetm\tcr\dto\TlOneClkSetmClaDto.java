package com.aidt.api.tl.oneclksetm.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-19 14:43:18
 * @modify date 2024-02-19 14:43:18
 * @desc [TlOneClkSetmCla Dto 원클릭학습설정 학급 조회 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlOneClkSetmClaDto {
    /** 다른 학급 운영교과서 ID */
    @Parameter(name="다른 학급 운영교과서ID")
    private String optTxbId;

    /** 현재 운영교과서 ID */
    @Parameter(name="현재 운영교과서ID")
    private String orgnOptTxbId;

    /** 수정자 ID */
    @Parameter(name="수정자 ID")
    private String mdfrId;

    /** 학급ID */
    @Parameter(name="학급ID")
    private String claId;

    /** 학년 */
    @Parameter(name="학년")
    private int sgy;

    /** 반명 */
    @Parameter(name="반명")
    private String claNm;
    
    /** 학년 반  */
    @Parameter(name="학년 반")
    private String classroomName;
    
    /** 재구성 데이터 여부  */
    @Parameter(name="재구성 데이터 여부")
    private String rcstnYn;
}
