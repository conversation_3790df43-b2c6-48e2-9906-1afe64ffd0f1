package com.aidt.api.sl.cm.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aidt.api.sl.cm.dto.SlCmSpLrnDto;
import com.aidt.api.sl.cm.dto.SlCmSrhDto;
import com.aidt.api.sl.common.SlCmUtil;
import com.aidt.common.CommonDao;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-08 09:37:48
 * @modify date 2024-05-08 09:37:48
 * @desc [공통 특별학습 조회 service]
 */
@Service
public class SlCmStuService {
    private String MAPPER_NAMESPACE = "api.sl.cm.stu.";

    @Autowired
    private CommonDao commonDao;
	
    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    /**
     * 특별학습 목록조회처리
     * @param slCmSrhDto
     * @return
     */
    public List<SlCmSpLrnDto> selectSlCmSpLrnList(SlCmSrhDto slCmSrhDto) {
        List<SlCmSpLrnDto> spLrnDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectSlCmSpLrnList",
                slCmSrhDto);
        for(int ii=0; ii < spLrnDtoList.size(); ii++)  {
            SlCmSpLrnDto dto = spLrnDtoList.get(ii);
            dto.setPcPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getPcPath()));
            dto.setTaPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getTaPath()));
            dto.setMoPath(SlCmUtil.makeFleCdnUrl(BUCKET_NAME, dto.getMoPath()));
        }

        return spLrnDtoList;
    }
}