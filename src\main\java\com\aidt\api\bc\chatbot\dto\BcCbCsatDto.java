package com.aidt.api.bc.chatbot.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCbCsatDto{	
	@Parameter(name="사용자아이디")
	private String lrnUsrId;

	@Parameter(name="만족도평가")
	private String csatEv;

	@Parameter(name="DB아이디")
	private String dbId;
}
