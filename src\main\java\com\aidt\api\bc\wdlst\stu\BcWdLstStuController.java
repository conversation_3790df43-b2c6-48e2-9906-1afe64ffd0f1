package com.aidt.api.bc.wdlst.stu;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.wdlst.dto.BcWdLstDto;
import com.aidt.api.bc.wdlst.dto.BcWdSrhHstDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:55:40
 * @modify 2024-01-05 17:55:40
 * @desc 단어장 Controller
 */

@Slf4j
@Tag(name="[bc] 나의단어장[LwWdLstStu]", description="단어장(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/wdLst")
public class BcWdLstStuController {

	@Autowired
	private JwtProvider jwtProvider;
	
    @Autowired
    private BcWdLstStuService bcWdLstStuService;

    /**
     * 단어장 단어목록 조회 요청
     *
     * @param BcWdLstDto
     * @return ResponseList<LwWdLstDto>
     */
    @Operation(summary="단어 목록 조회", description="단어장 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectWdLstList")
    public ResponseDto<List<BcWdLstDto>> selectWdLstList(BcWdLstDto bcWdLstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcWdLstDto.getUsrId())) {
    		bcWdLstDto.setUsrId(userDetails.getUsrId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getOptTxbId())) {
    		bcWdLstDto.setOptTxbId(userDetails.getOptTxbId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getTxbId())) {
    		bcWdLstDto.setTxbId(userDetails.getTxbId());
    	}
    	
        log.debug("Entrance selectWdLstList");
        return Response.ok(bcWdLstStuService.selectWdLstList(bcWdLstDto));
    }
    
    /**
     * 단어장 단어목록 조회 요청
     *
     * @param BcWdLstDto
     * @return ResponseList<LwWdLstDto>
     */
    @Operation(summary="단어장 팝업 목록 조회", description="팝업창 단어장 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectPopWdLstList")
    public ResponseDto<List<BcWdLstDto>> selectPopWdLstList(BcWdLstDto bcWdLstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcWdLstDto.getUsrId())) {
    		bcWdLstDto.setUsrId(userDetails.getUsrId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getOptTxbId())) {
    		bcWdLstDto.setOptTxbId(userDetails.getOptTxbId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getTxbId())) {
    		bcWdLstDto.setTxbId(userDetails.getTxbId());
    	}
    	
        log.debug("Entrance selectPopWdLstList");
        return Response.ok(bcWdLstStuService.selectPopWdLstList(bcWdLstDto));
    }
    
    /**
     * 영어단어 관련검색어 조회 요청
     *
     * @param BcWdLstDto
     * @return ResponseList<LwWdLstDto>
     */
    @Operation(summary="영어단어 관련검색어 조회", description="팝업창 영어단어 관련검색어를 조회한다.(학생)")
    @GetMapping(value = "/srchWdLst")
    public ResponseDto<List<BcWdLstDto>> srchWdLst(BcWdLstDto bcWdLstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcWdLstDto.getOptTxbId())) {
    		bcWdLstDto.setOptTxbId(userDetails.getOptTxbId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getTxbId())) {
    		bcWdLstDto.setTxbId(userDetails.getTxbId());
    	}
    	
    	log.debug("Entrance selectCmEnWdLst");
    	return Response.ok(bcWdLstStuService.srchWdLst(bcWdLstDto));
    }
    
    /**
     * 영어단어 조회 요청
     *
     * @param BcWdLstDto
     * @return ResponseList<LwWdLstDto>
     */
    @Operation(summary="영어단어 조회", description="팝업창 영어단어를 조회한다.(학생)")
    @GetMapping(value = "/selectCmEnWdLst")
    public ResponseDto<List<BcWdLstDto>> selectCmEnWdLst(BcWdLstDto bcWdLstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcWdLstDto.getUsrId())) {
    		bcWdLstDto.setUsrId(userDetails.getUsrId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getOptTxbId())) {
    		bcWdLstDto.setOptTxbId(userDetails.getOptTxbId());
    	}
    	if(StringUtils.isEmpty(bcWdLstDto.getTxbId())) {
    		bcWdLstDto.setTxbId(userDetails.getTxbId());
    	}
    	log.debug("Entrance selectCmEnWdLst");
    	return Response.ok(bcWdLstStuService.selectCmEnWdLst(bcWdLstDto));
    }

    /**
     * 단어장 단어등록 요청
     *
     * @param List<LwWdSrhHstDto>
     * @return Integer
     */
    @Operation(summary="단어장 단어등록", description="단어장 단어를 등록한다.(학생)")
    @PostMapping(value = "/insertWdLstList")
    public int insertWdLstList(@RequestBody BcWdLstDto bcWdLstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	bcWdLstDto.setUsrId(userDetails.getUsrId());
    	bcWdLstDto.setOptTxbId(userDetails.getOptTxbId());
    	bcWdLstDto.setDbId(userDetails.getTxbId());
    	
    	log.debug("Entrance insertWdLstList");
    	return bcWdLstStuService.insertWdLstList(bcWdLstDto);
    }
    
    /**
     * 단어장 단어 삭제 요청
     *
     * @param List<LwWdSrhHstDto>
     * @return Integer
     */
    @Operation(summary="단어 삭제", description="단어장 단어를 삭제한다.(학생)")
    @DeleteMapping(value = "/deleteWdLstList")
    public int deleteWdLstList(@RequestBody BcWdLstDto list) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	list.setDbId(userDetails.getTxbId());
        log.debug("Entrance deleteWdLstList");
        return bcWdLstStuService.deleteWdLstList(list);
    }
    
    /**
     * 단어 검색이력 목록 조회 요청
     *
     * @param BcWdSrhHstDto
     * @return ResponseList<LwWdSrhHstDto>
     */
    @Operation(summary="단어 검색이력 목록 조회", description="단어장 검색이력 목록을 조회한다.(학생)")
    @GetMapping(value = "/selectWdSrhHstList")
    public ResponseDto<List<BcWdSrhHstDto>> selectWdSrhHstList(BcWdSrhHstDto bcWdSrhHstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	if(StringUtils.isEmpty(bcWdSrhHstDto.getOptTxbId())) {
    		bcWdSrhHstDto.setOptTxbId(userDetails.getOptTxbId());
    	}
    	if(StringUtils.isEmpty(bcWdSrhHstDto.getUsrId())) {
    		bcWdSrhHstDto.setUsrId(userDetails.getUsrId());
    	}
    	
    	if(StringUtils.isEmpty(bcWdSrhHstDto.getTxbId())) {
    		bcWdSrhHstDto.setTxbId(userDetails.getTxbId());
    	}
    	
    	log.debug("Entrance selectWdSrhHstList");
    	return Response.ok(bcWdLstStuService.selectWdSrhHstList(bcWdSrhHstDto));
    }
    
    /**
     * 단어 검색이력 등록 요청
     *
     * @param BcWdSrhHstDto
     * @return ResponseList<LwWdSrhHstDto>
     */
    @Operation(summary="단어 검색이력 등록", description="단어장 검색이력을 등록한다.(학생)")
    @PostMapping(value = "/insertWdSrhHstList")
    public ResponseDto<Integer> insertWdSrhHstList(@RequestBody BcWdSrhHstDto bcWdSrhHstDto) {
    	
    	CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
    	
    	bcWdSrhHstDto.setOptTxbId(userDetails.getOptTxbId());
    	bcWdSrhHstDto.setUsrId(userDetails.getUsrId());
    	bcWdSrhHstDto.setDbId(userDetails.getTxbId());
    	
    	log.debug("Entrance selectWdSrhHstList");
    	return Response.ok(bcWdLstStuService.insertWdSrhHstList(bcWdSrhHstDto));
    }
}
