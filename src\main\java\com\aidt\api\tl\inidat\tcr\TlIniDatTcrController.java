package com.aidt.api.tl.inidat.tcr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.inidat.dto.TlIniDatCondDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-05 15:53:04
 * @modify date 2024-03-05 15:53:04
 * @desc TlIniDatTcr  교과학습 초기데이터작성 Service
 */
@Slf4j
@Tag(name="[tl] 교과학습초기데이터작성[TlSbcLrnTcr]", description="로그인한 교사가 담당하는 담당클래스를 조회하여, 교과학습재구성 초기데이터를 작성한다.")
@RestController
@RequestMapping("/api/v1/tl/tcr/inidat")
public class TlIniDatTcrController {
    
    @Autowired
    private JwtProvider jwtProvider;
	
    @Autowired
    private TlIniDatTcrService tlIniDatTcrService; //교사용 서비스

/**
     * 교과학습 초기데이터생성 처리
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교과학습 재구성", description="해당 교과서의 재구성초기데이터를 생성한다.")
    @PostMapping(value = "/registIniDat")
    public ResponseDto<Integer> registIniDat() {
        log.debug("Entrance registIniDat");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        TlIniDatCondDto srhDto = new TlIniDatCondDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setTcrUsrId(userDetails.getUsrId());

        return Response.ok(tlIniDatTcrService.registIniDat(srhDto));
    }

    /**
     * 교과학습 초기화(단건)
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교과학습 초기화(단건)", description="교과학습 초기화(단건)")
    @PostMapping(value = "/iniSbcLrnDat")
    public ResponseDto<Integer> iniSbcLrnDat() {
        log.debug("Entrance iniSbcLrnDat");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        TlIniDatCondDto srhDto = new TlIniDatCondDto();
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setTcrUsrId(userDetails.getUsrId());

        return Response.ok(tlIniDatTcrService.iniSbcLrnDat(srhDto));
    }
    
    /**
     * 교과학습 초기화(다건)
     * 
     * @return ResponseDto<Integer>
     */
    @Operation(summary="교과학습 초기화(다건)", description="교과학습 초기화(다건)")
    @PostMapping(value = "/iniSbcLrnDatTot", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> iniSbcLrnDatTot(@RequestBody int txbId, String txbCd) {
        log.debug("Entrance iniSbcLrnDatTot");

        return Response.ok(tlIniDatTcrService.iniSbcLrnDatTot(txbId, txbCd));
    }
 
}