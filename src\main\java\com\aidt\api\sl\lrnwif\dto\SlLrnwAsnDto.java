package com.aidt.api.sl.lrnwif.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-03-07 14:13:26
 * @modify : date 2024-03-07 14:13:26
 * @desc : SlLrnwAsnDto 과제 dto
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlLrnwAsnDto {
	@Parameter(name="과제ID")
	private int asnId;
	
	@Parameter(name="총학습수")
	private int ttlLrnCnt;
	
	@Parameter(name="완료학습수")
	private int cmplLrnCnt;
	
	@Parameter(name="과제제출완료여부")
	private String smtCmplYn;

}
