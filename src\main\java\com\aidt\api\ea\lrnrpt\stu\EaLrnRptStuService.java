package com.aidt.api.ea.lrnrpt.stu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.cm.en.AlEnService;
import com.aidt.api.al.pl.common.AlConstUtil;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlMluTcLstInqStuResponseDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlEaInfoDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlMluInfoDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlOvInfoDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlTcInfoDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlTpcInfoDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlRcmTsshQtmDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptSlSpLrnContentDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptSlSpLrnDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptSlSpLrnNodFDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptSlSpLrnNodSDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTalkDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTalkTpcDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlCmTxbTcDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvEvDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvEvQtmDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvListDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvStChkDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvStaDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvWbDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptWriteTpcDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 학습 리포트 - 학생 Service
 */
@Slf4j
@Service
public class EaLrnRptStuService {

	private final String MAPPER_NAMESPACE = "api.ea.lrnrpt.cm.";
	
	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private AlEnService alEnService;
	
	/**
	 * 학습리포트 > 학습현황 > Let's Talk
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return EaLrnRptTalkDtoList
	 */
	public List<EaLrnRptTalkDto> selectTalkList(EaLrnRptTalkDto eaLrnRptTalkDto) {
		String userId  = eaLrnRptTalkDto.getUserId();	//사용자Id
		String optTxbId = eaLrnRptTalkDto.getOptTxbId();	//운영교과서 Id
		
		List<EaLrnRptTalkDto> resultList = new ArrayList<EaLrnRptTalkDto>();
		//영어 회화의 대단원 조회
		List<EaLrnRptTalkTpcDto> eaLrnRptTalkDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTalkList",
				Map.of("userId", userId, "optTxbId", optTxbId));
		
		if(eaLrnRptTalkDtoList.size() > 0) {
			//String kmmpNodId = //차시 정보
			// 이전에 등록된 kmmpNodId를 추적하기 위한 Set 생성
		    //Set<String> kmmpNodIdCk = new HashSet<>();	//레슨id 체크
		    //Set<String> tpcNodIdCk = new HashSet<>();	//토픽id 체크
		    //EaLrnRptTalkDto reTalkDto = null;	//레슨 정보 등록 dto
		    
			for (EaLrnRptTalkTpcDto talkDto : eaLrnRptTalkDtoList) {
				EaLrnRptTalkDto reTalkDto = new EaLrnRptTalkDto();	//레슨 정보 등록 dto
	            String kmmpNodId = talkDto.getKmmpNodId();	//레슨 ID
	            //String kmmpNodNm = talkDto.getKmmpNodNm();	//레슨명
	            
	            // kmmpNodId가 이미 등록되어 있는지 확인
	            /*if (!kmmpNodIdCk.contains(kmmpNodId)) {	 // 이미 등록된 경우: talkDto.eaLrnRptTalkTpcDto의 리스트에 데이터 추가
	            	 
	            	if (reTalkDto != null) {
	                    resultList.add(reTalkDto);
	                }
	            	
	            	reTalkDto = new EaLrnRptTalkDto();
	                reTalkDto.setKmmpNodId(kmmpNodId);
	                reTalkDto.setKmmpNodNm(kmmpNodNm);
	                reTalkDto.setTcUseYn(talkDto.getTcUseYn() != null ? talkDto.getTcUseYn() : ""); // 차시 잠금여부
	                reTalkDto.setEaLrnRptTalkTpcDtoList(new ArrayList<>()); // 초기화
	                reTalkDto.setRcstnOrdn(talkDto.getRcstnOrdn() != null ? talkDto.getRcstnOrdn() : ""); // 순서
	                kmmpNodIdCk.add(kmmpNodId);
	                tpcNodIdCk.clear(); // 새로운 레슨이 시작되면 토픽 체크 초기화
	            }
	            */
	            EaLrnRptTalkTpcDto tpcDto = new EaLrnRptTalkTpcDto(); // 토픽의 상세값 저장
	            tpcDto.setTpcKmmpNodId(talkDto.getTpcKmmpNodId() != null ? talkDto.getTpcKmmpNodId() : ""); // 토픽 ID
	            tpcDto.setTpcKmmpNodNm(talkDto.getTpcKmmpNodNm() != null ? talkDto.getTpcKmmpNodNm() : ""); // 토픽명
	            tpcDto.setSrtOrdn(talkDto.getSrtOrdn() != null ? talkDto.getSrtOrdn() : ""); // 토픽 순서
	            
	            //tpcDto.setLrnStCd(talkDto.getLrnStCd() != null ? talkDto.getLrnStCd() : ""); // 토픽에 따른 학습상태
	            // 학습 상태 값 설정
	            String lrnStCd = talkDto.getLrnStCd();
	            if (lrnStCd == null || "NL".equals(lrnStCd)) {
	                tpcDto.setLrnStCd("학습전");
	            } else if ("DL".equals(lrnStCd)) {
	                tpcDto.setLrnStCd("학습중");
	            } else if ("CL".equals(lrnStCd)) {
	                tpcDto.setLrnStCd("학습완료");
	            } else {
	                tpcDto.setLrnStCd(lrnStCd);
	            }
	            
	            //tpcDto.setCrtDtm(talkDto.getCrtDtm() != null ? talkDto.getCrtDtm() : ""); // 생성일자
	            //tpcDto.setMdfDtm(talkDto.getMdfDtm() != null ? talkDto.getMdfDtm() : ""); // 수정일자
	            ///tpcDto.setLrnTime(talkDto.getLrnTime() != null ? talkDto.getLrnTime() : ""); // 학습시간
	            
	            // 학습시간이 "" 또는 NULL 일 경우 수정일자와 학습 시간을 "-"로 표시
	            String lrnTime = talkDto.getLrnTime();
	            tpcDto.setLrnTime(lrnTime != null && !lrnTime.isEmpty() ? lrnTime : "-");
	            tpcDto.setCrtDtm(talkDto.getCrtDtm() != null && !lrnTime.isEmpty() ? talkDto.getCrtDtm() : "-"); // 생성일자
	            tpcDto.setMdfDtm(talkDto.getMdfDtm() != null && !lrnTime.isEmpty() ? talkDto.getMdfDtm() : "-"); // 수정일자

	            
	            tpcDto.setNodRank(talkDto.getNodRank() != null ? talkDto.getNodRank() : ""); // 노드랭크
	            
	            tpcDto.setKmmpNodId(kmmpNodId);
	            
	            /*
	            if (!tpcNodIdCk.contains(tpcDto.getTpcKmmpNodId())) {
	                reTalkDto.getEaLrnRptTalkTpcDtoList().add(tpcDto);
	                tpcNodIdCk.add(tpcDto.getTpcKmmpNodId());
	            }
	            */
	            
	          //신규
	            reTalkDto.setKmmpNodId(talkDto.getKmmpNodId());
	            reTalkDto.setKmmpNodNm(talkDto.getKmmpNodNm());
	            reTalkDto.setTpcKmmpNodId(talkDto.getTpcKmmpNodId() != null ? talkDto.getTpcKmmpNodId() : "");
	            reTalkDto.setTpcKmmpNodNm(talkDto.getTpcKmmpNodNm() != null ? talkDto.getTpcKmmpNodNm() : "");
	            reTalkDto.setLrnStCd(tpcDto.getLrnStCd());
	            reTalkDto.setTcUseYn(talkDto.getTcUseYn() != null ? talkDto.getTcUseYn() : "");
				reTalkDto.setLcknYn(talkDto.getLcknYn() != null ? talkDto.getLcknYn() : "");
				reTalkDto.setUseYn(talkDto.getUseYn() != null ? talkDto.getUseYn() : "");
	            reTalkDto.setMdfDtm(talkDto.getMdfDtm() != null ? talkDto.getMdfDtm() : "");
	            reTalkDto.setLrnTime(talkDto.getLrnTime() != null ? talkDto.getLrnTime() : "");
	            
	            if (reTalkDto != null) {
		            resultList.add(reTalkDto);
		        }
	        }
	            
//			if (reTalkDto != null) {
//	            resultList.add(reTalkDto);
//	        }
		}
		return resultList;
	}
	
	/**
	 * 학습리포트 > 학습현황 > Let's Write
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return EaLrnRptWriteTpcDtoList
	 */
	public List<EaLrnRptWriteTpcDto> selectWriteList(EaLrnRptWriteTpcDto eaLrnRptWritePtcDto) {
		String userId  = eaLrnRptWritePtcDto.getUserId();	//사용자Id
		String optTxbId = eaLrnRptWritePtcDto.getOptTxbId();	//운영교과서 Id
		
		List<EaLrnRptWriteTpcDto> resultList = new ArrayList<EaLrnRptWriteTpcDto>();
		//영어 첨삭
		resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectWriteList",
				Map.of("userId", userId, "optTxbId", optTxbId));
		
		return resultList;
	}

	/**
	 * 학습리포트 > 학습현황 > 선생님 추천학습
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return EaLrnRptSlSpLrnDtoList
	 */
	public List<EaLrnRptSlSpLrnDto> selectSlSpLrnRptList(EaLrnRptSlSpLrnDto eaLrnRptTalkDto) {
		String userId  = eaLrnRptTalkDto.getUserId();	//사용자Id
		String optTxbId = eaLrnRptTalkDto.getOptTxbId();	//운영교과서 Id
		String usrTpCd = eaLrnRptTalkDto.getUsrTpCd();	//사용자 분류
		// 결과 리스트를 초기화
	    List<EaLrnRptSlSpLrnDto> resultList = new ArrayList<>();
	    
		//선생님 추천학습 데이터 조회
		List<EaLrnRptSlSpLrnContentDto> eaLrnRptSlPlLrnList = commonDao.selectList(MAPPER_NAMESPACE + "selectSlSpLrnRptList",
				Map.of("userId", userId, "optTxbId", optTxbId, "usrTpCd", usrTpCd));
		
		for (EaLrnRptSlSpLrnContentDto content : eaLrnRptSlPlLrnList) {
	        String spLrnId = content.getSpLrnId();
	        String spLrnNm = content.getSpLrnNm();

	        // spLrnId에 해당하는 EaLrnRptSlSpLrnDto 찾기
	        EaLrnRptSlSpLrnDto spLrnDto = resultList.stream()
	            .filter(dto -> dto.getSpLrnId().equals(spLrnId))
	            .findFirst()
	            .orElse(null);

	        if (spLrnDto == null) {
	            spLrnDto = new EaLrnRptSlSpLrnDto();
	            spLrnDto.setSpLrnId(spLrnId);
	            spLrnDto.setSpLrnNm(spLrnNm);
	            spLrnDto.setEaLrnRptSlSpLrnNodFList(new ArrayList<>()); // 리스트 초기화
	            resultList.add(spLrnDto);
	        }

	        // fDpthSpLrnNodId에 해당하는 EaLrnRptSlSpLrnNodDto 찾기
	        String fDpthSpLrnNodId = content.getFDpthSpLrnNodId();
	        EaLrnRptSlSpLrnNodFDto fDpthSpLrnNodDto = spLrnDto.getEaLrnRptSlSpLrnNodFList().stream()
	            .filter(nodDto -> nodDto.getFDpthSpLrnNodId().equals(fDpthSpLrnNodId))
	            .findFirst()
	            .orElse(null);

	        if (fDpthSpLrnNodDto == null) {
	            fDpthSpLrnNodDto = new EaLrnRptSlSpLrnNodFDto();
	            fDpthSpLrnNodDto.setFDpthSpLrnNodId(fDpthSpLrnNodId);
	            fDpthSpLrnNodDto.setFDpthSpLrnNodNm(content.getFDpthSpLrnNodNm()); // 상위 노드명 설정
	            fDpthSpLrnNodDto.setLDpth(content.getLDpth());
	            fDpthSpLrnNodDto.setEaLrnRptSlSpLrnNodSList(new ArrayList<>());  		//2dpth 초기화
	           // fDpthSpLrnNodDto.setEaLrnRptSlSpLrnContentList(new ArrayList<>()); // 리스트 초기화
	            spLrnDto.getEaLrnRptSlSpLrnNodFList().add(fDpthSpLrnNodDto);
	        }
	        
	        // sDpthSpLrnNodId에 해당하는 EaLrnRptSlSpLrnNodSDto 찾기
	        String sDpthSpLrnNodId = content.getSDpthSpLrnNodId();
	        EaLrnRptSlSpLrnNodSDto sDpthSpLrnNodDto = fDpthSpLrnNodDto.getEaLrnRptSlSpLrnNodSList().stream()
	                .filter(nodDto -> nodDto.getSDpthSpLrnNodId().equals(sDpthSpLrnNodId))
	                .findFirst()
	                .orElse(null);

	        if (sDpthSpLrnNodDto == null) {
	            sDpthSpLrnNodDto = new EaLrnRptSlSpLrnNodSDto();
	            sDpthSpLrnNodDto.setSDpthSpLrnNodId(sDpthSpLrnNodId);
	            sDpthSpLrnNodDto.setSDpthSpLrnNodNm(content.getSDpthSpLrnNodNm()); // 2DPTH 노드명 설정
	            sDpthSpLrnNodDto.setEaLrnRptSlSpLrnContentList(new ArrayList<>()); // 리스트 초기화
	            fDpthSpLrnNodDto.getEaLrnRptSlSpLrnNodSList().add(sDpthSpLrnNodDto);
	        }
	        

	        // cSpLrnNodId에 해당하는 EaLrnRptSlSpLrnContentDto 찾기
	        String cSpLrnNodId = content.getCSpLrnNodId();
	        EaLrnRptSlSpLrnContentDto existingContent = sDpthSpLrnNodDto.getEaLrnRptSlSpLrnContentList().stream()
	            .filter(cDto -> cDto.getCSpLrnNodId().equals(cSpLrnNodId))
	            .findFirst()
	            .orElse(null);

	        if (existingContent == null) {
	        	sDpthSpLrnNodDto.getEaLrnRptSlSpLrnContentList().add(content);
	        }
	    }

	    return resultList;
	}
	
	/**
     * 학습리포트 > 학습현황 > 우리반 수업 > 최근 학습한 학습활동ID 취득
     * 
     * @param srhDto
     * @return
     */
	public List<EaLrnRptTlSbcLrnAtvStChkDto> selectTlRptAtvStChk(EaLrnRptTlSrhDto eaLrnRptAlPlEaEDto) {
		List<EaLrnRptTlSbcLrnAtvStChkDto> resultList = new ArrayList<>();
        List<EaLrnRptTlSbcLrnAtvStChkDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectTlRptAtvStChk", eaLrnRptAlPlEaEDto);
        String lastLrmpNodId = commonDao.select(MAPPER_NAMESPACE + "selectLastLrmpNodIdRpt", eaLrnRptAlPlEaEDto);
        boolean searchNext = false;
        boolean isNotFound = true;

        for (EaLrnRptTlSbcLrnAtvStChkDto item : list) {
            if (lastLrmpNodId == null || "".equals(lastLrmpNodId)) {
                break;
            }
            if (!searchNext) {
                if (lastLrmpNodId.equals(item.getLrmpNodId())) { // 마직막 학습한 노드이면
                    if (item.getCntTot().equals(item.getCntCl())) { // 총건수와 완료건수가 일치하는 경우는 다음 학습노드를 찾는다.
                        searchNext = true;
                    } else {
                    	resultList.add(item);
                        isNotFound = false;
                        break;
                    }
                }
            } else {
                if (!item.getCntTot().equals(item.getCntCl())) { // 완료되지 않은 학습노드를 찾는다.
                	resultList.add(item);
                    isNotFound = false;
                    break;
                }
            }
        }
        // 최근학습데이터를 못찾은 경우는 처음것을 보여준다.
        if (isNotFound) {
            if (0 < list.size()) {
            	resultList.add(list.get(0));
            }
        }
        return resultList;
	}
	
	/**
	 * 학습리포트 > 학습현황 > 우리반 수업
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return selectTlRptList
	 */
	public List<EaLrnRptTlDto> selectTlRptList(EaLrnRptTlSrhDto eaLrnRptTlDto) {
		//String userId  = eaLrnRptTlDto.getUserId();	//사용자Id
		//String optTxbId = eaLrnRptTlDto.getOptTxbId();	//운영교과서 Id
		//String sbjCd = eaLrnRptTlDto.getSbjCd();	//과목 종류
		
		List<EaLrnRptTlDto> getTxbLluList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaLrnRptTxbTcList", eaLrnRptTlDto);
        List<EaLrnRptTlDto> resultList = getTxbLluList.stream()
                                            .filter(idx -> idx.getDpth() == 1)
                                            .collect(Collectors.toList());
        
        resultList.forEach(list1 -> { //대단원
            List<EaLrnRptTlCmTxbTcDto> tcList = new ArrayList<>(); // child에 넣을 list

            getTxbLluList.stream().filter(idx1 -> idx1.getDpth() == 2 && idx1.getUrnkLrmpNodId().equals(list1.getLrmpNodId()))
                                    .forEach(list2 -> { //중단원
                                        getTxbLluList.stream().filter(idx2 -> idx2.getDpth() == 3 && idx2.getUrnkLrmpNodId().equals(list2.getLrmpNodId()))
                                                                .forEach(list3 -> { //소단원
                                                                    getTxbLluList.stream().filter(idx3 -> idx3.getDpth() == 4 && idx3.getUrnkLrmpNodId().equals(list3.getLrmpNodId()))
                                                                                            .forEach(list4 -> { //차시
                                                                                            	EaLrnRptTlCmTxbTcDto tlCmTxbTcDto = new EaLrnRptTlCmTxbTcDto();
                                                                                                tlCmTxbTcDto.setLrmpNodId2(list2.getLrmpNodId()); //중단원 ID
                                                                                                tlCmTxbTcDto.setLrmpNodNm2(list2.getLrmpNodNm()); //중단원명
                                                                                                tlCmTxbTcDto.setLrmpNodId3(list3.getLrmpNodId()); //소단원 ID
                                                                                                tlCmTxbTcDto.setLrmpNodNm3(list3.getLrmpNodNm()); //소단원명
                                                                                                tlCmTxbTcDto.setLrmpNodId4(list4.getLrmpNodId()); //차시 ID
                                                                                                tlCmTxbTcDto.setLrmpNodNm4(list4.getLrmpNodNm()); //차시명
                                                                                                tlCmTxbTcDto.setRcstnOrdn(list4.getRcstnOrdn()); //재구성 순서
                                                                                                tlCmTxbTcDto.setLcknYn(list4.getLcknYn()); //잠금 여부
                                                                                                tlCmTxbTcDto.setUseYn(list4.getUseYn()); //사용 여부
                                                                                                tlCmTxbTcDto.setLuNoUseYn(list4.getLuNoUseYn()); //단원번호 사용여부
                                                                                                tcList.add(tlCmTxbTcDto);
                                                                                            });
                                                                });
                                    });
            list1.setChild(tcList);
        });
        
		return resultList;
	}
	
	/**
	 * 학습리포트 > 학습현황 > 우리반 수업 > 차시에 따른 학습현황 정보 조회
	 * 
	 * @param optTxbId
	 * @param userId
	 * @param lrmpNodId
	 * @return selectLrnTocStatList
	 */
	public List<EaLrnRptTlSbcLrnAtvListDto> selectRptLrnTocStatList(List<EaLrnRptTlSrhDto> eaLrnRptTlSrhDto) {
		List<EaLrnRptTlSbcLrnAtvListDto> returnList = new ArrayList<>();
		for(int i=0; i < eaLrnRptTlSrhDto.size(); i++) {
			String usrTpCd = eaLrnRptTlSrhDto.get(i).getUsrTpCd();	//사용자 분류 (교사 TL, 학생 ST)
			log.info("eaLrnRptTlSrhDto.size() : " +eaLrnRptTlSrhDto.size());
			String lrmpTcNodId = eaLrnRptTlSrhDto.get(i).getLrmpNodId();	//차시id
			String lrmpTcNodNm = eaLrnRptTlSrhDto.get(i).getLrmpNodNm();	//차시명
			String sbjCd = eaLrnRptTlSrhDto.get(i).getSbjCd();	//과목
			String schlGrdCd = eaLrnRptTlSrhDto.get(i).getSchlGrdCd();	//학년등급 분류
			EaLrnRptTlSbcLrnAtvListDto dto = new EaLrnRptTlSbcLrnAtvListDto();
			
			//교과서 공부 학습현황 조회
			List<EaLrnRptTlSbcLrnAtvStaDto> tcInfoData = commonDao.selectList(MAPPER_NAMESPACE + "selectRptLrnTocStatList", eaLrnRptTlSrhDto.get(i));
			int totalLearningCount = 0;
			int lrnStCdCount = 0; //학습완료
			double learningProgressRate = 0;
			int lrnTmScnt = 0;	//학습시간
			if(tcInfoData.size() > 0) {
	            //교과서 공부에 따른 진척율, 학습시간 조회
	            for(int n=0; n < tcInfoData.size(); n++) {
	            	if(tcInfoData.get(n).getLrnStCd().equals("CL")) {
	            		lrnStCdCount = lrnStCdCount+1;
	            	}
	            	lrnTmScnt = lrnTmScnt + tcInfoData.get(n).getLrnTmScnt();
	            }
	            //교과공부 진척율 계산
	            totalLearningCount = tcInfoData.size() > 0 ? tcInfoData.size() : 0 ; // 교과공부 학습 총 개수
	            learningProgressRate = lrnStCdCount > 0 ? (double) lrnStCdCount / totalLearningCount * 100 : 0;
			}
			dto.setLrmpTcNodId(lrmpTcNodId);
            dto.setLrmpTcNodNm(lrmpTcNodNm);
            dto.setLearningTotCount(totalLearningCount);		//교과공부 학습 총 개수
            dto.setLearningCmplCount(lrnStCdCount);			//교과공부 학습 완료 개수
            dto.setLearningProgressRate(learningProgressRate); // 교과공부 진척율 (100분율)
            dto.setLearningTmSum(lrnTmScnt); 	//교과공부 총 학습시간
            dto.setEaLrnRptTlSbcLrnAtvStaDto(tcInfoData);		//교과학습 관련 데이터 전체 저장
			
			//수학 익힘 데이터(초등 수학만 존재)
			// 익힘문제정보취득 EaLrnRptTlSbcLrnAtvWbDto
            if(sbjCd.equals("MA") || sbjCd.equals("CM1")  ||  sbjCd.equals("CM2")) {
				EaLrnRptTlSbcLrnAtvWbDto wkDto = new EaLrnRptTlSbcLrnAtvWbDto();
				Map<String, String> wkMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvWkbSumRpt", eaLrnRptTlSrhDto.get(i));
				String showYn = "N";
		        if (wkMap != null) {
		        	wkDto.setLrnAtvId(wkMap.get("LRN_ATV_ID") != null ? String.valueOf(wkMap.get("LRN_ATV_ID")) : "");
		        	wkDto.setWkbFinCnt(wkMap.get("WKB_FIN_CNT") != null ? String.valueOf(wkMap.get("WKB_FIN_CNT")) : "0");
		        	wkDto.setWkbTotCnt(wkMap.get("WKB_TOT_CNT") != null ? String.valueOf(wkMap.get("WKB_TOT_CNT")) : "0");
		        	wkDto.setLrnTmScnt(wkMap.get("LRN_TM_SCNT") != null ? String.valueOf(wkMap.get("LRN_TM_SCNT")) : wkMap.get("LRN_TM_SCNT"));
		            String wkbStCd = "학습전";
		            if(wkDto.getWkbFinCnt() != null )
		            {
		            	if (!"0".equals(wkDto.getWkbFinCnt()) && !wkDto.getWkbFinCnt().equals(wkDto.getWkbTotCnt())) {
			                wkbStCd = "학습중";
			            } else if (!"0".equals(wkDto.getWkbFinCnt()) && wkDto.getWkbFinCnt().equals(wkDto.getWkbTotCnt())) {
			                wkbStCd = "학습완료";
			            }
		            }
		            wkDto.setWkbLrnStCd(wkbStCd);
		            showYn = "0".equals(wkDto.getWkbTotCnt() != null ? wkDto.getWkbTotCnt() : "0") ? "N" : "Y";
		            wkDto.setShowYn(showYn);
		            
		            int totalMathCount = Integer.parseInt(wkMap.get("WKB_TOT_CNT") != null ? String.valueOf(wkMap.get("WKB_TOT_CNT")) : "0");
		            int wkFinCount = Integer.parseInt(wkMap.get("WKB_FIN_CNT") != null ? String.valueOf(wkMap.get("WKB_FIN_CNT")) : "0");
		            double mathCompletionRate = totalMathCount > 0 ? (double) wkFinCount / totalMathCount * 100 : 0;
		           
		            dto.setEaLrnRptTlSbcLrnAtvMathDtoList(wkDto);
		            dto.setMathTotCount(String.valueOf(wkMap.get("WKB_TOT_CNT")));	//수학익힘 총 문제수
		            dto.setMathCmplCount(String.valueOf(wkMap.get("WKB_FIN_CNT") != null ? wkMap.get("WKB_FIN_CNT") : 0));	//수학익힘 총 완료 건수
		            dto.setMathTmSum(String.valueOf(wkMap.get("LRN_TM_SCNT"))); 	//수학익힘 총 학습 시간
		            dto.setMathCompletionRate(mathCompletionRate); 					//수학익힘 진척율
		        }else {
		        	dto.setEaLrnRptTlSbcLrnAtvMathDtoList(wkDto);
		            dto.setMathTotCount("0");	//수학익힘 총 문제수
		            dto.setMathCmplCount("0");	//수학익힘 총 완료 건수
		            dto.setMathTmSum("0"); 	//수학익힘 총 학습 시간
		            dto.setMathCompletionRate(0); 					//수학익힘 진척율
		        }
            }
	        
	        log.info("usrTpCd : " + usrTpCd);
	        //if(usrTpCd.equals("ST")) {
		        //형성평가 데이터(학생)
	        	EaLrnRptTlSbcLrnAtvEvDto rtnDto = commonDao.select(MAPPER_NAMESPACE + "selectLrnEvInfoRpt", eaLrnRptTlSrhDto.get(i)); // 평가정보취득
	        	int cansCnt = 0;	//맞힌 정답수
	        	String fnlQstCnt = "0";	//총 평가수
	        	String evIdCk = "null";
	        	String evCmplYn = "N";
	        	String evActStCd = null;
	        	String evActLrnScnt = "0";
	        	int xplTmScnt = 0;	//문항별 풀이시간
	            if (rtnDto != null && evIdCk != null) {
	                // 평가문항리스트조회
	            	evIdCk = rtnDto.getEvId() != null ? rtnDto.getEvId() : null;
	            	evCmplYn = rtnDto.getEvCmplYn() != null ? rtnDto.getEvCmplYn() : "N";
	            	fnlQstCnt = rtnDto.getFnlQstCnt() != null ? rtnDto.getFnlQstCnt() : "0";
	            	evActStCd = rtnDto.getLrnStCd() != null ? rtnDto.getLrnStCd() : "";
	            	evActLrnScnt = rtnDto.getLrnTmScnt() != null ? rtnDto.getLrnTmScnt() : "0";
	                List<EaLrnRptTlSbcLrnAtvEvQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnEvQtmRptList", Map
	                        .of("optTxbId",  eaLrnRptTlSrhDto.get(i).getOptTxbId(), "userId", eaLrnRptTlSrhDto.get(i).getUserId(), "evId", rtnDto.getEvId()));
	                rtnDto.setEvQtmList(list);
	                if(list.size() > 0) {
	                	//문항에서 정답 맞힌것 개수 추적
	                	for(int l=0; l < list.size(); l++) {
	                		if(list.get(l).getCansYn().equals("Y")) {
	                			cansCnt = cansCnt + 1;
	                		}
	                		String xplTmScnt2 = list.get(l).getXplTmScnt() != null ? list.get(l).getXplTmScnt() : "0";
	                		xplTmScnt = xplTmScnt + Integer.parseInt(xplTmScnt2);
	                	}
	                }
	            }
	            dto.setEaLrnRptTlSbcLrnAtvEvDtoList(rtnDto);   //eaLrnRptTlSbcLrnAtvEvDtoList
	            dto.setEaTotalCount(fnlQstCnt);		//평가 총 개수
	            dto.setEaCansCount(cansCnt);		//평가 정답 수
	            dto.setEaCmplYn(evCmplYn);			//평가완료여부
	            dto.setEvIdCk(evIdCk);				//평가 존재여부
	            dto.setEvActLrnScnt(evActLrnScnt);		//학습현황의 형성평가 시간
	            dto.setEaTmSum(String.valueOf(xplTmScnt));			//평가 문항별 풀이시간의 합
	            
	       // }else {
	        	//형성평가 데이터(교사)
	        	/**
	            EaLrnRptTlSbcLrnAtvEvTcrDto rtnDto = new EaLrnRptTlSbcLrnAtvEvTcrDto();
	        	Map<String, String> evMap = commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvEvSumRpt", eaLrnRptTlSrhDto.get(i));
	        	String showYn = "N";
	        	
	        	int cansCnt = 0;	//맞힌 정답수
	        	String TolQstCnt = "0";	//총 평가수
	        	String evIdCk = "null";
	        	String evCmplYn = "N";
	        	String xplTmScnt = "0";	//문항별 풀이시간
	        	if (evMap != null) {
	                // 평가문항리스트조회
	            	String evId = evMap.get("EV_ID") != null ? evMap.get("EV_ID") : null;
	            	log.info("==============================");
	            	evIdCk = evId;
	            	if(evId != null) {
	            		List<EaLrnRptTlSbcLrnAtvEvQtmDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnEvQtmRptList", Map
		                        .of("optTxbId",  eaLrnRptTlSrhDto.get(i).getOptTxbId(), "userId", eaLrnRptTlSrhDto.get(i).getUserId(), "evId", evId));
		                rtnDto.setEvQtmList(list);
		                if(list.size() > 0) {
		                	//문항에서 정답 맞힌것 개수 추적
		                	for(int l=0; l < list.size(); l++) {
		                		if(list.get(l).getCansYn().equals("Y")) {
		                			cansCnt = cansCnt + 1;
		                		}
		                	}
		                }
	            	}
	                
	                rtnDto.setNodNo(String.valueOf(evMap.get("NOD_NO")));
	                rtnDto.setLrmpNodNm1(evMap.get("LRMP_NOD_NM1"));
	                rtnDto.setEvLrnAtvId(evMap.get("LRN_ATV_ID"));
	                rtnDto.setEvLrnAtvNm(evMap.get("LRN_ATV_NM"));
	                rtnDto.setEvLrnStpNm(evMap.get("LRN_STP_NM"));
	                rtnDto.setEvTotCnt(String.valueOf(evMap.get("EV_TOT_CNT")));
	                rtnDto.setEvFinCnt(String.valueOf(evMap.get("EV_FIN_CNT")));
	                rtnDto.setEvLrnStCd(evMap.get("LRN_ST_CD"));
	                rtnDto.setEvId(String.valueOf(evMap.get("EV_ID")));
	                rtnDto.setEvXplTmMin(String.valueOf(evMap.get("XPL_TM_MIN")));
	                rtnDto.setEvXplTmSetmYn(evMap.get("XPL_TM_SETM_YN"));
	                rtnDto.setExtrEvId(String.valueOf(evMap.get("EXTR_EV_ID")));
	                rtnDto.setEvTmScnt(String.valueOf(evMap.get("EV_TM_SCNT")));
	                showYn = "Y";
	                
	                log.debug(">> showYn:{}",showYn);
	                
	                TolQstCnt = String.valueOf(evMap.get("EV_TOT_CNT"));
	                evCmplYn = evMap.get("LRN_ST_CD");
	                xplTmScnt = rtnDto.getEvTmScnt();
	            }
	        	dto.setEaLrnRptTlSbcLrnAtvEvTcrData(rtnDto);   //eaLrnRptTlSbcLrnAtvEvDtoList
	            dto.setEaTotalCount(TolQstCnt);		//평가 총 개수
	            dto.setEaCansCount(cansCnt);		//평가 정답 수
	            dto.setEaCmplYn(evCmplYn);			//평가완료여부
	            dto.setEvIdCk(evIdCk);				//평가 존재여부
	            dto.setEaTmSum(xplTmScnt);			//평가 문항별 풀이시간의 합
	        }
	        **/
			returnList.add(dto);
		}
        return returnList;
	}
	
	
	/**
	 * 학습리포트 > 학습현황 > AI 추천학습 > 수학
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return selectAlPlMaRptList
	 */
	public List<EaLrnRptAlPlMluInfoDto> selectAlPlMaRptList(EaLrnRptAlPlDto eaLrnRptAlPlEaEDto) {	    
	    String userId  = eaLrnRptAlPlEaEDto.getUserId();	//사용자Id
		String optTxbId = eaLrnRptAlPlEaEDto.getOptTxbId();	//운영교과서 Id
		
		// 결과 리스트를 초기화
		List<EaLrnRptAlPlMluInfoDto> resultList = new ArrayList<>();
	    
		//AI 추천학습 > 수학 중단원 조회
		List<EaLrnRptAlPlMluInfoDto> mluInfoDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPlMluInfo",
				Map.of("userId", userId, "optTxbId", optTxbId));
		
		
		if (!mluInfoDto.isEmpty()) {
	        for (EaLrnRptAlPlMluInfoDto mluInfo : mluInfoDto) {
	            String mKmmpNodId = mluInfo.getMKmmpNodId();
	            String mKmmpNodNm = mluInfo.getMKmmpNodNm();
	            String tcUseYn = mluInfo.getTcUseYn();
				String lcknYn = mluInfo.getLcknYn();
				String useYn = mluInfo.getUseYn();
	            String rcstnOrdn = mluInfo.getRcstnOrdn();

	            List<EaLrnRptAlPlTpcInfoDto> tpcInfoDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPlTpcInfo",
	                    Map.of("userId", userId, "optTxbId", optTxbId, "mKmmpNodId", mKmmpNodId));

	            // 그룹핑된 정보를 저장할 맵
	            Map<String, List<EaLrnRptAlPlTpcInfoDto>> groupedTpcInfo = tpcInfoDto.stream()
	                    .collect(Collectors.groupingBy(t -> t.getEvId() + "_" + t.getEvDtlEvCd() + "_" + t.getEvTmScnt() + "_" + t.getEvRsMdfDtm()));

	            // 최종 결과 DTO를 초기화
	            EaLrnRptAlPlMluInfoDto resultInfoDto = new EaLrnRptAlPlMluInfoDto();
	            resultInfoDto.setMKmmpNodId(mKmmpNodId);
	            resultInfoDto.setMKmmpNodNm(mKmmpNodNm);
	            resultInfoDto.setTcUseYn(tcUseYn);
				resultInfoDto.setLcknYn(lcknYn);
				resultInfoDto.setUseYn(useYn);
	            resultInfoDto.setRcstnOrdn(rcstnOrdn);

	            List<EaLrnRptAlPlEaInfoDto> eaInfoDtoList = new ArrayList<>();
	            
	         // 완료 여부 플래그를 AtomicBoolean으로 초기화
                AtomicBoolean isCmplYn = new AtomicBoolean(false);

	            for (Map.Entry<String, List<EaLrnRptAlPlTpcInfoDto>> entry : groupedTpcInfo.entrySet()) {
	                String[] keys = entry.getKey().split("_");
	                String evId = keys[0];
	                String evDtlEvCd = keys[1];
	                String evTmScnt = keys[2];
	                String evRsMdfDtm = keys[3];

	                EaLrnRptAlPlEaInfoDto eaInfoDto = new EaLrnRptAlPlEaInfoDto();
	                eaInfoDto.setEvId(evId);
	                eaInfoDto.setEvDtlEvCd(evDtlEvCd);
	                eaInfoDto.setEvTmScnt(evTmScnt);
	                eaInfoDto.setEvRsMdfDtm(evRsMdfDtm);
	                
	                List<EaLrnRptAlPlTpcInfoDto> tpcInfoList = entry.getValue().stream().map(t -> {
	                    EaLrnRptAlPlTpcInfoDto tpcInfoItem = new EaLrnRptAlPlTpcInfoDto();
	                    tpcInfoItem.setMKmmpNodId(t.getMKmmpNodId());
	                    tpcInfoItem.setMKmmpNodNm(t.getMKmmpNodNm());
	                    tpcInfoItem.setTpcKmmpNodId(t.getTpcKmmpNodId());
	                    tpcInfoItem.setTpcKmmpNodNm(t.getTpcKmmpNodNm());
	                    tpcInfoItem.setDpth(t.getDpth());
	                    tpcInfoItem.setTpcAvn(t.getTpcAvn());
	                    
	                 // 추가된 부분: 카운트 로직
	                    if ("OV".equals(evDtlEvCd)) {
	                        if (t.getTpcAvn() > 0.8) {
	                            eaInfoDto.setTpcOvPass(eaInfoDto.getTpcOvPass() + 1);
	                        } else {
	                            eaInfoDto.setTpcOvWeak(eaInfoDto.getTpcOvWeak() + 1);
	                        }
	                        isCmplYn.set(true);  // OV 완료된 내용이 있으면 플래그 설정
	                        
	                    } else if ("C1".equals(evDtlEvCd)) {
	                        if (t.getTpcAvn() > 0.8) {
	                            eaInfoDto.setTpcC1Pass(eaInfoDto.getTpcC1Pass() + 1);
	                        } else {
	                            eaInfoDto.setTpcC1Weak(eaInfoDto.getTpcC1Weak() + 1);
	                        }
	                        isCmplYn.set(true); // C1 완료된 내용이 있으면 플래그 설정
	                        
	                    } else if ("C2".equals(evDtlEvCd)) {
	                        if (t.getTpcAvn() > 0.8) {
	                            eaInfoDto.setTpcC2Pass(eaInfoDto.getTpcC2Pass() + 1);
	                        } else {
	                            eaInfoDto.setTpcC2Weak(eaInfoDto.getTpcC2Weak() + 1);
	                        }
	                        isCmplYn.set(true);// C2 완료된 내용이 있으면 플래그 설정
	                        
	                    }
//	                    else if ("C3".equals(evDtlEvCd)) {
//	                        if (t.getTpcAvn() > 0.8) {
////	                            eaInfoDto.setTpcC3Pass(eaInfoDto.getTpcC3Pass() + 1);
//	                        }else {
//	                            eaInfoDto.setTpcC3Weak(eaInfoDto.getTpcC3Weak() + 1);
//	                        }
//	                        isCmplYn.set(true); // C3 완료된 내용이 있으면 플래그 설정
//	                    }
	                    
	                    return tpcInfoItem;
	                }).collect(Collectors.toList());
	                
	                
	                // OV 및 C1, C2, C3의 완료 여부 설정
	                eaInfoDto.setEvCmplYn(isCmplYn.get() ? "Y" : "N");
	                
	                
	                eaInfoDto.setTpcInfoList(tpcInfoList);
	                eaInfoDtoList.add(eaInfoDto);
	            }

	            resultInfoDto.setEaInfoList(eaInfoDtoList); // eaInfoDtoList를 설정
	            resultList.add(resultInfoDto);
	        }
	    }
	    
	    return resultList;
	}
	
	/**
	 * 학습리포트 > 학습현황 > AI 추천학습 > 고등 영어
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return selectAlPlMaRptList
	 */
	public List<EaLrnRptAlPlMluInfoDto> selectAlPlEaMHRptList2(EaLrnRptAlPlDto eaLrnRptAlPlEaEDto) {
		String userId  = eaLrnRptAlPlEaEDto.getUserId();	//사용자Id
		String optTxbId = eaLrnRptAlPlEaEDto.getOptTxbId();	//운영교과서 Id
		
		// 결과 리스트를 초기화
		List<EaLrnRptAlPlMluInfoDto> resultList = new ArrayList<>();
	    
		//AI 추천학습 > 영어 중단원 조회
		List<EaLrnRptAlPlMluInfoDto> mluInfoDto = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPlMluInfo",
				Map.of("userId", userId, "optTxbId", optTxbId));
		
		if (!mluInfoDto.isEmpty()) {
	        for (EaLrnRptAlPlMluInfoDto mluInfo : mluInfoDto) {
	            String mKmmpNodId = mluInfo.getMKmmpNodId();	//중단원 노드 id
	            //String mKmmpNodNm = mluInfo.getMKmmpNodNm();	//중단원 노드명
	            //String tcUseYn = mluInfo.getTcUseYn();			//차시 사용여부(재구성에서 중단원 사용여부로 활용)
	            //String rcstnOrdn = mluInfo.getRcstnOrdn();		//재구성 순서
	            
	            //중단원을 기주능로 차시의 값들을 조회 (OV, C1, C2 모두 가져온다)
	            List<EaLrnRptAlPlTcInfoDto> tcInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlPlEaMHRptList",
	                    Map.of("userId", userId, "optTxbId", optTxbId, "mKmmpNodId", mKmmpNodId));
	            
	            EaLrnRptAlPlTcInfoDto tcTotInfoDto = new EaLrnRptAlPlTcInfoDto();	 // 새로운 정리 tcInfo(차시별 정보들 저장)
	            List<EaLrnRptAlPlOvInfoDto> ovInfoDto = new ArrayList<>();		// 차시별 'OV'인 데이터를 저장할 리스트
	            List<EaLrnRptAlPlOvInfoDto> cInfoDataList = new ArrayList<>();	//차시별 'OV' 이외의 데이터를 저장할 리스트(C1, C2)

	            /*
	             * OV 데이터만 차시별로 분류 : ovInfoDto
	             * C1, C2, 데이터 별도 그룹화하여 차시별로 분류 : ctotInfoDto
	             */
	            // tcInfoDto에서 데이터를 분류
	            for (EaLrnRptAlPlTcInfoDto tcInfoData : tcInfoList) {
	                //조회된 데이터에서 OV만 추출
	            	if ("OV".equals(tcInfoData.getEvDtlEvCd())) {
	                    // EaLrnRptAlPlOvInfoDto 생성 후 ovInfoDto에 추가
	                    EaLrnRptAlPlOvInfoDto ovInfo = new EaLrnRptAlPlOvInfoDto();
	                    ovInfo.setMKmmpNodId(tcInfoData.getMKmmpNodId());		// 중단원id
	                    ovInfo.setTcKmmpNodId(tcInfoData.getTcKmmpNodId());		// 차시id
	                    ovInfo.setTcKmmpNodNm(tcInfoData.getTcKmmpNodNm());		// 차시명
	                    log.info("tcInfoData.getEvId() : " + tcInfoData.getEvId());
	                    ovInfo.setEvId(tcInfoData.getEvId() != null ? tcInfoData.getEvId() : null);					// 진단평가ID (차시별로 동일)
	                    ovInfo.setEvDtlEvCd(tcInfoData.getEvDtlEvCd());			//평가 구분코드 : OV
	                    ovInfo.setEvTmScnt(tcInfoData.getEvTmScnt() != null ? tcInfoData.getEvTmScnt() : "0");		//평가 풀이시간
	                    ovInfo.setEvCmplYn(tcInfoData.getEvCmplYn() != null ? tcInfoData.getEvCmplYn() : "N");		//평가 완료여부
	                    ovInfo.setTotalQtmCount(tcInfoData.getTotalQtmCount() != null ? tcInfoData.getTotalQtmCount() : "0");	//진다평가 총 문제 개수
	                    ovInfo.setCansYnCount(tcInfoData.getCansYnCount() != null ? tcInfoData.getCansYnCount() : "0");		//진단평가 정답 개수
	                    ovInfoDto.add(ovInfo);		//차시별로 저장
	                } 
	            	//OV 이외의 C1, C2를 저장 => 나중에 차시별로 그룹핑 => 따로 다시 차시 영역별로 추출하여 C1, C2나열
	            	else {
	                	EaLrnRptAlPlOvInfoDto cInfo = new EaLrnRptAlPlOvInfoDto();
	                	cInfo.setMKmmpNodId(tcInfoData.getMKmmpNodId());
	                	cInfo.setMKmmpNodNm(tcInfoData.getMKmmpNodNm());
	                	cInfo.setTcKmmpNodId(tcInfoData.getTcKmmpNodId());
	                	cInfo.setTcKmmpNodNm(tcInfoData.getTcKmmpNodNm());
	                	cInfo.setEvId(tcInfoData.getEvId());
	                	cInfo.setEvDtlEvCd(tcInfoData.getEvDtlEvCd());
	                	cInfo.setEvRsMdfDtm(tcInfoData.getEvRsMdfDtm());	// 평가 마지막 수정일
	                	cInfo.setEvTmScnt(tcInfoData.getEvTmScnt() != null ? tcInfoData.getEvTmScnt() : "0");
	                	cInfo.setEvCmplYn(tcInfoData.getEvCmplYn() != null ? tcInfoData.getEvCmplYn() : "N");
	                    cInfo.setTotalQtmCount(tcInfoData.getTotalQtmCount() != null ? tcInfoData.getTotalQtmCount() : "0");
	                    cInfo.setCansYnCount(tcInfoData.getCansYnCount() != null ? tcInfoData.getCansYnCount() : "0");
	                    cInfo.setLuevCmplYn(tcInfoData.getLuevCmplYn() != null ? tcInfoData.getLuevCmplYn() : "N"); //차시별 맞춤학습 최종 완료 여부
	                    cInfo.setAvgTpcAvn(tcInfoData.getAvgTpcAvn() != null ? tcInfoData.getAvgTpcAvn() : "-");	//차시별 토픽 점수 평균값
	                    cInfoDataList.add(cInfo);
	                }
	            }
	            
	            // 차시별 AI 맞춤학습 데이터를 그룹핑하여 재조합
	            Map<String, List<EaLrnRptAlPlOvInfoDto>> groupedCInfoMap = cInfoDataList.stream()
	                    .collect(Collectors.groupingBy(cInfo -> cInfo.getMKmmpNodId() + "-" + cInfo.getTcKmmpNodId() + "-" + cInfo.getTcKmmpNodNm()));
	            
	            List<EaLrnRptAlPlTcInfoDto> tcCTotInfoList = new ArrayList<>();
	            
	            for (Map.Entry<String, List<EaLrnRptAlPlOvInfoDto>> entry : groupedCInfoMap.entrySet()) {
	            	String[] keys = entry.getKey().split("-");
	                String mKmmpNodId2 = keys[0];
	                String tcKmmpNodId = keys[1];
	                String tcKmmpNodNm = keys[2];
	                
	                EaLrnRptAlPlTcInfoDto cTotInfoData = new EaLrnRptAlPlTcInfoDto();
	                cTotInfoData.setMKmmpNodId(mKmmpNodId2);
	                cTotInfoData.setTcKmmpNodId(tcKmmpNodId);
	                cTotInfoData.setTcKmmpNodNm(tcKmmpNodNm);
	                
	                List<EaLrnRptAlPlOvInfoDto> cInfoTotalData = entry.getValue().stream().map(t -> {
	                	EaLrnRptAlPlOvInfoDto cInfoItem = new EaLrnRptAlPlOvInfoDto();
	                	cInfoItem.setMKmmpNodId(t.getMKmmpNodId());
	                	cInfoItem.setMKmmpNodNm(t.getMKmmpNodNm());
	                	cInfoItem.setTcKmmpNodId(t.getTcKmmpNodId());
	                	cInfoItem.setTcKmmpNodNm(t.getTcKmmpNodNm());
	                	cInfoItem.setEvId(t.getEvId());
	                	cInfoItem.setEvDtlEvCd(t.getEvDtlEvCd());
	                	cInfoItem.setEvCmplYn(t.getEvCmplYn());
	                	cInfoItem.setEvRsMdfDtm(t.getEvRsMdfDtm());
	                	cInfoItem.setTotalQtmCount(t.getTotalQtmCount());
	                	cInfoItem.setCansYnCount(t.getCansYnCount());
	                	cInfoItem.setEvTmScnt(t.getEvTmScnt());
	                	cInfoItem.setAvgTpcAvn(t.getAvgTpcAvn());
	                	cInfoItem.setLuevCmplYn(t.getLuevCmplYn());
	                    return cInfoItem;
	                }).collect(Collectors.toList());
	                
	                // 그룹 내의 모든 항목의 cmplYn 값이 동일한지 확인하여 설정
	                if (!cInfoTotalData.isEmpty()) {
	                    cTotInfoData.setLuevCmplYn(cInfoTotalData.get(0).getLuevCmplYn());		//차시 최종 완료 여부 정보
	                    cTotInfoData.setAvgTpcAvn(cInfoTotalData.get(0).getAvgTpcAvn());		//차시에 따른 토픽 평균 숙련도값
	                }
	                cTotInfoData.setCDetailInfoDto(cInfoTotalData);
	                tcCTotInfoList.add(cTotInfoData);
	            }
	         // tcTotInfoDto 객체에 tcInfoDto 리스트를 설정
	            tcTotInfoDto.setOvInfoDto(ovInfoDto);		//OV 데이터
	            tcTotInfoDto.setTcCInfoTotDto(tcCTotInfoList);	//차시별 C1, C2 데이터
	            mluInfo.setTcInfoData(tcTotInfoDto);

	            
	            // 결과 리스트에 추가
	            resultList.add(mluInfo);
	        }
		}
		
		return resultList;
	}
	
	/**
	 * 학습리포트 > 학습현황 > AI 추천학습 > 초등 영어
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return selectAlPlMaRptList
	 */
	public List<EaLrnRptAlPlMluInfoDto> selectAlPlEaElRptList(EaLrnRptAlPlDto eaLrnRptAlPlEaEDto) {
		String userId  = eaLrnRptAlPlEaEDto.getUserId();	//사용자Id
		String optTxbId = eaLrnRptAlPlEaEDto.getOptTxbId();	//운영교과서 Id
		
		// 결과 리스트를 초기화
		//List<EaLrnRptAlPlMluInfoDto> resultList = new ArrayList<>();
	    
		//AI 추천학습 > 영어 중단원 조회
		List<EaLrnRptAlPlMluInfoDto> resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiPlMluInfo",Map.of("userId", userId, "optTxbId", optTxbId));
			
		return resultList;
	}
	
	/**
	 * 학습리포트 > 학습현황 > AI 추천학습 > 초등 영어2
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return selectAlPlMaRptList
	 */
	public Map<String,Object> selectAlPlEaMHRptList(EaLrnRptAlPlDto req) {
		Map<String,Object> resultMap = new HashMap<>();
		
		//List<EaLrnRptAlRcmTsshQtmDto> mluTcList = commonDao.selectList(MAPPER_NAMESPACE + "selectMluTcList2", req);
		//List<EaLrnRptAlRcmTsshQtmDto> aeEvInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectAeEvInfoList2", req);
		

		AiRcmTsshQtmDto alReq =  new AiRcmTsshQtmDto();
		alReq.setOptTxbId(req.getOptTxbId());
		alReq.setUsrId(req.getUserId());		
		List<AiRcmTsshQtmDto> mluTcList = alEnService.selectMluTcList(alReq);	//중단원 리스트
		List<EaLrnRptAlRcmTsshQtmDto> aeEvInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectAeEvInfoList2", req);
		
		List<Map<String,Object>> mluList = new ArrayList<>();
		List<String> mluStr = new ArrayList<>();
		for (AiRcmTsshQtmDto mlu : mluTcList) {//1dpth
			if(mluStr.contains(mlu.getMluKmmpNodId())) {
				continue;
			}
			//단원완료여부
			boolean luevCmplYn = true;
			
			//중단원맵
			Map<String, Object> mluMap = new HashMap<>();
			mluMap.put("luImgPth", mlu.getLuImgPth());
			mluMap.put("lluKmmpNodId", mlu.getLluKmmpNodId());
			mluMap.put("lluKmmpNodNm", mlu.getLluKmmpNodNm());
			mluMap.put("mluKmmpNodId", mlu.getMluKmmpNodId());
			mluMap.put("mluKmmpNodNm", mlu.getMluKmmpNodNm());
			mluMap.put("tcEpsYn", mlu.getTcEpsYn());
			mluMap.put("tcUseYn", mlu.getTcUseYn());
			mluMap.put("lcknYn", mlu.getLcknYn());
			mluMap.put("useYn", mlu.getUseYn());
			mluMap.put("rcstnOrdn", mlu.getRcstnOrdn());
			mluMap.put("orglOrdn", mlu.getOrglOrdn());

			
			//단원 학습자수준
	    	String lrnrVelTpCd = selectEnLuLrnrVelTpCd(mlu.getMluKmmpNodId(), req.getUserId());
	    	mluMap.put("lrnrVelTpCd", lrnrVelTpCd);
			
			//진단평가
			for (EaLrnRptAlRcmTsshQtmDto ug : aeEvInfoList) {
				if(mlu.getMluKmmpNodId().equals(ug.getMluKmmpNodId())
						&& ug.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
					Map<String, String> ugMap = new HashMap<>();
					ugMap.put("evId", ug.getEvId().toString());
					ugMap.put("evDtlDvCd", ug.getEvDtlDvCd());
					ugMap.put("evCmplYn", ug.getEvCmplYn());
					ugMap.put("mdfDtm", ug.getMdfDtm());
					ugMap.put("totalQtmCount", ug.getTotalQtmCount());
					ugMap.put("cansYnCount", ug.getCansYnCount());
					ugMap.put("xplTmScnt", ug.getXplTmScnt());
					mluMap.put(ug.getEvDtlDvCd(), ugMap);
				}
			}//ev end
			
			//차시리스트
			List<Map<String, Object>> tcList = new ArrayList<>();
			List<String> tcStr = new ArrayList<>();
			String tcKmmpNodIdsStr = "";
			for (AiRcmTsshQtmDto tc : mluTcList) {
				
				if(tcStr.contains(tc.getTcKmmpNodId())) {
					continue;
				}
				if(mlu.getMluKmmpNodId().equals(tc.getMluKmmpNodId())) {
					tcKmmpNodIdsStr = tcKmmpNodIdsStr + tc.getTcKmmpNodId() + ", ";
					
					Map<String, Object> tcMap = new HashMap<>();
					tcMap.put("tcKmmpNodId", tc.getTcKmmpNodId());
					tcMap.put("tcKmmpNodNm", tc.getTcKmmpNodNm());
					tcMap.put("lcknYn", tc.getLcknYn());
					tcMap.put("useYn", tc.getUseYn());
					
					if(tc.getTpcAvn() < AlConstUtil.TPC_AVN_01) {
						tcMap.put("tcAvn", "01");
					}else if(tc.getTpcAvn() >= AlConstUtil.TPC_AVN_01 && tc.getTpcAvn() < AlConstUtil.TPC_AVN_03) {
						tcMap.put("tcAvn", "02");
					}else {
						tcMap.put("tcAvn", "03");	
					}
					
					List<String> tpc01 = new ArrayList<>();
					List<String> tpc02 = new ArrayList<>();
					List<String> tpc03 = new ArrayList<>();
					for (AiRcmTsshQtmDto tpc : mluTcList) {
						if(tc.getTcKmmpNodId().equals(tpc.getTcKmmpNodId())) {
							if(tc.getTpcAvn() < AlConstUtil.TPC_AVN_01) {
								tpc01.add(tpc.getTpcKmmpNodNm());
							}else if(tc.getTpcAvn() > AlConstUtil.TPC_AVN_01 && tc.getTpcAvn() < AlConstUtil.TPC_AVN_03) {
								tpc03.add(tpc.getTpcKmmpNodNm());
							}else {
								tpc02.add(tpc.getTpcKmmpNodNm());	
							}
						}
					}
					tcMap.put("tpc01", tpc01);
					tcMap.put("tpc02", tpc02);
					tcMap.put("tpc03", tpc03);
					
					String tcevCmplYn = "";
					for (EaLrnRptAlRcmTsshQtmDto evInfo : aeEvInfoList) {
						if(tc.getMluKmmpNodId().equals(evInfo.getMluKmmpNodId()) 
								&& tc.getTcKmmpNodId().equals(evInfo.getTcKmmpNodId())
								&& !evInfo.getEvDtlDvCd().equals(AlConstUtil.EV_DTL_DV_CD_OV)) {
							
							//단원완료여부 모든차시가 완료되어야 한다. 
							if(!evInfo.getLuevCmplYn().equals("Y")) {
								luevCmplYn = false;
							}
							//차시완료여부
							tcevCmplYn = evInfo.getLuevCmplYn();
							
							Map<String, String> evMap = new HashMap<>();
							evMap.put("evId", evInfo.getEvId().toString());
							evMap.put("evDtlDvCd", evInfo.getEvDtlDvCd());
							evMap.put("evCmplYn", evInfo.getEvCmplYn());
							evMap.put("mdfDtm", evInfo.getMdfDtm());
							evMap.put("totalQtmCount", evInfo.getTotalQtmCount());
							evMap.put("cansYnCount", evInfo.getCansYnCount());
							evMap.put("xplTmScnt", evInfo.getXplTmScnt());
							tcMap.put(evInfo.getEvDtlDvCd(), evMap);
						}
					}//ev end
					tcMap.put("tcevCmplYn", tcevCmplYn);
					tcList.add(tcMap);
					tcStr.add(tc.getTcKmmpNodId());
				}
			}//tc end
			mluMap.put("luevCmplYn", luevCmplYn ? "Y" : "N");
			mluMap.put("tcList", tcList);
			mluMap.put("tcKmmpNodIdsStr", tcKmmpNodIdsStr);
			mluStr.add(mlu.getMluKmmpNodId());
			

			mluList.add(mluMap);
		}//mlu end
		
		resultMap.put("mluList", mluList);
		//resultMap.put("recentLearningList", recentLearningList);
		
		log.debug("resultMapList: " + resultMap);
		return resultMap;
	}
	
	/**
	 * 영어는 사용자수준이 차시별로 저장되기떄문에 단원 전체의 사용자수준이 필요한경우 해당서비스에서 계산
	 * @param mluKmmpNodId
	 * @param usrId
	 * @return lrnrVelTpCd
	 * 
	 * */
	public String selectEnLuLrnrVelTpCd(String mluKmmpNodId, String usrId) {
		String lrnrVelTpCd = "NM";
		
		EaLrnRptAlRcmTsshQtmDto req = new EaLrnRptAlRcmTsshQtmDto();
		req.setMluKmmpNodId(mluKmmpNodId);
		req.setUsrId(usrId);
		List<EaLrnRptAlRcmTsshQtmDto> tcCansYCntList = commonDao.selectList(MAPPER_NAMESPACE + "selectEnLuLrnrVelTpCd2", req);
		
		Integer tcCnt = tcCansYCntList.size();
		Integer ugYCnt = 0;
		if(!tcCansYCntList.isEmpty()) {
			for (EaLrnRptAlRcmTsshQtmDto tc : tcCansYCntList) {
				ugYCnt += tc.getUgMmYCnt();
			}
			
			if(ugYCnt == AlConstUtil.EN_LRNR_VEL_FS * tcCnt) {
				lrnrVelTpCd = "FS";
			}
			else if(ugYCnt == AlConstUtil.EN_LRNR_VEL_NM * tcCnt) {
				lrnrVelTpCd = "NM";
			}
			else if(ugYCnt == AlConstUtil.EN_LRNR_VEL_SL * tcCnt) {
				lrnrVelTpCd = "SL";
			}
		}
		
		return lrnrVelTpCd;
	}
	
	
}
