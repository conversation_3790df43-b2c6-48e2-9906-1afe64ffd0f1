package com.aidt.api.tl.lrnwif.stu;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.tl.common.TlConstUtil;
import com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwLrnPdfDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocAtvDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebDto;
import com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebSaveDto;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;
import com.aidt.common.util.ConstantsExt;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 15:57:52
 * @modify date 2024-02-15 15:57:52
 * @desc TlLrnwIfStu Service 교과학습 학습창연계처리 서비스
 */
@Slf4j
@Tag(name="[tl] 교과학습 학습창연계처리[TlLrnwIfStu]", description="학습창관련 연계데이터 처리(학생용)")
@RestController
@RequestMapping("/api/v1/tl/stu/lrnwif")
public class TlLrnwIfStuController {
	
    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private TlLrnwIfStuService tlLrnwIfStuService;

    /**
     * 교과학습 목차및학습활동 조회 서비스
     *
     * @return ResponseDto<List<TlLrnwSbcLrnTocDto>>
     */
    @Operation(summary="교과학습 목차및학습활동 리스트 조회", description="학습창에서 표시할 목차및학습활동정보를 가져온다.")
    @PostMapping(value = "/selectLrnTocAtvInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<TlLrnwTocAtvDto> selectLrnTocAtvInfo(@Valid @RequestBody TlLrnwTocSrhDto srhDto) {
        log.debug("Entrance selectLrnTocAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        srhDto.setOptTxbId(userDetails.getOptTxbId());
        srhDto.setUsrId(userDetails.getUsrId());
        if(srhDto.getOneClkYn() == null || srhDto.getOneClkYn().equals("")) {
        	srhDto.setOneClkYn("N");
        }
        return Response.ok(tlLrnwIfStuService.selectLrnTocAtvInfo(srhDto, TlConstUtil.USR_DIV_STU, userDetails.getTxbId()));
    }

    /**
     * 교과학습 학습활동 저장 서비스
     *
     * @return ResponseDto<Integer>
     * @throws JsonProcessingException 
     */
    @Operation(summary="교과학습 학습활동 저장", description="학습창에서 사용자의 학습활동상태및 학습시간을 저장한다.")
    @PostMapping(value = "/saveLrnAtvInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Object> saveLrnAtvInfo(@Valid @RequestBody TlLrnwAtvSaveDto saveDto, HttpServletRequest request) throws JsonProcessingException {
        log.debug("Entrance saveLrnAtvInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setLrnUsrId(userDetails.getUsrId());
        // DB_ID
        saveDto.setDbId(userDetails.getTxbId());
        // 학생
        Object result = tlLrnwIfStuService.saveLrnAtvInfo(saveDto, TlConstUtil.USR_DIV_STU);
        // 마이홈 point지급처리
		Map<String, Object> apiResult = this.regMyhmPnt(saveDto, request);
		log.debug(">> apiResult:{}",apiResult);
		
        return Response.ok(result);
    }

    /**
     * MyHome point 등록처리
     * @param saveDto
     * @return
     * @throws JsonProcessingException 
     */
    private Map<String, Object> regMyhmPnt(TlLrnwAtvSaveDto saveDto, HttpServletRequest request) throws JsonProcessingException {
        Map<String, Object> apiResult = null;
        // 마이홈 포인트처리
        if ("CL".equals(saveDto.getLrnStCd())) { // 상태값이 완료로 되는 경우만 실행
            Map<String, Object> lrmpNodMap = tlLrnwIfStuService.getLrmpNodInfo(saveDto);
            if(lrmpNodMap != null && !lrmpNodMap.isEmpty()) {
            	String cmplClYn = lrmpNodMap.get("CMPL_CL_YN").toString(); // 개념학습완료여부 (마이홈 포인트처리용)
                String cmplExYn = lrmpNodMap.get("CMPL_EX_YN").toString(); // 평가완료여부 (마이홈 포인트처리용)
                String cmplWbYn = lrmpNodMap.get("CMPL_WB_YN").toString(); // 익힘책완료여부 (마이홈 포인트처리용)
                String lrnStpDvCd = lrmpNodMap.get("LRN_STP_DV_CD").toString(); // 학습단계구분코드 (마이홈 포인트처리용)
                String accessToken = jwtProvider.resolveToken(request, ConstantsExt.accessTokenHeader);

                //boolean isAsnPnt = false; // 과제제출 포인트 부과여부
                if (TlConstUtil.LRN_STP_DV_CL.equals(lrnStpDvCd)) {
                    if ("Y".equals(cmplClYn)) { // 차시완료인 경우
                        apiResult = tlLrnwIfStuService.callMyhmApi(accessToken, Map.of(
                            "pntCd", TlConstUtil.MYHOME_PNT_CD_CL,
                            "pntChkBsVl", saveDto.getLrmpNodId()));
                            // isAsnPnt = true;
                    }
                } else if(TlConstUtil.LRN_STP_DV_EX.equals(lrnStpDvCd) && "Y".equals(cmplExYn)) {
//                    if ("Y".equals(cmplExYn)) { // 평가 완료인 경우
//                        apiResult = tlLrnwIfStuService.callMyhmApi(accessToken, Map.of(
//                            "pntCd", TlConstUtil.MYHOME_PNT_CD_EX,
//                            "pntChkBsVl", saveDto.getLrmpNodId()));
//                            // isAsnPnt = true;
//                    }
                } else if(TlConstUtil.LRN_STP_DV_WB.equals(lrnStpDvCd) && "Y".equals(cmplWbYn)) {
                    if ("Y".equals(cmplWbYn)) { // 익힘책 완료인 경우
                        apiResult = tlLrnwIfStuService.callMyhmApi(accessToken, Map.of(
                            "pntCd", TlConstUtil.MYHOME_PNT_CD_WB,
                            "pntChkBsVl", saveDto.getLrmpNodId()));
                            // isAsnPnt = true;
                    }
                }
            }
        }
        return apiResult;
    }

    @Operation(summary="교과서/익힘책 보기 리스트", description="해당 차시의 교과서 정보 Page no, 이미지 path 를 가져온다")
    @PostMapping(value = "/selectLrntxtwkbList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<List<TlLrnwLrnPdfDto>> selectLrntxtwkbList(@Valid @RequestBody TlLrnwTxbWebDto txbWebDto) throws JsonMappingException, JsonProcessingException {
        log.debug("Entrance selectLrntxtwkbList");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        txbWebDto.setOptTxbId(userDetails.getOptTxbId());
        if (txbWebDto.getUsrId() == "") {
        	txbWebDto.setUsrId(userDetails.getUsrId());	
        }
        
        List<TlLrnwLrnPdfDto> pdfData = tlLrnwIfStuService.selectLrntxtwkbList(txbWebDto);

        return Response.ok(pdfData);
    }


    @Operation(summary="교과서/익힘책 필기 정보 저장", description="교과서/익힘책 필기 정보를 저장한다")
    @PostMapping(value = "/saveLrntxtwkbInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseDto<Integer> saveLrntxtwkbInfo(@Valid @RequestBody TlLrnwTxbWebSaveDto saveDto) {
        log.debug("Entrance saveLrntxtwkbInfo");
        CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

        // 세션정보에서 설정
        saveDto.setOptTxbId(userDetails.getOptTxbId());
        saveDto.setUsrId(userDetails.getUsrId());
        saveDto.setDbId(userDetails.getTxbId());
        return Response.ok(tlLrnwIfStuService.saveLrntxtwkbInfo(saveDto));
    }

}
