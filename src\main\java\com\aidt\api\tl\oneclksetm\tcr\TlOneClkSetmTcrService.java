package com.aidt.api.tl.oneclksetm.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm;
import com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink;
import com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto;
import com.aidt.api.tl.oneclksetm.TlOneclkCacheService;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlAnnxFleDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlPlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAlTocDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmAtvDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmClaDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmExtcmpTxbDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmExtcmpTxbInfoDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmFncUseDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDtlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmCtnDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRcmStuDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmRegMtrlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSbcLrnDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSpDtlDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmSrhDto;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto;
import com.aidt.api.util.RedisUtil;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 16:11:29
 * @modify date 2024-02-15 16:11:29
 * @desc [TlOneClkSetmTcr Service 원클릭학습설정]
 */
@Slf4j
@Service
public class TlOneClkSetmTcrService {
    private final String MAPPER_NAMESPACE = "api.tl.oneclksetm.tcr.";

    @Autowired
    private CommonDao commonDao;
    
    @Autowired
    private TlOneclkCacheService tlOneclkCacheService;
    
    @Autowired
    private EaAsnComService eaAsnComService;

    @Autowired
    private BcCmService bcCmService;
    
    @Autowired
    private RedisUtil redisUtil;

    @Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;

    /**
     * 원클릭학습설정 학습목차 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmTocDto>
     */
    @Transactional(readOnly = true)
    @Cacheable(
    		cacheNames = "longCache",
    		key = "'tl:' + #srhDto.optTxbId + ':selectLrnTocList'",
    		unless = "#result.size() == 0",
    		cacheManager = "aidtCacheManager"
    		)
    public List<TlOneClkSetmTocDto> selectLrnTocList(TlOneClkSetmSrhDto srhDto) {
    	List<TlOneClkSetmTocDto> result = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnTocList", srhDto);
        return result;
    }

    /**
     * 원클릭학습설정 학습활동 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmAtvDto>
     */
    @Transactional(readOnly = true)
    @Cacheable(
    		cacheNames = "longCache",
    		key = "'tl:' + #srhDto.optTxbId + ':selectLrnAtvList'",
    		condition = "''.equals(#srhDto.getLrmpNodId())",
    		unless = "#result.size() == 0",
    		cacheManager = "aidtCacheManager"
    		)
    public List<TlOneClkSetmAtvDto> selectLrnAtvList(TlOneClkSetmSrhDto srhDto) {
    	List<TlOneClkSetmAtvDto> atvList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnAtvList", srhDto);
    	for(TlOneClkSetmAtvDto atvDto : atvList) {
    		atvDto.setLrnAtvThbPth(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, atvDto.getLrnAtvThbPth()));
    	}
        return atvList;
    }

    /**
     * 원클릭학습설정 학급 리스트 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmClaDto>
     */
    @Transactional(readOnly = true)
    public List<TlOneClkSetmClaDto> selectClaList(TlOneClkSetmSrhDto srhDto) {
    	List<TlOneClkSetmClaDto> claList = commonDao.selectList(MAPPER_NAMESPACE + "selectClaList", srhDto);
    	for(TlOneClkSetmClaDto cla : claList) {
    		int claCnt = commonDao.select(MAPPER_NAMESPACE + "selectRcstnCnt", Map.of("optTxbId",cla.getOptTxbId()));
    		if(claCnt > 0) {
    			cla.setRcstnYn("Y");
    		} else {
    			cla.setRcstnYn("N");
    		}
    	}
        return claList;
    }

    /**
     * 원클릭학습설정 Ai맞춤학습 목차 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmAlTocDto>
     */
    @Transactional(readOnly = true)
    public List<TlOneClkSetmAlTocDto> selectAlTocList(TlOneClkSetmSrhDto srhDto) {
        return commonDao.selectList(MAPPER_NAMESPACE + "selectAlTocList", srhDto);
    }

    /**
     * 원클릭학습설정 교과학습 재구성 저장, 다른 학급 저장
     * 
     * @param updDto
     * @return int
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':txbTcList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectAllTxbTcList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectTxbTcListHm'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectLrnTocAtvInfo'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectLrnTocList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectLrnAtvList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectLsnMtrlInfo'", cacheManager="aidtCacheManager")
    })
    public int updateSbclrnRcstn(TlOneClkSetmSbcLrnDto sbcLrnDto, String mdfrId, String kerisUsrId, String orgnOptTxbId, String dbId) {
        int cnt = 0;

        List<TlOneClkSetmClaDto> claDtoList = sbcLrnDto.getClaList(); // 다른 학급 리스트
        List<TlOneClkSetmTocDto> tocDtoList = sbcLrnDto.getTocList(); // 학습목차 리스트
        List<TlOneClkSetmAtvDto> atvDtoList = sbcLrnDto.getAtvList(); // 학습활동 리스트
        List<TlOneClkSetmAtvDto> ctnDtoList = sbcLrnDto.getCtnList(); // 교사 추가 콘텐츠 리스트
        List<TlOneClkSetmTocDto> ansDtoList =  new ArrayList<>(); // 과제 설정 리스트
        List<TlOneClkSetmTocDto> aiAnsDtoList = new ArrayList<>(); // ai 과제 설정 리스트
        List<TlOneClkSetmRegMtrlDto> mtrlDtoList = sbcLrnDto.getMtrlList(); //수업자료 리스트
        int extcmpTxb = sbcLrnDto.getExtcmpTxb();
        
        TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();
        srhDto.setOptTxbId(orgnOptTxbId);

        if (claDtoList != null) { // 교과학습 재구성 다른 학급 저장
        	Map<String, Object> extcmpTxbInfo = new HashMap<>();
        	List<Map<String, Object>> orglTxbInfo = new ArrayList<>();
        	
        	extcmpTxbInfo = commonDao.select(MAPPER_NAMESPACE + "selectExtcmpTxbMpn", Map.of("optTxbId",orgnOptTxbId));
        	orglTxbInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbAtvInfo", Map.of("optTxbId",orgnOptTxbId));
        	
        	for (TlOneClkSetmClaDto claDto : claDtoList) {
        		if (StringUtils.isEmpty(claDto.getOptTxbId())) {
                    throw new IllegalArgumentException("Invalid optTxbId in TlOneClkSetmClaDto ");
                }
        		int claCnt = commonDao.select(MAPPER_NAMESPACE + "selectRcstnCnt", Map.of("optTxbId",claDto.getOptTxbId()));
        		if(claCnt > 0) {
            		cnt +=  tlOneclkCacheService.updateAntClaSbclrnRcstn(orgnOptTxbId, claDto, ansDtoList, aiAnsDtoList, mdfrId, kerisUsrId, extcmpTxbInfo, orglTxbInfo);
        		} else {
        	    	CmClaCpLogDto logDto = CmClaCpLogDto.builder().optTxbId(orgnOptTxbId).cpOptTxbId(claDto.getOptTxbId()).cpDvCd("OCK").cpPrcsYn("N")
        					.backendFlePth("TlOneclkCacheService.updateAntClaSbclrnRcstn").kerisUsrId(kerisUsrId).crtrId(mdfrId).build();
        	    	bcCmService.insertClaCpLog(logDto);
        		}
        	}
        } else if (claDtoList == null) { // 교과학습 재구성 저장
            ansDtoList = sbcLrnDto.getAnsList(); // 과제 설정 리스트
            aiAnsDtoList = sbcLrnDto.getAiAnsList(); // ai 과제 설정 리스트
            
//            String searchKey = "longCache:tl:" + orgnOptTxbId + ":txbTcList*";
//            redisUtil.redisKeyDeleteArray(searchKey);
            String detailLocationSearchKey = "shortCache:bc:" + orgnOptTxbId + ":selectStuCurLrnStDtl*";
            redisUtil.redisKeyDeleteArray(detailLocationSearchKey);

            // 학습 목차 저장
            for (TlOneClkSetmTocDto tocDto : tocDtoList) {
                tocDto.setMdfrId(mdfrId);
                cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnTocList", tocDto);
            }
            // 학습 활동 저장
            for (TlOneClkSetmAtvDto atvDto : atvDtoList) {
                atvDto.setMdfrId(mdfrId);
                cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnAtvList", atvDto);
            }
            // 교사 추가 콘텐츠 저장
            for (TlOneClkSetmAtvDto ctnDto : ctnDtoList) {
                ctnDto.setMdfrId(mdfrId);
                if("Y".equals(ctnDto.getDelYn())) {
                	cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteLrnCtnList", ctnDto);
                } else {
                    cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnCtnList", ctnDto);	
                }
            }
            
            if(mtrlDtoList != null && mtrlDtoList.size() > 0) {
            	for (TlOneClkSetmRegMtrlDto mtrlDto : mtrlDtoList) {
            		mtrlDto.setOptTxbId(orgnOptTxbId);
            		mtrlDto.setMdfrId(mdfrId);
            		mtrlDto.setDbId(dbId);		
            		if(mtrlDto.getChgLrmpNodId() == null) {
            			mtrlDto.setChgLrmpNodId(mtrlDto.getLrmpNodId());
            		}
            		
            		if(mtrlDto.getLsnMtrlNo()==0) {
            			TlOneClkSetmRegMtrlDto infoDto = commonDao.select(MAPPER_NAMESPACE + "selectMtrlInfo", mtrlDto);
            	    	mtrlDto.setLsnMtrlNo(infoDto.getLsnMtrlNo()+1);
            	    	mtrlDto.setRcstnOrdn(infoDto.getRcstnOrdn()+1);
            	    	mtrlDto.setBsMtrlYn("Y");
            			cnt = commonDao.insert(MAPPER_NAMESPACE + "insertRegMtrl", mtrlDto);
            		} else {
            			cnt = commonDao.update(MAPPER_NAMESPACE + "updateRegMtrl", mtrlDto);
            		}
            	}
            }
            log.debug(String.valueOf(eaAsnComService.callTlAsn(ansDtoList)));

			if (aiAnsDtoList != null) {
				if (aiAnsDtoList.size() > 0) {
					for (TlOneClkSetmTocDto aiAnsDto : aiAnsDtoList) {
						EaAsnTcrDto asnDto = new EaAsnTcrDto();
						asnDto.setOptTxbId(orgnOptTxbId);
						asnDto.setLuNodId(aiAnsDto.getLluNodId());
						asnDto.setUseYn(aiAnsDto.getUseYn());
						log.debug("ai과제 수정 ----------------------------------------------");
						log.debug(String.valueOf(eaAsnComService.updateAiAsnStatus(asnDto)));
					}
				}
			}
			
			if(extcmpTxb != 0) {
				Map<String, Object> extcmpTxbInfo = new HashMap<>();
				extcmpTxbInfo = commonDao.select(MAPPER_NAMESPACE + "selectExtcmpTxbMpn", Map.of("optTxbId",srhDto.getOptTxbId()));
				if (extcmpTxbInfo.get("EXTCMP_TXB_ID") == null || extcmpTxbInfo.get("EXTCMP_TXB_ID").equals("")) {
					commonDao.insert(MAPPER_NAMESPACE + "insertExtcmpCnt", Map.of("optTxbId",srhDto.getOptTxbId(), "txbId", extcmpTxb, "crtrId", mdfrId));
				} else {
					commonDao.update(MAPPER_NAMESPACE + "updateExtcmpCnt", Map.of("optTxbId",srhDto.getOptTxbId(), "txbId", extcmpTxb, "crtrId", mdfrId));
				}
			}

            
            updateLrnSortRcstnList(srhDto);
        }

        return cnt;
    }

    /**
     * 원클릭학습설정 Ai맞춤학습 재구성 저장, 다른 학급 저장
     * 
     * @param alPlDto
     * @param mdfrId
     * @return int
     */
    @Transactional
    public int updateAlPlRcstn(TlOneClkSetmAlPlDto alPlDto, String mdfrId, String orgnOptTxbId) {
        int cnt = 0;

        List<TlOneClkSetmAlTocDto> alTocDtoList = alPlDto.getAlTocList(); // 학습목차 리스트
        List<TlOneClkSetmClaDto> claDtoList = alPlDto.getClaList(); // 다른 학급 리스트

        if (claDtoList != null) { // 다른 학급 저장
            for (TlOneClkSetmClaDto claDto : claDtoList) {
                // 다른 학급 리스트 데이터 검증
                if (StringUtils.isEmpty(claDto.getOptTxbId())) {
                    throw new IllegalArgumentException("Invalid optTxbId in TlOneClkSetmClaDto ");
                }
                claDto.setMdfrId(mdfrId);
                claDto.setOrgnOptTxbId(orgnOptTxbId);
                cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaAlPlRcstn", claDto); // 재구성 저장

            }
        } else if (claDtoList == null) {
            for (TlOneClkSetmAlTocDto alTocDto : alTocDtoList) {
                alTocDto.setMdfrId(mdfrId);
                cnt += commonDao.update(MAPPER_NAMESPACE + "updateAlPlRcstnOrdn", alTocDto); // 재구성 순서 저장
                cnt += commonDao.update(MAPPER_NAMESPACE + "updateAlPlRcstn", alTocDto); // 사용여부, 잠금여부 저장
            }
        }

        return cnt;
    }

    /**
     * 원클릭학습설정 학습일정관리 저장
     * 
     * @param sbcLrnDto
     * @param mdfrId
     * @return int
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':txbTcList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectAllTxbTcList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectTxbTcListHm'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectLrnTocAtvInfo'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key="'tl:' + #orgnOptTxbId + ':selectLrnTocList'", cacheManager="aidtCacheManager")
    })
    public int updateLrnScdlMg(TlOneClkSetmSbcLrnDto sbcLrnDto, String orgnOptTxbId, String mdfrId, String kerisUsrId) {
    	int cnt = 0;
    	List<TlOneClkSetmClaDto> claDtoList = sbcLrnDto.getClaList(); // 다른 학급 리스트
        List<TlOneClkSetmTocDto> ansDtoList =  new ArrayList<>(); // 과제 설정 리스트
        List<TlOneClkSetmTocDto> aiAnsDtoList = new ArrayList<>(); // ai 과제 설정 리스트
    	
    	if (claDtoList != null) {
    		List<Map<String, Object>> orglTxbInfo = new ArrayList<>();
        	orglTxbInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectTxbAtvInfo", Map.of("optTxbId",orgnOptTxbId));
        	
    		for (TlOneClkSetmClaDto claDto : claDtoList) {
                // 다른 학급 리스트 데이터 검증
                if (StringUtils.isEmpty(claDto.getOptTxbId())) {
                    throw new IllegalArgumentException("Invalid optTxbId in TlOneClkSetmClaDto ");
                }
                
                int claCnt = commonDao.select(MAPPER_NAMESPACE + "selectRcstnCnt", Map.of("optTxbId",claDto.getOptTxbId()));
        		if(claCnt > 0) {
        			cnt += tlOneclkCacheService.updateAntClaLrnScdlMg(orgnOptTxbId, claDto, ansDtoList, aiAnsDtoList, mdfrId, kerisUsrId, orglTxbInfo);
        		} else {
        	    	CmClaCpLogDto logDto = CmClaCpLogDto.builder().optTxbId(orgnOptTxbId).cpOptTxbId(claDto.getOptTxbId()).cpDvCd("OCK").cpPrcsYn("N")
        					.backendFlePth("TlOneclkCacheService.updateAntClaLrnScdlMg").kerisUsrId(kerisUsrId).crtrId(mdfrId).build();
        	    	bcCmService.insertClaCpLog(logDto);
        		}
            }
    	} else if (claDtoList == null) {
    		List<TlOneClkSetmTocDto> lrnScdlMgList = sbcLrnDto.getTocList();
    		ansDtoList = sbcLrnDto.getAnsList();
        	aiAnsDtoList = sbcLrnDto.getAiAnsList();
        	
//        	String searchKey = "longCache:tl:" + orgnOptTxbId + ":txbTcList*";
//            redisUtil.redisKeyDeleteArray(searchKey);
            String detailLocationSearchKey = "shortCache:bc:" + orgnOptTxbId + ":selectStuCurLrnStDtl*";
            redisUtil.redisKeyDeleteArray(detailLocationSearchKey);

            for (TlOneClkSetmTocDto dto : lrnScdlMgList) {
    	    	if(dto.getLrnStrDt() != null && !dto.getLrnStrDt().equals("")) {
    	    		dto.setLrnStrDt(dto.getLrnStrDt().replace(".", "-").replace(" ",""));
    	    	}
    	        dto.setMdfrId(mdfrId);
    	        cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnScdlMg", dto);
    	    }
            eaAsnComService.updateTlAsnLockStatus(ansDtoList);
    	    
    	    for (TlOneClkSetmTocDto aiAnsDto : aiAnsDtoList) {
    	    	EaAsnTcrDto asnDto = new EaAsnTcrDto();
    	    	asnDto.setOptTxbId(orgnOptTxbId);
    	    	asnDto.setLuNodId(aiAnsDto.getLluNodId());
    	    	asnDto.setTcNodId(aiAnsDto.getLrmpNodId());
    	    	asnDto.setLcknYn(aiAnsDto.getLcknYn());
    	    	asnDto.setTcLcknYn(aiAnsDto.getTcLcknYn());
    	    	log.debug(String.valueOf(asnDto));
    	    	log.debug("ai과제 수정 ----------------------------------------------");
    	    	eaAsnComService.updateAiAsnLockStatus(asnDto);
    	    }
    	}

        return cnt;
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 목차 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmRcmCtnDto>
     */
    @Transactional(readOnly = true)
    @Cacheable(
    		cacheNames = "longCache",
    		key = "'tl:' + #srhDto.optTxbId + ':selectRcmCtnList'",
    		unless = "#result.size() == 0",
    		cacheManager = "aidtCacheManager"
    		)
    public List<TlOneClkSetmRcmCtnDto> selectRcmCtnList(TlOneClkSetmSrhDto srhDto) {
        List<TlOneClkSetmRcmCtnDto> rcmCtnList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmCtnList", srhDto);
        List<TlOneClkSetmRcmStuDto> stuList = tlOneclkCacheService.selectRcmCtnStuList(srhDto.getOptTxbId());
        List<TlOneClkSetmSpDtlDto> lwsList = commonDao.selectList(MAPPER_NAMESPACE + "selectSlSpLrnDtlList", srhDto);

        rcmCtnList.stream().forEach(list -> {
            // 콘텐츠 개수 셋팅
            list.setCtnCnt(0);
            lwsList.stream().forEach(idx -> {
                if (idx.getSpLrnId().equals(list.getSpLrnId())) {
                    list.setCtnCnt(list.getCtnCnt() + 1);
                }
            });
            List<TlOneClkSetmRcmStuDto> rcmStuList = new ArrayList<TlOneClkSetmRcmStuDto>();
            
            stuList.stream().filter(stu -> stu.getSpLrnId().equals(list.getSpLrnId())).forEach(list2 -> {
            	if(list.getSpLrnId().equals(list2.getSpLrnId())) {
            		rcmStuList.add(list2);
            	}
            });
            
            list.setStuList(rcmStuList);
            // 추천 학생수
//            list.setRcmStuCnt(0);
//            stuList.stream().forEach(idx2 -> {
//                if (list.getSpLrnDffn().equals("HH")) {
//                    if (idx2.getSpLrnId().equals(list.getSpLrnId()) && idx2.getLrnrVelTpCd().equals("FS")
//                            && idx2.getRcmYn() != "N") {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    } else if (idx2.getSpLrnId().equals(list.getSpLrnId()) && idx2.getRcmYn().equals("Y")) {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    }
//                } else if (list.getSpLrnDffn().equals("MM")) {
//                    if (idx2.getSpLrnId().equals(list.getSpLrnId()) && idx2.getLrnrVelTpCd().equals("NM")
//                            && idx2.getRcmYn() != "N") {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    } else if (idx2.getSpLrnId().equals(list.getSpLrnId()) && idx2.getRcmYn().equals("Y")) {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    }
//                } else if (list.getSpLrnDffn().equals("LL")) {
//                    if (idx2.getSpLrnId().equals(list.getSpLrnId()) && idx2.getLrnrVelTpCd().equals("SL")
//                            && idx2.getRcmYn() != "N") {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    } else if (idx2.getSpLrnId().equals(list.getSpLrnId()) && idx2.getRcmYn().equals("Y")) {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    }
//                } else {
//                    if (idx2.getSpLrnId().equals(list.getSpLrnId())) {
//                        list.setRcmStuCnt(list.getRcmStuCnt() + 1);
//                    }
//                }
//            });
            list.setPcPath(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, list.getPcPath()));
            list.setTaPath(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, list.getTaPath()));
        });

        return rcmCtnList;
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 우리반 일괄 저장
     * 
     * @param srhDto
     * @return int
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="longCache", key="'tl:' + #optTxbId + ':selectRcmCtnList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key = "'sl:' + #optTxbId + ':selectRcmCtnStuList'", cacheManager = "aidtCacheManager")
    })
    public int updateRcmCtnCla(TlOneClkSetmSrhDto srhDto, String optTxbId, String txbId, String mdfrId) {
        int cnt = 0;
        for (String list : srhDto.getSpLrnIdList()) {
            srhDto.setSpLrnId(list);
            srhDto.setOptTxbId(optTxbId);
            List<TlOneClkSetmRcmCtnDtlDto> stuList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmCtnDtlList",
                    srhDto);
            for (TlOneClkSetmRcmCtnDtlDto list2 : stuList) {
                list2.setDbId(txbId);
                list2.setOptTxbId(optTxbId);
                list2.setMdfrId(mdfrId);
                list2.setRcmYn("Y");
                if (list2.getDatYn().equals("Y") || list2.getDatYn().equals("N")) {
                    commonDao.update(MAPPER_NAMESPACE + "updateRcmCtnInfo", list2);
                    cnt++;
                } else {
                    commonDao.insert(MAPPER_NAMESPACE + "insertRcmCtnInfo", list2);
                    cnt++;
                }
            }
        }
        return cnt;
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 학생별 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmRcmCtnDtlDto>
     */
    @Transactional(readOnly = true)
    public List<TlOneClkSetmRcmCtnDtlDto> selectRcmCtnDtlList(TlOneClkSetmSrhDto srhDto) {
    	List<TlOneClkSetmRcmCtnDtlDto> rcmCtnDtlList = commonDao.selectList(MAPPER_NAMESPACE + "selectRcmCtnDtlList", srhDto);
    	for (TlOneClkSetmRcmCtnDtlDto list : rcmCtnDtlList) {
    		TlOneClkSetmRcmCtnDtlDto lrnStInfo = commonDao.select(MAPPER_NAMESPACE + "selectRcmCtnLrnSt", Map.of("optTxbId", srhDto.getOptTxbId(), "spLrnId", list.getSpLrnId(), "usrId", list.getUsrId()));
    		list.setDone(lrnStInfo.getDone());
    		list.setEntire(lrnStInfo.getEntire());
    		list.setEvYn(lrnStInfo.getEvYn());
    		list.setCtnDone(lrnStInfo.getCtnDone());   	}
        return rcmCtnDtlList;
    }

    /**
     * 원클릭학습설정 추천콘텐츠 재구성 학생 추천 저장
     * 
     * @param dtlDto
     * @return int
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames="longCache", key="'tl:' + #optTxbId + ':selectRcmCtnList'", cacheManager="aidtCacheManager"),
            @CacheEvict(cacheNames="longCache", key = "'sl:' + #optTxbId + ':selectRcmCtnStuList'", cacheManager = "aidtCacheManager")
    })
    public int updateRcmCtnInfo(List<TlOneClkSetmRcmCtnDtlDto> dtlList, String optTxbId, String txbId, String mdfrId) {
        int cnt = 0;
        for (TlOneClkSetmRcmCtnDtlDto dtlDto : dtlList) {
            dtlDto.setOptTxbId(optTxbId);
            dtlDto.setDbId(txbId);
            dtlDto.setMdfrId(mdfrId);
            if (dtlDto.getDatYn() == null || dtlDto.getDatYn().equals("")) {
                commonDao.insert(MAPPER_NAMESPACE + "insertRcmCtnInfo", dtlDto);
                cnt++;
            } else {
                commonDao.update(MAPPER_NAMESPACE + "updateRcmCtnInfo", dtlDto);
                cnt++;
            }
        }
        return cnt;
    }

    /**
     * 원클릭학습설정 기능사용설정 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmFncUseDto>
     */
    @Transactional(readOnly = true)
    public TlOneClkSetmFncUseDto selectFncUseSetm(TlOneClkSetmSrhDto srhDto) {
        return commonDao.select(MAPPER_NAMESPACE + "selectFncUseSetm", srhDto);
    }

    /**
     * 원클릭학습설정 기능사용설정 수정
     * 
     * @param fncDto
     * @return int
     */
    @Transactional
    public int updateFncUseSetm(TlOneClkSetmFncUseDto fncDto, String orgnOptTxbId, String kerisUsrId) {
    	int cnt = 0;
    	List<TlOneClkSetmClaDto> claDtoList = fncDto.getClaList(); // 다른 학급 리스트

    	if (claDtoList != null) {
    		for (TlOneClkSetmClaDto claDto : claDtoList) {
                // 다른 학급 리스트 데이터 검증
                if (StringUtils.isEmpty(claDto.getOptTxbId())) {
                    throw new IllegalArgumentException("Invalid optTxbId in TlOneClkSetmClaDto ");
                }
                
                int claCnt = commonDao.select(MAPPER_NAMESPACE + "selectRcstnCnt", Map.of("optTxbId",claDto.getOptTxbId()));
    			CmClaCpLogDto logDto = CmClaCpLogDto.builder().optTxbId(orgnOptTxbId).cpOptTxbId(claDto.getOptTxbId()).cpDvCd("OCK").cpPrcsYn("Y")
    					.backendFlePth("TlOneclkCacheService.updateFncUseSetm").kerisUsrId(kerisUsrId).crtrId(fncDto.getMdfrId()).build();
                
        		if(claCnt > 0) {
                    claDto.setMdfrId(fncDto.getMdfrId());
                    claDto.setOrgnOptTxbId(orgnOptTxbId);
                    // 학습일정관리 저장
                    cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaFncUseSetm", claDto);
        		}else {
        			logDto.setCpPrcsYn("N");
        		}
        		
        		bcCmService.insertClaCpLog(logDto);
            }
    	} else if (claDtoList == null) {
    		cnt += commonDao.update(MAPPER_NAMESPACE + "updateFncUseSetm", fncDto);
    	}
        return cnt;
    }

    /**
     * 원클릭학습설정 내 자료 등록
     * 
     * @param mtrlDto
     * @return int
     */
    @Transactional
	public TlLsnMtrlDto insertRegMtrl(TlOneClkSetmRegMtrlDto mtrlDto) {
    	TlOneClkSetmRegMtrlDto infoDto = commonDao.select(MAPPER_NAMESPACE + "selectMtrlInfo", mtrlDto);
    	mtrlDto.setLsnMtrlNo(infoDto.getLsnMtrlNo()+1);
    	mtrlDto.setRcstnOrdn(infoDto.getRcstnOrdn()+1);
    	mtrlDto.setLrnMtrlId(0);
    	mtrlDto.setDelYn("Y");
    	mtrlDto.setBsMtrlYn("N");
    	mtrlDto.setOpnpYn("Y");
    	
		int cnt = commonDao.insert(MAPPER_NAMESPACE + "insertRegMtrl", mtrlDto);
		
		TlLsnMtrlDto regMtrl = new TlLsnMtrlDto();
		if(cnt > 0) {
			regMtrl = commonDao.select(MAPPER_NAMESPACE + "selectRegMtrl", mtrlDto);
			if("VI".equals(regMtrl.getLsnMtrlTpCd())) {
                regMtrl.setFlePthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, "/"+ regMtrl.getFlePthNm()));	
            }
		}
		return regMtrl;
	}

    /**
     * 원클릭학습설정 자료 삭제
     * 
     * @param mtrlDto
     * @return int
     */
    @Transactional
	public int deleteLsnMtrl(TlOneClkSetmRegMtrlDto mtrlDto) {
		int cnt = 0;

		if(mtrlDto.getLsnMtrlNo()==0) {
			TlOneClkSetmRegMtrlDto infoDto = commonDao.select(MAPPER_NAMESPACE + "selectMtrlInfo", mtrlDto);
	    	mtrlDto.setLsnMtrlNo(infoDto.getLsnMtrlNo()+1);
	    	mtrlDto.setRcstnOrdn(infoDto.getRcstnOrdn()+1);
	    	mtrlDto.setBsMtrlYn("Y");
			cnt = commonDao.insert(MAPPER_NAMESPACE + "insertRegMtrl", mtrlDto);
		} else {
			cnt = commonDao.update(MAPPER_NAMESPACE + "updateRegMtrl", mtrlDto);
		}
		return cnt;
	}
    
    /**
     * 원클릭학습설정 내 자료 게시물 등록
     * 
     * @param mtrlDto
     * @return int
     */
    @Transactional
	public TlLsnMtrlDto insertBlwr(TlOneClkSetmRegMtrlDto mtrlDto) {
    	TlOneClkSetmRegMtrlDto infoDto = commonDao.select(MAPPER_NAMESPACE + "selectMtrlInfo", mtrlDto);
    	mtrlDto.setLsnMtrlNo(infoDto.getLsnMtrlNo()+1);
    	mtrlDto.setRcstnOrdn(infoDto.getRcstnOrdn()+1);
    	mtrlDto.setLsnMtrlTpCd("BW");
    	mtrlDto.setDelYn("Y");
    	mtrlDto.setBsMtrlYn("N");
    	mtrlDto.setOpnpYn("Y");
		int cnt = commonDao.insert(MAPPER_NAMESPACE + "insertRegMtrl", mtrlDto);
		
		TlLsnMtrlDto regMtrl = new TlLsnMtrlDto();
		
		if(cnt > 0) {
			regMtrl = commonDao.select(MAPPER_NAMESPACE + "selectRegMtrl", mtrlDto);
		}
		
		return regMtrl;
	}
    
//    /**
//     * 원클릭학습설정 내 자료 게시물 수정
//     * 
//     * @param mtrlDto
//     * @return int
//     */
//    @Transactional
//	public int updateBlwr(TlOneClkSetmRegMtrlDto mtrlDto) {
//    	
//		return commonDao.insert(MAPPER_NAMESPACE + "updateBlwr", mtrlDto);
//    }
    
    /**
     * 원클릭학습설정 자료 게시물 상세 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmRegMtrlDto>
     */
    @Transactional(readOnly = true)
    public TlOneClkSetmRegMtrlDto selectBlwrInfo(TlOneClkSetmRegMtrlDto mtrlDto) {
    	TlOneClkSetmRegMtrlDto mtrlInfo = new TlOneClkSetmRegMtrlDto();
    	
    	if(mtrlDto.getLsnMtrlNo() == 0) {
    		mtrlInfo = commonDao.select(MAPPER_NAMESPACE + "selectTcrBlwrInfo", mtrlDto);
    	} else {
    		mtrlInfo = commonDao.select(MAPPER_NAMESPACE + "selectBlwrInfo", mtrlDto);
    		if(mtrlInfo == null) {
    			throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "게시글 정보를 확인할 수 없습니다."); 
    		}
    	}
    	
    	// 첨부파일 조회
    	int annxId = mtrlInfo.getAnnxId();
		if(annxId > 0) {
        	List<TlAnnxFleDto> fleList = commonDao.selectList(MAPPER_NAMESPACE + "selectAnnxFleList", annxId);
        	mtrlInfo.setFleList(fleList);
    	}
        return mtrlInfo;
    }
    
    
    /**
     * 원클릭학습설정 번호 노출 목록 조회
     * 
     * @param srhDto
     * @return List<TlOneClkSetmTocDto>
     */
    @Transactional
    public int updateLrnSortRcstnList(TlOneClkSetmSrhDto srhDto) {
    	List<TlOneClkSetmTocDto> list = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnSortList", srhDto);
    	List<TlOneClkSetmTocDto> updatelist=new ArrayList<TlOneClkSetmTocDto>();
    	updateLrnSortRcstn(list, 1, null,updatelist);
    	
    	int updateCnt=0;
    	
    	for(TlOneClkSetmTocDto dto :  updatelist) {
    		updateCnt+=commonDao.update(MAPPER_NAMESPACE + "updateLrnSort", dto);
    	}
    	return updateCnt;
    }

    @Transactional
    public int updateLrnSortRcstn(List<TlOneClkSetmTocDto> list,int dpth,String urnkLrmpNodId,List<TlOneClkSetmTocDto> updatelist) {
    	log.debug("dpth : " + dpth + " urnkLrmpNodId : " + urnkLrmpNodId );
    	if(dpth>4) {
    		return 0;
    	}
    	int rcstnNo=0;
    	for(TlOneClkSetmTocDto dto :  list) {
    		if(dpth==1) {
    			if(dto.getDpth()==1) {
    				rcstnNo++;		
    				dto.setRcstnNo(rcstnNo);
    				updateLrnSortRcstn(list, 4, dto.getLrmpNodId(),updatelist);
    				
    				updatelist.add(dto);
    			}
    		}else {
    			if(dto.getUrnkLrmpNodId() != null) {
        			if(dto.getDpth()==dpth && dto.getUrnkLrmpNodId().equals(urnkLrmpNodId)) {
        				rcstnNo++;		
        				dto.setRcstnNo(rcstnNo);
        				if(dpth<4) {
    	    				int nextDpt = dto.getDpth()+1;
    	    				updateLrnSortRcstn(list, nextDpt , dto.getLrmpNodId(),updatelist);
        				}
        				updatelist.add(dto);
        			}	
    			}
    		}
    	}
    	return rcstnNo;
    }

    /**
     * 원클릭학습설정 학습완료 수 조회
     * 
     */
    @Transactional(readOnly = true)
    public int selectLrnAtvCnt(String optTxbId, String lrmpNodId) {
        return commonDao.select(MAPPER_NAMESPACE + "selectLrnAtvCnt", Map.of("optTxbId", optTxbId, "lrmpNodId", lrmpNodId));
    }
    
    /**
     * 원클릭학습설정 선생님 콘텐츠 추가
     * 
     * @param mtrlDto
     * @return int
     */
    @Transactional
	public TlOneClkSetmAtvDto insertTcrCtnReg(TlOneClkSetmRegMtrlDto mtrlDto) {
    	int totCnt = commonDao.select(MAPPER_NAMESPACE + "selectTcrCtnCnt", mtrlDto);
    	mtrlDto.setTcrRegCtnId("TC" + totCnt);
    	
    	int cnt = 0;
    	cnt += commonDao.insert(MAPPER_NAMESPACE + "insertTcrCtnReg", mtrlDto);	
    	cnt += commonDao.insert(MAPPER_NAMESPACE + "insertTcrCtnRegMpn", mtrlDto);
    	
    	TlOneClkSetmAtvDto atvDto = new TlOneClkSetmAtvDto();
    	
    	if(cnt > 0) {
    		EaAsnTcrDto asnDto = new EaAsnTcrDto();
    		int atvCnt = this.selectLrnAtvCnt(mtrlDto.getOptTxbId(), mtrlDto.getLrmpNodId());
            asnDto.setOptTxbId(mtrlDto.getOptTxbId());
            asnDto.setLuNodId(mtrlDto.getLluNodId());
            asnDto.setTcNodId(mtrlDto.getLrmpNodId());
            asnDto.setTtlLrnCnt(atvCnt);
//            eaAsnComService.updateTlAsn(asnDto);
            
            atvDto = commonDao.select(MAPPER_NAMESPACE + "selectRegCtn", mtrlDto);
            if("VI".equals(atvDto.getCtnTpCd())) {
                atvDto.setAnnxFlePthNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, "/"+atvDto.getAnnxFlePthNm()));	
            }
        }
            
		return atvDto;
	}

    /**
     * 원클릭학습설정 타교과서 조회
     * 
     * @param String
     * @return List<TlOneClkSetmExtcmpTxbDto>
     */
    @Transactional
	public List<TlOneClkSetmExtcmpTxbDto> selectExtcmpTxbList(String optTxbId) {
		List<TlOneClkSetmExtcmpTxbDto> extcmpTxbList = commonDao.selectList(MAPPER_NAMESPACE + "selectExtcmpTxbList", Map.of("optTxbId",optTxbId));
		if(!extcmpTxbList.isEmpty() || extcmpTxbList != null || extcmpTxbList.size() != 0) {
			extcmpTxbList.forEach(list -> {
				List<TlOneClkSetmExtcmpTxbInfoDto> txbInfoList = commonDao.selectList(MAPPER_NAMESPACE + "selectExtcmpTxbInfoList", Map.of("optTxbId",optTxbId,"txbId",list.getTxbId(),"orgnYn",list.getOrgnYn()));
				if(!txbInfoList.isEmpty() || txbInfoList != null || txbInfoList.size() != 0) {
					list.setExtcmbTxbInfoList(txbInfoList);
				}
			});
		}
		return extcmpTxbList;
	}

    /**
     * 원클릭학습설정 타사교과서 사용 체크
     * 
     * @param String
     * @return String
     */
	public String selectExtcmpTxbChk(String optTxbId) {
		Map<String, Object> mpnTxbId = commonDao.select(MAPPER_NAMESPACE + "selectExtcmpTxbMpn", Map.of("optTxbId",optTxbId));
		return mpnTxbId.get("EXTCMP_TXB_ID").toString();
	}
	
	/**
     * 원클릭학습설정 외부활동설정 조회
     * 
     * @param srhDto
     * @return TlLrnwExtAtvSetm
     */
    @Transactional(readOnly = true)
    public TlLrnwExtAtvSetm selectExtAtvSetm(TlOneClkSetmSrhDto srhDto) {
    	TlLrnwExtAtvSetm extAtv = commonDao.select(MAPPER_NAMESPACE + "selectExtAtvSetm", Map.of("param", srhDto));
    	
    	if(extAtv == null) {
    		extAtv = TlLrnwExtAtvSetm.builder().clsPpngUseYn("N").clsArchvUseYn("N").padlUseYn("N").canvUseYn("N").mirCanvUseYn("N").gglDocUseYn("N").build();
    	}
        
    	extAtv.setExtrLink(commonDao.selectList(MAPPER_NAMESPACE + "selectExtLink", Map.of("param", srhDto)));

        return extAtv;
    }

	/**
     * 원클릭학습설정 외부활동설정 수정
     * 
     * @param extDto
     * @return int
     */
    @Transactional
    public int updateExtAtvSetm(TlLrnwExtAtvSetm extDto, String orgnOptTxbId, String kerisUsrId) {
    	int cnt = 0;
    	List<TlOneClkSetmClaDto> claDtoList = extDto.getClaList(); // 다른 학급 리스트

    	if (claDtoList != null) {
    		TlOneClkSetmSrhDto srhDto = new TlOneClkSetmSrhDto();
    		srhDto.setOptTxbId(orgnOptTxbId);
    		
    		for (TlOneClkSetmClaDto claDto : claDtoList) {
                // 다른 학급 리스트 데이터 검증
                if (StringUtils.isEmpty(claDto.getOptTxbId())) {
                    throw new IllegalArgumentException("Invalid optTxbId in TlOneClkSetmClaDto ");
                }
                
                int claCnt = commonDao.select(MAPPER_NAMESPACE + "selectRcstnCnt", Map.of("optTxbId",claDto.getOptTxbId()));
                
    			CmClaCpLogDto logDto = CmClaCpLogDto.builder().optTxbId(orgnOptTxbId).cpOptTxbId(claDto.getOptTxbId()).cpDvCd("OCK").cpPrcsYn("Y")
    					.backendFlePth("TlOneclkCacheService.updateExtAtvSetm").kerisUsrId(kerisUsrId).crtrId(extDto.getMdfrId()).build();
                
        		if(claCnt > 0) {
                    claDto.setMdfrId(extDto.getMdfrId());
                    claDto.setOrgnOptTxbId(orgnOptTxbId);

                    cnt += commonDao.update(MAPPER_NAMESPACE + "updateAntClaExtAtvLink", claDto);
                    commonDao.delete(MAPPER_NAMESPACE + "deleteAntClaExtAtvLink", claDto);
                    cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAntClaExtAtvLink", Map.of("orgnOptTxbId", orgnOptTxbId, "optTxbId", claDto.getOptTxbId(), "usrId", claDto.getMdfrId()));
        		} else {
        			logDto.setCpPrcsYn("N");
        		}
        		bcCmService.insertClaCpLog(logDto);
            }
    		return cnt;
    	} else if (claDtoList == null) {
    		List<TlLrnwExtrLink> linkList = extDto.getExtrLink();
    		cnt += commonDao.update(MAPPER_NAMESPACE + "updateExtAtvSetm", extDto);
    		if(linkList.size() != 0 && linkList != null) {   			
    			for(TlLrnwExtrLink list : linkList) {
    				list.setOptTxbId(extDto.getOptTxbId());
    				list.setMdfrId(extDto.getMdfrId());
    				if("Y".equals(list.getOrgnLink())) {
    					if("Y".equals(list.getDelYn())) {
        					cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteExtAtvLink", list);
    					} else {
        					cnt += commonDao.update(MAPPER_NAMESPACE + "updateExtAtvLink", list);
    					}
    				} else {
    					int linkNo = commonDao.select(MAPPER_NAMESPACE + "selectExtrLinkNo", list);
    					list.setExtrLinkNo(linkNo +1);
    					cnt += commonDao.insert(MAPPER_NAMESPACE + "insertExtAtvLink", list);
    				}
    			}
    		}
    	}
        return cnt;
    }

	public int selectTotStuCnt(String orgnOptTxbId) {
		int cnt = 0;
		cnt = commonDao.select(MAPPER_NAMESPACE + "selectTotStuCnt", orgnOptTxbId);
		return cnt;
	}

	@Transactional
	public List<TlOneClkSetmAtvDto> insertTcrAtvReg(List<TlOneClkSetmAtvDto> atvList, String optTxbId, String usrId, String txbId) {
		List<TlOneClkSetmAtvDto> returnList = new ArrayList<TlOneClkSetmAtvDto>();
		
		int totCnt = commonDao.select(MAPPER_NAMESPACE + "selectTcrCtnCnt");
		int rcstnOrdn = commonDao.select(MAPPER_NAMESPACE + "selectAtvMinRcstnOrdn", atvList.get(0));

		for(TlOneClkSetmAtvDto atv : atvList) {
			atv.setDbId(txbId);
			atv.setMdfrId(usrId);
			atv.setOptTxbId(optTxbId);
			atv.setRcstnOrdn(rcstnOrdn);
			atv.setCpYn("Y");
			atv.setLrmpNodId(atv.getCpLrmpNodId());
			atv.setLrnStpId(atv.getCpLrnStpId());
			
			if("N".equals(atv.getTcrCtnYn())) {
				String chkAtvCpInfo = commonDao.select(MAPPER_NAMESPACE + "chkAtvCpInfo", atv);

				if(chkAtvCpInfo == null || "".equals(chkAtvCpInfo)){
					atv.setTcrRegCtnId("TC" + totCnt);
					commonDao.insert(MAPPER_NAMESPACE + "insertTcrAtvReg", atv);
					totCnt++;
				} else {
					atv.setTcrRegCtnId(chkAtvCpInfo);
				}
			};
			
			int cnt = commonDao.insert(MAPPER_NAMESPACE + "insertTcrAtvRegMpn", atv);
			
			rcstnOrdn--;
			
			if(cnt > 0) {
				TlOneClkSetmAtvDto returnAtv = new TlOneClkSetmAtvDto();
				if("N".equals(atv.getTcrCtnYn())) {
					returnAtv = commonDao.select(MAPPER_NAMESPACE + "selectCpAtv", atv);
					returnAtv.setLrnAtvThbPth(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, returnAtv.getLrnAtvThbPth()));
				} else {
					returnAtv = commonDao.select(MAPPER_NAMESPACE + "selectCpCtn", atv);	
				}
				returnList.add(returnAtv);
			};
		};
		return returnList;
	}

}
