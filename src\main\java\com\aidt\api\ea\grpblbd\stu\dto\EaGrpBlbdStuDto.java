package com.aidt.api.ea.grpblbd.stu.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-23
 * @modify date 2024-01-23
 * @desc 모둠 게시판 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaGrpBlbdStuDto {

	@Parameter(name="순번")
	private int rowNum;

	@Parameter(name="게시판ID")
	private int blbdId;

	@Parameter(name="모둠ID")
	private int grpId;

	@Parameter(name="모둠명")
	private String grpClNm;

	@Parameter(name="모둠팀ID")
	private int grpTemId;

	@Parameter(name="모둠팀명")
	private String grpTemNm;

	@Parameter(name="모둠팀장여부")
	private String grpTmgrYn;

	@Parameter(name="학생사용자ID")
	private String usrId;

	@Parameter(name="사용자명")
	private String usrNm;

	@Parameter(name="과제ID")
	private int asnId;

	@Parameter(name="게시판명")
	private String blbdNm;

	@Parameter(name="생성자ID")
	private String crtrId;

	@Parameter(name="생성자이름")
	private String crtrNm;

	@Parameter(name="생성일시")
	private String crtDtm;

	@Parameter(name="수정자ID")
	private String mdfrId;

	@Parameter(name="수정일시")
	private String mdfDtm;

	@Parameter(name="데이터베이스ID")
	private String dbId;

	@Parameter(name="게시글ID")
	private int blwrId;

	@Parameter(name="게시글제목명")
	private String blwrTitlNm;

	@Parameter(name="게시글내용")
	private String blwrCn;

	@Parameter(name="공지사항 여부")
	private String annlstYn;

	@Parameter(name="댓글ID")
	private int ucwrId;

	@Parameter(name="댓글내용")
	private String ucwrCn;

	@Parameter(name="상위댓글ID")
	private int urnkUcwrId;

	@Parameter(name="상위게시글ID")
	private int urnkBlwrId;

	@Parameter(name="상위게시판ID")
	private int urnkBlbdId;

	@Parameter(name="댓글깊이")
	private int ucwrDpth;

	@Parameter(name="구분자")
	private String flag;

	@Parameter(name="유저구분코드")
	private String usrTpCd;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="댓글개수")
	private int commentCnt;

	@Parameter(name="댓글ID_temp")
	private int tempUcwrId;
	
	@Parameter(name="생성일시")
	private String crtDtmNm;

	/** 파일 관련 VO **/
	@Parameter(name="첨부파일ID")
	private int annxFileId;

	@Parameter(name="첨부ID")
	private int annxId;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="첨부파일명")
	private String annxFileNm;

	@Parameter(name="첨부파일원본명")
	private String annxFileOrglNm;

	@Parameter(name="첨부파일확장자명")
	private String annxFileFextNm;

	@Parameter(name="첨부파일사이즈")
	private int annxFileSize;

	@Parameter(name="첨부파일경로명")
	private String annxFilePathNm;
	
	@Parameter(name="문서뷰어ID")
	private String docViId;

	@Parameter(name="학생ID")
	private String stuUsrId;
	
	@Parameter(name="작성자코드")
	private String crtrTpCd;
	
	private int pageNo;
	private int pageSize;
	
	@Parameter(name="총 개수")
	private int totalCnt;


	/* session에 들어가는 유저 정보 (추후 삭제 예졍) */
	private String sessionClaId;
	private String sessionOptTxbId;
	private String sessionRole;
	private String sessionTxbId;
	private String sessionUsrId;
	private String sessionUsrNm;
	private String sessionUsrTpCd;
	/* session에 들어가는 유저 정보 (추후 삭제 예졍) */
}