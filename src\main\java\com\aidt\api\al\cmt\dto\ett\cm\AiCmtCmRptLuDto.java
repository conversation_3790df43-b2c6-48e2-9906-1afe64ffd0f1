package com.aidt.api.al.cmt.dto.ett.cm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtCmRptLuDto {

    private String strthTpc;

    private String wknsTpc;

    @Builder.Default
    private String usrTpCd = "ST";

}
