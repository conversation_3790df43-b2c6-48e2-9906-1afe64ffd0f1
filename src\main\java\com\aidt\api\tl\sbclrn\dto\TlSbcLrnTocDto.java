package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:46:01
 * @modify date 2024-01-04 14:46:01
 * @desc TlStuSbcLrnToc Dto 단원목차
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnTocDto {
        /** 운영교과서ID */
        @Parameter(name="운영교과서ID")
        private String optTxbId;
        /** LCMS교과서_지식맵_키 */
        @Parameter(name="LCMS교과서_지식맵_키")
        private String lrmpNodId;
        /** LCMS교과서_지식맵_부모_노드_키 */
        @Parameter(name="LCMS교과서_지식맵_부모_노드_키")
        private String urnkLrmpNodId;
        /** 지식맵노드명 */
        @Parameter(name="지식맵노드명")
        private String lrmpNodNm;
        /** 상위노드명(대단원) */
        @Parameter(name="상위노드명(대단원)")
        private String lluNodNm;
        /** 상위노드ID(대단원ID) */
        @Parameter(name="상위노드ID(대단원)")
        private String lluNodId;
        /** 대단원No */
        @Parameter(name="대단원No")
        private String lluNodNo;
        /** 사용여부 */
        @Parameter(name="사용여부")
        private String useYn;
        /** 잠금여부 */
        @Parameter(name="잠금여부")
        private String lcknYn;
        /** 깊이 */
        @Parameter(name="깊이")
        private String dpth;
        /** 학습활동총건수 */
        @Parameter(name="학습활동총건수")
        private String cntTot;
        /** 학습활동완료건수 */
        @Parameter(name="학습활동완료건수")
        private String cntCl;
        /** 재구성순서 */
        @Parameter(name="재구성순서")
        private int rcstnOrdn;
 
}