package com.aidt.api.sl.splrn.dto;

import java.util.Date;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> eunhye11
 * @email : <EMAIL>
 * @create : date 2024-02-19 17:13:26
 * @modify : date 2024-02-19 17:13:26
 * @desc : 특별학습 마지막 시점 정보조회
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlSpLrnLastPrgsDto {

	@Parameter(name="특별학습ID")
	private String spLrnId;
	
	@Parameter(name="특별학습상위노드ID")
	private String urnkSpLrnNodId;

	@Parameter(name="특별학습 노드ID")
	private String spLrnNodId;

	@Parameter(name="콘텐츠ID")
	private String spLrnCtnId;

	@Parameter(name="특별학습명")
	private String spLrnNm;

	@Parameter(name="특별학습노드명")
	private String spLrnNodNm;
	
	@Parameter(name="특별학습목표")
	private String lrnGoalCn;

	@Parameter(name="콘텐츠 총건수")
	private int spLrnCtnTotCnt;

	/*
	 * 학습한 이력이 없는 경우 첫번째 CtnId 마지막으로 학습한 콘텐츠ID가 학습완료인 경우는 다음 콘텐츠 ID를 설정
	 */
	@Parameter(name="시작특별학습콘텐츠ID")
	private String strSpLrnCtnId;

	@Parameter(name="원본정렬순서")
	private int srtOrdn;
	
	@Parameter(name="상태코드")
	private String lrnStCd;

	@Parameter(name="수정일시")
	private Date mdfDtm;

	/** 마지막학습여부 */
	@Parameter(name="마지막학습여부")
	private String lastLrnYn;
}