package com.aidt.api.al.pl.cm.cache;

import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class cacheService {

    @Caching(evict = {
            @CacheEvict(cacheNames = "shortCache", key = "'al:' + #dto.optTxbId + ':selectEnMluList:' + #dto.usrId", cacheManager = "aidtCacheManager"),
            @CacheEvict(cacheNames = "shortCache", key = "'al:' + #dto.optTxbId + ':selectMluLstStuInq:' + #dto.usrId", cacheManager = "aidtCacheManager"),
            @CacheEvict(cacheNames = "shortCache", key = "'al:' + #dto.optTxbId + ':selectIansQtmSeUgCntEn:' + #dto.usrId", cacheManager = "aidtCacheManager"),
            @CacheEvict(cacheNames = "shortCache", key = "'al:' + #dto.optTxbId + ':selectIansQtmSeUgCntMa:' + #dto.usrId", cacheManager = "aidtCacheManager")
    })
    public Integer cacheDelete(AiRcmTsshQtmDto dto){
        return 0;
    }

}
