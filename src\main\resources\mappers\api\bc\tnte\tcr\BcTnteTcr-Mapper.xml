<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.tnte.tcr">
	<!-- 
		2025.02.03 LRN_ATV_ID 컬럼표준화 BIGINT 타입
		LMS_LRM.MY_TNTE.LRN_ATV_ID는 문자열도 들어가는 타입으로 LRN_ATV_INFO로 컬럼명 변경됨에 따라 쿼리문에 사용된 LRN_ATV_ID -> LRN_ATV_INFO 수정처리
	-->

	<!-- 2024-07-11 액티비티 단위 학생별 필기 목록 조회(IN 학습창) -->
	<!-- 2024-11-11 학습창-학생별현황 조회 기존 실시간 모니터링 데이터 조회에서 CM_USR 로 변경  -->
	<select id="selectTcrTnteInfoInLwList" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcTnteDto">
		/** BcCm-Mapper.xml - selectTcrTnteInfoInLwList */
		SELECT A.USR_NM
			 , ATV.LRMP_NOD_ID
			 , ATV.LRN_ATV_ID
			 , A.KERIS_USR_ID AS KERIS_USR_ID
			 , A.USR_ID AS LRN_USR_ID
			 , ATV.LRN_ST_CD	/*학습상태코드 CL:학습완료, DL:학습충, NL미학습*/
			 , ATV.LRN_TM_SCNT	/*학습시간*/
			 , MT.TNTE_ID
			 , MT.TC_NOD_ID
			 , MT.LRN_ATV_INFO AS LRN_ATV_ID
			 , ROW_NUMBER() OVER (ORDER BY A.STU_NO) AS ROW_NUM
		  FROM LMS_LRM.CM_USR A
		  LEFT OUTER JOIN (
		<choose>
		    <when test="lrnTpCd != null and lrnTpCd == 'SL'">
		        SELECT S.SP_LRN_ID AS LRMP_NOD_ID
  					 , S.SP_LRN_CTN_ID AS LRN_ATV_ID
  					 , S.LRN_ST_CD 
  					 , S.LRN_TM_SCNT
  					 , S.LRN_USR_ID
  				  FROM LMS_LRM.SL_SP_LRN_PGRS_ST S
  				 WHERE OPT_TXB_ID = #{optTxbId}                /*운영교과서ID*/
					  AND SP_LRN_ID =  #{spLrnId}            /*특별학습ID*/
					  AND SP_LRN_CTN_ID =  #{lrnAtvId}         /*컨텐츠ID*/    
		    </when>
		    <when test="lrnTpCd != null and lrnTpCd == 'TL'">
		        SELECT C.LRMP_NOD_ID  
				     , C.LRN_ATV_ID 
				     , C.LRN_ST_CD	/* CL:학습완료, DL:학습충, NL미학습*/
				     , C.LRN_TM_SCNT
				     , C.LRN_USR_ID
      			  FROM LMS_LRM.TL_SBC_LRN_ATV_ST C
			     WHERE C.OPT_TXB_ID = #{optTxbId}	
			       AND C.LRMP_NOD_ID = #{tcNodId}
			       AND C.LRN_ATV_ID = #{lrnAtvId}
			     UNION ALL
			    SELECT M.LRMP_NOD_ID  
			         , M.TCR_REG_CTN_ID AS LRN_ATV_ID 
			         , T.LRN_ST_CD
			         , T.LRN_TM_SCNT
			         , T.LRN_USR_ID
			  	  FROM LMS_LRM.TL_TCR_REG_CTN_MPN M
				 INNER JOIN LMS_LRM.TL_TCR_REG_CTN C
				 	ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
				 INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN R
					ON M.OPT_TXB_ID = R.OPT_TXB_ID
				   AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
				 INNER JOIN LMS_CMS.BC_LRN_STP S
					ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
				   AND M.LRN_STP_ID = S.LRN_STP_ID
				   AND S.DEL_YN = 'N'
				  LEFT JOIN LMS_LRM.TL_TCR_REG_CTN_ST T
					ON M.OPT_TXB_ID = T.OPT_TXB_ID
				   AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
				 WHERE M.OPT_TXB_ID = #{optTxbId}  
				   AND M.LRMP_NOD_ID = #{tcNodId} 
				   AND M.TCR_REG_CTN_ID = #{lrnAtvId}
				   AND M.DEL_YN = 'N'
				   AND M.USE_YN = 'Y'
		    </when>
		</choose>
			) ATV ON A.USR_ID = atv.lrn_usr_id
		LEFT OUTER JOIN LMS_LRM.MY_TNTE MT /*노트*/  
		  ON MT.LRN_USR_ID = A.USR_ID 
		 AND MT.LRN_TP_CD = #{lrnTpCd} 
		 AND MT.TC_NOD_ID = #{tcNodId}  
		 AND MT.LRN_ATV_INFO = #{lrnAtvId}
		 AND MT.TNTE_DV_CD = #{tnteDvCd}
	   WHERE A.USR_TP_CD = 'ST'
		 AND A.CLA_ID = #{claId}
	   ORDER BY A.STU_NO
	</select>

	<!-- 필기 조회 -->
	<select id="selectTnteList" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcTnteDto">
		/** BcTnteTcr-Mapper.xml - selectTnteList */
		<include refid="api.bc.common.pagingHeader"/>
		SELECT
			TNTE.TNTE_ID,
			B.RCSTN_ORDN,
			TNTE.TNTE_DV_CD,
			TNTE.LRN_USR_ID,
			TNTE.LRN_TP_CD,
			(
				SELECT B.CM_CD_NM FROM LMS_LRM.CM_CM_CD B WHERE B.URNK_CM_CD = 'LRN_TP_CD' AND B.CM_CD = TNTE.LRN_TP_CD
			) AS LRN_TP_CD_NM,
			TNTE.OPT_TXB_ID,
			TNTE.LU_NOD_ID,
			TNTE.TC_NOD_ID,
			TNTE.LRN_ATV_INFO AS LRN_ATV_ID,
			B.LRMP_NOD_NM AS LLU_NM,
			C.LRMP_NOD_NM AS TC_NM,
			(
				SELECT D.LRN_ATV_NM FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN D
				WHERE D.OPT_TXB_ID = TNTE.OPT_TXB_ID
				AND D.LRN_ATV_ID = TNTE.LRN_ATV_INFO
			) AS LRN_ATV_NM,
			TNTE.CDN_PTH_NM,
			'' as checked,
			DATE_FORMAT(TNTE.CRT_DTM, '%Y.%m.%d') AS CRT_DTM
		FROM
			LMS_LRM.MY_TNTE TNTE
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON TNTE.OPT_TXB_ID = B.OPT_TXB_ID
												  AND TNTE.LU_NOD_ID = B.LRMP_NOD_ID
												  AND B.DPTH = 1
												  AND B.LCKN_YN = 'N'
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C ON TNTE.TC_NOD_ID = C.LRMP_NOD_ID
												  AND TNTE.OPT_TXB_ID = C.OPT_TXB_ID
												  AND C.DPTH = 4
												  AND C.LCKN_YN = 'N'
		<where>
			<if test = 'optTxbId != null and !"".equals(optTxbId)'>
				AND TNTE.OPT_TXB_ID = #{optTxbId}
			</if>
			<if test = 'luNodId != null and !"".equals(luNodId)'>
				AND TNTE.LU_NOD_ID = #{luNodId}
			</if>
			<if test = 'tcNodId != null and !"".equals(tcNodId)'>
				AND TNTE.TC_NOD_ID = #{tcNodId}
			</if>
			<if test = 'lrnAtvId != null and !"".equals(lrnAtvId)'>
				AND TNTE.LRN_ATV_INFO = #{lrnAtvId}
			</if>
			<if test = 'lrnUsrId != null and !"".equals(lrnUsrId)'>
				AND TNTE.LRN_USR_ID = #{lrnUsrId}
			</if>
			<if test = 'tnteDvCd != null and !"".equals(tnteDvCd)'>
				AND TNTE.TNTE_DV_CD = #{tnteDvCd}
			</if>
			<if test = 'scWord != null and !"".equals(scWord)'>
				<![CDATA[
					AND (B.LRMP_NOD_NM LIKE CONCAT(#{scWord},'%') OR C.LRMP_NOD_NM LIKE CONCAT(#{scWord},'%'))
				]]>
			</if>
		</where>
		<choose>
			<when test='srhSrt != null and !"".equals(srhSrt)'>
				<choose>
					<when test='srhSrt == "ins" '>
						ORDER BY TNTE.CRT_DTM
					</when>
					<otherwise>
						ORDER BY TNTE.LU_NOD_ID
					</otherwise>
				</choose>
				<if test='srtInfo == "desc" '>
					desc
				</if>
				<if test='srtInfo == "asc" '>
					asc
				</if>
			</when>
			<otherwise>
				ORDER BY B.RCSTN_ORDN
				<if test='srtInfo == "desc" '>
					desc
				</if>
				<if test='srtInfo == "asc" '>
					asc
				</if>
			</otherwise>
		</choose>

		<include refid="api.bc.common.pagingFooter"/>
	</select>

	<!-- 필기 상세 조회 -->
	<select id="selectTnteDtl" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcTnteDto">
		/** BcTnteTcr-Mapper.xml - selectTnteDtl */
		SELECT
			TNTE.TNTE_ID,
			TNTE.TNTE_DV_CD,
			TNTE.LRN_USR_ID,
			TNTE.LRN_TP_CD,
			(
				SELECT B.CM_CD_NM FROM LMS_LRM.CM_CM_CD B WHERE B.URNK_CM_CD = 'LRN_TP_CD' AND B.CM_CD = TNTE.LRN_TP_CD
			) AS LRN_TP_CD_NM,
			TNTE.OPT_TXB_ID,
			TNTE.LU_NOD_ID,
			TNTE.TC_NOD_ID,
			TNTE.LRN_ATV_INFO AS LRN_ATV_ID,
			(
				SELECT D.LRN_ATV_NM FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN D
				WHERE D.OPT_TXB_ID = TNTE.OPT_TXB_ID
				AND D.LRN_ATV_ID = TNTE.LRN_ATV_INFO
			) AS LRN_ATV_NM,
			TNTE.CDN_PTH_NM,
			TNTE.ANNX_ID,
			TNTE.ANNX_FLE_ID,
			DATE_FORMAT(TNTE.CRT_DTM, '%Y.%m.%d') AS CRT_DTM,
			DATE_FORMAT(TNTE.CRT_DTM, '%H:%i') AS CRT_TM
		FROM
			LMS_LRM.MY_TNTE TNTE
		<where>
			<if test='tnteId != null and tnteId neq ""'>
			AND TNTE.TNTE_ID = #{tnteId}
			</if>
			<if test='optTxbId != null and optTxbId neq ""'>
			AND TNTE.OPT_TXB_ID = #{optTxbId}
			</if>
			<if test='lrnTpCd != null and lrnTpCd neq ""'>
			AND TNTE.LRN_TP_CD = #{lrnTpCd}
			</if>
			<if test='lrnAtvId != null and lrnAtvId neq ""'>
			AND TNTE.LRN_ATV_INFO = #{lrnAtvId}
			</if>
			<if test='lrnUsrId != null and lrnUsrId neq ""'>
			AND TNTE.LRN_USR_ID = #{lrnUsrId}
			</if>
			<if test='tnteDvCd != null and tnteDvCd neq ""'>
			AND TNTE.TNTE_DV_CD = #{tnteDvCd}
			</if>
		</where>
		LIMIT 1
	</select>

	<!-- 필기 상세 조회(학습창) -->
	<select id="selectLayoutTnteDtl" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcTnteDto">
		/** BcTnteTcr-Mapper.xml - selectLayoutTnteDtl */
		SELECT
			TNTE.TNTE_ID,
			TNTE.TNTE_DV_CD,
			TNTE.LRN_USR_ID,
			TNTE.LRN_TP_CD,
			(
				SELECT B.CM_CD_NM FROM LMS_LRM.CM_CM_CD B WHERE B.URNK_CM_CD = 'LRN_TP_CD' AND B.CM_CD = TNTE.LRN_TP_CD
			) AS LRN_TP_CD_NM,
			TNTE.OPT_TXB_ID,
			TNTE.LU_NOD_ID,
			TNTE.TC_NOD_ID,
			TNTE.LRN_ATV_INFO AS LRN_ATV_ID,
			B.LRMP_NOD_NM AS LLU_NM,
			C.LRMP_NOD_NM AS TC_NM,
			(
				SELECT D.LRN_ATV_NM FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN D
				WHERE D.OPT_TXB_ID = TNTE.OPT_TXB_ID
				AND D.LRN_ATV_ID = TNTE.LRN_ATV_INFO
			) AS LRN_ATV_NM,
			TNTE.CDN_PTH_NM,
			DATE_FORMAT(TNTE.CRT_DTM, '%Y.%m.%d') AS CRT_DTM,
			DATE_FORMAT(TNTE.CRT_DTM, '%H:%i') AS CRT_TM
		FROM
			LMS_LRM.MY_TNTE TNTE
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON TNTE.OPT_TXB_ID = B.OPT_TXB_ID
												  AND TNTE.LU_NOD_ID = B.LRMP_NOD_ID
												  AND B.DPTH = 1
												  AND B.LCKN_YN = 'N'
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C ON TNTE.TC_NOD_ID = C.LRMP_NOD_ID
												  AND TNTE.OPT_TXB_ID = C.OPT_TXB_ID
												  AND C.DPTH = 4
												  AND C.LCKN_YN = 'N'
		<where>
			AND TNTE.OPT_TXB_ID = #{optTxbId}
			AND TNTE.LRN_TP_CD = #{lrnTpCd}
			AND TNTE.LRN_ATV_INFO = #{lrnAtvId}
			AND TNTE.LRN_USR_ID = #{lrnUsrId}
			<if test = 'tnteDvCd != null and !"".equals(tnteDvCd)'>
				AND TNTE.TNTE_DV_CD = #{tnteDvCd}
			</if>
		</where>
	</select>

	<!-- 필기 등록 전 확인 -->
	<select id="selectTnteCheck" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" resultType="int">
		/** BcTnteStu-Mapper.xml - selectTnteCheck */
		SELECT
			COALESCE(SUM(A.TNTE_ID), 0) AS TNTE_ID
		FROM
			LMS_LRM.MY_TNTE A
		<where>
			AND A.OPT_TXB_ID = #{optTxbId}
			AND A.LRN_TP_CD = #{lrnTpCd}
			AND A.LRN_ATV_INFO = #{lrnAtvId}
			AND A.LRN_USR_ID = #{lrnUsrId}
			<if test = 'tnteDvCd != null and !"".equals(tnteDvCd)'>
				AND A.TNTE_DV_CD = #{tnteDvCd}
			</if>
		</where>
	</select>

	<!-- 필기 등록 -->
	<insert id="insertTnte" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" useGeneratedKeys="true" keyProperty="tnteId">
		/** BcTnteStu-Mapper.xml - insertTnte */
		INSERT INTO LMS_LRM.MY_TNTE(
			TNTE_DV_CD, LRN_USR_ID, LRN_TP_CD, OPT_TXB_ID, LU_NOD_ID, TC_NOD_ID, LRN_ATV_INFO
			, CDN_PTH_NM, ANNX_ID, ANNX_FLE_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{tnteDvCd}
			, #{lrnUsrId}
			, #{lrnTpCd}
			, #{optTxbId}
			, #{luNodId}
			, #{tcNodId}
			, #{lrnAtvId}
			, #{cdnPthNm}
			, #{annxId}
			, #{annxFleId}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 필기 수정 -->
	<update id="updateTnte" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto" >
		/** BcTnteStu-Mapper.xml - updateTnte */
		UPDATE LMS_LRM.MY_TNTE
		SET
			CDN_PTH_NM = #{cdnPthNm}
			, ANNX_ID = #{annxId}
			, ANNX_FLE_ID = #{annxFleId}
			, MDFR_ID = #{mdfrId}
			, MDF_DTM = NOW()
		WHERE
			TNTE_DV_CD = #{tnteDvCd}
		AND LRN_TP_CD = #{lrnTpCd}
		AND LRN_USR_ID = #{lrnUsrId}
		AND OPT_TXB_ID = #{optTxbId}
		AND LU_NOD_ID = #{luNodId}
		AND TC_NOD_ID = #{tcNodId}
		AND LRN_ATV_INFO = #{lrnAtvId}
	</update>


	<!-- 필기 삭제 -->
	<delete id="deleteTnte" parameterType="com.aidt.api.bc.tnte.dto.BcTnteDto">
		/** BcTnteStu-Mapper.xml - deleteTnte */
		DELETE FROM LMS_LRM.MY_TNTE
		WHERE TNTE_ID IN (
		<foreach collection="tnteIds" item="tnteId" separator=", ">
			#{tnteId}
		</foreach>
		)
		AND LRN_USR_ID = #{lrnUsrId}
	</delete>
	
	
	
	
	
	

	
	<!-- 2024-07-16 노트 구분 selectBox 조회(저장되어 있는 케이스만 보여줌) -->
	<select id="selectNewTcrTnteDvList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteDvList */
		SELECT 
			LRN_TP_CD
			, '우리 반 수업' AS LRN_TP_CD_NM
			, CASE
				WHEN COUNT(TNTE_ID) > 0 THEN 'Y'
				ELSE 'N'
			END AS SAVE_YN
		FROM LMS_LRM.MY_TNTE
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRN_USR_ID = #{lrnUsrId}
		AND LRN_TP_CD = 'TL'
		AND TNTE_DV_CD = 'MC'
		UNION ALL
		SELECT 
			EV_DV_CD AS LRN_TP_CD
			, 'AI 맞춤 학습([임시]평가)' AS LRN_TP_CD_NM
			, CASE
				WHEN COUNT(A.EV_ID) > 0 THEN 'Y'
				ELSE 'N'
			END AS SAVE_YN
		FROM LMS_LRM.EA_EV_QTM_ANW A
		INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
		WHERE A.USR_ID = #{lrnUsrId}
		AND A.ANNX_FLE_ID   >   0
		AND B.OPT_TXB_ID = #{optTxbId}
		AND B.EV_DV_CD = 'AE'
		UNION ALL
		SELECT 
			LRN_TP_CD
			, 'AI 맞춤 학습([임시]학습)' AS LRN_TP_CD_NM
			, CASE
				WHEN COUNT(TNTE_ID) > 0 THEN 'Y'
				ELSE 'N'
			END AS SAVE_YN
		FROM LMS_LRM.MY_TNTE
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRN_USR_ID = #{lrnUsrId}
		AND LRN_TP_CD = 'AL'
		UNION ALL
		SELECT 
			LRN_TP_CD
			, '선생님 추천 학습' AS LRN_TP_CD_NM
			, CASE
				WHEN COUNT(TNTE_ID) > 0 THEN 'Y'
				ELSE 'N'
			END AS SAVE_YN
		FROM LMS_LRM.MY_TNTE
		WHERE OPT_TXB_ID = #{optTxbId}
		AND LRN_USR_ID = #{lrnUsrId}
		AND LRN_TP_CD = 'SL'
		AND TNTE_DV_CD = 'TC'
		UNION ALL
		SELECT 
			EV_DV_CD AS LRN_TP_CD
			, '우리반 평가' AS LRN_TP_CD_NM
			, CASE
				WHEN COUNT(A.EV_ID) > 0 THEN 'Y'
				ELSE 'N'
			END AS SAVE_YN
		FROM LMS_LRM.EA_EV_QTM_ANW A
		INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
		WHERE A.USR_ID = #{lrnUsrId}
		AND A.ANNX_FLE_ID   >   0
		AND B.OPT_TXB_ID = #{optTxbId}
		AND B.EV_DTL_DV_CD NOT IN ('ST', 'ET')
		AND B.EV_DV_CD = 'SE'
		UNION ALL
		SELECT 
			EV_DV_CD AS LRN_TP_CD
			, '내가 만든 평가' AS LRN_TP_CD_NM
			, CASE
				WHEN COUNT(A.EV_ID) > 0 THEN 'Y'
				ELSE 'N'
			END AS SAVE_YN
		FROM LMS_LRM.EA_EV_QTM_ANW A
		INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
		WHERE A.USR_ID = #{lrnUsrId}
		AND A.ANNX_FLE_ID   >   0
		AND B.OPT_TXB_ID = #{optTxbId}
		AND B.EV_DV_CD = 'DE'
	</select>
	
	<!-- 2024-07-16 노트 구분 상세 selectBox 조회(저장되어 있는 케이스만 보여줌) -->
	<select id="selectNewTcrTnteDvDtlList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteDvDtlList */
		<choose>
			<when test='"TL".equals(lrnTpCd)'>
				SELECT
					A.LU_NOD_ID
					, B.LRMP_NOD_NM AS LLU_NM
				FROM
					LMS_LRM.MY_TNTE A
				INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON A.OPT_TXB_ID = B.OPT_TXB_ID
														  AND A.LU_NOD_ID = B.LRMP_NOD_ID
														  AND B.DPTH = 1
														  AND B.LCKN_YN = 'N'
				WHERE A.OPT_TXB_ID = #{optTxbId}
				AND A.LRN_USR_ID = #{lrnUsrId}
				AND A.LRN_TP_CD = 'TL'
				AND A.TNTE_DV_CD = 'MC'
				GROUP BY A.LU_NOD_ID
			</when>
			<when test='"AE".equals(lrnTpCd) or "AL".equals(lrnTpCd) or "AI".equals(lrnTpCd)'>
			SELECT
				*
			FROM
			(
				SELECT
					A.LU_NOD_ID
					, B.KMMP_NOD_NM AS LLU_NM
				FROM
					LMS_LRM.MY_TNTE A
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN B 	ON A.OPT_TXB_ID = B.OPT_TXB_ID
														AND A.LU_NOD_ID = B.KMMP_NOD_ID
														AND B.DPTH = '1'
														AND B.DEL_YN = 'N'
				WHERE A.OPT_TXB_ID = #{optTxbId}
				AND A.LRN_USR_ID = #{lrnUsrId}
				AND A.LRN_TP_CD = 'AL'
				GROUP BY A.LU_NOD_ID
				UNION ALL
				SELECT
					A.EV_ID AS LU_NOD_ID
					, C.MLU_KMMP_NOD_NM AS LLU_NM
			--		, B.EV_NM AS LLU_NM
				FROM
					LMS_LRM.EA_EV_QTM_ANW A
				INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE C ON A.EV_ID = C.EV_ID 
				WHERE A.USR_ID = #{lrnUsrId}
				AND A.ANNX_FLE_ID   >   0
				AND B.OPT_TXB_ID = #{optTxbId}
				AND B.EV_DV_CD = 'AE'
				GROUP BY A.EV_ID
			) A
			</when>
			<when test='"SL".equals(lrnTpCd)'>
				SELECT
					A.LU_NOD_ID
					, B.SP_LRN_NM AS LLU_NM
				FROM
					LMS_LRM.MY_TNTE A
				INNER JOIN LMS_LRM.SL_SP_LRN_RCSTN B 	ON A.OPT_TXB_ID = B.OPT_TXB_ID
														AND A.LU_NOD_ID = B.SP_LRN_ID
														AND B.USE_YN = 'Y'
				INNER JOIN LMS_CMS.BC_SP_LRN_NOD C 		ON A.TC_NOD_ID = C.SP_LRN_NOD_ID
														AND C.DPTH = 1
														AND C.DEL_YN = 'N'
				WHERE A.OPT_TXB_ID = #{optTxbId}
				AND A.LRN_USR_ID = #{lrnUsrId}
				AND A.LRN_TP_CD = 'SL'
				AND A.TNTE_DV_CD = 'TC'
				GROUP BY A.LU_NOD_ID
			</when>
			<when test='"SE".equals(lrnTpCd)'>
				SELECT
					A.EV_ID AS LU_NOD_ID
					, B.EV_NM AS LLU_NM
				FROM
					LMS_LRM.EA_EV_QTM_ANW A
				INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
				WHERE A.USR_ID = #{lrnUsrId}
				AND A.ANNX_FLE_ID   >   0
				AND B.OPT_TXB_ID = #{optTxbId}
		--		AND B.EV_DTL_DV_CD NOT IN ('ST', 'ET')
				AND B.EV_DV_CD = 'SE'
				GROUP BY A.EV_ID
			</when>
			<when test='"DE".equals(lrnTpCd)'>
				SELECT
					A.EV_ID AS LU_NOD_ID
					, B.EV_NM AS LLU_NM
				FROM
					LMS_LRM.EA_EV_QTM_ANW A
				INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
				WHERE A.USR_ID = #{lrnUsrId}
				AND A.ANNX_FLE_ID   >   0
				AND B.OPT_TXB_ID = #{optTxbId}
				AND B.EV_DV_CD = 'DE'
				GROUP BY A.EV_ID
			</when>
		</choose>
	</select>
	
	

	<!-- 2024-07-16 노트 목록 조회(개선안 버전) -->
	<select id="selectNewTcrTnteList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteList */
		<include refid="api.bc.common.pagingHeader"/>
		<choose>
			<when test='"TL".equals(lrnTpCd)'>
			SELECT *
			  FROM (
					SELECT
						A.LU_NOD_ID
						, A.TC_NOD_ID
						, MAX(B.LRMP_NOD_NM) AS LLU_NM
						, MAX(C.LRMP_NOD_NM) AS TC_NM
						, '' AS TPC_NM
						, '우리 반 수업' AS LRN_TP_CD_NM
						, DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
						, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
						, A.LRN_TP_CD
						, MAX(C.LCKN_YN) AS LCKN_YN
		    			, MAX(C.USE_YN)  AS USE_YN
		    			, 0 AS TXM_PN
		    			, MAX(B.RCSTN_ORDN) AS RCSTN_ORDN
					FROM
						LMS_LRM.MY_TNTE A
					INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON A.OPT_TXB_ID = B.OPT_TXB_ID
															  AND A.LU_NOD_ID = B.LRMP_NOD_ID
															  AND B.DPTH = 1
															  AND B.LCKN_YN = 'N'
					INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C ON A.TC_NOD_ID = C.LRMP_NOD_ID
															  AND A.OPT_TXB_ID = C.OPT_TXB_ID
															  AND C.DPTH = 4
															  AND C.LCKN_YN = 'N'
												 		 	  AND C.USE_YN = 'Y'
					WHERE A.OPT_TXB_ID = #{optTxbId}
					AND A.LRN_USR_ID = #{lrnUsrId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
						AND A.LU_NOD_ID = #{luNodId}
					</if>
					AND A.LRN_TP_CD = 'TL'
					AND A.TNTE_DV_CD = 'MC'
					GROUP BY A.LU_NOD_ID, A.TC_NOD_ID,A.LRN_TP_CD
				) AS A
			</when>
			<when test='"AL".equals(lrnTpCd) or "AI".equals(lrnTpCd) or "AE".equals(lrnTpCd)'>
			SELECT
				*
			FROM
			(
				SELECT
					A.EV_ID AS LU_NOD_ID
					, MAX(C.QTM_ID) AS TC_NOD_ID
					, MAX(n2.KMMP_NOD_NM) AS LLU_NM
					, MAX(n4.KMMP_NOD_NM) AS TC_NM  -- 영역
					, MAX(n5.KMMP_NOD_NM) AS TPC_NM -- 토픽
					, 'AI 맞춤 학습' AS LRN_TP_CD_NM
					, DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
					, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
					, MAX(B.EV_DV_CD) AS LRN_TP_CD
					, MAX(E.LCKN_YN) AS LCKN_YN
	    			, MAX(E.USE_YN)	AS USE_YN
	    			, 0 AS TXM_PN
	    			, MAX(E.RCSTN_ORDN) AS RCSTN_ORDN
				FROM
					LMS_LRM.EA_EV_QTM_ANW A
				INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
				INNER JOIN LMS_LRM.EA_EV_QTM C	ON A.EV_ID = C.EV_ID
												AND A.QTM_ID = C.QTM_ID
												AND C.DEL_YN = 'N'
				INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE D ON A.EV_ID = D.EV_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN E ON D.MLU_KMMP_NOD_ID = E.KMMP_NOD_ID	
													   AND D.OPT_TXB_ID = E.OPT_TXB_ID
													   AND E.LCKN_YN = 'N'
													   AND E.USE_YN = 'Y'
				INNER JOIN  LMS_CMS.BC_KMMP_NOD n5 ON C.TPC_ID=n5.KMMP_NOD_ID																		
				INNER JOIN  LMS_CMS.BC_KMMP_NOD n4 ON n5.URNK_KMMP_NOD_ID=n4.KMMP_NOD_ID															
				INNER JOIN  LMS_CMS.BC_KMMP_NOD n3 ON n4.URNK_KMMP_NOD_ID=n3.KMMP_NOD_ID															
				INNER JOIN  LMS_CMS.BC_KMMP_NOD n2 ON n3.URNK_KMMP_NOD_ID=n2.KMMP_NOD_ID
				WHERE A.USR_ID = #{lrnUsrId}
				AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
				AND B.OPT_TXB_ID = #{optTxbId}
				<if test = 'luNodId != null and !"".equals(luNodId)'>
				     AND A.EV_ID IN ( SELECT EV_ID from LMS_LRM.EA_EV_TS_RNGE WHERE  LU_LRMP_NOD_ID = #{luNodId}
				    				 UNION
									 SELECT EV_ID FROM LMS_LRM.EA_AI_EV_TS_RNGE WHERE MLU_KMMP_NOD_ID  = #{luNodId}
				    				)
				</if>
				AND B.EV_DV_CD = 'AE'
				GROUP BY A.EV_ID
				<!-- UNION ALL
				SELECT
					A.LU_NOD_ID
					, A.TC_NOD_ID
					, B.KMMP_NOD_NM AS LLU_NM
					, C.KMMP_NOD_NM AS TC_NM
					, 'AI 맞춤 학습' AS LRN_TP_CD_NM
					, DATE_FORMAT(A.CRT_DTM, '%m.%d') AS CRT_DTM
					, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
					, A.LRN_TP_CD
				FROM
					LMS_LRM.MY_TNTE A
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN B 	ON A.OPT_TXB_ID = B.OPT_TXB_ID
														AND A.LU_NOD_ID = B.KMMP_NOD_ID
														AND B.DPTH = '1'
														AND B.DEL_YN = 'N'
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN C 	ON A.OPT_TXB_ID = C.OPT_TXB_ID
														AND A.TC_NOD_ID = C.KMMP_NOD_ID
														AND A.LU_NOD_ID = C.KMMP_NOD_ID
														AND B.DPTH = '4'
														AND B.DEL_YN = 'N'
				WHERE A.OPT_TXB_ID = #{optTxbId}
				AND A.LRN_USR_ID = #{lrnUsrId}
				<if test = 'luNodId != null and !"".equals(luNodId)'>
					AND A.LU_NOD_ID = #{luNodId}
				</if>
				AND A.LRN_TP_CD = 'AL'
				GROUP BY A.OPT_TXB_ID, A.LU_NOD_ID, A.TC_NOD_ID -->
			) A
			</when>
			
			<when test='"SL".equals(lrnTpCd)'>
			SELECT *
			  FROM (
					SELECT
						A.LU_NOD_ID
						, A.TC_NOD_ID
						, MAX(B.SP_LRN_NM) AS LLU_NM
						, MAX(C.SP_LRN_NOD_NM) AS TC_NM
						, '' AS TPC_NM
						, '선생님 추천 학습' AS LRN_TP_CD_NM
						, DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
						, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
						, MAX(A.LRN_TP_CD) AS LRN_TP_CD
						, '' AS LCKN_YN 
		    			, '' AS USE_YN
		    			, 0 AS TXM_PN
		    			, MAX(B.RCSTN_ORDN) AS RCSTN_ORDN
					FROM
						LMS_LRM.MY_TNTE A
					INNER JOIN LMS_LRM.SL_SP_LRN_RCSTN B 	ON A.OPT_TXB_ID = B.OPT_TXB_ID
															AND A.LU_NOD_ID = B.SP_LRN_ID
															AND B.USE_YN = 'Y'
					INNER JOIN LMS_CMS.BC_SP_LRN_NOD C 		ON A.TC_NOD_ID = C.SP_LRN_NOD_ID
															AND C.DPTH = 1
															AND C.DEL_YN = 'N'
					WHERE A.OPT_TXB_ID = #{optTxbId}
					AND A.LRN_USR_ID = #{lrnUsrId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
						AND A.LU_NOD_ID = #{luNodId}
					</if>
					AND A.LRN_TP_CD = 'SL'
					AND A.TNTE_DV_CD = 'TC'
					GROUP BY A.OPT_TXB_ID, A.LU_NOD_ID, A.TC_NOD_ID
				) AS A
			</when>
			<when test='"SE".equals(lrnTpCd)'>
			SELECT 
					A.LU_NOD_ID
			     , MAX(A.TC_NOD_ID) AS TC_NOD_ID
			     , MAX(A.LLU_NM) AS LLU_NM
			     , MAX(A.TC_NM) AS TC_NM
			     , MAX(A.TPC_NM) AS TPC_NM
			     , MAX(A.LRN_TP_CD_NM) AS LRN_TP_CD_NM
			     , MAX(A.CRT_DTM) AS CRT_DTM
			     , A.C_DTM
			     , MAX(A.LRN_TP_CD) AS LRN_TP_CD
			     , MAX(A.LCKN_YN) AS LCKN_YN
			     , MAX(A.USE_YN) AS USE_YN
			     , A.TXM_PN 
			     , MAX(A.RCSTN_ORDN) AS RCSTN_ORDN
			  FROM (
					SELECT
						A.EV_ID AS LU_NOD_ID
						, MAX(C.QTM_ID) AS TC_NOD_ID
						, (SELECT CM_CD_NM FROM CM_CM_CD C WHERE URNK_CM_CD ='EV_DTL_DV_CD' AND  CM_CD = B.EV_DTL_DV_CD) AS LLU_NM
						, MAX(B.EV_NM) AS TC_NM
						, '' AS TPC_NM
						, '우리 반 평가' AS LRN_TP_CD_NM
						, DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
						, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
						, MAX(B.EV_DV_CD) AS LRN_TP_CD
						, MAX(B.LCKN_YN) AS LCKN_YN
		    			, MAX(B.USE_YN) AS USE_YN
		    			, 0 AS TXM_PN
		    			, MAX(E.RCSTN_ORDN) AS RCSTN_ORDN
					FROM
						LMS_LRM.EA_EV_QTM_ANW A
					INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
													AND B.LCKN_YN = 'N'
												    AND B.USE_YN = 'Y'
					INNER JOIN LMS_LRM.EA_EV_QTM C	ON A.EV_ID = C.EV_ID
													AND A.QTM_ID = C.QTM_ID
													AND C.DEL_YN = 'N'
					INNER JOIN LMS_LRM.EA_EV_TS_RNGE D ON A.EV_ID = D.EV_ID
											   AND D.TS_RNGE_SEQ_NO = 1
					INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN E ON D.LU_LRMP_NOD_ID = E.LLU_NOD_ID
													  AND D.LU_OPT_TXB_ID = E.OPT_TXB_ID
													  AND E.DPTH = 1
					WHERE A.USR_ID = #{lrnUsrId}
					AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
					AND B.OPT_TXB_ID = #{optTxbId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
					    AND A.EV_ID IN ( SELECT EV_ID from LMS_LRM.EA_EV_TS_RNGE WHERE  LU_LRMP_NOD_ID = #{luNodId})
					</if>
					AND B.EV_DV_CD IN('SE','TE')
					GROUP BY A.EV_ID
					UNION ALL
					SELECT
						A.EV_ID AS LU_NOD_ID
						, MAX(A.QTM_ID) AS TC_NOD_ID
						, (SELECT CM_CD_NM FROM CM_CM_CD C WHERE URNK_CM_CD ='EV_DTL_DV_CD' AND  CM_CD = B.EV_DTL_DV_CD) AS LLU_NM
						, MAX(B.EV_NM) AS TC_NM
						, '' AS TPC_NM
						, '우리 반 평가' AS LRN_TP_CD_NM
						, DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
						, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
						, MAX(B.EV_DV_CD) AS LRN_TP_CD
						, MAX(B.LCKN_YN) AS LCKN_YN
		    			, MAX(B.USE_YN) AS USE_YN
		    			, A.TXM_PN 
		    			, MAX(E.RCSTN_ORDN) AS RCSTN_ORDN
					FROM
						LMS_LRM.EA_EV_SPP_NTN_QTM_ANW A
					INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
													AND B.LCKN_YN = 'N'
												    AND B.USE_YN = 'Y'
					INNER JOIN LMS_LRM.EA_EV_TS_RNGE C ON A.EV_ID = C.EV_ID
											   AND C.TS_RNGE_SEQ_NO = 1
					INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN E ON C.LU_LRMP_NOD_ID = E.LLU_NOD_ID
													  AND C.LU_OPT_TXB_ID = E.OPT_TXB_ID
													  AND E.DPTH = 1
					WHERE A.USR_ID = #{lrnUsrId}
					AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
					AND B.OPT_TXB_ID = #{optTxbId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
					    AND A.EV_ID IN ( SELECT EV_ID from LMS_LRM.EA_EV_TS_RNGE WHERE  LU_LRMP_NOD_ID = #{luNodId})
					</if>
					AND B.EV_DV_CD IN('SE','TE')
					GROUP BY A.EV_ID ,A.TXM_PN
					UNION ALL 
					SELECT
						A.EV_ID AS LU_NOD_ID
						, MAX(C.QTM_ID) AS TC_NOD_ID
						, (SELECT CM_CD_NM FROM CM_CM_CD C WHERE URNK_CM_CD ='EV_DTL_DV_CD' AND  CM_CD = B.EV_DTL_DV_CD) AS LLU_NM
						, MAX(B.EV_NM) AS TC_NM
						, '' AS TPC_NM
						, '우리 반 평가' AS LRN_TP_CD_NM
						, DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
						, GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
						, MAX(B.EV_DV_CD) AS LRN_TP_CD
						, MAX(B.LCKN_YN) AS LCKN_YN
		    			, MAX(B.USE_YN) AS USE_YN
		    			, A.RTXM_PN AS TXM_PN
		    			, MAX(E.RCSTN_ORDN) AS RCSTN_ORDN
					FROM
						LMS_LRM.EA_EV_QTM_ANW_RTXM A
					INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
													AND B.LCKN_YN = 'N'
												    AND B.USE_YN = 'Y'
					INNER JOIN LMS_LRM.EA_EV_QTM C	ON A.EV_ID = C.EV_ID
													AND A.QTM_ID = C.QTM_ID
													AND C.DEL_YN = 'N'
					INNER JOIN LMS_LRM.EA_EV_TS_RNGE D ON A.EV_ID = D.EV_ID
				   							   AND D.TS_RNGE_SEQ_NO = 1
				   	INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN E ON D.LU_LRMP_NOD_ID = E.LLU_NOD_ID
				   									 AND D.LU_OPT_TXB_ID = E.OPT_TXB_ID
				   									 AND E.DPTH = 1
					WHERE A.USR_ID = #{lrnUsrId}
					AND A.ANNX_FLE_ID  <![CDATA[ > ]]> 0
					AND B.OPT_TXB_ID = #{optTxbId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
					    AND A.EV_ID IN ( SELECT EV_ID from LMS_LRM.EA_EV_TS_RNGE WHERE  LU_LRMP_NOD_ID = #{luNodId})
					</if>
					AND B.EV_DV_CD IN('SE','TE')
					GROUP BY A.EV_ID,A.RTXM_PN
				)AS A
				GROUP BY A.LU_NOD_ID,A.TXM_PN,A.C_DTM
			</when>
			<when test='"DE".equals(lrnTpCd)'>
				SELECT 
					 A.LU_NOD_ID
				    ,MAX(A.TC_NOD_ID) AS TC_NOD_ID
				    ,MAX(A.LLU_NM) AS LLU_NM
				    ,MAX(A.TC_NM) AS TC_NM
				    ,MAX(A.TPC_NM) AS TPC_NM
				    ,MAX(A.LRN_TP_CD_NM) AS LRN_TP_CD_NM
				    ,MAX(A.CRT_DTM) AS CRT_DTM
				    ,A.C_DTM
				    ,MAX(A.LRN_TP_CD) AS LRN_TP_CD
				    ,MAX(A.LCKN_YN) AS LCKN_YN
				    ,MAX(A.USE_YN) AS USE_YN
				    ,MAX(A.TXM_PN) AS TXM_PN
				    ,MAX(A.RCSTN_ORDN) AS RCSTN_ORDN
				FROM (
					SELECT
						 A.EV_ID AS LU_NOD_ID
						,MAX(C.QTM_ID) AS TC_NOD_ID
				        ,MAX(B.EV_NM) AS LLU_NM
				        ,MAX(C.QP_LLU_NM) AS TC_NM
				        ,'' AS TPC_NM
				        ,'내가 만든 평가' AS LRN_TP_CD_NM
				        ,DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
				        ,GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
				        ,B.EV_DV_CD AS LRN_TP_CD
				        ,MAX(E.LCKN_YN) AS LCKN_YN
				        ,MAX(E.USE_YN) AS USE_YN
				        ,0 AS TXM_PN
				        ,0 AS RCSTN_ORDN
					FROM
						LMS_LRM.EA_EV_QTM_ANW A
					INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
					INNER JOIN LMS_LRM.EA_EV_QTM C	ON A.EV_ID = C.EV_ID
													AND A.QTM_ID = C.QTM_ID
													AND C.DEL_YN = 'N'
					LEFT JOIN LMS_LRM.EA_AI_EV_TS_RNGE D ON A.EV_ID = D.EV_ID
				    LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN E ON D.MLU_KMMP_NOD_ID = E.KMMP_NOD_ID	
														   AND D.OPT_TXB_ID = E.OPT_TXB_ID
					WHERE A.USR_ID = #{lrnUsrId}
					AND A.ANNX_FLE_ID  <![CDATA[ > ]]>  0
					AND B.OPT_TXB_ID = #{optTxbId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
						AND A.EV_ID IN ( SELECT EV_ID FROM LMS_LRM.EA_EV_TS_RNGE WHERE  LU_LRMP_NOD_ID = #{luNodId}
										<if test="KmmpNodIdList != null and KmmpNodIdList.size() > 0">
											 UNION
											 SELECT EV_ID FROM LMS_LRM.EA_AI_EV_TS_RNGE WHERE MLU_KMMP_NOD_ID IN 
											 <foreach item="id" collection="KmmpNodIdList" open="(" separator="," close=")">
												#{id}
											 </foreach>
										 </if>
					    				)
					</if>
					AND B.EV_DV_CD = 'DE'
					GROUP BY A.EV_ID
					UNION ALL
					SELECT
						A.EV_ID AS LU_NOD_ID
						,MAX(A.QTM_ID) AS TC_NOD_ID
				        ,MAX(B.EV_NM) AS LLU_NM
				        ,'' AS TC_NM
				        ,'' AS TPC_NM
				        ,'내가 만든 평가' AS LRN_TP_CD_NM
				        ,DATE_FORMAT(GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)), '%m. %d.') AS CRT_DTM
				        ,GREATEST(MAX(A.CRT_DTM), MAX(A.MDF_DTM)) AS C_DTM
				        ,B.EV_DV_CD AS LRN_TP_CD
				        ,MAX(E.LCKN_YN) AS LCKN_YN
				        ,MAX(E.USE_YN) AS USE_YN
				        ,0 AS TXM_PN
				        ,0 AS RCSTN_ORDN
					FROM
						LMS_LRM.EA_EV_SPP_NTN_QTM_ANW A
					INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
					LEFT JOIN LMS_LRM.EA_AI_EV_TS_RNGE D ON A.EV_ID = D.EV_ID
				    LEFT JOIN LMS_LRM.AI_KMMP_NOD_RCSTN E ON D.MLU_KMMP_NOD_ID = E.KMMP_NOD_ID	
														  AND D.OPT_TXB_ID = E.OPT_TXB_ID
					WHERE A.USR_ID = #{lrnUsrId}
					AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
					AND B.OPT_TXB_ID = #{optTxbId}
					<if test = 'luNodId != null and !"".equals(luNodId)'>
						AND A.EV_ID IN ( SELECT EV_ID FROM LMS_LRM.EA_EV_TS_RNGE WHERE  LU_LRMP_NOD_ID = #{luNodId}
										<if test="KmmpNodIdList != null and KmmpNodIdList.size() > 0">
											 UNION
											 SELECT EV_ID FROM LMS_LRM.EA_AI_EV_TS_RNGE WHERE MLU_KMMP_NOD_ID IN 
											 <foreach item="id" collection="KmmpNodIdList" open="(" separator="," close=")">
												#{id}
											 </foreach>
										</if>
					    				)
					</if>
					AND B.EV_DV_CD = 'DE'
					GROUP BY A.EV_ID
				) AS A
				WHERE A.LCKN_YN IS NULL AND A.USE_YN IS NULL OR (A.LCKN_YN = 'N' AND A.USE_YN = 'Y')
				GROUP BY A.LU_NOD_ID ,A.C_DTM
			</when>
		</choose>
		
		<choose>
			<when test='srhSrt != null and !"".equals(srhSrt)'>
				<choose>
					<when test='srhSrt == "ins" '>
						ORDER BY A.C_DTM
						<if test='srtInfo == "desc" '>
							desc
						</if>
						<if test='srtInfo == "asc" '>
							asc
						</if>
					</when>
					<otherwise>
						ORDER BY 
						 CASE 
					      	  WHEN A.LRN_TP_CD = 'TE' THEN 1 
					        ELSE 2 
					    END, 
					    CASE 
					        WHEN A.LRN_TP_CD = 'TE' THEN A.C_DTM 
					        ELSE NULL
					    END DESC, 
					    CASE 
					        WHEN A.LRN_TP_CD != 'TE' THEN A.RCSTN_ORDN 
					        ELSE NULL
					    <if test='srtInfo == "desc" '>
							END DESC,
						</if>
						<if test='srtInfo == "asc" '>
							END	ASC,
						</if>
					    CASE 
					        WHEN A.LRN_TP_CD != 'TE' THEN A.C_DTM
					        ELSE NULL
					    END DESC												
					</otherwise>
				</choose>
			</when>
			<otherwise>
				ORDER BY A.C_DTM
			</otherwise>
		</choose>
		
		<include refid="api.bc.common.pagingFooter"/>
	</select>

	
	<!-- 노트 학습활동 목록 조회(우리반 수업) -->
	<select id="selectNewTcrTnteAtvTeList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteAtvTeList */
		SELECT *
		  FROM (
	            SELECT
	                A.TNTE_ID
	                , A.LRN_ATV_INFO AS LRN_ATV_ID
	                , B.EV_ID
	                , B.LRN_ATV_NM 
	                , G.FLE_PTH_NM AS CDN_PTH_NM
	                , E.LRN_STP_DV_CD
	                , '' AS EV_DTL_DV_CD
	                , A.CRT_DTM
	                , A.CDN_PTH_NM AS TNTE_CDN_PTH_NM
	                , B.RCSTN_ORDN AS RCSTN_ORDN
	                , 'N' AS TCR_CTN
	                , '' AS TP_CD
	            FROM
	                LMS_LRM.MY_TNTE A
	            INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN B    ON A.LRN_ATV_INFO = B.LRN_ATV_ID
	                                                        AND B.OPT_TXB_ID = #{optTxbId}
	                                                        AND B.USE_YN = 'Y'
	            LEFT OUTER JOIN LMS_CMS.BC_LRN_STP E         ON B.LRN_STP_ID = E.LRN_STP_ID
	                                                        AND E.DEL_YN = 'N'
	            LEFT OUTER JOIN LMS_CMS.BC_CTN_MTD C        ON B.LRN_ATV_ID = C.LRN_ATV_ID
	                                                        AND C.USE_YN = 'Y'
	                                                        AND C.DEL_YN = 'N'
	            LEFT JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN D ON A.TC_NOD_ID = D.LRMP_NOD_ID
	                                                    AND B.OPT_TXB_ID = D.OPT_TXB_ID
	            LEFT JOIN LMS_CMS.BC_LRMP_NOD_THB_MPN F ON D.LRMP_NOD_ID = F.LRMP_NOD_ID
	            LEFT JOIN LMS_CMS.BC_UPL_FLE G ON F.PC_THB_UPL_ID = G.UPL_FLE_ID AND G.DEL_YN ='N'
	            WHERE A.LU_NOD_ID = #{luNodId}
	            AND A.TC_NOD_ID = #{tcNodId}
	            AND A.LRN_USR_ID = #{lrnUsrId}
	            AND A.OPT_TXB_ID = #{optTxbId}
	            AND A.TNTE_DV_CD = 'MC'
	            UNION ALL 
	            SELECT
	                A.TNTE_ID
	                , A.LRN_ATV_INFO AS LRN_ATV_ID
	                , B.EV_ID
	                , C.TCR_REG_CTN_NM AS LRN_ATV_NM 
	                , '' AS CDN_PTH_NM
	                ,'' AS LRN_STP_DV_CD
	                , '' AS EV_DTL_DV_CD
	                , A.CRT_DTM
	                , A.CDN_PTH_NM AS TNTE_CDN_PTH_NM
	                , D.RCSTN_ORDN AS RCSTN_ORDN
	                ,CASE WHEN D.TCR_REG_CTN_ID IS NOT NULL OR D.TCR_REG_CTN_ID != '' THEN 'Y' 
	                      ELSE 'N'
	                  END AS TCR_CTN
	                ,C.TP_CD
	            FROM
	                LMS_LRM.MY_TNTE A
	            LEFT OUTER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN B    ON A.LRN_ATV_INFO = B.LRN_ATV_ID
	                                                            AND B.OPT_TXB_ID = #{optTxbId}
	            INNER JOIN LMS_LRM.TL_TCR_REG_CTN C ON A.LRN_ATV_INFO = C.TCR_REG_CTN_ID
	            INNER JOIN LMS_LRM.TL_TCR_REG_CTN_MPN D ON C.TCR_REG_CTN_ID = D.TCR_REG_CTN_ID 
	                                                         AND D.OPT_TXB_ID =#{optTxbId} 
	                                                         AND D.DEL_YN ='N' AND D.USE_YN ='Y'
	            WHERE A.LU_NOD_ID = #{luNodId}
	            AND A.TC_NOD_ID = #{tcNodId}
	            AND A.LRN_USR_ID = #{lrnUsrId}
	            AND A.OPT_TXB_ID = #{optTxbId}
	            AND A.TNTE_DV_CD = 'MC'
      		) AS T
        ORDER BY T.RCSTN_ORDN
	</select>
	
	
	<!-- 노트 학습활동 목록 조회(선생님 추천 학습) -->
	<select id="selectNewTcrTnteAtvSlList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteAtvSlList */
		SELECT
			A.TNTE_ID
			, MAX(A.LRN_ATV_INFO) AS LRN_ATV_ID
			, '' AS EV_ID
			, MAX(C.SP_LRN_NOD_NM) AS LRN_ATV_NM
			, MAX(F.FLE_PTH_NM) AS CDN_PTH_NM
			, '' AS LRN_STP_DV_CD
			, '' AS EV_DTL_DV_CD
			, MAX(A.CRT_DTM) AS CRT_DTM
			, MAX(A.CDN_PTH_NM) AS TNTE_CDN_PTH_NM
			, MAX(C.SRT_ORDN) AS RCSTN_ORDN
			, '' AS TCR_CTN
			, '' AS TP_CD
		FROM
			LMS_LRM.MY_TNTE A
		INNER JOIN LMS_CMS.BC_SP_LRN_CTN B	ON A.LRN_ATV_INFO = B.SP_LRN_CTN_ID
											AND B.DEL_YN = 'N'
		INNER JOIN LMS_CMS.BC_SP_LRN_NOD C	ON B.SP_LRN_NOD_ID = C.SP_LRN_NOD_ID
											AND A.LU_NOD_ID = C.SP_LRN_ID
											AND A.TC_NOD_ID = C.URNK_SP_LRN_NOD_ID
											AND C.DPTH = 2
											AND C.DEL_YN = 'N'
	    LEFT JOIN LMS_LRM.SL_SP_LRN_RCSTN D ON A.LU_NOD_ID = D.SP_LRN_ID
    	LEFT JOIN LMS_CMS.BC_SP_LRN_THB_FLE_MPN E ON D.SP_LRN_ID = E.SP_LRN_ID
    	LEFT JOIN LMS_CMS.BC_UPL_FLE F ON E.UPL_FLE_ID = F.UPL_FLE_ID
		WHERE A.LU_NOD_ID = #{luNodId}
		AND A.TC_NOD_ID = #{tcNodId}
		AND A.LRN_USR_ID = #{lrnUsrId}
		AND A.OPT_TXB_ID = #{optTxbId}
		AND A.LRN_TP_CD = 'SL'
		AND A.TNTE_DV_CD = 'TC'
		GROUP BY A.TNTE_ID
		ORDER BY C.SRT_ORDN
	</select>
	
	<!-- 노트 학습활동 목록 조회(우리반 평가, 내가 만든 평가) -->
	<select id="selectNewTcrTnteEvAtvList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteEvAtvList */
		SELECT
			*
		FROM
		(
			SELECT 
				A.EV_ID AS TNTE_ID
				, A.QTM_ID AS LRN_ATV_ID
				, '' AS EV_ID
				, concat('문항', C.QTM_ORDN) AS LRN_ATV_NM
				, 'CDN_PTH_NM' AS CDN_PTH_NM
				, '' AS LRN_STP_DV_CD
				, B.EV_DTL_DV_CD
				, A.CRT_DTM
				,( select ANNX_FLE_PTH_NM  from CM_ANNX_FLE WHERE  ANNX_FLE_ID=A.ANNX_FLE_ID) AS TNTE_CDN_PTH_NM
				, C.QTM_ORDN AS RCSTN_ORDN
				, '' AS TCR_CTN
				, '' AS TP_CD
				, '' AS SPP_NTN_TP_CD
				,  0 AS TXM_PN
			FROM 
				LMS_LRM.EA_EV_QTM_ANW A
			INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM C ON C.EV_ID = A.EV_ID
												AND C.QTM_ID = A.QTM_ID
			WHERE A.EV_ID = #{luNodId}
			AND A.USR_ID = #{lrnUsrId}
			AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
			AND B.OPT_TXB_ID = #{optTxbId}
			UNION ALL
			SELECT 
				A.EV_ID AS TNTE_ID
				, A.QTM_ID AS LRN_ATV_ID
				, '' AS EV_ID
				, concat('문항', C.QTM_ORDN) AS LRN_ATV_NM
				, 'CDN_PTH_NM' AS CDN_PTH_NM
				, '' AS LRN_STP_DV_CD
				, B.EV_DTL_DV_CD
				, A.CRT_DTM
				,( select ANNX_FLE_PTH_NM  from CM_ANNX_FLE WHERE  ANNX_FLE_ID=A.ANNX_FLE_ID) AS TNTE_CDN_PTH_NM
				, C.QTM_ORDN AS RCSTN_ORDN
				, '' AS TCR_CTN
				, '' AS TP_CD
				, '' AS SPP_NTN_TP_CD
				, A.RTXM_PN AS TXM_PN
			FROM 
				LMS_LRM.EA_EV_QTM_ANW_RTXM A
			INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM C ON C.EV_ID = A.EV_ID
												AND C.QTM_ID = A.QTM_ID
			WHERE A.EV_ID = #{luNodId}
			AND A.USR_ID = #{lrnUsrId}
			AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
			AND B.OPT_TXB_ID = #{optTxbId}
			UNION ALL
			SELECT 
				A.EV_ID AS TNTE_ID
				, A.QTM_ID AS LRN_ATV_ID
				, '' AS EV_ID
				, CASE 
					WHEN A.SPP_NTN_TP_CD = 'DE' THEN concat('[심화 문제] ','문항', A.QTM_ORDN) 
					ELSE concat('[오답 유사] ','문항', A.QTM_ORDN) 
				  END AS LRN_ATV_NM
				, 'CDN_PTH_NM' AS CDN_PTH_NM
				, '' AS LRN_STP_DV_CD
				, B.EV_DTL_DV_CD
				, A.CRT_DTM
				,( select ANNX_FLE_PTH_NM  from CM_ANNX_FLE WHERE  ANNX_FLE_ID=A.ANNX_FLE_ID) AS TNTE_CDN_PTH_NM
				, A.QTM_ORDN AS RCSTN_ORDN
				, '' AS TCR_CTN
				, '' AS TP_CD
				, A.SPP_NTN_TP_CD
				, A.TXM_PN
			FROM 
				LMS_LRM.EA_EV_SPP_NTN_QTM_ANW A
			INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
			WHERE A.EV_ID = #{luNodId}
			AND A.USR_ID = #{lrnUsrId}
			AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
			AND B.OPT_TXB_ID = #{optTxbId}
		) AS T
		WHERE  T.TXM_PN = #{txmPn}
		ORDER BY 
		CASE WHEN T.SPP_NTN_TP_CD = '' THEN 0 ELSE 1 END, 
    	T.RCSTN_ORDN ASC
	</select>
	
	
	<!-- 노트 학습활동 목록 조회(AI맞춤학습) -->
	<select id="selectNewTcrTnteAtvAiList" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto" resultType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto">
		/** BcTnteTcr-Mapper.xml - selectNewTcrTnteAtvAlList */
		SELECT
			*
		FROM
		(
			SELECT
				A.TNTE_ID
				, A.LRN_ATV_INFO AS LRN_ATV_ID
				, '' AS EV_ID
				, C.LRN_ATV_NM AS LRN_ATV_NM
				, D.LU_IMG_PTH AS CDN_PTH_NM
				, '' AS LRN_STP_DV_CD
				, '' AS EV_DTL_DV_CD
				, A.CRT_DTM
				, A.CDN_PTH_NM AS TNTE_CDN_PTH_NM
				, B.RCSTN_ORDN AS RCSTN_ORDN
				, '' AS TCR_CTN
				, '' AS TP_CD
			FROM
				LMS_LRM.MY_TNTE A
			INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN B	ON A.LU_NOD_ID = B.KMMP_NOD_ID
		--											AND B.DPTH = '4'
													AND DEL_YN = 'N'
			INNER JOIN LMS_CMS.BC_AI_LRN_ATV_CTN C  ON  A.LRN_ATV_INFO = C.AI_LRN_ATV_ID 
													AND C.USE_YN = 'Y'
													AND C.DEL_YN = 'N'
			LEFT JOIN (SELECT T.KMMP_NOD_ID , T.URNK_KMMP_NOD_ID, T.OPT_TXB_ID
		 	              FROM LMS_LRM.AI_KMMP_NOD_RCSTN T
		 	             WHERE T.URNK_KMMP_NOD_ID = #{luNodId}
						   AND T.OPT_TXB_ID = #{optTxbId}
						   AND DEL_YN = 'N'
						   AND T.DPTH = '2'
						) AS BT
													ON A.LU_NOD_ID = BT.URNK_KMMP_NOD_ID
													AND A.OPT_TXB_ID = BT.OPT_TXB_ID
			LEFT OUTER JOIN LMS_CMS.BC_AI_LU_IMG D	ON BT.KMMP_NOD_ID = D.LU_NOD_ID
			WHERE A.LU_NOD_ID = #{luNodId}
			AND A.TC_NOD_ID = #{tcNodId}
			AND A.LRN_USR_ID = #{lrnUsrId}
			AND A.OPT_TXB_ID = #{optTxbId}
			AND A.LRN_TP_CD = 'AL'
			UNION ALL
			SELECT 
				A.EV_ID AS TNTE_ID
				, A.QTM_ID AS LRN_ATV_ID
				, '' AS EV_ID
				, concat('문항', C.QTM_ORDN) AS LRN_ATV_NM
				, 'CDN_PTH_NM' AS CDN_PTH_NM
				, '' AS LRN_STP_DV_CD
				, B.EV_DTL_DV_CD
				, A.CRT_DTM
				,( select ANNX_FLE_PTH_NM  from CM_ANNX_FLE WHERE  ANNX_FLE_ID=A.ANNX_FLE_ID) AS TNTE_CDN_PTH_NM
				, C.QTM_ORDN AS RCSTN_ORDN
				, '' AS TCR_CTN
				, '' AS TP_CD
			FROM 
				LMS_LRM.EA_EV_QTM_ANW A
			INNER JOIN LMS_LRM.EA_EV B		ON A.EV_ID = B.EV_ID
			LEFT OUTER JOIN LMS_LRM.EA_EV_QTM C ON C.EV_ID = A.EV_ID
												AND C.QTM_ID = A.QTM_ID
												AND C.DEL_YN = 'N'
			WHERE A.EV_ID = #{luNodId}
			AND A.USR_ID = #{lrnUsrId}
			AND A.ANNX_FLE_ID <![CDATA[ > ]]> 0
			AND B.OPT_TXB_ID = #{optTxbId}
		) A
		ORDER BY RCSTN_ORDN
--		ORDER BY A.CRT_DTM DESC

	</select>
	
	
	<delete id="deleteNewTcrTnte" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto">
		/** BcTnteTcr-Mapper.xml - deleteNewTcrTnte */
		DELETE FROM LMS_LRM.MY_TNTE
		WHERE TNTE_ID = #{tnteId}
		AND OPT_TXB_ID = #{optTxbId}
		AND LRN_USR_ID = #{lrnUsrId}
	</delete>


	<delete id="deleteNewTcrForEvTnte" parameterType="com.aidt.api.bc.tnte.dto.BcNewTnteLrnAtvDto">
		/** BcTnteTcr-Mapper.xml - deleteNewTcrForEvTnte */
		        UPDATE LMS_LRM.EA_EV_QTM_ANW
           SET 
                  ANNX_FLE_ID = NULL 
         WHERE EV_ID = #{tnteId}
           AND QTM_ID = #{lrnAtvId}
           AND USR_ID = #{lrnUsrId}
	</delete>
	
	

</mapper>