<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.cmt.stu">
	<!-- AI 코멘트 조회(평가/수학/학기초평가) -->
	<select id="selectMaEvStList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectMaEvStList */
		<trim prefixOverrides="UNION ALL">
			(<include refid="api.al.cmt.cm.selectN01"/>)
			<choose>
				<when test="existAiRcmCtn == true">
					UNION ALL
					(<include refid="api.al.cmt.cm.selectN04"/>)
				</when>
				<otherwise>
					UNION ALL
					(<include refid="api.al.cmt.cm.selectN05"/>)
				</otherwise>
			</choose>
		</trim>
	</select>

	<!-- AI 코멘트 조회(평가/수학/단원진단) -->
	<select id="selectMaEvUdList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectMaEvUdList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		<choose>
			<when test="existAiRcmCtn == true">
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN04"/>)
			</when>
			<otherwise>
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN05"/>)
			</otherwise>
		</choose>
		</trim>
	</select>

	<!-- AI 코멘트 조회(평가/수학/학기말총괄) -->
	<select id="selectMaEvEtList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectMaEvEtList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN01"/>)
		<if test="n13 != null">
			UNION ALL
			(<include refid="api.al.cmt.cm.selectN13"/>)
		</if>
		<if test="n14 != null">
			UNION ALL
			(<include refid="api.al.cmt.cm.selectN14"/>)
		</if>
		<if test="n15 != null">
			UNION ALL
			(<include refid="api.al.cmt.cm.selectN15"/>)
		</if>
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN05"/>)
		</trim>
	</select>

	<!-- AI 코멘트 조회(AI 진단 리포트) -->
	<select id="selectMaAiDgnRptList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectMaAiDgnRptList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN10"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN11"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN07"/>)
		</trim>
	</select>

	<!-- AI 코멘트 조회(AI 진단 리포트) -->
	<select id="selectMaAiLrnRptList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtStu-Mapper.xml - selectMaAiLrnRptList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN02"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN10"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN11"/>)
		<choose>
			<when test="isStudying == true">
			UNION ALL
				(<include refid="api.al.cmt.cm.selectN07"/>)
			</when>
			<otherwise>
				UNION ALL
				(<include refid="api.al.cmt.cm.selectN09"/>)
			</otherwise>
		</choose>
		</trim>
	</select>

	<!-- AI 코멘트 조회(AI 진단 리포트) -->
	<select id="selectMaRptLuList" resultType="com.aidt.api.al.cmt.dto.res.AiCmtResDto">
		/** AlCmtMaStu-Mapper.xml - selectMaRptLuList */
		<trim prefixOverrides="UNION ALL">
		(<include refid="api.al.cmt.cm.selectN10"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN11"/>)
		UNION ALL
		(<include refid="api.al.cmt.cm.selectN06"/>)
		</trim>
	</select>

</mapper>