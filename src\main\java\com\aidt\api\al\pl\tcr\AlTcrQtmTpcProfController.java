//package com.aidt.api.al.pl.tcr;
//
//import javax.validation.Valid;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
//import com.aidt.api.al.pl.stu.AlQtmTpcProfService;
//import com.aidt.base.jwt.CommonUserDetail;
//import com.aidt.base.response.Response;
//import com.aidt.base.response.ResponseDto;
//import com.aidt.base.util.CoreUtil;
//
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * AI 맞춤 문항추천
// */
//
//@Slf4j
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/api/v1/al/pl/tcr/qtmTpcProf")
//@Tag(name="[al] AI맞춤 사용자별 문항토픽별 프로파일", description="AI맞춤 사용자별 문항토픽별 프로파일")
//public class AlTcrQtmTpcProfController {
//	
//	@Autowired
//	private AlQtmTpcProfService alQtmTpcProfService;
//	
//	//@Autowired
//	//private JwtProvider jwtProvider;
//	
//	/**
//	 * 평가완료시 사용자별 문항,토픽 프로파일 데이터 적재
//	 * 
//	 * @return 
//	 * */
//	@Tag(name="[al] 사용자별 프로파일 적재", description="평가완료시 사용자별 문항,토픽 프로파일 데이터 적재")
//    @PostMapping(value = "/selectUsrlyQtmProf")
//    public ResponseDto<Integer> selectAiRcmTsshQtmList(@Valid @RequestBody AlPlQtmTpcProfDto dto) {
//		log.debug(dto.toString());
//		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//        if(dto.getUsrId() == null) {
//        	dto.setUsrId(securityUserDetailDto.getUsrId());
//        }
//        
//     // 삭제예정
//        if(dto.getSbjCd() == null) {
//        	dto.setSbjCd("MA");
//        }
//        if(dto.getSchlGrdCd() == null) {
//        	dto.setSchlGrdCd("E");
//        }
//        
//		int res = alQtmTpcProfService.selectUsrlyQtmProf(dto);
//    	return Response.ok(res);
//    }
//	
//}
