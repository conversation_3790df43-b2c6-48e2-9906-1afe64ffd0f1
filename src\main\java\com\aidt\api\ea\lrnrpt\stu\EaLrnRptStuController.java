package com.aidt.api.ea.lrnrpt.stu;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptAlPlMluInfoDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptSlSpLrnDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTalkDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvListDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSbcLrnAtvStChkDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptTlSrhDto;
import com.aidt.api.ea.lrnrpt.dto.EaLrnRptWriteTpcDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name="[ea] 학습 리포트 - 학생", description="학습 리포트 - 학생")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/ea/cm/lrnrpt")
public class EaLrnRptStuController {

	@Autowired
	private EaLrnRptStuService eaLrnRptStuService;
	
	/**
     * 학습 리포트 > Lset's Talk 조회
     * @param
     * @return selectTalkList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 Lset's Talk 조회(공통)")
    @PostMapping(value = "/selectTalkList")
    public ResponseDto<List<EaLrnRptTalkDto>> selectTalkList( @RequestBody EaLrnRptTalkDto eaLrnRptTalkDto ){
    	log.info("Entrance selectTalkList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<EaLrnRptTalkDto> result = eaLrnRptStuService.selectTalkList(eaLrnRptTalkDto);
    	return Response.ok(result);
    }
	
	/**
     * 학습 리포트 > Lset's Write 조회
     * @param
     * @return selectSpLrnTcrList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 Lset's Write 조회(공통)")
    @PostMapping(value = "/selectWriteList")
    public ResponseDto<List<EaLrnRptWriteTpcDto>> selectWriteList( @RequestBody EaLrnRptWriteTpcDto eaLrnRptWritePtcDto ){
    	log.info("Entrance selectWriteList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<EaLrnRptWriteTpcDto> result = eaLrnRptStuService.selectWriteList(eaLrnRptWritePtcDto);
    	return Response.ok(result);
    }
	
	/**
     * 학습 리포트 > 선생님 추천 학습
     * @param
     * @return selectSlSpLrnRptList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 선생님추천학습 조회(공통)")
    @PostMapping(value = "/selectSlSpLrnRptList")
    public ResponseDto<List<EaLrnRptSlSpLrnDto>> selectSlSpLrnRptList( @RequestBody EaLrnRptSlSpLrnDto eaLrnRptSlSpLrnDto ){
    	log.info("Entrance selectSlSpLrnRptList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<EaLrnRptSlSpLrnDto> result = eaLrnRptStuService.selectSlSpLrnRptList(eaLrnRptSlSpLrnDto);
    	return Response.ok(result);
    }
	
	/**
     * 우리반 수업 학습활동상태 체크 (마지막 학습 체크)
     * 
     * @return ResponseDto<TlSbcLrnAtvStChkDto>
     */
    @Operation(summary="교과학습 학습활동상태 체크", description="교과학습 학습활동상태 체크하여 최근에 학습한 차시ID를 취득한다.")
    @PostMapping(value = "/selectTlRptAtvStChk")
    public ResponseDto<List<EaLrnRptTlSbcLrnAtvStChkDto>> selectTlRptAtvStChk( @RequestBody EaLrnRptTlSrhDto eaLrnRptAlPlEaEDto) {
        log.debug("Entrance selectTlRptAtvStChk");
        //CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        return Response.ok(eaLrnRptStuService.selectTlRptAtvStChk(eaLrnRptAlPlEaEDto));
    }
    
	
	/**
     * 학습 리포트 > 우리반 수업 > 대단원 및 차시 정보 조회
     * @param
     * @return selectTlRptList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 우리반 수업 초등 수학 조회(공통)")
    @PostMapping(value = "/selectTlRptList")
    public ResponseDto<List<EaLrnRptTlDto>> selectTlRptList( @RequestBody EaLrnRptTlSrhDto eaLrnRptTlSrhDto ){
    	log.info("Entrance selectTlRptList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<EaLrnRptTlDto> result = eaLrnRptStuService.selectTlRptList(eaLrnRptTlSrhDto);
    	return Response.ok(result);
    }
	
	/**
     * 학습 리포트 > 우리반 수업 > 차시에 따른 학습 현황 정보 조회
     * @param
     * @return selectTlRptList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 우리반 수업 차시에 따른 학습현황 조회(공통)")
    @PostMapping(value = "/selectRptLrnTocStatList")
    public ResponseDto<List<EaLrnRptTlSbcLrnAtvListDto>> selectRptLrnTocStatList( @RequestBody List<EaLrnRptTlSrhDto> eaLrnRptTlSrhDtoList ){
    	log.info("Entrance selectRptLrnTocStatList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<EaLrnRptTlSbcLrnAtvListDto> result = eaLrnRptStuService.selectRptLrnTocStatList(eaLrnRptTlSrhDtoList);
    	return Response.ok(result);
    }	
	
	/**
     * 학습 리포트 > AI 추천학습 > 수학
     * @param
     * @return selectAlPlMaRptList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 AI 추천학습 수학 조회(공통)")
    @PostMapping(value = "/selectAlPlMaRptList")
    public ResponseDto<List<EaLrnRptAlPlMluInfoDto>> selectAlPlMaRptList( @RequestBody EaLrnRptAlPlDto eaLrnRptAlPlEaEDto ){
    	log.info("Entrance selectAlPlMaRptList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<EaLrnRptAlPlMluInfoDto> result = eaLrnRptStuService.selectAlPlMaRptList(eaLrnRptAlPlEaEDto);
    	return Response.ok(result);
    }
	
	/**
     * 학습 리포트 > AI 추천학습 > 초등영어
     * @param
     * @return selectAlPlMaRptList
     */
//	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 AI 추천학습 초등 영어 조회(공통)")
//    @PostMapping(value = "/selectAlPlEaElRptList")
//    public ResponseDto<List<EaLrnRptAlPlMluInfoDto>> selectAlPlEaElRptList( @RequestBody EaLrnRptAlPlDto eaLrnRptAlPlEaEDto ){
//    	log.info("Entrance selectAlPlEaElRptList");
//    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//    	List<EaLrnRptAlPlMluInfoDto> result = eaLrnRptStuService.selectAlPlEaMHRptList2(eaLrnRptAlPlEaEDto);
//    	return Response.ok(result);
//    }
	
	/**
     * 학습 리포트 > AI 추천학습 > 초등영어
     * @param
     * @return selectAlPlMaRptList2
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 AI 추천학습 초등 영어 조회(공통)")
    @PostMapping(value = "/selectAlPlEaElRptList")
    public ResponseDto<Map<String,Object>> selectAlPlEaElRptList( @RequestBody EaLrnRptAlPlDto eaLrnRptAlPlEaEDto ){
    	log.info("Entrance selectAlPlEaElRptList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	return Response.ok(eaLrnRptStuService.selectAlPlEaMHRptList(eaLrnRptAlPlEaEDto));
    }
	
	/**
     * 학습 리포트 > AI 추천학습 > 고등영어
     * @param
     * @return selectAlPlMaRptList
     */
	@Tag(name="[ea] 학습 리포트(학생)", description="학습 리포트의 AI 추천학습 고등 영어 조회(공통)")
    @PostMapping(value = "/selectAlPlEaMHRptList")
    public ResponseDto<Map<String,Object>> selectAlPlEaMHRptList( @RequestBody EaLrnRptAlPlDto eaLrnRptAlPlEaEDto ){
    	log.info("Entrance selectAlPlEaMHRptList");
    	//CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
//    	List<EaLrnRptAlPlMluInfoDto> result = eaLrnRptStuService.selectAlPlEaMHRptList(eaLrnRptAlPlEaEDto);
//    	return Response.ok(result);
    	return Response.ok(eaLrnRptStuService.selectAlPlEaMHRptList(eaLrnRptAlPlEaEDto));
    }
	
}
