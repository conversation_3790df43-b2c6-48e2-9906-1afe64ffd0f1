package com.aidt.api.ea.lrnmg.tcr.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 교과 평가 - 학생 요청 Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaLrnMgTcrDtlReqDto {

	/* 공통*/
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="교사사용자ID")
	private String tcrUsrId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="페이지 번호")
	private int pageNo;
	
	@Parameter(name="페이지 크기")
	private int pageSize;	

	/* 조회 조건 */
	@Parameter(name="탭구분코드", required = true)
    @NotBlank(message = "{field.required}")
	private String tbscDvCd;
	
	@Parameter(name="평가탭 탭구분코드")
	private String tbscDvCdEv;	
	
	@Parameter(name="평가구분")
	private String evDvCd;
	
	@Parameter(name="조회대단원학습맵노드ID")
	private String srhLuLrmpNodId;
	
	@Parameter(name="정렬구분코드")
	private String srtDvCd;

	@Parameter(name="평가응시상태")
	private String evExmState;
	
	@Parameter(name="과제 미완료 여부")
	private String ncmplYn;
	
	
}
