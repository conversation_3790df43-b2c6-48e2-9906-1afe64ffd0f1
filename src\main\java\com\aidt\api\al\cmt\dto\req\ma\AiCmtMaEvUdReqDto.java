package com.aidt.api.al.cmt.dto.req.ma;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.ma.AiCmtMaEvUdDto;
import com.aidt.api.al.cmt.dto.req.cm.N02ReqDto;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc 수학 > 단원진단평가
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtMaEvUdReqDto {

    @Parameter(name="학교급코드(초등:E,중등:M,고등:H)", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^(E|M|H)$", message="학교급코드(초등:E,중등:M,고등:H)")
    private String schlGrdCd;

    @Valid
    @Parameter(name="성취수준 총평", required=true)
    private N02ReqDto total;

    @Valid
    @Parameter(name="AI 추천 콘텐츠 존재여부", required=true)
    private Boolean existAiRcmCtn;

    private String evId;

    public AiCmtMaEvUdDto toDto() {
        AiCmtLvlCalculator calculator = "H".equalsIgnoreCase(this.schlGrdCd)
                ? AiCmtLvlCalculator.MA_TYPE_50_80 : AiCmtLvlCalculator.MA_TYPE_60_80;

        return AiCmtMaEvUdDto.builder()
                .n02(this.total.toDto(calculator))
                .existAiRcmCtn(this.existAiRcmCtn)
                .build();
    }
    
}
