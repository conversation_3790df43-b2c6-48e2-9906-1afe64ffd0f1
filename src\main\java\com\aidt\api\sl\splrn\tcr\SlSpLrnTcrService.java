package com.aidt.api.sl.splrn.tcr;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.sl.splrn.dto.SlSpLrnEaAsnViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnNodDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-04 14:30:01
 * @modify date 2024-02-08 13:10:01
 * @desc SlTcrSpLrnService
 */
@Slf4j
@Service
public class SlSpLrnTcrService {
    final String MAPPER_NAMESPACE = "api.sl.splrn.tcr.";

    @Autowired
    private CommonDao commonDao;

	/**
	 * 특별학습 과제 제출 목록 조회
	 * 
	 * @param optTxbId
	 * @param userId
	 * @return selectSpLrnTcrEaExamList
	 */
	@Transactional(readOnly = true)
	public List<SlSpLrnEaAsnViewDto> selectSpLrnTcrEaSendDataList(String optTxbId, String userId, String txbId) {	
		//특별학습ID 조회
		List<SlSpLrnEaAsnViewDto> dataList = commonDao.selectList(MAPPER_NAMESPACE + "selectSpLrnTcrEaSendDataList",
				Map.of("optTxbId", optTxbId, "userId", userId, "txbId", txbId));
		
		List<SlSpLrnEaAsnViewDto> slSpLrnFroEaSendDtoList = new ArrayList<SlSpLrnEaAsnViewDto>();	//화면에 노출시킬 데이터 저장할 DTO
		if(dataList.size() > 0) {
			String bSpLrnId = "";
			String bSpLrnNm = "";
			List<SlSpLrnNodDto> nodList = new ArrayList<>();
			for(int ii = 0; ii < dataList.size(); ii++) {
				SlSpLrnNodDto nodDto = new SlSpLrnNodDto();	//노드를 저장할 dto
				SlSpLrnEaAsnViewDto spLrnDto = new SlSpLrnEaAsnViewDto();	//특별학습 저장할 dto
				
				String spLrnNodId = dataList.get(ii).getSpLrnNodId();
				String spLrnNodNm = dataList.get(ii).getSpLrnNodNm();
				String cSpLrnId = dataList.get(ii).getSpLrnId();
				String cSpLrnNm = dataList.get(ii).getSpLrnNm();
				// String cUseYn = dataList.get(i).getUseYn();
				if(ii == 0) {
					bSpLrnId = cSpLrnId;
					bSpLrnNm = cSpLrnNm;
				}
				if(!bSpLrnId.equals(cSpLrnId)) {
					spLrnDto.setSpLrnId(bSpLrnId);
					spLrnDto.setSpLrnNm(bSpLrnNm);
					spLrnDto.setSpLrnNodList(nodList);
					spLrnDto.setOptTxbId(optTxbId);
					spLrnDto.setUseYn(dataList.get(ii-1).getUseYn());
					
					slSpLrnFroEaSendDtoList.add(spLrnDto);
					
					nodList = new ArrayList<>(); // nodList 초기화
					bSpLrnNm = cSpLrnNm;
					bSpLrnId = cSpLrnId;
				}
				
				nodDto.setSpLrnNodId(spLrnNodId);
	            nodDto.setSpLrnNodNm(spLrnNodNm);
	            nodList.add(nodDto);
	            
	            // 마지막 데이터 처리
	            if (ii == dataList.size() - 1) {
	            	SlSpLrnEaAsnViewDto spLrnDto2 = new SlSpLrnEaAsnViewDto();
	            	spLrnDto2.setSpLrnId(cSpLrnId);
	            	spLrnDto2.setSpLrnNm(cSpLrnNm);
	            	spLrnDto2.setSpLrnNodList(nodList);
	            	spLrnDto2.setOptTxbId(optTxbId);
	            	spLrnDto2.setUseYn(dataList.get(ii).getUseYn());
	            	
	                slSpLrnFroEaSendDtoList.add(spLrnDto2);
	            }
			}
		}
		
		return slSpLrnFroEaSendDtoList;
	}
}
