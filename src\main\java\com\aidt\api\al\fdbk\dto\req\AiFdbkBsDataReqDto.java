package com.aidt.api.al.fdbk.dto.req;

import com.aidt.api.al.fdbk.dto.AiFdbkBsDataDto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-11 15:56:58
 * @modify date 2024-07-11 15:56:58
 * @desc
 */
@Data
@Builder
public class AiFdbkBsDataReqDto {

    private String usrId;

    private List<String> aiFdbkNoList;

    private String aiFdbkDvCd;

    public AiFdbkBsDataDto toDto() {
        return AiFdbkBsDataDto.builder()
                .aiFdbkNoList(this.aiFdbkNoList)
                .aiFdbkDvCd(this.aiFdbkDvCd)
                .build();
    }

}
