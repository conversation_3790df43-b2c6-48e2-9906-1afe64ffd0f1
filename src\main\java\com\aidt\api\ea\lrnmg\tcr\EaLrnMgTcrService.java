package com.aidt.api.ea.lrnmg.tcr;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.cm.mg.AlMgService;
import com.aidt.api.bc.mntr.dto.BcMntrDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgEaAsnStuDetailDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrDtlReqDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEaResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrEvResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrReqDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrTalkResDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrTxbReqDto;
import com.aidt.api.ea.lrnmg.tcr.dto.EaLrnMgTcrWriteResDto;
import com.aidt.common.CommonDao;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-01
 * @modify date 2024-05-01
 * @desc 학생 분석 Service
 */
@Slf4j
@Service
public class EaLrnMgTcrService {

	private final String MAPPER_NAMESPACE = "api.ea.lrnmg.tcr.";
	
	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private AlMgService alMgService;

	/**
	 * 학생 분석 조회 요청
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return ResponseList<List<EaLrnMgTcrResDto>>
	 */
	public Map<String, Object> selectEaStuAnMainList(EaLrnMgTcrReqDto reqDto) {
		String optTxbId = reqDto.getOptTxbId();
		String claId = reqDto.getClaId();
		//EaLrnMgTcrResDto resDto = commonDao.select(MAPPER_NAMESPACE + "selectEaStuAnMainInfo", reqDto);
		Map<String, Object> resDto = commonDao.select(MAPPER_NAMESPACE + "selectEaStuAnMainInfo", reqDto);
		//학생별 학습상태 리스트
		//List<EaLrnMgTcrMainListDto> usrLrnStList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnLrnStList", reqDto);
		//resDto.put("usrLrnStList", commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnLrnStList", reqDto));
		
		//종합 지도 필요(실시간)
		List<BcMntrDto> stuRtmAbnBhyTpList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuRtmAbnBhvTpList", reqDto);
		/*
		for (BcMntrDto dto : stuRtmAbnBhyTpList) {
			String gdeNeedTp = dto.getGdeNeedTp();
			if (gdeNeedTp != null && !gdeNeedTp.isEmpty()) {
		        ObjectMapper objectMapper = new ObjectMapper();
		        try {
		        	List<BcMntrJsonDto> gdeNeedTpJsonList = objectMapper.readValue(gdeNeedTp, new TypeReference<List<BcMntrJsonDto>>() {});
		            dto.setMntrJsonDto(gdeNeedTpJsonList);
		        } catch (JsonProcessingException e) {
		        	log.debug("JsonProcessingException");
		            dto.setMntrJsonDto(new ArrayList<>());
		        }
		    } else {
		        dto.setMntrJsonDto(new ArrayList<>());
		    }
		}
		*/

		//종합 성취율
		List<Map<String, Object>> stuAnLuList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnLuList", reqDto);
				
		//학생 정보 조회
		List<EaLrnMgTcrReqDto> userDto = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrClassStuList", reqDto);
		
		//학습 시간 정보 제공
		List<Map<String, Object>> lrnTimeUsrList = commonDao.selectList(MAPPER_NAMESPACE + "selectLrnSumDataList", reqDto);
		
		// 교과, 선생님 추천학습 학습 진행율
		List<Map<String, Object>> tlDataList = selectEaStuSumRtTxbSpList(optTxbId, claId);
		List<Map<String, Object>> slDataList = selectEaStuSumRtSpList(optTxbId, claId);
		
		//과제 진행율
		List<EaLrnMgTcrEaResDto> eaUsrDataList = selectEaStuAmTcrEaList(reqDto);
		
		//평가 진행율
		List<EaLrnMgTcrEvResDto> evUsrDataList = selectEaStuAmTcrEvList(reqDto);
		
		//챌린지 정보
		List<Map<String, Object>> stuLrnChlgList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuLrnChlgList", reqDto);
		
		//resDto.setUsrLrnStList(usrLrnStList);  
		resDto.put("userList", userDto);
		resDto.put("stuRtmAbnBhyTpList", stuRtmAbnBhyTpList);
		resDto.put("stuAnLuList", stuAnLuList);
		resDto.put("lrnTimeUsrList", lrnTimeUsrList);
		resDto.put("tlDataList", tlDataList);
		resDto.put("slDataList", slDataList);
		resDto.put("eaUsrDataList", eaUsrDataList);
		resDto.put("evUsrDataList", evUsrDataList);
		resDto.put("stuLrnChlgList", stuLrnChlgList);
		
		return resDto;
	}
	
	/**
	 * 학생별 우리반수업(개념), 선생님추천학습 진도율정보 목록조회
	 * @param reqDto
	 * @return
	 */
	public List<Map<String, Object>> selectEaStuSumRtTxbSpList(String optTxbId, String claId) {
		List<Map<String, Object>> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuSumRtTxbSpList", Map.of("optTxbId",optTxbId, "claId", claId));
		//List<Map<String, Object>> list2 = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuSumRtSpList", Map.of("optTxbId",optTxbId, "claId", claId));
		
		List<Map<String, Object>> rtnList  = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> map : list) {
			Map<String, Object> itm = new HashMap<String, Object>();
			itm.put("usrId", map.get("USR_ID"));
			itm.put("tlRate", map.get("TL_RATE"));
			//itm.put("slRate", map.get("SL_RATE"));
			rtnList.add(itm);
		}
		return rtnList;

	}
	
	/**
	 * 학생별 우리반수업(개념), 선생님추천학습 진도율정보 목록조회
	 * @param reqDto
	 * @return
	 */
	public List<Map<String, Object>> selectEaStuSumRtSpList(String optTxbId, String claId) {
		List<Map<String, Object>> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuSumRtSpList", Map.of("optTxbId",optTxbId, "claId", claId));
		
		List<Map<String, Object>> rtnList  = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> map : list) {
			Map<String, Object> itm = new HashMap<String, Object>();
			itm.put("usrId", map.get("USR_ID"));
			itm.put("slRate", map.get("SL_RATE"));
			rtnList.add(itm);
		}
		return rtnList;

	}

	
	/**
	 * 학생 분석 상세 조회 요청
	 *
	 * @param EaLrnMgTcrDtlReqDto
	 * @return List<Map<String, Object>>
	 */
	public List<Map<String, Object>> selectEaStuAnDtlList(EaLrnMgTcrDtlReqDto reqDto) {

		if(reqDto.getTbscDvCd().equals("0")) { // 교과학습
			return commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnDtlTxbList", reqDto);
		}
		else if(reqDto.getTbscDvCd().equals("1")) { // AI학습
			return null; 
			//commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnDtlTxbList", reqDto);
		}
		else if(reqDto.getTbscDvCd().equals("2")) { //평가 AND DIY 평가 탭
			return commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnDtlEvList", reqDto);			
		}
		else if(reqDto.getTbscDvCd().equals("3")) { //과제
			return commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnDtlAsnList", reqDto);
		}
		
		return null;
	}
	
	/**
	 * Let's talk 대단원 리스트
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return List<EaLrnMgTcrResDto>
	 */
	public List<EaLrnMgTcrResDto> selectEaStuAmTcrTalkLesList(EaLrnMgTcrReqDto reqDto) {
		List<EaLrnMgTcrResDto> resultList = new ArrayList<EaLrnMgTcrResDto>();
		resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrTalkLesList",reqDto);
		return resultList;
	}
	
	/**
	 * Let's talk 단원별 학생 정보
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return List<EaLrnMgTcrResDto>
	 */
	public List<EaLrnMgTcrTalkResDto> selectEaStuAmTcrTalkUsrList(EaLrnMgTcrReqDto reqDto) {
		List<EaLrnMgTcrTalkResDto> resultList = new ArrayList<EaLrnMgTcrTalkResDto>();
		String optTxbId = reqDto.getOptTxbId();
		String kmmpNodId = reqDto.getKmmpNodId();
		String sbjCd = reqDto.getSbjCd();	// 과목 코드

		//교사에 따른 학생리스트 조회
		
		List<EaLrnMgTcrReqDto> classStuList = new ArrayList<>();	//학생 리스트
		classStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrClassStuList", reqDto);
		
		if(classStuList.size() > 0) {
			for(int i = 0; i < classStuList.size(); i++) {
				EaLrnMgTcrReqDto reqDto2 = new EaLrnMgTcrReqDto();
				EaLrnMgTcrTalkResDto resultDto = new EaLrnMgTcrTalkResDto();
				resultDto.setUsrNo(classStuList.get(i).getUsrNo()); //학생 번호
				resultDto.setUsrId(classStuList.get(i).getUsrId()); //학생 id
				resultDto.setUsrNm(classStuList.get(i).getUsrNm());	//학생명
				resultDto.setLrnrVelTpCd(classStuList.get(i).getLrnrVelTpCd());	 //학생의 학습 수준		
				reqDto2.setOptTxbId(optTxbId);
				reqDto2.setKmmpNodId(kmmpNodId);
				reqDto2.setUsrId(classStuList.get(i).getUsrId());
				
				//let's talk 데이터 조회
				EaLrnMgTcrTalkResDto talkInfoDto = commonDao.select(MAPPER_NAMESPACE + "selectEaStuAmTalkUsrInfo", reqDto2);
				resultDto.setOptTxbId(talkInfoDto.getOptTxbId());
				resultDto.setKmmpNodId(talkInfoDto.getKmmpNodId());
				resultDto.setKmmpNodNm(talkInfoDto.getKmmpNodNm());
				resultDto.setTotTopicsCount(talkInfoDto.getTotTopicsCount());
				resultDto.setComplTopicsCount(talkInfoDto.getComplTopicsCount());
				resultDto.setProgressRate(talkInfoDto.getProgressRate());
				resultDto.setLastUpdated(talkInfoDto.getLastUpdated());
				resultDto.setLrnTime(talkInfoDto.getLrnTime());
				resultDto.setFirstUpdated(talkInfoDto.getFirstUpdated());
				resultDto.setDlClExist(talkInfoDto.getDlClExist());
				resultList.add(resultDto);
			}
		}
		
		//resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrTalkUsrList",reqDto);	
		
		return resultList;
	}
	
	/**
	 * Let's write 대단원 리스트
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return List<EaLrnMgTcrResDto>
	 */
	public List<EaLrnMgTcrResDto> selectEaStuAmTcrWriteLesList(EaLrnMgTcrReqDto reqDto) {
		List<EaLrnMgTcrResDto> resultList = new ArrayList<EaLrnMgTcrResDto>();
		resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrWriteLesList",reqDto);
		return resultList;
	}

	/**
	 * Let's write 단원별 학생 정보
	 *
	 * @param EaLrnMgTcrReqDto
	 * @return List<EaLrnMgTcrResDto>
	 */
	public List<EaLrnMgTcrWriteResDto> selectEaStuAmTcrWriteList(EaLrnMgTcrReqDto reqDto) {
		List<EaLrnMgTcrWriteResDto> resultList = new ArrayList<EaLrnMgTcrWriteResDto>();
		String optTxbId = reqDto.getOptTxbId();
		String kmmpNodId = reqDto.getKmmpNodId();
		String sbjCd = reqDto.getSbjCd();	// 과목 코드

		//교사에 따른 학생리스트 조회
		
		List<EaLrnMgTcrReqDto> classStuList = new ArrayList<>();	//학생 리스트
		//classStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrClassStuList", reqDto);
		classStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaWriLvStuList", reqDto);	//Let's Write 학생 단원별 학습자 수준 조회
		
		if(classStuList.size() > 0) {
			for(int i = 0; i < classStuList.size(); i++) {
				EaLrnMgTcrReqDto reqDto2 = new EaLrnMgTcrReqDto();
				EaLrnMgTcrWriteResDto resultDto = new EaLrnMgTcrWriteResDto();
				resultDto.setUsrNo(classStuList.get(i).getUsrNo()); //학생 번호
				resultDto.setUsrId(classStuList.get(i).getUsrId()); //학생 id
				resultDto.setUsrNm(classStuList.get(i).getUsrNm());	//학생명
				resultDto.setLrnrVelTpCd(classStuList.get(i).getLrnrVelTpCd());	 //학생의 학습 수준		
				reqDto2.setOptTxbId(optTxbId);
				reqDto2.setKmmpNodId(kmmpNodId);
				reqDto2.setUsrId(classStuList.get(i).getUsrId());

				//let's talk 데이터 조회
				EaLrnMgTcrWriteResDto writeInfoDto = commonDao.select(MAPPER_NAMESPACE + "selectEaStuAmTcrWriteList", reqDto2);
				resultDto.setOptTxbId(writeInfoDto.getOptTxbId());
				resultDto.setLluKmmpNodId(writeInfoDto.getLluKmmpNodId());
				resultDto.setLluKmmpNodNm(writeInfoDto.getLluKmmpNodNm());
				resultDto.setTotTopicsCount(writeInfoDto.getTotTopicsCount());
				resultDto.setComplTopicsCount(writeInfoDto.getComplTopicsCount());
				resultDto.setProgressRate(writeInfoDto.getProgressRate());
				resultDto.setLastUpdated(writeInfoDto.getLastUpdated());
				resultDto.setWrtStNm(writeInfoDto.getWrtStNm());
				resultDto.setEditStNm(writeInfoDto.getEditStNm());
				resultDto.setLrnTmScnt(writeInfoDto.getLrnTmScnt());
				resultDto.setPgrsStCd(writeInfoDto.getPgrsStCd());
				resultDto.setApFailYn(writeInfoDto.getApFailYn());
				resultDto.setTcrSavDtm(writeInfoDto.getTcrSavDtm());
				resultList.add(resultDto);
			}
		}
		
		return resultList;
	}
	
	/**
	 * 학생별 과제 목록 조회
	 * @param reqDto
	 * @return
	 */
	public List<EaLrnMgTcrEaResDto> selectEaStuAmTcrEaList(EaLrnMgTcrReqDto reqDto) {
		
		List<EaLrnMgTcrEaResDto> resultList = new ArrayList<EaLrnMgTcrEaResDto>();
		String optTxbId = reqDto.getOptTxbId();
		String kmmpNodId = reqDto.getKmmpNodId();
		String sbjCd = reqDto.getSbjCd();	// 과목 코드
		
		resultList  = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrEaList", reqDto);
		
		return resultList;
	}
	
	/**
	 * 학생별 과제 상세 조회
	 * @param reqDto
	 * @return
	 */
	public List<EaLrnMgEaAsnStuDetailDto> selectEaStuAmTcrEaDetail(EaLrnMgTcrReqDto reqDto) {
		List<EaLrnMgEaAsnStuDetailDto> resultList = new ArrayList<EaLrnMgEaAsnStuDetailDto>();
		String optTxbId = reqDto.getOptTxbId();
		String kmmpNodId = reqDto.getKmmpNodId();
		String sbjCd = reqDto.getSbjCd();	// 과목 코드
		
		//ai 지식맵 정보 가져오기
    	//String aiKmmpNodId = alMgService.selectKmmpNodIdByLrmpNodId(reqDto.getDropdown2());
    	//reqDto.setAiSearchOption(aiKmmpNodId);
    	
    	if(!StringUtils.isEmpty(reqDto.getDropdown2())) {
    		//ai 지식맵ID 조회
        	List<String> aiKmmpNodIds = alMgService.selectKmmpNodIdByLrmpNodId(reqDto.getDropdown2());
        	reqDto.setAiSearchOptionList(aiKmmpNodIds);
    	}
    	
    	
		resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrEaDetail", reqDto);
		return resultList;
	}
	
	
	/**
	 * 학생별 우리반수업분석 내용을 표시
	 * @param reqDto
	 * @return
	 */
	public List<Map<String, Object>> selectEaStuTcTxbList(EaLrnMgTcrTxbReqDto reqDto) {
				
		List<Map<String, Object>> list = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuTcTxbList", reqDto);
		List<Map<String, Object>> rtnList  = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> map : list) {
			Map<String, Object> itm = new HashMap<String, Object>();
			itm.put("usrId", map.get("USR_ID"));
			itm.put("usrNm",  map.get("USR_NM"));
			itm.put("stuNo", map.get("STU_NO"));
			itm.put("lrnrVelTpCd", map.get("LRNR_VEL_TP_CD"));
			itm.put("clRate", map.get("CL_RATE"));
			itm.put("wbRate", map.get("WB_RATE"));
			itm.put("wbYn", map.get("WB_YN"));
			itm.put("exYn", map.get("EX_YN"));
			itm.put("totLrnMin", map.get("TOT_LRN_MIN"));
			itm.put("evId", map.get("EV_ID"));
			itm.put("exCmplYn", map.get("EX_CMPL_YN"));
			itm.put("fnlQstCnt", map.get("FNL_QST_CNT"));
			itm.put("cansCnt", map.get("CANS_CNT"));
			itm.put("evDtlDvCd", map.get("EV_DTL_DV_CD"));
			itm.put("txmStrYn", map.get("TXM_STR_YN"));
			itm.put("evTmScnt", map.get("EV_TM_SCNT"));
			itm.put("clTotRate", map.get("CL_TOT_RATE"));
			itm.put("cntTotCount", map.get("CNT_TOT_COUNT"));
			itm.put("cntUsrCount", map.get("CNT_USR_COUNT"));
			itm.put("totalLrnSum", map.get("TOTAL_LRN_SUM"));
			rtnList.add(itm);
		}
		return rtnList;
	}
	
	/**
	 * 학생별 평가 목록 조회
	 * @param reqDto
	 * @return
	 */
	public List<EaLrnMgTcrEvResDto> selectEaStuAmTcrEvList(EaLrnMgTcrReqDto reqDto) {
		List<EaLrnMgTcrEvResDto> resultList = new ArrayList<EaLrnMgTcrEvResDto>();
		String optTxbId = reqDto.getOptTxbId();
		String kmmpNodId = reqDto.getKmmpNodId();
		String sbjCd = reqDto.getSbjCd();	// 과목 코드
		resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrEvList", reqDto);
		return resultList;
	}

	/**
	 * 학생별 평가 상세 조회
	 * @param reqDto
	 * @return
	 */
	public List<Map<String, Object>> selectEaStuAmTcrEvDetail(EaLrnMgTcrReqDto reqDto) {
		log.info(reqDto.toString());
		return commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAmTcrEvDetail", reqDto);	
	}

	/**
	 * 학생별 추천학 진행율조회
	 * @param optTxbId
	 * @param claId
	 * @return
	 */
	public List<Map<String, Object>> selectEaStuRcmLrnList(String optTxbId, String claId) {
	    List<Map<String, Object>> mList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuRcmLrnList", Map.of("optTxbId", optTxbId, "claId", claId));
	    List<Map<String, Object>> dList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuRcmLrnDtlList", Map.of("optTxbId", optTxbId));
	    List<Map<String, Object>> rtnList = new ArrayList<>();

	    // 사용자별 데이터 집계
	    Map<String, List<Map<String, Object>>> userGroupMap = dList.stream()
	        .collect(Collectors.groupingBy(dMap -> dMap.get("USR_ID").toString()));

	    // 사용자별로 각 spLrnId 데이터 처리
	    mList.forEach(mRowMap -> {
	        Map<String, Object> itmMap = new HashMap<>();
	        String usrId = mRowMap.get("USR_ID").toString();
	        String usrNm = mRowMap.get("USR_NM") != null ? mRowMap.get("USR_NM").toString() : null;
	        String stuNo = mRowMap.get("STU_NO") != null ? mRowMap.get("STU_NO").toString() : "0";

	        String cntRmdStr = mRowMap.get("CNT_RMD") != null ? mRowMap.get("CNT_RMD").toString() : "0";
	        int cntRmd = Integer.valueOf(cntRmdStr);
	        itmMap.put("usrId", usrId); // 사용자ID
	        itmMap.put("usrNm", usrNm); // 사용자NM
	        itmMap.put("lrnrVelTpCd", mRowMap.get("LRNR_VEL_TP_CD")); // 사용자ID
	        itmMap.put("stuNo", stuNo); // 학번
	        itmMap.put("cntRmd", mRowMap.get("CNT_RMD")); // 추천학습수
	        itmMap.put("lrnTmSec", mRowMap.get("LRN_TM_SEC")); // 추천시간(분)
	        itmMap.put("lastLrnYmd", mRowMap.get("LAST_LRN_YMD")); // 마지막수업일자

	        // 추천수업별 진척정보 설정
	        List<Map<String, Object>> childList = new ArrayList<>();
	        int totSlCnt = 0; // 개별 학생의 전체 선생님 추천학습개수
	        int totSlClCnt = 0; // 개별 학생의 총 완료한 추천학습개수

	     // 현재 사용자 ID에 해당하는 데이터 그룹화
	        List<Map<String, Object>> userSpecificList = userGroupMap.getOrDefault(usrId, Collections.emptyList());

	        // 각 spLrnId에 대해 집계
	        Map<String, Map<String, Object>> spLrnStats = new HashMap<>();
	        for (Map<String, Object> dMap : userSpecificList) {
	            String spLrnId = dMap.get("SP_LRN_ID").toString();
	            String spLrnNm = dMap.get("SP_LRN_NM").toString();
	            int spLrnTotCnt = Integer.parseInt(dMap.get("TOT_CNT").toString()); // 총 개수
	            int spLrnTotCntCl = Integer.parseInt(dMap.get("TOT_CNT_CL").toString()); // 완료 개수

	         // spLrnId에 대해 통계 업데이트
	            spLrnStats.putIfAbsent(spLrnId, new HashMap<>());
	            Map<String, Object> stats = spLrnStats.get(spLrnId);
	            
	            // 기존 값 가져오기 및 초기화
	            int totalCount = (Integer) stats.getOrDefault("totalCount", 0);
	            int completedCount = (Integer) stats.getOrDefault("completedCount", 0);
	            String storedSpLrnNm = (String) stats.getOrDefault("spLrnNm", spLrnNm);

	            // 누적
	            stats.put("totalCount", totalCount + spLrnTotCnt); // 총 개수 누적
	            stats.put("completedCount", completedCount + spLrnTotCntCl); // 완료 개수 누적
	            stats.put("spLrnNm", storedSpLrnNm); // 수업명 저장
	        }

	        // 통계 결과를 childList에 추가
	        for (Map.Entry<String, Map<String, Object>> entry : spLrnStats.entrySet()) {
	        	String spLrnId = entry.getKey();
	            Map<String, Object> stats = entry.getValue();
	            int totalCount = (Integer) stats.get("totalCount");
	            int completedCount = (Integer) stats.get("completedCount");
	            String spLrnNm = (String) stats.get("spLrnNm");

	            double rate = totalCount > 0 ? (completedCount / (double) totalCount) * 100 : 0;

	            Map<String, Object> childMap = new HashMap<>();
	            childMap.put("spLrnId", spLrnId);
	            childMap.put("spLrnNm", spLrnNm);
	            childMap.put("totalCount", totalCount);
	            childMap.put("completedCount", completedCount);
	            childMap.put("rate", rate);

	            childList.add(childMap);

	            // 전체 진행율 계산
	            totSlCnt += totalCount;
	            totSlClCnt += completedCount;
	        }

	        itmMap.put("childList", childList);
	        itmMap.put("totSlClCnt", totSlClCnt);
	        itmMap.put("totSlCnt", totSlCnt);
	        itmMap.put("rate", totSlCnt > 0 ? (totSlClCnt / (double) totSlCnt) * 100 : 0);
	        rtnList.add(itmMap);
	    });

	    return rtnList;
	}
	
	
	public List<Map<String, Object>> selectEaStuRcmLrnList2(String optTxbId, String claId) {
		List<Map<String, Object>> mList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuRcmLrnList", Map.of("optTxbId", optTxbId, "claId", claId));
		List<Map<String, Object>> dList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuRcmLrnDtlList", Map.of("optTxbId", optTxbId));
		List<Map<String, Object>> rtnList  = new ArrayList<Map<String, Object>>();
		mList.forEach(mRowMap -> {
			Map<String, Object> itmMap = new HashMap<String, Object>();
			String usrId = mRowMap.get("USR_ID").toString();
			String usrNm = mRowMap.get("USR_NM").toString();
			String stuNo = mRowMap.get("STU_NO").toString();
			//int cntRmd = Integer.valueOf(mRowMap.get("CNT_RMD").toString() != null ? mRowMap.get("CNT_RMD").toString() : "0");
			String cntRmdStr = mRowMap.get("CNT_RMD") != null ? mRowMap.get("CNT_RMD").toString() : "0";
	        int cntRmd = Integer.valueOf(cntRmdStr);
			itmMap.put("usrId", usrId); // 사용자ID
			itmMap.put("usrNm", usrNm); // 사용자NM
			itmMap.put("lrnrVelTpCd", mRowMap.get("LRNR_VEL_TP_CD")); // 사용자ID
			itmMap.put("stuNo", stuNo); // 학번
			itmMap.put("cntRmd", mRowMap.get("CNT_RMD")); // 추천학습수
			itmMap.put("lrnTmSec", mRowMap.get("LRN_TM_SEC")); // 추천시간(분)
			itmMap.put("lastLrnYmd", mRowMap.get("LAST_LRN_YMD")); // 마지막수업일자
			String spLrnId = "";
			String spLrnNm = "";
			int cntNod = 0;
			int cntCmpl = 0;
//			int ii = 0;
			int chk = 0;
			double sumRate = 0;
			String bfSpLrnId = "";
			// 추천수업별 진척정보 설정
			List<Map<String, Object>> childList  = new ArrayList<Map<String, Object>>();
			for(Map<String, Object> dMap: dList) {
//				ii++;
				Map<String, Object> childMap = new HashMap<String, Object>();
				String rowUsrId = dMap.get("USR_ID").toString();
				if (!rowUsrId.equals(usrId)) {
					continue;
				}
				chk = 1;
				spLrnId = dMap.get("SP_LRN_ID").toString();
				spLrnNm = dMap.get("SP_LRN_NM").toString();
				if (!"".equals(bfSpLrnId) && !bfSpLrnId.equals(spLrnId)) {
					childMap.put("spLrnNm", spLrnNm);
					childMap.put("rate", (cntCmpl / cntNod) * 100);
					childList.add(childMap);
					sumRate += (cntCmpl / cntNod) * 100; // 전체 진행율 계산용으로 누적
					cntNod = 0;
					cntCmpl = 0;
					chk = 2;
				}
				if ("Y".equals(dMap.get("FIN_YN").toString())) {
					cntCmpl++;
				}
				cntNod++;
				bfSpLrnId = spLrnId;
			}
			// 마지막 행이 처리되지 처리되지 않은 경우
			if (chk == 1) {
				Map<String, Object> childMap = new HashMap<String, Object>();
				childMap.put("spLrnNm", spLrnNm); // 추천수업명
				childMap.put("rate", (cntCmpl / cntNod) * 100); // 추천수업진척율
				childList.add(childMap);
			}
			//sumRate += (cntCmpl / cntNod) * 100; // 전체 진행율 계산용으로 누적
			sumRate += cntNod > 0 ? (cntCmpl / (double) cntNod) * 100 : 0; // 전체 진행율 계산용으로 누적
			itmMap.put("childList", childList);
			itmMap.put("rate", sumRate/cntRmd);
			rtnList.add(itmMap); // 학생 총진척율
		});
		return rtnList;
	}
	
	public List<Map<String, Object>> selectAlEnOnlyMluList(EaLrnMgTcrReqDto reqDto) {
		List<Map<String, Object>> resultList  = new ArrayList<Map<String, Object>>();
		String optTxbId = reqDto.getOptTxbId();
		String kmmpNodId = reqDto.getKmmpNodId();
		String sbjCd = reqDto.getSbjCd();	// 과목 코드
		resultList = commonDao.selectList(MAPPER_NAMESPACE + "selectAlEnOnlyMluList", reqDto);
		return resultList;
	}
	
	/**
	 * 학생 종합 성취 전체 조회
	 * @return
	 */
	public List<Map<String, Object>> selectEaStuAnLuList(EaLrnMgTcrReqDto reqDto) {
		//종합 성취율
		List<Map<String, Object>> rtnList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuAnLuList", reqDto);
		
		return rtnList;
	}
	
	/**
	 * 학생별  지도 필요
	 * @return
	 */
	public List<Map<String, Object>> selectStuGdeNeedList(EaLrnMgTcrReqDto reqDto) {
		List<Map<String, Object>> rtnList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuGdeNeedList", reqDto);
		
		return rtnList;
	}
	
	
	/**
	 * 학생별 추천학습 진행율조회 20241115
	 * @param optTxbId
	 * @param claId
	 * @return
	 */
	public List<Map<String, Object>> selectStuRcmLrnList(String optTxbId, String claId) {
	    List<Map<String, Object>> mList = commonDao.selectList(MAPPER_NAMESPACE + "selectEaStuRcmLrnList", Map.of("optTxbId", optTxbId, "claId", claId));
	    List<Map<String, Object>> dList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuRcmLrnTotList", Map.of("optTxbId", optTxbId));
	    List<Map<String, Object>> rtnList = new ArrayList<>();
	    
	    // dList를 usrId를 키로 하는 Map으로 변환하여 효율적인 검색이 가능하도록 구성
	    Map<String, Object> dListMap = dList.stream()
	            .collect(Collectors.toMap(
	                    dRow -> dRow.get("USR_ID").toString(),
	                    dRow -> dRow,
	                    (existing, replacement) -> existing // 중복 키가 발생하면 기존 값 유지
	            ));
	    
	    // 사용자별로 각 spLrnId 데이터 처리
	    mList.forEach(mRowMap -> {
	        Map<String, Object> itmMap = new HashMap<>();
	        String usrId = mRowMap.get("USR_ID").toString();
	        String usrNm = mRowMap.get("USR_NM") != null ? mRowMap.get("USR_NM").toString() : null;
	        String stuNo = mRowMap.get("STU_NO") != null ? mRowMap.get("STU_NO").toString() : "0";

	        String cntRmdStr = mRowMap.get("CNT_RMD") != null ? mRowMap.get("CNT_RMD").toString() : "0";
	        int cntRmd = Integer.valueOf(cntRmdStr);
	        itmMap.put("usrId", usrId); // 사용자ID
	        itmMap.put("usrNm", usrNm); // 사용자NM
	        itmMap.put("lrnrVelTpCd", mRowMap.get("LRNR_VEL_TP_CD")); // 사용자ID
	        itmMap.put("stuNo", stuNo); // 학번
	        itmMap.put("cntRmd", mRowMap.get("CNT_RMD")); // 추천학습수
	        itmMap.put("lrnTmSec", mRowMap.get("LRN_TM_SEC")); // 추천시간(분)
	        itmMap.put("lastLrnYmd", mRowMap.get("LAST_LRN_YMD")); // 마지막수업일자
	        
	     // dList에서 usrId 일치하는 항목 찾기
	        Map<String, Object> dRowMap = (Map<String, Object>) dListMap.get(usrId);
	        if (dRowMap != null && dRowMap.get("PROGRESS_RATE") != null) {
	            itmMap.put("rate", dRowMap.get("PROGRESS_RATE")); // 일치하면 rate 추가
	        } else {
	            itmMap.put("rate", null); // 일치하지 않으면 rate를 null로 설정
	        }
	        
	        rtnList.add(itmMap);
	    });

	    return rtnList;
	}

	/**
	 * 학생별 추천학습 학생별 상세 진도율 20241115
	 * @param optTxbId
	 * @param claId
	 * @return
	 */
	public Map<String, Object> selectStuRcmDetailList(EaLrnMgTcrReqDto reqDto) {
		String optTxbId = reqDto.getOptTxbId();
		String usrId = reqDto.getUsrId();
		List<Map<String, Object>> dList = commonDao.selectList(MAPPER_NAMESPACE + "selectStuRcmDetailList", Map.of("optTxbId", optTxbId, "usrId", usrId));
	    Map<String, Object> rtnList = new HashMap<>();
	    
        Map<String, Object> itmMap = new HashMap<>();

        // 추천수업별 진척정보 설정
        List<Map<String, Object>> childList = new ArrayList<>();

        // 각 spLrnId에 대해 집계
        Map<String, Map<String, Object>> spLrnStats = new LinkedHashMap<>();
        for (Map<String, Object> dMap : dList) {
            String spLrnId = dMap.get("SP_LRN_ID").toString();
            String spLrnNm = dMap.get("SP_LRN_NM").toString();
            int spLrnTotCnt = Integer.parseInt(dMap.get("TOT_CNT").toString()); // 총 개수
            int spLrnTotCntCl = Integer.parseInt(dMap.get("TOT_CNT_CL").toString()); // 완료 개수

            // spLrnId에 대해 통계 업데이트
            spLrnStats.putIfAbsent(spLrnId, new HashMap<>());
            Map<String, Object> stats = spLrnStats.get(spLrnId);
            
            // 기존 값 가져오기 및 초기화
            int totalCount = (Integer) stats.getOrDefault("totalCount", 0);
            int completedCount = (Integer) stats.getOrDefault("completedCount", 0);
            String storedSpLrnNm = (String) stats.getOrDefault("spLrnNm", spLrnNm);

            // 누적
            stats.put("totalCount", totalCount + spLrnTotCnt); // 총 개수 누적
            stats.put("completedCount", completedCount + spLrnTotCntCl); // 완료 개수 누적
            stats.put("spLrnNm", storedSpLrnNm); // 수업명 저장
        }

        // 통계 결과를 childList에 추가
        for (Map.Entry<String, Map<String, Object>> entry : spLrnStats.entrySet()) {
        	String spLrnId = entry.getKey();
            Map<String, Object> stats = entry.getValue();
            int totalCount = (Integer) stats.get("totalCount");
            int completedCount = (Integer) stats.get("completedCount");
            String spLrnNm = (String) stats.get("spLrnNm");

            double rate = totalCount > 0 ? (completedCount / (double) totalCount) * 100 : 0;

            Map<String, Object> childMap = new HashMap<>();
            childMap.put("spLrnId", spLrnId);
            childMap.put("spLrnNm", spLrnNm);
            childMap.put("totalCount", totalCount);
            childMap.put("completedCount", completedCount);
            childMap.put("rate", rate);

            childList.add(childMap);

        }

        rtnList.put("childList", childList);
	    return rtnList;
	}
	

}

