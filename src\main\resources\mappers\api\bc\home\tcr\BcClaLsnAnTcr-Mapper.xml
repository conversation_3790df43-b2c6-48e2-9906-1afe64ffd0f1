<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.clalsnan.tcr">

    <!-- [우리반 수업 분석] 차시목록 -->
    <select id="selectTcList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        /** BcClaLsnAnTcr-Mapper.xml - selectTcList */
        select 	MAX(llu.lrmp_nod_id) as llu_nod_id
		     , MAX(llu.lrmp_nod_nm) as llu_nod_nm
		     , tc.lrmp_nod_id as tc_nod_id
		     , MAX(tc.lrmp_nod_nm) as tc_nod_nm
		     , MAX(tc.lu_no_use_yn) AS tcLuNoUseYn
		     , MAX(tc.rcstn_no) AS   tcRcstnNo
		     , MAX(llu.rcstn_ordn) as llu_ordn
		     , MAX(tc.rcstn_ordn) as tc_ordn
		     , MAX(llu.lckn_yn) as llu_lckn_yn
		     , MAX(tc.lckn_yn) as tc_lckn_yn
		     , MAX(llu.LU_NO_USE_YN) AS luNoUseYn
		     , MAX(llu.RCSTN_NO) AS   luRcstnNo
		     , lag(tc.lrmp_nod_id) over (order by llu.rcstn_ordn, tc.rcstn_ordn) as prev_tc_nod_id
		     , lag(tc.lckn_yn) over (order by llu.rcstn_ordn, tc.rcstn_ordn) as prev_lckn_yn
		     , lead(tc.lrmp_nod_id) over (order by llu.rcstn_ordn, tc.rcstn_ordn) as next_tc_nod_id
		     , lead(tc.lckn_yn) over (order by llu.rcstn_ordn, tc.rcstn_ordn) as next_lckn_yn
		     , MAX(IFNULL(r.LRN_ATV_ID, '')) LRN_ATV_ID_EX
		     , MAX(IFNULL(n.LRN_ATV_ID, '')) LRN_ATV_ID_WB
		  from lms_lrm.tl_sbc_lrn_nod_rcstn llu
		 inner join (
		    select a.lrmp_nod_id
		         , a.lrmp_nod_nm
		         , a.rcstn_ordn
		         , a.lckn_yn
		         , a.lu_no_use_yn
		         ,a.rcstn_no
		         , a.llu_nod_id
		      from lms_lrm.tl_sbc_lrn_nod_rcstn a
		     where a.use_yn = 'Y'
		       and a.dpth = '4'
		       and a.lu_eps_yn = 'Y'
		       and a.opt_txb_id = #{optTxbId}
		 ) tc
		    on llu.lrmp_nod_id = tc.llu_nod_id
		    LEFT JOIN lms_lrm.tl_sbc_lrn_atv_rcstn r
		   	ON r.opt_txb_id = #{optTxbId}
		   	and tc.LRMP_NOD_ID = r.LRMP_NOD_ID
		   	AND r.LRN_STP_ID IN (SELECT s.LRN_STP_ID FROM lms_cms.bc_lrn_stp s
		   			WHERE s.LRN_STP_ID = r.LRN_STP_ID
						AND s.LRN_STP_DV_CD = 'EX')
			LEFT JOIN lms_lrm.tl_sbc_lrn_atv_rcstn n
		   	ON n.opt_txb_id = #{optTxbId}
		   	and tc.LRMP_NOD_ID = n.LRMP_NOD_ID
		   	AND n.LRN_STP_ID IN (SELECT s.LRN_STP_ID FROM lms_cms.bc_lrn_stp s
		   			WHERE s.LRN_STP_ID = n.LRN_STP_ID
						AND s.LRN_STP_DV_CD = 'WB')
		 where llu.lu_eps_yn = 'Y'
		   and llu.opt_txb_id = #{optTxbId}
		  GROUP BY tc_nod_id
		 order by llu.rcstn_ordn, tc.rcstn_ordn
    </select>
    
    <!-- 교과서공부탭 Start -->
    <!-- [교과서 공부 탭] - 헤더 목록 조회 -->
    <select id="selectTxbStuHdrList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        /** BcClaLsnAnTcr-Mapper.xml - selectTxbStuHdrList */
        SELECT * FROM
        (select a.lrmp_nod_id  
		     , a.lrn_atv_id 
		     , a.lrn_stp_id
		     , b.lrn_stp_nm
		     , a.lrn_atv_nm
		     , b.SRT_ORDN
		   	 , a.RCSTN_ORDN
		  from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		 inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		    on a.lrmp_nod_id = b.lrmp_nod_id
		   and a.lrn_stp_id = b.lrn_stp_id
		   and b.del_yn = 'N'
		 inner join lms_cms.bc_ctn_mtd m /* BC_콘텐츠메타데이터 */
		    on a.lrn_atv_id = m.lrn_atv_id
		   and a.ctn_cd = m.ctn_cd
		   and m.use_yn = 'Y'
		   and m.del_yn = 'N'
		 where a.opt_txb_id = #{optTxbId}            -- 파라미터: 운영교과서
		   and a.lrmp_nod_id = #{lrmpNodId}          -- 파라미터: 학습맵노드ID
		   and b.lrn_stp_dv_cd = 'CL'
		   and a.use_yn = 'Y'
		   
		 UNION ALL
		
		SELECT M.lrmp_nod_id  
		     , IFNULL(C.LRN_ATV_ID, M.TCR_REG_CTN_ID) AS lrn_atv_id 
		     , M.lrn_stp_id
		     , S.lrn_stp_nm
		     , C.TCR_REG_CTN_NM AS lrn_atv_nm					/* 평가ID */
		   	,S.SRT_ORDN
		   	,M.RCSTN_ORDN
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
			ON M.OPT_TXB_ID = R.OPT_TXB_ID
			AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		INNER JOIN LMS_CMS.bc_lrn_stp S
			ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
			AND M.LRN_STP_ID = S.LRN_STP_ID
			AND S.DEL_YN = 'N'
		WHERE M.OPT_TXB_ID = #{optTxbId}
		AND M.LRMP_NOD_ID = #{lrmpNodId} 
		AND M.DEL_YN = 'N'
		AND M.USE_YN = 'Y')ATV
		 order by ATV.srt_ordn asc, ATV.rcstn_ordn asc, ATV.lrmp_nod_id asc, ATV.lrn_atv_id asc
    </select>

	<!--
    <resultMap id="txbStuMap" type="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        <result property="usrId" column="usr_id"/>
        <result property="stuNo" column="stu_no"/>
        <result property="usrNm" column="usr_nm"/>
        <result property="lrnrVelTpCd" column="lrnr_vel_tp_cd"/>
        <result property="progPcnt" column="prog_pcnt"/>
        <result property="evCnt" column="EV_CNT"/>
        <collection 
            property="tcLsnAnList" 
            javaType="List"
            ofType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto"
            column="usrId=usr_id, optTxbId=opt_txb_id, lrmpNodId=lrmp_nod_id"
            select="selectTcLsnAnList"
        >
        	<result property="lrmpNodId" column="lrmp_nod_id"/>
	        <result property="lrnAtvId" column="lrn_atv_id"/>
	        <result property="lrnStpId" column="lrn_stp_id"/>
	        <result property="lrnStCd" column="lrn_st_cd"/>
	        <result property="lrnTmScnt" column="lrn_tm_scnt"/>
	        <result property="ctnTpCd" column="ctn_tp_cd"/>
        </collection>
    </resultMap>
    -->
    
    <!-- [교과서 공부 탭] - 학생 목록 조회 
    <select id="selectTxbStuList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        /** BcClaLsnAnTcr-Mapper.xml - selectTcLsnAnStuList */
        select usr.usr_id 
             , usr.stu_no
             , usr.usr_nm 
             , round(t1.cl_cnt/t1.tot_cnt*100, 1) as prog_pcnt
             , #{optTxbId} as opt_txb_id
             , #{lrmpNodId} as lrmp_nod_id
             , T2.EV_CNT
             , IFNULL(CD.CM_CD_NM, '-') AS lrnr_vel_tp_cd
          from lms_lrm.cm_usr usr
          INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn NR
          	ON NR.OPT_TXB_ID = #{optTxbId}
          	AND NR.LRMP_NOD_ID = #{lrmpNodId}
          	AND NR.LU_EPS_YN = 'Y'
          LEFT JOIN LMS_LRM.tl_lu_lrnr_lv L
          	ON L.OPT_TXB_ID = NR.OPT_TXB_ID
          	AND L.LLU_NOD_ID = NR.LLU_NOD_ID
          	AND L.USR_ID = usr.usr_id
          LEFT JOIN LMS_LRM.cm_cm_cd CD
          	ON CD.URNK_CM_CD = 'LRNR_VEL_TP_CD'
          	AND CD.CM_CD = L.LRNR_VEL_TP_CD
          	AND CD.DEL_YN = 'N'
          left outer join (
              select c.lrn_usr_id
                   , count(case when c.lrn_st_cd = 'CL' then 1 else 0 end) as cl_cnt
                   , count(c.lrn_st_cd) as tot_cnt
                from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
               inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
                  on a.lrmp_nod_id = b.lrmp_nod_id
                 and a.lrn_stp_id = b.lrn_stp_id
                 and b.del_yn = 'N'
               inner join lms_cms.bc_ctn_mtd m /* BC_콘텐츠메타데이터 */
			      on a.lrn_atv_id = m.lrn_atv_id
			     and a.ctn_cd = m.ctn_cd
			     and m.use_yn = 'Y'
			     and m.del_yn = 'N'
                left outer join lms_lrm.tl_sbc_lrn_atv_st c
                  on a.opt_txb_id = c.opt_txb_id
                 and a.lrmp_nod_id = c.lrmp_nod_id
                 and a.lrn_atv_id = c.lrn_atv_id
               where a.opt_txb_id = #{optTxbId}         
                 and a.lrmp_nod_id = #{lrmpNodId}        
                 and b.lrn_stp_dv_cd = 'CL'
                 and a.use_yn = 'Y'
               group by c.lrn_usr_id
          ) t1
            on usr.usr_id = t1.lrn_usr_id
         LEFT JOIN (
				SELECT
					R.USR_ID
					,SUM(case when R.EV_CMPL_YN = 'Y' then 1 else 0 END) AS EV_CNT
				FROM LMS_LRM.ea_ev E
					INNER JOIN LMS_LRM.ea_ev_rs R
						ON E.EV_ID = R.EV_ID
				WHERE E.OPT_TXB_ID=#{optTxbId}
				AND E.EV_DV_CD IN ('AE','SE')
				AND E.DEL_YN = 'N'
				GROUP BY R.USR_ID
         ) T2
         	on usr.usr_id = t2.usr_id
         where usr.usr_tp_cd = 'ST'
           and usr.cla_id = #{claId}     
         order by usr.stu_no
    </select>-->
    
    <!-- [교과서 공부 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회 -->
    <select id="selectTcLsnAnList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        /** BcClaLsnAnTcr-Mapper.xml - selectTcLsnAnStuList */
        SELECT * FROM
        (select a.lrmp_nod_id  
             , a.lrn_atv_id 
             , a.lrn_stp_id
             , c.lrn_st_cd
             , c.lrn_tm_scnt
             , b.srt_ordn
             , a.rcstn_ordn
             , m.ctn_tp_cd
             , u.USR_ID
          from (
		    SELECT 
		    	R.OPT_TXB_ID,
		        R.LRMP_NOD_ID AS LRMP_NOD_ID,
				R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
		        R.LRN_ATV_ID,
		        R.LRN_STP_ID,
		        R.CTN_CD,
		        R.LRN_ATV_NM,
		        R.CTN_TP_CD,
		        R.USE_YN,
		        R.CLS_BRD_URL,
		        R.ORGL_LRN_STP_ID,
		        R.ORGL_ORDN,
		        R.RCSTN_ORDN,
		        R.EV_ID,
		        R.CRTR_ID,
		        R.CRT_DTM,
		        R.MDFR_ID,
		        R.MDF_DTM,
		        R.DB_ID,
		        'N' AS TCR_CTN_YN
		    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
		    WHERE R.OPT_TXB_ID = #{optTxbId}
		      AND R.LRMP_NOD_ID = #{lrmpNodId}
		      
			UNION ALL
			
		    SELECT 
		        R.OPT_TXB_ID,
		        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
				R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
		        R.LRN_ATV_ID,
		        TTRCM.LRN_STP_ID,
		        R.CTN_CD,
		        R.LRN_ATV_NM,
		        R.CTN_TP_CD,
		        TTRCM.USE_YN,
		        R.CLS_BRD_URL,
		        R.ORGL_LRN_STP_ID,
		        R.ORGL_ORDN,
		        TTRCM.RCSTN_ORDN,
		        R.EV_ID,
		        R.CRTR_ID,
		        R.CRT_DTM,
		        R.MDFR_ID,
		        R.MDF_DTM,
		        R.DB_ID,
		        'Y' AS TCR_CTN_YN
		    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
		    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
		        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
		        and ttrcm.del_yn = 'N'
		    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
		        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
		    WHERE TTRCM.OPT_TXB_ID = #{optTxbId}
		      AND TTRCM.LRMP_NOD_ID = #{lrmpNodId}
		      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
		      AND TTRC.LRN_ATV_ID IS NOT NULL
			) a /* tl_교과학습활동재구성 */
         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
            on a.lrmp_nod_id = b.lrmp_nod_id
           and a.lrn_stp_id = b.lrn_stp_id
           and b.del_yn = 'N'
         inner join lms_cms.bc_ctn_mtd m /* BC_콘텐츠메타데이터 */
		    on a.lrn_atv_id = m.lrn_atv_id
		   and a.ctn_cd = m.ctn_cd
		   and m.use_yn = 'Y'
		   and m.del_yn = 'N'
		   INNER JOIN lms_lrm.cm_opt_txb t
		   	ON t.OPT_TXB_ID = #{optTxbId} 
		   INNER JOIN lms_lrm.cm_usr u
		   	ON u.CLA_ID = t.CLA_ID
         left outer join lms_lrm.tl_sbc_lrn_atv_st c
          	ON c.OPT_TXB_ID = #{optTxbId} 
           and c.opt_txb_id = a.opt_txb_id
           and c.lrmp_nod_id = a.ORGL_LRMP_NOD_ID
           and c.lrn_atv_id = a.lrn_atv_id   
			  AND u.USR_ID = c.LRN_USR_ID                   				-- 파라미터: 학생ID
         where a.opt_txb_id = #{optTxbId}                 					-- 파라미터: 운영교과서
           and a.lrmp_nod_id = #{lrmpNodId}                               -- 파라미터: 학습맵노드ID
           and b.lrn_stp_dv_cd = 'CL'
           and a.use_yn = 'Y'
           
         UNION ALL
         
         SELECT M.lrmp_nod_id  
             , M.TCR_REG_CTN_ID AS lrn_atv_id 
             , M.lrn_stp_id
             , T.lrn_st_cd
             , T.lrn_tm_scnt
             , S.SRT_ORDN
             , M.RCSTN_ORDN
             , c.TP_CD as ctn_tp_cd
             , u.USR_ID
      	FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
				INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
					ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
					AND C.TP_CD <![CDATA[<>]]> 'AT'
				INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
					ON M.OPT_TXB_ID = R.OPT_TXB_ID
					AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
				INNER JOIN LMS_CMS.bc_lrn_stp S
					ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
					AND M.LRN_STP_ID = S.LRN_STP_ID
					AND S.DEL_YN = 'N'
				INNER JOIN lms_lrm.cm_opt_txb ot
		   		ON ot.OPT_TXB_ID = #{optTxbId}
		   	INNER JOIN lms_lrm.cm_usr u
		   		ON u.CLA_ID = ot.CLA_ID
				LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
					ON T.OPT_TXB_ID = #{optTxbId}
					AND T.OPT_TXB_ID = M.OPT_TXB_ID
					AND T.TCR_REG_CTN_ID = M.TCR_REG_CTN_ID
					AND T.LRN_USR_ID = u.usr_id
				WHERE M.OPT_TXB_ID = #{optTxbId}
				AND M.LRMP_NOD_ID = #{lrmpNodId}
				AND M.DEL_YN = 'N'
				AND M.USE_YN = 'Y')ATV
         order by ATV.srt_ordn asc, ATV.rcstn_ordn asc, ATV.lrmp_nod_id asc, ATV.lrn_atv_id asc
    </select>
    
    <!-- 교과서공부탭 End -->
    
    <!-- 형성평가탭 Start -->
    
    <!-- [형성평가 탭] - 헤더 목록 조회 -->
    <select id="selectFrmEvHdrList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
    	/** BcClaLsnAnTcr-Mapper.xml - selectFrmEvHdrList */
    	    select t1.qtm_no, t1.fs_div_cd, concat(t1.fs_div_cd, t1.qtm_no) as hdr_id
		      from (
		        select d.qtm_ordn as qtm_no
		             , 'frmEv' as fs_div_cd
		          from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		            on a.lrmp_nod_id = b.lrmp_nod_id
		           and a.lrn_stp_id = b.lrn_stp_id
		           and b.del_yn = 'N'
		         inner join lms_lrm.ea_ev c
		            on c.ev_id = a.ev_id 
		           and c.del_yn = 'N'
		           and c.use_yn = 'Y'
		         inner join lms_lrm.ea_ev_qtm d
		            on c.ev_id = d.ev_id 
		           and d.del_yn = 'N'
		         where a.opt_txb_id = #{optTxbId}            -- 파라미터: 운영교과서
		           and a.lrmp_nod_id = #{lrmpNodId}          -- 파라미터: 학습맵노드ID
		           and b.lrn_stp_dv_cd = 'EX'
		         order by b.srt_ordn is null asc, a.rcstn_ordn, a.lrmp_nod_id, a.lrn_atv_id, d.qtm_ordn
		      ) t1
		union all
		    select t2.qtm_no, t2.fs_div_cd, concat(t2.fs_div_cd, t2.qtm_no) as hdr_id
		      from (
		        select e.qtm_ordn as qtm_no
		             , 'sppNtn' as fs_div_cd
		          from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		            on a.lrmp_nod_id = b.lrmp_nod_id
		           and a.lrn_stp_id = b.lrn_stp_id
		           and b.del_yn = 'N'
		         inner join lms_lrm.tl_sbc_lrn_atv_st c
		            on a.opt_txb_id = c.opt_txb_id
		           and a.lrmp_nod_id = c.lrmp_nod_id
		           and a.lrn_atv_id = c.lrn_atv_id
		         inner join lms_lrm.ea_ev d
		            on d.ev_id = c.extr_ev_id
		           and d.del_yn = 'N'
		           and d.use_yn = 'Y'
		         inner join lms_lrm.ea_ev_qtm e
		            on d.ev_id = e.ev_id 
		           and e.del_yn = 'N'
		         where a.opt_txb_id = #{optTxbId}          		-- 파라미터: 운영교과서
		           and a.lrmp_nod_id = #{lrmpNodId}             -- 파라미터: 학습맵노드ID
		           and b.lrn_stp_dv_cd = 'EX'
		         order by b.srt_ordn is null asc, a.rcstn_ordn, a.lrmp_nod_id, a.lrn_atv_id, e.qtm_ordn
		      ) t2
		order by fs_div_cd, qtm_no
    </select>
    
    <!-- 
    <resultMap id="frmEvMap" type="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        <result property="usrId" column="usr_id"/>
        <result property="stuNo" column="stu_no"/>
        <result property="usrNm" column="usr_nm"/>
        <result property="progPcnt" column="prog_pcnt"/>
        <result property="prevProgPcnt" column="prev_prog_pcnt"/>
        <result property="evId" column="ev_id"/>
        <result property="evDtlDvCd" column="ev_dtl_dv_cd"/>
        <result property="evCnt" column="EV_CNT"/>
        <collection 
            property="tcLsnAnList" 
            javaType="List"
            ofType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto"
            column="usrId=usr_id, optTxbId=opt_txb_id, lrmpNodId=lrmp_nod_id, txbId=txb_id"
            select="selectFrmEvAnList"
        >
        	<result property="qtmNo" column="qtm_no"/>
	        <result property="fsDivCd" column="fs_div_cd"/>
	        <result property="evId" column="ev_id"/>
	        <result property="qtmId" column="qtm_id"/>
	        <result property="cansYn" column="cans_yn"/>
	        <result property="hdrId" column="hdr_id"/>
        </collection>
    </resultMap>
    -->
    
    <!-- [형성평가 탭] - 학생 목록 조회 -->
    <select id="selectFrmEvList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
    	/** BcClaLsnAnTcr-Mapper.xml - selectFrmEvList */
    	select usr.usr_id 
		     , usr.stu_no
		     , usr.usr_nm 
		     , IFNULL(CD.CM_CD_NM, '-') AS lrnr_vel_tp_cd
		     , round(t1.sum_cans_rt/t1.tot_cnt, 1) as prog_pcnt
		     , round(t2.sum_cans_rt/t2.tot_cnt * 100, 0) as prev_prog_pcnt
		     , #{optTxbId} as opt_txb_id
		     , #{lrmpNodId} as lrmp_nod_id
		     , #{txbId} as txb_id
		     , IFNULL(t1.ev_id,'') as ev_id
		     , IFNULL(t1.ev_dtl_dv_cd,'') as ev_dtl_dv_cd
		     , T3.EV_CNT
		  from lms_lrm.cm_usr usr
		  INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn NR
          	ON NR.OPT_TXB_ID = #{optTxbId}
          	AND NR.LRMP_NOD_ID = #{lrmpNodId}
          	AND NR.LU_EPS_YN = 'Y'
          LEFT JOIN LMS_LRM.tl_lu_lrnr_lv L
          	ON L.OPT_TXB_ID = NR.OPT_TXB_ID
          	AND L.LLU_NOD_ID = NR.LLU_NOD_ID
          	AND L.USR_ID = usr.usr_id
          LEFT JOIN LMS_LRM.cm_cm_cd CD
          	ON CD.URNK_CM_CD = 'LRNR_VEL_TP_CD'
          	AND CD.CM_CD = L.LRNR_VEL_TP_CD
          	AND CD.DEL_YN = 'N'
		  left join (
		        select d.usr_id
		             , count(d.ev_id) as tot_cnt
		             , sum(d.cans_rt) as sum_cans_rt
		             , MAX(c.ev_id) AS ev_id
		             , MAX(c.ev_dtl_dv_cd) AS ev_dtl_dv_cd
		          from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		            on a.lrmp_nod_id = b.lrmp_nod_id
		           and a.lrn_stp_id = b.lrn_stp_id
		           and b.del_yn = 'N'
		         inner join lms_lrm.ea_ev c
		            on c.ev_id = a.ev_id 
		           and c.del_yn = 'N'
		           and c.use_yn = 'Y'
		         inner join lms_lrm.ea_ev_rs d
		            on d.ev_id = c.ev_id
		           and d.ev_cmpl_yn = 'Y'
		         where a.opt_txb_id = #{optTxbId}            	-- 파라미터: 운영교과서
		           and a.lrmp_nod_id = #{lrmpNodId}             -- 파라미터: 학습맵노드ID
		           and b.lrn_stp_dv_cd = 'EX'
		         group by d.usr_id
		  ) t1
		    on usr.usr_id = t1.usr_id
		  left outer join (
		        select d.usr_id
		             , MAX(C.QST_CNT + IFNULL(r.qst_cnt,0)) AS tot_cnt
		        	 , MAX(D.CANS_CNT + IFNULL(r.cans_cnt,0)) AS sum_cans_rt
		             , MAX(c.ev_id) AS ev_id
		             , MAX(c.ev_dtl_dv_cd) AS ev_dtl_dv_cd
		          from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		            on a.lrmp_nod_id = b.lrmp_nod_id
		           and a.lrn_stp_id = b.lrn_stp_id
		           and b.del_yn = 'N'
		         inner join lms_lrm.ea_ev c
		            on c.ev_id = a.ev_id 
		           and c.del_yn = 'N'
		           and c.use_yn = 'Y'
		         inner join lms_lrm.ea_ev_rs d
		            on d.ev_id = c.ev_id
		           and d.ev_cmpl_yn = 'Y'
		         LEFT JOIN lms_lrm.ea_ev_spp_ntn_rs r
		         	on d.ev_id = r.ev_id
		         	AND d.USR_ID = r.USR_ID
		         	and r.ev_cmpl_yn = 'Y'
		         where a.opt_txb_id = #{optTxbId}            	-- 파라미터: 운영교과서
		           and a.lrmp_nod_id = #{prevTcNodId}             -- 파라미터: 학습맵노드ID
		           and b.lrn_stp_dv_cd = 'EX'
		         group by d.usr_id
		  ) t2
		    on usr.usr_id = t2.usr_id
		    LEFT JOIN (
				SELECT
					R.USR_ID
					,SUM(case when R.EV_CMPL_YN = 'Y' then 1 else 0 END) AS EV_CNT
				FROM LMS_LRM.ea_ev E
					INNER JOIN LMS_LRM.ea_ev_rs R
						ON E.EV_ID = R.EV_ID
				WHERE E.OPT_TXB_ID=#{optTxbId}
				AND E.EV_DV_CD IN ('AE','SE')
				AND E.DEL_YN = 'N'
				GROUP BY R.USR_ID
         ) T3
         on usr.usr_id = T3.usr_id
		 where usr.usr_tp_cd = 'ST'
		   and usr.cla_id = #{claId}                  			-- 파라미터: 학급코드
		  order by usr.stu_no
    </select>
    
    <!-- [형성평가 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회 -->
    <select id="selectFrmEvAnList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
    	/** BcClaLsnAnTcr-Mapper.xml - selectFrmEvAnList */
    	    select t1.qtm_no
		         , t1.fs_div_cd
		         , t1.ev_id
		         , t1.qtm_id
		         , if(t1.ev_cmpl_yn = 'Y',t1.cans_yn,NULL) AS cans_yn
		         , concat(t1.fs_div_cd, t1.qtm_no) as hdr_id
		         , t1.usr_id
		      from (
		        select d.qtm_ordn as qtm_no
		             , 'frmEv' as fs_div_cd
		             , d.ev_id
		             , r.EV_CMPL_YN
		             , d.qtm_id
		             , e.cans_yn
		             , r.usr_id
		          from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		            on a.lrmp_nod_id = b.lrmp_nod_id
		           and a.lrn_stp_id = b.lrn_stp_id
		           and b.del_yn = 'N'
		         inner join lms_lrm.ea_ev c
		         	on c.ev_id = a.ev_id 
		         	and c.opt_txb_id = a.opt_txb_id
		           and c.del_yn = 'N'
		           and c.use_yn = 'Y'
		         INNER JOIN lms_lrm.ea_ev_rs r
		         	ON c.EV_ID = r.EV_ID
		         inner join lms_lrm.ea_ev_qtm d
		            on d.ev_id = c.ev_id  
		           and d.del_yn = 'N'
		          left outer join lms_lrm.ea_ev_qtm_anw e
		            on e.ev_id = d.ev_id
		           and e.qtm_id = d.qtm_id
		           and e.usr_id = r.usr_id							-- 파라미터: 학생ID
		         where a.opt_txb_id = #{optTxbId}            		-- 파라미터: 운영교과서
		           and a.lrmp_nod_id = #{lrmpNodId}                 -- 파라미터: 학습맵노드ID
		           and b.lrn_stp_dv_cd = 'EX'
		         order by b.srt_ordn is null asc, a.rcstn_ordn, a.lrmp_nod_id, a.lrn_atv_id, d.qtm_ordn
		      ) t1
		union all
		    select t2.qtm_no
		         , t2.fs_div_cd
		         , t2.ev_id
		         , t2.qtm_id
		         , if(t2.ev_cmpl_yn = 'Y',t2.cans_yn,NULL) AS cans_yn
		         , concat(t2.fs_div_cd, t2.qtm_no) as hdr_id
		         , t2.usr_id
		      from (
		        select e.qtm_ordn as qtm_no
		             , 'sppNtn' as fs_div_cd
		             , c.ev_id
		             , r.EV_CMPL_YN
		             , e.qtm_id
		             , e.cans_yn
		             , r.usr_id
		          from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		         inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		            on a.lrmp_nod_id = b.lrmp_nod_id
		           and a.lrn_stp_id = b.lrn_stp_id
		           and b.del_yn = 'N'
		         inner join lms_lrm.ea_ev c
		         	on c.opt_txb_id = #{optTxbId}
		           and c.ev_id = a.ev_id 
		           and c.del_yn = 'N'
		           and c.use_yn = 'Y'
		         INNER JOIN lms_lrm.ea_ev_spp_ntn_rs r
		         	ON c.EV_ID = r.EV_ID
		         	and r.txm_pn = 0
		         left outer join lms_lrm.ea_ev_spp_ntn_qtm_anw e
		            on e.ev_id = r.ev_id
		           and e.usr_id = r.usr_id
		           and e.txm_pn = 0
		         where a.opt_txb_id = #{optTxbId}          		-- 파라미터: 운영교과서
		           and a.lrmp_nod_id = #{lrmpNodId}                 -- 파라미터: 학습맵노드ID
		           and b.lrn_stp_dv_cd = 'EX'
		         order by b.srt_ordn is null asc, a.rcstn_ordn, a.lrmp_nod_id, a.lrn_atv_id, e.qtm_ordn
		      ) t2
		 order by fs_div_cd, qtm_no
    </select>
    
    <!-- 형성평가탭 End -->
    
    <!-- 수학익힘탭 Start -->
    <!-- [수학익힙 탭] - 헤더 목록 조회 -->
    <select id="selectMthPrtHdrList" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
    	/** BcClaLsnAnTcr-Mapper.xml - selectMthPrtHdrList */
    	select a.lrmp_nod_id  
		     , a.lrn_atv_id 
		     , a.lrn_stp_id
		     , b.lrn_stp_nm
		     , a.lrn_atv_nm
		     , case when a.lrn_atv_nm regexp '문제 [0-9]+$' then 'Y'
            		when a.lrn_atv_nm regexp '문제[0-9]+$' then 'Y'
		            else 'N'
		        end as qst_yn
		  from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		 inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		    on a.lrmp_nod_id = b.lrmp_nod_id
		   and a.lrn_stp_id = b.lrn_stp_id
		   and b.del_yn = 'N'
		 where a.opt_txb_id = #{optTxbId}            		-- 파라미터: 운영교과서
		   and a.lrmp_nod_id = #{lrmpNodId}                 -- 파라미터: 학습맵노드ID
		   and b.lrn_stp_dv_cd = 'WB'                       -- 학습단계구분코드 (CL:개념/WB:익힘/EX:평가)
		 order by b.srt_ordn is null asc, a.rcstn_ordn asc, a.lrmp_nod_id asc, a.lrn_atv_id asc
    </select>
    
    <!-- [수학익힙 탭] 학생목록 및 학습정보 resultMap 
    <resultMap id="mthPrtMap" type="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
        <result property="usrId" column="usr_id"/>
        <result property="stuNo" column="stu_no"/>
        <result property="usrNm" column="usr_nm"/>
        <result property="lrnrVelTpCd" column="lrnr_vel_tp_cd"/>
        <result property="evCnt" column="EV_CNT"/>
        <collection 
            property="tcLsnAnList" 
            javaType="List"
            ofType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto"
            column="optTxbId=opt_txb_id, lrmpNodId=lrmp_nod_id, usrId=usr_id"
            select="selectMthPrtAtvList"
        >
        	<result property="lrmpNodId" column="lrmp_nod_id"/>
	        <result property="lrnAtvId" column="lrn_atv_id"/>
	        <result property="lrnUsrId" column="lrn_usr_id"/>
	        <result property="lrnStCd" column="lrn_st_cd"/>
	        <result property="lrnTmScnt" column="lrn_tm_scnt"/>
        </collection>
    </resultMap>
    -->
    
    <!-- [수학익힙 탭] - 학생 목록 조회 -->
    <select id="selectMthPrtList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
    	/** BcClaLsnAnTcr-Mapper.xml - selectMthPrtList */
		select usr.usr_id 
		     , usr.stu_no
		     , usr.usr_nm 
		     , IFNULL(CD.CM_CD_NM, '-') AS lrnr_vel_tp_cd
		     , #{optTxbId} as opt_txb_id
		     , #{lrmpNodId} as lrmp_nod_id
		     ,T3.EV_CNT
		  from lms_lrm.cm_usr usr
		  INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn NR
          	ON NR.OPT_TXB_ID = #{optTxbId}
          	AND NR.LRMP_NOD_ID = #{lrmpNodId}
          	AND NR.LU_EPS_YN = 'Y'
          LEFT JOIN LMS_LRM.tl_lu_lrnr_lv L
          	ON L.OPT_TXB_ID = NR.OPT_TXB_ID
          	AND L.LLU_NOD_ID = NR.LLU_NOD_ID
          	AND L.USR_ID = usr.usr_id
          LEFT JOIN LMS_LRM.cm_cm_cd CD
          	ON CD.URNK_CM_CD = 'LRNR_VEL_TP_CD'
          	AND CD.CM_CD = L.LRNR_VEL_TP_CD
          	AND CD.DEL_YN = 'N'
		  LEFT JOIN (
				SELECT
					R.USR_ID
					,SUM(case when R.EV_CMPL_YN = 'Y' then 1 else 0 END) AS EV_CNT
				FROM LMS_LRM.ea_ev E
					INNER JOIN LMS_LRM.ea_ev_rs R
						ON E.EV_ID = R.EV_ID
				WHERE E.OPT_TXB_ID=#{optTxbId}
				AND E.EV_DV_CD IN ('AE','SE')
				AND E.DEL_YN = 'N'
				GROUP BY R.USR_ID
         ) T3
         on usr.usr_id = T3.usr_id
		 where usr.usr_tp_cd = 'ST'
		   and usr.cla_id = #{claId}                  -- 파라미터: 학급코드
		 order by usr.stu_no    
    </select>
    
    <!-- [수학익힙 탭] - 학생 목록 기준의 차시별 학습현황 목록 조회 -->
    <select id="selectMthPrtAtvList" parameterType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnReqDto" resultType="com.aidt.api.bc.home.tcr.dto.BcClaLsnAnResDto">
    	/** BcClaLsnAnTcr-Mapper.xml - selectMthPrtAtvList */
	 
		 select a.lrmp_nod_id  
		     , a.lrn_atv_id 
		     , c.lrn_usr_id 
		     , c.lrn_st_cd 
		     , c.lrn_tm_scnt
		     , u.USR_ID
		  from lms_lrm.tl_sbc_lrn_atv_rcstn a /* tl_교과학습활동재구성 */
		 inner join lms_cms.bc_lrn_stp b /* bc_학습단계 */
		    on a.lrmp_nod_id = b.lrmp_nod_id
		   and a.lrn_stp_id = b.lrn_stp_id
		   and b.del_yn = 'N'
		INNER JOIN lms_lrm.cm_opt_txb ot
		   		ON ot.OPT_TXB_ID = #{optTxbId}   
		INNER JOIN lms_lrm.cm_usr u
		   		ON u.CLA_ID = ot.CLA_ID
		LEFT join lms_lrm.tl_sbc_lrn_atv_st c
		 	on	c.opt_txb_id = #{optTxbId}   
		   AND  a.opt_txb_id = c.opt_txb_id 
		   and a.lrmp_nod_id = c.lrmp_nod_id 
		   and a.lrn_atv_id = c.lrn_atv_id
		   AND c.LRN_USR_ID = u.USR_ID
		 where a.opt_txb_id = #{optTxbId}           -- 파라미터: 운영교과서
		   and a.lrmp_nod_id = #{lrmpNodId}         -- 파라미터: 학습맵노드ID
		   and b.lrn_stp_dv_cd = 'WB'                -- 학습단계구분코드 (CL:개념/WB:익힘/EX:평가)
		 order by b.srt_ordn is null asc, a.rcstn_ordn asc, a.lrmp_nod_id asc, a.lrn_atv_id asc
    </select>
    
    <!-- 수학익힘탭 End -->
</mapper>
