package com.aidt.api.bc.chatbot.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2024-05-21 18:01:28
 * @modify 2024-05-21 18:01:28
 * @desc 챗봇 API
 */

 @Getter
 @Setter
 @Builder
 @NoArgsConstructor
 @AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcCbUsrUseDto{	
	@Parameter(name="챗봇이용내역 키")
	private String cbUseId;

	@Parameter(name="사용자아이디")
	private String lrnUsrId;

	@Parameter(name="질문내용")
	private String queCn;

	@Parameter(name="답변내용")
	private String anwCn;

	@Parameter(name="DB아이디")
	private String dbId;

	@Parameter(name="등록 일자")
	private String crtDate;

	@Parameter(name="등록 시간")
	private String crtTime;
}
