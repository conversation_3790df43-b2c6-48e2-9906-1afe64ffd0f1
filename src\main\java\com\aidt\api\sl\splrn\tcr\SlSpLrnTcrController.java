package com.aidt.api.sl.splrn.tcr;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.sl.splrn.dto.SlSpLrnDtlBanrViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnDtlViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnMainViewDto;
import com.aidt.api.sl.splrn.dto.SlSpLrnRcmdDto;
import com.aidt.api.sl.splrn.stu.SlSpLrnStuService;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-22 14:30:01
 * @modify date 2024-02-22 13:10:01
 * @desc SlTcrSpLrnTcrController
 */
@Slf4j
@Tag(name="[sl] 특별학습 교사[SlSpLrnTcr]", description="특별학습(교사)목록 및 상세정보 조회")
@RestController
@RequestMapping("/api/v1/sl/tcr/splrn")
public class SlSpLrnTcrController {

    @Autowired
    private JwtProvider jwtProvider;
    
	@Autowired
    private SlSpLrnTcrService slSpLrnTcrService;
    
    @Autowired
	private SlSpLrnStuService slSpLrnStuService;	//학생쪽 서비스
    /**
     * 특별학습 목록 조회
     * @param
     * @return selectSpLrnTcrList
     */
    @Operation(summary="특별학습목록조회(교사)", description="특별학습 목록 다건 조회(교사)")
    @PostMapping(value = "/selectSpLrnTcrList")
    public ResponseDto<List<SlSpLrnMainViewDto>> selectSpLrnTcrList(){
    	log.info("Entrance selectSpLrnTcrList");
    	//사용자정보, 운영교과서 아이디등 정보 가져오기
    	CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
    	List<SlSpLrnMainViewDto> result = slSpLrnStuService.selectSpLrnList(securityUserDetailDto.getOptTxbId(),
				securityUserDetailDto.getUsrId(), securityUserDetailDto.getUsrTpCd());
    	return Response.ok(result);
    }

	/**
	 * 특별학습 상세 - 상세 배너
	 * 
	 * @param spLrnId
	 * @return
	 */
    @Operation(summary="특별학습상세 - 상단 배너(교사)", description="특벽할습 상세 배너 목록 타이틀(교사")
	@PostMapping("/selectSpLrnTcrDtlBanner")
	public ResponseDto<SlSpLrnDtlBanrViewDto> selectSpLrnDtlBanner(
			@RequestBody SlSpLrnDtlBanrViewDto slSpLrnDtlBanrViewDto) {
		log.debug("Entrance selectSpLrnDtlBanner");
		String spLrnId = slSpLrnDtlBanrViewDto.getSpLrnId();
		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		SlSpLrnDtlBanrViewDto result = slSpLrnStuService.selectSpLrnDtlBanrViewDto(spLrnId,
				securityUserDetailDto.getUsrId(), securityUserDetailDto.getOptTxbId());

		return Response.ok(result);

	}


	/**
	 * 특별학습 상세 - 목록리스트조회(V3.1)
	 * 
	 * @param spCtnId //특별콘텐츠 id
	 * @return selectSpLrnDtlList 상세목록리스트
	 */
    @Operation(summary="(V3.1)특별학습상세목록조회(교사)", description="특벽할습 상세목록을 다건 조회한다")
	@PostMapping("/selectSpLrnTcrDtlList")
	public ResponseDto<List<SlSpLrnDtlViewDto>> selectSpDtlList(@RequestBody SlSpLrnDtlViewDto slSpLrnDtlViewDto) {
		log.debug("Entrance selectSpDtlList");
		String spLrnId = slSpLrnDtlViewDto.getSpLrnId();

		CommonUserDetail securityUserDetailDto = jwtProvider.getCommonUserDetail();
		List<SlSpLrnDtlViewDto> result = slSpLrnStuService.selectSpDtlList(spLrnId,
				securityUserDetailDto.getUsrId(), securityUserDetailDto.getOptTxbId());
		return Response.ok(result);
	}
}
