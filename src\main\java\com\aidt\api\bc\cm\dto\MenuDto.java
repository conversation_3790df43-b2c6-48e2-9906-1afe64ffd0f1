package com.aidt.api.bc.cm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "메뉴 공통")
public class MenuDto {

	@Parameter(name="메뉴ID")
	private String menuId;

	@Parameter(name="상위메뉴ID")
	private String urnkMenuId;

	@Parameter(name="메뉴사용자유형코드")
	private String menuUsrTpCd;

	@Parameter(name="메뉴명")
	private String menuNm;

	@Parameter(name="메뉴깊이")
	private String menuDpth;

	@Parameter(name="메뉴URL주소")
	private String menuUrlAdr;

	@Parameter(name="정렬순서")
	private int srtOrdn;

	@Parameter(name="사용여부")
	private String useYn;

	@Parameter(name="삭제여부")
	private String delYn;

	@Parameter(name="파일경로명")
	private String flePthNm;

	@Parameter(name="파일명")
	private String fleNm;


    @Parameter(name="상위메뉴ID(별칭사용)")
    private String parentMenuId;

    @Parameter(name="파일경로(별칭)")
    private String filePath;

    @Parameter(name="파일명(별칭)")
    private String fileNm;

    @Parameter(name="메뉴순서(별칭)")
    private String menuOrder;

    @Parameter(name="교과서ID")
    private String txbId;


	/*

    private String menuId;



    @Parameter(name="메뉴명")
    private String menuNm;

    @Parameter(name="파일경로")
    private String filePath;

    @Parameter(name="파일명")
    private String fileNm;

    @Parameter(name="사용여부")
    private String useYn;

    @Parameter(name="메뉴순서")
    private String menuOrder;

    @Parameter(name="등록자ID")
    private String regId;

    @Parameter(name="등록일시")
    private String regDt;




    */







}
