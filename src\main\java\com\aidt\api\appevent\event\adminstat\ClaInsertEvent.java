package com.aidt.api.appevent.event.adminstat;

import java.time.LocalDateTime;

import com.aidt.base.message.application.AbstractAppEvent;
import com.aidt.base.message.messagequeue.AbstractPayload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * 신규 학급 데이터 등록 Event
 * - 변수 추가 및 수정 시 관리자 kafka 개발 담당자 전달 필수
 */
public class ClaInsertEvent extends AbstractAppEvent {

	public ClaInsertEvent(ClaInsertPayload payload) {
		super(payload);
	}

	@Builder
	@Getter
	@ToString
	public static class ClaInsertPayload extends AbstractPayload {

		@Schema(description = "학급ID")
		private String claId;

		@Schema(description = "학교코드")
		private String schlCd;

		@Schema(description = "학년")
		private Long sgy;

		@Schema(description = "반번호")
		private Long claNo;

		@Schema(description = "반명")
		private String claNm;

		@Schema(description = "KERIS 반명")
		private String classroomNm;
		
		@Schema(description = "KERIS반코드")
		private String kerisClaCd;

		@Schema(description = "생성자ID")
		private String crtrId;

		@Schema(description = "생성일시")
		private LocalDateTime crtDtm;

		@Schema(description = "수정자ID")
		private String mdfrId;

		@Schema(description = "수정일시")
		private LocalDateTime mdfDtm;
	}
}
