package com.aidt.api.tl.cmtxb.dto;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-07-01 16:18:45
 * @modify date 2024-07-01 16:18:45
 * @desc [TlCmTxbTcTmDto 차시선택 dto]
 */
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TlCmTxbTcTmDto {
	/** 운영교과서 ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;
    
    /** 대단원ID */
    @Parameter(name="대단원ID")
    private String lluNodId;
    
    /** 차시ID */
    @Parameter(name="차시ID")
    private String lrmpNodId;
    
    /** 학습단계구분코드 */
    @Parameter(name="학습단계구분코드")
    private String lrnStpDvCd;

    /** 생성자ID */
    @Parameter(name="생성자ID")
    private String crtrId;
    
    /** 수정자ID */
    @Parameter(name="수정자ID")
    private String mdfrId;

    /** dbID */
    @Parameter(name="dbID")
    private String dbId;
}
