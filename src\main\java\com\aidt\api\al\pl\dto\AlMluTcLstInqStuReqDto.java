package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc [AI맞춤학습 단원차시조회 request dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlMluTcLstInqStuReqDto {
	/** 운영교과서ID */
    @Parameter(name="운영교과서ID")
    private String optTxbId;

    /** 학습맵노드ID */
    @Parameter(name="중단원노드ID")
    private String mluLrmpNodId;

    /** 사용자ID */
    @Parameter(name="사용자ID")
    private String usrId;
    
    /** 지식맵노드ID */
    @Parameter(name="중단원 지식맵 노드ID")
    //private String mKLuLrmpNodId;
    private String mkLuLrmNodId;//추후 삭제하도록하기
    private String mluKmmpNodId;
    
    /** 차시 지식맵노드ID */
    @Parameter(name="차시 지식맵 노드ID")
    private String tcKmmpNodId;
    
    /** 차시 지식맵노드명 */
    @Parameter(name="차시 지식맵 노드명")
    private String tcKmmpNodNm;
    
    @Parameter(name="맞춤학습1 평가ID")
    private String c1EvId;
    
    @Parameter(name="맞춤학습2 평가ID")
    private String c2EvId;
    
    @Parameter(name = "평가ID 배열")
    private String[] evIds;
    
    @Parameter(name = "평가코드")
    private String evDtlDvCd;
    
    @Parameter(name = "잠금여부")
    private String lcknYn;
}
