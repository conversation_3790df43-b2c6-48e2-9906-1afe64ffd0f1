package com.aidt.api.al.pl.cm.rcm.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum EvaluationCode {

	AE("AE", "AI평가"),
	DE("DE", "DIY평가"),
	SE("SE", "교과학습평가"),
	TE("TE", "교사평가");

	private final String code;
	private final String desc;

	EvaluationCode(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static EvaluationCode getEvaluationCode(String code) {
		return Arrays.stream(values())
			.filter(x -> x.getCode().equalsIgnoreCase(code))
			.findAny()
			.orElseThrow(() -> new IllegalArgumentException("평가 구분 코드를 찾을 수 없습니다."));
	}

}
