package com.aidt.api.bc.cm.file.service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.bc.cm.dto.BcAnnxFleDto;
import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.api.bc.cm.file.adapter.BcCmFileAdapter;
import com.aidt.api.bc.cm.file.dto.BcFileReqDto;
import com.aidt.api.bc.cm.file.dto.BcFileResDto;
import com.aidt.api.bc.cm.helper.StreamDocHelper;
import com.aidt.api.common.helper.S3Helper;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service
@RequiredArgsConstructor
public class BcCmFileService {

	private final S3Helper s3Helper;
	private final StreamDocHelper streamDocHelper;
	private final BcCmFileAdapter bcCmFileAdapter;
	private final JwtProvider jwtProvider;
	@Value("${server.meta.textbook.systemCode}")
	private String dbName;

	public List<BcFileResDto> uploadFlies(BcFileReqDto request) {

		var userDetail = jwtProvider.getCommonUserDetail();

		var annxId = getOrCreateAnnxId(request.getAnnxId(), userDetail);

		var uploadFiles = request.getFiles().stream()
			.map(file -> {
				var s3Path = s3Helper.upload(request.getTaskName(), file);
				var streamDocsId = streamDocHelper.save(s3Path);
				return BcAnnxFleDto.builder()
					//file
					.annxId(annxId)
					.annxFleOrglNm(file.getOriginalFilename())
					.annxFleFextNm(file.getContentType())
					.annxFleSze(file.getSize())
					//stream doc
					.docViId(streamDocsId)
					//s3 path
					.annxFlePthNm(s3Path)
					//uuid file name
					.annxFleNm(FilenameUtils.getName(s3Path))
					.useYn("Y")
					.crtrId(userDetail.getUsrId())
					.mdfrId(userDetail.getUsrId())
					.dbId(dbName)
					.build();
			}).collect(Collectors.toList());

		bcCmFileAdapter.saveAnnxFile(uploadFiles);

		return uploadFiles.stream()
			.map(BcFileResDto::of)
			.collect(Collectors.toList());
	}

	public ResponseEntity<Resource> downloadFile(Long annxFleId) {
		var file = bcCmFileAdapter.getAnnxFile(annxFleId);
		if (ObjectUtils.isEmpty(file)) {
			log.warn("다운로드할 파일이 존재하지 않습니다");
			throw new BizException("다운로드할 파일이 존재하지 않습니다.");
		}

		String originalFileName = file.getAnnxFleOrglNm();
		String encodedFileName = URLEncoder.encode(originalFileName, StandardCharsets.UTF_8)
			.replaceAll("\\+", "%20");
		String contentDisposition =
			"attachment; filename=\"" + originalFileName + "\"; filename*=UTF-8''" + encodedFileName;

		return ResponseEntity.ok()
			.contentType(MediaType.APPLICATION_OCTET_STREAM)
			.contentLength(file.getAnnxFleSze())
			.header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
			.body(s3Helper.download(file.getAnnxFlePthNm()));

	}

	public void removeFile(Long annxFleId) {
		var file = bcCmFileAdapter.getAnnxFile(annxFleId);

		if (ObjectUtils.isEmpty(file)) {
			log.warn("삭제할 파일이 존재하지 않습니다.");
			return;
		}
		//fixme: 첨부 파일을 등록한 사용자가 삭제해야 하지 않을까?

		// if (!file.getCrtrId().equals(jwtProvider.getCommonUserDetail().getUsrId())) {
		// 	throw new IllegalStateException("파일을 등록한 사용자만 삭제할 수 있습니다.");
		// }

		s3Helper.remove(file.getAnnxFlePthNm());
		//fixme: useYn=N으로 하는 soft delete로 바꿔야하지 않을까?
		bcCmFileAdapter.removeAnnxFile(file.getAnnxFleId(), file.getDbId());
	}

	private Long getOrCreateAnnxId(Long annxId, CommonUserDetail userDetails) {

		if (annxId != null) {
			return annxId;
		}
		var uploadAnnx = BcAnnxFleDto.builder()
			.useYn("Y")
			.crtrId(userDetails.getUsrId())
			.mdfrId(userDetails.getUsrId())
			.dbId(dbName)
			.build();
		bcCmFileAdapter.saveAnnx(uploadAnnx);
		return uploadAnnx.getAnnxId();
	}

}