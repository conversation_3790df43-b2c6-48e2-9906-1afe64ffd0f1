package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 학습창 연계
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlLrnwReqDto {
	
	@Parameter(name="운영교과서ID")
	private String optTxbId;
	
	@Parameter(name="사용자ID")
	private String usrId;
	
	@Parameter(name="사용자유형: S,T")
	private String usrDvCd;
	
	@Parameter(name="토픽지식맵노드ID")
	private String tpcKmmpNodId;
	private String tpcKmmpNodNm;
	
	@Parameter(name="학습구분: TL, AL")
	private String lrnTpCd="";	
	
	@Parameter(name="학습활동ID", required=true)
	private long lrnAtvId;
	
	@Parameter(name="지식맵노드ID")
	private String kmmpNodId;
	
	@Parameter(name="학습상태코드 NL:미학습,DL:학습중,CL:학습완료")
	private String lrnStCd;
	
	@Parameter(name="학습시간초수")
	private Double lrnTmScnt;
	
	@Parameter(name="DBID")
	private String dbId;
	
	@Parameter(name="평가ID")
	private String evId;
	
	@Parameter(name="문항ID")
	private String qtmId;
	
	@Parameter(name="지식맵노드ID")
	private String lrmpNodId;
	
}
