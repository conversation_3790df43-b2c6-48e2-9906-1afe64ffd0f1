<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.wrt.stu">
	
	<!-- AI 첨삭 : 토픽 목록 -->
	<select id="selectAlWrtTpcList" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto" resultType="com.aidt.api.al.wrt.dto.AlWrtResDto">
		/** AlWrtStu-Mapper.xml - selectAlWrtTpcList */
		select tpc.llu_kmmp_nod_id 					-- 대단원노드ID(레슨ID)
		     , llu.kmmp_nod_nm as llu_kmmp_nod_nm	-- 대단원노드명(레슨명)
		     , tpc.tpc_kmmp_nod_id 					-- 토픽노드ID 
		     , tpc.tpc_kmmp_nod_nm 					-- 토픽노드명
		     , wrt.pgrs_st_cd						-- 진행상태
		     , case when pgrs_st_cd is null then '글쓰기'
		            when pgrs_st_cd = 'LN' then '이어 하기'
		            when pgrs_st_cd in ('SM', 'AP', 'EC') then '첨삭 중'
		            when pgrs_st_cd in ('FC', 'CC') then '결과 보기'
		        end pgrs_st_nm
		     , llu.tc_use_yn 						-- 차시사용여부
			 , llu.lckn_yn 							-- 잠금여부
			 , llu.use_yn 							-- 사용여부
		     , rank() over (partition by llu.kmmp_nod_id order by tpc.tpc_kmmp_nod_id) as nod_rank	-- 노드랭크
		  from lms_lrm.ai_kmmp_nod_rcstn llu
		 inner join lms_cms.bc_tk_wrt_tpc tpc
		    on llu.kmmp_nod_id = tpc.llu_kmmp_nod_id 
		   and llu.dpth = 1
		   and llu.del_yn = 'N'
		   and llu.use_yn = 'Y'
		   and tpc.del_yn = 'N'
		   and llu.use_yn = 'Y'
		  left outer join lms_lrm.cm_wrt_mg wrt
		    on llu.opt_txb_id = wrt.opt_txb_id 
		   and tpc.llu_kmmp_nod_id = wrt.llu_kmmp_nod_id 
		   and tpc.tpc_kmmp_nod_id = wrt.tpc_kmmp_nod_id 
		   and wrt.del_yn = 'N'
		   and wrt.stu_usr_id = #{usrId} 				-- 파라미터: 학생ID
		 where tpc.tw_wrt_dv_cd = 'W'					-- 회화첨삭구분코드(T:talk/W:write)
		   and llu.opt_txb_id = #{optTxbId} 			-- 파라미터: 운영교과서ID
		 order by ifnull(llu.rcstn_ordn, llu.orgl_ordn), tpc.srt_ordn
	</select>
	
	<!-- AI 첨삭 : 최근학습정보 -->
	<select id="selectWrtRcLrnInfo"  parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto" resultType="com.aidt.api.al.wrt.dto.AlWrtResDto">
		/** AlWrtStu-Mapper.xml - selectWrtRcLrnInfo */
		select wrt.llu_kmmp_nod_id 
		     , wrt.tpc_kmmp_nod_id 
		  from lms_lrm.cm_wrt_mg wrt
		 where wrt.opt_txb_id = #{optTxbId}
		   and wrt.stu_usr_id = #{usrId}
		 order by wrt.stu_sav_dtm desc
		 limit 1
	</select>
	
	<!-- AI 첨삭관리 : 생성(학생 학습시작) -->
	<insert id="insertWrtMg" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtStu-Mapper.xml - insertWrtMg */
		insert into lms_lrm.cm_wrt_mg(  
			  opt_txb_id
			, llu_kmmp_nod_id
			, tpc_kmmp_nod_id
			, stu_usr_id
			, pgrs_st_cd
			, del_yn
			, crtr_id
			, crt_dtm
			, mdfr_id
			, mdf_dtm
			, db_id
		) values( 
			  #{optTxbId}
			, #{lluKmmpNodId}
			, #{tpcKmmpNodId}
			, #{usrId}
			, 'LN'
			, 'N'
			, #{usrId}
			, now()
			, #{usrId}
			, now()
			, #{txbId}
		);
	</insert>
	
	<!-- AI 첨삭 : 상세 -->
	<select id="selectWrtDtl" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto" resultType="com.aidt.api.al.wrt.dto.AlWrtResDto">
		/** AlWrtStu-Mapper.xml - selectWrtDtl */
		select t1.llu_kmmp_nod_id			-- 대단원노드ID(레슨ID)
		     , t1.llu_kmmp_nod_nm			-- 대단원노드명(레슨명)
		     , t1.tpc_kmmp_nod_id			-- 토픽노드ID 
		     , t1.tpc_kmmp_nod_nm			-- 토픽노드명
		     , t1.acp_id					-- 아키핀ID
		     , t1.tc_use_yn					-- 차시사용여부
			 , t1.use_yn					-- 사용여부
			 , t1.lckn_yn					-- 잠금여부
		     , t1.prev_use_yn				-- 이전단원 잠금여부
		     , t1.prev_llu_nod_id			-- 이전단원 단원ID
		     , t1.prev_tpc_nod_id			-- 이전단원 토픽ID
		     , t1.next_use_yn				-- 다음단원 잠금여부
		     , t1.next_llu_nod_id			-- 다음단원 단원ID
		     , t1.next_tpc_nod_id			-- 다음단원 토픽ID
		     , wrt.pgrs_st_cd				-- 진행상태코드
		     , wrt.stu_cans_cn				-- 학생정답내용
		     , wrt.tcr_annx_cn				-- 교사첨부내용
		     , wrt.tcr_annx_dtl				-- 교사첨부상세
		     , wrt.ai_annx_cn				-- AI첨삭내용
		     , wrt.stu_mrk_cn				-- 학생채점내용
		     , wrt.cstn_scr					-- 구성점수
		     , wrt.expr_scr					-- 표현점수
		     , wrt.voc_scr					-- 어휘점수
		     , wrt.grmr_scr					-- 문법점수
		     , wrt.stu_sav_dtm				-- 학생저장일시
		     , wrt.lrn_tm_scnt				-- 학습시간초수
		  from (
		  	select tpc.llu_kmmp_nod_id 					
			     , llu.kmmp_nod_nm as llu_kmmp_nod_nm	
			     , tpc.tpc_kmmp_nod_id 					
			     , tpc.tpc_kmmp_nod_nm 		
			     , tpc.acp_id
			     , llu.tc_use_yn
		  	     , llu.use_yn
		  	     , llu.lckn_yn
			     , llu.opt_txb_id
				 , lag(llu.tc_use_yn) OVER (ORDER BY llu.rcstn_ordn , tpc.srt_ordn) AS prev_use_yn
				 , lag(tpc.llu_kmmp_nod_id) OVER (ORDER BY llu.rcstn_ordn , tpc.srt_ordn) AS prev_llu_nod_id
				 , lag(tpc.tpc_kmmp_nod_id) OVER (ORDER BY llu.rcstn_ordn , tpc.srt_ordn) AS prev_tpc_nod_id
				 , lead(llu.tc_use_yn) OVER (ORDER BY llu.rcstn_ordn , tpc.srt_ordn) AS next_use_yn
				 , lead(tpc.llu_kmmp_nod_id) OVER (ORDER BY llu.rcstn_ordn , tpc.srt_ordn) AS next_llu_nod_id
				 , lead(tpc.tpc_kmmp_nod_id) OVER (ORDER BY llu.rcstn_ordn , tpc.srt_ordn) AS next_tpc_nod_id
			  from lms_lrm.ai_kmmp_nod_rcstn llu
			 inner join lms_cms.bc_tk_wrt_tpc tpc
			    on llu.kmmp_nod_id = tpc.llu_kmmp_nod_id 
			   and llu.dpth = 1
			   and llu.del_yn = 'N'
			   and tpc.del_yn = 'N'
			   and llu.use_yn = 'Y'
			 where tpc.tw_wrt_dv_cd = 'W'							-- 회화첨삭구분코드(T:talk/W:write)
			   and llu.opt_txb_id = #{optTxbId}
		  ) t1
		  left outer join lms_lrm.cm_wrt_mg wrt
			on t1.opt_txb_id = wrt.opt_txb_id 
		   and t1.llu_kmmp_nod_id = wrt.llu_kmmp_nod_id 
		   and t1.tpc_kmmp_nod_id = wrt.tpc_kmmp_nod_id 
		   and wrt.del_yn = 'N'
		   and wrt.stu_usr_id = #{stuId}
		 where t1.llu_kmmp_nod_id = #{lluKmmpNodId}
		   and t1.tpc_kmmp_nod_id = #{tpcKmmpNodId}
	</select>
	
	<!-- AI 첨삭관리 : 수정 (임시저장, 제출) -->
	<update id="updateWrtMg" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtStu-Mapper.xml - updateWrtMg */
		update lms_lrm.cm_wrt_mg set
		       pgrs_st_cd = #{pgrsStCd}
		     , stu_cans_cn = #{stuCansCn}
		     , lrn_tm_scnt = ifnull(lrn_tm_scnt,0) + #{lrnTmScnt}
		     , stu_sav_dtm = current_timestamp 
		     , mdfr_id = #{usrId}
		     , mdf_dtm = current_timestamp
		 where opt_txb_id = #{optTxbId}
		   and llu_kmmp_nod_id = #{lluKmmpNodId}
		   and tpc_kmmp_nod_id = #{tpcKmmpNodId}
		   and stu_usr_id = #{usrId}   
	</update>
	
	<!-- AI 첨삭관리 : 수정 (임시저장, 제출) -->
	<update id="updateWrtMgAiEdit" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtStu-Mapper.xml - updateWrtMg */
		update lms_lrm.cm_wrt_mg set
		         ai_annx_cn = #{aiAnnxCn}		-- ai 첨삭 정제 데이터
		       , ai_annx_dtl = #{aiAnnxDtl}		-- ai 첨삭 원본
		       , cstn_scr = #{cstnScr}			-- 구성점수
		       , expr_scr = #{exprScr}			-- 표현점수
		       , voc_scr = #{vocScr}			-- 어휘점수
		       , grmr_scr = #{grmrScr}			-- 문법점수
		       , pgrs_st_cd = 'SM'
		 where opt_txb_id = #{optTxbId}
		   and llu_kmmp_nod_id = #{lluKmmpNodId}
		   and tpc_kmmp_nod_id = #{tpcKmmpNodId}
		   and stu_usr_id = #{stuId}   
	</update>
	
	<!-- AI 첨삭관리 : 확인완료 및 다시쓰기 저장 -->
	<update id="updateWrtMgConfComp" parameterType="com.aidt.api.al.wrt.dto.AlWrtReqDto">
		/** AlWrtStu-Mapper.xml - updateWrtMg */
		update lms_lrm.cm_wrt_mg set
		       pgrs_st_cd = 'CC'
		     <if test='stuMrkCn != null and !stuMrkCn.equals("")'>
		     , stu_mrk_cn = #{stuMrkCn}
		     </if>
		     <if test='lrnTmScnt != 0'>
		     , lrn_tm_scnt = ifnull(lrn_tm_scnt,0) + #{lrnTmScnt}
		     </if>
		     , mdfr_id = #{usrId}
		     , mdf_dtm = current_timestamp
		 where opt_txb_id = #{optTxbId}
		   and llu_kmmp_nod_id = #{lluKmmpNodId}
		   and tpc_kmmp_nod_id = #{tpcKmmpNodId}
		   and stu_usr_id = #{usrId}   
	</update>

	<insert id="insertAiwriteReqLog" parameterType="com.aidt.api.al.wrt.dto.AIWrtReqLogDto">
		/** AlWrtStu-Mapper.xml - insertAiwriteReqLog */
		INSERT INTO lms_lrm.cm_aiwrite_req_log
		(  
			 opt_txb_id
			,llu_kmmp_nod_id
			,tpc_kmmp_nod_id
			,stu_id
			,system_cd
			,req_dv_cd
			,rs_yn
			,rs_msg
			,conn_url
			,body_cn
			,reg_dtm
		)
		VALUES
		( 
			 #{optTxbId}
			,#{lluKmmpNodId}
			,#{tpcKmmpNodId}
			,#{stuId}
			,#{systemCd}
			,#{reqDvCd}
			,#{rsYn}
			,#{rsMsg}
			,#{connUrl}
			,#{bodyCn}
			,NOW()
		)
	</insert>
	
	<insert id="insertAiwriteRsLog" parameterType="com.aidt.api.al.wrt.dto.AIWrtRsLogDto">
		/** AlWrtStu-Mapper.xml - insertAiwriteRsLog */
		INSERT INTO lms_lrm.cm_aiwrite_rs_log
		(  
			 opt_txb_id
			,llu_kmmp_nod_id
			,tpc_kmmp_nod_id
			,stu_id
			,rs_yn
			,rs_msg
			,body_cn
			,reg_dtm
		)
		VALUES
		( 
			 #{optTxbId}
			,#{lluKmmpNodId}
			,#{tpcKmmpNodId}
			,#{stuId}
			,#{rsYn}
			,#{rsMsg}
			,#{bodyCn}
			,NOW()
		)
	</insert>

</mapper>