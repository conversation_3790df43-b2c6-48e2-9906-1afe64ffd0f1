package com.aidt.api.ea.asncom;

import java.math.BigDecimal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.aidt.api.al.pl.cm.mg.AlMgService;
import com.aidt.api.bc.cm.dto.BcStuListDto;
import com.aidt.api.bc.inf.dto.BcInfDto;
import com.aidt.api.bc.inf.tcr.BcInfTcrService;
import com.aidt.api.ea.asn.stu.dto.EaAsnStuDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.asncom.dto.EaAsnFleDto;
import com.aidt.api.ea.grpblbd.stu.dto.EaUserSessionDto;
import com.aidt.api.tl.cmtxb.tcr.TlCmTxbTcrService;
import com.aidt.api.tl.common.TlCmUtil;
import com.aidt.api.tl.oneclksetm.tcr.dto.TlOneClkSetmTocDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 과제 - 학생 Service
 */
@Service
public class EaAsnComService {

	private final String MAPPER_NAMESPACE = "api.ea.asncom.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private BcInfTcrService bcInfTcrService;
	
	@Autowired
	private TlCmTxbTcrService tlCmTxbTcrService;
	
	@Autowired
	private AlMgService alMgService;
	
	@Value("${ncloud.s3.content-bucket-name:lms-op-dev-sto-02}")
	private String BUCKET_NAME;
	

	// Return Object
	List<EaAsnStuDto> responseList = new ArrayList<>();



	/**
	 * 과제 상세 조회 - 학생
	 * @param eaAsnStuDto
	 * @return hashMap
	 */
	public Map<String, Object> selectAsnStuDetail(EaAsnStuDto eaAsnStuDto) {

		// Clear Object;
		responseList.clear();

		List<EaUserSessionDto> userInfo = this.setSessionInfo();

		if(StringUtils.isEmpty(eaAsnStuDto.getStuUsrId())) {
			eaAsnStuDto.setStuUsrId(userInfo.get(0).getSessionUsrId());
		}
		eaAsnStuDto.setUserId(userInfo.get(0).getSessionUsrId());
		eaAsnStuDto.setOptTxbId(userInfo.get(0).getSessionOptTxbId());

		Map<String, Object> resMap = new HashMap<>();
		List<EaAsnFleDto> tcrFileList = new ArrayList<>();//교사 파일목록
		List<EaAsnFleDto> stuFileList = new ArrayList<>();//학생 파일목록

		
		// 과제 상세 조회(교과,특별,AI,일반)
		EaAsnStuDto resEaAsnDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnDetail" ,eaAsnStuDto);
		if(resEaAsnDto == null || resEaAsnDto.getAsnId() == null) {
			resMap.put("resCd", "fail");
			resMap.put("resMsg", "과제 정보를 확인할 수 없습니다.");
			return resMap;
		}
		
		if("AL".equals(resEaAsnDto.getLrnTpCd())) {
			// AI맞춤학습 학습창 파라미터 조회
			EaAsnStuDto resAiDto = commonDao.select(MAPPER_NAMESPACE + "selectAiIsRcm" ,resEaAsnDto);
			if(resAiDto != null &&!StringUtils.isEmpty(resAiDto.getEvCmplYn()) && "Y".equals(resAiDto.getEvCmplYn())){
				resEaAsnDto.setIsRcm("Y");
			} else {
				resEaAsnDto.setIsRcm("N");
			}
			resEaAsnDto.setEvDtlDvCd("OV");
			String evCmplYn = commonDao.select(MAPPER_NAMESPACE + "selectAiEvRcmYn" ,resEaAsnDto);
			if("Y".equals(evCmplYn)) {
				resEaAsnDto.setEvDtlDvCd("C1");
				resEaAsnDto.setEvRcmYn(commonDao.select(MAPPER_NAMESPACE + "selectAiEvRcmYn" ,resEaAsnDto));	
			} else {
				resEaAsnDto.setEvRcmYn("Y");
			}
		}
		resMap.put("resEaAsnDto", resEaAsnDto);


		// 첨부ID가 있을 경우
		if(!StringUtils.isEmpty(resEaAsnDto.getTcrAnnxId())&& !"0".equals(resEaAsnDto.getTcrAnnxId())) { // 교사 파일첨부ID
			//파일 조회
			tcrFileList = this.selectFile(resEaAsnDto.getTcrAnnxId());

			resMap.put("tcrFileList", tcrFileList);
		}
		if (!StringUtils.isEmpty(resEaAsnDto.getStuAnnxId()) && !"0".equals(resEaAsnDto.getStuAnnxId())) {	// 학생 파일첨부ID
			//파일 조회
			stuFileList = this.selectFile(resEaAsnDto.getStuAnnxId());

			resMap.put("stuFileList", stuFileList);
		}

		return resMap;
	}



	/**
	 * 모둠 과제 상세 조회
	 * @param eaAsnStuDto
	 * @return hashMap
	 */
	public Map<String, Object> selectAsnGrpStuDetail(EaAsnStuDto eaAsnStuDto) {
		Map<String, Object> resMap = new HashMap<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
        if(eaAsnStuDto.getStuUsrId() == null) {
        	eaAsnStuDto.setStuUsrId(userDetails.getUsrId());
        }
    	eaAsnStuDto.setOptTxbId(userDetails.getOptTxbId());

    	// 모둠 과제 조회
		List<EaAsnStuDto> asnDetail = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnGrpStuDetail" , eaAsnStuDto);
		if(asnDetail.isEmpty() || asnDetail.get(0).getAsnId() == null) {
			resMap.put("resCd", "fail");
			resMap.put("resMsg", "과제 정보를 확인할 수 없습니다.");
			return resMap;
		}
		// 2024-05-24 모듬 상세 첨부파일 조회
    	for(EaAsnStuDto item : asnDetail) {

    		// 과제 첨부파일
    		String annxId = item.getAnnxId();
    		if(!StringUtils.isEmpty(annxId)) {
            	List<EaAsnFleDto> fleList = this.selectFile(annxId);
            	item.setFleList(fleList);
        	}


    	}

		// 모둠 구성원 조회
		List<EaAsnStuDto> grpDetail = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpDetail" , eaAsnStuDto);

		if(grpDetail.size() > 0 ) {
			if(!StringUtils.isEmpty(grpDetail.get(0).getAnnxIdForSmt())) {
				// 과제제출 첨부파일
				String smtAnnxId = grpDetail.get(0).getAnnxIdForSmt();

	            List<EaAsnFleDto> smtFleList = this.selectFile(smtAnnxId);
	            asnDetail.get(0).setSmtFleList(smtFleList);
			}

			eaAsnStuDto.setGrpId(grpDetail.get(0).getGrpId());
			eaAsnStuDto.setGrpTemId(grpDetail.get(0).getGrpTemId());
		}



		// 모둠 게시판 존재 여부
		int blbdCnt = commonDao.select(MAPPER_NAMESPACE + "selectBlbdCnt" , eaAsnStuDto);

		resMap.put("asnDetail", asnDetail);
		resMap.put("grpDetail", grpDetail);
		resMap.put("blbdCnt", blbdCnt);


		return resMap;
	}

	public Map<String, Object> selectAllSmt(EaAsnStuDto eaAsnStuDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		return this.selectAllSmt(eaAsnStuDto, userDetails);
	}
	/**
	 * 과제 전체 제출 알림 전송 
	 * @param eaAsnStuDto
	 * @return eaAsnStuDto
	 */
	public Map<String, Object> selectAllSmt(EaAsnStuDto eaAsnStuDto, CommonUserDetail userDetail) {
		Map<String, Object> result = new HashMap<>();
		List<EaUserSessionDto> userInfo = this.setSessionInfo(userDetail);
		String allSmtYn = "";
		EaAsnStuDto eaDto = new EaAsnStuDto();
		eaAsnStuDto.setOptTxbId(userInfo.get(0).getSessionOptTxbId());
		eaAsnStuDto.setStuUsrId(userInfo.get(0).getSessionUsrId());
		
		if(eaAsnStuDto.getAsnId() == null || StringUtils.isEmpty(eaAsnStuDto.getAsnLrnTp()) || StringUtils.isEmpty(eaAsnStuDto.getAsnLrnTpNm())) {
			result.put("resCd", "fail");
			result.put("resMsg", "Params does not exist.");
			return result;
		}
		
		if(!StringUtils.isEmpty(eaAsnStuDto.getAsnLrnTp()) &&"GR".equals(eaAsnStuDto.getAsnLrnTp())) {
			allSmtYn = commonDao.select(MAPPER_NAMESPACE + "selectAllGrSmt", eaAsnStuDto);
			eaDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnGrpStuDetail", eaAsnStuDto);
			
		} else {
			allSmtYn = commonDao.select(MAPPER_NAMESPACE + "selectAllSmt", eaAsnStuDto);
			eaDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnDetail", eaAsnStuDto);
		}
		
		
		if(!StringUtils.isEmpty(allSmtYn) && "Y".equals(allSmtYn)) {
			String tpNm = eaAsnStuDto.getAsnLrnTpNm();
			String fmCn = "[" + tpNm + "] 모든 학생들이 과제를 제출했어요. 지금 확인해보세요.";
			BcInfDto bcInfDto = new BcInfDto();
			String mvCn = "";
			
			if(!StringUtils.isEmpty(eaAsnStuDto.getAsnLrnTp()) &&"GR".equals(eaAsnStuDto.getAsnLrnTp())) {
				//모둠 과제
				mvCn = "{'filePath':'/ea/asn/tcr/EaAsnGrpTcr.vue','layout':'default','params':{'asnId':"+ eaDto.getAsnId() +"}}";
			} else if(!StringUtils.isEmpty(eaAsnStuDto.getAsnLrnTp()) &&"GE".equals(eaAsnStuDto.getAsnLrnTp())) {
				//일반 과제
				mvCn = "{'filePath':'/ea/asn/tcr/EaAsnGeneralTcrDetail.vue','layout':'default','params':{'asnId':"+ eaDto.getAsnId() +"}}";
			} else if (!StringUtils.isEmpty(eaAsnStuDto.getAsnLrnTp()) &&("TL".equals(eaAsnStuDto.getAsnLrnTp()) || "AL".equals(eaAsnStuDto.getAsnLrnTp()))) {
				//학습과제
				mvCn = "{'filePath':'/ea/asn/tcr/EaAsnSbjTcrDetail.vue','layout':'default','params':{'asnId':"+ eaDto.getAsnId() +",'asnLrnTp':'"+eaAsnStuDto.getAsnLrnTp()+"'}}";
			}
			
			BcStuListDto bcInfListDto = new BcStuListDto();
			bcInfListDto.setUsrId(eaDto.getTcrUsrId());
			bcInfListDto.setInfmMvCn(mvCn);
			List<BcStuListDto> stuList = new ArrayList<>();
			stuList.add(bcInfListDto);
			
			bcInfDto.setInfmCn(fmCn);
			bcInfDto.setInfmTpCd("LR");
			bcInfDto.setInfmClCd("AS");
			bcInfDto.setInfmDtlClCd("SC");			
			bcInfDto.setInfmNm("[과제]제출 완료");
//			bcInfDto.setInfmMvDvCd("COMP");
//			bcInfDto.setInfmMvCn(mvCn);
			bcInfDto.setUsrId(userInfo.get(0).getSessionUsrId());
			bcInfDto.setOptTxbId(userInfo.get(0).getSessionOptTxbId());
			bcInfDto.setTcrUsrId(eaDto.getTcrUsrId());
			bcInfDto.setStuList(stuList);
			bcInfDto.setDbId(userInfo.get(0).getSessionTxbId());
			
			int cnt = bcInfTcrService.insertInfList(bcInfDto);
			result.put("resCd", "success");
			result.put("cnt", cnt);
		} else if(!StringUtils.isEmpty(allSmtYn) && "N".equals(allSmtYn)){
			result.put("resCd", "success");
			result.put("resMsg", "학생 전체 미 완료 상태");
		} else {
			result.put("resCd", "fail");
			result.put("resMsg", "조회실패");
		}

		return result;

	}
	
	
	/**
	 * 우리반 학습 과제 변경 분기
	 * @param eaAsnTcrDto
	 * @return eaAsnTcrDto
	 */
	public Map<String, Object> callTlAsn (List<TlOneClkSetmTocDto> ansDtoList){
		Map<String, Object> result = new HashMap<>();
		
		if(ansDtoList == null || ansDtoList.isEmpty()) {
			result.put("resCd", "S01");
			result.put("asnId", null);
			return result;
		}
		
		
		// 학습과제 변경 
		List<TlOneClkSetmTocDto> cmplTlDtoList = ansDtoList.stream()
				.filter(cmpl -> cmpl.getAtvChnCnt() > 0)
				.collect(Collectors.toList());
		if(cmplTlDtoList != null && !cmplTlDtoList.isEmpty()) {
			result = this.updateTlAsn(cmplTlDtoList);
		}
		
		
		// 사용여부 변경
		List<TlOneClkSetmTocDto> statusTlDtoList = cmplTlDtoList.stream()
				.filter(cmpl -> cmpl.getAtvCnt() == 0)
				.peek(cmpl -> cmpl.setUseYn("Y")) 
				.collect(Collectors.toList());
		if(statusTlDtoList != null && !statusTlDtoList.isEmpty()) {
			result = this.updateTlAsnStatus(statusTlDtoList);
		}
		
		
		List<TlOneClkSetmTocDto> upStatusTlDtoList = ansDtoList.stream()
				.filter(cmpl -> cmpl.getAtvChnCnt() == 0)
				.collect(Collectors.toList());
		if(upStatusTlDtoList != null && !upStatusTlDtoList.isEmpty()) {
			result = this.updateTlAsnStatus(upStatusTlDtoList);
		}
		
		
		
		return result;
	}
	
	
	/**
	 * 우리반 학습 과제 변경
	 * @param eaAsnTcrDto
	 * @return eaAsnTcrDto
	 */
	public Map<String, Object> updateTlAsn(List<TlOneClkSetmTocDto> ansDtoList) {
		Map<String, Object> result = new HashMap<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		
		/**
		 * 2025.03.17 
		 * 원클릭설정 변경에 따른 파라미터 변경 수정건 
		*/
		if(ansDtoList == null || ansDtoList.isEmpty()) {
			 result.put("resCd", "E01");
			 return result;
		}
		
		// 우리반 수업 중 개념학습(CL)인 과제가 미완료이면서 진행중인 과제 조회
		EaAsnTcrDto eaAsnTcrDto = new EaAsnTcrDto();
		eaAsnTcrDto.setOptTxbId(ansDtoList.get(0).getOptTxbId());
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		List<EaAsnTcrDto> tlAsnDtoList = commonDao.selectList(MAPPER_NAMESPACE + "selectTlAsn", eaAsnTcrDto);

		// 과제가 없을 경우
		if(tlAsnDtoList == null || tlAsnDtoList.isEmpty()) {
			result.put("resCd", "S01");
			result.put("asnId", null);
			return result;
		}
		
        List<EaAsnTcrDto> filteredList = tlAsnDtoList.stream()
        	    .filter(tl -> ansDtoList.stream()
        	        .anyMatch(ans -> ans.getLluNodId().equals(tl.getLuNodId()) &&
        	                         ans.getLrmpNodId().equals(tl.getTcNodId()))
        	    )
        	    .map(tl -> {
        	        ansDtoList.stream()
        	            .filter(ans -> ans.getLluNodId().equals(tl.getLuNodId()) &&
        	                           ans.getLrmpNodId().equals(tl.getTcNodId()))
        	            .findFirst()
        	            .ifPresent(ans -> {
        	                tl.setLuNodId(ans.getLluNodId());
        	                tl.setTcNodId(ans.getLrmpNodId());
        	                tl.setTtlLrnCnt(ans.getAtvChnCnt());
        	                tl.setMdfrId(userDetails.getUsrId());
        	            });
        	        return tl;
        	    })
        	    .collect(Collectors.toList());
	
		
		for(EaAsnTcrDto tlAsnDto : filteredList) {
			commonDao.update(MAPPER_NAMESPACE + "updateLrnCnt" , tlAsnDto);
			
			// 해당 과제 학생 조회
			List<EaAsnTcrDto> tlAsnStuList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnStu", tlAsnDto);
			// 학생별 우리반 수업 과제 완료 개수 조회
			List<Map<String,Object>> lrnAtvStList = tlCmTxbTcrService.selectLrnAtvSt(tlAsnDto.getOptTxbId(), tlAsnDto.getTcNodId());
			
		   
			Map<String, Integer> lrnAtvMap = lrnAtvStList.stream()
				    .filter(lrnAtv -> lrnAtv.get("LRN_USR_ID") != null && lrnAtv.get("CL_CNT") != null) 
				    .collect(Collectors.toMap(
				        lrnAtv -> (String) lrnAtv.get("LRN_USR_ID"), 
				        lrnAtv -> {
				           
				            Object clCntObj = lrnAtv.get("CL_CNT");

				            return clCntObj != null ? 
				                    (clCntObj instanceof BigDecimal ? ((BigDecimal) clCntObj).intValue() : 0) 
				                    : 0;
				        }
				    ));

			// stuUsrId와 LRN_USR_ID가 같은 경우만 남김
			List<EaAsnTcrDto> filteredStuList = tlAsnStuList.stream()
				    .filter(stu -> lrnAtvMap.containsKey(stu.getStuUsrId())) 
				    .peek(stu -> {
				        int ttlLrnCnt = tlAsnDto.getTtlLrnCnt();
				        
				        // stuUsrId와 lrnUsrId가 같은 경우에만 clCnt 값을 설정
				        String stuUsrId = stu.getStuUsrId();
				        int clCnt = lrnAtvMap.getOrDefault(stuUsrId, 0);  

				        stu.setSmtCmplYn(ttlLrnCnt > clCnt ? "N" : "Y");

				        if (ttlLrnCnt < clCnt) {
				            stu.setCmplLrnCnt(ttlLrnCnt);
				        } else {
				            stu.setCmplLrnCnt(clCnt);
				        }

				    })
				    .collect(Collectors.toList());
			
			
			
				for(EaAsnTcrDto stuDto : filteredStuList) {
					commonDao.update(MAPPER_NAMESPACE + "updateCmpl" , stuDto);
				}
		}
		
		result.put("resCd", "S01");
		//result.put("asnId", tlAsnDto.getAsnId());
		return result;

	}
	
	/**
	 * 우리반 학습 과제 사용여부 변경
	 * @param eaAsnTcrDto
	 * @return eaAsnTcrDto
	 */
	public Map<String, Object> updateTlAsnStatus(List<TlOneClkSetmTocDto> ansDtoList) {
		Map<String, Object> result = new HashMap<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		int cnt = 0;
		
		/**
		 * 2025.03.17 
		 * 원클릭설정 변경에 따른 파라미터 변경 수정건 
		*/
		if(ansDtoList == null || ansDtoList.isEmpty()) {
			 result.put("resCd", "E01");
			 return result;
		}
		
		// 우리반 수업 과제 조회 
		EaAsnTcrDto eaAsnTcrDto = new EaAsnTcrDto();
		eaAsnTcrDto.setOptTxbId(ansDtoList.get(0).getOptTxbId());
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		List<EaAsnTcrDto> tlAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTlAsnStatus", eaAsnTcrDto);
    	
		
		// 과제가 없을 경우
		if(tlAsnList.isEmpty() || tlAsnList.size() < 1) {
			result.put("resCd", "S01");
			result.put("asnCnt", cnt);
			return result;
		}
		
	  List<EaAsnTcrDto> filteredList = tlAsnList.stream()
        	    .filter(tl -> ansDtoList.stream()
        	        .anyMatch(ans -> ans.getLluNodId().equals(tl.getLuNodId()) &&
        	                         ans.getLrmpNodId().equals(tl.getTcNodId()))
        	    )
        	    .map(tl -> {
        	        ansDtoList.stream()
        	            .filter(ans -> ans.getLluNodId().equals(tl.getLuNodId()) &&
        	                           ans.getLrmpNodId().equals(tl.getTcNodId()))
        	            .findFirst()
        	            .ifPresent(ans -> {
        	                tl.setLuNodId(ans.getLluNodId());
        	                tl.setTcNodId(ans.getLrmpNodId());
        	                tl.setUseYn(ans.getUseYn());
        	                tl.setTtlLrnCnt(ans.getAtvChnCnt());
        	                tl.setMdfrId(userDetails.getUsrId());
        	            });
        	        return tl;
        	    })
        	    .collect(Collectors.toList());
		
		// 사용여부 업데이트
		for(EaAsnTcrDto asnDto : filteredList) {
		
			if("Y".equals(asnDto.getUseYn()) && asnDto.getTtlLrnCnt() < 1) {
				if("CL".equals(asnDto.getLrnStpDvCd())) {
					asnDto.setUseYn("N");
				}
				
			} 
			
			cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnUseYn" , asnDto);
		}
		
		
		
		result.put("resCd", "S01");
		result.put("asnCnt", cnt);
		return result;

	}
	
	/**
	 * AI 맞춤학습 과제 사용여부 변경
	 * @param eaAsnTcrDto
	 * @return eaAsnTcrDto
	 */
	public Map<String, Object> updateAiAsnStatus(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> result = new HashMap<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		int cnt = 0;
		if (StringUtils.isEmpty(eaAsnTcrDto.getLuNodId()) || StringUtils.isEmpty(eaAsnTcrDto.getOptTxbId()) 
				|| StringUtils.isEmpty(eaAsnTcrDto.getUseYn())){
			 result.put("resCd", "E01");
			 result.put("asnCnt", cnt);
    		 return result;
    	}
		
		if(!StringUtils.isEmpty(eaAsnTcrDto.getLuNodId())) {
    		//ai 지식맵ID 조회
        	List<String> kmmpNodIds = alMgService.selectKmmpNodIdByLrmpNodId(eaAsnTcrDto.getLuNodId());
            if(kmmpNodIds.isEmpty() || kmmpNodIds.size() < 1) {
                result.put("resCd", "S01");
                result.put("asnCnt", cnt);
                return result;
            }
        	eaAsnTcrDto.setAiSearchOptionList(kmmpNodIds);
    	}
    	
    	
		// AI 맞춤 학습 중 특정 단원 과제 조회	
		List<EaAsnTcrDto>  aiAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiAsnStatus", eaAsnTcrDto);
		
		// 과제가 없을 경우
		if(aiAsnList.isEmpty() || aiAsnList.size() < 1) {
			result.put("resCd", "S01");
			result.put("asnCnt", cnt);
			return result;
		}
		
		// 사용여부 업데이트
		for(EaAsnTcrDto asnDto : aiAsnList) {
			asnDto.setMdfrId(userDetails.getUsrId());
			asnDto.setUseYn(eaAsnTcrDto.getUseYn());
			
			cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnUseYn" , asnDto);
		}
		
		
		
		result.put("resCd", "S01");
		result.put("asnCnt", cnt);
		return result;

	}

	
	/**
	 * 우리반 학습 과제 잠금여부 변경
	 * @param eaAsnTcrDto
	 * @return eaAsnTcrDto
	 */
	public Map<String, Object> updateTlAsnLockStatus(List<TlOneClkSetmTocDto> ansDtoList) {
		Map<String, Object> result = new HashMap<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		int cnt = 0;
    	
		/**
		 * 2025.03.17 
		 * 원클릭설정 변경에 따른 파라미터 변경 수정건
		*/
		if(ansDtoList == null || ansDtoList.isEmpty()) {
			 result.put("resCd", "E01");
			 result.put("asnCnt", cnt);
			 return result;
		}
		
		// 우리반 수업  과제 조회
		EaAsnTcrDto eaAsnTcrDto = new EaAsnTcrDto();
		eaAsnTcrDto.setOptTxbId(ansDtoList.get(0).getOptTxbId());
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		List<EaAsnTcrDto> tlAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectTlAsnStatus", eaAsnTcrDto);
		
		// 과제가 없을 경우
		if(tlAsnList.isEmpty() || tlAsnList.size() < 1) {
			result.put("resCd", "S01");
			result.put("asnCnt", cnt);
			return result;
		}
		
		
	  List<EaAsnTcrDto> filteredList = tlAsnList.stream()
        	    .filter(tl -> ansDtoList.stream()
        	        .anyMatch(ans -> ans.getLluNodId().equals(tl.getLuNodId()) &&
        	                         ans.getLrmpNodId().equals(tl.getTcNodId()))
        	    )
        	    .map(tl -> {
        	        ansDtoList.stream()
        	            .filter(ans -> ans.getLluNodId().equals(tl.getLuNodId()) &&
        	                           ans.getLrmpNodId().equals(tl.getTcNodId()))
        	            .findFirst()
        	            .ifPresent(ans -> {
        	                tl.setLuNodId(ans.getLluNodId());
        	                tl.setTcNodId(ans.getLrmpNodId());
        	                tl.setTtlLrnCnt(ans.getAtvChnCnt());
        	                tl.setMdfrId(userDetails.getUsrId());
        	                tl.setLcknYn(ans.getLcknYn());
        	            });
        	        return tl;
        	    })
        	    .collect(Collectors.toList());
		 
		// 잠금여부 업데이트
		for(EaAsnTcrDto asnDto : filteredList) {
			
			cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnLockYn" , asnDto);
		}
		
		
		
		result.put("resCd", "S01");
		result.put("asnCnt", cnt);
		return result;

	}
	
	/**
	 * ai 맞춤 학습 과제 잠금여부 변경
	 * @param eaAsnTcrDto
	 * @return eaAsnTcrDto
	 */
	public Map<String, Object> updateAiAsnLockStatus(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> result = new HashMap<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		int cnt = 0;
		if (StringUtils.isEmpty(eaAsnTcrDto.getLuNodId()) || StringUtils.isEmpty(eaAsnTcrDto.getTcNodId()) 
				|| StringUtils.isEmpty(eaAsnTcrDto.getOptTxbId()) || StringUtils.isEmpty(eaAsnTcrDto.getLcknYn())){
			 result.put("resCd", "E01");
			 result.put("asnCnt", cnt);
    		 return result;
    	}
    	

		if(!StringUtils.isEmpty(eaAsnTcrDto.getLuNodId())) {
    		//ai 지식맵ID 조회
        	List<String> kmmpNodIds = alMgService.selectKmmpNodIdByLrmpNodId(eaAsnTcrDto.getLuNodId());
            
            if(kmmpNodIds.isEmpty() || kmmpNodIds.size() < 1) {
                result.put("resCd", "S01");
                result.put("asnCnt", cnt);
                return result;
            }
            
        	eaAsnTcrDto.setAiSearchOptionList(kmmpNodIds);
    	}
    	
		// AI 맞춤 학습 중 특정 단원 과제 조회	
		List<EaAsnTcrDto>  aiAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiAsnStatus", eaAsnTcrDto);
		
		// 과제가 없을 경우
		if(aiAsnList.isEmpty() || aiAsnList.size() < 1) {
			result.put("resCd", "S01");
			result.put("asnCnt", cnt);
			return result;
		}
		
		// 잠금 여부 업데이트
		for(EaAsnTcrDto asnDto : aiAsnList) {
			asnDto.setMdfrId(userDetails.getUsrId());
			// tcLcknYn(차시잠금여부) : 'Y' -  잠금여부가 변경된 단원,차시의 같은 단원중 잠긴 차시가 1개라도 있는 경우
			// 잠금여부가 변경된 단원,차시의 단원중 잠긴 차시가 1개라도 있으면 AI 맞춤학습의 경우에는 단원이 잠김 처리됨
			if(!StringUtils.isEmpty(eaAsnTcrDto.getTcLcknYn()) && "Y".equals(eaAsnTcrDto.getTcLcknYn())) {
				asnDto.setLcknYn("Y");
			} else {
				asnDto.setLcknYn(eaAsnTcrDto.getLcknYn());
			}
			
			cnt += commonDao.update(MAPPER_NAMESPACE + "updateLrnLockYn" , asnDto);
		}
		
		
		
		result.put("resCd", "S01");
		result.put("asnCnt", cnt);
		return result;

	}
	
	
	/**
	 * 첨부 파일 조회
	 * @param eaAsnStuDto
	 * @return hashMap
	 */
	public List<EaAsnFleDto> selectFile(String annxId) {
	
		List<EaAsnFleDto> fileList = new ArrayList<>();
		
		fileList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnFile", annxId);
	
		if(fileList != null && fileList.size() > 0) {
			for(EaAsnFleDto file : fileList) {
				file.setAnnxFilePathNm(TlCmUtil.makeFleCdnUrl(BUCKET_NAME, file.getAnnxFilePathNm()));
			}
		} 
		
			
		return fileList;
	}

	//todo: 비동기 작업 임시용
	private List<EaUserSessionDto> setSessionInfo() {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		return setSessionInfo(userDetails);
	}

	/**
	 * Set Session User Information
	 * @param
	 * @return  List<EaGrpBlbdStuDto>
	 */
	private List<EaUserSessionDto> setSessionInfo(CommonUserDetail userDetails) {

		EaUserSessionDto eaUserSessionDto = new EaUserSessionDto();

		eaUserSessionDto.setSessionClaId(userDetails.getClaId());
		eaUserSessionDto.setSessionOptTxbId(userDetails.getOptTxbId());
		eaUserSessionDto.setSessionRole(userDetails.getRole());
		eaUserSessionDto.setSessionTxbId(userDetails.getTxbId());
		eaUserSessionDto.setSessionUsrId(userDetails.getUsrId());
		eaUserSessionDto.setSessionUsrNm(userDetails.getUsrNm());
		eaUserSessionDto.setSessionUsrTpCd(userDetails.getUsrTpCd());

		List<EaUserSessionDto> userInfo = new ArrayList<>();
		userInfo.add(eaUserSessionDto);

		return userInfo;
	}


}

