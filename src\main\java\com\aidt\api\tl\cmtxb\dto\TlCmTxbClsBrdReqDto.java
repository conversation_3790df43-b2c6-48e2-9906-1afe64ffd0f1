package com.aidt.api.tl.cmtxb.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TlCmTxbClsBrdReqDto {
	
	/** 차시노드ID */
	@Parameter(name="차시노드ID")
	private String tcNodId;		
	
	/** 단원노드명 */
	@Parameter(name="단원노드명")
    private String lluNm;		
	
	/** 차시노드명 */
	@Parameter(name="차시노드명")
    private String tcNm;		
	
	/** 액티브ID */
	@Parameter(name="액티브ID")
    private String lrnAtvId;		
	
	/** 대제목 */
	@Parameter(name="대제목")
    private String projectName;		
	
	/** 소제목 */
	@Parameter(name="소제목")
	private String[] boardNames;		
	
	/** 보드유형 */
    @Parameter(name="보드유형")
    private String[] boardTypes;		 
    
    /** 보드ID */
    @Parameter(name="보드ID")
    private String[] sequences;		
    
    /** 사용자유형코드(ST:학생/TE:선생님) */
    @Parameter(name="사용자유형코드(ST:학생/TE:선생님)")
    private String usrTpCd;		
    
    /** accessToken */
    @Parameter(name="accessToken")
    private String accessToken;		
    
    /** refreshToken */
    @Parameter(name="refreshToken")
    private String refreshToken;
    
    /** subDomain */
    @Parameter(name="subDomain")
    private String subDomain;		

    /** teacherId */
    @Parameter(name="teacherId")
    private String teacherId;
     
    @Parameter(name="usrId")
    private String usrId;
    
    @Parameter(name="luNodId")
    private String luNodId;
}
