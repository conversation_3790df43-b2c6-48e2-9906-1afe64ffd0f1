package com.aidt.api.bc.home.stu;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.bc.home.stu.dto.BcHomeStuDto;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2024-01-05 17:51:03
 * @modify 2024-01-05 17:51:03
 * @desc 학생_홈 Controller
 */

@Slf4j
@Tag(name="[bc] 홈[BcHomeStu]", description="홈(학생)")
@RestController
@RequestMapping("/api/v1/bc/stu/home")
public class BcHomeStuController {

    @Autowired
    private BcHomeStuService bcHomeStuService;

    /**
     * 홈 조회 요청
     *
     * @param String
     * @return ResponseList<BcHomeStuDto>
     */
    @Operation(summary="홈 조회", description="홈을 조회한다.(학생)")
    @GetMapping(value = "/{userId}")
    public ResponseDto<List<BcHomeStuDto>> selectHomeList(@PathVariable("userId") String userId) {

    	// 2024-01-11 : 조회 방식의 파라미터는 단일 타겟ID가 아닐것으로 판단. 변경 필요

        log.debug("Entrance selectHomeList");
        return Response.ok(bcHomeStuService.selectHomeList(userId));
    }
}
