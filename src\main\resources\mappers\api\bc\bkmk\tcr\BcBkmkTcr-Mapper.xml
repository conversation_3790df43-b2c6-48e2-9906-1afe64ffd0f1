<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.bkmk.tcr">

	<!-- 
		2025.02.03 LU_NOD_ID 컬럼표준화 BIGINT 타입
		LMS_LRM.MY_BKMK.LU_NOD_ID는 문자열도 들어가는 타입으로 LU_NOD_INFO로 컬럼명 변경됨에 따라 쿼리문에 사용된 LU_NOD_ID -> LU_NOD_INFO 수정처리 
	-->

	<!-- 북마크 목록 조회 -->
	<select id="selectBkmkList" resultType="com.aidt.api.bc.bkmk.dto.BcBkmkDto">
		/** BcBkmkTcr-Mapper.xml - selectBkmkList */
		<include refid="api.bc.common.pagingHeader"/>
		SELECT
			A.BKMK_ID,
			A.OPT_TXB_ID,
			<PERSON><PERSON>LU_NOD_INFO AS LU_NOD_ID,
			A.TC_NOD_ID,
			A.LRN_ATV_ID,
			A.LRN_USR_ID,
			A.LRN_TP_CD,
			B.LRMP_NOD_NM AS LLU_NM,
			C.LRMP_NOD_NM AS TC_NM,
			(
				SELECT D.LRN_ATV_NM FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN D
				WHERE D.OPT_TXB_ID = A.OPT_TXB_ID
				AND D.LRN_ATV_ID = A.LRN_ATV_ID
			) AS LRN_ATV_NM,
			'' as checked,
			DATE_FORMAT(A.CRT_DTM, '%Y.%m.%d') AS CRT_DTM
		FROM
			LMS_LRM.MY_BKMK A
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON A.OPT_TXB_ID = B.OPT_TXB_ID
												  AND A.LU_NOD_INFO = B.LRMP_NOD_ID
												  AND B.DPTH = 1
												  AND B.LCKN_YN = 'N'
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C ON A.TC_NOD_ID = C.LRMP_NOD_ID
												  AND A.OPT_TXB_ID = C.OPT_TXB_ID
												  AND C.DPTH = 4
												  AND C.LCKN_YN = 'N'
		<where>
			<if test = 'luNodId != null and !"".equals(luNodId)'>
				AND A.LU_NOD_INFO = #{luNodId}
			</if>
			<if test = 'tcNodId != null and !"".equals(tcNodId)'>
				AND A.TC_NOD_ID = #{tcNodId}
			</if>
			<if test = 'lrnTpCd != null and !"".equals(lrnTpCd)'>
				AND A.LRN_TP_CD = #{lrnTpCd}
			</if>
			<if test = 'optTxbId != null and !"".equals(optTxbId)'>
				AND A.OPT_TXB_ID = #{optTxbId}
			</if>
			<if test = 'lrnAtvId != null and !"".equals(lrnAtvId)'>
				AND A.LRN_ATV_ID = #{lrnAtvId}
			</if>
			<if test = 'lrnUsrId != null and !"".equals(lrnUsrId)'>
				AND A.LRN_USR_ID = #{lrnUsrId}
			</if>
		</where>
		<choose>
			<when test='srhSrt != null and !"".equals(srhSrt)'>
				<choose>
					<when test='srhSrt == "ins" '>
						ORDER BY A.CRT_DTM
					</when>
					<otherwise>
						ORDER BY A.LU_NOD_INFO
					</otherwise>
				</choose>
				<if test='srtInfo == "desc" '>
					desc
				</if>
				<if test='srtInfo == "asc" '>
					asc
				</if>
			</when>
			<otherwise>
				ORDER BY B.RCSTN_ORDN
				<if test='srtInfo == "desc" '>
					desc
				</if>
				<if test='srtInfo == "asc" '>
					asc
				</if>
			</otherwise>
		</choose>

		<include refid="api.bc.common.pagingFooter"/>

	</select>

	<!-- 북마크 상세 조회 -->
	<select id="selectBkmkDtl" parameterType="com.aidt.api.bc.bkmk.dto.BcBkmkDto" resultType="com.aidt.api.bc.bkmk.dto.BcBkmkDto">
		/** BcBkmkTcr-Mapper.xml - selectBkmkDtl */
		SELECT
			A.BKMK_ID,
			A.LRN_TP_CD,
			A.OPT_TXB_ID,
			A.LU_NOD_INFO AS LU_NOD_ID,
			A.TC_NOD_ID,
			A.LRN_ATV_ID,
			A.LRN_USR_ID,
			B.LRMP_NOD_NM AS LLU_NM,
			C.LRMP_NOD_NM AS TC_NM,
			(
				SELECT D.LRN_ATV_NM FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN D
				WHERE D.OPT_TXB_ID = A.OPT_TXB_ID
				AND D.LRN_ATV_ID = A.LRN_ATV_ID
			) AS LRN_ATV_NM,
			A.CRTR_ID,
			A.CRT_DTM,
			A.MDFR_ID,
			A.MDF_DTM,
			A.DB_ID
		FROM
			LMS_LRM.MY_BKMK A
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN B ON A.OPT_TXB_ID = B.OPT_TXB_ID
												  AND A.LU_NOD_INFO = B.LRMP_NOD_ID
												  AND B.DPTH = 1
												  AND B.LCKN_YN = 'N'
		INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN C ON A.TC_NOD_ID = C.LRMP_NOD_ID
												  AND A.OPT_TXB_ID = C.OPT_TXB_ID
												  AND C.DPTH = 4
												  AND C.LCKN_YN = 'N'
		<where>
			AND A.OPT_TXB_ID = #{optTxbId}
			AND A.LRN_TP_CD = #{lrnTpCd}
			AND A.LRN_ATV_ID = #{lrnAtvId}
			AND A.LRN_USR_ID = #{lrnUsrId}
		</where>
	</select>

	<!-- 북마크 등록 전 확인 -->
	<select id="selectBkmkCheck" parameterType="com.aidt.api.bc.bkmk.dto.BcBkmkDto" resultType="int">
		/** BcBkmkTcr-Mapper.xml - selectBkmkCheck */
		SELECT
			COALESCE(SUM(A.BKMK_ID), 0) AS BKMK_ID
		FROM
			LMS_LRM.MY_BKMK A
		<where>
			AND A.OPT_TXB_ID = #{optTxbId}
			AND A.LRN_TP_CD = #{lrnTpCd}
			AND A.LRN_ATV_ID = #{lrnAtvId}
			AND A.LRN_USR_ID = #{lrnUsrId}
		</where>
	</select>

	<!-- 북마크 저장 -->
	<insert id="insertBkmk" parameterType="com.aidt.api.bc.bkmk.dto.BcBkmkDto" useGeneratedKeys="true" keyProperty="bkmkId">
		/** BcBkmkTcr-Mapper.xml - insertBkmk */
		INSERT INTO LMS_LRM.MY_BKMK(
			OPT_TXB_ID, LU_NOD_INFO, TC_NOD_ID, LRN_ATV_ID, LRN_USR_ID, LRN_TP_CD, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{optTxbId}
			, #{luNodId}
			, #{tcNodId}
			, #{lrnAtvId}
			, #{lrnUsrId}
			, #{lrnTpCd}
			, #{crtrId}
			, NOW()
			, #{mdfrId}
			, NOW()
			, #{dbId}
		)
	</insert>

	<!-- 북마크 삭제 -->
	<delete id="deleteBkmk" parameterType="java.lang.Integer">
		/** BcBkmkTcr-Mapper.xml - deleteBkmk */
		DELETE FROM LMS_LRM.MY_BKMK
		WHERE BKMK_ID IN (
		<foreach collection="bkmkIds" item="bkmkId" separator=", ">
			#{bkmkId}
		</foreach>
		)
	</delete>

</mapper>