package com.aidt.api.al.pl.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class AlOneClickDto {
	@Parameter(name="운영교과서ID", required=true)
	private String optTxbId;

	@Parameter(name="사용자ID")
	private String usrId;

	@Parameter(name="대단원ID", required=true)
	private String lluNodId;	
	
	@Parameter(name="차시단원ID")
	private String tluNodId;	
	
	@Parameter(name="잠금여부")
	private String lcknYn;
	
	@Parameter(name="사용여부")
	private String useYn;
	
	@Parameter(name="평가ID")
	private Integer evId;
	
	@Parameter(name="대단원지식맵노드ID")
	private String lluKmmpNodId;
	
	private List<String> lluKmmpNodIdList;
}
