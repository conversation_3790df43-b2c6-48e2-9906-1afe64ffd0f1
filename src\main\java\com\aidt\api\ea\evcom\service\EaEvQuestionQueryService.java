package com.aidt.api.ea.evcom.service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.ea.evcom.adapter.EaEvQuestionAdapter;
import com.aidt.api.ea.evcom.dto.EaEvQuestionSolution;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true)
@Service
@RequiredArgsConstructor
public class EaEvQuestionQueryService {

	private final EaEvQuestionAdapter eaEvQuestionAdapter;

	public Map<Long, EaEvQuestionSolution> getEaEvQuestionSolutionMap(List<Long> qtmIds) {
		return eaEvQuestionAdapter.getEaEvQuestionSolution(qtmIds).stream()
			.collect(Collectors.toMap(EaEvQuestionSolution::getQpQtmId, Function.identity()));
	}

	public Map<Long, List<EaEvQuestionSolution.EaEvQuestionKeyword>> getEaEvQuestionKeywordMap(List<Long> qtmIds) {
		return eaEvQuestionAdapter.getEaEvQuestionKeywords(qtmIds).stream()
			.collect(Collectors.groupingBy(EaEvQuestionSolution.EaEvQuestionKeyword::getQpQtmId, Collectors.toList()));
	}

}