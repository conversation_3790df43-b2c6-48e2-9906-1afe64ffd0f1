package com.aidt.api.al.pl.cm.rcm.adapter;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.aidt.api.al.pl.cm.rcm.dto.EaAiEv;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvPredictProfile;
import com.aidt.common.CommonDao;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class AiQuestionProfileAdapter {

	private final String MAPPER_NAMESPACE = "api.al.pl.profile.";
	private final CommonDao commonDao;

	public List<EaAiEv> getEaAiEvs(Integer evId, String optTxbId, String usrId) {

		if(ObjectUtils.isEmpty(evId) || StringUtils.isAnyBlank(optTxbId, usrId)) {
			return null;
		}

		return commonDao.selectList(MAPPER_NAMESPACE + "getEaAiEvs", Map.of("evId",evId,"optTxbId",optTxbId,"usrId",usrId));
	}

	public void updateEaAiEvRange(EaAiEv eaAiEv) {
		commonDao.update(MAPPER_NAMESPACE + "updateEaAiEvRange", eaAiEv);
	}

	public List<EaAiEvPredictProfile> getUnitAllEaAiEvByQuestions(EaAiEv eaAiEv) {
		return commonDao.selectList(MAPPER_NAMESPACE + "getUnitAllEaAiEvQuestions", eaAiEv);
	}

	public List<EaAiEvPredictProfile> getEaAiEvQuestionAnswers(EaAiEv eaAiEv) {
		return commonDao.selectList(MAPPER_NAMESPACE + "getEaAiEvQuestionAnswers", eaAiEv);
	}

	public void upsertEaAiEvQuestionProfile(EaAiEv eaAiEv, List<EaAiEvPredictProfile> predictProfiles) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaAiEvQuestionProfile", Map.of("eaAiEv",eaAiEv,"predictProfiles",predictProfiles) );
	}

	public void upsertEaAiEvTopicProfile(EaAiEv eaAiEv, Set<Integer> topicIdSet) {
		commonDao.insert(MAPPER_NAMESPACE + "upsertEaAiEvTopicProfile", Map.of("eaAiEv",eaAiEv,"topicIdSet",topicIdSet) );
	}

}