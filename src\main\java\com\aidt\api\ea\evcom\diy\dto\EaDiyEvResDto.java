package com.aidt.api.ea.evcom.diy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 08:57:오전 8:57
 * @modify date 2024-03-29 08:57:오전 8:57
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class EaDiyEvResDto {

    @Parameter(name="평가 ID")
    private long evId;

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="평가명")
    private String evNm;

    @Parameter(name="문제 수")
    private int qstCnt;

    @Parameter(name="잠금 여부")
    private String lcknYn;

    @Parameter(name="재응시 허용 여부")
    private String rtxmPmsnYn;

    @Parameter(name="생성 일시")
    private String crtDtm;

    @Parameter(name="평가 일시")
    private String smtDtm;

    @Parameter(name="문제 버튼 명")
    private String evBtnNm;

    @Parameter(name="평가 시작 여부")
    private String txmStrYn;
    
    @Parameter(name="평가 완료 여부")
    private String evCmplYn;

    @Parameter(name="평가 완료 명")
    private String evCmplNm;    

    @Parameter(name="응시 회차")
    private long txmPn;
    

	private int totalCnt; 
}
