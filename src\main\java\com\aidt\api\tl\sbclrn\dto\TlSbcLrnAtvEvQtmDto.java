package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-12 16:20:19
 * @modify date 2024-04-12 16:20:19
 * @desc TlSbcLrnAtvEvQtmDto 교과학습 활동상태 평가문항정보
 */
@Data
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvEvQtmDto {
    /** 평가ID */
    @Parameter(name="평가ID")
    private String evId;
    /** 문항ID */
    @Parameter(name="문항ID")
    private String qtmId;
    /** 문항번호 */
    @Parameter(name="문항번호")
    private String qtmOrdn;
    /** 사용자ID */
    @Parameter(name="사용자ID")
    private String usrId;
    /** 정답여부 */
    @Parameter(name="정답여부")
    private String cansYn;
}
