<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.tl.lrnwif.stu">
    <!-- 단원목차정보조회 -->
    <select id="selectLrnTocAtvInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocAtvDto">
        WITH RECURSIVE RCS AS
        (
            SELECT
                   T1.OPT_TXB_ID
                  ,T1.LRMP_NOD_ID
                  ,T1.URNK_LRMP_NOD_ID
                  ,T1.LRMP_NOD_NM
                  ,T1.LCKN_YN
                  ,T1.RCSTN_ORDN
                  ,T1.WKB_STR_PGE_NO
                  ,T1.WKB_END_PGE_NO
                  ,T1.TXB_STR_PGE_NO
                  ,T1.TXB_END_PGE_NO
                  ,T1.DPTH
                  ,T1.LRMP_NOD_ID         AS LRMP_NOD_ID1
                  ,CAST('' AS CHAR(30))   AS LRMP_NOD_ID2
                  ,CAST('' AS CHAR(30))   AS LRMP_NOD_ID3
                  ,CAST('' AS CHAR(30))   AS LRMP_NOD_ID4
                  ,T1.LRMP_NOD_NM         AS LRMP_NOD_NM1
                  ,CAST('' AS CHAR(300))  AS LRMP_NOD_NM2
                  ,CAST('' AS CHAR(300))  AS LRMP_NOD_NM3
                  ,CAST('' AS CHAR(300))  AS LRMP_NOD_NM4
                  ,@NODNUM := @NODNUM + 1 AS NOD_NUM
            FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN T1 /* TL_교과학습노드재구성 */
                 JOIN (SELECT @NODNUM := 0) R
            WHERE T1.URNK_LRMP_NOD_ID IS NULL
            AND T1.OPT_TXB_ID = #{param.optTxbId}
            UNION ALL
            SELECT
                   T1.OPT_TXB_ID
                  ,T1.LRMP_NOD_ID
                  ,T1.URNK_LRMP_NOD_ID
                  ,T1.LRMP_NOD_NM
                  ,T1.LCKN_YN
                  ,T1.RCSTN_ORDN
                  ,T1.WKB_STR_PGE_NO
                  ,T1.WKB_END_PGE_NO
                  ,T1.TXB_STR_PGE_NO
                  ,T1.TXB_END_PGE_NO
                  ,T1.DPTH
                  ,T2.LRMP_NOD_ID1
                  ,CONCAT(IF(T2.DPTH = 1, T1.LRMP_NOD_ID, T2.LRMP_NOD_ID2))             AS LRMP_NOD_ID2
                  ,CONCAT(IF(T2.DPTH = 2, T1.LRMP_NOD_ID, T2.LRMP_NOD_ID3))             AS LRMP_NOD_ID3
                  ,CONCAT(IF(T2.DPTH <![CDATA[<]]> 4, T1.LRMP_NOD_ID, T2.LRMP_NOD_ID4)) AS LRMP_NOD_ID4
                  ,T2.LRMP_NOD_NM1
                  ,CONCAT(IF(T2.DPTH = 1, T1.LRMP_NOD_NM, T2.LRMP_NOD_NM2))             AS LRMP_NOD_NM2
                  ,CONCAT(IF(T2.DPTH = 2, T1.LRMP_NOD_NM, T2.LRMP_NOD_NM3))             AS LRMP_NOD_NM3
                  ,CONCAT(IF(T2.DPTH <![CDATA[<]]> 4, T1.LRMP_NOD_NM, T2.LRMP_NOD_NM4)) AS LRMP_NOD_NM4
                  ,T2.NOD_NUM
            FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN T1 /* TL_교과학습노드재구성 */
                 INNER JOIN RCS T2
                       ON T1.OPT_TXB_ID = T2.OPT_TXB_ID
                       AND T1.URNK_LRMP_NOD_ID = T2.LRMP_NOD_ID
            WHERE T1.OPT_TXB_ID = #{param.optTxbId}
        )
        SELECT
               R.OPT_TXB_ID                                /* 운영교과서ID */
              ,CAST(R.NOD_NUM AS CHAR(3))    AS LLU_NOD_NO /* 대단원No */
              ,R.LRMP_NOD_ID1                AS LLU_NOD_ID /* 대단원ID */
              ,R.LRMP_NOD_ID2                AS MLU_NOD_ID /* 중단원ID */
              ,R.LRMP_NOD_ID3                AS SLU_NOD_ID /* 소단원ID */
              ,R.LRMP_NOD_ID4                AS TC_NOD_ID  /* 차시ID */
              ,R.LRMP_NOD_NM1                AS LLU_NOD_NM /* 대단원명 */
              ,R.LRMP_NOD_NM2                AS MLU_NOD_NM /* 중단원명 */
              ,R.LRMP_NOD_NM3                AS SLU_NOD_NM /* 소단원명 */
              ,R.LRMP_NOD_NM4                AS TC_NOD_NM  /* 차시단원명 */
              ,IFNULL(T.LLU_ESP_YN, '')      AS LLU_ESP_YN /* 대단원노출여부 */
              ,IFNULL(T.MLU_ESP_YN, '')      AS MLU_ESP_YN /* 중단원노출여부 */
              ,IFNULL(T.SLU_ESP_YN, '')      AS SLU_ESP_YN /* 소단원노출여부 */
              ,IFNULL(T.TC_ESP_YN, '')       AS TC_ESP_YN /* 차시단원노출여부 */
              ,IFNULL(R.LCKN_YN, '')         AS LCKN_YN /* 차시잠금 */
              ,IFNULL(IF(R.TXB_STR_PGE_NO = 0, '', R.TXB_STR_PGE_NO), '')  AS TXB_STR_PGE_NO /* 교과서시작PAGE */
              ,IFNULL(IF(R.TXB_END_PGE_NO = 0, '', R.TXB_END_PGE_NO), '')  AS TXB_END_PGE_NO /* 교과서종료PAGE */
              ,IFNULL(IF(R.WKB_STR_PGE_NO = 0, '', R.WKB_STR_PGE_NO), '')  AS WKB_STR_PGE_NO /* 익힘책시작PAGE */
              ,IFNULL(IF(R.WKB_END_PGE_NO = 0, '', R.WKB_END_PGE_NO), '')  AS WKB_END_PGE_NO /* 익힘책종료PAGE */
        FROM RCS R /* 교과서 단원정보 */
             INNER JOIN (
                    SELECT A1.TXB_ID
                        ,IF(0 <![CDATA[<]]> INSTR(A2.EPS_LU_DPTH_LST_CN, 'D1'), 'Y', 'N') AS LLU_ESP_YN /* 대단원노출여부 */
                        ,IF(0 <![CDATA[<]]> INSTR(A2.EPS_LU_DPTH_LST_CN, 'D2'), 'Y', 'N') AS MLU_ESP_YN /* 중단원노출여부 */
                        ,IF(0 <![CDATA[<]]> INSTR(A2.EPS_LU_DPTH_LST_CN, 'D3'), 'Y', 'N') AS SLU_ESP_YN /* 소단원노출여부 */
                        ,IF(0 <![CDATA[<]]> INSTR(A2.EPS_LU_DPTH_LST_CN, 'D4'), 'Y', 'N') AS TC_ESP_YN /* 차시단원노출여부 */
                    FROM LMS_LRM.CM_OPT_TXB A1 /* CM_운영교과서 */
                        INNER JOIN LMS_CMS.BC_TXB  A2 /* BC_교과서 */
                        ON A1.TXB_ID = A2.TXB_ID
                        AND A2.USE_YN = 'Y'
                        AND A2.DEL_YN = 'N'
                    WHERE A1.OPT_TXB_ID = #{param.optTxbId}
             ) T  /* 교과서정보 */
                  ON 1=1
             LEFT JOIN LMS_LRM.CM_OPT_TXB T2 /* CM_운영교과서 */
                  ON R.OPT_TXB_ID = T2.OPT_TXB_ID

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnTocAtvInfo */
    </select>
    
    <select id="selectAtvCnt" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocAtvDto">
        select COUNT(CASE WHEN ATV.LRN_STP_DV_CD = 'CL' THEN 1 END) as atv_cl_cnt, 
				COUNT(CASE WHEN ATV.LRN_STP_DV_CD = 'WB' THEN 1 END) as atv_wb_cnt
				FROM
				(SELECT XA.LRN_ATV_ID , XB.LRN_STP_DV_CD
                FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN XA /* TL_교과학습활동재구성 */
                       INNER JOIN LMS_CMS.BC_LRN_STP XB  /* BC_학습단계 */
                       ON XA.LRMP_NOD_ID = XB.LRMP_NOD_ID
                       AND XA.LRN_STP_ID = XB.LRN_STP_ID
                WHERE XA.OPT_TXB_ID = #{param.optTxbId}
                AND XA.LRMP_NOD_ID = #{param.lrmpNodId}
				<if test = 'param.oneClkYn != "Y"'>
					AND XA.USE_YN = 'Y'
				</if>
                AND XB.LRN_STP_DV_CD in ('CL', 'WB')
                AND XB.DEL_YN = 'N'

                UNION ALL

                SELECT M.TCR_REG_CTN_ID AS LRN_ATV_ID , S.LRN_STP_DV_CD
				FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
				INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
					ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
				INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
					ON M.OPT_TXB_ID = R.OPT_TXB_ID
					AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
				INNER JOIN LMS_CMS.bc_lrn_stp S
					ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
					AND M.LRN_STP_ID = S.LRN_STP_ID
					AND S.DEL_YN = 'N'
				WHERE M.OPT_TXB_ID = #{param.optTxbId}
				AND M.LRMP_NOD_ID = #{param.lrmpNodId}
				AND M.DEL_YN = 'N'
				<if test = 'param.oneClkYn != "Y"'>
					AND M.USE_YN = 'Y'
				</if>)ATV

        /* 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectAtvCnt */
    </select>

    <!-- 학습도구 목록조회 -->
    <select id="selectLrnTlList" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwLrnTlDto">
        SELECT
               A.TXB_ID                                               /* 교과서ID*/
              ,C.LRN_TL_ID                                            /* 학습도구ID */
              ,C.LRN_TL_CTM_ALS_NM                 AS LRN_TL_KN_NM    /* 학습도구 별명 */
              ,D.LRN_TL_KN_CD                                         /* 학습도구종류코드 */
              ,C1.CM_CD_NM                         AS LRN_TL_TP_NM    /* 학습도구유형 */
              ,D.LRN_TL_TP_CD                                         /* 학습도구유형코드 WT=필기도구, TT=교과도구, MT=소통도구, CT=수업도구 */
        FROM LMS_LRM.CM_OPT_TXB A /* CM_운영교과서 */
             LEFT JOIN LMS_CMS.BC_TXB_LRNW B /* BC_교과서학습창 */
                ON A.TXB_ID = B.TXB_ID
             LEFT JOIN LMS_CMS.BC_TXB_LRN_TL_MPN C /* BC_교과서학습도구매핑 */
                 ON B.LRNW_ID =C.LRNW_ID
             LEFT JOIN LMS_CMS.BC_LRN_TL D /* BC_학습도구 */
                ON C.LRN_TL_ID = D.LRN_TL_ID
            LEFT JOIN LMS_LRM.CM_CM_CD C1  /* CM_공통코드 */
               ON C1.URNK_CM_CD = 'LRN_TL_TP_CD'
                AND CURDATE() BETWEEN C1.VALD_STR_DT AND C1.VALD_END_DT
                AND C1.LMS_USE_YN = 'Y'
                AND C1.DEL_YN = 'N'
                AND C1.CM_CD = D.LRN_TL_TP_CD
             LEFT JOIN LMS_LRM.CM_CM_CD C2 /* CM_공통코드 */
               ON C1.URNK_CM_CD = C2.CM_CD
                AND CURDATE() BETWEEN C2.VALD_STR_DT AND C2.VALD_END_DT
                AND C2.LMS_USE_YN = 'Y'
                AND C2.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        AND B.LRN_TP_CD = 'TL'  /* TL=교과학습 */
        <if test='usrDvCd == "T"'>
            AND B.LRNW_USR_TP_CD = 'TE'
        </if>
        <if test='usrDvCd != "T"'>
            AND B.LRNW_USR_TP_CD = 'ST'
        </if>
        AND C.LRN_TL_ID IS NOT NULL

        ORDER BY C1.SRT_ORDN ASC

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnTlList */
    </select>
    <!--학습단계/학습활동/학습콘텐츠 목록조회 -->
    <select id="selectLrnAtvCtnMtdList" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="HashMap">
        SELECT * FROM
        (SELECT A.LRMP_NOD_ID
        	  ,A.LRN_STP_ID /* 학습단계ID */
              ,B.LRN_STP_DV_CD /* 학습단계구분코드 */
              ,IFNULL(B.LRN_STP_CD, B.LRN_STP_ID) AS LRN_STP_CD  /* 학습단계코드 */
              ,B.LRN_STP_NM /* 학습단계명 */
              ,B.SRT_ORDN /* 학습단계 표시순서 */
              ,A.LRN_ATV_ID /* 학습활동ID */
              ,A.LRN_ATV_NM /* 학습활동명 */
              ,A.CLS_BRD_URL /* 클레스보드URL */
              ,IFNULL(C.LRN_ST_CD, 'NL')         AS LRN_ST_CD /* 학습상태코드*/
              ,C.LRN_TM_SCNT /* 학습시간초수*/
              ,CAST(A.RCSTN_ORDN AS CHAR(20))    AS RCSTN_ORDN /* 학습활동 재구성 순서 */
              ,IFNULL(A.EV_ID, '')               AS EV_ID /* 평가 */
              ,D.CTN_META_DATA_ID /* 메타데이터ID */
              ,D.CDN_PTH_NM                      AS CDN_PTH_NM /* 콘텐츠메타데이터-CDN */
              ,D.STR_FLE_NM  /* 시작파일명 */
              ,IFNULL(D.CTN_TP_CD, A.CTN_TP_CD)  AS CTN_TP_CD /* 텐츠메타데이터-콘텐츠유형코드 */
              ,D.CTN_CD                          AS CTN_CD /* 콘텐츠메타데이터-텐츠ID */
              ,D.LRN_RCNT_TM_SCNT                AS LRN_COFM_TM_SCNT /* 콘텐츠메타데이터-학습인정시간 */
              ,D.CTN_DFFD_DV_CD                     /* 콘텐츠메타데이터-난이도 */
              ,D.LRN_ATV_TTL_QTM_CNT             AS ATV_QTM_CNT /* 콘텐츠메타데이터-액티비티총문항수*/
              ,D.VDO_FLE_NM_1280P                /* 동영상파일명1280P */
              ,D.VDO_FLE_NM_720P                 /* 동영상파일명720P */
              ,D.VDO_FLE_NM_480P                 /* 동영상파일명480P */
              ,E.USE_YN 						AS EV_USE_YN
              ,E.DEL_YN 						AS EV_DEL_YN
              ,E.LCKN_YN 						AS EV_LCKN_YN
              ,E.TXM_PTME_SETM_YN 				AS EV_TXM_PTME_SETM_YN
              ,E.TXM_STR_DTM 					AS EV_TXM_STR_DTM
              ,E.TXM_END_DTM 					AS EV_TXM_END_DTM
              ,E.RTXM_PMSN_YN 					AS EV_RTXM_PMSN_YN
              ,F.CM_CD_NM 						AS EV_DTL_DV_NM
              ,A.TCR_CTN_YN
        FROM (
		    SELECT 
		    	R.OPT_TXB_ID,
		        R.LRMP_NOD_ID AS LRMP_NOD_ID,
				R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
		        R.LRN_ATV_ID,
		        R.LRN_STP_ID,
		        R.CTN_CD,
		        R.LRN_ATV_NM,
		        R.CTN_TP_CD,
		        R.USE_YN,
		        R.CLS_BRD_URL,
		        R.ORGL_LRN_STP_ID,
		        R.ORGL_ORDN,
		        R.RCSTN_ORDN,
		        R.EV_ID,
		        R.CRTR_ID,
		        R.CRT_DTM,
		        R.MDFR_ID,
		        R.MDF_DTM,
		        R.DB_ID,
		        'N' AS TCR_CTN_YN
		    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
		    WHERE R.OPT_TXB_ID = #{param.optTxbId}
		      AND R.LRMP_NOD_ID = #{param.lrmpNodId}
		      
			UNION ALL
			
		    SELECT 
		        R.OPT_TXB_ID,
		        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
				R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
		        R.LRN_ATV_ID,
		        TTRCM.LRN_STP_ID,
		        R.CTN_CD,
		        R.LRN_ATV_NM,
		        R.CTN_TP_CD,
		        TTRCM.USE_YN,
		        R.CLS_BRD_URL,
		        R.ORGL_LRN_STP_ID,
		        R.ORGL_ORDN,
		        TTRCM.RCSTN_ORDN,
		        R.EV_ID,
		        R.CRTR_ID,
		        R.CRT_DTM,
		        R.MDFR_ID,
		        R.MDF_DTM,
		        R.DB_ID,
		        'Y' AS TCR_CTN_YN
		    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
		    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
		        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
		        and ttrcm.del_yn = 'N'
		    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
		        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
		    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
		      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
		      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
		      AND TTRC.LRN_ATV_ID IS NOT NULL
			) A /* TL_교과학습활동재구성 */
		INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
        	ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
            AND A.LRN_STP_ID = B.LRN_STP_ID
		LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C /* TL_교과학습활동상태 */
			ON A.OPT_TXB_ID = C.OPT_TXB_ID
			AND A.ORGL_LRMP_NOD_ID = C.LRMP_NOD_ID
			AND A.LRN_ATV_ID = C.LRN_ATV_ID
			AND C.LRN_USR_ID = #{param.usrId}
		LEFT JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
			ON A.LRN_ATV_ID = D.LRN_ATV_ID
			AND D.USE_YN= 'Y'
			AND D.DEL_YN= 'N'
		LEFT JOIN LMS_LRM.EA_EV E		/* EA_평가 */
			ON A.EV_ID = E.EV_ID
		LEFT JOIN LMS_LRM.CM_CM_CD F
			ON E.EV_DTL_DV_CD  = F.CM_CD 
			AND F.URNK_CM_CD  = 'EV_DTL_DV_CD'
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        <if test = 'param.oneClkYn != "Y"'>
			AND A.USE_YN = 'Y'
		</if>
        AND B.LRN_STP_DV_CD IN ('CL', 'EX', 'WB') /* 학습단계구분코드=개념학습, 평가 */
        AND B.DEL_YN = 'N'
        AND A.LRMP_NOD_ID = #{param.lrmpNodId}
        <if test='param.lrnAtvId != null and param.lrnAtvId != ""'>
        AND A.LRN_ATV_ID = #{param.lrnAtvId}
        </if>
        
        UNION ALL
 
 			SELECT M.LRMP_NOD_ID
 			  ,M.LRN_STP_ID /* 학습단계ID */
              ,S.LRN_STP_DV_CD /* 학습단계구분코드 */
              ,IFNULL(S.LRN_STP_CD, S.LRN_STP_ID) AS LRN_STP_CD  /* 학습단계코드 */
              ,S.LRN_STP_NM /* 학습단계명 */
              ,S.SRT_ORDN /* 학습단계 표시순서 */
              ,M.TCR_REG_CTN_ID AS LRN_ATV_ID /* 학습활동ID */
              ,C.TCR_REG_CTN_NM AS LRN_ATV_NM /* 학습활동명 */
              ,'' CLS_BRD_URL /* 클레스보드URL */
              ,IFNULL(T.LRN_ST_CD, 'NL')         AS LRN_ST_CD /* 학습상태코드*/
              ,T.LRN_TM_SCNT /* 학습시간초수*/
              ,CAST(M.RCSTN_ORDN AS CHAR(20))    AS RCSTN_ORDN /* 학습활동 재구성 순서 */
              ,''             AS EV_ID /* 평가 */
              ,F.ANNX_FLE_ID AS CTN_META_DATA_ID /* 메타데이터ID */
              ,IF(C.TP_CD = 'BW', IFNULL(F.ANNX_FLE_PTH_NM,''), F.ANNX_FLE_PTH_NM)                      AS CDN_PTH_NM /* 콘텐츠메타데이터-CDN */
              ,IFNULL(F.DOC_VI_ID,'')							 AS STR_FLE_NM  /* 시작파일명 */
              ,C.TP_CD  AS CTN_TP_CD /* 텐츠메타데이터-콘텐츠유형코드 */
              ,''                          AS CTN_CD /* 콘텐츠메타데이터-텐츠ID */
              ,C.LRN_CMPL_BS		AS LRN_COFM_TM_SCNT /* 콘텐츠메타데이터-학습인정시간 */
              ,'' 					AS CTN_DFFD_DV_CD                     /* 콘텐츠메타데이터-난이도 */
              ,''						AS ATV_QTM_CNT /* 콘텐츠메타데이터-액티비티총문항수*/
              ,''						AS VDO_FLE_NM_1280P                /* 동영상파일명1280P */
              ,''						AS VDO_FLE_NM_720P                 /* 동영상파일명720P */
              ,''						AS VDO_FLE_NM_480P                 /* 동영상파일명480P */
              ,''						AS EV_USE_YN
              ,'' 						AS EV_DEL_YN
              ,'' 						AS EV_LCKN_YN
              ,'' 				AS EV_TXM_PTME_SETM_YN
              ,''					AS EV_TXM_STR_DTM
              ,''					AS EV_TXM_END_DTM
              ,''					AS EV_RTXM_PMSN_YN
              ,''						AS EV_DTL_DV_NM
              ,'Y' AS TCR_CTN_YN
 			FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
				INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
					ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
					AND C.TP_CD <![CDATA[<>]]> 'AT'
				INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
					ON M.OPT_TXB_ID = R.OPT_TXB_ID
					AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
				INNER JOIN LMS_CMS.bc_lrn_stp S
					ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
					AND M.LRN_STP_ID = S.LRN_STP_ID
					AND S.DEL_YN = 'N'
				LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
					ON M.OPT_TXB_ID = T.OPT_TXB_ID
					AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
					AND T.LRN_USR_ID = #{param.usrId}
				LEFT JOIN LMS_LRM.cm_annx_fle F
					ON F.ANNX_ID = C.ANNX_ID
					AND F.USE_YN = 'Y'
			WHERE M.OPT_TXB_ID = #{param.optTxbId}
			AND M.LRMP_NOD_ID = #{param.lrmpNodId}
			<if test='param.lrnAtvId != null and param.lrnAtvId != ""'>
        	AND M.TCR_REG_CTN_ID = #{param.lrnAtvId}
        	</if>
			AND M.DEL_YN = 'N'
			<if test = 'param.oneClkYn != "Y"'>
				AND M.USE_YN = 'Y'
			</if>) ATV
        
        ORDER BY ATV.LRN_STP_DV_CD ASC, ATV.SRT_ORDN ASC, ATV.LRN_STP_ID ASC, ATV.RCSTN_ORDN ASC, ATV.LRN_ATV_ID ASC

        /** 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnAtvCtnMtdList */

    </select>

    <!--차시단위-학습콘텐츠 교육과정표준체계 목록조회 -->
    <select id="selectNtnlCrclStdList" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="String">
        SELECT --  A.LRN_ATV_ID /* 학습활동ID */
               -- ,D.CTN_META_DATA_ID /* 메타데이터ID */
               -- ,IFNULL(F.EDU_CRS_CN_CD, '') AS EDU_CRS_CN_CD /* 교육과정콘텐츠표준ID */
               DISTINCT IFNULL(E.CRCL_CTN_ELM2_CD, '') AS EDU_CRS_CN_CD /* 교육과정콘텐츠표준ID */
        FROM (
			    SELECT 
			    	R.OPT_TXB_ID,
			        R.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        R.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        R.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'N' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
			    WHERE R.OPT_TXB_ID = #{param.optTxbId}
			      AND R.LRMP_NOD_ID = #{param.lrmpNodId}
			      
				UNION ALL
				
			    SELECT 
			        R.OPT_TXB_ID,
			        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        TTRCM.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        TTRCM.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'Y' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
			        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
			        and ttrcm.del_yn = 'N'
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
			        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
			    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
			      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
			      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
			      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
              INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                    ON A.ORGL_LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.DEL_YN = 'N'
              INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                   ON A.LRN_ATV_ID = D.LRN_ATV_ID
                   AND D.USE_YN= 'Y'
                   AND D.DEL_YN= 'N'
              INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 E /* BC_국가수준교육과정컨텐츠매핑 */
                   ON E.CTN_ID = D.LRN_ATV_ID
                   AND E.CRCL_CTN_TP_CD = 'TL'  -- 교과
                   AND E.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        AND A.USE_YN  = 'Y'
        <choose>
            <when test = 'param.lrnStpDvCd == "WB"'>
                AND B.LRN_STP_DV_CD = 'WB' /* 학습단계구분코드=익힘책 */
            </when>
            <otherwise>
                AND B.LRN_STP_DV_CD IN ('CL', 'EX') /* 학습단계구분코드=개념학습, 평가 */
            </otherwise>
        </choose>
        AND B.DEL_YN = 'N'
        AND A.LRMP_NOD_ID = #{param.lrmpNodId}
        -- GROUP BY A.LRN_ATV_ID, D.CTN_META_DATA_ID

        /** 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectNtnlCrclStdList */
    </select>

    <!--학습활동-학습콘텐츠 교육과정표준체계 목록조회 -->
    <select id="selectNtnlCrclStdList2" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="Map">
        SELECT A.LRN_ATV_ID /* 학습활동ID */
              ,D.CTN_META_DATA_ID /* 메타데이터ID */
              ,MAX(IFNULL(E.CRCL_CTN_ELM2_CD, '')) AS EDU_CRS_CN_CD /* 교육과정콘텐츠표준ID */
        FROM ( SELECT 
			    	R.OPT_TXB_ID,
			        R.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        R.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        R.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'N' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
			    WHERE R.OPT_TXB_ID = #{param.optTxbId}
			      AND R.LRMP_NOD_ID = #{param.lrmpNodId}
			      
				UNION ALL
				
			    SELECT 
			        R.OPT_TXB_ID,
			        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
					R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
			        R.LRN_ATV_ID,
			        R.LRN_STP_ID,
			        R.CTN_CD,
			        R.LRN_ATV_NM,
			        R.CTN_TP_CD,
			        TTRCM.USE_YN,
			        R.CLS_BRD_URL,
			        R.ORGL_LRN_STP_ID,
			        R.ORGL_ORDN,
			        TTRCM.RCSTN_ORDN,
			        R.EV_ID,
			        R.CRTR_ID,
			        R.CRT_DTM,
			        R.MDFR_ID,
			        R.MDF_DTM,
			        R.DB_ID,
			        'Y' AS TCR_CTN_YN
			    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
			        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
			        and ttrcm.del_yn = 'N'
			    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
			        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
			    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
			      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
			      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
			      AND TTRC.LRN_ATV_ID IS NOT NULL) A /* TL_교과학습활동재구성 */
              INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                    ON A.ORGL_LRMP_NOD_ID = B.LRMP_NOD_ID
                    AND A.LRN_STP_ID = B.LRN_STP_ID
                    AND B.DEL_YN = 'N'
              INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                   ON A.LRN_ATV_ID = D.LRN_ATV_ID
                   AND D.USE_YN= 'Y'
                   AND D.DEL_YN= 'N'
              INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 E /* BC_국가수준교육과정컨텐츠매핑 */
                   ON E.CTN_ID = D.LRN_ATV_ID
                   AND E.CRCL_CTN_TP_CD = 'TL'  -- 교과
                   AND E.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        AND A.USE_YN  = 'Y'
        <choose>
            <when test = 'param.lrnStpDvCd == "WB"'>
                AND B.LRN_STP_DV_CD = 'WB' /* 학습단계구분코드=익힘책 */
            </when>
            <otherwise>
                AND B.LRN_STP_DV_CD IN ('CL', 'EX') /* 학습단계구분코드=개념학습, 평가 */
            </otherwise>
        </choose>
        AND B.DEL_YN = 'N'
        AND A.LRMP_NOD_ID = #{param.lrmpNodId}
        GROUP BY A.LRN_ATV_ID, D.CTN_META_DATA_ID

        /** 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectNtnlCrclStdList2 */
    </select>

    <!--학습활동-클래스보드목록조회 -->
    <select id="selectClabdList" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="HashMap">
        SELECT
             A.LRMP_NOD_ID /* 학습맵노드ID */
            ,A.LRN_ATV_ID /* 학습활동ID */
            ,A.CLABD_LRGS_ID /* 클래스보드대제목ID */
            ,A.CLABD_SML_ID /* 클래스보드소제목ID */
            ,A.CLABD_LRGS_NM /* 클래스보드대제목명 */
            ,A.CLABD_SML_NM /* 클래스보드소제목명 */
            ,A.CLABD_TYP /* 클래스보드형태 */
            ,A.CLABD_URL /* 클래스보드URL */
        FROM LMS_LRM.TL_SBC_LRN_ATV_CLABD A /* TL_교과학습활동클래스보드 */
        WHERE A.OPT_TXB_ID = #{param.optTxbId}
        AND A.LRMP_NOD_ID in (select * from (SELECT
												R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID
										    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
										    WHERE R.OPT_TXB_ID = #{param.optTxbId}
										      AND R.LRMP_NOD_ID = #{param.lrmpNodId}
										      
											UNION ALL
											
										    SELECT 
												R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID
										    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
										    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
										        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
										        and ttrcm.del_yn = 'N'
										    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
										        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
										    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
										      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
										      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
										      AND TTRC.LRN_ATV_ID IS NOT null) atv group by orgl_lrmp_nod_id)
        ORDER BY A.CLABD_ORDN ASC, A.CLABD_SML_ID ASC;

        /** 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectClabdList */
    </select>
    <!--마지막학습활동ID 목록조회 -->
    <select id="selectLrnAtvLst" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="HashMap">
		SELECT 
		    ATV.LRN_ATV_ID,
		    MAX(ATV.LAST_YN) AS LAST_YN,
		    MAX(ATV.LRN_ST_CD) AS LRN_ST_CD,
		    MAX(ATV.LRN_STP_DV_CD) AS LRN_STP_DV_CD,
		    MAX(ATV.SRT_ORDN) AS SRT_ORDN,
		    MAX(ATV.RCSTN_ORDN) AS RCSTN_ORDN
		FROM (
		    SELECT 
		        A.LRN_ATV_ID,
		        C.LRN_USR_ID,
		        IF(A.LRN_ATV_ID = D.LRN_ATV_ID, 'Y', 'N') AS LAST_YN,
		        IFNULL(C.LRN_ST_CD, 'NL') AS LRN_ST_CD,
		        B.LRN_STP_DV_CD,
		        B.SRT_ORDN,
		        A.RCSTN_ORDN
		    FROM (
		    SELECT 
		    	R.OPT_TXB_ID,
		        R.LRMP_NOD_ID AS LRMP_NOD_ID,
				R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
		        R.LRN_ATV_ID,
		        R.LRN_STP_ID,
		        R.CTN_CD,
		        R.LRN_ATV_NM,
		        R.CTN_TP_CD,
		        R.USE_YN,
		        R.CLS_BRD_URL,
		        R.ORGL_LRN_STP_ID,
		        R.ORGL_ORDN,
		        R.RCSTN_ORDN,
		        R.EV_ID,
		        R.CRTR_ID,
		        R.CRT_DTM,
		        R.MDFR_ID,
		        R.MDF_DTM,
		        R.DB_ID
		    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R 
		    WHERE R.OPT_TXB_ID = #{param.optTxbId}
		      AND R.LRMP_NOD_ID = #{param.lrmpNodId}
		      
			UNION ALL
			
		    SELECT 
		        R.OPT_TXB_ID,
		        TTRCM.LRMP_NOD_ID AS LRMP_NOD_ID,
				R.LRMP_NOD_ID AS ORGL_LRMP_NOD_ID,
		        R.LRN_ATV_ID,
		        TTRCM.LRN_STP_ID,
		        R.CTN_CD,
		        R.LRN_ATV_NM,
		        R.CTN_TP_CD,
		        TTRCM.USE_YN,
		        R.CLS_BRD_URL,
		        R.ORGL_LRN_STP_ID,
		        R.ORGL_ORDN,
		        TTRCM.RCSTN_ORDN,
		        R.EV_ID,
		        R.CRTR_ID,
		        R.CRT_DTM,
		        R.MDFR_ID,
		        R.MDF_DTM,
		        R.DB_ID
		    FROM LMS_LRM.tl_sbc_lrn_atv_rcstn R
		    INNER JOIN LMS_LRM.tl_tcr_reg_ctn_mpn TTRCM 
		        ON R.OPT_TXB_ID = TTRCM.OPT_TXB_ID
		        and ttrcm.del_yn = 'N'
		    INNER JOIN LMS_LRM.tl_tcr_reg_ctn TTRC 
		        ON TTRCM.TCR_REG_CTN_ID = TTRC.TCR_REG_CTN_ID 
		    WHERE TTRCM.OPT_TXB_ID = #{param.optTxbId}
		      AND TTRCM.LRMP_NOD_ID = #{param.lrmpNodId}
		      AND R.LRN_ATV_ID = TTRC.LRN_ATV_ID
		      AND TTRC.LRN_ATV_ID IS NOT NULL
			) A /* TL_교과학습활동재구성 */
		    JOIN LMS_CMS.BC_LRN_STP B 
		        ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
		       AND A.LRN_STP_ID = B.LRN_STP_ID
		       AND B.DEL_YN = 'N'
		    LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C
		        ON A.OPT_TXB_ID = C.OPT_TXB_ID
		       AND A.ORGL_LRMP_NOD_ID = C.LRMP_NOD_ID
		       AND A.LRN_ATV_ID = C.LRN_ATV_ID
		       AND C.LRN_USR_ID = #{param.usrId}
		    LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D
		        ON A.OPT_TXB_ID = D.OPT_TXB_ID
		       AND A.ORGL_LRMP_NOD_ID = D.LRMP_NOD_ID
		       AND D.LRN_USR_ID = C.LRN_USR_ID
		       AND D.MDF_DTM = (
		            SELECT MAX(X.MDF_DTM)
		            FROM (
		                SELECT MDF_DTM
		                FROM LMS_LRM.TL_SBC_LRN_ATV_ST
		                WHERE OPT_TXB_ID = A.OPT_TXB_ID
		                  AND LRMP_NOD_ID = A.LRMP_NOD_ID
		                  AND LRN_USR_ID = C.LRN_USR_ID
		                UNION ALL
		                SELECT CS.MDF_DTM
		                FROM LMS_LRM.TL_TCR_REG_CTN_MPN CM
		                JOIN LMS_LRM.TL_TCR_REG_CTN_ST CS
		                  ON CM.TCR_REG_CTN_ID = CS.TCR_REG_CTN_ID
		                 AND CM.OPT_TXB_ID = CS.OPT_TXB_ID
		                 AND CM.USE_YN = 'Y' AND CM.DEL_YN = 'N'
		                WHERE CM.LRMP_NOD_ID = A.LRMP_NOD_ID
		                  AND CM.OPT_TXB_ID = A.OPT_TXB_ID
		                  AND CS.LRN_USR_ID = C.LRN_USR_ID
		            ) X
		       )
		    WHERE A.OPT_TXB_ID = #{param.optTxbId}
		      AND A.LRMP_NOD_ID = #{param.lrmpNodId}
		      AND A.USE_YN = 'Y'
		      <choose>
	            <when test = 'param.lrnStpDvCd == "WB"'>
	                AND B.LRN_STP_DV_CD = 'WB' /* 학습단계구분코드=익힘책 */
	            </when>
	            <otherwise>
	                AND B.LRN_STP_DV_CD IN ('CL') /* 학습단계구분코드=개념학습, 평가 */
	            </otherwise>
	         </choose>
	         
		    UNION ALL
		    
		    SELECT 
		        M.TCR_REG_CTN_ID AS LRN_ATV_ID,
		        T.LRN_USR_ID,
		        IF(M.TCR_REG_CTN_ID = T2.TCR_REG_CTN_ID, 'Y', 'N') AS LAST_YN,
		        IFNULL(T.LRN_ST_CD, 'NL') AS LRN_ST_CD,
		        S.LRN_STP_DV_CD,
		        S.SRT_ORDN,
		        M.RCSTN_ORDN
		    FROM LMS_LRM.TL_TCR_REG_CTN_MPN M
		    JOIN LMS_LRM.TL_TCR_REG_CTN C
		        ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		        AND C.TP_CD <![CDATA[<>]]> 'AT'
		    JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN R
		        ON M.OPT_TXB_ID = R.OPT_TXB_ID
		       AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
		    JOIN LMS_CMS.BC_LRN_STP S
		        ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
		       AND M.LRN_STP_ID = S.LRN_STP_ID
		       AND S.DEL_YN = 'N'
		    LEFT JOIN LMS_LRM.TL_TCR_REG_CTN_ST T
		        ON M.OPT_TXB_ID = T.OPT_TXB_ID
		       AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
		       AND T.LRN_USR_ID = #{param.usrId}
		    LEFT JOIN LMS_LRM.TL_TCR_REG_CTN_ST T2
		        ON M.OPT_TXB_ID = T2.OPT_TXB_ID
		       AND M.TCR_REG_CTN_ID = T2.TCR_REG_CTN_ID
		       AND T2.LRN_USR_ID = T.LRN_USR_ID
		       AND T2.MDF_DTM = (
		            SELECT MAX(X.MDF_DTM)
		            FROM (
		                SELECT MDF_DTM
		                FROM LMS_LRM.TL_SBC_LRN_ATV_ST
		                WHERE OPT_TXB_ID = M.OPT_TXB_ID
		                  AND LRMP_NOD_ID = M.LRMP_NOD_ID
		                  AND LRN_USR_ID = T.LRN_USR_ID
		                UNION ALL
		                SELECT CS.MDF_DTM
		                FROM LMS_LRM.TL_TCR_REG_CTN_MPN CM
		                JOIN LMS_LRM.TL_TCR_REG_CTN_ST CS
		                  ON CM.TCR_REG_CTN_ID = CS.TCR_REG_CTN_ID
		                 AND CM.OPT_TXB_ID = CS.OPT_TXB_ID
		                 AND CM.USE_YN = 'Y' AND CM.DEL_YN = 'N'
		                WHERE CM.OPT_TXB_ID = M.OPT_TXB_ID
		                  AND CM.LRMP_NOD_ID = M.LRMP_NOD_ID
		                  AND CS.LRN_USR_ID = T.LRN_USR_ID
		            ) X
		       )
		    WHERE M.OPT_TXB_ID = #{param.optTxbId}
		      AND M.LRMP_NOD_ID = #{param.lrmpNodId}
		      AND M.USE_YN = 'Y'
		      AND M.DEL_YN = 'N'
		     <choose>
	            <when test = 'param.lrnStpDvCd == "WB"'>
	                AND S.LRN_STP_DV_CD = 'WB' /* 학습단계구분코드=익힘책 */
	            </when>
	            <otherwise>
	                AND S.LRN_STP_DV_CD IN ('CL') /* 학습단계구분코드=개념학습, 평가 */
	            </otherwise>
        	</choose>
		) ATV
		GROUP BY ATV.LRN_ATV_ID
		ORDER BY LRN_STP_DV_CD, SRT_ORDN, RCSTN_ORDN, LRN_ATV_ID;

        /* 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectLrnAtvLst */
    </select>

    <!--
        학습활동상태 건수취득
    -->
    <select id="selectLrnAtvStInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto" resultType="Map">
        SELECT 
               COUNT(1) AS CNT
              ,max(LRN_ST_CD) as LRN_ST_CD
              ,max(S.LRN_STP_DV_CD) AS LRN_STP_DV_CD
        FROM LMS_LRM.TL_SBC_LRN_ATV_ST A /* TL_교과학습활동상태 */
        INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN L
        	ON L.OPT_TXB_ID = #{optTxbId}
        	AND A.LRMP_NOD_ID = L.LRMP_NOD_ID
            AND A.LRN_ATV_ID = L.LRN_ATV_ID
        INNER JOIN LMS_CMS.bc_lrn_stp S
        	ON  L.LRMP_NOD_ID = S.LRMP_NOD_ID
            AND L.LRN_STP_ID = S.LRN_STP_ID
            AND S.DEL_YN = 'N'
        WHERE A.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND A.LRMP_NOD_ID = #{lrmpNodId}  /* 학습맵노드ID */
        AND A.LRN_ATV_ID = #{lrnAtvId}  /* 학습활동ID */
        AND A.LRN_USR_ID = #{lrnUsrId}  /* 학습사용자ID */

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnAtvStInfo */
    </select>
    
    <!--
        교사콘텐츠상태 건수취득
    -->
    <select id="selectTcrCtnStInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto" resultType="Map">
        SELECT
        	COUNT(1) AS CNT
        	,LRN_ST_CD
		FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
			ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
		INNER JOIN LMS_LRM.tl_tcr_reg_ctn_st S
			ON M.OPT_TXB_ID = S.OPT_TXB_ID
			AND S.TCR_REG_CTN_ID = M.TCR_REG_CTN_ID
			AND S.LRN_USR_ID = #{lrnUsrId}
		WHERE  M.OPT_TXB_ID = #{optTxbId}
		AND M.LRMP_NOD_ID = #{lrmpNodId}
		AND M.TCR_REG_CTN_ID = #{lrnAtvId}
		AND M.DEL_YN = 'N'

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectTcrCtnStInfo */
    </select>
    
    <!--
        원본 차시 id 조회
    -->
    <select id="selectOrgnLrmpNodId" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto" resultType="String">
        select R.LRMP_NOD_ID from LMS_LRM.tl_sbc_lrn_atv_rcstn R where R.OPT_TXB_ID = #{optTxbId} and R.LRN_ATV_ID = #{lrnAtvId};

        /* 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectOrgnLrmpNodId */
    </select>

    <!--
        학습활동상태 정보취득(학생용)
    -->
    <select id="selectLrnAtvStpDvInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto" resultType="HashMap">
        
        SELECT MAX(IF(ATV.LRN_ATV_ID =#{lrnAtvId}, ATV.LRN_STP_DV_CD, ''))                                                        AS LRN_STP_DV_CD /* 학습단계구분 */
      	,IFNULL(IF(SUM(IF(ATV.LRN_STP_DV_CD = 'CL', 1, 0)) = SUM(IF(ATV.LRN_STP_DV_CD = 'CL' AND ATV.LRN_ST_CD = 'CL', 1, 0)), 'Y', 'N'),'N') AS CMPL_CL_YN /* 개념학습완료여부 */
      	,IFNULL(IF(SUM(IF(ATV.LRN_STP_DV_CD = 'EX', 1, 0)) = SUM(IF(ATV.LRN_STP_DV_CD = 'EX' AND ATV.LRN_ST_CD = 'CL', 1, 0)), 'Y', 'N'),'N') AS CMPL_EX_YN /* 평가완료여부 */
      	,IFNULL(IF(SUM(IF(ATV.LRN_STP_DV_CD = 'WB', 1, 0)) = SUM(IF(ATV.LRN_STP_DV_CD = 'WB' AND ATV.LRN_ST_CD = 'CL', 1, 0)), 'Y', 'N'),'N') AS CMPL_WB_YN /* 익힘완료책여부 */
		FROM  (SELECT A.LRN_ATV_ID
        			,B.LRN_STP_DV_CD
        			,C.LRN_ST_CD
        			,A.LRMP_NOD_ID
		  		FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
            		INNER JOIN LMS_CMS.BC_LRN_STP B /* BC_학습단계 */
              		  ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
           		     AND A.LRN_STP_ID = B.LRN_STP_ID
           		     AND B.DEL_YN = 'N'
          		LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST C /* TL_교과학습활동상태 */
                	ON A.OPT_TXB_ID = C.OPT_TXB_ID
                	AND A.LRMP_NOD_ID = C.LRMP_NOD_ID
                	AND A.LRN_ATV_ID = C.LRN_ATV_ID
                	AND C.LRN_USR_ID = #{lrnUsrId}
        		WHERE A.OPT_TXB_ID = #{optTxbId}
        		AND A.LRMP_NOD_ID = #{lrmpNodId}
        		AND A.USE_YN = 'Y'
        
        UNION ALL
        
        SELECT M.TCR_REG_CTN_ID AS LRN_ATV_ID
        			,S.LRN_STP_DV_CD
        			,T.LRN_ST_CD
        			,M.LRMP_NOD_ID
        FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
				ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
			INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
				ON M.OPT_TXB_ID = R.OPT_TXB_ID
				AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
			INNER JOIN LMS_CMS.bc_lrn_stp S
				ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
				AND M.LRN_STP_ID = S.LRN_STP_ID
				AND S.DEL_YN = 'N'
			LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
				ON M.OPT_TXB_ID = T.OPT_TXB_ID
				AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
				AND T.LRN_USR_ID = #{lrnUsrId}
			WHERE M.OPT_TXB_ID = #{optTxbId}
			AND M.LRMP_NOD_ID = #{lrmpNodId}
			AND M.DEL_YN = 'N'
			AND M.USE_YN = 'Y') ATV

  		 GROUP BY ATV.LRMP_NOD_ID
        
        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnAtvStpDvInfo */
    </select>

    <!--
        대단원 학습활동상태 정보취득(학생용)
    -->
    <!-- 마이홈 포인트지급조건변경 대응 (24.05.31)
    <select id="selectLrnLluNodInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto" resultType="HashMap">
        SELECT M.LLU_NOD_ID        /* 대단원노드 ID*/
              ,L.LRMP_NOD_NM                                              AS LLU_NOD_NM /* 대단원노드명 */
              ,IF(COUNT(1) = SUM(IF(D.LRN_ST_CD = 'CL', 1, 0)), 'Y', 'N') AS LLU_COMPL_YN
        FROM LMS_LRM.TL_SBC_LRN_NOD_RCSTN M /* TL_교과학습노드재구성 */
            INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN L /* TL_교과학습노드재구성 */
                ON M.OPT_TXB_ID = L.OPT_TXB_ID
                AND M.LLU_NOD_ID = L.LRMP_NOD_ID
                AND L.USE_YN = 'Y'
                AND L.DPTH = 1
            INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN A /* TL_교과학습노드재구성 */
                ON M.OPT_TXB_ID = A.OPT_TXB_ID
                AND M.LLU_NOD_ID = A.LLU_NOD_ID
                AND A.USE_YN = 'Y'
                AND A.DPTH = 4
            INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN B /* TL_교과학습활동재구성 */
                ON A.OPT_TXB_ID = B.OPT_TXB_ID
                AND A.LRMP_NOD_ID = B.LRMP_NOD_ID
                AND B.USE_YN = 'Y'
            INNER JOIN LMS_CMS.BC_LRN_STP C /* BC_학습단계 */
                ON B.LRMP_NOD_ID = C.LRMP_NOD_ID
                AND B.LRN_STP_ID = C.LRN_STP_ID
                AND C.LRN_STP_DV_CD = 'CL'  /* 개념학습 */
                AND C.DEL_YN = 'N'
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST D /* TL_교과학습활동상태 */
                ON B.OPT_TXB_ID = D.OPT_TXB_ID
                AND B.LRMP_NOD_ID = D.LRMP_NOD_ID
                AND B.LRN_ATV_ID = D.LRN_ATV_ID
                AND D.LRN_USR_ID = #{lrnUsrId}
        WHERE M.OPT_TXB_ID = #{optTxbId}
        AND M.LRMP_NOD_ID = #{lrmpNodId}
        AND M.USE_YN = 'Y'
        AND M.DPTH = 4
        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnLluNodInfo */
    </select>
    -->

    <!-- 교과서/익힘책 보기 -->
    <select id="selectLrntxtwkbList" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwLrnPdfDto">
        SELECT 	C.PGE_NO, D.CDN_PTH_NM, E.ANNX_ID, E.ANNX_FLE_ID, IFNULL(E.CDN_PTH_NM, '') AS TNTE_CDN_PTH_NM
        FROM 	LMS_LRM.TL_SBC_LRN_NOD_RCSTN 	A	/* TL_교과학습노드재구성 */
        INNER JOIN LMS_LRM.CM_OPT_TXB			B	/* CM_운영교과서 */
            ON 	A.OPT_TXB_ID  = B.OPT_TXB_ID
        INNER JOIN LMS_CMS.BC_TXB_PDF_FLE_MPN 	C 	/* BC_교과서PDF파일매핑 */
            ON B.TXB_ID = C.TXB_ID
        INNER JOIN LMS_CMS.BC_TXB_UPL_FLE 		D 	/* BC_교과서업로드파일 */
            ON C.UPL_FLE_ID = D.UPL_FLE_ID
            AND D.FLE_TP_CD = 'IM'
            AND D.DEL_YN = 'N'
        LEFT JOIN LMS_LRM.TL_TXB_WKB_TNTE       E   /* TL_교과서익힘책필기 */
            ON A.OPT_TXB_ID = E.OPT_TXB_ID
            AND E.USR_ID = #{usrId}
            AND C.TXB_WKB_DV_CD = E.TXB_WKB_DV_CD
            AND C.PGE_NO = E.PGE_NO
        WHERE 	A.OPT_TXB_ID  	= #{optTxbId} 	/* 운영교과서ID */
            AND A.LRMP_NOD_ID  	= #{lrmpNodId}  	/* 학습맵노드ID(차시정보) */
            <if test='txbWebDvCd == "TE"'>
                AND C.TXB_WKB_DV_CD = 'TE'					/* TE(교과서보기), WO(익힘책보기) */
                AND C.PGE_NO BETWEEN A.TXB_STR_PGE_NO AND A.TXB_END_PGE_NO
            </if>
            <if test='txbWebDvCd == "WO"'>
                AND C.TXB_WKB_DV_CD = 'WO'					/* TE(교과서보기), WO(익힘책보기) */
                AND C.PGE_NO BETWEEN A.WKB_STR_PGE_NO  AND A.WKB_END_PGE_NO
            </if>
        ORDER BY C.PGE_NO ASC

        /* 추연도 TlLrnwIfStu-Mapper.xml - selectLrntxtwkbList */
    </select>

    <!-- 학습활동상태 건수취득 
    <select id="existsLrnAtv" resultType="boolean" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto">
        SELECT EXISTS (
            SELECT 1
            FROM LMS_LRM.TL_SBC_LRN_ATV_ST
            WHERE OPT_TXB_ID = #{optTxbId}
              AND LRMP_NOD_ID = #{lrmpNodId}
              AND LRN_ATV_ID = #{lrnAtvId}
              AND LRN_USR_ID = #{lrnUsrId}
        )
    </select>
    -->
    
    
    <!-- 학습활동상태 등록처리 -->
    <insert id="insertLrnAtv" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto">
        INSERT INTO LMS_LRM.TL_SBC_LRN_ATV_ST /* TL_교과학습활동상태 */
              (OPT_TXB_ID  /* 운영교과서ID */
              ,LRMP_NOD_ID  /* 학습맵노드ID */
              ,LRN_ATV_ID  /* 학습활동ID */
              ,LRN_TC_ID
              ,LRN_USR_ID  /* 학습사용자ID */
              ,LRN_ST_CD  /* 진행상태 */
              ,LRN_TM_SCNT  /* 학습시간초수 */
              ,CRTR_ID  /* 생성자ID */
              ,CRT_DTM  /* 생성일시 */
              ,MDFR_ID  /* 수정자ID */
              ,MDF_DTM  /* 수정일시 */
              ,DB_ID  /* 접속DB인스턴스ID */
              )
        VALUES (
                 #{optTxbId}  /* 운영교과서ID */
                ,#{lrmpNodId}  /* 학습맵노드ID */
                ,#{lrnAtvId}  /* 학습활동ID */
                ,#{lrnTcId}
                ,#{lrnUsrId}  /* 학습사용자ID */
                ,#{lrnStCd}  /* 학습상태코드 */
                ,#{lrnTmScnt}  /* 학습시간초수 */
                ,#{lrnUsrId}  /* 생성자ID */
                ,NOW()  /* 생성일시 */
                ,#{lrnUsrId}  /* 수정자ID */
                ,NOW()  /* 수정일시 */
                ,#{dbId}  /* 접속DB인스턴스ID */
        )
        ON DUPLICATE KEY UPDATE
              LRN_ST_CD = #{lrnStCd}  /* 진행 상태 업데이트 */
             ,LRN_TM_SCNT = #{lrnTmScnt}  /* 학습 시간 업데이트 */
             ,LRN_TC_ID = #{lrnTcId}
             ,MDFR_ID = #{lrnUsrId}  /* 수정자ID 업데이트 */
             ,MDF_DTM = NOW()  /* 수정일시 업데이트 */
             ,DB_ID = #{dbId}  /* DB 인스턴스 ID 업데이트 */

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - insertLrnAtv */

    </insert>
    
    <!-- 학습활동상태 등록처리 -->
    <insert id="insertTcrCtn" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto">    
        INSERT INTO LMS_LRM.TL_TCR_REG_CTN_ST
        		(OPT_TXB_ID
        		,TCR_REG_CTN_ID
        		,LRN_USR_ID
        		,LRN_ST_CD
        		,LRN_TM_SCNT
        		,CRTR_ID  /* 생성자ID */
              	,CRT_DTM  /* 생성일시 */
              	,MDFR_ID  /* 수정자ID */
              	,MDF_DTM  /* 수정일시 */
              	,DB_ID  /* 접속DB인스턴스ID */)
        VALUES (#{optTxbId}
        		,#{lrnAtvId}
        		,#{lrnUsrId}
        		,#{lrnStCd}
        		,#{lrnTmScnt}
        		,#{lrnUsrId}  /* 생성자ID */
                ,NOW()  /* 생성일시 */
                ,#{lrnUsrId}  /* 수정자ID */
                ,NOW()  /* 수정일시 */
                ,#{dbId}  /* 접속DB인스턴스ID */
        )
        ON DUPLICATE KEY UPDATE
              LRN_ST_CD = #{lrnStCd}  /* 진행 상태 업데이트 */
             ,LRN_TM_SCNT = #{lrnTmScnt}  /* 학습 시간 업데이트 */
             ,MDFR_ID = #{lrnUsrId}  /* 수정자ID 업데이트 */
             ,MDF_DTM = NOW()  /* 수정일시 업데이트 */
             ,DB_ID = #{dbId}  /* DB 인스턴스 ID 업데이트 */

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - insertTcrCtn */

    </insert>

    <!-- 학습활동상태 갱신처리 -->
    <update id="updateLrnAtv" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto">
        UPDATE LMS_LRM.TL_SBC_LRN_ATV_ST /* TL_교과학습활동상태 */
        SET MDFR_ID = #{lrnUsrId}  /* 수정자ID */
           ,MDF_DTM = NOW()  /* 수정일시 */
           ,LRN_ST_CD = IF(LRN_ST_CD = 'CL', 'CL', #{lrnStCd})   /* 학습상태코드 */
           ,LRN_tC_ID = #{lrnTcId}
           ,LRN_TM_SCNT = #{lrnTmScnt}  /* 학습시간초수 */
        WHERE OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND LRMP_NOD_ID = #{lrmpNodId}  /* 학습맵노드ID */
        AND LRN_ATV_ID = #{lrnAtvId}  /* 학습활동ID */
        AND LRN_USR_ID = #{lrnUsrId}  /* 학습사용자ID */

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - updateLrnAtv */
    </update>
    
    <!-- 교사콘텐츠상태 갱신처리 -->
    <update id="updateTcrCtn" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto">
        UPDATE LMS_LRM.tl_tcr_reg_ctn_st
        SET MDFR_ID = #{lrnUsrId}  /* 수정자ID */
           ,MDF_DTM = NOW()  /* 수정일시 */
           ,LRN_ST_CD = IF(LRN_ST_CD = 'CL', 'CL', #{lrnStCd})   /* 학습상태코드 */
           ,LRN_TM_SCNT = #{lrnTmScnt}  /* 학습시간초수 */
        WHERE OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND TCR_REG_CTN_ID = #{lrnAtvId}  /* 학습활동ID */
        AND LRN_USR_ID = #{lrnUsrId}  /* 학습사용자ID */

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - updateTcrCtn */
    </update>

    <!-- 교과학습활동 과제관련 정보취득 -->
    <select id="selectEvLrnCmplInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwAtvSaveDto" resultType="Map">
        
        SELECT
			MAX(INFO.LLU_NOD_ID) as LLU_NOD_ID                             /* 대단원명 */
             ,INFO.TOC_NOD_ID  /* 차시명   */
             ,COUNT(INFO.OPT_TXB_ID)       AS LRN_CNT     /* 학습활동 완료건수 */
             ,MAX(INFO.ASN_ID) as ASN_ID
             ,MAX(INFO.TTL_LRN_CNT) as TTL_LRN_CNT /* 총학습수 */
             ,MAX(INFO.CMPL_LRN_CNT) as CMPL_LRN_CNT /* 완료학습수 */
             ,MAX(INFO.SMT_CMPL_YN) as SMT_CMPL_YN   /* 제출완료여부 */
             ,MAX(INFO.LRN_STP_DV_CD) as LRN_STP_DV_CD /* 학습단계구분코드 개념CL, 평가=EX, 익힘=WB*/
      FROM
			(SELECT
				 TA.OPT_TXB_ID
              ,TF.LLU_NOD_ID                             /* 대단원명 */
             ,TA.LRMP_NOD_ID             AS TOC_NOD_ID  /* 차시명   */
             ,TA.LRN_ATV_ID
             ,EA.ASN_ID
             ,IFNULL(EA.TTL_LRN_CNT, 0)  AS TTL_LRN_CNT  /* 총학습수 */
             ,IFNULL(EC.CMPL_LRN_CNT, 0) AS CMPL_LRN_CNT /* 완료학습수 */
             ,EC.SMT_CMPL_YN   /* 제출완료여부 */
             ,TC.LRN_STP_DV_CD /* 학습단계구분코드 개념CL, 평가=EX, 익힘=WB*/
        FROM LMS_LRM.TL_SBC_LRN_ATV_ST TA  /* TL_교과학습활동상태 */
             INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN TB /* TL_교과학습활동재구성 */
                   ON TA.OPT_TXB_ID = TB.OPT_TXB_ID
                   AND TA.LRMP_NOD_ID = TB.LRMP_NOD_ID
                   AND TA.LRN_ATV_ID = TB.LRN_ATV_ID
            INNER JOIN LMS_CMS.BC_LRN_STP TC /* BC_학습단계 */
                  ON  TB.LRMP_NOD_ID = TC.LRMP_NOD_ID
                  AND TB.LRN_STP_ID = TC.LRN_STP_ID
                  AND TC.DEL_YN = 'N'
            INNER JOIN LMS_LRM.TL_SBC_LRN_ATV_RCSTN TD /* TL_교과학습활동재구성 */
                  ON TA.OPT_TXB_ID = TD.OPT_TXB_ID
                  AND TA.LRMP_NOD_ID = TD.LRMP_NOD_ID
                  AND TB.LRN_ATV_ID = TD.LRN_ATV_ID
            INNER JOIN LMS_CMS.BC_LRN_STP TE   /* BC_학습단계 */
                  ON TB.LRMP_NOD_ID = TE.LRMP_NOD_ID
                  AND TB.LRN_STP_ID = TE.LRN_STP_ID
                  AND TE.DEL_YN = 'N'
            INNER JOIN LMS_LRM.TL_SBC_LRN_NOD_RCSTN TF /* TL_교과학습노드재구성 */
                  ON TA.OPT_TXB_ID = TF.OPT_TXB_ID
                  AND TA.LRMP_NOD_ID = TF.LRMP_NOD_ID
            INNER JOIN LMS_LRM.EA_ASN EA /* EA_과제 */
                  ON TA.OPT_TXB_ID = EA.OPT_TXB_ID
                  AND EA.LRN_TP_CD = 'TL' /* 교과 학습 */
                  AND EA.DEL_YN='N'
            INNER JOIN LMS_LRM.EA_ASN_RNGE EB /* EA_과제범위 */
                  ON EA.ASN_ID = EB.ASN_ID
                  AND EA.OPT_TXB_ID = EB.OPT_TXB_ID
                  AND EA.LRN_TP_CD = EB.LRN_TP_CD
                  AND EB.LU_NOD_ID = TF.LLU_NOD_ID
                  AND EB.TC_NOD_ID = TA.LRMP_NOD_ID
                  AND EB.LRN_STP_DV_CD = TC.LRN_STP_DV_CD /* 학습단계구분코드 CL=개념학습,WB=익힘책,EX=평가 */
                  AND EB.DEL_YN = 'N'
            INNER JOIN LMS_LRM.EA_ASN_SMT EC /* EA_과제제출 */
                  ON EA.ASN_ID = EC.ASN_ID
                  AND EC.STU_USR_ID = TA.LRN_USR_ID
        WHERE TA.OPT_TXB_ID = #{optTxbId}  /* 운영교과서ID */
        AND TA.LRMP_NOD_ID = #{lrmpNodId}   /* 학습맵노드ID */
        AND TA.LRN_USR_ID = #{lrnUsrId}  /* 학습사용자ID */
        AND TC.LRN_STP_DV_CD =  TE.LRN_STP_DV_CD /* 학습단계구분코드 CL=개념학습,WB=익힘책,EX=평가 */
        AND TA.LRN_ST_CD = 'CL' /* CL= 학습완료 */
 		and TC.LRN_STP_DV_CD=#{lrnStpDvCd}      
 		 
        UNION ALL
        
        SELECT
        		 M.OPT_TXB_ID
        		 ,R.LLU_NOD_ID                             /* 대단원명 */
             ,M.LRMP_NOD_ID             AS TOC_NOD_ID  /* 차시명   */
             ,M.TCR_REG_CTN_ID			AS LRN_ATV_ID
             ,EA.ASN_ID
             ,IFNULL(EA.TTL_LRN_CNT, 0)  AS TTL_LRN_CNT  /* 총학습수 */
             ,IFNULL(EC.CMPL_LRN_CNT, 0) AS CMPL_LRN_CNT /* 완료학습수 */
             ,EC.SMT_CMPL_YN   /* 제출완료여부 */
             ,S.LRN_STP_DV_CD /* 학습단계구분코드 개념CL, 평가=EX, 익힘=WB*/
		  FROM LMS_LRM.tl_tcr_reg_ctn_mpn M
			INNER JOIN LMS_LRM.tl_tcr_reg_ctn C
				ON M.TCR_REG_CTN_ID = C.TCR_REG_CTN_ID
			INNER JOIN LMS_LRM.tl_sbc_lrn_nod_rcstn R
				ON M.OPT_TXB_ID = R.OPT_TXB_ID
				AND M.LRMP_NOD_ID = R.LRMP_NOD_ID
			INNER JOIN LMS_CMS.bc_lrn_stp S
				ON M.LRMP_NOD_ID = S.LRMP_NOD_ID
				AND M.LRN_STP_ID = S.LRN_STP_ID
				AND S.DEL_YN = 'N'
			LEFT JOIN LMS_LRM.tl_tcr_reg_ctn_st T
				ON M.OPT_TXB_ID = T.OPT_TXB_ID
				AND M.TCR_REG_CTN_ID = T.TCR_REG_CTN_ID
				AND T.LRN_USR_ID = #{lrnUsrId}
			INNER JOIN LMS_LRM.EA_ASN EA /* EA_과제 */
               ON M.OPT_TXB_ID = EA.OPT_TXB_ID
               AND EA.LRN_TP_CD = 'TL' /* 교과 학습 */
               AND EA.DEL_YN='N'
         INNER JOIN LMS_LRM.EA_ASN_RNGE EB /* EA_과제범위 */
               ON EA.ASN_ID = EB.ASN_ID
               AND EA.OPT_TXB_ID = EB.OPT_TXB_ID
               AND EA.LRN_TP_CD = EB.LRN_TP_CD
               AND EB.LU_NOD_ID = R.LLU_NOD_ID
               AND EB.TC_NOD_ID = M.LRMP_NOD_ID
               AND EB.LRN_STP_DV_CD = S.LRN_STP_DV_CD /* 학습단계구분코드 CL=개념학습,WB=익힘책,EX=평가 */
               AND EB.DEL_YN = 'N'
         INNER JOIN LMS_LRM.EA_ASN_SMT EC /* EA_과제제출 */
               ON EA.ASN_ID = EC.ASN_ID
               AND EC.STU_USR_ID = #{lrnUsrId}
			WHERE M.OPT_TXB_ID = #{optTxbId}
			AND M.LRMP_NOD_ID = #{lrmpNodId} 
			AND M.DEL_YN = 'N'
			AND M.USE_YN = 'Y'
			AND T.LRN_ST_CD = 'CL'
			and S.LRN_STP_DV_CD='CL') INFO
		group by INFO.TOC_NOD_ID
        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectEvLrnCmplInfo */
    </select>

    <!-- 과제정보 학습완료정보 업데이트 -->
    <update id="updateEaAsnSmt" parameterType="Map">
        UPDATE LMS_LRM.EA_ASN_SMT /* EA_과제제출 */
        SET MDFR_ID =  #{param.lrnUsrId}
           ,MDF_DTM = NOW()
		   ,CMPL_LRN_CNT = #{cmplLrnCnt}
		   <if test='smtCmplYn != null and smtCmplYn != ""'>
		       ,SMT_CMPL_YN = #{smtCmplYn}
		       ,SMT_DTM = NOW() -- 제출일시
		   </if>
        WHERE ASN_ID = #{asnId}
        AND STU_USR_ID =  #{param.lrnUsrId}

        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - updateEaAsnSmt */
    </update>
    <!-- 교과학습활동 교육과정표준체계ID 목록 정보취득 -->
    <select id="selectNtnlCrclStdCn" parameterType="Map" resultType="Map">
        SELECT E.CRCL_CTN_ELM2_CD  EDU_CRS_CN_CD
              <if test='srchUnit == "ATV"'>
                  ,SUM(IF(S.LRN_ST_CD = 'CL', 0, 1)) AS CNT_NL
              </if>
              <if test='srchUnit == "CD"'>
                  ,CEIL(SUM(IF(S.LRN_ST_CD = 'CL', 1, 0)) / COUNT(1) * 100) AS RATE
              </if>
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
            INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                   AND A.LRN_STP_ID = B.LRN_STP_ID
                   AND B.DEL_YN = 'N'
                  <if test='srchUnit == "STP"'>
                      AND B.LRN_STP_DV_CD = #{lrnStpDvCd}
                  </if>
            INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                    ON A.LRN_ATV_ID = D.LRN_ATV_ID
                   AND D.USE_YN= 'Y'
                   AND D.DEL_YN= 'N'
            INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 E /* BC_국가수준교육과정컨텐츠매핑 */
                    ON E.CTN_ID = D.LRN_ATV_ID
                   AND E.CRCL_CTN_TP_CD = 'TL'  -- 교과
                   AND E.DEL_YN = 'N'
              <if test='srchUnit == "ATV" or srchUnit == "CD"'>
	            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST S  /* TL_교과학습활동상태 */
	                   ON S.OPT_TXB_ID = A.OPT_TXB_ID
	                  AND S.LRMP_NOD_ID = A.LRMP_NOD_ID
	                  AND S.LRN_ATV_ID = A.LRN_ATV_ID
	                  AND S.LRN_USR_ID = #{lrnUsrId}
              </if>
        WHERE A.OPT_TXB_ID = #{optTxbId}
          AND A.USE_YN = 'Y'
          <if test='srchUnit == "CD"'>
              AND A.LRMP_NOD_ID = #{lrmpNodId}
          </if>
          AND  E.CRCL_CTN_ELM2_CD IN
             (SELECT STRAIGHT_JOIN SE.CRCL_CTN_ELM2_CD /* 교육과정콘텐츠표준ID  STRAIGHT_JOIN 20240701추가 */
              FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN SA /* TL_교과학습활동재구성 */
                  INNER JOIN LMS_CMS.BC_LRN_STP SB  /* BC_학습단계 */
                          ON SA.LRMP_NOD_ID = SB.LRMP_NOD_ID
                         AND SA.LRN_STP_ID = SB.LRN_STP_ID
                         AND SB.DEL_YN = 'N'
                        <if test='srchUnit == "STP"'>
                         AND SB.LRN_STP_DV_CD = #{lrnStpDvCd}
                        </if>
                  INNER JOIN LMS_CMS.BC_CTN_MTD SD /* BC_콘텐츠메타데이터 */
                          ON SA.LRN_ATV_ID = SD.LRN_ATV_ID
                         AND SD.USE_YN= 'Y'
                         AND SD.DEL_YN= 'N'
                  INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 SE /* BC_국가수준교육과정컨텐츠매핑 */
                          ON SE.CTN_ID = SD.LRN_ATV_ID
                         AND SE.CRCL_CTN_TP_CD = 'TL'  -- 교과
                         AND SE.DEL_YN = 'N'
              WHERE SA.OPT_TXB_ID = #{optTxbId}
                AND SA.LRMP_NOD_ID = #{lrmpNodId}
                <if test='srchUnit == "ATV" or srchUnit == "CD"'>
                    AND SA.LRN_ATV_ID = #{lrnAtvId}
                </if>
                AND SA.USE_YN = 'Y'
             )
        GROUP BY CRCL_CTN_ELM2_CD 
    
        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectNtnlCrclStdCn */
    </select>
    
    <!-- [임시] 2024-07-03 차시 기준 표준체계ID 리스트 / 교과학습활동 교육과정표준체계ID 목록 정보취득 
    <select id="selectNtnlCrclStdCn1" parameterType="Map" resultType="Map">
        SELECT E.CRCL_CTN_ELM2_CD  EDU_CRS_CN_CD
              ,CEIL(SUM(IF(S.LRN_ST_CD = 'CL', 1, 0)) / COUNT(1) * 100) AS RATE
        FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN A /* TL_교과학습활동재구성 */
            INNER JOIN LMS_CMS.BC_LRN_STP B  /* BC_학습단계 */
                    ON A.LRMP_NOD_ID = B.LRMP_NOD_ID
                   AND A.LRN_STP_ID = B.LRN_STP_ID
                   AND B.DEL_YN = 'N'
            INNER JOIN LMS_CMS.BC_CTN_MTD D /* BC_콘텐츠메타데이터 */
                    ON A.LRN_ATV_ID = D.LRN_ATV_ID
                   AND D.USE_YN= 'Y'
                   AND D.DEL_YN= 'N'
            INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2  E /* BC_국가수준교육과정컨텐츠매핑 */
                    ON E.CTN_ID = D.LRN_ATV_ID
                   AND E.CRCL_CTN_TP_CD = 'TL'
                   AND E.DEL_YN = 'N'
            LEFT JOIN LMS_LRM.TL_SBC_LRN_ATV_ST S  /* TL_교과학습활동상태 */
                   ON S.OPT_TXB_ID = A.OPT_TXB_ID
                  AND S.LRMP_NOD_ID = A.LRMP_NOD_ID
                  AND S.LRN_ATV_ID = A.LRN_ATV_ID
                  AND S.LRN_USR_ID = #{lrnUsrId}
        WHERE A.OPT_TXB_ID = #{optTxbId}
          AND A.USE_YN = 'Y'
          AND E.CRCL_CTN_ELM2_CD IN
             (SELECT SE.CRCL_CTN_ELM2_CD /* 교육과정콘텐츠표준ID STRAIGHT_JOIN 20240701추가 */
              FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN SA /* TL_교과학습활동재구성 */
                  INNER JOIN LMS_CMS.BC_LRN_STP SB  /* BC_학습단계 */
                          ON SA.LRMP_NOD_ID = SB.LRMP_NOD_ID
                         AND SA.LRN_STP_ID = SB.LRN_STP_ID
                         AND SB.DEL_YN = 'N'
                  INNER JOIN LMS_CMS.BC_CTN_MTD SD /* BC_콘텐츠메타데이터 */
                          ON SA.LRN_ATV_ID = SD.LRN_ATV_ID
                         AND SD.USE_YN= 'Y'
                         AND SD.DEL_YN= 'N'
                  INNER JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN_V2 SE /* BC_국가수준교육과정컨텐츠매핑 */
                          ON SE.CTN_ID = SD.LRN_ATV_ID
                         AND SE.CRCL_CTN_TP_CD = 'TL'
                         AND SE.DEL_YN = 'N'
              WHERE SA.OPT_TXB_ID = #{optTxbId}
                AND SA.LRMP_NOD_ID = #{lrmpNodId}
                AND SA.USE_YN = 'Y'
             )
        GROUP BY E.CRCL_CTN_ELM2_CD
    
        /* 교과학습 강성희 TlLrnwIfStu-Mapper.xml - 20240703_추가selectNtnlCrclStdCn1 */
    </select>
-->
    <!-- 교과서/익힘책 보기 필기 정보 저장 -->
    <insert id="insertLrntxtwkb" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebSaveDto">
        INSERT INTO LMS_LRM.TL_TXB_WKB_TNTE /* TL_교과서익힘책필기 */
              (
                OPT_TXB_ID      /* 운영교과서ID */
                ,USR_ID         /* 사용자ID */
                ,TXB_WKB_DV_CD  /* 교과서익힘책구분코드 */
                ,PGE_NO         /* 페이지번호*/
                ,ANNX_ID		/* 첨부ID */
                ,ANNX_FLE_ID	/* 첨부파일ID */
                ,CDN_PTH_NM     /* CDN경로명 */
                ,CRTR_ID        /* 생성자ID */
                ,CRT_DTM        /* 생성일시 */
                ,MDFR_ID        /* 수정자ID */
                ,MDF_DTM        /* 수정일시 */
                ,DB_ID          /* 접속DB인스턴스ID */
              )
        VALUES (
                 #{optTxbId}    /* 운영교과서ID */
                ,#{usrId}       /* 사용자ID */
                ,#{txbWebDvCd}  /* 교과서익힘책구분코드 */
                ,#{pgeNo}       /* 페이지번호*/
                ,#{annxId}    	/* 첨부ID */
                ,#{annxFleId}   /* 첨부파일ID */
                ,#{cdnPthNm}    /* CDN경로명 */
                ,#{usrId}       /* 생성자ID */
                ,NOW()          /* 생성일시 */
                ,#{usrId}       /* 수정자ID */
                ,NOW()          /* 수정일시 */
                ,#{dbId}        /* 접속DB인스턴스ID */
        )
        ON DUPLICATE KEY UPDATE
                CDN_PTH_NM  = #{cdnPthNm}
            , 	ANNX_ID		= #{annxId}
            , 	ANNX_FLE_ID	= #{annxFleId}
            ,   MDFR_ID     = #{usrId}
            ,   MDF_DTM     = NOW()

        /* 추연도 TlLrnwIfStu-Mapper.xml - insertLrntxtwkb */
    </insert>

    <!-- 교육과정콘텐츠표준 정보 조회 -->
    <select id="selectCrclCntStdInfoList" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTxbWebDto" resultType="com.aidt.api.tl.common.dto.TlCrclCntStdInfoDto">
		SELECT
			B.LRN_ATV_ID
			, C.CRCL_CTN_ID
			, C.CTN_ID
			, C.CRCL_CTN_TP_CD
			, C.CRCL_CTN_STD_ID
			, C.CRCL_CTN_ELM1_ID
			, C.CRCL_CTN_ELM2_ID
			, C.CRCL_ACH_BS_ID
			, C.CRCL_CTGR
			, C.CRCL_CN_ARA
			, C.CRCL_ACT_ARA
			, C.CRCL_SBC_CPBL
			, C.SRT_ORDN
			, C.DEL_YN
			FROM
				LMS_LRM.TL_SBC_LRN_ATV_RCSTN B
			LEFT JOIN LMS_CMS.BC_NTNL_CRCL_CTN_MPN C ON B.LRN_ATV_ID = C.CTN_ID
													 AND C.CRCL_CTN_TP_CD = 'TL'
													 AND C.DEL_YN = 'N'
			WHERE B.OPT_TXB_ID = #{optTxbId}
			AND B.LRMP_NOD_ID = #{lrmpNodId}
        /* 신동환 TlLrnwIfStu-Mapper.xml - selectCrclCntStdInfoList */
    </select>
     
     <!-- 
        수업자료 기본목록 조회 
        LCMS에서 등록한 기본수업자료를 조회한다.
    -->
    <select id="selectLrnMtrlBsList" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto" resultType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        SELECT
		     IFNULL(C.LSN_MTRL_NO, '')   AS LSN_MTRL_NO 
            ,A.LSN_SPP_MTRL_ID           AS LSN_SPP_MTRL_ID  /* 수업보충자료ID */
            ,'BS'                        AS MTRL_DV_CD       /* 자료구분=LCMS등록자료 */
            ,A.LSN_SPP_MTRL_NM           AS MTRL_NM          /* 수업보충자료명 */
            ,A.FLE_MG_TP_CD                                  /* 파일관리유형코드 */
            ,A.FLE_LINK_URL_ADR                              /* 파일링크URL주소 */
            ,IFNULL(B.FLE_PTH_NM, '')    AS FLE_PTH_NM       /* 파일경로명 */
            ,B.FLE_TP_CD                                     /* 파일유형코드 */
            ,B.ALTN_TXT_CN                                   /* 대체텍스트내용 */
            ,IFNULL(C.OPNP_YN, A.EPS_YN) AS OPNP_YN          /* 공개여부 LMS에서 공개여부가 설정되지 않았으면 LCMS설정을 따른다. */
            ,A.LRMP_NOD_ID                                   /* 학습맵노드ID */
            ,IFNULL(B.UPL_FLE_NM, '')    AS FLE_NM           /* 파일명 */
            ,IFNULL(A.DOC_VI_ID,'')	AS  DOC_VI_ID
        FROM LMS_CMS.BC_LSN_SPP_MTRL A /* BC_수업보충자료 */
            LEFT JOIN LMS_CMS.BC_UPL_FLE B /* BC_업로드파일 */
                 ON A.UPL_FLE_ID = B.UPL_FLE_ID
                 AND B.DEL_YN = 'N'
                 AND A.FLE_MG_TP_CD = 'DM'
            LEFT JOIN LMS_LRM.TL_SBC_LRN_LSN_MTRL C /* TL_교과학습수업자료 */
                 ON C.OPT_TXB_ID = #{optTxbId}
                 AND A.LRMP_NOD_ID = C.LRMP_NOD_ID
                 AND A.LSN_SPP_MTRL_ID = C.LRN_MTRL_ID
                 AND C.BS_MTRL_YN = 'Y'
                 AND A.DEL_YN = 'N'
        WHERE A.LRMP_NOD_ID = #{lrmpNodId} 
        AND A.EPS_YN = 'Y'
        AND IFNULL(C.OPNP_YN, A.EPS_YN) = 'Y'
        AND A.DEL_YN = 'N'
        ORDER BY A.CRT_DTM DESC, A.LSN_SPP_MTRL_ID DESC

        /** 교과학습 강성희 TlLrnwIfStu-Mapper.xml - selectLrnMtrlBsList */
    </select>
    
    <!-- v3.1 대응 내 자료 조회-->
    <select id="selectLrnMtrlLmList" parameterType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlTocSrhDto" resultType="com.aidt.api.tl.lsnmtrl.dto.TlLsnMtrlDto">
        SELECT 
        	 M.LSN_MTRL_NO                            /* 수업자료번호 */
        	,M.LSN_MTRL_TP_CD                         /* 수업자료유형코드 */
        	,M.ANNX_ID                                /* 첨부ID */
        	,M.LSN_SPP_MTRL_NM   AS MTRL_NM           /* 수업보충자료ID */
        	,M.URL               AS FLE_LINK_URL_ADR  /* 파일링크URL주소 */
            ,M.OPNP_YN
        	,F.ANNX_FLE_ORGL_NM  AS FLE_NM            /* 파일명 */
        	,IFNULL(F.ANNX_FLE_PTH_NM, '')   AS FLE_PTH_NM        /* 파일경로명 */
        	,IFNULL(F.DOC_VI_ID, '') AS DOC_VI_ID
        	,M.LRMP_NOD_ID
        FROM LMS_LRM.TL_SBC_LRN_LSN_MTRL M      /* TL_교과학습수업자료 */
            LEFT JOIN LMS_LRM.CM_ANNX A             /* CM_첨부 */
                   ON M.ANNX_ID = A.ANNX_ID
                  AND A.USE_YN = 'Y'
            LEFT JOIN LMS_LRM.CM_ANNX_FLE F         /* CM_첨부파일 */
                   ON A.ANNX_ID = F.ANNX_ID
                  AND F.USE_YN = 'Y'
        WHERE M.OPT_TXB_ID = #{optTxbId}
          AND M.LRMP_NOD_ID = #{lrmpNodId}
          AND M.DEL_YN = 'N'
          AND M.BS_MTRL_YN = 'N'  -- 원클릭학습설정에서 교사가 올린것만
        ORDER BY M.CRT_DTM DESC
    
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectLrnMtrlLmList */
    </select>
    
    <!-- 외부활동설정 -->
    <select id="selectExtAtvSetm" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtAtvSetm">
        SELECT
        	CLS_PPNG_USE_YN
        	,CLS_ARCHV_USE_YN
        	,PADL_USE_YN
        	,"N" as CANV_USE_YN
        	,MIR_CANV_USE_YN
        	,"N" as GGL_DOC_USE_YN
    	FROM LMS_LRM.cm_ext_atv_setm
    	WHERE OPT_TXB_ID = #{param.optTxbId}
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectExtAtvSetm */
    </select>
    
    <!-- 외부활동추가링크 -->
    <select id="selectExtLink" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="com.aidt.api.tl.lrnwif.dto.TlLrnwExtrLink">
        SELECT
        	EXTR_LINK_NM
        	,EXTR_LINK_URL
    	FROM LMS_LRM.cm_ext_atv_extr_link
    	WHERE OPT_TXB_ID = #{param.optTxbId}
    	AND DEL_YN = "N"
    	ORDER BY EXTR_LINK_NO ASC
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectExtAtvSetm */
    </select>
    
     <!-- 미등록 클래스보드 정보 조회 -->
    <select id="selectClabdInfo" parameterType="com.aidt.api.tl.lrnwif.dto.TlLrnwTocSrhDto" resultType="Map">
    SELECT C.LRN_ATV_ID, B.CLABD_SML_ID
 		FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN C
	INNER JOIN LMS_CMS.BC_CLABD_LRGS A /* BC_클래스보드 대제목  */
          ON A.LRN_ATV_ID = C.LRN_ATV_ID
 	INNER JOIN LMS_CMS.BC_CLABD_SML B /* BC_클래스보드 소제목  */
           ON A.CLABD_LRGS_ID =B.CLABD_LRGS_ID
 	left join LMS_LRM.tl_sbc_lrn_atv_clabd L
 		on L.OPT_TXB_ID = #{param.optTxbId}
 		and L.LRMP_NOD_ID = #{param.lrmpNodId}
 		and L.LRN_ATV_ID = A.LRN_ATV_ID 
 	WHERE C.OPT_TXB_ID = #{param.optTxbId} -- 로그인세션정보의 운영교과서ID
 	and C.LRMP_NOD_ID = #{param.lrmpNodId}
 	AND A.MAP_TYP='TL'
 	AND A.USE_YN = 'Y'
 	and A.LRN_ATV_ID <![CDATA[<>]]> IFNULL(L.LRN_ATV_ID,0)
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - selectClabdInfo */
    </select>
    
    <insert id="insertTlSbcLrnAtvClabd" parameterType="Map"> 
        INSERT INTO LMS_LRM.TL_SBC_LRN_ATV_CLABD /* TL_교과학습활동클래스보드 */
            (OPT_TXB_ID
            ,LRMP_NOD_ID
            ,LRN_ATV_ID
            ,CLABD_LRGS_ID
            ,CLABD_SML_ID
            ,CLABD_LRGS_NM
            ,CLABD_SML_NM
            ,CLABD_TYP
            ,CLABD_URL
            ,CLABD_ORDN
            ,CRTR_ID
            ,CRT_DTM
            ,MDFR_ID
            ,MDF_DTM
            ,DB_ID)
        SELECT C.OPT_TXB_ID  
              ,C.LRMP_NOD_ID
              ,A.LRN_ATV_ID
              ,A.CLABD_LRGS_ID
              ,B.CLABD_SML_ID
              ,A.CLABD_LRGS_NM
              ,B.CLABD_SML_NM
              ,B.CLABD_TYP
              ,NULL            AS CLABD_URL
              ,B.CLABD_ORDN
              ,'System'	       AS CRTR_ID
              ,NOW()           AS CRT_DTM
              ,'System'		   AS MDFR_ID
              ,NOW()           AS MDF_DTM
              ,#{txbId}         AS DB_ID
        FROM 
        	( SELECT *
			    FROM LMS_LRM.TL_SBC_LRN_ATV_RCSTN
			    WHERE OPT_TXB_ID =  #{param.optTxbId}   -- 로그인세션정보의 운영교과서ID
    		) C 
        	INNER JOIN LMS_CMS.BC_CLABD_LRGS A /* BC_클래스보드 대제목  */
                 ON A.LRN_ATV_ID = C.LRN_ATV_ID
            INNER JOIN LMS_CMS.BC_CLABD_SML B /* BC_클래스보드 소제목  */
                  ON A.CLABD_LRGS_ID =B.CLABD_LRGS_ID
                  and B.CLABD_SML_ID = #{clabdSmlId}
        WHERE A.MAP_TYP='TL'
        AND A.USE_YN = 'Y'
        and C.LRN_ATV_ID = #{lrnAtvId}
        
        /** 교과학습 김형준 TlLrnwIfStu-Mapper.xml - insertTlSbcLrnAtvClabd */
    </insert>
</mapper>