<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.slpp.tcr">
	<!-- 쪽지 조회 -->
	<select id="selectSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto" resultType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppTcr-Mapper.xml - selectSlppList */
		SELECT
			SLPP_ID,
			OPT_TXB_ID,
			TCR_USR_ID,
			STU_USR_ID,
			TRNM_USR_TP_CD,
			GRUP_SND_DV_CD,
			SLPP_CN,
			COFM_YN,
			TCR_DEL_YN,
			STU_DEL_YN,
			CRTR_ID,
			CRT_DTM,
			DATE_FORMAT(CRT_DTM, '%y-%m-%d') AS CRT_DTM_FORMAT1,
			(CASE
	         WHEN INSTR(DATE_FORMAT(CRT_DTM, '%Y-%m-%d %p %h:%i'), 'PM') > 0
	         THEN CONCAT('오후 ',DATE_FORMAT(CRT_DTM, '%h:%i'))
	         ELSE CONCAT('오전 ',DATE_FORMAT(CRT_DTM, '%h:%i'))
	         END) AS CRT_DTM_FORMAT2,
			MDFR_ID,
			MDF_DTM,
			DB_ID
		FROM LMS_LRM.CM_SLPP
		WHERE TCR_USR_ID = #{tcrUsrId}
		AND STU_USR_ID = #{stuUsrId}
		AND TCR_DEL_YN != 'Y'
		and STU_DEL_YN != 'Y'
		<if test = 'slppCn != null and !"".equals(slppCn)'>
			AND SLPP_CN LIKE CONCAT('%', #{slppCn}, '%')
		</if>
		ORDER BY CRT_DTM DESC, SLPP_ID DESC
		<if test = 'pageSize != null and !"".equals(pageSize) and pageNo != null and !"".equals(pageNo)'>
			LIMIT #{pageSize}
			OFFSET #{pageOffset}
		</if>
		;/** BcSlppTcr-Mapper.xml - selectSlppList */
	</select>

	<!-- 쪽지 확인 처리 -->
	<update id="updateCofmYnSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto">
		/** BcSlppTcr-Mapper.xml - updateCofmYnSlppList */
		UPDATE LMS_LRM.CM_SLPP CS
		SET
			CS.COFM_YN = 'Y',
			CS.MDF_DTM = NOW()
		WHERE CS.TCR_USR_ID = #{tcrUsrId}
		AND CS.STU_USR_ID = #{stuUsrId}
		AND (CS.TCR_USR_ID = #{crtrId} OR CS.STU_USR_ID = #{crtrId})
		AND CS.CRTR_ID != #{crtrId}
		;/** BcSlppTcr-Mapper.xml - updateCofmYnSlppList */
	</update>

	<!-- 쪽지 등록 (리스트) -->
	<insert id="insertSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppTcr-Mapper.xml - insertSlppList */
		INSERT INTO LMS_LRM.CM_SLPP(
			OPT_TXB_ID, TCR_USR_ID, STU_USR_ID, TRNM_USR_TP_CD, GRUP_SND_DV_CD, SLPP_CN
			, COFM_YN, TCR_DEL_YN, STU_DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES
		<foreach collection="list" item="dto" separator=", ">
			(
				#{dto.optTxbId},
				#{dto.tcrUsrId},
				#{dto.stuUsrId},
				#{dto.trnmUsrTpCd},
				#{dto.grupSndDvCd},
				#{dto.slppCn},
				'N',
				'N',
				'N',
				#{dto.crtrId},
				NOW(),
				#{dto.crtrId},
				NOW(),
				'1'
			)
		</foreach>
		;/** BcSlppTcr-Mapper.xml - insertSlppList */
	</insert>

	<!-- 쪽지 등록 -->
	<insert id="insertSlpp" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppTcr-Mapper.xml - insertSlpp */
		INSERT INTO LMS_LRM.CM_SLPP(
		OPT_TXB_ID, TCR_USR_ID, STU_USR_ID, TRNM_USR_TP_CD, GRUP_SND_DV_CD, SLPP_CN
		, COFM_YN, TCR_DEL_YN, STU_DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
		#{optTxbId},
		#{tcrUsrId},
		#{stuUsrId},
		#{trnmUsrTpCd},
		#{grupSndDvCd},
		#{slppCn},
		'N',
		'N',
		'N',
		#{crtrId},
		NOW(),
		#{crtrId},
		NOW(),
		'1'
		)
		;/** BcSlppTcr-Mapper.xml - insertSlpp */
	</insert>

	<!-- 쪽지 삭제 -->
	<update id="deleteSlpp" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppTcr-Mapper.xml - deleteSlpp */
		UPDATE LMS_LRM.CM_SLPP
		SET
			TCR_DEL_YN = 'Y',
			MDF_DTM = NOW()
		WHERE SLPP_ID = #{slppId}
		;/** BcSlppTcr-Mapper.xml - deleteSlpp */
	</update>
	<!-- 쪽지 삭제 (리스트) -->
	<update id="deleteSlppList" parameterType="com.aidt.api.bc.slpp.dto.BcSlppDto">
		/** BcSlppTcr-Mapper.xml - deleteSlppList */
		UPDATE LMS_LRM.CM_SLPP CS
		INNER JOIN (
		<foreach collection="list" item="dto" separator=" UNION ALL ">
			SELECT
			#{dto.slppId} AS SLPP_ID
		</foreach>
		) TMP
			ON CS.SLPP_ID = TMP.SLPP_ID
		SET
			CS.TCR_DEL_YN = 'Y',
			CS.MDF_DTM = NOW()
		;/** BcSlppTcr-Mapper.xml - deleteSlppList */
	</update>
	<!-- 쪽지 전체 삭제 -->
	<update id="deleteAllSlpp" parameterType="com.aidt.api.bc.slpp.dto.BcSlppComandDto">
		/** BcSlppTcr-Mapper.xml - deleteSlppList */
		UPDATE LMS_LRM.CM_SLPP CS
		SET
			CS.TCR_DEL_YN = 'Y',
			CS.MDF_DTM = NOW()
		WHERE CS.TCR_USR_ID = #{tcrUsrId}
		AND CS.STU_USR_ID = #{stuUsrId}
		;/** BcSlppTcr-Mapper.xml - deleteSlppList */
	</update>

	<select id="selectSlppRoomList" parameterType="java.lang.String" resultType="com.aidt.api.bc.slpp.dto.BcSlppRoomDto">
		/** BcSlppTcr-Mapper.xml - selectSlppRoomList */
		SELECT
			MAX(CS.STU_USR_ID) as STU_USR_ID
			, MAX(CS.SLPP_CN) as SLPP_CN
			, MAX(CS.CRTR_ID) as CRTR_ID
			, MAX(CS.CRT_DTM) as CRT_DTM
			, CASE WHEN MAX(CS.COFM_YN) = 'N' AND MAX(CS.CRTR_ID) != #{tcrUsrId} THEN 'N'
				ELSE 'Y'
				END AS COFM_YN
			, MAX(COALESCE(CU1.USR_NM, CU2.USR_NM)) AS STU_NAME
		FROM LMS_LRM.CM_SLPP CS
		INNER JOIN (
			SELECT
				CS.STU_USR_ID,
				FIRST_VALUE(CS.SLPP_ID) OVER(PARTITION BY CS.STU_USR_ID ORDER BY CS.CRT_DTM DESC, CS.SLPP_ID DESC) AS MAX_SLPP_ID
			FROM LMS_LRM.CM_SLPP CS
			WHERE CS.TCR_USR_ID = #{tcrUsrId}
			AND CS.TCR_DEL_YN = 'N'
			and CS.STU_DEL_YN = 'N'
		) TMP ON CS.SLPP_ID = TMP.MAX_SLPP_ID
			LEFT JOIN LMS_LRM.CM_USR CU1
			    ON CU1.USR_ID = CS.STU_USR_ID  -- 1차 조인 (USR_ID 기준)
			LEFT JOIN LMS_LRM.CM_USR CU2
			    ON CU2.KERIS_USR_ID = CS.STU_USR_ID
		GROUP BY TMP.STU_USR_ID
		order by MAX(CS.COFM_YN) , MAX(CS.SLPP_ID) ;
		/** BcSlppTcr-Mapper.xml - selectSlppRoomList */
	</select>

	<select id="selectSlppTargetList" parameterType="java.lang.String" resultType="com.aidt.api.bc.slpp.dto.BcSlppTargetDto">
		/** BcSlppTcr-Mapper.xml - selectSlppTargetList */
		SELECT
			CU.USR_ID AS STU_USR_ID
		FROM LMS_LRM.CM_CLA CC
		LEFT JOIN LMS_LRM.CM_USR CU
			ON CU.CLA_ID = CC.CLA_ID
		WHERE CC.CHG_TCR_USR_ID  = #{tcrUsrId}
		;/** BcSlppTcr-Mapper.xml - selectSlppTargetList */
	</select>

	<!-- 교사 대화 시간 설정 신규 -->
	<insert id="insertSlppTmSetm4Save" parameterType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		/** BcSlppTcr-Mapper.xml - insertSlppTmSetm4Save */
		INSERT INTO LMS_LRM.CM_SLPP_TM_SETM (
			OPT_TXB_ID, MON_YN, TUE_YN, WED_YN, THU_YN, FRI_YN, SAT_YN, SUN_YN,
			SLPP_ABLE_STR_HM, SLPP_ABLE_END_HM, LGLH_YN,
			CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{optTxbId},
			#{monYn},
			#{tueYn},
			#{wedYn},
			#{thuYn},
			#{friYn},
			#{satYn},
			#{sunYn},
			#{slppAbleStrHm},
			#{slppAbleEndHm},
			#{lglhYn},
			#{mdfrId},
			NOW(),
			#{mdfrId},
			NOW(),
			'1'
		)
		/** BcSlppTcr-Mapper.xml - insertSlppTmSetm4Save */
	</insert>

	<!-- 교사 최초 로그인시, 교사 대화 시간 설정 신규 -->
	<insert id="insertSlppTmSetm" parameterType="java.lang.String">
		/** BcSlppTcr-Mapper.xml - insertSlppTmSetm */
		INSERT INTO LMS_LRM.CM_SLPP_TM_SETM (
			OPT_TXB_ID, MON_YN, TUE_YN, WED_YN, THU_YN, FRI_YN, SAT_YN, SUN_YN,
			SLPP_ABLE_STR_HM, SLPP_ABLE_END_HM, LGLH_YN,
			CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID
		) VALUES (
			#{optTxbId},
			'Y',
			'Y',
			'Y',
			'Y',
			'Y',
			'N',
			'N',
			'0900',
			'1800',
			'N',
			'SYSTEM',
			NOW(),
			'SYSTEM',
			NOW(),
			'1'
		)
		;/** BcSlppTcr-Mapper.xml - insertSlppTmSetm */
	</insert>

	<!-- 교사 대화 시간 설정 갱신 -->
	<update id="updateSlppTmSetm" parameterType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		/** BcSlppTcr-Mapper.xml - updateSlppTmSetm */
		UPDATE LMS_LRM.CM_SLPP_TM_SETM CSTS
		SET
			CSTS.MON_YN = #{monYn},
			CSTS.TUE_YN = #{tueYn},
			CSTS.WED_YN = #{wedYn},
			CSTS.THU_YN = #{thuYn},
			CSTS.FRI_YN = #{friYn},
			CSTS.SAT_YN = #{satYn},
			CSTS.SUN_YN = #{sunYn},
			CSTS.SLPP_ABLE_STR_HM = #{slppAbleStrHm},
			CSTS.SLPP_ABLE_END_HM = #{slppAbleEndHm},
			CSTS.LGLH_YN = #{lglhYn},
			CSTS.MDFR_ID = #{mdfrId},
			CSTS.MDF_DTM = NOW(),
			DB_ID = '1'
		WHERE CSTS.OPT_TXB_ID = #{optTxbId}
		;/** BcSlppTcr-Mapper.xml - updateSlppTmSetm */
	</update>
	
	<update id="updateSlppUseYn" parameterType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		/** BcSlppTcr-Mapper.xml - selectSlppUseYn */
		UPDATE LMS_LRM.CM_SLPP_TM_SETM CSTS
		SET 
			USE_YN = #{useYn}
		WHERE CSTS.OPT_TXB_ID = #{optTxbId}
	</update>

	<!-- 교사 대화 시간 조회-->
	<select id="selectSlppTmSetm" parameterType="java.lang.String" resultType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		/** BcSlppTcr-Mapper.xml - selectSlppTmSetm */
		SELECT
			CSTS.OPT_TXB_ID,
			CSTS.MON_YN,
			CSTS.TUE_YN,
			CSTS.WED_YN,
			CSTS.THU_YN,
			CSTS.FRI_YN,
			CSTS.SAT_YN,
			CSTS.SUN_YN,
			CSTS.SLPP_ABLE_STR_HM,
			CSTS.SLPP_ABLE_END_HM,
			CSTS.LGLH_YN,
			CSTS.DB_ID,
			CFUS.DILG_USE_YN as USE_YN
		FROM LMS_LRM.CM_SLPP_TM_SETM CSTS
		inner join CM_FNC_USE_SETM CFUS on
			CSTS.OPT_TXB_ID = CFUS.OPT_TXB_ID 
		WHERE CSTS.OPT_TXB_ID = #{optTxbId}
		LIMIT 1
		;/** BcSlppTcr-Mapper.xml - selectSlppTmSetm */
	</select>
	
	
	<select id="selectSlppTmSetmDummy" parameterType="java.lang.String" resultType="com.aidt.api.bc.slpp.dto.BcSlppTmSetmDto">
		SELECT
		    #{optTxbId} AS OPT_TXB_ID,
		    'Y' AS MON_YN,
		    'Y' AS TUE_YN,
		    'Y' AS WED_YN,
		    'Y' AS THU_YN,
		    'Y' AS FRI_YN,
		    'N' AS SAT_YN,
		    'N' AS SUN_YN,
		    '0900' AS SLPP_ABLE_STR_HM,  -- 타임 형식
		    '1800' AS SLPP_ABLE_END_HM,  -- 타임 형식
		    'N' AS LGLH_YN,
		    'Y' AS DB_ID,
		    DILG_USE_YN AS USE_YN
		from
			CM_FNC_USE_SETM
		where
			OPT_TXB_ID = #{optTxbId}
	</select>
	
	
	<select id="selectSlppCofmCnt" parameterType="com.aidt.api.bc.slpp.dto.BcSlppPagingRequestDto" resultType="int">
		SELECT
			count(*)
		FROM
			CM_SLPP CS
		WHERE
			cs.TCR_USR_ID = #{tcrUsrId}
			AND cs.CRTR_ID != #{crtrId} 
			AND TRNM_USR_TP_CD = 'ST' 
			AND COFM_YN = 'N'
	</select>

</mapper>

