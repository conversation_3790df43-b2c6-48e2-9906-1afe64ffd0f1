package com.aidt.api.tl.oneclksetm.tcr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-02-15 16:23:14
 * @modify date 2024-02-15 16:23:14
 * @desc [TlOneClkSetmAtv Dto 원클릭학습설정 활동목록 dto]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlOneClkSetmAtvDto {
       /** 운영교과서ID */
       @Parameter(name="운영교과서ID")
       private String optTxbId;

       /** 학습맵노드ID */
       @Parameter(name="학습맵노드ID")
       private String lrmpNodId;
       
       /** 원본차시ID */
       @Parameter(name="원본차시ID")
       private String orglLrmpNodId;
       
       /** 대단원ID */
       @Parameter(name="대단원ID")
       private String lluNodId;

       /** 학습활동ID */
       @Parameter(name="학습활동ID")
       private String lrnAtvId;

       /** 학습단계ID */
       @Parameter(name="학습단계ID")
       private long lrnStpId;
       
       /** 복사차시ID */
       @Parameter(name="복사차시ID")
       private String cpLrmpNodId;
       
       /** 복사학습단계ID */
       @Parameter(name="복사학습단계ID")
       private long cpLrnStpId;

       /** 원본 학습단계ID */
       @Parameter(name="원본 학습단계ID")
       private long orglLrnStpId;

       /** LCMS 학습활동코드(varchar(10)) */
       @Parameter(name="LCMS 학습활동코드(varchar(10))")
       private String ctnCd;

       /** 문항아이디 or 파일명 */
       @Parameter(name="문항아이디 or 파일명")
       private String lrnAtvNm;

       /** 콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지) */
       @Parameter(name="콘텐츠 타입(QU:문항, HT:HTML5, PL:동영상, EX:평가지)")
       private String ctnTpCd;

       /** 사용여부 */
       @Parameter(name="사용여부")
       private String useYn;
       
       /** 삭제여부 */
       @Parameter(name="삭제여부")
       private String delYn;
       
       /** 저장여부 */
       @Parameter(name="저장여부")
       private String savYn;
       
       /** 복사여부 */
       @Parameter(name="복사여부")
       private String cpYn;

       /** 재구성순서 */
       @Parameter(name="재구성순서")
       private int rcstnOrdn;

       /** 원본순서 */
       @Parameter(name="원본순서")
       private int orglOrdn;

       /** 평가ID */
       @Parameter(name="평가ID")
       private long evId;

       /** 수정자ID */
       @Parameter(name="수정자ID")
       private String mdfrId;

       /** 학습단계 구분코드 */
       @Parameter(name="학습단계 구분코드")
       private String lrnStpDvCd;

       /** 학습단계 코드 */
       @Parameter(name="학습단계 코드")
       private String lrnStpCd;

       /** 원본 학습단계 코드 */
       @Parameter(name="원본 학습단계 코드")
       private String orglLrnStpCd;

       /** 학습단계명 */
       @Parameter(name="학습단계명")
       private String lrnStpNm;

       /** 원본 학습단계명 */
       @Parameter(name="원본 학습단계명")
       private String orglLrnStpNm;

       /** 학습단계 순서 */
       @Parameter(name="학습단계 순서")
       private String srtOrdn;
       
       /** 원본 학습단계 순서 */
       @Parameter(name="원본 학습단계 순서")
       private String orglSrtOrdn;
      
       /** 썸네일 패스 */
       @Parameter(name="썸네일 패스")
       private String lrnAtvThbPth;

       /** 교육과정내용코드 */
       @Parameter(name="교육과정내용코드")
       private String eduCrsCnCd;
       
       /** 선생님 추가 콘텐츠 여부 */
       @Parameter(name="선생님 추가 콘텐츠 여부")
       private String tcrCtnYn;
       
       /** 선생님 추가 콘텐츠 ID */
       @Parameter(name="선생님 추가 콘텐츠 ID")
       private String tcrRegCtnId;
       
       /** 단원 노출 여부 */
       @Parameter(name="단원 노출 여부")
       private String luEpsYn;
       
       /** 뷰어코드 */
       @Parameter(name="뷰어코드")
       private String docViId;
       
       /** 파일경로 */
       @Parameter(name="파일경로")
       private String annxFlePthNm;

       /** db id */
       @Parameter(name="dbId")
       private String dbId;
}
