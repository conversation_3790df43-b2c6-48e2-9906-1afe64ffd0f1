package com.aidt.api.al.cmt.dto.ett.en;

import com.aidt.api.al.cmt.dto.ett.cm.N05Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N13Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N14Dto;
import com.aidt.api.al.cmt.dto.ett.cm.N15Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnEvEtDto {

	private N05Dto n05;

    private N12Dto n12;

    private N13Dto n13;

    private N14Dto n14;

    private N15Dto n15;

    @Builder.Default
    private String usrTpCd = "ST";

}
