<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.bc.ct.tcr">
	<!-- URL 전송목록 조회 -->
	<select id="selectUrlSendList" parameterType="com.aidt.api.bc.ct.dto.BcCtDto" resultType="com.aidt.api.bc.ct.dto.BcCtDto">
		/** BcCtTcr-Mapper.xml - selectUrlSendList */
		SELECT
			SEQ_NO,
			OPT_TXB_ID,
			URL,
			USR_ID,
			CRTR_ID,
			CRT_DTM,
			MDFR_ID,
			MDF_DTM
		FROM LMS_LRM.CT_URL_SND
		WHERE OPT_TXB_ID = #{optTxbId}
		ORDER BY SEQ_NO DESC
		/** BcCtTcr-Mapper.xml - selectUrlSendList */
	</select>
	
	<!-- URL 전송목록 등록 -->
	<insert id="insertUrlSend" parameterType="com.aidt.api.bc.ct.dto.BcCtDto">
		/** BcCtTcr-Mapper.xml - insertUrlSend */
		INSERT INTO LMS_LRM.CT_URL_SND(
			OPT_TXB_ID, URL, USR_ID, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM
		) VALUES (
			#{optTxbId},
			#{url},
			#{usrId},
			#{usrId},
			NOW(),
			#{usrId},
			NOW()
		)
		/** BcCtTcr-Mapper.xml - insertUrlSend */
	</insert>	
	
	<!-- URL 전송목록 삭제 -->
	<delete id="deleteUrlSend" parameterType="com.aidt.api.bc.ct.dto.BcCtDto">
		/** BcCtTcr-Mapper.xml - deleteUrlSend */
		DELETE FROM LMS_LRM.CT_URL_SND
		WHERE SEQ_NO = #{seqNo}
		/** BcCtTcr-Mapper.xml - deleteUrlSend */
	</delete>	
	
	<!-- URL 전송목록 개수 조회 -->
	<select id="selectUrlSendcount" parameterType="com.aidt.api.bc.ct.dto.BcCtDto" resultType="int">
		/** BcCtTcr-Mapper.xml - selectUrlSendcount */
		SELECT
			COUNT(SEQ_NO) AS CNT
		FROM LMS_LRM.CT_URL_SND
		WHERE OPT_TXB_ID = #{optTxbId}
		/** BcCtTcr-Mapper.xml - selectUrlSendcount */
	</select>	
	
	<!-- URL 전송목록 첫번째 삭제 -->
	<delete id="deleteFirstUrlSend" parameterType="com.aidt.api.bc.ct.dto.BcCtDto">
		/** BcCtTcr-Mapper.xml - deleteFirstUrlSend */
		DELETE FROM LMS_LRM.CT_URL_SND
		WHERE SEQ_NO = (SELECT OLD_SEQ FROM (SELECT MIN(SEQ_NO) AS OLD_SEQ FROM LMS_LRM.CT_URL_SND WHERE OPT_TXB_ID = #{optTxbId}) T)
		/** BcCtTcr-Mapper.xml - deleteFirstUrlSend */
	</delete>		
	
	<!-- URL 중복체크 조회 -->
	<select id="selectUrlDupCheck" parameterType="com.aidt.api.bc.ct.dto.BcCtDto" resultType="int">
		/** BcCtTcr-Mapper.xml - selectUrlDupCheck */
		SELECT
			CASE WHEN COUNT(SEQ_NO) > 0 THEN MAX(SEQ_NO) ELSE 0 END
		FROM LMS_LRM.CT_URL_SND
		WHERE OPT_TXB_ID = #{optTxbId}
		  AND URL = #{url}
		/** BcCtTcr-Mapper.xml - selectUrlDupCheck */
	</select>		
</mapper>