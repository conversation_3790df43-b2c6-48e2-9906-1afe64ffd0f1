package com.aidt.api.tl.sbclrn.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-04-12 16:20:19
 * @modify date 2024-04-12 16:20:19
 * @desc TlSbcLrnAtvEvDto 교과학습 활동상태 익힘정보
 */
@Data
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class TlSbcLrnAtvWbDto {

    /** 차시노드ID(차시) */
    @Parameter(name="차시노드ID")
    private String lrmpNodId;
    /** 활동ID */
    @Parameter(name="활동ID")
    private String lrnAtvId;
    /** 총건수 */
    @Parameter(name="총건수")
    private String wkbTotCnt;
    /** 완료건수 */
    @Parameter(name="완료건수")
    private String wkbFinCnt;
    /** 진행상태코드 */
    @Parameter(name="진행상태코드")
    private String wkbLrnStCd;
    /** 표시여부 */
    @Parameter(name="표시여부")
    private String showYn;
}
