package com.aidt.api.al.pl.tcr;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aidt.api.al.pl.dto.AlPlCtnDto;
import com.aidt.api.al.pl.stu.AlPlStuCtnService;
import com.aidt.common.Response;
import com.aidt.common.Response.ResponseDto;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-07 22:42:32
 * @modify date 2024-05-07 22:42:32
 * @desc AI맞춤학습[추천 동영상]
 */
//@Slf4j
@Tag(name="[al] AI맞춤학습[추천 동영상]", description="AI맞춤학습(학생용)")
@RestController
@RequestMapping("/api/v1/al/pl/tcr/ctn")
public class AlPlTcrCtnController {
    //@Autowired
    //private AlPlStuCtnService stuCtnService;

    /**
     * AI맞춤학습 학습창 컨텐츠 목록 조회 요청
     * 
     * 
     */
    @Operation(summary="AI맞춤학습 학습창 컨텐츠 목록 조회")
    @PostMapping(value = "/selectLearningData")
    public ResponseDto<AlPlCtnDto> selectLearningData(AlPlCtnDto lwCtnDto) {
        return Response.ok(AlPlStuCtnService.selectLearningData(lwCtnDto));
    }

    

}
