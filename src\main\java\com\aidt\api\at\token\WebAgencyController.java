package com.aidt.api.at.token;

import com.aidt.api.al.pl.dto.AlPlStuDto;
import com.aidt.api.al.pl.stu.AlPlIniDatStuService;
import com.aidt.api.at.token.dto.*;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.BcCmUtil;
import com.aidt.api.bc.cm.dto.BcUsrInfoDto;
import com.aidt.api.ea.evcom.EaEvComService;
import com.aidt.api.sl.inidat.dto.SlIniDatCondDto;
import com.aidt.api.sl.inidat.tcr.SlIniDatTcrService;
import com.aidt.api.tl.inidat.dto.TlIniDatCondDto;
import com.aidt.api.tl.inidat.tcr.TlIniDatTcrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "[at] WebAgency", description = "WebAgency SSO 인증")
@Slf4j
@RestController
@RequestMapping("/api/v1/at/token")
public class WebAgencyController {

    @Value("${server.meta.textbook.systemCode}")
    private String systemCode;

    @Value("${spring.profiles.active}")
    private String serverActive;

    @Value("${server.meta.textbook.systemCode}")
    private String DB_ID;

    private final KerisService kerisService;

    private final BcCmService bcCmService;

    private final EaEvComService eaEvComService;

    private final TlIniDatTcrService tlIniDatTcrService;

    private final SlIniDatTcrService slIniDatTcrService;

    private final AlPlIniDatStuService alPlIniDatStuService;

    public WebAgencyController(KerisService kerisService,
                               BcCmService bcCmService,
                               EaEvComService eaEvComService,
                               TlIniDatTcrService tlIniDatTcrService,
                               SlIniDatTcrService slIniDatTcrService,
                               AlPlIniDatStuService alPlIniDatStuService) {
        this.kerisService = kerisService;
        this.bcCmService = bcCmService;
        this.eaEvComService = eaEvComService;
        this.tlIniDatTcrService = tlIniDatTcrService;
        this.slIniDatTcrService = slIniDatTcrService;
        this.alPlIniDatStuService = alPlIniDatStuService;
    }

    @PostMapping("/web-agency/update-user-name")
    public ResponseEntity<?> updateUserName(@RequestBody Map<String, String> params) {
        String userId = params.get("userId");
        String newName = params.get("newName");
        int result = bcCmService.updateStudentName(userId, newName);
        return ResponseEntity.ok(result);
    }

    /**
     * 웹전시 연수용 데이터 생성, 학생 이름과 선생님 이름을 params로 받아서 학생들을 생성하고 선생님을 생성하는 API
     * 기존에는 authCode(32) + txbId(3) + 0~9(교사: "t")로 유저를 생성했지만,
     * 연수용 데이터는 더 많은 학생의 생성을 위해 authCode(30: substring) + txbId(3) + 000~999(교사: "ttt")로 생성
     * @param webAgencyDto
     * @return
     */
    public void joinTeacherWithParams(WebAgencyWithParamsDto params) {
        KerisDto kerisDto = Optional.ofNullable(params.getKerisDto())
                .orElseThrow(() -> new IllegalArgumentException("KerisDto is null."));

        int studentCount = params.getStudentCount();
        List<String> userNameList = params.getStudentNameList() == null ? this.getStudentNameByDefault(studentCount) : params.getStudentNameList();
        List<BcUsrInfoDto> bcUsrInfoDtoList = new ArrayList<>(userNameList.size());
        for (int i = 0; i < userNameList.size(); i++) {
            String user_id = params.getAuthCode().substring(0, 30) + params.getTxbId() + String.format("%03d", i);
            String userNumber = (i+1)+"";
            String userName = userNameList.get(i);
            BcUsrInfoDto student = BcUsrInfoDto.builder().user_id(user_id).userName(userName).userNumber(userNumber).build();
            bcUsrInfoDtoList.add(student);
        }

        BcUsrInfoDto teacherBcUserInfoDto = this.makeLectureTcrUserInfo(kerisDto, params.getSchoolId());
        teacherBcUserInfoDto.setTxbId(params.getTxbId());
        teacherBcUserInfoDto.setStuList(bcUsrInfoDtoList);
        teacherBcUserInfoDto.setUsrNm(params.getTeacherName());
        teacherBcUserInfoDto.setUserType("T");
        teacherBcUserInfoDto.setUsrId((params.getAuthCode().substring(0, 30) + params.getTxbId() + "ttt"));

        bcCmService.checkLectureTcrUserInfoWeb(teacherBcUserInfoDto);
    }

    List<String> getStudentNameByDefault(int count) {
        List<String> studentNameList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            studentNameList.add("학생" + (i + 1));
        }
        return studentNameList;
    }

    @Operation(summary = "웹 에이전시 연수용 데이터 생성", description = "웹 에이전시 연수를 위해 데이터를 생성합니다.")
    @PostMapping("/web-agency/join-teacher")
    public ResponseEntity<?> webAgencyJoinTeacher(@RequestBody WebAgencyWithParamsDto params) {

        if(params==null) {
            return ResponseEntity.badRequest().body("webAgencyDto is null");
        }

        if(params.getAuthCode()==null || "".equals(params.getAuthCode())) {
            return ResponseEntity.badRequest().body("authCode is empty");
        }
        if(params.getTxbId()==null || "".equals(params.getTxbId())) {
            return ResponseEntity.badRequest().body("txbId is empty");
        }

        if(params.getAuthCode().length()>32) {
            return ResponseEntity.badRequest().body("authCode is too long");
        }
        if(params.getAuthCode().length()<30) {
            return ResponseEntity.badRequest().body("authCode is too short");
        }

        String profId = params.getAuthCode().substring(0, 30)+params.getTxbId()+"ttt";

        params.setUserId(profId);
        String lectureCode = profId;

        params.setSchoolId(params.getAuthCode().substring(0,10));
        String claId = DB_ID + "-" + lectureCode;


        KerisDto kerisDto = KerisDto.builder()
                .user_id(profId)
                .class_code(lectureCode)
                .lecture_code(lectureCode)
                .build();

        params.setKerisDto(kerisDto);

        // 회원 생성
        BcUsrInfoDto bcUsrInfoDto = BcUsrInfoDto.builder()
                .claId(claId)
                .build();

        boolean addExtra =  params.isAddExtra();
        boolean isTeacherExist = bcCmService.isTeacherExists(bcUsrInfoDto);
        if (!isTeacherExist || addExtra) { // 선생님이 존재하지 않으면 선생님과 학생들을 생성
            this.joinTeacherWithParams(params);
        }

        WebAgencyDto initDataParam = WebAgencyDto.builder()
                .txbId(params.getTxbId())
                .claId(claId)
                .optTxbId(claId)
                .userId(params.getUserId())
                .schoolId(params.getSchoolId())
                .authCode(params.getAuthCode())
                .kerisDto(kerisDto)
                .build();
        // 초기 데이터 생성
        initData(initDataParam, lectureCode);

        // 리다이렉트 응답 반환 (임시주석)
        return ResponseEntity.ok(1L);
    }


    /**
     * 요청 파라미터 정제 및 추출
     * @param request HttpServletRequest 객체
     * @return 정제된 파라미터 맵
     */
    private Map<String, String> extractAndCleanParametersSignIn(HttpServletRequest request) {
        Map<String, String> parameters = new HashMap<>();
        String txbId = this.cleanRequestParameter(request.getParameter("txb_id"));
        String teacherIdKey = this.cleanRequestParameter(request.getParameter("auth_code") + txbId + "t"); // 교사 ID로 key 생성
        String training = this.cleanRequestParameter(request.getParameter("training"));
        if (training == null) {
            training = "false";
        }
        if (training.equals("true")) {
            String sliceAuthCode = this.cleanRequestParameter(request.getParameter("auth_code")).substring(0, 30);
            teacherIdKey = this.cleanRequestParameter(sliceAuthCode + txbId + "ttt"); // 연수용 데이터 생성을 위한 key
        }
        parameters.put("teacherIdKey", teacherIdKey);
        parameters.put("token", this.cleanRequestParameter(request.getParameter("access_token.token")));
        parameters.put("accessId", this.cleanRequestParameter(request.getParameter("access_token.access_id")));
        parameters.put("userId", this.cleanRequestParameter(request.getParameter("user_id")));
        parameters.put("userStatus", "1"); // 기본값 설정
        parameters.put("userType", this.cleanRequestParameter(request.getParameter("user_type")));
        parameters.put("apiDomain", this.cleanRequestParameter(request.getParameter("api_domain")));
        parameters.put("classCode", teacherIdKey); // class_code, lecture_code: 교사의 user_id
        parameters.put("lectureCode", teacherIdKey);
        parameters.put("partnerId", parameters.get("auth_code"));
        parameters.put("logoutUri", this.cleanRequestParameter(request.getParameter("logout_uri")));
        parameters.put("schoolId", teacherIdKey);

        parameters.put("auth_code", this.cleanRequestParameter(request.getParameter("auth_code")));
        parameters.put("signup_method", this.cleanRequestParameter(request.getParameter("signup_method")));
        parameters.put("txb_id", this.cleanRequestParameter(request.getParameter("txb_id")));
        parameters.put("web_user_id", this.cleanRequestParameter(request.getParameter("web_user_id")));
        parameters.put("training", this.cleanRequestParameter(request.getParameter("training")));
        return parameters;
    }

    /**
     * 선생님이 가입하지 않고 학생이 먼저 로그인하려고 했을 때 선생님을 먼저 가입 시키는 API
     * @param webAgencyDto
     * @return
     */
    public void joinTeacher(WebAgencyDto webAgencyDto) {
        KerisDto kerisDto = Optional.ofNullable(webAgencyDto.getKerisDto())
                .orElseThrow(() -> new IllegalArgumentException("KerisDto is null."));
        // 학생 생성
        int studentCount = 10;
        List<BcUsrInfoDto> bcUsrInfoDtoList = new ArrayList<>(studentCount);
        List<String> userNameList = List.of("학생1", "학생2", "학생3", "학생4", "학생5", "학생6", "학생7", "학생8", "학생9", "학생10");
        for (int i = 0; i < studentCount; i++) {
            String user_id = webAgencyDto.getAuthCode() + webAgencyDto.getTxbId() + i;
            String userNumber = (i+1)+"";
            String userName = userNameList.get(i);
            BcUsrInfoDto student = BcUsrInfoDto.builder().user_id(user_id).userName(userName).userNumber(userNumber).build();
            bcUsrInfoDtoList.add(student);
        }

        BcUsrInfoDto teacherBcUserInfoDto = this.makeLectureTcrUserInfo(kerisDto, webAgencyDto.getSchoolId());
        teacherBcUserInfoDto.setStuList(bcUsrInfoDtoList);
        teacherBcUserInfoDto.setUsrNm("이영현");
        teacherBcUserInfoDto.setUserType("T");
        teacherBcUserInfoDto.setTxbId(webAgencyDto.getTxbId());
        teacherBcUserInfoDto.setUsrId((webAgencyDto.getAuthCode() + webAgencyDto.getTxbId() + 't'));

        bcCmService.checkLectureTcrUserInfoWeb(teacherBcUserInfoDto);
    }

    /**
     * 초기 데이터 설정
     * @param webAgencyDto
     * @param teacherBcUserInfoDto
     * @return
     */
    public boolean initData(WebAgencyDto webAgencyDto, String lectureCd) {
        String txbId = BcCmUtil.getTxbID(DB_ID);
        String optTxbId = DB_ID + "-" + lectureCd;

        // 시험 등록 및 관련 데이터 설정
        String usrId = "system";
        String claId = DB_ID + "-" + lectureCd;
        String userId = (webAgencyDto.getAuthCode() + webAgencyDto.getTxbId() + 't');

        // LMS 데이터 등록
        eaEvComService.createLcmsEv(usrId, optTxbId, txbId, claId, DB_ID);

        // 교과 데이터 등록
        TlIniDatCondDto tlIniDatCondDto = TlIniDatCondDto.builder()
                .optTxbId(optTxbId)
                .tcrUsrId(userId)
                .build();
        tlIniDatTcrService.registIniDat(tlIniDatCondDto);

        // 특별학습 데이터 등록
        SlIniDatCondDto slIniDatCondDto = SlIniDatCondDto.builder()
                .optTxbId(optTxbId)
                .tcrUsrId(userId)
                .build();
        slIniDatTcrService.registIniDat(slIniDatCondDto);

        // AI 초기화
        AlPlStuDto alPlStuDto = AlPlStuDto.builder()
                .optTxbId(optTxbId)
                .usrId(usrId)
                .build();
        alPlIniDatStuService.registIniDat(alPlStuDto);

        // 기능 사용 설정 초기데이터
        BcUsrInfoDto teacherBcUsrInfoDto = BcUsrInfoDto.builder()
                .usrId(userId)
                .mdfrId(userId)
                .crtrId(userId)
                .dbId(DB_ID)
                .optTxbId(optTxbId)
                .build();
        bcCmService.checkFncUseSetm(teacherBcUsrInfoDto);

        return true;
    }

    public WebAgencyDto mapToWebAgencyDto(Map<String, String> parameters, KerisDto kerisDto) {
        String teacherKey = parameters.get("teacherIdKey");
        return WebAgencyDto.builder()
                .txbId(parameters.get("txb_id"))
                .claId(teacherKey)
                .optTxbId(teacherKey)
                .userId(parameters.get("userId"))
                .schoolId(teacherKey)
                .authCode(parameters.get("auth_code"))
                .kerisDto(kerisDto)
                .build();
    }
    
    @Operation(summary = "웹 에이전시 생성", description = "웹 에이전시 생성")
    @PostMapping("/web-agency/create")
    public ResponseEntity<Long> webAgencyCreate(@RequestBody WebAgencyDto webAgencyDto) {
    	
    	if(webAgencyDto==null) {
            return ResponseEntity.ok(-1L);
    	}
    	
    	if(webAgencyDto.getAuthCode()==null || "".equals(webAgencyDto.getAuthCode())) {
    		return ResponseEntity.ok(-1L);
    	}
    	if(webAgencyDto.getTxbId()==null || "".equals(webAgencyDto.getTxbId())) {
    		return ResponseEntity.ok(-1L);
    	}
    	
    	if(webAgencyDto.getAuthCode().length()>32) {
    		return ResponseEntity.ok(-2L);
    	}
    	if(webAgencyDto.getAuthCode().length()<11) {
    		return ResponseEntity.ok(-2L);
    	}
    	
    	String profId=webAgencyDto.getAuthCode()+webAgencyDto.getTxbId()+"t";
    	webAgencyDto.setUserId(profId);
    	String lectureCode = profId;
    	

        String txbId = BcCmUtil.getTxbID(DB_ID);
        
        if(!txbId.equals(webAgencyDto.getTxbId())) {
    		return ResponseEntity.ok(-3L);
        }

    	webAgencyDto.setSchoolId(webAgencyDto.getAuthCode().substring(0,10));
        String claId = DB_ID + "-" + lectureCode;
        
        
        KerisDto kerisDto = KerisDto.builder()
        .user_id(profId)
        .class_code(lectureCode)
        .lecture_code(lectureCode)
        .build();
        
        webAgencyDto.setKerisDto(kerisDto);
        
        //회원생성 
        createMember(claId, webAgencyDto);
    	// 초기 데이터 생성 
        initData(webAgencyDto, lectureCode);
    	
        // 리다이렉트 응답 반환 (임시주석)
        return ResponseEntity.ok(1L);
    	
    }
    
    
    /**
     * 회원 체크 및 생성 
     * @param bcUsrInfoDto
     * @param webAgencyDto
     */
    private void createMember(String claId, WebAgencyDto webAgencyDto) {
    	BcUsrInfoDto bcUsrInfoDto = BcUsrInfoDto.builder()
                .claId(claId)
                .build();
    	
    	boolean isTeacherExist = bcCmService.isTeacherExists(bcUsrInfoDto);
        if (!isTeacherExist) { // 선생님이 존재하지 않으면 선생님과 학생들을 생성
            this.joinTeacher(webAgencyDto);
        }
    }

    private boolean isValidTxbId(String txbId) {
    	String txbIdFromDBId = BcCmUtil.getTxbID(DB_ID);
        if (serverActive.equals("dev") || serverActive.equals("local")) {
            return true;
        }
    	return txbId.equals(txbIdFromDBId);
    }

    @Operation(summary = "웹 에이전시 인증", description = "웹 에이전시 인증")
    @GetMapping("/web-agency/sign-in")
    public ResponseEntity<?> webAgencySignIn(HttpServletRequest request) {
//        if (!validationToken(request.getParameter("access_token.token"))) {
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid token");
//        }
        // 도메인 결정
        String domain = determineDomain();
        
        log.debug("웹 에이전시 인증 : " + domain );
        if (domain == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid server active");
        }

        // URL 유효성 검사
        String url = request.getRequestURL().toString();
        if (!validateUrl(url)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid URL format");
        }

        // 파라미터 정제 및 추출
        Map<String, String> parameters = extractAndCleanParametersSignIn(request);
        if (parameters.get("auth_code") == null || parameters.get("auth_code").isEmpty() || parameters.get("auth_code").length() > 32) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid auth_code");  // auth code 검증
        }

        // KerisDto 생성 및 서비스 호출
        KerisDto kerisDto = createKerisDto(parameters);

        // TODO: 유저 확인 및 생성

        String lectureCd = parameters.get("lectureCode");
        String claId = DB_ID + "-" + lectureCd;
        WebAgencyDto webAgencyDto = mapToWebAgencyDto(parameters, kerisDto);
        
        //txbid  정상여부 체크 
        if (!isValidTxbId(webAgencyDto.getTxbId())) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid txbId check!! page txbid : " + webAgencyDto.getTxbId() + " db_id : "+DB_ID );
        }

        
        this.createMember(claId, webAgencyDto);
        this.initData(mapToWebAgencyDto(parameters, kerisDto), webAgencyDto.getKerisDto().getLecture_code());
        
        kerisService.loginAndUsrCheck(kerisDto);

        // 리다이렉트 URL 생성
        String redirectUrl = buildRedirectUrl(domain, parameters);

        // 리다이렉트 응답 반환 (임시주석)
        return ResponseEntity.ok(redirectUrl);
//                .status(HttpStatus.FOUND) // HTTP 302 상태
//                .header("Location", redirectUrl) // 리다이렉트 URL 설정
//                .build();
    }

    /**
     * URL 유효성 검사
     * @param url 검증할 URL
     * @return 유효 여부
     */
    private boolean validateUrl(String url) {
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            log.error("KERIS 호출받은 URL 추출 실패!", e);
            return false;
        }
    }

    /**
     * 서버 환경에 따라 도메인 결정
     * @return 도메인 URL
     */
    private String determineDomain() {
        switch (serverActive) {
            case "local":
                return "http://localhost:9080";
            case "dev":
                return "https://dev.aitextbook.co.kr";
            case "prod":
                /**
                 * 2025.02.21 부하 테스트를 위해서 추가
                 *
                 * 부하 테스트:
                 *  웹전시 개발서버 -> LMS 운영서버로 계정 생성 및 기능 테스트 예정
                 *
                 *  main에서는
                 *  "https://" + systemCode + ".aitextbook.co.kr"
                 *  나머지는
                 *  "https://" + systemCode + "-w.aitextbook.co.kr"
                 */
                return "https://" + systemCode + ".aitextbook.co.kr";
            default:
                return null;
        }
    }

    /**
     * KerisDto 생성
     * @param parameters 정제된 파라미터 맵
     * @return KerisDto 객체
     */
    private KerisDto createKerisDto(Map<String, String> parameters) {
        return KerisDto.builder()
                .user_id(parameters.get("userId"))
                .user_status(parameters.get("userStatus"))
                .user_type(parameters.get("userType").toUpperCase())
                .lecture_code(parameters.get("lectureCode"))
                .kerisUsrId(parameters.get("userId"))
                .build();
    }

    /**
     * 리다이렉트 URL 생성
     * @param domain 도메인 URL
     * @param parameters 정제된 파라미터 맵
     * @return 리다이렉트 URL
     */
    private String buildRedirectUrl(String domain, Map<String, String> parameters) {
        return String.format(
                "%s/webAgency?access_token.token=%s&access_token.access_id=%s&user_id=%s&user_status=%s&user_type=%s&api_domain=%s&class_code=%s&lecture_code=%s&partnerId=%s&webAgency=%s&web_user_id=%s&training=%s",
                domain,
                parameters.get("token"),
                parameters.get("accessId"),
                parameters.get("userId"),
                parameters.get("userStatus"),
                parameters.get("userType"),
                parameters.get("apiDomain"),
                parameters.get("classCode"),
                parameters.get("lectureCode"),
                parameters.get("partnerId"),
                1, // 웹 에이전시 표시값
                parameters.get("web_user_id"),
                parameters.get("training")
        );
    }

    /**
     * 요청 파라미터 정제
     * @param param 요청 파라미터
     * @return 정제된 문자열
     */
    private String cleanRequestParameter(String param) {
        return param == null ? "" : param.replaceAll("\r", "").replaceAll("\n", "").trim();
    }

    /**
     * 기본값을 설정하여 파라미터 반환
     * @param value 파라미터 값
     * @param defaultValue 기본값
     * @return 값 또는 기본값
     */
    private String getOrDefault(String value, String defaultValue) {
        return StringUtils.isNotBlank(value) ? value : defaultValue;
    }


    private BcUsrInfoDto makeLectureTcrUserInfo(KerisDto kerisDto, String schoolId) {
        return BcUsrInfoDto
                .builder()
                .lectureCd(kerisDto.getLecture_code())
                .usrId(kerisDto.getUser_id())
                .userType(kerisDto.getUser_type())
                .schlCd(schoolId.substring(0, 10))
                .classCode(kerisDto.getClass_code())
                .build();
    }

    /**
     * 토큰 유효성 검사
     * @param token
     * @return
     */
    private boolean validationToken(String token) {
        try {
            String decode = URLDecoder.decode(token, StandardCharsets.UTF_8);
            String plaintText = AesEncryptionUtil.decrypt(decode);
            String[] list = plaintText.split("_");
            if(list.length == 0) {
                return false;
            }

            LocalDateTime parsedTime = LocalDateTime.parse(list[2]);

            if ("chunjae".equals(list[0]) && this.isPastFiveMinutes(parsedTime)) {
                return true;
            }
        }catch (Exception e) {
            e.printStackTrace();
        }

        return false;

    }

    /**
     * 현재 시간과 입력 시간을 비교하여 5분 이상 지났는지 확인
     * @param inputTime
     * @return
     */
    private boolean isPastFiveMinutes(LocalDateTime inputTime) {
        LocalDateTime currentTime = LocalDateTime.now(ZoneId.of("Asia/Seoul"));
        return currentTime.isBefore(inputTime.plusMinutes(5));
    }

    private String extractToken(String token) {
        return token.split("_")[0];

    }

}


