package com.aidt.api.ea.evcom.lansnte.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-03-29 14:03:오후 2:03
 * @modify date 2024-03-29 14:03:오후 2:03
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
public class CreateQuesSimilarDiyDto {

    @Parameter(name="운영 교과서 ID")
    private String optTxbId;

    @Parameter(name="사용자 ID")
    private String usrId;

    @Parameter(name="실수 OR 몰라서 구분")
    @NotNull
    private String type;

    @Parameter(name="평가 ID")
    @NotNull
    private long evId;
    
    @Parameter(name = "응시 회차")
    private int txmPn;    

    @Parameter(name="평가지 명")
    private String evNm;

    @Parameter(name="DB ID")
    private String dbId;

    @Parameter(name="단원 별 오답 문항 list")
    @NotNull
    private List<EaLansNteReqDto> qpList;

    @Parameter(name="문항 ID")
    private String qtmId;

    @Parameter(name="문항 플랫폼 난이도 구분 코드")
    private String qtmDffdDvCd;

	
	@Parameter(name="버킷url")
	private String bucketUrl;
}
