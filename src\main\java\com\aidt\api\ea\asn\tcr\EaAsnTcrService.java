package com.aidt.api.ea.asn.tcr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.aidt.api.al.pl.dto.AlMluTcLstInqTcrReqDto;
import com.aidt.api.al.pl.tcr.AlPlTcrService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.aidt.api.al.pl.cm.mg.AlMgService;
import com.aidt.api.bc.cm.BcCmService;
import com.aidt.api.bc.cm.dto.BcUserInfoDto;
import com.aidt.api.bc.cm.dto.CmClaCpLogDto;
import com.aidt.api.bc.inf.infCom.InfComService;
import com.aidt.api.bc.inf.infCom.dto.InfComDto;
import com.aidt.api.ea.asn.tcr.dto.EaAnsCopyFleDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto.grpUserInfo;
import com.aidt.api.ea.asn.tcr.dto.EaAsnTcrDto.userName;
import com.aidt.api.ea.asncom.EaAsnComService;
import com.aidt.api.ea.asncom.dto.EaAsnFleDto;
import com.aidt.common.CommonDao;
import com.aidt.common.CommonUserDetail;
import com.aidt.common.JwtProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-01-12
 * @modify date 2024-01-12
 * @desc 과제 - 교사 Service
 */
@Slf4j
@Service
public class EaAsnTcrService {

	private final String MAPPER_NAMESPACE = "api.ea.asn.tcr.";

	@Autowired
	private CommonDao commonDao;

	@Autowired
	private PlatformTransactionManager transactionManager;

	@Autowired
	private JwtProvider jwtProvider;

	@Autowired
	private AlMgService alMgService;

	@Autowired
	private BcCmService bcCmService;

	@Autowired
	private EaAsnComService eaAsnComService;

	@Autowired
	private InfComService infComService;

	@Autowired
	private AlPlTcrService alPlTcrService;

	// Return Object
	List<EaAsnTcrDto> responseList = new ArrayList<>();

	/**
	 * 과제 목록 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public List<EaAsnTcrDto> selectAsnTcrList(EaAsnTcrDto eaAsnTcrDto) {
		List<EaAsnTcrDto> responseTcrList = new ArrayList<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());

		if (!StringUtils.isEmpty(eaAsnTcrDto.getSearchOption())) {
			//ai 지식맵ID 조회
			List<String> kmmpNodIds = alMgService.selectKmmpNodIdByLrmpNodId(eaAsnTcrDto.getSearchOption());
			eaAsnTcrDto.setAiSearchOptionList(kmmpNodIds);
		}

		// 과제 목록 조회
		responseTcrList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnTcrList", eaAsnTcrDto);
		if (responseTcrList == null) {
			responseTcrList = new ArrayList<>();
		}
		return responseTcrList;
	}

	/**
	 * 과제 상세 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectAsnTcrDetail(EaAsnTcrDto eaAsnTcrDto) {

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		Map<String, Object> result = new HashMap<>();
		List<EaAsnFleDto> fileList = new ArrayList<>(); // 파일DTO 생성필요

		// 과제 상세 조회
		EaAsnTcrDto resDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnTcrDetail", eaAsnTcrDto);
		if (resDto == null) {
			result.put("resultCode", "FAIL");
			result.put("resultMsg", "과제 정보를 확인할 수 없습니다.");
			return result;
		}
		result.put("resTcr", resDto);

		// 첨부ID가 있을 경우
		if (resDto.getAnnxId() > 0) {
			eaAsnTcrDto.setAnnxId(resDto.getAnnxId());

			//파일 조회
			String annxId = String.valueOf(resDto.getAnnxId());
			fileList = eaAsnComService.selectFile(annxId);

			result.put("fileList", fileList);
		}

		return result;
	}

	/**
	 * 과제 상세 조회 - 학생별 현황 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectAsnTcrDetailStuList(EaAsnTcrDto eaAsnTcrDto) {
		List<EaAsnTcrDto> responseDetailList = new ArrayList<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		Map<String, Object> result = new HashMap<>();

		//교사 상세 - 학생별 현황 조회 목록
		responseDetailList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnTcrDetailStuList", eaAsnTcrDto);

		result.put("stuList", responseDetailList);

		return result;
	}

	/**
	 * 유저가 5초 이내에 게시글을 등록했는지 확인합니다.
	 * @param String usrId
	 * @return boolean
	 */
	public boolean isUserPostingTooQuickly(String usrId) {
		Long timeDiff = commonDao.select(MAPPER_NAMESPACE + "selectUsrLstEaAsnTmDff", usrId);
		return timeDiff != null && timeDiff < 10;
	}

	/**
	 * 과제 출제 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	@Transactional
	public Map<String, Object> insertAsnTcr(EaAsnTcrDto eaAsnTcrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		if (StringUtils.isEmpty(eaAsnTcrDto.getPkgAsnYn()) && isUserPostingTooQuickly(userDetails.getUsrId())) {
			throw new RuntimeException("게시글을 연속으로 등록할 수 없습니다.");
		}
		eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		if (StringUtils.isEmpty(eaAsnTcrDto.getPkgAsnYn()) || !"Y".equals(eaAsnTcrDto.getPkgAsnYn())) {
			eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		}

		eaAsnTcrDto.setCrtrId(userDetails.getUsrId());
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		eaAsnTcrDto.setDbId(userDetails.getTxbId());

		Map<String, Object> result = new HashMap<>();
		int cnt = 0;

		//		DefaultTransactionDefinition DTD = new DefaultTransactionDefinition();
		//		DTD.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		//		TransactionStatus status = transactionManager.getTransaction(DTD);

		// 마감이후제출가능여부 - 화면에서 미사용 -확인필요

		// 과제 출제 시 중복 체크 로직 추가 - 2차 방어 (AI맞춤학습/우리반수업)
		if ("AL".equals(eaAsnTcrDto.getLrnTpCd()) || "TL".equals(eaAsnTcrDto.getLrnTpCd())) {
			int compareRes = commonDao.select(MAPPER_NAMESPACE + "compareAsnStuCount", eaAsnTcrDto);
			if (compareRes == 1) {
				result.put("error", true);
				return result;
			}
		}

		//EA_과제 저장
		cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsn", eaAsnTcrDto);

		if (eaAsnTcrDto.getAsnId() != null) {

			//일반 or 모둠 이면서 단원,차시가 있는 경우
			if (("GE".equals(eaAsnTcrDto.getAsnTpCd()) || "GR".equals(eaAsnTcrDto.getAsnTpCd()))
				&& (!StringUtils.isEmpty(eaAsnTcrDto.getLuNodId()) || !StringUtils.isEmpty(eaAsnTcrDto.getTcNodId()))) {
				//				eaAsnTcrDto.setTcrUsrId(null);
				//EA_과제범위 저장
				cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsnRnge", eaAsnTcrDto);
			}
			// 모둠 과제가 아닐 때
			// 모둠 과제는 따로 저장
			if (!"".equals(eaAsnTcrDto.getAsnTpCd()) && (!"GR".equals(eaAsnTcrDto.getAsnTpCd()))) {
				if (eaAsnTcrDto.getUsrIdList() != null && !eaAsnTcrDto.getUsrIdList().isEmpty()) {
					//EA_과제제출 저장
					cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsnSmt", eaAsnTcrDto);
				}

			}

			// 교과,AI 과제 인 경우
			if (!"".equals(eaAsnTcrDto.getLrnTpCd()) && ("TL".equals(eaAsnTcrDto.getLrnTpCd()) ||
				"AL".equals(eaAsnTcrDto.getLrnTpCd()))) {

				//EA_과제범위 저장
				cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsnRnge", eaAsnTcrDto);

				EaAsnTcrDto cmplStuDto = new EaAsnTcrDto();
				//학습과제 완료 여부 조회
				if ("TL".equals(eaAsnTcrDto.getLrnTpCd())) {
					for (String userId : eaAsnTcrDto.getUsrIdList()) {
						eaAsnTcrDto.setStuUsrId(userId);
						if("EX".equals(eaAsnTcrDto.getLrnStpDvCd())) {
							//평가 완료 여부
							String exCmplYn = commonDao.select(MAPPER_NAMESPACE + "selectExCmplYn", eaAsnTcrDto);
							exCmplYn = exCmplYn == null ? "N" : exCmplYn;
							if("Y".equals(exCmplYn)) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						} else {
							//우리반 학습
							EaAsnTcrDto tlEaAsnDto = commonDao.select(MAPPER_NAMESPACE + "selectLrnCompt", eaAsnTcrDto);
							
							if (tlEaAsnDto != null && eaAsnTcrDto.getTtlLrnCnt() == tlEaAsnDto.getCmplCnt()) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}	
						}
					}

				} else if ("AL".equals(eaAsnTcrDto.getLrnTpCd())) {
					if("MA".equals(eaAsnTcrDto.getSbjCd())) {
						for (String userId : eaAsnTcrDto.getUsrIdList()) {
							eaAsnTcrDto.setStuUsrId(userId);
							//해야하는 AI 맞춤학습 개수
							int totCnt = commonDao.select(MAPPER_NAMESPACE + "selectAiTotCnt", eaAsnTcrDto);
							//완료한 AI 맞춤학습 개수
							int cmplCnt = commonDao.select(MAPPER_NAMESPACE + "selectAiCmplCnt", eaAsnTcrDto);

							if (totCnt != 0 && totCnt == cmplCnt) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						}	
					} else {
						for (String userId : eaAsnTcrDto.getUsrIdList()) {
							eaAsnTcrDto.setStuUsrId(userId);
							//AI 맞춤학습
							List<EaAsnTcrDto> alEaAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiCompt",
								eaAsnTcrDto);
							if (alEaAsnList.isEmpty()) {
								continue;
							}

							boolean allCompleted = true;
							for (EaAsnTcrDto dto : alEaAsnList) {
								String luevCmplYn = dto.getLuevCmplYn();
								if ("X".equals(luevCmplYn)) {
									// luev_cmpl_yn이 'X'인 경우는 무시
									continue;
								}
								if (luevCmplYn == null || "N".equals(luevCmplYn)) {
									// luev_cmpl_yn이 null이거나 'N'인 경우
									allCompleted = false;
									break;
								}
							}

							if (allCompleted) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						}	
					}
				}
				//학습과제 완료 학생인 경우 과제 출제 후 즉시 과제완료 처리
				if (cmplStuDto.getUsrIdList() != null && cmplStuDto.getUsrIdList().size() > 0) {
					cmplStuDto.setAsnId(eaAsnTcrDto.getAsnId());
					cmplStuDto.setCmplLrnCnt(eaAsnTcrDto.getTtlLrnCnt());
					cmplStuDto.setMdfrId(userDetails.getUsrId());
					commonDao.update(MAPPER_NAMESPACE + "updateLrnAsn", cmplStuDto);
				}

			}

			// 특별 과제 인 경우
			if (!"".equals(eaAsnTcrDto.getLrnTpCd()) && "SL".equals(eaAsnTcrDto.getLrnTpCd())) {
				//EA_과제범위 저장
				cnt += commonDao.insert(MAPPER_NAMESPACE + "insertSlAsnRnge", eaAsnTcrDto);
			}

			// 모둠 과제 인 경우
			if (!"".equals(eaAsnTcrDto.getAsnTpCd()) && "GR".equals(eaAsnTcrDto.getAsnTpCd())) {
				// 해당 모둠 그룹의 모둠팀, 인원, 팀 수 조회
				List<grpUserInfo> grpInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpInfo", eaAsnTcrDto);
				eaAsnTcrDto.setGrpUserInfo(grpInfo);
				//				eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());

				// 해당 모둠 그룹의 팀 수 조회
				int temCnt = commonDao.select(MAPPER_NAMESPACE + "selectTemCnt", eaAsnTcrDto);

				// 해당 모둠 그룹의 인원수 만큼 EA_ASN_SMT Table Insert
				cnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpAsn", eaAsnTcrDto);

				// 해당 모둠 그룹의 모둠팀 수 만큼 EA_GRP_ASN_SMT Table Insert
				for (int i = 0; i < temCnt; i++) {
					eaAsnTcrDto.setGrpTemId(i + 1);

					// EA_GRP_ASN_SMT Table Insert
					cnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpAsnSmt", eaAsnTcrDto);

					// 모둠별 게시판 생성 체크했을 때 (0 - 미생성 / 1 - 생성)
					if (!"".equals(String.valueOf(eaAsnTcrDto.getCreateBlbdAll()))
						&& !"0".equals(String.valueOf(eaAsnTcrDto.getCreateBlbdAll()))
						&& eaAsnTcrDto.getCreateBlbdAll() == 1) {
						// EA_GRP_BLBD Table Insert
						cnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpBoard", eaAsnTcrDto);
					}
				}
			}

		}

		result.put("cnt", cnt);
		result.put("resData", eaAsnTcrDto);

		return result;
	}

	/**
	 * 과제 평가 저장 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	@Transactional
	public int updateAsnEv(List<EaAsnTcrDto> evList) {
		int resCnt = 0;
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		//과제 평가 저장
		for (EaAsnTcrDto eaAsnTcrDto : evList) {
			if ("GE".equals(eaAsnTcrDto.getAsnTpCd())) { // 일반
				eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
				eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
				eaAsnTcrDto.setMdfrId(userDetails.getUsrId());

				resCnt = commonDao.update(MAPPER_NAMESPACE + "updateAsnEv", eaAsnTcrDto);
			} else if ("GR".equals(eaAsnTcrDto.getAsnTpCd())) { // 모둠
				eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
				eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
				eaAsnTcrDto.setMdfrId(userDetails.getUsrId());

				resCnt = commonDao.update(MAPPER_NAMESPACE + "updateAsnGrpEv", eaAsnTcrDto);

				// Update EA_ANS_SMT Table
				resCnt += commonDao.update(MAPPER_NAMESPACE + "saveGrpScFbAsnSmtTb", eaAsnTcrDto);
			}
		}

		return resCnt;
	}

	/**
	 * 과제 수정 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	@Transactional
	public int updateAsnTcr(EaAsnTcrDto eaAsnTcrDto) {
		int resCnt = 0;
		int cnt = -1;
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		if (StringUtils.isEmpty(eaAsnTcrDto.getPkgAsnYn()) || !"Y".equals(eaAsnTcrDto.getPkgAsnYn())) {
			eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		}
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		eaAsnTcrDto.setDbId(userDetails.getTxbId());
		eaAsnTcrDto.setCrtrId(userDetails.getUsrId());

		EaAsnTcrDto eaDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnTcrDetail", eaAsnTcrDto);
		if (eaDto == null || eaDto.getAsnId() == null) {
			return resCnt;
		}

		//EA_과제 수정
		resCnt += commonDao.update(MAPPER_NAMESPACE + "updateAsn", eaAsnTcrDto);

		// 일반 or 모둠 단원 차시 수정
		if (("GE".equals(eaAsnTcrDto.getAsnTpCd()) || "GR".equals(eaAsnTcrDto.getAsnTpCd()))) {
			EaAsnTcrDto eaNod = commonDao.select(MAPPER_NAMESPACE + "selectNodId", eaAsnTcrDto);

			if (eaNod == null) {
				if (!StringUtils.isEmpty(eaAsnTcrDto.getLuNodId())) {
					eaAsnTcrDto.setDelYn("N");
					//EA_과제범위 저장
					resCnt += commonDao.update(MAPPER_NAMESPACE + "insertAsnRnge", eaAsnTcrDto);
				}
			} else {
				//2025.02.25 삭제 여부에 따라 삭져여부 변경에서  - > 데이터 삭제 처리로 수정
				if (!StringUtils.isEmpty(eaNod.getLuNodId()) && StringUtils.isEmpty(eaAsnTcrDto.getLuNodId())) {
					//EA_과제범위 삭제
					resCnt += commonDao.update(MAPPER_NAMESPACE + "deleteAsnRnge", eaAsnTcrDto);
				} else {
					//EA_과제범위 수정
					resCnt += commonDao.update(MAPPER_NAMESPACE + "updateAsnRnge", eaAsnTcrDto);
				}

			}

			/**
			 * 과제 수정 변경에 따른  개발중 2025.02.05  
			 * 일반, 모둠 과제만 해당 ,평가방식 변경 시 학생 평가 초기화
			 */
			if (StringUtils.isNotEmpty(eaDto.getEvMthdTpCd()) && StringUtils.isNotEmpty(eaAsnTcrDto.getEvMthdTpCd()) &&
				!eaDto.getEvMthdTpCd().equals(eaAsnTcrDto.getEvMthdTpCd())) {
				//해당과제의 과제제출 학생 평가 초기화
				commonDao.update(MAPPER_NAMESPACE + "updateSmt", eaAsnTcrDto);

				// 모둠일 경우 해당과제 모둠과제제출 학생 평가 초기화
				if ("GR".equals(eaAsnTcrDto.getAsnTpCd())) {
					commonDao.update(MAPPER_NAMESPACE + "updateGrpSmt", eaAsnTcrDto);
				}
			}
		}

		// 모둠 과제일 경우
		if ("GR".equals(eaAsnTcrDto.getAsnTpCd())) {

			EaAsnTcrDto grpDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnTcrDetail", eaAsnTcrDto);
			eaAsnTcrDto.setOriginGrpId(grpDto.getGrpId());
			eaAsnTcrDto.setTcrUsrId(grpDto.getTcrUsrId());

			//기존 그룹ID와 비교필요
			if (!grpDto.getGrpId().equals(eaAsnTcrDto.getGrpId())) {
				//EA_모둠과제제출 삭제
				resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpAsnSmt", eaAsnTcrDto);
				// EA_과제제출 삭제
				resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmt", eaAsnTcrDto);

				int blbdCount = commonDao.select(MAPPER_NAMESPACE + "selectBoardCnt", eaAsnTcrDto);
				if (blbdCount > 0) {
					//2025.02.25 삭제 여부에 따라 삭져여부 변경에서  - > 데이터 삭제 처리로 수정
					//모둠 게시판 댓글 삭제
					resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoardUcwr", eaAsnTcrDto);

					//모둠 게시판 게시글 삭제
					resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoardBlwr", eaAsnTcrDto);

					//모둠 게시판 삭제
					resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoard", eaAsnTcrDto);
				}

				// 해당 모둠 그룹의 모둠팀, 인원, 팀 수 조회
				List<grpUserInfo> grpInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpInfo", eaAsnTcrDto);
				eaAsnTcrDto.setGrpUserInfo(grpInfo);

				List<EaAsnTcrDto> grpInfo_1 = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpInfo", eaAsnTcrDto);
				// 학생 ID만 담을 배열
				List<String> userIdList = new ArrayList<>();

				for (int i = 0; i < grpInfo_1.size(); i++) {
					userIdList.add(grpInfo_1.get(i).getStuUsrId());
				}

				eaAsnTcrDto.setUsrIdList(userIdList);

				//EA_과제제출 저장
				resCnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsnSmt", eaAsnTcrDto);

				// 해당 모둠 그룹의 팀 수 조회
				int temCnt = commonDao.select(MAPPER_NAMESPACE + "selectTemCnt", eaAsnTcrDto);

				// 해당 모둠 그룹의 모둠팀 수 만큼 EA_GRP_ASN_SMT Table Insert
				for (int i = 0; i < temCnt; i++) {
					eaAsnTcrDto.setGrpTemId(i + 1);

					// EA_GRP_ASN_SMT Table Insert
					resCnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpAsnSmt", eaAsnTcrDto);

					// 모둠별 게시판 생성 체크했을 때 (0 - 미생성 / 1 - 생성)
					if (eaAsnTcrDto.getCreateBlbdAll() > 0) {
						if (i == 0) {
							// 선택한 모둠 그룹의 해당 과제에 대해 모둠 게시판 존재 여부 체크
							// cnt가 0이면 게시판 생성, 아니면 pass
							cnt = commonDao.select(MAPPER_NAMESPACE + "selectBoardCnt", eaAsnTcrDto);
						}

						if (cnt == 0) {
							// EA_GRP_BLBD Table Insert
							commonDao.insert(MAPPER_NAMESPACE + "insertGrpBoard", eaAsnTcrDto);
						}
					}
				}
			} else {
				if (eaAsnTcrDto.getCreateBlbdAll() > 0) {
					// 해당 모둠 그룹의 팀 수 조회
					int temCount = commonDao.select(MAPPER_NAMESPACE + "selectTemCnt", eaAsnTcrDto);

					for (int i = 0; i < temCount; i++) {
						eaAsnTcrDto.setGrpTemId(i + 1);
						if (i == 0) {
							// 선택한 모둠 그룹의 해당 과제에 대해 모둠 게시판 존재 여부 체크
							// cnt가 0이면 게시판 생성, 아니면 pass
							cnt = commonDao.select(MAPPER_NAMESPACE + "selectBoardCnt", eaAsnTcrDto);
						}
						if (cnt == 0) {
							//모둠 게시판 등록
							resCnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpBoard", eaAsnTcrDto);
						}
					}
				} else {
					//2025.02.25 삭제 여부에 따라 삭져여부 변경에서  - > 데이터 삭제 처리로 수정
					//모둠 게시판 댓글 삭제
					resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoardUcwr", eaAsnTcrDto);

					//모둠 게시판 게시글 삭제
					resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoardBlwr", eaAsnTcrDto);

					//모둠 게시판 삭제
					resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoard", eaAsnTcrDto);
				}
			}
		} else {
			//일괄 과제 수정이 아닐때만 EA_과제제출 수정진행 
			if (StringUtils.isEmpty(eaAsnTcrDto.getPkgAsnYn()) || !"Y".equals(eaAsnTcrDto.getPkgAsnYn())) {
				// 교사가 과제를 수정 할 경우 과제제출테이블 데이터 삭제 후 새로 저장 2024.03.04
				// EA_과제제출 삭제
				resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmt", eaAsnTcrDto);
				
				if(eaAsnTcrDto.getUsrIdList() != null && eaAsnTcrDto.getUsrIdList().size() > 0) {
					//EA_과제제출 저장
					resCnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsnSmt", eaAsnTcrDto);
				}
				
			}

		}
		
		// 교과,AI 과제 인 경우
		if (!"".equals(eaAsnTcrDto.getLrnTpCd())
				&& ("TL".equals(eaAsnTcrDto.getLrnTpCd()) || "AL".equals(eaAsnTcrDto.getLrnTpCd()))) {

			if (eaAsnTcrDto.getAddStuList() != null && eaAsnTcrDto.getAddStuList().size() > 0) {
				EaAsnTcrDto cmplStuDto = new EaAsnTcrDto();
				// 학습과제 완료 여부 조회
				if ("TL".equals(eaAsnTcrDto.getLrnTpCd())) {
					for (String userId : eaAsnTcrDto.getAddStuList()) {
						eaAsnTcrDto.setStuUsrId(userId);
						if ("EX".equals(eaAsnTcrDto.getLrnStpDvCd())) {
							// 평가 완료 여부
							String exCmplYn = commonDao.select(MAPPER_NAMESPACE + "selectExCmplYn", eaAsnTcrDto);
							exCmplYn = exCmplYn == null ? "N" : exCmplYn;
							if ("Y".equals(exCmplYn)) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						} else {
							// 우리반 학습
							EaAsnTcrDto tlEaAsnDto = commonDao.select(MAPPER_NAMESPACE + "selectLrnCompt", eaAsnTcrDto);
							eaAsnTcrDto.setTtlLrnCnt(commonDao.select(MAPPER_NAMESPACE + "selectTtlLrnCnt", eaAsnTcrDto));
							if (tlEaAsnDto != null && eaAsnTcrDto.getTtlLrnCnt() == tlEaAsnDto.getCmplCnt()) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						}
					}

				} else if ("AL".equals(eaAsnTcrDto.getLrnTpCd())) {
					if ("MA".equals(eaAsnTcrDto.getSbjCd())) {
						for (String userId : eaAsnTcrDto.getAddStuList()) {
							eaAsnTcrDto.setStuUsrId(userId);
							// 해야하는 AI 맞춤학습 개수
							int totCnt = commonDao.select(MAPPER_NAMESPACE + "selectAiTotCnt", eaAsnTcrDto);
							// 완료한 AI 맞춤학습 개수
							int cmplCnt = commonDao.select(MAPPER_NAMESPACE + "selectAiCmplCnt", eaAsnTcrDto);

							if (totCnt != 0 && totCnt == cmplCnt) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						}
					} else {
						for (String userId : eaAsnTcrDto.getAddStuList()) {
							eaAsnTcrDto.setStuUsrId(userId);
							// AI 맞춤학습
							List<EaAsnTcrDto> alEaAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectAiCompt",
									eaAsnTcrDto);
							if (alEaAsnList.isEmpty()) {
								continue;
							}

							boolean allCompleted = true;
							for (EaAsnTcrDto dto : alEaAsnList) {
								String luevCmplYn = dto.getLuevCmplYn();
								if ("X".equals(luevCmplYn)) {
									// luev_cmpl_yn이 'X'인 경우는 무시
									continue;
								}
								if (luevCmplYn == null || "N".equals(luevCmplYn)) {
									// luev_cmpl_yn이 null이거나 'N'인 경우
									allCompleted = false;
									break;
								}
							}

							if (allCompleted) {
								if (cmplStuDto.getUsrIdList() == null) {
									cmplStuDto.setUsrIdList(new ArrayList<>());
								}
								cmplStuDto.getUsrIdList().add(userId);
							}
						}
					}
				}
				// 학습과제 완료 학생인 경우 과제 출제 후 즉시 과제완료 처리
				if (cmplStuDto.getUsrIdList() != null && cmplStuDto.getUsrIdList().size() > 0) {
					cmplStuDto.setAsnId(eaAsnTcrDto.getAsnId());
					cmplStuDto.setCmplLrnCnt(eaAsnTcrDto.getTtlLrnCnt());
					cmplStuDto.setMdfrId(userDetails.getUsrId());
					commonDao.update(MAPPER_NAMESPACE + "updateLrnAsn", cmplStuDto);
				}

			}
		}
		return resCnt;
	}

	/**
	 * 과제 삭제 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	@Transactional
	public int deleteAsnTcr(EaAsnTcrDto eaAsnTcrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();

		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		int resCnt = 0;

		// 과제 상세 조회
		EaAsnTcrDto resDto = commonDao.select(MAPPER_NAMESPACE + "selectAsnTcrDetail", eaAsnTcrDto);
		if (resDto == null) {
			return resCnt;
		}

		//과제 삭제
		resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsn", eaAsnTcrDto);

		//과제 제출 삭제
		resCnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmt", eaAsnTcrDto);

		//2025.02.25 삭제 여부에 따라 삭져여부 변경에서  - > 데이터 삭제 처리로 수정
		//EA_과제범위 삭제
		resCnt += commonDao.update(MAPPER_NAMESPACE + "deleteAsnRnge", eaAsnTcrDto);

		return resCnt;
	}

	/**
	 * 학생 별 과제 상세 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectStuAsnDetail(EaAsnTcrDto eaAsnTcrDto) {
		List<EaAsnTcrDto> responseStuDetailList = new ArrayList<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		Map<String, Object> result = new HashMap<>();
		List<EaAsnFleDto> fileList = new ArrayList<>(); // 
		EaAsnTcrDto stuAsnDetail = new EaAsnTcrDto();

		// 학생 별 과제 상세 조회
		responseStuDetailList = commonDao.selectList(MAPPER_NAMESPACE + "selectAsnTcrDetailStuList", eaAsnTcrDto);
		if (responseStuDetailList.isEmpty()) {
			result.put("resCd", "Fail");
			result.put("resMsg", "과제 정보를 확인할 수 없습니다.");
			return result;
		}

		stuAsnDetail = responseStuDetailList.get(0);
		result.put("resStuAsnDto", stuAsnDetail);

		// 첨부ID가 있을 경우
		if (!StringUtils.isEmpty(eaAsnTcrDto.getStuUsrId()) && stuAsnDetail.getAnnxId() > 0) {
			eaAsnTcrDto.setAnnxId(stuAsnDetail.getAnnxId());

			//파일 조회
			String annxId = String.valueOf(stuAsnDetail.getAnnxId());
			fileList = eaAsnComService.selectFile(annxId);
			if (fileList.isEmpty()) {
				result.put("resultCode", "Fail");
				return result;
			}
			result.put("fileList", fileList);
		}

		return result;
	}

	/**
	 * 과제 중복 학생 목록 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> dplctAsnStuList(EaAsnTcrDto eaAsnTcrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		eaAsnTcrDto.setDbId(userDetails.getTxbId());
		Map<String, Object> result = new HashMap<>();

		if (eaAsnTcrDto.getLrnTpCd() != null) {
			// 과제 중복체크 학생 목록 조회
			List<EaAsnTcrDto> dplctAsnStuList = commonDao.selectList(MAPPER_NAMESPACE + "dplctAsnStuList", eaAsnTcrDto);
			if (dplctAsnStuList.size() > 0) {
				result.put("dplctAsnStuList", dplctAsnStuList);
			}

		}

		return result;
	}

	/**
	 * 우리 반 수업 액티비티 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectAtvIdList(EaAsnTcrDto eaAsnTcrDto) {

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		Map<String, Object> result = new HashMap<>();

		List<EaAsnTcrDto> atvIdList = commonDao.selectList(MAPPER_NAMESPACE + "selectAtvIdList", eaAsnTcrDto);
		if (atvIdList.size() > 0) {
			result.put("atvIdList", atvIdList);
		}

		return result;
	}

	/**
	 *  모둠 과제 수정
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	@Transactional
	public Map<String, Object> updateGrpAsnTcr(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());

		DefaultTransactionDefinition DTD = new DefaultTransactionDefinition();
		DTD.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		TransactionStatus status = transactionManager.getTransaction(DTD);

		int cnt = 0;
		// 파일 첨부

		// EA_ASN 테이블 수정
		commonDao.update(MAPPER_NAMESPACE + "updateGrpAsn", eaAsnTcrDto);

		// 대상 설정에서 모둠이 바뀌었을 경우
		if (!eaAsnTcrDto.getGrpId().equals(eaAsnTcrDto.getOriginGrpId())) {
			// 1. 해당 과제 ID로 EA_ASN_SMT 테이블 삭제 (기존 모둠 인원)
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmt", eaAsnTcrDto);

			// 2. 해당 과제 ID와 해당 모둠 ID로 EA_GRP_ASN_SMT 테이블 삭제 (기존 모둠)
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpAsnSmt", eaAsnTcrDto);

			// 3. 해당 과제 ID로 EA_ASN_SMT 테이블 다시 등록 (신규 모둠 인원)
			cnt += commonDao.insert(MAPPER_NAMESPACE + "insertAsnSmt", eaAsnTcrDto);

			// 4. 해당 과제 ID로 EA_GRP_ASN_SMT 테이블 다시 등록 (신규 모둠)
			cnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpAsnSmt", eaAsnTcrDto);

			// 5. 모둠 게시판 삭제 (기존 모둠 대상)
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoard", eaAsnTcrDto);

			// 6. 모둠 게시판 생성에 체크 했을 경우
			if ("Y".equals(eaAsnTcrDto.getCreateBoardYn())) {
				// 6. 모둠 게시판 등록 (신규 모둠 대상)
				cnt += commonDao.insert(MAPPER_NAMESPACE + "insertGrpBoard", eaAsnTcrDto);
			}
		}

		if (cnt <= 0) {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 *  모둠 과제 삭제
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	public Map<String, Object> deleteGrpAsnTcr(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();
		int cnt = 0;
		eaAsnTcrDto.setMdfrId(eaAsnTcrDto.getTcrUsrId());

		// 모둠 과제 삭제
		cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmt", eaAsnTcrDto);

		if (cnt <= 0) {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 *  모둠 과제 상세 조회
	 * @param eaAsnTcrDto
	 * @return ResponseDto<Integer>
	 */
	public Map<String, Object> selectGrpAsnTcr(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();
		List<EaAsnFleDto> fileList = new ArrayList<>();
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		// 모둠 과제 상세 조회
		List<EaAsnTcrDto> selectGrpAsnDetail = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpAsnDetail",
			eaAsnTcrDto);

		if (selectGrpAsnDetail.isEmpty() || selectGrpAsnDetail.get(0).getAsnId() == null) {
			resMap.put("msg", "FAIL");
			resMap.put("resMsg", "과제 정보를 확인할 수 없습니다.");
			return resMap;
		}
		// 첨부ID가 있을 경우
		if (selectGrpAsnDetail.get(0).getAnnxId() > 0) {

			eaAsnTcrDto.setAnnxId(selectGrpAsnDetail.get(0).getAnnxId());

			// 첨부 파일 조회
			String annxId = String.valueOf(eaAsnTcrDto.getAnnxId());
			fileList = eaAsnComService.selectFile(annxId);
			if (fileList.isEmpty()) {
				resMap.put("fileList", "");
			} else {
				resMap.put("fileList", fileList);
			}
		}

		// 모둠별 현황 조회
		List<EaAsnTcrDto> selectCurrentGrpAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectCurrentGrpAsnList",
			eaAsnTcrDto);

		for (int i = 0; i < selectCurrentGrpAsnList.size(); i++) {
			eaAsnTcrDto.setGrpId(selectCurrentGrpAsnList.get(i).getGrpId());
			eaAsnTcrDto.setGrpTemId(selectCurrentGrpAsnList.get(i).getGrpTemId());

			// 모둠 인원
			List<userName> selectUsrNm = commonDao.selectList(MAPPER_NAMESPACE + "selectUsrNm", eaAsnTcrDto);

			// 모둠별 현황 List에 Add
			selectCurrentGrpAsnList.get(i).setUserInfo(selectUsrNm);

		}

		if (selectGrpAsnDetail.size() > 0) {
			resMap.put("msg", "SUCCESS");
			resMap.put("selectGrpAsnDetail", selectGrpAsnDetail);
			resMap.put("selectCurrentGrpAsnList", selectCurrentGrpAsnList);
		} else {
			resMap.put("msg", "FAIL");

		}

		return resMap;
	}

	/**
	 * 모둠 과제 삭제
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> modifyGrpAsn(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();
		int cnt = 0;

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());

		// 모둠 과제 상세 조회
		List<EaAsnTcrDto> selectGrpAsnDetail = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpAsnDetail",
			eaAsnTcrDto);

		if (selectGrpAsnDetail.isEmpty() || selectGrpAsnDetail.get(0).getAsnId() == null) {
			resMap.put("msg", "FAIL");
			resMap.put("resMsg", "과제 정보를 확인할 수 없습니다.");
			return resMap;
		}

		// 모둠 과제 삭제
		if ("delete".equals(eaAsnTcrDto.getFlag())) {
			//2025.02.25 삭제 여부에 따라 삭져여부 변경에서  - > 데이터 삭제 처리로 수정
			eaAsnTcrDto.setOriginGrpId(eaAsnTcrDto.getGrpId());
			//모둠 게시판 댓글 삭제
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoardUcwr", eaAsnTcrDto);

			//모둠 게시판 게시글 삭제
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoardBlwr", eaAsnTcrDto);

			//모둠 게시판 삭제
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpBoard", eaAsnTcrDto);

			// 과제 삭제
			cnt += commonDao.update(MAPPER_NAMESPACE + "deleteAsn", eaAsnTcrDto);

			// EA_과제제출 삭제
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteAsnSmt", eaAsnTcrDto);

			// 모둠 과제 제출 삭제
			cnt += commonDao.delete(MAPPER_NAMESPACE + "deleteGrpAsnSmt", eaAsnTcrDto);

			//EA_과제범위 삭제
			cnt += commonDao.update(MAPPER_NAMESPACE + "deleteAsnRnge", eaAsnTcrDto);
		}

		if (cnt > 0) {
			resMap.put("msg", "SUCCESS");
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 * 모둠 과제 점수, 피드백 입력
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> saveGrpScFb(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();
		int cnt = 0;

		// Update EA_GRP_ANS_SMT Table
		cnt += commonDao.update(MAPPER_NAMESPACE + "saveGrpScFbGrpAnsTb", eaAsnTcrDto);

		// Update EA_ANS_SMT Table
		cnt += commonDao.update(MAPPER_NAMESPACE + "saveGrpScFbAsnSmtTb", eaAsnTcrDto);

		if (cnt > 0) {
			resMap.put("msg", "SUCCESS");
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 * 	모둠 과제 출제 - 대상 설정
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectGrpTarget(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();

		// 대상 설정 (해당 교사의 모둠 그룹 조회)
		List<EaAsnTcrDto> selectGrpTarget = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpTarget", eaAsnTcrDto);

		if (selectGrpTarget.size() > 0) {
			resMap.put("msg", "SUCCESS");
			resMap.put("selectGrpTarget", selectGrpTarget);
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 * 첨부 파일 조회
	 * @param eaAsnStuDto
	 * @return hashMap
	 */
	public Map<String, Object> selectFile(EaAsnTcrDto eaAsnTcrDto) {
		Map<String, Object> resMap = new HashMap<>();
		List<EaAsnFleDto> fileList = new ArrayList<>();

		String annxId = String.valueOf(eaAsnTcrDto.getAnnxId());
		if (!StringUtils.isEmpty(annxId)) {
			fileList = eaAsnComService.selectFile(annxId);
		}

		if (fileList.size() > 0) {
			resMap.put("msg", "SUCCESS");
			resMap.put("fileList", fileList);
		} else {
			resMap.put("msg", "FAIL");
		}

		return resMap;
	}

	/**
	 * 모둠원 조회
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectGrpInfo(EaAsnTcrDto eaAsnTcrDto) {

		Map<String, Object> result = new HashMap<>();

		List<grpUserInfo> grpInfo = commonDao.selectList(MAPPER_NAMESPACE + "selectGrpInfo", eaAsnTcrDto);

		result.put("grpInfo", grpInfo);
		return result;
	}

	/**
	 * 과제 일괄 적용 조회 - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	public Map<String, Object> selectPkgAsn(EaAsnTcrDto eaAsnTcrDto) {
		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		Map<String, Object> result = new HashMap<>();

		List<EaAsnTcrDto> pkgAsnList = commonDao.selectList(MAPPER_NAMESPACE + "selectPkgAsn", eaAsnTcrDto);

		result.put("pkgAsnList", pkgAsnList);
		return result;
	}

	/**
	 * 과제 일괄 적용(등록,수정) - 교사
	 * @param eaAsnTcrDto
	 * @return
	 */
	@Transactional
	public Map<String, Object> updatePkgAsn(EaAsnTcrDto eaAsnTcrDto) {

		CommonUserDetail userDetails = jwtProvider.getCommonUserDetail();
		//    	eaAsnTcrDto.setTcrUsrId(userDetails.getUsrId());
		eaAsnTcrDto.setOptTxbId(userDetails.getOptTxbId());
		eaAsnTcrDto.setCrtrId(userDetails.getUsrId());
		eaAsnTcrDto.setMdfrId(userDetails.getUsrId());
		eaAsnTcrDto.setDbId(userDetails.getTxbId());

		Map<String, Object> result = new HashMap<>();

		if (eaAsnTcrDto.getOptTxbList().size() > 0) {

			//원본 과제 묶음ID 업데이트 - 묶음ID는 원본 과제의 asnId 로 셋팅
			if (StringUtils.isEmpty(eaAsnTcrDto.getPkgAsnId())) {
				eaAsnTcrDto.setPkgAsnId(String.valueOf(eaAsnTcrDto.getAsnId()));
				commonDao.update(MAPPER_NAMESPACE + "updatePkgAsnId", eaAsnTcrDto);
			}
			// 원본 과제 상세 조회
			EaAsnTcrDto pkgDetail = commonDao.select(MAPPER_NAMESPACE + "selectAsnTcrDetail", eaAsnTcrDto);
			for (EaAsnTcrDto.OptTxbInfo optTxbInfo : eaAsnTcrDto.getOptTxbList()) {
				EaAsnTcrDto pkgAsnDto = new EaAsnTcrDto();
				pkgAsnDto.setOptTxbId(optTxbInfo.getOptTxbId());
				pkgAsnDto.setPkgAsnId(eaAsnTcrDto.getPkgAsnId());
				//				pkgAsnDto.setTcrUsrId(eaAsnTcrDto.getTcrUsrId());
				//묶음 과제 여부 조회
				EaAsnTcrDto pkgDto = new EaAsnTcrDto();
				if (!StringUtils.isEmpty(pkgAsnDto.getPkgAsnId())) {
					pkgDto = commonDao.select(MAPPER_NAMESPACE + "selectPkgAsn", pkgAsnDto);
				}

				pkgDetail.setOptTxbId(optTxbInfo.getOptTxbId());
				pkgDetail.setPkgAsnYn("Y");

				if (pkgDto == null || StringUtils.isEmpty(pkgDto.getPkgAsnId())) {

					BcUserInfoDto bcInfo = new BcUserInfoDto();
					bcInfo.setClaId(optTxbInfo.getClaId());
					bcInfo.setOptTxbId(optTxbInfo.getOptTxbId());
					//학생 조회
					List<BcUserInfoDto> useList = bcCmService.selectStuInfoList(bcInfo);

					// 교사 포함 조회 되므로 학생ID만 필터링
					List<String> filteredUsrIdList = useList.stream()
						.filter(user -> "ST".equals(user.getUsrTpCd()))
						.map(user -> user.getUsrId())
						.collect(Collectors.toList());

					pkgDetail.setUsrIdList(filteredUsrIdList);
					pkgDetail.setFlnAfSmtAbleYn("Y");

					//과제 등록
					Map<String, Object> res = this.insertAsnTcr(pkgDetail);

					if (res != null && res.get("resData") != null) {
						EaAsnTcrDto resData = (EaAsnTcrDto)res.get("resData");
						String cpPrcsYn = "Y";
						if (resData.getAsnId() == null) {
							cpPrcsYn = "N";
						}
						// 다른학급저장 log 등록
						bcCmService.insertClaCpLog(CmClaCpLogDto.builder()
							.optTxbId(userDetails.getOptTxbId())
							.cpOptTxbId(optTxbInfo.getOptTxbId()) // 다른학급 운영교과서ID
							.cpDvCd("ASN")
							.cpPrcsYn(cpPrcsYn)
							.backendFlePth("EaAsnTcrService.updatePkgAsn")
							.kerisUsrId(userDetails.getKerisUsrId())
							.crtrId(userDetails.getUsrId())
							.build());

						if (resData.getAsnId() == null) {
							result.put("resCode", "FAIL");
							return result;
						}

						// 등록 알림
						InfComDto infm = new InfComDto();
						infm.setAsnId(pkgDetail.getAsnId());
						infm.setInfmClCd("AS");
						infm.setOptTxbId(optTxbInfo.getOptTxbId());
						infm.setClaId(optTxbInfo.getClaId());
						infm.setInfmDtlClCd("AR");
						infm.setUsrId(eaAsnTcrDto.getCrtrId());
						infComService.insertInfmOtherCla(infm);

					} else {
						result.put("resCode", "FAIL");
						return result;
					}

				} else {
					int cnt = 0;
					pkgAsnDto.setAsnId(pkgDto.getAsnId());
					//과제 제출 완료 여부 확인
					EaAsnTcrDto pkgAsnCmp = commonDao.select(MAPPER_NAMESPACE + "selectPkgAsnCmp", pkgAsnDto);
					if ("N".equals(pkgAsnCmp.getPkgCmpYn())) {
						pkgDetail.setAsnId(pkgDto.getAsnId());
						//묶음과제 수정
						cnt += this.updateAsnTcr(pkgDetail);

						String cpPrcsYn = "Y";
						if (cnt < 1) {
							cpPrcsYn = "N";
						}
						// 다른학급저장 log 등록
						bcCmService.insertClaCpLog(CmClaCpLogDto.builder()
							.optTxbId(userDetails.getOptTxbId())
							.cpOptTxbId(optTxbInfo.getOptTxbId()) // 다른학급 운영교과서ID
							.cpDvCd("ASN")
							.cpPrcsYn(cpPrcsYn)
							.backendFlePth("EaAsnTcrService.updatePkgAsn")
							.kerisUsrId(userDetails.getKerisUsrId())
							.crtrId(userDetails.getUsrId())
							.build());

						if (cnt < 1) {
							result.put("resCode", "FAIL");
							return result;
						}

						// 수정 알림
						InfComDto infm = new InfComDto();
						infm.setAsnId(pkgDetail.getAsnId());
						infm.setInfmClCd("AS");
						infm.setOptTxbId(optTxbInfo.getOptTxbId());
						infm.setClaId(optTxbInfo.getClaId());
						infm.setInfmDtlClCd("AM");
						infm.setUsrId(eaAsnTcrDto.getCrtrId());
						infComService.insertInfmOtherCla(infm);
					}

				}

			}
		}
		result.put("resCode", "SUCCESS");
		return result;
	}

	// 신규 학생 > 기존 과제 반영
	@Transactional
	public int insertAsnSmtNewStu(EaAsnTcrDto eaAsnTcrDto) {
		int cnt = commonDao.insert(MAPPER_NAMESPACE + "insertAsnSmtNewStu", eaAsnTcrDto);
		log.debug("##### 신규 학생 과제 등록 건수 = " + String.valueOf(cnt));

		return cnt;
	}

	/**
	 * 동일 첨부ID를 가지고 있는지 확인
	 * 
	 * @param dto
	 * @return
	 */
	public boolean selectOtherAsnCopyAnnxFleYn(EaAnsCopyFleDto dto) {
		return ((Integer)commonDao.select(MAPPER_NAMESPACE + "selectOtherAsnCopyAnnxFleYn", dto)) > 0 ? true : false;
	}
}
