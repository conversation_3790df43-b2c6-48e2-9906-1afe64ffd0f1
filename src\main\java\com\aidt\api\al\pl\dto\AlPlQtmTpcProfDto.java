package com.aidt.api.al.pl.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI 맞춤 사용자별 문항 토픽 프로파일
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlPlQtmTpcProfDto {

	@Parameter(name="평가ID", required = true)
	private String evId;
	
	@Parameter(name="토픽완료여부", required = true)
    private String tpcCmplYn;

	@Parameter(name="유저ID", required = true)
    private String usrId;
	
	@Parameter(name="운영교과서")
    private String optTxbId;
	
	@Parameter(name="풀이횟수")
    private int qstCnt;
	
	@Parameter(name="평가구분코드")
    private String evDvCd;
	
	@Parameter(name="평가상세구분코드")
    private String evDtlDvCd;
	
	@Parameter(name="평가풀이시간")
    private int evTmScnt;
	
	@Parameter(name="평가 정답수")
    private int cansCnt;
	
	@Parameter(name="평가정답률")
    private double cansRt;
	
	@Parameter(name="평가완료여부")
    private String evCmplYn;
	
	@Parameter(name="학습맵대단원")
    private String luLrmpNodId;
	
	@Parameter(name="학습맵중단원")
    private String mluLrmpNodId;
	
	@Parameter(name="학습맵소단원")
    private String tcLrmpNodId;
	
	@Parameter(name="학습맵차시")
    private String tpcId;
	
	@Parameter(name="문항코드")
    private String qtmId;
	
	@Parameter(name="문항순번")
    private int qtmOrdn;
	
	@Parameter(name="문항난이도코드")
    private String qtmDffdDvCd;
	
	@Parameter(name="문항플랫폼대단원")
    private long qpLluId;
	
	@Parameter(name="문항플랫폼차시")
    private long qpTcId;
	
	@Parameter(name="제출답변값")
    private String smtAnwVl;
	
	@Parameter(name="정답여부")
    private String cansYn;
	
	@Parameter(name="문항별풀이시간초수")
    private int xplTmScnt;
	
	@Parameter(name="찜여부")
    private String dibsYn;
	
	@Parameter(name="지식맵대단원")
    private String lluKmmpNodId;
	
	@Parameter(name="지식맵중단원")
    private String mluKmmpNodId;
	
	@Parameter(name="지식맵소단원")
    private String sluKmmpNodId;
	
	@Parameter(name="지식맵차시")
    private String tcKmmpNodId;
	private String tcKmmpNodNm;
    
	@Parameter(name="지식맵토픽")
	private String kmmpNodId;
    
	@Parameter(name="지식맵메타문항코드")
    private String ctnCd;
	
	@Parameter(name="학습맵대단원")
    private String aklmLluLrmpNodId;
	
	@Parameter(name="학습맵중단원")
    private String aklmMluLrmpNodId;
	
	@Parameter(name="학습맵소단원")
    private String aklmSluLrmpNodId;
	
	@Parameter(name="학습맵차시")
    private String aklmTcLrmpNodId;
	
	@Parameter(name="사용자별 문항별 총 풀이횟수")
    private int aCan;
	
	@Parameter(name="사용자별 문항별 총 정답횟수")
    private int yCanCnt;
	
	@Parameter(name="사용자별 문항별 총 오답횟수")
    private int canNcnt;
	
	@Parameter(name="사용자별 문항별 총 정답율 100프로")
    private double canRt;
	
	@Parameter(name="사용자별 문항별 총 정답율")
    private double canRts;
	
	@Parameter(name="사용자별 문항별 난이도 획특포인트")
    private double qtmDffdPoint;
	
	@Parameter(name="문항플랫폼난이도구분코드")
	private String qpDffdDvCd;
	
	@Parameter(name="예측 평균 정답률")
	private String aiPredAvgCansRt;
	
	@Parameter(name="예측평균점수")
	private String aiPredAvgScr;
	
	@Parameter(name="학습자속도유형코드")
	private String lrnrVelTpCd;
	
	@Parameter(name="교과서")
    private String txbId;
	
	@Parameter(name="과목코드")
	private String sbjCd;
	
	@Parameter(name="학교급코드")
	private String schlGrdCd;
	
	@Parameter(name="학년코드")
	private Integer sgyCd;
	
	@Parameter(name="저자코드")
	private String autrCd;
	
	@Parameter(name="저자명")
	private String autrNm;
	
	@Parameter(name="컨텐츠유형코드")
	private String ctnTpCd;
	
	@Parameter(name="문항난이도코드")
	private String ctnDffdDvCd;
	
}
