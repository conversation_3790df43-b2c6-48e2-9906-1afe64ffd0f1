package com.aidt.api.ea.grpmgmt.dto;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @email 
 * @create date 2024-04-26 10:53:20
 * @modify date 2024-04-26 10:53:20
 * @desc EaGrpMgGrpDto 모둠관리 모둠 팀 학생 정보 Dto
 */

 @Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EaGrpMgmtGrpTeamStuDto {
	 /** 모둠ID */
	 @Parameter(name = "모둠ID")
	 private int grpId;

	 /** 모둠팀ID */
	 @Parameter(name = "모둠팀ID")
	 private int grpTemId;

	 /** 학생사용자ID */
	 @Parameter(name = "학생사용자ID")
	 @NotBlank(message = "{field.required}")
	 private String usrId;

	 /** 사용자명 */
	 @Parameter(name = "사용자명")
	 private String usrNm;

	 /**  순번*/
	 @Parameter(name = "순번")
	 private int stuNo;

	 /** 모둠팀장여부 */
	 @Parameter(name = "모둠팀장여부")
	 private String grpTmgrYn;

	 // FS	LRNR_VEL_TP_CD	빠른학습자
	 // NM	LRNR_VEL_TP_CD	보통학습자
	 // SL	LRNR_VEL_TP_CD	느린학습자
    
	 /**  학습 수준(느린/보통/빠른)*/
	 @Parameter(name = "학습 수준")
	 private String lrnrVelTpCd;
	 
	 /**  학습 수준(느린/보통/빠른)*/
	 @Parameter(name = "학습 수준 명칭")
	 private String lrnrVelTpNm;

	 /** 삭제여부 */
	 @Parameter(name = "삭제여부")
	 private String delYn;

	 /** 생성자ID */
	 @Parameter(name = "생성자ID")
	 private String crtrId;

	 /** 생성일시 */
	 @Parameter(name = "생성일시")
	 private String crtDtm;

	 /** 수정자ID */
	 @Parameter(name = "수정자ID")
	    private String mdfrId;

	 /** 수정일시 */
	 @Parameter(name = "수정일시")
	 private String MdfDtm;

	 /** 데이터베이스ID */
	 @Parameter(name = "데이터베이스ID")
	 private String dbId;
}