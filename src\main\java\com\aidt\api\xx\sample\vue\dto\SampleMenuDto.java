package com.aidt.api.xx.sample.vue.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "Sample Menu DTO")
public class SampleMenuDto {

    @Parameter(name="메뉴ID")   
    private String menuId;

    @Parameter(name="상위메뉴ID")   
    private String parentMenuId;

    @Parameter(name="메뉴명")   
    private String menuNm;

    @Parameter(name="파일경로")   
    private String filePath;

    @Parameter(name="파일명")   
    private String fileNm;

    @Parameter(name="사용여부")   
    private String useYn;

    @Parameter(name="메뉴순서")   
    private String menuOrder;

    @Parameter(name="등록자ID")   
    private String regId;

    @Parameter(name="등록일시")   
    private String regDt;    
}
