<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="api.al.pl.stu.AlPlIniDat">

	<select id="selectTxbId" parameterType="com.aidt.api.al.pl.dto.AlPlStuDto" resultType="com.aidt.api.al.pl.dto.AlPlStuDto">
		SELECT
			MAX(COT2.TXB_ID) AS TXB_ID,
			MAX(COT2.OPT_TXB_ID) AS OPT_TXB_ID,
			COUNT(AKNR.KMMP_NOD_ID) AS ROW_CNT
		FROM LMS_LRM.CM_OPT_TXB COT2 
		LEFT OUTER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN AKNR 
		ON COT2.OPT_TXB_ID = AKNR.OPT_TXB_ID 
		WHERE COT2.OPT_TXB_ID = #{optTxbId}
		/* AI_지식맵노드재구성 및 교과서ID 조회  - 이혜인 -  AlPlIniDatStu-Mapper.xml - selectTxbId */
	</select>
	
	<insert id="insertAiKmmpNodRcstn" parameterType="com.aidt.api.al.pl.dto.AlPlStuDto">
		INSERT INTO LMS_LRM.AI_KMMP_NOD_RCSTN
			 ( OPT_TXB_ID, KMMP_NOD_ID, URNK_KMMP_NOD_ID, KMMP_ID, KMMP_NOD_NM, DPTH, TC_EPS_YN, TC_USE_YN
			 , TXB_ID, ORGL_ORDN, RCSTN_ORDN, DEL_YN, CRTR_ID, CRT_DTM, MDFR_ID, MDF_DTM, DB_ID)
		 SELECT 
		 	   #{optTxbId}, BKN.KMMP_NOD_ID, BKN.URNK_KMMP_NOD_ID , BK.KMMP_ID, BKN.KMMP_NOD_NM, BKN.DPTH, 'Y', 'Y'
		 	   , BK.TXB_ID, BKN.SRT_ORDN, BKN.SRT_ORDN, 'N', #{usrId}, NOW(), #{usrId}, NOW(), 'AI_registIniDat'
		FROM LMS_CMS.BC_KMMP BK 
		INNER JOIN LMS_CMS.BC_KMMP_NOD BKN ON BKN.KMMP_ID = BK.KMMP_ID
		WHERE BK.TXB_ID = #{txbId}
		AND BK.DEL_YN = 'N'
		AND BKN.DEL_YN = 'N'
		AND BKN.TPC_DV_CD = 'IN'
		<!-- AND BKN.LU_EPS_YN = 'Y' -->
		/* AI_지식맵노드재구성 및 교과서ID 조회  - 이혜인 -  AlPlIniDatStu-Mapper.xml - insertAiKmmpNodRcstn */
	</insert>
	
	
	<select id="selectEnLrnrVelTpCd" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			ALL2.USR_ID,
			ALL2.OPT_TXB_ID,
			DPTH1.KMMP_NOD_ID AS LLU_KMMP_NOD_ID,
			DPTH1.KMMP_NOD_NM AS LLU_KMMP_NOD_NM,
			DPTH2.KMMP_NOD_ID AS MLU_KMMP_NOD_ID,
			DPTH2.KMMP_NOD_NM AS MLU_KMMP_NOD_NM,
			DPTH3.KMMP_NOD_ID AS SLU_KMMP_NOD_ID,
			DPTH3.KMMP_NOD_NM AS SLU_KMMP_NOD_NM,
			DPTH4.KMMP_NOD_ID AS TC_KMMP_NOD_ID,
			DPTH4.KMMP_NOD_NM AS TC_KMMP_NOD_NM,
			ALL2.LRNR_VEL_TP_CD
		FROM
			LMS_LRM.AI_LRNR_LV ALL2
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH4
					ON ALL2.LU_KMMP_NOD_ID = DPTH4.KMMP_NOD_ID
					AND ALL2.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH3
					ON DPTH4.URNK_KMMP_NOD_ID = DPTH3.KMMP_NOD_ID
					AND DPTH3.OPT_TXB_ID = DPTH4.OPT_TXB_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH2
					ON DPTH3.URNK_KMMP_NOD_ID = DPTH2.KMMP_NOD_ID
					AND DPTH2.OPT_TXB_ID = DPTH3.OPT_TXB_ID
				INNER JOIN LMS_LRM.AI_KMMP_NOD_RCSTN DPTH1
					ON DPTH2.URNK_KMMP_NOD_ID = DPTH1.KMMP_NOD_ID
					AND DPTH1.OPT_TXB_ID = DPTH2.OPT_TXB_ID
		WHERE 1=1
		  AND ALL2.USR_ID = #{usrId}
		  AND ALL2.OPT_TXB_ID = #{optTxbId}
		  AND DPTH2.KMMP_NOD_ID = #{mluKmmpNodId}
		  AND ALL2.DEL_YN = 'N'
		ORDER BY
			DPTH1.RCSTN_ORDN, DPTH2.RCSTN_ORDN, DPTH3.RCSTN_ORDN, DPTH4.RCSTN_ORDN
	</select>
	
	
	<select id="selectEvCmplList" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EE.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(EE.EV_DTL_DV_CD) AS EV_DTL_DV_CD,
			MAX(EAETR.LLU_KMMP_NOD_ID) AS LLU_KMMP_NOD_ID,
			MAX(EAETR.LLU_KMMP_NOD_NM) AS LLU_KMMP_NOD_NM,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(EAETR.MLU_KMMP_NOD_NM) AS MLU_KMMP_NOD_NM,
			MAX(EAETR.TC_KMMP_NOD_ID) AS TC_KMMP_NOD_ID,
			MAX(EAETR.TC_KMMP_NOD_NM) AS TC_KMMP_NOD_NM,
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'>
			MAX(EE.EV_ID) AS EV_ID,
			EAETR.TPC_KMMP_NOD_ID,
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'>
			EE.EV_ID,
			MAX(EAETR.TPC_KMMP_NOD_ID) AS TPC_KMMP_NOD_ID,
		</if>
			MAX(EAETR.TPC_KMMP_NOD_NM) AS TPC_KMMP_NOD_NM,
			MAX(EAETR.LUEV_CMPL_YN) AS LUEV_CMPL_YN
		FROM
			LMS_LRM.EA_EV EE
			INNER JOIN LMS_LRM.EA_EV_RS EER
				ON EE.EV_ID = EER.EV_ID
			INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
				ON EE.EV_ID = EAETR.EV_ID
				AND EE.OPT_TXB_ID = EAETR.OPT_TXB_ID
		WHERE 1=1
			AND EE.EV_DV_CD = 'AE'
			AND EER.USR_ID = #{usrId}
			AND EAETR.MLU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND EE.OPT_TXB_ID = #{optTxbId}
			AND EE.EV_DTL_DV_CD <![CDATA[<>]]> 'OV'
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'> <!-- 수학 -->
			AND EAETR.TPC_KMMP_NOD_ID IN (
			<foreach collection="kmmpNodIdList" index="index" item="item" separator=",">
				#{item}
			</foreach>
			)
			GROUP BY EAETR.TPC_KMMP_NOD_ID
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'> <!-- 영어 -->
			AND EAETR.TC_KMMP_NOD_ID = #{tcKmmpNodId}
			GROUP BY EE.EV_ID
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("MA") or sbjCd.equals("CM1") or sbjCd.equals("CM2"))'>
			ORDER BY MAX(EAETR.TC_KMMP_NOD_ID), EAETR.TPC_KMMP_NOD_ID, MAX(EE.EV_DTL_DV_CD) DESC, MAX(EE.EV_ID)
		</if>
		<if test = 'sbjCd != null and (sbjCd.equals("EN") or sbjCd.equals("CE1") or sbjCd.equals("CE2"))'>
			ORDER BY MAX(EAETR.TC_KMMP_NOD_ID), MAX(EAETR.TPC_KMMP_NOD_ID), MAX(EE.EV_DTL_DV_CD) DESC, EE.EV_ID
		</if>
	</select>
	
	
	<select id="selectMaLuevCmplYn" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto" resultType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		SELECT
			MAX(AUTLO.USR_ID) AS USR_ID,
			MAX(AUTLO.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(AUTLO.LU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			AUTLO.TPC_KMMP_NOD_ID,
			MAX(AUTLO.LRN_ORDN) AS LRN_ORDN,
			IFNULL(MAX(EV.LUEV_CMPL_YN), 'N') AS LUEV_CMPL_YN
		FROM LMS_LRM.AI_USRLY_TPC_LRN_ORDN AUTLO 
			LEFT OUTER JOIN (
				SELECT
				    EER.USR_ID
				  , EAETR.OPT_TXB_ID
				  , EAETR.MLU_KMMP_NOD_ID
				  , EAETR.TPC_KMMP_NOD_ID
				  , EAETR.LUEV_CMPL_YN
				FROM LMS_LRM.EA_EV EE
					INNER JOIN LMS_LRM.EA_EV_RS EER
					    ON EE.EV_ID = EER.EV_ID
					INNER JOIN LMS_LRM.EA_AI_EV_TS_RNGE EAETR
					    ON EE.EV_ID = EAETR.EV_ID
			) EV
				ON EV.OPT_TXB_ID = AUTLO.OPT_TXB_ID
				AND EV.USR_ID = AUTLO.USR_ID
				AND AUTLO.LU_KMMP_NOD_ID = EV.MLU_KMMP_NOD_ID
				AND AUTLO.TPC_KMMP_NOD_ID = EV.TPC_KMMP_NOD_ID
		WHERE
		    AUTLO.USR_ID = #{usrId}
			AND AUTLO.OPT_TXB_ID = #{optTxbId}
			AND AUTLO.LU_KMMP_NOD_ID = #{mluKmmpNodId}
			AND AUTLO.LRN_YN = 'Y'
		GROUP BY AUTLO.TPC_KMMP_NOD_ID
		ORDER BY MAX(AUTLO.LRN_ORDN)
	</select>
	
	<insert id="updateLrnPgrsProf" parameterType="com.aidt.api.al.pl.dto.AiRcmTsshQtmDto">
		INSERT INTO LMS_LRM.AI_LRN_PGRS_PROF
		(
		  USR_ID
		, OPT_TXB_ID
		, MLU_KMMP_NOD_ID
		, AI_LRN_PGRS_RT
		, CRTR_ID
		, CRT_DTM
		, MDFR_ID
		, MDF_DTM
		, DB_ID
		) VALUES (
		  #{usrId}
		, #{optTxbId}
		, #{mluKmmpNodId}
		, #{aiLrnPgrsRt}
		, #{usrId}
		, NOW()
		, #{usrId}
		, NOW()
		, #{dbId}
		) ON DUPLICATE KEY UPDATE
		AI_LRN_PGRS_RT = #{aiLrnPgrsRt}
		, MDFR_ID = #{usrId}
		, MDF_DTM = NOW()
		/* AI맞춤 학습진도율 - 강성현 - AiRcmTsshQtm-Mapper.xml - updateLrnPgrsProf */
	</insert>
	
		<select id="selectEvTarget" parameterType="com.aidt.api.al.pl.dto.AlPlStuDto" resultType="com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto">
		SELECT
			MAX(EER.USR_ID) AS USR_ID,
			MAX(EAETR.MLU_KMMP_NOD_ID) AS MLU_KMMP_NOD_ID,
			MAX(EAETR.OPT_TXB_ID) AS OPT_TXB_ID,
			MAX(BT.SCHL_GRD_CD) AS SCHL_GRD_CD,
			MAX(BT.SBJ_CD) AS SBJ_CD,
			MAX(ALPP.AI_LRN_PGRS_RT) AS AI_LRN_PGRS_RT
		FROM LMS_LRM.EA_AI_EV_TS_RNGE EAETR
			 INNER JOIN LMS_LRM.EA_EV_RS EER
				ON EAETR.EV_ID = EER.EV_ID
				AND EER.USR_ID = EAETR.CRTR_ID
			 INNER JOIN LMS_LRM.CM_OPT_TXB COT2
				ON EAETR.OPT_TXB_ID = COT2.OPT_TXB_ID
			 INNER JOIN LMS_CMS.BC_TXB BT
				ON COT2.TXB_ID = BT.TXB_ID
			 LEFT OUTER JOIN LMS_LRM.AI_LRN_PGRS_PROF ALPP
				ON ALPP.USR_ID = EER.USR_ID
				AND ALPP.OPT_TXB_ID = EAETR.OPT_TXB_ID
				AND ALPP.MLU_KMMP_NOD_ID = EAETR.MLU_KMMP_NOD_ID
		WHERE
			ALPP.AI_LRN_PGRS_RT IS NULL
			AND EAETR.OPT_TXB_ID = #{optTxbId}
		GROUP BY
			EAETR.EV_ID
	</select>
	
</mapper>