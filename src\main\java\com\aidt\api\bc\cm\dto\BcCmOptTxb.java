package com.aidt.api.bc.cm.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 값인 필드 제외
@Schema(description = "CM_운영교과서")
public class BcCmOptTxb {

	@Schema(description = "운영교과서ID")
	private String optTxbId;
	
	@Schema(description = "교과서ID")
	private Long txbId;
	
	@Schema(description = "학급ID")
	private String claId;
	
	@Schema(description = "KERIS강의코드")
	private String kerisLectCd;
	
	@Schema(description = "생성자ID")
	private String crtrId;

	@Schema(description = "생성일시")
	private LocalDateTime crtDtm;
	
	@Schema(description = "수정자ID")
	private String mdfrId;
	
	@Schema(description = "수정일시")
	private LocalDateTime mdfDtm;
}
