package com.aidt.api.al.pl.stu;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.aidt.api.al.pl.dto.AlPlCtnDto;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-12-12 22:42:32
 * @modify date 2023-12-12 22:42:32
 * @desc AI맞춤학습 단원목차 테스트
 */
//@Slf4j
@Service
public class AlPlStuCtnService {
	
	//@Autowired
    //private JwtProvider jwtProvider;

	public static AlPlCtnDto selectLearningData(AlPlCtnDto AlPlCtnDto) {
		// TODO Auto-generated method stub
		AlPlCtnDto rtnVal = new AlPlCtnDto();
		
		rtnVal.setLluNodNm("대단원명~");
    	rtnVal.setLluNodId("대단원명ID");
    	rtnVal.setLluEpsYn("대단원노출여부");
    	rtnVal.setLluUseYn("대단원사용여부");
    	rtnVal.setMluNodId("중단원ID");
    	rtnVal.setMluNodNm("중단원명");
    	rtnVal.setMluEpsYn("중단원노출여부");
    	rtnVal.setMluUseYn("중단원사용여부");
    	rtnVal.setSluNodId("소단원ID");
    	rtnVal.setSluNodNm("소단원명");
    	rtnVal.setSluEpsYn("중단원노출여부");
    	rtnVal.setSluUseYn("중단원사용여부");
    	rtnVal.setTcNodId("차시ID");
    	rtnVal.setTcNodNm("차시명");
    	rtnVal.setTcEpsYn("차시노출여부");
    	rtnVal.setTcUseYn("차시사용여부");
    	rtnVal.setAtvTotCnt("1");
    	rtnVal.setStrAtvId("01");
    	rtnVal.setTxbEndPgeNo("2");
    	rtnVal.setTxbStrPgeNo("1");
    	rtnVal.setTxbUseYn("Y");
    	rtnVal.setWkbUseYn("N");
    	AlPlCtnDto.TxbPdfList txbLrn = new AlPlCtnDto.TxbPdfList();
    	txbLrn.setPgeNo("01");
    	txbLrn.setCdnPthNm("111111");

    	List<AlPlCtnDto.TxbPdfList> txbLrnList = new ArrayList<AlPlCtnDto.TxbPdfList>();
    	txbLrnList.add(txbLrn);
    	rtnVal.setTxbPdfList(txbLrnList);
		
		AlPlCtnDto.LrnStp lrnStp3 = new AlPlCtnDto.LrnStp();
    	lrnStp3.setLrnStpId("1001");
    	lrnStp3.setLrnStpNm("도입!!!!");
    	lrnStp3.setLrnStpDvCd("CL");	//학습단계구분코드 CL=개념학습, EX=평가
    	lrnStp3.setLrnStpOrdn(1);
		
		AlPlCtnDto.LrnAtv lrnAtv2 = new AlPlCtnDto.LrnAtv();
		lrnAtv2.setLrnAtvId("01");
		lrnAtv2.setLrnAtvNm("activity명-- 추천 동영상");
		lrnAtv2.setCtnTpCd("PL");
		lrnAtv2.setCtnUrl("/api/v1/content/ene34h203/lms/sample/video/");
//		lrnAtv2.setCtnUrl("/api/v1/content/ene34h203/lms/sample/video/media/01_video_1080.mp4");
		lrnAtv2.setRcstnOrdn(1);
		lrnAtv2.setImgCdn("");
		lrnAtv2.setQtmId("");
		//2024.04.05
		lrnAtv2.setLrnStCd("NL");

		AlPlCtnDto.CtnMtd ctnMtd2 = new AlPlCtnDto.CtnMtd();
		ctnMtd2.setCtnTpCd("PL");
		ctnMtd2.setVdsLRsln("media/01_video_480.mp4");
		ctnMtd2.setVdsMRsln("media/01_video_720.mp4");
		ctnMtd2.setVdsHRsln("media/01_video_1080.mp4");
		ctnMtd2.setSttlSmiFleNm("media/01_caption.smi");
		ctnMtd2.setSttlVttFleNm("media/01_caption.vtt");
		ctnMtd2.setScrbFleNm("media/01_script.txt");
		ctnMtd2.setVceFleNm("media/01_audio.mp3");
		ctnMtd2.setThbFleNm("images/poster.png");

		lrnAtv2.setCtnMtd(ctnMtd2);
		
		
		List<AlPlCtnDto.LrnAtv> lrnAtvList = new ArrayList<AlPlCtnDto.LrnAtv>();
		lrnAtvList.add(lrnAtv2);
		lrnStp3.setLrnAtvList(lrnAtvList); //전개
		List<AlPlCtnDto.LrnStp> lrnStpList = new ArrayList<AlPlCtnDto.LrnStp>();
    	lrnStpList.add(lrnStp3);
    	rtnVal.setLrnStpList(lrnStpList);
    	
    	
    	///학습도구
    	List<AlPlCtnDto.LrnTl> lrnTlList = new ArrayList<AlPlCtnDto.LrnTl>();
    	AlPlCtnDto.LrnTl lrnTl = new AlPlCtnDto.LrnTl();
    	lrnTl.setLrnTlTpCd("CT");
    	lrnTl.setLrnTlTpNm("수업도구");
    	lrnTl.setLrnTlKnCd("TI");
    	lrnTl.setLrnTlKnNm("타이머");

    	AlPlCtnDto.LrnTl lrnTl2 = new AlPlCtnDto.LrnTl();
    	lrnTl2.setLrnTlTpCd("CT");
    	lrnTl2.setLrnTlTpNm("수업도구");
    	lrnTl2.setLrnTlKnCd("HI");
    	lrnTl2.setLrnTlKnNm("깜깜이");

//    	AlPlCtnDto.LrnTl lrnTl3 = new AlPlCtnDto.LrnTl();
//    	lrnTl3.setLrnTlTpCd("MT");
//    	lrnTl3.setLrnTlTpNm("소통도구");
//    	lrnTl3.setLrnTlKnCd("LQ");
//    	lrnTl3.setLrnTlKnNm("라이브퀴즈");
//
//    	AlPlCtnDto.LrnTl lrnTl4 = new AlPlCtnDto.LrnTl();
//    	lrnTl4.setLrnTlTpCd("MT");
//    	lrnTl4.setLrnTlTpNm("소통도구");
//    	lrnTl4.setLrnTlKnCd("DI");
//    	lrnTl4.setLrnTlKnNm("토의토론");
//
//    	AlPlCtnDto.LrnTl lrnTl5 = new AlPlCtnDto.LrnTl();
//    	lrnTl5.setLrnTlTpCd("MT");
//    	lrnTl5.setLrnTlTpNm("소통도구");
//    	lrnTl5.setLrnTlKnCd("CB");
//    	lrnTl5.setLrnTlKnNm("클래스보드");
//
//    	AlPlCtnDto.LrnTl lrnTl6 = new AlPlCtnDto.LrnTl();
//    	lrnTl6.setLrnTlTpCd("WT");
//    	lrnTl6.setLrnTlTpNm("필기도구");
//    	lrnTl6.setLrnTlKnCd("BO");
//    	lrnTl6.setLrnTlKnNm("북마크");

    	lrnTlList.add(lrnTl);
    	lrnTlList.add(lrnTl2);
//    	lrnTlList.add(lrnTl3);
//    	lrnTlList.add(lrnTl4);
//    	lrnTlList.add(lrnTl5);
//    	lrnTlList.add(lrnTl6);

//    	AlPlCtnDto.LrnTl lrnTl7 = new AlPlCtnDto.LrnTl();
//    	lrnTl7.setLrnTlTpCd("CT");
//    	lrnTl7.setLrnTlTpNm("수업도구");
//    	lrnTl7.setLrnTlKnCd("WB");
//    	lrnTl7.setLrnTlKnNm("화이트보드");
//
//
//    	AlPlCtnDto.LrnTl lrnTl8 = new AlPlCtnDto.LrnTl();
//    	lrnTl8.setLrnTlTpCd("CT");
//    	lrnTl8.setLrnTlTpNm("수업도구");
//    	lrnTl8.setLrnTlKnCd("BE");
//    	lrnTl8.setLrnTlKnNm("집중벨");
//
//    	AlPlCtnDto.LrnTl lrnTl9 = new AlPlCtnDto.LrnTl();
//    	lrnTl9.setLrnTlTpCd("CT");
//    	lrnTl9.setLrnTlTpNm("수업도구");
//    	lrnTl9.setLrnTlKnCd("SP");
//    	lrnTl9.setLrnTlKnNm("발표도우미");
//
//    	AlPlCtnDto.LrnTl lrnTl10 = new AlPlCtnDto.LrnTl();
//    	lrnTl10.setLrnTlTpCd("TT");
//    	lrnTl10.setLrnTlTpNm("교과도구");
//    	lrnTl10.setLrnTlKnCd("EN");
//    	lrnTl10.setLrnTlKnNm("영어 단어사전");
//
//    	AlPlCtnDto.LrnTl lrnTl11 = new AlPlCtnDto.LrnTl();
//    	lrnTl11.setLrnTlTpCd("WT");
//    	lrnTl11.setLrnTlTpNm("필기도구");
//    	lrnTl11.setLrnTlKnCd("NO");
//    	lrnTl11.setLrnTlKnNm("필기/연습장");
//
//    	AlPlCtnDto.LrnTl lrnTl12 = new AlPlCtnDto.LrnTl();
//    	lrnTl12.setLrnTlTpCd("TT");
//    	lrnTl12.setLrnTlTpNm("교과도구");
//    	lrnTl12.setLrnTlKnCd("CA");
//    	lrnTl12.setLrnTlKnNm("계산기");
//
//    	AlPlCtnDto.LrnTl lrnTl13 = new AlPlCtnDto.LrnTl();
//    	lrnTl13.setLrnTlTpCd("TT");
//    	lrnTl13.setLrnTlTpNm("교과도구");
//    	lrnTl13.setLrnTlKnCd("MA");
//    	lrnTl13.setLrnTlKnNm("수학 교구");
//
//    	AlPlCtnDto.LrnTl lrnTl14 = new AlPlCtnDto.LrnTl();
//    	lrnTl14.setLrnTlTpCd("TT");
//    	lrnTl14.setLrnTlTpNm("교과도구");
//    	lrnTl14.setLrnTlKnCd("GM");
//    	lrnTl14.setLrnTlKnNm("그래프마법사");
//
//
//    	lrnTlList.add(lrnTl7);
//    	lrnTlList.add(lrnTl8);
//    	lrnTlList.add(lrnTl9);
//    	lrnTlList.add(lrnTl10);
//    	lrnTlList.add(lrnTl11);
//    	lrnTlList.add(lrnTl12);
//    	lrnTlList.add(lrnTl13);
//    	lrnTlList.add(lrnTl14);

    	rtnVal.setLrnTlList(lrnTlList);
    	
		return rtnVal;
	}
    
    
    

}
