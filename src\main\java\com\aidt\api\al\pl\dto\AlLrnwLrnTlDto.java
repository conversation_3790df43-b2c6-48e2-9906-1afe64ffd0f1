package com.aidt.api.al.pl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 학습창 연계
 * 학습도구
 * */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlLrnwLrnTlDto {

    @Parameter(name="학습도구유형코드")
    private String lrnTlTpCd;
    
    @Parameter(name="학습도구유형명")
    private String lrnTlTpNm;
    
    @Parameter(name="학습도구기능코드")
    private String lrnTlKnCd;
    
    @Parameter(name="학습도구명")
    private String lrnTlKnNm;
}
