package com.aidt.api.al.pl.cm.rcm.service;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aidt.api.al.pl.cm.rcm.AiRcmTsshQtmCommService;
import com.aidt.api.al.pl.cm.rcm.adapter.AiQuestionProfileAdapter;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEv;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvLoader;
import com.aidt.api.al.pl.cm.rcm.dto.EaAiEvPredictProfile;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationCode;
import com.aidt.api.al.pl.cm.rcm.enums.EvaluationDetailCode;
import com.aidt.api.al.pl.cm.rcm.event.AiQuestionTopicProfileBuildEvent;
import com.aidt.api.al.pl.cm.rcm.factory.ProfileFactory;
import com.aidt.api.al.pl.cm.rcm.helper.AiCenterHelper;
import com.aidt.api.al.pl.dto.AiRcmTsshQtmDto;
import com.aidt.api.al.pl.dto.AlPlQtmTpcProfDto;
import com.aidt.api.bc.cm.exception.BizException;
import com.aidt.api.bc.cm.textbook.dto.Textbook;
import com.aidt.api.bc.cm.textbook.service.BcTextbookQueryService;
import com.aidt.api.common.helper.JwtHelper;
import com.aidt.api.error.ApiErrorLogException;
import com.aidt.api.error.ErrorCode;
import com.aidt.common.CommonUserDetail;
import com.google.common.collect.Lists;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service
@RequiredArgsConstructor
public class AiQuestionTopicProfileLoadService {

	private final AiQuestionProfileAdapter aiQuestionProfileAdapter;
	private final BcTextbookQueryService bcTextbookQueryService;
	private final ProfileFactory profileFactory;
	private final JwtHelper jwtHelper;
	private final AiCenterHelper aiCenterHelper;
	private final AiRcmTsshQtmCommService aiRcmTsshQtmCommService;
	private final ApplicationEventPublisher publisher;

	public void load(Integer evId, boolean isTopicCompleted) {

		var user = jwtHelper.getCommonUserDetail();
		var eaAiEvLoader = new EaAiEvLoader(evId, isTopicCompleted, getEaAiEvs(evId, user), getTextbook(user));
		var strategy = profileFactory.getProfileStrategy(eaAiEvLoader.getSubjectCode());

		//1. AI 평가 시험 범위 완료 처리
		executeStrategyMethod(ErrorCode.LU_EV_CMP_YN, () -> strategy.completeAiEvaluationTestRange(eaAiEvLoader));

		//todo: 2.학습자 수준 (수학&영어 상중하 인터페이스 화) => strategy
		// executeStrategyMethod("AI 학습자 수준 업데이트", ()-> strategy.updateAiLearningLevel(eaAiEvLoader));

		//2. AI 학습자 수준 업데이트
		updateAiLearningLevel(eaAiEvLoader);

		//3. AI 정답 예측률 조회 및 문항 & 토픽 프로파일 등록/업데이트
		var eaAiEvQuestionAnswers = saveUserAiProfilePrediction(eaAiEvLoader);

		//todo: 4.토픽 숙련도 (AI_사용자별토픽프로파일) => strategy
		// executeStrategyMethod("AI 사용자 별 토픽 숙련도", ()-> strategy.updateTopicProficiency(eaAiEvLoader));

		//todo: 5.토픽 학습 순서 저장 => strategy
		// executeStrategyMethod("AI 학습 토픽 순서", ()-> strategy.updateTopicLearningOrder(eaAiEvLoader));

		//todo: 9.학습 진도율 => strategy
		// executeStrategyMethod("AI 학습 진도율 업데이트", ()-> strategy.saveLearningProgressProfile(eaAiEvLoader));

		//4. 토픽 숙련도
		updateTopicProficiency(eaAiEvLoader);

		//5. 토픽 학습 순서 저장
		updateTopicLearningOrder(eaAiEvLoader, eaAiEvQuestionAnswers);

		//9. 학습 진도율
		saveLearningProgressProfile(eaAiEvLoader);

		//6,7,8 비동기 처리
		submitEvent(eaAiEvLoader, eaAiEvQuestionAnswers, user);

	}

	private List<EaAiEv> getEaAiEvs(Integer evId, CommonUserDetail user) {

		var eaAiEvs = aiQuestionProfileAdapter.getEaAiEvs(evId, user.getOptTxbId(), user.getUsrId());

		if (CollectionUtils.isEmpty(eaAiEvs)) {
			log.warn("완료된 AI 평가 정보를 찾을 수 없습니다.");
			throw new BizException("완료된 AI 평가 정보를 찾을 수 없습니다.");
		}

		return eaAiEvs;

	}

	private Textbook getTextbook(CommonUserDetail user) {
		return bcTextbookQueryService.getTextbook(user.getTxbId());
	}
	
	//fixme: 코드 구분 변경
	private void executeStrategyMethod(ErrorCode errorCode, Runnable runnable) {
		try {
			runnable.run();
		} catch (Exception e) {
			log.error(errorCode.getMessage());
			throw new ApiErrorLogException(errorCode, e, ErrorCode.TYPE_EXTERNAL);
		}
	}

	private List<EaAiEvPredictProfile> saveUserAiProfilePrediction(EaAiEvLoader eaAiEvLoader) {
		var eaAiEv = eaAiEvLoader.getEaAiEv();
		try {
			// AI 맞춤 진단은 중단원 하위 모든 문항의 예측율을 계산한다.
			if (EvaluationDetailCode.OV.equals(eaAiEv.getEvaluationDetailCode())) {
				var unitAllEaAiEvQuestions = aiQuestionProfileAdapter.getUnitAllEaAiEvByQuestions(eaAiEv);
				if (CollectionUtils.isEmpty(unitAllEaAiEvQuestions)) {
					log.warn("단원 하위 문항이 존재하지 않습니다.");
					throw new IllegalArgumentException("단원 하위 문항이 존재하지 않습니다.");
				}
				upsertUserAiProfilePrediction(eaAiEv, unitAllEaAiEvQuestions);
			}

			//단원 내 문항이 평가지 문항에 포함되어 있지 않는 경우가 있어, 불러오는듯
			var eaAiEvQuestionAnswers = aiQuestionProfileAdapter.getEaAiEvQuestionAnswers(eaAiEv);

			//DIY평가 외에는 평가지 내의 문항 예측율을 계산한다.
			if (!EvaluationCode.DE.equals(eaAiEv.getEvaluationCode())) {
				upsertUserAiProfilePrediction(eaAiEv, eaAiEvQuestionAnswers);
			}

			return eaAiEvQuestionAnswers;

		} catch (ApiErrorLogException apiErrorLogException) {
			throw apiErrorLogException;
		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.AI_CENTER, e, ErrorCode.TYPE_EXTERNAL);
		}

	}

	private void upsertUserAiProfilePrediction(EaAiEv eaAiEv, List<EaAiEvPredictProfile> eaAiEvPredictProfiles) {

		var aiPredictionRequests = eaAiEvPredictProfiles.stream()
			.map(x -> AiCenterHelper.AiPredictionRequest.of(eaAiEv, x))
			.collect(Collectors.toList());

		//문항별 AI 정답 예측률 By KT AI Center
		var correctAnswerRatesMap = aiCenterHelper
			.getAiPredictionCorrectAnswerRates(aiPredictionRequests, eaAiEv.getTextbook()).stream()
			.collect(Collectors.toMap(AiCenterHelper.AiPredictionCorrectAnswerRate::getQtm_id,
				AiCenterHelper.AiPredictionCorrectAnswerRate::getInfer));

		var questionProfiles = eaAiEvPredictProfiles.stream()
			.peek(predictProfile ->
				predictProfile.addCorrectAnswerRate(correctAnswerRatesMap.get(predictProfile.getQtmId())))
			.collect(Collectors.toList());

		//문항 프로파일 적재
		aiQuestionProfileAdapter.upsertEaAiEvQuestionProfile(eaAiEv, questionProfiles);

		var topicIdSet = questionProfiles.stream()
			.map(EaAiEvPredictProfile::getTpcKmmpNodId)
			.collect(Collectors.toSet());

		//토픽 프로파일 적재
		aiQuestionProfileAdapter.upsertEaAiEvTopicProfile(eaAiEv, topicIdSet);

	}

	private void submitEvent(EaAiEvLoader eaAiEvLoader, List<EaAiEvPredictProfile> eaAiEvQuestionAnswers,
		CommonUserDetail user) {
		publisher.publishEvent(
			new AiQuestionTopicProfileBuildEvent(eaAiEvLoader, eaAiEvQuestionAnswers, user,
				jwtHelper.getAccessToken()));
	}

	/**
	 * todo: 해당 소스들은 구 소스로 구현. 추후 각 과목의 전략패턴으로 정의하여 대체
	 */

	@Deprecated
	private void updateAiLearningLevel(EaAiEvLoader eaAiEvLoader) {
		var eaAiEv = eaAiEvLoader.getEaAiEv();
		try {
			aiRcmTsshQtmCommService.updateLrnrVelTpCd(AiRcmTsshQtmDto.builder()
				.optTxbId(eaAiEv.getOptTxbId())
				.usrId(eaAiEv.getUsrId())
				.evDtlDvCd(eaAiEv.getEvDtlDvCd())
				.mluKmmpNodId(eaAiEv.getMluKmmpNodId().toString())
				.sbjCd(eaAiEv.getSbjCd())
				.schlGrdCd(eaAiEv.getSchlGrdCd())
				.build());
		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.LRNR_LEVEL_TP_CD, e, ErrorCode.TYPE_INTERNAL);
		}
	}

	@Deprecated
	private void updateTopicProficiency(EaAiEvLoader eaAiEvLoader) {
		var eaAiEv = eaAiEvLoader.getEaAiEv();
		try {
			aiRcmTsshQtmCommService.setTpcAvn(eaAiEv.getSbjCd(), eaAiEv.getUsrId(), eaAiEv.getEvId().toString(),
				eaAiEv.getMluKmmpNodId().toString(), eaAiEv.getOptTxbId(), eaAiEv.getEvDtlDvCd());

		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.TPC_AVN, e, ErrorCode.TYPE_INTERNAL);
		}
	}

	@Deprecated
	private void updateTopicLearningOrder(EaAiEvLoader eaAiEvLoader, List<EaAiEvPredictProfile> eaAiEvQuestionAnswers) {
		var eaAiEv = eaAiEvLoader.getEaAiEv();
		try {
			aiRcmTsshQtmCommService.updateAiUsrlyTpcLrnOrdn(
				eaAiEvQuestionAnswers.stream()
					.map(x -> AiRcmTsshQtmDto.builder()
						.cansYn(x.getCansYn())
						.build())
					.collect(Collectors.toList()),
				eaAiEv.getUsrId(),
				eaAiEv.getOptTxbId(),
				eaAiEv.getSbjCd(),
				eaAiEv.getEvDtlDvCd(),
				eaAiEv.getMluKmmpNodId().toString()
			);
		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.AI_USRLY_TPC_LRN_ORDN, e, ErrorCode.TYPE_INTERNAL);
		}
	}

	@Deprecated
	public void saveLearningProgressProfile(EaAiEvLoader eaAiEvLoader) {
		var eaAiEv = eaAiEvLoader.getEaAiEv();
		try {
			aiRcmTsshQtmCommService.updateLrnPrgsProf(AlPlQtmTpcProfDto.builder()
				.usrId(eaAiEv.getUsrId())
				.mluKmmpNodId(eaAiEv.getMluKmmpNodId().toString())
				.optTxbId(eaAiEv.getOptTxbId())
				.sbjCd(eaAiEv.getSbjCd())
				.schlGrdCd(eaAiEv.getSchlGrdCd())
				.build()
			);
		} catch (Exception e) {
			throw new ApiErrorLogException(ErrorCode.LRN_PRGS_PROF, e, ErrorCode.TYPE_INTERNAL);
		}
	}

}