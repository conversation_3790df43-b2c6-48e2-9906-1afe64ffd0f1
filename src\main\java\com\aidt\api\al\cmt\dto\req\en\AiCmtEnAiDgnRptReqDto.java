package com.aidt.api.al.cmt.dto.req.en;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.aidt.api.al.cmt.cm.AiCmtLvlCalculator;
import com.aidt.api.al.cmt.dto.ett.cm.N12Dto;
import com.aidt.api.al.cmt.dto.ett.en.AiCmtEnAiDgnRptDto;
import com.aidt.api.al.cmt.dto.req.cm.N02ReqDto;
import com.aidt.api.al.cmt.dto.req.cm.N12ReqDto;
import com.aidt.api.al.cmt.dto.req.cm.QtmCntReqDto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-05-21 11:03:46
 * @modify date 2024-05-21 11:03:46
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCmtEnAiDgnRptReqDto {

    @Parameter(name="학교급코드(초등:E,중등:M,고등:H)", required=true)
    @NotBlank(message = "{field.required}")
    @Pattern(regexp = "^(E|M|H)$", message="학교급코드(초등:E,중등:M,고등:H)")
    private String schlGrdCd;

    @Parameter(name="성취수준 총평", required=true)
    private N02ReqDto total;

    @Parameter(name="강점 토픽", required=true)
    private String strthTpc;

    @Parameter(name="약점 토픽", required=true)
    private String wknsTpc;

    @Valid
    @Parameter(name="영역별 분석", required=true)
    private N12ReqDto area;

    @Parameter(name="학습자속도유형코드(빠른:FS,보통:NM,느린:SL)", required=true)
    @Pattern(regexp = "^(FS|NM|SL)$", message="학습자속도유형코드(빠른:FS,보통:NM,느린:SL)")
    private String lrnrVelTpCd;

    private String evId;

    private String usrId;

    public AiCmtEnAiDgnRptDto toDto() {
        N12Dto n12Dto = this.area.toDto(AiCmtLvlCalculator.MA_TYPE_50_80);

        List<Map<String, Object>> totalList = this.toList(n12Dto);
        List<String> araRankList = totalList.stream()
                .sorted((f1, f2) -> Float.compare((float) f2.get("rate"), (float) f1.get("rate")))
                .map(s -> (String) s.get("type")).collect(Collectors.toList());

        long all100Cnt = totalList.stream().filter(t -> (float) t.get("rate") == 1).count();
        long all0Cnt = totalList.stream().filter(t -> (float) t.get("rate") == 0).count();

        return AiCmtEnAiDgnRptDto.builder()
                .n02(total.toDto(AiCmtLvlCalculator.MA_TYPE_50_80))
                .araLvlCd(this.getAraLvlCd(totalList))
                .strthTpc(all0Cnt == totalList.size() ? null : this.strthTpc)
                .wknsTpc(all100Cnt == totalList.size() ? null : this.wknsTpc)
                .n12(n12Dto)
                .lrnrVelTpCd(this.lrnrVelTpCd)
                .araRankList(araRankList)
                .build();
    }
    private List<Map<String, Object>> toList(N12Dto n12Dto) {
        List<Map<String, Object>> totalList = new ArrayList<>();

        if(n12Dto.getGrmrLvlCd() != null) totalList.add(this.getMergeMap("문법", n12Dto.getGrmrLvlCd(), this.area.getGrammar()));
        if(n12Dto.getPhncsLvlCd() != null) totalList.add(this.getMergeMap("파닉스", n12Dto.getPhncsLvlCd(), this.area.getPhonics()));
        if(n12Dto.getLstngLvlCd() != null) totalList.add(this.getMergeMap("듣기", n12Dto.getLstngLvlCd(), this.area.getListening()));
        if(n12Dto.getRdngLvlCd() != null) totalList.add(this.getMergeMap("읽기", n12Dto.getRdngLvlCd(), this.area.getReading()));
        if(n12Dto.getWrdLvlCd() != null) totalList.add(this.getMergeMap("단어", n12Dto.getWrdLvlCd(), this.area.getWord()));
        if(n12Dto.getSpkngLvlCd() != null) totalList.add(this.getMergeMap("말하기", n12Dto.getSpkngLvlCd(), this.area.getSpeaking()));
        if(n12Dto.getWrtngLvlCd() != null) totalList.add(this.getMergeMap("쓰기", n12Dto.getWrtngLvlCd(), this.area.getWriting()));
        if(n12Dto.getAlpbLvlCd() != null) totalList.add(this.getMergeMap("알파벳", n12Dto.getAlpbLvlCd(), this.area.getAlphabet()));

        return totalList;
    }

    private String getAraLvlCd(List<Map<String, Object>> totalList) {
        if(totalList.size() >= 3) {
            long h = totalList.stream().filter(s -> "HH".equalsIgnoreCase((String) s.get("lvlCd"))).count();
            long m = totalList.stream().filter(s -> "MM".equalsIgnoreCase((String) s.get("lvlCd"))).count();
            long l = totalList.stream().filter(s -> "LL".equalsIgnoreCase((String) s.get("lvlCd"))).count();

            if(totalList.size() == h) return "HH";
            if(totalList.size() == m) return "HH";
            if(totalList.size() == l) return "LL";

            long strongCnt = 0;
            long weakCnt = 0;

            if(h>0 && m>0 && l>0){
                if(h == 1) {
                    strongCnt=(h+m);
                    weakCnt=l;
                } else {
                    strongCnt=h;
                    weakCnt=(m+l);
                }
            }

            if(h==0 || m==0 || l==0) {
                if(h>0) {
                    strongCnt=h;
                    weakCnt=(m+l);
                } else {
                    strongCnt=m;
                    weakCnt=l;
                }
            }

            if(strongCnt==1 && weakCnt>=2) return "ML";
            else if(strongCnt>=2 && weakCnt>=2) return "MM";
            else if(weakCnt==1 && strongCnt>=2) return "HM";
        }

        return null;
    }

    private Map<String, Object> getMergeMap(String type, String lvlCd, QtmCntReqDto qtmCnt) {
        return Map.of(
                "type", type,
                "rate", ((float) qtmCnt.getCorrectQtmCnt() / qtmCnt.getTotalQtmCnt()),
                "lvlCd", lvlCd);
    }

}
